"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[2122],{92122:(t,e,o)=>{o.r(e),o.d(e,{default:()=>i});var a=o(36768),l=o(35526),n=o(12982),r=o(71677);const s={id:"@jupyterlab/cell-toolbar-extension:plugin",description:"Add the cells toolbar.",autoStart:!0,activate:async(t,e,o,a)=>{const i=e&&o?(0,n.createToolbarFactory)(o,e,l.CellBarExtension.FACTORY_NAME,s.id,null!=a?a:r.nullTranslator):void 0;t.docRegistry.addWidgetExtension("Notebook",new l.CellBarExtension(t.commands,i))},optional:[a.<PERSON>etting<PERSON>eg<PERSON>ry,n.IToolbarWidgetRegistry,r.ITranslator]},i=s}}]);
"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[9380],{19380:(e,t,o)=>{o.r(t),o.d(t,{default:()=>c});var a=o(12982),n=o(97104),r=o(71677),u=o(50331),l=o(78156);const s=[{text:"About Jupyter",url:"https://jupyter.org"},{text:"Markdown Reference",url:"https://commonmark.org/help/"},{text:"Documentation",url:"https://jupyter-notebook.readthedocs.io/en/stable/"}];var p;!function(e){e.open="help:open",e.about="help:about"}(p||(p={}));const c=[{id:"@jupyter-notebook/help-extension:open",autoStart:!0,description:"A plugin to open the about section with resources",activate:e=>{const{commands:t}=e;t.addCommand(p.open,{label:e=>e.text,execute:e=>{const t=e.url;window.open(t)}})}},{id:"@jupyter-notebook/help-extension:about",autoStart:!0,requires:[r.ITranslator],optional:[n.IMainMenu,a.ICommandPalette],description:"Plugin to add a command to show an About Jupyter Notebook and Markdown Reference",activate:(e,t,o,n)=>{const{commands:r}=e,c=t.load("notebook"),b=c.__("Help");r.addCommand(p.about,{label:c.__("About %1",e.name),execute:()=>{const t=l.createElement(l.Fragment,null,l.createElement("span",{className:"jp-AboutNotebook-header"},l.createElement(u.jupyterIcon.react,{width:"196px",height:"auto"}))),o=c.__("JUPYTER NOTEBOOK ON GITHUB"),n=c.__("CONTRIBUTOR LIST"),r=l.createElement("span",null,l.createElement("a",{href:"https://github.com/jupyter/notebook",target:"_blank",rel:"noopener noreferrer",className:"jp-Button-flat jp-AboutNotebook-about-externalLinks"},o),l.createElement("a",{href:"https://github.com/jupyter/notebook/pulse",target:"_blank",rel:"noopener noreferrer",className:"jp-Button-flat jp-AboutNotebook-about-externalLinks"},n)),s=c.__("Version: %1",e.version),p=c.__("© 2021-2023 Jupyter Notebook Contributors"),b=l.createElement(l.Fragment,null,l.createElement("span",{className:"jp-AboutNotebook-version"},s),l.createElement("div",null,r),l.createElement("span",{className:"jp-AboutNotebook-about-copyright"},p)),m=new a.Dialog({title:t,body:b,buttons:[a.Dialog.createButton({label:c.__("Dismiss"),className:"jp-AboutNotebook-about-button jp-mod-reject jp-mod-styled"})]});m.addClass("jp-AboutNotebook"),m.launch()}}),n&&n.addItem({command:p.about,category:b});const m=s.map((e=>({args:e,command:p.open})));o&&o.helpMenu.addGroup(m,30)}}]}}]);
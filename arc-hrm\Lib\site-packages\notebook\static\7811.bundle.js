/*! For license information please see 7811.bundle.js.LICENSE.txt */
(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[7811],{79349:r=>{r.exports=function(r,t,e){switch(e.length){case 0:return r.call(t);case 1:return r.call(t,e[0]);case 2:return r.call(t,e[0],e[1]);case 3:return r.call(t,e[0],e[1],e[2])}return r.apply(t,e)}},80594:r=>{r.exports=function(r,t){for(var e=-1,n=null==r?0:r.length;++e<n&&!1!==t(r[e],e,r););return r}},71928:(r,t,e)=>{var n=e(88799),o=e(85638),a=Object.prototype.hasOwnProperty;r.exports=function(r,t,e){var c=r[t];a.call(r,t)&&o(c,e)&&(void 0!==e||t in r)||n(r,t,e)}},41876:(r,t,e)=>{var n=e(35159),o=e(50098);r.exports=function(r,t){return r&&n(t,o(t),r)}},5947:(r,t,e)=>{var n=e(35159),o=e(53893);r.exports=function(r,t){return r&&n(t,o(t),r)}},88799:(r,t,e)=>{var n=e(42630);r.exports=function(r,t,e){"__proto__"==t&&n?n(r,t,{configurable:!0,enumerable:!0,value:e,writable:!0}):r[t]=e}},40699:(r,t,e)=>{var n=e(23694),o=e(80594),a=e(71928),c=e(41876),u=e(5947),i=e(2734),f=e(37561),s=e(31102),p=e(37048),l=e(51385),v=e(39759),b=e(3533),y=e(86541),x=e(2078),j=e(9560),h=e(19785),g=e(43854),O=e(98247),d=e(11611),w=e(47614),_=e(50098),A=e(53893),S="[object Arguments]",m="[object Function]",P="[object Object]",U={};U[S]=U["[object Array]"]=U["[object ArrayBuffer]"]=U["[object DataView]"]=U["[object Boolean]"]=U["[object Date]"]=U["[object Float32Array]"]=U["[object Float64Array]"]=U["[object Int8Array]"]=U["[object Int16Array]"]=U["[object Int32Array]"]=U["[object Map]"]=U["[object Number]"]=U[P]=U["[object RegExp]"]=U["[object Set]"]=U["[object String]"]=U["[object Symbol]"]=U["[object Uint8Array]"]=U["[object Uint8ClampedArray]"]=U["[object Uint16Array]"]=U["[object Uint32Array]"]=!0,U["[object Error]"]=U[m]=U["[object WeakMap]"]=!1,r.exports=function r(t,e,E,I,R,T){var k,C=1&e,F=2&e,B=4&e;if(E&&(k=R?E(t,I,R,T):E(t)),void 0!==k)return k;if(!d(t))return t;var D=h(t);if(D){if(k=y(t),!C)return f(t,k)}else{var L=b(t),M=L==m||"[object GeneratorFunction]"==L;if(g(t))return i(t,C);if(L==P||L==S||M&&!R){if(k=F||M?{}:j(t),!C)return F?p(t,u(k,t)):s(t,c(k,t))}else{if(!U[L])return R?t:{};k=x(t,L,C)}}T||(T=new n);var N=T.get(t);if(N)return N;T.set(t,k),w(t)?t.forEach((function(n){k.add(r(n,e,E,n,t,T))})):O(t)&&t.forEach((function(n,o){k.set(o,r(n,e,E,o,t,T))}));var Y=D?void 0:(B?F?v:l:F?A:_)(t);return o(Y||t,(function(n,o){Y&&(n=t[o=n]),a(k,o,r(n,e,E,o,t,T))})),k}},80158:(r,t,e)=>{var n=e(11611),o=Object.create,a=function(){function r(){}return function(t){if(!n(t))return{};if(o)return o(t);r.prototype=t;var e=new r;return r.prototype=void 0,e}}();r.exports=a},23545:(r,t,e)=>{var n=e(97141),o=e(72889);r.exports=function r(t,e,a,c,u){var i=-1,f=t.length;for(a||(a=o),u||(u=[]);++i<f;){var s=t[i];e>0&&a(s)?e>1?r(s,e-1,a,c,u):n(u,s):c||(u[u.length]=s)}return u}},30124:r=>{var t=Object.prototype.hasOwnProperty;r.exports=function(r,e){return null!=r&&t.call(r,e)}},21610:r=>{r.exports=function(r,t){return null!=r&&t in Object(r)}},57657:(r,t,e)=>{var n=e(3533),o=e(92360);r.exports=function(r){return o(r)&&"[object Map]"==n(r)}},26903:(r,t,e)=>{var n=e(3533),o=e(92360);r.exports=function(r){return o(r)&&"[object Set]"==n(r)}},59464:(r,t,e)=>{var n=e(11611),o=e(56016),a=e(21586),c=Object.prototype.hasOwnProperty;r.exports=function(r){if(!n(r))return a(r);var t=o(r),e=[];for(var u in r)("constructor"!=u||!t&&c.call(r,u))&&e.push(u);return e}},78859:(r,t,e)=>{var n=e(71928),o=e(76747),a=e(42383),c=e(11611),u=e(37948);r.exports=function(r,t,e,i){if(!c(r))return r;for(var f=-1,s=(t=o(t,r)).length,p=s-1,l=r;null!=l&&++f<s;){var v=u(t[f]),b=e;if("__proto__"===v||"constructor"===v||"prototype"===v)return r;if(f!=p){var y=l[v];void 0===(b=i?i(y,v,l):void 0)&&(b=c(y)?y:a(t[f+1])?[]:{})}n(l,v,b),l=l[v]}return r}},54459:(r,t,e)=>{var n=e(551),o=e(42630),a=e(31137),c=o?function(r,t){return o(r,"toString",{configurable:!0,enumerable:!1,value:n(t),writable:!0})}:a;r.exports=c},75733:r=>{r.exports=function(r,t,e){var n=-1,o=r.length;t<0&&(t=-t>o?0:o+t),(e=e>o?o:e)<0&&(e+=o),o=t>e?0:e-t>>>0,t>>>=0;for(var a=Array(o);++n<o;)a[n]=r[n+t];return a}},70830:(r,t,e)=>{var n=e(76747),o=e(31159),a=e(64373),c=e(37948);r.exports=function(r,t){return t=n(t,r),null==(r=a(r,t))||delete r[c(o(t))]}},95825:(r,t,e)=>{var n=e(59942);r.exports=function(r){var t=new r.constructor(r.byteLength);return new n(t).set(new n(r)),t}},2734:(r,t,e)=>{r=e.nmd(r);var n=e(77400),o=t&&!t.nodeType&&t,a=o&&r&&!r.nodeType&&r,c=a&&a.exports===o?n.Buffer:void 0,u=c?c.allocUnsafe:void 0;r.exports=function(r,t){if(t)return r.slice();var e=r.length,n=u?u(e):new r.constructor(e);return r.copy(n),n}},61859:(r,t,e)=>{var n=e(95825);r.exports=function(r,t){var e=t?n(r.buffer):r.buffer;return new r.constructor(e,r.byteOffset,r.byteLength)}},9377:r=>{var t=/\w*$/;r.exports=function(r){var e=new r.constructor(r.source,t.exec(r));return e.lastIndex=r.lastIndex,e}},24116:(r,t,e)=>{var n=e(96539),o=n?n.prototype:void 0,a=o?o.valueOf:void 0;r.exports=function(r){return a?Object(a.call(r)):{}}},63428:(r,t,e)=>{var n=e(95825);r.exports=function(r,t){var e=t?n(r.buffer):r.buffer;return new r.constructor(e,r.byteOffset,r.length)}},37561:r=>{r.exports=function(r,t){var e=-1,n=r.length;for(t||(t=Array(n));++e<n;)t[e]=r[e];return t}},35159:(r,t,e)=>{var n=e(71928),o=e(88799);r.exports=function(r,t,e,a){var c=!e;e||(e={});for(var u=-1,i=t.length;++u<i;){var f=t[u],s=a?a(e[f],r[f],f,e,r):void 0;void 0===s&&(s=r[f]),c?o(e,f,s):n(e,f,s)}return e}},31102:(r,t,e)=>{var n=e(35159),o=e(83080);r.exports=function(r,t){return n(r,o(r),t)}},37048:(r,t,e)=>{var n=e(35159),o=e(30791);r.exports=function(r,t){return n(r,o(r),t)}},97820:(r,t,e)=>{var n=e(40861);r.exports=function(r){return n(r)?void 0:r}},42630:(r,t,e)=>{var n=e(81822),o=function(){try{var r=n(Object,"defineProperty");return r({},"",{}),r}catch(r){}}();r.exports=o},24288:(r,t,e)=>{var n=e(89754),o=e(11871),a=e(63132);r.exports=function(r){return a(o(r,void 0,n),r+"")}},39759:(r,t,e)=>{var n=e(11324),o=e(30791),a=e(53893);r.exports=function(r){return n(r,a,o)}},2173:(r,t,e)=>{var n=e(58023)(Object.getPrototypeOf,Object);r.exports=n},30791:(r,t,e)=>{var n=e(97141),o=e(2173),a=e(83080),c=e(15937),u=Object.getOwnPropertySymbols?function(r){for(var t=[];r;)n(t,a(r)),r=o(r);return t}:c;r.exports=u},731:(r,t,e)=>{var n=e(76747),o=e(2900),a=e(19785),c=e(42383),u=e(84194),i=e(37948);r.exports=function(r,t,e){for(var f=-1,s=(t=n(t,r)).length,p=!1;++f<s;){var l=i(t[f]);if(!(p=null!=r&&e(r,l)))break;r=r[l]}return p||++f!=s?p:!!(s=null==r?0:r.length)&&u(s)&&c(l,s)&&(a(r)||o(r))}},86541:r=>{var t=Object.prototype.hasOwnProperty;r.exports=function(r){var e=r.length,n=new r.constructor(e);return e&&"string"==typeof r[0]&&t.call(r,"index")&&(n.index=r.index,n.input=r.input),n}},2078:(r,t,e)=>{var n=e(95825),o=e(61859),a=e(9377),c=e(24116),u=e(63428);r.exports=function(r,t,e){var i=r.constructor;switch(t){case"[object ArrayBuffer]":return n(r);case"[object Boolean]":case"[object Date]":return new i(+r);case"[object DataView]":return o(r,e);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return u(r,e);case"[object Map]":case"[object Set]":return new i;case"[object Number]":case"[object String]":return new i(r);case"[object RegExp]":return a(r);case"[object Symbol]":return c(r)}}},9560:(r,t,e)=>{var n=e(80158),o=e(2173),a=e(56016);r.exports=function(r){return"function"!=typeof r.constructor||a(r)?{}:n(o(r))}},72889:(r,t,e)=>{var n=e(96539),o=e(2900),a=e(19785),c=n?n.isConcatSpreadable:void 0;r.exports=function(r){return a(r)||o(r)||!!(c&&r&&r[c])}},21586:r=>{r.exports=function(r){var t=[];if(null!=r)for(var e in Object(r))t.push(e);return t}},11871:(r,t,e)=>{var n=e(79349),o=Math.max;r.exports=function(r,t,e){return t=o(void 0===t?r.length-1:t,0),function(){for(var a=arguments,c=-1,u=o(a.length-t,0),i=Array(u);++c<u;)i[c]=a[t+c];c=-1;for(var f=Array(t+1);++c<t;)f[c]=a[c];return f[t]=e(i),n(r,this,f)}}},64373:(r,t,e)=>{var n=e(79867),o=e(75733);r.exports=function(r,t){return t.length<2?r:n(r,o(t,0,-1))}},63132:(r,t,e)=>{var n=e(54459),o=e(49591)(n);r.exports=o},49591:r=>{var t=Date.now;r.exports=function(r){var e=0,n=0;return function(){var o=t(),a=16-(o-n);if(n=o,a>0){if(++e>=800)return arguments[0]}else e=0;return r.apply(void 0,arguments)}}},30454:(r,t,e)=>{var n=e(40699);r.exports=function(r){return n(r,5)}},551:r=>{r.exports=function(r){return function(){return r}}},89754:(r,t,e)=>{var n=e(23545);r.exports=function(r){return null!=r&&r.length?n(r,1):[]}},73915:(r,t,e)=>{var n=e(30124),o=e(731);r.exports=function(r,t){return null!=r&&o(r,t,n)}},79749:(r,t,e)=>{var n=e(21610),o=e(731);r.exports=function(r,t){return null!=r&&o(r,t,n)}},31137:r=>{r.exports=function(r){return r}},90104:(r,t,e)=>{var n=e(20186),o=e(3533),a=e(2900),c=e(19785),u=e(80068),i=e(43854),f=e(56016),s=e(48519),p=Object.prototype.hasOwnProperty;r.exports=function(r){if(null==r)return!0;if(u(r)&&(c(r)||"string"==typeof r||"function"==typeof r.splice||i(r)||s(r)||a(r)))return!r.length;var t=o(r);if("[object Map]"==t||"[object Set]"==t)return!r.size;if(f(r))return!n(r).length;for(var e in r)if(p.call(r,e))return!1;return!0}},98247:(r,t,e)=>{var n=e(57657),o=e(39334),a=e(18125),c=a&&a.isMap,u=c?o(c):n;r.exports=u},40861:(r,t,e)=>{var n=e(99736),o=e(2173),a=e(92360),c=Function.prototype,u=Object.prototype,i=c.toString,f=u.hasOwnProperty,s=i.call(Object);r.exports=function(r){if(!a(r)||"[object Object]"!=n(r))return!1;var t=o(r);if(null===t)return!0;var e=f.call(t,"constructor")&&t.constructor;return"function"==typeof e&&e instanceof e&&i.call(e)==s}},47614:(r,t,e)=>{var n=e(26903),o=e(39334),a=e(18125),c=a&&a.isSet,u=c?o(c):n;r.exports=u},53893:(r,t,e)=>{var n=e(98213),o=e(59464),a=e(80068);r.exports=function(r){return a(r)?n(r,!0):o(r)}},31159:r=>{r.exports=function(r){var t=null==r?0:r.length;return t?r[t-1]:void 0}},48159:(r,t,e)=>{var n=e(66070),o=e(40699),a=e(70830),c=e(76747),u=e(35159),i=e(97820),f=e(24288),s=e(39759),p=f((function(r,t){var e={};if(null==r)return e;var f=!1;t=n(t,(function(t){return t=c(t,r),f||(f=t.length>1),t})),u(r,s(r),e),f&&(e=o(e,7,i));for(var p=t.length;p--;)a(e,t[p]);return e}));r.exports=p},47215:(r,t,e)=>{var n=e(78859);r.exports=function(r,t,e){return null==r?r:n(r,t,e)}},40110:(r,t,e)=>{var n=e(66070),o=e(37561),a=e(19785),c=e(55193),u=e(23419),i=e(37948),f=e(65567);r.exports=function(r){return a(r)?n(r,i):c(r)?[r]:o(u(f(r)))}},71426:(r,t,e)=>{"use strict";var n=e(78156),o=Symbol.for("react.element"),a=Symbol.for("react.fragment"),c=Object.prototype.hasOwnProperty,u=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,i={key:!0,ref:!0,__self:!0,__source:!0};function f(r,t,e){var n,a={},f=null,s=null;for(n in void 0!==e&&(f=""+e),void 0!==t.key&&(f=""+t.key),void 0!==t.ref&&(s=t.ref),t)c.call(t,n)&&!i.hasOwnProperty(n)&&(a[n]=t[n]);if(r&&r.defaultProps)for(n in t=r.defaultProps)void 0===a[n]&&(a[n]=t[n]);return{$$typeof:o,type:r,key:f,ref:s,props:a,_owner:u.current}}t.Fragment=a,t.jsx=f,t.jsxs=f},24246:(r,t,e)=>{"use strict";r.exports=e(71426)}}]);
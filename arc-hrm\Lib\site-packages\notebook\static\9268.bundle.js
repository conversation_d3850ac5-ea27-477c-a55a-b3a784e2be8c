"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[9268,3129],{79268:(e,n,r)=>{r.r(n),r.d(n,{createMarkdownParser:()=>l,default:()=>u});var t=r(20998),a=r(38639),i=r(52337),o=r(70856),s=r(20652);const c="```~~~";function l(e,n){return{render:r=>d.render(r,e,n)}}const u={id:"@jupyterlab/markedparser-extension:plugin",description:"Provides the Markdown parser.",autoStart:!0,provides:o.IMarkdownParser,requires:[i.IEditorLanguageRegistry],optional:[s.IMermaidMarkdown],activate:(e,n,r)=>l(n,{blocks:r?[r]:[]})};var d;!function(e){let n=null,i=null,o=[],s=null,l={},u=new a.LruCache;async function d(e){if(i)return i;if(n)return await n.promise;o=(null==e?void 0:e.blocks)||[],o=o.sort(((e,n)=>{var r,t;return(null!==(r=e.rank)&&void 0!==r?r:1/0)-(null!==(t=n.rank)&&void 0!==t?t:1/0)})),n=new t.PromiseDelegate;const[{marked:a,Renderer:s},c]=await Promise.all([r.e(4329).then(r.t.bind(r,74329,23)),f()]);for(const e of c)a.use(e);return l={async:!0,gfm:!0,walkTokens:w,renderer:g(s)},i=a,n.resolve(i),i}async function f(){return Promise.all([(async()=>(await r.e(4152).then(r.t.bind(r,24152,23))).gfmHeadingId())(),(async()=>(await r.e(9853).then(r.t.bind(r,29853,23))).mangle())()])}function g(e){const n=new e,r=n.code;return n.code=(e,t)=>{for(const n of o)if(n.languages.includes(t)){const r=n.render(e);if(null!=r)return r}const a=`${t}${c}${e}${c}`,i=u.get(a);return null!=i?i:r.call(n,e,t)},n}async function w(e){if("code"===e.type){if(e.lang)for(const n of o)if(n.languages.includes(e.lang))return void await n.walk(e.text);await async function(e){const{lang:n,text:r}=e;if(!n||!s)return;const t=`${n}${c}${r}${c}`;if(u.get(t))return;const a=document.createElement("div");try{await s.highlight(r,s.findBest(n),a);const e=`<pre><code class="language-${n}">${a.innerHTML}</code></pre>`;u.set(t,e)}catch(e){console.error(`Failed to highlight ${n} code`,e)}finally{a.remove()}}(e)}}e.render=async function(e,n,r){return s=n,i||(i=await d(r)),i(e,l)},e.initializeMarked=d}(d||(d={}))}}]);
"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[1696],{1696:(t,e,o)=>{var i,n,a,r,c;o.r(e),o.d(e,{ClipboardExt:()=>i,ElementExt:()=>n,Platform:()=>a,Selector:()=>r}),function(t){t.copyText=function(t){const e=document.body,o=i=>{i.preventDefault(),i.stopPropagation(),i.clipboardData.setData("text",t),e.removeEventListener("copy",o,!0)};e.addEventListener("copy",o,!0),document.execCommand("copy")}}(i||(i={})),function(t){t.boxSizing=function(t){let e=window.getComputedStyle(t),o=parseFloat(e.borderTopWidth)||0,i=parseFloat(e.borderLeftWidth)||0,n=parseFloat(e.borderRightWidth)||0,a=parseFloat(e.borderBottomWidth)||0,r=parseFloat(e.paddingTop)||0,c=parseFloat(e.paddingLeft)||0,l=parseFloat(e.paddingRight)||0,h=parseFloat(e.paddingBottom)||0;return{borderTop:o,borderLeft:i,borderRight:n,borderBottom:a,paddingTop:r,paddingLeft:c,paddingRight:l,paddingBottom:h,horizontalSum:i+c+l+n,verticalSum:o+r+h+a}},t.sizeLimits=function(t){let e=window.getComputedStyle(t),o=parseFloat(e.minWidth)||0,i=parseFloat(e.minHeight)||0,n=parseFloat(e.maxWidth)||1/0,a=parseFloat(e.maxHeight)||1/0;return n=Math.max(o,n),a=Math.max(i,a),{minWidth:o,minHeight:i,maxWidth:n,maxHeight:a}},t.hitTest=function(t,e,o){let i=t.getBoundingClientRect();return e>=i.left&&e<i.right&&o>=i.top&&o<i.bottom},t.scrollIntoViewIfNeeded=function(t,e){let o=t.getBoundingClientRect(),i=e.getBoundingClientRect();i.top<=o.top&&i.bottom>=o.bottom||(i.top<o.top&&i.height<=o.height||i.bottom>o.bottom&&i.height>=o.height?t.scrollTop-=o.top-i.top:(i.top<o.top&&i.height>o.height||i.bottom>o.bottom&&i.height<o.height)&&(t.scrollTop-=o.bottom-i.bottom))}}(n||(n={})),function(t){t.IS_MAC=!!navigator.platform.match(/Mac/i),t.IS_WIN=!!navigator.platform.match(/Win/i),t.IS_IE=/Trident/.test(navigator.userAgent),t.IS_EDGE=/Edge/.test(navigator.userAgent),t.accelKey=function(e){return t.IS_MAC?e.metaKey:e.ctrlKey}}(a||(a={})),function(t){t.calculateSpecificity=function(t){if(t in c.specificityCache)return c.specificityCache[t];let e=c.calculateSingle(t);return c.specificityCache[t]=e},t.isValid=function(t){if(t in c.validityCache)return c.validityCache[t];let e=!0;try{c.testElem.querySelector(t)}catch(t){e=!1}return c.validityCache[t]=e},t.matches=function(t,e){return c.protoMatchFunc.call(t,e)}}(r||(r={})),function(t){t.specificityCache=Object.create(null),t.validityCache=Object.create(null),t.testElem=document.createElement("div"),t.protoMatchFunc=(()=>{let t=Element.prototype;return t.matches||t.matchesSelector||t.mozMatchesSelector||t.msMatchesSelector||t.oMatchesSelector||t.webkitMatchesSelector||function(t){let e=this,o=e.ownerDocument?e.ownerDocument.querySelectorAll(t):[];return-1!==Array.prototype.indexOf.call(o,e)}})(),t.calculateSingle=function(t){let h=0,s=0,p=0;function d(e){let o=t.match(e);return null!==o&&(t=t.slice(o[0].length),!0)}for(t=(t=t.split(",",1)[0]).replace(l," $1 ");t.length>0;)if(d(e))h++;else if(d(o))s++;else if(d(i))s++;else if(d(a))p++;else if(d(r))s++;else if(d(n))p++;else if(!d(c))return 0;return h=Math.min(h,255),s=Math.min(s,255),p=Math.min(p,255),h<<16|s<<8|p};const e=/^#[^\s\+>~#\.\[:]+/,o=/^\.[^\s\+>~#\.\[:]+/,i=/^\[[^\]]+\]/,n=/^[^\s\+>~#\.\[:]+/,a=/^(::[^\s\+>~#\.\[:]+|:first-line|:first-letter|:before|:after)/,r=/^:[^\s\+>~#\.\[:]+/,c=/^[\s\+>~\*]+/,l=/:not\(([^\)]+)\)/g}(c||(c={}))}}]);
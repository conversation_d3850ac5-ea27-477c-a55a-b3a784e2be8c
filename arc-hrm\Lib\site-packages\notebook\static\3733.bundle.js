"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[3733],{13733:(e,t,a)=>{a.r(t),a.d(t,{AttachedProperty:()=>i});class i{constructor(e){this._pid=r.nextPID(),this.name=e.name,this._create=e.create,this._coerce=e.coerce||null,this._compare=e.compare||null,this._changed=e.changed||null}get(e){let t,a=r.ensureMap(e);return t=this._pid in a?a[this._pid]:a[this._pid]=this._createValue(e),t}set(e,t){let a,i=r.ensureMap(e);a=this._pid in i?i[this._pid]:i[this._pid]=this._createValue(e);let c=this._coerceValue(e,t);this._maybeNotify(e,a,i[this._pid]=c)}coerce(e){let t,a=r.ensureMap(e);t=this._pid in a?a[this._pid]:a[this._pid]=this._createValue(e);let i=this._coerceValue(e,t);this._maybeNotify(e,t,a[this._pid]=i)}_createValue(e){return(0,this._create)(e)}_coerceValue(e,t){let a=this._coerce;return a?a(e,t):t}_compareValue(e,t){let a=this._compare;return a?a(e,t):e===t}_maybeNotify(e,t,a){let i=this._changed;i&&!this._compareValue(t,a)&&i(e,t,a)}}var r;!function(e){e.clearData=function(e){r.ownerData.delete(e)}}(i||(i={})),function(e){e.ownerData=new WeakMap,e.nextPID=(()=>{let e=0;return()=>`pid-${`${Math.random()}`.slice(2)}-${e++}`})(),e.ensureMap=function(t){let a=e.ownerData.get(t);return a||(a=Object.create(null),e.ownerData.set(t,a),a)}}(r||(r={}))}}]);
"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[9233],{9233:(t,e,i)=>{i.r(e),i.d(e,{yaml:()=>a});var r=new RegExp("\\b(("+["true","false","on","off","yes","no"].join(")|(")+"))$","i");const a={name:"yaml",token:function(t,e){var i=t.peek(),a=e.escaped;if(e.escaped=!1,"#"==i&&(0==t.pos||/\s/.test(t.string.charAt(t.pos-1))))return t.skipToEnd(),"comment";if(t.match(/^('([^']|\\.)*'?|"([^"]|\\.)*"?)/))return"string";if(e.literal&&t.indentation()>e.keyCol)return t.skipToEnd(),"string";if(e.literal&&(e.literal=!1),t.sol()){if(e.keyCol=0,e.pair=!1,e.pairStart=!1,t.match("---"))return"def";if(t.match("..."))return"def";if(t.match(/^\s*-\s+/))return"meta"}if(t.match(/^(\{|\}|\[|\])/))return"{"==i?e.inlinePairs++:"}"==i?e.inlinePairs--:"["==i?e.inlineList++:e.inlineList--,"meta";if(e.inlineList>0&&!a&&","==i)return t.next(),"meta";if(e.inlinePairs>0&&!a&&","==i)return e.keyCol=0,e.pair=!1,e.pairStart=!1,t.next(),"meta";if(e.pairStart){if(t.match(/^\s*(\||\>)\s*/))return e.literal=!0,"meta";if(t.match(/^\s*(\&|\*)[a-z0-9\._-]+\b/i))return"variable";if(0==e.inlinePairs&&t.match(/^\s*-?[0-9\.\,]+\s?$/))return"number";if(e.inlinePairs>0&&t.match(/^\s*-?[0-9\.\,]+\s?(?=(,|}))/))return"number";if(t.match(r))return"keyword"}return!e.pair&&t.match(/^\s*(?:[,\[\]{}&*!|>'"%@`][^\s'":]|[^,\[\]{}#&*!|>'"%@`])[^#]*?(?=\s*:($|\s))/)?(e.pair=!0,e.keyCol=t.indentation(),"atom"):e.pair&&t.match(/^:\s*/)?(e.pairStart=!0,"meta"):(e.pairStart=!1,e.escaped="\\"==i,t.next(),null)},startState:function(){return{pair:!1,pairStart:!1,keyCol:0,inlinePairs:0,inlineList:0,literal:!1,escaped:!1}},languageData:{commentTokens:{line:"#"}}}}}]);
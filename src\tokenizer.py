"""
Module de tokenisation grammaticale pour les programmes ARC.
Ce tokenizer convertit les commandes textuelles en séquences d'entiers (tokens)
et vice-versa, en se basant sur un vocabulaire défini qui correspond
aux commandes unifiées du projet.
"""
import re

class GrammarTokenizer:
    """
    Tokenizer basé sur un vocabulaire grammatical fixe pour les commandes ARC.
    """
    def __init__(self):
        """Initialise le tokenizer avec un vocabulaire complet et unifié."""
        self.vocab = {
            # --- Jetons Spéciaux ---
            '<pad>': 0, '<sos>': 1, '<eos>': 2, '<unk>': 3,

            # --- Délimiteurs et Symboles ---
            ' ': 4, ',': 5, ';': 6, '(': 7, ')': 8,
            '[': 9, ']': 10, '{': 11, '}': 12,

            # === Actions de Base ===
            'CLEAR': 13, 
            'FILL': 14, 
            'SURROUND': 15, 
            'REPLACE': 16, 
            'EDIT': 17,
            'FLOODFILL': 18,

            # === Modifications Structurelles ===
            'INSERT': 19, 
            'DELETE': 20, 
            'EXTRACT': 21,
            'RESIZE': 22, # Conservé pour compatibilité

            # === Transformations de Motifs ===
            'FLIP': 23, 
            'ROTATE': 24,
            'MULTIPLY': 25,
            'DIVIDE': 26,

            # === Presse-papiers ===
            'COPY': 27, 
            'CUT': 28, 
            'PASTE': 29,

            # === Sélections Spéciales (agissent sur les coordonnées) ===
            'INVERT': 30, 
            'COLOR': 31,

            # === Commandes de Groupement (décompressées) ===
            'EDITS': 32, 
            'FILLS': 33, 
            'REPLACES': 34,
            'CLEARS': 35,
            'SURROUNDS': 36,
            'FLIPS': 37,
            'ROTATES': 38,
            'INSERTS': 39,
            'DELETES': 40,
            'FLOODFILLS': 41,

            # === Commandes Système et Blocs ===
            'INIT': 42,
            'TRANSFERT': 43,
            'MOTIF': 44,
            'END': 45,

            # === Paramètres ===
            # --- Directions et Positions ---
            'HORIZONTAL': 46, 'VERTICAL': 47,
            'LEFT': 48, 'RIGHT': 49,
            'ABOVE': 50, 'BELOW': 51,
            'BEFORE': 52, 'AFTER': 53,
            # --- Types d'éléments ---
            'ROWS': 54, 'COLUMNS': 55,
        }
        
        # --- Ajout dynamique des chiffres 0-9 ---
        for i in range(10):
            self.vocab[str(i)] = len(self.vocab)
        
        self.reverse_vocab = {idx: token for token, idx in self.vocab.items()}
        self.unk_token_id = self.vocab['<unk>']

    def tokenize(self, program: str) -> list[int]:
        """
        Tokenize un programme en une séquence d'IDs de vocabulaire.
        La tokenisation est simple et basée sur les délimiteurs.
        """
        if not program:
            return []

        # Utiliser une regex pour splitter tout en conservant les délimiteurs
        # Délimiteurs: espace, ,, ;, (, ), [, ], {, }
        tokens = re.findall(r'\d+|[A-Z_]+|\S', program)
        
        token_ids = []
        for token in tokens:
            token_ids.append(self.vocab.get(token, self.unk_token_id))
            
        return token_ids

    def detokenize(self, token_ids: list[int]) -> str:
        """
        Convertit une séquence d'IDs en une chaîne de programme lisible.
        """
        tokens = [self.reverse_vocab.get(idx, '<unk>') for idx in token_ids]
        
        # Post-traitement pour gérer l'espacement correctement
        program_str = ""
        for i, token in enumerate(tokens):
            program_str += token
            # Ajouter un espace après la plupart des tokens, sauf avant un délimiteur
            if i + 1 < len(tokens) and tokens[i+1] not in '.,;)]}':
                if token not in '([{' :
                     program_str += " "
        
        return program_str.replace(" ; ", "; ").strip()


"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[5263],{95263:(e,t,i)=>{i.r(t),i.d(t,{CodeMirrorEditor:()=>Mi,CodeMirrorEditorFactory:()=>Ii,CodeMirrorMimeTypeService:()=>Li,CodeMirrorSearchHighlighter:()=>qi,EditorExtensionRegistry:()=>z,EditorLanguageRegistry:()=>Ei,EditorSearchProvider:()=>zi,EditorThemeRegistry:()=>Ti,ExtensionsHandler:()=>D,IEditorExtensionRegistry:()=>Bi,IEditorLanguageRegistry:()=>Fi,IEditorThemeRegistry:()=>Hi,PythonBuiltin:()=>Oi,StateCommands:()=>n,YRange:()=>$,YSyncConfig:()=>E,customTheme:()=>f,jupyterEditorTheme:()=>Ni,jupyterHighlightStyle:()=>Ci,jupyterTheme:()=>ji,parseMathIPython:()=>S,pythonBuiltin:()=>Pi,rulers:()=>O,ySync:()=>I,ySyncAnnotation:()=>A,ySyncFacet:()=>M,ybinding:()=>L});var n,s=i(83861),r=i(31536);!function(e){e.indentMoreOrInsertTab=function(e){var t;if(null===(t=e.dom.parentElement)||void 0===t?void 0:t.classList.contains(r.COMPLETER_ENABLED_CLASS))return!1;const i={state:e.state,dispatch:e.dispatch},n=e.state.selection.main.from;if(n!=e.state.selection.main.to)return(0,s.indentMore)(i);const a=e.state.doc.lineAt(n),o=e.state.doc.slice(a.from,n).toString();return/^\s*$/.test(o)?(0,s.indentMore)(i):(0,s.insertTab)(i)},e.completerOrInsertNewLine=function(e){var t;if(null===(t=e.dom.parentElement)||void 0===t?void 0:t.classList.contains(r.COMPLETER_ACTIVE_CLASS))return!0;const i={state:e.state,dispatch:e.dispatch};return(0,s.insertNewlineAndIndent)(i)},e.preventNewLineOnRun=function(e){return!!e.dom.closest("[data-jp-code-runner]")},e.dedentIfNotLaunchingTooltip=function(e){return!!e.dom.closest(".jp-CodeMirrorEditor:not(.jp-mod-has-primary-selection):not(.jp-mod-in-leading-whitespace):not(.jp-mod-completer-active)")||(0,s.indentLess)(e)}}(n||(n={}));var a=i(7201),o=i(17811),l=i(30890),c=i(20998),h=i(81997),u=i(56318),d=i(71677);const m=o.Facet.define({combine:e=>(0,o.combineConfig)(e,{fontFamily:null,fontSize:null,lineHeight:null},{fontFamily:(e,t)=>null!=e?e:t,fontSize:(e,t)=>null!=e?e:t,lineHeight:(e,t)=>null!=e?e:t})});function p(e){const{fontFamily:t,fontSize:i,lineHeight:n}=e.state.facet(m);let s="";return i&&(s+=`font-size: ${i}px !important;`),t&&(s+=`font-family: ${t} !important;`),n&&(s+=`line-height: ${n.toString()} !important`),{style:s}}function f(e){return[m.of(e),l.EditorView.editorAttributes.of(p)]}var g=i(73887),y=i(66361);const x="InlineMathDollar",b="InlineMathBracket",_="BlockMathDollar",w="BlockMathBracket",k={[x]:1,[b]:3,[_]:2,[w]:3},v=Object.keys(k).reduce(((e,t)=>(e[t]={mark:`${t}Mark`,resolve:t},e)),{});function S(e){const t=new Array;return Object.keys(k).forEach((e=>{t.push({name:e,style:y.tags.emphasis},{name:`${e}Mark`,style:y.tags.processingInstruction})})),{defineNodes:t,parseInline:[{name:_,parse:(e,t,i)=>36!=t||36!=e.char(i+1)?-1:e.addDelimiter(v[_],i,i+k[_],!0,!0)},{name:x,parse:(e,t,i)=>36!=t||36==e.char(i+1)?-1:e.addDelimiter(v[x],i,i+k[x],!0,!0)},{name:b,before:"Escape",parse:(e,t,i)=>92==t&&92==e.char(i+1)&&[40,41].includes(e.char(i+2))?e.addDelimiter(v[b],i,i+k[b],40==e.char(i+2),41==e.char(i+2)):-1},{name:w,before:"Escape",parse:(e,t,i)=>92==t&&92==e.char(i+1)&&[91,93].includes(e.char(i+2))?e.addDelimiter(v[w],i,i+k[w],91==e.char(i+2),93==e.char(i+2)):-1}],wrap:e?(0,g.parseMixed)(((t,i)=>{const n=k[t.type.name];return n?{parser:e,overlay:[{from:t.from+n,to:t.to-n}]}:null})):void 0}}const N="cm-rulers",C=l.EditorView.baseTheme({[`.${N}`]:{borderRight:"1px dotted gray",opacity:.7}}),j=o.Facet.define({combine:e=>e.reduce(((e,t)=>e.concat(t.filter(((i,n)=>!e.includes(i)&&n==t.lastIndexOf(i))))),[])}),T=l.ViewPlugin.fromClass(class{constructor(e){var t,i;this.rulersContainer=e.dom.appendChild(document.createElement("div")),this.rulersContainer.style.cssText="\n                position: absolute;\n                left: 0;\n                top: 0;\n                width: 100%;\n                height: 100%;\n                pointer-events: none;\n                overflow: hidden;\n            ";const n=e.defaultCharacterWidth,s=e.state.facet(j),r=null!==(i=null===(t=e.scrollDOM.querySelector(".cm-gutters"))||void 0===t?void 0:t.clientWidth)&&void 0!==i?i:0;this.rulers=s.map((e=>{const t=this.rulersContainer.appendChild(document.createElement("div"));return t.classList.add(N),t.style.cssText=`\n                position: absolute;\n                left: ${r+e*n}px;\n                height: 100%;\n            `,t.style.width="6px",t}))}update(e){var t,i;const n=e.view.state.facet(j);if(e.viewportChanged||e.geometryChanged||!c.JSONExt.deepEqual(n,e.startState.facet(j))){const s=null!==(i=null===(t=e.view.scrollDOM.querySelector(".cm-gutters"))||void 0===t?void 0:t.clientWidth)&&void 0!==i?i:0,r=e.view.defaultCharacterWidth;this.rulers.forEach(((e,t)=>{e.style.left=`${s+n[t]*r}px`}))}}destroy(){this.rulers.forEach((e=>{e.remove()})),this.rulersContainer.remove()}});function O(e){return[C,j.of(e),T]}var P=i(17843);class ${constructor(e,t){this.yanchor=e,this.yhead=t}toJSON(){return{yanchor:(0,P.relativePositionToJSON)(this.yanchor),yhead:(0,P.relativePositionToJSON)(this.yhead)}}static fromJSON(e){return new $((0,P.createRelativePositionFromJSON)(e.yanchor),(0,P.createRelativePositionFromJSON)(e.yhead))}}class E{constructor(e){this.ytext=e}toYPos(e,t=0){return(0,P.createRelativePositionFromTypeIndex)(this.ytext,e,t)}fromYPos(e){const t=(0,P.createAbsolutePositionFromRelativePosition)((0,P.createRelativePositionFromJSON)(e),this.ytext.doc);if(null==t||t.type!==this.ytext)throw new Error("[y-codemirror] The position you want to retrieve was created by a different document");return{pos:t.index,assoc:t.assoc}}toYRange(e){const t=e.assoc,i=this.toYPos(e.anchor,t),n=this.toYPos(e.head,t);return new $(i,n)}fromYRange(e){const t=this.fromYPos(e.yanchor),i=this.fromYPos(e.yhead);return t.pos===i.pos?o.EditorSelection.cursor(i.pos,i.assoc):o.EditorSelection.range(t.pos,i.pos)}}const M=o.Facet.define({combine:e=>e[e.length-1]}),A=o.Annotation.define(),I=l.ViewPlugin.fromClass(class{constructor(e){this.conf=e.state.facet(M),this._observer=(t,i)=>{var n;if(i.origin!==this.conf){const i=t.delta,s=[];let r=0;for(let e=0;e<i.length;e++){const t=i[e];null!=t.insert?s.push({from:r,to:r,insert:t.insert}):null!=t.delete?(s.push({from:r,to:r+t.delete,insert:""}),r+=t.delete):r+=null!==(n=t.retain)&&void 0!==n?n:0}e.dispatch({changes:s,annotations:[A.of(this.conf)]})}},this._ytext=this.conf.ytext,this._ytext.observe(this._observer)}update(e){if(!e.docChanged||e.transactions.length>0&&e.transactions[0].annotation(A)===this.conf)return;const t=this.conf.ytext;t.doc.transact((()=>{let i=0;e.changes.iterChanges(((e,n,s,r,a)=>{const o=a.sliceString(0,a.length,"\n");e!==n&&t.delete(e+i,n-e),o.length>0&&t.insert(e+i,o),i+=o.length-(n-e)}))}),this.conf)}destroy(){this._ytext.unobserve(this._observer)}});function L({ytext:e,undoManager:t}){const i=new E(e);return[M.of(i),I,t?l.ViewPlugin.define((()=>(t.addTrackedOrigin(i),{destroy:()=>{t.removeTrackedOrigin(i)}}))):[]]}var R=i(35260);class D{constructor({baseConfiguration:e,config:t,defaultExtensions:i}={}){this._configChanged=new h.Signal(this),this._disposed=new h.Signal(this),this._isDisposed=!1,this._immutables=new Set,this._baseConfig=null!=e?e:{},this._config=null!=t?t:{},this._configurableBuilderMap=new Map(i);const n=Object.keys(this._config).concat(Object.keys(this._baseConfig));this._immutables=new Set([...this._configurableBuilderMap.keys()].filter((e=>!n.includes(e))))}get configChanged(){return this._configChanged}get disposed(){return this._disposed}get isDisposed(){return this._isDisposed}dispose(){this.isDisposed||(this._isDisposed=!0,this._disposed.emit(),h.Signal.clearData(this))}getOption(e){var t;return null!==(t=this._config[e])&&void 0!==t?t:this._baseConfig[e]}hasOption(e){return Object.keys(this._config).includes(e)||Object.keys(this._baseConfig).includes(e)}setOption(e,t){this._config[e]!==t&&(this._config[e]=t,this._configChanged.emit({[e]:t}))}setBaseOptions(e){const t=this._getChangedOptions(e,this._baseConfig);if(t.length>0){this._baseConfig=e;const i=Object.keys(this._config),n=t.filter((e=>!i.includes(e)));n.length>0&&this._configChanged.emit(n.reduce(((e,t)=>(e[t]=this._baseConfig[t],e)),{}))}}setOptions(e){const t=this._getChangedOptions(e,this._config);t.length>0&&(this._config={...e},this._configChanged.emit(t.reduce(((e,t)=>{var i;return e[t]=null!==(i=this._config[t])&&void 0!==i?i:this._baseConfig[t],e}),{})))}reconfigureExtension(e,t,i){const n=this.getEffect(e.state,t,i);n&&e.dispatch({effects:[n]})}reconfigureExtensions(e,t){const i=Object.keys(t).filter((e=>this.has(e))).map((i=>this.getEffect(e.state,i,t[i])));e.dispatch({effects:i.filter((e=>null!==e))})}injectExtension(e,t){e.dispatch({effects:o.StateEffect.appendConfig.of(t)})}getInitialExtensions(){const e={...this._baseConfig,...this._config},t=[...this._immutables].map((e=>{var t;return null===(t=this.get(e))||void 0===t?void 0:t.instance(void 0)})).filter((e=>e));for(const i of Object.keys(e)){const n=this.get(i);if(n){const s=e[i];t.push(n.instance(s))}}return t}get(e){return this._configurableBuilderMap.get(e)}has(e){return this._configurableBuilderMap.has(e)}getEffect(e,t,i){var n;const s=this.get(t);return null!==(n=null==s?void 0:s.reconfigure(i))&&void 0!==n?n:null}_getChangedOptions(e,t){const i=new Array,n=new Array;for(const[s,r]of Object.entries(e))n.push(s),t[s]!==r&&i.push(s);return i.push(...Object.keys(t).filter((e=>!n.includes(e)))),i}}class z{constructor(){this.configurationBuilder=new Map,this.configurationSchema={},this.defaultOptions={},this.handlers=new Set,this.immutableExtensions=new Set,this._baseConfiguration={}}get baseConfiguration(){return{...this.defaultOptions,...this._baseConfiguration}}set baseConfiguration(e){if(!c.JSONExt.deepEqual(e,this._baseConfiguration)){this._baseConfiguration=e;for(const e of this.handlers)e.setBaseOptions(this.baseConfiguration)}}get defaultConfiguration(){return Object.freeze({...this.defaultOptions})}get settingsSchema(){return Object.freeze(c.JSONExt.deepCopy(this.configurationSchema))}addExtension(e){var t;if(this.configurationBuilder.has(e.name))throw new Error(`Extension named ${e.name} is already registered.`);this.configurationBuilder.set(e.name,e),void 0!==e.default&&(this.defaultOptions[e.name]=e.default),e.schema&&(this.configurationSchema[e.name]={default:null!==(t=e.default)&&void 0!==t?t:null,...e.schema},this.defaultOptions[e.name]=this.configurationSchema[e.name].default)}createNew(e){const t=new Array;for(const[i,n]of this.configurationBuilder.entries()){const s=n.factory(e);s&&t.push([i,s])}const i=new D({baseConfiguration:this.baseConfiguration,config:e.config,defaultExtensions:t});return this.handlers.add(i),i.disposed.connect((()=>{this.handlers.delete(i)})),i}}!function(e){class t{constructor(e){this._compartment=new o.Compartment,this._builder=e}instance(e){return this._compartment.of(this._builder(e))}reconfigure(e){return this._compartment.reconfigure(this._builder(e))}}class i{constructor(e){this._extension=e}instance(){return this._extension}reconfigure(){return null}}function r(e){return new t(e)}function c(e,i=[]){return new t((t=>t?e:i))}function h(e){return new i(e)}e.createConfigurableExtension=r,e.createConditionalExtension=c,e.createImmutableExtension=h,e.getDefaultExtensions=function(e={}){const{themes:t,translator:i}=e,m=(null!=i?i:d.nullTranslator).load("jupyterlab"),p=[Object.freeze({name:"autoClosingBrackets",default:!1,factory:()=>c((0,u.vQ)()),schema:{type:"boolean",title:m.__("Auto Closing Brackets")}}),Object.freeze({name:"codeFolding",default:!1,factory:()=>c((0,a.foldGutter)()),schema:{type:"boolean",title:m.__("Code Folding")}}),Object.freeze({name:"cursorBlinkRate",default:1200,factory:()=>r((e=>(0,l.drawSelection)({cursorBlinkRate:e}))),schema:{type:"number",title:m.__("Cursor blinking rate"),description:m.__("Half-period in milliseconds used for cursor blinking. The default blink rate is 1200ms. By setting this to zero, blinking can be disabled.")}}),Object.freeze({name:"highlightActiveLine",default:!1,factory:()=>c((0,l.highlightActiveLine)()),schema:{type:"boolean",title:m.__("Highlight the active line")}}),Object.freeze({name:"highlightSpecialCharacters",default:!0,factory:()=>c((0,l.highlightSpecialChars)()),schema:{type:"boolean",title:m.__("Highlight special characters")}}),Object.freeze({name:"highlightTrailingWhitespace",default:!1,factory:()=>c((0,l.highlightTrailingWhitespace)()),schema:{type:"boolean",title:m.__("Highlight trailing white spaces")}}),Object.freeze({name:"highlightWhitespace",default:!1,factory:()=>c((0,l.highlightWhitespace)()),schema:{type:"boolean",title:m.__("Highlight white spaces")}}),Object.freeze({name:"indentUnit",default:"4",factory:()=>r((e=>"Tab"==e?a.indentUnit.of("\t"):a.indentUnit.of(" ".repeat(parseInt(e,10))))),schema:{type:"string",title:m.__("Indentation unit"),description:m.__("The indentation is a `Tab` or the number of spaces. This defaults to 4 spaces."),enum:["Tab","1","2","4","8"]}}),Object.freeze({name:"keymap",default:[{key:"Mod-Enter",run:n.preventNewLineOnRun},{key:"Enter",run:n.completerOrInsertNewLine},...s.defaultKeymap,{key:"Tab",run:n.indentMoreOrInsertTab,shift:n.dedentIfNotLaunchingTooltip}],factory:()=>r((e=>l.keymap.of(e)))}),Object.freeze({name:"lineNumbers",default:!0,factory:()=>c((0,l.lineNumbers)()),schema:{type:"boolean",title:m.__("Line Numbers")}}),Object.freeze({name:"lineWrap",factory:()=>c(l.EditorView.lineWrapping),default:!0,schema:{type:"boolean",title:m.__("Line Wrap")}}),Object.freeze({name:"matchBrackets",default:!0,factory:()=>c([(0,a.bracketMatching)(),o.Prec.high(l.keymap.of(u.GA))]),schema:{type:"boolean",title:m.__("Match Brackets")}}),Object.freeze({name:"rectangularSelection",default:!0,factory:()=>c([(0,l.rectangularSelection)(),(0,l.crosshairCursor)()]),schema:{type:"boolean",title:m.__("Rectangular selection"),description:m.__("Rectangular (block) selection can be created by dragging the mouse pointer while holding the left mouse button and the Alt key. When the Alt key is pressed, a crosshair cursor will appear, indicating that the rectangular selection mode is active.")}}),Object.freeze({name:"readOnly",default:!1,factory:()=>r((e=>[o.EditorState.readOnly.of(e),e?l.EditorView.editorAttributes.of({class:"jp-mod-readOnly"}):[]]))}),Object.freeze({name:"rulers",default:[],factory:()=>r((e=>e.length>0?O(e):[])),schema:{type:"array",title:m.__("Rulers"),items:{type:"number",minimum:0}}}),Object.freeze({name:"extendSelection",default:!0,factory:()=>c(l.keymap.of([{key:"Mod-Shift-l",run:R.selectSelectionMatches,preventDefault:!0},{key:"Mod-d",run:R.selectNextOccurrence,preventDefault:!0}]))}),Object.freeze({name:"searchWithCM",default:!1,factory:()=>c(l.keymap.of([{key:"Mod-f",run:R.openSearchPanel,scope:"editor search-panel"},{key:"F3",run:R.findNext,shift:R.findPrevious,scope:"editor search-panel",preventDefault:!0},{key:"Mod-g",run:R.findNext,shift:R.findPrevious,scope:"editor search-panel",preventDefault:!0},{key:"Escape",run:R.closeSearchPanel,scope:"editor search-panel"}]))}),Object.freeze({name:"scrollPastEnd",default:!1,factory:e=>e.inline?null:c((0,l.scrollPastEnd)())}),Object.freeze({name:"smartIndent",default:!0,factory:()=>c((0,a.indentOnInput)()),schema:{type:"boolean",title:m.__("Smart Indentation")}}),Object.freeze({name:"tabFocusable",default:!0,factory:()=>c(l.EditorView.contentAttributes.of({tabIndex:"0"}),l.EditorView.contentAttributes.of({tabIndex:"-1"}))}),Object.freeze({name:"tabSize",default:4,factory:()=>r((e=>o.EditorState.tabSize.of(e))),schema:{type:"number",title:m.__("Tab size")}}),Object.freeze({name:"tooltips",factory:()=>h((0,l.tooltips)({position:"absolute",parent:document.body}))}),Object.freeze({name:"allowMultipleSelections",default:!0,factory:()=>r((e=>o.EditorState.allowMultipleSelections.of(e))),schema:{type:"boolean",title:m.__("Multiple selections")}}),Object.freeze({name:"customStyles",factory:()=>r((e=>f(e))),default:{fontFamily:null,fontSize:null,lineHeight:null},schema:{title:m.__("Custom editor styles"),type:"object",properties:{fontFamily:{type:["string","null"],title:m.__("Font Family")},fontSize:{type:["number","null"],minimum:1,maximum:100,title:m.__("Font Size")},lineHeight:{type:["number","null"],title:m.__("Line Height")}},additionalProperties:!1}})];return t&&p.push(Object.freeze({name:"theme",default:"jupyter",factory:()=>r((e=>t.getTheme(e))),schema:{type:"string",title:m.__("Theme"),description:m.__("CodeMirror theme")}})),i&&p.push(Object.freeze({name:"translation",default:{"Control character":m.__("Control character"),"Selection deleted":m.__("Selection deleted"),"Folded lines":m.__("Folded lines"),"Unfolded lines":m.__("Unfolded lines"),to:m.__("to"),"folded code":m.__("folded code"),unfold:m.__("unfold"),"Fold line":m.__("Fold line"),"Unfold line":m.__("Unfold line"),"Go to line":m.__("Go to line"),go:m.__("go"),Find:m.__("Find"),Replace:m.__("Replace"),next:m.__("next"),previous:m.__("previous"),all:m.__("all"),"match case":m.__("match case"),replace:m.__("replace"),"replace all":m.__("replace all"),close:m.__("close"),"current match":m.__("current match"),"replaced $ matches":m.__("replaced $ matches"),"replaced match on line $":m.__("replaced match on line $"),"on line":m.__("on line"),Completions:m.__("Completions"),Diagnostics:m.__("Diagnostics"),"No diagnostics":m.__("No diagnostics")},factory:()=>r((e=>o.EditorState.phrases.of(e)))})),p}}(z||(z={}));var q=i(38639),B=i(49906),F=i(27061);class H{constructor(e){this.start=e}}class G extends H{constructor(e,t,i,n,s,r,a,o,l,c,h,u,d,m,p){super(e),this.rules=t,this.topRules=i,this.tokens=n,this.localTokens=s,this.context=r,this.externalTokens=a,this.externalSpecializers=o,this.externalPropSources=l,this.precedences=c,this.mainSkip=h,this.scopedSkip=u,this.dialects=d,this.externalProps=m,this.autoDelim=p}toString(){return Object.values(this.rules).join("\n")}}class J extends H{constructor(e,t,i,n,s){super(e),this.id=t,this.props=i,this.params=n,this.expr=s}toString(){return this.id.name+(this.params.length?`<${this.params.join()}>`:"")+" -> "+this.expr}}class V extends H{constructor(e,t){super(e),this.items=t}}class U extends H{constructor(e,t){super(e),this.items=t}}class Q extends H{constructor(e,t,i){super(e),this.a=t,this.b=i}}class W extends H{constructor(e,t,i,n,s){super(e),this.precedences=t,this.conflicts=i,this.rules=n,this.literals=s}}class Y extends H{constructor(e,t,i,n){super(e),this.precedences=t,this.rules=i,this.fallback=n}}class X extends H{constructor(e,t,i){super(e),this.literal=t,this.props=i}}class K extends H{constructor(e,t,i){super(e),this.id=t,this.source=i}}class Z extends H{constructor(e,t,i,n){super(e),this.id=t,this.source=i,this.tokens=n}}class ee extends H{constructor(e,t,i,n,s,r){super(e),this.type=t,this.token=i,this.id=n,this.source=s,this.tokens=r}}class te extends H{constructor(e,t,i){super(e),this.id=t,this.source=i}}class ie extends H{constructor(e,t,i,n){super(e),this.id=t,this.externalID=i,this.source=n}}class ne extends H{constructor(e,t){super(e),this.name=t}toString(){return this.name}}class se extends H{walk(e){return e(this)}eq(e){return!1}}se.prototype.prec=10;class re extends se{constructor(e,t,i){super(e),this.id=t,this.args=i}toString(){return this.id.name+(this.args.length?`<${this.args.join()}>`:"")}eq(e){return this.id.name==e.id.name&&be(this.args,e.args)}walk(e){let t=fe(this.args,e);return e(t==this.args?this:new re(this.start,this.id,t))}}class ae extends se{constructor(e,t,i,n,s){super(e),this.type=t,this.props=i,this.token=n,this.content=s}toString(){return`@${this.type}[${this.props.join(",")}]<${this.token}, ${this.content}>`}eq(e){return this.type==e.type&&_e.eqProps(this.props,e.props)&&xe(this.token,e.token)&&xe(this.content,e.content)}walk(e){let t=this.token.walk(e),i=this.content.walk(e);return e(t==this.token&&i==this.content?this:new ae(this.start,this.type,this.props,t,i))}}class oe extends se{constructor(e,t){super(e),this.rule=t}toString(){let e=this.rule;return`${e.id}${e.props.length?`[${e.props.join(",")}]`:""} { ${e.expr} }`}eq(e){let t=this.rule,i=e.rule;return xe(t.expr,i.expr)&&t.id.name==i.id.name&&_e.eqProps(t.props,i.props)}walk(e){let t=this.rule,i=t.expr.walk(e);return e(i==t.expr?this:new oe(this.start,new J(t.start,t.id,t.props,[],i)))}}class le extends se{constructor(e,t){super(e),this.exprs=t}toString(){return this.exprs.map((e=>ke(e,this))).join(" | ")}eq(e){return be(this.exprs,e.exprs)}walk(e){let t=fe(this.exprs,e);return e(t==this.exprs?this:new le(this.start,t))}}le.prototype.prec=1;class ce extends se{constructor(e,t,i,n=!1){super(e),this.exprs=t,this.markers=i,this.empty=n}toString(){return this.empty?"()":this.exprs.map((e=>ke(e,this))).join(" ")}eq(e){return be(this.exprs,e.exprs)&&this.markers.every(((t,i)=>{let n=e.markers[i];return t.length==n.length&&t.every(((e,t)=>e.eq(n[t])))}))}walk(e){let t=fe(this.exprs,e);return e(t==this.exprs?this:new ce(this.start,t,this.markers,this.empty&&!t.length))}}ce.prototype.prec=2;class he extends H{constructor(e,t,i){super(e),this.id=t,this.type=i}toString(){return("ambig"==this.type?"~":"!")+this.id.name}eq(e){return this.id.name==e.id.name&&this.type==e.type}}class ue extends se{constructor(e,t,i){super(e),this.expr=t,this.kind=i}toString(){return ke(this.expr,this)+this.kind}eq(e){return xe(this.expr,e.expr)&&this.kind==e.kind}walk(e){let t=this.expr.walk(e);return e(t==this.expr?this:new ue(this.start,t,this.kind))}}ue.prototype.prec=3;class de extends se{constructor(e,t){super(e),this.value=t}toString(){return JSON.stringify(this.value)}eq(e){return this.value==e.value}}class me extends se{constructor(e,t,i){super(e),this.ranges=t,this.inverted=i}toString(){return`[${this.inverted?"^":""}${this.ranges.map((([e,t])=>String.fromCodePoint(e)+(t==e+1?"":"-"+String.fromCodePoint(t))))}]`}eq(e){return this.inverted==e.inverted&&this.ranges.length==e.ranges.length&&this.ranges.every((([t,i],n)=>{let[s,r]=e.ranges[n];return t==s&&i==r}))}}class pe extends se{constructor(e){super(e)}toString(){return"_"}eq(){return!0}}function fe(e,t){let i=null;for(let n=0;n<e.length;n++){let s=e[n].walk(t);s==e[n]||i||(i=e.slice(0,n)),i&&i.push(s)}return i||e}const ge={asciiLetter:[[65,91],[97,123]],asciiLowercase:[[97,123]],asciiUppercase:[[65,91]],digit:[[48,58]],whitespace:[[9,14],[32,33],[133,134],[160,161],[5760,5761],[8192,8203],[8232,8234],[8239,8240],[8287,8288],[12288,12289]],eof:[[65535,65535]]};class ye extends se{constructor(e,t){super(e),this.type=t}toString(){return"@"+this.type}eq(e){return this.type==e.type}}function xe(e,t){return e.constructor==t.constructor&&e.eq(t)}function be(e,t){return e.length==t.length&&e.every(((e,i)=>xe(e,t[i])))}class _e extends H{constructor(e,t,i,n){super(e),this.at=t,this.name=i,this.value=n}eq(e){return this.name==e.name&&this.value.length==e.value.length&&this.value.every(((t,i)=>t.value==e.value[i].value&&t.name==e.value[i].name))}toString(){let e=(this.at?"@":"")+this.name;if(this.value.length){e+="=";for(let{name:t,value:i}of this.value)e+=t?`{${t}}`:/[^\w-]/.test(i)?JSON.stringify(i):i}return e}static eqProps(e,t){return e.length==t.length&&e.every(((e,i)=>e.eq(t[i])))}}class we extends H{constructor(e,t,i){super(e),this.value=t,this.name=i}}function ke(e,t){return e.prec<t.prec?"("+e.toString()+")":e.toString()}class ve extends Error{}function Se(e){for(let t in e)return!0;return!1}let Ne=0;class Ce{constructor(e,t,i,n={}){this.name=e,this.flags=t,this.nodeName=i,this.props=n,this.hash=++Ne,this.id=-1,this.rules=[]}toString(){return this.name}get nodeType(){return this.top||null!=this.nodeName||Se(this.props)||this.repeated}get terminal(){return(1&this.flags)>0}get eof(){return(4&this.flags)>0}get error(){return"error"in this.props}get top(){return(2&this.flags)>0}get interesting(){return this.flags>0||null!=this.nodeName}get repeated(){return(16&this.flags)>0}set preserve(e){this.flags=e?8|this.flags:-9&this.flags}get preserve(){return(8&this.flags)>0}set inline(e){this.flags=e?32|this.flags:-33&this.flags}get inline(){return(32&this.flags)>0}cmp(e){return this.hash-e.hash}}class je{constructor(){this.terms=[],this.names=Object.create(null),this.tops=[],this.eof=this.term("␄",null,5),this.error=this.term("⚠","⚠",8)}term(e,t,i=0,n={}){let s=new Ce(e,i,t,n);return this.terms.push(s),this.names[e]=s,s}makeTop(e,t){const i=this.term("@top",e,2,t);return this.tops.push(i),i}makeTerminal(e,t,i={}){return this.term(e,t,1,i)}makeNonTerminal(e,t,i={}){return this.term(e,t,0,i)}makeRepeat(e){return this.term(e,null,16)}uniqueName(e){for(let t=0;;t++){let i=t?`${e}-${t}`:e;if(!this.names[i])return i}}finish(e){for(let t of e)t.name.rules.push(t);this.terms=this.terms.filter((t=>t.terminal||t.preserve||e.some((e=>e.name==t||e.parts.includes(t)))));let t={},i=[this.error];this.error.id=0;let n=1;for(let e of this.terms)e.id<0&&e.nodeType&&!e.repeated&&(e.id=n++,i.push(e));let s=n;for(let e of this.terms)e.repeated&&(e.id=n++,i.push(e));this.eof.id=n++;for(let e of this.terms)e.id<0&&(e.id=n++),e.name&&(t[e.id]=e.name);if(n>=65534)throw new ve("Too many terms");return{nodeTypes:i,names:t,minRepeatTerm:s,maxTerm:n-1}}}function Te(e,t,i){if(e.length!=t.length)return e.length-t.length;for(let n=0;n<e.length;n++){let s=i(e[n],t[n]);if(s)return s}return 0}const Oe=[];class Pe{constructor(e,t=Oe,i=0){this.precedence=e,this.ambigGroups=t,this.cut=i}join(e){return this==Pe.none||this==e?e:e==Pe.none?this:new Pe(Math.max(this.precedence,e.precedence),$e(this.ambigGroups,e.ambigGroups),Math.max(this.cut,e.cut))}cmp(e){return this.precedence-e.precedence||Te(this.ambigGroups,e.ambigGroups,((e,t)=>e<t?-1:e>t?1:0))||this.cut-e.cut}}function $e(e,t){if(0==e.length||e==t)return t;if(0==t.length)return e;let i=e.slice();for(let n of t)e.includes(n)||i.push(n);return i.sort()}Pe.none=new Pe(0);let Ee=0;class Me{constructor(e,t,i,n){this.name=e,this.parts=t,this.conflicts=i,this.skip=n,this.id=Ee++}cmp(e){return this.id-e.id}cmpNoName(e){return this.parts.length-e.parts.length||this.skip.hash-e.skip.hash||this.parts.reduce(((t,i,n)=>t||i.cmp(e.parts[n])),0)||Te(this.conflicts,e.conflicts,((e,t)=>e.cmp(t)))}toString(){return this.name+" -> "+this.parts.join(" ")}get isRepeatWrap(){return this.name.repeated&&2==this.parts.length&&this.parts[0]==this.name}sameReduce(e){return this.name==e.name&&this.parts.length==e.parts.length&&this.isRepeatWrap==e.isRepeatWrap}}class Ae{constructor(e,t,i){this.from=e,this.to=t,this.target=i}toString(){return`-> ${this.target.id}[label=${JSON.stringify(this.from<0?"ε":Ie(this.from)+(this.to>this.from+1?"-"+Ie(this.to-1):""))}]`}}function Ie(e){return e>65535?"∞":10==e?"\\n":13==e?"\\r":e<32||e>=55296&&e<57343?"\\u{"+e.toString(16)+"}":String.fromCharCode(e)}function Le(e,t,i){if(e.edges.length!=t.edges.length)return!1;for(let n=0;n<e.edges.length;n++){let s=e.edges[n],r=t.edges[n];if(s.from!=r.from||s.to!=r.to||i[s.target.id]!=i[r.target.id])return!1}return!0}function Re(e,t,i){for(let t of e)for(let e=0;e<t.edges.length;e++){let n=t.edges[e],s=i[n.target.id][0];s!=n.target&&(t.edges[e]=new Ae(n.from,n.to,s))}return i[t.id][0]}let De=1,ze=class e{constructor(e=[],t=De++){this.accepting=e,this.id=t,this.edges=[]}edge(e,t,i){this.edges.push(new Ae(e,t,i))}nullEdge(e){this.edge(-1,-1,e)}compile(){let t=Object.create(null),i=0,n=function n(s){let r=t[Fe(s)]=new e(s.reduce(((e,t)=>$e(e,t.accepting)),[]),i++),a=[];for(let e of s)for(let t of e.edges)t.from>=0&&a.push(t);let o=function(e){let t=[],i=[];for(let i of e)t.includes(i.from)||t.push(i.from),t.includes(i.to)||t.push(i.to);t.sort(((e,t)=>e-t));for(let n=1;n<t.length;n++){let s=t[n-1],r=t[n],a=[];for(let t of e)if(t.to>s&&t.from<r)for(let e of t.target.closure())a.includes(e)||a.push(e);a.length&&i.push(new He(s,r,a))}let n=e.filter((e=>65535==e.from&&65535==e.to));if(n.length){let e=[];for(let t of n)for(let i of t.target.closure())e.includes(i)||e.push(i);e.length&&i.push(new He(65535,65535,e))}return i}(a);for(let e of o){let i=e.targets.sort(((e,t)=>e.id-t.id));r.edge(e.from,e.to,t[Fe(i)]||n(i))}return r}(this.closure().sort(((e,t)=>e.id-t.id)));return function(e,t){let i=Object.create(null),n=Object.create(null);for(let t of e){let e=Fe(t.accepting),s=n[e]||(n[e]=[]);s.push(t),i[t.id]=s}for(;;){let n=!1,s=Object.create(null);for(let t of e){if(s[t.id])continue;let e=i[t.id];if(1==e.length){s[e[0].id]=e;continue}let r=[];e:for(let t of e){for(let e of r)if(Le(t,e[0],i)){e.push(t);continue e}r.push([t])}r.length>1&&(n=!0);for(let e of r)for(let t of e)s[t.id]=e}if(!n)return Re(e,t,i);i=s}}(Object.values(t),n)}closure(){let e=[],t=Object.create(null);return function i(n){if(!t[n.id]){t[n.id]=!0,(n.edges.some((e=>e.from>=0))||n.accepting.length>0&&!n.edges.some((e=>function(e,t){if(e.length!=t.length)return!1;for(let i=0;i<e.length;i++)if(e[i]!=t[i])return!1;return!0}(n.accepting,e.target.accepting))))&&e.push(n);for(let e of n.edges)e.from<0&&i(e.target)}}(this),e}findConflicts(e){let t=[],i=this.cycleTerms();function n(e,i,n,s,r){e.id<i.id&&([e,i]=[i,e],n=-n);let a=t.find((t=>t.a==e&&t.b==i));a?a.soft!=n&&(a.soft=0):t.push(new qe(e,i,n,Be(s),r&&Be(r)))}return this.reachable(((t,s)=>{if(0!=t.accepting.length){for(let e=0;e<t.accepting.length;e++)for(let i=e+1;i<t.accepting.length;i++)n(t.accepting[e],t.accepting[i],0,s);t.reachable(((r,a)=>{if(r!=t)for(let o of r.accepting){let r=i.includes(o);for(let l of t.accepting)o!=l&&n(o,l,r||i.includes(l)||!e(o,l)?0:1,s,s.concat(a))}}))}})),t}cycleTerms(){let e=[];this.reachable((t=>{for(let{target:i}of t.edges)e.push(t,i)}));let t=new Map,i=[];for(let n=0;n<e.length;){let s=e[n++],r=e[n++],a=t.get(s);if(a||t.set(s,a=[]),!a.includes(r))if(s==r)i.includes(s)||i.push(s);else{for(let t of a)e.push(s,t);a.push(r)}}let n=[];for(let e of i)for(let t of e.accepting)n.includes(t)||n.push(t);return n}reachable(e){let t=[],i=[];!function n(s){e(s,i),t.push(s);for(let e of s.edges)t.includes(e.target)||(i.push(e),n(e.target),i.pop())}(this)}toString(){let e="digraph {\n";return this.reachable((t=>{t.accepting.length&&(e+=`  ${t.id} [label=${JSON.stringify(t.accepting.join())}];\n`);for(let i of t.edges)e+=`  ${t.id} ${i};\n`})),e+"}"}toArray(e,t){let i=[],n=[];this.reachable((s=>{let r=n.length,a=r+3+2*s.accepting.length;i[s.id]=r,n.push(s.stateMask(e),a,s.edges.length),s.accepting.sort(((e,i)=>t.indexOf(e.id)-t.indexOf(i.id)));for(let t of s.accepting)n.push(t.id,e[t.id]||65535);for(let e of s.edges)n.push(e.from,e.to,-e.target.id-1)}));for(let e=0;e<n.length;e++)n[e]<0&&(n[e]=i[-n[e]-1]);if(n.length>Math.pow(2,16))throw new ve("Tokenizer tables too big to represent with 16-bit offsets.");return Uint16Array.from(n)}stateMask(e){let t=0;return this.reachable((i=>{for(let n of i.accepting)t|=e[n.id]||65535})),t}},qe=class{constructor(e,t,i,n,s){this.a=e,this.b=t,this.soft=i,this.exampleA=n,this.exampleB=s}};function Be(e){let t="";for(let i=0;i<e.length;i++)t+=String.fromCharCode(e[i].from);return t}function Fe(e){let t="";for(let i of e)t.length&&(t+="-"),t+=i.id;return t}class He{constructor(e,t,i){this.from=e,this.to=t,this.targets=i}}let Ge=/[\w_-]+/gy;try{Ge=/[\p{Alphabetic}\d_-]+/guy}catch(e){}const Je=[];class Ve{constructor(e,t=null){this.string=e,this.fileName=t,this.type="sof",this.value=null,this.start=0,this.end=0,this.next()}lineInfo(e){for(let t=1,i=0;;){let n=this.string.indexOf("\n",i);if(!(n>-1&&n<e))return{line:t,ch:e-i};++t,i=n+1}}message(e,t=-1){let i=this.fileName||"";if(t>-1){let e=this.lineInfo(t);i+=(i?" ":"")+e.line+":"+e.ch}return i?e+` (${i})`:e}raise(e,t=-1){throw new ve(this.message(e,t))}match(e,t){let i=t.exec(this.string.slice(e));return i?e+i[0].length:-1}next(){let e=this.match(this.end,/^(\s|\/\/.*|\/\*[^]*?\*\/)*/);if(e==this.string.length)return this.set("eof",null,e,e);let t=this.string[e];if('"'==t){let t=this.match(e+1,/^(\\.|[^"\\])*"/);return-1==t&&this.raise("Unterminated string literal",e),this.set("string",ft(this.string.slice(e+1,t-1)),e,t)}if("'"==t){let t=this.match(e+1,/^(\\.|[^'\\])*'/);return-1==t&&this.raise("Unterminated string literal",e),this.set("string",ft(this.string.slice(e+1,t-1)),e,t)}if("@"==t){Ge.lastIndex=e+1;let t=Ge.exec(this.string);return t?this.set("at",t[0],e,e+1+t[0].length):this.raise("@ without a name",e)}if("$"!=t&&"!"!=t||"["!=this.string[e+1]){if(/[\[\]()!~+*?{}<>\.,|:$=]/.test(t))return this.set(t,null,e,e+1);{Ge.lastIndex=e;let i=Ge.exec(this.string);return i?this.set("id",i[0],e,e+i[0].length):this.raise("Unexpected character "+JSON.stringify(t),e)}}{let t=this.match(e+2,/^(?:\\.|[^\]\\])*\]/);return-1==t&&this.raise("Unterminated character set",e),this.set("set",this.string.slice(e+2,t-1),e,t)}}set(e,t,i,n){this.type=e,this.value=t,this.start=i,this.end=n}eat(e,t=null){return this.type==e&&(null==t||this.value===t)&&(this.next(),!0)}unexpected(){return this.raise(`Unexpected token '${this.string.slice(this.start,this.end)}'`,this.start)}expect(e,t=null){let i=this.value;return(this.type!=e||null!=t&&i!==t)&&this.unexpected(),this.next(),i}parse(){return function(e){let t=e.start,i=[],n=null,s=null,r=[],a=null,o=[],l=[],c=null,h=[],u=[],d=[],m=[],p=[],f=!1,g=!1;for(;"eof"!=e.type;){let t=e.start;if(e.eat("at","top"))"id"!=e.type&&e.raise("Top rules must have a name",e.start),p.push(Ue(e,st(e))),f=!0;else if("at"==e.type&&"tokens"==e.value)s?e.raise("Multiple @tokens declaractions",e.start):s=at(e);else if(e.eat("at","local"))e.expect("id","tokens"),r.push(ot(e,t));else if(e.eat("at","context")){c&&e.raise("Multiple @context declarations",t);let i=st(e);e.expect("id","from");let n=e.expect("string");c=new K(t,i,n)}else if(e.eat("at","external"))e.eat("id","tokens")?h.push(ut(e,t)):e.eat("id","prop")?d.push(pt(e,t)):e.eat("id","extend")?u.push(dt(e,"extend",t)):e.eat("id","specialize")?u.push(dt(e,"specialize",t)):e.eat("id","propSource")?m.push(mt(e,t)):e.unexpected();else if(e.eat("at","dialects")){e.expect("{");for(let t=!0;!e.eat("}");t=!1)t||e.eat(","),l.push(st(e))}else if("at"==e.type&&"precedence"==e.value)n&&e.raise("Multiple precedence declarations",e.start),n=rt(e);else if(e.eat("at","detectDelim"))g=!0;else if(e.eat("at","skip")){let t=Ye(e);if("{"==e.type){e.next();let i=[],n=[];for(;!e.eat("}");)e.eat("at","top")?(n.push(Ue(e,st(e))),f=!0):i.push(Ue(e));o.push({expr:t,topRules:n,rules:i})}else a&&e.raise("Multiple top-level skip declarations",e.start),a=t}else i.push(Ue(e))}return f?new G(t,i,p,s,r,c,h,u,m,n,a,o,l,d,g):e.raise("Missing @top declaration")}(this)}}function Ue(e,t){let i=t?t.start:e.start,n=t||st(e),s=Qe(e),r=[];if(e.eat("<"))for(;!e.eat(">");)r.length&&e.expect(","),r.push(st(e));let a=Ye(e);return new J(i,n,s,r,a)}function Qe(e){if("["!=e.type)return Je;let t=[];for(e.expect("[");!e.eat("]");)t.length&&e.expect(","),t.push(We(e));return t}function We(e){let t=e.start,i=[],n=e.value,s="at"==e.type;if(e.eat("at")||e.eat("id")||e.unexpected(),e.eat("="))for(;;)if("string"==e.type||"id"==e.type)i.push(new we(e.start,e.value,null)),e.next();else if(e.eat("."))i.push(new we(e.start,".",null));else{if(!e.eat("{"))break;i.push(new we(e.start,null,e.expect("id"))),e.expect("}")}return new _e(t,s,n,i)}function Ye(e){e.expect("{");let t=nt(e);return e.expect("}"),t}const Xe="﷚";function Ke(e){let t=e.start;if(e.eat("(")){if(e.eat(")"))return new ce(t,Je,[Je,Je]);let i=nt(e);return e.expect(")"),i}if("string"==e.type){let i=e.value;return e.next(),0==i.length?new ce(t,Je,[Je,Je]):new de(t,i)}if(e.eat("id","_"))return new pe(t);if("set"==e.type){let i=e.value,n="!"==e.string[e.start],s=ft(i.replace(/\\.|-|"/g,(e=>"-"==e?Xe:'"'==e?'\\"':e))),r=[];for(let t=0;t<s.length;){let i=s.codePointAt(t);if(t+=i>65535?2:1,t<s.length-1&&s[t]==Xe){let n=s.codePointAt(t+1);t+=n>65535?3:2,n<i&&e.raise("Invalid character range",e.start),Ze(e,r,i,n+1)}else i==Xe.charCodeAt(0)&&(i=45),Ze(e,r,i,i+1)}return e.next(),new me(t,r.sort(((e,t)=>e[0]-t[0])),n)}if("at"!=e.type||"specialize"!=e.value&&"extend"!=e.value){if("at"==e.type&&ge.hasOwnProperty(e.value)){let t=new ye(e.start,e.value);return e.next(),t}if("["==e.type){let i=Ue(e,new ne(t,"_anon"));return i.params.length&&e.raise("Inline rules can't have parameters",i.start),new oe(t,i)}{let i=st(e);if("["==e.type||"{"==e.type){let n=Ue(e,i);return n.params.length&&e.raise("Inline rules can't have parameters",n.start),new oe(t,n)}if(e.eat(".")&&"std"==i.name&&ge.hasOwnProperty(e.value)){let i=new ye(t,e.value);return e.next(),i}return new re(t,i,function(e){let t=[];if(e.eat("<"))for(;!e.eat(">");)t.length&&e.expect(","),t.push(nt(e));return t}(e))}}{let{start:t,value:i}=e;e.next();let n=Qe(e);e.expect("<");let s,r=nt(e);return e.eat(",")?s=nt(e):r instanceof de?s=r:e.raise(`@${i} requires two arguments when its first argument isn't a literal string`),e.expect(">"),new ae(t,i,n,r,s)}}function Ze(e,t,i,n){t.every((([e,t])=>t<=i||e>=n))||e.raise("Overlapping character range",e.start),t.push([i,n])}function et(e){let t=e.start,i=Ke(e);for(;;){let n=e.type;if(!(e.eat("*")||e.eat("?")||e.eat("+")))return i;i=new ue(t,i,n)}}function tt(e){return"}"==e.type||")"==e.type||"|"==e.type||"/"==e.type||"/\\"==e.type||"{"==e.type||","==e.type||">"==e.type}function it(e){let t=e.start,i=[],n=[Je];do{for(;;){let t,i=e.start;if(e.eat("~"))t="ambig";else{if(!e.eat("!"))break;t="prec"}n[n.length-1]=n[n.length-1].concat(new he(i,st(e),t))}if(tt(e))break;i.push(et(e)),n.push(Je)}while(!tt(e));return 1==i.length&&n.every((e=>0==e.length))?i[0]:new ce(t,i,n,!i.length)}function nt(e){let t=e.start,i=it(e);if(!e.eat("|"))return i;let n=[i];do{n.push(it(e))}while(e.eat("|"));let s=n.find((e=>e instanceof ce&&e.empty));return s&&e.raise("Empty expression in choice operator. If this is intentional, use () to make it explicit.",s.start),new le(t,n)}function st(e){"id"!=e.type&&e.unexpected();let t=e.start,i=e.value;return e.next(),new ne(t,i)}function rt(e){let t=e.start;e.next(),e.expect("{");let i=[];for(;!e.eat("}");)i.length&&e.eat(","),i.push({id:st(e),type:e.eat("at","left")?"left":e.eat("at","right")?"right":e.eat("at","cut")?"cut":null});return new V(t,i)}function at(e){let t=e.start;e.next(),e.expect("{");let i=[],n=[],s=[],r=[];for(;!e.eat("}");)"at"==e.type&&"precedence"==e.value?s.push(lt(e)):"at"==e.type&&"conflict"==e.value?r.push(ct(e)):"string"==e.type?n.push(new X(e.start,e.expect("string"),Qe(e))):i.push(Ue(e));return new W(t,s,r,i,n)}function ot(e,t){e.expect("{");let i=[],n=[],s=null;for(;!e.eat("}");)"at"==e.type&&"precedence"==e.value?n.push(lt(e)):e.eat("at","else")&&!s?s={id:st(e),props:Qe(e)}:i.push(Ue(e));return new Y(t,n,i,s)}function lt(e){let t=e.start;e.next(),e.expect("{");let i=[];for(;!e.eat("}");){i.length&&e.eat(",");let t=Ke(e);t instanceof de||t instanceof re?i.push(t):e.raise("Invalid expression in token precedences",t.start)}return new U(t,i)}function ct(e){let t=e.start;e.next(),e.expect("{");let i=Ke(e);i instanceof de||i instanceof re||e.raise("Invalid expression in token conflict",i.start),e.eat(",");let n=Ke(e);return n instanceof de||n instanceof re||e.raise("Invalid expression in token conflict",n.start),e.expect("}"),new Q(t,i,n)}function ht(e){let t=[];for(e.expect("{");!e.eat("}");){t.length&&e.eat(",");let i=st(e),n=Qe(e);t.push({id:i,props:n})}return t}function ut(e,t){let i=st(e);e.expect("id","from");let n=e.expect("string");return new Z(t,i,n,ht(e))}function dt(e,t,i){let n=Ye(e),s=st(e);e.expect("id","from");let r=e.expect("string");return new ee(i,t,n,s,r,ht(e))}function mt(e,t){let i=st(e);return e.expect("id","from"),new te(t,i,e.expect("string"))}function pt(e,t){let i=st(e),n=e.eat("id","as")?st(e):i;e.expect("id","from");let s=e.expect("string");return new ie(t,n,i,s)}function ft(e){let t,i=/\\(?:u\{([\da-f]+)\}|u([\da-f]{4})|x([\da-f]{2})|([ntbrf0])|(.))|[^]/giy,n="";for(;t=i.exec(e);){let[e,i,s,r,a,o]=t;n+=i||s||r?String.fromCodePoint(parseInt(i||s||r,16)):a?"n"==a?"\n":"t"==a?"\t":"0"==a?"\0":"r"==a?"\r":"f"==a?"\f":"\b":o||e}return n}function gt(e,t){return(e<<5)+e+t}function yt(e,t){for(let i=0;i<t.length;i++)e=gt(e,t.charCodeAt(i));return e}const xt=void 0!==F&&F.env.LOG||"",bt=/\btime\b/.test(xt),_t=bt?(e,t)=>{let i=Date.now(),n=t();return console.log(`${e} (${((Date.now()-i)/1e3).toFixed(2)}s)`),n}:(e,t)=>t();class wt{constructor(e,t,i,n,s,r){this.rule=e,this.pos=t,this.ahead=i,this.ambigAhead=n,this.skipAhead=s,this.via=r,this.hash=0}finish(){let e=gt(gt(this.rule.id,this.pos),this.skipAhead.hash);for(let t of this.ahead)e=gt(e,t.hash);for(let t of this.ambigAhead)e=yt(e,t);return this.hash=e,this}get next(){return this.pos<this.rule.parts.length?this.rule.parts[this.pos]:null}advance(){return new wt(this.rule,this.pos+1,this.ahead,this.ambigAhead,this.skipAhead,this.via).finish()}get skip(){return this.pos==this.rule.parts.length?this.skipAhead:this.rule.skip}cmp(e){return this.rule.cmp(e.rule)||this.pos-e.pos||this.skipAhead.hash-e.skipAhead.hash||Te(this.ahead,e.ahead,((e,t)=>e.cmp(t)))||Te(this.ambigAhead,e.ambigAhead,St)}eqSimple(e){return e.rule==this.rule&&e.pos==this.pos}toString(){let e=this.rule.parts.map((e=>e.name));return e.splice(this.pos,0,"·"),`${this.rule.name} -> ${e.join(" ")}`}eq(e){return this==e||this.hash==e.hash&&this.rule==e.rule&&this.pos==e.pos&&this.skipAhead==e.skipAhead&&jt(this.ahead,e.ahead)&&jt(this.ambigAhead,e.ambigAhead)}trail(e=60){let t=[];for(let e=this;e;e=e.via)for(let i=e.pos-1;i>=0;i--)t.push(e.rule.parts[i]);let i=t.reverse().join(" ");return i.length>e&&(i=i.slice(i.length-e).replace(/.*? /,"… ")),i}conflicts(e=this.pos){let t=this.rule.conflicts[e];return e==this.rule.parts.length&&this.ambigAhead.length&&(t=t.join(new Pe(0,this.ambigAhead))),t}static addOrigins(e,t){let i=e.slice();for(let e=0;e<i.length;e++){let n=i[e];if(0==n.pos)for(let e of t)e.next!=n.rule.name||i.includes(e)||i.push(e)}return i}}function kt(e){let t=Pe.none;for(let i of e)t=t.join(i.conflicts());return t}function vt(e,t){for(let i of e)if(i.rule.name.repeated)for(let e of t)if(e.rule.name==i.rule.name){if(i.rule.isRepeatWrap&&2==i.pos)return 1;if(e.rule.isRepeatWrap&&2==e.pos)return-1}return 0}function St(e,t){return e<t?-1:e>t?1:0}function Nt(e,t,i,n){let s=[];for(let i=t+1;i<e.parts.length;i++){let t=e.parts[i],r=!1;if(t.terminal)Et(t,s);else for(let e of n[t.name])null==e?r=!0:Et(e,s);if(!r)return s}for(let e of i)Et(e,s);return s}function Ct(e,t){if(e.length!=t.length)return!1;for(let i=0;i<e.length;i++)if(!e[i].eq(t[i]))return!1;return!0}function jt(e,t){if(e.length!=t.length)return!1;for(let i=0;i<e.length;i++)if(e[i]!=t[i])return!1;return!0}class Tt{constructor(e,t){this.term=e,this.target=t}eq(e){return e instanceof Tt&&this.term==e.term&&e.target.id==this.target.id}cmp(e){return e instanceof Ot?-1:this.term.id-e.term.id||this.target.id-e.target.id}matches(e,t){return e instanceof Tt&&t[e.target.id]==t[this.target.id]}toString(){return"s"+this.target.id}map(e,t){let i=t[e[this.target.id]];return i==this.target?this:new Tt(this.term,i)}}class Ot{constructor(e,t){this.term=e,this.rule=t}eq(e){return e instanceof Ot&&this.term==e.term&&e.rule.sameReduce(this.rule)}cmp(e){return e instanceof Tt?1:this.term.id-e.term.id||this.rule.name.id-e.rule.name.id||this.rule.parts.length-e.rule.parts.length}matches(e,t){return e instanceof Ot&&e.rule.sameReduce(this.rule)}toString(){return`${this.rule.name.name}(${this.rule.parts.length})`}map(){return this}}function Pt(e){let t=5381;for(let i of e)t=gt(t,i.hash);return t}class $t{constructor(e,t,i=0,n,s=Pt(t),r=null){this.id=e,this.set=t,this.flags=i,this.skip=n,this.hash=s,this.startRule=r,this.actions=[],this.actionPositions=[],this.goto=[],this.tokenGroup=-1,this.defaultReduce=null,this._actionsByTerm=null}toString(){let e=this.actions.map((e=>e.term+"="+e)).join(",")+(this.goto.length?" | "+this.goto.map((e=>e.term+"="+e)).join(","):"");return this.id+": "+this.set.filter((e=>e.pos>0)).join()+(this.defaultReduce?`\n  always ${this.defaultReduce.name}(${this.defaultReduce.parts.length})`:e.length?"\n  "+e:"")}addActionInner(e,t){e:for(let i=0;i<this.actions.length;i++){let n=this.actions[i];if(n.term==e.term){if(n.eq(e))return null;let s=wt.addOrigins(t,this.set),r=wt.addOrigins(this.actionPositions[i],this.set),a=kt(s),o=kt(r),l=vt(s,r)||a.precedence-o.precedence;if(l>0){this.actions.splice(i,1),this.actionPositions.splice(i,1),i--;continue e}if(l<0)return null;if(a.ambigGroups.some((e=>o.ambigGroups.includes(e))))continue e;return n}}return this.actions.push(e),this.actionPositions.push(t),null}addAction(e,t,i){let n=this.addActionInner(e,t);if(n){let s,r=this.actionPositions[this.actions.indexOf(n)][0],a=[t[0].rule.name,r.rule.name];if(i.some((e=>e.rules.some((e=>a.includes(e))))))return;s=n instanceof Tt?`shift/reduce conflict between\n  ${r}\nand\n  ${t[0].rule}`:`reduce/reduce conflict between\n  ${r.rule}\nand\n  ${t[0].rule}`,s+=`\nWith input:\n  ${t[0].trail(70)} · ${e.term} …`,s+=function(e,t){if(e.eqSimple(t))return"";function i(e,t){let i=[];for(let n=t.via;!n.eqSimple(e);n=n.via)i.push(n);return i.length?(i.unshift(t),i.reverse().map(((e,i)=>"\n"+"  ".repeat(i+1)+(e==t?"":"via ")+e)).join("")):""}for(let n=e;n;n=n.via)for(let s=t;s;s=s.via)if(n.eqSimple(s))return"\nShared origin: "+n+i(n,e)+i(n,t);return""}(r,t[0]),i.push(new At(s,a))}}getGoto(e){return this.goto.find((t=>t.term==e))}hasSet(e){return Ct(this.set,e)}actionsByTerm(){let e=this._actionsByTerm;if(!e){this._actionsByTerm=e=Object.create(null);for(let t of this.actions)(e[t.term.id]||(e[t.term.id]=[])).push(t)}return e}finish(){if(this.actions.length){let e=this.actions[0];if(e instanceof Ot){let{rule:t}=e;this.actions.every((e=>e instanceof Ot&&e.rule.sameReduce(t)))&&(this.defaultReduce=t)}}this.actions.sort(((e,t)=>e.cmp(t))),this.goto.sort(((e,t)=>e.cmp(t)))}eq(e){let t=this.defaultReduce,i=e.defaultReduce;return t||i?!(!t||!i)&&t.sameReduce(i):this.skip==e.skip&&this.tokenGroup==e.tokenGroup&&Ct(this.actions,e.actions)&&Ct(this.goto,e.goto)}}function Et(e,t){t.includes(e)||t.push(e)}class Mt{constructor(e,t){this.set=e,this.state=t}}class At{constructor(e,t){this.error=e,this.rules=t}}function It(e){let t=null,i=1;for(let n of e){let e=n.rule.conflicts[n.pos-1].cut;e<i||((!t||e>i)&&(i=e,t=[]),t.push(n))}return t||e}function Lt(e,t,i){for(let n of e.goto)for(let e of t.goto)if(n.term==e.term&&i[n.target.id]!=i[e.target.id])return!1;let n=t.actionsByTerm();for(let t of e.actions){let s=n[t.term.id];if(s&&s.some((e=>!e.matches(t,i)))){if(1==s.length)return!1;let n=e.actionsByTerm()[t.term.id];if(n.length!=s.length||n.some((e=>!s.some((t=>e.matches(t,i))))))return!1}}return!0}function Rt(e,t){let i=[];for(let n of e){let e=t[n.id];i[e]||(i[e]=new $t(e,n.set,0,n.skip,n.hash,n.startRule),i[e].tokenGroup=n.tokenGroup,i[e].defaultReduce=n.defaultReduce)}for(let n of e){let e=i[t[n.id]];e.flags|=n.flags;for(let s=0;s<n.actions.length;s++){let r=n.actions[s].map(t,i);e.actions.some((e=>e.eq(r)))||(e.actions.push(r),e.actionPositions.push(n.actionPositions[s]))}for(let s of n.goto){let n=s.map(t,i);e.goto.some((e=>e.eq(n)))||e.goto.push(n)}}return i}class Dt{constructor(e,t){this.origin=e,this.members=[t]}}function zt(e,t){if(e.length!=t.length)return!1;for(let i=0;i<e.length;i++)if(!e[i].eqSimple(t[i]))return!1;return!0}const qt=[];function Bt(e){let t=e+32;return t>=34&&t++,t>=92&&t++,String.fromCharCode(t)}function Ft(e,t=65535){if(e>t)throw new Error("Trying to encode a number that's too big: "+e);if(65535==e)return String.fromCharCode(126);let i="";for(let t=46;;t=0){let n=e%46,s=e-n;if(i=Bt(n+t)+i,0==s)break;e=s/46}return i}function Ht(e,t=65535){let i='"'+Ft(e.length,4294967295);for(let n=0;n<e.length;n++)i+=Ft(e[n],t);return i+='"',i}const Gt=[];class Jt{constructor(e,t){this.terms=e,this.conflicts=t}concat(e){if(this==Jt.none)return e;if(e==Jt.none)return this;let t=null;if(this.conflicts||e.conflicts){t=this.conflicts?this.conflicts.slice():this.ensureConflicts();let i=e.ensureConflicts();t[t.length-1]=t[t.length-1].join(i[0]);for(let e=1;e<i.length;e++)t.push(i[e])}return new Jt(this.terms.concat(e.terms),t)}withConflicts(e,t){if(t==Pe.none)return this;let i=this.conflicts?this.conflicts.slice():this.ensureConflicts();return i[e]=i[e].join(t),new Jt(this.terms,i)}ensureConflicts(){if(this.conflicts)return this.conflicts;let e=[];for(let t=0;t<=this.terms.length;t++)e.push(Pe.none);return e}}function Vt(...e){return new Jt(e,null)}Jt.none=new Jt(Gt,null);class Ut{constructor(e,t,i){this.id=e,this.args=t,this.term=i}matches(e){return this.id==e.id.name&&be(e.args,this.args)}matchesRepeat(e){return"+"==this.id&&xe(e.expr,this.args[0])}}class Qt{constructor(e,t){this.options=t,this.terms=new je,this.specialized=Object.create(null),this.tokenOrigins=Object.create(null),this.rules=[],this.built=[],this.ruleNames=Object.create(null),this.namespaces=Object.create(null),this.namedTerms=Object.create(null),this.termTable=Object.create(null),this.knownProps=Object.create(null),this.dynamicRulePrecedences=[],this.definedGroups=[],this.astRules=[],this.currentSkip=[],_t("Parse",(()=>{this.input=new Ve(e,t.fileName),this.ast=this.input.parse()}));let i=g.NodeProp;for(let e in i)i[e]instanceof g.NodeProp&&!i[e].perNode&&(this.knownProps[e]={prop:i[e],source:{name:e,from:null}});for(let e of this.ast.externalProps)this.knownProps[e.id.name]={prop:this.options.externalProp?this.options.externalProp(e.id.name):new g.NodeProp,source:{name:e.externalID.name,from:e.source}};this.dialects=this.ast.dialects.map((e=>e.name)),this.tokens=new oi(this,this.ast.tokens),this.localTokens=this.ast.localTokens.map((e=>new li(this,e))),this.externalTokens=this.ast.externalTokens.map((e=>new _i(this,e))),this.externalSpecializers=this.ast.externalSpecializers.map((e=>new wi(this,e))),_t("Build rules",(()=>{let e=this.newName("%noskip",!0);this.defineRule(e,[]);let t=this.ast.mainSkip?this.newName("%mainskip",!0):e,i=[],n=[];for(let e of this.ast.rules)this.astRules.push({skip:t,rule:e});for(let e of this.ast.topRules)n.push({skip:t,rule:e});for(let s of this.ast.scopedSkip){let r=e,a=this.ast.scopedSkip.findIndex(((e,t)=>t<i.length&&xe(e.expr,s.expr)));a>-1?r=i[a]:this.ast.mainSkip&&xe(s.expr,this.ast.mainSkip)?r=t:gi(s.expr)||(r=this.newName("%skip",!0)),i.push(r);for(let e of s.rules)this.astRules.push({skip:r,rule:e});for(let e of s.topRules)n.push({skip:r,rule:e})}for(let{rule:e}of this.astRules)this.unique(e.id);this.currentSkip.push(e),this.skipRules=t==e?[t]:[e,t],t!=e&&this.defineRule(t,this.normalizeExpr(this.ast.mainSkip));for(let t=0;t<this.ast.scopedSkip.length;t++){let n=i[t];this.skipRules.includes(n)||(this.skipRules.push(n),n!=e&&this.defineRule(n,this.normalizeExpr(this.ast.scopedSkip[t].expr)))}this.currentSkip.pop();for(let{rule:e,skip:t}of n.sort(((e,t)=>e.rule.start-t.rule.start))){this.unique(e.id),this.used(e.id.name),this.currentSkip.push(t);let{name:i,props:n}=this.nodeInfo(e.props,"a",e.id.name,Gt,Gt,e.expr),s=this.terms.makeTop(i,n);this.namedTerms[i]=s,this.defineRule(s,this.normalizeExpr(e.expr)),this.currentSkip.pop()}for(let e of this.externalSpecializers)e.finish();for(let{skip:e,rule:t}of this.astRules)this.ruleNames[t.id.name]&&Si(t)&&!t.params.length&&(this.buildRule(t,[],e,!1),t.expr instanceof ce&&0==t.expr.exprs.length&&this.used(t.id.name))}));for(let e in this.ruleNames){let t=this.ruleNames[e];t&&this.warn(`Unused rule '${t.name}'`,t.start)}this.tokens.takePrecedences(),this.tokens.takeConflicts();for(let e of this.localTokens)e.takePrecedences();for(let{name:e,group:t,rule:i}of this.definedGroups)this.defineGroup(e,t,i);this.checkGroups()}unique(e){e.name in this.ruleNames&&this.raise(`Duplicate definition of rule '${e.name}'`,e.start),this.ruleNames[e.name]=e}used(e){this.ruleNames[e]=null}newName(e,t=null,i={}){for(let n=t?0:1;;n++){let s=n?`${e}-${n}`:e;if(!this.terms.names[s])return this.terms.makeNonTerminal(s,!0===t?null:t,i)}}prepareParser(){let e=_t("Simplify rules",(()=>function(e,t){return function(e){let t,i=Object.create(null);for(let n=0;n<e.length;){let s=n,r=e[n++].name;for(;n<e.length&&e[n].name==r;)n++;let a=n-s;if(!r.interesting)for(let o=n;o<e.length;){let n=o,l=e[o++].name;for(;o<e.length&&e[o].name==l;)o++;if(o-n!=a||l.interesting)continue;let c=!0;for(let t=0;t<a&&c;t++){let i=e[s+t],r=e[n+t];0!=i.cmpNoName(r)&&(c=!1)}c&&(t=i[r.name]=l)}}if(!t)return e;let n=[];for(let t of e)i[t.name.name]||n.push(t.parts.every((e=>!i[e.name]))?t:new Me(t.name,t.parts.map((e=>i[e.name]||e)),t.conflicts,t.skip));return n}(function(e,t){for(let i=0;;i++){let n,s=Object.create(null);if(0==i)for(let a of e)if(a.name.inline&&!s[a.name.name]){let o=e.filter((e=>e.name==a.name));if(o.some((e=>e.parts.includes(a.name))))continue;n=s[a.name.name]=o}for(let l=0;l<e.length;l++){let c=e[l];c.name.interesting||c.parts.includes(c.name)||!(c.parts.length<3)||t.includes(c.name)||1!=c.parts.length&&!e.every((e=>e.skip==c.skip||!e.parts.includes(c.name)))||c.parts.some((e=>!!s[e.name]))||e.some(((e,t)=>t!=l&&e.name==c.name))||(n=s[c.name.name]=[c])}if(!n)return e;let r=[];for(let h of e){function u(e,t,i){if(e==h.parts.length)return void r.push(new Me(h.name,i,t,h.skip));let n=h.parts[e],a=s[n.name];if(a)for(let n of a)u(e+1,t.slice(0,t.length-1).concat(t[e].join(n.conflicts[0])).concat(n.conflicts.slice(1,n.conflicts.length-1)).concat(h.conflicts[e+1].join(n.conflicts[n.conflicts.length-1])),i.concat(n.parts));else u(e+1,t.concat(h.conflicts[e+1]),i.concat(n))}s[h.name.name]||(h.parts.some((e=>!!s[e.name]))?u(0,[h.conflicts[0]],[]):r.push(h))}e=r}}(e,t))}(this.rules,[...this.skipRules,...this.terms.tops]))),{nodeTypes:t,names:i,minRepeatTerm:n,maxTerm:s}=this.terms.finish(e);for(let e in this.namedTerms)this.termTable[e]=this.namedTerms[e].id;/\bgrammar\b/.test(xt)&&console.log(e.join("\n"));let r=this.terms.tops.slice(),a=function(e){let t=Object.create(null);for(let i of e.terms)i.terminal||(t[i.name]=[]);for(;;){let i=!1;for(let n of e.terms)if(!n.terminal)for(let e of n.rules){let s=t[n.name],r=!1,a=s.length;for(let i of e.parts){if(r=!0,i.terminal)Et(i,s);else for(let e of t[i.name])null==e?r=!1:Et(e,s);if(r)break}r||Et(null,s),s.length>a&&(i=!0)}if(!i)return t}}(this.terms),o=this.skipRules.map(((e,t)=>{let i=[],n=[],s=[];for(let t of e.rules){if(!t.parts.length)continue;let e=t.parts[0];for(let t of e.terminal?[e]:a[e.name]||[])n.includes(t)||n.push(t);e.terminal&&1==t.parts.length&&!s.some((i=>i!=t&&i.parts[0]==e))?i.push(e):s.push(t)}return e.rules=s,s.length&&r.push(e),{skip:i,rule:s.length?e:null,startTokens:n,id:t}})),l=_t("Build full automaton",(()=>function(e,t,i){let n=[],s={},r={},a=Date.now();function o(e,t){if(0==e.length)return null;let o,l=Pt(e),c=r[l];for(let t of e)if(o){if(o!=t.skip)throw new ve("Inconsistent skip sets after "+t.trail())}else o=t.skip;if(c)for(let t of c)if(Ct(e,t.set)){if(t.state.skip!=o)throw new ve("Inconsistent skip sets after "+t.set[0].trail());return t.state}let h,u=function(e,t){let i=[],n=[];function s(t,s,r,a,o){for(let l of t.rules){let t=i.find((e=>e.rule==l));if(!t){let n=e.find((e=>0==e.pos&&e.rule==l));t=n?new wt(l,0,n.ahead.slice(),n.ambigAhead,n.skipAhead,n.via):new wt(l,0,[],qt,a,o),i.push(t)}if(t.skipAhead!=a)throw new ve("Inconsistent skip sets after "+o.trail());t.ambigAhead=$e(t.ambigAhead,r);for(let e of s)t.ahead.includes(e)||(t.ahead.push(e),t.rule.parts.length&&!t.rule.parts[0].terminal&&Et(t,n))}}for(let i of e){let e=i.next;e&&!e.terminal&&s(e,Nt(i.rule,i.pos,i.ahead,t),i.conflicts(i.pos+1).ambigGroups,i.pos==i.rule.parts.length-1?i.skipAhead:i.rule.skip,i)}for(;n.length;){let e=n.pop();s(e.rule.parts[0],Nt(e.rule,0,e.ahead,t),$e(e.rule.conflicts[1].ambigGroups,1==e.rule.parts.length?e.ambigAhead:qt),1==e.rule.parts.length?e.skipAhead:e.rule.skip,e)}let r=e.slice();for(let t of i){t.ahead.sort(((e,t)=>e.hash-t.hash)),t.finish();let i=e.findIndex((e=>0==e.pos&&e.rule==t.rule));i>-1?r[i]=t:r.push(t)}return r.sort(((e,t)=>e.cmp(t)))}(e,i),d=Pt(u),m=s[d]||(s[d]=[]);if(!t)for(let e of m)e.hasSet(u)&&(h=e);return h||(h=new $t(n.length,u,0,o,d,t),m.push(h),n.push(h),bt&&n.length%500==0&&console.log(`${n.length} states after ${((Date.now()-a)/1e3).toFixed(2)}s`)),(r[l]||(r[l]=[])).push(new Mt(e,h)),h}for(const i of t){const t=i.rules.length?i.rules[0].skip:e.names["%noskip"];o(i.rules.map((i=>new wt(i,0,[e.eof],qt,t,null).finish())),i)}let l=[];for(let e=0;e<n.length;e++){let t=n[e],s=[],r=[],a=[];for(let e of t.set)if(e.pos==e.rule.parts.length)e.rule.name.top||a.push(e);else{let t=e.rule.parts[e.pos],i=s.indexOf(t);i<0?(s.push(t),r.push([e])):r[i].push(e)}for(let e=0;e<s.length;e++){let i=s[e],n=r[e].map((e=>e.advance()));if(i.terminal){let s=o(It(n));s&&t.addAction(new Tt(i,s),r[e],l)}else{let e=o(n);e&&t.goto.push(new Tt(i,e))}}let c=!1;for(let e of a)for(let i of e.ahead){let n=t.actions.length;t.addAction(new Ot(i,e.rule),[e],l),t.actions.length==n&&(c=!0)}if(c)for(let e=0;e<t.goto.length;e++)i[t.goto[e].term.name].some((e=>t.actions.some((t=>t.term==e&&t instanceof Tt))))||t.goto.splice(e--,1)}if(l.length)throw new ve(l.map((e=>e.error)).join("\n\n"));for(let e of n)e.finish();return bt&&console.log(`${n.length} states total.`),n}(this.terms,r,a))),c=this.localTokens.map(((e,t)=>e.buildLocalGroup(l,o,t))),{tokenGroups:h,tokenPrec:u,tokenData:d}=_t("Build token groups",(()=>this.tokens.buildTokenGroups(l,o,c.length))),m=_t("Finish automaton",(()=>function(e){for(let t=1;;t++){let i=[],n=!1,s=Date.now(),r=[];for(let t=0;t<e.length;t++){let s=e[t],a=r.findIndex((e=>s.eq(e)));if(a<0)i[t]=r.length,r.push(s);else{i[t]=a,n=!0;let e=r[a],o=null;for(let t of s.set)e.set.some((e=>e.eqSimple(t)))||(o||(o=[])).push(t);o&&(e.set=o.concat(e.set).sort(((e,t)=>e.cmp(t))))}}if(bt&&console.log(`Merge identical pass ${t}${n?"":", done"} (${((Date.now()-s)/1e3).toFixed(2)}s)`),!n)return e;for(let e of r)e.defaultReduce||(e.actions=e.actions.map((e=>e.map(i,r))),e.goto=e.goto.map((e=>e.map(i,r))));for(let e=0;e<r.length;e++)r[e].id=e;e=r}}(function(e){let t=[],i=[];e:for(let n=0;n<e.length;n++){let s=e[n];if(!s.startRule)for(let r=0;r<i.length;r++){let a=i[r],o=e[a.members[0]];if(s.tokenGroup==o.tokenGroup&&s.skip==o.skip&&!o.startRule&&zt(s.set,o.set)){a.members.push(n),t.push(r);continue e}}t.push(i.length),i.push(new Dt(i.length,n))}function n(n,s){let r=i[n],a=e[r.members[s]],o=r.members.pop();s!=r.members.length&&(r.members[s]=o);for(let s=n+1;s<i.length;s++)if(t[a.id]=s,i[s].origin==r.origin&&i[s].members.every((i=>Lt(a,e[i],t))))return void i[s].members.push(a.id);t[a.id]=i.length,i.push(new Dt(r.origin,a.id))}for(let s=1;;s++){let r=!1,a=Date.now();for(let s=0,a=i.length;s<a;s++){let a=i[s];for(let i=0;i<a.members.length-1;i++)for(let o=i+1;o<a.members.length;o++){let l=a.members[i],c=a.members[o];Lt(e[l],e[c],t)||(r=!0,n(s,o--))}}if(bt&&console.log(`Collapse pass ${s}${r?"":", done"} (${((Date.now()-a)/1e3).toFixed(2)}s)`),!r)return Rt(e,t)}}(l)))),p=function(e,t){let i=Object.create(null),n=[],s=e=>{i[e.id]||(i[e.id]=!0,n.push(e))};for(let i of e)i.startRule&&t.includes(i.startRule)&&s(i);for(let e=0;e<n.length;e++){for(let t of n[e].actions)t instanceof Tt&&s(t.target);for(let t of n[e].goto)s(t.target)}return e=>!i[e]}(m,this.terms.tops);/\blr\b/.test(xt)&&console.log(m.join("\n"));let f=[];for(let e of this.externalSpecializers)f.push(e);for(let e in this.specialized)f.push({token:this.terms.names[e],table:Xt(this.specialized[e])});let g=e=>e instanceof _i?e.ast.start:this.tokens.ast?this.tokens.ast.start:-1,y=h.concat(this.externalTokens).sort(((e,t)=>g(e)-g(t))).concat(c),x=new Zt,b=o.map((e=>{let t=[];for(let i of e.skip)t.push(i.id,0,4);if(e.rule){let i=m.find((t=>t.startRule==e.rule));for(let e of i.actions)t.push(e.term.id,i.id,2)}return t.push(65535,0),x.storeArray(t)})),_=_t("Finish states",(()=>{let e=new Uint32Array(6*m.length),t=this.computeForceReductions(m,o),i=new Wt(y,x,e,b,o,m,this);for(let e of m)i.finish(e,p(e.id),t[e.id]);return e})),w=Object.create(null);for(let e=0;e<this.dialects.length;e++)w[this.dialects[e]]=x.storeArray((this.tokens.byDialect[e]||Gt).map((e=>e.id)).concat(65535));let k=null;if(this.dynamicRulePrecedences.length){k=Object.create(null);for(let{rule:e,prec:t}of this.dynamicRulePrecedences)k[e.id]=t}let v=Object.create(null);for(let e of this.terms.tops)v[e.nodeName]=[m.find((t=>t.startRule==e)).id,e.id];let S=x.storeArray(u.concat(65535)),{nodeProps:N,skippedTypes:C}=this.gatherNodeProps(t);return{states:_,stateData:x.finish(),goto:ei(m),nodeNames:t.filter((e=>e.id<n)).map((e=>e.nodeName)).join(" "),nodeProps:N,skippedTypes:C,maxTerm:s,repeatNodeCount:t.length-n,tokenizers:y,tokenData:d,topRules:v,dialects:w,dynamicPrecedences:k,specialized:f,tokenPrec:S,termNames:i}}getParser(){let{states:e,stateData:t,goto:i,nodeNames:n,nodeProps:s,skippedTypes:r,maxTerm:a,repeatNodeCount:o,tokenizers:l,tokenData:c,topRules:h,dialects:u,dynamicPrecedences:d,specialized:m,tokenPrec:p,termNames:f}=this.prepareParser(),g=m.map((e=>{if(e instanceof wi){let t=this.options.externalSpecializer(e.ast.id.name,this.termTable);return{term:e.term.id,get:(i,n)=>t(i,n)<<1|("extend"==e.ast.type?1:0),external:t,extend:"extend"==e.ast.type}}return{term:e.token.id,get:t=>e.table[t]||-1}}));return B.WQ.deserialize({version:14,states:e,stateData:t,goto:i,nodeNames:n,maxTerm:a,repeatNodeCount:o,nodeProps:s.map((({prop:e,terms:t})=>[this.knownProps[e].prop,...t])),propSources:this.options.externalPropSource?this.ast.externalPropSources.map((e=>this.options.externalPropSource(e.id.name))):void 0,skippedNodes:r,tokenData:c,tokenizers:l.map((e=>e.create())),context:this.ast.context?"function"==typeof this.options.contextTracker?this.options.contextTracker(this.termTable):this.options.contextTracker:void 0,topRules:h,dialects:u,dynamicPrecedences:d,specialized:g,tokenPrec:p,termNames:f})}getParserFile(){let{states:e,stateData:t,goto:i,nodeNames:n,nodeProps:s,skippedTypes:r,maxTerm:a,repeatNodeCount:o,tokenizers:l,tokenData:c,topRules:h,dialects:u,dynamicPrecedences:d,specialized:m,tokenPrec:p,termNames:f}=this.prepareParser(),g=this.options.moduleStyle||"es",y=this.options.typeScript?": any":"",x="// This file was generated by lezer-generator. You probably shouldn't edit it.\n",b=x,_={},w=Object.create(null),k=Object.create(null);for(let e of vi)k[e]=!0;let v=this.options.exportName||"parser";k[v]=!0;let S=e=>{for(let t=0;;t++){let i=e+(t?"_"+t:"");if(!k[i])return i}},N=(e,t,i=e)=>{let n=e+" from "+t;if(w[n])return w[n];let s=JSON.stringify(t),r=e;return e in k&&(r=S(i),e+=`${"cjs"==g?":":" as"} ${r}`),k[r]=!0,(_[s]||(_[s]=[])).push(e),w[n]=r},C=N("LRParser","@lezer/lr"),j=l.map((e=>e.createSource(N))),T=this.ast.context?N(this.ast.context.id.name,this.ast.context.source):null,O=s.map((({prop:e,terms:t})=>{let{source:i}=this.knownProps[e];return`[${i.from?N(i.name,i.from):JSON.stringify(i.name)}, ${t.map(M).join(",")}]`})),P="",$=m.map((e=>{if(e instanceof wi){let t=N(e.ast.id.name,e.ast.source);return`{term: ${e.term.id}, get: (value${y}, stack${y}) => (${t}(value, stack) << 1)${"extend"==e.ast.type?" | 1":""}, external: ${t}${"extend"==e.ast.type?", extend: true":""}}`}{let i=S("spec_"+e.token.name.replace(/\W/g,""));return k[i]=!0,P+=`const ${i} = ${t=e.table,"{__proto__:null,"+Object.keys(t).map((e=>`${/\W/.test(e)?JSON.stringify(e):e}:${t[e]}`)).join(", ")+"}"}\n`,`{term: ${e.token.id}, get: (value${y}) => ${i}[value] || -1}`}var t})),E=this.ast.externalPropSources.map((e=>N(e.id.name,e.source)));for(let e in _)b+="cjs"==g?`const {${_[e].join(", ")}} = require(${e})\n`:`import {${_[e].join(", ")}} from ${e}\n`;function M(e){return"string"!=typeof e||/^(true|false|\d+(\.\d+)?|\.\d+)$/.test(e)?e:JSON.stringify(e)}b+=P;let A=Object.keys(u).map((e=>`${e}: ${u[e]}`)),I=`${C}.deserialize({\n  version: 14,\n  states: ${Ht(e,4294967295)},\n  stateData: ${Ht(t)},\n  goto: ${Ht(i)},\n  nodeNames: ${JSON.stringify(n)},\n  maxTerm: ${a}${T?`,\n  context: ${T}`:""}${O.length?`,\n  nodeProps: [\n    ${O.join(",\n    ")}\n  ]`:""}${E.length?`,\n  propSources: [${E.join()}]`:""}${r.length?`,\n  skippedNodes: ${JSON.stringify(r)}`:""},\n  repeatNodeCount: ${o},\n  tokenData: ${Ht(c)},\n  tokenizers: [${j.join(", ")}],\n  topRules: ${JSON.stringify(h)}${A.length?`,\n  dialects: {${A.join(", ")}}`:""}${d?`,\n  dynamicPrecedences: ${JSON.stringify(d)}`:""}${$.length?`,\n  specialized: [${$.join(",")}]`:""},\n  tokenPrec: ${p}${this.options.includeNames?`,\n  termNames: ${JSON.stringify(f)}`:""}\n})`,L=[];for(let e in this.termTable){let t=e;if(vi.includes(t))for(let i=1;t="_".repeat(i)+e,t in this.termTable;i++);L.push(`${t}${"cjs"==g?":":" ="} ${this.termTable[e]}`)}for(let e=0;e<this.dialects.length;e++)L.push(`Dialect_${this.dialects[e]}${"cjs"==g?":":" ="} ${e}`);return{parser:b+("cjs"==g?`exports.${v} = ${I}\n`:`export const ${v} = ${I}\n`),terms:"cjs"==g?`${x}module.exports = {\n  ${L.join(",\n  ")}\n}`:`${x}export const\n  ${L.join(",\n  ")}\n`}}gatherNonSkippedNodes(){let e=Object.create(null),t=[],i=i=>{e[i.id]||(e[i.id]=!0,t.push(i))};this.terms.tops.forEach(i);for(let e=0;e<t.length;e++)for(let n of t[e].rules)for(let e of n.parts)i(e);return e}gatherNodeProps(e){let t=this.gatherNonSkippedNodes(),i=[],n=[];for(let s of e){t[s.id]||s.error||i.push(s.id);for(let e in s.props){let t=this.knownProps[e];if(!t)throw new ve("No known prop type for "+e);if(null==t.source.from&&("repeated"==t.source.name||"error"==t.source.name))continue;let i=n.find((t=>t.prop==e));i||n.push(i={prop:e,values:{}}),(i.values[s.props[e]]||(i.values[s.props[e]]=[])).push(s.id)}}return{nodeProps:n.map((({prop:e,values:t})=>{let i=[];for(let e in t){let n=t[e];if(1==n.length)i.push(n[0],e);else{i.push(-n.length);for(let e of n)i.push(e);i.push(e)}}return{prop:e,terms:i}})),skippedTypes:i}}makeTerminal(e,t,i){return this.terms.makeTerminal(this.terms.uniqueName(e),t,i)}computeForceReductions(e,t){let i=[],n=[],s=Object.create(null);for(let t of e){i.push(0);for(let e of t.goto){let i=s[e.term.id]||(s[e.term.id]=[]),n=i.find((t=>t.target==e.target.id));n?n.parents.push(t.id):i.push({parents:[t.id],target:e.target.id})}n[t.id]=t.set.filter((e=>e.pos>0&&!e.rule.name.top)).sort(((e,t)=>t.pos-e.pos||e.rule.parts.length-t.rule.parts.length))}let r=Object.create(null);function a(e,t,i=null){let n=s[e];return!!n&&n.some((e=>{let n=i?i.filter((t=>e.parents.includes(t))):e.parents;if(0==n.length)return!1;if(e.target==t)return!0;let s=r[e.target];return null!=s&&a(s,t,n)}))}for(let n of e)n.defaultReduce&&n.defaultReduce.parts.length>0&&(i[n.id]=Kt(n.defaultReduce,t),1==n.defaultReduce.parts.length&&(r[n.id]=n.defaultReduce.name.id));for(let s=1;;s++){let o=!0;for(let l of e){if(l.defaultReduce)continue;let e=n[l.id];if(e.length==s){for(let n of e)if(1!=n.pos||!a(n.rule.name.id,l.id)){i[l.id]=Kt(n.rule,t,n.pos),1==n.pos&&(r[l.id]=n.rule.name.id);break}}else e.length>s&&(o=!1)}if(o)break}return i}substituteArgs(e,t,i){return 0==t.length?e:e.walk((e=>{let n;if(e instanceof re&&(n=i.findIndex((t=>t.name==e.id.name)))>-1){let i=t[n];if(e.args.length){if(i instanceof re&&!i.args.length)return new re(e.start,i.id,e.args);this.raise("Passing arguments to a parameter that already has arguments",e.start)}return i}if(e instanceof oe){let n=e.rule,s=this.substituteArgsInProps(n.props,t,i);return s==n.props?e:new oe(e.start,new J(n.start,n.id,s,n.params,n.expr))}if(e instanceof ae){let n=this.substituteArgsInProps(e.props,t,i);return n==e.props?e:new ae(e.start,e.type,n,e.token,e.content)}return e}))}substituteArgsInProps(e,t,i){let n=e=>{let n=e;for(let s=0;s<e.length;s++){let r=e[s];if(!r.name)continue;let a=i.findIndex((e=>e.name==r.name));if(a<0)continue;n==e&&(n=e.slice());let o=t[a];o instanceof re&&!o.args.length?n[s]=new we(r.start,o.id.name,null):o instanceof de?n[s]=new we(r.start,o.value,null):this.raise(`Trying to interpolate expression '${o}' into a prop`,r.start)}return n},s=e;for(let t=0;t<e.length;t++){let i=e[t],r=n(i.value);r!=i.value&&(s==e&&(s=e.slice()),s[t]=new _e(i.start,i.at,i.name,r))}return s}conflictsFor(e){let t=Pe.none,i=Pe.none;for(let n of e)if("ambig"==n.type)t=t.join(new Pe(0,[n.id.name]));else{let e=this.ast.precedences,s=e?e.items.findIndex((e=>e.id.name==n.id.name)):-1;s<0&&this.raise(`Reference to unknown precedence: '${n.id.name}'`,n.id.start);let r=e.items[s],a=e.items.length-s;"cut"==r.type?t=t.join(new Pe(0,Gt,a)):(t=t.join(new Pe(a<<2)),i=i.join(new Pe((a<<2)+("left"==r.type?1:"right"==r.type?-1:0))))}return{here:t,atEnd:i}}raise(e,t=1){return this.input.raise(e,t)}warn(e,t=-1){let i=this.input.message(e,t);this.options.warn?this.options.warn(i):console.warn(i)}defineRule(e,t){let i=this.currentSkip[this.currentSkip.length-1];for(let n of t)this.rules.push(new Me(e,n.terms,n.ensureConflicts(),i))}resolve(e){for(let t of this.built)if(t.matches(e))return[Vt(t.term)];let t=this.tokens.getToken(e);if(t)return[Vt(t)];for(let t of this.localTokens){let i=t.getToken(e);if(i)return[Vt(i)]}for(let t of this.externalTokens){let i=t.getToken(e);if(i)return[Vt(i)]}for(let t of this.externalSpecializers){let i=t.getToken(e);if(i)return[Vt(i)]}let i=this.astRules.find((t=>t.rule.id.name==e.id.name));return i?(i.rule.params.length!=e.args.length&&this.raise(`Wrong number or arguments for '${e.id.name}'`,e.start),this.used(i.rule.id.name),[Vt(this.buildRule(i.rule,e.args,i.skip))]):this.raise(`Reference to undefined rule '${e.id.name}'`,e.start)}normalizeRepeat(e){let t=this.built.find((t=>t.matchesRepeat(e)));if(t)return Vt(t.term);let i=e.expr.prec<e.prec?`(${e.expr})+`:`${e.expr}+`,n=this.terms.makeRepeat(this.terms.uniqueName(i));return this.built.push(new Ut("+",[e.expr],n)),this.defineRule(n,this.normalizeExpr(e.expr).concat(Vt(n,n))),Vt(n)}normalizeSequence(e){let t=e.exprs.map((e=>this.normalizeExpr(e))),i=this;return function n(s,r,a){let{here:o,atEnd:l}=i.conflictsFor(e.markers[r]);if(r==t.length)return[s.withConflicts(s.terms.length,o.join(a))];let c=[];for(let e of t[r])for(let t of n(s.concat(e).withConflicts(s.terms.length,o),r+1,a.join(l)))c.push(t);return c}(Jt.none,0,Pe.none)}normalizeExpr(e){if(e instanceof ue&&"?"==e.kind)return[Jt.none,...this.normalizeExpr(e.expr)];if(e instanceof ue){let t=this.normalizeRepeat(e);return"+"==e.kind?[t]:[Jt.none,t]}return e instanceof le?e.exprs.reduce(((e,t)=>e.concat(this.normalizeExpr(t))),[]):e instanceof ce?this.normalizeSequence(e):e instanceof de?[Vt(this.tokens.getLiteral(e))]:e instanceof re?this.resolve(e):e instanceof ae?[Vt(this.resolveSpecialization(e))]:e instanceof oe?[Vt(this.buildRule(e.rule,Gt,this.currentSkip[this.currentSkip.length-1],!0))]:this.raise(`This type of expression ('${e}') may not occur in non-token rules`,e.start)}buildRule(e,t,i,n=!1){let s=this.substituteArgs(e.expr,t,e.params),{name:r,props:a,dynamicPrec:o,inline:l,group:c,exported:h}=this.nodeInfo(e.props||Gt,n?"pg":"pgi",e.id.name,t,e.params,e.expr);h&&e.params.length&&this.warn("Can't export parameterized rules",e.start),h&&n&&this.warn("Can't export inline rule",e.start);let u=this.newName(e.id.name+(t.length?"<"+t.join(",")+">":""),r||!0,a);return l&&(u.inline=!0),o&&this.registerDynamicPrec(u,o),(u.nodeType||h)&&0==e.params.length&&(r||(u.preserve=!0),n||(this.namedTerms[h||e.id.name]=u)),n||this.built.push(new Ut(e.id.name,t,u)),this.currentSkip.push(i),this.defineRule(u,this.normalizeExpr(s)),this.currentSkip.pop(),c&&this.definedGroups.push({name:u,group:c,rule:e}),u}nodeInfo(e,t,i=null,n=Gt,s=Gt,r,a){let o={},l=i&&(t.indexOf("a")>-1||!function(e){let t=e[0];return"_"==t||t.toUpperCase()!=t}(i))&&!/ /.test(i)?i:null,c=null,h=0,u=!1,d=null,m=null;for(let r of e)if(r.at)if("name"==r.name)l=this.finishProp(r,n,s),/ /.test(l)&&this.raise(`Node names cannot have spaces ('${l}')`,r.start);else if("dialect"==r.name){t.indexOf("d")<0&&this.raise("Can't specify a dialect on non-token rules",e[0].start),1==r.value.length||r.value[0].value||this.raise("The '@dialect' rule prop must hold a plain string value");let i=this.dialects.indexOf(r.value[0].value);i<0&&this.raise(`Unknown dialect '${r.value[0].value}'`,r.value[0].start),c=i}else"dynamicPrecedence"==r.name?(t.indexOf("p")<0&&this.raise("Dynamic precedence can only be specified on nonterminals"),1==r.value.length&&/^-?(?:10|\d)$/.test(r.value[0].value)||this.raise("The '@dynamicPrecedence' rule prop must hold an integer between -10 and 10"),h=+r.value[0].value):"inline"==r.name?(r.value.length&&this.raise("'@inline' doesn't take a value",r.value[0].start),t.indexOf("i")<0&&this.raise("Inline can only be specified on nonterminals"),u=!0):"isGroup"==r.name?(t.indexOf("g")<0&&this.raise("'@isGroup' can only be specified on nonterminals"),d=r.value.length?this.finishProp(r,n,s):i):"export"==r.name?m=r.value.length?this.finishProp(r,n,s):i:this.raise(`Unknown built-in prop name '@${r.name}'`,r.start);else{if(!this.knownProps[r.name]){let e=["name","dialect","dynamicPrecedence","export","isGroup"].includes(r.name)?` (did you mean '@${r.name}'?)`:"";this.raise(`Unknown prop name '${r.name}'${e}`,r.start)}o[r.name]=this.finishProp(r,n,s)}if(r&&this.ast.autoDelim&&(l||Se(o))){let e=this.findDelimiters(r);e&&(Yt(e[0],"closedBy",e[1].nodeName),Yt(e[1],"openedBy",e[0].nodeName))}if(a&&Se(a))for(let e in a)e in o||(o[e]=a[e]);return Se(o)&&!l&&this.raise("Node has properties but no name",e.length?e[0].start:r.start),u&&(Se(o)||c||h)&&this.raise("Inline nodes can't have props, dynamic precedence, or a dialect",e[0].start),u&&l&&(l=null),{name:l,props:o,dialect:c,dynamicPrec:h,inline:u,group:d,exported:m}}finishProp(e,t,i){return e.value.map((e=>{if(e.value)return e.value;let n=i.findIndex((t=>t.name==e.name));n<0&&this.raise(`Property refers to '${e.name}', but no parameter by that name is in scope`,e.start);let s=t[n];return s instanceof re&&!s.args.length?s.id.name:s instanceof de?s.value:this.raise(`Expression '${s}' can not be used as part of a property value`,e.start)})).join("")}resolveSpecialization(e){let t,i=e.type,{name:n,props:s,dialect:r}=this.nodeInfo(e.props,"d"),a=this.normalizeExpr(e.token);if(1==a.length&&1==a[0].terms.length&&a[0].terms[0].terminal||this.raise(`The first argument to '${i}' must resolve to a token`,e.token.start),e.content instanceof de)t=[e.content.value];else{if(!(e.content instanceof le&&e.content.exprs.every((e=>e instanceof de))))return this.raise(`The second argument to '${e.type}' must be a literal or choice of literals`,e.content.start);t=e.content.exprs.map((e=>e.value))}let o=a[0].terms[0],l=null,c=this.specialized[o.name]||(this.specialized[o.name]=[]);for(let a of t){let t=c.find((e=>e.value==a));null==t?(l||(l=this.makeTerminal(o.name+"/"+JSON.stringify(a),n,s),null!=r&&(this.tokens.byDialect[r]||(this.tokens.byDialect[r]=[])).push(l)),c.push({value:a,term:l,type:i,dialect:r,name:n}),this.tokenOrigins[l.name]={spec:o}):(t.type!=i&&this.raise(`Conflicting specialization types for ${JSON.stringify(a)} of ${o.name} (${i} vs ${t.type})`,e.start),t.dialect!=r&&this.raise(`Conflicting dialects for specialization ${JSON.stringify(a)} of ${o.name}`,e.start),t.name!=n&&this.raise(`Conflicting names for specialization ${JSON.stringify(a)} of ${o.name}`,e.start),l&&t.term!=l&&this.raise(`Conflicting specialization tokens for ${JSON.stringify(a)} of ${o.name}`,e.start),l=t.term)}return l}findDelimiters(e){if(!(e instanceof ce)||e.exprs.length<2)return null;let t=e=>{if(e instanceof de)return{term:this.tokens.getLiteral(e),str:e.value};if(e instanceof re&&0==e.args.length){let i=this.ast.rules.find((t=>t.id.name==e.id.name));if(i)return t(i.expr);let n=this.tokens.rules.find((t=>t.id.name==e.id.name));if(n&&n.expr instanceof de)return{term:this.tokens.getToken(e),str:n.expr.value}}return null},i=t(e.exprs[e.exprs.length-1]);if(!i||!i.term.nodeName)return null;let n=["()","[]","{}","<>"].find((e=>i.str.indexOf(e[1])>-1&&i.str.indexOf(e[0])<0));if(!n)return null;let s=t(e.exprs[0]);return!s||!s.term.nodeName||s.str.indexOf(n[0])<0||s.str.indexOf(n[1])>-1?null:[s.term,i.term]}registerDynamicPrec(e,t){this.dynamicRulePrecedences.push({rule:e,prec:t}),e.preserve=!0}defineGroup(e,t,i){var n;let s=[],r=e=>{if(e.nodeName)return[e];s.includes(e)&&this.raise(`Rule '${i.id.name}' cannot define a group because it contains a non-named recursive rule ('${e.name}')`,i.start);let t=[];s.push(e);for(let n of this.rules)if(n.name==e){let e=n.parts.map(r).filter((e=>e.length));if(e.length>1&&this.raise(`Rule '${i.id.name}' cannot define a group because some choices produce multiple named nodes`,i.start),1==e.length)for(let i of e[0])t.push(i)}return s.pop(),t};for(let i of r(e))i.props.group=((null===(n=i.props.group)||void 0===n?void 0:n.split(" "))||[]).concat(t).sort().join(" ")}checkGroups(){let e=Object.create(null),t=Object.create(null);for(let i of this.terms.terms)if(i.nodeName&&(t[i.nodeName]=!0,i.props.group))for(let t of i.props.group.split(" "))(e[t]||(e[t]=[])).push(i);let i=Object.keys(e);for(let n=0;n<i.length;n++){let s=i[n],r=e[s];t[s]&&this.warn(`Group name '${s}' conflicts with a node of the same name`);for(let t=n+1;t<i.length;t++){let n=e[i[t]];r.some((e=>n.includes(e)))&&(r.length>n.length?n.some((e=>!r.includes(e))):r.some((e=>!n.includes(e))))&&this.warn(`Groups '${s}' and '${i[t]}' overlap without one being a superset of the other`)}}}}class Wt{constructor(e,t,i,n,s,r,a){this.tokenizers=e,this.data=t,this.stateArray=i,this.skipData=n,this.skipInfo=s,this.states=r,this.builder=a,this.sharedActions=[]}findSharedActions(e){if(e.actions.length<5)return null;let t=null;for(let i of this.sharedActions)(!t||i.actions.length>t.actions.length)&&i.actions.every((t=>e.actions.some((e=>e.eq(t)))))&&(t=i);if(t)return t;let i=null,n=[];for(let t=e.id+1;t<this.states.length;t++){let s=this.states[t],r=0;if(!(s.defaultReduce||s.actions.length<5)){for(let t of e.actions)for(let e of s.actions)t.eq(e)&&(n[r++]=t);r>=5&&(!i||i.length<r)&&(i=n,n=[])}}if(!i)return null;let s={actions:i,addr:this.storeActions(i,-1,null)};return this.sharedActions.push(s),s}storeActions(e,t,i){if(t<0&&i&&i.actions.length==e.length)return i.addr;let n=[];for(let s of e)if(!i||!i.actions.some((e=>e.eq(s))))if(s instanceof Tt)n.push(s.term.id,s.target.id,0);else{let e=Kt(s.rule,this.skipInfo);e!=t&&n.push(s.term.id,65535&e,e>>16)}return n.push(65535),t>-1?n.push(2,65535&t,t>>16):i?n.push(1,65535&i.addr,i.addr>>16):n.push(0),this.data.storeArray(n)}finish(e,t,i){let n=this.builder,s=n.skipRules.indexOf(e.skip),r=this.skipData[s],a=this.skipInfo[s].startTokens,o=e.defaultReduce?Kt(e.defaultReduce,this.skipInfo):0,l=t?1:0,c=-1,h=null;if(0==o){if(t)for(const t of e.actions)t instanceof Ot&&t.term.eof&&(c=Kt(t.rule,this.skipInfo));c<0&&(h=this.findSharedActions(e))}e.set.some((e=>e.rule.name.top&&e.pos==e.rule.parts.length))&&(l|=2);let u=[];for(let t=0;t<e.actions.length+a.length;t++){let i=t<e.actions.length?e.actions[t].term:a[t-e.actions.length];for(;;){let e=n.tokenOrigins[i.name];if(!e||!e.spec){e&&e.external instanceof _i&&ii(u,e.external);break}i=e.spec}}let d=0;for(let t=0;t<this.tokenizers.length;t++){let i=this.tokenizers[t];(u.includes(i)||i.groupID==e.tokenGroup)&&(d|=1<<t)}let m=6*e.id;this.stateArray[m+0]=l,this.stateArray[m+1]=this.storeActions(o?Gt:e.actions,c,h),this.stateArray[m+2]=r,this.stateArray[m+3]=d,this.stateArray[m+4]=o,this.stateArray[m+5]=i}}function Yt(e,t,i){let n=e.props[t];(!n||n.split(" ").indexOf(i)<0)&&(e.props[t]=n?n+" "+i:i)}function Xt(e){let t=Object.create(null);for(let{value:i,term:n,type:s}of e){let e="specialize"==s?0:1;t[i]=n.id<<1|e}return t}function Kt(e,t,i=e.parts.length){return 65536|e.name.id|(e.isRepeatWrap&&i==e.parts.length?131072:0)|(t.some((t=>t.rule==e.name))?262144:0)|i<<19}class Zt{constructor(){this.data=[]}storeArray(e){let t=function(e,t){e:for(let i=0;;){let n=e.indexOf(t[0],i);if(-1==n||n+t.length>e.length)break;for(let s=1;s<t.length;s++)if(t[s]!=e[n+s]){i=n+1;continue e}return n}return-1}(this.data,e);if(t>-1)return t;let i=this.data.length;for(let t of e)this.data.push(t);return i}finish(){return Uint16Array.from(this.data)}}function ei(e){let t={},i=0;for(let n of e)for(let e of n.goto){i=Math.max(e.term.id,i);let s=t[e.term.id]||(t[e.term.id]={});(s[e.target.id]||(s[e.target.id]=[])).push(n.id)}let n=new Zt,s=[],r=i+2;for(let e=0;e<=i;e++){let i=t[e];if(!i){s.push(1);continue}let a=[],o=Object.keys(i);for(let e of o){let t=i[e];a.push((e==o[o.length-1]?1:0)+(t.length<<1)),a.push(+e);for(let e of t)a.push(e)}s.push(n.storeArray(a)+r)}if(s.some((e=>e>65535)))throw new ve("Goto table too large");return Uint16Array.from([i+1,...s,...n.data])}class ti{constructor(e,t){this.tokens=e,this.groupID=t}create(){return this.groupID}createSource(){return String(this.groupID)}}function ii(e,t){e.includes(t)||e.push(t)}function ni(e){let t=Object.create(null);for(let i of e){let e=1<<i.groupID;for(let n of i.tokens)t[n.id]=(t[n.id]||0)|e}return t}class si{constructor(e,t,i){this.name=e,this.expr=t,this.scope=i}}class ri{constructor(e,t,i,n){this.name=e,this.start=t,this.to=i,this.args=n}}class ai{constructor(e,t){this.b=e,this.ast=t,this.startState=new ze,this.built=[],this.building=[],this.byDialect=Object.create(null),this.precedenceRelations=[],this.rules=t?t.rules:Gt;for(let t of this.rules)e.unique(t.id)}getToken(e){for(let t of this.built)if(t.matches(e))return t.term;let t=e.id.name,i=this.rules.find((e=>e.id.name==t));if(!i)return null;let{name:n,props:s,dialect:r,exported:a}=this.b.nodeInfo(i.props,"d",t,e.args,i.params.length!=e.args.length?Gt:i.params),o=this.b.makeTerminal(e.toString(),n,s);return null!=r&&(this.byDialect[r]||(this.byDialect[r]=[])).push(o),(o.nodeType||a)&&0==i.params.length&&(o.nodeType||(o.preserve=!0),this.b.namedTerms[a||t]=o),this.buildRule(i,e,this.startState,new ze([o])),this.built.push(new Ut(t,e.args,o)),o}buildRule(e,t,i,n,s=Gt){let r=t.id.name;e.params.length!=t.args.length&&this.b.raise(`Incorrect number of arguments for token '${r}'`,t.start);let a=this.building.find((e=>e.name==r&&be(t.args,e.args)));if(a){if(a.to==n)return void i.nullEdge(a.start);let e=this.building.length-1;for(;this.building[e].name!=r;)e--;this.b.raise(`Invalid (non-tail) recursion in token rules: ${this.building.slice(e).map((e=>e.name)).join(" -> ")}`,t.start)}this.b.used(e.id.name);let o=new ze;i.nullEdge(o),this.building.push(new ri(r,o,n,t.args)),this.build(this.b.substituteArgs(e.expr,t.args,e.params),o,n,t.args.map(((t,i)=>new si(e.params[i].name,t,s)))),this.building.pop()}build(e,t,i,n){if(e instanceof re){let s,r=e.id.name,a=n.find((e=>e.name==r));if(a)return this.build(a.expr,t,i,a.scope);for(let e=0,t=this.b.localTokens;e<=t.length;e++)s=(e==t.length?this.b.tokens:t[e]).rules.find((e=>e.id.name==r));if(!s)return this.b.raise(`Reference to token rule '${e.id.name}', which isn't found`,e.start);this.buildRule(s,e,t,i,n)}else if(e instanceof ye)for(let[n,s]of ge[e.type])t.edge(n,s,i);else if(e instanceof le)for(let s of e.exprs)this.build(s,t,i,n);else if(gi(e))t.nullEdge(i);else if(e instanceof ce){let s=e.markers.find((e=>e.length>0));s&&this.b.raise("Conflict marker in token expression",s[0].start);for(let s=0;s<e.exprs.length;s++){let r=s==e.exprs.length-1?i:new ze;this.build(e.exprs[s],t,r,n),t=r}}else if(e instanceof ue)if("*"==e.kind){let s=new ze;t.nullEdge(s),this.build(e.expr,s,s,n),s.nullEdge(i)}else if("+"==e.kind){let s=new ze;this.build(e.expr,t,s,n),this.build(e.expr,s,s,n),s.nullEdge(i)}else t.nullEdge(i),this.build(e.expr,t,i,n);else if(e instanceof me)for(let[n,s]of e.inverted?function(e){let t=0,i=[];for(let[n,s]of e)n>t&&i.push([t,n]),t=s;return t<=di&&i.push([t,di+1]),i}(e.ranges):e.ranges)fi(t,i,n,s);else if(e instanceof de)for(let n=0;n<e.value.length;n++){let s=e.value.charCodeAt(n),r=n==e.value.length-1?i:new ze;t.edge(s,s+1,r),t=r}else{if(!(e instanceof pe))return this.b.raise("Unrecognized expression type in token",e.start);{let e=new ze;t.edge(0,56320,i),t.edge(56320,65536,i),t.edge(55296,56320,e),e.edge(56320,57344,i)}}}takePrecedences(){let e=this.precedenceRelations=[];if(this.ast)for(let t of this.ast.precedences){let i=[];for(let n of t.items){let t=[];if(n instanceof re)for(let e of this.built)(n.args.length?e.matches(n):e.id==n.id.name)&&t.push(e.term);else{let e=JSON.stringify(n.value),i=this.built.find((t=>t.id==e));i&&t.push(i.term)}t.length||this.b.warn(`Precedence specified for unknown token ${n}`,n.start);for(let n of t)bi(e,n,i);i=i.concat(t)}}}precededBy(e,t){let i=this.precedenceRelations.find((t=>t.term==e));return i&&i.after.includes(t)}buildPrecTable(e){let t=[],i=this.precedenceRelations.slice();for(let{a:t,b:n,soft:s}of e)if(s){if(!i.some((e=>e.term==t))||!i.some((e=>e.term==n)))continue;s<0&&([t,n]=[n,t]),bi(i,n,[t]),bi(i,t,[])}e:for(;i.length;){for(let e=0;e<i.length;e++){let n=i[e];if(n.after.every((e=>t.includes(e.id)))){if(t.push(n.term.id),1==i.length)break e;i[e]=i.pop();continue e}}this.b.raise(`Cyclic token precedence relation between ${i.map((e=>e.term)).join(", ")}`)}return t}}class oi extends ai{constructor(){super(...arguments),this.explicitConflicts=[]}getLiteral(e){let t=JSON.stringify(e.value);for(let e of this.built)if(e.id==t)return e.term;let i=null,n={},s=null,r=null,a=this.ast?this.ast.literals.find((t=>t.literal==e.value)):null;a&&({name:i,props:n,dialect:s,exported:r}=this.b.nodeInfo(a.props,"da",e.value));let o=this.b.makeTerminal(t,i,n);return null!=s&&(this.byDialect[s]||(this.byDialect[s]=[])).push(o),r&&(this.b.namedTerms[r]=o),this.build(e,this.startState,new ze([o]),Gt),this.built.push(new Ut(t,Gt,o)),o}takeConflicts(){var e;let t=e=>{if(e instanceof re){for(let t of this.built)if(t.matches(e))return t.term}else{let t=JSON.stringify(e.value),i=this.built.find((e=>e.id==t));if(i)return i.term}return this.b.warn(`Precedence specified for unknown token ${e}`,e.start),null};for(let i of(null===(e=this.ast)||void 0===e?void 0:e.conflicts)||[]){let e=t(i.a),n=t(i.b);e&&n&&(e.id<n.id&&([e,n]=[n,e]),this.explicitConflicts.push({a:e,b:n}))}}buildTokenGroups(e,t,i){let n=this.startState.compile();n.accepting.length&&this.b.raise(`Grammar contains zero-length tokens (in '${n.accepting[0].name}')`,this.rules.find((e=>e.id.name==n.accepting[0].name)).start),/\btokens\b/.test(xt)&&console.log(n.toString());let s=n.findConflicts(function(e,t,i){let n=Object.create(null);function s(e,n){return e.actions.some((e=>e.term==n))||i[t.skipRules.indexOf(e.skip)].startTokens.includes(n)}return(t,i)=>{t.id<i.id&&([t,i]=[i,t]);let r=t.id|i.id<<16,a=n[r];return null!=a?a:n[r]=e.some((e=>s(e,t)&&s(e,i)))}}(e,this.b,t)).filter((({a:e,b:t})=>!this.precededBy(e,t)&&!this.precededBy(t,e)));for(let{a:e,b:t}of this.explicitConflicts)s.some((i=>i.a==e&&i.b==t))||s.push(new qe(e,t,0,"",""));let r=s.filter((e=>e.soft)),a=s.filter((e=>!e.soft)),o=[],l=[];for(let n of e){if(n.defaultReduce||n.tokenGroup>-1)continue;let e=[],s=[],r=t[this.b.skipRules.indexOf(n.skip)].startTokens;for(let e of r)n.actions.some((t=>t.term==e))&&this.b.raise(`Use of token ${e.name} conflicts with skip rule`);let c=[];for(let e=0;e<n.actions.length+(r?r.length:0);e++){let t=e<n.actions.length?n.actions[e].term:r[e-n.actions.length],i=this.b.tokenOrigins[t.name];if(i&&i.spec)t=i.spec;else if(i&&i.external)continue;ii(c,t)}if(0==c.length)continue;for(let t of c)for(let i of a){let r=i.a==t?i.b:i.b==t?i.a:null;if(r){if(c.includes(r)&&!o.some((e=>e.conflict==i))){let e=i.exampleA?` (example: ${JSON.stringify(i.exampleA)}${i.exampleB?` vs ${JSON.stringify(i.exampleB)}`:""})`:"";o.push({error:`Overlapping tokens ${t.name} and ${r.name} used in same context${e}\nAfter: ${n.set[0].trail()}`,conflict:i})}ii(e,t),ii(s,r)}}let h=null;for(let t of l)if(!s.some((e=>t.tokens.includes(e)))){for(let i of e)ii(t.tokens,i);h=t;break}h||(h=new ti(e,l.length+i),l.push(h)),n.tokenGroup=h.groupID}o.length&&this.b.raise(o.map((e=>e.error)).join("\n\n")),l.length+i>16&&this.b.raise(`Too many different token groups (${l.length}) to represent them as a 16-bit bitfield`);let c=this.buildPrecTable(r);return{tokenGroups:l,tokenPrec:c,tokenData:n.toArray(ni(l),c)}}}class li extends ai{constructor(e,t){super(e,t),this.fallback=null,t.fallback&&e.unique(t.fallback.id)}getToken(e){let t=null;if(this.ast.fallback&&this.ast.fallback.id.name==e.id.name){if(e.args.length&&this.b.raise(`Incorrect number of arguments for ${e.id.name}`,e.start),!this.fallback){let{name:t,props:i,exported:n}=this.b.nodeInfo(this.ast.fallback.props,"",e.id.name,Gt,Gt),s=this.fallback=this.b.makeTerminal(e.id.name,t,i);(s.nodeType||n)&&(s.nodeType||(s.preserve=!0),this.b.namedTerms[n||e.id.name]=s),this.b.used(e.id.name)}t=this.fallback}else t=super.getToken(e);return t&&!this.b.tokenOrigins[t.name]&&(this.b.tokenOrigins[t.name]={group:this}),t}buildLocalGroup(e,t,i){let n=this.startState.compile();n.accepting.length&&this.b.raise(`Grammar contains zero-length tokens (in '${n.accepting[0].name}')`,this.rules.find((e=>e.id.name==n.accepting[0].name)).start);for(let{a:e,b:t,exampleA:i}of n.findConflicts((()=>!0)))this.precededBy(e,t)||this.precededBy(t,e)||this.b.raise(`Overlapping tokens ${e.name} and ${t.name} in local token group${i?` (example: ${JSON.stringify(i)})`:""}`);for(let n of e){if(n.defaultReduce)continue;let e=null,s=t[this.b.skipRules.indexOf(n.skip)].startTokens[0];for(let{term:t}of n.actions){let i=this.b.tokenOrigins[t.name];(null==i?void 0:i.group)==this?e=t:s=t}e&&(s&&this.b.raise(`Tokens from a local token group used together with other tokens (${e.name} with ${s.name})`),n.tokenGroup=i)}let s=this.buildPrecTable(Gt),r=n.toArray({[i]:65535},s),a=r.length,o=new Uint16Array(r.length+s.length+1);return o.set(r,0),o.set(s,a),o[o.length-1]=65535,{groupID:i,create:()=>new B.RA(o,a,this.fallback?this.fallback.id:void 0),createSource:e=>`new ${e("LocalTokenGroup","@lezer/lr")}(${Ht(o)}, ${a}${this.fallback?`, ${this.fallback.id}`:""})`}}}const ci=65536,hi=55296,ui=57344,di=1114111,mi=56320,pi=57343;function fi(e,t,i,n){if(i<ci&&(i<hi&&e.edge(i,Math.min(n,hi),t),n>ui&&e.edge(Math.max(i,ui),Math.min(n,65536),t),i=ci),n<=ci)return;let s=String.fromCodePoint(i),r=String.fromCodePoint(n-1),a=s.charCodeAt(0),o=s.charCodeAt(1),l=r.charCodeAt(0),c=r.charCodeAt(1);if(a==l){let i=new ze;e.edge(a,a+1,i),i.edge(o,c+1,t)}else{let i=a,n=l;if(o>mi){i++;let n=new ze;e.edge(a,a+1,n),n.edge(o,pi+1,t)}if(c<pi){n--;let i=new ze;e.edge(l,l+1,i),i.edge(mi,c+1,t)}if(i<=n){let s=new ze;e.edge(i,n+1,s),s.edge(mi,pi+1,t)}}}function gi(e){return e instanceof ce&&0==e.exprs.length}function yi(e,t){let i=Object.create(null);for(let n of t){e.unique(n.id);let{name:t,props:s,dialect:r}=e.nodeInfo(n.props,"d",n.id.name),a=e.makeTerminal(n.id.name,t,s);null!=r&&(e.tokens.byDialect[r]||(e.tokens.byDialect[r]=[])).push(a),e.namedTerms[n.id.name]=i[n.id.name]=a}return i}function xi(e,t,i){let n=t[i.id.name];return n?(i.args.length&&e.raise("External tokens cannot take arguments",i.args[0].start),e.used(i.id.name),n):null}function bi(e,t,i){let n=e.findIndex((e=>e.term==t));n<0?e.push({term:t,after:i}):e[n]={term:t,after:e[n].after.concat(i)}}class _i{constructor(e,t){this.b=e,this.ast=t,this.tokens=yi(e,t.tokens);for(let e in this.tokens)this.b.tokenOrigins[this.tokens[e].name]={external:this}}getToken(e){return xi(this.b,this.tokens,e)}create(){return this.b.options.externalTokenizer(this.ast.id.name,this.b.termTable)}createSource(e){let{source:t,id:{name:i}}=this.ast;return e(i,t)}}class wi{constructor(e,t){this.b=e,this.ast=t,this.term=null,this.tokens=yi(e,t.tokens)}finish(){let e=this.b.normalizeExpr(this.ast.token);1==e.length&&1==e[0].terms.length&&e[0].terms[0].terminal||this.b.raise(`The token expression to '@external ${this.ast.type}' must resolve to a token`,this.ast.token.start),this.term=e[0].terms[0];for(let e in this.tokens)this.b.tokenOrigins[this.tokens[e].name]={spec:this.term,external:this}}getToken(e){return xi(this.b,this.tokens,e)}}function ki(e,t={}){let i=new Qt(e,t),n=i.getParser();return n.termTable=i.termTable,n}const vi=["break","case","catch","continue","debugger","default","do","else","finally","for","function","if","return","switch","throw","try","var","while","with","null","true","false","instanceof","typeof","void","delete","new","in","this","const","class","extends","export","import","super","enum","implements","interface","let","package","private","protected","public","static","yield","require"];function Si(e){return e.props.some((e=>e.at&&"export"==e.name))}const Ni=l.EditorView.theme({"&":{background:"var(--jp-layout-color0)",color:"var(--jp-content-font-color1)"},".jp-CodeConsole &, .jp-Notebook &":{background:"transparent"},".cm-content":{caretColor:"var(--jp-editor-cursor-color)"},".cm-scroller":{fontFamily:"inherit"},".cm-cursor, .cm-dropCursor":{borderLeft:"var(--jp-code-cursor-width0) solid var(--jp-editor-cursor-color)"},".cm-selectionBackground, .cm-content ::selection":{backgroundColor:"var(--jp-editor-selected-background)"},"&.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{backgroundColor:"var(--jp-editor-selected-focused-background)"},".cm-gutters":{borderRight:"1px solid var(--jp-border-color2)",backgroundColor:"var(--jp-layout-color2)"},".cm-gutter":{backgroundColor:"var(--jp-layout-color2)"},".cm-activeLine":{backgroundColor:"color-mix(in srgb, var(--jp-layout-color3) 25%, transparent)"},".cm-lineNumbers":{color:"var(--jp-ui-font-color2)"},".cm-searchMatch":{backgroundColor:"var(--jp-search-unselected-match-background-color)",color:"var(--jp-search-unselected-match-color)"},".cm-searchMatch.cm-searchMatch-selected":{backgroundColor:"var(--jp-search-selected-match-background-color) !important",color:"var(--jp-search-selected-match-color) !important"},".cm-tooltip":{backgroundColor:"var(--jp-layout-color1)"},".cm-builtin":{color:"var(--jp-mirror-editor-builtin-color)"}}),Ci=a.HighlightStyle.define([{tag:y.tags.meta,color:"var(--jp-mirror-editor-meta-color)"},{tag:y.tags.heading,color:"var(--jp-mirror-editor-header-color)"},{tag:[y.tags.heading1,y.tags.heading2,y.tags.heading3,y.tags.heading4],color:"var(--jp-mirror-editor-header-color)",fontWeight:"bold"},{tag:y.tags.keyword,color:"var(--jp-mirror-editor-keyword-color)",fontWeight:"bold"},{tag:y.tags.atom,color:"var(--jp-mirror-editor-atom-color)"},{tag:y.tags.number,color:"var(--jp-mirror-editor-number-color)"},{tag:[y.tags.definition(y.tags.name),y.tags.function(y.tags.definition(y.tags.variableName))],color:"var(--jp-mirror-editor-def-color)"},{tag:y.tags.standard(y.tags.variableName),color:"var(--jp-mirror-editor-builtin-color)"},{tag:[y.tags.special(y.tags.variableName),y.tags.self],color:"var(--jp-mirror-editor-variable-2-color)"},{tag:y.tags.punctuation,color:"var(--jp-mirror-editor-punctuation-color)"},{tag:y.tags.propertyName,color:"var(--jp-mirror-editor-property-color)"},{tag:y.tags.operator,color:"var(--jp-mirror-editor-operator-color)",fontWeight:"bold"},{tag:y.tags.comment,color:"var(--jp-mirror-editor-comment-color)",fontStyle:"italic"},{tag:y.tags.string,color:"var(--jp-mirror-editor-string-color)"},{tag:[y.tags.labelName,y.tags.monospace,y.tags.special(y.tags.string)],color:"var(--jp-mirror-editor-string-2-color)"},{tag:y.tags.bracket,color:"var(--jp-mirror-editor-bracket-color)"},{tag:y.tags.tagName,color:"var(--jp-mirror-editor-tag-color)"},{tag:y.tags.attributeName,color:"var(--jp-mirror-editor-attribute-color)"},{tag:y.tags.quote,color:"var(--jp-mirror-editor-quote-color)"},{tag:y.tags.link,color:"var(--jp-mirror-editor-link-color)",textDecoration:"underline"},{tag:[y.tags.separator,y.tags.derefOperator,y.tags.paren],color:""},{tag:y.tags.strong,fontWeight:"bold"},{tag:y.tags.emphasis,fontStyle:"italic"},{tag:y.tags.strikethrough,textDecoration:"line-through"},{tag:y.tags.bool,color:"var(--jp-mirror-editor-keyword-color)",fontWeight:"bold"}]),ji=[Ni,(0,a.syntaxHighlighting)(Ci)];class Ti{constructor(){this._themeMap=new Map([["jupyter",Object.freeze({name:"jupyter",theme:ji})]])}get themes(){return Array.from(this._themeMap.values())}defaultTheme(){return this._themeMap.get("jupyter").theme}addTheme(e){if(this._themeMap.has(e.name))throw new Error(`A theme named '${e.name}' is already registered.`);this._themeMap.set(e.name,{displayName:e.name,...e})}getTheme(e){var t;const i=null===(t=this._themeMap.get(e))||void 0===t?void 0:t.theme;return null!=i?i:this.defaultTheme()}}!function(e){e.getDefaultThemes=function(e){const t=(null!=e?e:d.nullTranslator).load("jupyterlab");return[Object.freeze({name:"codemirror",displayName:t.__("codemirror"),theme:[l.EditorView.baseTheme({}),(0,a.syntaxHighlighting)(a.defaultHighlightStyle)]})]}}(Ti||(Ti={}));class Oi{constructor(e,t){this.langPython=t,this.tree=(0,a.syntaxTree)(e.state),this.mark=l.Decoration.mark({class:"cm-builtin"}),this.decorations=this.buildDeco(e),this.decoratedTo=e.viewport.to}update(e){let t=(0,a.syntaxTree)(e.state),{viewport:i}=e.view,n=e.changes.mapPos(this.decoratedTo,1);t.length<i.to&&t.type==this.tree.type&&n>=i.to?(this.decorations=this.decorations.map(e.changes),this.decoratedTo=n):(t!=this.tree||e.viewportChanged)&&(this.tree=t,this.decorations=this.buildDeco(e.view),this.decoratedTo=i.to)}buildDeco(e){if(!this.tree.length)return l.Decoration.none;let t=new o.RangeSetBuilder;const i=n=>{var s;const r=n.node.cursor(),a=r.tree&&r.tree.prop(g.NodeProp.mounted);if(a&&a.overlay&&(null===(s=n.node.enter(a.overlay[0].from+n.from,1))||void 0===s||s.cursor().iterate(i)),this.langPython.isActiveAt(e.state,n.from+1)&&"VariableName"===n.name){const i=e.state.sliceDoc(n.from,n.to);$i.includes(i)&&t.add(n.from,n.to,this.mark)}};for(let{from:t,to:n}of e.visibleRanges)this.tree.iterate({enter:i,from:t,to:n});return t.finish()}}function Pi(e){return l.ViewPlugin.define((t=>new Oi(t,e)),{decorations:e=>e.decorations})}const $i=["abs","aiter","all","any","anext","ascii","bin","bool","breakpoint","bytearray","bytes","callable","chr","classmethod","compile","complex","delattr","dict","dir","divmod","enumerate","eval","exec","filter","float","format","frozenset","getattr","globals","hasattr","hash","help","hex","id","input","int","isinstance","issubclass","iter","len","list","locals","map","max","memoryview","min","next","object","oct","open","ord","pow","print","property","range","repr","reversed","round","set","setattr","slice","sorted","staticmethod","str","sum","super","tuple","type","vars","zip","__import__"];class Ei{constructor(){this._modeList=[],this.addLanguage({name:"none",mime:"text/plain",support:new a.LanguageSupport(a.LRLanguage.define({parser:ki("@top Program { }")}))})}addLanguage(e){var t;if(null!==(t=this.findByName(e.name))&&void 0!==t?t:this.findByMIME(e.mime,!0))throw new Error(`${e.mime} already registered`);this._modeList.push(this.makeSpec(e))}async getLanguage(e){const t=this.findBest(e);return t&&!t.support&&(t.support=await t.load()),t}getLanguages(){return[...this._modeList]}findByMIME(e,t=!1){if(Array.isArray(e)){for(let t=0;t<e.length;t++){const i=this.findByMIME(e[t]);if(i)return i}return null}e=e.toLowerCase();for(let t=0;t<this._modeList.length;t++){let i=this._modeList[t];if(Array.isArray(i.mime)){for(let t=0;t<i.mime.length;t++)if(i.mime[t]==e)return i}else if(i.mime==e)return i}if(!t){if(/\+xml$/.test(e))return this.findByMIME("application/xml");if(/\+json$/.test(e))return this.findByMIME("application/json")}return null}findByName(e){e=e.toLowerCase();for(let t=0;t<this._modeList.length;t++){let i=this._modeList[t];if(i.name.toLowerCase()==e)return i;if(i.alias)for(let t=0;t<i.alias.length;t++)if(i.alias[t].toLowerCase()==e)return i}return null}findByExtension(e){if(Array.isArray(e)){for(let t=0;t<e.length;t++){const i=this.findByExtension(e[t]);if(i)return i}return null}e=e.toLowerCase();for(let t=0;t<this._modeList.length;t++){let i=this._modeList[t];for(let t=0;t<i.extensions.length;t++)if(i.extensions[t].toLowerCase()==e)return i}return null}findByFileName(e){const t=q.PathExt.basename(e);for(let e=0;e<this._modeList.length;e++){let i=this._modeList[e];if(i.filename&&i.filename.test(t))return i}let i=t.lastIndexOf("."),n=i>-1&&t.substring(i+1,t.length);return n?this.findByExtension(n):null}findBest(e,t=!0){var i,n,s,a;const o="string"==typeof e?e:e.name,l="string"!=typeof e?e.mime:o,c="string"!=typeof e&&null!==(i=e.extensions)&&void 0!==i?i:[];return null!==(a=null!==(s=null!==(n=o?this.findByName(o):null)&&void 0!==n?n:l?this.findByMIME(l):null)&&void 0!==s?s:this.findByExtension(c))&&void 0!==a?a:t?this.findByMIME(r.IEditorMimeTypeService.defaultMimeType):null}async highlight(e,t,i){var n;t&&await this.getLanguage(t);const s=null===(n=null==t?void 0:t.support)||void 0===n?void 0:n.language;if(!s)return void i.appendChild(document.createTextNode(e));const r=s.parser.parse(e);let a=0;(0,y.highlightTree)(r,Ci,((t,n,s)=>{t>a&&i.appendChild(document.createTextNode(e.slice(a,t)));const r=i.appendChild(document.createElement("span"));r.className=s,r.appendChild(document.createTextNode(e.slice(t,n))),a=n})),a<r.length-1&&i.appendChild(document.createTextNode(e.slice(a,r.length)))}makeSpec(e){let t=a.LanguageDescription.of(e);return t.mime=e.mime,t.displayName=e.displayName,t}}!function(e){function t(e){return new a.LanguageSupport(a.StreamLanguage.define(e))}async function n(e){const t=await i.e(5828).then(i.bind(i,25828));return t.sql({dialect:t[e]})}e.legacy=t,e.getDefaultLanguages=function(e){const s=(null!=e?e:d.nullTranslator).load("jupyterlab");return[{name:"C",displayName:s.__("C"),mime:"text/x-csrc",extensions:["c","h","ino"],load:async()=>(await i.e(7674).then(i.bind(i,87674))).cpp()},{name:"C++",displayName:s.__("C++"),mime:"text/x-c++src",extensions:["cpp","c++","cc","cxx","hpp","h++","hh","hxx"],load:async()=>(await i.e(7674).then(i.bind(i,87674))).cpp()},{name:"CQL",displayName:s.__("CQL"),mime:"text/x-cassandra",extensions:["cql"],load:()=>n("Cassandra")},{name:"CSS",displayName:s.__("CSS"),mime:"text/css",extensions:["css"],load:async()=>(await i.e(9239).then(i.bind(i,59239))).css()},{name:"HTML",displayName:s.__("HTML"),alias:["xhtml"],mime:"text/html",extensions:["html","htm","handlebars","hbs"],load:async()=>(await Promise.all([i.e(5850),i.e(9239),i.e(7866)]).then(i.bind(i,77866))).html()},{name:"Java",displayName:s.__("Java"),mime:"text/x-java",extensions:["java"],load:async()=>(await i.e(1053).then(i.bind(i,41053))).java()},{name:"Javascript",displayName:s.__("Javascript"),alias:["ecmascript","js","node"],mime:["text/javascript","text/ecmascript","application/javascript","application/x-javascript","application/ecmascript"],extensions:["js","mjs","cjs"],load:async()=>(await i.e(5850).then(i.bind(i,65850))).javascript()},{name:"JSON",displayName:s.__("JSON"),alias:["json5"],mime:["application/json","application/x-json"],extensions:["json","map"],load:async()=>(await i.e(4670).then(i.bind(i,44670))).json()},{name:"JSX",displayName:s.__("JSX"),mime:"text/jsx",extensions:["jsx"],load:async()=>(await i.e(5850).then(i.bind(i,65850))).javascript({jsx:!0})},{name:"MariaDB SQL",displayName:s.__("MariaDB SQL"),mime:"text/x-mariadb",load:()=>n("MariaSQL")},{name:"Markdown",displayName:s.__("Markdown"),mime:"text/x-markdown",extensions:["md","markdown","mkd"],async load(){return(await i.e(252).then(i.t.bind(i,252,23))).markdown({codeLanguages:this._modeList})}},{name:"MS SQL",displayName:s.__("MS SQL"),mime:"text/x-mssql",load:()=>n("MSSQL")},{name:"MySQL",displayName:s.__("MySQL"),mime:"text/x-mysql",load:()=>n("MySQL")},{name:"PHP",displayName:s.__("PHP"),mime:["text/x-php","application/x-httpd-php","application/x-httpd-php-open"],extensions:["php","php3","php4","php5","php7","phtml"],load:async()=>(await Promise.all([i.e(5850),i.e(9239),i.e(7866),i.e(8010)]).then(i.bind(i,78010))).php()},{name:"PLSQL",displayName:s.__("PLSQL"),mime:"text/x-plsql",extensions:["pls"],load:()=>n("PLSQL")},{name:"PostgreSQL",displayName:s.__("PostgreSQL"),mime:"text/x-pgsql",load:()=>n("PostgreSQL")},{name:"Python",displayName:s.__("Python"),mime:"text/x-python",extensions:["BUILD","bzl","py","pyw"],filename:/^(BUCK|BUILD)$/,async load(){const e=await i.e(2552).then(i.bind(i,12552));return new a.LanguageSupport(e.pythonLanguage,Pi(e.pythonLanguage))}},{name:"ipython",displayName:s.__("ipython"),mime:"text/x-ipython",async load(){const e=await i.e(2552).then(i.bind(i,12552));return new a.LanguageSupport(e.pythonLanguage,Pi(e.pythonLanguage))}},{name:"Rust",displayName:s.__("Rust"),mime:"text/x-rustsrc",extensions:["rs"],load:async()=>(await i.e(6972).then(i.bind(i,58879))).rust()},{name:"SQL",displayName:s.__("SQL"),mime:["application/sql","text/x-sql"],extensions:["sql"],load:()=>n("StandardSQL")},{name:"SQLite",displayName:s.__("SQLite"),mime:"text/x-sqlite",load:()=>n("SQLite")},{name:"TSX",displayName:s.__("TSX"),alias:["TypeScript-JSX"],mime:"text/typescript-jsx",extensions:["tsx"],load:async()=>(await i.e(5850).then(i.bind(i,65850))).javascript({jsx:!0,typescript:!0})},{name:"TypeScript",displayName:s.__("TypeScript"),alias:["ts"],mime:"application/typescript",extensions:["ts"],load:async()=>(await i.e(5850).then(i.bind(i,65850))).javascript({typescript:!0})},{name:"WebAssembly",displayName:s.__("WebAssembly"),mime:"text/webassembly",extensions:["wat","wast"],load:async()=>(await i.e(4810).then(i.bind(i,14810))).wast()},{name:"XML",displayName:s.__("XML"),alias:["rss","wsdl","xsd"],mime:["application/xml","text/xml"],extensions:["xml","xsl","xsd","svg"],load:async()=>(await i.e(4588).then(i.bind(i,64588))).xml()},{name:"APL",displayName:s.__("APL"),mime:"text/apl",extensions:["dyalog","apl"],load:async()=>t((await i.e(563).then(i.bind(i,10563))).apl)},{name:"PGP",displayName:s.__("PGP"),alias:["asciiarmor"],mime:["application/pgp","application/pgp-encrypted","application/pgp-keys","application/pgp-signature"],extensions:["asc","pgp","sig"],load:async()=>t((await i.e(8983).then(i.bind(i,98983))).asciiArmor)},{name:"ASN.1",displayName:s.__("ASN.1"),mime:"text/x-ttcn-asn",extensions:["asn","asn1"],load:async()=>t((await i.e(7534).then(i.bind(i,97534))).asn1({}))},{name:"Asterisk",displayName:s.__("Asterisk"),mime:"text/x-asterisk",filename:/^extensions\.conf$/i,load:async()=>t((await i.e(403).then(i.bind(i,70403))).asterisk)},{name:"Brainfuck",displayName:s.__("Brainfuck"),mime:"text/x-brainfuck",extensions:["b","bf"],load:async()=>t((await i.e(2702).then(i.bind(i,12702))).brainfuck)},{name:"Cobol",displayName:s.__("Cobol"),mime:"text/x-cobol",extensions:["cob","cpy"],load:async()=>t((await i.e(8979).then(i.bind(i,38979))).cobol)},{name:"C#",displayName:s.__("C#"),alias:["csharp","cs"],mime:"text/x-csharp",extensions:["cs"],load:async()=>t((await i.e(9425).then(i.bind(i,49425))).csharp)},{name:"Clojure",displayName:s.__("Clojure"),mime:"text/x-clojure",extensions:["clj","cljc","cljx"],load:async()=>t((await i.e(4002).then(i.bind(i,54002))).clojure)},{name:"ClojureScript",displayName:s.__("ClojureScript"),mime:"text/x-clojurescript",extensions:["cljs"],load:async()=>t((await i.e(4002).then(i.bind(i,54002))).clojure)},{name:"Closure Stylesheets (GSS)",displayName:s.__("Closure Stylesheets (GSS)"),mime:"text/x-gss",extensions:["gss"],load:async()=>t((await i.e(5019).then(i.bind(i,85019))).gss)},{name:"CMake",displayName:s.__("CMake"),mime:"text/x-cmake",extensions:["cmake","cmake.in"],filename:/^CMakeLists\.txt$/,load:async()=>t((await i.e(1941).then(i.bind(i,41941))).cmake)},{name:"CoffeeScript",displayName:s.__("CoffeeScript"),alias:["coffee","coffee-script"],mime:["application/vnd.coffeescript","text/coffeescript","text/x-coffeescript"],extensions:["coffee"],load:async()=>t((await i.e(3449).then(i.bind(i,83449))).coffeeScript)},{name:"Common Lisp",displayName:s.__("Common Lisp"),alias:["lisp"],mime:"text/x-common-lisp",extensions:["cl","lisp","el"],load:async()=>t((await i.e(3370).then(i.bind(i,43370))).commonLisp)},{name:"Cypher",displayName:s.__("Cypher"),mime:"application/x-cypher-query",extensions:["cyp","cypher"],load:async()=>t((await i.e(4387).then(i.bind(i,24387))).cypher)},{name:"Cython",displayName:s.__("Cython"),mime:"text/x-cython",extensions:["pyx","pxd","pxi"],load:async()=>t((await i.e(2871).then(i.bind(i,72871))).cython)},{name:"Crystal",displayName:s.__("Crystal"),mime:"text/x-crystal",extensions:["cr"],load:async()=>t((await i.e(3420).then(i.bind(i,13420))).crystal)},{name:"D",displayName:s.__("D"),mime:"text/x-d",extensions:["d"],load:async()=>t((await i.e(3501).then(i.bind(i,73501))).d)},{name:"Dart",displayName:s.__("Dart"),mime:["application/dart","text/x-dart"],extensions:["dart"],load:async()=>t((await i.e(9425).then(i.bind(i,49425))).dart)},{name:"diff",displayName:s.__("diff"),mime:"text/x-diff",extensions:["diff","patch"],load:async()=>t((await i.e(9116).then(i.bind(i,39116))).diff)},{name:"Dockerfile",displayName:s.__("Dockerfile"),mime:"text/x-dockerfile",filename:/^Dockerfile$/,load:async()=>t((await i.e(2343).then(i.bind(i,22343))).dockerFile)},{name:"DTD",displayName:s.__("DTD"),mime:"application/xml-dtd",extensions:["dtd"],load:async()=>t((await i.e(221).then(i.bind(i,30221))).dtd)},{name:"Dylan",displayName:s.__("Dylan"),mime:"text/x-dylan",extensions:["dylan","dyl","intr"],load:async()=>t((await i.e(9331).then(i.bind(i,69331))).dylan)},{name:"EBNF",displayName:s.__("EBNF"),mime:"text/x-ebnf",load:async()=>t((await i.e(647).then(i.bind(i,90647))).ebnf)},{name:"ECL",displayName:s.__("ECL"),mime:"text/x-ecl",extensions:["ecl"],load:async()=>t((await i.e(5061).then(i.bind(i,75061))).ecl)},{name:"edn",displayName:s.__("edn"),mime:"application/edn",extensions:["edn"],load:async()=>t((await i.e(4002).then(i.bind(i,54002))).clojure)},{name:"Eiffel",displayName:s.__("Eiffel"),mime:"text/x-eiffel",extensions:["e"],load:async()=>t((await i.e(7264).then(i.bind(i,57264))).eiffel)},{name:"Elm",displayName:s.__("Elm"),mime:"text/x-elm",extensions:["elm"],load:async()=>t((await i.e(1558).then(i.bind(i,21558))).elm)},{name:"Erlang",displayName:s.__("Erlang"),mime:"text/x-erlang",extensions:["erl"],load:async()=>t((await i.e(883).then(i.bind(i,20883))).erlang)},{name:"Esper",displayName:s.__("Esper"),mime:"text/x-esper",load:async()=>t((await i.e(8479).then(i.bind(i,48479))).esper)},{name:"Factor",displayName:s.__("Factor"),mime:"text/x-factor",extensions:["factor"],load:async()=>t((await i.e(9037).then(i.bind(i,59037))).factor)},{name:"FCL",displayName:s.__("FCL"),mime:"text/x-fcl",load:async()=>t((await i.e(4521).then(i.bind(i,74521))).fcl)},{name:"Forth",displayName:s.__("Forth"),mime:"text/x-forth",extensions:["forth","fth","4th"],load:async()=>t((await i.e(7817).then(i.bind(i,77817))).forth)},{name:"Fortran",displayName:s.__("Fortran"),mime:"text/x-fortran",extensions:["f","for","f77","f90","f95"],load:async()=>t((await i.e(3562).then(i.bind(i,33562))).fortran)},{name:"F#",displayName:s.__("F#"),alias:["fsharp"],mime:"text/x-fsharp",extensions:["fs"],load:async()=>t((await i.e(7022).then(i.bind(i,47022))).fSharp)},{name:"Gas",displayName:s.__("Gas"),mime:"text/x-gas",extensions:["s"],load:async()=>t((await i.e(5972).then(i.bind(i,15972))).gas)},{name:"Gherkin",displayName:s.__("Gherkin"),mime:"text/x-feature",extensions:["feature"],load:async()=>t((await i.e(906).then(i.bind(i,10906))).gherkin)},{name:"Go",displayName:s.__("Go"),mime:"text/x-go",extensions:["go"],load:async()=>t((await i.e(9022).then(i.bind(i,89022))).go)},{name:"Groovy",displayName:s.__("Groovy"),mime:"text/x-groovy",extensions:["groovy","gradle"],filename:/^Jenkinsfile$/,load:async()=>t((await i.e(7360).then(i.bind(i,7360))).groovy)},{name:"Haskell",displayName:s.__("Haskell"),mime:"text/x-haskell",extensions:["hs"],load:async()=>t((await i.e(3111).then(i.bind(i,63111))).haskell)},{name:"Haxe",displayName:s.__("Haxe"),mime:"text/x-haxe",extensions:["hx"],load:async()=>t((await i.e(4039).then(i.bind(i,84039))).haxe)},{name:"HXML",displayName:s.__("HXML"),mime:"text/x-hxml",extensions:["hxml"],load:async()=>t((await i.e(4039).then(i.bind(i,84039))).hxml)},{name:"HTTP",displayName:s.__("HTTP"),mime:"message/http",load:async()=>t((await i.e(9060).then(i.bind(i,19060))).http)},{name:"IDL",displayName:s.__("IDL"),mime:"text/x-idl",extensions:["pro"],load:async()=>t((await i.e(9250).then(i.bind(i,89250))).idl)},{name:"JSON-LD",displayName:s.__("JSON-LD"),alias:["jsonld"],mime:"application/ld+json",extensions:["jsonld"],load:async()=>t((await i.e(661).then(i.bind(i,70661))).jsonld)},{name:"Jinja2",displayName:s.__("Jinja2"),mime:"text/jinja2",extensions:["j2","jinja","jinja2"],load:async()=>t((await i.e(9558).then(i.bind(i,79558))).jinja2)},{name:"Julia",displayName:s.__("Julia"),mime:"text/x-julia",extensions:["jl"],load:async()=>t((await i.e(1837).then(i.bind(i,81837))).julia)},{name:"Kotlin",displayName:s.__("Kotlin"),mime:"text/x-kotlin",extensions:["kt"],load:async()=>t((await i.e(9425).then(i.bind(i,49425))).kotlin)},{name:"LESS",displayName:s.__("LESS"),mime:"text/x-less",extensions:["less"],load:async()=>t((await i.e(5019).then(i.bind(i,85019))).less)},{name:"LiveScript",displayName:s.__("LiveScript"),alias:["ls"],mime:"text/x-livescript",extensions:["ls"],load:async()=>t((await i.e(3211).then(i.bind(i,3211))).liveScript)},{name:"Lua",displayName:s.__("Lua"),mime:"text/x-lua",extensions:["lua"],load:async()=>t((await i.e(5494).then(i.bind(i,25494))).lua)},{name:"mIRC",displayName:s.__("mIRC"),mime:"text/mirc",load:async()=>t((await i.e(8937).then(i.bind(i,58937))).mirc)},{name:"Mathematica",displayName:s.__("Mathematica"),mime:"text/x-mathematica",extensions:["m","nb","wl","wls"],load:async()=>t((await i.e(1418).then(i.bind(i,1418))).mathematica)},{name:"Modelica",displayName:s.__("Modelica"),mime:"text/x-modelica",extensions:["mo"],load:async()=>t((await i.e(6788).then(i.bind(i,26788))).modelica)},{name:"MUMPS",displayName:s.__("MUMPS"),mime:"text/x-mumps",extensions:["mps"],load:async()=>t((await i.e(7679).then(i.bind(i,66139))).mumps)},{name:"mbox",displayName:s.__("mbox"),mime:"application/mbox",extensions:["mbox"],load:async()=>t((await i.e(4965).then(i.bind(i,24965))).mbox)},{name:"Nginx",displayName:s.__("Nginx"),mime:"text/x-nginx-conf",filename:/nginx.*\.conf$/i,load:async()=>t((await i.e(67).then(i.bind(i,80067))).nginx)},{name:"NSIS",displayName:s.__("NSIS"),mime:"text/x-nsis",extensions:["nsh","nsi"],load:async()=>t((await i.e(1091).then(i.bind(i,61091))).nsis)},{name:"NTriples",displayName:s.__("NTriples"),mime:["application/n-triples","application/n-quads","text/n-triples"],extensions:["nt","nq"],load:async()=>t((await i.e(9676).then(i.bind(i,39676))).ntriples)},{name:"Objective-C",displayName:s.__("Objective-C"),alias:["objective-c","objc"],mime:"text/x-objectivec",extensions:["m"],load:async()=>t((await i.e(9425).then(i.bind(i,49425))).objectiveC)},{name:"Objective-C++",displayName:s.__("Objective-C++"),alias:["objective-c++","objc++"],mime:"text/x-objectivec++",extensions:["mm"],load:async()=>t((await i.e(9425).then(i.bind(i,49425))).objectiveCpp)},{name:"OCaml",displayName:s.__("OCaml"),mime:"text/x-ocaml",extensions:["ml","mli","mll","mly"],load:async()=>t((await i.e(7022).then(i.bind(i,47022))).oCaml)},{name:"Octave",displayName:s.__("Octave"),mime:"text/x-octave",extensions:["m"],load:async()=>t((await i.e(3336).then(i.bind(i,33336))).octave)},{name:"Oz",displayName:s.__("Oz"),mime:"text/x-oz",extensions:["oz"],load:async()=>t((await i.e(632).then(i.bind(i,632))).oz)},{name:"Pascal",displayName:s.__("Pascal"),mime:"text/x-pascal",extensions:["p","pas"],load:async()=>t((await i.e(2386).then(i.bind(i,42386))).pascal)},{name:"Perl",displayName:s.__("Perl"),mime:"text/x-perl",extensions:["pl","pm"],load:async()=>t((await i.e(3322).then(i.bind(i,53322))).perl)},{name:"Pig",displayName:s.__("Pig"),mime:"text/x-pig",extensions:["pig"],load:async()=>t((await i.e(8381).then(i.bind(i,68381))).pig)},{name:"PowerShell",displayName:s.__("PowerShell"),mime:"application/x-powershell",extensions:["ps1","psd1","psm1"],load:async()=>t((await i.e(8378).then(i.bind(i,18378))).powerShell)},{name:"Properties files",displayName:s.__("Properties files"),alias:["ini","properties"],mime:"text/x-properties",extensions:["properties","ini","in"],load:async()=>t((await i.e(7803).then(i.bind(i,87803))).properties)},{name:"ProtoBuf",displayName:s.__("ProtoBuf"),mime:"text/x-protobuf",extensions:["proto"],load:async()=>t((await i.e(114).then(i.bind(i,40114))).protobuf)},{name:"Puppet",displayName:s.__("Puppet"),mime:"text/x-puppet",extensions:["pp"],load:async()=>t((await i.e(7005).then(i.bind(i,57005))).puppet)},{name:"Q",displayName:s.__("Q"),mime:"text/x-q",extensions:["q"],load:async()=>t((await i.e(1088).then(i.bind(i,31088))).q)},{name:"R",displayName:s.__("R"),alias:["rscript"],mime:"text/x-rsrc",extensions:["r","R"],load:async()=>t((await i.e(2188).then(i.bind(i,42188))).r)},{name:"RPM Changes",displayName:s.__("RPM Changes"),mime:"text/x-rpm-changes",load:async()=>t((await i.e(3700).then(i.bind(i,13700))).rpmChanges)},{name:"RPM Spec",displayName:s.__("RPM Spec"),mime:"text/x-rpm-spec",extensions:["spec"],load:async()=>t((await i.e(3700).then(i.bind(i,13700))).rpmSpec)},{name:"Ruby",displayName:s.__("Ruby"),alias:["jruby","macruby","rake","rb","rbx"],mime:"text/x-ruby",extensions:["rb"],load:async()=>t((await i.e(5299).then(i.bind(i,68433))).ruby)},{name:"SAS",displayName:s.__("SAS"),mime:"text/x-sas",extensions:["sas"],load:async()=>t((await i.e(4825).then(i.bind(i,44825))).sas)},{name:"Scala",displayName:s.__("Scala"),mime:"text/x-scala",extensions:["scala"],load:async()=>t((await i.e(9425).then(i.bind(i,49425))).scala)},{name:"Scheme",displayName:s.__("Scheme"),mime:"text/x-scheme",extensions:["scm","ss"],load:async()=>t((await i.e(899).then(i.bind(i,20899))).scheme)},{name:"SCSS",displayName:s.__("SCSS"),mime:"text/x-scss",extensions:["scss"],load:async()=>t((await i.e(5019).then(i.bind(i,85019))).sCSS)},{name:"Shell",displayName:s.__("Shell"),alias:["bash","sh","zsh"],mime:["text/x-sh","application/x-sh"],extensions:["sh","ksh","bash"],filename:/^PKGBUILD$/,load:async()=>t((await i.e(7170).then(i.bind(i,27170))).shell)},{name:"Sieve",displayName:s.__("Sieve"),mime:"application/sieve",extensions:["siv","sieve"],load:async()=>t((await i.e(745).then(i.bind(i,50745))).sieve)},{name:"Smalltalk",displayName:s.__("Smalltalk"),mime:"text/x-stsrc",extensions:["st"],load:async()=>t((await i.e(9234).then(i.bind(i,9234))).smalltalk)},{name:"Solr",displayName:s.__("Solr"),mime:"text/x-solr",load:async()=>t((await i.e(85).then(i.bind(i,50085))).solr)},{name:"SML",displayName:s.__("SML"),mime:"text/x-sml",extensions:["sml","sig","fun","smackspec"],load:async()=>t((await i.e(7022).then(i.bind(i,47022))).sml)},{name:"SPARQL",displayName:s.__("SPARQL"),alias:["sparul"],mime:"application/sparql-query",extensions:["rq","sparql"],load:async()=>t((await i.e(5249).then(i.bind(i,55249))).sparql)},{name:"Spreadsheet",displayName:s.__("Spreadsheet"),alias:["excel","formula"],mime:"text/x-spreadsheet",load:async()=>t((await i.e(69).then(i.bind(i,70069))).spreadsheet)},{name:"Squirrel",displayName:s.__("Squirrel"),mime:"text/x-squirrel",extensions:["nut"],load:async()=>t((await i.e(9425).then(i.bind(i,49425))).squirrel)},{name:"Stylus",displayName:s.__("Stylus"),mime:"text/x-styl",extensions:["styl"],load:async()=>t((await i.e(6739).then(i.bind(i,56739))).stylus)},{name:"Swift",displayName:s.__("Swift"),mime:"text/x-swift",extensions:["swift"],load:async()=>t((await i.e(1618).then(i.bind(i,51618))).swift)},{name:"sTeX",displayName:s.__("sTeX"),mime:"text/x-stex",load:async()=>t((await i.e(311).then(i.bind(i,60311))).stex)},{name:"LaTeX",displayName:s.__("LaTeX"),alias:["tex"],mime:"text/x-latex",extensions:["text","ltx","tex"],load:async()=>t((await i.e(311).then(i.bind(i,60311))).stex)},{name:"SystemVerilog",displayName:s.__("SystemVerilog"),mime:"text/x-systemverilog",extensions:["v","sv","svh"],load:async()=>t((await i.e(5822).then(i.bind(i,45822))).verilog)},{name:"Tcl",displayName:s.__("Tcl"),mime:"text/x-tcl",extensions:["tcl"],load:async()=>t((await i.e(8446).then(i.bind(i,8446))).tcl)},{name:"Textile",displayName:s.__("Textile"),mime:"text/x-textile",extensions:["textile"],load:async()=>t((await i.e(4030).then(i.bind(i,84030))).textile)},{name:"TiddlyWiki",displayName:s.__("TiddlyWiki"),mime:"text/x-tiddlywiki",load:async()=>t((await i.e(7054).then(i.bind(i,27054))).tiddlyWiki)},{name:"Tiki wiki",displayName:s.__("Tiki wiki"),mime:"text/tiki",load:async()=>t((await i.e(5425).then(i.bind(i,15425))).tiki)},{name:"TOML",displayName:s.__("TOML"),mime:"text/x-toml",extensions:["toml"],load:async()=>t((await i.e(2682).then(i.bind(i,92682))).toml)},{name:"troff",displayName:s.__("troff"),mime:"text/troff",extensions:["1","2","3","4","5","6","7","8","9"],load:async()=>t((await i.e(8433).then(i.bind(i,28433))).troff)},{name:"TTCN",displayName:s.__("TTCN"),mime:"text/x-ttcn",extensions:["ttcn","ttcn3","ttcnpp"],load:async()=>t((await i.e(1122).then(i.bind(i,21122))).ttcn)},{name:"TTCN_CFG",displayName:s.__("TTCN_CFG"),mime:"text/x-ttcn-cfg",extensions:["cfg"],load:async()=>t((await i.e(6942).then(i.bind(i,6942))).ttcnCfg)},{name:"Turtle",displayName:s.__("Turtle"),mime:"text/turtle",extensions:["ttl"],load:async()=>t((await i.e(9604).then(i.bind(i,19604))).turtle)},{name:"Web IDL",displayName:s.__("Web IDL"),mime:"text/x-webidl",extensions:["webidl"],load:async()=>t((await i.e(4148).then(i.bind(i,64148))).webIDL)},{name:"VB.NET",displayName:s.__("VB.NET"),mime:"text/x-vb",extensions:["vb"],load:async()=>t((await i.e(5834).then(i.bind(i,75834))).vb)},{name:"VBScript",displayName:s.__("VBScript"),mime:"text/vbscript",extensions:["vbs"],load:async()=>t((await i.e(5996).then(i.bind(i,35996))).vbScript)},{name:"Velocity",displayName:s.__("Velocity"),mime:"text/velocity",extensions:["vtl"],load:async()=>t((await i.e(431).then(i.bind(i,431))).velocity)},{name:"Verilog",displayName:s.__("Verilog"),mime:"text/x-verilog",extensions:["v"],load:async()=>t((await i.e(5822).then(i.bind(i,45822))).verilog)},{name:"VHDL",displayName:s.__("VHDL"),mime:"text/x-vhdl",extensions:["vhd","vhdl"],load:async()=>t((await i.e(677).then(i.bind(i,30677))).vhdl)},{name:"XQuery",displayName:s.__("XQuery"),mime:"application/xquery",extensions:["xy","xquery"],load:async()=>t((await i.e(3230).then(i.bind(i,83230))).xQuery)},{name:"Yacas",displayName:s.__("Yacas"),mime:"text/x-yacas",extensions:["ys"],load:async()=>t((await i.e(4038).then(i.bind(i,44038))).yacas)},{name:"YAML",displayName:s.__("YAML"),alias:["yml"],mime:["text/x-yaml","text/yaml"],extensions:["yaml","yml"],load:async()=>t((await i.e(9233).then(i.bind(i,9233))).yaml)},{name:"Z80",displayName:s.__("Z80"),mime:"text/x-z80",extensions:["z80"],load:async()=>t((await i.e(5698).then(i.bind(i,75698))).z80)},{name:"mscgen",displayName:s.__("mscgen"),mime:"text/x-mscgen",extensions:["mscgen","mscin","msc"],load:async()=>t((await i.e(4843).then(i.bind(i,64843))).mscgen)},{name:"xu",displayName:s.__("xu"),mime:"text/x-xu",extensions:["xu"],load:async()=>t((await i.e(4843).then(i.bind(i,64843))).xu)},{name:"msgenny",displayName:s.__("msgenny"),mime:"text/x-msgenny",extensions:["msgenny"],load:async()=>t((await i.e(4843).then(i.bind(i,64843))).msgenny)}]}}(Ei||(Ei={}));class Mi{constructor(e){var t,i,n,s,r,a;this.edgeRequested=new h.Signal(this),this._isDisposed=!1,this._language=new o.Compartment,this._uuid="",this._languages=null!==(t=e.languages)&&void 0!==t?t:new Ei,this._configurator=null!==(s=null===(i=e.extensionsRegistry)||void 0===i?void 0:i.createNew({...e,inline:null!==(n=e.inline)&&void 0!==n&&n}))&&void 0!==s?s:new D;const u=this.host=e.host;u.classList.add("jp-CodeMirrorEditor"),u.classList.add("jp-Editor"),u.addEventListener("focus",this,!0),u.addEventListener("blur",this,!0),u.addEventListener("scroll",this,!0),this._uuid=null!==(r=e.uuid)&&void 0!==r?r:c.UUID.uuid4();const d=this._model=e.model,m=l.EditorView.domEventHandlers({keydown:(e,t)=>this.onKeydown(e)}),p=l.EditorView.updateListener.of((e=>{this._onDocChanged(e)}));this._editor=Ai.createEditor(u,this._configurator,[o.Prec.high(m),p,this._language.of([]),...null!==(a=e.extensions)&&void 0!==a?a:[]],d.sharedModel.source),this._onMimeTypeChanged(),this._onCursorActivity(),this._configurator.configChanged.connect(this.onConfigChanged,this),d.mimeTypeChanged.connect(this._onMimeTypeChanged,this)}get uuid(){return this._uuid}set uuid(e){this._uuid=e}get editor(){return this._editor}get doc(){return this._editor.state.doc}get lineCount(){return this.doc.lines}get model(){return this._model}get lineHeight(){return this._editor.defaultLineHeight}get charWidth(){return this._editor.defaultCharacterWidth}get isDisposed(){return this._isDisposed}dispose(){this.isDisposed||(this._isDisposed=!0,this.host.removeEventListener("focus",this,!0),this.host.removeEventListener("blur",this,!0),this.host.removeEventListener("scroll",this,!0),this._configurator.dispose(),h.Signal.clearData(this),this.editor.destroy())}getOption(e){return this._configurator.getOption(e)}hasOption(e){return this._configurator.hasOption(e)}setOption(e,t){this._configurator.setOption(e,t)}setOptions(e){this._configurator.setOptions(e)}injectExtension(e){this._configurator.injectExtension(this._editor,e)}getLine(e){return(e+=1)<=this.doc.lines?this.doc.line(e).text:void 0}getOffsetAt(e){return this.doc.line(e.line+1).from+e.column}getPositionAt(e){const t=this.doc.lineAt(e);return{line:t.number-1,column:e-t.from}}undo(){this.model.sharedModel.undo()}redo(){this.model.sharedModel.redo()}clearHistory(){this.model.sharedModel.clearUndoHistory()}focus(){this._editor.focus()}hasFocus(){return this._editor.hasFocus}blur(){this._editor.contentDOM.blur()}get state(){return this._editor.state}firstLine(){return 0}lastLine(){return this.doc.lines-1}cursorCoords(e,t){const i=this.state.selection.main,n=e?i.from:i.to;return this.editor.coordsAtPos(n)}getRange(e,t,i){const n=this.getOffsetAt(this._toPosition(e)),s=this.getOffsetAt(this._toPosition(t));return this.state.sliceDoc(n,s)}revealPosition(e){const t=this.getOffsetAt(e);this._editor.dispatch({effects:l.EditorView.scrollIntoView(t)})}revealSelection(e){const t=this.getOffsetAt(e.start),i=this.getOffsetAt(e.end);this._editor.dispatch({effects:l.EditorView.scrollIntoView(o.EditorSelection.range(t,i))})}getCoordinateForPosition(e){const t=this.getOffsetAt(e);return this.editor.coordsAtPos(t)}getPositionForCoordinate(e){const t=this.editor.posAtCoords({x:e.left,y:e.top});return this.getPositionAt(t)||null}getCursorPosition(){const e=this.state.selection.main.head;return this.getPositionAt(e)}setCursorPosition(e,t={}){const i=this.getOffsetAt(e);this.editor.dispatch({selection:{anchor:i},scrollIntoView:!1!==t.scroll}),this.editor.hasFocus||this.model.selections.set(this.uuid,this.getSelections())}getSelection(){return this.getSelections()[0]}setSelection(e){this.setSelections([e])}getSelections(){const e=this.state.selection.ranges;if(e.length>0)return e.map((e=>({anchor:this._toCodeMirrorPosition(this.getPositionAt(e.from)),head:this._toCodeMirrorPosition(this.getPositionAt(e.to))}))).map((e=>this._toSelection(e)));const t=this._toCodeMirrorPosition(this.getPositionAt(this.state.selection.main.head));return[this._toSelection({anchor:t,head:t})]}setSelections(e){const t=e.length?e.map((e=>o.EditorSelection.range(this.getOffsetAt(e.start),this.getOffsetAt(e.end)))):[o.EditorSelection.range(0,0)];this.editor.dispatch({selection:o.EditorSelection.create(t)})}replaceSelection(e){const t=this.getSelections()[0];this.model.sharedModel.updateSource(this.getOffsetAt(t.start),this.getOffsetAt(t.end),e);const i=this.getPositionAt(this.getOffsetAt(t.start)+e.length);this.setSelection({start:i,end:i})}getTokens(){const e=[],t=(0,a.ensureSyntaxTree)(this.state,this.doc.length);return t&&t.iterate({enter:t=>(null===t.node.firstChild&&e.push({value:this.state.sliceDoc(t.from,t.to),offset:t.from,type:t.name}),!0)}),e}getTokenAt(e){const t=(0,a.ensureSyntaxTree)(this.state,e);let i=null;return t&&t.iterate({enter:t=>!(i||!t.node.firstChild&&e>=t.from&&e<=t.to&&(i={value:this.state.sliceDoc(t.from,t.to),offset:t.from,type:t.name},1))}),i||{offset:e,value:""}}getTokenAtCursor(){return this.getTokenAt(this.state.selection.main.head)}newIndentedLine(){(0,s.insertNewlineAndIndent)({state:this.state,dispatch:this.editor.dispatch})}execCommand(e){e(this.editor)}onConfigChanged(e,t){e.reconfigureExtensions(this._editor,t)}onKeydown(e){const t=this.state.selection.main.head;return 0===t&&38===e.keyCode?(e.shiftKey||this.edgeRequested.emit("top"),!1):1===this.doc.lineAt(t).number&&38===e.keyCode?(e.shiftKey||this.edgeRequested.emit("topLine"),!1):t===this.doc.length&&40===e.keyCode&&(e.shiftKey||this.edgeRequested.emit("bottom"),!1)}_onMimeTypeChanged(){this._languages.getLanguage(this._model.mimeType).then((e=>{var t;this._editor.dispatch({effects:this._language.reconfigure(null!==(t=null==e?void 0:e.support)&&void 0!==t?t:[])})})).catch((e=>{console.log(`Failed to load language for '${this._model.mimeType}'.`,e),this._editor.dispatch({effects:this._language.reconfigure([])})}))}_onCursorActivity(){if(this._editor.hasFocus){const e=this.getSelections();this.model.selections.set(this.uuid,e)}}_toSelection(e){return{uuid:this.uuid,start:this._toPosition(e.anchor),end:this._toPosition(e.head)}}_toPosition(e){return{line:e.line,column:e.ch}}_toCodeMirrorPosition(e){return{line:e.line,ch:e.column}}_onDocChanged(e){e.transactions.length&&e.transactions[0].selection&&this._onCursorActivity()}handleEvent(e){switch(e.type){case"focus":this._evtFocus(e);break;case"blur":this._evtBlur(e)}}_evtFocus(e){this.host.classList.add("jp-mod-focused"),this._onCursorActivity()}_evtBlur(e){this.host.classList.remove("jp-mod-focused")}}var Ai;!function(e){e.createEditor=function(e,t,i,n){const s=t.getInitialExtensions();return s.push(...i),new l.EditorView({state:o.EditorState.create({doc:n,extensions:s}),parent:e})}}(Ai||(Ai={}));class Ii{constructor(e={}){var t,i,n;this.newInlineEditor=e=>(e.host.dataset.type="inline",this.newEditor({...e,config:{...this.inlineCodeMirrorConfig,...e.config||{}},inline:!0})),this.newDocumentEditor=e=>{var t,i;return e.host.dataset.type="document",this.newEditor({...e,config:{...this.documentCodeMirrorConfig,...null!==(t=e.config)&&void 0!==t?t:{}},inline:!1,extensions:[l.keymap.of([{key:"Shift-Enter",run:e=>!0}])].concat(null!==(i=e.extensions)&&void 0!==i?i:[])})},this.languages=null!==(t=e.languages)&&void 0!==t?t:new Ei,this.extensions=null!==(i=e.extensions)&&void 0!==i?i:new z,this.translator=null!==(n=e.translator)&&void 0!==n?n:d.nullTranslator,this.inlineCodeMirrorConfig={searchWithCM:!0},this.documentCodeMirrorConfig={lineNumbers:!0,scrollPastEnd:!0}}newEditor(e){return new Mi({extensionsRegistry:this.extensions,languages:this.languages,translator:this.translator,...e})}}class Li{constructor(e){this.languages=e}getMimeTypeByLanguage(e){var t;const i=e.file_extension||"",n=this.languages.findBest(e.codemirror_mode||{mimetype:e.mimetype,name:e.name,ext:[i.split(".").slice(-1)[0]]});return n?Array.isArray(n.mime)?null!==(t=n.mime[0])&&void 0!==t?t:r.IEditorMimeTypeService.defaultMimeType:n.mime:r.IEditorMimeTypeService.defaultMimeType}getMimeTypeByFilePath(e){var t;const i=q.PathExt.extname(e);if(".ipy"===i)return"text/x-python";if(".md"===i)return"text/x-ipythongfm";const n=this.languages.findByFileName(e);return n?Array.isArray(n.mime)?null!==(t=n.mime[0])&&void 0!==t?t:r.IEditorMimeTypeService.defaultMimeType:n.mime:r.IEditorMimeTypeService.defaultMimeType}}var Ri,Di=i(3486);class zi{constructor(){this.currentIndex=null,this.query=null,this._isActive=!0,this._inSelection=null,this._isDisposed=!1,this._cmHandler=null,this.currentIndex=null,this._stateChanged=new h.Signal(this)}get cmHandler(){return this._cmHandler||(this._cmHandler=new qi(this.editor)),this._cmHandler}get stateChanged(){return this._stateChanged}get currentMatchIndex(){return this.isActive?this.currentIndex:null}get isActive(){return this._isActive}get isDisposed(){return this._isDisposed}get matchesCount(){return this.isActive?this.cmHandler.matches.length:0}clearHighlight(){return this.currentIndex=null,this.cmHandler.clearHighlight(),Promise.resolve()}dispose(){this._isDisposed||(this._isDisposed=!0,h.Signal.clearData(this),this.isActive&&this.endQuery().catch((e=>{console.error("Failed to end search query on cells.",e)})))}async setIsActive(e){this._isActive!==e&&(this._isActive=e,this._isActive?null!==this.query&&await this.startQuery(this.query,this.filters):await this.endQuery())}async setSearchSelection(e){this._inSelection!==e&&(this._inSelection=e,await this.updateCodeMirror(this.model.sharedModel.getSource()),this._stateChanged.emit())}setProtectSelection(e){this.cmHandler.protectSelection=e}async startQuery(e,t){this.query=e,this.filters=t;const i=this.model.sharedModel.getSource();await this.updateCodeMirror(i),this.model.sharedModel.changed.connect(this.onSharedModelChanged,this)}async endQuery(){await this.clearHighlight(),await this.cmHandler.endQuery(),this.currentIndex=null}async highlightNext(e=!0,t){if(0!==this.matchesCount&&this.isActive){let i=await this.cmHandler.highlightNext(t);return this.currentIndex=i?this.cmHandler.currentIndex:e?0:null,i}return this.currentIndex=null,Promise.resolve(this.getCurrentMatch())}async highlightPrevious(e=!0,t){if(0!==this.matchesCount&&this.isActive){let i=await this.cmHandler.highlightPrevious(t);return this.currentIndex=i?this.cmHandler.currentIndex:e?this.matchesCount-1:null,i}return this.currentIndex=null,Promise.resolve(this.getCurrentMatch())}replaceCurrentMatch(e,t,i){if(!this.isActive)return Promise.resolve(!1);let n=!1;if(null!==this.currentIndex&&this.currentIndex<this.cmHandler.matches.length){const t=this.getCurrentMatch();if(t){this.cmHandler.matches.splice(this.currentIndex,1);const s=this.cmHandler.matches.length;this.currentIndex=this.currentIndex<s?this.currentIndex:null;const r=(null==i?void 0:i.regularExpression)?t.text.replace(this.query,e):e,a=(null==i?void 0:i.preserveCase)?Di.GenericSearchProvider.preserveCase(t.text,r):r;this.model.sharedModel.updateSource(t.position,t.position+t.text.length,a),n=!0}else this.currentIndex=null}return Promise.resolve(n)}replaceAllMatches(e,t){if(!this.isActive)return Promise.resolve(!1);let i=this.cmHandler.matches.length>0,n=this.model.sharedModel.getSource(),s=0;const r=this.cmHandler.matches.reduce(((i,r)=>{const a=r.position,o=a+r.text.length,l=(null==t?void 0:t.regularExpression)?r.text.replace(this.query,e):e,c=(null==t?void 0:t.preserveCase)?Di.GenericSearchProvider.preserveCase(r.text,l):l,h=`${i}${n.slice(s,a)}${c}`;return s=o,h}),"");return i&&(this.cmHandler.matches=[],this.currentIndex=null,this.model.sharedModel.setSource(`${r}${n.slice(s)}`)),Promise.resolve(i)}getCurrentMatch(){if(null!==this.currentIndex){let e;return this.currentIndex<this.cmHandler.matches.length&&(e=this.cmHandler.matches[this.currentIndex]),e}}async onSharedModelChanged(e,t){t.sourceChange&&(await this.updateCodeMirror(e.getSource()),this._stateChanged.emit())}async updateCodeMirror(e){if(null!==this.query&&this.isActive){const t=await Di.TextSearchEngine.search(this.query,e);if(this._inSelection){const e=this.editor,i=e.getOffsetAt(this._inSelection.start),n=e.getOffsetAt(this._inSelection.end);this.cmHandler.matches=t.filter((e=>e.position>=i&&e.position<=n)),null===this.cmHandler.currentIndex&&this.cmHandler.matches.length>0&&await this.cmHandler.highlightNext({from:"selection",select:!1,scroll:!1}),this.currentIndex=this.cmHandler.currentIndex}else this.cmHandler.matches=t}else this.cmHandler.matches=[]}}class qi{constructor(e){this._current=null,this._cm=e,this._matches=new Array,this._currentIndex=null,this._highlightEffect=o.StateEffect.define({map:(e,t)=>{const i=e=>({text:e.text,position:t.mapPos(e.position)});return{matches:e.matches.map(i),currentMatch:e.currentMatch?i(e.currentMatch):null}}}),this._highlightMark=l.Decoration.mark({class:"cm-searching"}),this._currentMark=l.Decoration.mark({class:"jp-current-match"}),this._highlightField=o.StateField.define({create:()=>l.Decoration.none,update:(e,t)=>{e=e.map(t.changes);for(let i of t.effects)if(i.is(this._highlightEffect)){const t=i;e=t.value.matches.length?(e=e.update({add:t.value.matches.map((e=>this._highlightMark.range(e.position,e.position+e.text.length))),filter:()=>!1})).update({add:t.value.currentMatch?[this._currentMark.range(t.value.currentMatch.position,t.value.currentMatch.position+t.value.currentMatch.text.length)]:[]}):l.Decoration.none}return e},provide:e=>l.EditorView.decorations.from(e)}),this._domEventHandlers=l.EditorView.domEventHandlers({focus:()=>{this._selectCurrentMatch()}})}get currentIndex(){return this._currentIndex}get matches(){return this._matches}set matches(e){this._matches=e,null!==this._currentIndex&&this._currentIndex>this._matches.length&&(this._currentIndex=this._matches.length>0?0:null),this._highlightCurrentMatch({select:!1})}get protectSelection(){return this._protectSelection}set protectSelection(e){this._protectSelection=e}clearHighlight(){this._currentIndex=null,this._highlightCurrentMatch()}endQuery(){return this._currentIndex=null,this._matches=[],this._cm&&this._cm.editor.dispatch({effects:this._highlightEffect.of({matches:[],currentMatch:null})}),Promise.resolve()}highlightNext(e){var t;return this._currentIndex=this._findNext(!1,null!==(t=null==e?void 0:e.from)&&void 0!==t?t:"auto"),this._highlightCurrentMatch(e),Promise.resolve(null!==this._currentIndex?this._matches[this._currentIndex]:void 0)}highlightPrevious(e){var t;return this._currentIndex=this._findNext(!0,null!==(t=null==e?void 0:e.from)&&void 0!==t?t:"auto"),this._highlightCurrentMatch(e),Promise.resolve(null!==this._currentIndex?this._matches[this._currentIndex]:void 0)}setEditor(e){if(this._cm)throw new Error("CodeMirrorEditor already set.");this._cm=e,null!==this._currentIndex&&this._highlightCurrentMatch(),this._cm.editor.dispatch({effects:o.StateEffect.appendConfig.of(this._domEventHandlers)}),this._refresh()}_selectCurrentMatch(e=!0){const t=this._current;if(!t)return;if(!this._cm)return;const i={anchor:t.position,head:t.position+t.text.length},n=this._cm.editor.state.selection.main;if(n.from===t.position&&n.to===t.position+t.text.length||this._protectSelection){if(e)return void this._cm.editor.dispatch({effects:l.EditorView.scrollIntoView(o.EditorSelection.range(i.anchor,i.head))})}else this._cm.editor.dispatch({selection:i,scrollIntoView:e})}_highlightCurrentMatch(e){var t,i,n;if(this._cm){if(null!==this._currentIndex){const s=this.matches[this._currentIndex];this._current=s,(null===(t=null==e?void 0:e.select)||void 0===t||t)&&(this._cm.hasFocus()?this._selectCurrentMatch(null===(i=null==e?void 0:e.scroll)||void 0===i||i):(null===(n=null==e?void 0:e.scroll)||void 0===n||n)&&this._cm.editor.dispatch({effects:l.EditorView.scrollIntoView(s.position)}))}else this._current=null;this._refresh()}}_refresh(){if(!this._cm)return;let e=[this._highlightEffect.of({matches:this.matches,currentMatch:this._current})];this._cm.state.field(this._highlightField,!1)||e.push(o.StateEffect.appendConfig.of([this._highlightField])),this._cm.editor.dispatch({effects:e})}_findNext(e,t="auto"){var i,n,s,r;if(0===this._matches.length)return null;this._cm||["previous-match","start"].includes(t)||(t="previous-match");let a=0;if("auto"===t&&null!==(n=null===(i=this._cm)||void 0===i?void 0:i.hasFocus())&&void 0!==n&&n||"selection"===t){const t=this._cm.state.selection.main;a=e?t.anchor:t.head}else if("selection-start"===t){const e=this._cm.state.selection.main;a=Math.min(e.anchor,e.head)}else"start"===t?a=0:this._current&&(a=e?this._current.position:this._current.position+this._current.text.length);var o;0===a&&e&&null===this.currentIndex&&(a=null!==(r=null===(s=this._cm)||void 0===s?void 0:s.doc.length)&&void 0!==r?r:(o=this._matches[this._matches.length-1])?o.position+o.text.length:0);const l=a;let c=Ri.findNext(this._matches,l,0,this._matches.length-1);return null===c?e?this._matches.length-1:null:e&&(c-=1,c<0)?null:c}}!function(e){e.findNext=function(e,t,i=0,n=1/0){for(n=Math.min(e.length-1,n);i<=n;){let s=Math.floor(.5*(i+n));const r=e[s].position;if(r<t){if((i=s+1)<e.length&&e[i].position>t)return i}else{if(!(r>t))return s;if((n=s-1)>0&&e[n].position<t)return s}}const s=i>0?i-1:0;return e[s].position>=t?s:null}}(Ri||(Ri={}));const Bi=new c.Token("@jupyterlab/codemirror:IEditorExtensionRegistry","A registry for CodeMirror extension factories."),Fi=new c.Token("@jupyterlab/codemirror:IEditorLanguageRegistry","A registry for CodeMirror languages."),Hi=new c.Token("@jupyterlab/codemirror:IEditorThemeRegistry","A registry for CodeMirror theme.")}}]);
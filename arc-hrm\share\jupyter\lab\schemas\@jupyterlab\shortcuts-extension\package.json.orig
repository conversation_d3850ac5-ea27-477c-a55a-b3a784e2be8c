{"name": "@jupyterlab/shortcuts-extension", "version": "4.1.8", "description": "JupyterLab - Shortcuts Extension", "homepage": "https://github.com/jupyterlab/jupyterlab", "bugs": {"url": "https://github.com/jupyterlab/jupyterlab/issues"}, "repository": {"type": "git", "url": "https://github.com/jupyterlab/jupyterlab.git"}, "license": "BSD-3-<PERSON><PERSON>", "author": "Project Jupyter", "sideEffects": ["style/*.css", "style/index.js"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "directories": {"lib": "lib/"}, "files": ["lib/**/*.{d.ts,eot,gif,html,jpg,js,js.map,json,png,svg,woff2,ttf}", "style/**/*.{css,eot,gif,html,jpg,json,png,svg,woff2,ttf}", "style/index.js", "schema/*.json", "src/**/*.{ts,tsx}"], "scripts": {"build": "tsc -b", "build:test": "tsc --build tsconfig.test.json", "clean": "rimraf lib && rimraf tsconfig.tsbuildinfo", "docs": "typedoc src", "test": "jest", "test:cov": "jest --collect-coverage", "test:debug": "node --inspect-brk ../../node_modules/.bin/jest --runInBand", "test:debug:watch": "node --inspect-brk ../../node_modules/.bin/jest --runInBand --watch", "watch": "tsc -b --watch"}, "dependencies": {"@jupyterlab/application": "^4.1.8", "@jupyterlab/settingregistry": "^4.1.8", "@jupyterlab/translation": "^4.1.8", "@jupyterlab/ui-components": "^4.1.8", "@lumino/algorithm": "^2.0.1", "@lumino/commands": "^2.2.0", "@lumino/coreutils": "^2.1.2", "@lumino/disposable": "^2.1.2", "@lumino/domutils": "^2.0.1", "@lumino/keyboard": "^2.0.1", "@lumino/widgets": "^2.3.1", "react": "^18.2.0"}, "devDependencies": {"@jupyterlab/testing": "^4.1.8", "@types/jest": "^29.2.0", "jest": "^29.2.0", "rimraf": "~5.0.5", "typedoc": "~0.24.7", "typescript": "~5.1.6"}, "publishConfig": {"access": "public"}, "jupyterlab": {"extension": true, "schemaDir": "schema"}, "styleModule": "style/index.js"}
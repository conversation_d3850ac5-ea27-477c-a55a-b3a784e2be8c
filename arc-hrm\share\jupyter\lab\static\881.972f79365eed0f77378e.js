"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[881],{40881:(t,e,i)=>{i.r(e);i.d(e,{diagram:()=>it});var n=i(23787);var r=i(34596);var s=i(96001);var a=i(67058);var l=i(27484);var c=i.n(l);var o=i(17967);var h=i(27856);var u=i.n(h);var y=function(){var t=function(t,e,i,n){for(i=i||{},n=t.length;n--;i[t[n]]=e);return i},e=[1,3],i=[1,4],n=[1,5],r=[1,6],s=[5,6,8,9,11,13,31,32,33,34,35,36,44,62,63],a=[1,18],l=[2,7],c=[1,22],o=[1,23],h=[1,24],u=[1,25],y=[1,26],d=[1,27],p=[1,20],f=[1,28],_=[1,29],E=[62,63],g=[5,8,9,11,13,31,32,33,34,35,36,44,51,53,62,63],R=[1,47],m=[1,48],I=[1,49],b=[1,50],k=[1,51],S=[1,52],T=[1,53],N=[53,54],x=[1,64],v=[1,60],A=[1,61],q=[1,62],w=[1,63],$=[1,65],O=[1,69],C=[1,70],L=[1,67],M=[1,68],F=[5,8,9,11,13,31,32,33,34,35,36,44,62,63];var D={trace:function t(){},yy:{},symbols_:{error:2,start:3,directive:4,NEWLINE:5,RD:6,diagram:7,EOF:8,acc_title:9,acc_title_value:10,acc_descr:11,acc_descr_value:12,acc_descr_multiline_value:13,requirementDef:14,elementDef:15,relationshipDef:16,requirementType:17,requirementName:18,STRUCT_START:19,requirementBody:20,ID:21,COLONSEP:22,id:23,TEXT:24,text:25,RISK:26,riskLevel:27,VERIFYMTHD:28,verifyType:29,STRUCT_STOP:30,REQUIREMENT:31,FUNCTIONAL_REQUIREMENT:32,INTERFACE_REQUIREMENT:33,PERFORMANCE_REQUIREMENT:34,PHYSICAL_REQUIREMENT:35,DESIGN_CONSTRAINT:36,LOW_RISK:37,MED_RISK:38,HIGH_RISK:39,VERIFY_ANALYSIS:40,VERIFY_DEMONSTRATION:41,VERIFY_INSPECTION:42,VERIFY_TEST:43,ELEMENT:44,elementName:45,elementBody:46,TYPE:47,type:48,DOCREF:49,ref:50,END_ARROW_L:51,relationship:52,LINE:53,END_ARROW_R:54,CONTAINS:55,COPIES:56,DERIVES:57,SATISFIES:58,VERIFIES:59,REFINES:60,TRACES:61,unqString:62,qString:63,$accept:0,$end:1},terminals_:{2:"error",5:"NEWLINE",6:"RD",8:"EOF",9:"acc_title",10:"acc_title_value",11:"acc_descr",12:"acc_descr_value",13:"acc_descr_multiline_value",19:"STRUCT_START",21:"ID",22:"COLONSEP",24:"TEXT",26:"RISK",28:"VERIFYMTHD",30:"STRUCT_STOP",31:"REQUIREMENT",32:"FUNCTIONAL_REQUIREMENT",33:"INTERFACE_REQUIREMENT",34:"PERFORMANCE_REQUIREMENT",35:"PHYSICAL_REQUIREMENT",36:"DESIGN_CONSTRAINT",37:"LOW_RISK",38:"MED_RISK",39:"HIGH_RISK",40:"VERIFY_ANALYSIS",41:"VERIFY_DEMONSTRATION",42:"VERIFY_INSPECTION",43:"VERIFY_TEST",44:"ELEMENT",47:"TYPE",49:"DOCREF",51:"END_ARROW_L",53:"LINE",54:"END_ARROW_R",55:"CONTAINS",56:"COPIES",57:"DERIVES",58:"SATISFIES",59:"VERIFIES",60:"REFINES",61:"TRACES",62:"unqString",63:"qString"},productions_:[0,[3,3],[3,2],[3,4],[4,2],[4,2],[4,1],[7,0],[7,2],[7,2],[7,2],[7,2],[7,2],[14,5],[20,5],[20,5],[20,5],[20,5],[20,2],[20,1],[17,1],[17,1],[17,1],[17,1],[17,1],[17,1],[27,1],[27,1],[27,1],[29,1],[29,1],[29,1],[29,1],[15,5],[46,5],[46,5],[46,2],[46,1],[16,5],[16,5],[52,1],[52,1],[52,1],[52,1],[52,1],[52,1],[52,1],[18,1],[18,1],[23,1],[23,1],[25,1],[25,1],[45,1],[45,1],[48,1],[48,1],[50,1],[50,1]],performAction:function t(e,i,n,r,s,a,l){var c=a.length-1;switch(s){case 4:this.$=a[c].trim();r.setAccTitle(this.$);break;case 5:case 6:this.$=a[c].trim();r.setAccDescription(this.$);break;case 7:this.$=[];break;case 13:r.addRequirement(a[c-3],a[c-4]);break;case 14:r.setNewReqId(a[c-2]);break;case 15:r.setNewReqText(a[c-2]);break;case 16:r.setNewReqRisk(a[c-2]);break;case 17:r.setNewReqVerifyMethod(a[c-2]);break;case 20:this.$=r.RequirementType.REQUIREMENT;break;case 21:this.$=r.RequirementType.FUNCTIONAL_REQUIREMENT;break;case 22:this.$=r.RequirementType.INTERFACE_REQUIREMENT;break;case 23:this.$=r.RequirementType.PERFORMANCE_REQUIREMENT;break;case 24:this.$=r.RequirementType.PHYSICAL_REQUIREMENT;break;case 25:this.$=r.RequirementType.DESIGN_CONSTRAINT;break;case 26:this.$=r.RiskLevel.LOW_RISK;break;case 27:this.$=r.RiskLevel.MED_RISK;break;case 28:this.$=r.RiskLevel.HIGH_RISK;break;case 29:this.$=r.VerifyType.VERIFY_ANALYSIS;break;case 30:this.$=r.VerifyType.VERIFY_DEMONSTRATION;break;case 31:this.$=r.VerifyType.VERIFY_INSPECTION;break;case 32:this.$=r.VerifyType.VERIFY_TEST;break;case 33:r.addElement(a[c-3]);break;case 34:r.setNewElementType(a[c-2]);break;case 35:r.setNewElementDocRef(a[c-2]);break;case 38:r.addRelationship(a[c-2],a[c],a[c-4]);break;case 39:r.addRelationship(a[c-2],a[c-4],a[c]);break;case 40:this.$=r.Relationships.CONTAINS;break;case 41:this.$=r.Relationships.COPIES;break;case 42:this.$=r.Relationships.DERIVES;break;case 43:this.$=r.Relationships.SATISFIES;break;case 44:this.$=r.Relationships.VERIFIES;break;case 45:this.$=r.Relationships.REFINES;break;case 46:this.$=r.Relationships.TRACES;break}},table:[{3:1,4:2,6:e,9:i,11:n,13:r},{1:[3]},{3:8,4:2,5:[1,7],6:e,9:i,11:n,13:r},{5:[1,9]},{10:[1,10]},{12:[1,11]},t(s,[2,6]),{3:12,4:2,6:e,9:i,11:n,13:r},{1:[2,2]},{4:17,5:a,7:13,8:l,9:i,11:n,13:r,14:14,15:15,16:16,17:19,23:21,31:c,32:o,33:h,34:u,35:y,36:d,44:p,62:f,63:_},t(s,[2,4]),t(s,[2,5]),{1:[2,1]},{8:[1,30]},{4:17,5:a,7:31,8:l,9:i,11:n,13:r,14:14,15:15,16:16,17:19,23:21,31:c,32:o,33:h,34:u,35:y,36:d,44:p,62:f,63:_},{4:17,5:a,7:32,8:l,9:i,11:n,13:r,14:14,15:15,16:16,17:19,23:21,31:c,32:o,33:h,34:u,35:y,36:d,44:p,62:f,63:_},{4:17,5:a,7:33,8:l,9:i,11:n,13:r,14:14,15:15,16:16,17:19,23:21,31:c,32:o,33:h,34:u,35:y,36:d,44:p,62:f,63:_},{4:17,5:a,7:34,8:l,9:i,11:n,13:r,14:14,15:15,16:16,17:19,23:21,31:c,32:o,33:h,34:u,35:y,36:d,44:p,62:f,63:_},{4:17,5:a,7:35,8:l,9:i,11:n,13:r,14:14,15:15,16:16,17:19,23:21,31:c,32:o,33:h,34:u,35:y,36:d,44:p,62:f,63:_},{18:36,62:[1,37],63:[1,38]},{45:39,62:[1,40],63:[1,41]},{51:[1,42],53:[1,43]},t(E,[2,20]),t(E,[2,21]),t(E,[2,22]),t(E,[2,23]),t(E,[2,24]),t(E,[2,25]),t(g,[2,49]),t(g,[2,50]),{1:[2,3]},{8:[2,8]},{8:[2,9]},{8:[2,10]},{8:[2,11]},{8:[2,12]},{19:[1,44]},{19:[2,47]},{19:[2,48]},{19:[1,45]},{19:[2,53]},{19:[2,54]},{52:46,55:R,56:m,57:I,58:b,59:k,60:S,61:T},{52:54,55:R,56:m,57:I,58:b,59:k,60:S,61:T},{5:[1,55]},{5:[1,56]},{53:[1,57]},t(N,[2,40]),t(N,[2,41]),t(N,[2,42]),t(N,[2,43]),t(N,[2,44]),t(N,[2,45]),t(N,[2,46]),{54:[1,58]},{5:x,20:59,21:v,24:A,26:q,28:w,30:$},{5:O,30:C,46:66,47:L,49:M},{23:71,62:f,63:_},{23:72,62:f,63:_},t(F,[2,13]),{22:[1,73]},{22:[1,74]},{22:[1,75]},{22:[1,76]},{5:x,20:77,21:v,24:A,26:q,28:w,30:$},t(F,[2,19]),t(F,[2,33]),{22:[1,78]},{22:[1,79]},{5:O,30:C,46:80,47:L,49:M},t(F,[2,37]),t(F,[2,38]),t(F,[2,39]),{23:81,62:f,63:_},{25:82,62:[1,83],63:[1,84]},{27:85,37:[1,86],38:[1,87],39:[1,88]},{29:89,40:[1,90],41:[1,91],42:[1,92],43:[1,93]},t(F,[2,18]),{48:94,62:[1,95],63:[1,96]},{50:97,62:[1,98],63:[1,99]},t(F,[2,36]),{5:[1,100]},{5:[1,101]},{5:[2,51]},{5:[2,52]},{5:[1,102]},{5:[2,26]},{5:[2,27]},{5:[2,28]},{5:[1,103]},{5:[2,29]},{5:[2,30]},{5:[2,31]},{5:[2,32]},{5:[1,104]},{5:[2,55]},{5:[2,56]},{5:[1,105]},{5:[2,57]},{5:[2,58]},{5:x,20:106,21:v,24:A,26:q,28:w,30:$},{5:x,20:107,21:v,24:A,26:q,28:w,30:$},{5:x,20:108,21:v,24:A,26:q,28:w,30:$},{5:x,20:109,21:v,24:A,26:q,28:w,30:$},{5:O,30:C,46:110,47:L,49:M},{5:O,30:C,46:111,47:L,49:M},t(F,[2,14]),t(F,[2,15]),t(F,[2,16]),t(F,[2,17]),t(F,[2,34]),t(F,[2,35])],defaultActions:{8:[2,2],12:[2,1],30:[2,3],31:[2,8],32:[2,9],33:[2,10],34:[2,11],35:[2,12],37:[2,47],38:[2,48],40:[2,53],41:[2,54],83:[2,51],84:[2,52],86:[2,26],87:[2,27],88:[2,28],90:[2,29],91:[2,30],92:[2,31],93:[2,32],95:[2,55],96:[2,56],98:[2,57],99:[2,58]},parseError:function t(e,i){if(i.recoverable){this.trace(e)}else{var n=new Error(e);n.hash=i;throw n}},parse:function t(e){var i=this,n=[0],r=[],s=[null],a=[],l=this.table,c="",o=0,h=0,u=2,y=1;var d=a.slice.call(arguments,1);var p=Object.create(this.lexer);var f={yy:{}};for(var _ in this.yy){if(Object.prototype.hasOwnProperty.call(this.yy,_)){f.yy[_]=this.yy[_]}}p.setInput(e,f.yy);f.yy.lexer=p;f.yy.parser=this;if(typeof p.yylloc=="undefined"){p.yylloc={}}var E=p.yylloc;a.push(E);var g=p.options&&p.options.ranges;if(typeof f.yy.parseError==="function"){this.parseError=f.yy.parseError}else{this.parseError=Object.getPrototypeOf(this).parseError}function R(){var t;t=r.pop()||p.lex()||y;if(typeof t!=="number"){if(t instanceof Array){r=t;t=r.pop()}t=i.symbols_[t]||t}return t}var m,I,b,k,S={},T,N,x,v;while(true){I=n[n.length-1];if(this.defaultActions[I]){b=this.defaultActions[I]}else{if(m===null||typeof m=="undefined"){m=R()}b=l[I]&&l[I][m]}if(typeof b==="undefined"||!b.length||!b[0]){var A="";v=[];for(T in l[I]){if(this.terminals_[T]&&T>u){v.push("'"+this.terminals_[T]+"'")}}if(p.showPosition){A="Parse error on line "+(o+1)+":\n"+p.showPosition()+"\nExpecting "+v.join(", ")+", got '"+(this.terminals_[m]||m)+"'"}else{A="Parse error on line "+(o+1)+": Unexpected "+(m==y?"end of input":"'"+(this.terminals_[m]||m)+"'")}this.parseError(A,{text:p.match,token:this.terminals_[m]||m,line:p.yylineno,loc:E,expected:v})}if(b[0]instanceof Array&&b.length>1){throw new Error("Parse Error: multiple actions possible at state: "+I+", token: "+m)}switch(b[0]){case 1:n.push(m);s.push(p.yytext);a.push(p.yylloc);n.push(b[1]);m=null;{h=p.yyleng;c=p.yytext;o=p.yylineno;E=p.yylloc}break;case 2:N=this.productions_[b[1]][1];S.$=s[s.length-N];S._$={first_line:a[a.length-(N||1)].first_line,last_line:a[a.length-1].last_line,first_column:a[a.length-(N||1)].first_column,last_column:a[a.length-1].last_column};if(g){S._$.range=[a[a.length-(N||1)].range[0],a[a.length-1].range[1]]}k=this.performAction.apply(S,[c,h,o,f.yy,b[1],s,a].concat(d));if(typeof k!=="undefined"){return k}if(N){n=n.slice(0,-1*N*2);s=s.slice(0,-1*N);a=a.slice(0,-1*N)}n.push(this.productions_[b[1]][0]);s.push(S.$);a.push(S._$);x=l[n[n.length-2]][n[n.length-1]];n.push(x);break;case 3:return true}}return true}};var P=function(){var t={EOF:1,parseError:function t(e,i){if(this.yy.parser){this.yy.parser.parseError(e,i)}else{throw new Error(e)}},setInput:function(t,e){this.yy=e||this.yy||{};this._input=t;this._more=this._backtrack=this.done=false;this.yylineno=this.yyleng=0;this.yytext=this.matched=this.match="";this.conditionStack=["INITIAL"];this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0};if(this.options.ranges){this.yylloc.range=[0,0]}this.offset=0;return this},input:function(){var t=this._input[0];this.yytext+=t;this.yyleng++;this.offset++;this.match+=t;this.matched+=t;var e=t.match(/(?:\r\n?|\n).*/g);if(e){this.yylineno++;this.yylloc.last_line++}else{this.yylloc.last_column++}if(this.options.ranges){this.yylloc.range[1]++}this._input=this._input.slice(1);return t},unput:function(t){var e=t.length;var i=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input;this.yytext=this.yytext.substr(0,this.yytext.length-e);this.offset-=e;var n=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1);this.matched=this.matched.substr(0,this.matched.length-1);if(i.length-1){this.yylineno-=i.length-1}var r=this.yylloc.range;this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:i?(i.length===n.length?this.yylloc.first_column:0)+n[n.length-i.length].length-i[0].length:this.yylloc.first_column-e};if(this.options.ranges){this.yylloc.range=[r[0],r[0]+this.yyleng-e]}this.yyleng=this.yytext.length;return this},more:function(){this._more=true;return this},reject:function(){if(this.options.backtrack_lexer){this._backtrack=true}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}return this},less:function(t){this.unput(this.match.slice(t))},pastInput:function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var t=this.match;if(t.length<20){t+=this._input.substr(0,20-t.length)}return(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var t=this.pastInput();var e=new Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"},test_match:function(t,e){var i,n,r;if(this.options.backtrack_lexer){r={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done};if(this.options.ranges){r.yylloc.range=this.yylloc.range.slice(0)}}n=t[0].match(/(?:\r\n?|\n).*/g);if(n){this.yylineno+=n.length}this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:n?n[n.length-1].length-n[n.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length};this.yytext+=t[0];this.match+=t[0];this.matches=t;this.yyleng=this.yytext.length;if(this.options.ranges){this.yylloc.range=[this.offset,this.offset+=this.yyleng]}this._more=false;this._backtrack=false;this._input=this._input.slice(t[0].length);this.matched+=t[0];i=this.performAction.call(this,this.yy,this,e,this.conditionStack[this.conditionStack.length-1]);if(this.done&&this._input){this.done=false}if(i){return i}else if(this._backtrack){for(var s in r){this[s]=r[s]}return false}return false},next:function(){if(this.done){return this.EOF}if(!this._input){this.done=true}var t,e,i,n;if(!this._more){this.yytext="";this.match=""}var r=this._currentRules();for(var s=0;s<r.length;s++){i=this._input.match(this.rules[r[s]]);if(i&&(!e||i[0].length>e[0].length)){e=i;n=s;if(this.options.backtrack_lexer){t=this.test_match(i,r[s]);if(t!==false){return t}else if(this._backtrack){e=false;continue}else{return false}}else if(!this.options.flex){break}}}if(e){t=this.test_match(e,r[n]);if(t!==false){return t}return false}if(this._input===""){return this.EOF}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}},lex:function t(){var e=this.next();if(e){return e}else{return this.lex()}},begin:function t(e){this.conditionStack.push(e)},popState:function t(){var e=this.conditionStack.length-1;if(e>0){return this.conditionStack.pop()}else{return this.conditionStack[0]}},_currentRules:function t(){if(this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules}else{return this.conditions["INITIAL"].rules}},topState:function t(e){e=this.conditionStack.length-1-Math.abs(e||0);if(e>=0){return this.conditionStack[e]}else{return"INITIAL"}},pushState:function t(e){this.begin(e)},stateStackSize:function t(){return this.conditionStack.length},options:{"case-insensitive":true},performAction:function t(e,i,n,r){switch(n){case 0:return"title";case 1:this.begin("acc_title");return 9;case 2:this.popState();return"acc_title_value";case 3:this.begin("acc_descr");return 11;case 4:this.popState();return"acc_descr_value";case 5:this.begin("acc_descr_multiline");break;case 6:this.popState();break;case 7:return"acc_descr_multiline_value";case 8:return 5;case 9:break;case 10:break;case 11:break;case 12:return 8;case 13:return 6;case 14:return 19;case 15:return 30;case 16:return 22;case 17:return 21;case 18:return 24;case 19:return 26;case 20:return 28;case 21:return 31;case 22:return 32;case 23:return 33;case 24:return 34;case 25:return 35;case 26:return 36;case 27:return 37;case 28:return 38;case 29:return 39;case 30:return 40;case 31:return 41;case 32:return 42;case 33:return 43;case 34:return 44;case 35:return 55;case 36:return 56;case 37:return 57;case 38:return 58;case 39:return 59;case 40:return 60;case 41:return 61;case 42:return 47;case 43:return 49;case 44:return 51;case 45:return 54;case 46:return 53;case 47:this.begin("string");break;case 48:this.popState();break;case 49:return"qString";case 50:i.yytext=i.yytext.trim();return 62}},rules:[/^(?:title\s[^#\n;]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:(\r?\n)+)/i,/^(?:\s+)/i,/^(?:#[^\n]*)/i,/^(?:%[^\n]*)/i,/^(?:$)/i,/^(?:requirementDiagram\b)/i,/^(?:\{)/i,/^(?:\})/i,/^(?::)/i,/^(?:id\b)/i,/^(?:text\b)/i,/^(?:risk\b)/i,/^(?:verifyMethod\b)/i,/^(?:requirement\b)/i,/^(?:functionalRequirement\b)/i,/^(?:interfaceRequirement\b)/i,/^(?:performanceRequirement\b)/i,/^(?:physicalRequirement\b)/i,/^(?:designConstraint\b)/i,/^(?:low\b)/i,/^(?:medium\b)/i,/^(?:high\b)/i,/^(?:analysis\b)/i,/^(?:demonstration\b)/i,/^(?:inspection\b)/i,/^(?:test\b)/i,/^(?:element\b)/i,/^(?:contains\b)/i,/^(?:copies\b)/i,/^(?:derives\b)/i,/^(?:satisfies\b)/i,/^(?:verifies\b)/i,/^(?:refines\b)/i,/^(?:traces\b)/i,/^(?:type\b)/i,/^(?:docref\b)/i,/^(?:<-)/i,/^(?:->)/i,/^(?:-)/i,/^(?:["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:[\w][^\r\n\{\<\>\-\=]*)/i],conditions:{acc_descr_multiline:{rules:[6,7],inclusive:false},acc_descr:{rules:[4],inclusive:false},acc_title:{rules:[2],inclusive:false},unqString:{rules:[],inclusive:false},token:{rules:[],inclusive:false},string:{rules:[48,49],inclusive:false},INITIAL:{rules:[0,1,3,5,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,50],inclusive:true}}};return t}();D.lexer=P;function V(){this.yy={}}V.prototype=D;D.Parser=V;return new V}();y.parser=y;const d=y;let p=[];let f={};let _={};let E={};let g={};const R={REQUIREMENT:"Requirement",FUNCTIONAL_REQUIREMENT:"Functional Requirement",INTERFACE_REQUIREMENT:"Interface Requirement",PERFORMANCE_REQUIREMENT:"Performance Requirement",PHYSICAL_REQUIREMENT:"Physical Requirement",DESIGN_CONSTRAINT:"Design Constraint"};const m={LOW_RISK:"Low",MED_RISK:"Medium",HIGH_RISK:"High"};const I={VERIFY_ANALYSIS:"Analysis",VERIFY_DEMONSTRATION:"Demonstration",VERIFY_INSPECTION:"Inspection",VERIFY_TEST:"Test"};const b={CONTAINS:"contains",COPIES:"copies",DERIVES:"derives",SATISFIES:"satisfies",VERIFIES:"verifies",REFINES:"refines",TRACES:"traces"};const k=(t,e)=>{if(_[t]===void 0){_[t]={name:t,type:e,id:f.id,text:f.text,risk:f.risk,verifyMethod:f.verifyMethod}}f={};return _[t]};const S=()=>_;const T=t=>{if(f!==void 0){f.id=t}};const N=t=>{if(f!==void 0){f.text=t}};const x=t=>{if(f!==void 0){f.risk=t}};const v=t=>{if(f!==void 0){f.verifyMethod=t}};const A=t=>{if(g[t]===void 0){g[t]={name:t,type:E.type,docRef:E.docRef};n.l.info("Added new requirement: ",t)}E={};return g[t]};const q=()=>g;const w=t=>{if(E!==void 0){E.type=t}};const $=t=>{if(E!==void 0){E.docRef=t}};const O=(t,e,i)=>{p.push({type:t,src:e,dst:i})};const C=()=>p;const L=()=>{p=[];f={};_={};E={};g={};(0,n.t)()};const M={RequirementType:R,RiskLevel:m,VerifyType:I,Relationships:b,getConfig:()=>(0,n.c)().req,addRequirement:k,getRequirements:S,setNewReqId:T,setNewReqText:N,setNewReqRisk:x,setNewReqVerifyMethod:v,setAccTitle:n.s,getAccTitle:n.g,setAccDescription:n.b,getAccDescription:n.a,addElement:A,getElements:q,setNewElementType:w,setNewElementDocRef:$,addRelationship:O,getRelationships:C,clear:L};const F=t=>`\n\n  marker {\n    fill: ${t.relationColor};\n    stroke: ${t.relationColor};\n  }\n\n  marker.cross {\n    stroke: ${t.lineColor};\n  }\n\n  svg {\n    font-family: ${t.fontFamily};\n    font-size: ${t.fontSize};\n  }\n\n  .reqBox {\n    fill: ${t.requirementBackground};\n    fill-opacity: 1.0;\n    stroke: ${t.requirementBorderColor};\n    stroke-width: ${t.requirementBorderSize};\n  }\n  \n  .reqTitle, .reqLabel{\n    fill:  ${t.requirementTextColor};\n  }\n  .reqLabelBox {\n    fill: ${t.relationLabelBackground};\n    fill-opacity: 1.0;\n  }\n\n  .req-title-line {\n    stroke: ${t.requirementBorderColor};\n    stroke-width: ${t.requirementBorderSize};\n  }\n  .relationshipLine {\n    stroke: ${t.relationColor};\n    stroke-width: 1;\n  }\n  .relationshipLabel {\n    fill: ${t.relationLabelColor};\n  }\n\n`;const D=F;const P={CONTAINS:"contains",ARROW:"arrow"};const V=(t,e)=>{let i=t.append("defs").append("marker").attr("id",P.CONTAINS+"_line_ending").attr("refX",0).attr("refY",e.line_height/2).attr("markerWidth",e.line_height).attr("markerHeight",e.line_height).attr("orient","auto").append("g");i.append("circle").attr("cx",e.line_height/2).attr("cy",e.line_height/2).attr("r",e.line_height/2).attr("fill","none");i.append("line").attr("x1",0).attr("x2",e.line_height).attr("y1",e.line_height/2).attr("y2",e.line_height/2).attr("stroke-width",1);i.append("line").attr("y1",0).attr("y2",e.line_height).attr("x1",e.line_height/2).attr("x2",e.line_height/2).attr("stroke-width",1);t.append("defs").append("marker").attr("id",P.ARROW+"_line_ending").attr("refX",e.line_height).attr("refY",.5*e.line_height).attr("markerWidth",e.line_height).attr("markerHeight",e.line_height).attr("orient","auto").append("path").attr("d",`M0,0\n      L${e.line_height},${e.line_height/2}\n      M${e.line_height},${e.line_height/2}\n      L0,${e.line_height}`).attr("stroke-width",1)};const Y={ReqMarkers:P,insertLineEndings:V};let U={};let B=0;const Q=(t,e)=>t.insert("rect","#"+e).attr("class","req reqBox").attr("x",0).attr("y",0).attr("width",U.rect_min_width+"px").attr("height",U.rect_min_height+"px");const H=(t,e,i)=>{let n=U.rect_min_width/2;let r=t.append("text").attr("class","req reqLabel reqTitle").attr("id",e).attr("x",n).attr("y",U.rect_padding).attr("dominant-baseline","hanging");let s=0;i.forEach((t=>{if(s==0){r.append("tspan").attr("text-anchor","middle").attr("x",U.rect_min_width/2).attr("dy",0).text(t)}else{r.append("tspan").attr("text-anchor","middle").attr("x",U.rect_min_width/2).attr("dy",U.line_height*.75).text(t)}s++}));let a=1.5*U.rect_padding;let l=s*U.line_height*.75;let c=a+l;t.append("line").attr("class","req-title-line").attr("x1","0").attr("x2",U.rect_min_width).attr("y1",c).attr("y2",c);return{titleNode:r,y:c}};const W=(t,e,i,n)=>{let r=t.append("text").attr("class","req reqLabel").attr("id",e).attr("x",U.rect_padding).attr("y",n).attr("dominant-baseline","hanging");let s=0;const a=30;let l=[];i.forEach((t=>{let e=t.length;while(e>a&&s<3){let i=t.substring(0,a);t=t.substring(a,t.length);e=t.length;l[l.length]=i;s++}if(s==3){let t=l[l.length-1];l[l.length-1]=t.substring(0,t.length-4)+"..."}else{l[l.length]=t}s=0}));l.forEach((t=>{r.append("tspan").attr("x",U.rect_padding).attr("dy",U.line_height).text(t)}));return r};const K=(t,e,i,n)=>{const r=e.node().getTotalLength();const s=e.node().getPointAtLength(r*.5);const a="rel"+B;B++;const l=t.append("text").attr("class","req relationshipLabel").attr("id",a).attr("x",s.x).attr("y",s.y).attr("text-anchor","middle").attr("dominant-baseline","middle").text(n);const c=l.node().getBBox();t.insert("rect","#"+a).attr("class","req reqLabelBox").attr("x",s.x-c.width/2).attr("y",s.y-c.height/2).attr("width",c.width).attr("height",c.height).attr("fill","white").attr("fill-opacity","85%")};const j=function(t,e,i,s,a){const l=i.edge(Z(e.src),Z(e.dst));const c=(0,r.jvg)().x((function(t){return t.x})).y((function(t){return t.y}));const o=t.insert("path","#"+s).attr("class","er relationshipLine").attr("d",c(l.points)).attr("fill","none");if(e.type==a.db.Relationships.CONTAINS){o.attr("marker-start","url("+n.e.getUrl(U.arrowMarkerAbsolute)+"#"+e.type+"_line_ending)")}else{o.attr("stroke-dasharray","10,7");o.attr("marker-end","url("+n.e.getUrl(U.arrowMarkerAbsolute)+"#"+Y.ReqMarkers.ARROW+"_line_ending)")}K(t,o,U,`<<${e.type}>>`);return};const G=(t,e,i)=>{Object.keys(t).forEach((r=>{let s=t[r];r=Z(r);n.l.info("Added new requirement: ",r);const a=i.append("g").attr("id",r);const l="req-"+r;const c=Q(a,l);let o=H(a,r+"_title",[`<<${s.type}>>`,`${s.name}`]);W(a,r+"_body",[`Id: ${s.id}`,`Text: ${s.text}`,`Risk: ${s.risk}`,`Verification: ${s.verifyMethod}`],o.y);const h=c.node().getBBox();e.setNode(r,{width:h.width,height:h.height,shape:"rect",id:r})}))};const z=(t,e,i)=>{Object.keys(t).forEach((n=>{let r=t[n];const s=Z(n);const a=i.append("g").attr("id",s);const l="element-"+s;const c=Q(a,l);let o=H(a,l+"_title",[`<<Element>>`,`${n}`]);W(a,l+"_body",[`Type: ${r.type||"Not Specified"}`,`Doc Ref: ${r.docRef||"None"}`],o.y);const h=c.node().getBBox();e.setNode(s,{width:h.width,height:h.height,shape:"rect",id:s})}))};const X=(t,e)=>{t.forEach((function(t){let i=Z(t.src);let n=Z(t.dst);e.setEdge(i,n,{relationship:t})}));return t};const J=function(t,e){e.nodes().forEach((function(i){if(i!==void 0&&e.node(i)!==void 0){t.select("#"+i);t.select("#"+i).attr("transform","translate("+(e.node(i).x-e.node(i).width/2)+","+(e.node(i).y-e.node(i).height/2)+" )")}}));return};const Z=t=>t.replace(/\s/g,"").replace(/\./g,"_");const tt=(t,e,i,l)=>{U=(0,n.c)().requirement;const c=U.securityLevel;let o;if(c==="sandbox"){o=(0,r.Ys)("#i"+e)}const h=c==="sandbox"?(0,r.Ys)(o.nodes()[0].contentDocument.body):(0,r.Ys)("body");const u=h.select(`[id='${e}']`);Y.insertLineEndings(u,U);const y=new a.k({multigraph:false,compound:false,directed:true}).setGraph({rankdir:U.layoutDirection,marginx:20,marginy:20,nodesep:100,edgesep:100,ranksep:100}).setDefaultEdgeLabel((function(){return{}}));let d=l.db.getRequirements();let p=l.db.getElements();let f=l.db.getRelationships();G(d,y,u);z(p,y,u);X(f,y);(0,s.bK)(y);J(u,y);f.forEach((function(t){j(u,t,y,e,l)}));const _=U.rect_padding;const E=u.node().getBBox();const g=E.width+_*2;const R=E.height+_*2;(0,n.i)(u,R,g,U.useMaxWidth);u.attr("viewBox",`${E.x-_} ${E.y-_} ${g} ${R}`)};const et={draw:tt};const it={parser:d,db:M,renderer:et,styles:D}}}]);
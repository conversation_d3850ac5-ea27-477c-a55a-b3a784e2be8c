"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[6621],{78947:(e,t,s)=>{s.d(t,{q:()=>Be});var i=s(68239);const r=1540483477,n=new TextEncoder;function o(e,t){const s=n.encode(e);let i=s.length,o=t^i,a=0;for(;i>=4;){let e=255&s[a]|(255&s[++a])<<8|(255&s[++a])<<16|(255&s[++a])<<24;e=(65535&e)*r+(((e>>>16)*r&65535)<<16),e^=e>>>24,e=(65535&e)*r+(((e>>>16)*r&65535)<<16),o=(65535&o)*r+(((o>>>16)*r&65535)<<16)^e,i-=4,++a}switch(i){case 3:o^=(255&s[a+2])<<16;case 2:o^=(255&s[a+1])<<8;case 1:o^=255&s[a],o=(65535&o)*r+(((o>>>16)*r&65535)<<16)}return o^=o>>>13,o=(65535&o)*r+(((o>>>16)*r&65535)<<16),o^=o>>>15,o>>>0}class a{constructor(){this._fileParams=new Map,this._hashMethods=new Map}getCodeId(e,t){const s=this._fileParams.get(t);if(!s)throw new Error(`Kernel (${t}) has no tmp file params.`);const i=this._hashMethods.get(t);if(!i)throw new Error(`Kernel (${t}) has no hashing params.`);const{prefix:r,suffix:n}=s;return`${r}${i(e)}${n}`}setHashParams(e){const{kernel:t,method:s,seed:i}=e;if(!t)throw new TypeError("Kernel name is not defined.");if("Murmur2"!==s)throw new Error(`Hash method (${s}) is not supported.`);this._hashMethods.set(t,(e=>o(e,i).toString()))}setTmpFileParams(e){const{kernel:t,prefix:s,suffix:i}=e;if(!t)throw new TypeError("Kernel name is not defined.");this._fileParams.set(t,{kernel:t,prefix:s,suffix:i})}getTmpFileParams(e){return this._fileParams.get(e)}}var l,d=s(12982),c=s(24912),h=s(31516);!function(e){e.getCode=function(e){return new u({...e,body:new p(e),buttons:[d.Dialog.cancelButton({label:e.cancelLabel}),d.Dialog.okButton({label:e.okLabel})]}).launch()}}(l||(l={}));class u extends d.Dialog{handleEvent(e){if("keydown"===e.type){const t=e,{code:s,shiftKey:i}=t;if(i&&"Enter"===s)return this.resolve();if("Enter"===s)return}super.handleEvent(e)}}class p extends h.Widget{constructor(e){super();const{contentFactory:t,rendermime:s,mimeType:i}=e,r=new c.CodeCellModel;r.mimeType=null!=i?i:"",this._prompt=new c.CodeCell({contentFactory:t,rendermime:s,model:r,placeholder:!1}).initializeState(),this._prompt.inputArea.promptNode.remove(),this.node.appendChild(this._prompt.node)}getValue(){return this._prompt.model.sharedModel.getSource()}onAfterAttach(e){super.onAfterAttach(e),this._prompt.activate()}}var g=s(31536);class _{constructor(e){this._services=e.editorServices}createNewEditor(e){const{content:t,mimeType:s,path:i}=e,r=this._services.factoryService.newInlineEditor,n=this._services.mimeTypeService,o=new g.CodeEditor.Model({mimeType:s||n.getMimeTypeByFilePath(i)});o.sharedModel.source=t;const a=new g.CodeEditorWrapper({editorOptions:{config:{readOnly:!0,lineNumbers:!0}},model:o,factory:r});return a.node.setAttribute("data-jp-debugger","true"),a.disposed.connect((()=>{o.dispose()})),a}}var m,v=s(71677),b=s(11351),f=s(81997),w=s(38639),k=s(17811),S=s(30890);class C{constructor(e){var t,s,i,r;this._src=e.src,this._id=null!==(i=null===(s=null===(t=e.debuggerService.session)||void 0===t?void 0:t.connection)||void 0===s?void 0:s.id)&&void 0!==i?i:"",this._path=null!==(r=e.path)&&void 0!==r?r:"",this._debuggerService=e.debuggerService,this._editor=e.getEditor,this._editorMonitor=new w.ActivityMonitor({signal:this._src.changed,timeout:1e3}),this._editorMonitor.activityStopped.connect((()=>{this._sendEditorBreakpoints()}),this),this._debuggerService.model.breakpoints.changed.connect((async()=>{const e=this.editor;e&&!e.isDisposed&&this._addBreakpointsToEditor()})),this._debuggerService.model.breakpoints.restored.connect((async()=>{const e=this.editor;e&&!e.isDisposed&&this._addBreakpointsToEditor()})),this._debuggerService.model.callstack.currentFrameChanged.connect((()=>{const e=this.editor;e&&C.clearHighlight(e)})),this._breakpointEffect=k.StateEffect.define({map:(e,t)=>({pos:e.pos.map((e=>t.mapPos(e)))})}),this._breakpointState=k.StateField.define({create:()=>k.RangeSet.empty,update:(e,t)=>{e=e.map(t.changes);for(let s of t.effects)if(s.is(this._breakpointEffect)){let t=s;e=t.value.pos.length?e.update({add:t.value.pos.map((e=>m.breakpointMarker.range(e))),sort:!0}):k.RangeSet.empty}return e}}),this._gutter=new k.Compartment,this._highlightDeco=S.Decoration.line({class:"jp-DebuggerEditor-highlight"}),this._highlightState=k.StateField.define({create:()=>S.Decoration.none,update:(e,t)=>{e=e.map(t.changes);for(let s of t.effects)if(s.is(C._highlightEffect)){let t=s;e=t.value.pos.length?e.update({add:t.value.pos.map((e=>this._highlightDeco.range(e)))}):S.Decoration.none}return e},provide:e=>S.EditorView.decorations.from(e)}),e.editorReady().then((()=>{this._setupEditor()}))}get editor(){return this._editor()}dispose(){this.isDisposed||(this._editorMonitor.dispose(),this._clearEditor(),this.isDisposed=!0,f.Signal.clearData(this))}refreshBreakpoints(){this._addBreakpointsToEditor()}_setupEditor(){const e=this.editor;if(!e||e.isDisposed)return;e.setOption("lineNumbers",!0);const t=[this._breakpointState,this._highlightState,k.Prec.highest((0,S.gutter)({class:"cm-breakpoint-gutter",renderEmptyElements:!0,markers:e=>e.state.field(this._breakpointState),initialSpacer:()=>m.breakpointMarker,domEventHandlers:{mousedown:(e,t)=>(this._onGutterClick(e,t.from),!0)}}))];e.injectExtension(this._gutter.of(t)),this._addBreakpointsToEditor()}_clearEditor(){const e=this.editor;e&&!e.isDisposed&&(C.clearHighlight(e),this._clearGutter(e),e.setOption("lineNumbers",!1),e.editor.dispatch({effects:this._gutter.reconfigure([])}))}_sendEditorBreakpoints(){var e;if(null===(e=this.editor)||void 0===e?void 0:e.isDisposed)return;const t=this._getBreakpointsFromEditor().map((e=>{var t,s;return m.createBreakpoint((null===(s=null===(t=this._debuggerService.session)||void 0===t?void 0:t.connection)||void 0===s?void 0:s.name)||"",e)}));this._debuggerService.updateBreakpoints(this._src.getSource(),t,this._path)}_onGutterClick(e,t){var s,i,r;if(this._id!==(null===(i=null===(s=this._debuggerService.session)||void 0===s?void 0:s.connection)||void 0===i?void 0:i.id))return;const n=e.state.doc.lineAt(t).number;let o=e.state.field(this._breakpointState),a=!1;o.between(t,t,(()=>{a=!0}));let l=this._getBreakpoints();a?l=l.filter((e=>e.line!==n)):l.push(m.createBreakpoint(null!==(r=this._path)&&void 0!==r?r:this._debuggerService.session.connection.name,n)),l.sort(((e,t)=>e.line-t.line)),this._debuggerService.updateBreakpoints(this._src.getSource(),l,this._path)}_addBreakpointsToEditor(){var e,t;if(this._id!==(null===(t=null===(e=this._debuggerService.session)||void 0===e?void 0:e.connection)||void 0===t?void 0:t.id))return;const s=this.editor,i=this._getBreakpoints();this._clearGutter(s);const r=i.map((e=>s.state.doc.line(e.line).from));s.editor.dispatch({effects:this._breakpointEffect.of({pos:r})})}_getBreakpointsFromEditor(){const e=this.editor,t=e.editor.state.field(this._breakpointState);let s=[];return t.between(0,e.doc.length,(t=>{s.push(e.doc.lineAt(t).number)})),s}_clearGutter(e){e&&e.editor.dispatch({effects:this._breakpointEffect.of({pos:[]})})}_getBreakpoints(){const e=this._src.getSource();return this._debuggerService.model.breakpoints.getBreakpoints(this._path||this._debuggerService.getCodeId(e))}}!function(e){function t(t){t&&!t.isDisposed&&t.editor.dispatch({effects:e._highlightEffect.of({pos:[]})})}e._highlightEffect=k.StateEffect.define({map:(e,t)=>({pos:e.pos.map((e=>t.mapPos(e)))})}),e.showCurrentLine=function(s,i){t(s);const r=s,n=r.doc.line(i).from;r.editor.dispatch({effects:e._highlightEffect.of({pos:[n]})})},e.clearHighlight=t}(C||(C={})),function(e){e.breakpointMarker=new class extends S.GutterMarker{toDOM(){return document.createTextNode("●")}},e.createBreakpoint=function(e,t){return{line:t,verified:!0,source:{name:e}}}}(m||(m={}));class y{constructor(e){this._debuggerService=e.debuggerService,this._consolePanel=e.widget,this._cellMap=new b.ObservableMap;const t=this._consolePanel.console;t.promptCell&&this._addEditorHandler(t.promptCell),t.promptCellCreated.connect(((e,t)=>{this._addEditorHandler(t)}));const s=()=>{for(const e of t.cells)this._addEditorHandler(e)};s(),this._consolePanel.console.cells.changed.connect(s)}dispose(){this.isDisposed||(this.isDisposed=!0,this._cellMap.values().forEach((e=>e.dispose())),this._cellMap.dispose(),f.Signal.clearData(this))}_addEditorHandler(e){const t=e.model.id;if("code"!==e.model.type||this._cellMap.has(t))return;const s=e,i=new C({debuggerService:this._debuggerService,editorReady:async()=>(await s.ready,s.editor),getEditor:()=>s.editor,src:e.model.sharedModel});s.disposed.connect((()=>{this._cellMap.delete(t),i.dispose()})),this._cellMap.set(t,i)}}class E{constructor(e){var t;this._debuggerService=e.debuggerService,this._fileEditor=e.widget.content,this._hasLineNumber=null!==(t=this._fileEditor.editor.getOption("lineNumbers"))&&void 0!==t&&t,this._editorHandler=new C({debuggerService:this._debuggerService,editorReady:()=>Promise.resolve(this._fileEditor.editor),getEditor:()=>this._fileEditor.editor,src:this._fileEditor.model.sharedModel})}dispose(){var e,t;this.isDisposed||(this.isDisposed=!0,null===(e=this._editorHandler)||void 0===e||e.dispose(),null===(t=this._editorHandler)||void 0===t||t.editor.setOptions({lineNumbers:this._hasLineNumber}),f.Signal.clearData(this))}}class x{constructor(e){this._debuggerService=e.debuggerService,this._notebookPanel=e.widget,this._cellMap=new b.ObservableMap,this._notebookPanel.content.model.cells.changed.connect(this._onCellsChanged,this),this._onCellsChanged()}dispose(){this.isDisposed||(this.isDisposed=!0,this._cellMap.values().forEach((e=>{var t;e.dispose(),null===(t=e.editor)||void 0===t||t.setOptions({...this._notebookPanel.content.editorConfig.code})})),this._cellMap.dispose(),f.Signal.clearData(this))}_onCellsChanged(e,t){var s;if(this._notebookPanel.content.widgets.forEach((e=>this._addEditorHandler(e))),"move"===(null==t?void 0:t.type))for(const e of t.newValues)null===(s=this._cellMap.get(e.id))||void 0===s||s.refreshBreakpoints()}_addEditorHandler(e){const t=e.model.id;if("code"!==e.model.type||this._cellMap.has(t))return;const s=e,i=new C({debuggerService:this._debuggerService,editorReady:async()=>(await s.ready,s.editor),getEditor:()=>s.editor,src:e.model.sharedModel});s.disposed.connect((()=>{this._cellMap.delete(t),i.dispose()})),this._cellMap.set(e.model.id,i)}}const M="debugger-icon";function D(e,t,s=!0,i){e&&(e.enabled=s,e.pressed=t,i&&(e.onClick=i))}class T{constructor(e){this._handlers={},this._contextKernelChangedHandlers={},this._kernelChangedHandlers={},this._statusChangedHandlers={},this._iopubMessageHandlers={},this._iconButtons={},this._type=e.type,this._shell=e.shell,this._service=e.service}get activeWidget(){return this._activeWidget}async update(e,t){if(!t)return delete this._kernelChangedHandlers[e.id],delete this._statusChangedHandlers[e.id],delete this._iopubMessageHandlers[e.id],this.updateWidget(e,t);const s=()=>{this.updateWidget(e,t)},i=this._kernelChangedHandlers[e.id];i&&t.kernelChanged.disconnect(i),this._kernelChangedHandlers[e.id]=s,t.kernelChanged.connect(s);const r=(s,i)=>{i.endsWith("restarting")&&this.updateWidget(e,t)},n=this._statusChangedHandlers[e.id];n&&t.statusChanged.disconnect(n),t.statusChanged.connect(r),this._statusChangedHandlers[e.id]=r;const o=(e,t)=>{"execute_request"==t.parent_header.msg_type&&this._service.isStarted&&!this._service.hasStoppedThreads()&&this._service.displayDefinedVariables()},a=this._iopubMessageHandlers[e.id];return a&&t.iopubMessage.disconnect(a),t.iopubMessage.connect(o),this._iopubMessageHandlers[e.id]=o,this._activeWidget=e,this.updateWidget(e,t)}async updateContext(e,t){const s=()=>{const{session:s}=t;this.update(e,s)},i=this._contextKernelChangedHandlers[e.id];return i&&t.kernelChanged.disconnect(i),this._contextKernelChangedHandlers[e.id]=s,t.kernelChanged.connect(s),this.update(e,t.session)}async updateWidget(e,t){var s,r,n,o;if(!this._service.model||!t)return;const a=()=>this._shell.currentWidget===e,l=()=>{this._handlers[e.id]?e.node.setAttribute("data-jp-debugger","true"):e.node.removeAttribute("data-jp-debugger")},d=()=>{if(!this._handlers[e.id]){switch(this._type){case"notebook":this._handlers[e.id]=new x({debuggerService:this._service,widget:e});break;case"console":this._handlers[e.id]=new y({debuggerService:this._service,widget:e});break;case"file":this._handlers[e.id]=new E({debuggerService:this._service,widget:e});break;default:throw Error(`No handler for the type ${this._type}`)}l()}},c=()=>{var s,i,r,n;const o=this._handlers[e.id];o&&(o.dispose(),delete this._handlers[e.id],delete this._kernelChangedHandlers[e.id],delete this._statusChangedHandlers[e.id],delete this._iopubMessageHandlers[e.id],delete this._contextKernelChangedHandlers[e.id],(null===(i=null===(s=this._service.session)||void 0===s?void 0:s.connection)||void 0===i?void 0:i.path)!==(null==t?void 0:t.path)&&(null===(n=null===(r=this._service.session)||void 0===r?void 0:r.connection)||void 0===n?void 0:n.kernel)||this._service.model.clear(),l())},h=()=>{var e;return this._service.isStarted&&(null===(e=this._previousConnection)||void 0===e?void 0:e.id)===(null==t?void 0:t.id)},u=async()=>{this._service.session.connection=t,await this._service.stop()},p=async()=>{var e,s;this._service.session.connection=t,this._previousConnection=t,await this._service.restoreState(!0),await this._service.displayDefinedVariables(),(null===(s=null===(e=this._service.session)||void 0===e?void 0:e.capabilities)||void 0===s?void 0:s.supportsModulesRequest)&&await this._service.displayModules()},g=async()=>{if(!a())return;const t=this._iconButtons[e.id];h()?(await u(),c(),D(t,!1)):(await p(),d(),D(t,!0))};return((t=!0)=>{const s=this._iconButtons[e.id];s?D(s,this._service.isStarted,t,g):this._iconButtons[e.id]=function(e,t,s,r,n=v.nullTranslator){const o=n.load("jupyterlab"),a=new i.ToolbarButton({className:"jp-DebuggerBugButton",icon:i.bugIcon,tooltip:o.__("Enable Debugger"),pressedIcon:i.bugDotIcon,pressedTooltip:o.__("Disable Debugger"),disabledTooltip:o.__("Select a kernel that supports debugging to enable debugger"),enabled:s,pressed:r,onClick:t});return e.toolbar.insertBefore("kernelName",M,a)||e.toolbar.addItem(M,a),a}(e,g,this._service.isStarted,t)})(!1),e.disposed.connect((async()=>{h()&&await u(),c(),delete this._iconButtons[e.id],delete this._contextKernelChangedHandlers[e.id]})),await this._service.isAvailable(t)?(this._service.session?(this._previousConnection=(null===(s=this._service.session.connection)||void 0===s?void 0:s.kernel)?this._service.session.connection:null,this._service.session.connection=t):this._service.session=new Be.Session({connection:t,config:this._service.config}),await this._service.restoreState(!1),this._service.isStarted&&!this._service.hasStoppedThreads()&&(await this._service.displayDefinedVariables(),(null===(n=null===(r=this._service.session)||void 0===r?void 0:r.capabilities)||void 0===n?void 0:n.supportsModulesRequest)&&await this._service.displayModules()),D(this._iconButtons[e.id],this._service.isStarted,!0),this._service.isStarted?(d(),void(this._previousConnection=t)):(c(),this._service.session.connection=null!==(o=this._previousConnection)&&void 0!==o?o:t,void await this._service.restoreState(!1))):(c(),void D(this._iconButtons[e.id],!1,!1))}}const I=new i.LabIcon({name:"debugger:close-all",svgstr:'<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">\n  <g class="jp-icon3" fill="#616161">\n    <path d="M16.4805 17.2481C16.8158 16.3972 17 15.4701 17 14.5C17 10.3579 13.6421 7.00001 9.5 7.00001C8.36314 7.00001 7.28536 7.25295 6.31986 7.70564C7.60064 6.0592 9.60074 5 11.8482 5C15.7142 5 18.8482 8.13401 18.8482 12C18.8482 14.0897 17.9325 15.9655 16.4805 17.2481Z" />\n    <path d="M19.1607 14.2481C19.496 13.3971 19.6801 12.4701 19.6801 11.5C19.6801 7.35786 16.3223 4 12.1801 4C11.0433 4 9.9655 4.25295 9.00001 4.70563C10.2808 3.05919 12.2809 2 14.5284 2C18.3944 2 21.5284 5.134 21.5284 9C21.5284 11.0897 20.6127 12.9655 19.1607 14.2481Z" />\n    <path d="M16 15C16 18.866 12.866 22 9 22C5.13401 22 2 18.866 2 15C2 11.134 5.13401 8 9 8C12.866 8 16 11.134 16 15ZM11.7914 11L13 12.2086L10.2086 15L13 17.7914L11.7914 19L9 16.2086L6.20857 19L5 17.7914L7.79143 15L5 12.2086L6.20857 11L9 13.7914L11.7914 11Z" />\n    </g>\n</svg>\n'}),V=new i.LabIcon({name:"debugger:pause-on-exception",svgstr:'<svg height="24" width="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">\n  <g class="jp-icon3" fill="#616161" transform="matrix(1.1999396,0,0,1.3858273,-2.3726971,-4.6347192)">\n    <path\n      d="M 12.023438,0.30859375 11.158203,1.8085937 -1.2382812,23.285156 l 26.5292972,-0.002 z m 0.002,3.99999995 9.800781,16.9746093 -19.6015626,0.002 z"\n      transform="matrix(0.72509832,0,0,0.7247701,3.2918397,3.480876)"\n    />\n    <path d="m 11.144475,9.117095 h 1.666751 v 7.215906 h -1.666751 z" />\n    <path d="m 11.144475,17.054592 h 1.666751 v 1.443181 h -1.666751 z" />\n  </g>\n</svg>\n'}),B=new i.LabIcon({name:"debugger:pause",svgstr:'<svg height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">\n    <g class="jp-icon3" fill="#616161">\n        <path d="m 7,6 h 4 V 18 H 7 Z" />\n        <path d="m 13,6 h 4 v 12 h -4 z" />\n    </g>\n</svg>\n'}),H=new i.LabIcon({name:"debugger:step-into",svgstr:'<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">\n\t<g class="jp-icon3" fill="#616161">\n\t\t<path d="M7.99998 9.53198H8.54198L12.447 5.62698L11.386 4.56698L8.74898 7.17698L8.74898 0.999985H7.99998H7.25098L7.25098 7.17698L4.61398 4.56698L3.55298 5.62698L7.45798 9.53198H7.99998ZM9.95598 13.013C9.95598 14.1175 9.06055 15.013 7.95598 15.013C6.85141 15.013 5.95598 14.1175 5.95598 13.013C5.95598 11.9084 6.85141 11.013 7.95598 11.013C9.06055 11.013 9.95598 11.9084 9.95598 13.013Z"/>\n\t</g>\n</svg>\n'}),R=new i.LabIcon({name:"debugger:step-over",svgstr:'<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">\n\t<g class="jp-icon3" fill="#616161">\n\t\t<path d="M14.25 5.75V1.75H12.75V4.2916C11.605 2.93303 9.83899 2.08334 7.90914 2.08334C4.73316 2.08334 1.98941 4.39036 1.75072 7.48075L1.72992 7.75H3.231L3.25287 7.5241C3.46541 5.32932 5.45509 3.58334 7.90914 3.58334C9.6452 3.58334 11.1528 4.45925 11.9587 5.75H9.12986V7.25H13.292L14.2535 6.27493V5.75H14.25ZM7.99997 14C9.10454 14 9.99997 13.1046 9.99997 12C9.99997 10.8954 9.10454 10 7.99997 10C6.8954 10 5.99997 10.8954 5.99997 12C5.99997 13.1046 6.8954 14 7.99997 14Z"/>\n\t</g>\n</svg>\n'}),j=new i.LabIcon({name:"debugger:step-out",svgstr:'<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">\n\t<g class="jp-icon3" fill="#616161">\n\t\t<path d="M7.99998 1H7.45798L3.55298 4.905L4.61398 5.965L7.25098 3.355V9.532H7.99998H8.74898V3.355L11.386 5.965L12.447 4.905L8.54198 1H7.99998ZM9.95598 13.013C9.95598 14.1176 9.06055 15.013 7.95598 15.013C6.85141 15.013 5.95598 14.1176 5.95598 13.013C5.95598 11.9084 6.85141 11.013 7.95598 11.013C9.06055 11.013 9.95598 11.9084 9.95598 13.013Z"/>\n\t</g>\n</svg>\n'}),F=new i.LabIcon({name:"debugger:variable",svgstr:'<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">\n<path fill-rule="evenodd" clip-rule="evenodd" d="M1.5 4L1 4.5V12.5L1.5 13H4V12H2V5H4V4H1.5ZM14.5 13L15 12.5L15 4.5L14.5 4H12V5L14 5L14 12H12V13H14.5ZM8.79693 5L4.29693 7L4 7.45691V9.95691L4.24275 10.3857L6.74275 11.8857L7.20307 11.9138L11.7031 9.91381L12 9.45691V6.95691L11.7572 6.52816L9.25725 5.02816L8.79693 5ZM5 8.34V9.67381L6.5 10.5738V9.24L5 8.34ZM7.5 9.28184V10.6875L11 9.13197V7.72629L7.5 9.28184ZM10.4178 6.89071L8.96559 6.01936L5.58216 7.52311L7.03441 8.39445L10.4178 6.89071Z" fill="#007ACC"/>\n</svg>\n'}),L=new i.LabIcon({name:"debugger:view-breakpoint",svgstr:'<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">\n    <g class="jp-icon3" fill="#616161">\n        <path d="M5 2H15L20 7V20C20 20.5304 19.7893 21.0391 19.4142 21.4142C19.0391 21.7893 18.5304 22 18 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V14H4V16L8 13L4 10V12H3V4C3 3.46957 3.21071 2.96086 3.58579 2.58579C3.96086 2.21071 4.46957 2 5 2ZM12 18H16V16H12V18ZM12 14H18V12H12V14ZM12 10H18V8H12V10ZM10 14C10.5523 14 11 13.5523 11 13C11 12.4477 10.5523 12 10 12C9.44771 12 9 12.4477 9 13C9 13.5523 9.44771 14 10 14Z"/>\n        <path d="M3 12V14H1V13V12H3Z"/>\n    </g>\n</svg>\n'}),P=new i.LabIcon({name:"debugger:open-kernel-source",svgstr:'<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">\n    <g class="jp-icon3" fill="#616161">\n        <path d="M5 2H15L20 7V20C20 20.5304 19.7893 21.0391 19.4142 21.4142C19.0391 21.7893 18.5304 22 18 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V14H4V16L8 13L4 10V12H3V4C3 3.46957 3.21071 2.96086 3.58579 2.58579C3.96086 2.21071 4.46957 2 5 2ZM12 18H16V16H12V18ZM12 14H18V12H12V14ZM12 10H18V8H12V10ZM10 14C10.5523 14 11 13.5523 11 13C11 12.4477 10.5523 12 10 12C9.44771 12 9 12.4477 9 13C9 13.5523 9.44771 14 10 14Z"/>\n        <path d="M3 12V14H1V13V12H3Z"/>\n    </g>\n</svg>\n'});class O{constructor(){this._breakpoints=new Map,this._changed=new f.Signal(this),this._restored=new f.Signal(this),this._clicked=new f.Signal(this)}get changed(){return this._changed}get restored(){return this._restored}get clicked(){return this._clicked}get breakpoints(){return this._breakpoints}setBreakpoints(e,t){this._breakpoints.set(e,t),this._changed.emit(t)}getBreakpoints(e){var t;return null!==(t=this._breakpoints.get(e))&&void 0!==t?t:[]}restoreBreakpoints(e){this._breakpoints=e,this._restored.emit()}}class N{constructor(){this._state=[],this._currentFrame=null,this._framesChanged=new f.Signal(this),this._currentFrameChanged=new f.Signal(this)}get frames(){return this._state}set frames(e){this._state=e;const t=null!==this.frame?A.getFrameId(this.frame):"";e.find((e=>A.getFrameId(e)===t))||(this.frame=e[0]),this._framesChanged.emit(e)}get frame(){return this._currentFrame}set frame(e){this._currentFrame=e,this._currentFrameChanged.emit(e)}get framesChanged(){return this._framesChanged}get currentFrameChanged(){return this._currentFrameChanged}}var A;!function(e){e.getFrameId=function(e){var t;return`${null===(t=null==e?void 0:e.source)||void 0===t?void 0:t.path}-${null==e?void 0:e.id}`}}(A||(A={}));class q{constructor(e){this._currentSourceOpened=new f.Signal(this),this._currentSourceChanged=new f.Signal(this),this.currentFrameChanged=e.currentFrameChanged}get currentSourceOpened(){return this._currentSourceOpened}get currentSourceChanged(){return this._currentSourceChanged}get currentSource(){return this._currentSource}set currentSource(e){this._currentSource=e,this._currentSourceChanged.emit(e)}open(){this._currentSourceOpened.emit(this._currentSource)}}var W=s(97934);const Z=(e,t)=>e.name<t.name?-1:e.name>t.name?1:0;class K{constructor(){this._filteredKernelSources=null,this._filter="",this._isDisposed=!1,this._kernelSources=null,this._changed=new f.Signal(this),this._filterChanged=new f.Signal(this),this._kernelSourceOpened=new f.Signal(this),this.refresh=this.refresh.bind(this),this._refreshDebouncer=new W.Debouncer(this.refresh,500)}get filter(){return this._filter}set filter(e){this._filter=e,this._filterChanged.emit(e),this._refreshDebouncer.invoke()}get isDisposed(){return this._isDisposed}get kernelSources(){return this._kernelSources}set kernelSources(e){this._kernelSources=e,this.refresh()}get changed(){return this._changed}get filterChanged(){return this._filterChanged}get kernelSourceOpened(){return this._kernelSourceOpened}dispose(){this._isDisposed||(this._isDisposed=!0,this._refreshDebouncer.dispose(),f.Signal.clearData(this))}open(e){this._kernelSourceOpened.emit(e)}getFilteredKernelSources(){const e=new RegExp(this._filter);return this._kernelSources.filter((t=>e.test(t.name)))}refresh(){this._kernelSources?(this._filteredKernelSources=this._filter?this.getFilteredKernelSources():this._kernelSources,this._filteredKernelSources.sort(Z)):(this._kernelSources=new Array,this._filteredKernelSources=new Array),this._changed.emit(this._filteredKernelSources)}}class G{constructor(){this._selectedVariable=null,this._state=[],this._variableExpanded=new f.Signal(this),this._changed=new f.Signal(this)}get scopes(){return this._state}set scopes(e){this._state=e,this._changed.emit()}get changed(){return this._changed}get variableExpanded(){return this._variableExpanded}get selectedVariable(){return this._selectedVariable}set selectedVariable(e){this._selectedVariable=e}expandVariable(e){this._variableExpanded.emit(e)}}class z{constructor(){this._disposed=new f.Signal(this),this._isDisposed=!1,this._hasRichVariableRendering=!1,this._supportCopyToGlobals=!1,this._stoppedThreads=new Set,this._title="-",this._titleChanged=new f.Signal(this),this.breakpoints=new O,this.callstack=new N,this.variables=new G,this.sources=new q({currentFrameChanged:this.callstack.currentFrameChanged}),this.kernelSources=new K}get disposed(){return this._disposed}get hasRichVariableRendering(){return this._hasRichVariableRendering}set hasRichVariableRendering(e){this._hasRichVariableRendering=e}get supportCopyToGlobals(){return this._supportCopyToGlobals}set supportCopyToGlobals(e){this._supportCopyToGlobals=e}get isDisposed(){return this._isDisposed}get stoppedThreads(){return this._stoppedThreads}set stoppedThreads(e){this._stoppedThreads=e}get title(){return this._title}set title(e){e!==this._title&&(this._title=null!=e?e:"-",this._titleChanged.emit(e))}get titleChanged(){return this._titleChanged}dispose(){this._isDisposed||(this._isDisposed=!0,this.kernelSources.dispose(),this._disposed.emit())}clear(){this._stoppedThreads.clear();const e=new Map;this.breakpoints.restoreBreakpoints(e),this.callstack.frames=[],this.variables.scopes=[],this.sources.currentSource=null,this.kernelSources.kernelSources=null,this.title="-"}}class $ extends h.Panel{constructor(e){super(),this._filter=new Set,this._grid=null,this._pending=null,this.commands=e.commands,this.model=e.model,this.themeManager=e.themeManager,this.translator=e.translator,this.model.changed.connect((()=>this.update()),this),this.addClass("jp-DebuggerVariables-body")}get filter(){return this._filter}set filter(e){this._filter=e,this.update()}get scope(){return this._scope}set scope(e){this._scope=e,"Globals"!==e?this.addClass("jp-debuggerVariables-local"):this.removeClass("jp-debuggerVariables-local"),this.update()}async initialize(){if(this._grid||this._pending)return;const{Grid:e}=await(this._pending=Promise.all([s.e(5585),s.e(1958)]).then(s.bind(s,31958))),{commands:t,model:i,themeManager:r,translator:n}=this;this._grid=new e({commands:t,model:i,themeManager:r,translator:n}),this._grid.addClass("jp-DebuggerVariables-grid"),this._pending=null,this.addWidget(this._grid),this.update()}onBeforeShow(e){this._grid||this._pending||this.initialize(),super.onBeforeShow(e)}onUpdateRequest(e){var t;if(this._grid){const{dataModel:e}=this._grid;e.filter=this._filter,e.scope=this._scope,e.setData(null!==(t=this.model.scopes)&&void 0!==t?t:[])}super.onUpdateRequest(e)}}var U=s(70856),J=s(20998);class Y extends d.MainAreaWidget{constructor(e){const{dataLoader:t,rendermime:s,translator:i}=e,r=new h.Panel,n=new J.PromiseDelegate;super({content:r,reveal:Promise.all([t,n.promise])}),this.content.addClass("jp-VariableRendererPanel"),this.trans=(null!=i?i:v.nullTranslator).load("jupyterlab"),this.dataLoader=t,this.renderMime=s,this._dataHash=null,this.refresh().then((()=>{n.resolve()})).catch((e=>n.reject(e)))}async refresh(e=!1){let t=await this.dataLoader();if(0===Object.keys(t.data).length&&(t={data:{"text/plain":this.trans.__("The variable is undefined in the active context.")},metadata:{}}),!t.data)return this._dataHash=null,Promise.reject("Unable to get a view on the variable.");{const s=o(JSON.stringify(t),17);if(e||this._dataHash!==s){this.content.layout&&this.content.widgets.forEach((e=>{this.content.layout.removeWidget(e)}));const e=this.renderMime.preferredMimeType(t.data,"any");if(!e)return this._dataHash=null,Promise.reject("Unable to determine the preferred mime type.");{const i=this.renderMime.createRenderer(e);i.addClass("jp-VariableRendererPanel-renderer");const r=new U.MimeModel({...t,trusted:!0});this._dataHash=s,await i.renderModel(r),this.content.addWidget(i)}}}}}class Q{constructor(e){var t,s;this._eventMessage=new f.Signal(this),this._isDisposed=!1,this._sessionChanged=new f.Signal(this),this._pauseOnExceptionChanged=new f.Signal(this),this._config=e.config,this._session=null,this._specsManager=null!==(t=e.specsManager)&&void 0!==t?t:null,this._model=new Be.Model,this._debuggerSources=null!==(s=e.debuggerSources)&&void 0!==s?s:null,this._trans=(e.translator||v.nullTranslator).load("jupyterlab")}get eventMessage(){return this._eventMessage}get config(){return this._config}get isDisposed(){return this._isDisposed}get isStarted(){var e,t;return null!==(t=null===(e=this._session)||void 0===e?void 0:e.isStarted)&&void 0!==t&&t}get pauseOnExceptionChanged(){return this._pauseOnExceptionChanged}get model(){return this._model}get session(){return this._session}set session(e){var t;this._session!==e&&(this._session&&this._session.dispose(),this._session=e,null===(t=this._session)||void 0===t||t.eventMessage.connect(((e,t)=>{"stopped"===t.event?(this._model.stoppedThreads.add(t.body.threadId),this._getAllFrames()):"continued"===t.event&&(this._model.stoppedThreads.delete(t.body.threadId),this._clearModel(),this._clearSignals()),this._eventMessage.emit(t)})),this._sessionChanged.emit(e))}get sessionChanged(){return this._sessionChanged}dispose(){this.isDisposed||(this._isDisposed=!0,f.Signal.clearData(this))}getCodeId(e){var t,s,i,r;try{return this._config.getCodeId(e,null!==(r=null===(i=null===(s=null===(t=this.session)||void 0===t?void 0:t.connection)||void 0===s?void 0:s.kernel)||void 0===i?void 0:i.name)&&void 0!==r?r:"")}catch(e){return""}}hasStoppedThreads(){var e,t;return null!==(t=(null===(e=this._model)||void 0===e?void 0:e.stoppedThreads.size)>0)&&void 0!==t&&t}async isAvailable(e){var t,s,i,r;if(!this._specsManager)return!0;await this._specsManager.ready;const n=null==e?void 0:e.kernel;if(!n)return!1;const o=n.name;return!(null===(t=this._specsManager.specs)||void 0===t?void 0:t.kernelspecs[o])||!(null===(r=null===(i=null===(s=this._specsManager.specs.kernelspecs[o])||void 0===s?void 0:s.metadata)||void 0===i?void 0:i.debugger)||void 0===r||!r)}async clearBreakpoints(){var e;if(!0!==(null===(e=this.session)||void 0===e?void 0:e.isStarted))return;this._model.breakpoints.breakpoints.forEach(((e,t,s)=>{this._setBreakpoints([],t)}));let t=new Map;this._model.breakpoints.restoreBreakpoints(t)}async continue(){try{if(!this.session)throw new Error("No active debugger session");await this.session.sendRequest("continue",{threadId:this._currentThread()}),this._model.stoppedThreads.delete(this._currentThread()),this._clearModel(),this._clearSignals()}catch(e){console.error("Error:",e.message)}}async getSource(e){var t,s;if(!this.session)throw new Error("No active debugger session");return{...(await this.session.sendRequest("source",{source:e,sourceReference:null!==(t=e.sourceReference)&&void 0!==t?t:0})).body,path:null!==(s=e.path)&&void 0!==s?s:""}}async evaluate(e){var t;if(!this.session)throw new Error("No active debugger session");const s=null===(t=this.model.callstack.frame)||void 0===t?void 0:t.id,i=await this.session.sendRequest("evaluate",{context:"repl",expression:e,frameId:s});return i.success?(this._clearModel(),await this._getAllFrames(),i.body):null}async next(){try{if(!this.session)throw new Error("No active debugger session");await this.session.sendRequest("next",{threadId:this._currentThread()})}catch(e){console.error("Error:",e.message)}}async inspectRichVariable(e,t){if(!this.session)throw new Error("No active debugger session");const s=await this.session.sendRequest("richInspectVariables",{variableName:e,frameId:t});if(s.success)return s.body;throw new Error(s.message)}async inspectVariable(e){if(!this.session)throw new Error("No active debugger session");const t=await this.session.sendRequest("variables",{variablesReference:e});if(t.success)return t.body.variables;throw new Error(t.message)}async copyToGlobals(e){if(!this.session)throw new Error("No active debugger session");if(!this.model.supportCopyToGlobals)throw new Error('The "copyToGlobals" request is not supported by the kernel');const t=this.model.callstack.frames;this.session.sendRequest("copyToGlobals",{srcVariableName:e,dstVariableName:e,srcFrameId:t[0].id}).then((async()=>{const e=await this._getScopes(t[0]),s=await Promise.all(e.map((e=>this._getVariables(e)))),i=this._convertScopes(e,s);this._model.variables.scopes=i})).catch((e=>{console.error(e)}))}async displayDefinedVariables(){if(!this.session)throw new Error("No active debugger session");const e=(await this.session.sendRequest("inspectVariables",{})).body.variables,t=[{name:this._trans.__("Globals"),variables:e}];this._model.variables.scopes=t}async displayModules(){if(!this.session)throw new Error("No active debugger session");const e=await this.session.sendRequest("modules",{});this._model.kernelSources.kernelSources=e.body.modules.map((e=>({name:e.name,path:e.path})))}async restart(){const{breakpoints:e}=this._model.breakpoints;await this.stop(),await this.start(),await this._restoreBreakpoints(e)}async restoreState(e){var t,s,i,r,n,o,a,l,d,c;if(!this.model||!this.session)return;const h=await this.session.restoreState(),{body:u}=h,p=this._mapBreakpoints(u.breakpoints),g=new Set(u.stoppedThreads);if(this._model.hasRichVariableRendering=!0===u.richRendering,this._model.supportCopyToGlobals=!0===u.copyToGlobals,this._config.setHashParams({kernel:null!==(r=null===(i=null===(s=null===(t=this.session)||void 0===t?void 0:t.connection)||void 0===s?void 0:s.kernel)||void 0===i?void 0:i.name)&&void 0!==r?r:"",method:u.hashMethod,seed:u.hashSeed}),this._config.setTmpFileParams({kernel:null!==(l=null===(a=null===(o=null===(n=this.session)||void 0===n?void 0:n.connection)||void 0===o?void 0:o.kernel)||void 0===a?void 0:a.name)&&void 0!==l?l:"",prefix:u.tmpFilePrefix,suffix:u.tmpFileSuffix}),this._model.stoppedThreads=g,this.isStarted||!e&&0===g.size||await this.start(),(this.isStarted||e)&&(this._model.title=this.isStarted&&(null===(c=null===(d=this.session)||void 0===d?void 0:d.connection)||void 0===c?void 0:c.name)||"-"),this._debuggerSources){const e=this._filterBreakpoints(p);this._model.breakpoints.restoreBreakpoints(e)}else this._model.breakpoints.restoreBreakpoints(p);0!==g.size?await this._getAllFrames():this.isStarted&&(this._clearModel(),this._clearSignals()),this.session.currentExceptionFilters&&await this.pauseOnExceptions(this.session.currentExceptionFilters)}start(){if(!this.session)throw new Error("No active debugger session");return this.session.start()}async pause(){try{if(!this.session)throw new Error("No active debugger session");await this.session.sendRequest("pause",{threadId:this._currentThread()})}catch(e){console.error("Error:",e.message)}}async stepIn(){try{if(!this.session)throw new Error("No active debugger session");await this.session.sendRequest("stepIn",{threadId:this._currentThread()})}catch(e){console.error("Error:",e.message)}}async stepOut(){try{if(!this.session)throw new Error("No active debugger session");await this.session.sendRequest("stepOut",{threadId:this._currentThread()})}catch(e){console.error("Error:",e.message)}}async stop(){if(!this.session)throw new Error("No active debugger session");await this.session.stop(),this._model&&this._model.clear()}async updateBreakpoints(e,t,s){var i;if(!(null===(i=this.session)||void 0===i?void 0:i.isStarted))return;s||(s=(await this._dumpCell(e)).body.sourcePath);const r=await this.session.restoreState(),n=t.filter((({line:e})=>"number"==typeof e)).map((({line:e})=>({line:e}))),o=this._mapBreakpoints(r.body.breakpoints);if(this._debuggerSources){const e=this._filterBreakpoints(o);this._model.breakpoints.restoreBreakpoints(e)}else this._model.breakpoints.restoreBreakpoints(o);let a=new Set;const l=(await this._setBreakpoints(n,s)).body.breakpoints.filter(((e,t,s)=>{const i=s.findIndex((t=>t.line===e.line))>-1,r=!a.has(e.line);return a.add(e.line),i&&r}));this._model.breakpoints.setBreakpoints(s,l),await this.session.sendRequest("configurationDone",{})}pauseOnExceptionsIsValid(){var e,t;return!(!this.isStarted||0===(null===(t=null===(e=this.session)||void 0===e?void 0:e.exceptionBreakpointFilters)||void 0===t?void 0:t.length))}async pauseOnExceptionsFilter(e){var t;if(!(null===(t=this.session)||void 0===t?void 0:t.isStarted))return;let s=this.session.currentExceptionFilters;if(this.session.isPausingOnException(e)){const t=s.indexOf(e);s.splice(t,1)}else null==s||s.push(e);await this.pauseOnExceptions(s)}async pauseOnExceptions(e){var t,s;if(!(null===(t=this.session)||void 0===t?void 0:t.isStarted))return;const i=(null===(s=this.session.exceptionBreakpointFilters)||void 0===s?void 0:s.map((e=>e.filter)))||[];let r={filters:[]};e.forEach((e=>{i.includes(e)&&r.filters.push(e)})),this.session.currentExceptionFilters=r.filters,await this.session.sendRequest("setExceptionBreakpoints",r),this._pauseOnExceptionChanged.emit()}getDebuggerState(){var e,t,s,i,r,n,o;const a=this._model.breakpoints.breakpoints;let l=[];if(this._debuggerSources)for(const d of a.keys()){const a=this._debuggerSources.find({focus:!1,kernel:null!==(i=null===(s=null===(t=null===(e=this.session)||void 0===e?void 0:e.connection)||void 0===t?void 0:t.kernel)||void 0===s?void 0:s.name)&&void 0!==i?i:"",path:null!==(o=null===(n=null===(r=this._session)||void 0===r?void 0:r.connection)||void 0===n?void 0:n.path)&&void 0!==o?o:"",source:d}).map((e=>e.src.getSource()));l=l.concat(a)}return{cells:l,breakpoints:a}}async restoreDebuggerState(e){var t,s,i,r;await this.start();for(const t of e.cells)await this._dumpCell(t);const n=new Map,o=null!==(r=null===(i=null===(s=null===(t=this.session)||void 0===t?void 0:t.connection)||void 0===s?void 0:s.kernel)||void 0===i?void 0:i.name)&&void 0!==r?r:"",{prefix:a,suffix:l}=this._config.getTmpFileParams(o);for(const t of e.breakpoints){const[e,s]=t,i=e.substr(0,e.length-l.length),r=i.substr(i.lastIndexOf("/")+1),o=a.concat(r).concat(l);n.set(o,s)}await this._restoreBreakpoints(n);const d=await this.session.sendRequest("configurationDone",{});return await this.restoreState(!1),d.success}_clearModel(){this._model.callstack.frames=[],this._model.variables.scopes=[]}_clearSignals(){this._model.callstack.currentFrameChanged.disconnect(this._onCurrentFrameChanged,this),this._model.variables.variableExpanded.disconnect(this._onVariableExpanded,this)}_convertScopes(e,t){return t&&e?e.map(((e,s)=>({name:e.name,variables:t[s].map((e=>({...e})))}))):[]}_currentThread(){return 1}async _dumpCell(e){if(!this.session)throw new Error("No active debugger session");return this.session.sendRequest("dumpCell",{code:e})}_filterBreakpoints(e){if(!this._debuggerSources)return e;let t=new Map;for(const s of e){const[e,i]=s;i.forEach((()=>{var s,r,n,o,a,l,d;this._debuggerSources.find({focus:!1,kernel:null!==(o=null===(n=null===(r=null===(s=this.session)||void 0===s?void 0:s.connection)||void 0===r?void 0:r.kernel)||void 0===n?void 0:n.name)&&void 0!==o?o:"",path:null!==(d=null===(l=null===(a=this._session)||void 0===a?void 0:a.connection)||void 0===l?void 0:l.path)&&void 0!==d?d:"",source:e}).forEach((()=>{i.length>0&&t.set(e,i)}))}))}return t}async _getAllFrames(){this._model.callstack.currentFrameChanged.connect(this._onCurrentFrameChanged,this),this._model.variables.variableExpanded.connect(this._onVariableExpanded,this);const e=await this._getFrames(this._currentThread());this._model.callstack.frames=e}async _getFrames(e){if(!this.session)throw new Error("No active debugger session");return(await this.session.sendRequest("stackTrace",{threadId:e})).body.stackFrames}async _getScopes(e){if(!this.session)throw new Error("No active debugger session");return e?(await this.session.sendRequest("scopes",{frameId:e.id})).body.scopes:[]}async _getVariables(e){if(!this.session)throw new Error("No active debugger session");return e?(await this.session.sendRequest("variables",{variablesReference:e.variablesReference})).body.variables:[]}_mapBreakpoints(e){return e.length?e.reduce(((e,t)=>{const{breakpoints:s,source:i}=t;return e.set(i,s.map((e=>({...e,source:{path:i},verified:!0})))),e}),new Map):new Map}async _onCurrentFrameChanged(e,t){if(!t)return;const s=await this._getScopes(t),i=await Promise.all(s.map((e=>this._getVariables(e)))),r=this._convertScopes(s,i);this._model.variables.scopes=r}async _onVariableExpanded(e,t){if(!this.session)throw new Error("No active debugger session");const s=await this.session.sendRequest("variables",{variablesReference:t.variablesReference});let i={...t,expanded:!0};s.body.variables.forEach((e=>{i={[e.name]:e,...i}}));const r=this._model.variables.scopes.map((e=>{const s=e.variables.findIndex((e=>e.variablesReference===t.variablesReference));return e.variables[s]=i,{...e}}));return this._model.variables.scopes=[...r],s.body.variables}async _setBreakpoints(e,t){if(!this.session)throw new Error("No active debugger session");return await this.session.sendRequest("setBreakpoints",{breakpoints:e,source:{path:t},sourceModified:!1})}async _restoreBreakpoints(e){for(const[t,s]of e)await this._setBreakpoints(s.filter((({line:e})=>"number"==typeof e)).map((({line:e})=>({line:e}))),t);this._model.breakpoints.restoreBreakpoints(e)}}class X{constructor(e){this._seq=0,this._ready=new J.PromiseDelegate,this._isDisposed=!1,this._isStarted=!1,this._exceptionPaths=[],this._exceptionBreakpointFilters=[],this._currentExceptionFilters={},this._disposed=new f.Signal(this),this._eventMessage=new f.Signal(this),this.connection=e.connection,this._config=e.config,this.translator=e.translator||v.nullTranslator}get isDisposed(){return this._isDisposed}get capabilities(){return this._capabilities}get disposed(){return this._disposed}get connection(){return this._connection}set connection(e){var t,s;if(this._connection&&this._connection.iopubMessage.disconnect(this._handleEvent,this),this._connection=e,!this._connection)return void(this._isStarted=!1);this._connection.iopubMessage.connect(this._handleEvent,this),this._ready=new J.PromiseDelegate;const i=null===(s=null===(t=this.connection)||void 0===t?void 0:t.kernel)||void 0===s?void 0:s.requestDebug({type:"request",seq:0,command:"debugInfo"});i&&(i.onReply=e=>{this._ready.resolve(),i.dispose()})}get isStarted(){return this._isStarted}get exceptionPaths(){return this._exceptionPaths}get exceptionBreakpointFilters(){return this._exceptionBreakpointFilters}get currentExceptionFilters(){var e,t,s;const i=null!==(s=null===(t=null===(e=this.connection)||void 0===e?void 0:e.kernel)||void 0===t?void 0:t.name)&&void 0!==s?s:"";if(!i)return[];const r=this._config.getTmpFileParams(i);if(!r)return[];let n=r.prefix;return Object.keys(this._currentExceptionFilters).includes(n)?this._currentExceptionFilters[n]:[]}set currentExceptionFilters(e){var t,s,i;const r=null!==(i=null===(s=null===(t=this.connection)||void 0===t?void 0:t.kernel)||void 0===s?void 0:s.name)&&void 0!==i?i:"";if(!r)return;const n=this._config.getTmpFileParams(r);if(!n)return;let o=n.prefix;null===e?Object.keys(this._currentExceptionFilters).includes(o)&&delete this._currentExceptionFilters[o]:this._currentExceptionFilters[o]=e}get eventMessage(){return this._eventMessage}dispose(){this._isDisposed||(this._isDisposed=!0,this._disposed.emit(),f.Signal.clearData(this))}async start(){var e,t,s,i;const r=await this.sendRequest("initialize",{clientID:"jupyterlab",clientName:"JupyterLab",adapterID:null!==(s=null===(t=null===(e=this.connection)||void 0===e?void 0:e.kernel)||void 0===t?void 0:t.name)&&void 0!==s?s:"",pathFormat:"path",linesStartAt1:!0,columnsStartAt1:!0,supportsVariableType:!0,supportsVariablePaging:!0,supportsRunInTerminalRequest:!0,locale:document.documentElement.lang});if(!r.success)throw new Error(`Could not start the debugger: ${r.message}`);this._capabilities=r.body,this._isStarted=!0,this._exceptionBreakpointFilters=null===(i=r.body)||void 0===i?void 0:i.exceptionBreakpointFilters,await this.sendRequest("attach",{})}async stop(){this._isStarted=!1,await this.sendRequest("disconnect",{restart:!1,terminateDebuggee:!1})}async restoreState(){var e;const t=await this.sendRequest("debugInfo",{});return this._isStarted=t.body.isStarted,this._exceptionPaths=null===(e=t.body)||void 0===e?void 0:e.exceptionPaths,t}isPausingOnException(e){var t,s;return e?null!==(s=null===(t=this.currentExceptionFilters)||void 0===t?void 0:t.includes(e))&&void 0!==s&&s:this.currentExceptionFilters.length>0}async sendRequest(e,t){return await this._ready.promise,(await this._sendDebugMessage({type:"request",seq:this._seq++,command:e,arguments:t})).content}_handleEvent(e,t){if("debug_event"!==t.header.msg_type)return;const s=t.content;this._eventMessage.emit(s)}async _sendDebugMessage(e){var t;const s=null===(t=this.connection)||void 0===t?void 0:t.kernel;if(!s)return Promise.reject(new Error("A kernel is required to send debug messages."));const i=new J.PromiseDelegate,r=s.requestDebug(e);return r.onReply=e=>{i.resolve(e)},await r.done,i.promise}}var ee=s(78156),te=s.n(ee);class se extends i.ReactWidget{constructor(e){super(),this._model=e,this.addClass("jp-DebuggerBreakpoints-body")}render(){return te().createElement(ie,{model:this._model})}}const ie=({model:e})=>{const[t,s]=(0,ee.useState)(Array.from(e.breakpoints.entries()));return(0,ee.useEffect)((()=>{const t=(t,i)=>{s(Array.from(e.breakpoints.entries()))},i=t=>{s(Array.from(e.breakpoints.entries()))};return e.changed.connect(t),e.restored.connect(i),()=>{e.changed.disconnect(t),e.restored.disconnect(i)}})),te().createElement(te().Fragment,null,t.map((t=>te().createElement(re,{key:t[0],breakpoints:t[1],model:e}))))},re=({breakpoints:e,model:t})=>te().createElement(te().Fragment,null,e.sort(((e,t)=>{var s,i;return(null!==(s=e.line)&&void 0!==s?s:0)-(null!==(i=t.line)&&void 0!==i?i:0)})).map(((e,s)=>{var i,r;return te().createElement(ne,{key:(null!==(r=null===(i=e.source)||void 0===i?void 0:i.path)&&void 0!==r?r:"")+s,breakpoint:e,model:t})}))),ne=({breakpoint:e,model:t})=>{var s,i,r,n;return te().createElement("div",{className:"jp-DebuggerBreakpoint",onClick:()=>t.clicked.emit(e),title:null===(s=e.source)||void 0===s?void 0:s.path},te().createElement("span",{className:"jp-DebuggerBreakpoint-marker"},"●"),te().createElement("span",{className:"jp-DebuggerBreakpoint-source jp-left-truncated"},"/"===(n=null!==(r=null===(i=e.source)||void 0===i?void 0:i.path)&&void 0!==r?r:"")[0]?n.slice(1)+"/":n),te().createElement("span",{className:"jp-DebuggerBreakpoint-line"},e.line))},oe="jp-PauseOnExceptions";class ae extends i.ToolbarButton{constructor(e){super(),this.onclick=()=>{this._menu.open(this.node.getBoundingClientRect().left,this.node.getBoundingClientRect().bottom)},this._menu=new le({service:e.service,commands:{registry:e.commands.registry,pauseOnExceptions:e.commands.pauseOnExceptions}}),this.node.className="jp-debugger-pauseOnExceptions",this._props=e,this._props.className=oe,this._props.service.eventMessage.connect(((e,t)=>{"initialized"!==t.event&&"terminated"!==t.event||this.onChange()}),this),this._props.enabled=this._props.service.pauseOnExceptionsIsValid(),this._props.service.pauseOnExceptionChanged.connect(this.onChange,this)}onChange(){var e;const t=this._props.service.session,s=null==t?void 0:t.exceptionBreakpointFilters;this._props.className=oe,(null===(e=this._props.service.session)||void 0===e?void 0:e.isStarted)&&s?(this._props.pressed=t.isPausingOnException(),this._props.enabled=!0):this._props.enabled=!1,this.update()}render(){return ee.createElement(i.ToolbarButtonComponent,{...this._props,onClick:this.onclick})}}class le extends i.MenuSvg{constructor(e){super({commands:e.commands.registry}),this._service=e.service,this._command=e.commands.pauseOnExceptions,e.service.eventMessage.connect(((e,t)=>{"initialized"===t.event&&this._build()}),this),this._build(),this.addClass("jp-PauseOnExceptions-menu")}_build(){var e,t;this.clearItems(),(null!==(t=null===(e=this._service.session)||void 0===e?void 0:e.exceptionBreakpointFilters)&&void 0!==t?t:[]).map(((e,t)=>{this.addItem({command:this._command,args:{filter:e.filter,description:e.description}})}))}}class de extends i.PanelWithToolbar{constructor(e){var t;super(e),this.clicked=new f.Signal(this);const{model:s,service:r,commands:n}=e,o=(null!==(t=e.translator)&&void 0!==t?t:v.nullTranslator).load("jupyterlab");this.title.label=o.__("Breakpoints");const a=new se(s);this.toolbar.addItem("pauseOnException",new ae({service:r,commands:n,icon:V,tooltip:o.__("Pause on exception filter")})),this.toolbar.addItem("closeAll",new i.ToolbarButton({icon:I,onClick:async()=>{if(0!==s.breakpoints.size)return(await(0,d.showDialog)({title:o.__("Remove All Breakpoints"),body:o.__("Are you sure you want to remove all breakpoints?"),buttons:[d.Dialog.okButton({label:o.__("Remove breakpoints")}),d.Dialog.cancelButton()],hasClose:!0})).button.accept?r.clearBreakpoints():void 0},tooltip:o.__("Remove All Breakpoints")})),this.addWidget(a),this.addClass("jp-DebuggerBreakpoints")}}class ce extends i.ReactWidget{constructor(e){super(),this._model=e,this.addClass("jp-DebuggerCallstack-body")}render(){return te().createElement(he,{model:this._model})}}const he=({model:e})=>{const[t,s]=(0,ee.useState)(e.frames),[i,r]=(0,ee.useState)(e.frame);return(0,ee.useEffect)((()=>{const t=()=>{r(e.frame),s(e.frames)};return e.framesChanged.connect(t),()=>{e.framesChanged.disconnect(t)}}),[e]),te().createElement("ul",null,t.map((t=>{var s;return te().createElement("li",{key:t.id,onClick:()=>{return r(s=t),void(e.frame=s);var s},className:(null==i?void 0:i.id)===t.id?"selected jp-DebuggerCallstackFrame":"jp-DebuggerCallstackFrame"},te().createElement("span",{className:"jp-DebuggerCallstackFrame-name"},t.name),te().createElement("span",{className:"jp-DebuggerCallstackFrame-location",title:null===(s=t.source)||void 0===s?void 0:s.path},(e=>{var t;const s=(null===(t=e.source)||void 0===t?void 0:t.path)||"",i=w.PathExt.basename(w.PathExt.dirname(s)),r=w.PathExt.basename(s);return`${w.PathExt.join(i,r)}:${e.line}`})(t)))})))};class ue extends i.PanelWithToolbar{constructor(e){var t;super(e);const{commands:s,model:r}=e,n=(null!==(t=e.translator)&&void 0!==t?t:v.nullTranslator).load("jupyterlab");this.title.label=n.__("Callstack");const o=new ce(r);this.toolbar.addItem("continue",new i.CommandToolbarButton({commands:s.registry,id:s.continue,label:""})),this.toolbar.addItem("terminate",new i.CommandToolbarButton({commands:s.registry,id:s.terminate,label:""})),this.toolbar.addItem("step-over",new i.CommandToolbarButton({commands:s.registry,id:s.next,label:""})),this.toolbar.addItem("step-in",new i.CommandToolbarButton({commands:s.registry,id:s.stepIn,label:""})),this.toolbar.addItem("step-out",new i.CommandToolbarButton({commands:s.registry,id:s.stepOut,label:""})),this.toolbar.addItem("evaluate",new i.CommandToolbarButton({commands:s.registry,id:s.evaluate,label:""})),this.addWidget(o),this.addClass("jp-DebuggerCallstack")}}const pe=({model:e})=>te().createElement(i.UseSignal,{signal:e.currentSourceChanged,initialSender:e},(e=>{var t,s;return te().createElement("span",{onClick:()=>null==e?void 0:e.open(),className:"jp-DebuggerSources-header-path"},null!==(s=null===(t=null==e?void 0:e.currentSource)||void 0===t?void 0:t.path)&&void 0!==s?s:"")}));class ge extends h.Widget{constructor(e){super(),this._model=e.model,this._debuggerService=e.service,this._mimeTypeService=e.editorServices.mimeTypeService;const t=new Be.ReadOnlyEditorFactory({editorServices:e.editorServices});this._editor=t.createNewEditor({content:"",mimeType:"",path:""}),this._editor.hide(),this._model.currentFrameChanged.connect((async(e,t)=>{t?this._showSource(t):this._clearEditor()}));const s=new h.PanelLayout;s.addWidget(this._editor),this.layout=s,this.addClass("jp-DebuggerSources-body")}dispose(){var e;this.isDisposed||(null===(e=this._editorHandler)||void 0===e||e.dispose(),f.Signal.clearData(this),super.dispose())}_clearEditor(){this._model.currentSource=null,this._editor.hide()}async _showSource(e){var t;const s=null===(t=e.source)||void 0===t?void 0:t.path,i=await this._debuggerService.getSource({sourceReference:0,path:s});if(!(null==i?void 0:i.content))return void this._clearEditor();this._editorHandler&&this._editorHandler.dispose();const{content:r,mimeType:n}=i,o=n||this._mimeTypeService.getMimeTypeByFilePath(null!=s?s:"");this._editor.model.sharedModel.setSource(r),this._editor.model.mimeType=o,this._editorHandler=new C({debuggerService:this._debuggerService,editorReady:()=>Promise.resolve(this._editor.editor),getEditor:()=>this._editor.editor,path:s,src:this._editor.model.sharedModel}),this._model.currentSource={content:r,mimeType:o,path:null!=s?s:""},requestAnimationFrame((()=>{C.showCurrentLine(this._editor.editor,e.line)})),this._editor.show()}}class _e extends i.PanelWithToolbar{constructor(e){var t;super();const{model:s,service:r,editorServices:n}=e,o=(null!==(t=e.translator)&&void 0!==t?t:v.nullTranslator).load("jupyterlab");this.title.label=o.__("Source"),this.toolbar.addClass("jp-DebuggerSources-header");const a=new ge({service:r,model:s,editorServices:n});this.toolbar.addItem("open",new i.ToolbarButton({icon:L,onClick:()=>s.open(),tooltip:o.__("Open in the Main Area")}));const l=i.ReactWidget.create(te().createElement(pe,{model:s}));this.toolbar.addItem("sourcePath",l),this.addClass("jp-DebuggerSources-header"),this.addWidget(a),this.addClass("jp-DebuggerSources")}}const me=e=>te().createElement(i.InputGroup,{type:"text",onChange:t=>{const s=t.target.value;e.model.filter=s},placeholder:"Filter the kernel sources",value:e.model.filter}),ve=e=>te().createElement(i.UseSignal,{signal:e.model.filterChanged,initialArgs:e.model.filter},(t=>te().createElement(me,{model:e.model})));class be extends i.ReactWidget{constructor(e){var t;super(),this._showFilter=!1,this._model=e.model,this._debuggerService=e.service,this._trans=(null!==(t=e.translator)&&void 0!==t?t:v.nullTranslator).load("jupyterlab"),this.addClass("jp-DebuggerKernelSources-body")}render(){let e="jp-DebuggerKernelSource-filterBox";return this._showFilter||(e+=" jp-DebuggerKernelSource-filterBox-hidden"),te().createElement(te().Fragment,null,te().createElement("div",{className:e,key:"filter"},te().createElement(ve,{model:this._model})),te().createElement(i.UseSignal,{signal:this._model.changed},((e,t)=>{const s={};return(null!=t?t:[]).map((e=>{var t;const r=e.name,n=e.path,o=r+(s[r]=(null!==(t=s[r])&&void 0!==t?t:0)+1).toString();return te().createElement("div",{key:o,title:n,className:"jp-DebuggerKernelSource-source",onClick:()=>{this._debuggerService.getSource({sourceReference:0,path:n}).then((e=>{this._model.open(e)})).catch((e=>{(0,d.showErrorMessage)(this._trans.__("Fail to get source"),this._trans.__("Fail to get '%1' source:\n%2",n,e))}))}},te().createElement(i.LabIcon.resolveReact,{icon:P,iconClass:(0,i.classes)("jp-Icon"),tag:null}),r)}))})))}toggleFilterbox(){this._showFilter=!this._showFilter,this.update()}}class fe extends i.PanelWithToolbar{constructor(e){var t;super();const{model:s,service:r}=e;this._model=s;const n=(null!==(t=e.translator)&&void 0!==t?t:v.nullTranslator).load("jupyterlab");this.title.label=n.__("Kernel Sources"),this.toolbar.addClass("jp-DebuggerKernelSources-header"),this._body=new be({service:r,model:s,translator:e.translator}),this.toolbar.addItem("open-filter",new i.ToolbarButton({icon:i.searchIcon,onClick:async()=>{this._body.toggleFilterbox()},tooltip:n.__("Toggle search filter")})),this.toolbar.addItem("refresh",new i.ToolbarButton({icon:i.refreshIcon,onClick:()=>{this._model.kernelSources=[],r.displayModules().catch((e=>{(0,d.showErrorMessage)(n.__("Fail to get kernel sources"),n.__("Fail to get kernel sources:\n%2",e))}))},tooltip:n.__("Refresh kernel sources")})),this.addClass("jp-DebuggerKernelSources-header"),this.addWidget(this._body),this.addClass("jp-DebuggerKenelSources")}set filter(e){this._model.filter=e}}const we=({model:e,tree:t,grid:s,trans:r})=>{const[n,o]=(0,ee.useState)("-"),a=e.scopes;return te().createElement(i.HTMLSelect,{onChange:e=>{const i=e.target.value;o(i),t.scope=i,s.scope=i},value:n,"aria-label":r.__("Scope")},a.map((e=>te().createElement("option",{key:e.name,value:e.name},r.__(e.name)))))};class ke extends i.ReactWidget{constructor(e){super();const{translator:t,model:s,tree:i,grid:r}=e;this._model=s,this._tree=i,this._grid=r,this._trans=(t||v.nullTranslator).load("jupyterlab")}render(){return te().createElement(i.UseSignal,{signal:this._model.changed,initialSender:this._model},(()=>te().createElement(we,{model:this._model,trans:this._trans,tree:this._tree,grid:this._grid})))}}var Se=s(33625);const Ce="jp-DebuggerVariables-buttons";class ye extends i.ReactWidget{constructor(e){super(),this._scope="",this._scopes=[],this._filter=new Set,this._commands=e.commands,this._service=e.service,this._translator=e.translator,this._hoverChanged=new f.Signal(this),(this.model=e.model).changed.connect(this._updateScopes,this),this.addClass("jp-DebuggerVariables-body")}render(){var e;const t=null!==(e=this._scopes.find((e=>e.name===this._scope)))&&void 0!==e?e:this._scopes[0],s=e=>{this.model.selectedVariable=e},r=te().createElement(i.caretDownEmptyIcon.react,{stylesheet:"menuItem",tag:"span"});return"Globals"!==(null==t?void 0:t.name)?this.addClass("jp-debuggerVariables-local"):this.removeClass("jp-debuggerVariables-local"),t?te().createElement(te().Fragment,null,te().createElement(xe,{key:t.name,commands:this._commands,service:this._service,data:t.variables,filter:this._filter,translator:this._translator,handleSelectVariable:s,onHoverChanged:e=>{this._hoverChanged.emit(e)},collapserIcon:r}),te().createElement(Ee,{commands:this._commands,service:this._service,hoverChanged:this._hoverChanged,handleSelectVariable:s})):te().createElement("div",null)}set filter(e){this._filter=e,this.update()}set scope(e){this._scope=e,this.update()}_updateScopes(e){Se.ArrayExt.shallowEqual(this._scopes,e.scopes)||(this._scopes=e.scopes,this.update())}}const Ee=e=>{var t;const{commands:s,service:r,translator:n,handleSelectVariable:o}=e,a=(null!=n?n:v.nullTranslator).load("jupyterlab"),[l,d]=(0,ee.useState)(0),[c,h]=(0,ee.useState)(null);let u=0;const p=(0,ee.useCallback)(((e,t)=>{const s=++u;if(t.variable)h(t.variable),requestAnimationFrame((()=>{s===u&&t.target&&d(t.target.offsetTop)}));else{if(s!==u)return;const e=t.target;if(e&&e instanceof Element&&e.closest(`.${Ce}`))return;h(null)}}),[]);return(0,ee.useEffect)((()=>(e.hoverChanged.connect(p),()=>{e.hoverChanged.disconnect(p)})),[p]),te().createElement("div",{className:Ce,style:{transform:`translateY(${l}px)`,opacity:!c||["special variables","protected variables","function variables","class variables"].includes(c.name)?0:1}},te().createElement("button",{className:"jp-DebuggerVariables-renderVariable",disabled:!c||!r.model.hasRichVariableRendering||!s.isEnabled(Be.CommandIDs.renderMimeVariable,{name:c.name,frameID:null===(t=r.model.callstack.frame)||void 0===t?void 0:t.id}),onClick:e=>{var t;c&&o&&(e.stopPropagation(),o(c),s.execute(Be.CommandIDs.renderMimeVariable,{name:c.name,frameID:null===(t=r.model.callstack.frame)||void 0===t?void 0:t.id}).catch((e=>{console.error(`Failed to render variable ${null==c?void 0:c.name}`,e)})))},title:a.__("Render variable: %1",null==c?void 0:c.name)},te().createElement(i.searchIcon.react,{stylesheet:"menuItem",tag:"span"})))},xe=e=>{const{commands:t,data:s,service:i,filter:r,translator:n,handleSelectVariable:o,onHoverChanged:a,collapserIcon:l}=e,[d,c]=(0,ee.useState)(s);return(0,ee.useEffect)((()=>{c(s)}),[s]),te().createElement("ul",{className:"jp-DebuggerVariables-branch"},d.filter((e=>!(r||new Set).has(e.evaluateName||""))).map((e=>{const s=`${e.name}-${e.evaluateName}-${e.type}-${e.value}-${e.variablesReference}`;return te().createElement(Me,{key:s,commands:t,data:e,service:i,filter:r,translator:n,onSelect:o,onHoverChanged:a,collapserIcon:l})})))},Me=e=>{const{commands:t,data:s,service:i,filter:r,translator:n,onSelect:o,onHoverChanged:a,collapserIcon:l}=e,[d]=(0,ee.useState)(s),[c,h]=(0,ee.useState)(),[u,p]=(0,ee.useState)(),g=null!=o?o:()=>{},_=0!==d.variablesReference||"function"===d.type;return te().createElement("li",{onClick:e=>(async e=>{if(!_)return;e.stopPropagation();const t=await i.inspectVariable(d.variablesReference);h(!c),p(t)})(e),onMouseDown:e=>{e.stopPropagation(),g(d)},onMouseOver:e=>{a&&(a({target:e.currentTarget,variable:d}),e.stopPropagation())},onMouseLeave:e=>{a&&(a({target:e.relatedTarget,variable:null}),e.stopPropagation())}},te().createElement("span",{className:"jp-DebuggerVariables-collapser"+(c?" jp-mod-expanded":"")},_?te().cloneElement(l):null),te().createElement("span",{className:"jp-DebuggerVariables-name"},d.name),te().createElement("span",{className:"jp-DebuggerVariables-detail"},function(e){if("float"===e.type&&("inf"==e.value||"-inf"==e.value))return e.value;const t=Te(e);return"float"===e.type&&isNaN(t)?"NaN":t}(d)),c&&u&&te().createElement(xe,{key:d.name,commands:t,data:u,service:i,filter:r,translator:n,handleSelectVariable:o,onHoverChanged:a,collapserIcon:l}))};class De extends i.PanelWithToolbar{constructor(e){super(e);const{model:t,service:s,commands:r,themeManager:n}=e,o=e.translator||v.nullTranslator,a=o.load("jupyterlab");this.title.label=a.__("Variables"),this.toolbar.addClass("jp-DebuggerVariables-toolbar"),this._tree=new ye({model:t,service:s,commands:r,translator:o}),this._table=new $({model:t,commands:r,themeManager:n,translator:o}),this._table.hide(),this.toolbar.addItem("scope-switcher",new ke({translator:o,model:t,tree:this._tree,grid:this._table}));const l=()=>{this._table.isHidden?(this._tree.hide(),this._table.show(),this.node.setAttribute("data-jp-table","true"),h("table")):(this._tree.show(),this._table.hide(),this.node.removeAttribute("data-jp-table"),h("tree")),this.update()},d=new i.ToolbarButton({icon:i.treeViewIcon,className:"jp-TreeView",onClick:l,tooltip:a.__("Tree View")}),c=new i.ToolbarButton({icon:i.tableRowsIcon,className:"jp-TableView",onClick:l,tooltip:a.__("Table View")}),h=e=>{c.pressed="tree"!==e,d.pressed=!c.pressed};h(this._table.isHidden?"tree":"table"),this.toolbar.addItem("view-VariableTreeView",d),this.toolbar.addItem("view-VariableTableView",c),this.addWidget(this._tree),this.addWidget(this._table),this.addClass("jp-DebuggerVariables")}set filter(e){this._tree.filter=e,this._table.filter=e}onResize(e){super.onResize(e),this._resizeBody(e)}_resizeBody(e){const t=e.height-this.toolbar.node.offsetHeight;this._tree.node.style.height=`${t}px`}}const Te=e=>{var t,s;const{type:i,value:r}=e;switch(i){case"int":return parseInt(r,10);case"float":return parseFloat(r);case"bool":return r;case"str":return(null===(s=null===(t=e.presentationHint)||void 0===t?void 0:t.attributes)||void 0===s?void 0:s.includes("rawString"))?r.slice(1,r.length-1):r;default:return null!=i?i:r}};class Ie extends i.SidePanel{constructor(e){const t=e.translator||v.nullTranslator;super({translator:t}),this.id="jp-debugger-sidebar",this.title.icon=i.bugIcon,this.addClass("jp-DebuggerSidebar");const{callstackCommands:s,breakpointsCommands:r,editorServices:n,service:o,themeManager:a}=e,l=o.model;this.variables=new De({model:l.variables,commands:s.registry,service:o,themeManager:a,translator:t}),this.callstack=new ue({commands:s,model:l.callstack,translator:t}),this.breakpoints=new de({service:o,commands:r,model:l.breakpoints,translator:t}),this.sources=new _e({model:l.sources,service:o,editorServices:n,translator:t}),this.kernelSources=new fe({model:l.kernelSources,service:o,translator:t});const d=new Ie.Header;this.header.addWidget(d),l.titleChanged.connect(((e,t)=>{d.title.label=t})),this.content.addClass("jp-DebuggerSidebar-body"),this.addWidget(this.variables),this.addWidget(this.callstack),this.addWidget(this.breakpoints),this.addWidget(this.sources),this.addWidget(this.kernelSources)}}var Ve,Be;!function(e){class t extends h.Widget{constructor(){super({node:Ve.createHeader()}),this.title.changed.connect((e=>{this.node.textContent=this.title.label}))}}e.Header=t}(Ie||(Ie={})),function(e){e.createHeader=function(){const e=document.createElement("h2");return e.textContent="-",e.classList.add("jp-text-truncated"),e}}(Ve||(Ve={}));class He{constructor(e){var t,s,i;this._config=e.config,this._shell=e.shell,this._notebookTracker=null!==(t=e.notebookTracker)&&void 0!==t?t:null,this._consoleTracker=null!==(s=e.consoleTracker)&&void 0!==s?s:null,this._editorTracker=null!==(i=e.editorTracker)&&void 0!==i?i:null,this._readOnlyEditorTracker=new d.WidgetTracker({namespace:"@jupyterlab/debugger"})}find(e){return[...this._findInConsoles(e),...this._findInEditors(e),...this._findInNotebooks(e),...this._findInReadOnlyEditors(e)]}open(e){const{editorWrapper:t,label:s,caption:r}=e,n=new d.MainAreaWidget({content:t});n.id=d.DOMUtils.createDomID(),n.title.label=s,n.title.closable=!0,n.title.caption=r,n.title.icon=i.textEditorIcon,this._shell.add(n,"main",{type:"Debugger Sources"}),this._readOnlyEditorTracker.add(n)}_findInNotebooks(e){if(!this._notebookTracker)return[];const{focus:t,kernel:s,path:i,source:r}=e,n=[];return this._notebookTracker.forEach((e=>{const o=e.sessionContext;if(i!==o.path)return;const a=e.content;t&&(a.mode="command"),e.content.widgets.forEach(((i,o)=>{const l=i.model.sharedModel.getSource(),d=this._getCodeId(l,s);d&&r===d&&(t&&(a.activeCellIndex=o,a.activeCell&&a.scrollToItem(a.activeCellIndex).catch((e=>{})),this._shell.activateById(e.id)),n.push(Object.freeze({get:()=>i.editor,reveal:()=>a.scrollToItem(o),src:i.model.sharedModel})))}))})),n}_findInConsoles(e){if(!this._consoleTracker)return[];const{focus:t,kernel:s,path:i,source:r}=e,n=[];return this._consoleTracker.forEach((e=>{const o=e.sessionContext;if(i!==o.path)return;const a=e.console.cells;for(const i of a){const o=i.model.sharedModel.getSource(),a=this._getCodeId(o,s);if(!a)break;if(r!==a)break;n.push(Object.freeze({get:()=>i.editor,reveal:()=>Promise.resolve(this._shell.activateById(e.id)),src:i.model.sharedModel})),t&&this._shell.activateById(e.id)}})),n}_findInEditors(e){if(!this._editorTracker)return[];const{focus:t,kernel:s,path:i,source:r}=e,n=[];return this._editorTracker.forEach((e=>{const o=e.content;if(i!==o.context.path)return;const a=o.editor;if(!a)return;const l=a.model.sharedModel.getSource(),d=this._getCodeId(l,s);d&&r===d&&(n.push(Object.freeze({get:()=>a,reveal:()=>Promise.resolve(this._shell.activateById(e.id)),src:o.model.sharedModel})),t&&this._shell.activateById(e.id))})),n}_findInReadOnlyEditors(e){const{focus:t,kernel:s,source:i}=e,r=[];return this._readOnlyEditorTracker.forEach((e=>{var n;const o=null===(n=e.content)||void 0===n?void 0:n.editor;if(!o)return;const a=o.model.sharedModel.getSource(),l=this._getCodeId(a,s);l&&(e.title.caption!==i&&i!==l||(r.push(Object.freeze({get:()=>o,reveal:()=>Promise.resolve(this._shell.activateById(e.id)),src:o.model.sharedModel})),t&&this._shell.activateById(e.id)))})),r}_getCodeId(e,t){try{return this._config.getCodeId(e,t)}catch(e){return""}}}!function(e){let t,s,r;e.Config=class extends a{},e.EditorHandler=class extends C{},e.Handler=class extends T{},e.Model=class extends z{},e.ReadOnlyEditorFactory=class extends _{},e.Service=class extends Q{},e.Session=class extends X{},e.Sidebar=class extends Ie{},e.Sources=class extends He{},e.VariablesGrid=class extends ${},e.VariableRenderer=class extends Y{},function(e){e.debugContinue="debugger:continue",e.terminate="debugger:terminate",e.next="debugger:next",e.showPanel="debugger:show-panel",e.stepIn="debugger:stepIn",e.stepOut="debugger:stepOut",e.inspectVariable="debugger:inspect-variable",e.renderMimeVariable="debugger:render-mime-variable",e.evaluate="debugger:evaluate",e.restartDebug="debugger:restart-debug",e.pauseOnExceptions="debugger:pause-on-exceptions",e.copyToClipboard="debugger:copy-to-clipboard",e.copyToGlobals="debugger:copy-to-globals",e.openSource="debugger:open-source"}(t=e.CommandIDs||(e.CommandIDs={})),function(e){e.closeAllIcon=I,e.evaluateIcon=i.codeIcon,e.continueIcon=i.runIcon,e.pauseIcon=B,e.stepIntoIcon=H,e.stepOutIcon=j,e.stepOverIcon=R,e.terminateIcon=i.stopIcon,e.variableIcon=F,e.viewBreakpointIcon=L,e.pauseOnExceptionsIcon=B}(s=e.Icons||(e.Icons={})),function(e){e.getCode=l.getCode}(r=e.Dialogs||(e.Dialogs={}))}(Be||(Be={}))},36621:(e,t,s)=>{s.r(t),s.d(t,{Debugger:()=>i.q,IDebugger:()=>n,IDebuggerConfig:()=>o,IDebuggerHandler:()=>d,IDebuggerSidebar:()=>l,IDebuggerSourceViewer:()=>c,IDebuggerSources:()=>a});var i=s(78947),r=s(20998);const n=new r.Token("@jupyterlab/debugger:IDebugger","A debugger user interface."),o=new r.Token("@jupyterlab/debugger:IDebuggerConfig","A service to handle the debugger configuration."),a=new r.Token("@jupyterlab/debugger:IDebuggerSources","A service to display sources in debug mode."),l=new r.Token("@jupyterlab/debugger:IDebuggerSidebar","A service for the debugger sidebar."),d=new r.Token("@jupyterlab/debugger:IDebuggerHandler","A service for handling notebook debugger."),c=new r.Token("@jupyterlab/debugger:IDebuggerSourceViewer","A debugger source viewer.")}}]);
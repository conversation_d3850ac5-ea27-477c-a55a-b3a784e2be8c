"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[8579],{88579:(e,t,o)=>{o.r(t),o.d(t,{default:()=>q});var n=o(3053),a=o(12982),i=o(46122),r=o(38639),d=o(90157),l=o(35312),s=o(97104),c=o(70856),p=o(36768),u=o(71677),g=o(42266),m=o(50331),h=o(20998),b=o(2549),f=o(31516);const v=new RegExp("/(notebooks|edit)/(.*)"),y=/\.ipynb$/;var k;!function(e){e.handleLink="application:handle-local-link",e.toggleTop="application:toggle-top",e.togglePanel="application:toggle-panel",e.toggleZen="application:toggle-zen",e.openLab="application:open-lab",e.openTree="application:open-tree",e.rename="application:rename",e.resolveTree="application:resolve-tree"}(k||(k={}));const w={id:"@jupyter-notebook/application-extension:dirty",description:"Check if the application is dirty before closing the browser tab.",autoStart:!0,requires:[n.ILabStatus,u.ITranslator],activate:(e,t,o)=>{if(!(e instanceof g.NotebookApp))throw new Error(`${w.id} must be activated in Jupyter Notebook.`);const n=o.load("notebook").__("Are you sure you want to exit Jupyter Notebook?\n\nAny unsaved changes will be lost.");window.addEventListener("beforeunload",(t=>{if(e.status.isDirty)return t.returnValue=n}))}},x={id:"@jupyter-notebook/application-extension:logo",description:"The logo plugin.",autoStart:!0,activate:e=>{const t=r.PageConfig.getBaseUrl(),o=document.createElement("a");o.href=`${t}tree`,o.target="_blank",o.rel="noopener noreferrer";const n=new f.Widget({node:o});m.jupyterIcon.element({container:o,elementPosition:"center",padding:"2px 2px 2px 8px",height:"28px",width:"auto",cursor:"pointer",margin:"auto"}),n.id="jp-NotebookLogo",e.shell.add(n,"top",{rank:0})}},I={id:"@jupyter-notebook/application-extension:opener",description:"A plugin to open documents in the main area.",autoStart:!0,requires:[n.IRouter,d.IDocumentManager],optional:[p.ISettingRegistry],activate:(e,t,o,n)=>{const{commands:a,docRegistry:i}=e,r="router:tree";a.addCommand(r,{execute:t=>{var a;const r=t,d=null!==(a=r.path.match(v))&&void 0!==a?a:[],[,,l]=d;l&&e.started.then((async()=>{var e;const t=decodeURIComponent(l),a=new URLSearchParams(r.search);let d=i.defaultWidgetFactory(l).name;if(n){const e=(await n.load("@jupyterlab/docmanager-extension:plugin")).get("defaultViewers").composite;i.getFileTypesForPath(l).forEach((t=>{void 0!==e[t.name]&&i.getWidgetFactory(e[t.name])&&(d=e[t.name])}))}const s=null!==(e=a.get("factory"))&&void 0!==e?e:d;o.open(t,s,void 0,{ref:"_noref"})}))}}),t.register({command:r,pattern:v})}},P={id:"@jupyter-notebook/application-extension:menus",description:"A plugin to customize menus.",requires:[s.IMainMenu],autoStart:!0,activate:(e,t)=>{switch(t.tabsMenu.dispose(),r.PageConfig.getOption("notebookPage")){case"consoles":case"terminals":case"tree":t.editMenu.dispose(),t.kernelMenu.dispose(),t.runMenu.dispose();break;case"edit":t.kernelMenu.dispose(),t.runMenu.dispose()}}},C={id:"@jupyter-notebook/application-extension:menu-spacer",description:"A plugin to provide a spacer at rank 900 in the menu area.",autoStart:!0,activate:e=>{const t=new f.Widget;t.id=a.DOMUtils.createDomID(),t.addClass("jp-NotebookSpacer"),e.shell.add(t,"menu",{rank:900})}},S={id:"@jupyter-notebook/application-extension:pages",description:"Add commands to open the tree and running pages.",autoStart:!0,requires:[u.ITranslator],optional:[a.ICommandPalette],activate:(e,t,o)=>{const n=t.load("notebook"),a=r.PageConfig.getBaseUrl();e.commands.addCommand(k.openLab,{label:n.__("Open JupyterLab"),execute:()=>{window.open(r.URLExt.join(a,"lab"))}});const i=r.PageConfig.getOption("notebookPage");e.commands.addCommand(k.openTree,{label:n.__("File Browser"),execute:()=>{"tree"===i?e.commands.execute("filebrowser:activate"):window.open(r.URLExt.join(a,"tree"))}}),o&&(o.addItem({command:k.openLab,category:"View"}),o.addItem({command:k.openTree,category:"View"}))}},T={id:"@jupyter-notebook/application-extension:path-opener",description:"A plugin to open paths in new browser tabs.",autoStart:!0,provides:g.INotebookPathOpener,activate:e=>g.defaultNotebookPathOpener},R={id:"@jupyter-notebook/application-extension:paths",description:"The default paths for a Jupyter Notebook app.",autoStart:!0,provides:n.JupyterFrontEnd.IPaths,activate:e=>{if(!(e instanceof g.NotebookApp))throw new Error(`${R.id} must be activated in Jupyter Notebook.`);return e.paths}},E={id:"@jupyter-notebook/application-extension:rendermime",description:"A plugin providing a rendermime registry.",autoStart:!0,provides:c.IRenderMimeRegistry,optional:[d.IDocumentManager,c.ILatexTypesetter,a.ISanitizer,c.IMarkdownParser,u.ITranslator,g.INotebookPathOpener],activate:(e,t,o,n,a,i,d)=>{const l=(null!=i?i:u.nullTranslator).load("jupyterlab"),s=null!=d?d:g.defaultNotebookPathOpener;return t&&e.commands.addCommand(k.handleLink,{label:l.__("Handle Local Link"),execute:e=>{const o=e.path;if(null!=o)return t.services.contents.get(o,{content:!1}).then((e=>{const t=r.PageConfig.getBaseUrl();s.open({prefix:r.URLExt.join(t,"tree"),path:e.path,target:"_blank"})}))}}),new c.RenderMimeRegistry({initialFactories:c.standardRendererFactories,linkHandler:t?{handleLink:(t,o,n)=>{"A"===t.tagName&&t.hasAttribute("download")||e.commandLinker.connectNode(t,k.handleLink,{path:o,id:n})}}:void 0,latexTypesetter:null!=o?o:void 0,markdownParser:null!=a?a:void 0,translator:null!=i?i:void 0,sanitizer:null!=n?n:void 0})}},_={id:"@jupyter-notebook/application-extension:shell",description:"The default Jupyter Notebook application shell.",autoStart:!0,provides:g.INotebookShell,optional:[p.ISettingRegistry],activate:(e,t)=>{if(!(e.shell instanceof g.NotebookShell))throw new Error(`${_.id} did not find a NotebookShell instance.`);const o=e.shell;return t&&t.load(_.id).then((e=>{const t=e.composite.layout;o.restoreLayout(t)})).catch((e=>{console.error("Fail to load settings for the layout restorer."),console.error(e)})),o}},L={id:"@jupyter-notebook/application-extension:splash",description:"Provides an empty splash screen.",autoStart:!0,provides:a.ISplashScreen,activate:e=>{const{restored:t}=e,o=document.createElement("div");return o.style.position="absolute",o.style.width="100%",o.style.height="100%",o.style.zIndex="10",{show:(e=!0)=>(o.style.backgroundColor=e?"white":"#111111",document.body.appendChild(o),new b.DisposableDelegate((async()=>{await t,document.body.removeChild(o)})))}}},j={id:"@jupyter-notebook/application-extension:status",description:"The default JupyterLab application status provider.",autoStart:!0,provides:n.ILabStatus,activate:e=>{if(!(e instanceof g.NotebookApp))throw new Error(`${j.id} must be activated in Jupyter Notebook.`);return e.status}},N={id:"@jupyter-notebook/application-extension:tab-title",description:"A plugin to display the document title in the browser tab title.",autoStart:!0,requires:[g.INotebookShell],activate:(e,t)=>{const o=()=>{const e=t.currentWidget;if(e instanceof i.ConsolePanel){const t=()=>{const t=e.sessionContext.path||e.sessionContext.name,o=r.PathExt.basename(t);document.title=o.replace(y,"")};return e.sessionContext.sessionChanged.connect(t),void t()}if(e instanceof l.DocumentWidget){const t=()=>{const t=r.PathExt.basename(e.context.path);document.title=t.replace(y,"")};e.context.pathChanged.connect(t),t()}};t.currentChanged.connect(o),o()}},H={id:"@jupyter-notebook/application-extension:title",description:"A plugin to display and rename the title of a file.",autoStart:!0,requires:[g.INotebookShell,u.ITranslator],optional:[d.IDocumentManager,n.IRouter,a.IToolbarWidgetRegistry],activate:(e,t,o,n,a,i)=>{const{commands:s}=e,c=o.load("notebook"),p=document.createElement("div");i&&i.addFactory("TopBar","widgetTitle",(e=>{const t=new f.Widget({node:p});return t.id="jp-title",t}));const u=async()=>{const e=t.currentWidget;if(!(e&&e instanceof l.DocumentWidget))return;if(p.children.length>0)return;const o=document.createElement("h1");if(o.textContent=e.title.label.replace(y,""),p.appendChild(o),p.style.marginLeft="10px",!n)return;const i=()=>{const{currentWidget:e}=t;return!(!e||!n.contextForWidget(e))};s.addCommand(k.rename,{label:()=>c.__("Rename…"),isEnabled:i,execute:async()=>{var t;if(!i())return;const l=await(0,d.renameDialog)(n,e.context);if(e&&e.activate(),null===l)return;const s=e.context.path,c=r.PathExt.basename(s);if(o.textContent=c.replace(y,""),!a)return;const p=null!==(t=a.current.path.match(v))&&void 0!==t?t:[],[,u,g]=p;if(!u||!g)return;const m=encodeURIComponent(s);a.navigate(`/${u}/${m}`,{skipRouting:!0})}}),p.onclick=async()=>{s.execute(k.rename)}};t.currentChanged.connect(u),u()}},A={id:"@jupyter-notebook/application-extension:top",description:"Plugin to toggle the top header visibility.",requires:[g.INotebookShell,u.ITranslator],optional:[p.ISettingRegistry,a.ICommandPalette],activate:(e,t,o,n,a)=>{const i=o.load("notebook"),r=t.top,d=A.id;e.commands.addCommand(k.toggleTop,{label:i.__("Show Header"),execute:()=>{r.setHidden(r.isVisible),n&&n.set(d,"visible",r.isVisible?"yes":"no")},isToggled:()=>r.isVisible});let l=!1;if(n){const t=n.load(d),o=e=>{let t=e.get("visible").composite;void 0!==e.user.visible&&(t=e.user.visible),r.setHidden("no"===t),l="automatic"===t};Promise.all([t,e.restored]).then((([e])=>{o(e),e.changed.connect((e=>{o(e)}))})).catch((e=>{console.error(e.message)}))}a&&a.addItem({command:k.toggleTop,category:"View"}),e.formatChanged.connect((()=>{l&&("desktop"===e.format?t.expandTop():t.collapseTop())}))},autoStart:!0},M={id:"@jupyter-notebook/application-extension:sidepanel",description:"Plugin to toggle the visibility of left or right side panel.",requires:[g.INotebookShell,u.ITranslator],optional:[s.IMainMenu,a.ICommandPalette],autoStart:!0,activate:(e,t,o,n,a)=>{const i=o.load("notebook");e.commands.addCommand(k.togglePanel,{label:e=>e.title,caption:e=>"left"===e.side?i.__("Show %1 in the left sidebar",e.title):"right"===e.side?i.__("Show %1 in the right sidebar",e.title):i.__("Show %1 in the sidebar",e.title),execute:e=>{var o,n;switch(e.side){case"left":t.leftCollapsed||(null===(o=t.leftHandler.currentWidget)||void 0===o?void 0:o.id)!==e.id?t.expandLeft(e.id):(t.collapseLeft(),t.currentWidget&&t.activateById(t.currentWidget.id));break;case"right":t.rightCollapsed||(null===(n=t.rightHandler.currentWidget)||void 0===n?void 0:n.id)!==e.id?t.expandRight(e.id):(t.collapseRight(),t.currentWidget&&t.activateById(t.currentWidget.id))}},isToggled:e=>{switch(e.side){case"left":{if(t.leftCollapsed)return!1;const o=t.leftHandler.currentWidget;return!!o&&o.id===e.id}case"right":{if(t.rightCollapsed)return!1;const o=t.rightHandler.currentWidget;return!!o&&o.id===e.id}}return!1}});const r={left:null,right:null},d=(o,a)=>{var i;if(null===n)return null;null===(i=r[o])||void 0===i||i.dispose();const d=new f.Menu({commands:e.commands});d.title.label=a;const l=t.widgets(o);let s=!1;for(const e of l)d.addItem({command:k.togglePanel,args:{side:o,title:`Show ${e.title.caption}`,id:e.id}}),s=!0;s&&(r[o]=n.viewMenu.addItem({type:"submenu",submenu:d}))};e.restored.then((()=>{if(n){const e=e=>"left"===e?i.__("Left Sidebar"):i.__("Right Sidebar"),o=t.leftHandler.area,n=e(o);d(o,n);const a=t.rightHandler.area,r=e(a);d(a,r);const l=(t,o)=>{const n=e(t.area);d(t.area,n)};t.leftHandler.widgetAdded.connect(l),t.leftHandler.widgetRemoved.connect(l),t.rightHandler.widgetAdded.connect(l),t.rightHandler.widgetRemoved.connect(l)}if(a){const e=new g.SidePanelPalette({commandPalette:a,command:k.togglePanel});t.leftHandler.widgets.forEach((o=>{e.addItem(o,t.leftHandler.area)})),t.rightHandler.widgets.forEach((o=>{e.addItem(o,t.rightHandler.area)})),t.leftHandler.widgetAdded.connect(((t,o)=>{e.addItem(o,t.area)})),t.leftHandler.widgetRemoved.connect(((t,o)=>{e.removeItem(o,t.area)})),t.rightHandler.widgetAdded.connect(((t,o)=>{e.addItem(o,t.area)})),t.rightHandler.widgetRemoved.connect(((t,o)=>{e.removeItem(o,t.area)}))}}))}},U={id:"@jupyter-notebook/application-extension:tree-resolver",description:"The default tree route resolver plugin.",autoStart:!0,requires:[n.IRouter],provides:n.JupyterFrontEnd.ITreeResolver,activate:(e,t)=>{const{commands:o}=e,n=new b.DisposableSet,a=new h.PromiseDelegate,i=new RegExp("/(/tree/.*)?");n.add(o.addCommand(k.resolveTree,{execute:async e=>{var t;if(n.isDisposed)return;const o=r.URLExt.queryStringToObject(null!==(t=e.search)&&void 0!==t?t:""),i=o["file-browser-path"]||"";delete o["file-browser-path"],n.dispose(),a.resolve({browser:i,file:r.PageConfig.getOption("treePath")})}})),n.add(t.register({command:k.resolveTree,pattern:i}));const d=()=>{n.isDisposed||(n.dispose(),a.resolve(null))};return t.routed.connect(d),n.add(new b.DisposableDelegate((()=>{t.routed.disconnect(d)}))),{paths:a.promise}}},W={id:"@jupyter-notebook/application-extension:tree-updater",description:"Plugin to update tree path.",requires:[n.IRouter],provides:n.ITreePathUpdater,activate:(e,t)=>function(e){if(e!==r.PageConfig.getOption("treePath")){const o=r.URLExt.join(r.PageConfig.getOption("baseUrl")||"/","tree",r.URLExt.encodeParts(e));t.navigate(o,{skipRouting:!0}),r.PageConfig.setOption("treePath",e)}},autoStart:!0},D={id:"@jupyter-notebook/application-extension:translator",description:"Translator plugin",requires:[g.INotebookShell,u.ITranslator],autoStart:!0,activate:(e,t,o)=>{t.translator=o}},O={id:"@jupyter-notebook/application-extension:zen",description:"Zen mode plugin.",autoStart:!0,requires:[u.ITranslator],optional:[a.ICommandPalette,g.INotebookShell],activate:(e,t,o,n)=>{const{commands:a}=e,i=document.documentElement,r=t.load("notebook"),d=()=>{null==n||n.expandTop(),null==n||n.menu.setHidden(!1),l=!1};let l=!1;a.addCommand(k.toggleZen,{label:r.__("Toggle Zen Mode"),execute:()=>{l?(document.exitFullscreen(),d()):(i.requestFullscreen(),null==n||n.collapseTop(),null==n||n.menu.setHidden(!0),l=!0)}}),document.addEventListener("fullscreenchange",(()=>{document.fullscreenElement||d()})),o&&o.addItem({command:k.toggleZen,category:"Mode"})}},q=[w,x,P,C,I,S,T,R,E,_,M,L,j,N,H,A,U,W,D,O]}}]);
"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[7812,9680],{99680:(e,t,n)=>{n.r(t),n.d(t,{IMarkdownViewerTracker:()=>a,MarkdownDocument:()=>w,<PERSON><PERSON><PERSON>iewer:()=>f,MarkdownViewerFactory:()=>_,MarkdownViewerTableOfContentsFactory:()=>s,MarkdownViewerTableOfContentsModel:()=>i});var r=n(87749);class i extends r.TableOfContentsModel{constructor(e,t,n){super(e,n),this.parser=t}get documentType(){return"markdown-viewer"}get isAlwaysActive(){return!0}get supportedOptions(){return["maximalDepth","numberingH1","numberHeaders"]}getHeadings(){const e=this.widget.context.model.toString(),t=r.TableOfContentsUtils.filterHeadings(r.TableOfContentsUtils.Markdown.getHeadings(e),{...this.configuration,baseNumbering:1});return Promise.resolve(t)}}class s extends r.TableOfContentsFactory{constructor(e,t,n){super(e),this.parser=t,this.sanitizer=n}_createNew(e,t){const n=new i(e,this.parser,t);let s=new WeakMap;const o=(t,n)=>{if(n){const t=s.get(n);if(t){const n=e.content.node.getBoundingClientRect(),r=t.getBoundingClientRect();(r.top>n.bottom||r.bottom<n.top)&&t.scrollIntoView({block:"center"})}else console.warn("Heading element not found for heading",n,"in widget",e)}},a=()=>{this.parser&&(r.TableOfContentsUtils.clearNumbering(e.content.node),s=new WeakMap,n.headings.forEach((async t=>{var n;const i=await r.TableOfContentsUtils.Markdown.getHeadingId(this.parser,t.raw,t.level,this.sanitizer);if(!i)return;const o=`h${t.level}[id="${CSS.escape(i)}"]`;s.set(t,r.TableOfContentsUtils.addPrefix(e.content.node,o,null!==(n=t.prefix)&&void 0!==n?n:""))})))};return e.content.ready.then((()=>{a(),e.content.rendered.connect(a),n.activeHeadingChanged.connect(o),n.headingsChanged.connect(a),e.disposed.connect((()=>{e.content.rendered.disconnect(a),n.activeHeadingChanged.disconnect(o),n.headingsChanged.disconnect(a)}))})),n}}var o=n(20998);const a=new o.Token("@jupyterlab/markdownviewer:IMarkdownViewerTracker","A widget tracker for markdown\n  document viewers. Use this if you want to iterate over and interact with rendered markdown documents.");var d=n(12982),c=n(38639),l=n(35312),h=n(70856),u=n(71677),g=n(81997),m=n(31516);const p="text/markdown";class f extends m.Widget{constructor(e){super(),this._config={...f.defaultConfig},this._fragment="",this._ready=new o.PromiseDelegate,this._isRendering=!1,this._renderRequested=!1,this._rendered=new g.Signal(this),this.context=e.context,this.translator=e.translator||u.nullTranslator,this._trans=this.translator.load("jupyterlab"),this.renderer=e.renderer,this.node.tabIndex=0,this.addClass("jp-MarkdownViewer"),(this.layout=new m.StackedLayout).addWidget(this.renderer),this.context.ready.then((async()=>{await this._render(),this._monitor=new c.ActivityMonitor({signal:this.context.model.contentChanged,timeout:this._config.renderTimeout}),this._monitor.activityStopped.connect(this.update,this),this._ready.resolve(void 0)}))}get ready(){return this._ready.promise}get rendered(){return this._rendered}setFragment(e){this._fragment=e,this.update()}setOption(e,t){if(this._config[e]===t)return;this._config[e]=t;const{style:n}=this.renderer.node;switch(e){case"fontFamily":n.setProperty("font-family",t);break;case"fontSize":n.setProperty("font-size",t?t+"px":null);break;case"hideFrontMatter":this.update();break;case"lineHeight":n.setProperty("line-height",t?t.toString():null);break;case"lineWidth":{const e=t?`calc(50% - ${t/2}ch)`:null;n.setProperty("padding-left",e),n.setProperty("padding-right",e);break}case"renderTimeout":this._monitor&&(this._monitor.timeout=t)}}dispose(){this.isDisposed||(this._monitor&&this._monitor.dispose(),this._monitor=null,super.dispose())}onUpdateRequest(e){this.context.isReady&&!this.isDisposed&&(this._render(),this._fragment="")}onActivateRequest(e){this.node.focus()}async _render(){if(this.isDisposed)return;if(this._isRendering)return void(this._renderRequested=!0);this._renderRequested=!1;const{context:e}=this,{model:t}=e,n=t.toString(),r={};r[p]=this._config.hideFrontMatter?y.removeFrontMatter(n):n;const i=new h.MimeModel({data:r,metadata:{fragment:this._fragment}});try{if(this._isRendering=!0,await this.renderer.renderModel(i),this._isRendering=!1,this._renderRequested)return this._render();this._rendered.emit()}catch(t){requestAnimationFrame((()=>{this.dispose()})),(0,d.showErrorMessage)(this._trans.__("Renderer Failure: %1",e.path),t)}}}!function(e){e.defaultConfig={fontFamily:null,fontSize:null,lineHeight:null,lineWidth:null,hideFrontMatter:!0,renderTimeout:1e3}}(f||(f={}));class w extends l.DocumentWidget{setFragment(e){this.content.setFragment(e)}}class _ extends l.ABCWidgetFactory{constructor(e){super(y.createRegistryOptions(e)),this._fileType=e.primaryFileType,this._rendermime=e.rendermime}createNewWidget(e){var t,n,r,i,s;const o=this._rendermime.clone({resolver:e.urlResolver}).createRenderer(p),a=new f({context:e,renderer:o});return a.title.icon=null===(t=this._fileType)||void 0===t?void 0:t.icon,a.title.iconClass=null!==(r=null===(n=this._fileType)||void 0===n?void 0:n.iconClass)&&void 0!==r?r:"",a.title.iconLabel=null!==(s=null===(i=this._fileType)||void 0===i?void 0:i.iconLabel)&&void 0!==s?s:"",a.title.caption=this.label,new w({content:a,context:e})}}var y;!function(e){e.createRegistryOptions=function(e){return{...e,readOnly:!0}},e.removeFrontMatter=function(e){const t=e.match(/^---\n[^]*?\n(---|...)\n/);if(!t)return e;const{length:n}=t[0];return e.slice(n)}}(y||(y={}))}}]);
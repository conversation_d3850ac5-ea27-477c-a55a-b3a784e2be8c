(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[3676],{78037:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.BaseManager=void 0;const n=s(81997),i=s(43851);t.BaseManager=class{constructor(e){var t;this._isDisposed=!1,this._disposed=new n.Signal(this),this.serverSettings=null!==(t=e.serverSettings)&&void 0!==t?t:i.ServerConnection.makeSettings()}get disposed(){return this._disposed}get isDisposed(){return this._isDisposed}get isActive(){return!0}dispose(){this.isDisposed||(this._disposed.emit(void 0),n.Signal.clearData(this))}}},76418:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.BuildManager=void 0;const n=s(38639),i=s(43851);t.BuildManager=class{constructor(e={}){var t;this._url="",this.serverSettings=null!==(t=e.serverSettings)&&void 0!==t?t:i.ServerConnection.makeSettings();const{baseUrl:s,appUrl:r}=this.serverSettings;this._url=n.URLExt.join(s,r,"api/build")}get isAvailable(){return"true"===n.PageConfig.getOption("buildAvailable").toLowerCase()}get shouldCheck(){return"true"===n.PageConfig.getOption("buildCheck").toLowerCase()}getStatus(){const{_url:e,serverSettings:t}=this;return i.ServerConnection.makeRequest(e,{},t).then((e=>{if(200!==e.status)throw new i.ServerConnection.ResponseError(e);return e.json()})).then((e=>{if("string"!=typeof e.status)throw new Error("Invalid data");if("string"!=typeof e.message)throw new Error("Invalid data");return e}))}build(){const{_url:e,serverSettings:t}=this;return i.ServerConnection.makeRequest(e,{method:"POST"},t).then((e=>{if(400===e.status)throw new i.ServerConnection.ResponseError(e,"Build aborted");if(200!==e.status){const t=`Build failed with ${e.status}.\n\n        If you are experiencing the build failure after installing an extension (or trying to include previously installed extension after updating JupyterLab) please check the extension repository for new installation instructions as many extensions migrated to the prebuilt extensions system which no longer requires rebuilding JupyterLab (but uses a different installation procedure, typically involving a package manager such as 'pip' or 'conda').\n\n        If you specifically intended to install a source extension, please run 'jupyter lab build' on the server for full output.`;throw new i.ServerConnection.ResponseError(e,t)}}))}cancel(){const{_url:e,serverSettings:t}=this;return i.ServerConnection.makeRequest(e,{method:"DELETE"},t).then((e=>{if(204!==e.status)throw new i.ServerConnection.ResponseError(e)}))}}},34027:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ConfigWithDefaults=t.ConfigSection=void 0;const n=s(38639),i=s(83676);var r;!function(e){e.create=function(e){const t=new o(e);return t.load().then((()=>t))}}(r||(t.ConfigSection=r={}));class o{constructor(e){var t;this._url="unknown";const s=this.serverSettings=null!==(t=e.serverSettings)&&void 0!==t?t:i.ServerConnection.makeSettings();this._url=n.URLExt.join(s.baseUrl,"api/config",encodeURIComponent(e.name))}get data(){return this._data}async load(){const e=await i.ServerConnection.makeRequest(this._url,{},this.serverSettings);if(200!==e.status)throw await i.ServerConnection.ResponseError.create(e);this._data=await e.json()}async update(e){this._data={...this._data,...e};const t={method:"PATCH",body:JSON.stringify(e)},s=await i.ServerConnection.makeRequest(this._url,t,this.serverSettings);if(200!==s.status)throw await i.ServerConnection.ResponseError.create(s);return this._data=await s.json(),this._data}}t.ConfigWithDefaults=class{constructor(e){var t,s;this._className="",this._section=e.section,this._defaults=null!==(t=e.defaults)&&void 0!==t?t:{},this._className=null!==(s=e.className)&&void 0!==s?s:""}get(e){const t=this._classData();return e in t?t[e]:this._defaults[e]}set(e,t){const s={};if(s[e]=t,this._className){const e={};return e[this._className]=s,this._section.update(e)}return this._section.update(s)}_classData(){const e=this._section.data;return this._className&&this._className in e?e[this._className]:e}}},87594:function(e,t,s){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,s,n){void 0===n&&(n=s);var i=Object.getOwnPropertyDescriptor(t,s);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[s]}}),Object.defineProperty(e,n,i)}:function(e,t,s,n){void 0===n&&(n=s),e[n]=t[s]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var s in e)"default"!==s&&Object.prototype.hasOwnProperty.call(e,s)&&n(t,e,s);return i(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.Drive=t.ContentsManager=t.Contents=void 0;const o=s(38639),a=s(81997),l=s(83676),c=r(s(36556));var h,u;!function(e){e.validateContentsModel=function(e){c.validateContentsModel(e)},e.validateCheckpointModel=function(e){c.validateCheckpointModel(e)}}(h||(t.Contents=h={})),t.ContentsManager=class{constructor(e={}){var t,s;this._isDisposed=!1,this._additionalDrives=new Map,this._fileChanged=new a.Signal(this);const n=this.serverSettings=null!==(t=e.serverSettings)&&void 0!==t?t:l.ServerConnection.makeSettings();this._defaultDrive=null!==(s=e.defaultDrive)&&void 0!==s?s:new d({serverSettings:n}),this._defaultDrive.fileChanged.connect(this._onFileChanged,this)}get fileChanged(){return this._fileChanged}get isDisposed(){return this._isDisposed}dispose(){this.isDisposed||(this._isDisposed=!0,a.Signal.clearData(this))}addDrive(e){this._additionalDrives.set(e.name,e),e.fileChanged.connect(this._onFileChanged,this)}getSharedModelFactory(e){var t;const[s]=this._driveForPath(e);return null!==(t=null==s?void 0:s.sharedModelFactory)&&void 0!==t?t:null}localPath(e){const t=e.split("/"),s=t[0].split(":");return 1!==s.length&&this._additionalDrives.has(s[0])?o.PathExt.join(s.slice(1).join(":"),...t.slice(1)):o.PathExt.removeSlash(e)}normalize(e){const t=e.split(":");return 1===t.length?o.PathExt.normalize(e):`${t[0]}:${o.PathExt.normalize(t.slice(1).join(":"))}`}resolvePath(e,t){const s=this.driveName(e),n=this.localPath(e),i=o.PathExt.resolve("/",n,t);return s?`${s}:${i}`:i}driveName(e){const t=e.split("/")[0].split(":");return 1===t.length?"":this._additionalDrives.has(t[0])?t[0]:""}get(e,t){const[s,n]=this._driveForPath(e);return s.get(n,t).then((e=>{const t=[];if("directory"===e.type&&e.content){for(const n of e.content)t.push({...n,path:this._toGlobalPath(s,n.path)});return{...e,path:this._toGlobalPath(s,n),content:t,serverPath:e.path}}return{...e,path:this._toGlobalPath(s,n),serverPath:e.path}}))}getDownloadUrl(e){const[t,s]=this._driveForPath(e);return t.getDownloadUrl(s)}newUntitled(e={}){if(e.path){const t=this.normalize(e.path),[s,n]=this._driveForPath(t);return s.newUntitled({...e,path:n}).then((e=>({...e,path:o.PathExt.join(t,e.name),serverPath:e.path})))}return this._defaultDrive.newUntitled(e)}delete(e){const[t,s]=this._driveForPath(e);return t.delete(s)}rename(e,t){const[s,n]=this._driveForPath(e),[i,r]=this._driveForPath(t);if(s!==i)throw Error("ContentsManager: renaming files must occur within a Drive");return s.rename(n,r).then((e=>({...e,path:this._toGlobalPath(s,r),serverPath:e.path})))}save(e,t={}){const s=this.normalize(e),[n,i]=this._driveForPath(e);return n.save(i,{...t,path:i}).then((e=>({...e,path:s,serverPath:e.path})))}copy(e,t){const[s,n]=this._driveForPath(e),[i,r]=this._driveForPath(t);if(s===i)return s.copy(n,r).then((e=>({...e,path:this._toGlobalPath(s,e.path),serverPath:e.path})));throw Error("Copying files between drives is not currently implemented")}createCheckpoint(e){const[t,s]=this._driveForPath(e);return t.createCheckpoint(s)}listCheckpoints(e){const[t,s]=this._driveForPath(e);return t.listCheckpoints(s)}restoreCheckpoint(e,t){const[s,n]=this._driveForPath(e);return s.restoreCheckpoint(n,t)}deleteCheckpoint(e,t){const[s,n]=this._driveForPath(e);return s.deleteCheckpoint(n,t)}_toGlobalPath(e,t){return e===this._defaultDrive?o.PathExt.removeSlash(t):`${e.name}:${o.PathExt.removeSlash(t)}`}_driveForPath(e){const t=this.driveName(e),s=this.localPath(e);return t?[this._additionalDrives.get(t),s]:[this._defaultDrive,s]}_onFileChanged(e,t){var s,n;if(e===this._defaultDrive)this._fileChanged.emit(t);else{let i=null,r=null;(null===(s=t.newValue)||void 0===s?void 0:s.path)&&(i={...t.newValue,path:this._toGlobalPath(e,t.newValue.path)}),(null===(n=t.oldValue)||void 0===n?void 0:n.path)&&(r={...t.oldValue,path:this._toGlobalPath(e,t.oldValue.path)}),this._fileChanged.emit({type:t.type,newValue:i,oldValue:r})}}};class d{constructor(e={}){var t,s,n;this._isDisposed=!1,this._fileChanged=new a.Signal(this),this.name=null!==(t=e.name)&&void 0!==t?t:"Default",this._apiEndpoint=null!==(s=e.apiEndpoint)&&void 0!==s?s:"api/contents",this.serverSettings=null!==(n=e.serverSettings)&&void 0!==n?n:l.ServerConnection.makeSettings()}get fileChanged(){return this._fileChanged}get isDisposed(){return this._isDisposed}dispose(){this.isDisposed||(this._isDisposed=!0,a.Signal.clearData(this))}async get(e,t){let s=this._getUrl(e);if(t){"notebook"===t.type&&delete t.format;const e=t.content?"1":"0",n=t.hash?"1":"0",i={...t,content:e,hash:n};s+=o.URLExt.objectToQueryString(i)}const n=this.serverSettings,i=await l.ServerConnection.makeRequest(s,{},n);if(200!==i.status)throw await l.ServerConnection.ResponseError.create(i);const r=await i.json();return c.validateContentsModel(r),r}getDownloadUrl(e){const t=this.serverSettings.baseUrl;let s=o.URLExt.join(t,"files",o.URLExt.encodeParts(e)),n="";try{n=document.cookie}catch(e){}const i=n.match("\\b_xsrf=([^;]*)\\b");if(i){const e=new URL(s);e.searchParams.append("_xsrf",i[1]),s=e.toString()}return Promise.resolve(s)}async newUntitled(e={}){var t;let s="{}";e&&(e.ext&&(e.ext=u.normalizeExtension(e.ext)),s=JSON.stringify(e));const n=this.serverSettings,i=this._getUrl(null!==(t=e.path)&&void 0!==t?t:""),r={method:"POST",body:s},o=await l.ServerConnection.makeRequest(i,r,n);if(201!==o.status)throw await l.ServerConnection.ResponseError.create(o);const a=await o.json();return c.validateContentsModel(a),this._fileChanged.emit({type:"new",oldValue:null,newValue:a}),a}async delete(e){const t=this._getUrl(e),s=this.serverSettings,n=await l.ServerConnection.makeRequest(t,{method:"DELETE"},s);if(204!==n.status)throw await l.ServerConnection.ResponseError.create(n);this._fileChanged.emit({type:"delete",oldValue:{path:e},newValue:null})}async rename(e,t){const s=this.serverSettings,n=this._getUrl(e),i={method:"PATCH",body:JSON.stringify({path:t})},r=await l.ServerConnection.makeRequest(n,i,s);if(200!==r.status)throw await l.ServerConnection.ResponseError.create(r);const o=await r.json();return c.validateContentsModel(o),this._fileChanged.emit({type:"rename",oldValue:{path:e},newValue:o}),o}async save(e,t={}){const s=this.serverSettings,n=this._getUrl(e),i={method:"PUT",body:JSON.stringify(t)},r=await l.ServerConnection.makeRequest(n,i,s);if(200!==r.status&&201!==r.status)throw await l.ServerConnection.ResponseError.create(r);const o=await r.json();return c.validateContentsModel(o),this._fileChanged.emit({type:"save",oldValue:null,newValue:o}),o}async copy(e,t){const s=this.serverSettings,n=this._getUrl(t),i={method:"POST",body:JSON.stringify({copy_from:e})},r=await l.ServerConnection.makeRequest(n,i,s);if(201!==r.status)throw await l.ServerConnection.ResponseError.create(r);const o=await r.json();return c.validateContentsModel(o),this._fileChanged.emit({type:"new",oldValue:null,newValue:o}),o}async createCheckpoint(e){const t=this._getUrl(e,"checkpoints"),s=await l.ServerConnection.makeRequest(t,{method:"POST"},this.serverSettings);if(201!==s.status)throw await l.ServerConnection.ResponseError.create(s);const n=await s.json();return c.validateCheckpointModel(n),n}async listCheckpoints(e){const t=this._getUrl(e,"checkpoints"),s=await l.ServerConnection.makeRequest(t,{},this.serverSettings);if(200!==s.status)throw await l.ServerConnection.ResponseError.create(s);const n=await s.json();if(!Array.isArray(n))throw new Error("Invalid Checkpoint list");for(let e=0;e<n.length;e++)c.validateCheckpointModel(n[e]);return n}async restoreCheckpoint(e,t){const s=this._getUrl(e,"checkpoints",t),n=await l.ServerConnection.makeRequest(s,{method:"POST"},this.serverSettings);if(204!==n.status)throw await l.ServerConnection.ResponseError.create(n)}async deleteCheckpoint(e,t){const s=this._getUrl(e,"checkpoints",t),n=await l.ServerConnection.makeRequest(s,{method:"DELETE"},this.serverSettings);if(204!==n.status)throw await l.ServerConnection.ResponseError.create(n)}_getUrl(...e){const t=e.map((e=>o.URLExt.encodeParts(e))),s=this.serverSettings.baseUrl;return o.URLExt.join(s,this._apiEndpoint,...t)}}t.Drive=d,function(e){e.normalizeExtension=function(e){return e.length>0&&0!==e.indexOf(".")&&(e=`.${e}`),e}}(u||(u={}))},36556:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateCheckpointModel=t.validateContentsModel=void 0;const n=s(45669);t.validateContentsModel=function(e){(0,n.validateProperty)(e,"name","string"),(0,n.validateProperty)(e,"path","string"),(0,n.validateProperty)(e,"type","string"),(0,n.validateProperty)(e,"created","string"),(0,n.validateProperty)(e,"last_modified","string"),(0,n.validateProperty)(e,"mimetype","object"),(0,n.validateProperty)(e,"content","object"),(0,n.validateProperty)(e,"format","object")},t.validateCheckpointModel=function(e){(0,n.validateProperty)(e,"id","string"),(0,n.validateProperty)(e,"last_modified","string")}},95348:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EventManager=void 0;const n=s(38639),i=s(97934),r=s(81997),o=s(43851),a="api/events";t.EventManager=class{constructor(e={}){var t;this._socket=null,this.serverSettings=null!==(t=e.serverSettings)&&void 0!==t?t:o.ServerConnection.makeSettings(),this._poll=new i.Poll({factory:()=>this._subscribe()}),this._stream=new r.Stream(this),this._poll.start()}get isDisposed(){return this._poll.isDisposed}get stream(){return this._stream}dispose(){if(this.isDisposed)return;this._poll.dispose();const e=this._socket;e&&(this._socket=null,e.onopen=()=>{},e.onerror=()=>{},e.onmessage=()=>{},e.onclose=()=>{},e.close()),r.Signal.clearData(this),this._stream.stop()}async emit(e){const{serverSettings:t}=this,{baseUrl:s,token:i}=t,{makeRequest:r,ResponseError:l}=o.ServerConnection,c=n.URLExt.join(s,a)+(i?`?token=${i}`:""),h={body:JSON.stringify(e),method:"POST"},u=await r(c,h,t);if(204!==u.status)throw new l(u)}_subscribe(){return new Promise(((e,t)=>{if(this.isDisposed)return;const{token:s,WebSocket:i,wsUrl:r}=this.serverSettings,o=n.URLExt.join(r,a,"subscribe")+(s?`?token=${encodeURIComponent(s)}`:""),l=this._socket=new i(o),c=this._stream;l.onclose=()=>t(new Error("EventManager socket closed")),l.onmessage=e=>e.data&&c.emit(JSON.parse(e.data))}))}}},83676:function(e,t,s){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,s,n){void 0===n&&(n=s);var i=Object.getOwnPropertyDescriptor(t,s);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[s]}}),Object.defineProperty(e,n,i)}:function(e,t,s,n){void 0===n&&(n=s),e[n]=t[s]}),i=this&&this.__exportStar||function(e,t){for(var s in e)"default"===s||Object.prototype.hasOwnProperty.call(t,s)||n(t,e,s)};Object.defineProperty(t,"__esModule",{value:!0}),i(s(78037),t),i(s(34027),t),i(s(87594),t),i(s(95348),t),i(s(30335),t),i(s(27504),t),i(s(84711),t),i(s(43851),t),i(s(82351),t),i(s(69494),t),i(s(23549),t),i(s(78162),t),i(s(53265),t),i(s(8178),t)},35139:function(e,t,s){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,s,n){void 0===n&&(n=s);var i=Object.getOwnPropertyDescriptor(t,s);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[s]}}),Object.defineProperty(e,n,i)}:function(e,t,s,n){void 0===n&&(n=s),e[n]=t[s]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var s in e)"default"!==s&&Object.prototype.hasOwnProperty.call(e,s)&&n(t,e,s);return i(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.CommHandler=void 0;const o=s(2549),a=r(s(70552));class l extends o.DisposableDelegate{constructor(e,t,s,n){super(n),this._target="",this._id="",this._id=t,this._target=e,this._kernel=s}get commId(){return this._id}get targetName(){return this._target}get onClose(){return this._onClose}set onClose(e){this._onClose=e}get onMsg(){return this._onMsg}set onMsg(e){this._onMsg=e}open(e,t,s=[]){if(this.isDisposed||this._kernel.isDisposed)throw new Error("Cannot open");const n=a.createMessage({msgType:"comm_open",channel:"shell",username:this._kernel.username,session:this._kernel.clientId,content:{comm_id:this._id,target_name:this._target,data:null!=e?e:{}},metadata:t,buffers:s});return this._kernel.sendShellMessage(n,!1,!0)}send(e,t,s=[],n=!0){if(this.isDisposed||this._kernel.isDisposed)throw new Error("Cannot send");const i=a.createMessage({msgType:"comm_msg",channel:"shell",username:this._kernel.username,session:this._kernel.clientId,content:{comm_id:this._id,data:e},metadata:t,buffers:s});return this._kernel.sendShellMessage(i,!1,n)}close(e,t,s=[]){if(this.isDisposed||this._kernel.isDisposed)throw new Error("Cannot close");const n=a.createMessage({msgType:"comm_close",channel:"shell",username:this._kernel.username,session:this._kernel.clientId,content:{comm_id:this._id,data:null!=e?e:{}},metadata:t,buffers:s}),i=this._kernel.sendShellMessage(n,!1,!0),r=this._onClose;return r&&r(a.createMessage({msgType:"comm_close",channel:"iopub",username:this._kernel.username,session:this._kernel.clientId,content:{comm_id:this._id,data:null!=e?e:{}},metadata:t,buffers:s})),this.dispose(),i}}t.CommHandler=l},45992:function(e,t,s){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,s,n){void 0===n&&(n=s);var i=Object.getOwnPropertyDescriptor(t,s);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[s]}}),Object.defineProperty(e,n,i)}:function(e,t,s,n){void 0===n&&(n=s),e[n]=t[s]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var s in e)"default"!==s&&Object.prototype.hasOwnProperty.call(e,s)&&n(t,e,s);return i(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.KernelConnection=void 0;const o=s(38639),a=s(20998),l=s(81997),c=s(83676),h=s(35139),u=r(s(70552)),d=s(60682),p=r(s(92917)),_=s(27504),g=r(s(33505)),m="_RESTARTING_";class f{constructor(e){var t,s,n,i;this._createSocket=(e=!0)=>{this._errorIfDisposed(),this._clearSocket(),this._updateConnectionStatus("connecting");const t=this.serverSettings,s=o.URLExt.join(t.wsUrl,g.KERNEL_SERVICE_URL,encodeURIComponent(this._id)),n=s.replace(/^((?:\w+:)?\/\/)(?:[^@\/]+@)/,"$1");console.debug(`Starting WebSocket: ${n}`);let i=o.URLExt.join(s,"channels?session_id="+encodeURIComponent(this._clientId));const r=t.token;t.appendToken&&""!==r&&(i+=`&token=${encodeURIComponent(r)}`);const a=e?this._supportedProtocols:[];this._ws=new t.WebSocket(i,a),this._ws.binaryType="arraybuffer";let l=!1;const h=async e=>{var s,n;if(!this._isDisposed){this._reason="",this._model=void 0;try{const s=await g.getKernelModel(this._id,t);this._model=s,"dead"===(null==s?void 0:s.execution_state)?this._updateStatus("dead"):this._onWSClose(e)}catch(t){if(t instanceof c.ServerConnection.NetworkError||503===(null===(s=t.response)||void 0===s?void 0:s.status)||424===(null===(n=t.response)||void 0===n?void 0:n.status)){const t=1e3*v.getRandomIntInclusive(10,30);setTimeout(h,t,e)}else this._reason="Kernel died unexpectedly",this._updateStatus("dead")}}},u=async e=>{l||(l=!0,await h(e))};this._ws.onmessage=this._onWSMessage,this._ws.onopen=this._onWSOpen,this._ws.onclose=u,this._ws.onerror=u},this._onWSOpen=e=>{if(""!==this._ws.protocol&&!this._supportedProtocols.includes(this._ws.protocol))throw console.log("Server selected unknown kernel wire protocol:",this._ws.protocol),this._updateStatus("dead"),new Error(`Unknown kernel wire protocol:  ${this._ws.protocol}`);this._selectedProtocol=this._ws.protocol,this._ws.onclose=this._onWSClose,this._ws.onerror=this._onWSClose,this._updateConnectionStatus("connected")},this._onWSMessage=e=>{let t;try{t=this.serverSettings.serializer.deserialize(e.data,this._ws.protocol),p.validateMessage(t)}catch(e){throw e.message=`Kernel message validation error: ${e.message}`,e}this._kernelSession=t.header.session,this._msgChain=this._msgChain.then((()=>this._handleMessage(t))).catch((e=>{e.message.startsWith("Canceled future for ")&&console.error(e)})),this._anyMessage.emit({msg:t,direction:"recv"})},this._onWSClose=e=>{this.isDisposed||this._reconnect()},this._id="",this._name="",this._status="unknown",this._connectionStatus="connecting",this._kernelSession="",this._isDisposed=!1,this._ws=null,this._username="",this._reconnectLimit=7,this._reconnectAttempt=0,this._reconnectTimeout=null,this._supportedProtocols=Object.values(u.supportedKernelWebSocketProtocols),this._selectedProtocol="",this._futures=new Map,this._comms=new Map,this._targetRegistry=Object.create(null),this._info=new a.PromiseDelegate,this._pendingMessages=[],this._statusChanged=new l.Signal(this),this._connectionStatusChanged=new l.Signal(this),this._disposed=new l.Signal(this),this._iopubMessage=new l.Signal(this),this._anyMessage=new l.Signal(this),this._pendingInput=new l.Signal(this),this._unhandledMessage=new l.Signal(this),this._displayIdToParentIds=new Map,this._msgIdToDisplayIds=new Map,this._msgChain=Promise.resolve(),this._hasPendingInput=!1,this._reason="",this._noOp=()=>{},this._name=e.model.name,this._id=e.model.id,this.serverSettings=null!==(t=e.serverSettings)&&void 0!==t?t:c.ServerConnection.makeSettings(),this._clientId=null!==(s=e.clientId)&&void 0!==s?s:a.UUID.uuid4(),this._username=null!==(n=e.username)&&void 0!==n?n:"",this.handleComms=null===(i=e.handleComms)||void 0===i||i,this._createSocket()}get disposed(){return this._disposed}get statusChanged(){return this._statusChanged}get connectionStatusChanged(){return this._connectionStatusChanged}get iopubMessage(){return this._iopubMessage}get unhandledMessage(){return this._unhandledMessage}get model(){return this._model||{id:this.id,name:this.name,reason:this._reason}}get anyMessage(){return this._anyMessage}get pendingInput(){return this._pendingInput}get id(){return this._id}get name(){return this._name}get username(){return this._username}get clientId(){return this._clientId}get status(){return this._status}get connectionStatus(){return this._connectionStatus}get isDisposed(){return this._isDisposed}get info(){return this._info.promise}get spec(){return this._specPromise||(this._specPromise=_.KernelSpecAPI.getSpecs(this.serverSettings).then((e=>e.kernelspecs[this._name]))),this._specPromise}clone(e={}){return new f({model:this.model,username:this.username,serverSettings:this.serverSettings,handleComms:!1,...e})}dispose(){this.isDisposed||(this._isDisposed=!0,this._disposed.emit(),this._updateConnectionStatus("disconnected"),this._clearKernelState(),this._pendingMessages=[],this._clearSocket(),l.Signal.clearData(this))}sendShellMessage(e,t=!1,s=!0){return this._sendKernelShellControl(d.KernelShellFutureHandler,e,t,s)}sendControlMessage(e,t=!1,s=!0){return this._sendKernelShellControl(d.KernelControlFutureHandler,e,t,s)}_sendKernelShellControl(e,t,s=!1,n=!0){this._sendMessage(t),this._anyMessage.emit({msg:t,direction:"send"});const i=new e((()=>{const e=t.header.msg_id;this._futures.delete(e);const s=this._msgIdToDisplayIds.get(e);s&&(s.forEach((t=>{const s=this._displayIdToParentIds.get(t);if(s){const n=s.indexOf(e);if(-1===n)return;1===s.length?this._displayIdToParentIds.delete(t):(s.splice(n,1),this._displayIdToParentIds.set(t,s))}})),this._msgIdToDisplayIds.delete(e))}),t,s,n,this);return this._futures.set(t.header.msg_id,i),i}_sendMessage(e,t=!0){if("dead"===this.status)throw new Error("Kernel is dead");if((""===this._kernelSession||this._kernelSession===m)&&u.isInfoRequestMsg(e)){if("connected"===this.connectionStatus)return void this._ws.send(this.serverSettings.serializer.serialize(e,this._ws.protocol));throw new Error("Could not send message: status is not connected")}if(t&&this._pendingMessages.length>0)this._pendingMessages.push(e);else if("connected"===this.connectionStatus&&this._kernelSession!==m)this._ws.send(this.serverSettings.serializer.serialize(e,this._ws.protocol));else{if(!t)throw new Error("Could not send message");this._pendingMessages.push(e)}}async interrupt(){if(this.hasPendingInput=!1,"dead"===this.status)throw new Error("Kernel is dead");return g.interruptKernel(this.id,this.serverSettings)}async restart(){if("dead"===this.status)throw new Error("Kernel is dead");this._updateStatus("restarting"),this._clearKernelState(),this._kernelSession=m,await g.restartKernel(this.id,this.serverSettings),await this.reconnect(),this.hasPendingInput=!1}reconnect(){this._errorIfDisposed();const e=new a.PromiseDelegate,t=(s,n)=>{"connected"===n?(e.resolve(),this.connectionStatusChanged.disconnect(t,this)):"disconnected"===n&&(e.reject(new Error("Kernel connection disconnected")),this.connectionStatusChanged.disconnect(t,this))};return this.connectionStatusChanged.connect(t,this),this._reconnectAttempt=0,this._reconnect(),e.promise}async shutdown(){"dead"!==this.status&&await g.shutdownKernel(this.id,this.serverSettings),this.handleShutdown()}handleShutdown(){this._updateStatus("dead"),this.dispose()}async requestKernelInfo(){const e=u.createMessage({msgType:"kernel_info_request",channel:"shell",username:this._username,session:this._clientId,content:{}});let t;try{t=await v.handleShellMessage(this,e)}catch(e){if(this.isDisposed)return;throw e}if(this._errorIfDisposed(),t)return void 0===t.content.status&&(t.content.status="ok"),"ok"!==t.content.status?(this._info.reject("Kernel info reply errored"),t):(this._info.resolve(t.content),this._kernelSession=t.header.session,t)}requestComplete(e){const t=u.createMessage({msgType:"complete_request",channel:"shell",username:this._username,session:this._clientId,content:e});return v.handleShellMessage(this,t)}requestInspect(e){const t=u.createMessage({msgType:"inspect_request",channel:"shell",username:this._username,session:this._clientId,content:e});return v.handleShellMessage(this,t)}requestHistory(e){const t=u.createMessage({msgType:"history_request",channel:"shell",username:this._username,session:this._clientId,content:e});return v.handleShellMessage(this,t)}requestExecute(e,t=!0,s){const n=u.createMessage({msgType:"execute_request",channel:"shell",username:this._username,session:this._clientId,content:{silent:!1,store_history:!0,user_expressions:{},allow_stdin:!0,stop_on_error:!1,...e},metadata:s});return this.sendShellMessage(n,!0,t)}requestDebug(e,t=!0){const s=u.createMessage({msgType:"debug_request",channel:"control",username:this._username,session:this._clientId,content:e});return this.sendControlMessage(s,!0,t)}requestIsComplete(e){const t=u.createMessage({msgType:"is_complete_request",channel:"shell",username:this._username,session:this._clientId,content:e});return v.handleShellMessage(this,t)}requestCommInfo(e){const t=u.createMessage({msgType:"comm_info_request",channel:"shell",username:this._username,session:this._clientId,content:e});return v.handleShellMessage(this,t)}sendInputReply(e,t){const s=u.createMessage({msgType:"input_reply",channel:"stdin",username:this._username,session:this._clientId,content:e});s.parent_header=t,this._sendMessage(s),this._anyMessage.emit({msg:s,direction:"send"}),this.hasPendingInput=!1}createComm(e,t=a.UUID.uuid4()){if(!this.handleComms)throw new Error("Comms are disabled on this kernel connection");if(this._comms.has(t))throw new Error("Comm is already created");const s=new h.CommHandler(e,t,this,(()=>{this._unregisterComm(t)}));return this._comms.set(t,s),s}hasComm(e){return this._comms.has(e)}registerCommTarget(e,t){this.handleComms&&(this._targetRegistry[e]=t)}removeCommTarget(e,t){this.handleComms&&(this.isDisposed||this._targetRegistry[e]!==t||delete this._targetRegistry[e])}registerMessageHook(e,t){var s;const n=null===(s=this._futures)||void 0===s?void 0:s.get(e);n&&n.registerMessageHook(t)}removeMessageHook(e,t){var s;const n=null===(s=this._futures)||void 0===s?void 0:s.get(e);n&&n.removeMessageHook(t)}removeInputGuard(){this.hasPendingInput=!1}async _handleDisplayId(e,t){var s,n;const i=t.parent_header.msg_id;let r=this._displayIdToParentIds.get(e);if(r){const e={header:a.JSONExt.deepCopy(t.header),parent_header:a.JSONExt.deepCopy(t.parent_header),metadata:a.JSONExt.deepCopy(t.metadata),content:a.JSONExt.deepCopy(t.content),channel:t.channel,buffers:t.buffers?t.buffers.slice():[]};e.header.msg_type="update_display_data",await Promise.all(r.map((async t=>{const s=this._futures&&this._futures.get(t);s&&await s.handleMsg(e)})))}if("update_display_data"===t.header.msg_type)return!0;r=null!==(s=this._displayIdToParentIds.get(e))&&void 0!==s?s:[],-1===r.indexOf(i)&&r.push(i),this._displayIdToParentIds.set(e,r);const o=null!==(n=this._msgIdToDisplayIds.get(i))&&void 0!==n?n:[];return-1===o.indexOf(i)&&o.push(i),this._msgIdToDisplayIds.set(i,o),!1}_clearSocket(){null!==this._ws&&(this._ws.onopen=this._noOp,this._ws.onclose=this._noOp,this._ws.onerror=this._noOp,this._ws.onmessage=this._noOp,this._ws.close(),this._ws=null)}_updateStatus(e){this._status!==e&&"dead"!==this._status&&(this._status=e,v.logKernelStatus(this),this._statusChanged.emit(e),"dead"===e&&this.dispose())}_sendPending(){for(;"connected"===this.connectionStatus&&this._kernelSession!==m&&this._pendingMessages.length>0;)this._sendMessage(this._pendingMessages[0],!1),this._pendingMessages.shift()}_clearKernelState(){this._kernelSession="",this._pendingMessages=[],this._futures.forEach((e=>{e.dispose()})),this._comms.forEach((e=>{e.dispose()})),this._msgChain=Promise.resolve(),this._futures=new Map,this._comms=new Map,this._displayIdToParentIds.clear(),this._msgIdToDisplayIds.clear()}_assertCurrentMessage(e){if(this._errorIfDisposed(),e.header.session!==this._kernelSession)throw new Error(`Canceling handling of old message: ${e.header.msg_type}`)}async _handleCommOpen(e){this._assertCurrentMessage(e);const t=e.content,s=new h.CommHandler(t.target_name,t.comm_id,this,(()=>{this._unregisterComm(t.comm_id)}));this._comms.set(t.comm_id,s);try{const n=await v.loadObject(t.target_name,t.target_module,this._targetRegistry);await n(s,e)}catch(e){throw s.close(),console.error("Exception opening new comm"),e}}async _handleCommClose(e){this._assertCurrentMessage(e);const t=e.content,s=this._comms.get(t.comm_id);if(!s)return void console.error("Comm not found for comm id "+t.comm_id);this._unregisterComm(s.commId);const n=s.onClose;n&&await n(e),s.dispose()}async _handleCommMsg(e){this._assertCurrentMessage(e);const t=e.content,s=this._comms.get(t.comm_id);if(!s)return;const n=s.onMsg;n&&await n(e)}_unregisterComm(e){this._comms.delete(e)}_updateConnectionStatus(e){if(this._connectionStatus!==e){if(this._connectionStatus=e,"connecting"!==e&&(this._reconnectAttempt=0,clearTimeout(this._reconnectTimeout)),"dead"!==this.status)if("connected"===e){let e=this._kernelSession===m,t=this.requestKernelInfo(),s=!1,n=()=>{s||(s=!0,e&&this._kernelSession===m&&(this._kernelSession=""),clearTimeout(i),this._pendingMessages.length>0&&this._sendPending())};t.then(n);let i=setTimeout(n,3e3)}else this._updateStatus("unknown");this._connectionStatusChanged.emit(e)}}async _handleMessage(e){var t,s;let n=!1;if(e.parent_header&&"iopub"===e.channel&&(u.isDisplayDataMsg(e)||u.isUpdateDisplayDataMsg(e)||u.isExecuteResultMsg(e))){const s=(null!==(t=e.content.transient)&&void 0!==t?t:{}).display_id;s&&(n=await this._handleDisplayId(s,e),this._assertCurrentMessage(e))}if(!n&&e.parent_header){const t=e.parent_header,n=null===(s=this._futures)||void 0===s?void 0:s.get(t.msg_id);if(n)await n.handleMsg(e),this._assertCurrentMessage(e);else{const s=t.session===this.clientId;"iopub"!==e.channel&&s&&this._unhandledMessage.emit(e)}}if("iopub"===e.channel){switch(e.header.msg_type){case"status":{const t=e.content.execution_state;"restarting"===t&&Promise.resolve().then((async()=>{this._updateStatus("autorestarting"),this._clearKernelState(),await this.reconnect()})),this._updateStatus(t);break}case"comm_open":this.handleComms&&await this._handleCommOpen(e);break;case"comm_msg":this.handleComms&&await this._handleCommMsg(e);break;case"comm_close":this.handleComms&&await this._handleCommClose(e)}this.isDisposed||(this._assertCurrentMessage(e),this._iopubMessage.emit(e))}}_reconnect(){if(this._errorIfDisposed(),clearTimeout(this._reconnectTimeout),this._reconnectAttempt<this._reconnectLimit){this._updateConnectionStatus("connecting");const e=v.getRandomIntInclusive(0,1e3*(Math.pow(2,this._reconnectAttempt)-1));console.warn(`Connection lost, reconnecting in ${Math.floor(e/1e3)} seconds.`);const t=""!==this._selectedProtocol;this._reconnectTimeout=setTimeout(this._createSocket,e,t),this._reconnectAttempt+=1}else this._updateConnectionStatus("disconnected");this._clearSocket()}_errorIfDisposed(){if(this.isDisposed)throw new Error("Kernel connection is disposed")}get hasPendingInput(){return this._hasPendingInput}set hasPendingInput(e){this._hasPendingInput=e,this._pendingInput.emit(e)}}var v;t.KernelConnection=f,function(e){e.logKernelStatus=function(e){switch(e.status){case"idle":case"busy":case"unknown":return;default:console.debug(`Kernel: ${e.status} (${e.id})`)}},e.handleShellMessage=async function(e,t){return e.sendShellMessage(t,!0).done},e.loadObject=function(e,t,s){return new Promise(((n,i)=>{if(t){if("undefined"==typeof requirejs)throw new Error("requirejs not found");requirejs([t],(s=>{void 0===s[e]?i(new Error(`Object '${e}' not found in module '${t}'`)):n(s[e])}),i)}else(null==s?void 0:s[e])?n(s[e]):i(new Error(`Object '${e}' not found in registry`))}))},e.getRandomIntInclusive=function(e,t){return e=Math.ceil(e),t=Math.floor(t),Math.floor(Math.random()*(t-e+1))+e}}(v||(v={}))},60682:function(e,t,s){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,s,n){void 0===n&&(n=s);var i=Object.getOwnPropertyDescriptor(t,s);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[s]}}),Object.defineProperty(e,n,i)}:function(e,t,s,n){void 0===n&&(n=s),e[n]=t[s]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var s in e)"default"!==s&&Object.prototype.hasOwnProperty.call(e,s)&&n(t,e,s);return i(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.KernelShellFutureHandler=t.KernelControlFutureHandler=t.KernelFutureHandler=void 0;const o=s(20998),a=s(2549),l=r(s(70552));class c extends a.DisposableDelegate{constructor(e,t,s,n,i){super(e),this._status=0,this._stdin=h.noOp,this._iopub=h.noOp,this._reply=h.noOp,this._done=new o.PromiseDelegate,this._hooks=new h.HookList,this._disposeOnDone=!0,this._msg=t,s||this._setFlag(h.KernelFutureFlag.GotReply),this._disposeOnDone=n,this._kernel=i}get msg(){return this._msg}get done(){return this._done.promise}get onReply(){return this._reply}set onReply(e){this._reply=e}get onIOPub(){return this._iopub}set onIOPub(e){this._iopub=e}get onStdin(){return this._stdin}set onStdin(e){this._stdin=e}registerMessageHook(e){if(this.isDisposed)throw new Error("Kernel future is disposed");this._hooks.add(e)}removeMessageHook(e){this.isDisposed||this._hooks.remove(e)}sendInputReply(e,t){this._kernel.sendInputReply(e,t)}dispose(){this._stdin=h.noOp,this._iopub=h.noOp,this._reply=h.noOp,this._hooks=null,this._testFlag(h.KernelFutureFlag.IsDone)||(this._done.promise.catch((()=>{})),this._done.reject(new Error(`Canceled future for ${this.msg.header.msg_type} message before replies were done`))),super.dispose()}async handleMsg(e){switch(e.channel){case"control":case"shell":e.channel===this.msg.channel&&e.parent_header.msg_id===this.msg.header.msg_id&&await this._handleReply(e);break;case"stdin":await this._handleStdin(e);break;case"iopub":await this._handleIOPub(e)}}async _handleReply(e){const t=this._reply;t&&await t(e),this._replyMsg=e,this._setFlag(h.KernelFutureFlag.GotReply),this._testFlag(h.KernelFutureFlag.GotIdle)&&this._handleDone()}async _handleStdin(e){this._kernel.hasPendingInput=!0;const t=this._stdin;t&&await t(e)}async _handleIOPub(e){const t=await this._hooks.process(e),s=this._iopub;t&&s&&await s(e),l.isStatusMsg(e)&&"idle"===e.content.execution_state&&(this._setFlag(h.KernelFutureFlag.GotIdle),this._testFlag(h.KernelFutureFlag.GotReply)&&this._handleDone())}_handleDone(){this._testFlag(h.KernelFutureFlag.IsDone)||(this._setFlag(h.KernelFutureFlag.IsDone),this._done.resolve(this._replyMsg),this._disposeOnDone&&this.dispose())}_testFlag(e){return 0!=(this._status&e)}_setFlag(e){this._status|=e}}var h;t.KernelFutureHandler=c,t.KernelControlFutureHandler=class extends c{},t.KernelShellFutureHandler=class extends c{},function(e){e.noOp=()=>{};const t="function"==typeof requestAnimationFrame?requestAnimationFrame:setImmediate;let s;e.HookList=class{constructor(){this._hooks=[]}add(e){this.remove(e),this._hooks.push(e)}remove(e){const t=this._hooks.indexOf(e);t>=0&&(this._hooks[t]=null,this._scheduleCompact())}async process(e){await this._processing;const t=new o.PromiseDelegate;let s;this._processing=t.promise;for(let n=this._hooks.length-1;n>=0;n--){const i=this._hooks[n];if(null!==i){try{s=await i(e)}catch(e){s=!0,console.error(e)}if(!1===s)return t.resolve(void 0),!1}}return t.resolve(void 0),!0}_scheduleCompact(){this._compactScheduled||(this._compactScheduled=!0,t((()=>{this._processing=this._processing.then((()=>{this._compactScheduled=!1,this._compact()}))})))}_compact(){let e=0;for(let t=0,s=this._hooks.length;t<s;t++){const s=this._hooks[t];null===this._hooks[t]?e++:this._hooks[t-e]=s}this._hooks.length-=e}},function(e){e[e.GotReply=1]="GotReply",e[e.GotIdle=2]="GotIdle",e[e.IsDone=4]="IsDone",e[e.DisposeOnDone=8]="DisposeOnDone"}(s=e.KernelFutureFlag||(e.KernelFutureFlag={}))}(h||(h={}))},30335:function(e,t,s){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,s,n){void 0===n&&(n=s);var i=Object.getOwnPropertyDescriptor(t,s);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[s]}}),Object.defineProperty(e,n,i)}:function(e,t,s,n){void 0===n&&(n=s),e[n]=t[s]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var s in e)"default"!==s&&Object.prototype.hasOwnProperty.call(e,s)&&n(t,e,s);return i(t,e),t},o=this&&this.__exportStar||function(e,t){for(var s in e)"default"===s||Object.prototype.hasOwnProperty.call(t,s)||n(t,e,s)};Object.defineProperty(t,"__esModule",{value:!0}),t.KernelConnection=t.KernelAPI=t.KernelMessage=t.Kernel=void 0;const a=r(s(56357));t.Kernel=a;const l=r(s(70552));t.KernelMessage=l;const c=r(s(33505));t.KernelAPI=c;const h=s(45992);Object.defineProperty(t,"KernelConnection",{enumerable:!0,get:function(){return h.KernelConnection}}),o(s(57729),t)},56357:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},57729:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.KernelManager=void 0;const n=s(97934),i=s(81997),r=s(83676),o=s(78037),a=s(33505),l=s(45992);class c extends o.BaseManager{constructor(e={}){var t;super(e),this._isReady=!1,this._kernelConnections=new Set,this._models=new Map,this._runningChanged=new i.Signal(this),this._connectionFailure=new i.Signal(this),this._pollModels=new n.Poll({auto:!1,factory:()=>this.requestRunning(),frequency:{interval:1e4,backoff:!0,max:3e5},name:"@jupyterlab/services:KernelManager#models",standby:null!==(t=e.standby)&&void 0!==t?t:"when-hidden"}),this._ready=(async()=>{await this._pollModels.start(),await this._pollModels.tick,this._isReady=!0})()}get isReady(){return this._isReady}get ready(){return this._ready}get runningChanged(){return this._runningChanged}get connectionFailure(){return this._connectionFailure}dispose(){this.isDisposed||(this._models.clear(),this._kernelConnections.forEach((e=>e.dispose())),this._pollModels.dispose(),super.dispose())}connectTo(e){var t;const{id:s}=e.model;let n=null===(t=e.handleComms)||void 0===t||t;if(void 0===e.handleComms)for(const e of this._kernelConnections)if(e.id===s&&e.handleComms){n=!1;break}const i=new l.KernelConnection({handleComms:n,...e,serverSettings:this.serverSettings});return this._onStarted(i),this._models.has(s)||this.refreshRunning().catch((()=>{})),i}running(){return this._models.values()}async refreshRunning(){await this._pollModels.refresh(),await this._pollModels.tick}async startNew(e={},t={}){const s=await(0,a.startNew)(e,this.serverSettings);return this.connectTo({...t,model:s})}async shutdown(e){await(0,a.shutdownKernel)(e,this.serverSettings),await this.refreshRunning()}async shutdownAll(){await this.refreshRunning(),await Promise.all([...this._models.keys()].map((e=>(0,a.shutdownKernel)(e,this.serverSettings)))),await this.refreshRunning()}async findById(e){return this._models.has(e)||await this.refreshRunning(),this._models.get(e)}async requestRunning(){var e,t;let s;try{s=await(0,a.listRunning)(this.serverSettings)}catch(s){throw(s instanceof r.ServerConnection.NetworkError||503===(null===(e=s.response)||void 0===e?void 0:e.status)||424===(null===(t=s.response)||void 0===t?void 0:t.status))&&this._connectionFailure.emit(s),s}this.isDisposed||this._models.size===s.length&&s.every((e=>{const t=this._models.get(e.id);return!!t&&t.connections===e.connections&&t.execution_state===e.execution_state&&t.last_activity===e.last_activity&&t.name===e.name&&t.reason===e.reason&&t.traceback===e.traceback}))||(this._models=new Map(s.map((e=>[e.id,e]))),this._kernelConnections.forEach((e=>{this._models.has(e.id)||e.handleShutdown()})),this._runningChanged.emit(s))}_onStarted(e){this._kernelConnections.add(e),e.statusChanged.connect(this._onStatusChanged,this),e.disposed.connect(this._onDisposed,this)}_onDisposed(e){this._kernelConnections.delete(e),this.refreshRunning().catch((()=>{}))}_onStatusChanged(e,t){"dead"===t&&this.refreshRunning().catch((()=>{}))}}t.KernelManager=c,function(e){e.NoopManager=class extends e{constructor(){super(...arguments),this._readyPromise=new Promise((()=>{}))}get isActive(){return!1}get parentReady(){return super.ready}async startNew(e={},t={}){return Promise.reject(new Error("Not implemented in no-op Kernel Manager"))}connectTo(e){throw new Error("Not implemented in no-op Kernel Manager")}async shutdown(e){return Promise.reject(new Error("Not implemented in no-op Kernel Manager"))}get ready(){return this.parentReady.then((()=>this._readyPromise))}async requestRunning(){return Promise.resolve()}}}(c||(t.KernelManager=c={}))},70552:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.supportedKernelWebSocketProtocols=t.isInputReplyMsg=t.isInputRequestMsg=t.isDebugReplyMsg=t.isDebugRequestMsg=t.isExecuteReplyMsg=t.isInfoRequestMsg=t.isCommMsgMsg=t.isCommCloseMsg=t.isCommOpenMsg=t.isDebugEventMsg=t.isClearOutputMsg=t.isStatusMsg=t.isErrorMsg=t.isExecuteResultMsg=t.isExecuteInputMsg=t.isUpdateDisplayDataMsg=t.isDisplayDataMsg=t.isStreamMsg=t.createMessage=void 0;const n=s(20998);var i;t.createMessage=function(e){var t,s,i,r,o;return{buffers:null!==(t=e.buffers)&&void 0!==t?t:[],channel:e.channel,content:e.content,header:{date:(new Date).toISOString(),msg_id:null!==(s=e.msgId)&&void 0!==s?s:n.UUID.uuid4(),msg_type:e.msgType,session:e.session,username:null!==(i=e.username)&&void 0!==i?i:"",version:"5.2"},metadata:null!==(r=e.metadata)&&void 0!==r?r:{},parent_header:null!==(o=e.parentHeader)&&void 0!==o?o:{}}},t.isStreamMsg=function(e){return"stream"===e.header.msg_type},t.isDisplayDataMsg=function(e){return"display_data"===e.header.msg_type},t.isUpdateDisplayDataMsg=function(e){return"update_display_data"===e.header.msg_type},t.isExecuteInputMsg=function(e){return"execute_input"===e.header.msg_type},t.isExecuteResultMsg=function(e){return"execute_result"===e.header.msg_type},t.isErrorMsg=function(e){return"error"===e.header.msg_type},t.isStatusMsg=function(e){return"status"===e.header.msg_type},t.isClearOutputMsg=function(e){return"clear_output"===e.header.msg_type},t.isDebugEventMsg=function(e){return"debug_event"===e.header.msg_type},t.isCommOpenMsg=function(e){return"comm_open"===e.header.msg_type},t.isCommCloseMsg=function(e){return"comm_close"===e.header.msg_type},t.isCommMsgMsg=function(e){return"comm_msg"===e.header.msg_type},t.isInfoRequestMsg=function(e){return"kernel_info_request"===e.header.msg_type},t.isExecuteReplyMsg=function(e){return"execute_reply"===e.header.msg_type},t.isDebugRequestMsg=function(e){return"debug_request"===e.header.msg_type},t.isDebugReplyMsg=function(e){return"debug_reply"===e.header.msg_type},t.isInputRequestMsg=function(e){return"input_request"===e.header.msg_type},t.isInputReplyMsg=function(e){return"input_reply"===e.header.msg_type},function(e){e.v1KernelWebsocketJupyterOrg="v1.kernel.websocket.jupyter.org"}(i||(t.supportedKernelWebSocketProtocols=i={}))},33505:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getKernelModel=t.shutdownKernel=t.interruptKernel=t.restartKernel=t.startNew=t.listRunning=t.KERNEL_SERVICE_URL=void 0;const n=s(43851),i=s(38639),r=s(92917);t.KERNEL_SERVICE_URL="api/kernels",t.listRunning=async function(e=n.ServerConnection.makeSettings()){const s=i.URLExt.join(e.baseUrl,t.KERNEL_SERVICE_URL),o=await n.ServerConnection.makeRequest(s,{},e);if(200!==o.status)throw await n.ServerConnection.ResponseError.create(o);const a=await o.json();return(0,r.validateModels)(a),a},t.startNew=async function(e={},s=n.ServerConnection.makeSettings()){const o=i.URLExt.join(s.baseUrl,t.KERNEL_SERVICE_URL),a={method:"POST",body:JSON.stringify(e)},l=await n.ServerConnection.makeRequest(o,a,s);if(201!==l.status)throw await n.ServerConnection.ResponseError.create(l);const c=await l.json();return(0,r.validateModel)(c),c},t.restartKernel=async function(e,s=n.ServerConnection.makeSettings()){const o=i.URLExt.join(s.baseUrl,t.KERNEL_SERVICE_URL,encodeURIComponent(e),"restart"),a=await n.ServerConnection.makeRequest(o,{method:"POST"},s);if(200!==a.status)throw await n.ServerConnection.ResponseError.create(a);const l=await a.json();(0,r.validateModel)(l)},t.interruptKernel=async function(e,s=n.ServerConnection.makeSettings()){const r=i.URLExt.join(s.baseUrl,t.KERNEL_SERVICE_URL,encodeURIComponent(e),"interrupt"),o=await n.ServerConnection.makeRequest(r,{method:"POST"},s);if(204!==o.status)throw await n.ServerConnection.ResponseError.create(o)},t.shutdownKernel=async function(e,s=n.ServerConnection.makeSettings()){const r=i.URLExt.join(s.baseUrl,t.KERNEL_SERVICE_URL,encodeURIComponent(e)),o=await n.ServerConnection.makeRequest(r,{method:"DELETE"},s);if(404===o.status){const t=`The kernel "${e}" does not exist on the server`;console.warn(t)}else if(204!==o.status)throw await n.ServerConnection.ResponseError.create(o)},t.getKernelModel=async function(e,s=n.ServerConnection.makeSettings()){const o=i.URLExt.join(s.baseUrl,t.KERNEL_SERVICE_URL,encodeURIComponent(e)),a=await n.ServerConnection.makeRequest(o,{},s);if(404===a.status)return;if(200!==a.status)throw await n.ServerConnection.ResponseError.create(a);const l=await a.json();return(0,r.validateModel)(l),l}},81259:function(e,t,s){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,s,n){void 0===n&&(n=s);var i=Object.getOwnPropertyDescriptor(t,s);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[s]}}),Object.defineProperty(e,n,i)}:function(e,t,s,n){void 0===n&&(n=s),e[n]=t[s]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var s in e)"default"!==s&&Object.prototype.hasOwnProperty.call(e,s)&&n(t,e,s);return i(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.deserialize=t.serialize=void 0;const o=r(s(70552));var a;t.serialize=function(e,t=""){return t===o.supportedKernelWebSocketProtocols.v1KernelWebsocketJupyterOrg?a.serializeV1KernelWebsocketJupyterOrg(e):a.serializeDefault(e)},t.deserialize=function(e,t=""){return t===o.supportedKernelWebSocketProtocols.v1KernelWebsocketJupyterOrg?a.deserializeV1KernelWebsocketJupyterOrg(e):a.deserializeDefault(e)},function(e){e.deserializeV1KernelWebsocketJupyterOrg=function(e){let t;const s=new DataView(e),n=Number(s.getBigUint64(0,!0));let i=[];for(let e=0;e<n;e++)i.push(Number(s.getBigUint64(8*(e+1),!0)));const r=new TextDecoder("utf8"),o=r.decode(e.slice(i[0],i[1])),a=JSON.parse(r.decode(e.slice(i[1],i[2]))),l=JSON.parse(r.decode(e.slice(i[2],i[3]))),c=JSON.parse(r.decode(e.slice(i[3],i[4]))),h=JSON.parse(r.decode(e.slice(i[4],i[5])));let u=[];for(let t=5;t<i.length-1;t++)u.push(new DataView(e.slice(i[t],i[t+1])));return t={channel:o,header:a,parent_header:l,metadata:c,content:h,buffers:u},t},e.serializeV1KernelWebsocketJupyterOrg=function(e){const t=JSON.stringify(e.header),s=null==e.parent_header?"{}":JSON.stringify(e.parent_header),n=JSON.stringify(e.metadata),i=JSON.stringify(e.content),r=void 0!==e.buffers?e.buffers:[],o=5+r.length+1;let a=[];a.push(8*(1+o)),a.push(e.channel.length+a[a.length-1]);const l=new TextEncoder,c=l.encode(e.channel),h=l.encode(t),u=l.encode(s),d=l.encode(n),p=l.encode(i),_=new Uint8Array(c.length+h.length+u.length+d.length+p.length);_.set(c),_.set(h,c.length),_.set(u,c.length+h.length),_.set(d,c.length+h.length+u.length),_.set(p,c.length+h.length+u.length+d.length);for(let e of[h.length,u.length,d.length,p.length])a.push(e+a[a.length-1]);let g=0;for(let e of r){let t=e.byteLength;a.push(t+a[a.length-1]),g+=t}const m=new Uint8Array(8*(1+o)+_.byteLength+g),f=new ArrayBuffer(8),v=new DataView(f);v.setBigUint64(0,BigInt(o),!0),m.set(new Uint8Array(f),0);for(let e=0;e<a.length;e++)v.setBigUint64(0,BigInt(a[e]),!0),m.set(new Uint8Array(f),8*(e+1));m.set(_,a[0]);for(let e=0;e<r.length;e++){const t=r[e];m.set(new Uint8Array(ArrayBuffer.isView(t)?t.buffer:t),a[5+e])}return m.buffer},e.deserializeDefault=function(e){let t;return t="string"==typeof e?JSON.parse(e):function(e){const t=new DataView(e),s=t.getUint32(0),n=[];if(s<2)throw new Error("Invalid incoming Kernel Message");for(let e=1;e<=s;e++)n.push(t.getUint32(4*e));const i=new Uint8Array(e.slice(n[0],n[1])),r=JSON.parse(new TextDecoder("utf8").decode(i));r.buffers=[];for(let t=1;t<s;t++){const s=n[t],i=n[t+1]||e.byteLength;r.buffers.push(new DataView(e.slice(s,i)))}return r}(e),t},e.serializeDefault=function(e){var t;let s;return s=(null===(t=e.buffers)||void 0===t?void 0:t.length)?function(e){const t=[],s=[],n=new TextEncoder;let i=[];void 0!==e.buffers&&(i=e.buffers,delete e.buffers);const r=n.encode(JSON.stringify(e));s.push(r.buffer);for(let e=0;e<i.length;e++){const t=i[e];s.push(ArrayBuffer.isView(t)?t.buffer:t)}const o=s.length;t.push(4*(o+1));for(let e=0;e+1<s.length;e++)t.push(t[t.length-1]+s[e].byteLength);const a=new Uint8Array(t[t.length-1]+s[s.length-1].byteLength),l=new DataView(a.buffer);l.setUint32(0,o);for(let e=0;e<t.length;e++)l.setUint32(4*(e+1),t[e]);for(let e=0;e<s.length;e++)a.set(new Uint8Array(s[e]),t[e]);return a.buffer}(e):JSON.stringify(e),s}}(a||(a={}))},92917:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateModels=t.validateModel=t.validateMessage=void 0;const n=s(45669),i=["username","version","session","msg_id","msg_type"],r={stream:{name:"string",text:"string"},display_data:{data:"object",metadata:"object"},execute_input:{code:"string",execution_count:"number"},execute_result:{execution_count:"number",data:"object",metadata:"object"},error:{ename:"string",evalue:"string",traceback:"object"},status:{execution_state:["string",["starting","idle","busy","restarting","dead"]]},clear_output:{wait:"boolean"},comm_open:{comm_id:"string",target_name:"string",data:"object"},comm_msg:{comm_id:"string",data:"object"},comm_close:{comm_id:"string"},shutdown_reply:{restart:"boolean"}};function o(e){(0,n.validateProperty)(e,"name","string"),(0,n.validateProperty)(e,"id","string")}t.validateMessage=function(e){(0,n.validateProperty)(e,"metadata","object"),(0,n.validateProperty)(e,"content","object"),(0,n.validateProperty)(e,"channel","string"),function(e){for(let t=0;t<i.length;t++)(0,n.validateProperty)(e,i[t],"string")}(e.header),"iopub"===e.channel&&function(e){if("iopub"===e.channel){const t=r[e.header.msg_type];if(void 0===t)return;const s=Object.keys(t),i=e.content;for(let e=0;e<s.length;e++){let r=t[s[e]];Array.isArray(r)||(r=[r]),(0,n.validateProperty)(i,s[e],...r)}}}(e)},t.validateModel=o,t.validateModels=function(e){if(!Array.isArray(e))throw new Error("Invalid kernel list");e.forEach((e=>o(e)))}},27504:function(e,t,s){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,s,n){void 0===n&&(n=s);var i=Object.getOwnPropertyDescriptor(t,s);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[s]}}),Object.defineProperty(e,n,i)}:function(e,t,s,n){void 0===n&&(n=s),e[n]=t[s]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var s in e)"default"!==s&&Object.prototype.hasOwnProperty.call(e,s)&&n(t,e,s);return i(t,e),t},o=this&&this.__exportStar||function(e,t){for(var s in e)"default"===s||Object.prototype.hasOwnProperty.call(t,s)||n(t,e,s)};Object.defineProperty(t,"__esModule",{value:!0}),t.KernelSpecAPI=t.KernelSpec=void 0;const a=r(s(8441));t.KernelSpec=a;const l=r(s(29723));t.KernelSpecAPI=l,o(s(15691),t)},8441:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},15691:function(e,t,s){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,s,n){void 0===n&&(n=s);var i=Object.getOwnPropertyDescriptor(t,s);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[s]}}),Object.defineProperty(e,n,i)}:function(e,t,s,n){void 0===n&&(n=s),e[n]=t[s]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var s in e)"default"!==s&&Object.prototype.hasOwnProperty.call(e,s)&&n(t,e,s);return i(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.KernelSpecManager=void 0;const o=s(20998),a=s(97934),l=s(81997),c=r(s(29723)),h=s(78037);class u extends h.BaseManager{constructor(e={}){var t;super(e),this._isReady=!1,this._connectionFailure=new l.Signal(this),this._specs=null,this._specsChanged=new l.Signal(this),this._ready=Promise.all([this.requestSpecs()]).then((e=>{})).catch((e=>{})).then((()=>{this.isDisposed||(this._isReady=!0)})),this._pollSpecs=new a.Poll({auto:!1,factory:()=>this.requestSpecs(),frequency:{interval:61e3,backoff:!0,max:3e5},name:"@jupyterlab/services:KernelSpecManager#specs",standby:null!==(t=e.standby)&&void 0!==t?t:"when-hidden"}),this.ready.then((()=>{this._pollSpecs.start()}))}get isReady(){return this._isReady}get ready(){return this._ready}get specs(){return this._specs}get specsChanged(){return this._specsChanged}get connectionFailure(){return this._connectionFailure}dispose(){this._pollSpecs.dispose(),super.dispose()}async refreshSpecs(){await this._pollSpecs.refresh(),await this._pollSpecs.tick}async requestSpecs(){const e=await c.getSpecs(this.serverSettings);this.isDisposed||o.JSONExt.deepEqual(e,this._specs)||(this._specs=e,this._specsChanged.emit(e))}}t.KernelSpecManager=u},29723:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getSpecs=void 0;const n=s(43851),i=s(4891),r=s(38639);t.getSpecs=async function(e=n.ServerConnection.makeSettings()){const t=r.URLExt.join(e.baseUrl,"api/kernelspecs"),s=await n.ServerConnection.makeRequest(t,{},e);if(200!==s.status)throw await n.ServerConnection.ResponseError.create(s);const o=await s.json();return(0,i.validateSpecModels)(o)}},4891:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateSpecModels=t.validateSpecModel=void 0;const n=s(45669);function i(e){const t=e.spec;if(!t)throw new Error("Invalid kernel spec");(0,n.validateProperty)(e,"name","string"),(0,n.validateProperty)(e,"resources","object"),(0,n.validateProperty)(t,"language","string"),(0,n.validateProperty)(t,"display_name","string"),(0,n.validateProperty)(t,"argv","array");let s=null;t.hasOwnProperty("metadata")&&((0,n.validateProperty)(t,"metadata","object"),s=t.metadata);let i=null;return t.hasOwnProperty("env")&&((0,n.validateProperty)(t,"env","object"),i=t.env),{name:e.name,resources:e.resources,language:t.language,display_name:t.display_name,argv:t.argv,metadata:s,env:i}}t.validateSpecModel=i,t.validateSpecModels=function(e){if(!e.hasOwnProperty("kernelspecs"))throw new Error("No kernelspecs found");let t=Object.keys(e.kernelspecs);const s=Object.create(null);let n=e.default;for(let n=0;n<t.length;n++){const r=e.kernelspecs[t[n]];try{s[t[n]]=i(r)}catch(e){console.warn(`Removing errant kernel spec: ${t[n]}`)}}if(t=Object.keys(s),!t.length)throw new Error("No valid kernelspecs found");return n&&"string"==typeof n&&n in s||(n=t[0],console.warn(`Default kernel not found, using '${t[0]}'`)),{default:n,kernelspecs:s}}},84711:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ServiceManager=void 0;const n=s(81997),i=s(76418),r=s(87594),o=s(95348),a=s(30335),l=s(27504),c=s(8178),h=s(43851),u=s(82351),d=s(69494),p=s(23549),_=s(78162),g=s(53265);t.ServiceManager=class{constructor(e={}){var t,s;this._isDisposed=!1,this._connectionFailure=new n.Signal(this),this._isReady=!1;const m=e.defaultDrive,f=null!==(t=e.serverSettings)&&void 0!==t?t:h.ServerConnection.makeSettings(),v={defaultDrive:m,serverSettings:f,standby:null!==(s=e.standby)&&void 0!==s?s:"when-hidden"};this.serverSettings=f,this.contents=e.contents||new r.ContentsManager(v),this.events=e.events||new o.EventManager(v),this.kernels=e.kernels||new a.KernelManager(v),this.sessions=e.sessions||new u.SessionManager({...v,kernelManager:this.kernels}),this.settings=e.settings||new d.SettingManager(v),this.terminals=e.terminals||new p.TerminalManager(v),this.builder=e.builder||new i.BuildManager(v),this.workspaces=e.workspaces||new g.WorkspaceManager(v),this.nbconvert=e.nbconvert||new c.NbConvertManager(v),this.kernelspecs=e.kernelspecs||new l.KernelSpecManager(v),this.user=e.user||new _.UserManager(v),this.kernelspecs.connectionFailure.connect(this._onConnectionFailure,this),this.sessions.connectionFailure.connect(this._onConnectionFailure,this),this.terminals.connectionFailure.connect(this._onConnectionFailure,this);const w=[this.sessions.ready,this.kernelspecs.ready];this.terminals.isAvailable()&&w.push(this.terminals.ready),this._readyPromise=Promise.all(w).then((()=>{this._isReady=!0}))}get connectionFailure(){return this._connectionFailure}get isDisposed(){return this._isDisposed}dispose(){this.isDisposed||(this._isDisposed=!0,n.Signal.clearData(this),this.contents.dispose(),this.events.dispose(),this.sessions.dispose(),this.terminals.dispose())}get isReady(){return this._isReady}get ready(){return this._readyPromise}_onConnectionFailure(e,t){this._connectionFailure.emit(t)}}},8178:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.NbConvertManager=void 0;const n=s(38639),i=s(43851),r=s(20998);t.NbConvertManager=class{constructor(e={}){var t;this._exportFormats=null,this.serverSettings=null!==(t=e.serverSettings)&&void 0!==t?t:i.ServerConnection.makeSettings()}async fetchExportFormats(){this._requestingFormats=new r.PromiseDelegate,this._exportFormats=null;const e=this.serverSettings.baseUrl,t=n.URLExt.join(e,"api/nbconvert"),{serverSettings:s}=this,o=await i.ServerConnection.makeRequest(t,{},s);if(200!==o.status)throw await i.ServerConnection.ResponseError.create(o);const a=await o.json(),l={};return Object.keys(a).forEach((function(e){const t=a[e].output_mimetype;l[e]={output_mimetype:t}})),this._exportFormats=l,this._requestingFormats.resolve(l),l}async getExportFormats(e=!0){return this._requestingFormats?this._requestingFormats.promise:e||!this._exportFormats?await this.fetchExportFormats():this._exportFormats}}},43851:(e,t,s)=>{"use strict";var n=s(27061);Object.defineProperty(t,"__esModule",{value:!0}),t.ServerConnection=void 0;const i=s(38639),r=s(81259);let o;var a,l;o="undefined"==typeof window?s(67277):WebSocket,function(e){e.makeSettings=function(e){return l.makeSettings(e)},e.makeRequest=function(e,t,s){return l.handleRequest(e,t,s)};class t extends Error{static async create(e){try{const s=await e.json(),{message:n,traceback:i}=s;return i&&console.error(i),new t(e,null!=n?n:t._defaultMessage(e),null!=i?i:"")}catch(s){return console.debug(s),new t(e)}}constructor(e,s=t._defaultMessage(e),n=""){super(s),this.response=e,this.traceback=n}static _defaultMessage(e){return`Invalid response: ${e.status} ${e.statusText}`}}e.ResponseError=t;class s extends TypeError{constructor(e){super(e.message),this.stack=e.stack}}e.NetworkError=s}(a||(t.ServerConnection=a={})),function(e){e.makeSettings=function(e={}){var t;const s=i.PageConfig.getBaseUrl(),a=i.PageConfig.getWsUrl(),l=i.URLExt.normalize(e.baseUrl)||s;let c=e.wsUrl;return c||l!==s||(c=a),c||0!==l.indexOf("http")||(c="ws"+l.slice(4)),c=null!=c?c:a,{init:{cache:"no-store",credentials:"same-origin"},fetch,Headers,Request,WebSocket:o,token:i.PageConfig.getToken(),appUrl:i.PageConfig.getOption("appUrl"),appendToken:"undefined"==typeof window||void 0!==n&&void 0!==(null===(t=null==n?void 0:n.env)||void 0===t?void 0:t.JEST_WORKER_ID)||i.URLExt.getHostName(s)!==i.URLExt.getHostName(c),serializer:{serialize:r.serialize,deserialize:r.deserialize},...e,baseUrl:l,wsUrl:c}},e.handleRequest=function(e,t,s){var n;if(0!==e.indexOf(s.baseUrl))throw new Error("Can only be used for notebook server requests");"no-store"===(null!==(n=t.cache)&&void 0!==n?n:s.init.cache)&&(e+=(/\?/.test(e)?"&":"?")+(new Date).getTime());const i=new s.Request(e,{...s.init,...t});let r=!1;if(s.token&&(r=!0,i.headers.append("Authorization",`token ${s.token}`)),"undefined"!=typeof document){const e=function(e){let t="";try{t=document.cookie}catch(e){return}const s=t.match("\\b_xsrf=([^;]*)\\b");return null==s?void 0:s[1]}();void 0!==e&&(r=!0,i.headers.append("X-XSRFToken",e))}return!i.headers.has("Content-Type")&&r&&i.headers.set("Content-Type","application/json"),s.fetch.call(null,i).catch((e=>{throw new a.NetworkError(e)}))}}(l||(l={}))},46858:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SessionConnection=void 0;const n=s(81997),i=s(83676),r=s(79),o=s(20998);t.SessionConnection=class{constructor(e){var t,s,r,a;this._id="",this._path="",this._name="",this._type="",this._kernel=null,this._isDisposed=!1,this._disposed=new n.Signal(this),this._kernelChanged=new n.Signal(this),this._statusChanged=new n.Signal(this),this._connectionStatusChanged=new n.Signal(this),this._pendingInput=new n.Signal(this),this._iopubMessage=new n.Signal(this),this._unhandledMessage=new n.Signal(this),this._anyMessage=new n.Signal(this),this._propertyChanged=new n.Signal(this),this._id=e.model.id,this._name=e.model.name,this._path=e.model.path,this._type=e.model.type,this._username=null!==(t=e.username)&&void 0!==t?t:"",this._clientId=null!==(s=e.clientId)&&void 0!==s?s:o.UUID.uuid4(),this._connectToKernel=e.connectToKernel,this._kernelConnectionOptions=null!==(r=e.kernelConnectionOptions)&&void 0!==r?r:{},this.serverSettings=null!==(a=e.serverSettings)&&void 0!==a?a:i.ServerConnection.makeSettings(),this.setupKernel(e.model.kernel)}get disposed(){return this._disposed}get kernelChanged(){return this._kernelChanged}get statusChanged(){return this._statusChanged}get connectionStatusChanged(){return this._connectionStatusChanged}get pendingInput(){return this._pendingInput}get iopubMessage(){return this._iopubMessage}get unhandledMessage(){return this._unhandledMessage}get anyMessage(){return this._anyMessage}get propertyChanged(){return this._propertyChanged}get id(){return this._id}get kernel(){return this._kernel}get path(){return this._path}get type(){return this._type}get name(){return this._name}get model(){return{id:this.id,kernel:this.kernel&&{id:this.kernel.id,name:this.kernel.name},path:this._path,type:this._type,name:this._name}}get isDisposed(){return this._isDisposed}update(e){const t=this.model;if(this._path=e.path,this._name=e.name,this._type=e.type,null===this._kernel&&null!==e.kernel||null!==this._kernel&&null===e.kernel||null!==this._kernel&&null!==e.kernel&&this._kernel.id!==e.kernel.id){null!==this._kernel&&this._kernel.dispose();const t=this._kernel||null;this.setupKernel(e.kernel);const s=this._kernel||null;this._kernelChanged.emit({name:"kernel",oldValue:t,newValue:s})}this._handleModelChange(t)}dispose(){if(!this.isDisposed){if(this._isDisposed=!0,this._disposed.emit(),this._kernel){this._kernel.dispose();const e=this._kernel;this._kernel=null;const t=this._kernel;this._kernelChanged.emit({name:"kernel",oldValue:e,newValue:t})}n.Signal.clearData(this)}}async setPath(e){if(this.isDisposed)throw new Error("Session is disposed");await this._patch({path:e})}async setName(e){if(this.isDisposed)throw new Error("Session is disposed");await this._patch({name:e})}async setType(e){if(this.isDisposed)throw new Error("Session is disposed");await this._patch({type:e})}async changeKernel(e){if(this.isDisposed)throw new Error("Session is disposed");return await this._patch({kernel:e}),this.kernel}async shutdown(){if(this.isDisposed)throw new Error("Session is disposed");await(0,r.shutdownSession)(this.id,this.serverSettings),this.dispose()}setupKernel(e){if(null===e)return void(this._kernel=null);const t=this._connectToKernel({...this._kernelConnectionOptions,model:e,username:this._username,clientId:this._clientId,serverSettings:this.serverSettings});this._kernel=t,t.statusChanged.connect(this.onKernelStatus,this),t.connectionStatusChanged.connect(this.onKernelConnectionStatus,this),t.pendingInput.connect(this.onPendingInput,this),t.unhandledMessage.connect(this.onUnhandledMessage,this),t.iopubMessage.connect(this.onIOPubMessage,this),t.anyMessage.connect(this.onAnyMessage,this)}onKernelStatus(e,t){this._statusChanged.emit(t)}onKernelConnectionStatus(e,t){this._connectionStatusChanged.emit(t)}onPendingInput(e,t){this._pendingInput.emit(t)}onIOPubMessage(e,t){this._iopubMessage.emit(t)}onUnhandledMessage(e,t){this._unhandledMessage.emit(t)}onAnyMessage(e,t){this._anyMessage.emit(t)}async _patch(e){const t=await(0,r.updateSession)({...e,id:this._id},this.serverSettings);return this.update(t),t}_handleModelChange(e){e.name!==this._name&&this._propertyChanged.emit("name"),e.type!==this._type&&this._propertyChanged.emit("type"),e.path!==this._path&&this._propertyChanged.emit("path")}}},82351:function(e,t,s){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,s,n){void 0===n&&(n=s);var i=Object.getOwnPropertyDescriptor(t,s);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[s]}}),Object.defineProperty(e,n,i)}:function(e,t,s,n){void 0===n&&(n=s),e[n]=t[s]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var s in e)"default"!==s&&Object.prototype.hasOwnProperty.call(e,s)&&n(t,e,s);return i(t,e),t},o=this&&this.__exportStar||function(e,t){for(var s in e)"default"===s||Object.prototype.hasOwnProperty.call(t,s)||n(t,e,s)};Object.defineProperty(t,"__esModule",{value:!0}),t.SessionAPI=t.Session=void 0;const a=r(s(62560));t.Session=a;const l=r(s(79));t.SessionAPI=l,o(s(75028),t)},75028:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SessionManager=void 0;const n=s(97934),i=s(81997),r=s(43851),o=s(78037),a=s(46858),l=s(79);class c extends o.BaseManager{constructor(e){var t;super(e),this._isReady=!1,this._sessionConnections=new Set,this._models=new Map,this._runningChanged=new i.Signal(this),this._connectionFailure=new i.Signal(this),this._connectToKernel=e=>this._kernelManager.connectTo(e),this._kernelManager=e.kernelManager,this._pollModels=new n.Poll({auto:!1,factory:()=>this.requestRunning(),frequency:{interval:1e4,backoff:!0,max:3e5},name:"@jupyterlab/services:SessionManager#models",standby:null!==(t=e.standby)&&void 0!==t?t:"when-hidden"}),this._ready=(async()=>{await this._pollModels.start(),await this._pollModels.tick,this._kernelManager.isActive&&await this._kernelManager.ready,this._isReady=!0})()}get isReady(){return this._isReady}get ready(){return this._ready}get runningChanged(){return this._runningChanged}get connectionFailure(){return this._connectionFailure}dispose(){this.isDisposed||(this._models.clear(),this._sessionConnections.forEach((e=>e.dispose())),this._pollModels.dispose(),super.dispose())}connectTo(e){const t=new a.SessionConnection({...e,connectToKernel:this._connectToKernel,serverSettings:this.serverSettings});return this._onStarted(t),this._models.has(e.model.id)||this.refreshRunning().catch((()=>{})),t}running(){return this._models.values()}async refreshRunning(){await this._pollModels.refresh(),await this._pollModels.tick}async startNew(e,t={}){const s=await(0,l.startSession)(e,this.serverSettings);return await this.refreshRunning(),this.connectTo({...t,model:s})}async shutdown(e){await(0,l.shutdownSession)(e,this.serverSettings),await this.refreshRunning()}async shutdownAll(){await this.refreshRunning(),await Promise.all([...this._models.keys()].map((e=>(0,l.shutdownSession)(e,this.serverSettings)))),await this.refreshRunning()}async stopIfNeeded(e){try{const t=(await(0,l.listRunning)(this.serverSettings)).filter((t=>t.path===e));if(1===t.length){const e=t[0].id;await this.shutdown(e)}}catch(e){}}async findById(e){return this._models.has(e)||await this.refreshRunning(),this._models.get(e)}async findByPath(e){for(const t of this._models.values())if(t.path===e)return t;await this.refreshRunning();for(const t of this._models.values())if(t.path===e)return t}async requestRunning(){var e,t;let s;try{s=await(0,l.listRunning)(this.serverSettings)}catch(s){throw(s instanceof r.ServerConnection.NetworkError||503===(null===(e=s.response)||void 0===e?void 0:e.status)||424===(null===(t=s.response)||void 0===t?void 0:t.status))&&this._connectionFailure.emit(s),s}this.isDisposed||this._models.size===s.length&&s.every((e=>{var t,s,n,i;const r=this._models.get(e.id);return!!r&&(null===(t=r.kernel)||void 0===t?void 0:t.id)===(null===(s=e.kernel)||void 0===s?void 0:s.id)&&(null===(n=r.kernel)||void 0===n?void 0:n.name)===(null===(i=e.kernel)||void 0===i?void 0:i.name)&&r.name===e.name&&r.path===e.path&&r.type===e.type}))||(this._models=new Map(s.map((e=>[e.id,e]))),this._sessionConnections.forEach((e=>{this._models.has(e.id)?e.update(this._models.get(e.id)):e.dispose()})),this._runningChanged.emit(s))}_onStarted(e){this._sessionConnections.add(e),e.disposed.connect(this._onDisposed,this),e.propertyChanged.connect(this._onChanged,this),e.kernelChanged.connect(this._onChanged,this)}_onDisposed(e){this._sessionConnections.delete(e),this.refreshRunning().catch((()=>{}))}_onChanged(){this.refreshRunning().catch((()=>{}))}}t.SessionManager=c,function(e){e.NoopManager=class extends e{constructor(){super(...arguments),this._readyPromise=new Promise((()=>{}))}get isActive(){return!1}get parentReady(){return super.ready}async startNew(e,t={}){return Promise.reject(new Error("Not implemented in no-op Session Manager"))}connectTo(e){throw Error("Not implemented in no-op Session Manager")}get ready(){return this.parentReady.then((()=>this._readyPromise))}async shutdown(e){return Promise.reject(new Error("Not implemented in no-op Session Manager"))}async requestRunning(){return Promise.resolve()}}}(c||(t.SessionManager=c={}))},79:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.updateSession=t.startSession=t.getSessionModel=t.shutdownSession=t.getSessionUrl=t.listRunning=t.SESSION_SERVICE_URL=void 0;const n=s(43851),i=s(38639),r=s(11180);function o(e,s){const n=i.URLExt.join(e,t.SESSION_SERVICE_URL),r=i.URLExt.join(n,s);if(!r.startsWith(n))throw new Error("Can only be used for services requests");return r}t.SESSION_SERVICE_URL="api/sessions",t.listRunning=async function(e=n.ServerConnection.makeSettings()){const s=i.URLExt.join(e.baseUrl,t.SESSION_SERVICE_URL),o=await n.ServerConnection.makeRequest(s,{},e);if(200!==o.status)throw await n.ServerConnection.ResponseError.create(o);const a=await o.json();if(!Array.isArray(a))throw new Error("Invalid Session list");return a.forEach((e=>{(0,r.updateLegacySessionModel)(e),(0,r.validateModel)(e)})),a},t.getSessionUrl=o,t.shutdownSession=async function(e,t=n.ServerConnection.makeSettings()){var s;const i=o(t.baseUrl,e),r=await n.ServerConnection.makeRequest(i,{method:"DELETE"},t);if(404===r.status){const t=null!==(s=(await r.json()).message)&&void 0!==s?s:`The session "${e}"" does not exist on the server`;console.warn(t)}else{if(410===r.status)throw new n.ServerConnection.ResponseError(r,"The kernel was deleted but the session was not");if(204!==r.status)throw await n.ServerConnection.ResponseError.create(r)}},t.getSessionModel=async function(e,t=n.ServerConnection.makeSettings()){const s=o(t.baseUrl,e),i=await n.ServerConnection.makeRequest(s,{},t);if(200!==i.status)throw await n.ServerConnection.ResponseError.create(i);const a=await i.json();return(0,r.updateLegacySessionModel)(a),(0,r.validateModel)(a),a},t.startSession=async function(e,s=n.ServerConnection.makeSettings()){const o=i.URLExt.join(s.baseUrl,t.SESSION_SERVICE_URL),a={method:"POST",body:JSON.stringify(e)},l=await n.ServerConnection.makeRequest(o,a,s);if(201!==l.status)throw await n.ServerConnection.ResponseError.create(l);const c=await l.json();return(0,r.updateLegacySessionModel)(c),(0,r.validateModel)(c),c},t.updateSession=async function(e,t=n.ServerConnection.makeSettings()){const s=o(t.baseUrl,e.id),i={method:"PATCH",body:JSON.stringify(e)},a=await n.ServerConnection.makeRequest(s,i,t);if(200!==a.status)throw await n.ServerConnection.ResponseError.create(a);const l=await a.json();return(0,r.updateLegacySessionModel)(l),(0,r.validateModel)(l),l}},62560:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},11180:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateModels=t.updateLegacySessionModel=t.validateModel=void 0;const n=s(92917),i=s(45669);function r(e){(0,i.validateProperty)(e,"id","string"),(0,i.validateProperty)(e,"type","string"),(0,i.validateProperty)(e,"name","string"),(0,i.validateProperty)(e,"path","string"),(0,i.validateProperty)(e,"kernel","object"),(0,n.validateModel)(e.kernel)}t.validateModel=r,t.updateLegacySessionModel=function(e){void 0===e.path&&void 0!==e.notebook&&(e.path=e.notebook.path,e.type="notebook",e.name="")},t.validateModels=function(e){if(!Array.isArray(e))throw new Error("Invalid session list");e.forEach((e=>r(e)))}},69494:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SettingManager=void 0;const n=s(38639),i=s(66853),r=s(43851);class o extends i.DataConnector{constructor(e={}){var t;super(),this.serverSettings=null!==(t=e.serverSettings)&&void 0!==t?t:r.ServerConnection.makeSettings()}async fetch(e){if(!e)throw new Error("Plugin `id` parameter is required for settings fetch.");const{serverSettings:t}=this,{baseUrl:s,appUrl:n}=t,{makeRequest:i,ResponseError:o}=r.ServerConnection,l=s+n,c=a.url(l,e),h=await i(c,{},t);if(200!==h.status)throw await o.create(h);return h.json()}async list(e){var t,s,n,i;const{serverSettings:o}=this,{baseUrl:l,appUrl:c}=o,{makeRequest:h,ResponseError:u}=r.ServerConnection,d=l+c,p=a.url(d,"","ids"===e),_=await h(p,{},o);if(200!==_.status)throw new u(_);const g=await _.json(),m=null!==(s=null===(t=null==g?void 0:g.settings)||void 0===t?void 0:t.map((e=>e.id)))&&void 0!==s?s:[];let f=[];return e||(f=null!==(i=null===(n=null==g?void 0:g.settings)||void 0===n?void 0:n.map((e=>(e.data={composite:{},user:{}},e))))&&void 0!==i?i:[]),{ids:m,values:f}}async save(e,t){const{serverSettings:s}=this,{baseUrl:n,appUrl:i}=s,{makeRequest:o,ResponseError:l}=r.ServerConnection,c=n+i,h=a.url(c,e),u={body:JSON.stringify({raw:t}),method:"PUT"},d=await o(h,u,s);if(204!==d.status)throw new l(d)}}var a;t.SettingManager=o,function(e){e.url=function(e,t,s){const i=s?n.URLExt.objectToQueryString({ids_only:!0}):"",r=n.URLExt.join(e,"api/settings"),o=n.URLExt.join(r,t);if(!o.startsWith(r))throw new Error("Can only be used for workspaces requests");return`${o}${i}`}}(a||(a={}))},67277:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=WebSocket},49253:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TerminalConnection=void 0;const n=s(38639),i=s(20998),r=s(81997),o=s(83676),a=s(11887);class l{constructor(e){var t;this._createSocket=()=>{this._errorIfDisposed(),this._clearSocket(),this._updateConnectionStatus("connecting");const e=this._name,t=this.serverSettings;let s=n.URLExt.join(t.wsUrl,"terminals","websocket",encodeURIComponent(e));const i=t.token;t.appendToken&&""!==i&&(s+=`?token=${encodeURIComponent(i)}`),this._ws=new t.WebSocket(s),this._ws.onmessage=this._onWSMessage,this._ws.onclose=this._onWSClose,this._ws.onerror=this._onWSClose},this._onWSMessage=e=>{if(this._isDisposed)return;const t=JSON.parse(e.data);"disconnect"===t[0]&&this.dispose(),"connecting"!==this._connectionStatus?this._messageReceived.emit({type:t[0],content:t.slice(1)}):"setup"===t[0]&&this._updateConnectionStatus("connected")},this._onWSClose=e=>{console.warn(`Terminal websocket closed: ${e.code}`),this.isDisposed||this._reconnect()},this._connectionStatus="connecting",this._connectionStatusChanged=new r.Signal(this),this._isDisposed=!1,this._disposed=new r.Signal(this),this._messageReceived=new r.Signal(this),this._reconnectTimeout=null,this._ws=null,this._noOp=()=>{},this._reconnectLimit=7,this._reconnectAttempt=0,this._pendingMessages=[],this._name=e.model.name,this.serverSettings=null!==(t=e.serverSettings)&&void 0!==t?t:o.ServerConnection.makeSettings(),this._createSocket()}get disposed(){return this._disposed}get messageReceived(){return this._messageReceived}get name(){return this._name}get model(){return{name:this._name}}get isDisposed(){return this._isDisposed}dispose(){this._isDisposed||(this._isDisposed=!0,this._disposed.emit(),this._updateConnectionStatus("disconnected"),this._clearSocket(),r.Signal.clearData(this))}send(e){this._sendMessage(e)}_sendMessage(e,t=!0){if(!this._isDisposed&&e.content)if("connected"===this.connectionStatus&&this._ws){const t=[e.type,...e.content];this._ws.send(JSON.stringify(t))}else{if(!t)throw new Error(`Could not send message: ${JSON.stringify(e)}`);this._pendingMessages.push(e)}}_sendPending(){for(;"connected"===this.connectionStatus&&this._pendingMessages.length>0;)this._sendMessage(this._pendingMessages[0],!1),this._pendingMessages.shift()}reconnect(){this._errorIfDisposed();const e=new i.PromiseDelegate,t=(s,n)=>{"connected"===n?(e.resolve(),this.connectionStatusChanged.disconnect(t,this)):"disconnected"===n&&(e.reject(new Error("Terminal connection disconnected")),this.connectionStatusChanged.disconnect(t,this))};return this.connectionStatusChanged.connect(t,this),this._reconnectAttempt=0,this._reconnect(),e.promise}_reconnect(){if(this._errorIfDisposed(),clearTimeout(this._reconnectTimeout),this._reconnectAttempt<this._reconnectLimit){this._updateConnectionStatus("connecting");const e=c.getRandomIntInclusive(0,1e3*(Math.pow(2,this._reconnectAttempt)-1));console.error(`Connection lost, reconnecting in ${Math.floor(e/1e3)} seconds.`),this._reconnectTimeout=setTimeout(this._createSocket,e),this._reconnectAttempt+=1}else this._updateConnectionStatus("disconnected");this._clearSocket()}_clearSocket(){null!==this._ws&&(this._ws.onopen=this._noOp,this._ws.onclose=this._noOp,this._ws.onerror=this._noOp,this._ws.onmessage=this._noOp,this._ws.close(),this._ws=null)}async shutdown(){await(0,a.shutdownTerminal)(this.name,this.serverSettings),this.dispose()}clone(){return new l(this)}_updateConnectionStatus(e){this._connectionStatus!==e&&(this._connectionStatus=e,"connecting"!==e&&(this._reconnectAttempt=0,clearTimeout(this._reconnectTimeout)),"connected"===e&&this._sendPending(),this._connectionStatusChanged.emit(e))}_errorIfDisposed(){if(this.isDisposed)throw new Error("Terminal connection is disposed")}get connectionStatusChanged(){return this._connectionStatusChanged}get connectionStatus(){return this._connectionStatus}}var c;t.TerminalConnection=l,function(e){e.getTermUrl=function(e,t){return n.URLExt.join(e,a.TERMINAL_SERVICE_URL,encodeURIComponent(t))},e.getRandomIntInclusive=function(e,t){return e=Math.ceil(e),t=Math.floor(t),Math.floor(Math.random()*(t-e+1))+e}}(c||(c={}))},23549:function(e,t,s){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,s,n){void 0===n&&(n=s);var i=Object.getOwnPropertyDescriptor(t,s);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[s]}}),Object.defineProperty(e,n,i)}:function(e,t,s,n){void 0===n&&(n=s),e[n]=t[s]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var s in e)"default"!==s&&Object.prototype.hasOwnProperty.call(e,s)&&n(t,e,s);return i(t,e),t},o=this&&this.__exportStar||function(e,t){for(var s in e)"default"===s||Object.prototype.hasOwnProperty.call(t,s)||n(t,e,s)};Object.defineProperty(t,"__esModule",{value:!0}),t.TerminalAPI=t.Terminal=void 0;const a=r(s(95310));t.Terminal=a;const l=r(s(11887));t.TerminalAPI=l,o(s(52982),t)},52982:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TerminalManager=void 0;const n=s(97934),i=s(81997),r=s(83676),o=s(78037),a=s(11887),l=s(49253);class c extends o.BaseManager{constructor(e={}){var t;if(super(e),this._isReady=!1,this._names=[],this._terminalConnections=new Set,this._runningChanged=new i.Signal(this),this._connectionFailure=new i.Signal(this),!this.isAvailable())return this._ready=Promise.reject("Terminals unavailable"),void this._ready.catch((e=>{}));this._pollModels=new n.Poll({auto:!1,factory:()=>this.requestRunning(),frequency:{interval:1e4,backoff:!0,max:3e5},name:"@jupyterlab/services:TerminalManager#models",standby:null!==(t=e.standby)&&void 0!==t?t:"when-hidden"}),this._ready=(async()=>{await this._pollModels.start(),await this._pollModels.tick,this._isReady=!0})()}get isReady(){return this._isReady}get ready(){return this._ready}get runningChanged(){return this._runningChanged}get connectionFailure(){return this._connectionFailure}dispose(){this.isDisposed||(this._names.length=0,this._terminalConnections.forEach((e=>e.dispose())),this._pollModels.dispose(),super.dispose())}isAvailable(){return(0,a.isAvailable)()}connectTo(e){const t=new l.TerminalConnection({...e,serverSettings:this.serverSettings});return this._onStarted(t),this._names.includes(e.model.name)||this.refreshRunning().catch((()=>{})),t}running(){return this._models[Symbol.iterator]()}async refreshRunning(){await this._pollModels.refresh(),await this._pollModels.tick}async startNew(e){const t=await(0,a.startNew)(this.serverSettings,null==e?void 0:e.name,null==e?void 0:e.cwd);return await this.refreshRunning(),this.connectTo({model:t})}async shutdown(e){await(0,a.shutdownTerminal)(e,this.serverSettings),await this.refreshRunning()}async shutdownAll(){await this.refreshRunning(),await Promise.all(this._names.map((e=>(0,a.shutdownTerminal)(e,this.serverSettings)))),await this.refreshRunning()}async requestRunning(){var e,t;let s;try{s=await(0,a.listRunning)(this.serverSettings)}catch(s){throw(s instanceof r.ServerConnection.NetworkError||503===(null===(e=s.response)||void 0===e?void 0:e.status)||424===(null===(t=s.response)||void 0===t?void 0:t.status))&&this._connectionFailure.emit(s),s}if(this.isDisposed)return;const n=s.map((({name:e})=>e)).sort();n!==this._names&&(this._names=n,this._terminalConnections.forEach((e=>{n.includes(e.name)||e.dispose()})),this._runningChanged.emit(this._models))}_onStarted(e){this._terminalConnections.add(e),e.disposed.connect(this._onDisposed,this)}_onDisposed(e){this._terminalConnections.delete(e),this.refreshRunning().catch((()=>{}))}get _models(){return this._names.map((e=>({name:e})))}}t.TerminalManager=c,function(e){e.NoopManager=class extends e{constructor(){super(...arguments),this._readyPromise=new Promise((()=>{}))}get isActive(){return!1}get parentReady(){return super.ready}get ready(){return this.parentReady.then((()=>this._readyPromise))}async startNew(e){return Promise.reject(new Error("Not implemented in no-op Terminal Manager"))}connectTo(e){throw Error("Not implemented in no-op Terminal Manager")}async shutdown(e){return Promise.reject(new Error("Not implemented in no-op Terminal Manager"))}async requestRunning(){return Promise.resolve()}}}(c||(t.TerminalManager=c={}))},11887:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.shutdownTerminal=t.listRunning=t.startNew=t.isAvailable=t.TERMINAL_SERVICE_URL=void 0;const n=s(38639),i=s(43851);function r(){return"true"===String(n.PageConfig.getOption("terminalsAvailable")).toLowerCase()}var o;t.TERMINAL_SERVICE_URL="api/terminals",t.isAvailable=r,t.startNew=async function(e=i.ServerConnection.makeSettings(),s,r){o.errorIfNotAvailable();const a=n.URLExt.join(e.baseUrl,t.TERMINAL_SERVICE_URL),l={method:"POST",body:JSON.stringify({name:s,cwd:r})},c=await i.ServerConnection.makeRequest(a,l,e);if(200!==c.status)throw await i.ServerConnection.ResponseError.create(c);return await c.json()},t.listRunning=async function(e=i.ServerConnection.makeSettings()){o.errorIfNotAvailable();const s=n.URLExt.join(e.baseUrl,t.TERMINAL_SERVICE_URL),r=await i.ServerConnection.makeRequest(s,{},e);if(200!==r.status)throw await i.ServerConnection.ResponseError.create(r);const a=await r.json();if(!Array.isArray(a))throw new Error("Invalid terminal list");return a},t.shutdownTerminal=async function(e,s=i.ServerConnection.makeSettings()){var r;o.errorIfNotAvailable();const a=n.URLExt.join(s.baseUrl,t.TERMINAL_SERVICE_URL),l=n.URLExt.join(a,e);if(!l.startsWith(a))throw new Error("Can only be used for terminal requests");const c=await i.ServerConnection.makeRequest(l,{method:"DELETE"},s);if(404===c.status){const t=null!==(r=(await c.json()).message)&&void 0!==r?r:`The terminal session "${e}"" does not exist on the server`;console.warn(t)}else if(204!==c.status)throw await i.ServerConnection.ResponseError.create(c)},function(e){e.errorIfNotAvailable=function(){if(!r())throw new Error("Terminals Unavailable")}}(o||(o={}))},95310:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isAvailable=void 0;const n=s(11887);Object.defineProperty(t,"isAvailable",{enumerable:!0,get:function(){return n.isAvailable}})},78162:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.UserManager=void 0;const n=s(38639),i=s(20998),r=s(97934),o=s(81997),a=s(43851),l=s(78037),c="@jupyterlab/services:UserManager#user";class h extends l.BaseManager{constructor(e={}){var t;super(e),this._isReady=!1,this._userChanged=new o.Signal(this),this._connectionFailure=new o.Signal(this),this._ready=this.requestUser().then((()=>{this.isDisposed||(this._isReady=!0)})).catch((e=>new Promise((()=>{})))),this._pollSpecs=new r.Poll({auto:!1,factory:()=>this.requestUser(),frequency:{interval:61e3,backoff:!0,max:3e5},name:c,standby:null!==(t=e.standby)&&void 0!==t?t:"when-hidden"}),this.ready.then((()=>{this._pollSpecs.start()}))}get isReady(){return this._isReady}get ready(){return this._ready}get identity(){return this._identity}get permissions(){return this._permissions}get userChanged(){return this._userChanged}get connectionFailure(){return this._connectionFailure}dispose(){this._pollSpecs.dispose(),super.dispose()}async refreshUser(){await this._pollSpecs.refresh(),await this._pollSpecs.tick}async requestUser(){if(this.isDisposed)return;const{baseUrl:e}=this.serverSettings,{makeRequest:t,ResponseError:s}=a.ServerConnection,r=n.URLExt.join(e,"api/me"),o=await t(r,{},this.serverSettings);if(200!==o.status)throw await s.create(o);const l={identity:this._identity,permissions:this._permissions},h=await o.json(),d=h.identity,{localStorage:p}=window,_=p.getItem(c);if(_&&(!d.initials||!d.color)){const e=JSON.parse(_);d.initials=d.initials||e.initials||d.name.substring(0,1),d.color=d.color||e.color||u.getRandomColor()}i.JSONExt.deepEqual(h,l)||(this._identity=d,this._permissions=h.permissions,p.setItem(c,JSON.stringify(d)),this._userChanged.emit(h))}}var u;t.UserManager=h,function(e){const t=["var(--jp-collaborator-color1)","var(--jp-collaborator-color2)","var(--jp-collaborator-color3)","var(--jp-collaborator-color4)","var(--jp-collaborator-color5)","var(--jp-collaborator-color6)","var(--jp-collaborator-color7)"];e.getRandomColor=()=>t[Math.floor(Math.random()*t.length)]}(u||(u={}))},45669:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateProperty=void 0,t.validateProperty=function(e,t,s,n=[]){if(!e.hasOwnProperty(t))throw Error(`Missing property '${t}'`);const i=e[t];if(void 0!==s){let e=!0;switch(s){case"array":e=Array.isArray(i);break;case"object":e=void 0!==i;break;default:e=typeof i===s}if(!e)throw new Error(`Property '${t}' is not of type '${s}'`);if(n.length>0){let e=!0;switch(s){case"string":case"number":case"boolean":e=n.includes(i);break;default:e=n.findIndex((e=>e===i))>=0}if(!e)throw new Error(`Property '${t}' is not one of the valid values ${JSON.stringify(n)}`)}}}},53265:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WorkspaceManager=void 0;const n=s(38639),i=s(66853),r=s(43851);class o extends i.DataConnector{constructor(e={}){var t;super(),this.serverSettings=null!==(t=e.serverSettings)&&void 0!==t?t:r.ServerConnection.makeSettings()}async fetch(e){const{serverSettings:t}=this,{baseUrl:s,appUrl:n}=t,{makeRequest:i,ResponseError:o}=r.ServerConnection,l=s+n,c=a.url(l,e),h=await i(c,{},t);if(200!==h.status)throw await o.create(h);return h.json()}async list(){const{serverSettings:e}=this,{baseUrl:t,appUrl:s}=e,{makeRequest:n,ResponseError:i}=r.ServerConnection,o=t+s,l=a.url(o,""),c=await n(l,{},e);if(200!==c.status)throw await i.create(c);return(await c.json()).workspaces}async remove(e){const{serverSettings:t}=this,{baseUrl:s,appUrl:n}=t,{makeRequest:i,ResponseError:o}=r.ServerConnection,l=s+n,c=a.url(l,e),h=await i(c,{method:"DELETE"},t);if(204!==h.status)throw await o.create(h)}async save(e,t){const{serverSettings:s}=this,{baseUrl:n,appUrl:i}=s,{makeRequest:o,ResponseError:l}=r.ServerConnection,c=n+i,h=a.url(c,e),u={body:JSON.stringify(t),method:"PUT"},d=await o(h,u,s);if(204!==d.status)throw await l.create(d)}}var a;t.WorkspaceManager=o,function(e){e.url=function(e,t){const s=n.URLExt.join(e,"api/workspaces"),i=n.URLExt.join(s,t);if(!i.startsWith(s))throw new Error("Can only be used for workspaces requests");return i}}(a||(a={}))},27061:e=>{var t,s,n=e.exports={};function i(){throw new Error("setTimeout has not been defined")}function r(){throw new Error("clearTimeout has not been defined")}function o(e){if(t===setTimeout)return setTimeout(e,0);if((t===i||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(s){try{return t.call(null,e,0)}catch(s){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:i}catch(e){t=i}try{s="function"==typeof clearTimeout?clearTimeout:r}catch(e){s=r}}();var a,l=[],c=!1,h=-1;function u(){c&&a&&(c=!1,a.length?l=a.concat(l):h=-1,l.length&&d())}function d(){if(!c){var e=o(u);c=!0;for(var t=l.length;t;){for(a=l,l=[];++h<t;)a&&a[h].run();h=-1,t=l.length}a=null,c=!1,function(e){if(s===clearTimeout)return clearTimeout(e);if((s===r||!s)&&clearTimeout)return s=clearTimeout,clearTimeout(e);try{return s(e)}catch(t){try{return s.call(null,e)}catch(t){return s.call(this,e)}}}(e)}}function p(e,t){this.fun=e,this.array=t}function _(){}n.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var s=1;s<arguments.length;s++)t[s-1]=arguments[s];l.push(new p(e,t)),1!==l.length||c||o(d)},p.prototype.run=function(){this.fun.apply(null,this.array)},n.title="browser",n.browser=!0,n.env={},n.argv=[],n.version="",n.versions={},n.on=_,n.addListener=_,n.once=_,n.off=_,n.removeListener=_,n.removeAllListeners=_,n.emit=_,n.prependListener=_,n.prependOnceListener=_,n.listeners=function(e){return[]},n.binding=function(e){throw new Error("process.binding is not supported")},n.cwd=function(){return"/"},n.chdir=function(e){throw new Error("process.chdir is not supported")},n.umask=function(){return 0}}}]);
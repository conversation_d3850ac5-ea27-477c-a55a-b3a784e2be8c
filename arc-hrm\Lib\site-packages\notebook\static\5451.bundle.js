"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[5451],{65451:(s,e,i)=>{i.r(e),i.d(e,{DisposableDelegate:()=>o,DisposableSet:()=>r,ObservableDisposableDelegate:()=>d,ObservableDisposableSet:()=>n});var t=i(81997);class o{constructor(s){this._fn=s}get isDisposed(){return!this._fn}dispose(){if(!this._fn)return;let s=this._fn;this._fn=null,s()}}class d extends o{constructor(){super(...arguments),this._disposed=new t.Signal(this)}get disposed(){return this._disposed}dispose(){this.isDisposed||(super.dispose(),this._disposed.emit(void 0),t.Signal.clearData(this))}}class r{constructor(){this._isDisposed=!1,this._items=new Set}get isDisposed(){return this._isDisposed}dispose(){this._isDisposed||(this._isDisposed=!0,this._items.forEach((s=>{s.dispose()})),this._items.clear())}contains(s){return this._items.has(s)}add(s){this._items.add(s)}remove(s){this._items.delete(s)}clear(){this._items.clear()}}!function(s){s.from=function(e){let i=new s;for(const s of e)i.add(s);return i}}(r||(r={}));class n extends r{constructor(){super(...arguments),this._disposed=new t.Signal(this)}get disposed(){return this._disposed}dispose(){this.isDisposed||(super.dispose(),this._disposed.emit(void 0),t.Signal.clearData(this))}}!function(s){s.from=function(e){let i=new s;for(const s of e)i.add(s);return i}}(n||(n={}))}}]);
(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[4928],{44928:e=>{self,e.exports=(()=>{"use strict";var e={6:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.LinkComputer=t.WebLinkProvider=void 0,t.WebLinkProvider=class{constructor(e,t,n,i={}){this._terminal=e,this._regex=t,this._handler=n,this._options=i}provideLinks(e,t){const i=n.computeLink(e,this._regex,this._terminal,this._handler);t(this._addCallbacks(i))}_addCallbacks(e){return e.map((e=>(e.leave=this._options.leave,e.hover=(t,n)=>{if(this._options.hover){const{range:i}=e;this._options.hover(t,n,i)}},e)))}};class n{static computeLink(e,t,i,r){const s=new RegExp(t.source,(t.flags||"")+"g"),[o,a]=n._getWindowedLineStrings(e-1,i),l=o.join("");let c;const d=[];for(;c=s.exec(l);){const t=c[0];try{const e=new URL(t),n=decodeURI(e.toString());if(t!==n&&t+"/"!==n)continue}catch(e){continue}const[s,o]=n._mapStrIdx(i,a,0,c.index),[l,h]=n._mapStrIdx(i,s,o,t.length);if(-1===s||-1===o||-1===l||-1===h)continue;const p={start:{x:o+1,y:s+1},end:{x:h,y:l+1}};d.push({range:p,text:t,activate:r})}return d}static _getWindowedLineStrings(e,t){let n,i=e,r=e,s=0,o="";const a=[];if(n=t.buffer.active.getLine(e)){const e=n.translateToString(!0);if(n.isWrapped&&" "!==e[0]){for(s=0;(n=t.buffer.active.getLine(--i))&&s<2048&&(o=n.translateToString(!0),s+=o.length,a.push(o),n.isWrapped&&-1===o.indexOf(" ")););a.reverse()}for(a.push(e),s=0;(n=t.buffer.active.getLine(++r))&&n.isWrapped&&s<2048&&(o=n.translateToString(!0),s+=o.length,a.push(o),-1===o.indexOf(" ")););}return[a,i]}static _mapStrIdx(e,t,n,i){const r=e.buffer.active,s=r.getNullCell();let o=n;for(;i;){const e=r.getLine(t);if(!e)return[-1,-1];for(let n=o;n<e.length;++n){e.getCell(n,s);const o=s.getChars();if(s.getWidth()&&(i-=o.length||1,n===e.length-1&&""===o)){const e=r.getLine(t+1);e&&e.isWrapped&&(e.getCell(0,s),2===s.getWidth()&&(i+=1))}if(i<0)return[t,n]}t++,o=0}return[t,o]}}t.LinkComputer=n}},t={};function n(i){var r=t[i];if(void 0!==r)return r.exports;var s=t[i]={exports:{}};return e[i](s,s.exports,n),s.exports}var i={};return(()=>{var e=i;Object.defineProperty(e,"__esModule",{value:!0}),e.WebLinksAddon=void 0;const t=n(6),r=/https?:[/]{2}[^\s"'!*(){}|\\\^<>`]*[^\s"':,.!?{}|\\\^~\[\]`()<>]/;function s(e,t){const n=window.open();if(n){try{n.opener=null}catch(e){}n.location.href=t}else console.warn("Opening link blocked as opener could not be cleared")}e.WebLinksAddon=class{constructor(e=s,t={}){this._handler=e,this._options=t}activate(e){this._terminal=e;const n=this._options,i=n.urlRegex||r;this._linkProvider=this._terminal.registerLinkProvider(new t.WebLinkProvider(this._terminal,i,this._handler,n))}dispose(){var e;null===(e=this._linkProvider)||void 0===e||e.dispose()}}})(),i})()}}]);
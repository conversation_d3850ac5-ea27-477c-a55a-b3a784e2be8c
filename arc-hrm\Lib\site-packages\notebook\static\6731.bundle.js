"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[6731],{16731:(t,e,n)=>{n.r(e),n.d(e,{Application:()=>h});var i,r,s,o,l=n(32895),a=n(20998),u=n(31516);!function(t){function e(t,e,n=0,i=-1){let r,s=t.length;if(0===s)return-1;n=n<0?Math.max(0,n+s):Math.min(n,s-1),r=(i=i<0?Math.max(0,i+s):Math.min(i,s-1))<n?i+1+(s-n):i-n+1;for(let i=0;i<r;++i){let r=(n+i)%s;if(t[r]===e)return r}return-1}function n(t,e,n=-1,i=0){let r,s=t.length;if(0===s)return-1;r=(n=n<0?Math.max(0,n+s):Math.min(n,s-1))<(i=i<0?Math.max(0,i+s):Math.min(i,s-1))?n+1+(s-i):n-i+1;for(let i=0;i<r;++i){let r=(n-i+s)%s;if(t[r]===e)return r}return-1}function i(t,e,n=0,i=-1){let r,s=t.length;if(0===s)return-1;n=n<0?Math.max(0,n+s):Math.min(n,s-1),r=(i=i<0?Math.max(0,i+s):Math.min(i,s-1))<n?i+1+(s-n):i-n+1;for(let i=0;i<r;++i){let r=(n+i)%s;if(e(t[r],r))return r}return-1}function r(t,e,n=-1,i=0){let r,s=t.length;if(0===s)return-1;r=(n=n<0?Math.max(0,n+s):Math.min(n,s-1))<(i=i<0?Math.max(0,i+s):Math.min(i,s-1))?n+1+(s-i):n-i+1;for(let i=0;i<r;++i){let r=(n-i+s)%s;if(e(t[r],r))return r}return-1}function s(t,e=0,n=-1){let i=t.length;if(!(i<=1))for(e=e<0?Math.max(0,e+i):Math.min(e,i-1),n=n<0?Math.max(0,n+i):Math.min(n,i-1);e<n;){let i=t[e],r=t[n];t[e++]=r,t[n--]=i}}function o(t,e){let n=t.length;if(e<0&&(e+=n),e<0||e>=n)return;let i=t[e];for(let i=e+1;i<n;++i)t[i-1]=t[i];return t.length=n-1,i}t.firstIndexOf=e,t.lastIndexOf=n,t.findFirstIndex=i,t.findLastIndex=r,t.findFirstValue=function(t,e,n=0,r=-1){let s=i(t,e,n,r);return-1!==s?t[s]:void 0},t.findLastValue=function(t,e,n=-1,i=0){let s=r(t,e,n,i);return-1!==s?t[s]:void 0},t.lowerBound=function(t,e,n,i=0,r=-1){let s=t.length;if(0===s)return 0;let o=i=i<0?Math.max(0,i+s):Math.min(i,s-1),l=(r=r<0?Math.max(0,r+s):Math.min(r,s-1))-i+1;for(;l>0;){let i=l>>1,r=o+i;n(t[r],e)<0?(o=r+1,l-=i+1):l=i}return o},t.upperBound=function(t,e,n,i=0,r=-1){let s=t.length;if(0===s)return 0;let o=i=i<0?Math.max(0,i+s):Math.min(i,s-1),l=(r=r<0?Math.max(0,r+s):Math.min(r,s-1))-i+1;for(;l>0;){let i=l>>1,r=o+i;n(t[r],e)>0?l=i:(o=r+1,l-=i+1)}return o},t.shallowEqual=function(t,e,n){if(t===e)return!0;if(t.length!==e.length)return!1;for(let i=0,r=t.length;i<r;++i)if(n?!n(t[i],e[i]):t[i]!==e[i])return!1;return!0},t.slice=function(t,e={}){let{start:n,stop:i,step:r}=e;if(void 0===r&&(r=1),0===r)throw new Error("Slice `step` cannot be zero.");let s,o=t.length;void 0===n?n=r<0?o-1:0:n<0?n=Math.max(n+o,r<0?-1:0):n>=o&&(n=r<0?o-1:o),void 0===i?i=r<0?-1:o:i<0?i=Math.max(i+o,r<0?-1:0):i>=o&&(i=r<0?o-1:o),s=r<0&&i>=n||r>0&&n>=i?0:r<0?Math.floor((i-n+1)/r+1):Math.floor((i-n-1)/r+1);let l=[];for(let e=0;e<s;++e)l[e]=t[n+e*r];return l},t.move=function(t,e,n){let i=t.length;if(i<=1)return;if((e=e<0?Math.max(0,e+i):Math.min(e,i-1))===(n=n<0?Math.max(0,n+i):Math.min(n,i-1)))return;let r=t[e],s=e<n?1:-1;for(let i=e;i!==n;i+=s)t[i]=t[i+s];t[n]=r},t.reverse=s,t.rotate=function(t,e,n=0,i=-1){let r=t.length;if(r<=1)return;if((n=n<0?Math.max(0,n+r):Math.min(n,r-1))>=(i=i<0?Math.max(0,i+r):Math.min(i,r-1)))return;let o=i-n+1;if(e>0?e%=o:e<0&&(e=(e%o+o)%o),0===e)return;let l=n+e;s(t,n,l-1),s(t,l,i),s(t,n,i)},t.fill=function(t,e,n=0,i=-1){let r,s=t.length;if(0!==s){n=n<0?Math.max(0,n+s):Math.min(n,s-1),r=(i=i<0?Math.max(0,i+s):Math.min(i,s-1))<n?i+1+(s-n):i-n+1;for(let i=0;i<r;++i)t[(n+i)%s]=e}},t.insert=function(t,e,n){let i=t.length;e=e<0?Math.max(0,e+i):Math.min(e,i);for(let n=i;n>e;--n)t[n]=t[n-1];t[e]=n},t.removeAt=o,t.removeFirstOf=function(t,n,i=0,r=-1){let s=e(t,n,i,r);return-1!==s&&o(t,s),s},t.removeLastOf=function(t,e,i=-1,r=0){let s=n(t,e,i,r);return-1!==s&&o(t,s),s},t.removeAllOf=function(t,e,n=0,i=-1){let r=t.length;if(0===r)return 0;n=n<0?Math.max(0,n+r):Math.min(n,r-1),i=i<0?Math.max(0,i+r):Math.min(i,r-1);let s=0;for(let o=0;o<r;++o)n<=i&&o>=n&&o<=i&&t[o]===e||i<n&&(o<=i||o>=n)&&t[o]===e?s++:s>0&&(t[o-s]=t[o]);return s>0&&(t.length=r-s),s},t.removeFirstWhere=function(t,e,n=0,r=-1){let s,l=i(t,e,n,r);return-1!==l&&(s=o(t,l)),{index:l,value:s}},t.removeLastWhere=function(t,e,n=-1,i=0){let s,l=r(t,e,n,i);return-1!==l&&(s=o(t,l)),{index:l,value:s}},t.removeAllWhere=function(t,e,n=0,i=-1){let r=t.length;if(0===r)return 0;n=n<0?Math.max(0,n+r):Math.min(n,r-1),i=i<0?Math.max(0,i+r):Math.min(i,r-1);let s=0;for(let o=0;o<r;++o)n<=i&&o>=n&&o<=i&&e(t[o],o)||i<n&&(o<=i||o>=n)&&e(t[o],o)?s++:s>0&&(t[o-s]=t[o]);return s>0&&(t.length=r-s),s}}(i||(i={})),function(t){t.rangeLength=function(t,e,n){return 0===n?1/0:t>e&&n>0||t<e&&n<0?0:Math.ceil((e-t)/n)}}(r||(r={})),function(t){function e(t,e,n=0){let i=new Array(e.length);for(let r=0,s=n,o=e.length;r<o;++r,++s){if(s=t.indexOf(e[r],s),-1===s)return null;i[r]=s}return i}t.findIndices=e,t.matchSumOfSquares=function(t,n,i=0){let r=e(t,n,i);if(!r)return null;let s=0;for(let t=0,e=r.length;t<e;++t){let e=r[t]-i;s+=e*e}return{score:s,indices:r}},t.matchSumOfDeltas=function(t,n,i=0){let r=e(t,n,i);if(!r)return null;let s=0,o=i-1;for(let t=0,e=r.length;t<e;++t){let e=r[t];s+=e-o-1,o=e}return{score:s,indices:r}},t.highlight=function(t,e,n){let i=[],r=0,s=0,o=e.length;for(;r<o;){let l=e[r],a=e[r];for(;++r<o&&e[r]===a+1;)a++;s<l&&i.push(t.slice(s,l)),l<a+1&&i.push(n(t.slice(l,a+1))),s=a+1}return s<t.length&&i.push(t.slice(s)),i},t.cmp=function(t,e){return t<e?-1:t>e?1:0}}(s||(s={}));class h{constructor(t){this._delegate=new a.PromiseDelegate,this._plugins=new Map,this._services=new Map,this._started=!1,this._bubblingKeydown=!1,this.commands=new l.CommandRegistry,this.contextMenu=new u.ContextMenu({commands:this.commands,renderer:t.contextMenuRenderer}),this.shell=t.shell}get started(){return this._delegate.promise}getPluginDescription(t){var e,n;return null!==(n=null===(e=this._plugins.get(t))||void 0===e?void 0:e.description)&&void 0!==n?n:""}hasPlugin(t){return this._plugins.has(t)}isPluginActivated(t){var e,n;return null!==(n=null===(e=this._plugins.get(t))||void 0===e?void 0:e.activated)&&void 0!==n&&n}listPlugins(){return Array.from(this._plugins.keys())}registerPlugin(t){if(this._plugins.has(t.id))throw new TypeError(`Plugin '${t.id}' is already registered.`);const e=o.createPluginData(t);o.ensureNoCycle(e,this._plugins,this._services),e.provides&&this._services.set(e.provides,e.id),this._plugins.set(e.id,e)}registerPlugins(t){for(const e of t)this.registerPlugin(e)}deregisterPlugin(t,e){const n=this._plugins.get(t);if(n){if(n.activated&&!e)throw new Error(`Plugin '${t}' is still active.`);this._plugins.delete(t)}}async activatePlugin(t){const e=this._plugins.get(t);if(!e)throw new ReferenceError(`Plugin '${t}' is not registered.`);if(e.activated)return;if(e.promise)return e.promise;const n=e.requires.map((t=>this.resolveRequiredService(t))),i=e.optional.map((t=>this.resolveOptionalService(t)));return e.promise=Promise.all([...n,...i]).then((t=>e.activate.apply(void 0,[this,...t]))).then((t=>{e.service=t,e.activated=!0,e.promise=null})).catch((t=>{throw e.promise=null,t})),e.promise}async deactivatePlugin(t){const e=this._plugins.get(t);if(!e)throw new ReferenceError(`Plugin '${t}' is not registered.`);if(!e.activated)return[];if(!e.deactivate)throw new TypeError(`Plugin '${t}'#deactivate() method missing`);const n=o.findDependents(t,this._plugins,this._services),i=n.map((t=>this._plugins.get(t)));for(const e of i)if(!e.deactivate)throw new TypeError(`Plugin ${e.id}#deactivate() method missing (depends on ${t})`);for(const t of i){const e=[...t.requires,...t.optional].map((t=>{const e=this._services.get(t);return e?this._plugins.get(e).service:null}));await t.deactivate(this,...e),t.service=null,t.activated=!1}return n.pop(),n}async resolveRequiredService(t){const e=this._services.get(t);if(!e)throw new TypeError(`No provider for: ${t.name}.`);const n=this._plugins.get(e);return n.activated||await this.activatePlugin(e),n.service}async resolveOptionalService(t){const e=this._services.get(t);if(!e)return null;const n=this._plugins.get(e);if(!n.activated)try{await this.activatePlugin(e)}catch(t){return console.error(t),null}return n.service}start(t={}){if(this._started)return this._delegate.promise;this._started=!0,this._bubblingKeydown=t.bubblingKeydown||!1;const e=t.hostID||"",n=o.collectStartupPlugins(this._plugins,t).map((t=>this.activatePlugin(t).catch((e=>{console.error(`Plugin '${t}' failed to activate.`),console.error(e)}))));return Promise.all(n).then((()=>{this.attachShell(e),this.addEventListeners(),this._delegate.resolve()})),this._delegate.promise}get deferredPlugins(){return Array.from(this._plugins).filter((([t,e])=>"defer"===e.autoStart)).map((([t,e])=>t))}async activateDeferredPlugins(){const t=this.deferredPlugins.filter((t=>this._plugins.get(t).autoStart)).map((t=>this.activatePlugin(t)));await Promise.all(t)}handleEvent(t){switch(t.type){case"resize":this.evtResize(t);break;case"keydown":this.evtKeydown(t);break;case"contextmenu":this.evtContextMenu(t)}}attachShell(t){u.Widget.attach(this.shell,t&&document.getElementById(t)||document.body)}addEventListeners(){document.addEventListener("contextmenu",this),document.addEventListener("keydown",this,!this._bubblingKeydown),window.addEventListener("resize",this)}evtKeydown(t){this.commands.processKeydownEvent(t)}evtContextMenu(t){t.shiftKey||this.contextMenu.open(t)&&(t.preventDefault(),t.stopPropagation())}evtResize(t){this.shell.update()}}!function(t){t.createPluginData=function(t){var e,n,i,r;return{id:t.id,description:null!==(e=t.description)&&void 0!==e?e:"",service:null,promise:null,activated:!1,activate:t.activate,deactivate:null!==(n=t.deactivate)&&void 0!==n?n:null,provides:null!==(i=t.provides)&&void 0!==i?i:null,autoStart:null!==(r=t.autoStart)&&void 0!==r&&r,requires:t.requires?t.requires.slice():[],optional:t.optional?t.optional.slice():[]}},t.ensureNoCycle=function(t,e,n){const i=[...t.requires,...t.optional],r=i=>{if(i===t.provides)return!0;const o=n.get(i);if(!o)return!1;const l=e.get(o),a=[...l.requires,...l.optional];return 0!==a.length&&(s.push(o),!!a.some(r)||(s.pop(),!1))};if(!t.provides||0===i.length)return;const s=[t.id];if(i.some(r))throw new ReferenceError(`Cycle detected: ${s.join(" -> ")}.`)},t.findDependents=function(t,e,n){const i=new Array,r=t=>{const r=e.get(t),s=[...r.requires,...r.optional];i.push(...s.reduce(((e,i)=>{const r=n.get(i);return r&&e.push([t,r]),e}),[]))};for(const t of e.keys())r(t);const s=i.filter((e=>e[1]===t));let o=0;for(;s.length>o;){const t=s.length,e=new Set(s.map((t=>t[0])));for(const t of e)i.filter((e=>e[1]===t)).forEach((t=>{s.includes(t)||s.push(t)}));o=t}const l=function(t){let e=[],n=new Set,i=new Map;for(const e of t)r(e);for(const[t]of i)s(t);return e;function r(t){let[e,n]=t,r=i.get(n);r?r.push(e):i.set(n,[e])}function s(t){if(n.has(t))return;n.add(t);let r=i.get(t);if(r)for(const t of r)s(t);e.push(t)}}(s),a=l.findIndex((e=>e===t));return-1===a?[t]:l.slice(0,a+1)},t.collectStartupPlugins=function(t,e){const n=new Set;for(const e of t.keys())!0===t.get(e).autoStart&&n.add(e);if(e.startPlugins)for(const t of e.startPlugins)n.add(t);if(e.ignorePlugins)for(const t of e.ignorePlugins)n.delete(t);return Array.from(n)}}(o||(o={}))}}]);
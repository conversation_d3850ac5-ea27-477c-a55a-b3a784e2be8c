"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[2944],{62944:(e,t,i)=>{i.r(t),i.d(t,{CONTEXT_PROVIDER_ID:()=>E,Completer:()=>b,CompleterModel:()=>_,CompletionHandler:()=>u,CompletionProviderManager:()=>G,CompletionTriggerKind:()=>s,ContextCompleterProvider:()=>k,HistoryInlineCompletionProvider:()=>$,ICompletionProviderManager:()=>c,IInlineCompleterFactory:()=>d,InlineCompleter:()=>V,InlineCompletionTriggerKind:()=>n,KERNEL_PROVIDER_ID:()=>L,KernelCompleterProvider:()=>H,ProviderReconciliator:()=>M,completerWidgetIcon:()=>K,inlineCompleterIcon:()=>U});var s,n,o=i(31536),r=i(38639),l=i(49503),a=i(81997),h=i(20998);!function(e){e[e.Invoked=1]="Invoked",e[e.TriggerCharacter=2]="TriggerCharacter",e[e.TriggerForIncompleteCompletions=3]="TriggerForIncompleteCompletions"}(s||(s={})),function(e){e[e.Invoke=0]="Invoke",e[e.Automatic=1]="Automatic"}(n||(n={}));const d=new h.Token("@jupyterlab/completer:IInlineCompleterFactory","A factory of inline completer widgets."),c=new h.Token("@jupyterlab/completer:ICompletionProviderManager","A service for the completion providers management.");class u{constructor(e){this._fetchingInline=0,this._editor=null,this._enabled=!1,this._isDisposed=!1,this._autoCompletion=!1,this._continuousInline=!0,this.completer=e.completer,this.inlineCompleter=e.inlineCompleter,this.completer.selected.connect(this.onCompletionSelected,this),this.completer.visibilityChanged.connect(this.onVisibilityChanged,this),this._reconciliator=e.reconciliator}set reconciliator(e){this._reconciliator=e}get editor(){return this._editor}set editor(e){if(e===this._editor)return;let t=this._editor;if(t&&!t.isDisposed){const e=t.model;t.host.classList.remove(o.COMPLETER_ENABLED_CLASS),t.host.classList.remove(o.COMPLETER_ACTIVE_CLASS),e.selections.changed.disconnect(this.onSelectionsChanged,this),e.sharedModel.changed.disconnect(this._onSharedModelChanged,this)}if(this.completer.reset(),this.completer.editor=e,t=this._editor=e,t){const e=t.model;this._enabled=!1,e.selections.changed.connect(this.onSelectionsChanged,this),e.sharedModel.changed.connect(this._onSharedModelChanged,this),this.onSelectionsChanged(),this.inlineCompleter&&(this.inlineCompleter.editor=t)}}get isDisposed(){return this._isDisposed}set autoCompletion(e){this._autoCompletion=e}get autoCompletion(){return this._autoCompletion}dispose(){this.isDisposed||(this._isDisposed=!0,a.Signal.clearData(this))}invokeInline(){const e=this._editor;e&&this._makeInlineRequest(e.getCursorPosition(),n.Invoke).catch((e=>{console.warn("Inline invoke request bailed",e)}))}invoke(){l.MessageLoop.sendMessage(this,u.Msg.InvokeRequest)}processMessage(e){e.type===u.Msg.InvokeRequest.type&&this.onInvokeRequest(e)}getState(e,t){return{text:e.model.sharedModel.getSource(),line:t.line,column:t.column}}onCompletionSelected(e,t){const i=e.model,s=this._editor;if(!s||!i)return;const n=i.createPatch(t);if(!n)return;const{start:o,end:r,value:l}=n,a=s.getOffsetAt(s.getCursorPosition());s.model.sharedModel.updateSource(o,r,l),a<=r&&a>=o&&s.setCursorPosition(s.getPositionAt(o+l.length))}onInvokeRequest(e){if(!this.completer.model)return;if(this.completer.model.original)return;const t=this._editor;t&&this._makeRequest(t.getCursorPosition(),s.Invoked).catch((e=>{console.warn("Invoke request bailed",e)}))}onSelectionsChanged(){var e;const t=this.completer.model,i=this._editor;if(!i)return;const s=null===(e=this.inlineCompleter)||void 0===e?void 0:e.model;s&&s.handleSelectionChange(i.getSelection());const n=i.host;if(!t)return this._enabled=!1,void n.classList.remove(o.COMPLETER_ENABLED_CLASS);if(t.subsetMatch)return;const r=i.getCursorPosition(),l=i.getLine(r.line);if(!l)return this._enabled=!1,t.reset(!0),void n.classList.remove(o.COMPLETER_ENABLED_CLASS);const{start:a,end:h}=i.getSelection();return a.column!==h.column||a.line!==h.line||l.slice(0,r.column).match(/^\s*$/)?(this._enabled=!1,t.reset(!0),void n.classList.remove(o.COMPLETER_ENABLED_CLASS)):(this._enabled||(this._enabled=!0,n.classList.add(o.COMPLETER_ENABLED_CLASS)),void t.handleCursorChange(this.getState(i,i.getCursorPosition())))}async onTextChanged(e,t){var i;if(!this._enabled)return;const o=this.completer.model,r=this.editor;if(!r)return;o&&this._autoCompletion&&this._reconciliator.shouldShowContinuousHint&&await this._reconciliator.shouldShowContinuousHint(this.completer.isVisible,t)&&this._makeRequest(r.getCursorPosition(),s.TriggerCharacter);const l=null===(i=this.inlineCompleter)||void 0===i?void 0:i.model;if(l&&(l.handleTextChange(t),this._continuousInline&&this._makeInlineRequest(r.getCursorPosition(),n.Automatic)),o){const{start:e,end:t}=r.getSelection();if(e.column!==t.column||e.line!==t.line)return;o.handleTextChange(this.getState(r,r.getCursorPosition()))}}onVisibilityChanged(e){e.isDisposed||e.isHidden?this._editor&&(this._editor.host.classList.remove(o.COMPLETER_ACTIVE_CLASS),this._editor.focus()):this._editor&&this._editor.host.classList.add(o.COMPLETER_ACTIVE_CLASS)}async _onSharedModelChanged(e,t){t.sourceChange&&await this.onTextChanged(e,t)}_makeRequest(e,t){const i=this.editor;if(!i)return Promise.reject(new Error("No active editor"));const s=this._composeRequest(i,e),n=this.getState(i,e);return this._reconciliator.fetch(s,t).then((e=>{if(!e)return;const t=this._updateModel(n,e.start,e.end);t&&t.setCompletionItems&&t.setCompletionItems(e.items)})).catch((e=>{}))}async _makeInlineRequest(e,t){const i=this.editor;if(!i)return Promise.reject(new Error("No active editor"));if(!this.inlineCompleter)return Promise.reject(new Error("No inline completer"));const s=i.getLine(e.line);if(void 0===s||e.column<s.length)return;const n=this._composeRequest(i,e),o=this.inlineCompleter.model;if(!o)return;o.cursor=e;const r=++this._fetchingInline,l=this._reconciliator.fetchInline(n,t),a=new Set;for(const e of l)e.then((t=>{t&&t.items&&r===this._fetchingInline&&(a.add(e),1===a.size?o.setCompletions(t):o.appendCompletions(t))})).catch((e=>{console.warn(e)})).finally((()=>{a.add(e);const t=l.length-a.size;o.notifyProgress({pendingProviders:t,totalProviders:l.length})}))}_composeRequest(e,t){const i=e.model.sharedModel.getSource(),s=e.model.mimeType;return{text:i,offset:r.Text.jsIndexToCharIndex(e.getOffsetAt(t),i),mimeType:s}}_updateModel(e,t,i){const s=this.completer.model,n=e.text;return s?(s.original=e,s.cursor={start:r.Text.charIndexToJsIndex(t,n),end:r.Text.charIndexToJsIndex(i,n)},s):null}}!function(e){let t,i;!function(e){e[e.opened=0]="opened",e[e.update=1]="update",e[e.closed=2]="closed"}(t=e.StraemEvent||(e.StraemEvent={})),function(e){e.InvokeRequest=new l.Message("invoke-request")}(i=e.Msg||(e.Msg={}))}(u||(u={}));var m,p=i(33625);function g(e){const t=document.createElement("span");return t.textContent=e,t.innerHTML}class _{constructor(){this.processedItemsCache=null,this._current=null,this._cursor=null,this._isDisposed=!1,this._completionItems=[],this._original=null,this._query="",this._subsetMatch=!1,this._typeMap={},this._orderedTypes=[],this._stateChanged=new a.Signal(this),this._queryChanged=new a.Signal(this),this._processedToOriginalItem=null,this._resolvingItem=0}get stateChanged(){return this._stateChanged}get queryChanged(){return this._queryChanged}get original(){return this._original}set original(e){this._original===e||this._original&&e&&h.JSONExt.deepEqual(e,this._original)||(this._reset(),this._current=this._original=e,this._stateChanged.emit(void 0))}get current(){return this._current}set current(e){if(this._current===e||this._current&&e&&h.JSONExt.deepEqual(e,this._current))return;const t=this._original;if(!t)return;const i=this._cursor;if(!i)return;const s=this._current=e;if(!s)return void this._stateChanged.emit(void 0);const n=t.text.split("\n")[t.line],o=s.text.split("\n")[s.line];if(!this._subsetMatch&&o.length<n.length)return void this.reset(!0);const{start:r,end:l}=i;let a=s.text.substring(r);const d=t.text.substring(l);a=a.substring(0,a.lastIndexOf(d)),this._query=a,this.processedItemsCache=null,this._processedToOriginalItem=null,this._queryChanged.emit({newValue:this._query,origin:"editorUpdate"}),this._stateChanged.emit(void 0)}get cursor(){return this._cursor}set cursor(e){this.original&&(this._cursor=e)}get query(){return this._query}set query(e){this._query=e,this.processedItemsCache=null,this._processedToOriginalItem=null,this._queryChanged.emit({newValue:this._query,origin:"setter"})}get subsetMatch(){return this._subsetMatch}set subsetMatch(e){this._subsetMatch=e}get isDisposed(){return this._isDisposed}dispose(){this._isDisposed||(this._isDisposed=!0,a.Signal.clearData(this))}completionItems(){if(!this.processedItemsCache){let e=this._query;if(e){const t=this._markup(e);this.processedItemsCache=t.map((e=>e.processedItem)),this._processedToOriginalItem=new WeakMap(t.map((e=>[e.processedItem,e.originalItem])))}else this.processedItemsCache=this._completionItems.map((e=>this._escapeItemLabel(e))),this._processedToOriginalItem=null}return this.processedItemsCache}setCompletionItems(e){h.JSONExt.deepEqual(e,this._completionItems)||(this._completionItems=e,this._orderedTypes=m.findOrderedCompletionItemTypes(this._completionItems),this.processedItemsCache=null,this._processedToOriginalItem=null,this._stateChanged.emit(void 0))}typeMap(){return this._typeMap}orderedTypes(){return this._orderedTypes}handleCursorChange(e){if(!this._original)return;const{column:t,line:i}=e,{current:s,original:n}=this;if(!n)return;if(i!==n.line)return void this.reset(!0);if(t<n.column)return void this.reset(!0);const{cursor:o}=this;if(!o||!s)return;const r=o.end-o.start,l=n.text.split("\n")[n.line],a=s.text.split("\n")[s.line].length-l.length;t>n.column+r+a&&this.reset(!0)}handleTextChange(e){const t=this._original;if(!t)return;const{text:i,column:s,line:n}=e,o=i.split("\n")[n][s-1];o&&o.match(/\S/)||e.column>=t.column?this.current=e:this.reset(!1)}createPatch(e){const t=this._original,i=this._cursor,s=this._current;if(!t||!i||!s)return;let{start:n,end:o}=i;return o+=s.text.length-t.text.length,{start:n,end:o,value:e}}reset(e=!1){!e&&this._subsetMatch||(this._reset(),this._stateChanged.emit(void 0))}_markup(e){var t;const i=this._completionItems;let s=[];for(const n of i){const i=n.label.indexOf("("),o=i>-1?n.label.substring(0,i):n.label,r=p.StringExt.matchSumOfSquares(g(o),e);if(r){let e=p.StringExt.highlight(g(n.label),r.indices,m.mark);const i=Object.assign({},n);i.label=e.join(""),i.insertText=null!==(t=n.insertText)&&void 0!==t?t:n.label,s.push({item:i,score:r.score,originalItem:n})}}return s.sort(m.scoreCmp),s.map((e=>({processedItem:e.item,originalItem:e.originalItem})))}resolveItem(e){let t,i;if("number"==typeof e){const i=this.completionItems();if(!i||!i[e])return;t=i[e]}else t=e;if(t)return i=this._processedToOriginalItem?this._processedToOriginalItem.get(t):t,i?this._resolveItemByValue(i):void 0}_resolveItemByValue(e){const t=++this._resolvingItem;let i;if(e.resolve){let t;e.insertText&&(t=this.createPatch(e.insertText)),i=e.resolve(t)}else i=Promise.resolve(e);return i.then((i=>(this._escapeItemLabel(i,!0),Object.keys(i).forEach((t=>{e[t]=i[t]})),e.resolve=void 0,t!==this._resolvingItem?Promise.resolve(null):i))).catch((t=>(console.error(t),Promise.resolve(e))))}_escapeItemLabel(e,t=!1){var i;const s=g(e.label);if(s!==e.label){const n=t?e:Object.assign({},e);return n.insertText=null!==(i=e.insertText)&&void 0!==i?i:e.label,n.label=s,n}return e}_reset(){const e=this._query;this._current=null,this._cursor=null,this._completionItems=[],this._original=null,this._query="",this.processedItemsCache=null,this._processedToOriginalItem=null,this._subsetMatch=!1,this._typeMap={},this._orderedTypes=[],e&&this._queryChanged.emit({newValue:this._query,origin:"reset"})}}!function(e){const t=["function","instance","class","module","keyword"],i=t.reduce(((e,t)=>(e[t]=null,e)),{});e.mark=function(e){return`<mark>${e}</mark>`},e.scoreCmp=function(e,t){var i,s,n;const o=e.score-t.score;return 0!==o?o:null!==(n=null===(i=e.item.insertText)||void 0===i?void 0:i.localeCompare(null!==(s=t.item.insertText)&&void 0!==s?s:""))&&void 0!==n?n:0},e.findOrderedCompletionItemTypes=function(e){const i=new Set;e.forEach((e=>{!e.type||t.includes(e.type)||i.has(e.type)||i.add(e.type)}));const s=Array.from(i);return s.sort(((e,t)=>e.localeCompare(t))),t.concat(s)},e.findOrderedTypes=function(e){const s=Object.keys(e).map((t=>e[t])).filter((e=>!!e&&!(e in i))).sort(((e,t)=>e.localeCompare(t)));return t.concat(s)}}(m||(m={}));var v=i(12982),f=i(70856),C=i(68239),y=i(18395),w=i(31516);const x="jp-Completer-item",P="jp-mod-active",I="jp-Completer-list",S=!0;class b extends w.Widget{constructor(e){var t,i,s,n;super({node:document.createElement("div")}),this._activeIndex=0,this._editor=null,this._model=null,this._selected=new a.Signal(this),this._visibilityChanged=new a.Signal(this),this._indexChanged=new a.Signal(this),this._lastSubsetMatch="",this._geometryLock=!1,this._geometryCounter=0,this._docPanelExpanded=!1,this._renderCounter=0,this.sanitizer=null!==(t=e.sanitizer)&&void 0!==t?t:new v.Sanitizer,this._defaultRenderer=b.getDefaultRenderer(this.sanitizer),this._renderer=null!==(i=e.renderer)&&void 0!==i?i:this._defaultRenderer,this._docPanel=this._createDocPanelNode(),this.model=null!==(s=e.model)&&void 0!==s?s:null,this.editor=null!==(n=e.editor)&&void 0!==n?n:null,this.addClass("jp-Completer"),this._updateConstraints()}_updateConstraints(){const e=document.createElement("div");e.classList.add(I),e.style.visibility="hidden",e.style.overflowY="scroll",document.body.appendChild(e);const t=window.getComputedStyle(e);this._maxHeight=parseInt(t.maxHeight,10),this._minHeight=parseInt(t.minHeight,10),this._scrollbarWidth=e.offsetWidth-e.clientWidth,document.body.removeChild(e);const i=this._createDocPanelNode();this._docPanelWidth=T.measureSize(i,"inline-block").width}get activeIndex(){return this._activeIndex}get editor(){return this._editor}set editor(e){this._editor=e}get selected(){return this._selected}get visibilityChanged(){return this._visibilityChanged}get indexChanged(){return this._indexChanged}get model(){return this._model}set model(e){(e||this._model)&&e!==this._model&&(this._model&&(this._model.stateChanged.disconnect(this.onModelStateChanged,this),this._model.queryChanged.disconnect(this.onModelQueryChanged,this)),this._model=e,this._model&&(this._model.stateChanged.connect(this.onModelStateChanged,this),this._model.queryChanged.connect(this.onModelQueryChanged,this)))}get renderer(){return this._renderer}set renderer(e){this._renderer=e}set showDocsPanel(e){this._showDoc=e}get showDocsPanel(){return this._showDoc}dispose(){this._sizeCache=void 0,this._model=null,super.dispose()}handleEvent(e){if(!this.isHidden&&this._editor)switch(e.type){case"keydown":this._evtKeydown(e);break;case"pointerdown":this._evtPointerdown(e);break;case"scroll":this._evtScroll(e)}}reset(){this._activeIndex=0,this._lastSubsetMatch="",this._model&&this._model.reset(!0),this._docPanel.style.display="none",this._sizeCache=void 0,this.node.scrollTop=0}selectActive(){const e=this.node.querySelector(`.${P}`);e?(this._selected.emit(e.getAttribute("data-value")),this.reset()):this.reset()}onAfterAttach(e){document.addEventListener("keydown",this,S),document.addEventListener("pointerdown",this,S),document.addEventListener("scroll",this,S)}onBeforeDetach(e){document.removeEventListener("keydown",this,S),document.removeEventListener("pointerdown",this,S),document.removeEventListener("scroll",this,S)}onModelStateChanged(){this.isAttached&&(this._activeIndex=0,this._indexChanged.emit(this._activeIndex),this.update())}onModelQueryChanged(e,t){if(this._sizeCache&&"editorUpdate"===t.origin){const t=e.completionItems(),i=this._sizeCache.items,s=i[this._findWidestItemIndex(i)],n=t[this._findWidestItemIndex(t)],o=this._getPreferredItemWidthHeuristic();t.length===this._sizeCache.items.length&&o(s)===o(n)||(this._sizeCache=void 0)}}onUpdateRequest(e){var t;const i=this._model;if(!i)return;i.query||this._populateSubset();let s=i.completionItems();if(!s.length)return void(this.isHidden||(this.reset(),this.hide(),this._visibilityChanged.emit(void 0)));this._updateConstraints(),this._geometryLock=!0;const n=this._createCompleterNode(i,s);let o=n.querySelectorAll(`.${x}`)[this._activeIndex];o.classList.add(P);const r=null===(t=this.model)||void 0===t?void 0:t.resolveItem(s[this._activeIndex]);this._showDoc&&(this._docPanel.innerText="",n.appendChild(this._docPanel),this._docPanelExpanded=!1,this._docPanel.style.display="none",this._updateDocPanel(r,o)),this.isHidden?(this.show(),this._setGeometry(),this._visibilityChanged.emit(void 0)):this._setGeometry(),this._geometryLock=!1}get sizeCache(){if(this._sizeCache)return{width:this._sizeCache.width+this._sizeCache.docPanelWidth,height:Math.max(this._sizeCache.height,this._sizeCache.docPanelHeight)}}_createDocPanelNode(){const e=document.createElement("div");return e.className="jp-Completer-docpanel",e}_createCompleterNode(e,t){const i=++this._renderCounter;let s=this.node;s.textContent="";let n=e.orderedTypes(),o=document.createElement("ul");o.className=I;const r=this._renderer.createCompletionItemNode(t[0],n),l=[r],a=T.measureSize(r,"inline-grid"),h=Math.max(Math.ceil(this._maxHeight/a.height),5),d=Math.min(h+1,t.length),c=performance.now();for(let e=1;e<d;e++){const i=this._renderer.createCompletionItemNode(t[e],n);l.push(i)}for(const e of l)o.appendChild(e);const u=this._findWidestItemIndex(t),m=u<l.length?l[u]:this._renderer.createCompletionItemNode(t[u],n),p=T.measureSize(m.cloneNode(!0),"inline-grid");if(this._sizeCache={height:Math.min(this._maxHeight,a.height*t.length),width:p.width+this._scrollbarWidth,items:t,docPanelWidth:0,docPanelHeight:0},d<t.length){const e=(performance.now()-c)/d,s=Math.max(5,Math.floor(12/e));let r=d,h=l[l.length-1];const u=()=>{if(r>=t.length)return;const e=a.height*(t.length-r);h.style.marginBottom=`${e}px`,requestAnimationFrame((()=>{if(i!=this._renderCounter)return;h.style.marginBottom="";const e=Math.min(t.length,r+s);for(let i=r;i<e;i++){const e=this._renderer.createCompletionItemNode(t[i],n);o.appendChild(e),h=e}r=e,u()}))};u()}return s.appendChild(o),s}_findWidestItemIndex(e){const t=this._getPreferredItemWidthHeuristic(),i=e.map(t);return i.indexOf(Math.max(...i))}_getPreferredItemWidthHeuristic(){return this._renderer.itemWidthHeuristic?this._renderer.itemWidthHeuristic.bind(this._renderer):this._defaultRenderer.itemWidthHeuristic.bind(this._defaultRenderer)}_cycle(e){var t,i;const s=this.node.querySelectorAll(`.${x}`),n=this._activeIndex,o=s.length-1;let r=this.node.querySelector(`.${P}`);switch(r.classList.remove(P),e){case"up":this._activeIndex=0===n?o:n-1;break;case"down":this._activeIndex=n<o?n+1:0;break;case"pageUp":case"pageDown":{const t=this.node.getBoundingClientRect(),i=r.getBoundingClientRect(),s=Math.floor(t.height/i.height),l="pageUp"===e?-1:1;this._activeIndex=Math.min(Math.max(0,n+l*s),o);break}}r=s[this._activeIndex],r.classList.add(P);let l=this.node.querySelector(`.${I}`);y.ElementExt.scrollIntoViewIfNeeded(l,r),this._indexChanged.emit(this._activeIndex);const a=null===(t=this.model)||void 0===t?void 0:t.completionItems(),h=null==a?void 0:a[this._activeIndex];if(h){const e=null===(i=this.model)||void 0===i?void 0:i.resolveItem(h);this._showDoc&&this._updateDocPanel(e,r)}}_evtKeydown(e){if(!this.isHidden&&this._editor)if(this._editor.host.contains(e.target))switch(e.keyCode){case 9:{e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation();const t=this._model;if(!t)return;const i=t.completionItems();if(i&&1===i.length)return this._selected.emit(i[0].insertText||i[0].label),void this.reset();const s=this._populateSubset();return t.query&&t.query!==this._lastSubsetMatch&&(t.subsetMatch=!0,this._selected.emit(t.query),t.subsetMatch=!1,this._lastSubsetMatch=t.query),s&&this.update(),void this._cycle(e.shiftKey?"up":"down")}case 27:return e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),void this.reset();case 33:case 34:case 38:case 40:{e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation();const t=T.keyCodeMap[e.keyCode];return void this._cycle(t)}default:return}else this.reset()}_evtPointerdown(e){if(this.isHidden||!this._editor)return;if(T.nonstandardClick(e))return void this.reset();let t=e.target;for(;t!==document.documentElement;){if(t.classList.contains(x))return e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),this._selected.emit(t.getAttribute("data-value")),void this.reset();if(t===this.node)return e.preventDefault(),e.stopPropagation(),void e.stopImmediatePropagation();t=t.parentElement}this.reset()}_evtScroll(e){if(this.isHidden||!this._editor)return;const{node:t}=this;t.contains(e.target)||requestAnimationFrame((()=>{this._setGeometry()}))}_populateSubset(){const{model:e}=this;if(!e)return!1;const t=e.completionItems(),i=T.commonSubset(t.map((e=>e.insertText||e.label))),{query:s}=e;return!(!i||i===s||0!==i.indexOf(s)||(e.query=i,0))}_setGeometry(){const{node:e}=this,t=this._model,i=this._editor;if(!(i&&t&&t.original&&t.cursor))return;const s=t.cursor.start,n=i.getPositionAt(s),o=i.getCoordinateForPosition(n),r=window.getComputedStyle(e),l=parseInt(r.borderLeftWidth,10)||0,a=parseInt(r.paddingLeft,10)||0,h=i.host.closest(".jp-MainAreaWidget > .lm-Widget")||i.host,d=t.completionItems();this._sizeCache&&this._sizeCache.items.length!==d.length&&(this._sizeCache=void 0),C.HoverBox.setGeometry({anchor:o,host:h,maxHeight:this._maxHeight,minHeight:this._minHeight,node:e,size:this.sizeCache,offset:{horizontal:l+a},privilege:"below",style:r,outOfViewDisplay:{top:"stick-inside",bottom:"stick-inside",left:"stick-inside",right:"stick-outside"}});const c=++this._geometryCounter;this._sizeCache||requestAnimationFrame((()=>{if(c!=this._geometryCounter)return;let t=e.getBoundingClientRect(),i=this._docPanel.getBoundingClientRect();this._sizeCache={width:t.width-i.width,height:t.height,items:d,docPanelWidth:i.width,docPanelHeight:i.height}}))}_updateDocPanel(e,t){var i,s,n;let o=this._docPanel;if(!e)return void this._toggleDocPanel(!1);const r=null!==(n=null===(s=(i=this._renderer).createLoadingDocsIndicator)||void 0===s?void 0:s.call(i))&&void 0!==n?n:this._defaultRenderer.createLoadingDocsIndicator();t.appendChild(r),e.then((e=>{var t,i,s;if(e&&o)if(e.documentation){const n=null!==(s=null===(i=(t=this._renderer).createDocumentationNode)||void 0===i?void 0:i.call(t,e))&&void 0!==s?s:this._defaultRenderer.createDocumentationNode(e);o.textContent="",o.appendChild(n),this._toggleDocPanel(!0)}else this._toggleDocPanel(!1)})).catch((e=>console.error(e))).finally((()=>{t.removeChild(r)}))}_toggleDocPanel(e){let t=this._docPanel;if(e){if(this._docPanelExpanded)return;t.style.display="",this._docPanelExpanded=!0}else{if(!this._docPanelExpanded)return;t.style.display="none",this._docPanelExpanded=!1}const i=this._sizeCache;i&&(i.docPanelHeight=e?this._maxHeight:0,i.docPanelWidth=e?this._docPanelWidth:0,this._geometryLock||this._setGeometry())}}var T;!function(e){class t{constructor(e){this.sanitizer=(null==e?void 0:e.sanitizer)||new v.Sanitizer}createCompletionItemNode(e,t){let i=this._createWrapperNode(e.insertText||e.label);return e.deprecated&&i.classList.add("jp-Completer-deprecated"),this._constructNode(i,this._createLabelNode(e.label),!!e.type,e.type,t,e.icon)}createDocumentationNode(e){const t=document.createElement("div");t.classList.add("jp-RenderedText");const i=this.sanitizer,s=e.documentation||"";return(0,f.renderText)({host:t,sanitizer:i,source:s}).catch(console.error),t}itemWidthHeuristic(e){var t;return e.label.replace(/<(\/)?mark>/g,"").length+((null===(t=e.type)||void 0===t?void 0:t.length)||0)}createLoadingDocsIndicator(){const e=document.createElement("div");e.classList.add("jp-Completer-loading-bar-container");const t=document.createElement("div");return t.classList.add("jp-Completer-loading-bar"),e.append(t),e}_createWrapperNode(e){const t=document.createElement("li");return t.className=x,t.setAttribute("data-value",e),t}_createLabelNode(e){const t=document.createElement("code");return t.className="jp-Completer-match",t.innerHTML=e,t}_constructNode(e,t,i,s,n,o){if(o){const t=o.element({className:"jp-Completer-type jp-Completer-icon"});e.appendChild(t)}else if(i){const t=document.createElement("span");t.textContent=(s[0]||"").toLowerCase();const i=n.indexOf(s)%10+1;t.className="jp-Completer-type jp-Completer-monogram",t.setAttribute("data-color-index",i.toString()),e.appendChild(t)}else{const t=document.createElement("span");t.className="jp-Completer-monogram",e.appendChild(t)}if(e.appendChild(t),i){e.title=s;const t=document.createElement("code");t.className="jp-Completer-typeExtended",t.textContent=s.toLocaleLowerCase(),e.appendChild(t)}else{const t=document.createElement("span");t.className="jp-Completer-typeExtended",e.appendChild(t)}return e}}let i;e.Renderer=t,e.getDefaultRenderer=function(e){return(!i||e&&i.sanitizer!==e)&&(i=new t({sanitizer:e})),i}}(b||(b={})),function(e){e.keyCodeMap={38:"up",40:"down",33:"pageUp",34:"pageDown"},e.commonSubset=function(e){const t=e.length;let i="";if(t<2)return i;const s=e[0].length;for(let n=0;n<s;n++){const s=e[0][n];for(let o=1;o<t;o++)if(e[o][n]!==s)return i;i+=s}return i},e.nonstandardClick=function(e){return 0!==e.button||e.altKey||e.ctrlKey||e.shiftKey||e.metaKey},e.measureSize=function(e,t){e.isConnected&&console.warn("Measuring connected elements with `measureSize` has side-effects"),e.style.visibility="hidden",e.style.display=t,document.body.appendChild(e);const i=e.getBoundingClientRect();return document.body.removeChild(e),e.removeAttribute("style"),i}}(T||(T={}));class M{constructor(e){var t,i;this._resolveFactory=(e,t)=>e.resolve?i=>e.resolve(t,this._context,i):void 0,this._fetching=0,this._inlineFetching=0,this._providers=e.providers,this._inlineProviders=null!==(t=e.inlineProviders)&&void 0!==t?t:[],this._inlineProvidersSettings=null!==(i=e.inlineProvidersSettings)&&void 0!==i?i:{},this._context=e.context,this._timeout=e.timeout}async applicableProviders(){const e=this._providers.map((e=>e.isApplicable(this._context))),t=await Promise.all(e);return this._providers.filter(((e,i)=>t[i]))}fetchInline(e,t){let i=[];const s=++this._inlineFetching;for(const o of this._inlineProviders){const r=this._inlineProvidersSettings[o.identifier];let l=0;t===n.Automatic&&(l=r.debouncerDelay);const h=()=>{const i=o.fetch(e,{...this._context,triggerKind:t}).then((e=>({...e,items:e.items.map((e=>{const t=e;return t.stream=new a.Signal(t),t.provider=o,this._stream(t,o),t}))}))),s=new Promise((e=>setTimeout((()=>e(null)),l+r.timeout)));return Promise.race([i,s])},d=0===l?h():new Promise(((e,t)=>setTimeout((()=>s!=this._inlineFetching?t(null):e(h())),l)));i.push(d.catch((e=>e)))}return i}async _stream(e,t){if(!e.isIncomplete||!t.stream||!e.token)return;const i=e.stream,s=e.token;e.token=void 0,e.streaming=!0,i.emit(u.StraemEvent.opened);for await(const n of t.stream(s)){const t=n.response,s=t.insertText.substring(e.insertText.length);e.insertText=t.insertText,e.lastStreamed=s,i.emit(u.StraemEvent.update)}e.isIncomplete=!1,e.lastStreamed=void 0,e.streaming=!1,i.emit(u.StraemEvent.closed)}async fetch(e,t){const i=++this._fetching;let s=[];const n=await this.applicableProviders();for(const o of n){let n;n=o.fetch(e,this._context,t).then((e=>{if(i!==this._fetching)return Promise.reject(void 0);const t=e.items.map((e=>({...e,resolve:this._resolveFactory(o,e)})));return{...e,items:t}}));const r=new Promise((e=>setTimeout((()=>e(null)),this._timeout)));n=Promise.race([n,r]),s.push(n.catch((e=>e)))}const o=Promise.all(s);return this._mergeCompletions(o)}async shouldShowContinuousHint(e,t){const i=await this.applicableProviders();return 0!==i.length&&(i[0].shouldShowContinuousHint?i[0].shouldShowContinuousHint(e,t,this._context):this._defaultShouldShowContinuousHint(e,t))}_alignPrefixes(e,t,i){if(t!=i){const t=this._context.editor;if(!t)return e;const s=t.getCursorPosition(),n=t.getLine(s.line);return n?e.map((e=>{if(e.start==i)return e;let t=n.substring(e.start,i);return{...e,items:e.items.map((e=>{let i=e.insertText||e.label;return e.insertText=i.startsWith(t)?i.slice(t.length):i,e}))}})):e}return e}async _mergeCompletions(e){let t=(await e).filter((e=>!(!e||e instanceof Error||!e.items.length)));if(0==t.length)return null;if(1==t.length)return t[0];const i=Math.min(...t.map((e=>e.end))),s=t.map((e=>e.start)),n=Math.min(...s),o=Math.max(...s);t=this._alignPrefixes(t,n,o);const r=new Set,l=new Array;for(const e of t)e.items.forEach((e=>{let t=(e.insertText||e.label).trim();r.has(t)||(r.add(t),l.push(e))}));return{start:o,end:i,items:l}}_defaultShouldShowContinuousHint(e,t){return!e&&(null==t.sourceChange||t.sourceChange.some((e=>null!=e.insert&&e.insert.length>0)))}}const E="CompletionProvider:context";class k{constructor(){this.identifier=E,this.rank=500,this.renderer=null}async isApplicable(e){return!0}fetch(e,t){const i=t.editor;return i?new Promise((e=>{e(D.contextHint(i))})):Promise.reject("No editor")}}var D;!function(e){e.contextHint=function(e){const t=e.getTokenAtCursor(),i=function(e,t){return t.getTokens().filter((t=>0===t.value.indexOf(e.value)&&t.value!==e.value))}(t,e).filter((e=>e.type)).map((e=>e.value)),s=new Set(i),n=new Array;return s.forEach((e=>n.push({label:e}))),{start:t.offset,end:t.offset+t.value.length,items:n}}}(D||(D={}));const L="CompletionProvider:kernel";class H{constructor(){this.identifier=L,this.rank=550,this.renderer=null}async isApplicable(e){var t;return!!(null===(t=e.session)||void 0===t?void 0:t.kernel)}async fetch(e,t){var i;const s=null===(i=t.session)||void 0===i?void 0:i.kernel;if(!s)throw new Error("No kernel for completion request.");const n={code:e.text,cursor_pos:e.offset},o=(await s.requestComplete(n)).content;if("ok"!==o.status)throw new Error("Completion fetch failed to return successfully.");const r=new Array,l=o.metadata._jupyter_types_experimental;return o.matches.forEach(((e,t)=>{l&&l[t]?r.push({label:e,type:l[t].type,insertText:l[t].text}):r.push({label:e})})),{start:o.cursor_start,end:o.cursor_end,items:r}}async resolve(e,t,i){const{editor:s,session:n}=t;if(n&&s){let t=s.model.sharedModel.getSource();const o=s.getCursorPosition();let l=r.Text.jsIndexToCharIndex(s.getOffsetAt(o),t);const a=n.kernel;if(!t||!a)return Promise.resolve(e);if(i){const{start:e,value:s}=i;t=t.substring(0,e)+s,l+=s.length}const h={code:t,cursor_pos:l,detail_level:0},d=(await a.requestInspect(h)).content;return"ok"===d.status&&d.found?(e.documentation=d.data["text/plain"],e):e}return e}shouldShowContinuousHint(e,t){const i=t.sourceChange;return null==i||!i.some((e=>null!=e.delete))&&i.some((t=>null!=t.insert&&("."===t.insert||!e&&t.insert.trim().length>0)))}}var A,q=i(30890),O=i(17811);class R{constructor(e){this.options=e}placeGhost(e,t){const i=[A.addMark.of(t)];e.state.field(A.markField,!1)||(i.push(O.StateEffect.appendConfig.of([A.markField])),i.push(O.StateEffect.appendConfig.of([q.EditorView.domEventHandlers({blur:t=>{if(!1===this.options.onBlur(t))return!0;const i=[A.removeMark.of(null)];setTimeout((()=>{e.dispatch({effects:i})}),0)}})]))),e.dispatch({effects:i})}clearGhosts(e){const t=[A.removeMark.of(null)];e.dispatch({effects:t})}}R.streamingAnimation="uncover";class W extends q.WidgetType{constructor(e){super(),this.options=e,this.isSpacer=!1}eq(e){return e.content==this.content&&e.options.streaming===this.options.streaming}get lineBreaks(){return(this.content.match(/\n/g)||"").length}updateDOM(e,t){return this._updateDOM(e),!0}get content(){return this.options.content}toDOM(){let e=document.createElement("span");return this.options.onPointerOver&&e.addEventListener("pointerover",this.options.onPointerOver),this.options.onPointerLeave&&e.addEventListener("pointerleave",this.options.onPointerLeave),e.classList.add("jp-GhostText"),e.dataset.animation=R.streamingAnimation,e.dataset.providedBy=this.options.providerId,this._updateDOM(e),e}_updateDOM(e){const t=this.content;let i=this.options.addedPart;if(i&&!this.isSpacer){i.startsWith("\n")&&(i=i.substring(1)),e.innerText=t.substring(0,t.length-i.length);const s=document.createElement("span");s.className="jp-GhostText-streamedToken",s.innerText=i,e.appendChild(s)}else e.innerText=t;if(!this.isSpacer&&this.options.streaming){const t=document.createElement("span");t.className="jp-GhostText-streamingIndicator",e.appendChild(t)}}destroy(e){this.options.onPointerOver&&e.removeEventListener("pointerover",this.options.onPointerOver),this.options.onPointerLeave&&e.removeEventListener("pointerleave",this.options.onPointerLeave),super.destroy(e)}}class j extends W{constructor(){super(...arguments),this.isSpacer=!0}}class N extends j{toDOM(){const e=super.toDOM();return e.classList.add("jp-GhostText-lineSpacer"),e}}class z extends j{get content(){return this.options.content[0]}toDOM(){const e=super.toDOM();return e.classList.add("jp-GhostText-letterSpacer"),e}}!function(e){let t;function i(e,t){return q.Decoration.widget({widget:new W(e),side:1,ghostSpec:e}).range(Math.min(e.from,t.newDoc.length),Math.min(e.from,t.newDoc.length))}!function(e){e[e.Set=0]="Set",e[e.Remove=1]="Remove",e[e.FilterAndUpdate=2]="FilterAndUpdate"}(t||(t={})),e.addMark=O.StateEffect.define({map:(e,t)=>({...e,from:t.mapPos(e.from),to:t.mapPos(e.from+e.content.length)})}),e.removeMark=O.StateEffect.define(),e.markField=O.StateField.define({create:()=>q.Decoration.none,update(s,n){const o=function(i){for(let s of i.effects){if(s.is(e.addMark))return{action:t.Set,spec:s.value};if(s.is(e.removeMark))return{action:t.Remove}}return i.docChanged||i.selection?{action:t.FilterAndUpdate}:null}(n);if(s=s.update({filter:(e,t,i)=>!(i.spec.widget instanceof j&&i.spec.timeoutInfo.elapsed)}),!o)return s.map(n.changes);switch(o.action){case t.Set:{const e=i(o.spec,n);return s.update({add:[e],filter:(t,i,s)=>s===e.value})}case t.Remove:return s.update({filter:()=>!1});case t.FilterAndUpdate:{let e=s.iter();for(;e.value&&e.value.spec.widget instanceof j;)e.next();if(!e.value)return s.map(n.changes);const t=e.value.spec.ghostSpec,o={...t};let r=!1;n.changes.iterChanges(((e,t,i,s,n)=>{if(!r)if(e===t&&i!==s)for(let e=0;e<n.lines;e++){const t=n.lineAt(e).text,i=e>0?"\n"+t:t;if(!o.content.startsWith(i)){r=!0;break}o.content=o.content.slice(i.length),o.from+=i.length}else r=!0}));const l=r?function(e,t,i=1e3){if(e.content.length<2)return[];const s={elapsed:!1};setTimeout((()=>{s.elapsed=!0}),i);const n=q.Decoration.widget({widget:new z(e),side:1,timeoutInfo:s}),o=q.Decoration.widget({widget:new N(e),side:1,timeoutInfo:s});return[n.range(Math.min(e.from,t.newDoc.length),Math.min(e.from,t.newDoc.length)),o.range(Math.min(e.from,t.newDoc.length),Math.min(e.from,t.newDoc.length))]}(t,n):[i(o,n)],a=l.map((e=>e.value));if(s=s.update({add:l,filter:(e,t,i)=>a.includes(i)}),r)try{s=s.map(n.changes)}catch(e){return console.warn(e),q.Decoration.none}return s}}},provide:e=>q.EditorView.decorations.from(e)})}(A||(A={}));const B="jp-mod-inline-completer-active",F="jp-InlineCompleter-hover";class V extends w.Widget{constructor(e){var t,i;super({node:document.createElement("div")}),this._clearHoverTimeout=null,this._current=0,this._editor=null,this._lastItem=null,this._model=null,this._providerWidget=new w.Widget,this._showShortcuts=V.defaultSettings.showShortcuts,this._showWidget=V.defaultSettings.showWidget,this._suggestionsCounter=new w.Widget,this._toolbar=new C.Toolbar,this.model=null!==(t=e.model)&&void 0!==t?t:null,this.editor=null!==(i=e.editor)&&void 0!==i?i:null,this.addClass("jp-InlineCompleter"),this._ghostManager=new R({onBlur:this._onEditorBlur.bind(this)}),this._trans=e.trans;const s=this.layout=new w.PanelLayout;s.addWidget(this._suggestionsCounter),s.addWidget(this.toolbar),s.addWidget(this._providerWidget),this._progressBar=document.createElement("div"),this._progressBar.className="jp-InlineCompleter-progressBar",this.node.appendChild(this._progressBar),this._updateShortcutsVisibility(),this._updateDisplay(),this.node.tabIndex=0}get toolbar(){return this._toolbar}get editor(){return this._editor}set editor(e){var t;null===(t=this.model)||void 0===t||t.reset(),this._editor=e}get model(){return this._model}set model(e){(e||this._model)&&e!==this._model&&(this._model&&(this._model.suggestionsChanged.disconnect(this._onModelSuggestionsChanged,this),this._model.filterTextChanged.disconnect(this._onModelFilterTextChanged,this),this._model.provisionProgress.disconnect(this._onProvisionProgress,this)),this._model=e,this._model&&(this._model.suggestionsChanged.connect(this._onModelSuggestionsChanged,this),this._model.filterTextChanged.connect(this._onModelFilterTextChanged,this),this._model.provisionProgress.connect(this._onProvisionProgress,this)))}cycle(e){var t,i;const s=null===(i=null===(t=this.model)||void 0===t?void 0:t.completions)||void 0===i?void 0:i.items;if(s){if("next"===e){const e=this._current+1;this._current=e===s.length?0:e}else{const e=this._current-1;this._current=-1===e?s.length-1:e}this._updateStreamTracking(),this._render()}}accept(){const e=this.model,t=this.current,i=this._editor;if(!i||!e||!t)return;const s=e.cursor,n=t.insertText,o=i.getOffsetAt(i.getCursorPosition()),r=i.getOffsetAt(s),l=r,a=o;i.model.sharedModel.updateSource(r,o,n),o<=a&&o>=l&&i.setCursorPosition(i.getPositionAt(l+n.length)),e.reset(),this.update()}get current(){var e;const t=null===(e=this.model)||void 0===e?void 0:e.completions;return t?t.items[this._current]:null}_updateStreamTracking(){this._lastItem&&this._lastItem.stream.disconnect(this._onStream,this);const e=this.current;e&&e.stream.connect(this._onStream,this),this._lastItem=e}_onStream(e,t){var i;const s=null===(i=this.model)||void 0===i?void 0:i.completions;if(!s||!s.items||0===s.items.length)return;if(this.isHidden)return;const n=s.items[this._current];this._setText(n)}configure(e){this._showWidget=e.showWidget,this._updateDisplay(),e.showShortcuts!==this._showShortcuts&&(this._showShortcuts=e.showShortcuts,this._updateShortcutsVisibility()),R.streamingAnimation=e.streamingAnimation}handleEvent(e){if(!this.isHidden&&this._editor)switch(e.type){case"pointerdown":this._evtPointerdown(e);break;case"scroll":this._evtScroll(e)}}onUpdateRequest(e){super.onUpdateRequest(e);const t=this._model;if(!t)return;let i=t.completions;i&&i.items&&0!==i.items.length?this.isHidden&&(this.show(),this._setGeometry()):this.isHidden||this.hide()}onAfterAttach(e){document.addEventListener("scroll",this,!0),document.addEventListener("pointerdown",this,!0)}onBeforeDetach(e){document.removeEventListener("scroll",this,!0),document.removeEventListener("pointerdown",this,!0)}_evtPointerdown(e){var t;if(this.isHidden||!this._editor)return;const i=e.target;if(this.node.contains(i))return!0;this.hide(),null===(t=this.model)||void 0===t||t.reset()}_evtScroll(e){if(this.isHidden||!this._editor)return;const{node:t}=this;t.contains(e.target)||requestAnimationFrame((()=>{this._setGeometry()}))}_onEditorBlur(e){var t;if(this.node.contains(e.relatedTarget))return!1;null===(t=this._editor)||void 0===t||t.host.classList.remove(B),this.hide()}_onModelSuggestionsChanged(e,t){var i;if(this.isAttached){if("set"===t.event)this._current=null!==(i=t.indexMap.get(this._current))&&void 0!==i?i:0;else if("clear"===t.event){const e=this.editor;e&&(this._ghostManager.clearGhosts(e.editor),e.host.classList.remove(B))}this._updateStreamTracking(),this.update(),this._render()}else this.update()}_onModelFilterTextChanged(e,t){var i,s;const n=null===(i=this.model)||void 0===i?void 0:i.completions;n&&n.items&&0!==n.items.length&&(this._current=null!==(s=t.get(this._current))&&void 0!==s?s:0,this._updateStreamTracking(),setTimeout((()=>{this._render(),this._setGeometry()}),0))}_onProvisionProgress(e,t){requestAnimationFrame((()=>{0===t.pendingProviders?this._progressBar.style.display="none":(this._progressBar.style.display="",this._progressBar.style.width=100*t.pendingProviders/t.totalProviders+"%")}))}_render(){var e,t;const i=null===(e=this.model)||void 0===e?void 0:e.completions;if(!i||!i.items||0===i.items.length)return;const s=i.items[this._current];this._setText(s),"never"!==this._showWidget&&(this._suggestionsCounter.node.innerText=this._trans.__("%1/%2",this._current+1,i.items.length),this._providerWidget.node.title=this._trans.__("Provider: %1",s.provider.name),(null!==(t=s.provider.icon)&&void 0!==t?t:C.kernelIcon).render(this._providerWidget.node))}_setText(e){const t=e.insertText,i=this._editor,s=this._model;if(!s||!i)return;const n=i.editor;this._ghostManager.placeGhost(n,{from:i.getOffsetAt(s.cursor),content:t,providerId:e.provider.identifier,addedPart:e.lastStreamed,streaming:e.streaming,onPointerOver:this._onPointerOverGhost.bind(this),onPointerLeave:this._onPointerLeaveGhost.bind(this)}),i.host.classList.add(B)}_onPointerOverGhost(){null!==this._clearHoverTimeout&&(window.clearTimeout(this._clearHoverTimeout),this._clearHoverTimeout=null),this.node.classList.add(F)}_onPointerLeaveGhost(){this._clearHoverTimeout=window.setTimeout((()=>this.node.classList.remove(F)),500)}_setGeometry(){const{node:e}=this,t=this._model,i=this._editor;if(!i||!t||!t.cursor)return;const s=i.host.closest(".jp-MainAreaWidget > .lm-Widget")||i.host;let n;try{n=i.getCoordinateForPosition(t.cursor)}catch(e){return void this.hide()}C.HoverBox.setGeometry({anchor:n,host:s,maxHeight:40,minHeight:20,node:e,privilege:"forceAbove",outOfViewDisplay:{top:"stick-outside",bottom:"stick-inside",left:"stick-inside",right:"stick-outside"}})}_updateShortcutsVisibility(){this.node.dataset.showShortcuts=this._showShortcuts+""}_updateDisplay(){this.node.dataset.display=this._showWidget}}!function(e){e.defaultSettings={showWidget:"onHover",showShortcuts:!0,streamingAnimation:"uncover",providers:{}},e.Model=class{constructor(){this.suggestionsChanged=new a.Signal(this),this.filterTextChanged=new a.Signal(this),this.provisionProgress=new a.Signal(this),this._isDisposed=!1,this._completions=null}setCompletions(e){var t,i;const s=new Map(null===(i=null===(t=this._completions)||void 0===t?void 0:t.items)||void 0===i?void 0:i.map(((e,t)=>[e.insertText,t])));this._completions=e;const n=new Map(e.items.map(((e,t)=>[s.get(e.insertText),t])));this.suggestionsChanged.emit({event:"set",indexMap:n})}appendCompletions(e){this._completions&&this._completions.items?(this._completions.items.push(...e.items),this.suggestionsChanged.emit({event:"append"})):console.warn("No completions to append to")}notifyProgress(e){this.provisionProgress.emit(e)}get cursor(){return this._cursor}set cursor(e){this._cursor=e}get completions(){return this._completions}reset(){this._completions=null,this.suggestionsChanged.emit({event:"clear"})}get isDisposed(){return this._isDisposed}handleTextChange(e){var t;const i=this._completions;if(!i||!i.items||0===i.items.length)return;const s=new Map(i.items.map(((e,t)=>[e,t])));for(let s of null!==(t=e.sourceChange)&&void 0!==t?t:[]){const e=s.insert;if(e){const t=i.items.filter((t=>{var i;const s=null!==(i=t.filterText)&&void 0!==i?i:t.insertText;return!!s.startsWith(e)&&(t.filterText=s.substring(e.length),t.insertText=t.insertText.substring(e.length),!0)}));0===t.length&&(this._completions=null),i.items=t}else s.retain||(this._completions=null)}const n=new Map(i.items.map(((e,t)=>[s.get(e),t])));this.filterTextChanged.emit(n)}handleSelectionChange(e){const t=this.cursor;if(!t)return;const{start:i,end:s}=e;i.column===s.column&&i.line===s.line||this.reset(),(i.line!==t.line||i.column<t.column)&&this.reset()}dispose(){this._isDisposed||(this._isDisposed=!0,a.Signal.clearData(this))}}}(V||(V={}));class G{constructor(){this._activeProviders=new Set([L,E]),this._inlineCompleterSettings=V.defaultSettings,this._providers=new Map,this._inlineProviders=new Map,this._panelHandlers=new Map,this._mostRecentContext=new Map,this._activeProvidersChanged=new a.Signal(this),this._inlineCompleterFactory=null}get activeProvidersChanged(){return this._activeProvidersChanged}setTimeout(e){this._timeout=e}setShowDocumentationPanel(e){this._panelHandlers.forEach((t=>t.completer.showDocsPanel=e)),this._showDoc=e}setContinuousHinting(e){this._panelHandlers.forEach((t=>t.autoCompletion=e)),this._autoCompletion=e}registerProvider(e){const t=e.identifier;this._providers.has(t)?console.warn(`Completion provider with identifier ${t} is already registered`):(this._providers.set(t,e),this._panelHandlers.forEach(((e,t)=>{this.updateCompleter(this._mostRecentContext.get(t))})))}registerInlineProvider(e){const t=e.identifier;this._inlineProviders.has(t)?console.warn(`Completion provider with identifier ${t} is already registered`):(this._inlineProviders.set(t,e),this._panelHandlers.forEach(((e,t)=>{this.updateCompleter(this._mostRecentContext.get(t))})))}getProviders(){return this._providers}activateProvider(e){this._activeProviders=new Set([]),e.forEach((e=>{this._providers.has(e)&&this._activeProviders.add(e)})),0===this._activeProviders.size&&(this._activeProviders.add(L),this._activeProviders.add(E)),this._activeProvidersChanged.emit()}async updateCompleter(e){var t,i;const{widget:s,editor:n,sanitizer:o}=e,r=s.id,l=this._panelHandlers.get(r),a=[...this._activeProviders][0],h=this._providers.get(a);let d=null!==(t=null==h?void 0:h.renderer)&&void 0!==t?t:b.getDefaultRenderer(o);const c=null==h?void 0:h.modelFactory;let u;u=c?await c.call(h,e):new _,this._mostRecentContext.set(s.id,e);const m={model:u,editor:n,renderer:d,sanitizer:o,showDoc:this._showDoc};if(l){const t=l.completer;null===(i=t.model)||void 0===i||i.dispose(),t.model=m.model,t.renderer=m.renderer,t.showDocsPanel=m.showDoc,l.autoCompletion=this._autoCompletion,n&&(l.editor=n,l.reconciliator=await this.generateReconciliator(e))}else{const t=await this._generateHandler(e,m);this._panelHandlers.set(s.id,t),s.disposed.connect((e=>{this.disposeHandler(e.id,t),this._mostRecentContext.delete(r)}))}}invoke(e){const t=this._panelHandlers.get(e);t&&t.invoke()}select(e){const t=this._panelHandlers.get(e);t&&t.completer.selectActive()}setInlineCompleterFactory(e){this._inlineCompleterFactory=e,this._panelHandlers.forEach(((e,t)=>{this.updateCompleter(this._mostRecentContext.get(t))})),this.inline||(this.inline={invoke:e=>{const t=this._panelHandlers.get(e);t&&t.inlineCompleter&&t.invokeInline()},cycle:(e,t)=>{const i=this._panelHandlers.get(e);i&&i.inlineCompleter&&i.inlineCompleter.cycle(t)},accept:e=>{const t=this._panelHandlers.get(e);t&&t.inlineCompleter&&t.inlineCompleter.accept()},configure:e=>{this._inlineCompleterSettings=e,this._panelHandlers.forEach(((t,i)=>{for(const[t,i]of this._inlineProviders.entries())i.configure&&i.configure(e.providers[t]);t.inlineCompleter&&t.inlineCompleter.configure(e),this.updateCompleter(this._mostRecentContext.get(i))}))}})}get inlineProviders(){return[...this._inlineProviders.values()]}async generateReconciliator(e){const t=[];for(const[e,i]of Object.entries(this._inlineCompleterSettings.providers))!0===i.enabled&&t.push(e);const i=[...this._inlineProviders.values()].filter((e=>t.includes(e.identifier))),s=[];for(const e of this._activeProviders){const t=this._providers.get(e);t&&s.push(t)}return new M({context:e,providers:s,inlineProviders:i,inlineProvidersSettings:this._inlineCompleterSettings.providers,timeout:this._timeout})}disposeHandler(e,t){var i,s,n,o;null===(i=t.completer.model)||void 0===i||i.dispose(),t.completer.dispose(),null===(n=null===(s=t.inlineCompleter)||void 0===s?void 0:s.model)||void 0===n||n.dispose(),null===(o=t.inlineCompleter)||void 0===o||o.dispose(),t.dispose(),this._panelHandlers.delete(e)}async _generateHandler(e,t){const i=new b(t),s=this._inlineCompleterFactory?this._inlineCompleterFactory.factory({...t,model:new V.Model}):void 0;i.hide(),w.Widget.attach(i,document.body),s&&(w.Widget.attach(s,document.body),s.hide(),s.configure(this._inlineCompleterSettings));const n=await this.generateReconciliator(e),o=new u({completer:i,inlineCompleter:s,reconciliator:n});return o.editor=e.editor,o}}const U=new C.LabIcon({name:"completer:inline",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24">\n  <path class="jp-icon4" fill="#bbbbbb" d="M17 15H3v2h14v-2Zm0-8h-6v2h6V7ZM3 13h18v-2H3v2Zm0 8h18v-2H3v2Z"/>\n  <path class="jp-icon1" fill="#616161" d="M3 3v2h18V3H3ZM3 7v2h8V7H3Z"/>\n</svg>\n'}),K=new C.LabIcon({name:"completer:widget",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24">\n<g fill="#616161" class="jp-icon2 jp-icon-selectable">\n  <path d="M6 8h16v14.3H6z" style="fill:#eee;stroke:#444;stroke-linejoin:round"/>\n  <path d="M3 3v2h10V3H3Z" transform="matrix(.8 0 0 1 .5 0)"/>\n</g>\n<g class="jp-icon3 jp-icon-selectable">\n  <path fill="#616161" d="M18 14.1H8v2h11v-2ZM8 12.1H20V10.1H8v2Zm0 8H20v-2H8v3Z"/>\n</g>\n</svg>\n'});var Z=i(71677);class ${constructor(e){this.options=e,this.identifier="@jupyterlab/inline-completer:history",this._maxSuggestions=100;const t=e.translator||Z.nullTranslator;this._trans=t.load("jupyterlab")}get name(){return this._trans.__("History")}get icon(){return C.historyIcon}get schema(){return{properties:{maxSuggestions:{title:this._trans.__("Maximum number of suggestions"),description:this._trans.__("The maximum number of suggestions to retrieve from history."),type:"number"}},default:{enabled:!1,maxSuggestions:100}}}configure(e){var t;this._maxSuggestions=null!==(t=e.maxSuggestions)&&void 0!==t?t:100}async fetch(e,t,i){var s;const n=null===(s=t.session)||void 0===s?void 0:s.kernel;if(!n)throw new Error("No kernel for completion request.");const o=e.text.slice(0,e.offset).split("\n").slice(-1)[0],r={output:!1,raw:!0,hist_access_type:"search",pattern:o+"*",unique:!0,n:this._maxSuggestions},l=await n.requestHistory(r),a=[];if(""===o)return{items:[]};if("ok"===l.content.status)for(const e of l.content.history){const t=e[2].split("\n");for(let e=0;e<t.length;e++){const i=t[e];if(i.startsWith(o)){const s=i.slice(o.length,i.length)+"\n"+t.slice(e+1).join("\n");a.push({insertText:s})}}}return{items:a}}}}}]);
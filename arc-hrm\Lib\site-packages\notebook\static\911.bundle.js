"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[911],{30911:(e,t,i)=>{i.r(t),i.d(t,{AccordionLayout:()=>N,AccordionPanel:()=>W,BoxEngine:()=>s,BoxLayout:()=>q,BoxPanel:()=>F,BoxSizer:()=>b,CommandPalette:()=>O,ContextMenu:()=>U,DockLayout:()=>te,DockPanel:()=>ie,FocusTracker:()=>se,GridLayout:()=>ne,Layout:()=>x,LayoutItem:()=>y,Menu:()=>$,MenuBar:()=>ae,Panel:()=>P,PanelLayout:()=>w,ScrollBar:()=>re,SingletonLayout:()=>oe,SplitLayout:()=>R,SplitPanel:()=>H,StackedLayout:()=>he,StackedPanel:()=>de,TabBar:()=>Y,TabPanel:()=>le,Title:()=>v,Widget:()=>M});var s,n,a,r,o=i(33625),h=i(20998),d=i(18395),l=i(49503),c=i(14421),u=i(81997),m=i(99615),g=i(32895),p=i(24475),_=i(2549),f=i(80554);class b{constructor(){this.sizeHint=0,this.minSize=0,this.maxSize=1/0,this.stretch=1,this.size=0,this.done=!1}}!function(e){e.calc=function(e,t){let i=e.length;if(0===i)return t;let s=0,n=0,a=0,r=0,o=0;for(let t=0;t<i;++t){let i=e[t],h=i.minSize,d=i.maxSize,l=i.sizeHint;i.done=!1,i.size=Math.max(h,Math.min(l,d)),a+=i.size,s+=h,n+=d,i.stretch>0&&(r+=i.stretch,o++)}if(t===a)return 0;if(t<=s){for(let t=0;t<i;++t){let i=e[t];i.size=i.minSize}return t-s}if(t>=n){for(let t=0;t<i;++t){let i=e[t];i.size=i.maxSize}return t-n}let h=.01,d=i;if(t<a){let s=a-t;for(;o>0&&s>h;){let t=s,n=r;for(let a=0;a<i;++a){let i=e[a];if(i.done||0===i.stretch)continue;let h=i.stretch*t/n;i.size-h<=i.minSize?(s-=i.size-i.minSize,r-=i.stretch,i.size=i.minSize,i.done=!0,d--,o--):(s-=h,i.size-=h)}}for(;d>0&&s>h;){let t=s/d;for(let n=0;n<i;++n){let i=e[n];i.done||(i.size-t<=i.minSize?(s-=i.size-i.minSize,i.size=i.minSize,i.done=!0,d--):(s-=t,i.size-=t))}}}else{let s=t-a;for(;o>0&&s>h;){let t=s,n=r;for(let a=0;a<i;++a){let i=e[a];if(i.done||0===i.stretch)continue;let h=i.stretch*t/n;i.size+h>=i.maxSize?(s-=i.maxSize-i.size,r-=i.stretch,i.size=i.maxSize,i.done=!0,d--,o--):(s-=h,i.size+=h)}}for(;d>0&&s>h;){let t=s/d;for(let n=0;n<i;++n){let i=e[n];i.done||(i.size+t>=i.maxSize?(s-=i.maxSize-i.size,i.size=i.maxSize,i.done=!0,d--):(s-=t,i.size+=t))}}}return 0},e.adjust=function(e,t,i){0!==e.length&&0!==i&&(i>0?function(e,t,i){let s=0;for(let i=0;i<=t;++i){let t=e[i];s+=t.maxSize-t.size}let n=0;for(let i=t+1,s=e.length;i<s;++i){let t=e[i];n+=t.size-t.minSize}let a=i=Math.min(i,s,n);for(let i=t;i>=0&&a>0;--i){let t=e[i],s=t.maxSize-t.size;s>=a?(t.sizeHint=t.size+a,a=0):(t.sizeHint=t.size+s,a-=s)}let r=i;for(let i=t+1,s=e.length;i<s&&r>0;++i){let t=e[i],s=t.size-t.minSize;s>=r?(t.sizeHint=t.size-r,r=0):(t.sizeHint=t.size-s,r-=s)}}(e,t,i):function(e,t,i){let s=0;for(let i=t+1,n=e.length;i<n;++i){let t=e[i];s+=t.maxSize-t.size}let n=0;for(let i=0;i<=t;++i){let t=e[i];n+=t.size-t.minSize}let a=i=Math.min(i,s,n);for(let i=t+1,s=e.length;i<s&&a>0;++i){let t=e[i],s=t.maxSize-t.size;s>=a?(t.sizeHint=t.size+a,a=0):(t.sizeHint=t.size+s,a-=s)}let r=i;for(let i=t;i>=0&&r>0;--i){let t=e[i],s=t.size-t.minSize;s>=r?(t.sizeHint=t.size-r,r=0):(t.sizeHint=t.size-s,r-=s)}}(e,t,-i))}}(s||(s={}));class v{constructor(e){this._label="",this._caption="",this._mnemonic=-1,this._icon=void 0,this._iconClass="",this._iconLabel="",this._className="",this._closable=!1,this._changed=new u.Signal(this),this._isDisposed=!1,this.owner=e.owner,void 0!==e.label&&(this._label=e.label),void 0!==e.mnemonic&&(this._mnemonic=e.mnemonic),void 0!==e.icon&&(this._icon=e.icon),void 0!==e.iconClass&&(this._iconClass=e.iconClass),void 0!==e.iconLabel&&(this._iconLabel=e.iconLabel),void 0!==e.caption&&(this._caption=e.caption),void 0!==e.className&&(this._className=e.className),void 0!==e.closable&&(this._closable=e.closable),this._dataset=e.dataset||{}}get changed(){return this._changed}get label(){return this._label}set label(e){this._label!==e&&(this._label=e,this._changed.emit(void 0))}get mnemonic(){return this._mnemonic}set mnemonic(e){this._mnemonic!==e&&(this._mnemonic=e,this._changed.emit(void 0))}get icon(){return this._icon}set icon(e){this._icon!==e&&(this._icon=e,this._changed.emit(void 0))}get iconClass(){return this._iconClass}set iconClass(e){this._iconClass!==e&&(this._iconClass=e,this._changed.emit(void 0))}get iconLabel(){return this._iconLabel}set iconLabel(e){this._iconLabel!==e&&(this._iconLabel=e,this._changed.emit(void 0))}get caption(){return this._caption}set caption(e){this._caption!==e&&(this._caption=e,this._changed.emit(void 0))}get className(){return this._className}set className(e){this._className!==e&&(this._className=e,this._changed.emit(void 0))}get closable(){return this._closable}set closable(e){this._closable!==e&&(this._closable=e,this._changed.emit(void 0))}get dataset(){return this._dataset}set dataset(e){this._dataset!==e&&(this._dataset=e,this._changed.emit(void 0))}get isDisposed(){return this._isDisposed}dispose(){this.isDisposed||(this._isDisposed=!0,u.Signal.clearData(this))}}class M{constructor(e={}){this._flags=0,this._layout=null,this._parent=null,this._disposed=new u.Signal(this),this._hiddenMode=M.HiddenMode.Display,this.node=n.createNode(e),this.addClass("lm-Widget")}dispose(){this.isDisposed||(this.setFlag(M.Flag.IsDisposed),this._disposed.emit(void 0),this.parent?this.parent=null:this.isAttached&&M.detach(this),this._layout&&(this._layout.dispose(),this._layout=null),this.title.dispose(),u.Signal.clearData(this),l.MessageLoop.clearData(this),c.AttachedProperty.clearData(this))}get disposed(){return this._disposed}get isDisposed(){return this.testFlag(M.Flag.IsDisposed)}get isAttached(){return this.testFlag(M.Flag.IsAttached)}get isHidden(){return this.testFlag(M.Flag.IsHidden)}get isVisible(){return this.testFlag(M.Flag.IsVisible)}get title(){return n.titleProperty.get(this)}get id(){return this.node.id}set id(e){this.node.id=e}get dataset(){return this.node.dataset}get hiddenMode(){return this._hiddenMode}set hiddenMode(e){this._hiddenMode!==e&&(this.isHidden&&this._toggleHidden(!1),e==M.HiddenMode.Scale?this.node.style.willChange="transform":this.node.style.willChange="auto",this._hiddenMode=e,this.isHidden&&this._toggleHidden(!0))}get parent(){return this._parent}set parent(e){if(this._parent!==e){if(e&&this.contains(e))throw new Error("Invalid parent widget.");if(this._parent&&!this._parent.isDisposed){let e=new M.ChildMessage("child-removed",this);l.MessageLoop.sendMessage(this._parent,e)}if(this._parent=e,this._parent&&!this._parent.isDisposed){let e=new M.ChildMessage("child-added",this);l.MessageLoop.sendMessage(this._parent,e)}this.isDisposed||l.MessageLoop.sendMessage(this,M.Msg.ParentChanged)}}get layout(){return this._layout}set layout(e){if(this._layout!==e){if(this.testFlag(M.Flag.DisallowLayout))throw new Error("Cannot set widget layout.");if(this._layout)throw new Error("Cannot change widget layout.");if(e.parent)throw new Error("Cannot change layout parent.");this._layout=e,e.parent=this}}*children(){this._layout&&(yield*this._layout)}contains(e){for(let t=e;t;t=t._parent)if(t===this)return!0;return!1}hasClass(e){return this.node.classList.contains(e)}addClass(e){this.node.classList.add(e)}removeClass(e){this.node.classList.remove(e)}toggleClass(e,t){return!0===t?(this.node.classList.add(e),!0):!1===t?(this.node.classList.remove(e),!1):this.node.classList.toggle(e)}update(){l.MessageLoop.postMessage(this,M.Msg.UpdateRequest)}fit(){l.MessageLoop.postMessage(this,M.Msg.FitRequest)}activate(){l.MessageLoop.postMessage(this,M.Msg.ActivateRequest)}close(){l.MessageLoop.sendMessage(this,M.Msg.CloseRequest)}show(){if(this.testFlag(M.Flag.IsHidden)&&(!this.isAttached||this.parent&&!this.parent.isVisible||l.MessageLoop.sendMessage(this,M.Msg.BeforeShow),this.clearFlag(M.Flag.IsHidden),this._toggleHidden(!1),!this.isAttached||this.parent&&!this.parent.isVisible||l.MessageLoop.sendMessage(this,M.Msg.AfterShow),this.parent)){let e=new M.ChildMessage("child-shown",this);l.MessageLoop.sendMessage(this.parent,e)}}hide(){if(!this.testFlag(M.Flag.IsHidden)&&(!this.isAttached||this.parent&&!this.parent.isVisible||l.MessageLoop.sendMessage(this,M.Msg.BeforeHide),this.setFlag(M.Flag.IsHidden),this._toggleHidden(!0),!this.isAttached||this.parent&&!this.parent.isVisible||l.MessageLoop.sendMessage(this,M.Msg.AfterHide),this.parent)){let e=new M.ChildMessage("child-hidden",this);l.MessageLoop.sendMessage(this.parent,e)}}setHidden(e){e?this.hide():this.show()}testFlag(e){return 0!=(this._flags&e)}setFlag(e){this._flags|=e}clearFlag(e){this._flags&=~e}processMessage(e){switch(e.type){case"resize":this.notifyLayout(e),this.onResize(e);break;case"update-request":this.notifyLayout(e),this.onUpdateRequest(e);break;case"fit-request":this.notifyLayout(e),this.onFitRequest(e);break;case"before-show":this.notifyLayout(e),this.onBeforeShow(e);break;case"after-show":this.setFlag(M.Flag.IsVisible),this.notifyLayout(e),this.onAfterShow(e);break;case"before-hide":this.notifyLayout(e),this.onBeforeHide(e);break;case"after-hide":this.clearFlag(M.Flag.IsVisible),this.notifyLayout(e),this.onAfterHide(e);break;case"before-attach":this.notifyLayout(e),this.onBeforeAttach(e);break;case"after-attach":this.isHidden||this.parent&&!this.parent.isVisible||this.setFlag(M.Flag.IsVisible),this.setFlag(M.Flag.IsAttached),this.notifyLayout(e),this.onAfterAttach(e);break;case"before-detach":this.notifyLayout(e),this.onBeforeDetach(e);break;case"after-detach":this.clearFlag(M.Flag.IsVisible),this.clearFlag(M.Flag.IsAttached),this.notifyLayout(e),this.onAfterDetach(e);break;case"activate-request":this.notifyLayout(e),this.onActivateRequest(e);break;case"close-request":this.notifyLayout(e),this.onCloseRequest(e);break;case"child-added":this.notifyLayout(e),this.onChildAdded(e);break;case"child-removed":this.notifyLayout(e),this.onChildRemoved(e);break;default:this.notifyLayout(e)}}notifyLayout(e){this._layout&&this._layout.processParentMessage(e)}onCloseRequest(e){this.parent?this.parent=null:this.isAttached&&M.detach(this)}onResize(e){}onUpdateRequest(e){}onFitRequest(e){}onActivateRequest(e){}onBeforeShow(e){}onAfterShow(e){}onBeforeHide(e){}onAfterHide(e){}onBeforeAttach(e){}onAfterAttach(e){}onBeforeDetach(e){}onAfterDetach(e){}onChildAdded(e){}onChildRemoved(e){}_toggleHidden(e){if(e)switch(this._hiddenMode){case M.HiddenMode.Display:this.addClass("lm-mod-hidden");break;case M.HiddenMode.Scale:this.node.style.transform="scale(0)",this.node.setAttribute("aria-hidden","true");break;case M.HiddenMode.ContentVisibility:this.node.style.contentVisibility="hidden",this.node.style.zIndex="-1"}else switch(this._hiddenMode){case M.HiddenMode.Display:this.removeClass("lm-mod-hidden");break;case M.HiddenMode.Scale:this.node.style.transform="",this.node.removeAttribute("aria-hidden");break;case M.HiddenMode.ContentVisibility:this.node.style.contentVisibility="",this.node.style.zIndex=""}}}!function(e){var t,i,s;(t=e.HiddenMode||(e.HiddenMode={}))[t.Display=0]="Display",t[t.Scale=1]="Scale",t[t.ContentVisibility=2]="ContentVisibility",(i=e.Flag||(e.Flag={}))[i.IsDisposed=1]="IsDisposed",i[i.IsAttached=2]="IsAttached",i[i.IsHidden=4]="IsHidden",i[i.IsVisible=8]="IsVisible",i[i.DisallowLayout=16]="DisallowLayout",(s=e.Msg||(e.Msg={})).BeforeShow=new l.Message("before-show"),s.AfterShow=new l.Message("after-show"),s.BeforeHide=new l.Message("before-hide"),s.AfterHide=new l.Message("after-hide"),s.BeforeAttach=new l.Message("before-attach"),s.AfterAttach=new l.Message("after-attach"),s.BeforeDetach=new l.Message("before-detach"),s.AfterDetach=new l.Message("after-detach"),s.ParentChanged=new l.Message("parent-changed"),s.UpdateRequest=new l.ConflatableMessage("update-request"),s.FitRequest=new l.ConflatableMessage("fit-request"),s.ActivateRequest=new l.ConflatableMessage("activate-request"),s.CloseRequest=new l.ConflatableMessage("close-request");class n extends l.Message{constructor(e,t){super(e),this.child=t}}e.ChildMessage=n;class a extends l.Message{constructor(e,t){super("resize"),this.width=e,this.height=t}}e.ResizeMessage=a,function(e){e.UnknownSize=new e(-1,-1)}(a=e.ResizeMessage||(e.ResizeMessage={})),e.attach=function(t,i,s=null){if(t.parent)throw new Error("Cannot attach a child widget.");if(t.isAttached||t.node.isConnected)throw new Error("Widget is already attached.");if(!i.isConnected)throw new Error("Host is not attached.");l.MessageLoop.sendMessage(t,e.Msg.BeforeAttach),i.insertBefore(t.node,s),l.MessageLoop.sendMessage(t,e.Msg.AfterAttach)},e.detach=function(t){if(t.parent)throw new Error("Cannot detach a child widget.");if(!t.isAttached||!t.node.isConnected)throw new Error("Widget is not attached.");l.MessageLoop.sendMessage(t,e.Msg.BeforeDetach),t.node.parentNode.removeChild(t.node),l.MessageLoop.sendMessage(t,e.Msg.AfterDetach)}}(M||(M={})),function(e){e.titleProperty=new c.AttachedProperty({name:"title",create:e=>new v({owner:e})}),e.createNode=function(e){return e.node||document.createElement(e.tag||"div")}}(n||(n={}));class x{constructor(e={}){this._disposed=!1,this._parent=null,this._fitPolicy=e.fitPolicy||"set-min-size"}dispose(){this._parent=null,this._disposed=!0,u.Signal.clearData(this),c.AttachedProperty.clearData(this)}get isDisposed(){return this._disposed}get parent(){return this._parent}set parent(e){if(this._parent!==e){if(this._parent)throw new Error("Cannot change parent widget.");if(e.layout!==this)throw new Error("Invalid parent widget.");this._parent=e,this.init()}}get fitPolicy(){return this._fitPolicy}set fitPolicy(e){if(this._fitPolicy!==e&&(this._fitPolicy=e,this._parent)){let e=this._parent.node.style;e.minWidth="",e.minHeight="",e.maxWidth="",e.maxHeight="",this._parent.fit()}}processParentMessage(e){switch(e.type){case"resize":this.onResize(e);break;case"update-request":this.onUpdateRequest(e);break;case"fit-request":this.onFitRequest(e);break;case"before-show":this.onBeforeShow(e);break;case"after-show":this.onAfterShow(e);break;case"before-hide":this.onBeforeHide(e);break;case"after-hide":this.onAfterHide(e);break;case"before-attach":this.onBeforeAttach(e);break;case"after-attach":this.onAfterAttach(e);break;case"before-detach":this.onBeforeDetach(e);break;case"after-detach":this.onAfterDetach(e);break;case"child-removed":this.onChildRemoved(e);break;case"child-shown":this.onChildShown(e);break;case"child-hidden":this.onChildHidden(e)}}init(){for(const e of this)e.parent=this.parent}onResize(e){for(const e of this)l.MessageLoop.sendMessage(e,M.ResizeMessage.UnknownSize)}onUpdateRequest(e){for(const e of this)l.MessageLoop.sendMessage(e,M.ResizeMessage.UnknownSize)}onBeforeAttach(e){for(const t of this)l.MessageLoop.sendMessage(t,e)}onAfterAttach(e){for(const t of this)l.MessageLoop.sendMessage(t,e)}onBeforeDetach(e){for(const t of this)l.MessageLoop.sendMessage(t,e)}onAfterDetach(e){for(const t of this)l.MessageLoop.sendMessage(t,e)}onBeforeShow(e){for(const t of this)t.isHidden||l.MessageLoop.sendMessage(t,e)}onAfterShow(e){for(const t of this)t.isHidden||l.MessageLoop.sendMessage(t,e)}onBeforeHide(e){for(const t of this)t.isHidden||l.MessageLoop.sendMessage(t,e)}onAfterHide(e){for(const t of this)t.isHidden||l.MessageLoop.sendMessage(t,e)}onChildRemoved(e){this.removeWidget(e.child)}onFitRequest(e){}onChildShown(e){}onChildHidden(e){}}!function(e){e.getHorizontalAlignment=function(e){return a.horizontalAlignmentProperty.get(e)},e.setHorizontalAlignment=function(e,t){a.horizontalAlignmentProperty.set(e,t)},e.getVerticalAlignment=function(e){return a.verticalAlignmentProperty.get(e)},e.setVerticalAlignment=function(e,t){a.verticalAlignmentProperty.set(e,t)}}(x||(x={}));class y{constructor(e){this._top=NaN,this._left=NaN,this._width=NaN,this._height=NaN,this._minWidth=0,this._minHeight=0,this._maxWidth=1/0,this._maxHeight=1/0,this._disposed=!1,this.widget=e,this.widget.node.style.position="absolute",this.widget.node.style.contain="strict"}dispose(){if(this._disposed)return;this._disposed=!0;let e=this.widget.node.style;e.position="",e.top="",e.left="",e.width="",e.height="",e.contain=""}get minWidth(){return this._minWidth}get minHeight(){return this._minHeight}get maxWidth(){return this._maxWidth}get maxHeight(){return this._maxHeight}get isDisposed(){return this._disposed}get isHidden(){return this.widget.isHidden}get isVisible(){return this.widget.isVisible}get isAttached(){return this.widget.isAttached}fit(){let e=d.ElementExt.sizeLimits(this.widget.node);this._minWidth=e.minWidth,this._minHeight=e.minHeight,this._maxWidth=e.maxWidth,this._maxHeight=e.maxHeight}update(e,t,i,s){let n=Math.max(this._minWidth,Math.min(i,this._maxWidth)),a=Math.max(this._minHeight,Math.min(s,this._maxHeight));if(n<i)switch(x.getHorizontalAlignment(this.widget)){case"left":break;case"center":e+=(i-n)/2;break;case"right":e+=i-n;break;default:throw"unreachable"}if(a<s)switch(x.getVerticalAlignment(this.widget)){case"top":break;case"center":t+=(s-a)/2;break;case"bottom":t+=s-a;break;default:throw"unreachable"}let r=!1,o=this.widget.node.style;if(this._top!==t&&(this._top=t,o.top=`${t}px`),this._left!==e&&(this._left=e,o.left=`${e}px`),this._width!==n&&(r=!0,this._width=n,o.width=`${n}px`),this._height!==a&&(r=!0,this._height=a,o.height=`${a}px`),r){let e=new M.ResizeMessage(n,a);l.MessageLoop.sendMessage(this.widget,e)}}}!function(e){function t(e){e.parent&&e.parent.layout&&e.parent.update()}e.horizontalAlignmentProperty=new c.AttachedProperty({name:"horizontalAlignment",create:()=>"center",changed:t}),e.verticalAlignmentProperty=new c.AttachedProperty({name:"verticalAlignment",create:()=>"top",changed:t})}(a||(a={}));class w extends x{constructor(){super(...arguments),this._widgets=[]}dispose(){for(;this._widgets.length>0;)this._widgets.pop().dispose();super.dispose()}get widgets(){return this._widgets}*[Symbol.iterator](){yield*this._widgets}addWidget(e){this.insertWidget(this._widgets.length,e)}insertWidget(e,t){t.parent=this.parent;let i=this._widgets.indexOf(t),s=Math.max(0,Math.min(e,this._widgets.length));if(-1===i)return o.ArrayExt.insert(this._widgets,s,t),void(this.parent&&this.attachWidget(s,t));s===this._widgets.length&&s--,i!==s&&(o.ArrayExt.move(this._widgets,i,s),this.parent&&this.moveWidget(i,s,t))}removeWidget(e){this.removeWidgetAt(this._widgets.indexOf(e))}removeWidgetAt(e){let t=o.ArrayExt.removeAt(this._widgets,e);t&&this.parent&&this.detachWidget(e,t)}init(){super.init();let e=0;for(const t of this)this.attachWidget(e++,t)}attachWidget(e,t){let i=this.parent.node.children[e];this.parent.isAttached&&l.MessageLoop.sendMessage(t,M.Msg.BeforeAttach),this.parent.node.insertBefore(t.node,i),this.parent.isAttached&&l.MessageLoop.sendMessage(t,M.Msg.AfterAttach)}moveWidget(e,t,i){this.parent.isAttached&&l.MessageLoop.sendMessage(i,M.Msg.BeforeDetach),this.parent.node.removeChild(i.node),this.parent.isAttached&&l.MessageLoop.sendMessage(i,M.Msg.AfterDetach);let s=this.parent.node.children[t];this.parent.isAttached&&l.MessageLoop.sendMessage(i,M.Msg.BeforeAttach),this.parent.node.insertBefore(i.node,s),this.parent.isAttached&&l.MessageLoop.sendMessage(i,M.Msg.AfterAttach)}detachWidget(e,t){this.parent.isAttached&&l.MessageLoop.sendMessage(t,M.Msg.BeforeDetach),this.parent.node.removeChild(t.node),this.parent.isAttached&&l.MessageLoop.sendMessage(t,M.Msg.AfterDetach)}}!function(e){e.clampDimension=function(e){return Math.max(0,Math.floor(e))}}(r||(r={}));var A,C,E,S,I,z,L,T,D,B,k=r;class R extends w{constructor(e){super(),this.widgetOffset=0,this._fixed=0,this._spacing=4,this._dirty=!1,this._hasNormedSizes=!1,this._sizers=[],this._items=[],this._handles=[],this._box=null,this._alignment="start",this._orientation="horizontal",this.renderer=e.renderer,void 0!==e.orientation&&(this._orientation=e.orientation),void 0!==e.alignment&&(this._alignment=e.alignment),void 0!==e.spacing&&(this._spacing=r.clampDimension(e.spacing))}dispose(){for(const e of this._items)e.dispose();this._box=null,this._items.length=0,this._sizers.length=0,this._handles.length=0,super.dispose()}get orientation(){return this._orientation}set orientation(e){this._orientation!==e&&(this._orientation=e,this.parent&&(this.parent.dataset.orientation=e,this.parent.fit()))}get alignment(){return this._alignment}set alignment(e){this._alignment!==e&&(this._alignment=e,this.parent&&(this.parent.dataset.alignment=e,this.parent.update()))}get spacing(){return this._spacing}set spacing(e){e=r.clampDimension(e),this._spacing!==e&&(this._spacing=e,this.parent&&this.parent.fit())}get handles(){return this._handles}absoluteSizes(){return this._sizers.map((e=>e.size))}relativeSizes(){return A.normalize(this._sizers.map((e=>e.size)))}setRelativeSizes(e,t=!0){let i=this._sizers.length,s=e.slice(0,i);for(;s.length<i;)s.push(0);let n=A.normalize(s);for(let e=0;e<i;++e){let t=this._sizers[e];t.sizeHint=n[e],t.size=n[e]}this._hasNormedSizes=!0,t&&this.parent&&this.parent.update()}moveHandle(e,t){let i,n=this._handles[e];if(n&&!n.classList.contains("lm-mod-hidden")&&(i="horizontal"===this._orientation?t-n.offsetLeft:t-n.offsetTop,0!==i)){for(let e of this._sizers)e.size>0&&(e.sizeHint=e.size);s.adjust(this._sizers,e,i),this.parent&&this.parent.update()}}init(){this.parent.dataset.orientation=this.orientation,this.parent.dataset.alignment=this.alignment,super.init()}attachWidget(e,t){let i=new y(t),s=A.createHandle(this.renderer),n=A.averageSize(this._sizers),a=A.createSizer(n);o.ArrayExt.insert(this._items,e,i),o.ArrayExt.insert(this._sizers,e,a),o.ArrayExt.insert(this._handles,e,s),this.parent.isAttached&&l.MessageLoop.sendMessage(t,M.Msg.BeforeAttach),this.parent.node.appendChild(t.node),this.parent.node.appendChild(s),this.parent.isAttached&&l.MessageLoop.sendMessage(t,M.Msg.AfterAttach),this.parent.fit()}moveWidget(e,t,i){o.ArrayExt.move(this._items,e,t),o.ArrayExt.move(this._sizers,e,t),o.ArrayExt.move(this._handles,e,t),this.parent.fit()}detachWidget(e,t){let i=o.ArrayExt.removeAt(this._items,e),s=o.ArrayExt.removeAt(this._handles,e);o.ArrayExt.removeAt(this._sizers,e),this.parent.isAttached&&l.MessageLoop.sendMessage(t,M.Msg.BeforeDetach),this.parent.node.removeChild(t.node),this.parent.node.removeChild(s),this.parent.isAttached&&l.MessageLoop.sendMessage(t,M.Msg.AfterDetach),i.dispose(),this.parent.fit()}onBeforeShow(e){super.onBeforeShow(e),this.parent.update()}onBeforeAttach(e){super.onBeforeAttach(e),this.parent.fit()}onChildShown(e){this.parent.fit()}onChildHidden(e){this.parent.fit()}onResize(e){this.parent.isVisible&&this._update(e.width,e.height)}onUpdateRequest(e){this.parent.isVisible&&this._update(-1,-1)}onFitRequest(e){this.parent.isAttached&&this._fit()}updateItemPosition(e,t,i,s,n,a,r){const o=this._items[e];if(o.isHidden)return;let h=this._handles[e].style;t?(i+=this.widgetOffset,o.update(i,s,r,n),i+=r,h.top=`${s}px`,h.left=`${i}px`,h.width=`${this._spacing}px`,h.height=`${n}px`):(s+=this.widgetOffset,o.update(i,s,a,r),s+=r,h.top=`${s}px`,h.left=`${i}px`,h.width=`${a}px`,h.height=`${this._spacing}px`)}_fit(){let e=0,t=-1;for(let i=0,s=this._items.length;i<s;++i)this._items[i].isHidden?this._handles[i].classList.add("lm-mod-hidden"):(this._handles[i].classList.remove("lm-mod-hidden"),t=i,e++);-1!==t&&this._handles[t].classList.add("lm-mod-hidden"),this._fixed=this._spacing*Math.max(0,e-1)+this.widgetOffset*this._items.length;let i="horizontal"===this._orientation,s=i?this._fixed:0,n=i?0:this._fixed;for(let e=0,t=this._items.length;e<t;++e){let t=this._items[e],a=this._sizers[e];a.size>0&&(a.sizeHint=a.size),t.isHidden?(a.minSize=0,a.maxSize=0):(t.fit(),a.stretch=R.getStretch(t.widget),i?(a.minSize=t.minWidth,a.maxSize=t.maxWidth,s+=t.minWidth,n=Math.max(n,t.minHeight)):(a.minSize=t.minHeight,a.maxSize=t.maxHeight,n+=t.minHeight,s=Math.max(s,t.minWidth)))}let a=this._box=d.ElementExt.boxSizing(this.parent.node);s+=a.horizontalSum,n+=a.verticalSum;let r=this.parent.node.style;r.minWidth=`${s}px`,r.minHeight=`${n}px`,this._dirty=!0,this.parent.parent&&l.MessageLoop.sendMessage(this.parent.parent,M.Msg.FitRequest),this._dirty&&l.MessageLoop.sendMessage(this.parent,M.Msg.UpdateRequest)}_update(e,t){this._dirty=!1;let i=0;for(let e=0,t=this._items.length;e<t;++e)i+=+!this._items[e].isHidden;if(0===i&&0===this.widgetOffset)return;e<0&&(e=this.parent.node.offsetWidth),t<0&&(t=this.parent.node.offsetHeight),this._box||(this._box=d.ElementExt.boxSizing(this.parent.node));let n=this._box.paddingTop,a=this._box.paddingLeft,r=e-this._box.horizontalSum,o=t-this._box.verticalSum,h=0,l=0,c="horizontal"===this._orientation;if(i>0){let e;if(e=c?Math.max(0,r-this._fixed):Math.max(0,o-this._fixed),this._hasNormedSizes){for(let t of this._sizers)t.sizeHint*=e;this._hasNormedSizes=!1}let t=s.calc(this._sizers,e);if(t>0)switch(this._alignment){case"start":break;case"center":h=0,l=t/2;break;case"end":h=0,l=t;break;case"justify":h=t/i,l=0;break;default:throw"unreachable"}}for(let e=0,t=this._items.length;e<t;++e){const t=this._items[e].isHidden?0:this._sizers[e].size+h;this.updateItemPosition(e,c,c?a+l:a,c?n:n+l,o,r,t);const i=this.widgetOffset+(this._handles[e].classList.contains("lm-mod-hidden")?0:this._spacing);c?a+=t+i:n+=t+i}}}!function(e){e.getStretch=function(e){return A.stretchProperty.get(e)},e.setStretch=function(e,t){A.stretchProperty.set(e,t)}}(R||(R={})),function(e){e.stretchProperty=new c.AttachedProperty({name:"stretch",create:()=>0,coerce:(e,t)=>Math.max(0,Math.floor(t)),changed:function(e){e.parent&&e.parent.layout instanceof R&&e.parent.fit()}}),e.createSizer=function(e){let t=new b;return t.sizeHint=Math.floor(e),t},e.createHandle=function(e){let t=e.createHandle();return t.style.position="absolute",t.style.contain="style",t},e.averageSize=function(e){return e.reduce(((e,t)=>e+t.size),0)/e.length||0},e.normalize=function(e){let t=e.length;if(0===t)return[];let i=e.reduce(((e,t)=>e+Math.abs(t)),0);return 0===i?e.map((e=>1/t)):e.map((e=>e/i))}}(A||(A={}));class N extends R{constructor(e){super({...e,orientation:e.orientation||"vertical"}),this._titles=[],this.titleSpace=e.titleSpace||22}get titleSpace(){return this.widgetOffset}set titleSpace(e){e=k.clampDimension(e),this.widgetOffset!==e&&(this.widgetOffset=e,this.parent&&this.parent.fit())}get titles(){return this._titles}dispose(){this.isDisposed||(this._titles.length=0,super.dispose())}updateTitle(e,t){const i=this._titles[e],s=i.classList.contains("lm-mod-expanded"),n=C.createTitle(this.renderer,t.title,s);this._titles[e]=n,this.parent.node.replaceChild(n,i)}insertWidget(e,t){t.id||(t.id=`id-${h.UUID.uuid4()}`),super.insertWidget(e,t)}attachWidget(e,t){const i=C.createTitle(this.renderer,t.title);o.ArrayExt.insert(this._titles,e,i),this.parent.node.appendChild(i),t.node.setAttribute("role","region"),t.node.setAttribute("aria-labelledby",i.id),super.attachWidget(e,t)}moveWidget(e,t,i){o.ArrayExt.move(this._titles,e,t),super.moveWidget(e,t,i)}detachWidget(e,t){const i=o.ArrayExt.removeAt(this._titles,e);this.parent.node.removeChild(i),super.detachWidget(e,t)}updateItemPosition(e,t,i,s,n,a,r){const o=this._titles[e].style;o.top=`${s}px`,o.left=`${i}px`,o.height=`${this.widgetOffset}px`,o.width=t?`${n}px`:`${a}px`,super.updateItemPosition(e,t,i,s,n,a,r)}}!function(e){e.createTitle=function(e,t,i=!0){const s=e.createSectionTitle(t);return s.style.position="absolute",s.style.contain="strict",s.setAttribute("aria-label",`${t.label} Section`),s.setAttribute("aria-expanded",i?"true":"false"),s.setAttribute("aria-controls",t.owner.id),i&&s.classList.add("lm-mod-expanded"),s}}(C||(C={}));class P extends M{constructor(e={}){super(),this.addClass("lm-Panel"),this.layout=E.createLayout(e)}get widgets(){return this.layout.widgets}addWidget(e){this.layout.addWidget(e)}insertWidget(e,t){this.layout.insertWidget(e,t)}}!function(e){e.createLayout=function(e){return e.layout||new w}}(E||(E={}));class H extends P{constructor(e={}){super({layout:S.createLayout(e)}),this._handleMoved=new u.Signal(this),this._pressData=null,this.addClass("lm-SplitPanel")}dispose(){this._releaseMouse(),super.dispose()}get orientation(){return this.layout.orientation}set orientation(e){this.layout.orientation=e}get alignment(){return this.layout.alignment}set alignment(e){this.layout.alignment=e}get spacing(){return this.layout.spacing}set spacing(e){this.layout.spacing=e}get renderer(){return this.layout.renderer}get handleMoved(){return this._handleMoved}get handles(){return this.layout.handles}relativeSizes(){return this.layout.relativeSizes()}setRelativeSizes(e,t=!0){this.layout.setRelativeSizes(e,t)}handleEvent(e){switch(e.type){case"pointerdown":this._evtPointerDown(e);break;case"pointermove":this._evtPointerMove(e);break;case"pointerup":this._evtPointerUp(e);break;case"keydown":this._evtKeyDown(e);break;case"contextmenu":e.preventDefault(),e.stopPropagation()}}onBeforeAttach(e){this.node.addEventListener("pointerdown",this)}onAfterDetach(e){this.node.removeEventListener("pointerdown",this),this._releaseMouse()}onChildAdded(e){e.child.addClass("lm-SplitPanel-child"),this._releaseMouse()}onChildRemoved(e){e.child.removeClass("lm-SplitPanel-child"),this._releaseMouse()}_evtKeyDown(e){this._pressData&&(e.preventDefault(),e.stopPropagation()),27===e.keyCode&&this._releaseMouse()}_evtPointerDown(e){if(0!==e.button)return;let t,i=this.layout,s=o.ArrayExt.findFirstIndex(i.handles,(t=>t.contains(e.target)));if(-1===s)return;e.preventDefault(),e.stopPropagation(),document.addEventListener("pointerup",this,!0),document.addEventListener("pointermove",this,!0),document.addEventListener("keydown",this,!0),document.addEventListener("contextmenu",this,!0);let n=i.handles[s],a=n.getBoundingClientRect();t="horizontal"===i.orientation?e.clientX-a.left:e.clientY-a.top;let r=window.getComputedStyle(n),h=m.Drag.overrideCursor(r.cursor);this._pressData={index:s,delta:t,override:h}}_evtPointerMove(e){let t;e.preventDefault(),e.stopPropagation();let i=this.layout,s=this.node.getBoundingClientRect();t="horizontal"===i.orientation?e.clientX-s.left-this._pressData.delta:e.clientY-s.top-this._pressData.delta,i.moveHandle(this._pressData.index,t)}_evtPointerUp(e){0===e.button&&(e.preventDefault(),e.stopPropagation(),this._releaseMouse())}_releaseMouse(){this._pressData&&(this._pressData.override.dispose(),this._pressData=null,this._handleMoved.emit(),document.removeEventListener("keydown",this,!0),document.removeEventListener("pointerup",this,!0),document.removeEventListener("pointermove",this,!0),document.removeEventListener("contextmenu",this,!0))}}!function(e){class t{createHandle(){let e=document.createElement("div");return e.className="lm-SplitPanel-handle",e}}e.Renderer=t,e.defaultRenderer=new t,e.getStretch=function(e){return R.getStretch(e)},e.setStretch=function(e,t){R.setStretch(e,t)}}(H||(H={})),function(e){e.createLayout=function(e){return e.layout||new R({renderer:e.renderer||H.defaultRenderer,orientation:e.orientation,alignment:e.alignment,spacing:e.spacing})}}(S||(S={}));class W extends H{constructor(e={}){super({...e,layout:I.createLayout(e)}),this._widgetSizesCache=new WeakMap,this._expansionToggled=new u.Signal(this),this.addClass("lm-AccordionPanel")}get renderer(){return this.layout.renderer}get titleSpace(){return this.layout.titleSpace}set titleSpace(e){this.layout.titleSpace=e}get titles(){return this.layout.titles}get expansionToggled(){return this._expansionToggled}addWidget(e){super.addWidget(e),e.title.changed.connect(this._onTitleChanged,this)}collapse(e){const t=this.layout.widgets[e];t&&!t.isHidden&&this._toggleExpansion(e)}expand(e){const t=this.layout.widgets[e];t&&t.isHidden&&this._toggleExpansion(e)}insertWidget(e,t){super.insertWidget(e,t),t.title.changed.connect(this._onTitleChanged,this)}handleEvent(e){switch(super.handleEvent(e),e.type){case"click":this._evtClick(e);break;case"keydown":this._eventKeyDown(e)}}onBeforeAttach(e){this.node.addEventListener("click",this),this.node.addEventListener("keydown",this),super.onBeforeAttach(e)}onAfterDetach(e){super.onAfterDetach(e),this.node.removeEventListener("click",this),this.node.removeEventListener("keydown",this)}_onTitleChanged(e){const t=o.ArrayExt.findFirstIndex(this.widgets,(t=>t.contains(e.owner)));t>=0&&(this.layout.updateTitle(t,e.owner),this.update())}_computeWidgetSize(e){const t=this.layout,i=t.widgets[e];if(!i)return;const s=i.isHidden,n=t.absoluteSizes(),a=(s?-1:1)*this.spacing,r=n.reduce(((e,t)=>e+t));let o=[...n];if(s){const t=this._widgetSizesCache.get(i);if(!t)return;o[e]+=t;const s=o.map((e=>e-t>0)).lastIndexOf(!0);-1===s?o.forEach(((i,s)=>{s!==e&&(o[s]-=n[s]/r*(t-a))})):o[s]-=t-a}else{const t=n[e];this._widgetSizesCache.set(i,t),o[e]=0;const s=o.map((e=>e>0)).lastIndexOf(!0);if(-1===s)return;o[s]=n[s]+t+a}return o.map((e=>e/(r+a)))}_evtClick(e){const t=e.target;if(t){const i=o.ArrayExt.findFirstIndex(this.titles,(e=>e.contains(t)));i>=0&&(e.preventDefault(),e.stopPropagation(),this._toggleExpansion(i))}}_eventKeyDown(e){if(e.defaultPrevented)return;const t=e.target;let i=!1;if(t){const s=o.ArrayExt.findFirstIndex(this.titles,(e=>e.contains(t)));if(s>=0){const n=e.keyCode.toString();if(e.key.match(/Space|Enter/)||n.match(/13|32/))t.click(),i=!0;else if("horizontal"===this.orientation?e.key.match(/ArrowLeft|ArrowRight/)||n.match(/37|39/):e.key.match(/ArrowUp|ArrowDown/)||n.match(/38|40/)){const t=e.key.match(/ArrowLeft|ArrowUp/)||n.match(/37|38/)?-1:1,a=this.titles.length,r=(s+a+t)%a;this.titles[r].focus(),i=!0}else"End"===e.key||"35"===n?(this.titles[this.titles.length-1].focus(),i=!0):"Home"!==e.key&&"36"!==n||(this.titles[0].focus(),i=!0)}i&&e.preventDefault()}}_toggleExpansion(e){const t=this.titles[e],i=this.layout.widgets[e],s=this._computeWidgetSize(e);s&&this.setRelativeSizes(s,!1),i.isHidden?(t.classList.add("lm-mod-expanded"),t.setAttribute("aria-expanded","true"),i.show()):(t.classList.remove("lm-mod-expanded"),t.setAttribute("aria-expanded","false"),i.hide()),this._expansionToggled.emit(e)}}!function(e){class t extends H.Renderer{constructor(){super(),this.titleClassName="lm-AccordionPanel-title",this._titleID=0,this._titleKeys=new WeakMap,this._uuid=++t._nInstance}createCollapseIcon(e){return document.createElement("span")}createSectionTitle(e){const t=document.createElement("h3");t.setAttribute("tabindex","0"),t.id=this.createTitleKey(e),t.className=this.titleClassName;for(const i in e.dataset)t.dataset[i]=e.dataset[i];t.appendChild(this.createCollapseIcon(e)).className="lm-AccordionPanel-titleCollapser";const i=t.appendChild(document.createElement("span"));return i.className="lm-AccordionPanel-titleLabel",i.textContent=e.label,i.title=e.caption||e.label,t}createTitleKey(e){let t=this._titleKeys.get(e);return void 0===t&&(t=`title-key-${this._uuid}-${this._titleID++}`,this._titleKeys.set(e,t)),t}}t._nInstance=0,e.Renderer=t,e.defaultRenderer=new t}(W||(W={})),function(e){e.createLayout=function(e){return e.layout||new N({renderer:e.renderer||W.defaultRenderer,orientation:e.orientation,alignment:e.alignment,spacing:e.spacing,titleSpace:e.titleSpace})}}(I||(I={}));class q extends w{constructor(e={}){super(),this._fixed=0,this._spacing=4,this._dirty=!1,this._sizers=[],this._items=[],this._box=null,this._alignment="start",this._direction="top-to-bottom",void 0!==e.direction&&(this._direction=e.direction),void 0!==e.alignment&&(this._alignment=e.alignment),void 0!==e.spacing&&(this._spacing=k.clampDimension(e.spacing))}dispose(){for(const e of this._items)e.dispose();this._box=null,this._items.length=0,this._sizers.length=0,super.dispose()}get direction(){return this._direction}set direction(e){this._direction!==e&&(this._direction=e,this.parent&&(this.parent.dataset.direction=e,this.parent.fit()))}get alignment(){return this._alignment}set alignment(e){this._alignment!==e&&(this._alignment=e,this.parent&&(this.parent.dataset.alignment=e,this.parent.update()))}get spacing(){return this._spacing}set spacing(e){e=k.clampDimension(e),this._spacing!==e&&(this._spacing=e,this.parent&&this.parent.fit())}init(){this.parent.dataset.direction=this.direction,this.parent.dataset.alignment=this.alignment,super.init()}attachWidget(e,t){o.ArrayExt.insert(this._items,e,new y(t)),o.ArrayExt.insert(this._sizers,e,new b),this.parent.isAttached&&l.MessageLoop.sendMessage(t,M.Msg.BeforeAttach),this.parent.node.appendChild(t.node),this.parent.isAttached&&l.MessageLoop.sendMessage(t,M.Msg.AfterAttach),this.parent.fit()}moveWidget(e,t,i){o.ArrayExt.move(this._items,e,t),o.ArrayExt.move(this._sizers,e,t),this.parent.update()}detachWidget(e,t){let i=o.ArrayExt.removeAt(this._items,e);o.ArrayExt.removeAt(this._sizers,e),this.parent.isAttached&&l.MessageLoop.sendMessage(t,M.Msg.BeforeDetach),this.parent.node.removeChild(t.node),this.parent.isAttached&&l.MessageLoop.sendMessage(t,M.Msg.AfterDetach),i.dispose(),this.parent.fit()}onBeforeShow(e){super.onBeforeShow(e),this.parent.update()}onBeforeAttach(e){super.onBeforeAttach(e),this.parent.fit()}onChildShown(e){this.parent.fit()}onChildHidden(e){this.parent.fit()}onResize(e){this.parent.isVisible&&this._update(e.width,e.height)}onUpdateRequest(e){this.parent.isVisible&&this._update(-1,-1)}onFitRequest(e){this.parent.isAttached&&this._fit()}_fit(){let e=0;for(let t=0,i=this._items.length;t<i;++t)e+=+!this._items[t].isHidden;this._fixed=this._spacing*Math.max(0,e-1);let t=z.isHorizontal(this._direction),i=t?this._fixed:0,s=t?0:this._fixed;for(let e=0,n=this._items.length;e<n;++e){let n=this._items[e],a=this._sizers[e];n.isHidden?(a.minSize=0,a.maxSize=0):(n.fit(),a.sizeHint=q.getSizeBasis(n.widget),a.stretch=q.getStretch(n.widget),t?(a.minSize=n.minWidth,a.maxSize=n.maxWidth,i+=n.minWidth,s=Math.max(s,n.minHeight)):(a.minSize=n.minHeight,a.maxSize=n.maxHeight,s+=n.minHeight,i=Math.max(i,n.minWidth)))}let n=this._box=d.ElementExt.boxSizing(this.parent.node);i+=n.horizontalSum,s+=n.verticalSum;let a=this.parent.node.style;a.minWidth=`${i}px`,a.minHeight=`${s}px`,this._dirty=!0,this.parent.parent&&l.MessageLoop.sendMessage(this.parent.parent,M.Msg.FitRequest),this._dirty&&l.MessageLoop.sendMessage(this.parent,M.Msg.UpdateRequest)}_update(e,t){this._dirty=!1;let i=0;for(let e=0,t=this._items.length;e<t;++e)i+=+!this._items[e].isHidden;if(0===i)return;e<0&&(e=this.parent.node.offsetWidth),t<0&&(t=this.parent.node.offsetHeight),this._box||(this._box=d.ElementExt.boxSizing(this.parent.node));let n,a=this._box.paddingTop,r=this._box.paddingLeft,o=e-this._box.horizontalSum,h=t-this._box.verticalSum;switch(this._direction){case"left-to-right":n=s.calc(this._sizers,Math.max(0,o-this._fixed));break;case"top-to-bottom":n=s.calc(this._sizers,Math.max(0,h-this._fixed));break;case"right-to-left":n=s.calc(this._sizers,Math.max(0,o-this._fixed)),r+=o;break;case"bottom-to-top":n=s.calc(this._sizers,Math.max(0,h-this._fixed)),a+=h;break;default:throw"unreachable"}let l=0,c=0;if(n>0)switch(this._alignment){case"start":break;case"center":l=0,c=n/2;break;case"end":l=0,c=n;break;case"justify":l=n/i,c=0;break;default:throw"unreachable"}for(let e=0,t=this._items.length;e<t;++e){let t=this._items[e];if(t.isHidden)continue;let i=this._sizers[e].size;switch(this._direction){case"left-to-right":t.update(r+c,a,i+l,h),r+=i+l+this._spacing;break;case"top-to-bottom":t.update(r,a+c,o,i+l),a+=i+l+this._spacing;break;case"right-to-left":t.update(r-c-i-l,a,i+l,h),r-=i+l+this._spacing;break;case"bottom-to-top":t.update(r,a-c-i-l,o,i+l),a-=i+l+this._spacing;break;default:throw"unreachable"}}}}!function(e){e.getStretch=function(e){return z.stretchProperty.get(e)},e.setStretch=function(e,t){z.stretchProperty.set(e,t)},e.getSizeBasis=function(e){return z.sizeBasisProperty.get(e)},e.setSizeBasis=function(e,t){z.sizeBasisProperty.set(e,t)}}(q||(q={})),function(e){function t(e){e.parent&&e.parent.layout instanceof q&&e.parent.fit()}e.stretchProperty=new c.AttachedProperty({name:"stretch",create:()=>0,coerce:(e,t)=>Math.max(0,Math.floor(t)),changed:t}),e.sizeBasisProperty=new c.AttachedProperty({name:"sizeBasis",create:()=>0,coerce:(e,t)=>Math.max(0,Math.floor(t)),changed:t}),e.isHorizontal=function(e){return"left-to-right"===e||"right-to-left"===e},e.clampSpacing=function(e){return Math.max(0,Math.floor(e))}}(z||(z={}));class F extends P{constructor(e={}){super({layout:L.createLayout(e)}),this.addClass("lm-BoxPanel")}get direction(){return this.layout.direction}set direction(e){this.layout.direction=e}get alignment(){return this.layout.alignment}set alignment(e){this.layout.alignment=e}get spacing(){return this.layout.spacing}set spacing(e){this.layout.spacing=e}onChildAdded(e){e.child.addClass("lm-BoxPanel-child")}onChildRemoved(e){e.child.removeClass("lm-BoxPanel-child")}}!function(e){e.getStretch=function(e){return q.getStretch(e)},e.setStretch=function(e,t){q.setStretch(e,t)},e.getSizeBasis=function(e){return q.getSizeBasis(e)},e.setSizeBasis=function(e,t){q.setSizeBasis(e,t)}}(F||(F={})),function(e){e.createLayout=function(e){return e.layout||new q(e)}}(L||(L={}));class O extends M{constructor(e){super({node:T.createNode()}),this._activeIndex=-1,this._items=[],this._results=null,this.addClass("lm-CommandPalette"),this.setFlag(M.Flag.DisallowLayout),this.commands=e.commands,this.renderer=e.renderer||O.defaultRenderer,this.commands.commandChanged.connect(this._onGenericChange,this),this.commands.keyBindingChanged.connect(this._onGenericChange,this)}dispose(){this._items.length=0,this._results=null,super.dispose()}get searchNode(){return this.node.getElementsByClassName("lm-CommandPalette-search")[0]}get inputNode(){return this.node.getElementsByClassName("lm-CommandPalette-input")[0]}get contentNode(){return this.node.getElementsByClassName("lm-CommandPalette-content")[0]}get items(){return this._items}addItem(e){let t=T.createItem(this.commands,e);return this._items.push(t),this.refresh(),t}addItems(e){const t=e.map((e=>T.createItem(this.commands,e)));return t.forEach((e=>this._items.push(e))),this.refresh(),t}removeItem(e){this.removeItemAt(this._items.indexOf(e))}removeItemAt(e){o.ArrayExt.removeAt(this._items,e)&&this.refresh()}clearItems(){0!==this._items.length&&(this._items.length=0,this.refresh())}refresh(){this._results=null,""!==this.inputNode.value?this.node.getElementsByClassName("lm-close-icon")[0].style.display="inherit":this.node.getElementsByClassName("lm-close-icon")[0].style.display="none",this.update()}handleEvent(e){switch(e.type){case"click":this._evtClick(e);break;case"keydown":this._evtKeyDown(e);break;case"input":this.refresh();break;case"focus":case"blur":this._toggleFocused()}}onBeforeAttach(e){this.node.addEventListener("click",this),this.node.addEventListener("keydown",this),this.node.addEventListener("input",this),this.node.addEventListener("focus",this,!0),this.node.addEventListener("blur",this,!0)}onAfterDetach(e){this.node.removeEventListener("click",this),this.node.removeEventListener("keydown",this),this.node.removeEventListener("input",this),this.node.removeEventListener("focus",this,!0),this.node.removeEventListener("blur",this,!0)}onAfterShow(e){this.update(),super.onAfterShow(e)}onActivateRequest(e){if(this.isAttached){let e=this.inputNode;e.focus(),e.select()}}onUpdateRequest(e){if(this.isHidden)return;let t=this.inputNode.value,i=this.contentNode,s=this._results;if(s||(s=this._results=T.search(this._items,t),this._activeIndex=t?o.ArrayExt.findFirstIndex(s,T.canActivate):-1),!t&&0===s.length)return void p.VirtualDOM.render(null,i);if(t&&0===s.length){let e=this.renderer.renderEmptyMessage({query:t});return void p.VirtualDOM.render(e,i)}let n=this.renderer,a=this._activeIndex,r=new Array(s.length);for(let e=0,t=s.length;e<t;++e){let t=s[e];if("header"===t.type){let i=t.indices,s=t.category;r[e]=n.renderHeader({category:s,indices:i})}else{let i=t.item,s=t.indices,o=e===a;r[e]=n.renderItem({item:i,indices:s,active:o})}}if(p.VirtualDOM.render(r,i),a<0||a>=s.length)i.scrollTop=0;else{let e=i.children[a];d.ElementExt.scrollIntoViewIfNeeded(i,e)}}_evtClick(e){if(0!==e.button)return;if(e.target.classList.contains("lm-close-icon"))return this.inputNode.value="",void this.refresh();let t=o.ArrayExt.findFirstIndex(this.contentNode.children,(t=>t.contains(e.target)));-1!==t&&(e.preventDefault(),e.stopPropagation(),this._execute(t))}_evtKeyDown(e){if(!(e.altKey||e.ctrlKey||e.metaKey||e.shiftKey))switch(e.keyCode){case 13:e.preventDefault(),e.stopPropagation(),this._execute(this._activeIndex);break;case 38:e.preventDefault(),e.stopPropagation(),this._activatePreviousItem();break;case 40:e.preventDefault(),e.stopPropagation(),this._activateNextItem()}}_activateNextItem(){if(!this._results||0===this._results.length)return;let e=this._activeIndex,t=this._results.length,i=e<t-1?e+1:0,s=0===i?t-1:i-1;this._activeIndex=o.ArrayExt.findFirstIndex(this._results,T.canActivate,i,s),this.update()}_activatePreviousItem(){if(!this._results||0===this._results.length)return;let e=this._activeIndex,t=this._results.length,i=e<=0?t-1:e-1,s=i===t-1?0:i+1;this._activeIndex=o.ArrayExt.findLastIndex(this._results,T.canActivate,i,s),this.update()}_execute(e){if(!this._results)return;let t=this._results[e];if(t){if("header"===t.type){let e=this.inputNode;return e.value=`${t.category.toLowerCase()} `,e.focus(),void this.refresh()}t.item.isEnabled&&(this.commands.execute(t.item.command,t.item.args),this.inputNode.value="",this.refresh())}}_toggleFocused(){let e=document.activeElement===this.inputNode;this.toggleClass("lm-mod-focused",e)}_onGenericChange(){this.refresh()}}!function(e){class t{renderHeader(e){let t=this.formatHeader(e);return p.h.li({className:"lm-CommandPalette-header"},t)}renderItem(e){let t=this.createItemClass(e),i=this.createItemDataset(e);return e.item.isToggleable?p.h.li({className:t,dataset:i,role:"menuitemcheckbox","aria-checked":`${e.item.isToggled}`},this.renderItemIcon(e),this.renderItemContent(e),this.renderItemShortcut(e)):p.h.li({className:t,dataset:i,role:"menuitem"},this.renderItemIcon(e),this.renderItemContent(e),this.renderItemShortcut(e))}renderEmptyMessage(e){let t=this.formatEmptyMessage(e);return p.h.li({className:"lm-CommandPalette-emptyMessage"},t)}renderItemIcon(e){let t=this.createIconClass(e);return p.h.div({className:t},e.item.icon,e.item.iconLabel)}renderItemContent(e){return p.h.div({className:"lm-CommandPalette-itemContent"},this.renderItemLabel(e),this.renderItemCaption(e))}renderItemLabel(e){let t=this.formatItemLabel(e);return p.h.div({className:"lm-CommandPalette-itemLabel"},t)}renderItemCaption(e){let t=this.formatItemCaption(e);return p.h.div({className:"lm-CommandPalette-itemCaption"},t)}renderItemShortcut(e){let t=this.formatItemShortcut(e);return p.h.div({className:"lm-CommandPalette-itemShortcut"},t)}createItemClass(e){let t="lm-CommandPalette-item";e.item.isEnabled||(t+=" lm-mod-disabled"),e.item.isToggled&&(t+=" lm-mod-toggled"),e.active&&(t+=" lm-mod-active");let i=e.item.className;return i&&(t+=` ${i}`),t}createItemDataset(e){return{...e.item.dataset,command:e.item.command}}createIconClass(e){let t="lm-CommandPalette-itemIcon",i=e.item.iconClass;return i?`${t} ${i}`:t}formatHeader(e){return e.indices&&0!==e.indices.length?o.StringExt.highlight(e.category,e.indices,p.h.mark):e.category}formatEmptyMessage(e){return`No commands found that match '${e.query}'`}formatItemShortcut(e){let t=e.item.keyBinding;return t?g.CommandRegistry.formatKeystroke(t.keys):null}formatItemLabel(e){return e.indices&&0!==e.indices.length?o.StringExt.highlight(e.item.label,e.indices,p.h.mark):e.item.label}formatItemCaption(e){return e.item.caption}}e.Renderer=t,e.defaultRenderer=new t}(O||(O={})),function(e){function t(e,t){let i=e.category.toLowerCase(),s=`${i} ${e.label.toLowerCase()}`,n=1/0,a=null,r=/\b\w/g;for(;;){let e=r.exec(s);if(!e)break;let i=o.StringExt.matchSumOfDeltas(s,t,e.index);if(!i)break;i.score<=n&&(n=i.score,a=i.indices)}if(!a||n===1/0)return null;let h=i.length+1,d=o.ArrayExt.lowerBound(a,h,((e,t)=>e-t)),l=a.slice(0,d),c=a.slice(d);for(let e=0,t=c.length;e<t;++e)c[e]-=h;return 0===l.length?{matchType:0,categoryIndices:null,labelIndices:c,score:n,item:e}:0===c.length?{matchType:1,categoryIndices:l,labelIndices:null,score:n,item:e}:{matchType:2,categoryIndices:l,labelIndices:c,score:n,item:e}}function i(e,t){let i=e.matchType-t.matchType;if(0!==i)return i;let s=e.score-t.score;if(0!==s)return s;let n=0,a=0;switch(e.matchType){case 0:n=e.labelIndices[0],a=t.labelIndices[0];break;case 1:case 2:n=e.categoryIndices[0],a=t.categoryIndices[0]}if(n!==a)return n-a;let r=e.item.category.localeCompare(t.item.category);if(0!==r)return r;let o=e.item.rank,h=t.item.rank;return o!==h?o<h?-1:1:e.item.label.localeCompare(t.item.label)}e.createNode=function(){let e=document.createElement("div"),t=document.createElement("div"),i=document.createElement("div"),s=document.createElement("input"),n=document.createElement("ul"),a=document.createElement("button");return t.className="lm-CommandPalette-search",i.className="lm-CommandPalette-wrapper",s.className="lm-CommandPalette-input",a.className="lm-close-icon",n.className="lm-CommandPalette-content",n.setAttribute("role","menu"),s.spellcheck=!1,i.appendChild(s),i.appendChild(a),t.appendChild(i),e.appendChild(t),e.appendChild(n),e},e.createItem=function(e,t){return new s(e,t)},e.search=function(e,s){let n=function(e,i){i=i.replace(/\s+/g,"").toLowerCase();let s=[];for(let n=0,a=e.length;n<a;++n){let a=e[n];if(!a.isVisible)continue;if(!i){s.push({matchType:3,categoryIndices:null,labelIndices:null,score:0,item:a});continue}let r=t(a,i);r&&(a.isEnabled||(r.score+=1e3),s.push(r))}return s}(e,s);return n.sort(i),function(e){let t=new Array(e.length);o.ArrayExt.fill(t,!1);let i=[];for(let s=0,n=e.length;s<n;++s){if(t[s])continue;let{item:a,categoryIndices:r}=e[s],o=a.category;i.push({type:"header",category:o,indices:r});for(let a=s;a<n;++a){if(t[a])continue;let{item:s,labelIndices:n}=e[a];s.category===o&&(i.push({type:"item",item:s,indices:n}),t[a]=!0)}}return i}(n)},e.canActivate=function(e){return"item"===e.type&&e.item.isEnabled};class s{constructor(e,t){this._commands=e,this.category=t.category.trim().replace(/\s+/g," "),this.command=t.command,this.args=t.args||h.JSONExt.emptyObject,this.rank=void 0!==t.rank?t.rank:1/0}get label(){return this._commands.label(this.command,this.args)}get icon(){return this._commands.icon(this.command,this.args)}get iconClass(){return this._commands.iconClass(this.command,this.args)}get iconLabel(){return this._commands.iconLabel(this.command,this.args)}get caption(){return this._commands.caption(this.command,this.args)}get className(){return this._commands.className(this.command,this.args)}get dataset(){return this._commands.dataset(this.command,this.args)}get isEnabled(){return this._commands.isEnabled(this.command,this.args)}get isToggled(){return this._commands.isToggled(this.command,this.args)}get isToggleable(){return this._commands.isToggleable(this.command,this.args)}get isVisible(){return this._commands.isVisible(this.command,this.args)}get keyBinding(){let{command:e,args:t}=this;return o.ArrayExt.findLastValue(this._commands.keyBindings,(i=>i.command===e&&h.JSONExt.deepEqual(i.args,t)))||null}}}(T||(T={}));class $ extends M{constructor(e){super({node:D.createNode()}),this._childIndex=-1,this._activeIndex=-1,this._openTimerID=0,this._closeTimerID=0,this._items=[],this._childMenu=null,this._parentMenu=null,this._aboutToClose=new u.Signal(this),this._menuRequested=new u.Signal(this),this.addClass("lm-Menu"),this.setFlag(M.Flag.DisallowLayout),this.commands=e.commands,this.renderer=e.renderer||$.defaultRenderer}dispose(){this.close(),this._items.length=0,super.dispose()}get aboutToClose(){return this._aboutToClose}get menuRequested(){return this._menuRequested}get parentMenu(){return this._parentMenu}get childMenu(){return this._childMenu}get rootMenu(){let e=this;for(;e._parentMenu;)e=e._parentMenu;return e}get leafMenu(){let e=this;for(;e._childMenu;)e=e._childMenu;return e}get contentNode(){return this.node.getElementsByClassName("lm-Menu-content")[0]}get activeItem(){return this._items[this._activeIndex]||null}set activeItem(e){this.activeIndex=e?this._items.indexOf(e):-1}get activeIndex(){return this._activeIndex}set activeIndex(e){(e<0||e>=this._items.length)&&(e=-1),-1===e||D.canActivate(this._items[e])||(e=-1),this._activeIndex!==e&&(this._activeIndex=e,this._activeIndex>=0&&this.contentNode.childNodes[this._activeIndex]&&this.contentNode.childNodes[this._activeIndex].focus(),this.update())}get items(){return this._items}activateNextItem(){let e=this._items.length,t=this._activeIndex,i=t<e-1?t+1:0,s=0===i?e-1:i-1;this.activeIndex=o.ArrayExt.findFirstIndex(this._items,D.canActivate,i,s)}activatePreviousItem(){let e=this._items.length,t=this._activeIndex,i=t<=0?e-1:t-1,s=i===e-1?0:i+1;this.activeIndex=o.ArrayExt.findLastIndex(this._items,D.canActivate,i,s)}triggerActiveItem(){if(!this.isAttached)return;let e=this.activeItem;if(!e)return;if(this._cancelOpenTimer(),this._cancelCloseTimer(),"submenu"===e.type)return void this._openChildMenu(!0);this.rootMenu.close();let{command:t,args:i}=e;this.commands.isEnabled(t,i)?this.commands.execute(t,i):console.log(`Command '${t}' is disabled.`)}addItem(e){return this.insertItem(this._items.length,e)}insertItem(e,t){this.isAttached&&this.close(),this.activeIndex=-1;let i=Math.max(0,Math.min(e,this._items.length)),s=D.createItem(this,t);return o.ArrayExt.insert(this._items,i,s),this.update(),s}removeItem(e){this.removeItemAt(this._items.indexOf(e))}removeItemAt(e){this.isAttached&&this.close(),this.activeIndex=-1,o.ArrayExt.removeAt(this._items,e)&&this.update()}clearItems(){this.isAttached&&this.close(),this.activeIndex=-1,0!==this._items.length&&(this._items.length=0,this.update())}open(e,t,i={}){if(this.isAttached)return;let s=i.forceX||!1,n=i.forceY||!1;D.openRootMenu(this,e,t,s,n),this.activate()}handleEvent(e){switch(e.type){case"keydown":this._evtKeyDown(e);break;case"mouseup":this._evtMouseUp(e);break;case"mousemove":this._evtMouseMove(e);break;case"mouseenter":this._evtMouseEnter(e);break;case"mouseleave":this._evtMouseLeave(e);break;case"mousedown":this._evtMouseDown(e);break;case"contextmenu":e.preventDefault(),e.stopPropagation()}}onBeforeAttach(e){this.node.addEventListener("keydown",this),this.node.addEventListener("mouseup",this),this.node.addEventListener("mousemove",this),this.node.addEventListener("mouseenter",this),this.node.addEventListener("mouseleave",this),this.node.addEventListener("contextmenu",this),document.addEventListener("mousedown",this,!0)}onAfterDetach(e){this.node.removeEventListener("keydown",this),this.node.removeEventListener("mouseup",this),this.node.removeEventListener("mousemove",this),this.node.removeEventListener("mouseenter",this),this.node.removeEventListener("mouseleave",this),this.node.removeEventListener("contextmenu",this),document.removeEventListener("mousedown",this,!0)}onActivateRequest(e){this.isAttached&&this.node.focus()}onUpdateRequest(e){let t=this._items,i=this.renderer,s=this._activeIndex,n=D.computeCollapsed(t),a=new Array(t.length);for(let e=0,r=t.length;e<r;++e){let r=t[e],o=e===s,h=n[e];a[e]=i.renderItem({item:r,active:o,collapsed:h,onfocus:()=>{this.activeIndex=e}})}p.VirtualDOM.render(a,this.contentNode)}onCloseRequest(e){this._cancelOpenTimer(),this._cancelCloseTimer(),this.activeIndex=-1;let t=this._childMenu;t&&(this._childIndex=-1,this._childMenu=null,t._parentMenu=null,t.close());let i=this._parentMenu;i&&(this._parentMenu=null,i._childIndex=-1,i._childMenu=null,i.activate()),this.isAttached&&this._aboutToClose.emit(void 0),super.onCloseRequest(e)}_evtKeyDown(e){e.preventDefault(),e.stopPropagation();let t=e.keyCode;if(13===t)return void this.triggerActiveItem();if(27===t)return void this.close();if(37===t)return void(this._parentMenu?this.close():this._menuRequested.emit("previous"));if(38===t)return void this.activatePreviousItem();if(39===t){let e=this.activeItem;return void(e&&"submenu"===e.type?this.triggerActiveItem():this.rootMenu._menuRequested.emit("next"))}if(40===t)return void this.activateNextItem();let i=(0,f.getKeyboardLayout)().keyForKeydownEvent(e);if(!i)return;let s=this._activeIndex+1,n=D.findMnemonic(this._items,i,s);-1===n.index||n.multiple?-1!==n.index?this.activeIndex=n.index:-1!==n.auto&&(this.activeIndex=n.auto):(this.activeIndex=n.index,this.triggerActiveItem())}_evtMouseUp(e){0===e.button&&(e.preventDefault(),e.stopPropagation(),this.triggerActiveItem())}_evtMouseMove(e){let t=o.ArrayExt.findFirstIndex(this.contentNode.children,(t=>d.ElementExt.hitTest(t,e.clientX,e.clientY)));if(t===this._activeIndex)return;if(this.activeIndex=t,t=this.activeIndex,t===this._childIndex)return this._cancelOpenTimer(),void this._cancelCloseTimer();-1!==this._childIndex&&this._startCloseTimer(),this._cancelOpenTimer();let i=this.activeItem;i&&"submenu"===i.type&&i.submenu&&this._startOpenTimer()}_evtMouseEnter(e){for(let e=this._parentMenu;e;e=e._parentMenu)e._cancelOpenTimer(),e._cancelCloseTimer(),e.activeIndex=e._childIndex}_evtMouseLeave(e){if(this._cancelOpenTimer(),!this._childMenu)return void(this.activeIndex=-1);let{clientX:t,clientY:i}=e;d.ElementExt.hitTest(this._childMenu.node,t,i)?this._cancelCloseTimer():(this.activeIndex=-1,this._startCloseTimer())}_evtMouseDown(e){this._parentMenu||(D.hitTestMenus(this,e.clientX,e.clientY)?(e.preventDefault(),e.stopPropagation()):this.close())}_openChildMenu(e=!1){let t=this.activeItem;if(!t||"submenu"!==t.type||!t.submenu)return void this._closeChildMenu();let i=t.submenu;if(i===this._childMenu)return;$.saveWindowData(),this._closeChildMenu(),this._childMenu=i,this._childIndex=this._activeIndex,i._parentMenu=this,l.MessageLoop.sendMessage(this,M.Msg.UpdateRequest);let s=this.contentNode.children[this._activeIndex];D.openSubmenu(i,s),e&&(i.activeIndex=-1,i.activateNextItem()),i.activate()}_closeChildMenu(){this._childMenu&&this._childMenu.close()}_startOpenTimer(){0===this._openTimerID&&(this._openTimerID=window.setTimeout((()=>{this._openTimerID=0,this._openChildMenu()}),D.TIMER_DELAY))}_startCloseTimer(){0===this._closeTimerID&&(this._closeTimerID=window.setTimeout((()=>{this._closeTimerID=0,this._closeChildMenu()}),D.TIMER_DELAY))}_cancelOpenTimer(){0!==this._openTimerID&&(clearTimeout(this._openTimerID),this._openTimerID=0)}_cancelCloseTimer(){0!==this._closeTimerID&&(clearTimeout(this._closeTimerID),this._closeTimerID=0)}static saveWindowData(){D.saveWindowData()}}!function(e){class t{renderItem(e){let t=this.createItemClass(e),i=this.createItemDataset(e),s=this.createItemARIA(e);return p.h.li({className:t,dataset:i,tabindex:"0",onfocus:e.onfocus,...s},this.renderIcon(e),this.renderLabel(e),this.renderShortcut(e),this.renderSubmenu(e))}renderIcon(e){let t=this.createIconClass(e);return p.h.div({className:t},e.item.icon,e.item.iconLabel)}renderLabel(e){let t=this.formatLabel(e);return p.h.div({className:"lm-Menu-itemLabel"},t)}renderShortcut(e){let t=this.formatShortcut(e);return p.h.div({className:"lm-Menu-itemShortcut"},t)}renderSubmenu(e){return p.h.div({className:"lm-Menu-itemSubmenuIcon"})}createItemClass(e){let t="lm-Menu-item";e.item.isEnabled||(t+=" lm-mod-disabled"),e.item.isToggled&&(t+=" lm-mod-toggled"),e.item.isVisible||(t+=" lm-mod-hidden"),e.active&&(t+=" lm-mod-active"),e.collapsed&&(t+=" lm-mod-collapsed");let i=e.item.className;return i&&(t+=` ${i}`),t}createItemDataset(e){let t,{type:i,command:s,dataset:n}=e.item;return t="command"===i?{...n,type:i,command:s}:{...n,type:i},t}createIconClass(e){let t="lm-Menu-itemIcon",i=e.item.iconClass;return i?`${t} ${i}`:t}createItemARIA(e){let t={};switch(e.item.type){case"separator":t.role="presentation";break;case"submenu":t["aria-haspopup"]="true",e.item.isEnabled||(t["aria-disabled"]="true");break;default:e.item.isEnabled||(t["aria-disabled"]="true"),t.role="menuitem"}return t}formatLabel(e){let{label:t,mnemonic:i}=e.item;if(i<0||i>=t.length)return t;let s=t.slice(0,i),n=t.slice(i+1),a=t[i];return[s,p.h.span({className:"lm-Menu-itemMnemonic"},a),n]}formatShortcut(e){let t=e.item.keyBinding;return t?g.CommandRegistry.formatKeystroke(t.keys):null}}e.Renderer=t,e.defaultRenderer=new t}($||($={})),function(e){e.TIMER_DELAY=300,e.SUBMENU_OVERLAP=3;let t=null,i=0;function s(){return i>0?(i--,t):a()}function n(e){return"separator"!==e.type&&e.isEnabled&&e.isVisible}function a(){return{pageXOffset:window.pageXOffset,pageYOffset:window.pageYOffset,clientWidth:document.documentElement.clientWidth,clientHeight:document.documentElement.clientHeight}}e.saveWindowData=function(){t=a(),i++},e.createNode=function(){let e=document.createElement("div"),t=document.createElement("ul");return t.className="lm-Menu-content",e.appendChild(t),t.setAttribute("role","menu"),e.tabIndex=0,e},e.canActivate=n,e.createItem=function(e,t){return new r(e.commands,t)},e.hitTestMenus=function(e,t,i){for(let s=e;s;s=s.childMenu)if(d.ElementExt.hitTest(s.node,t,i))return!0;return!1},e.computeCollapsed=function(e){let t=new Array(e.length);o.ArrayExt.fill(t,!1);let i=0,s=e.length;for(;i<s;++i){let s=e[i];if(s.isVisible){if("separator"!==s.type)break;t[i]=!0}}let n=s-1;for(;n>=0;--n){let i=e[n];if(i.isVisible){if("separator"!==i.type)break;t[n]=!0}}let a=!1;for(;++i<n;){let s=e[i];s.isVisible&&("separator"!==s.type?a=!1:a?t[i]=!0:a=!0)}return t},e.openRootMenu=function(e,t,i,n,a){const r=s();let o=r.pageXOffset,h=r.pageYOffset,d=r.clientWidth,c=r.clientHeight;l.MessageLoop.sendMessage(e,M.Msg.UpdateRequest);let u=c-(a?i:0),m=e.node,g=m.style;g.opacity="0",g.maxHeight=`${u}px`,M.attach(e,document.body);let{width:p,height:_}=m.getBoundingClientRect();!n&&t+p>o+d&&(t=o+d-p),!a&&i+_>h+c&&(i>h+c?i=h+c-_:i-=_),g.transform=`translate(${Math.max(0,t)}px, ${Math.max(0,i)}px`,g.opacity="1"},e.openSubmenu=function(t,i){const n=s();let a=n.pageXOffset,r=n.pageYOffset,o=n.clientWidth,h=n.clientHeight;l.MessageLoop.sendMessage(t,M.Msg.UpdateRequest);let c=h,u=t.node,m=u.style;m.opacity="0",m.maxHeight=`${c}px`,M.attach(t,document.body);let{width:g,height:p}=u.getBoundingClientRect(),_=d.ElementExt.boxSizing(t.node),f=i.getBoundingClientRect(),b=f.right-e.SUBMENU_OVERLAP;b+g>a+o&&(b=f.left+e.SUBMENU_OVERLAP-g);let v=f.top-_.borderTop-_.paddingTop;v+p>r+h&&(v=f.bottom+_.borderBottom+_.paddingBottom-p),m.transform=`translate(${Math.max(0,b)}px, ${Math.max(0,v)}px`,m.opacity="1"},e.findMnemonic=function(e,t,i){let s=-1,a=-1,r=!1,o=t.toUpperCase();for(let t=0,h=e.length;t<h;++t){let d=(t+i)%h,l=e[d];if(!n(l))continue;let c=l.label;if(0===c.length)continue;let u=l.mnemonic;u>=0&&u<c.length?c[u].toUpperCase()===o&&(-1===s?s=d:r=!0):-1===a&&c[0].toUpperCase()===o&&(a=d)}return{index:s,multiple:r,auto:a}};class r{constructor(e,t){this._commands=e,this.type=t.type||"command",this.command=t.command||"",this.args=t.args||h.JSONExt.emptyObject,this.submenu=t.submenu||null}get label(){return"command"===this.type?this._commands.label(this.command,this.args):"submenu"===this.type&&this.submenu?this.submenu.title.label:""}get mnemonic(){return"command"===this.type?this._commands.mnemonic(this.command,this.args):"submenu"===this.type&&this.submenu?this.submenu.title.mnemonic:-1}get icon(){return"command"===this.type?this._commands.icon(this.command,this.args):"submenu"===this.type&&this.submenu?this.submenu.title.icon:void 0}get iconClass(){return"command"===this.type?this._commands.iconClass(this.command,this.args):"submenu"===this.type&&this.submenu?this.submenu.title.iconClass:""}get iconLabel(){return"command"===this.type?this._commands.iconLabel(this.command,this.args):"submenu"===this.type&&this.submenu?this.submenu.title.iconLabel:""}get caption(){return"command"===this.type?this._commands.caption(this.command,this.args):"submenu"===this.type&&this.submenu?this.submenu.title.caption:""}get className(){return"command"===this.type?this._commands.className(this.command,this.args):"submenu"===this.type&&this.submenu?this.submenu.title.className:""}get dataset(){return"command"===this.type?this._commands.dataset(this.command,this.args):"submenu"===this.type&&this.submenu?this.submenu.title.dataset:{}}get isEnabled(){return"command"===this.type?this._commands.isEnabled(this.command,this.args):"submenu"!==this.type||null!==this.submenu}get isToggled(){return"command"===this.type&&this._commands.isToggled(this.command,this.args)}get isVisible(){return"command"===this.type?this._commands.isVisible(this.command,this.args):"submenu"!==this.type||null!==this.submenu}get keyBinding(){if("command"===this.type){let{command:e,args:t}=this;return o.ArrayExt.findLastValue(this._commands.keyBindings,(i=>i.command===e&&h.JSONExt.deepEqual(i.args,t)))||null}return null}}}(D||(D={}));class U{constructor(e){this._groupByTarget=!0,this._idTick=0,this._items=[],this._sortBySelector=!0;const{groupByTarget:t,sortBySelector:i,...s}=e;this.menu=new $(s),this._groupByTarget=!1!==t,this._sortBySelector=!1!==i}addItem(e){let t=B.createItem(e,this._idTick++);return this._items.push(t),new _.DisposableDelegate((()=>{o.ArrayExt.removeFirstOf(this._items,t)}))}open(e){if($.saveWindowData(),this.menu.clearItems(),0===this._items.length)return!1;let t=B.matchItems(this._items,e,this._groupByTarget,this._sortBySelector);if(!t||0===t.length)return!1;for(const e of t)this.menu.addItem(e);return this.menu.open(e.clientX,e.clientY),!0}}!function(e){function t(e,t){let i=e.rank,s=t.rank;return i!==s?i<s?-1:1:e.id-t.id}function i(e,i){let s=d.Selector.calculateSpecificity(e.selector),n=d.Selector.calculateSpecificity(i.selector);return s!==n?n-s:t(e,i)}e.createItem=function(e,t){let i=function(e){if(-1!==e.indexOf(","))throw new Error(`Selector cannot contain commas: ${e}`);if(!d.Selector.isValid(e))throw new Error(`Invalid selector: ${e}`);return e}(e.selector),s=void 0!==e.rank?e.rank:1/0;return{...e,selector:i,rank:s,id:t}},e.matchItems=function(e,s,n,a){let r=s.target;if(!r)return null;let o=s.currentTarget;if(!o)return null;if(!o.contains(r)&&(r=document.elementFromPoint(s.clientX,s.clientY),!r||!o.contains(r)))return null;let h=[],l=e.slice();for(;null!==r;){let e=[];for(let t=0,i=l.length;t<i;++t){let i=l[t];i&&d.Selector.matches(r,i.selector)&&(e.push(i),l[t]=null)}if(0!==e.length&&(n&&e.sort(a?i:t),h.push(...e)),r===o)break;r=r.parentElement}return n||h.sort(a?i:t),h}}(B||(B={}));const V=["ArrowLeft","ArrowUp","ArrowRight","ArrowDown","Home","End"];class Y extends M{constructor(e={}){super({node:X.createNode()}),this._currentIndex=-1,this._titles=[],this._titlesEditable=!1,this._previousTitle=null,this._dragData=null,this._addButtonEnabled=!1,this._tabMoved=new u.Signal(this),this._currentChanged=new u.Signal(this),this._addRequested=new u.Signal(this),this._tabCloseRequested=new u.Signal(this),this._tabDetachRequested=new u.Signal(this),this._tabActivateRequested=new u.Signal(this),this.addClass("lm-TabBar"),this.contentNode.setAttribute("role","tablist"),this.setFlag(M.Flag.DisallowLayout),this._document=e.document||document,this.tabsMovable=e.tabsMovable||!1,this.titlesEditable=e.titlesEditable||!1,this.allowDeselect=e.allowDeselect||!1,this.addButtonEnabled=e.addButtonEnabled||!1,this.insertBehavior=e.insertBehavior||"select-tab-if-needed",this.name=e.name||"",this.orientation=e.orientation||"horizontal",this.removeBehavior=e.removeBehavior||"select-tab-after",this.renderer=e.renderer||Y.defaultRenderer}dispose(){this._releaseMouse(),this._titles.length=0,this._previousTitle=null,super.dispose()}get currentChanged(){return this._currentChanged}get tabMoved(){return this._tabMoved}get tabActivateRequested(){return this._tabActivateRequested}get addRequested(){return this._addRequested}get tabCloseRequested(){return this._tabCloseRequested}get tabDetachRequested(){return this._tabDetachRequested}get document(){return this._document}get titlesEditable(){return this._titlesEditable}set titlesEditable(e){this._titlesEditable=e}get currentTitle(){return this._titles[this._currentIndex]||null}set currentTitle(e){this.currentIndex=e?this._titles.indexOf(e):-1}get currentIndex(){return this._currentIndex}set currentIndex(e){if((e<0||e>=this._titles.length)&&(e=-1),this._currentIndex===e)return;let t=this._currentIndex,i=this._titles[t]||null,s=e,n=this._titles[s]||null;this._currentIndex=s,this._previousTitle=i,this.update(),this._currentChanged.emit({previousIndex:t,previousTitle:i,currentIndex:s,currentTitle:n})}get name(){return this._name}set name(e){this._name=e,e?this.contentNode.setAttribute("aria-label",e):this.contentNode.removeAttribute("aria-label")}get orientation(){return this._orientation}set orientation(e){this._orientation!==e&&(this._releaseMouse(),this._orientation=e,this.dataset.orientation=e,this.contentNode.setAttribute("aria-orientation",e))}get addButtonEnabled(){return this._addButtonEnabled}set addButtonEnabled(e){this._addButtonEnabled!==e&&(this._addButtonEnabled=e,e?this.addButtonNode.classList.remove("lm-mod-hidden"):this.addButtonNode.classList.add("lm-mod-hidden"))}get titles(){return this._titles}get contentNode(){return this.node.getElementsByClassName("lm-TabBar-content")[0]}get addButtonNode(){return this.node.getElementsByClassName("lm-TabBar-addButton")[0]}addTab(e){return this.insertTab(this._titles.length,e)}insertTab(e,t){this._releaseMouse();let i=X.asTitle(t),s=this._titles.indexOf(i),n=Math.max(0,Math.min(e,this._titles.length));return-1===s?(o.ArrayExt.insert(this._titles,n,i),i.changed.connect(this._onTitleChanged,this),this.update(),this._adjustCurrentForInsert(n,i),i):(n===this._titles.length&&n--,s===n||(o.ArrayExt.move(this._titles,s,n),this.update(),this._adjustCurrentForMove(s,n)),i)}removeTab(e){this.removeTabAt(this._titles.indexOf(e))}removeTabAt(e){this._releaseMouse();let t=o.ArrayExt.removeAt(this._titles,e);t&&(t.changed.disconnect(this._onTitleChanged,this),t===this._previousTitle&&(this._previousTitle=null),this.update(),this._adjustCurrentForRemove(e,t))}clearTabs(){if(0===this._titles.length)return;this._releaseMouse();for(let e of this._titles)e.changed.disconnect(this._onTitleChanged,this);let e=this.currentIndex,t=this.currentTitle;this._currentIndex=-1,this._previousTitle=null,this._titles.length=0,this.update(),-1!==e&&this._currentChanged.emit({previousIndex:e,previousTitle:t,currentIndex:-1,currentTitle:null})}releaseMouse(){this._releaseMouse()}handleEvent(e){switch(e.type){case"pointerdown":this._evtPointerDown(e);break;case"pointermove":this._evtPointerMove(e);break;case"pointerup":this._evtPointerUp(e);break;case"dblclick":this._evtDblClick(e);break;case"keydown":e.eventPhase===Event.CAPTURING_PHASE?this._evtKeyDownCapturing(e):this._evtKeyDown(e);break;case"contextmenu":e.preventDefault(),e.stopPropagation()}}onBeforeAttach(e){this.node.addEventListener("pointerdown",this),this.node.addEventListener("dblclick",this),this.node.addEventListener("keydown",this)}onAfterDetach(e){this.node.removeEventListener("pointerdown",this),this.node.removeEventListener("dblclick",this),this.node.removeEventListener("keydown",this),this._releaseMouse()}onUpdateRequest(e){var t;let i=this._titles,s=this.renderer,n=this.currentTitle,a=new Array(i.length);const r=null!==(t=this._getCurrentTabindex())&&void 0!==t?t:this._currentIndex>-1?this._currentIndex:0;for(let e=0,t=i.length;e<t;++e){let o=i[e],h=o===n,d=h?t:t-e-1,l=r===e?0:-1;a[e]=s.renderTab({title:o,current:h,zIndex:d,tabIndex:l})}p.VirtualDOM.render(a,this.contentNode)}_getCurrentTabindex(){let e=null;const t=this.contentNode.querySelector('li[tabindex="0"]');return t?e=[...this.contentNode.children].indexOf(t):this._addButtonEnabled&&"0"===this.addButtonNode.getAttribute("tabindex")&&(e=-1),e}_evtDblClick(e){if(!this.titlesEditable)return;let t=this.contentNode.children,i=o.ArrayExt.findFirstIndex(t,(t=>d.ElementExt.hitTest(t,e.clientX,e.clientY)));if(-1===i)return;let s=this.titles[i],n=t[i].querySelector(".lm-TabBar-tabLabel");if(n&&n.contains(e.target)){let e=s.label||"",t=n.innerHTML;n.innerHTML="";let i=document.createElement("input");i.classList.add("lm-TabBar-tabInput"),i.value=e,n.appendChild(i);let a=()=>{i.removeEventListener("blur",a),n.innerHTML=t,this.node.addEventListener("keydown",this)};i.addEventListener("dblclick",(e=>e.stopPropagation())),i.addEventListener("blur",a),i.addEventListener("keydown",(e=>{"Enter"===e.key?(""!==i.value&&(s.label=s.caption=i.value),a()):"Escape"===e.key&&a()})),this.node.removeEventListener("keydown",this),i.select(),i.focus(),n.children.length>0&&n.children[0].focus()}}_evtKeyDownCapturing(e){e.eventPhase===Event.CAPTURING_PHASE&&(e.preventDefault(),e.stopPropagation(),"Escape"===e.key&&this._releaseMouse())}_evtKeyDown(e){var t,i,s;if("Tab"!==e.key&&e.eventPhase!==Event.CAPTURING_PHASE)if("Enter"===e.key||"Spacebar"===e.key||" "===e.key){const t=document.activeElement;if(this.addButtonEnabled&&this.addButtonNode.contains(t))e.preventDefault(),e.stopPropagation(),this._addRequested.emit();else{const i=o.ArrayExt.findFirstIndex(this.contentNode.children,(e=>e.contains(t)));i>=0&&(e.preventDefault(),e.stopPropagation(),this.currentIndex=i)}}else if(V.includes(e.key)){const n=[...this.contentNode.children];if(this.addButtonEnabled&&n.push(this.addButtonNode),n.length<=1)return;e.preventDefault(),e.stopPropagation();let a,r=n.indexOf(document.activeElement);-1===r&&(r=this._currentIndex),"ArrowRight"===e.key&&"horizontal"===this._orientation||"ArrowDown"===e.key&&"vertical"===this._orientation?a=null!==(t=n[r+1])&&void 0!==t?t:n[0]:"ArrowLeft"===e.key&&"horizontal"===this._orientation||"ArrowUp"===e.key&&"vertical"===this._orientation?a=null!==(i=n[r-1])&&void 0!==i?i:n[n.length-1]:"Home"===e.key?a=n[0]:"End"===e.key&&(a=n[n.length-1]),a&&(null===(s=n[r])||void 0===s||s.setAttribute("tabindex","-1"),null==a||a.setAttribute("tabindex","0"),a.focus())}}_evtPointerDown(e){if(0!==e.button&&1!==e.button)return;if(this._dragData)return;if(e.target.classList.contains("lm-TabBar-tabInput"))return;let t=this.addButtonEnabled&&this.addButtonNode.contains(e.target),i=this.contentNode.children,s=o.ArrayExt.findFirstIndex(i,(t=>d.ElementExt.hitTest(t,e.clientX,e.clientY)));if(-1===s&&!t)return;if(e.preventDefault(),e.stopPropagation(),this._dragData={tab:i[s],index:s,pressX:e.clientX,pressY:e.clientY,tabPos:-1,tabSize:-1,tabPressPos:-1,targetIndex:-1,tabLayout:null,contentRect:null,override:null,dragActive:!1,dragAborted:!1,detachRequested:!1},this.document.addEventListener("pointerup",this,!0),1===e.button||t)return;let n=i[s].querySelector(this.renderer.closeIconSelector);n&&n.contains(e.target)||(this.tabsMovable&&(this.document.addEventListener("pointermove",this,!0),this.document.addEventListener("keydown",this,!0),this.document.addEventListener("contextmenu",this,!0)),this.allowDeselect&&this.currentIndex===s?this.currentIndex=-1:this.currentIndex=s,-1!==this.currentIndex&&this._tabActivateRequested.emit({index:this.currentIndex,title:this.currentTitle}))}_evtPointerMove(e){let t=this._dragData;if(!t)return;e.preventDefault(),e.stopPropagation();let i=this.contentNode.children;if(t.dragActive||X.dragExceeded(t,e)){if(!t.dragActive){let e=t.tab.getBoundingClientRect();"horizontal"===this._orientation?(t.tabPos=t.tab.offsetLeft,t.tabSize=e.width,t.tabPressPos=t.pressX-e.left):(t.tabPos=t.tab.offsetTop,t.tabSize=e.height,t.tabPressPos=t.pressY-e.top),t.tabPressOffset={x:t.pressX-e.left,y:t.pressY-e.top},t.tabLayout=X.snapTabLayout(i,this._orientation),t.contentRect=this.contentNode.getBoundingClientRect(),t.override=m.Drag.overrideCursor("default"),t.tab.classList.add("lm-mod-dragging"),this.addClass("lm-mod-dragging"),t.dragActive=!0}if(!t.detachRequested&&X.detachExceeded(t,e)){t.detachRequested=!0;let s=t.index,n=e.clientX,a=e.clientY,r=i[s],o=this._titles[s];if(this._tabDetachRequested.emit({index:s,title:o,tab:r,clientX:n,clientY:a,offset:t.tabPressOffset}),t.dragAborted)return}X.layoutTabs(i,t,e,this._orientation)}}_evtPointerUp(e){if(0!==e.button&&1!==e.button)return;const t=this._dragData;if(!t)return;if(e.preventDefault(),e.stopPropagation(),this.document.removeEventListener("pointermove",this,!0),this.document.removeEventListener("pointerup",this,!0),this.document.removeEventListener("keydown",this,!0),this.document.removeEventListener("contextmenu",this,!0),!t.dragActive){if(this._dragData=null,this.addButtonEnabled&&this.addButtonNode.contains(e.target))return void this._addRequested.emit(void 0);let i=this.contentNode.children,s=o.ArrayExt.findFirstIndex(i,(t=>d.ElementExt.hitTest(t,e.clientX,e.clientY)));if(s!==t.index)return;let n=this._titles[s];if(!n.closable)return;if(1===e.button)return void this._tabCloseRequested.emit({index:s,title:n});let a=i[s].querySelector(this.renderer.closeIconSelector);return a&&a.contains(e.target)?void this._tabCloseRequested.emit({index:s,title:n}):void 0}if(0!==e.button)return;X.finalizeTabPosition(t,this._orientation),t.tab.classList.remove("lm-mod-dragging");let i=X.parseTransitionDuration(t.tab);setTimeout((()=>{if(t.dragAborted)return;this._dragData=null,X.resetTabPositions(this.contentNode.children,this._orientation),t.override.dispose(),this.removeClass("lm-mod-dragging");let e=t.index,i=t.targetIndex;-1!==i&&e!==i&&(o.ArrayExt.move(this._titles,e,i),this._adjustCurrentForMove(e,i),this._tabMoved.emit({fromIndex:e,toIndex:i,title:this._titles[i]}),l.MessageLoop.sendMessage(this,M.Msg.UpdateRequest))}),i)}_releaseMouse(){let e=this._dragData;e&&(this._dragData=null,this.document.removeEventListener("pointermove",this,!0),this.document.removeEventListener("pointerup",this,!0),this.document.removeEventListener("keydown",this,!0),this.document.removeEventListener("contextmenu",this,!0),e.dragAborted=!0,e.dragActive&&(X.resetTabPositions(this.contentNode.children,this._orientation),e.override.dispose(),e.tab.classList.remove("lm-mod-dragging"),this.removeClass("lm-mod-dragging")))}_adjustCurrentForInsert(e,t){let i=this.currentTitle,s=this._currentIndex,n=this.insertBehavior;if("select-tab"===n||"select-tab-if-needed"===n&&-1===s)return this._currentIndex=e,this._previousTitle=i,void this._currentChanged.emit({previousIndex:s,previousTitle:i,currentIndex:e,currentTitle:t});s>=e&&this._currentIndex++}_adjustCurrentForMove(e,t){this._currentIndex===e?this._currentIndex=t:this._currentIndex<e&&this._currentIndex>=t?this._currentIndex++:this._currentIndex>e&&this._currentIndex<=t&&this._currentIndex--}_adjustCurrentForRemove(e,t){let i=this._currentIndex,s=this.removeBehavior;if(i===e){if(0===this._titles.length)return this._currentIndex=-1,void this._currentChanged.emit({previousIndex:e,previousTitle:t,currentIndex:-1,currentTitle:null});if("select-tab-after"===s)return this._currentIndex=Math.min(e,this._titles.length-1),void this._currentChanged.emit({previousIndex:e,previousTitle:t,currentIndex:this._currentIndex,currentTitle:this.currentTitle});if("select-tab-before"===s)return this._currentIndex=Math.max(0,e-1),void this._currentChanged.emit({previousIndex:e,previousTitle:t,currentIndex:this._currentIndex,currentTitle:this.currentTitle});if("select-previous-tab"===s)return this._previousTitle?(this._currentIndex=this._titles.indexOf(this._previousTitle),this._previousTitle=null):this._currentIndex=Math.min(e,this._titles.length-1),void this._currentChanged.emit({previousIndex:e,previousTitle:t,currentIndex:this._currentIndex,currentTitle:this.currentTitle});this._currentIndex=-1,this._currentChanged.emit({previousIndex:e,previousTitle:t,currentIndex:-1,currentTitle:null})}else i>e&&this._currentIndex--}_onTitleChanged(e){this.update()}}var X,K,G,j,J,Q,Z,ee;!function(e){class t{constructor(){this.closeIconSelector=".lm-TabBar-tabCloseIcon",this._tabID=0,this._tabKeys=new WeakMap,this._uuid=++t._nInstance}renderTab(e){let t=e.title.caption,i=this.createTabKey(e),s=i,n=this.createTabStyle(e),a=this.createTabClass(e),r=this.createTabDataset(e),o=this.createTabARIA(e);return e.title.closable?p.h.li({id:s,key:i,className:a,title:t,style:n,dataset:r,...o},this.renderIcon(e),this.renderLabel(e),this.renderCloseIcon(e)):p.h.li({id:s,key:i,className:a,title:t,style:n,dataset:r,...o},this.renderIcon(e),this.renderLabel(e))}renderIcon(e){const{title:t}=e;let i=this.createIconClass(e);return p.h.div({className:i},t.icon,t.iconLabel)}renderLabel(e){return p.h.div({className:"lm-TabBar-tabLabel"},e.title.label)}renderCloseIcon(e){return p.h.div({className:"lm-TabBar-tabCloseIcon"})}createTabKey(e){let t=this._tabKeys.get(e.title);return void 0===t&&(t=`tab-key-${this._uuid}-${this._tabID++}`,this._tabKeys.set(e.title,t)),t}createTabStyle(e){return{zIndex:`${e.zIndex}`}}createTabClass(e){let t="lm-TabBar-tab";return e.title.className&&(t+=` ${e.title.className}`),e.title.closable&&(t+=" lm-mod-closable"),e.current&&(t+=" lm-mod-current"),t}createTabDataset(e){return e.title.dataset}createTabARIA(e){var t;return{role:"tab","aria-selected":e.current.toString(),tabindex:`${null!==(t=e.tabIndex)&&void 0!==t?t:"-1"}`}}createIconClass(e){let t="lm-TabBar-tabIcon",i=e.title.iconClass;return i?`${t} ${i}`:t}}t._nInstance=0,e.Renderer=t,e.defaultRenderer=new t,e.addButtonSelector=".lm-TabBar-addButton"}(Y||(Y={})),function(e){e.DRAG_THRESHOLD=5,e.DETACH_THRESHOLD=20,e.createNode=function(){let e=document.createElement("div"),t=document.createElement("ul");t.setAttribute("role","tablist"),t.className="lm-TabBar-content",e.appendChild(t);let i=document.createElement("div");return i.className="lm-TabBar-addButton lm-mod-hidden",i.setAttribute("tabindex","-1"),i.setAttribute("role","button"),e.appendChild(i),e},e.asTitle=function(e){return e instanceof v?e:new v(e)},e.parseTransitionDuration=function(e){let t=window.getComputedStyle(e);return 1e3*(parseFloat(t.transitionDuration)||0)},e.snapTabLayout=function(e,t){let i=new Array(e.length);for(let s=0,n=e.length;s<n;++s){let n=e[s],a=window.getComputedStyle(n);i[s]="horizontal"===t?{pos:n.offsetLeft,size:n.offsetWidth,margin:parseFloat(a.marginLeft)||0}:{pos:n.offsetTop,size:n.offsetHeight,margin:parseFloat(a.marginTop)||0}}return i},e.dragExceeded=function(t,i){let s=Math.abs(i.clientX-t.pressX),n=Math.abs(i.clientY-t.pressY);return s>=e.DRAG_THRESHOLD||n>=e.DRAG_THRESHOLD},e.detachExceeded=function(t,i){let s=t.contentRect;return i.clientX<s.left-e.DETACH_THRESHOLD||i.clientX>=s.right+e.DETACH_THRESHOLD||i.clientY<s.top-e.DETACH_THRESHOLD||i.clientY>=s.bottom+e.DETACH_THRESHOLD},e.layoutTabs=function(e,t,i,s){let n,a,r,o;"horizontal"===s?(n=t.pressX,a=i.clientX-t.contentRect.left,r=i.clientX,o=t.contentRect.width):(n=t.pressY,a=i.clientY-t.contentRect.top,r=i.clientY,o=t.contentRect.height);let h=t.index,d=a-t.tabPressPos,l=d+t.tabSize;for(let i=0,a=e.length;i<a;++i){let a,c=t.tabLayout[i],u=c.pos+(c.size>>1);if(i<t.index&&d<u)a=`${t.tabSize+t.tabLayout[i+1].margin}px`,h=Math.min(h,i);else if(i>t.index&&l>u)a=-t.tabSize-c.margin+"px",h=Math.max(h,i);else if(i===t.index){let e=r-n,i=o-(t.tabPos+t.tabSize);a=`${Math.max(-t.tabPos,Math.min(e,i))}px`}else a="";"horizontal"===s?e[i].style.left=a:e[i].style.top=a}t.targetIndex=h},e.finalizeTabPosition=function(e,t){let i,s;if(i="horizontal"===t?e.contentRect.width:e.contentRect.height,e.targetIndex===e.index)s=0;else if(e.targetIndex>e.index){let t=e.tabLayout[e.targetIndex];s=t.pos+t.size-e.tabSize-e.tabPos}else s=e.tabLayout[e.targetIndex].pos-e.tabPos;let n=i-(e.tabPos+e.tabSize),a=Math.max(-e.tabPos,Math.min(s,n));"horizontal"===t?e.tab.style.left=`${a}px`:e.tab.style.top=`${a}px`},e.resetTabPositions=function(e,t){for(const i of e)"horizontal"===t?i.style.left="":i.style.top=""}}(X||(X={}));class te extends x{constructor(e){super(),this._spacing=4,this._dirty=!1,this._root=null,this._box=null,this._items=new Map,this.renderer=e.renderer,void 0!==e.spacing&&(this._spacing=k.clampDimension(e.spacing)),this._document=e.document||document,this._hiddenMode=void 0!==e.hiddenMode?e.hiddenMode:M.HiddenMode.Display}dispose(){let e=this[Symbol.iterator]();this._items.forEach((e=>{e.dispose()})),this._box=null,this._root=null,this._items.clear();for(const t of e)t.dispose();super.dispose()}get hiddenMode(){return this._hiddenMode}set hiddenMode(e){if(this._hiddenMode!==e){this._hiddenMode=e;for(const e of this.tabBars())if(e.titles.length>1)for(const t of e.titles)t.owner.hiddenMode=this._hiddenMode}}get spacing(){return this._spacing}set spacing(e){e=k.clampDimension(e),this._spacing!==e&&(this._spacing=e,this.parent&&this.parent.fit())}get isEmpty(){return null===this._root}[Symbol.iterator](){return this._root?this._root.iterAllWidgets():(0,o.empty)()}widgets(){return this._root?this._root.iterUserWidgets():(0,o.empty)()}selectedWidgets(){return this._root?this._root.iterSelectedWidgets():(0,o.empty)()}tabBars(){return this._root?this._root.iterTabBars():(0,o.empty)()}handles(){return this._root?this._root.iterHandles():(0,o.empty)()}moveHandle(e,t,i){let n=e.classList.contains("lm-mod-hidden");if(!this._root||n)return;let a,r=this._root.findSplitNode(e);r&&(a="horizontal"===r.node.orientation?t-e.offsetLeft:i-e.offsetTop,0!==a&&(r.node.holdSizes(),s.adjust(r.node.sizers,r.index,a),this.parent&&this.parent.update()))}saveLayout(){return this._root?(this._root.holdAllSizes(),{main:this._root.createConfig()}):{main:null}}restoreLayout(e){let t,i=new Set;t=e.main?K.normalizeAreaConfig(e.main,i):null;let s=this.widgets(),n=this.tabBars(),a=this.handles();this._root=null;for(const e of s)i.has(e)||(e.parent=null);for(const e of n)e.dispose();for(const e of a)e.parentNode&&e.parentNode.removeChild(e);for(const e of i)e.parent=this.parent;this._root=t?K.realizeAreaConfig(t,{createTabBar:e=>this._createTabBar(),createHandle:()=>this._createHandle()},this._document):null,this.parent&&(i.forEach((e=>{this.attachWidget(e)})),this.parent.fit())}addWidget(e,t={}){let i=t.ref||null,s=t.mode||"tab-after",n=null;if(this._root&&i&&(n=this._root.findTabNode(i)),i&&!n)throw new Error("Reference widget is not in the layout.");switch(e.parent=this.parent,s){case"tab-after":this._insertTab(e,i,n,!0);break;case"tab-before":this._insertTab(e,i,n,!1);break;case"split-top":this._insertSplit(e,i,n,"vertical",!1);break;case"split-left":this._insertSplit(e,i,n,"horizontal",!1);break;case"split-right":this._insertSplit(e,i,n,"horizontal",!0);break;case"split-bottom":this._insertSplit(e,i,n,"vertical",!0);break;case"merge-top":this._insertSplit(e,i,n,"vertical",!1,!0);break;case"merge-left":this._insertSplit(e,i,n,"horizontal",!1,!0);break;case"merge-right":this._insertSplit(e,i,n,"horizontal",!0,!0);break;case"merge-bottom":this._insertSplit(e,i,n,"vertical",!0,!0)}this.parent&&(this.attachWidget(e),this.parent.fit())}removeWidget(e){this._removeWidget(e),this.parent&&(this.detachWidget(e),this.parent.fit())}hitTestTabAreas(e,t){if(!this._root||!this.parent||!this.parent.isVisible)return null;this._box||(this._box=d.ElementExt.boxSizing(this.parent.node));let i=this.parent.node.getBoundingClientRect(),s=e-i.left-this._box.borderLeft,n=t-i.top-this._box.borderTop,a=this._root.hitTestTabNodes(s,n);if(!a)return null;let{tabBar:r,top:o,left:h,width:l,height:c}=a,u=this._box.borderLeft+this._box.borderRight,m=this._box.borderTop+this._box.borderBottom;return{tabBar:r,x:s,y:n,top:o,left:h,right:i.width-u-(h+l),bottom:i.height-m-(o+c),width:l,height:c}}init(){super.init();for(const e of this)this.attachWidget(e);for(const e of this.handles())this.parent.node.appendChild(e);this.parent.fit()}attachWidget(e){this.parent.node!==e.node.parentNode&&(this._items.set(e,new y(e)),this.parent.isAttached&&l.MessageLoop.sendMessage(e,M.Msg.BeforeAttach),this.parent.node.appendChild(e.node),this.parent.isAttached&&l.MessageLoop.sendMessage(e,M.Msg.AfterAttach))}detachWidget(e){if(this.parent.node!==e.node.parentNode)return;this.parent.isAttached&&l.MessageLoop.sendMessage(e,M.Msg.BeforeDetach),this.parent.node.removeChild(e.node),this.parent.isAttached&&l.MessageLoop.sendMessage(e,M.Msg.AfterDetach);let t=this._items.get(e);t&&(this._items.delete(e),t.dispose())}onBeforeShow(e){super.onBeforeShow(e),this.parent.update()}onBeforeAttach(e){super.onBeforeAttach(e),this.parent.fit()}onChildShown(e){this.parent.fit()}onChildHidden(e){this.parent.fit()}onResize(e){this.parent.isVisible&&this._update(e.width,e.height)}onUpdateRequest(e){this.parent.isVisible&&this._update(-1,-1)}onFitRequest(e){this.parent.isAttached&&this._fit()}_removeWidget(e){if(!this._root)return;let t=this._root.findTabNode(e);if(!t)return;if(K.removeAria(e),t.tabBar.titles.length>1)return t.tabBar.removeTab(e.title),void(this._hiddenMode===M.HiddenMode.Scale&&1==t.tabBar.titles.length&&(t.tabBar.titles[0].owner.hiddenMode=M.HiddenMode.Display));if(t.tabBar.dispose(),this._root===t)return void(this._root=null);this._root.holdAllSizes();let i=t.parent;t.parent=null;let s=o.ArrayExt.removeFirstOf(i.children,t),n=o.ArrayExt.removeAt(i.handles,s);if(o.ArrayExt.removeAt(i.sizers,s),n.parentNode&&n.parentNode.removeChild(n),i.children.length>1)return void i.syncHandles();let a=i.parent;i.parent=null;let r=i.children[0],h=i.handles[0];if(i.children.length=0,i.handles.length=0,i.sizers.length=0,h.parentNode&&h.parentNode.removeChild(h),this._root===i)return r.parent=null,void(this._root=r);let d=a,l=d.children.indexOf(i);if(r instanceof K.TabLayoutNode)return r.parent=d,void(d.children[l]=r);let c=o.ArrayExt.removeAt(d.handles,l);o.ArrayExt.removeAt(d.children,l),o.ArrayExt.removeAt(d.sizers,l),c.parentNode&&c.parentNode.removeChild(c);for(let e=0,t=r.children.length;e<t;++e){let t=r.children[e],i=r.handles[e],s=r.sizers[e];o.ArrayExt.insert(d.children,l+e,t),o.ArrayExt.insert(d.handles,l+e,i),o.ArrayExt.insert(d.sizers,l+e,s),t.parent=d}r.children.length=0,r.handles.length=0,r.sizers.length=0,r.parent=null,d.syncHandles()}_createTabNode(e){let t=new K.TabLayoutNode(this._createTabBar());return t.tabBar.addTab(e.title),K.addAria(e,t.tabBar),t}_insertTab(e,t,i,s){if(e===t)return;if(!this._root){let t=new K.TabLayoutNode(this._createTabBar());return t.tabBar.addTab(e.title),this._root=t,void K.addAria(e,t.tabBar)}let n;i||(i=this._root.findFirstTabNode()),-1===i.tabBar.titles.indexOf(e.title)&&(this._removeWidget(e),e.hide()),n=t?i.tabBar.titles.indexOf(t.title):i.tabBar.currentIndex,this._hiddenMode===M.HiddenMode.Scale?0===i.tabBar.titles.length?e.hiddenMode=M.HiddenMode.Display:1==i.tabBar.titles.length?i.tabBar.titles[0].owner.hiddenMode=M.HiddenMode.Scale:e.hiddenMode=M.HiddenMode.Scale:e.hiddenMode=this._hiddenMode,i.tabBar.insertTab(n+(s?1:0),e.title),K.addAria(e,i.tabBar)}_insertSplit(e,t,i,s,n,a=!1){if(e===t&&i&&1===i.tabBar.titles.length)return;if(this._removeWidget(e),!this._root)return void(this._root=this._createTabNode(e));if(!i||!i.parent){let t=this._splitRoot(s),a=n?t.children.length:0;t.normalizeSizes();let r=K.createSizer(i?1:K.GOLDEN_RATIO),h=this._createTabNode(e);return o.ArrayExt.insert(t.children,a,h),o.ArrayExt.insert(t.sizers,a,r),o.ArrayExt.insert(t.handles,a,this._createHandle()),h.parent=t,t.normalizeSizes(),void t.syncHandles()}let r=i.parent;if(r.orientation===s){let t=r.children.indexOf(i);if(a){let i=t+(n?1:-1),s=r.children[i];if(s instanceof K.TabLayoutNode)return this._insertTab(e,null,s,!0),void++s.tabBar.currentIndex}r.normalizeSizes();let s=r.sizers[t].sizeHint/=2,h=t+(n?1:0),d=this._createTabNode(e);return o.ArrayExt.insert(r.children,h,d),o.ArrayExt.insert(r.sizers,h,K.createSizer(s)),o.ArrayExt.insert(r.handles,h,this._createHandle()),d.parent=r,void r.syncHandles()}let h=o.ArrayExt.removeFirstOf(r.children,i),d=new K.SplitLayoutNode(s);d.normalized=!0,d.children.push(i),d.sizers.push(K.createSizer(.5)),d.handles.push(this._createHandle()),i.parent=d;let l=n?1:0,c=this._createTabNode(e);o.ArrayExt.insert(d.children,l,c),o.ArrayExt.insert(d.sizers,l,K.createSizer(.5)),o.ArrayExt.insert(d.handles,l,this._createHandle()),c.parent=d,d.syncHandles(),o.ArrayExt.insert(r.children,h,d),d.parent=r}_splitRoot(e){let t=this._root;if(t instanceof K.SplitLayoutNode&&t.orientation===e)return t;let i=this._root=new K.SplitLayoutNode(e);return t&&(i.children.push(t),i.sizers.push(K.createSizer(0)),i.handles.push(this._createHandle()),t.parent=i),i}_fit(){let e=0,t=0;if(this._root){let i=this._root.fit(this._spacing,this._items);e=i.minWidth,t=i.minHeight}let i=this._box=d.ElementExt.boxSizing(this.parent.node);e+=i.horizontalSum,t+=i.verticalSum;let s=this.parent.node.style;s.minWidth=`${e}px`,s.minHeight=`${t}px`,this._dirty=!0,this.parent.parent&&l.MessageLoop.sendMessage(this.parent.parent,M.Msg.FitRequest),this._dirty&&l.MessageLoop.sendMessage(this.parent,M.Msg.UpdateRequest)}_update(e,t){if(this._dirty=!1,!this._root)return;e<0&&(e=this.parent.node.offsetWidth),t<0&&(t=this.parent.node.offsetHeight),this._box||(this._box=d.ElementExt.boxSizing(this.parent.node));let i=this._box.paddingTop,s=this._box.paddingLeft,n=e-this._box.horizontalSum,a=t-this._box.verticalSum;this._root.update(i,s,n,a,this._spacing,this._items)}_createTabBar(){let e=this.renderer.createTabBar(this._document);return e.orientation="horizontal",this.parent&&this.attachWidget(e),e}_createHandle(){let e=this.renderer.createHandle(),t=e.style;return t.position="absolute",t.contain="strict",t.top="0",t.left="0",t.width="0",t.height="0",this.parent&&this.parent.node.appendChild(e),e}}!function(e){function t(e){let t=new b;return t.sizeHint=e,t.size=e,t}e.GOLDEN_RATIO=.618,e.createSizer=t,e.normalizeAreaConfig=function e(t,i){let s;return s="tab-area"===t.type?function(e,t){if(0===e.widgets.length)return null;let i=[];for(const s of e.widgets)t.has(s)||(t.add(s),i.push(s));if(0===i.length)return null;let s=e.currentIndex;return-1!==s&&(s<0||s>=i.length)&&(s=0),{type:"tab-area",widgets:i,currentIndex:s}}(t,i):function(t,i){let s=t.orientation,n=[],a=[];for(let r=0,o=t.children.length;r<o;++r){let o=e(t.children[r],i);o&&("tab-area"===o.type||o.orientation!==s?(n.push(o),a.push(Math.abs(t.sizes[r]||0))):(n.push(...o.children),a.push(...o.sizes)))}return 0===n.length?null:1===n.length?n[0]:{type:"split-area",orientation:s,children:n,sizes:a}}(t,i),s},e.realizeAreaConfig=function s(a,r,o){let h;return h="tab-area"===a.type?function(t,s,n){let a=s.createTabBar(n);for(const i of t.widgets)i.hide(),a.addTab(i.title),e.addAria(i,a);return a.currentIndex=t.currentIndex,new i(a)}(a,r,o):function(e,i,a){let r=new n(e.orientation);return e.children.forEach(((n,o)=>{let h=s(n,i,a),d=t(e.sizes[o]),l=i.createHandle();r.children.push(h),r.handles.push(l),r.sizers.push(d),h.parent=r})),r.syncHandles(),r.normalizeSizes(),r}(a,r,o),h};class i{constructor(e){this.parent=null,this._top=0,this._left=0,this._width=0,this._height=0;let t=new b,i=new b;t.stretch=0,i.stretch=1,this.tabBar=e,this.sizers=[t,i]}get top(){return this._top}get left(){return this._left}get width(){return this._width}get height(){return this._height}*iterAllWidgets(){yield this.tabBar,yield*this.iterUserWidgets()}*iterUserWidgets(){for(const e of this.tabBar.titles)yield e.owner}*iterSelectedWidgets(){let e=this.tabBar.currentTitle;e&&(yield e.owner)}*iterTabBars(){yield this.tabBar}*iterHandles(){}findTabNode(e){return-1!==this.tabBar.titles.indexOf(e.title)?this:null}findSplitNode(e){return null}findFirstTabNode(){return this}hitTestTabNodes(e,t){return e<this._left||e>=this._left+this._width||t<this._top||t>=this._top+this._height?null:this}createConfig(){return{type:"tab-area",widgets:this.tabBar.titles.map((e=>e.owner)),currentIndex:this.tabBar.currentIndex}}holdAllSizes(){}fit(e,t){let i=0,s=0,n=t.get(this.tabBar),a=this.tabBar.currentTitle,r=a?t.get(a.owner):void 0,[o,h]=this.sizers;return n&&n.fit(),r&&r.fit(),n&&!n.isHidden?(i=Math.max(i,n.minWidth),s+=n.minHeight,o.minSize=n.minHeight,o.maxSize=n.maxHeight):(o.minSize=0,o.maxSize=0),r&&!r.isHidden?(i=Math.max(i,r.minWidth),s+=r.minHeight,h.minSize=r.minHeight,h.maxSize=1/0):(h.minSize=0,h.maxSize=1/0),{minWidth:i,minHeight:s,maxWidth:1/0,maxHeight:1/0}}update(e,t,i,n,a,r){this._top=t,this._left=e,this._width=i,this._height=n;let o=r.get(this.tabBar),h=this.tabBar.currentTitle,d=h?r.get(h.owner):void 0;if(s.calc(this.sizers,n),o&&!o.isHidden){let s=this.sizers[0].size;o.update(e,t,i,s),t+=s}if(d&&!d.isHidden){let s=this.sizers[1].size;d.update(e,t,i,s)}}}e.TabLayoutNode=i;class n{constructor(e){this.parent=null,this.normalized=!1,this.children=[],this.sizers=[],this.handles=[],this.orientation=e}*iterAllWidgets(){for(const e of this.children)yield*e.iterAllWidgets()}*iterUserWidgets(){for(const e of this.children)yield*e.iterUserWidgets()}*iterSelectedWidgets(){for(const e of this.children)yield*e.iterSelectedWidgets()}*iterTabBars(){for(const e of this.children)yield*e.iterTabBars()}*iterHandles(){yield*this.handles;for(const e of this.children)yield*e.iterHandles()}findTabNode(e){for(let t=0,i=this.children.length;t<i;++t){let i=this.children[t].findTabNode(e);if(i)return i}return null}findSplitNode(e){let t=this.handles.indexOf(e);if(-1!==t)return{index:t,node:this};for(let t=0,i=this.children.length;t<i;++t){let i=this.children[t].findSplitNode(e);if(i)return i}return null}findFirstTabNode(){return 0===this.children.length?null:this.children[0].findFirstTabNode()}hitTestTabNodes(e,t){for(let i=0,s=this.children.length;i<s;++i){let s=this.children[i].hitTestTabNodes(e,t);if(s)return s}return null}createConfig(){let e=this.orientation,t=this.createNormalizedSizes();return{type:"split-area",orientation:e,children:this.children.map((e=>e.createConfig())),sizes:t}}syncHandles(){this.handles.forEach(((e,t)=>{e.setAttribute("data-orientation",this.orientation),t===this.handles.length-1?e.classList.add("lm-mod-hidden"):e.classList.remove("lm-mod-hidden")}))}holdSizes(){for(const e of this.sizers)e.sizeHint=e.size}holdAllSizes(){for(const e of this.children)e.holdAllSizes();this.holdSizes()}normalizeSizes(){let e=this.sizers.length;if(0===e)return;this.holdSizes();let t=this.sizers.reduce(((e,t)=>e+t.sizeHint),0);if(0===t)for(const t of this.sizers)t.size=t.sizeHint=1/e;else for(const e of this.sizers)e.size=e.sizeHint/=t;this.normalized=!0}createNormalizedSizes(){let e=this.sizers.length;if(0===e)return[];let t=this.sizers.map((e=>e.size)),i=t.reduce(((e,t)=>e+t),0);if(0===i)for(let i=t.length-1;i>-1;i--)t[i]=1/e;else for(let e=t.length-1;e>-1;e--)t[e]/=i;return t}fit(e,t){let i="horizontal"===this.orientation,s=Math.max(0,this.children.length-1)*e,n=i?s:0,a=i?0:s;for(let s=0,r=this.children.length;s<r;++s){let r=this.children[s].fit(e,t);i?(a=Math.max(a,r.minHeight),n+=r.minWidth,this.sizers[s].minSize=r.minWidth):(n=Math.max(n,r.minWidth),a+=r.minHeight,this.sizers[s].minSize=r.minHeight)}return{minWidth:n,minHeight:a,maxWidth:1/0,maxHeight:1/0}}update(e,t,i,n,a,r){let o="horizontal"===this.orientation,h=Math.max(0,this.children.length-1)*a,d=Math.max(0,(o?i:n)-h);if(this.normalized){for(const e of this.sizers)e.sizeHint*=d;this.normalized=!1}s.calc(this.sizers,d);for(let s=0,h=this.children.length;s<h;++s){let h=this.children[s],d=this.sizers[s].size,l=this.handles[s].style;o?(h.update(e,t,d,n,a,r),e+=d,l.top=`${t}px`,l.left=`${e}px`,l.width=`${a}px`,l.height=`${n}px`,e+=a):(h.update(e,t,i,d,a,r),t+=d,l.top=`${t}px`,l.left=`${e}px`,l.width=`${i}px`,l.height=`${a}px`,t+=a)}}}e.SplitLayoutNode=n,e.addAria=function(e,t){e.node.setAttribute("role","tabpanel");let i=t.renderer;if(i instanceof Y.Renderer){let t=i.createTabKey({title:e.title,current:!1,zIndex:0});e.node.setAttribute("aria-labelledby",t)}},e.removeAria=function(e){e.node.removeAttribute("role"),e.node.removeAttribute("aria-labelledby")}}(K||(K={}));class ie extends M{constructor(e={}){super(),this._drag=null,this._tabsMovable=!0,this._tabsConstrained=!1,this._addButtonEnabled=!1,this._pressData=null,this._layoutModified=new u.Signal(this),this._addRequested=new u.Signal(this),this.addClass("lm-DockPanel"),this._document=e.document||document,this._mode=e.mode||"multiple-document",this._renderer=e.renderer||ie.defaultRenderer,this._edges=e.edges||G.DEFAULT_EDGES,void 0!==e.tabsMovable&&(this._tabsMovable=e.tabsMovable),void 0!==e.tabsConstrained&&(this._tabsConstrained=e.tabsConstrained),void 0!==e.addButtonEnabled&&(this._addButtonEnabled=e.addButtonEnabled),this.dataset.mode=this._mode;let t={createTabBar:()=>this._createTabBar(),createHandle:()=>this._createHandle()};this.layout=new te({document:this._document,renderer:t,spacing:e.spacing,hiddenMode:e.hiddenMode}),this.overlay=e.overlay||new ie.Overlay,this.node.appendChild(this.overlay.node)}dispose(){this._releaseMouse(),this.overlay.hide(0),this._drag&&this._drag.dispose(),super.dispose()}get hiddenMode(){return this.layout.hiddenMode}set hiddenMode(e){this.layout.hiddenMode=e}get layoutModified(){return this._layoutModified}get addRequested(){return this._addRequested}get renderer(){return this.layout.renderer}get spacing(){return this.layout.spacing}set spacing(e){this.layout.spacing=e}get mode(){return this._mode}set mode(e){if(this._mode===e)return;this._mode=e,this.dataset.mode=e;let t=this.layout;switch(e){case"multiple-document":for(const e of t.tabBars())e.show();break;case"single-document":t.restoreLayout(G.createSingleDocumentConfig(this));break;default:throw"unreachable"}l.MessageLoop.postMessage(this,G.LayoutModified)}get tabsMovable(){return this._tabsMovable}set tabsMovable(e){this._tabsMovable=e;for(const t of this.tabBars())t.tabsMovable=e}get tabsConstrained(){return this._tabsConstrained}set tabsConstrained(e){this._tabsConstrained=e}get addButtonEnabled(){return this._addButtonEnabled}set addButtonEnabled(e){this._addButtonEnabled=e;for(const t of this.tabBars())t.addButtonEnabled=e}get isEmpty(){return this.layout.isEmpty}*widgets(){yield*this.layout.widgets()}*selectedWidgets(){yield*this.layout.selectedWidgets()}*tabBars(){yield*this.layout.tabBars()}*handles(){yield*this.layout.handles()}selectWidget(e){let t=(0,o.find)(this.tabBars(),(t=>-1!==t.titles.indexOf(e.title)));if(!t)throw new Error("Widget is not contained in the dock panel.");t.currentTitle=e.title}activateWidget(e){this.selectWidget(e),e.activate()}saveLayout(){return this.layout.saveLayout()}restoreLayout(e){this._mode="multiple-document",this.layout.restoreLayout(e),(d.Platform.IS_EDGE||d.Platform.IS_IE)&&l.MessageLoop.flush(),l.MessageLoop.postMessage(this,G.LayoutModified)}addWidget(e,t={}){"single-document"===this._mode?this.layout.addWidget(e):this.layout.addWidget(e,t),l.MessageLoop.postMessage(this,G.LayoutModified)}processMessage(e){"layout-modified"===e.type?this._layoutModified.emit(void 0):super.processMessage(e)}handleEvent(e){switch(e.type){case"lm-dragenter":this._evtDragEnter(e);break;case"lm-dragleave":this._evtDragLeave(e);break;case"lm-dragover":this._evtDragOver(e);break;case"lm-drop":this._evtDrop(e);break;case"pointerdown":this._evtPointerDown(e);break;case"pointermove":this._evtPointerMove(e);break;case"pointerup":this._evtPointerUp(e);break;case"keydown":this._evtKeyDown(e);break;case"contextmenu":e.preventDefault(),e.stopPropagation()}}onBeforeAttach(e){this.node.addEventListener("lm-dragenter",this),this.node.addEventListener("lm-dragleave",this),this.node.addEventListener("lm-dragover",this),this.node.addEventListener("lm-drop",this),this.node.addEventListener("pointerdown",this)}onAfterDetach(e){this.node.removeEventListener("lm-dragenter",this),this.node.removeEventListener("lm-dragleave",this),this.node.removeEventListener("lm-dragover",this),this.node.removeEventListener("lm-drop",this),this.node.removeEventListener("pointerdown",this),this._releaseMouse()}onChildAdded(e){G.isGeneratedTabBarProperty.get(e.child)||e.child.addClass("lm-DockPanel-widget")}onChildRemoved(e){G.isGeneratedTabBarProperty.get(e.child)||(e.child.removeClass("lm-DockPanel-widget"),l.MessageLoop.postMessage(this,G.LayoutModified))}_evtDragEnter(e){e.mimeData.hasData("application/vnd.lumino.widget-factory")&&(e.preventDefault(),e.stopPropagation())}_evtDragLeave(e){e.preventDefault(),this._tabsConstrained&&e.source!==this||(e.stopPropagation(),this.overlay.hide(1))}_evtDragOver(e){e.preventDefault(),this._tabsConstrained&&e.source!==this||"invalid"===this._showOverlay(e.clientX,e.clientY)?e.dropAction="none":(e.stopPropagation(),e.dropAction=e.proposedAction)}_evtDrop(e){if(e.preventDefault(),this.overlay.hide(0),"none"===e.proposedAction)return void(e.dropAction="none");let{clientX:t,clientY:i}=e,{zone:s,target:n}=G.findDropTarget(this,t,i,this._edges);if(this._tabsConstrained&&e.source!==this||"invalid"===s)return void(e.dropAction="none");let a=e.mimeData.getData("application/vnd.lumino.widget-factory");if("function"!=typeof a)return void(e.dropAction="none");let r=a();if(!(r instanceof M))return void(e.dropAction="none");if(r.contains(this))return void(e.dropAction="none");let o=n?G.getDropRef(n.tabBar):null;switch(s){case"root-all":this.addWidget(r);break;case"root-top":this.addWidget(r,{mode:"split-top"});break;case"root-left":this.addWidget(r,{mode:"split-left"});break;case"root-right":this.addWidget(r,{mode:"split-right"});break;case"root-bottom":this.addWidget(r,{mode:"split-bottom"});break;case"widget-all":case"widget-tab":this.addWidget(r,{mode:"tab-after",ref:o});break;case"widget-top":this.addWidget(r,{mode:"split-top",ref:o});break;case"widget-left":this.addWidget(r,{mode:"split-left",ref:o});break;case"widget-right":this.addWidget(r,{mode:"split-right",ref:o});break;case"widget-bottom":this.addWidget(r,{mode:"split-bottom",ref:o});break;default:throw"unreachable"}e.dropAction=e.proposedAction,e.stopPropagation(),this.activateWidget(r)}_evtKeyDown(e){e.preventDefault(),e.stopPropagation(),27===e.keyCode&&(this._releaseMouse(),l.MessageLoop.postMessage(this,G.LayoutModified))}_evtPointerDown(e){if(0!==e.button)return;let t=this.layout,i=e.target,s=(0,o.find)(t.handles(),(e=>e.contains(i)));if(!s)return;e.preventDefault(),e.stopPropagation(),this._document.addEventListener("keydown",this,!0),this._document.addEventListener("pointerup",this,!0),this._document.addEventListener("pointermove",this,!0),this._document.addEventListener("contextmenu",this,!0);let n=s.getBoundingClientRect(),a=e.clientX-n.left,r=e.clientY-n.top,h=window.getComputedStyle(s),d=m.Drag.overrideCursor(h.cursor,this._document);this._pressData={handle:s,deltaX:a,deltaY:r,override:d}}_evtPointerMove(e){if(!this._pressData)return;e.preventDefault(),e.stopPropagation();let t=this.node.getBoundingClientRect(),i=e.clientX-t.left-this._pressData.deltaX,s=e.clientY-t.top-this._pressData.deltaY;this.layout.moveHandle(this._pressData.handle,i,s)}_evtPointerUp(e){0===e.button&&(e.preventDefault(),e.stopPropagation(),this._releaseMouse(),l.MessageLoop.postMessage(this,G.LayoutModified))}_releaseMouse(){this._pressData&&(this._pressData.override.dispose(),this._pressData=null,this._document.removeEventListener("keydown",this,!0),this._document.removeEventListener("pointerup",this,!0),this._document.removeEventListener("pointermove",this,!0),this._document.removeEventListener("contextmenu",this,!0))}_showOverlay(e,t){let i,s,n,a,{zone:r,target:o}=G.findDropTarget(this,e,t,this._edges);if("invalid"===r)return this.overlay.hide(100),r;let h=d.ElementExt.boxSizing(this.node),l=this.node.getBoundingClientRect();switch(r){case"root-all":i=h.paddingTop,s=h.paddingLeft,n=h.paddingRight,a=h.paddingBottom;break;case"root-top":i=h.paddingTop,s=h.paddingLeft,n=h.paddingRight,a=l.height*G.GOLDEN_RATIO;break;case"root-left":i=h.paddingTop,s=h.paddingLeft,n=l.width*G.GOLDEN_RATIO,a=h.paddingBottom;break;case"root-right":i=h.paddingTop,s=l.width*G.GOLDEN_RATIO,n=h.paddingRight,a=h.paddingBottom;break;case"root-bottom":i=l.height*G.GOLDEN_RATIO,s=h.paddingLeft,n=h.paddingRight,a=h.paddingBottom;break;case"widget-all":i=o.top,s=o.left,n=o.right,a=o.bottom;break;case"widget-top":i=o.top,s=o.left,n=o.right,a=o.bottom+o.height/2;break;case"widget-left":i=o.top,s=o.left,n=o.right+o.width/2,a=o.bottom;break;case"widget-right":i=o.top,s=o.left+o.width/2,n=o.right,a=o.bottom;break;case"widget-bottom":i=o.top+o.height/2,s=o.left,n=o.right,a=o.bottom;break;case"widget-tab":{const e=o.tabBar.node.getBoundingClientRect().height;i=o.top,s=o.left,n=o.right,a=o.bottom+o.height-e;break}default:throw"unreachable"}return this.overlay.show({top:i,left:s,right:n,bottom:a}),r}_createTabBar(){let e=this._renderer.createTabBar(this._document);return G.isGeneratedTabBarProperty.set(e,!0),"single-document"===this._mode&&e.hide(),e.tabsMovable=this._tabsMovable,e.allowDeselect=!1,e.addButtonEnabled=this._addButtonEnabled,e.removeBehavior="select-previous-tab",e.insertBehavior="select-tab-if-needed",e.tabMoved.connect(this._onTabMoved,this),e.currentChanged.connect(this._onCurrentChanged,this),e.tabCloseRequested.connect(this._onTabCloseRequested,this),e.tabDetachRequested.connect(this._onTabDetachRequested,this),e.tabActivateRequested.connect(this._onTabActivateRequested,this),e.addRequested.connect(this._onTabAddRequested,this),e}_createHandle(){return this._renderer.createHandle()}_onTabMoved(){l.MessageLoop.postMessage(this,G.LayoutModified)}_onCurrentChanged(e,t){let{previousTitle:i,currentTitle:s}=t;i&&i.owner.hide(),s&&s.owner.show(),(d.Platform.IS_EDGE||d.Platform.IS_IE)&&l.MessageLoop.flush(),l.MessageLoop.postMessage(this,G.LayoutModified)}_onTabAddRequested(e){this._addRequested.emit(e)}_onTabActivateRequested(e,t){t.title.owner.activate()}_onTabCloseRequested(e,t){t.title.owner.close()}_onTabDetachRequested(e,t){if(this._drag)return;e.releaseMouse();let{title:i,tab:s,clientX:n,clientY:a,offset:r}=t,o=new h.MimeData;o.setData("application/vnd.lumino.widget-factory",(()=>i.owner));let d=s.cloneNode(!0);r&&(d.style.top=`-${r.y}px`,d.style.left=`-${r.x}px`),this._drag=new m.Drag({document:this._document,mimeData:o,dragImage:d,proposedAction:"move",supportedActions:"move",source:this}),s.classList.add("lm-mod-hidden"),this._drag.start(n,a).then((()=>{this._drag=null,s.classList.remove("lm-mod-hidden")}))}}!function(e){e.Overlay=class{constructor(){this._timer=-1,this._hidden=!0,this.node=document.createElement("div"),this.node.classList.add("lm-DockPanel-overlay"),this.node.classList.add("lm-mod-hidden"),this.node.style.position="absolute",this.node.style.contain="strict"}show(e){let t=this.node.style;t.top=`${e.top}px`,t.left=`${e.left}px`,t.right=`${e.right}px`,t.bottom=`${e.bottom}px`,clearTimeout(this._timer),this._timer=-1,this._hidden&&(this._hidden=!1,this.node.classList.remove("lm-mod-hidden"))}hide(e){if(!this._hidden)return e<=0?(clearTimeout(this._timer),this._timer=-1,this._hidden=!0,void this.node.classList.add("lm-mod-hidden")):void(-1===this._timer&&(this._timer=window.setTimeout((()=>{this._timer=-1,this._hidden=!0,this.node.classList.add("lm-mod-hidden")}),e)))}};class t{createTabBar(e){let t=new Y({document:e});return t.addClass("lm-DockPanel-tabBar"),t}createHandle(){let e=document.createElement("div");return e.className="lm-DockPanel-handle",e}}e.Renderer=t,e.defaultRenderer=new t}(ie||(ie={})),function(e){e.GOLDEN_RATIO=.618,e.DEFAULT_EDGES={top:12,right:40,bottom:40,left:40},e.LayoutModified=new l.ConflatableMessage("layout-modified"),e.isGeneratedTabBarProperty=new c.AttachedProperty({name:"isGeneratedTabBar",create:()=>!1}),e.createSingleDocumentConfig=function(e){if(e.isEmpty)return{main:null};let t=Array.from(e.widgets()),i=e.selectedWidgets().next().value,s=i?t.indexOf(i):-1;return{main:{type:"tab-area",widgets:t,currentIndex:s}}},e.findDropTarget=function(e,t,i,s){if(!d.ElementExt.hitTest(e.node,t,i))return{zone:"invalid",target:null};let n=e.layout;if(n.isEmpty)return{zone:"root-all",target:null};if("multiple-document"===e.mode){let n=e.node.getBoundingClientRect(),a=t-n.left+1,r=i-n.top+1,o=n.right-t,h=n.bottom-i;switch(Math.min(r,o,h,a)){case r:if(r<s.top)return{zone:"root-top",target:null};break;case o:if(o<s.right)return{zone:"root-right",target:null};break;case h:if(h<s.bottom)return{zone:"root-bottom",target:null};break;case a:if(a<s.left)return{zone:"root-left",target:null};break;default:throw"unreachable"}}let a=n.hitTestTabAreas(t,i);if(!a)return{zone:"invalid",target:null};if("single-document"===e.mode)return{zone:"widget-all",target:a};let r=a.x-a.left+1,o=a.y-a.top+1,h=a.left+a.width-a.x,l=a.top+a.height-a.y;if(o<a.tabBar.node.getBoundingClientRect().height)return{zone:"widget-tab",target:a};let c,u=Math.round(a.width/3),m=Math.round(a.height/3);if(r>u&&h>u&&o>m&&l>m)return{zone:"widget-all",target:a};switch(r/=u,o/=m,h/=u,l/=m,Math.min(r,o,h,l)){case r:c="widget-left";break;case o:c="widget-top";break;case h:c="widget-right";break;case l:c="widget-bottom";break;default:throw"unreachable"}return{zone:c,target:a}},e.getDropRef=function(e){return 0===e.titles.length?null:e.currentTitle?e.currentTitle.owner:e.titles[e.titles.length-1].owner}}(G||(G={}));class se{constructor(){this._counter=0,this._widgets=[],this._activeWidget=null,this._currentWidget=null,this._numbers=new Map,this._nodes=new Map,this._activeChanged=new u.Signal(this),this._currentChanged=new u.Signal(this)}dispose(){if(!(this._counter<0)){this._counter=-1,u.Signal.clearData(this);for(const e of this._widgets)e.node.removeEventListener("focus",this,!0),e.node.removeEventListener("blur",this,!0);this._activeWidget=null,this._currentWidget=null,this._nodes.clear(),this._numbers.clear(),this._widgets.length=0}}get currentChanged(){return this._currentChanged}get activeChanged(){return this._activeChanged}get isDisposed(){return this._counter<0}get currentWidget(){return this._currentWidget}get activeWidget(){return this._activeWidget}get widgets(){return this._widgets}focusNumber(e){let t=this._numbers.get(e);return void 0===t?-1:t}has(e){return this._numbers.has(e)}add(e){if(this._numbers.has(e))return;let t=e.node.contains(document.activeElement),i=t?this._counter++:-1;this._widgets.push(e),this._numbers.set(e,i),this._nodes.set(e.node,e),e.node.addEventListener("focus",this,!0),e.node.addEventListener("blur",this,!0),e.disposed.connect(this._onWidgetDisposed,this),t&&this._setWidgets(e,e)}remove(e){if(!this._numbers.has(e))return;if(e.disposed.disconnect(this._onWidgetDisposed,this),e.node.removeEventListener("focus",this,!0),e.node.removeEventListener("blur",this,!0),o.ArrayExt.removeFirstOf(this._widgets,e),this._nodes.delete(e.node),this._numbers.delete(e),this._currentWidget!==e)return;let t=this._widgets.filter((e=>-1!==this._numbers.get(e))),i=(0,o.max)(t,((e,t)=>this._numbers.get(e)-this._numbers.get(t)))||null;this._setWidgets(i,null)}handleEvent(e){switch(e.type){case"focus":this._evtFocus(e);break;case"blur":this._evtBlur(e)}}_setWidgets(e,t){let i=this._currentWidget;this._currentWidget=e;let s=this._activeWidget;this._activeWidget=t,i!==e&&this._currentChanged.emit({oldValue:i,newValue:e}),s!==t&&this._activeChanged.emit({oldValue:s,newValue:t})}_evtFocus(e){let t=this._nodes.get(e.currentTarget);t!==this._currentWidget&&this._numbers.set(t,this._counter++),this._setWidgets(t,t)}_evtBlur(e){let t=this._nodes.get(e.currentTarget),i=e.relatedTarget;i&&(t.node.contains(i)||(0,o.find)(this._widgets,(e=>e.node.contains(i))))||this._setWidgets(this._currentWidget,null)}_onWidgetDisposed(e){this.remove(e)}}class ne extends x{constructor(e={}){super(e),this._dirty=!1,this._rowSpacing=4,this._columnSpacing=4,this._items=[],this._rowStarts=[],this._columnStarts=[],this._rowSizers=[new b],this._columnSizers=[new b],this._box=null,void 0!==e.rowCount&&j.reallocSizers(this._rowSizers,e.rowCount),void 0!==e.columnCount&&j.reallocSizers(this._columnSizers,e.columnCount),void 0!==e.rowSpacing&&(this._rowSpacing=j.clampValue(e.rowSpacing)),void 0!==e.columnSpacing&&(this._columnSpacing=j.clampValue(e.columnSpacing))}dispose(){for(const e of this._items){let t=e.widget;e.dispose(),t.dispose()}this._box=null,this._items.length=0,this._rowStarts.length=0,this._rowSizers.length=0,this._columnStarts.length=0,this._columnSizers.length=0,super.dispose()}get rowCount(){return this._rowSizers.length}set rowCount(e){e!==this.rowCount&&(j.reallocSizers(this._rowSizers,e),this.parent&&this.parent.fit())}get columnCount(){return this._columnSizers.length}set columnCount(e){e!==this.columnCount&&(j.reallocSizers(this._columnSizers,e),this.parent&&this.parent.fit())}get rowSpacing(){return this._rowSpacing}set rowSpacing(e){e=j.clampValue(e),this._rowSpacing!==e&&(this._rowSpacing=e,this.parent&&this.parent.fit())}get columnSpacing(){return this._columnSpacing}set columnSpacing(e){e=j.clampValue(e),this._columnSpacing!==e&&(this._columnSpacing=e,this.parent&&this.parent.fit())}rowStretch(e){let t=this._rowSizers[e];return t?t.stretch:-1}setRowStretch(e,t){let i=this._rowSizers[e];i&&(t=j.clampValue(t),i.stretch!==t&&(i.stretch=t,this.parent&&this.parent.update()))}columnStretch(e){let t=this._columnSizers[e];return t?t.stretch:-1}setColumnStretch(e,t){let i=this._columnSizers[e];i&&(t=j.clampValue(t),i.stretch!==t&&(i.stretch=t,this.parent&&this.parent.update()))}*[Symbol.iterator](){for(const e of this._items)yield e.widget}addWidget(e){-1===o.ArrayExt.findFirstIndex(this._items,(t=>t.widget===e))&&(this._items.push(new y(e)),this.parent&&this.attachWidget(e))}removeWidget(e){let t=o.ArrayExt.findFirstIndex(this._items,(t=>t.widget===e));if(-1===t)return;let i=o.ArrayExt.removeAt(this._items,t);this.parent&&this.detachWidget(e),i.dispose()}init(){super.init();for(const e of this)this.attachWidget(e)}attachWidget(e){this.parent.isAttached&&l.MessageLoop.sendMessage(e,M.Msg.BeforeAttach),this.parent.node.appendChild(e.node),this.parent.isAttached&&l.MessageLoop.sendMessage(e,M.Msg.AfterAttach),this.parent.fit()}detachWidget(e){this.parent.isAttached&&l.MessageLoop.sendMessage(e,M.Msg.BeforeDetach),this.parent.node.removeChild(e.node),this.parent.isAttached&&l.MessageLoop.sendMessage(e,M.Msg.AfterDetach),this.parent.fit()}onBeforeShow(e){super.onBeforeShow(e),this.parent.update()}onBeforeAttach(e){super.onBeforeAttach(e),this.parent.fit()}onChildShown(e){this.parent.fit()}onChildHidden(e){this.parent.fit()}onResize(e){this.parent.isVisible&&this._update(e.width,e.height)}onUpdateRequest(e){this.parent.isVisible&&this._update(-1,-1)}onFitRequest(e){this.parent.isAttached&&this._fit()}_fit(){for(let e=0,t=this.rowCount;e<t;++e)this._rowSizers[e].minSize=0;for(let e=0,t=this.columnCount;e<t;++e)this._columnSizers[e].minSize=0;let e=this._items.filter((e=>!e.isHidden));for(let t=0,i=e.length;t<i;++t)e[t].fit();let t=this.rowCount-1,i=this.columnCount-1;e.sort(j.rowSpanCmp);for(let i=0,s=e.length;i<s;++i){let s=e[i],n=ne.getCellConfig(s.widget),a=Math.min(n.row,t),r=Math.min(n.row+n.rowSpan-1,t);j.distributeMin(this._rowSizers,a,r,s.minHeight)}e.sort(j.columnSpanCmp);for(let t=0,s=e.length;t<s;++t){let s=e[t],n=ne.getCellConfig(s.widget),a=Math.min(n.column,i),r=Math.min(n.column+n.columnSpan-1,i);j.distributeMin(this._columnSizers,a,r,s.minWidth)}if("set-no-constraint"===this.fitPolicy)return void l.MessageLoop.sendMessage(this.parent,M.Msg.UpdateRequest);let s=t*this._rowSpacing,n=i*this._columnSpacing;for(let e=0,t=this.rowCount;e<t;++e)s+=this._rowSizers[e].minSize;for(let e=0,t=this.columnCount;e<t;++e)n+=this._columnSizers[e].minSize;let a=this._box=d.ElementExt.boxSizing(this.parent.node);n+=a.horizontalSum,s+=a.verticalSum;let r=this.parent.node.style;r.minWidth=`${n}px`,r.minHeight=`${s}px`,this._dirty=!0,this.parent.parent&&l.MessageLoop.sendMessage(this.parent.parent,M.Msg.FitRequest),this._dirty&&l.MessageLoop.sendMessage(this.parent,M.Msg.UpdateRequest)}_update(e,t){this._dirty=!1,e<0&&(e=this.parent.node.offsetWidth),t<0&&(t=this.parent.node.offsetHeight),this._box||(this._box=d.ElementExt.boxSizing(this.parent.node));let i=this._box.paddingTop,n=this._box.paddingLeft,a=e-this._box.horizontalSum,r=t-this._box.verticalSum,o=this.rowCount-1,h=this.columnCount-1,l=o*this._rowSpacing,c=h*this._columnSpacing;s.calc(this._rowSizers,Math.max(0,r-l)),s.calc(this._columnSizers,Math.max(0,a-c));for(let e=0,t=i,s=this.rowCount;e<s;++e)this._rowStarts[e]=t,t+=this._rowSizers[e].size+this._rowSpacing;for(let e=0,t=n,i=this.columnCount;e<i;++e)this._columnStarts[e]=t,t+=this._columnSizers[e].size+this._columnSpacing;for(let e=0,t=this._items.length;e<t;++e){let t=this._items[e];if(t.isHidden)continue;let i=ne.getCellConfig(t.widget),s=Math.min(i.row,o),n=Math.min(i.column,h),a=Math.min(i.row+i.rowSpan-1,o),r=Math.min(i.column+i.columnSpan-1,h),d=this._columnStarts[n],l=this._rowStarts[s],c=this._columnStarts[r]+this._columnSizers[r].size-d,u=this._rowStarts[a]+this._rowSizers[a].size-l;t.update(d,l,c,u)}}}!function(e){e.getCellConfig=function(e){return j.cellConfigProperty.get(e)},e.setCellConfig=function(e,t){j.cellConfigProperty.set(e,j.normalizeConfig(t))}}(ne||(ne={})),function(e){e.cellConfigProperty=new c.AttachedProperty({name:"cellConfig",create:()=>({row:0,column:0,rowSpan:1,columnSpan:1}),changed:function(e){e.parent&&e.parent.layout instanceof ne&&e.parent.fit()}}),e.normalizeConfig=function(e){return{row:Math.max(0,Math.floor(e.row||0)),column:Math.max(0,Math.floor(e.column||0)),rowSpan:Math.max(1,Math.floor(e.rowSpan||0)),columnSpan:Math.max(1,Math.floor(e.columnSpan||0))}},e.clampValue=function(e){return Math.max(0,Math.floor(e))},e.rowSpanCmp=function(t,i){let s=e.cellConfigProperty.get(t.widget),n=e.cellConfigProperty.get(i.widget);return s.rowSpan-n.rowSpan},e.columnSpanCmp=function(t,i){let s=e.cellConfigProperty.get(t.widget),n=e.cellConfigProperty.get(i.widget);return s.columnSpan-n.columnSpan},e.reallocSizers=function(e,t){for(t=Math.max(1,Math.floor(t));e.length<t;)e.push(new b);e.length>t&&(e.length=t)},e.distributeMin=function(e,t,i,s){if(i<t)return;if(t===i){let i=e[t];return void(i.minSize=Math.max(i.minSize,s))}let n=0;for(let s=t;s<=i;++s)n+=e[s].minSize;if(n>=s)return;let a=(s-n)/(i-t+1);for(let s=t;s<=i;++s)e[s].minSize+=a}}(j||(j={}));class ae extends M{constructor(e={}){super({node:J.createNode()}),this._activeIndex=-1,this._tabFocusIndex=0,this._menus=[],this._childMenu=null,this._overflowMenu=null,this._menuItemSizes=[],this._overflowIndex=-1,this.addClass("lm-MenuBar"),this.setFlag(M.Flag.DisallowLayout),this.renderer=e.renderer||ae.defaultRenderer,this._forceItemsPosition=e.forceItemsPosition||{forceX:!0,forceY:!0},this._overflowMenuOptions=e.overflowMenuOptions||{isVisible:!0}}dispose(){this._closeChildMenu(),this._menus.length=0,super.dispose()}get childMenu(){return this._childMenu}get overflowIndex(){return this._overflowIndex}get overflowMenu(){return this._overflowMenu}get contentNode(){return this.node.getElementsByClassName("lm-MenuBar-content")[0]}get activeMenu(){return this._menus[this._activeIndex]||null}set activeMenu(e){this.activeIndex=e?this._menus.indexOf(e):-1}get activeIndex(){return this._activeIndex}set activeIndex(e){(e<0||e>=this._menus.length)&&(e=-1),e>-1&&0===this._menus[e].items.length&&(e=-1),this._activeIndex!==e&&(this._activeIndex=e,this.update())}get menus(){return this._menus}openActiveMenu(){-1!==this._activeIndex&&(this._openChildMenu(),this._childMenu&&(this._childMenu.activeIndex=-1,this._childMenu.activateNextItem()))}addMenu(e,t=!0){this.insertMenu(this._menus.length,e,t)}insertMenu(e,t,i=!0){this._closeChildMenu();let s=this._menus.indexOf(t),n=Math.max(0,Math.min(e,this._menus.length));if(-1===s)return o.ArrayExt.insert(this._menus,n,t),t.addClass("lm-MenuBar-menu"),t.aboutToClose.connect(this._onMenuAboutToClose,this),t.menuRequested.connect(this._onMenuMenuRequested,this),t.title.changed.connect(this._onTitleChanged,this),void(i&&this.update());n===this._menus.length&&n--,s!==n&&(o.ArrayExt.move(this._menus,s,n),i&&this.update())}removeMenu(e,t=!0){this.removeMenuAt(this._menus.indexOf(e),t)}removeMenuAt(e,t=!0){this._closeChildMenu();let i=o.ArrayExt.removeAt(this._menus,e);i&&(i.aboutToClose.disconnect(this._onMenuAboutToClose,this),i.menuRequested.disconnect(this._onMenuMenuRequested,this),i.title.changed.disconnect(this._onTitleChanged,this),i.removeClass("lm-MenuBar-menu"),t&&this.update())}clearMenus(){if(0!==this._menus.length){this._closeChildMenu();for(let e of this._menus)e.aboutToClose.disconnect(this._onMenuAboutToClose,this),e.menuRequested.disconnect(this._onMenuMenuRequested,this),e.title.changed.disconnect(this._onTitleChanged,this),e.removeClass("lm-MenuBar-menu");this._menus.length=0,this.update()}}handleEvent(e){switch(e.type){case"keydown":this._evtKeyDown(e);break;case"mousedown":this._evtMouseDown(e);break;case"mousemove":this._evtMouseMove(e);break;case"focusout":this._evtFocusOut(e);break;case"contextmenu":e.preventDefault(),e.stopPropagation()}}onBeforeAttach(e){this.node.addEventListener("keydown",this),this.node.addEventListener("mousedown",this),this.node.addEventListener("mousemove",this),this.node.addEventListener("focusout",this),this.node.addEventListener("contextmenu",this)}onAfterDetach(e){this.node.removeEventListener("keydown",this),this.node.removeEventListener("mousedown",this),this.node.removeEventListener("mousemove",this),this.node.removeEventListener("focusout",this),this.node.removeEventListener("contextmenu",this),this._closeChildMenu()}onActivateRequest(e){this.isAttached&&this._focusItemAt(0)}onResize(e){this.update(),super.onResize(e)}onUpdateRequest(e){var t;let i=this._menus,s=this.renderer,n=this._activeIndex,a=this._tabFocusIndex>=0&&this._tabFocusIndex<i.length?this._tabFocusIndex:0,r=this._overflowIndex>-1?this._overflowIndex:i.length,o=0,h=!1;r=null!==this._overflowMenu?r-1:r;let d=new Array(r);for(let e=0;e<r;++e)d[e]=s.renderItem({title:i[e].title,active:e===n,tabbable:e===a,disabled:0===i[e].items.length,onfocus:()=>{this._tabFocusIndex=e,this.activeIndex=e}}),o+=this._menuItemSizes[e],i[e].title.label===this._overflowMenuOptions.title&&(h=!0,r--);if(this._overflowMenuOptions.isVisible)if(this._overflowIndex>-1&&!h){if(null===this._overflowMenu){const e=null!==(t=this._overflowMenuOptions.title)&&void 0!==t?t:"...";this._overflowMenu=new $({commands:new g.CommandRegistry}),this._overflowMenu.title.label=e,this._overflowMenu.title.mnemonic=0,this.addMenu(this._overflowMenu,!1)}for(let e=i.length-2;e>=r;e--){const t=this.menus[e];t.title.mnemonic=0,this._overflowMenu.insertItem(0,{type:"submenu",submenu:t}),this.removeMenu(t,!1)}d[r]=s.renderItem({title:this._overflowMenu.title,active:r===n&&0!==i[r].items.length,tabbable:r===a,disabled:0===i[r].items.length,onfocus:()=>{this._tabFocusIndex=r,this.activeIndex=r}}),r++}else if(null!==this._overflowMenu){let e=this._overflowMenu.items,t=this.node.offsetWidth,n=this._overflowMenu.items.length;for(let h=0;h<n;++h){let n=i.length-1-h;if(t-o>this._menuItemSizes[n]){let t=e[0].submenu;this._overflowMenu.removeItemAt(0),this.insertMenu(r,t,!1),d[r]=s.renderItem({title:t.title,active:!1,tabbable:r===a,disabled:0===i[r].items.length,onfocus:()=>{this._tabFocusIndex=r,this.activeIndex=r}}),r++}}0===this._overflowMenu.items.length&&(this.removeMenu(this._overflowMenu,!1),d.pop(),this._overflowMenu=null,this._overflowIndex=-1)}p.VirtualDOM.render(d,this.contentNode),this._updateOverflowIndex()}_updateOverflowIndex(){if(!this._overflowMenuOptions.isVisible)return;const e=this.contentNode.childNodes;let t=this.node.offsetWidth,i=0,s=-1,n=e.length;if(0==this._menuItemSizes.length)for(let a=0;a<n;a++){let n=e[a];i+=n.offsetWidth,this._menuItemSizes.push(n.offsetWidth),i>t&&-1===s&&(s=a)}else for(let e=0;e<this._menuItemSizes.length;e++)if(i+=this._menuItemSizes[e],i>t){s=e;break}this._overflowIndex=s}_evtKeyDown(e){let t=e.keyCode;if(9===t)return void(this.activeIndex=-1);if(e.preventDefault(),e.stopPropagation(),13===t||32===t||38===t||40===t){if(this.activeIndex=this._tabFocusIndex,this.activeIndex!==this._tabFocusIndex)return;return void this.openActiveMenu()}if(27===t)return this._closeChildMenu(),void this._focusItemAt(this.activeIndex);if(37===t||39===t){let e=37===t?-1:1,i=this._tabFocusIndex+e,s=this._menus.length;for(let t=0;t<s;t++){let n=(s+i+e*t)%s;if(this._menus[n].items.length)return void this._focusItemAt(n)}return}let i=(0,f.getKeyboardLayout)().keyForKeydownEvent(e);if(!i)return;let s=this._activeIndex+1,n=J.findMnemonic(this._menus,i,s);-1===n.index||n.multiple?-1!==n.index?(this.activeIndex=n.index,this._focusItemAt(this.activeIndex)):-1!==n.auto&&(this.activeIndex=n.auto,this._focusItemAt(this.activeIndex)):(this.activeIndex=n.index,this.openActiveMenu())}_evtMouseDown(e){if(!d.ElementExt.hitTest(this.node,e.clientX,e.clientY))return;e.stopPropagation(),e.stopImmediatePropagation();let t=o.ArrayExt.findFirstIndex(this.contentNode.children,(t=>d.ElementExt.hitTest(t,e.clientX,e.clientY)));if(-1!==t){if(0===e.button)if(this._childMenu)this._closeChildMenu(),this.activeIndex=t;else{e.preventDefault();const i=this._positionForMenu(t);$.saveWindowData(),this.activeIndex=t,this._openChildMenu(i)}}else this._closeChildMenu()}_evtMouseMove(e){let t=o.ArrayExt.findFirstIndex(this.contentNode.children,(t=>d.ElementExt.hitTest(t,e.clientX,e.clientY)));if(t===this._activeIndex)return;if(-1===t&&this._childMenu)return;const i=t>=0&&this._childMenu?this._positionForMenu(t):null;$.saveWindowData(),this.activeIndex=t,i&&this._openChildMenu(i)}_positionForMenu(e){let t=this.contentNode.children[e],{left:i,bottom:s}=t.getBoundingClientRect();return{top:s,left:i}}_evtFocusOut(e){this._childMenu||this.node.contains(e.relatedTarget)||(this.activeIndex=-1)}_focusItemAt(e){const t=this.contentNode.childNodes[e];t&&t.focus()}_openChildMenu(e={}){let t=this.activeMenu;if(!t)return void this._closeChildMenu();let i=this._childMenu;if(i===t)return;this._childMenu=t,i?i.close():document.addEventListener("mousedown",this,!0),this._tabFocusIndex=this.activeIndex,l.MessageLoop.sendMessage(this,M.Msg.UpdateRequest);let{left:s,top:n}=e;void 0!==s&&void 0!==n||({left:s,top:n}=this._positionForMenu(this._activeIndex)),i||this.addClass("lm-mod-active"),t.items.length>0&&t.open(s,n,this._forceItemsPosition)}_closeChildMenu(){if(!this._childMenu)return;this.removeClass("lm-mod-active"),document.removeEventListener("mousedown",this,!0);let e=this._childMenu;this._childMenu=null,e.close(),this.activeIndex=-1}_onMenuAboutToClose(e){e===this._childMenu&&(this.removeClass("lm-mod-active"),document.removeEventListener("mousedown",this,!0),this._childMenu=null,this.activeIndex=-1)}_onMenuMenuRequested(e,t){if(e!==this._childMenu)return;let i=this._activeIndex,s=this._menus.length;switch(t){case"next":this.activeIndex=i===s-1?0:i+1;break;case"previous":this.activeIndex=0===i?s-1:i-1}this.openActiveMenu()}_onTitleChanged(){this.update()}}!function(e){class t{renderItem(e){let t=this.createItemClass(e),i=this.createItemDataset(e),s=this.createItemARIA(e);return p.h.li({className:t,dataset:i,...e.disabled?{}:{tabindex:e.tabbable?"0":"-1"},onfocus:e.onfocus,...s},this.renderIcon(e),this.renderLabel(e))}renderIcon(e){let t=this.createIconClass(e);return p.h.div({className:t},e.title.icon,e.title.iconLabel)}renderLabel(e){let t=this.formatLabel(e);return p.h.div({className:"lm-MenuBar-itemLabel"},t)}createItemClass(e){let t="lm-MenuBar-item";return e.title.className&&(t+=` ${e.title.className}`),e.active&&!e.disabled&&(t+=" lm-mod-active"),t}createItemDataset(e){return e.title.dataset}createItemARIA(e){return{role:"menuitem","aria-haspopup":"true","aria-disabled":e.disabled?"true":"false"}}createIconClass(e){let t="lm-MenuBar-itemIcon",i=e.title.iconClass;return i?`${t} ${i}`:t}formatLabel(e){let{label:t,mnemonic:i}=e.title;if(i<0||i>=t.length)return t;let s=t.slice(0,i),n=t.slice(i+1),a=t[i];return[s,p.h.span({className:"lm-MenuBar-itemMnemonic"},a),n]}}e.Renderer=t,e.defaultRenderer=new t}(ae||(ae={})),function(e){e.createNode=function(){let e=document.createElement("div"),t=document.createElement("ul");return t.className="lm-MenuBar-content",e.appendChild(t),t.setAttribute("role","menubar"),e},e.findMnemonic=function(e,t,i){let s=-1,n=-1,a=!1,r=t.toUpperCase();for(let t=0,o=e.length;t<o;++t){let h=(t+i)%o,d=e[h].title;if(0===d.label.length)continue;let l=d.mnemonic;l>=0&&l<d.label.length?d.label[l].toUpperCase()===r&&(-1===s?s=h:a=!0):-1===n&&d.label[0].toUpperCase()===r&&(n=h)}return{index:s,multiple:a,auto:n}}}(J||(J={}));class re extends M{constructor(e={}){super({node:Q.createNode()}),this._onRepeat=()=>{if(this._repeatTimer=-1,!this._pressData)return;let e=this._pressData.part;if("thumb"===e)return;this._repeatTimer=window.setTimeout(this._onRepeat,20);let t=this._pressData.mouseX,i=this._pressData.mouseY;if("decrement"!==e)if("increment"!==e){if("track"===e){if(!d.ElementExt.hitTest(this.trackNode,t,i))return;let e=this.thumbNode;if(d.ElementExt.hitTest(e,t,i))return;let s,n=e.getBoundingClientRect();return s="horizontal"===this._orientation?t<n.left?"decrement":"increment":i<n.top?"decrement":"increment",void this._pageRequested.emit(s)}}else{if(!d.ElementExt.hitTest(this.incrementNode,t,i))return;this._stepRequested.emit("increment")}else{if(!d.ElementExt.hitTest(this.decrementNode,t,i))return;this._stepRequested.emit("decrement")}},this._value=0,this._page=10,this._maximum=100,this._repeatTimer=-1,this._pressData=null,this._thumbMoved=new u.Signal(this),this._stepRequested=new u.Signal(this),this._pageRequested=new u.Signal(this),this.addClass("lm-ScrollBar"),this.setFlag(M.Flag.DisallowLayout),this._orientation=e.orientation||"vertical",this.dataset.orientation=this._orientation,void 0!==e.maximum&&(this._maximum=Math.max(0,e.maximum)),void 0!==e.page&&(this._page=Math.max(0,e.page)),void 0!==e.value&&(this._value=Math.max(0,Math.min(e.value,this._maximum)))}get thumbMoved(){return this._thumbMoved}get stepRequested(){return this._stepRequested}get pageRequested(){return this._pageRequested}get orientation(){return this._orientation}set orientation(e){this._orientation!==e&&(this._releaseMouse(),this._orientation=e,this.dataset.orientation=e,this.update())}get value(){return this._value}set value(e){e=Math.max(0,Math.min(e,this._maximum)),this._value!==e&&(this._value=e,this.update())}get page(){return this._page}set page(e){e=Math.max(0,e),this._page!==e&&(this._page=e,this.update())}get maximum(){return this._maximum}set maximum(e){e=Math.max(0,e),this._maximum!==e&&(this._maximum=e,this._value=Math.min(this._value,e),this.update())}get decrementNode(){return this.node.getElementsByClassName("lm-ScrollBar-button")[0]}get incrementNode(){return this.node.getElementsByClassName("lm-ScrollBar-button")[1]}get trackNode(){return this.node.getElementsByClassName("lm-ScrollBar-track")[0]}get thumbNode(){return this.node.getElementsByClassName("lm-ScrollBar-thumb")[0]}handleEvent(e){switch(e.type){case"mousedown":this._evtMouseDown(e);break;case"mousemove":this._evtMouseMove(e);break;case"mouseup":this._evtMouseUp(e);break;case"keydown":this._evtKeyDown(e);break;case"contextmenu":e.preventDefault(),e.stopPropagation()}}onBeforeAttach(e){this.node.addEventListener("mousedown",this),this.update()}onAfterDetach(e){this.node.removeEventListener("mousedown",this),this._releaseMouse()}onUpdateRequest(e){let t=100*this._value/this._maximum,i=100*this._page/(this._page+this._maximum);t=Math.max(0,Math.min(t,100)),i=Math.max(0,Math.min(i,100));let s=this.thumbNode.style;"horizontal"===this._orientation?(s.top="",s.height="",s.left=`${t}%`,s.width=`${i}%`,s.transform=`translate(${-t}%, 0%)`):(s.left="",s.width="",s.top=`${t}%`,s.height=`${i}%`,s.transform=`translate(0%, ${-t}%)`)}_evtKeyDown(e){if(e.preventDefault(),e.stopPropagation(),27!==e.keyCode)return;let t=this._pressData?this._pressData.value:-1;this._releaseMouse(),-1!==t&&this._moveThumb(t)}_evtMouseDown(e){if(0!==e.button)return;if(this.activate(),this._pressData)return;let t=Q.findPart(this,e.target);if(!t)return;e.preventDefault(),e.stopPropagation();let i=m.Drag.overrideCursor("default");if(this._pressData={part:t,override:i,delta:-1,value:-1,mouseX:e.clientX,mouseY:e.clientY},document.addEventListener("mousemove",this,!0),document.addEventListener("mouseup",this,!0),document.addEventListener("keydown",this,!0),document.addEventListener("contextmenu",this,!0),"thumb"===t){let t=this.thumbNode,i=t.getBoundingClientRect();return"horizontal"===this._orientation?this._pressData.delta=e.clientX-i.left:this._pressData.delta=e.clientY-i.top,t.classList.add("lm-mod-active"),void(this._pressData.value=this._value)}if("track"===t){let t,i=this.thumbNode.getBoundingClientRect();return t="horizontal"===this._orientation?e.clientX<i.left?"decrement":"increment":e.clientY<i.top?"decrement":"increment",this._repeatTimer=window.setTimeout(this._onRepeat,350),void this._pageRequested.emit(t)}return"decrement"===t?(this.decrementNode.classList.add("lm-mod-active"),this._repeatTimer=window.setTimeout(this._onRepeat,350),void this._stepRequested.emit("decrement")):"increment"===t?(this.incrementNode.classList.add("lm-mod-active"),this._repeatTimer=window.setTimeout(this._onRepeat,350),void this._stepRequested.emit("increment")):void 0}_evtMouseMove(e){if(!this._pressData)return;if(e.preventDefault(),e.stopPropagation(),this._pressData.mouseX=e.clientX,this._pressData.mouseY=e.clientY,"thumb"!==this._pressData.part)return;let t,i,s=this.thumbNode.getBoundingClientRect(),n=this.trackNode.getBoundingClientRect();"horizontal"===this._orientation?(t=e.clientX-n.left-this._pressData.delta,i=n.width-s.width):(t=e.clientY-n.top-this._pressData.delta,i=n.height-s.height);let a=0===i?0:t*this._maximum/i;this._moveThumb(a)}_evtMouseUp(e){0===e.button&&(e.preventDefault(),e.stopPropagation(),this._releaseMouse())}_releaseMouse(){this._pressData&&(clearTimeout(this._repeatTimer),this._repeatTimer=-1,this._pressData.override.dispose(),this._pressData=null,document.removeEventListener("mousemove",this,!0),document.removeEventListener("mouseup",this,!0),document.removeEventListener("keydown",this,!0),document.removeEventListener("contextmenu",this,!0),this.thumbNode.classList.remove("lm-mod-active"),this.decrementNode.classList.remove("lm-mod-active"),this.incrementNode.classList.remove("lm-mod-active"))}_moveThumb(e){e=Math.max(0,Math.min(e,this._maximum)),this._value!==e&&(this._value=e,this.update(),this._thumbMoved.emit(e))}}!function(e){e.createNode=function(){let e=document.createElement("div"),t=document.createElement("div"),i=document.createElement("div"),s=document.createElement("div"),n=document.createElement("div");return t.className="lm-ScrollBar-button",i.className="lm-ScrollBar-button",t.dataset.action="decrement",i.dataset.action="increment",s.className="lm-ScrollBar-track",n.className="lm-ScrollBar-thumb",s.appendChild(n),e.appendChild(t),e.appendChild(s),e.appendChild(i),e},e.findPart=function(e,t){return e.thumbNode.contains(t)?"thumb":e.trackNode.contains(t)?"track":e.decrementNode.contains(t)?"decrement":e.incrementNode.contains(t)?"increment":null}}(Q||(Q={}));class oe extends x{constructor(){super(...arguments),this._widget=null}dispose(){if(this._widget){let e=this._widget;this._widget=null,e.dispose()}super.dispose()}get widget(){return this._widget}set widget(e){e&&(e.parent=this.parent),this._widget!==e&&(this._widget&&this._widget.dispose(),this._widget=e,this.parent&&e&&this.attachWidget(e))}*[Symbol.iterator](){this._widget&&(yield this._widget)}removeWidget(e){this._widget===e&&(this._widget=null,this.parent&&this.detachWidget(e))}init(){super.init();for(const e of this)this.attachWidget(e)}attachWidget(e){this.parent.isAttached&&l.MessageLoop.sendMessage(e,M.Msg.BeforeAttach),this.parent.node.appendChild(e.node),this.parent.isAttached&&l.MessageLoop.sendMessage(e,M.Msg.AfterAttach)}detachWidget(e){this.parent.isAttached&&l.MessageLoop.sendMessage(e,M.Msg.BeforeDetach),this.parent.node.removeChild(e.node),this.parent.isAttached&&l.MessageLoop.sendMessage(e,M.Msg.AfterDetach)}}class he extends w{constructor(e={}){super(e),this._dirty=!1,this._items=[],this._box=null,this._hiddenMode=void 0!==e.hiddenMode?e.hiddenMode:M.HiddenMode.Display}get hiddenMode(){return this._hiddenMode}set hiddenMode(e){this._hiddenMode!==e&&(this._hiddenMode=e,this.widgets.length>1&&this.widgets.forEach((e=>{e.hiddenMode=this._hiddenMode})))}dispose(){for(const e of this._items)e.dispose();this._box=null,this._items.length=0,super.dispose()}attachWidget(e,t){this._hiddenMode===M.HiddenMode.Scale&&this._items.length>0?(1===this._items.length&&(this.widgets[0].hiddenMode=M.HiddenMode.Scale),t.hiddenMode=M.HiddenMode.Scale):t.hiddenMode=M.HiddenMode.Display,o.ArrayExt.insert(this._items,e,new y(t)),this.parent.isAttached&&l.MessageLoop.sendMessage(t,M.Msg.BeforeAttach),this.parent.node.appendChild(t.node),this.parent.isAttached&&l.MessageLoop.sendMessage(t,M.Msg.AfterAttach),this.parent.fit()}moveWidget(e,t,i){o.ArrayExt.move(this._items,e,t),this.parent.update()}detachWidget(e,t){let i=o.ArrayExt.removeAt(this._items,e);this.parent.isAttached&&l.MessageLoop.sendMessage(t,M.Msg.BeforeDetach),this.parent.node.removeChild(t.node),this.parent.isAttached&&l.MessageLoop.sendMessage(t,M.Msg.AfterDetach),i.widget.node.style.zIndex="",this._hiddenMode===M.HiddenMode.Scale&&(t.hiddenMode=M.HiddenMode.Display,1===this._items.length&&(this._items[0].widget.hiddenMode=M.HiddenMode.Display)),i.dispose(),this.parent.fit()}onBeforeShow(e){super.onBeforeShow(e),this.parent.update()}onBeforeAttach(e){super.onBeforeAttach(e),this.parent.fit()}onChildShown(e){this.parent.fit()}onChildHidden(e){this.parent.fit()}onResize(e){this.parent.isVisible&&this._update(e.width,e.height)}onUpdateRequest(e){this.parent.isVisible&&this._update(-1,-1)}onFitRequest(e){this.parent.isAttached&&this._fit()}_fit(){let e=0,t=0;for(let i=0,s=this._items.length;i<s;++i){let s=this._items[i];s.isHidden||(s.fit(),e=Math.max(e,s.minWidth),t=Math.max(t,s.minHeight))}let i=this._box=d.ElementExt.boxSizing(this.parent.node);e+=i.horizontalSum,t+=i.verticalSum;let s=this.parent.node.style;s.minWidth=`${e}px`,s.minHeight=`${t}px`,this._dirty=!0,this.parent.parent&&l.MessageLoop.sendMessage(this.parent.parent,M.Msg.FitRequest),this._dirty&&l.MessageLoop.sendMessage(this.parent,M.Msg.UpdateRequest)}_update(e,t){this._dirty=!1;let i=0;for(let e=0,t=this._items.length;e<t;++e)i+=+!this._items[e].isHidden;if(0===i)return;e<0&&(e=this.parent.node.offsetWidth),t<0&&(t=this.parent.node.offsetHeight),this._box||(this._box=d.ElementExt.boxSizing(this.parent.node));let s=this._box.paddingTop,n=this._box.paddingLeft,a=e-this._box.horizontalSum,r=t-this._box.verticalSum;for(let e=0,t=this._items.length;e<t;++e){let t=this._items[e];t.isHidden||(t.widget.node.style.zIndex=`${e}`,t.update(n,s,a,r))}}}class de extends P{constructor(e={}){super({layout:Z.createLayout(e)}),this._widgetRemoved=new u.Signal(this),this.addClass("lm-StackedPanel")}get hiddenMode(){return this.layout.hiddenMode}set hiddenMode(e){this.layout.hiddenMode=e}get widgetRemoved(){return this._widgetRemoved}onChildAdded(e){e.child.addClass("lm-StackedPanel-child")}onChildRemoved(e){e.child.removeClass("lm-StackedPanel-child"),this._widgetRemoved.emit(e.child)}}!function(e){e.createLayout=function(e){return e.layout||new he}}(Z||(Z={}));class le extends M{constructor(e={}){super(),this._currentChanged=new u.Signal(this),this._addRequested=new u.Signal(this),this.addClass("lm-TabPanel"),this.tabBar=new Y(e),this.tabBar.addClass("lm-TabPanel-tabBar"),this.stackedPanel=new de,this.stackedPanel.addClass("lm-TabPanel-stackedPanel"),this.tabBar.tabMoved.connect(this._onTabMoved,this),this.tabBar.currentChanged.connect(this._onCurrentChanged,this),this.tabBar.tabCloseRequested.connect(this._onTabCloseRequested,this),this.tabBar.tabActivateRequested.connect(this._onTabActivateRequested,this),this.tabBar.addRequested.connect(this._onTabAddRequested,this),this.stackedPanel.widgetRemoved.connect(this._onWidgetRemoved,this),this._tabPlacement=e.tabPlacement||"top";let t=ee.directionFromPlacement(this._tabPlacement),i=ee.orientationFromPlacement(this._tabPlacement);this.tabBar.orientation=i,this.tabBar.dataset.placement=this._tabPlacement;let s=new q({direction:t,spacing:0});q.setStretch(this.tabBar,0),q.setStretch(this.stackedPanel,1),s.addWidget(this.tabBar),s.addWidget(this.stackedPanel),this.layout=s}get currentChanged(){return this._currentChanged}get currentIndex(){return this.tabBar.currentIndex}set currentIndex(e){this.tabBar.currentIndex=e}get currentWidget(){let e=this.tabBar.currentTitle;return e?e.owner:null}set currentWidget(e){this.tabBar.currentTitle=e?e.title:null}get tabsMovable(){return this.tabBar.tabsMovable}set tabsMovable(e){this.tabBar.tabsMovable=e}get addButtonEnabled(){return this.tabBar.addButtonEnabled}set addButtonEnabled(e){this.tabBar.addButtonEnabled=e}get tabPlacement(){return this._tabPlacement}set tabPlacement(e){if(this._tabPlacement===e)return;this._tabPlacement=e;let t=ee.directionFromPlacement(e),i=ee.orientationFromPlacement(e);this.tabBar.orientation=i,this.tabBar.dataset.placement=e,this.layout.direction=t}get addRequested(){return this._addRequested}get widgets(){return this.stackedPanel.widgets}addWidget(e){this.insertWidget(this.widgets.length,e)}insertWidget(e,t){t!==this.currentWidget&&t.hide(),this.stackedPanel.insertWidget(e,t),this.tabBar.insertTab(e,t.title),t.node.setAttribute("role","tabpanel");let i=this.tabBar.renderer;if(i instanceof Y.Renderer){let e=i.createTabKey({title:t.title,current:!1,zIndex:0});t.node.setAttribute("aria-labelledby",e)}}_onCurrentChanged(e,t){let{previousIndex:i,previousTitle:s,currentIndex:n,currentTitle:a}=t,r=s?s.owner:null,o=a?a.owner:null;r&&r.hide(),o&&o.show(),this._currentChanged.emit({previousIndex:i,previousWidget:r,currentIndex:n,currentWidget:o}),(d.Platform.IS_EDGE||d.Platform.IS_IE)&&l.MessageLoop.flush()}_onTabAddRequested(e,t){this._addRequested.emit(e)}_onTabActivateRequested(e,t){t.title.owner.activate()}_onTabCloseRequested(e,t){t.title.owner.close()}_onTabMoved(e,t){this.stackedPanel.insertWidget(t.toIndex,t.title.owner)}_onWidgetRemoved(e,t){t.node.removeAttribute("role"),t.node.removeAttribute("aria-labelledby"),this.tabBar.removeTab(t.title)}}!function(e){e.orientationFromPlacement=function(e){return t[e]},e.directionFromPlacement=function(e){return i[e]};const t={top:"horizontal",left:"vertical",right:"vertical",bottom:"horizontal"},i={top:"top-to-bottom",left:"left-to-right",right:"right-to-left",bottom:"bottom-to-top"}}(ee||(ee={}))}}]);
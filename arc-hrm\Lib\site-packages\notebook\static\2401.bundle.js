(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[2401],{43967:(e,t,n)=>{"use strict";n.d(t,{g:()=>o});var r,s=n(11351),a=n(20998),i=n(81997);class o{constructor(e){this.trusted=!1,this._changed=new i.Signal(this),this._raw={};const t=r.getData(e.value);this._data=new s.ObservableJSON({values:t}),this._rawData=t;const n=e.value;for(const e in n)"data"===e||(this._raw[e]=r.extract(n,e))}get changed(){return this._changed}dispose(){this._data.dispose(),i.Signal.clearData(this)}get data(){return this._rawData}get metadata(){return{}}setData(e){e.data&&(this._updateObservable(this._data,e.data),this._rawData=e.data),this._changed.emit(void 0)}toJSON(){const e={};for(const t in this._raw)e[t]=r.extract(this._raw,t);return e}_updateObservable(e,t){const n=e.keys(),r=Object.keys(t);for(const t of n)-1===r.indexOf(t)&&e.delete(t);for(const n of r){const r=e.get(n),s=t[n];r!==s&&e.set(n,s)}}}!function(e){e.getData=function(e){return r.getData(e)}}(o||(o={})),function(e){function t(e){return function(e){const t=Object.create(null);for(const r in e)t[r]=n(e,r);return t}(e)}function n(e,t){const n=e[t];return void 0===n||a.JSONExt.isPrimitive(n)?n:a.JSONExt.deepCopy(n)}e.getData=t,e.getBundleOptions=function(e){return{data:t(e.value)}},e.extract=n}(r||(r={}))},15862:(e,t,n)=>{"use strict";n.d(t,{BJ:()=>d,Dq:()=>c,F:()=>l,Lz:()=>a,Nf:()=>u,hJ:()=>i,nF:()=>h,vy:()=>s,xr:()=>o});var r=n(28182);const s={safe:!0,mimeTypes:["text/html"],defaultRank:50,createRenderer:e=>new r.oI(e)},a={safe:!0,mimeTypes:["image/bmp","image/png","image/jpeg","image/gif","image/webp"],defaultRank:90,createRenderer:e=>new r.UH(e)},i={safe:!0,mimeTypes:["text/latex"],defaultRank:70,createRenderer:e=>new r.FK(e)},o={safe:!0,mimeTypes:["text/markdown"],defaultRank:60,createRenderer:e=>new r.cw(e)},l={safe:!1,mimeTypes:["image/svg+xml"],defaultRank:80,createRenderer:e=>new r.zt(e)},c={safe:!0,mimeTypes:["application/vnd.jupyter.stderr"],defaultRank:110,createRenderer:e=>new r.Hw(e)},d={safe:!0,mimeTypes:["text/plain","application/vnd.jupyter.stdout"],defaultRank:120,createRenderer:e=>new r.lH(e)},h={safe:!1,mimeTypes:["text/javascript","application/javascript"],defaultRank:110,createRenderer:e=>new r.ND(e)},u=[s,o,i,l,a,h,c,d]},72401:(e,t,n)=>{"use strict";n.r(t),n.d(t,{AttachmentModel:()=>a.g,ILatexTypesetter:()=>u._y,IMarkdownParser:()=>u.sc,IRenderMimeRegistry:()=>u.ZD,MimeModel:()=>l.a,OutputModel:()=>c.M,RenderMimeRegistry:()=>d.D,RenderedCommon:()=>p.pY,RenderedError:()=>p.Hw,RenderedHTML:()=>p.oI,RenderedHTMLCommon:()=>p.BP,RenderedImage:()=>p.UH,RenderedJavaScript:()=>p.ND,RenderedLatex:()=>p.FK,RenderedMarkdown:()=>p.cw,RenderedSVG:()=>p.zt,RenderedText:()=>p.lH,errorRendererFactory:()=>i.Dq,htmlRendererFactory:()=>i.vy,imageRendererFactory:()=>i.Lz,javaScriptRendererFactory:()=>i.nF,latexRendererFactory:()=>i.hJ,markdownRendererFactory:()=>i.xr,removeMath:()=>o.D,renderError:()=>h.Op,renderHTML:()=>h.NN,renderImage:()=>h.co,renderLatex:()=>h.K3,renderMarkdown:()=>h.ap,renderSVG:()=>h.KB,renderText:()=>h.IY,replaceMath:()=>o.b,standardRendererFactories:()=>i.Nf,svgRendererFactory:()=>i.F,textRendererFactory:()=>i.BJ});var r=n(56710),s={};for(const e in r)"default"!==e&&(s[e]=()=>r[e]);n.d(t,s);var a=n(43967),i=n(15862),o=n(69837),l=n(32021),c=n(4800),d=n(32549),h=n(3328),u=n(2186),p=n(28182)},69837:(e,t,n)=>{"use strict";n.d(t,{D:()=>a,b:()=>i});const r="$",s=/(\$\$?|\\(?:begin|end)\{[a-z]*\*?\}|\\[{}$]|[{}]|(?:\n\s*)+|@@\d+@@|\\\\(?:\(|\)|\[|\]))/i;function a(e){const t=[];let n,a=null,i=null,l=null,c=0;e.includes("`")||e.includes("~~~")?(e=e.replace(/~/g,"~T").replace(/^(?<fence>`{3,}|(~T){3,})[^`\n]*\n([\s\S]*?)^\k<fence>`*$/gm,(e=>e.replace(/\$/g,"~D"))).replace(/(^|[^\\])(`+)([^\n]*?[^`\n])\2(?!`)/gm,(e=>e.replace(/\$/g,"~D"))),n=e=>e.replace(/~([TD])/g,((e,t)=>"T"===t?"~":r))):n=e=>e;let d=e.replace(/\r\n?/g,"\n").split(s);for(let e=1,s=d.length;e<s;e+=2){const s=d[e];"@"===s.charAt(0)?(d[e]="@@"+t.length+"@@",t.push(s)):null!==a?s===i?c?l=e:(d=o(a,e,n,t,d),a=null,i=null,l=null):s.match(/\n.*\n/)?(null!==l&&(e=l,d=o(a,e,n,t,d)),a=null,i=null,l=null,c=0):"{"===s?c++:"}"===s&&c&&c--:s===r||"$$"===s?(a=e,i=s,c=0):"\\\\("===s||"\\\\["===s?(a=e,i="("===s.slice(-1)?"\\\\)":"\\\\]",c=0):"begin"===s.substr(1,5)&&(a=e,i="\\end"+s.substr(6),c=0)}return null!==a&&null!==l&&(d=o(a,l,n,t,d),a=null,i=null,l=null),{text:n(d.join("")),math:t}}function i(e,t){return e.replace(/@@(\d+)@@/g,((e,n)=>{let r=t[n];return"\\\\("===r.substr(0,3)&&"\\\\)"===r.substr(r.length-3)?r="\\("+r.substring(3,r.length-3)+"\\)":"\\\\["===r.substr(0,3)&&"\\\\]"===r.substr(r.length-3)&&(r="\\["+r.substring(3,r.length-3)+"\\]"),r}))}function o(e,t,n,r,s){let a=s.slice(e,t+1).join("").replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;");for(navigator&&"Microsoft Internet Explorer"===navigator.appName&&(a=a.replace(/(%[^\n]*)\n/g,"$1<br/>\n"));t>e;)s[t]="",t--;return s[e]="@@"+r.length+"@@",n&&(a=n(a)),r.push(a),s}},32021:(e,t,n)=>{"use strict";n.d(t,{a:()=>r});class r{constructor(e={}){this.trusted=!!e.trusted,this._data=e.data||{},this._metadata=e.metadata||{},this._callback=e.callback||s.noOp}get data(){return this._data}get metadata(){return this._metadata}setData(e){this._data=e.data||this._data,this._metadata=e.metadata||this._metadata,this._callback(e)}}var s;!function(e){e.noOp=function(){}}(s||(s={}))},4800:(e,t,n)=>{"use strict";n.d(t,{M:()=>l});var r,s=n(85923),a=n(11351),i=n(20998),o=n(81997);class l{constructor(e){this._changed=new o.Signal(this),this._raw={};const{data:t,metadata:n,trusted:i}=r.getBundleOptions(e);this._data=new a.ObservableJSON({values:t}),this._rawData=t,this._metadata=new a.ObservableJSON({values:n}),this._rawMetadata=n,this.trusted=i;const l=e.value;for(const e in l)switch(e){case"data":case"metadata":break;default:this._raw[e]=r.extract(l,e)}this.type=l.output_type,s.isExecuteResult(l)?this.executionCount=l.execution_count:this.executionCount=null}get changed(){return this._changed}dispose(){this._data.dispose(),this._metadata.dispose(),o.Signal.clearData(this)}get data(){return this._rawData}get metadata(){return this._rawMetadata}setData(e){e.data&&(this._updateObservable(this._data,e.data),this._rawData=e.data),e.metadata&&(this._updateObservable(this._metadata,e.metadata),this._rawMetadata=e.metadata),this._changed.emit()}toJSON(){const e={};for(const t in this._raw)e[t]=r.extract(this._raw,t);switch(this.type){case"display_data":case"execute_result":case"update_display_data":e.data=this.data,e.metadata=this.metadata}return delete e.transient,e}_updateObservable(e,t){const n=e.keys(),r=Object.keys(t);for(const t of n)-1===r.indexOf(t)&&e.delete(t);for(const n of r){const r=e.get(n),s=t[n];r!==s&&e.set(n,s)}}}!function(e){e.getData=function(e){return r.getData(e)},e.getMetadata=function(e){return r.getMetadata(e)}}(l||(l={})),function(e){function t(e){let t={};if(s.isExecuteResult(e)||s.isDisplayData(e)||s.isDisplayUpdate(e))t=e.data;else if(s.isStream(e))"stderr"===e.name?t["application/vnd.jupyter.stderr"]=e.text:t["application/vnd.jupyter.stdout"]=e.text;else if(s.isError(e)){t["application/vnd.jupyter.error"]=e;const n=e.traceback.join("\n");t["application/vnd.jupyter.stderr"]=n||`${e.ename}: ${e.evalue}`}return function(e){const t=Object.create(null);for(const n in e)t[n]=r(e,n);return t}(t)}function n(e){const t=Object.create(null);if(s.isExecuteResult(e)||s.isDisplayData(e))for(const n in e.metadata)t[n]=r(e.metadata,n);return t}function r(e,t){const n=e[t];return void 0===n||i.JSONExt.isPrimitive(n)?n:JSON.parse(JSON.stringify(n))}e.getData=t,e.getMetadata=n,e.getBundleOptions=function(e){return{data:t(e.value),metadata:n(e.value),trusted:!!e.trusted}},e.extract=r}(r||(r={}))},32549:(e,t,n)=>{"use strict";n.d(t,{D:()=>l});var r,s=n(12982),a=n(38639),i=n(71677),o=n(32021);class l{constructor(e={}){var t,n,r,a,o,l;if(this._id=0,this._ranks={},this._types=null,this._factories={},this.translator=null!==(t=e.translator)&&void 0!==t?t:i.nullTranslator,this.resolver=null!==(n=e.resolver)&&void 0!==n?n:null,this.linkHandler=null!==(r=e.linkHandler)&&void 0!==r?r:null,this.latexTypesetter=null!==(a=e.latexTypesetter)&&void 0!==a?a:null,this.markdownParser=null!==(o=e.markdownParser)&&void 0!==o?o:null,this.sanitizer=null!==(l=e.sanitizer)&&void 0!==l?l:new s.Sanitizer,e.initialFactories)for(const t of e.initialFactories)this.addFactory(t)}get mimeTypes(){return this._types||(this._types=r.sortedTypes(this._ranks))}preferredMimeType(e,t="ensure"){if("ensure"===t||"prefer"===t)for(const t of this.mimeTypes)if(t in e&&this._factories[t].safe)return t;if("ensure"!==t)for(const t of this.mimeTypes)if(t in e)return t}createRenderer(e){if(!(e in this._factories))throw new Error(`No factory for mime type: '${e}'`);return this._factories[e].createRenderer({mimeType:e,resolver:this.resolver,sanitizer:this.sanitizer,linkHandler:this.linkHandler,latexTypesetter:this.latexTypesetter,markdownParser:this.markdownParser,translator:this.translator})}createModel(e={}){return new o.a(e)}clone(e={}){var t,n,r,s,a,i,o,c,d,h;const u=new l({resolver:null!==(n=null!==(t=e.resolver)&&void 0!==t?t:this.resolver)&&void 0!==n?n:void 0,sanitizer:null!==(s=null!==(r=e.sanitizer)&&void 0!==r?r:this.sanitizer)&&void 0!==s?s:void 0,linkHandler:null!==(i=null!==(a=e.linkHandler)&&void 0!==a?a:this.linkHandler)&&void 0!==i?i:void 0,latexTypesetter:null!==(c=null!==(o=e.latexTypesetter)&&void 0!==o?o:this.latexTypesetter)&&void 0!==c?c:void 0,markdownParser:null!==(h=null!==(d=e.markdownParser)&&void 0!==d?d:this.markdownParser)&&void 0!==h?h:void 0,translator:this.translator});return u._factories={...this._factories},u._ranks={...this._ranks},u._id=this._id,u}getFactory(e){return this._factories[e]}addFactory(e,t){void 0===t&&void 0===(t=e.defaultRank)&&(t=100);for(const n of e.mimeTypes)this._factories[n]=e,this._ranks[n]={rank:t,id:this._id++};this._types=null}removeMimeType(e){delete this._factories[e],delete this._ranks[e],this._types=null}getRank(e){const t=this._ranks[e];return t&&t.rank}setRank(e,t){if(!this._ranks[e])return;const n=this._id++;this._ranks[e]={rank:t,id:n},this._types=null}}!function(e){e.UrlResolver=class{constructor(e){this._path=e.path,this._contents=e.contents}get path(){return this._path}set path(e){this._path=e}async resolveUrl(e){if(this.isLocal(e)){const t=encodeURI(a.PathExt.dirname(this.path));e=a.PathExt.resolve(t,e)}return e}async getDownloadUrl(e){return this.isLocal(e)?this._contents.getDownloadUrl(decodeURIComponent(e)):e}isLocal(e,t=!1){return!this.isMalformed(e)&&(a.URLExt.isLocal(e,t)||!!this._contents.driveName(decodeURI(e)))}async resolvePath(e){const t=a.PageConfig.getOption("rootUri").replace("file://","");if(e.startsWith("~/")&&t.startsWith("/home/")&&(e=t.split("/").slice(0,3).join("/")+e.substring(1)),e.startsWith(t)||e.startsWith("./"))try{const n=e.replace(t,"");return{path:(await this._contents.get(n,{content:!1})).path,scope:"server"}}catch(t){return console.warn(`Could not resolve location of ${e} on server`),null}return{path:e,scope:"kernel"}}isMalformed(e){try{return decodeURI(e),!1}catch(e){if(e instanceof URIError)return!0;throw e}}}}(l||(l={})),function(e){e.sortedTypes=function(e){return Object.keys(e).sort(((t,n)=>{const r=e[t],s=e[n];return r.rank!==s.rank?r.rank-s.rank:r.id-s.id}))}}(r||(r={}))},3328:(e,t,n)=>{"use strict";n.d(t,{IY:()=>w,K3:()=>u,KB:()=>f,NN:()=>d,Op:()=>T,ap:()=>p,co:()=>h});var r,s,a=n(38639),i=n(71677),o=n(8872),l=n.n(o),c=n(69837);function d(e){let{host:t,source:n,trusted:r,sanitizer:a,resolver:o,linkHandler:l,shouldTypeset:c,latexTypesetter:d,translator:h}=e;h=h||i.nullTranslator;const u=null==h?void 0:h.load("jupyterlab");let p,f=n;if(!n)return t.textContent="",Promise.resolve(void 0);if(r||(f=`${n}`,n=a.sanitize(n)),t.innerHTML=n,t.getElementsByTagName("script").length>0)if(r)s.evalInnerHTMLScriptTags(t);else{const e=document.createElement("div"),n=document.createElement("pre");n.textContent=u.__("This HTML output contains inline scripts. Are you sure that you want to run arbitrary Javascript within your JupyterLab session?");const r=document.createElement("button");r.textContent=u.__("Run"),r.onclick=e=>{t.innerHTML=f,s.evalInnerHTMLScriptTags(t),t.firstChild&&t.removeChild(t.firstChild)},e.appendChild(n),e.appendChild(r),t.insertBefore(e,t.firstChild)}return s.handleDefaults(t,o),p=o?s.handleUrls(t,o,l):Promise.resolve(void 0),p.then((()=>{c&&d&&d.typeset(t)}))}function h(e){const{host:t,mimeType:n,source:r,width:s,height:a,needsBackground:i,unconfined:o}=e;t.textContent="";const l=document.createElement("img");return l.src=`data:${n};base64,${r}`,"number"==typeof a&&(l.height=a),"number"==typeof s&&(l.width=s),"light"===i?l.classList.add("jp-needs-light-background"):"dark"===i&&l.classList.add("jp-needs-dark-background"),!0===o&&l.classList.add("jp-mod-unconfined"),t.appendChild(l),Promise.resolve(void 0)}function u(e){const{host:t,source:n,shouldTypeset:r,latexTypesetter:s}=e;return t.textContent=n,r&&s&&s.typeset(t),Promise.resolve(void 0)}async function p(e){const{host:t,source:n,markdownParser:r,...a}=e;if(!n)return void(t.textContent="");let i="";if(r){const e=(0,c.D)(n);i=await r.render(e.text),i=(0,c.b)(i,e.math)}else i=`<pre>${n}</pre>`;await d({host:t,source:i,...a}),s.headerAnchors(t)}function f(e){let{host:t,source:n,trusted:r,unconfined:s}=e;if(!n)return t.textContent="",Promise.resolve(void 0);if(!r)return t.textContent="Cannot display an untrusted SVG. Maybe you need to run the cell?",Promise.resolve(void 0);n.search("<svg[^>]+xmlns=[^>]+svg")<0&&(n=n.replace("<svg",'<svg xmlns="http://www.w3.org/2000/svg"'));const a=new Image;return a.src=`data:image/svg+xml,${encodeURIComponent(n)}`,t.appendChild(a),!0===s&&t.classList.add("jp-mod-unconfined"),Promise.resolve()}!function(e){e.createHeaderId=function(e){var t;return(null!==(t=e.textContent)&&void 0!==t?t:"").replace(/ /g,"-")}}(p||(p={})),function(e){const t="\\u0000-\\u0020\\u007f-\\u009f";e.webLinkRegex=new RegExp("(?<path>(?:[a-zA-Z][a-zA-Z0-9+.-]{2,}:\\/\\/|data:|www\\.)[^\\s"+t+'"]{2,}[^\\s'+t+"\"'(){}\\[\\],:;.!?])","ug");const n=new RegExp(`(${/(?:[a-zA-Z]:(?:(?:\\|\/)[\w\.-]*)+)/.source}|${/(?:(?:\~|\.)(?:(?:\\|\/)[\w\.-]*)+)/.source})`),r=navigator.userAgent.indexOf("Windows")>=0;e.pathLinkRegex=new RegExp(`(?<path>${r?n.source:/((?:\~|\.)?(?:\/[\w\.-]*)+)/.source})${/(?:(?:\:|", line )(?<line>[\d]+))?(?:\:(?<column>[\d]+))?/.source}`,"g")}(r||(r={}));class m{constructor(){this.regex=r.webLinkRegex}createAnchor(e,t){const n=document.createElement("a");return n.href=e.startsWith("www.")?"https://"+e:e,n.rel="noopener",n.target="_blank",n.appendChild(document.createTextNode(t)),n}processPath(e){const t=e.slice(-1),n=-1!==[">","<"].indexOf(t)?e.length-1:e.length;return e.slice(0,n)}processLabel(e){return this.processPath(e)}}class g{constructor(){this.regex=r.pathLinkRegex}createAnchor(e,t,n){const r=document.createElement("a");r.dataset.path=e;const s=parseInt(n.line,10);let a=isNaN(s)?"":"line="+(s-1);return r.dataset.locator=a,r.appendChild(document.createTextNode(t)),r}}function y(e,t){const n=[];t.checkWeb&&n.push(new m),t.checkPaths&&n.push(new g);const r=[],s=(e,t)=>{if(t>=n.length)return void r.push(document.createTextNode(e));const a=n[t];let i,o=0;const l=a.regex;for(l.lastIndex=0;null!=(i=l.exec(e));){const n=e.substring(o,i.index);n&&s(n,t+1);const{path:l,...c}=i.groups,d=a.processPath?a.processPath(l):l,h=a.processLabel?a.processLabel(i[0]):i[0];r.push(a.createAnchor(d,h,c)),o=i.index+h.length}const c=e.substring(o);c&&s(c,t+1)};return s(e,0),r}function v(e,t){var n,r;const s=e.cloneNode();s.textContent=null===(n=e.textContent)||void 0===n?void 0:n.slice(0,t);const a=e.cloneNode();return a.textContent=null===(r=e.textContent)||void 0===r?void 0:r.slice(t),{pre:s,post:a}}function*x(e){var t;let n,r=0;for(let s of e)n=r+((null===(t=s.textContent)||void 0===t?void 0:t.length)||0),yield{node:s,start:r,end:n,isText:s.nodeType===Node.TEXT_NODE},r=n}function w(e){var t,n;const{host:r,sanitizer:a,source:i}=e,o=a.sanitize(s.ansiSpan(i),{allowedTags:["span"]}),l=document.createElement("pre");l.innerHTML=o;const c=l.textContent;let d;if(c){const e=null===(n=null===(t=a.getAutolink)||void 0===t?void 0:t.call(a))||void 0===n||n?y(c,{checkWeb:!0,checkPaths:!1}):[document.createTextNode(o)];d=b(Array.from(l.childNodes),e)}else d=document.createElement("pre");return r.appendChild(d),Promise.resolve(void 0)}function T(e){var t,n;const{host:r,linkHandler:a,sanitizer:i,resolver:o,source:l}=e,c=i.sanitize(s.ansiSpan(l),{allowedTags:["span"]}),d=document.createElement("pre");d.innerHTML=c;const h=d.textContent;let u,p;if(h){const e=null===(n=null===(t=i.getAutolink)||void 0===t?void 0:t.call(i))||void 0===n||n?y(h,{checkWeb:!0,checkPaths:!0}):[document.createTextNode(c)];u=b(Array.from(d.childNodes),e)}else u=document.createElement("pre");return r.appendChild(u),p=o?s.handlePaths(r,o,a):Promise.resolve(void 0),p}function b(e,t){const n=document.createElement("pre");let r=!1;const s=[];for(let n of function*(e,t){var n,r;let s=x(e),a=x(t),i=s.next(),o=a.next();for(;!i.done&&!o.done;){let e=i.value,t=o.value;if(e.isText&&e.start<=t.start&&e.end>=t.end)yield[null,t.node],o=a.next();else if(t.isText&&t.start<=e.start&&t.end>=e.end)yield[e.node,null],i=s.next();else if(e.end===t.end&&e.start===t.start)yield[e.node,t.node],i=s.next(),o=a.next();else if(e.end>t.end){let{pre:r,post:s}=v(e.node,t.end-e.start);t.start<e.start&&(t.node.textContent=null===(n=t.node.textContent)||void 0===n?void 0:n.slice(e.start-t.start)),yield[r,t.node],e.node=s,e.start=t.end,o=a.next()}else{if(!(t.end>e.end))throw new Error(`Unexpected intersection: ${JSON.stringify(e)} ${JSON.stringify(t)}`);{let{pre:n,post:a}=v(t.node,e.end-t.start);e.start<t.start&&(e.node.textContent=null===(r=e.node.textContent)||void 0===r?void 0:r.slice(t.start-e.start)),yield[e.node,n],t.node=a,t.start=e.end,i=s.next()}}}}(e,t)){if(!n[0]){s.push(n[1]),r=n[1].nodeType!==Node.TEXT_NODE;continue}if(!n[1]){s.push(n[0]),r=!1;continue}let[e,t]=n;const a=s[s.length-1];r&&t.href===a.href?a.appendChild(e):t.nodeType!==Node.TEXT_NODE?(t.textContent="",t.appendChild(e),s.push(t),r=!0):(s.push(e),r=!1)}for(const e of s)n.appendChild(e);return n}!function(e){async function t(e,t,n){const r=e.getAttribute(t)||"",s=n.isLocal?n.isLocal(r):a.URLExt.isLocal(r);if(r&&s)try{const s=await n.resolveUrl(r);let i=await n.getDownloadUrl(s);"data:"!==a.URLExt.parse(i).protocol&&(i+=(/\?/.test(i)?"&":"?")+(new Date).getTime()),e.setAttribute(t,i)}catch(n){throw e.setAttribute(t,""),n}}function n(e,t,n){let r=e.getAttribute("href")||"";const s=t.isLocal?t.isLocal(r):a.URLExt.isLocal(r);if(!r||!s)return Promise.resolve(void 0);const i=e.hash;if(i){if(i===r)return e.target="_self",Promise.resolve(void 0);r=r.replace(i,"")}return t.resolveUrl(r).then((r=>{const s=decodeURIComponent(r);return n&&n.handleLink(e,s,i),t.getDownloadUrl(r)})).then((t=>{e.href=t+i})).catch((t=>{e.href=""}))}async function r(e,t,n){let r=e.dataset.path||"",s=e.dataset.locator?"#"+e.dataset.locator:"";delete e.dataset.path,delete e.dataset.locator;const i=t.isLocal?t.isLocal(r,!0):a.URLExt.isLocal(r,!0);if(!(r&&i&&t.resolvePath&&n&&n.handlePath))return e.replaceWith(...e.childNodes),Promise.resolve(void 0);try{const a=await t.resolvePath(r);if(!a)return console.log("Path resolution bailing: does not exist"),Promise.resolve(void 0);n.handlePath(e,a.path,a.scope,s),e.href=a.path+s}catch(t){console.warn("Path anchor error:",t),e.href="#linking-failed-see-console"}}e.evalInnerHTMLScriptTags=function(e){const t=Array.from(e.getElementsByTagName("script"));for(const e of t){if(!e.parentNode)continue;const t=document.createElement("script"),n=e.attributes;for(let e=0,r=n.length;e<r;++e){const{name:r,value:s}=n[e];t.setAttribute(r,s)}t.textContent=e.textContent,e.parentNode.replaceChild(t,e)}},e.handleDefaults=function(e,t){const n=e.getElementsByTagName("a");for(let e=0;e<n.length;e++){const r=n[e];if(!(r instanceof HTMLAnchorElement))continue;const s=r.href,i=t&&t.isLocal?t.isLocal(s):a.URLExt.isLocal(s);r.target||(r.target=i?"_self":"_blank"),i||(r.rel="noopener")}const r=e.getElementsByTagName("img");for(let e=0;e<r.length;e++)r[e].alt||(r[e].alt="Image")},e.handleUrls=function(e,r,s){const a=[],i=e.querySelectorAll("*[src]");for(let e=0;e<i.length;e++)a.push(t(i[e],"src",r));const o=e.getElementsByTagName("a");for(let e=0;e<o.length;e++)a.push(n(o[e],r,s));const l=e.getElementsByTagName("link");for(let e=0;e<l.length;e++)a.push(t(l[e],"href",r));return Promise.all(a).then((()=>{}))},e.handlePaths=async function(e,t,n){const s=e.getElementsByTagName("a");for(let e=0;e<s.length;e++)await r(s[e],t,n)},e.headerAnchors=function(e){const t=["h1","h2","h3","h4","h5","h6"];for(const n of t){const t=e.getElementsByTagName(n);for(let e=0;e<t.length;e++){const n=t[e];n.id=p.createHeaderId(n);const r=document.createElement("a");r.target="_self",r.textContent="¶",r.href="#"+n.id,r.classList.add("jp-InternalAnchorLink"),n.appendChild(r)}}};const s=["ansi-black","ansi-red","ansi-green","ansi-yellow","ansi-blue","ansi-magenta","ansi-cyan","ansi-white","ansi-black-intense","ansi-red-intense","ansi-green-intense","ansi-yellow-intense","ansi-blue-intense","ansi-magenta-intense","ansi-cyan-intense","ansi-white-intense"];function i(e,t,n,r,a,i,o){if(e){const l=[],c=[];r&&"number"==typeof t&&0<=t&&t<8&&(t+=8),i&&([t,n]=[n,t]),"number"==typeof t?l.push(s[t]+"-fg"):t.length?c.push(`color: rgb(${t})`):i&&l.push("ansi-default-inverse-fg"),"number"==typeof n?l.push(s[n]+"-bg"):n.length?c.push(`background-color: rgb(${n})`):i&&l.push("ansi-default-inverse-bg"),r&&l.push("ansi-bold"),a&&l.push("ansi-underline"),l.length||c.length?(o.push("<span"),l.length&&o.push(` class="${l.join(" ")}"`),c.length&&o.push(` style="${c.join("; ")}"`),o.push(">"),o.push(e),o.push("</span>")):o.push(e)}}function o(e){let t,n,r;const s=e.shift();if(2===s&&e.length>=3){if(t=e.shift(),n=e.shift(),r=e.shift(),[t,n,r].some((e=>e<0||255<e)))throw new RangeError("Invalid range for RGB colors")}else{if(!(5===s&&e.length>=1))throw new RangeError("Invalid extended color specification");{const s=e.shift();if(s<0)throw new RangeError("Color index must be >= 0");if(s<16)return s;if(s<232)t=Math.floor((s-16)/36),t=t>0?55+40*t:0,n=Math.floor((s-16)%36/6),n=n>0?55+40*n:0,r=(s-16)%6,r=r>0?55+40*r:0;else{if(!(s<256))throw new RangeError("Color index must be < 256");t=n=r=10*(s-232)+8}}}return[t,n,r]}e.ansiSpan=function(e){const t=/\x1b\[(.*?)([@-~])/g;let n,r=[],s=[],a=!1,c=!1,d=!1;const h=[],u=[];let p=0;for(e=l()(e),e+="[m";n=t.exec(e);){if("m"===n[2]){const e=n[1].split(";");for(let t=0;t<e.length;t++){const n=e[t];if(""===n)u.push(0);else{if(-1===n.search(/^\d+$/)){u.length=0;break}u.push(parseInt(n,10))}}}for(i(e.substring(p,n.index),r,s,a,c,d,h),p=t.lastIndex;u.length;){const e=u.shift();switch(e){case 0:r=s=[],a=!1,c=!1,d=!1;break;case 1:case 5:a=!0;break;case 4:c=!0;break;case 7:d=!0;break;case 21:case 22:a=!1;break;case 24:c=!1;break;case 27:d=!1;break;case 30:case 31:case 32:case 33:case 34:case 35:case 36:case 37:r=e-30;break;case 38:try{r=o(u)}catch(e){u.length=0}break;case 39:r=[];break;case 40:case 41:case 42:case 43:case 44:case 45:case 46:case 47:s=e-40;break;case 48:try{s=o(u)}catch(e){u.length=0}break;case 49:s=[];break;case 90:case 91:case 92:case 93:case 94:case 95:case 96:case 97:r=e-90+8;break;case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:s=e-100+8}}}return h.join("")}}(s||(s={}))},2186:(e,t,n)=>{"use strict";n.d(t,{ZD:()=>s,_y:()=>a,sc:()=>i});var r=n(20998);const s=new r.Token("@jupyterlab/rendermime:IRenderMimeRegistry",'A service for the rendermime registry for the application. Use this to create renderers for various mime-types in your extension. Many times it will be easier to create a "mime renderer extension" rather than using this service directly.'),a=new r.Token("@jupyterlab/rendermime:ILatexTypesetter","A service for the LaTeX typesetter for the application. Use this if you want to typeset math in your extension."),i=new r.Token("@jupyterlab/rendermime:IMarkdownParser","A service for rendering markdown syntax as HTML content.")},28182:(e,t,n)=>{"use strict";n.d(t,{BP:()=>o,FK:()=>c,Hw:()=>f,ND:()=>m,UH:()=>d,cw:()=>h,lH:()=>p,oI:()=>l,pY:()=>i,zt:()=>u});var r=n(71677),s=n(31516),a=n(3328);class i extends s.Widget{constructor(e){var t,n;super(),this.mimeType=e.mimeType,this.sanitizer=e.sanitizer,this.resolver=e.resolver,this.linkHandler=e.linkHandler,this.translator=null!==(t=e.translator)&&void 0!==t?t:r.nullTranslator,this.latexTypesetter=e.latexTypesetter,this.markdownParser=null!==(n=e.markdownParser)&&void 0!==n?n:null,this.node.dataset.mimeType=this.mimeType}async renderModel(e,t){if(!t)for(;this.node.firstChild;)this.node.removeChild(this.node.firstChild);this.toggleClass("jp-mod-trusted",e.trusted),await this.render(e);const{fragment:n}=e.metadata;n&&this.setFragment(n)}setFragment(e){}}class o extends i{constructor(e){super(e),this.addClass("jp-RenderedHTMLCommon")}setFragment(e){let t;try{t=this.node.querySelector(e.startsWith("#")?`#${CSS.escape(e.slice(1))}`:e)}catch(e){console.warn("Unable to set URI fragment identifier.",e)}t&&t.scrollIntoView()}}class l extends o{constructor(e){super(e),this._rendered=Promise.resolve(),this.addClass("jp-RenderedHTML")}render(e){return this._rendered=a.NN({host:this.node,source:String(e.data[this.mimeType]),trusted:e.trusted,resolver:this.resolver,sanitizer:this.sanitizer,linkHandler:this.linkHandler,shouldTypeset:this.isAttached,latexTypesetter:this.latexTypesetter,translator:this.translator})}onAfterAttach(e){this._rendered.then((()=>{this.latexTypesetter&&this.latexTypesetter.typeset(this.node)})).catch(console.warn)}}class c extends i{constructor(e){super(e),this._rendered=Promise.resolve(),this.addClass("jp-RenderedLatex")}render(e){return this._rendered=a.K3({host:this.node,source:String(e.data[this.mimeType]),shouldTypeset:this.isAttached,latexTypesetter:this.latexTypesetter})}onAfterAttach(e){this._rendered.then((()=>{this.latexTypesetter&&this.latexTypesetter.typeset(this.node)})).catch(console.warn)}}class d extends i{constructor(e){super(e),this.addClass("jp-RenderedImage")}render(e){const t=e.metadata[this.mimeType];return a.co({host:this.node,mimeType:this.mimeType,source:String(e.data[this.mimeType]),width:t&&t.width,height:t&&t.height,needsBackground:e.metadata.needs_background,unconfined:t&&t.unconfined})}}class h extends o{constructor(e){super(e),this._rendered=Promise.resolve(),this.addClass("jp-RenderedMarkdown")}render(e){return this._rendered=a.ap({host:this.node,source:String(e.data[this.mimeType]),trusted:e.trusted,resolver:this.resolver,sanitizer:this.sanitizer,linkHandler:this.linkHandler,shouldTypeset:this.isAttached,latexTypesetter:this.latexTypesetter,markdownParser:this.markdownParser,translator:this.translator})}async renderModel(e){await super.renderModel(e,!0)}onAfterAttach(e){this._rendered.then((()=>{this.latexTypesetter&&this.latexTypesetter.typeset(this.node)})).catch(console.warn)}}class u extends i{constructor(e){super(e),this._rendered=Promise.resolve(),this.addClass("jp-RenderedSVG")}render(e){const t=e.metadata[this.mimeType];return this._rendered=a.KB({host:this.node,source:String(e.data[this.mimeType]),trusted:e.trusted,unconfined:t&&t.unconfined,translator:this.translator})}onAfterAttach(e){this._rendered.then((()=>{this.latexTypesetter&&this.latexTypesetter.typeset(this.node)})).catch(console.warn)}}class p extends i{constructor(e){super(e),this.addClass("jp-RenderedText")}render(e){return a.IY({host:this.node,sanitizer:this.sanitizer,source:String(e.data[this.mimeType]),translator:this.translator})}}class f extends i{constructor(e){super(e),this.addClass("jp-RenderedText")}render(e){return a.Op({host:this.node,sanitizer:this.sanitizer,source:String(e.data[this.mimeType]),linkHandler:this.linkHandler,resolver:this.resolver,translator:this.translator})}}class m extends i{constructor(e){super(e),this.addClass("jp-RenderedJavaScript")}render(e){const t=this.translator.load("jupyterlab");return a.IY({host:this.node,sanitizer:this.sanitizer,source:t.__("JavaScript output is disabled in JupyterLab"),translator:this.translator})}}},8872:(e,t,n)=>{var r,s=/[&<>"'`]/g,a=RegExp(s.source),i="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g,o="object"==typeof self&&self&&self.Object===Object&&self,l=i||o||Function("return this")(),c=(r={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","`":"&#96;"},function(e){return null==r?void 0:r[e]}),d=Object.prototype.toString,h=l.Symbol,u=h?h.prototype:void 0,p=u?u.toString:void 0;e.exports=function(e){var t;return(e=null==(t=e)?"":function(e){if("string"==typeof e)return e;if(function(e){return"symbol"==typeof e||function(e){return!!e&&"object"==typeof e}(e)&&"[object Symbol]"==d.call(e)}(e))return p?p.call(e):"";var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}(t))&&a.test(e)?e.replace(s,c):e}}}]);
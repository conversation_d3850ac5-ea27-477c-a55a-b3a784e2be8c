"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[9161],{79161:(e,a,r)=>{r.r(a),r.d(a,{CommandIDs:()=>t,default:()=>d});var t,n=r(12982),o=r(20652),i=r(71677);!function(e){e.copySource="mermaid:copy-source"}(t||(t={}));const d=[{id:"@jupyterlab/mermaid-extension:core",description:"Provides the Mermaid manager.",autoStart:!0,optional:[n.IThemeManager],provides:o.IMermaidManager,activate:(e,a)=>{const r=new o.MermaidManager({themes:a});return o.RenderedMermaid.manager=r,r}},{id:"@jupyterlab/mermaid-extension:markdown",description:"Provides the Mermaid markdown renderer.",autoStart:!0,requires:[o.IMermaidManager],provides:o.IMermaidMarkdown,activate:(e,a)=>new o.MermaidMarkdown({mermaid:a})},{id:"@jupyterlab/mermaid-extension:context-commands",description:"Provides context menu commands for mermaid diagrams.",autoStart:!0,requires:[o.IMermaidManager],optional:[i.ITranslator],activate:(e,a,r)=>{const n=e=>e.classList.contains(o.MERMAID_CLASS),d=(null!=r?r:i.nullTranslator).load("jupyterlab");e.commands.addCommand(t.copySource,{label:d.__("Mermaid Copy Diagram Source"),execute:async a=>{const r=e.contextMenuHitTest(n);if(!r)return;const t=r.querySelector(`.${o.MERMAID_CODE_CLASS}`);t&&t.textContent&&await navigator.clipboard.writeText(t.textContent)}});const m={selector:`.${o.MERMAID_CLASS}`,rank:13};e.contextMenu.addItem({command:t.copySource,...m}),e.contextMenu.addItem({type:"separator",...m})}}]}}]);
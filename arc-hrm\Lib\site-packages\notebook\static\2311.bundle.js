"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[2311],{22311:(e,n,t)=>{t.r(n),t.d(n,{default:()=>m});var a=t(3053),i=t(12982),o=t(79031),s=t(36768),l=t(71677),d=t(68239);const r="@jupyterlab/extensionmanager-extension:plugin";var c;!function(e){e.showPanel="extensionmanager:show-panel",e.toggle="extensionmanager:toggle"}(c||(c={}));const g={id:r,description:"Adds the extension manager plugin.",autoStart:!0,requires:[s.ISettingRegistry],optional:[l.ITranslator,a.ILayoutRestorer,i.ICommandPalette],activate:async(e,n,t,a,i)=>{const{commands:s,shell:m,serviceManager:b}=e,h=(t=null!=t?t:l.nullTranslator).load("jupyterlab"),p=new o.ListModel(b,t),_=()=>{const e=new o.ExtensionsPanel({model:p,translator:t});return e.id="extensionmanager.main-view",e.title.icon=d.extensionIcon,e.title.caption=h.__("Extension Manager"),e.node.setAttribute("role","region"),e.node.setAttribute("aria-label",h.__("Extension Manager section")),a&&a.add(e,e.id),m.add(e,"left",{rank:1e3}),e};let E=_();Promise.all([e.restored,n.load(r)]).then((([,n])=>{p.isDisclaimed=n.get("disclaimed").composite,p.isEnabled=n.get("enabled").composite,p.stateChanged.connect((()=>{p.isDisclaimed!==n.get("disclaimed").composite&&n.set("disclaimed",p.isDisclaimed).catch((e=>{console.error(`Failed to set setting 'disclaimed'.\n${e}`)})),p.isEnabled!==n.get("enabled").composite&&n.set("enabled",p.isEnabled).catch((e=>{console.error(`Failed to set setting 'enabled'.\n${e}`)}))})),p.isEnabled?E=null!=E?E:_():(null==E||E.dispose(),E=null),n.changed.connect((async()=>{if(p.isDisclaimed=n.get("disclaimed").composite,p.isEnabled=n.get("enabled").composite,e.commands.notifyCommandChanged(c.toggle),p.isEnabled){if(!(null!==E&&E.isAttached||await u.showWarning(h)))return void n.set("enabled",!1);E=null!=E?E:_()}else null==E||E.dispose(),E=null}))})).catch((e=>{console.error(`Something went wrong when reading the settings.\n${e}`)})),s.addCommand(c.showPanel,{label:h.__("Extension Manager"),execute:()=>{E&&m.activateById(E.id)},isVisible:()=>p.isEnabled}),s.addCommand(c.toggle,{label:h.__("Enable Extension Manager"),execute:()=>{n&&n.set(g.id,"enabled",!p.isEnabled)},isToggled:()=>p.isEnabled}),i&&i.addItem({command:c.toggle,category:h.__("Extension Manager")})}},m=g;var u;!function(e){e.showWarning=async function(e){return(await(0,i.showDialog)({title:e.__("Enable Extension Manager?"),body:e.__("Thanks for trying out JupyterLab's extension manager.\nThe JupyterLab development team is excited to have a robust\nthird-party extension community.\nHowever, we cannot vouch for every extension,\nand some may introduce security risks.\nDo you want to continue?"),buttons:[i.Dialog.cancelButton({label:e.__("Disable")}),i.Dialog.warnButton({label:e.__("Enable")})]})).button.accept}}(u||(u={}))}}]);
"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[6770],{66770:(t,e,n)=>{n.r(e);n.d(e,{Bounds:()=>ad,CanvasHandler:()=>Sm,CanvasRenderer:()=>Nm,DATE:()=>it,DAY:()=>rt,DAYOFYEAR:()=>ot,Dataflow:()=>Ai,Debug:()=>p.cG,Error:()=>p.jj,EventStream:()=>Pn,Gradient:()=>If,GroupItem:()=>ld,HOURS:()=>at,Handler:()=>em,Info:()=>p.kI,Item:()=>sd,MILLISECONDS:()=>ut,MINUTES:()=>st,MONTH:()=>et,Marks:()=>Pp,MultiPulse:()=>pi,None:()=>p.Hq,Operator:()=>Tn,Parameters:()=>zn,Pulse:()=>fi,QUARTER:()=>tt,RenderType:()=>jg,Renderer:()=>im,ResourceLoader:()=>ud,SECONDS:()=>lt,SVGHandler:()=>qm,SVGRenderer:()=>vg,SVGStringRenderer:()=>Ng,Scenegraph:()=>Zp,TIME_UNITS:()=>ft,Transform:()=>Oi,View:()=>aP,WEEK:()=>nt,Warn:()=>p.uU,YEAR:()=>Q,accessor:()=>p.ZE,accessorFields:()=>p.Oj,accessorName:()=>p.el,array:()=>p.IX,ascending:()=>p.j2,bandwidthNRD:()=>er,bin:()=>nr,bootstrapCI:()=>or,boundClip:()=>Kg,boundContext:()=>$d,boundItem:()=>qp,boundMark:()=>jp,boundStroke:()=>dd,changeset:()=>En,clampRange:()=>p.l$,codegenExpression:()=>TO.YP,compare:()=>p.qu,constant:()=>p.a9,cumulativeLogNormal:()=>wr,cumulativeNormal:()=>mr,cumulativeUniform:()=>Dr,dayofyear:()=>yt,debounce:()=>p.Ds,defaultLocale:()=>Re,definition:()=>Ci,densityLogNormal:()=>_r,densityNormal:()=>pr,densityUniform:()=>zr,domChild:()=>Kp,domClear:()=>Vp,domCreate:()=>Jp,domFind:()=>Gp,dotbin:()=>ar,error:()=>p.vU,expressionFunction:()=>eU,extend:()=>p.l7,extent:()=>p.We,extentIndex:()=>p.dI,falsy:()=>p.k,fastmap:()=>p.Xr,field:()=>p.EP,flush:()=>p.yl,font:()=>Sp,fontFamily:()=>Ep,fontSize:()=>yp,format:()=>an,formatLocale:()=>xe,formats:()=>sn,hasOwnProperty:()=>p.nr,id:()=>p.id,identity:()=>p.yR,inferType:()=>Ke,inferTypes:()=>Ve,ingest:()=>bn,inherits:()=>p.XW,inrange:()=>p.u5,interpolate:()=>Gu,interpolateColors:()=>Bu,interpolateRange:()=>Xu,intersect:()=>Xg,intersectBoxLine:()=>Fd,intersectPath:()=>Pd,intersectPoint:()=>qd,intersectRule:()=>jd,isArray:()=>p.kJ,isBoolean:()=>p.jn,isDate:()=>p.J_,isFunction:()=>p.mf,isIterable:()=>p.TW,isNumber:()=>p.hj,isObject:()=>p.Kn,isRegExp:()=>p.Kj,isString:()=>p.HD,isTuple:()=>gn,key:()=>p.Jy,lerp:()=>p.t7,lineHeight:()=>vp,loader:()=>cn,locale:()=>De,logger:()=>p.kg,lruCache:()=>p.$m,markup:()=>ug,merge:()=>p.TS,mergeConfig:()=>p.fE,multiLineOffset:()=>_p,one:()=>p.kX,pad:()=>p.vk,panLinear:()=>p.Dw,panLog:()=>p.mJ,panPow:()=>p.QA,panSymlog:()=>p.Zw,parse:()=>zW,parseExpression:()=>TO.BJ,parseSelector:()=>cP.r,path:()=>Gs.ET,pathCurves:()=>qf,pathEqual:()=>Qg,pathParse:()=>Bf,pathRectangle:()=>bc,pathRender:()=>oc,pathSymbols:()=>uc,pathTrail:()=>xc,peek:()=>p.fj,point:()=>Qp,projection:()=>nM,quantileLogNormal:()=>kr,quantileNormal:()=>gr,quantileUniform:()=>Rr,quantiles:()=>Qi,quantizeInterpolator:()=>Zu,quarter:()=>p.mS,quartiles:()=>tr,random:()=>ir,randomInteger:()=>ur,randomKDE:()=>br,randomLCG:()=>lr,randomLogNormal:()=>Mr,randomMixture:()=>Er,randomNormal:()=>vr,randomUniform:()=>Ar,read:()=>un,regressionExp:()=>Ir,regressionLinear:()=>Nr,regressionLoess:()=>Br,regressionLog:()=>Ur,regressionPoly:()=>Lr,regressionPow:()=>Pr,regressionQuad:()=>qr,renderModule:()=>Wg,repeat:()=>p.rx,resetDefaultLocale:()=>Ae,resetSVGClipId:()=>rd,resetSVGDefIds:()=>ey,responseType:()=>ln,runtimeContext:()=>OU,sampleCurve:()=>Kr,sampleLogNormal:()=>xr,sampleNormal:()=>hr,sampleUniform:()=>Sr,scale:()=>Tu,sceneEqual:()=>Yg,sceneFromJSON:()=>Xp,scenePickVisit:()=>th,sceneToJSON:()=>Wp,sceneVisit:()=>Qd,sceneZOrder:()=>Yd,scheme:()=>nf,serializeXML:()=>fg,setRandom:()=>rr,span:()=>p.yP,splitAccessPath:()=>p._k,stringValue:()=>p.m8,textMetrics:()=>cp,timeBin:()=>fe,timeFloor:()=>Ct,timeFormatLocale:()=>Se,timeInterval:()=>Lt,timeOffset:()=>Wt,timeSequence:()=>Zt,timeUnitSpecifier:()=>pt,timeUnits:()=>dt,toBoolean:()=>p.sw,toDate:()=>p.ZU,toNumber:()=>p.He,toSet:()=>p.Rg,toString:()=>p.BB,transform:()=>Ni,transforms:()=>Ti,truncate:()=>p.$G,truthy:()=>p.yb,tupleid:()=>yn,typeParsers:()=>He,utcFloor:()=>It,utcInterval:()=>jt,utcOffset:()=>Xt,utcSequence:()=>Ht,utcdayofyear:()=>kt,utcquarter:()=>p.N3,utcweek:()=>Mt,version:()=>DW,visitArray:()=>p.FP,week:()=>vt,writeConfig:()=>p.iL,zero:()=>p.bM,zoomLinear:()=>p.ay,zoomLog:()=>p.dH,zoomPow:()=>p.mK,zoomSymlog:()=>p.bV});var i={};n.r(i);n.d(i,{aggregate:()=>xo,bin:()=>wo,collect:()=>Mo,compare:()=>Eo,countpattern:()=>zo,cross:()=>Ro,density:()=>Uo,dotbin:()=>Wo,expression:()=>Bo,extent:()=>Ho,facet:()=>Go,field:()=>Ko,filter:()=>Yo,flatten:()=>Qo,fold:()=>ta,formula:()=>ea,generate:()=>na,impute:()=>oa,joinaggregate:()=>ua,kde:()=>fa,key:()=>ca,load:()=>ha,lookup:()=>ga,multiextent:()=>ya,multivalues:()=>ba,params:()=>_a,pivot:()=>wa,prefacet:()=>Sa,project:()=>za,proxy:()=>Ra,quantile:()=>Aa,relay:()=>Oa,sample:()=>Ta,sequence:()=>Ca,sieve:()=>Na,subflow:()=>Jo,timeunit:()=>Ua,tupleindex:()=>Pa,values:()=>qa,window:()=>Ha});var r={};n.r(r);n.d(r,{bound:()=>qy,identifier:()=>Fy,mark:()=>Xy,overlap:()=>Zy,render:()=>tv,viewlayout:()=>Av});var o={};n.r(o);n.d(o,{axisticks:()=>Nv,datajoin:()=>Uv,encode:()=>qv,legendentries:()=>Lv,linkpath:()=>Bv,pie:()=>ob,scale:()=>fb,sortitems:()=>kb,stack:()=>Db});var a={};n.r(a);n.d(a,{contour:()=>TM,geojson:()=>IM,geopath:()=>PM,geopoint:()=>LM,geoshape:()=>jM,graticule:()=>WM,heatmap:()=>XM,isocontour:()=>xM,kde2d:()=>RM,projection:()=>GM});var s={};n.r(s);n.d(s,{force:()=>VE});var l={};n.r(l);n.d(l,{nest:()=>jz,pack:()=>Gz,partition:()=>Vz,stratify:()=>Yz,tree:()=>eD,treelinks:()=>nD,treemap:()=>oD});var u={};n.r(u);n.d(u,{label:()=>BD});var f={};n.r(f);n.d(f,{loess:()=>HD,regression:()=>KD});var c={};n.r(c);n.d(c,{voronoi:()=>eO});var d={};n.r(d);n.d(d,{wordcloud:()=>bO});var h={};n.r(h);n.d(h,{crossfilter:()=>$O,resolvefilter:()=>OO});var p=n(48823);var m={},g={},y=34,v=10,b=13;function x(t){return new Function("d","return {"+t.map((function(t,e){return JSON.stringify(t)+": d["+e+'] || ""'})).join(",")+"}")}function _(t,e){var n=x(t);return function(i,r){return e(n(i),r,t)}}function w(t){var e=Object.create(null),n=[];t.forEach((function(t){for(var i in t){if(!(i in e)){n.push(e[i]=i)}}}));return n}function k(t,e){var n=t+"",i=n.length;return i<e?new Array(e-i+1).join(0)+n:n}function M(t){return t<0?"-"+k(-t,6):t>9999?"+"+k(t,6):k(t,4)}function E(t){var e=t.getUTCHours(),n=t.getUTCMinutes(),i=t.getUTCSeconds(),r=t.getUTCMilliseconds();return isNaN(t)?"Invalid Date":M(t.getUTCFullYear(),4)+"-"+k(t.getUTCMonth()+1,2)+"-"+k(t.getUTCDate(),2)+(r?"T"+k(e,2)+":"+k(n,2)+":"+k(i,2)+"."+k(r,3)+"Z":i?"T"+k(e,2)+":"+k(n,2)+":"+k(i,2)+"Z":n||e?"T"+k(e,2)+":"+k(n,2)+"Z":"")}function S(t){var e=new RegExp('["'+t+"\n\r]"),n=t.charCodeAt(0);function i(t,e){var n,i,o=r(t,(function(t,r){if(n)return n(t,r-1);i=t,n=e?_(t,e):x(t)}));o.columns=i||[];return o}function r(t,e){var i=[],r=t.length,o=0,a=0,s,l=r<=0,u=false;if(t.charCodeAt(r-1)===v)--r;if(t.charCodeAt(r-1)===b)--r;function f(){if(l)return g;if(u)return u=false,m;var e,i=o,a;if(t.charCodeAt(i)===y){while(o++<r&&t.charCodeAt(o)!==y||t.charCodeAt(++o)===y);if((e=o)>=r)l=true;else if((a=t.charCodeAt(o++))===v)u=true;else if(a===b){u=true;if(t.charCodeAt(o)===v)++o}return t.slice(i+1,e-1).replace(/""/g,'"')}while(o<r){if((a=t.charCodeAt(e=o++))===v)u=true;else if(a===b){u=true;if(t.charCodeAt(o)===v)++o}else if(a!==n)continue;return t.slice(i,e)}return l=true,t.slice(i,r)}while((s=f())!==g){var c=[];while(s!==m&&s!==g)c.push(s),s=f();if(e&&(c=e(c,a++))==null)continue;i.push(c)}return i}function o(e,n){return e.map((function(e){return n.map((function(t){return f(e[t])})).join(t)}))}function a(e,n){if(n==null)n=w(e);return[n.map(f).join(t)].concat(o(e,n)).join("\n")}function s(t,e){if(e==null)e=w(t);return o(t,e).join("\n")}function l(t){return t.map(u).join("\n")}function u(e){return e.map(f).join(t)}function f(t){return t==null?"":t instanceof Date?E(t):e.test(t+="")?'"'+t.replace(/"/g,'""')+'"':t}return{parse:i,parseRows:r,format:a,formatBody:s,formatRows:l,formatRow:u,formatValue:f}}function z(t,e){var n,i=t.length,r=i-e;while(r<--i)n=t[r],t[r++]=t[i],t[i]=n}function D(t){return t}function R(t){if(t==null)return D;var e,n,i=t.scale[0],r=t.scale[1],o=t.translate[0],a=t.translate[1];return function(t,s){if(!s)e=n=0;var l=2,u=t.length,f=new Array(u);f[0]=(e+=t[0])*i+o;f[1]=(n+=t[1])*r+a;while(l<u)f[l]=t[l],++l;return f}}function A(t,e){if(typeof e==="string")e=t.objects[e];return e.type==="GeometryCollection"?{type:"FeatureCollection",features:e.geometries.map((function(e){return $(t,e)}))}:$(t,e)}function $(t,e){var n=e.id,i=e.bbox,r=e.properties==null?{}:e.properties,o=O(t,e);return n==null&&i==null?{type:"Feature",properties:r,geometry:o}:i==null?{type:"Feature",id:n,properties:r,geometry:o}:{type:"Feature",id:n,bbox:i,properties:r,geometry:o}}function O(t,e){var n=R(t.transform),i=t.arcs;function r(t,e){if(e.length)e.pop();for(var r=i[t<0?~t:t],o=0,a=r.length;o<a;++o){e.push(n(r[o],o))}if(t<0)z(e,a)}function o(t){return n(t)}function a(t){var e=[];for(var n=0,i=t.length;n<i;++n)r(t[n],e);if(e.length<2)e.push(e[0]);return e}function s(t){var e=a(t);while(e.length<4)e.push(e[0]);return e}function l(t){return t.map(s)}function u(t){var e=t.type,n;switch(e){case"GeometryCollection":return{type:e,geometries:t.geometries.map(u)};case"Point":n=o(t.coordinates);break;case"MultiPoint":n=t.coordinates.map(o);break;case"LineString":n=a(t.arcs);break;case"MultiLineString":n=t.arcs.map(a);break;case"Polygon":n=l(t.arcs);break;case"MultiPolygon":n=t.arcs.map(l);break;default:return null}return{type:e,coordinates:n}}return u(e)}function T(t,e){var n={},i={},r={},o=[],a=-1;e.forEach((function(n,i){var r=t.arcs[n<0?~n:n],o;if(r.length<3&&!r[1][0]&&!r[1][1]){o=e[++a],e[a]=n,e[i]=o}}));e.forEach((function(t){var e=s(t),n=e[0],o=e[1],a,l;if(a=r[n]){delete r[a.end];a.push(t);a.end=o;if(l=i[o]){delete i[l.start];var u=l===a?a:a.concat(l);i[u.start=a.start]=r[u.end=l.end]=u}else{i[a.start]=r[a.end]=a}}else if(a=i[o]){delete i[a.start];a.unshift(t);a.start=n;if(l=r[n]){delete r[l.end];var f=l===a?a:l.concat(a);i[f.start=l.start]=r[f.end=a.end]=f}else{i[a.start]=r[a.end]=a}}else{a=[t];i[a.start=n]=r[a.end=o]=a}}));function s(e){var n=t.arcs[e<0?~e:e],i=n[0],r;if(t.transform)r=[0,0],n.forEach((function(t){r[0]+=t[0],r[1]+=t[1]}));else r=n[n.length-1];return e<0?[r,i]:[i,r]}function l(t,e){for(var i in t){var r=t[i];delete e[r.start];delete r.start;delete r.end;r.forEach((function(t){n[t<0?~t:t]=1}));o.push(r)}}l(r,i);l(i,r);e.forEach((function(t){if(!n[t<0?~t:t])o.push([t])}));return o}function C(t){return O(t,N.apply(this,arguments))}function N(t,e,n){var i,r,o;if(arguments.length>1)i=U(t,e,n);else for(r=0,i=new Array(o=t.arcs.length);r<o;++r)i[r]=r;return{type:"MultiLineString",arcs:T(t,i)}}function U(t,e,n){var i=[],r=[],o;function a(t){var e=t<0?~t:t;(r[e]||(r[e]=[])).push({i:t,g:o})}function s(t){t.forEach(a)}function l(t){t.forEach(s)}function u(t){t.forEach(l)}function f(t){switch(o=t,t.type){case"GeometryCollection":t.geometries.forEach(f);break;case"LineString":s(t.arcs);break;case"MultiLineString":case"Polygon":l(t.arcs);break;case"MultiPolygon":u(t.arcs);break}}f(e);r.forEach(n==null?function(t){i.push(t[0].i)}:function(t){if(n(t[0].g,t[t.length-1].g))i.push(t[0].i)});return i}var I=n(67614);var P=n(46196);var q=n(53726);var L=n(37655);var j=n(68195);var F=n(47539);var W=n(14261);var X=n(69478);var B=n(22776);var Z=n(82209);var H=n(4584);var J=n(42784);var G=n(7197);var K=n(15086);var V=n(98179);var Y=n(2725);const Q="year";const tt="quarter";const et="month";const nt="week";const it="date";const rt="day";const ot="dayofyear";const at="hours";const st="minutes";const lt="seconds";const ut="milliseconds";const ft=[Q,tt,et,nt,it,rt,ot,at,st,lt,ut];const ct=ft.reduce(((t,e,n)=>(t[e]=1+n,t)),{});function dt(t){const e=(0,p.IX)(t).slice(),n={};if(!e.length)(0,p.vU)("Missing time unit.");e.forEach((t=>{if((0,p.nr)(ct,t)){n[t]=1}else{(0,p.vU)(`Invalid time unit: ${t}.`)}}));const i=(n[nt]||n[rt]?1:0)+(n[tt]||n[et]||n[it]?1:0)+(n[ot]?1:0);if(i>1){(0,p.vU)(`Incompatible time units: ${t}`)}e.sort(((t,e)=>ct[t]-ct[e]));return e}const ht={[Q]:"%Y ",[tt]:"Q%q ",[et]:"%b ",[it]:"%d ",[nt]:"W%U ",[rt]:"%a ",[ot]:"%j ",[at]:"%H:00",[st]:"00:%M",[lt]:":%S",[ut]:".%L",[`${Q}-${et}`]:"%Y-%m ",[`${Q}-${et}-${it}`]:"%Y-%m-%d ",[`${at}-${st}`]:"%H:%M"};function pt(t,e){const n=(0,p.l7)({},ht,e),i=dt(t),r=i.length;let o="",a=0,s,l;for(a=0;a<r;){for(s=i.length;s>a;--s){l=i.slice(a,s).join("-");if(n[l]!=null){o+=n[l];a=s;break}}}return o.trim()}const mt=new Date;function gt(t){mt.setFullYear(t);mt.setMonth(0);mt.setDate(1);mt.setHours(0,0,0,0);return mt}function yt(t){return bt(new Date(t))}function vt(t){return xt(new Date(t))}function bt(t){return X.rr.count(gt(t.getFullYear())-1,t)}function xt(t){return B.Zy.count(gt(t.getFullYear())-1,t)}function _t(t){return gt(t).getDay()}function wt(t,e,n,i,r,o,a){if(0<=t&&t<100){const s=new Date(-1,e,n,i,r,o,a);s.setFullYear(t);return s}return new Date(t,e,n,i,r,o,a)}function kt(t){return Et(new Date(t))}function Mt(t){return St(new Date(t))}function Et(t){const e=Date.UTC(t.getUTCFullYear(),0,1);return X.AN.count(e-1,t)}function St(t){const e=Date.UTC(t.getUTCFullYear(),0,1);return B.pI.count(e-1,t)}function zt(t){mt.setTime(Date.UTC(t,0,1));return mt.getUTCDay()}function Dt(t,e,n,i,r,o,a){if(0<=t&&t<100){const t=new Date(Date.UTC(-1,e,n,i,r,o,a));t.setUTCFullYear(n.y);return t}return new Date(Date.UTC(t,e,n,i,r,o,a))}function Rt(t,e,n,i,r){const o=e||1,a=(0,p.fj)(t),s=(t,e,r)=>{r=r||t;return At(n[r],i[r],t===a&&o,e)};const l=new Date,u=(0,p.Rg)(t),f=u[Q]?s(Q):(0,p.a9)(2012),c=u[et]?s(et):u[tt]?s(tt):p.bM,d=u[nt]&&u[rt]?s(rt,1,nt+rt):u[nt]?s(nt,1):u[rt]?s(rt,1):u[it]?s(it,1):u[ot]?s(ot,1):p.kX,h=u[at]?s(at):p.bM,m=u[st]?s(st):p.bM,g=u[lt]?s(lt):p.bM,y=u[ut]?s(ut):p.bM;return function(t){l.setTime(+t);const e=f(l);return r(e,c(l),d(l,e),h(l),m(l),g(l),y(l))}}function At(t,e,n,i){const r=n<=1?t:i?(e,r)=>i+n*Math.floor((t(e,r)-i)/n):(e,i)=>n*Math.floor(t(e,i)/n);return e?(t,n)=>e(r(t,n),n):r}function $t(t,e,n){return e+t*7-(n+6)%7}const Ot={[Q]:t=>t.getFullYear(),[tt]:t=>Math.floor(t.getMonth()/3),[et]:t=>t.getMonth(),[it]:t=>t.getDate(),[at]:t=>t.getHours(),[st]:t=>t.getMinutes(),[lt]:t=>t.getSeconds(),[ut]:t=>t.getMilliseconds(),[ot]:t=>bt(t),[nt]:t=>xt(t),[nt+rt]:(t,e)=>$t(xt(t),t.getDay(),_t(e)),[rt]:(t,e)=>$t(1,t.getDay(),_t(e))};const Tt={[tt]:t=>3*t,[nt]:(t,e)=>$t(t,0,_t(e))};function Ct(t,e){return Rt(t,e||1,Ot,Tt,wt)}const Nt={[Q]:t=>t.getUTCFullYear(),[tt]:t=>Math.floor(t.getUTCMonth()/3),[et]:t=>t.getUTCMonth(),[it]:t=>t.getUTCDate(),[at]:t=>t.getUTCHours(),[st]:t=>t.getUTCMinutes(),[lt]:t=>t.getUTCSeconds(),[ut]:t=>t.getUTCMilliseconds(),[ot]:t=>Et(t),[nt]:t=>St(t),[rt]:(t,e)=>$t(1,t.getUTCDay(),zt(e)),[nt+rt]:(t,e)=>$t(St(t),t.getUTCDay(),zt(e))};const Ut={[tt]:t=>3*t,[nt]:(t,e)=>$t(t,0,zt(e))};function It(t,e){return Rt(t,e||1,Nt,Ut,Dt)}const Pt={[Q]:Z.jB,[tt]:H.F0.every(3),[et]:H.F0,[nt]:B.Zy,[it]:X.rr,[rt]:X.rr,[ot]:X.rr,[at]:J.WQ,[st]:G.Z_,[lt]:K.E,[ut]:V.A};const qt={[Q]:Z.ol,[tt]:H.me.every(3),[et]:H.me,[nt]:B.pI,[it]:X.AN,[rt]:X.AN,[ot]:X.AN,[at]:J.lM,[st]:G.rz,[lt]:K.E,[ut]:V.A};function Lt(t){return Pt[t]}function jt(t){return qt[t]}function Ft(t,e,n){return t?t.offset(e,n):undefined}function Wt(t,e,n){return Ft(Lt(t),e,n)}function Xt(t,e,n){return Ft(jt(t),e,n)}function Bt(t,e,n,i){return t?t.range(e,n,i):undefined}function Zt(t,e,n,i){return Bt(Lt(t),e,n,i)}function Ht(t,e,n,i){return Bt(jt(t),e,n,i)}const Jt=1e3,Gt=Jt*60,Kt=Gt*60,Vt=Kt*24,Yt=Vt*7,Qt=Vt*30,te=Vt*365;const ee=[Q,et,it,at,st,lt,ut],ne=ee.slice(0,-1),ie=ne.slice(0,-1),re=ie.slice(0,-1),oe=re.slice(0,-1),ae=[Q,nt],se=[Q,et],le=[Q];const ue=[[ne,1,Jt],[ne,5,5*Jt],[ne,15,15*Jt],[ne,30,30*Jt],[ie,1,Gt],[ie,5,5*Gt],[ie,15,15*Gt],[ie,30,30*Gt],[re,1,Kt],[re,3,3*Kt],[re,6,6*Kt],[re,12,12*Kt],[oe,1,Vt],[ae,1,Yt],[se,1,Qt],[se,3,3*Qt],[le,1,te]];function fe(t){const e=t.extent,n=t.maxbins||40,i=Math.abs((0,p.yP)(e))/n;let r=(0,Y.Z)((t=>t[2])).right(ue,i),o,a;if(r===ue.length){o=le,a=(0,I.ly)(e[0]/te,e[1]/te,n)}else if(r){r=ue[i/ue[r-1][2]<ue[r][2]/i?r-1:r];o=r[0];a=r[1]}else{o=ee;a=Math.max((0,I.ly)(e[0],e[1],n),1)}return{units:o,step:a}}var ce=n(90317);var de=n(30472);function he(t){const e={};return n=>e[n]||(e[n]=t(n))}function pe(t,e){return n=>{const i=t(n),r=i.indexOf(e);if(r<0)return i;let o=me(i,r);const a=o<i.length?i.slice(o):"";while(--o>r)if(i[o]!=="0"){++o;break}return i.slice(0,o)+a}}function me(t,e){let n=t.lastIndexOf("e"),i;if(n>0)return n;for(n=t.length;--n>e;){i=t.charCodeAt(n);if(i>=48&&i<=57)return n+1}}function ge(t){const e=he(t.format),n=t.formatPrefix;return{format:e,formatPrefix:n,formatFloat(t){const n=(0,P.Z)(t||",");if(n.precision==null){n.precision=12;switch(n.type){case"%":n.precision-=2;break;case"e":n.precision-=1;break}return pe(e(n),e(".1f")(1)[1])}else{return e(n)}},formatSpan(t,i,r,o){o=(0,P.Z)(o==null?",f":o);const a=(0,I.ly)(t,i,r),s=Math.max(Math.abs(t),Math.abs(i));let l;if(o.precision==null){switch(o.type){case"s":{if(!isNaN(l=(0,q.Z)(a,s))){o.precision=l}return n(o,s)}case"":case"e":case"g":case"p":case"r":{if(!isNaN(l=(0,L.Z)(a,s))){o.precision=l-(o.type==="e")}break}case"f":case"%":{if(!isNaN(l=(0,j.Z)(a))){o.precision=l-(o.type==="%")*2}break}}}return e(o)}}}let ye;ve();function ve(){return ye=ge({format:F.WU,formatPrefix:F.jH})}function be(t){return ge((0,W.Z)(t))}function xe(t){return arguments.length?ye=be(t):ye}function _e(t,e,n){n=n||{};if(!(0,p.Kn)(n)){(0,p.vU)(`Invalid time multi-format specifier: ${n}`)}const i=e(lt),r=e(st),o=e(at),a=e(it),s=e(nt),l=e(et),u=e(tt),f=e(Q),c=t(n[ut]||".%L"),d=t(n[lt]||":%S"),h=t(n[st]||"%I:%M"),m=t(n[at]||"%I %p"),g=t(n[it]||n[rt]||"%a %d"),y=t(n[nt]||"%b %d"),v=t(n[et]||"%B"),b=t(n[tt]||"%B"),x=t(n[Q]||"%Y");return t=>(i(t)<t?c:r(t)<t?d:o(t)<t?h:a(t)<t?m:l(t)<t?s(t)<t?g:y:f(t)<t?u(t)<t?v:b:x)(t)}function we(t){const e=he(t.format),n=he(t.utcFormat);return{timeFormat:t=>(0,p.HD)(t)?e(t):_e(e,Lt,t),utcFormat:t=>(0,p.HD)(t)?n(t):_e(n,jt,t),timeParse:he(t.parse),utcParse:he(t.utcParse)}}let ke;Me();function Me(){return ke=we({format:ce.i$,parse:ce.Z1,utcFormat:ce.g0,utcParse:ce.wp})}function Ee(t){return we((0,de.Z)(t))}function Se(t){return arguments.length?ke=Ee(t):ke}const ze=(t,e)=>(0,p.l7)({},t,e);function De(t,e){const n=t?be(t):xe();const i=e?Ee(e):Se();return ze(n,i)}function Re(t,e){const n=arguments.length;if(n&&n!==2){(0,p.vU)("defaultLocale expects either zero or two arguments.")}return n?ze(xe(t),Se(e)):ze(xe(),Se())}function Ae(){ve();Me();return Re()}const $e=/^(data:|([A-Za-z]+:)?\/\/)/;const Oe=/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp|file|data):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i;const Te=/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205f\u3000]/g;const Ce="file://";function Ne(t,e){return n=>({options:n||{},sanitize:Ie,load:Ue,fileAccess:!!e,file:Pe(e),http:Le(t)})}async function Ue(t,e){const n=await this.sanitize(t,e),i=n.href;return n.localFile?this.file(i):this.http(i,e)}async function Ie(t,e){e=(0,p.l7)({},this.options,e);const n=this.fileAccess,i={href:null};let r,o,a;const s=Oe.test(t.replace(Te,""));if(t==null||typeof t!=="string"||!s){(0,p.vU)("Sanitize failure, invalid URI: "+(0,p.m8)(t))}const l=$e.test(t);if((a=e.baseURL)&&!l){if(!t.startsWith("/")&&!a.endsWith("/")){t="/"+t}t=a+t}o=(r=t.startsWith(Ce))||e.mode==="file"||e.mode!=="http"&&!l&&n;if(r){t=t.slice(Ce.length)}else if(t.startsWith("//")){if(e.defaultProtocol==="file"){t=t.slice(2);o=true}else{t=(e.defaultProtocol||"http")+":"+t}}Object.defineProperty(i,"localFile",{value:!!o});i.href=t;if(e.target){i.target=e.target+""}if(e.rel){i.rel=e.rel+""}if(e.context==="image"&&e.crossOrigin){i.crossOrigin=e.crossOrigin+""}return i}function Pe(t){return t?e=>new Promise(((n,i)=>{t.readFile(e,((t,e)=>{if(t)i(t);else n(e)}))})):qe}async function qe(){(0,p.vU)("No file system access.")}function Le(t){return t?async function(e,n){const i=(0,p.l7)({},this.options.http,n),r=n&&n.response,o=await t(e,i);return!o.ok?(0,p.vU)(o.status+""+o.statusText):(0,p.mf)(o[r])?o[r]():o.text()}:je}async function je(){(0,p.vU)("No HTTP fetch method available.")}const Fe=t=>t!=null&&t===t;const We=t=>t==="true"||t==="false"||t===true||t===false;const Xe=t=>!Number.isNaN(Date.parse(t));const Be=t=>!Number.isNaN(+t)&&!(t instanceof Date);const Ze=t=>Be(t)&&Number.isInteger(+t);const He={boolean:p.sw,integer:p.He,number:p.He,date:p.ZU,string:p.BB,unknown:p.yR};const Je=[We,Ze,Be,Xe];const Ge=["boolean","integer","number","date"];function Ke(t,e){if(!t||!t.length)return"unknown";const n=t.length,i=Je.length,r=Je.map(((t,e)=>e+1));for(let o=0,a=0,s,l;o<n;++o){l=e?t[o][e]:t[o];for(s=0;s<i;++s){if(r[s]&&Fe(l)&&!Je[s](l)){r[s]=0;++a;if(a===Je.length)return"string"}}}return Ge[r.reduce(((t,e)=>t===0?e:t),0)-1]}function Ve(t,e){return e.reduce(((e,n)=>{e[n]=Ke(t,n);return e}),{})}function Ye(t){const e=function(e,n){const i={delimiter:t};return Qe(e,n?(0,p.l7)(n,i):i)};e.responseType="text";return e}function Qe(t,e){if(e.header){t=e.header.map(p.m8).join(e.delimiter)+"\n"+t}return S(e.delimiter).parse(t+"")}Qe.responseType="text";function tn(t){return typeof Buffer==="function"&&(0,p.mf)(Buffer.isBuffer)?Buffer.isBuffer(t):false}function en(t,e){const n=e&&e.property?(0,p.EP)(e.property):p.yR;return(0,p.Kn)(t)&&!tn(t)?nn(n(t),e):n(JSON.parse(t))}en.responseType="json";function nn(t,e){if(!(0,p.kJ)(t)&&(0,p.TW)(t)){t=[...t]}return e&&e.copy?JSON.parse(JSON.stringify(t)):t}const rn={interior:(t,e)=>t!==e,exterior:(t,e)=>t===e};function on(t,e){let n,i,r,o;t=en(t,e);if(e&&e.feature){n=A;r=e.feature}else if(e&&e.mesh){n=C;r=e.mesh;o=rn[e.filter]}else{(0,p.vU)("Missing TopoJSON feature or mesh parameter.")}i=(i=t.objects[r])?n(t,i,o):(0,p.vU)("Invalid TopoJSON object: "+r);return i&&i.features||[i]}on.responseType="json";const an={dsv:Qe,csv:Ye(","),tsv:Ye("\t"),json:en,topojson:on};function sn(t,e){if(arguments.length>1){an[t]=e;return this}else{return(0,p.nr)(an,t)?an[t]:null}}function ln(t){const e=sn(t);return e&&e.responseType||"text"}function un(t,e,n,i){e=e||{};const r=sn(e.type||"json");if(!r)(0,p.vU)("Unknown data format type: "+e.type);t=r(t,e);if(e.parse)fn(t,e.parse,n,i);if((0,p.nr)(t,"columns"))delete t.columns;return t}function fn(t,e,n,i){if(!t.length)return;const r=Se();n=n||r.timeParse;i=i||r.utcParse;let o=t.columns||Object.keys(t[0]),a,s,l,u,f,c;if(e==="auto")e=Ve(t,o);o=Object.keys(e);const d=o.map((t=>{const r=e[t];let o,a;if(r&&(r.startsWith("date:")||r.startsWith("utc:"))){o=r.split(/:(.+)?/,2);a=o[1];if(a[0]==="'"&&a[a.length-1]==="'"||a[0]==='"'&&a[a.length-1]==='"'){a=a.slice(1,-1)}const t=o[0]==="utc"?i:n;return t(a)}if(!He[r]){throw Error("Illegal format pattern: "+t+":"+r)}return He[r]}));for(l=0,f=t.length,c=o.length;l<f;++l){a=t[l];for(u=0;u<c;++u){s=o[u];a[s]=d[u](a[s])}}}const cn=Ne(typeof fetch!=="undefined"&&fetch,null);function dn(t){const e=t||p.yR,n=[],i={};n.add=t=>{const r=e(t);if(!i[r]){i[r]=1;n.push(t)}return n};n.remove=t=>{const r=e(t);if(i[r]){i[r]=0;const e=n.indexOf(t);if(e>=0)n.splice(e,1)}return n};return n}async function hn(t,e){try{await e(t)}catch(n){t.error(n)}}const pn=Symbol("vega_id");let mn=1;function gn(t){return!!(t&&yn(t))}function yn(t){return t[pn]}function vn(t,e){t[pn]=e;return t}function bn(t){const e=t===Object(t)?t:{data:t};return yn(e)?e:vn(e,mn++)}function xn(t){return _n(t,bn({}))}function _n(t,e){for(const n in t)e[n]=t[n];return e}function wn(t,e){return vn(e,yn(t))}function kn(t,e){return!t?null:e?(n,i)=>t(n,i)||yn(e(n))-yn(e(i)):(e,n)=>t(e,n)||yn(e)-yn(n)}function Mn(t){return t&&t.constructor===En}function En(){const t=[],e=[],n=[],i=[],r=[];let o=null,a=false;return{constructor:En,insert(e){const n=(0,p.IX)(e),i=n.length;for(let r=0;r<i;++r)t.push(n[r]);return this},remove(t){const n=(0,p.mf)(t)?i:e,r=(0,p.IX)(t),o=r.length;for(let e=0;e<o;++e)n.push(r[e]);return this},modify(t,e,i){const o={field:e,value:(0,p.a9)(i)};if((0,p.mf)(t)){o.filter=t;r.push(o)}else{o.tuple=t;n.push(o)}return this},encode(t,e){if((0,p.mf)(t))r.push({filter:t,field:e});else n.push({tuple:t,field:e});return this},clean(t){o=t;return this},reflow(){a=true;return this},pulse(s,l){const u={},f={};let c,d,h,p,m,g;for(c=0,d=l.length;c<d;++c){u[yn(l[c])]=1}for(c=0,d=e.length;c<d;++c){m=e[c];u[yn(m)]=-1}for(c=0,d=i.length;c<d;++c){p=i[c];l.forEach((t=>{if(p(t))u[yn(t)]=-1}))}for(c=0,d=t.length;c<d;++c){m=t[c];g=yn(m);if(u[g]){u[g]=1}else{s.add.push(bn(t[c]))}}for(c=0,d=l.length;c<d;++c){m=l[c];if(u[yn(m)]<0)s.rem.push(m)}function y(t,e,n){if(n){t[e]=n(t)}else{s.encode=e}if(!a)f[yn(t)]=t}for(c=0,d=n.length;c<d;++c){h=n[c];m=h.tuple;p=h.field;g=u[yn(m)];if(g>0){y(m,p,h.value);s.modifies(p)}}for(c=0,d=r.length;c<d;++c){h=r[c];p=h.filter;l.forEach((t=>{if(p(t)&&u[yn(t)]>0){y(t,h.field,h.value)}}));s.modifies(h.field)}if(a){s.mod=e.length||i.length?l.filter((t=>u[yn(t)]>0)):l.slice()}else{for(g in f)s.mod.push(f[g])}if(o||o==null&&(e.length||i.length)){s.clean(true)}return s}}}const Sn="_:mod:_";function zn(){Object.defineProperty(this,Sn,{writable:true,value:{}})}zn.prototype={set(t,e,n,i){const r=this,o=r[t],a=r[Sn];if(e!=null&&e>=0){if(o[e]!==n||i){o[e]=n;a[e+":"+t]=-1;a[t]=-1}}else if(o!==n||i){r[t]=n;a[t]=(0,p.kJ)(n)?1+n.length:-1}return r},modified(t,e){const n=this[Sn];if(!arguments.length){for(const t in n){if(n[t])return true}return false}else if((0,p.kJ)(t)){for(let e=0;e<t.length;++e){if(n[t[e]])return true}return false}return e!=null&&e>=0?e+1<n[t]||!!n[e+":"+t]:!!n[t]},clear(){this[Sn]={};return this}};let Dn=0;const Rn="pulse",An=new zn;const $n=1,On=2;function Tn(t,e,n,i){this.id=++Dn;this.value=t;this.stamp=-1;this.rank=-1;this.qrank=-1;this.flags=0;if(e){this._update=e}if(n)this.parameters(n,i)}function Cn(t){return function(e){const n=this.flags;if(arguments.length===0)return!!(n&t);this.flags=e?n|t:n&~t;return this}}Tn.prototype={targets(){return this._targets||(this._targets=dn(p.id))},set(t){if(this.value!==t){this.value=t;return 1}else{return 0}},skip:Cn($n),modified:Cn(On),parameters(t,e,n){e=e!==false;const i=this._argval=this._argval||new zn,r=this._argops=this._argops||[],o=[];let a,s,l,u;const f=(t,n,a)=>{if(a instanceof Tn){if(a!==this){if(e)a.targets().add(this);o.push(a)}r.push({op:a,name:t,index:n})}else{i.set(t,n,a)}};for(a in t){s=t[a];if(a===Rn){(0,p.IX)(s).forEach((t=>{if(!(t instanceof Tn)){(0,p.vU)("Pulse parameters must be operator instances.")}else if(t!==this){t.targets().add(this);o.push(t)}}));this.source=s}else if((0,p.kJ)(s)){i.set(a,-1,Array(l=s.length));for(u=0;u<l;++u)f(a,u,s[u])}else{f(a,-1,s)}}this.marshall().clear();if(n)r.initonly=true;return o},marshall(t){const e=this._argval||An,n=this._argops;let i,r,o,a;if(n){const s=n.length;for(r=0;r<s;++r){i=n[r];o=i.op;a=o.modified()&&o.stamp===t;e.set(i.name,i.index,o.value,a)}if(n.initonly){for(r=0;r<s;++r){i=n[r];i.op.targets().remove(this)}this._argops=null;this._update=null}}return e},detach(){const t=this._argops;let e,n,i,r;if(t){for(e=0,n=t.length;e<n;++e){i=t[e];r=i.op;if(r._targets){r._targets.remove(this)}}}this.pulse=null;this.source=null},evaluate(t){const e=this._update;if(e){const n=this.marshall(t.stamp),i=e.call(this,n,t);n.clear();if(i!==this.value){this.value=i}else if(!this.modified()){return t.StopPropagation}}},run(t){if(t.stamp<this.stamp)return t.StopPropagation;let e;if(this.skip()){this.skip(false);e=0}else{e=this.evaluate(t)}return this.pulse=e||t}};function Nn(t,e,n,i){let r=1,o;if(t instanceof Tn){o=t}else if(t&&t.prototype instanceof Tn){o=new t}else if((0,p.mf)(t)){o=new Tn(null,t)}else{r=0;o=new Tn(t,e)}this.rank(o);if(r){i=n;n=e}if(n)this.connect(o,o.parameters(n,i));this.touch(o);return o}function Un(t,e){const n=t.rank,i=e.length;for(let r=0;r<i;++r){if(n<e[r].rank){this.rerank(t);return}}}let In=0;function Pn(t,e,n){this.id=++In;this.value=null;if(n)this.receive=n;if(t)this._filter=t;if(e)this._apply=e}function qn(t,e,n){return new Pn(t,e,n)}Pn.prototype={_filter:p.yb,_apply:p.yR,targets(){return this._targets||(this._targets=dn(p.id))},consume(t){if(!arguments.length)return!!this._consume;this._consume=!!t;return this},receive(t){if(this._filter(t)){const e=this.value=this._apply(t),n=this._targets,i=n?n.length:0;for(let t=0;t<i;++t)n[t].receive(e);if(this._consume){t.preventDefault();t.stopPropagation()}}},filter(t){const e=qn(t);this.targets().add(e);return e},apply(t){const e=qn(null,t);this.targets().add(e);return e},merge(){const t=qn();this.targets().add(t);for(let e=0,n=arguments.length;e<n;++e){arguments[e].targets().add(t)}return t},throttle(t){let e=-1;return this.filter((()=>{const n=Date.now();if(n-e>t){e=n;return 1}else{return 0}}))},debounce(t){const e=qn();this.targets().add(qn(null,null,(0,p.Ds)(t,(t=>{const n=t.dataflow;e.receive(t);if(n&&n.run)n.run()}))));return e},between(t,e){let n=false;t.targets().add(qn(null,null,(()=>n=true)));e.targets().add(qn(null,null,(()=>n=false)));return this.filter((()=>n))},detach(){this._filter=p.yb;this._targets=null}};function Ln(t,e,n,i){const r=this,o=qn(n,i),a=function(t){t.dataflow=r;try{o.receive(t)}catch(e){r.error(e)}finally{r.run()}};let s;if(typeof t==="string"&&typeof document!=="undefined"){s=document.querySelectorAll(t)}else{s=(0,p.IX)(t)}const l=s.length;for(let u=0;u<l;++u){s[u].addEventListener(e,a)}return o}function jn(t,e){const n=this.locale();return un(t,e,n.timeParse,n.utcParse)}function Fn(t,e,n){e=this.parse(e,n);return this.pulse(t,this.changeset().insert(e))}async function Wn(t,e){const n=this;let i=0,r;try{r=await n.loader().load(t,{context:"dataflow",response:ln(e&&e.type)});try{r=n.parse(r,e)}catch(o){i=-2;n.warn("Data ingestion failed",t,o)}}catch(o){i=-1;n.warn("Loading failed",t,o)}return{data:r,status:i}}async function Xn(t,e,n){const i=this,r=i._pending||Bn(i);r.requests+=1;const o=await i.request(e,n);i.pulse(t,i.changeset().remove(p.yb).insert(o.data||[]));r.done();return o}function Bn(t){let e;const n=new Promise((t=>e=t));n.requests=0;n.done=()=>{if(--n.requests===0){t._pending=null;e(t)}};return t._pending=n}const Zn={skip:true};function Hn(t,e,n,i,r){const o=t instanceof Tn?Gn:Jn;o(this,t,e,n,i,r);return this}function Jn(t,e,n,i,r,o){const a=(0,p.l7)({},o,Zn);let s,l;if(!(0,p.mf)(n))n=(0,p.a9)(n);if(i===undefined){s=e=>t.touch(n(e))}else if((0,p.mf)(i)){l=new Tn(null,i,r,false);s=e=>{l.evaluate(e);const i=n(e),r=l.value;Mn(r)?t.pulse(i,r,o):t.update(i,r,a)}}else{s=e=>t.update(n(e),i,a)}e.apply(s)}function Gn(t,e,n,i,r,o){if(i===undefined){e.targets().add(n)}else{const a=o||{},s=new Tn(null,Kn(n,i),r,false);s.modified(a.force);s.rank=e.rank;e.targets().add(s);if(n){s.skip(true);s.value=n.value;s.targets().add(n);t.connect(n,[s])}}}function Kn(t,e){e=(0,p.mf)(e)?e:(0,p.a9)(e);return t?function(n,i){const r=e(n,i);if(!t.skip()){t.skip(r!==this.value).value=r}return r}:e}function Vn(t){t.rank=++this._rank}function Yn(t){const e=[t];let n,i,r;while(e.length){this.rank(n=e.pop());if(i=n._targets){for(r=i.length;--r>=0;){e.push(n=i[r]);if(n===t)(0,p.vU)("Cycle detected in dataflow graph.")}}}}const Qn={};const ti=1<<0,ei=1<<1,ni=1<<2,ii=ti|ei,ri=ti|ni,oi=ti|ei|ni,ai=1<<3,si=1<<4,li=1<<5,ui=1<<6;function fi(t,e,n){this.dataflow=t;this.stamp=e==null?-1:e;this.add=[];this.rem=[];this.mod=[];this.fields=null;this.encode=n||null}function ci(t,e){const n=[];(0,p.FP)(t,e,(t=>n.push(t)));return n}function di(t,e){const n={};t.visit(e,(t=>{n[yn(t)]=1}));return t=>n[yn(t)]?null:t}function hi(t,e){return t?(n,i)=>t(n,i)&&e(n,i):e}fi.prototype={StopPropagation:Qn,ADD:ti,REM:ei,MOD:ni,ADD_REM:ii,ADD_MOD:ri,ALL:oi,REFLOW:ai,SOURCE:si,NO_SOURCE:li,NO_FIELDS:ui,fork(t){return new fi(this.dataflow).init(this,t)},clone(){const t=this.fork(oi);t.add=t.add.slice();t.rem=t.rem.slice();t.mod=t.mod.slice();if(t.source)t.source=t.source.slice();return t.materialize(oi|si)},addAll(){let t=this;const e=!t.source||t.add===t.rem||!t.rem.length&&t.source.length===t.add.length;if(e){return t}else{t=new fi(this.dataflow).init(this);t.add=t.source;t.rem=[];return t}},init(t,e){const n=this;n.stamp=t.stamp;n.encode=t.encode;if(t.fields&&!(e&ui)){n.fields=t.fields}if(e&ti){n.addF=t.addF;n.add=t.add}else{n.addF=null;n.add=[]}if(e&ei){n.remF=t.remF;n.rem=t.rem}else{n.remF=null;n.rem=[]}if(e&ni){n.modF=t.modF;n.mod=t.mod}else{n.modF=null;n.mod=[]}if(e&li){n.srcF=null;n.source=null}else{n.srcF=t.srcF;n.source=t.source;if(t.cleans)n.cleans=t.cleans}return n},runAfter(t){this.dataflow.runAfter(t)},changed(t){const e=t||oi;return e&ti&&this.add.length||e&ei&&this.rem.length||e&ni&&this.mod.length},reflow(t){if(t)return this.fork(oi).reflow();const e=this.add.length,n=this.source&&this.source.length;if(n&&n!==e){this.mod=this.source;if(e)this.filter(ni,di(this,ti))}return this},clean(t){if(arguments.length){this.cleans=!!t;return this}else{return this.cleans}},modifies(t){const e=this.fields||(this.fields={});if((0,p.kJ)(t)){t.forEach((t=>e[t]=true))}else{e[t]=true}return this},modified(t,e){const n=this.fields;return!((e||this.mod.length)&&n)?false:!arguments.length?!!n:(0,p.kJ)(t)?t.some((t=>n[t])):n[t]},filter(t,e){const n=this;if(t&ti)n.addF=hi(n.addF,e);if(t&ei)n.remF=hi(n.remF,e);if(t&ni)n.modF=hi(n.modF,e);if(t&si)n.srcF=hi(n.srcF,e);return n},materialize(t){t=t||oi;const e=this;if(t&ti&&e.addF){e.add=ci(e.add,e.addF);e.addF=null}if(t&ei&&e.remF){e.rem=ci(e.rem,e.remF);e.remF=null}if(t&ni&&e.modF){e.mod=ci(e.mod,e.modF);e.modF=null}if(t&si&&e.srcF){e.source=e.source.filter(e.srcF);e.srcF=null}return e},visit(t,e){const n=this,i=e;if(t&si){(0,p.FP)(n.source,n.srcF,i);return n}if(t&ti)(0,p.FP)(n.add,n.addF,i);if(t&ei)(0,p.FP)(n.rem,n.remF,i);if(t&ni)(0,p.FP)(n.mod,n.modF,i);const r=n.source;if(t&ai&&r){const t=n.add.length+n.mod.length;if(t===r.length);else if(t){(0,p.FP)(r,di(n,ri),i)}else{(0,p.FP)(r,n.srcF,i)}}return n}};function pi(t,e,n,i){const r=this;let o=0;this.dataflow=t;this.stamp=e;this.fields=null;this.encode=i||null;this.pulses=n;for(const a of n){if(a.stamp!==e)continue;if(a.fields){const t=r.fields||(r.fields={});for(const e in a.fields){t[e]=1}}if(a.changed(r.ADD))o|=r.ADD;if(a.changed(r.REM))o|=r.REM;if(a.changed(r.MOD))o|=r.MOD}this.changes=o}(0,p.XW)(pi,fi,{fork(t){const e=new fi(this.dataflow).init(this,t&this.NO_FIELDS);if(t!==undefined){if(t&e.ADD)this.visit(e.ADD,(t=>e.add.push(t)));if(t&e.REM)this.visit(e.REM,(t=>e.rem.push(t)));if(t&e.MOD)this.visit(e.MOD,(t=>e.mod.push(t)))}return e},changed(t){return this.changes&t},modified(t){const e=this,n=e.fields;return!(n&&e.changes&e.MOD)?0:(0,p.kJ)(t)?t.some((t=>n[t])):n[t]},filter(){(0,p.vU)("MultiPulse does not support filtering.")},materialize(){(0,p.vU)("MultiPulse does not support materialization.")},visit(t,e){const n=this,i=n.pulses,r=i.length;let o=0;if(t&n.SOURCE){for(;o<r;++o){i[o].visit(t,e)}}else{for(;o<r;++o){if(i[o].stamp===n.stamp){i[o].visit(t,e)}}}return n}});async function mi(t,e,n){const i=this,r=[];if(i._pulse)return bi(i);if(i._pending)await i._pending;if(e)await hn(i,e);if(!i._touched.length){i.debug("Dataflow invoked, but nothing to do.");return i}const o=++i._clock;i._pulse=new fi(i,o,t);i._touched.forEach((t=>i._enqueue(t,true)));i._touched=dn(p.id);let a=0,s,l,u;try{while(i._heap.size()>0){s=i._heap.pop();if(s.rank!==s.qrank){i._enqueue(s,true);continue}l=s.run(i._getPulse(s,t));if(l.then){l=await l}else if(l.async){r.push(l.async);l=Qn}if(l!==Qn){if(s._targets)s._targets.forEach((t=>i._enqueue(t)))}++a}}catch(f){i._heap.clear();u=f}i._input={};i._pulse=null;i.debug(`Pulse ${o}: ${a} operators`);if(u){i._postrun=[];i.error(u)}if(i._postrun.length){const t=i._postrun.sort(((t,e)=>e.priority-t.priority));i._postrun=[];for(let e=0;e<t.length;++e){await hn(i,t[e].callback)}}if(n)await hn(i,n);if(r.length){Promise.all(r).then((t=>i.runAsync(null,(()=>{t.forEach((t=>{try{t(i)}catch(f){i.error(f)}}))}))))}return i}async function gi(t,e,n){while(this._running)await this._running;const i=()=>this._running=null;(this._running=this.evaluate(t,e,n)).then(i,i);return this._running}function yi(t,e,n){return this._pulse?bi(this):(this.evaluate(t,e,n),this)}function vi(t,e,n){if(this._pulse||e){this._postrun.push({priority:n||0,callback:t})}else{try{t(this)}catch(i){this.error(i)}}}function bi(t){t.error("Dataflow already running. Use runAsync() to chain invocations.");return t}function xi(t,e){const n=t.stamp<this._clock;if(n)t.stamp=this._clock;if(n||e){t.qrank=t.rank;this._heap.push(t)}}function _i(t,e){const n=t.source,i=this._clock;return n&&(0,p.kJ)(n)?new pi(this,i,n.map((t=>t.pulse)),e):this._input[t.id]||wi(this._pulse,n&&n.pulse)}function wi(t,e){if(e&&e.stamp===t.stamp){return e}t=t.fork();if(e&&e!==Qn){t.source=e.source}return t}const ki={skip:false,force:false};function Mi(t,e){const n=e||ki;if(this._pulse){this._enqueue(t)}else{this._touched.add(t)}if(n.skip)t.skip(true);return this}function Ei(t,e,n){const i=n||ki;if(t.set(e)||i.force){this.touch(t,i)}return this}function Si(t,e,n){this.touch(t,n||ki);const i=new fi(this,this._clock+(this._pulse?0:1)),r=t.pulse&&t.pulse.source||[];i.target=t;this._input[t.id]=e.pulse(i,r);return this}function zi(t){let e=[];return{clear:()=>e=[],size:()=>e.length,peek:()=>e[0],push:n=>{e.push(n);return Di(e,0,e.length-1,t)},pop:()=>{const n=e.pop();let i;if(e.length){i=e[0];e[0]=n;Ri(e,0,t)}else{i=n}return i}}}function Di(t,e,n,i){let r,o;const a=t[n];while(n>e){o=n-1>>1;r=t[o];if(i(a,r)<0){t[n]=r;n=o;continue}break}return t[n]=a}function Ri(t,e,n){const i=e,r=t.length,o=t[e];let a=(e<<1)+1,s;while(a<r){s=a+1;if(s<r&&n(t[a],t[s])>=0){a=s}t[e]=t[a];e=a;a=(e<<1)+1}t[e]=o;return Di(t,i,e,n)}function Ai(){this.logger((0,p.kg)());this.logLevel(p.jj);this._clock=0;this._rank=0;this._locale=Re();try{this._loader=cn()}catch(t){}this._touched=dn(p.id);this._input={};this._pulse=null;this._heap=zi(((t,e)=>t.qrank-e.qrank));this._postrun=[]}function $i(t){return function(){return this._log[t].apply(this,arguments)}}Ai.prototype={stamp(){return this._clock},loader(t){if(arguments.length){this._loader=t;return this}else{return this._loader}},locale(t){if(arguments.length){this._locale=t;return this}else{return this._locale}},logger(t){if(arguments.length){this._log=t;return this}else{return this._log}},error:$i("error"),warn:$i("warn"),info:$i("info"),debug:$i("debug"),logLevel:$i("level"),cleanThreshold:1e4,add:Nn,connect:Un,rank:Vn,rerank:Yn,pulse:Si,touch:Mi,update:Ei,changeset:En,ingest:Fn,parse:jn,preload:Xn,request:Wn,events:Ln,on:Hn,evaluate:mi,run:yi,runAsync:gi,runAfter:vi,_enqueue:xi,_getPulse:_i};function Oi(t,e){Tn.call(this,t,null,e)}(0,p.XW)(Oi,Tn,{run(t){if(t.stamp<this.stamp)return t.StopPropagation;let e;if(this.skip()){this.skip(false)}else{e=this.evaluate(t)}e=e||t;if(e.then){e=e.then((t=>this.pulse=t))}else if(e!==t.StopPropagation){this.pulse=e}return e},evaluate(t){const e=this.marshall(t.stamp),n=this.transform(e,t);e.clear();return n},transform(){}});const Ti={};function Ci(t){const e=Ni(t);return e&&e.Definition||null}function Ni(t){t=t&&t.toLowerCase();return(0,p.nr)(Ti,t)?Ti[t]:null}var Ui=n(10318);var Ii=n(98823);var Pi=n(47622);function qi(t,...e){if(typeof t[Symbol.iterator]!=="function")throw new TypeError("values is not iterable");t=Array.from(t);let[n]=e;if(n&&n.length!==2||e.length>1){const i=Uint32Array.from(t,((t,e)=>e));if(e.length>1){e=e.map((e=>t.map(e)));i.sort(((t,n)=>{for(const i of e){const e=ji(i[t],i[n]);if(e)return e}}))}else{n=t.map(n);i.sort(((t,e)=>ji(n[t],n[e])))}return permute(t,i)}return t.sort(Li(n))}function Li(t=Ui.Z){if(t===Ui.Z)return ji;if(typeof t!=="function")throw new TypeError("compare is not a function");return(e,n)=>{const i=t(e,n);if(i||i===0)return i;return(t(n,n)===0)-(t(e,e)===0)}}function ji(t,e){return(t==null||!(t>=t))-(e==null||!(e>=e))||(t<e?-1:t>e?1:0)}function Fi(t,e,n=0,i=Infinity,r){e=Math.floor(e);n=Math.floor(Math.max(0,n));i=Math.floor(Math.min(t.length-1,i));if(!(n<=e&&e<=i))return t;r=r===undefined?ji:Li(r);while(i>n){if(i-n>600){const o=i-n+1;const a=e-n+1;const s=Math.log(o);const l=.5*Math.exp(2*s/3);const u=.5*Math.sqrt(s*l*(o-l)/o)*(a-o/2<0?-1:1);const f=Math.max(n,Math.floor(e-a*l/o+u));const c=Math.min(i,Math.floor(e+(o-a)*l/o+u));Fi(t,e,f,c,r)}const o=t[e];let a=n;let s=i;Wi(t,n,e);if(r(t[i],o)>0)Wi(t,n,i);while(a<s){Wi(t,a,s),++a,--s;while(r(t[a],o)<0)++a;while(r(t[s],o)>0)--s}if(r(t[n],o)===0)Wi(t,n,s);else++s,Wi(t,s,i);if(s<=e)n=s+1;if(e<=s)i=s-1}return t}function Wi(t,e,n){const i=t[e];t[e]=t[n];t[n]=i}var Xi=n(62921);function Bi(t,e,n){t=Float64Array.from((0,Xi.K)(t,n));if(!(i=t.length)||isNaN(e=+e))return;if(e<=0||i<2)return(0,Pi.Z)(t);if(e>=1)return(0,Ii.Z)(t);var i,r=(i-1)*e,o=Math.floor(r),a=(0,Ii.Z)(Fi(t,o).subarray(0,o+1)),s=(0,Pi.Z)(t.subarray(o+1));return a+(s-a)*(r-o)}function Zi(t,e,n=Xi.Z){if(!(i=t.length)||isNaN(e=+e))return;if(e<=0||i<2)return+n(t[0],0,t);if(e>=1)return+n(t[i-1],i-1,t);var i,r=(i-1)*e,o=Math.floor(r),a=+n(t[o],o,t),s=+n(t[o+1],o+1,t);return a+(s-a)*(r-o)}function Hi(t,e,n){t=Float64Array.from(numbers(t,n));if(!(i=t.length)||isNaN(e=+e))return;if(e<=0||i<2)return minIndex(t);if(e>=1)return maxIndex(t);var i,r=Math.floor((i-1)*e),o=(e,n)=>ascendingDefined(t[e],t[n]),a=quickselect(Uint32Array.from(t,((t,e)=>e)),r,0,i-1,o);return greatest(a.subarray(0,r+1),(e=>t[e]))}function Ji(t,e){let n=0;let i;let r=0;let o=0;if(e===undefined){for(let e of t){if(e!=null&&(e=+e)>=e){i=e-r;r+=i/++n;o+=i*(e-r)}}}else{let a=-1;for(let s of t){if((s=e(s,++a,t))!=null&&(s=+s)>=s){i=s-r;r+=i/++n;o+=i*(s-r)}}}if(n>1)return o/(n-1)}function Gi(t,e){const n=Ji(t,e);return n?Math.sqrt(n):n}function Ki(t,e){return Bi(t,.5,e)}function Vi(t,e){return quantileIndex(t,.5,e)}function*Yi(t,e){if(e==null){for(let e of t){if(e!=null&&e!==""&&(e=+e)>=e){yield e}}}else{let n=-1;for(let i of t){i=e(i,++n,t);if(i!=null&&i!==""&&(i=+i)>=i){yield i}}}}function Qi(t,e,n){const i=Float64Array.from(Yi(t,n));i.sort(Ui.Z);return e.map((t=>Zi(i,t)))}function tr(t,e){return Qi(t,[.25,.5,.75],e)}function er(t,e){const n=t.length,i=Gi(t,e),r=tr(t,e),o=(r[2]-r[0])/1.34,a=Math.min(i,o)||i||Math.abs(r[0])||1;return 1.06*a*Math.pow(n,-.2)}function nr(t){const e=t.maxbins||20,n=t.base||10,i=Math.log(n),r=t.divide||[5,2];let o=t.extent[0],a=t.extent[1],s,l,u,f,c,d;const h=t.span||a-o||Math.abs(o)||1;if(t.step){s=t.step}else if(t.steps){f=h/e;for(c=0,d=t.steps.length;c<d&&t.steps[c]<f;++c);s=t.steps[Math.max(0,c-1)]}else{l=Math.ceil(Math.log(e)/i);u=t.minstep||0;s=Math.max(u,Math.pow(n,Math.round(Math.log(h)/i)-l));while(Math.ceil(h/s)>e){s*=n}for(c=0,d=r.length;c<d;++c){f=s/r[c];if(f>=u&&h/f<=e)s=f}}f=Math.log(s);const p=f>=0?0:~~(-f/i)+1,m=Math.pow(n,-p-1);if(t.nice||t.nice===undefined){f=Math.floor(o/s+m)*s;o=o<f?f-s:f;a=Math.ceil(a/s)*s}return{start:o,stop:a===o?o+s:a,step:s}}var ir=Math.random;function rr(t){ir=t}function or(t,e,n,i){if(!t.length)return[undefined,undefined];const r=Float64Array.from(Yi(t,i)),o=r.length,a=e;let s,l,u,f;for(u=0,f=Array(a);u<a;++u){for(s=0,l=0;l<o;++l){s+=r[~~(ir()*o)]}f[u]=s/o}f.sort(Ui.Z);return[Bi(f,n/2),Bi(f,1-n/2)]}function ar(t,e,n,i){i=i||(t=>t);const r=t.length,o=new Float64Array(r);let a=0,s=1,l=i(t[0]),u=l,f=l+e,c;for(;s<r;++s){c=i(t[s]);if(c>=f){u=(l+u)/2;for(;a<s;++a)o[a]=u;f=c+e;l=c}u=c}u=(l+u)/2;for(;a<s;++a)o[a]=u;return n?sr(o,e+e/4):o}function sr(t,e){const n=t.length;let i=0,r=1,o,a;while(t[i]===t[r])++r;while(r<n){o=r+1;while(t[r]===t[o])++o;if(t[r]-t[r-1]<e){a=r+(i+o-r-r>>1);while(a<r)t[a++]=t[r];while(a>r)t[a--]=t[i]}i=r;r=o}return t}function lr(t){return function(){t=(1103515245*t+12345)%2147483647;return t/2147483647}}function ur(t,e){if(e==null){e=t;t=0}let n,i,r;const o={min(t){if(arguments.length){n=t||0;r=i-n;return o}else{return n}},max(t){if(arguments.length){i=t||0;r=i-n;return o}else{return i}},sample(){return n+Math.floor(r*ir())},pdf(t){return t===Math.floor(t)&&t>=n&&t<i?1/r:0},cdf(t){const e=Math.floor(t);return e<n?0:e>=i?1:(e-n+1)/r},icdf(t){return t>=0&&t<=1?n-1+Math.floor(t*r):NaN}};return o.min(t).max(e)}const fr=Math.sqrt(2*Math.PI);const cr=Math.SQRT2;let dr=NaN;function hr(t,e){t=t||0;e=e==null?1:e;let n=0,i=0,r,o;if(dr===dr){n=dr;dr=NaN}else{do{n=ir()*2-1;i=ir()*2-1;r=n*n+i*i}while(r===0||r>1);o=Math.sqrt(-2*Math.log(r)/r);n*=o;dr=i*o}return t+n*e}function pr(t,e,n){n=n==null?1:n;const i=(t-(e||0))/n;return Math.exp(-.5*i*i)/(n*fr)}function mr(t,e,n){e=e||0;n=n==null?1:n;const i=(t-e)/n,r=Math.abs(i);let o;if(r>37){o=0}else{const t=Math.exp(-r*r/2);let e;if(r<7.07106781186547){e=.0352624965998911*r+.700383064443688;e=e*r+6.37396220353165;e=e*r+33.912866078383;e=e*r+112.079291497871;e=e*r+221.213596169931;e=e*r+220.206867912376;o=t*e;e=.0883883476483184*r+1.75566716318264;e=e*r+16.064177579207;e=e*r+86.7807322029461;e=e*r+296.564248779674;e=e*r+637.333633378831;e=e*r+793.826512519948;e=e*r+440.413735824752;o=o/e}else{e=r+.65;e=r+4/e;e=r+3/e;e=r+2/e;e=r+1/e;o=t/e/2.506628274631}}return i>0?1-o:o}function gr(t,e,n){if(t<0||t>1)return NaN;return(e||0)+(n==null?1:n)*cr*yr(2*t-1)}function yr(t){let e=-Math.log((1-t)*(1+t)),n;if(e<6.25){e-=3.125;n=-364441206401782e-35;n=-16850591381820166e-35+n*e;n=128584807152564e-32+n*e;n=11157877678025181e-33+n*e;n=-1333171662854621e-31+n*e;n=20972767875968562e-33+n*e;n=6637638134358324e-30+n*e;n=-4054566272975207e-29+n*e;n=-8151934197605472e-29+n*e;n=26335093153082323e-28+n*e;n=-12975133253453532e-27+n*e;n=-5415412054294628e-26+n*e;n=1.0512122733215323e-9+n*e;n=-4.112633980346984e-9+n*e;n=-2.9070369957882005e-8+n*e;n=4.2347877827932404e-7+n*e;n=-13654692000834679e-22+n*e;n=-13882523362786469e-21+n*e;n=.00018673420803405714+n*e;n=-.000740702534166267+n*e;n=-.006033670871430149+n*e;n=.24015818242558962+n*e;n=1.6536545626831027+n*e}else if(e<16){e=Math.sqrt(e)-3.25;n=2.2137376921775787e-9;n=9.075656193888539e-8+n*e;n=-2.7517406297064545e-7+n*e;n=1.8239629214389228e-8+n*e;n=15027403968909828e-22+n*e;n=-4013867526981546e-21+n*e;n=29234449089955446e-22+n*e;n=12475304481671779e-21+n*e;n=-47318229009055734e-21+n*e;n=6828485145957318e-20+n*e;n=24031110387097894e-21+n*e;n=-.0003550375203628475+n*e;n=.0009532893797373805+n*e;n=-.0016882755560235047+n*e;n=.002491442096107851+n*e;n=-.003751208507569241+n*e;n=.005370914553590064+n*e;n=1.0052589676941592+n*e;n=3.0838856104922208+n*e}else if(Number.isFinite(e)){e=Math.sqrt(e)-5;n=-27109920616438573e-27;n=-2.555641816996525e-10+n*e;n=1.5076572693500548e-9+n*e;n=-3.789465440126737e-9+n*e;n=7.61570120807834e-9+n*e;n=-1.496002662714924e-8+n*e;n=2.914795345090108e-8+n*e;n=-6.771199775845234e-8+n*e;n=2.2900482228026655e-7+n*e;n=-9.9298272942317e-7+n*e;n=4526062597223154e-21+n*e;n=-1968177810553167e-20+n*e;n=7599527703001776e-20+n*e;n=-.00021503011930044477+n*e;n=-.00013871931833623122+n*e;n=1.0103004648645344+n*e;n=4.849906401408584+n*e}else{n=Infinity}return n*t}function vr(t,e){let n,i;const r={mean(t){if(arguments.length){n=t||0;return r}else{return n}},stdev(t){if(arguments.length){i=t==null?1:t;return r}else{return i}},sample:()=>hr(n,i),pdf:t=>pr(t,n,i),cdf:t=>mr(t,n,i),icdf:t=>gr(t,n,i)};return r.mean(t).stdev(e)}function br(t,e){const n=vr();let i=0;const r={data(n){if(arguments.length){t=n;i=n?n.length:0;return r.bandwidth(e)}else{return t}},bandwidth(n){if(!arguments.length)return e;e=n;if(!e&&t)e=er(t);return r},sample(){return t[~~(ir()*i)]+e*n.sample()},pdf(r){let o=0,a=0;for(;a<i;++a){o+=n.pdf((r-t[a])/e)}return o/e/i},cdf(r){let o=0,a=0;for(;a<i;++a){o+=n.cdf((r-t[a])/e)}return o/i},icdf(){throw Error("KDE icdf not supported.")}};return r.data(t)}function xr(t,e){t=t||0;e=e==null?1:e;return Math.exp(t+hr()*e)}function _r(t,e,n){if(t<=0)return 0;e=e||0;n=n==null?1:n;const i=(Math.log(t)-e)/n;return Math.exp(-.5*i*i)/(n*fr*t)}function wr(t,e,n){return mr(Math.log(t),e,n)}function kr(t,e,n){return Math.exp(gr(t,e,n))}function Mr(t,e){let n,i;const r={mean(t){if(arguments.length){n=t||0;return r}else{return n}},stdev(t){if(arguments.length){i=t==null?1:t;return r}else{return i}},sample:()=>xr(n,i),pdf:t=>_r(t,n,i),cdf:t=>wr(t,n,i),icdf:t=>kr(t,n,i)};return r.mean(t).stdev(e)}function Er(t,e){let n=0,i;function r(t){const e=[];let i=0,r;for(r=0;r<n;++r){i+=e[r]=t[r]==null?1:+t[r]}for(r=0;r<n;++r){e[r]/=i}return e}const o={weights(t){if(arguments.length){i=r(e=t||[]);return o}return e},distributions(i){if(arguments.length){if(i){n=i.length;t=i}else{n=0;t=[]}return o.weights(e)}return t},sample(){const e=ir();let r=t[n-1],o=i[0],a=0;for(;a<n-1;o+=i[++a]){if(e<o){r=t[a];break}}return r.sample()},pdf(e){let r=0,o=0;for(;o<n;++o){r+=i[o]*t[o].pdf(e)}return r},cdf(e){let r=0,o=0;for(;o<n;++o){r+=i[o]*t[o].cdf(e)}return r},icdf(){throw Error("Mixture icdf not supported.")}};return o.distributions(t).weights(e)}function Sr(t,e){if(e==null){e=t==null?1:t;t=0}return t+(e-t)*ir()}function zr(t,e,n){if(n==null){n=e==null?1:e;e=0}return t>=e&&t<=n?1/(n-e):0}function Dr(t,e,n){if(n==null){n=e==null?1:e;e=0}return t<e?0:t>n?1:(t-e)/(n-e)}function Rr(t,e,n){if(n==null){n=e==null?1:e;e=0}return t>=0&&t<=1?e+t*(n-e):NaN}function Ar(t,e){let n,i;const r={min(t){if(arguments.length){n=t||0;return r}else{return n}},max(t){if(arguments.length){i=t==null?1:t;return r}else{return i}},sample:()=>Sr(n,i),pdf:t=>zr(t,n,i),cdf:t=>Dr(t,n,i),icdf:t=>Rr(t,n,i)};if(e==null){e=t==null?1:t;t=0}return r.min(t).max(e)}function $r(t,e,n,i){const r=i-t*t,o=Math.abs(r)<1e-24?0:(n-t*e)/r,a=e-o*t;return[a,o]}function Or(t,e,n,i){t=t.filter((t=>{let i=e(t),r=n(t);return i!=null&&(i=+i)>=i&&r!=null&&(r=+r)>=r}));if(i){t.sort(((t,n)=>e(t)-e(n)))}const r=t.length,o=new Float64Array(r),a=new Float64Array(r);let s=0,l=0,u=0,f,c,d;for(d of t){o[s]=f=+e(d);a[s]=c=+n(d);++s;l+=(f-l)/s;u+=(c-u)/s}for(s=0;s<r;++s){o[s]-=l;a[s]-=u}return[o,a,l,u]}function Tr(t,e,n,i){let r=-1,o,a;for(const s of t){o=e(s);a=n(s);if(o!=null&&(o=+o)>=o&&a!=null&&(a=+a)>=a){i(o,a,++r)}}}function Cr(t,e,n,i,r){let o=0,a=0;Tr(t,e,n,((t,e)=>{const n=e-r(t),s=e-i;o+=n*n;a+=s*s}));return 1-o/a}function Nr(t,e,n){let i=0,r=0,o=0,a=0,s=0;Tr(t,e,n,((t,e)=>{++s;i+=(t-i)/s;r+=(e-r)/s;o+=(t*e-o)/s;a+=(t*t-a)/s}));const l=$r(i,r,o,a),u=t=>l[0]+l[1]*t;return{coef:l,predict:u,rSquared:Cr(t,e,n,r,u)}}function Ur(t,e,n){let i=0,r=0,o=0,a=0,s=0;Tr(t,e,n,((t,e)=>{++s;t=Math.log(t);i+=(t-i)/s;r+=(e-r)/s;o+=(t*e-o)/s;a+=(t*t-a)/s}));const l=$r(i,r,o,a),u=t=>l[0]+l[1]*Math.log(t);return{coef:l,predict:u,rSquared:Cr(t,e,n,r,u)}}function Ir(t,e,n){const[i,r,o,a]=Or(t,e,n);let s=0,l=0,u=0,f=0,c=0,d,h,p;Tr(t,e,n,((t,e)=>{d=i[c++];h=Math.log(e);p=d*e;s+=(e*h-s)/c;l+=(p-l)/c;u+=(p*h-u)/c;f+=(d*p-f)/c}));const[m,g]=$r(l/a,s/a,u/a,f/a),y=t=>Math.exp(m+g*(t-o));return{coef:[Math.exp(m-g*o),g],predict:y,rSquared:Cr(t,e,n,a,y)}}function Pr(t,e,n){let i=0,r=0,o=0,a=0,s=0,l=0;Tr(t,e,n,((t,e)=>{const n=Math.log(t),u=Math.log(e);++l;i+=(n-i)/l;r+=(u-r)/l;o+=(n*u-o)/l;a+=(n*n-a)/l;s+=(e-s)/l}));const u=$r(i,r,o,a),f=t=>u[0]*Math.pow(t,u[1]);u[0]=Math.exp(u[0]);return{coef:u,predict:f,rSquared:Cr(t,e,n,s,f)}}function qr(t,e,n){const[i,r,o,a]=Or(t,e,n),s=i.length;let l=0,u=0,f=0,c=0,d=0,h,p,m,g;for(h=0;h<s;){p=i[h];m=r[h++];g=p*p;l+=(g-l)/h;u+=(g*p-u)/h;f+=(g*g-f)/h;c+=(p*m-c)/h;d+=(g*m-d)/h}const y=f-l*l,v=l*y-u*u,b=(d*l-c*u)/v,x=(c*y-d*u)/v,_=-b*l,w=t=>{t=t-o;return b*t*t+x*t+_+a};return{coef:[_-x*o+b*o*o+a,x-2*b*o,b],predict:w,rSquared:Cr(t,e,n,a,w)}}function Lr(t,e,n,i){if(i===1)return Nr(t,e,n);if(i===2)return qr(t,e,n);const[r,o,a,s]=Or(t,e,n),l=r.length,u=[],f=[],c=i+1;let d,h,p,m,g;for(d=0;d<c;++d){for(p=0,m=0;p<l;++p){m+=Math.pow(r[p],d)*o[p]}u.push(m);g=new Float64Array(c);for(h=0;h<c;++h){for(p=0,m=0;p<l;++p){m+=Math.pow(r[p],d+h)}g[h]=m}f.push(g)}f.push(u);const y=Fr(f),v=t=>{t-=a;let e=s+y[0]+y[1]*t+y[2]*t*t;for(d=3;d<c;++d)e+=y[d]*Math.pow(t,d);return e};return{coef:jr(c,y,-a,s),predict:v,rSquared:Cr(t,e,n,s,v)}}function jr(t,e,n,i){const r=Array(t);let o,a,s,l;for(o=0;o<t;++o)r[o]=0;for(o=t-1;o>=0;--o){s=e[o];l=1;r[o]+=s;for(a=1;a<=o;++a){l*=(o+1-a)/a;r[o-a]+=s*Math.pow(n,a)*l}}r[0]+=i;return r}function Fr(t){const e=t.length-1,n=[];let i,r,o,a,s;for(i=0;i<e;++i){a=i;for(r=i+1;r<e;++r){if(Math.abs(t[i][r])>Math.abs(t[i][a])){a=r}}for(o=i;o<e+1;++o){s=t[o][i];t[o][i]=t[o][a];t[o][a]=s}for(r=i+1;r<e;++r){for(o=e;o>=i;o--){t[o][r]-=t[o][i]*t[i][r]/t[i][i]}}}for(r=e-1;r>=0;--r){s=0;for(o=r+1;o<e;++o){s+=t[o][r]*n[o]}n[r]=(t[e][r]-s)/t[r][r]}return n}const Wr=2,Xr=1e-12;function Br(t,e,n,i){const[r,o,a,s]=Or(t,e,n,true),l=r.length,u=Math.max(2,~~(i*l)),f=new Float64Array(l),c=new Float64Array(l),d=new Float64Array(l).fill(1);for(let h=-1;++h<=Wr;){const t=[0,u-1];for(let n=0;n<l;++n){const e=r[n],i=t[0],a=t[1],s=e-r[i]>r[a]-e?i:a;let l=0,u=0,h=0,p=0,m=0;const g=1/Math.abs(r[s]-e||1);for(let t=i;t<=a;++t){const n=r[t],i=o[t],a=Zr(Math.abs(e-n)*g)*d[t],s=n*a;l+=a;u+=s;h+=i*a;p+=i*s;m+=n*s}const[y,v]=$r(u/l,h/l,p/l,m/l);f[n]=y+v*e;c[n]=Math.abs(o[n]-f[n]);Hr(r,n+1,t)}if(h===Wr){break}const e=Ki(c);if(Math.abs(e)<Xr)break;for(let n=0,i,r;n<l;++n){i=c[n]/(6*e);d[n]=i>=1?Xr:(r=1-i*i)*r}}return Jr(r,f,a,s)}function Zr(t){return(t=1-t*t*t)*t*t}function Hr(t,e,n){const i=t[e];let r=n[0],o=n[1]+1;if(o>=t.length)return;while(e>r&&t[o]-i<=i-t[r]){n[0]=++r;n[1]=o;++o}}function Jr(t,e,n,i){const r=t.length,o=[];let a=0,s=0,l=[],u;for(;a<r;++a){u=t[a]+n;if(l[0]===u){l[1]+=(e[a]-l[1])/++s}else{s=0;l[1]+=i;l=[u,e[a]];o.push(l)}}l[1]+=i;return o}const Gr=.5*Math.PI/180;function Kr(t,e,n,i){n=n||25;i=Math.max(n,i||200);const r=e=>[e,t(e)],o=e[0],a=e[1],s=a-o,l=s/i,u=[r(o)],f=[];if(n===i){for(let t=1;t<i;++t){u.push(r(o+t/n*s))}u.push(r(a));return u}else{f.push(r(a));for(let t=n;--t>0;){f.push(r(o+t/n*s))}}let c=u[0];let d=f[f.length-1];const h=1/s;const p=Vr(c[1],f);while(d){const t=r((c[0]+d[0])/2);const e=t[0]-c[0]>=l;if(e&&Yr(c,t,d,h,p)>Gr){f.push(t)}else{c=d;u.push(d);f.pop()}d=f[f.length-1]}return u}function Vr(t,e){let n=t;let i=t;const r=e.length;for(let o=0;o<r;++o){const t=e[o][1];if(t<n)n=t;if(t>i)i=t}return 1/(i-n)}function Yr(t,e,n,i,r){const o=Math.atan2(r*(n[1]-t[1]),i*(n[0]-t[0])),a=Math.atan2(r*(e[1]-t[1]),i*(e[0]-t[0]));return Math.abs(o-a)}function Qr(t,e){let n=0;let i=0;if(e===undefined){for(let e of t){if(e!=null&&(e=+e)>=e){++n,i+=e}}}else{let r=-1;for(let o of t){if((o=e(o,++r,t))!=null&&(o=+o)>=o){++n,i+=o}}}if(n)return i/n}var to=n(34893);function eo(t){return e=>{const n=t.length;let i=1,r=String(t[0](e));for(;i<n;++i){r+="|"+t[i](e)}return r}}function no(t){return!t||!t.length?function(){return""}:t.length===1?t[0]:eo(t)}function io(t,e,n){return n||t+(!e?"":"_"+e)}const ro=()=>{};const oo={init:ro,add:ro,rem:ro,idx:0};const ao={values:{init:t=>t.cell.store=true,value:t=>t.cell.data.values(),idx:-1},count:{value:t=>t.cell.num},__count__:{value:t=>t.missing+t.valid},missing:{value:t=>t.missing},valid:{value:t=>t.valid},sum:{init:t=>t.sum=0,value:t=>t.sum,add:(t,e)=>t.sum+=+e,rem:(t,e)=>t.sum-=e},product:{init:t=>t.product=1,value:t=>t.valid?t.product:undefined,add:(t,e)=>t.product*=e,rem:(t,e)=>t.product/=e},mean:{init:t=>t.mean=0,value:t=>t.valid?t.mean:undefined,add:(t,e)=>(t.mean_d=e-t.mean,t.mean+=t.mean_d/t.valid),rem:(t,e)=>(t.mean_d=e-t.mean,t.mean-=t.valid?t.mean_d/t.valid:t.mean)},average:{value:t=>t.valid?t.mean:undefined,req:["mean"],idx:1},variance:{init:t=>t.dev=0,value:t=>t.valid>1?t.dev/(t.valid-1):undefined,add:(t,e)=>t.dev+=t.mean_d*(e-t.mean),rem:(t,e)=>t.dev-=t.mean_d*(e-t.mean),req:["mean"],idx:1},variancep:{value:t=>t.valid>1?t.dev/t.valid:undefined,req:["variance"],idx:2},stdev:{value:t=>t.valid>1?Math.sqrt(t.dev/(t.valid-1)):undefined,req:["variance"],idx:2},stdevp:{value:t=>t.valid>1?Math.sqrt(t.dev/t.valid):undefined,req:["variance"],idx:2},stderr:{value:t=>t.valid>1?Math.sqrt(t.dev/(t.valid*(t.valid-1))):undefined,req:["variance"],idx:2},distinct:{value:t=>t.cell.data.distinct(t.get),req:["values"],idx:3},ci0:{value:t=>t.cell.data.ci0(t.get),req:["values"],idx:3},ci1:{value:t=>t.cell.data.ci1(t.get),req:["values"],idx:3},median:{value:t=>t.cell.data.q2(t.get),req:["values"],idx:3},q1:{value:t=>t.cell.data.q1(t.get),req:["values"],idx:3},q3:{value:t=>t.cell.data.q3(t.get),req:["values"],idx:3},min:{init:t=>t.min=undefined,value:t=>t.min=Number.isNaN(t.min)?t.cell.data.min(t.get):t.min,add:(t,e)=>{if(e<t.min||t.min===undefined)t.min=e},rem:(t,e)=>{if(e<=t.min)t.min=NaN},req:["values"],idx:4},max:{init:t=>t.max=undefined,value:t=>t.max=Number.isNaN(t.max)?t.cell.data.max(t.get):t.max,add:(t,e)=>{if(e>t.max||t.max===undefined)t.max=e},rem:(t,e)=>{if(e>=t.max)t.max=NaN},req:["values"],idx:4},argmin:{init:t=>t.argmin=undefined,value:t=>t.argmin||t.cell.data.argmin(t.get),add:(t,e,n)=>{if(e<t.min)t.argmin=n},rem:(t,e)=>{if(e<=t.min)t.argmin=undefined},req:["min","values"],idx:3},argmax:{init:t=>t.argmax=undefined,value:t=>t.argmax||t.cell.data.argmax(t.get),add:(t,e,n)=>{if(e>t.max)t.argmax=n},rem:(t,e)=>{if(e>=t.max)t.argmax=undefined},req:["max","values"],idx:3}};const so=Object.keys(ao).filter((t=>t!=="__count__"));function lo(t,e){return n=>(0,p.l7)({name:t,out:n||t},oo,e)}[...so,"__count__"].forEach((t=>{ao[t]=lo(t,ao[t])}));function uo(t,e){return ao[t](e)}function fo(t,e){return t.idx-e.idx}function co(t){const e={};t.forEach((t=>e[t.name]=t));const n=t=>{if(!t.req)return;t.req.forEach((t=>{if(!e[t])n(e[t]=ao[t]())}))};t.forEach(n);return Object.values(e).sort(fo)}function ho(){this.valid=0;this.missing=0;this._ops.forEach((t=>t.init(this)))}function po(t,e){if(t==null||t===""){++this.missing;return}if(t!==t)return;++this.valid;this._ops.forEach((n=>n.add(this,t,e)))}function mo(t,e){if(t==null||t===""){--this.missing;return}if(t!==t)return;--this.valid;this._ops.forEach((n=>n.rem(this,t,e)))}function go(t){this._out.forEach((e=>t[e.out]=e.value(this)));return t}function yo(t,e){const n=e||p.yR,i=co(t),r=t.slice().sort(fo);function o(t){this._ops=i;this._out=r;this.cell=t;this.init()}o.prototype.init=ho;o.prototype.add=po;o.prototype.rem=mo;o.prototype.set=go;o.prototype.get=n;o.fields=t.map((t=>t.out));return o}function vo(t){this._key=t?(0,p.EP)(t):yn;this.reset()}const bo=vo.prototype;bo.reset=function(){this._add=[];this._rem=[];this._ext=null;this._get=null;this._q=null};bo.add=function(t){this._add.push(t)};bo.rem=function(t){this._rem.push(t)};bo.values=function(){this._get=null;if(this._rem.length===0)return this._add;const t=this._add,e=this._rem,n=this._key,i=t.length,r=e.length,o=Array(i-r),a={};let s,l,u;for(s=0;s<r;++s){a[n(e[s])]=1}for(s=0,l=0;s<i;++s){if(a[n(u=t[s])]){a[n(u)]=0}else{o[l++]=u}}this._rem=[];return this._add=o};bo.distinct=function(t){const e=this.values(),n={};let i=e.length,r=0,o;while(--i>=0){o=t(e[i])+"";if(!(0,p.nr)(n,o)){n[o]=1;++r}}return r};bo.extent=function(t){if(this._get!==t||!this._ext){const e=this.values(),n=(0,p.dI)(e,t);this._ext=[e[n[0]],e[n[1]]];this._get=t}return this._ext};bo.argmin=function(t){return this.extent(t)[0]||{}};bo.argmax=function(t){return this.extent(t)[1]||{}};bo.min=function(t){const e=this.extent(t)[0];return e!=null?t(e):undefined};bo.max=function(t){const e=this.extent(t)[1];return e!=null?t(e):undefined};bo.quartile=function(t){if(this._get!==t||!this._q){this._q=tr(this.values(),t);this._get=t}return this._q};bo.q1=function(t){return this.quartile(t)[0]};bo.q2=function(t){return this.quartile(t)[1]};bo.q3=function(t){return this.quartile(t)[2]};bo.ci=function(t){if(this._get!==t||!this._ci){this._ci=or(this.values(),1e3,.05,t);this._get=t}return this._ci};bo.ci0=function(t){return this.ci(t)[0]};bo.ci1=function(t){return this.ci(t)[1]};function xo(t){Oi.call(this,null,t);this._adds=[];this._mods=[];this._alen=0;this._mlen=0;this._drop=true;this._cross=false;this._dims=[];this._dnames=[];this._measures=[];this._countOnly=false;this._counts=null;this._prev=null;this._inputs=null;this._outputs=null}xo.Definition={type:"Aggregate",metadata:{generates:true,changes:true},params:[{name:"groupby",type:"field",array:true},{name:"ops",type:"enum",array:true,values:so},{name:"fields",type:"field",null:true,array:true},{name:"as",type:"string",null:true,array:true},{name:"drop",type:"boolean",default:true},{name:"cross",type:"boolean",default:false},{name:"key",type:"field"}]};(0,p.XW)(xo,Oi,{transform(t,e){const n=this,i=e.fork(e.NO_SOURCE|e.NO_FIELDS),r=t.modified();n.stamp=i.stamp;if(n.value&&(r||e.modified(n._inputs,true))){n._prev=n.value;n.value=r?n.init(t):{};e.visit(e.SOURCE,(t=>n.add(t)))}else{n.value=n.value||n.init(t);e.visit(e.REM,(t=>n.rem(t)));e.visit(e.ADD,(t=>n.add(t)))}i.modifies(n._outputs);n._drop=t.drop!==false;if(t.cross&&n._dims.length>1){n._drop=false;n.cross()}if(e.clean()&&n._drop){i.clean(true).runAfter((()=>this.clean()))}return n.changes(i)},cross(){const t=this,e=t.value,n=t._dnames,i=n.map((()=>({}))),r=n.length;function o(t){let e,o,a,s;for(e in t){a=t[e].tuple;for(o=0;o<r;++o){i[o][s=a[n[o]]]=s}}}o(t._prev);o(e);function a(o,s,l){const u=n[l],f=i[l++];for(const n in f){const i=o?o+"|"+n:n;s[u]=f[n];if(l<r)a(i,s,l);else if(!e[i])t.cell(i,s)}}a("",{},0)},init(t){const e=this._inputs=[],n=this._outputs=[],i={};function r(t){const n=(0,p.IX)((0,p.Oj)(t)),r=n.length;let o=0,a;for(;o<r;++o){if(!i[a=n[o]]){i[a]=1;e.push(a)}}}this._dims=(0,p.IX)(t.groupby);this._dnames=this._dims.map((t=>{const e=(0,p.el)(t);r(t);n.push(e);return e}));this.cellkey=t.key?t.key:no(this._dims);this._countOnly=true;this._counts=[];this._measures=[];const o=t.fields||[null],a=t.ops||["count"],s=t.as||[],l=o.length,u={};let f,c,d,h,m,g;if(l!==a.length){(0,p.vU)("Unmatched number of fields and aggregate ops.")}for(g=0;g<l;++g){f=o[g];c=a[g];if(f==null&&c!=="count"){(0,p.vU)("Null aggregate field specified.")}h=(0,p.el)(f);m=io(c,h,s[g]);n.push(m);if(c==="count"){this._counts.push(m);continue}d=u[h];if(!d){r(f);d=u[h]=[];d.field=f;this._measures.push(d)}if(c!=="count")this._countOnly=false;d.push(uo(c,m))}this._measures=this._measures.map((t=>yo(t,t.field)));return{}},cellkey:no(),cell(t,e){let n=this.value[t];if(!n){n=this.value[t]=this.newcell(t,e);this._adds[this._alen++]=n}else if(n.num===0&&this._drop&&n.stamp<this.stamp){n.stamp=this.stamp;this._adds[this._alen++]=n}else if(n.stamp<this.stamp){n.stamp=this.stamp;this._mods[this._mlen++]=n}return n},newcell(t,e){const n={key:t,num:0,agg:null,tuple:this.newtuple(e,this._prev&&this._prev[t]),stamp:this.stamp,store:false};if(!this._countOnly){const t=this._measures,e=t.length;n.agg=Array(e);for(let i=0;i<e;++i){n.agg[i]=new t[i](n)}}if(n.store){n.data=new vo}return n},newtuple(t,e){const n=this._dnames,i=this._dims,r=i.length,o={};for(let a=0;a<r;++a){o[n[a]]=i[a](t)}return e?wn(e.tuple,o):bn(o)},clean(){const t=this.value;for(const e in t){if(t[e].num===0){delete t[e]}}},add(t){const e=this.cellkey(t),n=this.cell(e,t);n.num+=1;if(this._countOnly)return;if(n.store)n.data.add(t);const i=n.agg;for(let r=0,o=i.length;r<o;++r){i[r].add(i[r].get(t),t)}},rem(t){const e=this.cellkey(t),n=this.cell(e,t);n.num-=1;if(this._countOnly)return;if(n.store)n.data.rem(t);const i=n.agg;for(let r=0,o=i.length;r<o;++r){i[r].rem(i[r].get(t),t)}},celltuple(t){const e=t.tuple,n=this._counts;if(t.store){t.data.values()}for(let i=0,r=n.length;i<r;++i){e[n[i]]=t.num}if(!this._countOnly){const n=t.agg;for(let t=0,i=n.length;t<i;++t){n[t].set(e)}}return e},changes(t){const e=this._adds,n=this._mods,i=this._prev,r=this._drop,o=t.add,a=t.rem,s=t.mod;let l,u,f,c;if(i)for(u in i){l=i[u];if(!r||l.num)a.push(l.tuple)}for(f=0,c=this._alen;f<c;++f){o.push(this.celltuple(e[f]));e[f]=null}for(f=0,c=this._mlen;f<c;++f){l=n[f];(l.num===0&&r?a:s).push(this.celltuple(l));n[f]=null}this._alen=this._mlen=0;this._prev=null;return t}});const _o=1e-14;function wo(t){Oi.call(this,null,t)}wo.Definition={type:"Bin",metadata:{modifies:true},params:[{name:"field",type:"field",required:true},{name:"interval",type:"boolean",default:true},{name:"anchor",type:"number"},{name:"maxbins",type:"number",default:20},{name:"base",type:"number",default:10},{name:"divide",type:"number",array:true,default:[5,2]},{name:"extent",type:"number",array:true,length:2,required:true},{name:"span",type:"number"},{name:"step",type:"number"},{name:"steps",type:"number",array:true},{name:"minstep",type:"number",default:0},{name:"nice",type:"boolean",default:true},{name:"name",type:"string"},{name:"as",type:"string",array:true,length:2,default:["bin0","bin1"]}]};(0,p.XW)(wo,Oi,{transform(t,e){const n=t.interval!==false,i=this._bins(t),r=i.start,o=i.step,a=t.as||["bin0","bin1"],s=a[0],l=a[1];let u;if(t.modified()){e=e.reflow(true);u=e.SOURCE}else{u=e.modified((0,p.Oj)(t.field))?e.ADD_MOD:e.ADD}e.visit(u,n?t=>{const e=i(t);t[s]=e;t[l]=e==null?null:r+o*(1+(e-r)/o)}:t=>t[s]=i(t));return e.modifies(n?a:s)},_bins(t){if(this.value&&!t.modified()){return this.value}const e=t.field,n=nr(t),i=n.step;let r=n.start,o=r+Math.ceil((n.stop-r)/i)*i,a,s;if((a=t.anchor)!=null){s=a-(r+i*Math.floor((a-r)/i));r+=s;o+=s}const l=function(t){let n=(0,p.He)(e(t));return n==null?null:n<r?-Infinity:n>o?+Infinity:(n=Math.max(r,Math.min(n,o-i)),r+i*Math.floor(_o+(n-r)/i))};l.start=r;l.stop=n.stop;l.step=i;return this.value=(0,p.ZE)(l,(0,p.Oj)(e),t.name||"bin_"+(0,p.el)(e))}});function ko(t,e,n){const i=t;let r=e||[],o=n||[],a={},s=0;return{add:t=>o.push(t),remove:t=>a[i(t)]=++s,size:()=>r.length,data:(t,e)=>{if(s){r=r.filter((t=>!a[i(t)]));a={};s=0}if(e&&t){r.sort(t)}if(o.length){r=t?(0,p.TS)(t,r,o.sort(t)):r.concat(o);o=[]}return r}}}function Mo(t){Oi.call(this,[],t)}Mo.Definition={type:"Collect",metadata:{source:true},params:[{name:"sort",type:"compare"}]};(0,p.XW)(Mo,Oi,{transform(t,e){const n=e.fork(e.ALL),i=ko(yn,this.value,n.materialize(n.ADD).add),r=t.sort,o=e.changed()||r&&(t.modified("sort")||e.modified(r.fields));n.visit(n.REM,i.remove);this.modified(o);this.value=n.source=i.data(kn(r),o);if(e.source&&e.source.root){this.value.root=e.source.root}return n}});function Eo(t){Tn.call(this,null,So,t)}(0,p.XW)(Eo,Tn);function So(t){return this.value&&!t.modified()?this.value:(0,p.qu)(t.fields,t.orders)}function zo(t){Oi.call(this,null,t)}zo.Definition={type:"CountPattern",metadata:{generates:true,changes:true},params:[{name:"field",type:"field",required:true},{name:"case",type:"enum",values:["upper","lower","mixed"],default:"mixed"},{name:"pattern",type:"string",default:'[\\w"]+'},{name:"stopwords",type:"string",default:""},{name:"as",type:"string",array:true,length:2,default:["text","count"]}]};function Do(t,e,n){switch(e){case"upper":t=t.toUpperCase();break;case"lower":t=t.toLowerCase();break}return t.match(n)}(0,p.XW)(zo,Oi,{transform(t,e){const n=e=>n=>{var i=Do(s(n),t.case,o)||[],r;for(var l=0,u=i.length;l<u;++l){if(!a.test(r=i[l]))e(r)}};const i=this._parameterCheck(t,e),r=this._counts,o=this._match,a=this._stop,s=t.field,l=t.as||["text","count"],u=n((t=>r[t]=1+(r[t]||0))),f=n((t=>r[t]-=1));if(i){e.visit(e.SOURCE,u)}else{e.visit(e.ADD,u);e.visit(e.REM,f)}return this._finish(e,l)},_parameterCheck(t,e){let n=false;if(t.modified("stopwords")||!this._stop){this._stop=new RegExp("^"+(t.stopwords||"")+"$","i");n=true}if(t.modified("pattern")||!this._match){this._match=new RegExp(t.pattern||"[\\w']+","g");n=true}if(t.modified("field")||e.modified(t.field.fields)){n=true}if(n)this._counts={};return n},_finish(t,e){const n=this._counts,i=this._tuples||(this._tuples={}),r=e[0],o=e[1],a=t.fork(t.NO_SOURCE|t.NO_FIELDS);let s,l,u;for(s in n){l=i[s];u=n[s]||0;if(!l&&u){i[s]=l=bn({});l[r]=s;l[o]=u;a.add.push(l)}else if(u===0){if(l)a.rem.push(l);n[s]=null;i[s]=null}else if(l[o]!==u){l[o]=u;a.mod.push(l)}}return a.modifies(e)}});function Ro(t){Oi.call(this,null,t)}Ro.Definition={type:"Cross",metadata:{generates:true},params:[{name:"filter",type:"expr"},{name:"as",type:"string",array:true,length:2,default:["a","b"]}]};(0,p.XW)(Ro,Oi,{transform(t,e){const n=e.fork(e.NO_SOURCE),i=t.as||["a","b"],r=i[0],o=i[1],a=!this.value||e.changed(e.ADD_REM)||t.modified("as")||t.modified("filter");let s=this.value;if(a){if(s)n.rem=s;s=e.materialize(e.SOURCE).source;n.add=this.value=Ao(s,r,o,t.filter||p.yb)}else{n.mod=s}n.source=this.value;return n.modifies(i)}});function Ao(t,e,n,i){var r=[],o={},a=t.length,s=0,l,u;for(;s<a;++s){o[e]=u=t[s];for(l=0;l<a;++l){o[n]=t[l];if(i(o)){r.push(bn(o));o={};o[e]=u}}}return r}const $o={kde:br,mixture:Er,normal:vr,lognormal:Mr,uniform:Ar};const Oo="distributions",To="function",Co="field";function No(t,e){const n=t[To];if(!(0,p.nr)($o,n)){(0,p.vU)("Unknown distribution function: "+n)}const i=$o[n]();for(const r in t){if(r===Co){i.data((t.from||e()).map(t[r]))}else if(r===Oo){i[r](t[r].map((t=>No(t,e))))}else if(typeof i[r]===To){i[r](t[r])}}return i}function Uo(t){Oi.call(this,null,t)}const Io=[{key:{function:"normal"},params:[{name:"mean",type:"number",default:0},{name:"stdev",type:"number",default:1}]},{key:{function:"lognormal"},params:[{name:"mean",type:"number",default:0},{name:"stdev",type:"number",default:1}]},{key:{function:"uniform"},params:[{name:"min",type:"number",default:0},{name:"max",type:"number",default:1}]},{key:{function:"kde"},params:[{name:"field",type:"field",required:true},{name:"from",type:"data"},{name:"bandwidth",type:"number",default:0}]}];const Po={key:{function:"mixture"},params:[{name:"distributions",type:"param",array:true,params:Io},{name:"weights",type:"number",array:true}]};Uo.Definition={type:"Density",metadata:{generates:true},params:[{name:"extent",type:"number",array:true,length:2},{name:"steps",type:"number"},{name:"minsteps",type:"number",default:25},{name:"maxsteps",type:"number",default:200},{name:"method",type:"string",default:"pdf",values:["pdf","cdf"]},{name:"distribution",type:"param",params:Io.concat(Po)},{name:"as",type:"string",array:true,default:["value","density"]}]};(0,p.XW)(Uo,Oi,{transform(t,e){const n=e.fork(e.NO_SOURCE|e.NO_FIELDS);if(!this.value||e.changed()||t.modified()){const i=No(t.distribution,qo(e)),r=t.steps||t.minsteps||25,o=t.steps||t.maxsteps||200;let a=t.method||"pdf";if(a!=="pdf"&&a!=="cdf"){(0,p.vU)("Invalid density method: "+a)}if(!t.extent&&!i.data){(0,p.vU)("Missing density extent parameter.")}a=i[a];const s=t.as||["value","density"],l=t.extent||(0,p.We)(i.data()),u=Kr(a,l,r,o).map((t=>{const e={};e[s[0]]=t[0];e[s[1]]=t[1];return bn(e)}));if(this.value)n.rem=this.value;this.value=n.add=n.source=u}return n}});function qo(t){return()=>t.materialize(t.SOURCE).source}function Lo(t,e){if(!t)return null;return t.map(((t,n)=>e[n]||(0,p.el)(t)))}function jo(t,e,n){const i=[],r=t=>t(l);let o,a,s,l,u,f;if(e==null){i.push(t.map(n))}else{for(o={},a=0,s=t.length;a<s;++a){l=t[a];u=e.map(r);f=o[u];if(!f){o[u]=f=[];f.dims=u;i.push(f)}f.push(n(l))}}return i}const Fo="bin";function Wo(t){Oi.call(this,null,t)}Wo.Definition={type:"DotBin",metadata:{modifies:true},params:[{name:"field",type:"field",required:true},{name:"groupby",type:"field",array:true},{name:"step",type:"number"},{name:"smooth",type:"boolean",default:false},{name:"as",type:"string",default:Fo}]};const Xo=(t,e)=>(0,p.yP)((0,p.We)(t,e))/30;(0,p.XW)(Wo,Oi,{transform(t,e){if(this.value&&!(t.modified()||e.changed())){return e}const n=e.materialize(e.SOURCE).source,i=jo(e.source,t.groupby,p.yR),r=t.smooth||false,o=t.field,a=t.step||Xo(n,o),s=kn(((t,e)=>o(t)-o(e))),l=t.as||Fo,u=i.length;let f=Infinity,c=-Infinity,d=0,h;for(;d<u;++d){const t=i[d].sort(s);h=-1;for(const e of ar(t,a,r,o)){if(e<f)f=e;if(e>c)c=e;t[++h][l]=e}}this.value={start:f,stop:c,step:a};return e.reflow(true).modifies(l)}});function Bo(t){Tn.call(this,null,Zo,t);this.modified(true)}(0,p.XW)(Bo,Tn);function Zo(t){const e=t.expr;return this.value&&!t.modified("expr")?this.value:(0,p.ZE)((n=>e(n,t)),(0,p.Oj)(e),(0,p.el)(e))}function Ho(t){Oi.call(this,[undefined,undefined],t)}Ho.Definition={type:"Extent",metadata:{},params:[{name:"field",type:"field",required:true}]};(0,p.XW)(Ho,Oi,{transform(t,e){const n=this.value,i=t.field,r=e.changed()||e.modified(i.fields)||t.modified("field");let o=n[0],a=n[1];if(r||o==null){o=+Infinity;a=-Infinity}e.visit(r?e.SOURCE:e.ADD,(t=>{const e=(0,p.He)(i(t));if(e!=null){if(e<o)o=e;if(e>a)a=e}}));if(!Number.isFinite(o)||!Number.isFinite(a)){let t=(0,p.el)(i);if(t)t=` for field "${t}"`;e.dataflow.warn(`Infinite extent${t}: [${o}, ${a}]`);o=a=undefined}this.value=[o,a]}});function Jo(t,e){Tn.call(this,t);this.parent=e;this.count=0}(0,p.XW)(Jo,Tn,{connect(t){this.detachSubflow=t.detachSubflow;this.targets().add(t);return t.source=this},add(t){this.count+=1;this.value.add.push(t)},rem(t){this.count-=1;this.value.rem.push(t)},mod(t){this.value.mod.push(t)},init(t){this.value.init(t,t.NO_SOURCE)},evaluate(){return this.value}});function Go(t){Oi.call(this,{},t);this._keys=(0,p.Xr)();const e=this._targets=[];e.active=0;e.forEach=t=>{for(let n=0,i=e.active;n<i;++n){t(e[n],n,e)}}}(0,p.XW)(Go,Oi,{activate(t){this._targets[this._targets.active++]=t},subflow(t,e,n,i){const r=this.value;let o=(0,p.nr)(r,t)&&r[t],a,s;if(!o){s=i||(s=this._group[t])&&s.tuple;a=n.dataflow;o=new Jo(n.fork(n.NO_SOURCE),this);a.add(o).connect(e(a,t,s));r[t]=o;this.activate(o)}else if(o.value.stamp<n.stamp){o.init(n);this.activate(o)}return o},clean(){const t=this.value;let e=0;for(const n in t){if(t[n].count===0){const i=t[n].detachSubflow;if(i)i();delete t[n];++e}}if(e){const t=this._targets.filter((t=>t&&t.count>0));this.initTargets(t)}},initTargets(t){const e=this._targets,n=e.length,i=t?t.length:0;let r=0;for(;r<i;++r){e[r]=t[r]}for(;r<n&&e[r]!=null;++r){e[r]=null}e.active=i},transform(t,e){const n=e.dataflow,i=t.key,r=t.subflow,o=this._keys,a=t.modified("key"),s=t=>this.subflow(t,r,e);this._group=t.group||{};this.initTargets();e.visit(e.REM,(t=>{const e=yn(t),n=o.get(e);if(n!==undefined){o.delete(e);s(n).rem(t)}}));e.visit(e.ADD,(t=>{const e=i(t);o.set(yn(t),e);s(e).add(t)}));if(a||e.modified(i.fields)){e.visit(e.MOD,(t=>{const e=yn(t),n=o.get(e),r=i(t);if(n===r){s(r).mod(t)}else{o.set(e,r);s(n).rem(t);s(r).add(t)}}))}else if(e.changed(e.MOD)){e.visit(e.MOD,(t=>{s(o.get(yn(t))).mod(t)}))}if(a){e.visit(e.REFLOW,(t=>{const e=yn(t),n=o.get(e),r=i(t);if(n!==r){o.set(e,r);s(n).rem(t);s(r).add(t)}}))}if(e.clean()){n.runAfter((()=>{this.clean();o.clean()}))}else if(o.empty>n.cleanThreshold){n.runAfter(o.clean)}return e}});function Ko(t){Tn.call(this,null,Vo,t)}(0,p.XW)(Ko,Tn);function Vo(t){return this.value&&!t.modified()?this.value:(0,p.kJ)(t.name)?(0,p.IX)(t.name).map((t=>(0,p.EP)(t))):(0,p.EP)(t.name,t.as)}function Yo(t){Oi.call(this,(0,p.Xr)(),t)}Yo.Definition={type:"Filter",metadata:{changes:true},params:[{name:"expr",type:"expr",required:true}]};(0,p.XW)(Yo,Oi,{transform(t,e){const n=e.dataflow,i=this.value,r=e.fork(),o=r.add,a=r.rem,s=r.mod,l=t.expr;let u=true;e.visit(e.REM,(t=>{const e=yn(t);if(!i.has(e))a.push(t);else i.delete(e)}));e.visit(e.ADD,(e=>{if(l(e,t))o.push(e);else i.set(yn(e),1)}));function f(e){const n=yn(e),r=l(e,t),f=i.get(n);if(r&&f){i.delete(n);o.push(e)}else if(!r&&!f){i.set(n,1);a.push(e)}else if(u&&r&&!f){s.push(e)}}e.visit(e.MOD,f);if(t.modified()){u=false;e.visit(e.REFLOW,f)}if(i.empty>n.cleanThreshold)n.runAfter(i.clean);return r}});function Qo(t){Oi.call(this,[],t)}Qo.Definition={type:"Flatten",metadata:{generates:true},params:[{name:"fields",type:"field",array:true,required:true},{name:"index",type:"string"},{name:"as",type:"string",array:true}]};(0,p.XW)(Qo,Oi,{transform(t,e){const n=e.fork(e.NO_SOURCE),i=t.fields,r=Lo(i,t.as||[]),o=t.index||null,a=r.length;n.rem=this.value;e.visit(e.SOURCE,(t=>{const e=i.map((e=>e(t))),s=e.reduce(((t,e)=>Math.max(t,e.length)),0);let l=0,u,f,c;for(;l<s;++l){f=xn(t);for(u=0;u<a;++u){f[r[u]]=(c=e[u][l])==null?null:c}if(o){f[o]=l}n.add.push(f)}}));this.value=n.source=n.add;if(o)n.modifies(o);return n.modifies(r)}});function ta(t){Oi.call(this,[],t)}ta.Definition={type:"Fold",metadata:{generates:true},params:[{name:"fields",type:"field",array:true,required:true},{name:"as",type:"string",array:true,length:2,default:["key","value"]}]};(0,p.XW)(ta,Oi,{transform(t,e){const n=e.fork(e.NO_SOURCE),i=t.fields,r=i.map(p.el),o=t.as||["key","value"],a=o[0],s=o[1],l=i.length;n.rem=this.value;e.visit(e.SOURCE,(t=>{for(let e=0,o;e<l;++e){o=xn(t);o[a]=r[e];o[s]=i[e](t);n.add.push(o)}}));this.value=n.source=n.add;return n.modifies(o)}});function ea(t){Oi.call(this,null,t)}ea.Definition={type:"Formula",metadata:{modifies:true},params:[{name:"expr",type:"expr",required:true},{name:"as",type:"string",required:true},{name:"initonly",type:"boolean"}]};(0,p.XW)(ea,Oi,{transform(t,e){const n=t.expr,i=t.as,r=t.modified(),o=t.initonly?e.ADD:r?e.SOURCE:e.modified(n.fields)||e.modified(i)?e.ADD_MOD:e.ADD;if(r){e=e.materialize().reflow(true)}if(!t.initonly){e.modifies(i)}return e.visit(o,(e=>e[i]=n(e,t)))}});function na(t){Oi.call(this,[],t)}(0,p.XW)(na,Oi,{transform(t,e){const n=e.fork(e.ALL),i=t.generator;let r=this.value,o=t.size-r.length,a,s,l;if(o>0){for(a=[];--o>=0;){a.push(l=bn(i(t)));r.push(l)}n.add=n.add.length?n.materialize(n.ADD).add.concat(a):a}else{s=r.slice(0,-o);n.rem=n.rem.length?n.materialize(n.REM).rem.concat(s):s;r=r.slice(-o)}n.source=this.value=r;return n}});const ia={value:"value",median:Ki,mean:Qr,min:Pi.Z,max:Ii.Z};const ra=[];function oa(t){Oi.call(this,[],t)}oa.Definition={type:"Impute",metadata:{changes:true},params:[{name:"field",type:"field",required:true},{name:"key",type:"field",required:true},{name:"keyvals",array:true},{name:"groupby",type:"field",array:true},{name:"method",type:"enum",default:"value",values:["value","mean","median","max","min"]},{name:"value",default:0}]};function aa(t){var e=t.method||ia.value,n;if(ia[e]==null){(0,p.vU)("Unrecognized imputation method: "+e)}else if(e===ia.value){n=t.value!==undefined?t.value:0;return()=>n}else{return ia[e]}}function sa(t){const e=t.field;return t=>t?e(t):NaN}(0,p.XW)(oa,Oi,{transform(t,e){var n=e.fork(e.ALL),i=aa(t),r=sa(t),o=(0,p.el)(t.field),a=(0,p.el)(t.key),s=(t.groupby||[]).map(p.el),l=la(e.source,t.groupby,t.key,t.keyvals),u=[],f=this.value,c=l.domain.length,d,h,m,g,y,v,b,x,_,w;for(y=0,x=l.length;y<x;++y){d=l[y];m=d.values;h=NaN;for(b=0;b<c;++b){if(d[b]!=null)continue;g=l.domain[b];w={_impute:true};for(v=0,_=m.length;v<_;++v)w[s[v]]=m[v];w[a]=g;w[o]=Number.isNaN(h)?h=i(d,r):h;u.push(bn(w))}}if(u.length)n.add=n.materialize(n.ADD).add.concat(u);if(f.length)n.rem=n.materialize(n.REM).rem.concat(f);this.value=u;return n}});function la(t,e,n,i){var r=t=>t(g),o=[],a=i?i.slice():[],s={},l={},u,f,c,d,h,p,m,g;a.forEach(((t,e)=>s[t]=e+1));for(d=0,m=t.length;d<m;++d){g=t[d];p=n(g);h=s[p]||(s[p]=a.push(p));f=(u=e?e.map(r):ra)+"";if(!(c=l[f])){c=l[f]=[];o.push(c);c.values=u}c[h-1]=g}o.domain=a;return o}function ua(t){xo.call(this,t)}ua.Definition={type:"JoinAggregate",metadata:{modifies:true},params:[{name:"groupby",type:"field",array:true},{name:"fields",type:"field",null:true,array:true},{name:"ops",type:"enum",array:true,values:so},{name:"as",type:"string",null:true,array:true},{name:"key",type:"field"}]};(0,p.XW)(ua,xo,{transform(t,e){const n=this,i=t.modified();let r;if(n.value&&(i||e.modified(n._inputs,true))){r=n.value=i?n.init(t):{};e.visit(e.SOURCE,(t=>n.add(t)))}else{r=n.value=n.value||this.init(t);e.visit(e.REM,(t=>n.rem(t)));e.visit(e.ADD,(t=>n.add(t)))}n.changes();e.visit(e.SOURCE,(t=>{(0,p.l7)(t,r[n.cellkey(t)].tuple)}));return e.reflow(i).modifies(this._outputs)},changes(){const t=this._adds,e=this._mods;let n,i;for(n=0,i=this._alen;n<i;++n){this.celltuple(t[n]);t[n]=null}for(n=0,i=this._mlen;n<i;++n){this.celltuple(e[n]);e[n]=null}this._alen=this._mlen=0}});function fa(t){Oi.call(this,null,t)}fa.Definition={type:"KDE",metadata:{generates:true},params:[{name:"groupby",type:"field",array:true},{name:"field",type:"field",required:true},{name:"cumulative",type:"boolean",default:false},{name:"counts",type:"boolean",default:false},{name:"bandwidth",type:"number",default:0},{name:"extent",type:"number",array:true,length:2},{name:"resolve",type:"enum",values:["shared","independent"],default:"independent"},{name:"steps",type:"number"},{name:"minsteps",type:"number",default:25},{name:"maxsteps",type:"number",default:200},{name:"as",type:"string",array:true,default:["value","density"]}]};(0,p.XW)(fa,Oi,{transform(t,e){const n=e.fork(e.NO_SOURCE|e.NO_FIELDS);if(!this.value||e.changed()||t.modified()){const i=e.materialize(e.SOURCE).source,r=jo(i,t.groupby,t.field),o=(t.groupby||[]).map(p.el),a=t.bandwidth,s=t.cumulative?"cdf":"pdf",l=t.as||["value","density"],u=[];let f=t.extent,c=t.steps||t.minsteps||25,d=t.steps||t.maxsteps||200;if(s!=="pdf"&&s!=="cdf"){(0,p.vU)("Invalid density method: "+s)}if(t.resolve==="shared"){if(!f)f=(0,p.We)(i,t.field);c=d=t.steps||d}r.forEach((e=>{const n=br(e,a)[s],i=t.counts?e.length:1,r=f||(0,p.We)(e);Kr(n,r,c,d).forEach((t=>{const n={};for(let i=0;i<o.length;++i){n[o[i]]=e.dims[i]}n[l[0]]=t[0];n[l[1]]=t[1]*i;u.push(bn(n))}))}));if(this.value)n.rem=this.value;this.value=n.add=n.source=u}return n}});function ca(t){Tn.call(this,null,da,t)}(0,p.XW)(ca,Tn);function da(t){return this.value&&!t.modified()?this.value:(0,p.Jy)(t.fields,t.flat)}function ha(t){Oi.call(this,[],t);this._pending=null}(0,p.XW)(ha,Oi,{transform(t,e){const n=e.dataflow;if(this._pending){return ma(this,e,this._pending)}if(pa(t))return e.StopPropagation;if(t.values){return ma(this,e,n.parse(t.values,t.format))}else if(t.async){const e=n.request(t.url,t.format).then((t=>{this._pending=(0,p.IX)(t.data);return t=>t.touch(this)}));return{async:e}}else{return n.request(t.url,t.format).then((t=>ma(this,e,(0,p.IX)(t.data))))}}});function pa(t){return t.modified("async")&&!(t.modified("values")||t.modified("url")||t.modified("format"))}function ma(t,e,n){n.forEach(bn);const i=e.fork(e.NO_FIELDS&e.NO_SOURCE);i.rem=t.value;t.value=i.source=i.add=n;t._pending=null;if(i.rem.length)i.clean(true);return i}function ga(t){Oi.call(this,{},t)}ga.Definition={type:"Lookup",metadata:{modifies:true},params:[{name:"index",type:"index",params:[{name:"from",type:"data",required:true},{name:"key",type:"field",required:true}]},{name:"values",type:"field",array:true},{name:"fields",type:"field",array:true,required:true},{name:"as",type:"string",array:true},{name:"default",default:null}]};(0,p.XW)(ga,Oi,{transform(t,e){const n=t.fields,i=t.index,r=t.values,o=t.default==null?null:t.default,a=t.modified(),s=n.length;let l=a?e.SOURCE:e.ADD,u=e,f=t.as,c,d,h;if(r){d=r.length;if(s>1&&!f){(0,p.vU)('Multi-field lookup requires explicit "as" parameter.')}if(f&&f.length!==s*d){(0,p.vU)('The "as" parameter has too few output field names.')}f=f||r.map(p.el);c=function(t){for(var e=0,a=0,l,u;e<s;++e){u=i.get(n[e](t));if(u==null)for(l=0;l<d;++l,++a)t[f[a]]=o;else for(l=0;l<d;++l,++a)t[f[a]]=r[l](u)}}}else{if(!f){(0,p.vU)("Missing output field names.")}c=function(t){for(var e=0,r;e<s;++e){r=i.get(n[e](t));t[f[e]]=r==null?o:r}}}if(a){u=e.reflow(true)}else{h=n.some((t=>e.modified(t.fields)));l|=h?e.MOD:0}e.visit(l,c);return u.modifies(f)}});function ya(t){Tn.call(this,null,va,t)}(0,p.XW)(ya,Tn);function va(t){if(this.value&&!t.modified()){return this.value}const e=t.extents,n=e.length;let i=+Infinity,r=-Infinity,o,a;for(o=0;o<n;++o){a=e[o];if(a[0]<i)i=a[0];if(a[1]>r)r=a[1]}return[i,r]}function ba(t){Tn.call(this,null,xa,t)}(0,p.XW)(ba,Tn);function xa(t){return this.value&&!t.modified()?this.value:t.values.reduce(((t,e)=>t.concat(e)),[])}function _a(t){Oi.call(this,null,t)}(0,p.XW)(_a,Oi,{transform(t,e){this.modified(t.modified());this.value=t;return e.fork(e.NO_SOURCE|e.NO_FIELDS)}});function wa(t){xo.call(this,t)}wa.Definition={type:"Pivot",metadata:{generates:true,changes:true},params:[{name:"groupby",type:"field",array:true},{name:"field",type:"field",required:true},{name:"value",type:"field",required:true},{name:"op",type:"enum",values:so,default:"sum"},{name:"limit",type:"number",default:0},{name:"key",type:"field"}]};(0,p.XW)(wa,xo,{_transform:xo.prototype.transform,transform(t,e){return this._transform(ka(t,e),e)}});function ka(t,e){const n=t.field,i=t.value,r=(t.op==="count"?"__count__":t.op)||"sum",o=(0,p.Oj)(n).concat((0,p.Oj)(i)),a=Ea(n,t.limit||0,e);if(e.changed())t.set("__pivot__",null,null,true);return{key:t.key,groupby:t.groupby,ops:a.map((()=>r)),fields:a.map((t=>Ma(t,n,i,o))),as:a.map((t=>t+"")),modified:t.modified.bind(t)}}function Ma(t,e,n,i){return(0,p.ZE)((i=>e(i)===t?n(i):NaN),i,t+"")}function Ea(t,e,n){const i={},r=[];n.visit(n.SOURCE,(e=>{const n=t(e);if(!i[n]){i[n]=1;r.push(n)}}));r.sort(p.j2);return e?r.slice(0,e):r}function Sa(t){Go.call(this,t)}(0,p.XW)(Sa,Go,{transform(t,e){const n=t.subflow,i=t.field,r=t=>this.subflow(yn(t),n,e,t);if(t.modified("field")||i&&e.modified((0,p.Oj)(i))){(0,p.vU)("PreFacet does not support field modification.")}this.initTargets();if(i){e.visit(e.MOD,(t=>{const e=r(t);i(t).forEach((t=>e.mod(t)))}));e.visit(e.ADD,(t=>{const e=r(t);i(t).forEach((t=>e.add(bn(t))))}));e.visit(e.REM,(t=>{const e=r(t);i(t).forEach((t=>e.rem(t)))}))}else{e.visit(e.MOD,(t=>r(t).mod(t)));e.visit(e.ADD,(t=>r(t).add(t)));e.visit(e.REM,(t=>r(t).rem(t)))}if(e.clean()){e.runAfter((()=>this.clean()))}return e}});function za(t){Oi.call(this,null,t)}za.Definition={type:"Project",metadata:{generates:true,changes:true},params:[{name:"fields",type:"field",array:true},{name:"as",type:"string",null:true,array:true}]};(0,p.XW)(za,Oi,{transform(t,e){const n=e.fork(e.NO_SOURCE),i=t.fields,r=Lo(t.fields,t.as||[]),o=i?(t,e)=>Da(t,e,i,r):_n;let a;if(this.value){a=this.value}else{e=e.addAll();a=this.value={}}e.visit(e.REM,(t=>{const e=yn(t);n.rem.push(a[e]);a[e]=null}));e.visit(e.ADD,(t=>{const e=o(t,bn({}));a[yn(t)]=e;n.add.push(e)}));e.visit(e.MOD,(t=>{n.mod.push(o(t,a[yn(t)]))}));return n}});function Da(t,e,n,i){for(let r=0,o=n.length;r<o;++r){e[i[r]]=n[r](t)}return e}function Ra(t){Oi.call(this,null,t)}(0,p.XW)(Ra,Oi,{transform(t,e){this.value=t.value;return t.modified("value")?e.fork(e.NO_SOURCE|e.NO_FIELDS):e.StopPropagation}});function Aa(t){Oi.call(this,null,t)}Aa.Definition={type:"Quantile",metadata:{generates:true,changes:true},params:[{name:"groupby",type:"field",array:true},{name:"field",type:"field",required:true},{name:"probs",type:"number",array:true},{name:"step",type:"number",default:.01},{name:"as",type:"string",array:true,default:["prob","value"]}]};const $a=1e-14;(0,p.XW)(Aa,Oi,{transform(t,e){const n=e.fork(e.NO_SOURCE|e.NO_FIELDS),i=t.as||["prob","value"];if(this.value&&!t.modified()&&!e.changed()){n.source=this.value;return n}const r=e.materialize(e.SOURCE).source,o=jo(r,t.groupby,t.field),a=(t.groupby||[]).map(p.el),s=[],l=t.step||.01,u=t.probs||(0,to.Z)(l/2,1-$a,l),f=u.length;o.forEach((t=>{const e=Qi(t,u);for(let n=0;n<f;++n){const r={};for(let e=0;e<a.length;++e){r[a[e]]=t.dims[e]}r[i[0]]=u[n];r[i[1]]=e[n];s.push(bn(r))}}));if(this.value)n.rem=this.value;this.value=n.add=n.source=s;return n}});function Oa(t){Oi.call(this,null,t)}(0,p.XW)(Oa,Oi,{transform(t,e){let n,i;if(this.value){i=this.value}else{n=e=e.addAll();i=this.value={}}if(t.derive){n=e.fork(e.NO_SOURCE);e.visit(e.REM,(t=>{const e=yn(t);n.rem.push(i[e]);i[e]=null}));e.visit(e.ADD,(t=>{const e=xn(t);i[yn(t)]=e;n.add.push(e)}));e.visit(e.MOD,(t=>{const e=i[yn(t)];for(const i in t){e[i]=t[i];n.modifies(i)}n.mod.push(e)}))}return n}});function Ta(t){Oi.call(this,[],t);this.count=0}Ta.Definition={type:"Sample",metadata:{},params:[{name:"size",type:"number",default:1e3}]};(0,p.XW)(Ta,Oi,{transform(t,e){const n=e.fork(e.NO_SOURCE),i=t.modified("size"),r=t.size,o=this.value.reduce(((t,e)=>(t[yn(e)]=1,t)),{});let a=this.value,s=this.count,l=0;function u(t){let e,i;if(a.length<r){a.push(t)}else{i=~~((s+1)*ir());if(i<a.length&&i>=l){e=a[i];if(o[yn(e)])n.rem.push(e);a[i]=t}}++s}if(e.rem.length){e.visit(e.REM,(t=>{const e=yn(t);if(o[e]){o[e]=-1;n.rem.push(t)}--s}));a=a.filter((t=>o[yn(t)]!==-1))}if((e.rem.length||i)&&a.length<r&&e.source){l=s=a.length;e.visit(e.SOURCE,(t=>{if(!o[yn(t)])u(t)}));l=-1}if(i&&a.length>r){const t=a.length-r;for(let e=0;e<t;++e){o[yn(a[e])]=-1;n.rem.push(a[e])}a=a.slice(t)}if(e.mod.length){e.visit(e.MOD,(t=>{if(o[yn(t)])n.mod.push(t)}))}if(e.add.length){e.visit(e.ADD,u)}if(e.add.length||l<0){n.add=a.filter((t=>!o[yn(t)]))}this.count=s;this.value=n.source=a;return n}});function Ca(t){Oi.call(this,null,t)}Ca.Definition={type:"Sequence",metadata:{generates:true,changes:true},params:[{name:"start",type:"number",required:true},{name:"stop",type:"number",required:true},{name:"step",type:"number",default:1},{name:"as",type:"string",default:"data"}]};(0,p.XW)(Ca,Oi,{transform(t,e){if(this.value&&!t.modified())return;const n=e.materialize().fork(e.MOD),i=t.as||"data";n.rem=this.value?e.rem.concat(this.value):e.rem;this.value=(0,to.Z)(t.start,t.stop,t.step||1).map((t=>{const e={};e[i]=t;return bn(e)}));n.add=e.add.concat(this.value);return n}});function Na(t){Oi.call(this,null,t);this.modified(true)}(0,p.XW)(Na,Oi,{transform(t,e){this.value=e.source;return e.changed()?e.fork(e.NO_SOURCE|e.NO_FIELDS):e.StopPropagation}});function Ua(t){Oi.call(this,null,t)}const Ia=["unit0","unit1"];Ua.Definition={type:"TimeUnit",metadata:{modifies:true},params:[{name:"field",type:"field",required:true},{name:"interval",type:"boolean",default:true},{name:"units",type:"enum",values:ft,array:true},{name:"step",type:"number",default:1},{name:"maxbins",type:"number",default:40},{name:"extent",type:"date",array:true},{name:"timezone",type:"enum",default:"local",values:["local","utc"]},{name:"as",type:"string",array:true,length:2,default:Ia}]};(0,p.XW)(Ua,Oi,{transform(t,e){const n=t.field,i=t.interval!==false,r=t.timezone==="utc",o=this._floor(t,e),a=(r?jt:Lt)(o.unit).offset,s=t.as||Ia,l=s[0],u=s[1],f=o.step;let c=o.start||Infinity,d=o.stop||-Infinity,h=e.ADD;if(t.modified()||e.changed(e.REM)||e.modified((0,p.Oj)(n))){e=e.reflow(true);h=e.SOURCE;c=Infinity;d=-Infinity}e.visit(h,(t=>{const e=n(t);let r,s;if(e==null){t[l]=null;if(i)t[u]=null}else{t[l]=r=s=o(e);if(i)t[u]=s=a(r,f);if(r<c)c=r;if(s>d)d=s}}));o.start=c;o.stop=d;return e.modifies(i?s:l)},_floor(t,e){const n=t.timezone==="utc";const{units:i,step:r}=t.units?{units:t.units,step:t.step||1}:fe({extent:t.extent||(0,p.We)(e.materialize(e.SOURCE).source,t.field),maxbins:t.maxbins});const o=dt(i),a=this.value||{},s=(n?It:Ct)(o,r);s.unit=(0,p.fj)(o);s.units=o;s.step=r;s.start=a.start;s.stop=a.stop;return this.value=s}});function Pa(t){Oi.call(this,(0,p.Xr)(),t)}(0,p.XW)(Pa,Oi,{transform(t,e){const n=e.dataflow,i=t.field,r=this.value,o=t=>r.set(i(t),t);let a=true;if(t.modified("field")||e.modified(i.fields)){r.clear();e.visit(e.SOURCE,o)}else if(e.changed()){e.visit(e.REM,(t=>r.delete(i(t))));e.visit(e.ADD,o)}else{a=false}this.modified(a);if(r.empty>n.cleanThreshold)n.runAfter(r.clean);return e.fork()}});function qa(t){Oi.call(this,null,t)}(0,p.XW)(qa,Oi,{transform(t,e){const n=!this.value||t.modified("field")||t.modified("sort")||e.changed()||t.sort&&e.modified(t.sort.fields);if(n){this.value=(t.sort?e.source.slice().sort(kn(t.sort)):e.source).map(t.field)}}});function La(t,e,n,i){const r=ja[t](e,n);return{init:r.init||p.bM,update:function(t,e){e[i]=r.next(t)}}}const ja={row_number:function(){return{next:t=>t.index+1}},rank:function(){let t;return{init:()=>t=1,next:e=>{const n=e.index,i=e.data;return n&&e.compare(i[n-1],i[n])?t=n+1:t}}},dense_rank:function(){let t;return{init:()=>t=1,next:e=>{const n=e.index,i=e.data;return n&&e.compare(i[n-1],i[n])?++t:t}}},percent_rank:function(){const t=ja.rank(),e=t.next;return{init:t.init,next:t=>(e(t)-1)/(t.data.length-1)}},cume_dist:function(){let t;return{init:()=>t=0,next:e=>{const n=e.data,i=e.compare;let r=e.index;if(t<r){while(r+1<n.length&&!i(n[r],n[r+1]))++r;t=r}return(1+t)/n.length}}},ntile:function(t,e){e=+e;if(!(e>0))(0,p.vU)("ntile num must be greater than zero.");const n=ja.cume_dist(),i=n.next;return{init:n.init,next:t=>Math.ceil(e*i(t))}},lag:function(t,e){e=+e||1;return{next:n=>{const i=n.index-e;return i>=0?t(n.data[i]):null}}},lead:function(t,e){e=+e||1;return{next:n=>{const i=n.index+e,r=n.data;return i<r.length?t(r[i]):null}}},first_value:function(t){return{next:e=>t(e.data[e.i0])}},last_value:function(t){return{next:e=>t(e.data[e.i1-1])}},nth_value:function(t,e){e=+e;if(!(e>0))(0,p.vU)("nth_value nth must be greater than zero.");return{next:n=>{const i=n.i0+(e-1);return i<n.i1?t(n.data[i]):null}}},prev_value:function(t){let e;return{init:()=>e=null,next:n=>{const i=t(n.data[n.index]);return i!=null?e=i:e}}},next_value:function(t){let e,n;return{init:()=>(e=null,n=-1),next:i=>{const r=i.data;return i.index<=n?e:(n=Fa(t,r,i.index))<0?(n=r.length,e=null):e=t(r[n])}}}};function Fa(t,e,n){for(let i=e.length;n<i;++n){const i=t(e[n]);if(i!=null)return n}return-1}const Wa=Object.keys(ja);function Xa(t){const e=(0,p.IX)(t.ops),n=(0,p.IX)(t.fields),i=(0,p.IX)(t.params),r=(0,p.IX)(t.as),o=this.outputs=[],a=this.windows=[],s={},l={},u=[],f=[];let c=true;function d(t){(0,p.IX)((0,p.Oj)(t)).forEach((t=>s[t]=1))}d(t.sort);e.forEach(((t,e)=>{const s=n[e],h=(0,p.el)(s),m=io(t,h,r[e]);d(s);o.push(m);if((0,p.nr)(ja,t)){a.push(La(t,n[e],i[e],m))}else{if(s==null&&t!=="count"){(0,p.vU)("Null aggregate field specified.")}if(t==="count"){u.push(m);return}c=false;let e=l[h];if(!e){e=l[h]=[];e.field=s;f.push(e)}e.push(uo(t,m))}}));if(u.length||f.length){this.cell=Za(f,u,c)}this.inputs=Object.keys(s)}const Ba=Xa.prototype;Ba.init=function(){this.windows.forEach((t=>t.init()));if(this.cell)this.cell.init()};Ba.update=function(t,e){const n=this.cell,i=this.windows,r=t.data,o=i&&i.length;let a;if(n){for(a=t.p0;a<t.i0;++a)n.rem(r[a]);for(a=t.p1;a<t.i1;++a)n.add(r[a]);n.set(e)}for(a=0;a<o;++a)i[a].update(t,e)};function Za(t,e,n){t=t.map((t=>yo(t,t.field)));const i={num:0,agg:null,store:false,count:e};if(!n){var r=t.length,o=i.agg=Array(r),a=0;for(;a<r;++a)o[a]=new t[a](i)}if(i.store){var s=i.data=new vo}i.add=function(t){i.num+=1;if(n)return;if(s)s.add(t);for(let e=0;e<r;++e){o[e].add(o[e].get(t),t)}};i.rem=function(t){i.num-=1;if(n)return;if(s)s.rem(t);for(let e=0;e<r;++e){o[e].rem(o[e].get(t),t)}};i.set=function(t){let r,a;if(s)s.values();for(r=0,a=e.length;r<a;++r)t[e[r]]=i.num;if(!n)for(r=0,a=o.length;r<a;++r)o[r].set(t)};i.init=function(){i.num=0;if(s)s.reset();for(let t=0;t<r;++t)o[t].init()};return i}function Ha(t){Oi.call(this,{},t);this._mlen=0;this._mods=[]}Ha.Definition={type:"Window",metadata:{modifies:true},params:[{name:"sort",type:"compare"},{name:"groupby",type:"field",array:true},{name:"ops",type:"enum",array:true,values:Wa.concat(so)},{name:"params",type:"number",null:true,array:true},{name:"fields",type:"field",null:true,array:true},{name:"as",type:"string",null:true,array:true},{name:"frame",type:"number",null:true,array:true,length:2,default:[null,0]},{name:"ignorePeers",type:"boolean",default:false}]};(0,p.XW)(Ha,Oi,{transform(t,e){this.stamp=e.stamp;const n=t.modified(),i=kn(t.sort),r=no(t.groupby),o=t=>this.group(r(t));let a=this.state;if(!a||n){a=this.state=new Xa(t)}if(n||e.modified(a.inputs)){this.value={};e.visit(e.SOURCE,(t=>o(t).add(t)))}else{e.visit(e.REM,(t=>o(t).remove(t)));e.visit(e.ADD,(t=>o(t).add(t)))}for(let s=0,l=this._mlen;s<l;++s){Ja(this._mods[s],a,i,t)}this._mlen=0;this._mods=[];return e.reflow(n).modifies(a.outputs)},group(t){let e=this.value[t];if(!e){e=this.value[t]=ko(yn);e.stamp=-1}if(e.stamp<this.stamp){e.stamp=this.stamp;this._mods[this._mlen++]=e}return e}});function Ja(t,e,n,i){const r=i.sort,o=r&&!i.ignorePeers,a=i.frame||[null,0],s=t.data(n),l=s.length,u=o?(0,Y.Z)(r):null,f={i0:0,i1:0,p0:0,p1:0,index:0,data:s,compare:r||(0,p.a9)(-1)};e.init();for(let c=0;c<l;++c){Ga(f,a,c,l);if(o)Ka(f,u);e.update(f,s[c])}}function Ga(t,e,n,i){t.p0=t.i0;t.p1=t.i1;t.i0=e[0]==null?0:Math.max(0,n-Math.abs(e[0]));t.i1=e[1]==null?i:Math.min(i,n+Math.abs(e[1])+1);t.index=n}function Ka(t,e){const n=t.i0,i=t.i1-1,r=t.compare,o=t.data,a=o.length-1;if(n>0&&!r(o[n],o[n-1]))t.i0=e.left(o,o[n]);if(i<a&&!r(o[i],o[i+1]))t.i1=e.right(o,o[i])}var Va=n(67829);var Ya=n(79938);var Qa=n(99717);var ts=n(24849);var es=n(97018);var ns=n(55669);var is=n(85200);var rs=n(79039);var os=n(68286);var as=n(33506);var ss=n(18143);var ls=n(57481);var us=n(65165);var fs=n(56732);var cs=n(77059);var ds=n(10233);var hs=n(5742);var ps=n(93072);var ms=n(25049);var gs=n(99372);var ys=n(78260);function vs(t,e,n){var i=null,r=(0,ps.Z)(true),o=null,a=ss.Z,s=null,l=(0,gs.d)(u);t=typeof t==="function"?t:t===undefined?ys.x:(0,ps.Z)(+t);e=typeof e==="function"?e:e===undefined?(0,ps.Z)(0):(0,ps.Z)(+e);n=typeof n==="function"?n:n===undefined?ys.y:(0,ps.Z)(+n);function u(u){var f,c,d,h=(u=(0,hs.Z)(u)).length,p,m=false,g,y=new Array(h),v=new Array(h);if(o==null)s=a(g=l());for(f=0;f<=h;++f){if(!(f<h&&r(p=u[f],f,u))===m){if(m=!m){c=f;s.areaStart();s.lineStart()}else{s.lineEnd();s.lineStart();for(d=f-1;d>=c;--d){s.point(y[d],v[d])}s.lineEnd();s.areaEnd()}}if(m){y[f]=+t(p,f,u),v[f]=+e(p,f,u);s.point(i?+i(p,f,u):y[f],n?+n(p,f,u):v[f])}}if(g)return s=null,g+""||null}function f(){return(0,ms.Z)().defined(r).curve(a).context(o)}u.x=function(e){return arguments.length?(t=typeof e==="function"?e:(0,ps.Z)(+e),i=null,u):t};u.x0=function(e){return arguments.length?(t=typeof e==="function"?e:(0,ps.Z)(+e),u):t};u.x1=function(t){return arguments.length?(i=t==null?null:typeof t==="function"?t:(0,ps.Z)(+t),u):i};u.y=function(t){return arguments.length?(e=typeof t==="function"?t:(0,ps.Z)(+t),n=null,u):e};u.y0=function(t){return arguments.length?(e=typeof t==="function"?t:(0,ps.Z)(+t),u):e};u.y1=function(t){return arguments.length?(n=t==null?null:typeof t==="function"?t:(0,ps.Z)(+t),u):n};u.lineX0=u.lineY0=function(){return f().x(t).y(e)};u.lineY1=function(){return f().x(t).y(n)};u.lineX1=function(){return f().x(i).y(e)};u.defined=function(t){return arguments.length?(r=typeof t==="function"?t:(0,ps.Z)(!!t),u):r};u.curve=function(t){return arguments.length?(a=t,o!=null&&(s=a(o)),u):a};u.context=function(t){return arguments.length?(t==null?o=s=null:s=a(o=t),u):o};return u}var bs=n(44915);const xs=(0,bs._b)(3);const _s={draw(t,e){const n=(0,bs._b)(e+(0,bs.VV)(e/28,.75))*.59436;const i=n/2;const r=i*xs;t.moveTo(0,n);t.lineTo(0,-n);t.moveTo(-r,-i);t.lineTo(r,i);t.moveTo(-r,i);t.lineTo(r,-i)}};const ws={draw(t,e){const n=(0,bs._b)(e/bs.pi);t.moveTo(n,0);t.arc(0,0,n,0,bs.BZ)}};const ks={draw(t,e){const n=(0,bs._b)(e/5)/2;t.moveTo(-3*n,-n);t.lineTo(-n,-n);t.lineTo(-n,-3*n);t.lineTo(n,-3*n);t.lineTo(n,-n);t.lineTo(3*n,-n);t.lineTo(3*n,n);t.lineTo(n,n);t.lineTo(n,3*n);t.lineTo(-n,3*n);t.lineTo(-n,n);t.lineTo(-3*n,n);t.closePath()}};const Ms=(0,bs._b)(1/3);const Es=Ms*2;const Ss={draw(t,e){const n=(0,bs._b)(e/Es);const i=n*Ms;t.moveTo(0,-n);t.lineTo(i,0);t.lineTo(0,n);t.lineTo(-i,0);t.closePath()}};const zs={draw(t,e){const n=(0,bs._b)(e)*.62625;t.moveTo(0,-n);t.lineTo(n,0);t.lineTo(0,n);t.lineTo(-n,0);t.closePath()}};const Ds={draw(t,e){const n=(0,bs._b)(e-(0,bs.VV)(e/7,2))*.87559;t.moveTo(-n,0);t.lineTo(n,0);t.moveTo(0,n);t.lineTo(0,-n)}};const Rs={draw(t,e){const n=(0,bs._b)(e);const i=-n/2;t.rect(i,i,n,n)}};const As={draw(t,e){const n=(0,bs._b)(e)*.4431;t.moveTo(n,n);t.lineTo(n,-n);t.lineTo(-n,-n);t.lineTo(-n,n);t.closePath()}};const $s=.8908130915292852;const Os=(0,bs.O$)(bs.pi/10)/(0,bs.O$)(7*bs.pi/10);const Ts=(0,bs.O$)(bs.BZ/10)*Os;const Cs=-(0,bs.mC)(bs.BZ/10)*Os;const Ns={draw(t,e){const n=(0,bs._b)(e*$s);const i=Ts*n;const r=Cs*n;t.moveTo(0,-n);t.lineTo(i,r);for(let o=1;o<5;++o){const e=bs.BZ*o/5;const a=(0,bs.mC)(e);const s=(0,bs.O$)(e);t.lineTo(s*n,-a*n);t.lineTo(a*i-s*r,s*i+a*r)}t.closePath()}};const Us=(0,bs._b)(3);const Is={draw(t,e){const n=-(0,bs._b)(e/(Us*3));t.moveTo(0,n*2);t.lineTo(-Us*n,-n);t.lineTo(Us*n,-n);t.closePath()}};const Ps=(0,bs._b)(3);const qs={draw(t,e){const n=(0,bs._b)(e)*.6824;const i=n/2;const r=n*Ps/2;t.moveTo(0,-n);t.lineTo(r,i);t.lineTo(-r,i);t.closePath()}};const Ls=-.5;const js=(0,bs._b)(3)/2;const Fs=1/(0,bs._b)(12);const Ws=(Fs/2+1)*3;const Xs={draw(t,e){const n=(0,bs._b)(e/Ws);const i=n/2,r=n*Fs;const o=i,a=n*Fs+n;const s=-o,l=a;t.moveTo(i,r);t.lineTo(o,a);t.lineTo(s,l);t.lineTo(Ls*i-js*r,js*i+Ls*r);t.lineTo(Ls*o-js*a,js*o+Ls*a);t.lineTo(Ls*s-js*l,js*s+Ls*l);t.lineTo(Ls*i+js*r,Ls*r-js*i);t.lineTo(Ls*o+js*a,Ls*a-js*o);t.lineTo(Ls*s+js*l,Ls*l-js*s);t.closePath()}};const Bs={draw(t,e){const n=(0,bs._b)(e-(0,bs.VV)(e/6,1.7))*.6189;t.moveTo(-n,-n);t.lineTo(n,n);t.moveTo(-n,n);t.lineTo(n,-n)}};const Zs=[ws,ks,Ss,Rs,Ns,Is,Xs];const Hs=[ws,Ds,Bs,qs,_s,As,zs];function Js(t,e){let n=null,i=(0,gs.d)(r);t=typeof t==="function"?t:(0,ps.Z)(t||ws);e=typeof e==="function"?e:(0,ps.Z)(e===undefined?64:+e);function r(){let r;if(!n)n=r=i();t.apply(this,arguments).draw(n,+e.apply(this,arguments));if(r)return n=null,r+""||null}r.type=function(e){return arguments.length?(t=typeof e==="function"?e:(0,ps.Z)(e),r):t};r.size=function(t){return arguments.length?(e=typeof t==="function"?t:(0,ps.Z)(+t),r):e};r.context=function(t){return arguments.length?(n=t==null?null:t,r):n};return r}var Gs=n(11108);function Ks(t,e){if(typeof document!=="undefined"&&document.createElement){const n=document.createElement("canvas");if(n&&n.getContext){n.width=t;n.height=e;return n}}return null}const Vs=()=>typeof Image!=="undefined"?Image:null;var Ys=n(44355);var Qs=n(50289);var tl=n(34299);function el(t){var e;function n(t){return t==null||isNaN(t=+t)?e:t}n.invert=n;n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,tl.Z),n):t.slice()};n.unknown=function(t){return arguments.length?(e=t,n):e};n.copy=function(){return el(t).unknown(e)};t=arguments.length?Array.from(t,tl.Z):[0,1];return(0,Qs.Q)(n)}var nl=n(37604);var il=n(56290);var rl=n(48348);function ol(t){return Math.log(t)}function al(t){return Math.exp(t)}function sl(t){return-Math.log(-t)}function ll(t){return-Math.exp(-t)}function ul(t){return isFinite(t)?+("1e"+t):t<0?0:t}function fl(t){return t===10?ul:t===Math.E?Math.exp:e=>Math.pow(t,e)}function cl(t){return t===Math.E?Math.log:t===10&&Math.log10||t===2&&Math.log2||(t=Math.log(t),e=>Math.log(e)/t)}function dl(t){return(e,n)=>-t(-e,n)}function hl(t){const e=t(ol,al);const n=e.domain;let i=10;let r;let o;function a(){r=cl(i),o=fl(i);if(n()[0]<0){r=dl(r),o=dl(o);t(sl,ll)}else{t(ol,al)}return e}e.base=function(t){return arguments.length?(i=+t,a()):i};e.domain=function(t){return arguments.length?(n(t),a()):n()};e.ticks=t=>{const e=n();let a=e[0];let s=e[e.length-1];const l=s<a;if(l)[a,s]=[s,a];let u=r(a);let f=r(s);let c;let d;const h=t==null?10:+t;let p=[];if(!(i%1)&&f-u<h){u=Math.floor(u),f=Math.ceil(f);if(a>0)for(;u<=f;++u){for(c=1;c<i;++c){d=u<0?c/o(-u):c*o(u);if(d<a)continue;if(d>s)break;p.push(d)}}else for(;u<=f;++u){for(c=i-1;c>=1;--c){d=u>0?c/o(-u):c*o(u);if(d<a)continue;if(d>s)break;p.push(d)}}if(p.length*2<h)p=(0,I.ZP)(a,s,h)}else{p=(0,I.ZP)(u,f,Math.min(f-u,h)).map(o)}return l?p.reverse():p};e.tickFormat=(t,n)=>{if(t==null)t=10;if(n==null)n=i===10?"s":",";if(typeof n!=="function"){if(!(i%1)&&(n=(0,P.Z)(n)).precision==null)n.trim=true;n=(0,F.WU)(n)}if(t===Infinity)return n;const a=Math.max(1,i*t/e.ticks().length);return t=>{let e=t/o(Math.round(r(t)));if(e*i<i-.5)e*=i;return e<=a?n(t):""}};e.nice=()=>n((0,nl.Z)(n(),{floor:t=>o(Math.floor(r(t))),ceil:t=>o(Math.ceil(r(t)))}));return e}function pl(){const t=hl((0,il.l4)()).domain([1,10]);t.copy=()=>(0,il.JG)(t,pl()).base(t.base());rl.o.apply(t,arguments);return t}function ml(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function gl(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function yl(t){return t<0?-t*t:t*t}function vl(t){var e=t(il.yR,il.yR),n=1;function i(){return n===1?t(il.yR,il.yR):n===.5?t(gl,yl):t(ml(n),ml(1/n))}e.exponent=function(t){return arguments.length?(n=+t,i()):n};return(0,Qs.Q)(e)}function bl(){var t=vl((0,il.l4)());t.copy=function(){return(0,il.JG)(t,bl()).exponent(t.exponent())};rl.o.apply(t,arguments);return t}function xl(){return bl.apply(null,arguments).exponent(.5)}function _l(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function wl(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function kl(t){var e=1,n=t(_l(e),wl(e));n.constant=function(n){return arguments.length?t(_l(e=+n),wl(e)):e};return(0,Qs.Q)(n)}function Ml(){var t=kl((0,il.l4)());t.copy=function(){return(0,il.JG)(t,Ml()).constant(t.constant())};return rl.o.apply(t,arguments)}var El=n(88383);var Sl=n(41955);function zl(){return rl.o.apply((0,El.Y)(Sl.WG,Sl.jo,Z.ol,H.me,B.pI,X.AN,J.lM,G.rz,K.E,ce.g0).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}var Dl=n(81265);var Rl=n(55720);function Al(){var t=0,e=1,n,i,r,o,a=il.yR,s=false,l;function u(t){return t==null||isNaN(t=+t)?l:a(r===0?.5:(t=(o(t)-n)*r,s?Math.max(0,Math.min(1,t)):t))}u.domain=function(a){return arguments.length?([t,e]=a,n=o(t=+t),i=o(e=+e),r=n===i?0:1/(i-n),u):[t,e]};u.clamp=function(t){return arguments.length?(s=!!t,u):s};u.interpolator=function(t){return arguments.length?(a=t,u):a};function f(t){return function(e){var n,i;return arguments.length?([n,i]=e,a=t(n,i),u):[a(0),a(1)]}}u.range=f(Dl.Z);u.rangeRound=f(Rl.Z);u.unknown=function(t){return arguments.length?(l=t,u):l};return function(a){o=a,n=a(t),i=a(e),r=n===i?0:1/(i-n);return u}}function $l(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function Ol(){var t=(0,Qs.Q)(Al()(il.yR));t.copy=function(){return $l(t,Ol())};return rl.O.apply(t,arguments)}function Tl(){var t=hl(Al()).domain([1,10]);t.copy=function(){return $l(t,Tl()).base(t.base())};return rl.O.apply(t,arguments)}function Cl(){var t=kl(Al());t.copy=function(){return $l(t,Cl()).constant(t.constant())};return rl.O.apply(t,arguments)}function Nl(){var t=vl(Al());t.copy=function(){return $l(t,Nl()).exponent(t.exponent())};return rl.O.apply(t,arguments)}function Ul(){return Nl.apply(null,arguments).exponent(.5)}var Il=n(30108);function Pl(){var t=0,e=.5,n=1,i=1,r,o,a,s,l,u=il.yR,f,c=false,d;function h(t){return isNaN(t=+t)?d:(t=.5+((t=+f(t))-o)*(i*t<i*o?s:l),u(c?Math.max(0,Math.min(1,t)):t))}h.domain=function(u){return arguments.length?([t,e,n]=u,r=f(t=+t),o=f(e=+e),a=f(n=+n),s=r===o?0:.5/(o-r),l=o===a?0:.5/(a-o),i=o<r?-1:1,h):[t,e,n]};h.clamp=function(t){return arguments.length?(c=!!t,h):c};h.interpolator=function(t){return arguments.length?(u=t,h):u};function p(t){return function(e){var n,i,r;return arguments.length?([n,i,r]=e,u=(0,Il.Z)(t,[n,i,r]),h):[u(0),u(.5),u(1)]}}h.range=p(Dl.Z);h.rangeRound=p(Rl.Z);h.unknown=function(t){return arguments.length?(d=t,h):d};return function(u){f=u,r=u(t),o=u(e),a=u(n),s=r===o?0:.5/(o-r),l=o===a?0:.5/(a-o),i=o<r?-1:1;return h}}function ql(){var t=(0,Qs.Q)(Pl()(il.yR));t.copy=function(){return $l(t,ql())};return rl.O.apply(t,arguments)}function Ll(){var t=hl(Pl()).domain([.1,1,10]);t.copy=function(){return $l(t,Ll()).base(t.base())};return rl.O.apply(t,arguments)}function jl(){var t=kl(Pl());t.copy=function(){return $l(t,jl()).constant(t.constant())};return rl.O.apply(t,arguments)}function Fl(){var t=vl(Pl());t.copy=function(){return $l(t,Fl()).exponent(t.exponent())};return rl.O.apply(t,arguments)}function Wl(){return Fl.apply(null,arguments).exponent(.5)}function Xl(){var t=[],e=[],n=[],i;function r(){var i=0,r=Math.max(1,e.length);n=new Array(r-1);while(++i<r)n[i-1]=Zi(t,i/r);return o}function o(t){return t==null||isNaN(t=+t)?i:e[(0,Ys.ZP)(n,t)]}o.invertExtent=function(i){var r=e.indexOf(i);return r<0?[NaN,NaN]:[r>0?n[r-1]:t[0],r<n.length?n[r]:t[t.length-1]]};o.domain=function(e){if(!arguments.length)return t.slice();t=[];for(let n of e)if(n!=null&&!isNaN(n=+n))t.push(n);t.sort(Ui.Z);return r()};o.range=function(t){return arguments.length?(e=Array.from(t),r()):e.slice()};o.unknown=function(t){return arguments.length?(i=t,o):i};o.quantiles=function(){return n.slice()};o.copy=function(){return Xl().domain(t).range(e).unknown(i)};return rl.o.apply(o,arguments)}function Bl(){var t=0,e=1,n=1,i=[.5],r=[0,1],o;function a(t){return t!=null&&t<=t?r[(0,Ys.ZP)(i,t,0,n)]:o}function s(){var r=-1;i=new Array(n);while(++r<n)i[r]=((r+1)*e-(r-n)*t)/(n+1);return a}a.domain=function(n){return arguments.length?([t,e]=n,t=+t,e=+e,s()):[t,e]};a.range=function(t){return arguments.length?(n=(r=Array.from(t)).length-1,s()):r.slice()};a.invertExtent=function(o){var a=r.indexOf(o);return a<0?[NaN,NaN]:a<1?[t,i[0]]:a>=n?[i[n-1],e]:[i[a-1],i[a]]};a.unknown=function(t){return arguments.length?(o=t,a):a};a.thresholds=function(){return i.slice()};a.copy=function(){return Bl().domain([t,e]).range(r).unknown(o)};return rl.o.apply((0,Qs.Q)(a),arguments)}function Zl(){var t=[.5],e=[0,1],n,i=1;function r(r){return r!=null&&r<=r?e[(0,Ys.ZP)(t,r,0,i)]:n}r.domain=function(n){return arguments.length?(t=Array.from(n),i=Math.min(t.length,e.length-1),r):t.slice()};r.range=function(n){return arguments.length?(e=Array.from(n),i=Math.min(t.length,e.length-1),r):e.slice()};r.invertExtent=function(n){var i=e.indexOf(n);return[t[i-1],t[i]]};r.unknown=function(t){return arguments.length?(n=t,r):n};r.copy=function(){return Zl().domain(t).range(e).unknown(n)};return rl.o.apply(r,arguments)}var Hl=n(57603);var Jl=n(47197);var Gl=n(33566);function Kl(t,e,n){const i=t-e+n*2;return t?i>0?i:1:0}const Vl="identity";const Yl="linear";const Ql="log";const tu="pow";const eu="sqrt";const nu="symlog";const iu="time";const ru="utc";const ou="sequential";const au="diverging";const su="quantile";const lu="quantize";const uu="threshold";const fu="ordinal";const cu="point";const du="band";const hu="bin-ordinal";const pu="continuous";const mu="discrete";const gu="discretizing";const yu="interpolating";const vu="temporal";function bu(t){return function(e){let n=e[0],i=e[1],r;if(i<n){r=n;n=i;i=r}return[t.invert(n),t.invert(i)]}}function xu(t){return function(e){const n=t.range();let i=e[0],r=e[1],o=-1,a,s,l,u;if(r<i){s=i;i=r;r=s}for(l=0,u=n.length;l<u;++l){if(n[l]>=i&&n[l]<=r){if(o<0)o=l;a=l}}if(o<0)return undefined;i=t.invertExtent(n[o]);r=t.invertExtent(n[a]);return[i[0]===undefined?i[1]:i[0],r[1]===undefined?r[0]:r[1]]}}function _u(){const t=(0,Hl.Z)().unknown(undefined),e=t.domain,n=t.range;let i=[0,1],r,o,a=false,s=0,l=0,u=.5;delete t.unknown;function f(){const t=e().length,f=i[1]<i[0],c=i[1-f],d=Kl(t,s,l);let h=i[f-0];r=(c-h)/(d||1);if(a){r=Math.floor(r)}h+=(c-h-r*(t-s))*u;o=r*(1-s);if(a){h=Math.round(h);o=Math.round(o)}const p=(0,to.Z)(t).map((t=>h+r*t));return n(f?p.reverse():p)}t.domain=function(t){if(arguments.length){e(t);return f()}else{return e()}};t.range=function(t){if(arguments.length){i=[+t[0],+t[1]];return f()}else{return i.slice()}};t.rangeRound=function(t){i=[+t[0],+t[1]];a=true;return f()};t.bandwidth=function(){return o};t.step=function(){return r};t.round=function(t){if(arguments.length){a=!!t;return f()}else{return a}};t.padding=function(t){if(arguments.length){l=Math.max(0,Math.min(1,t));s=l;return f()}else{return s}};t.paddingInner=function(t){if(arguments.length){s=Math.max(0,Math.min(1,t));return f()}else{return s}};t.paddingOuter=function(t){if(arguments.length){l=Math.max(0,Math.min(1,t));return f()}else{return l}};t.align=function(t){if(arguments.length){u=Math.max(0,Math.min(1,t));return f()}else{return u}};t.invertRange=function(t){if(t[0]==null||t[1]==null)return;const r=i[1]<i[0],a=r?n().reverse():n(),s=a.length-1;let l=+t[0],u=+t[1],f,c,d;if(l!==l||u!==u)return;if(u<l){d=l;l=u;u=d}if(u<a[0]||l>i[1-r])return;f=Math.max(0,(0,Ys.ml)(a,l)-1);c=l===u?f:(0,Ys.ml)(a,u)-1;if(l-a[f]>o+1e-10)++f;if(r){d=f;f=s-c;c=s-d}return f>c?undefined:e().slice(f,c+1)};t.invert=function(e){const n=t.invertRange([e,e]);return n?n[0]:n};t.copy=function(){return _u().domain(e()).range(i).round(a).paddingInner(s).paddingOuter(l).align(u)};return f()}function wu(t){const e=t.copy;t.padding=t.paddingOuter;delete t.paddingInner;t.copy=function(){return wu(e())};return t}function ku(){return wu(_u().paddingInner(1))}var Mu=Array.prototype.map;function Eu(t){return Mu.call(t,p.He)}const Su=Array.prototype.slice;function zu(){let t=[],e=[];function n(n){return n==null||n!==n?undefined:e[((0,Ys.ZP)(t,n)-1)%e.length]}n.domain=function(e){if(arguments.length){t=Eu(e);return n}else{return t.slice()}};n.range=function(t){if(arguments.length){e=Su.call(t);return n}else{return e.slice()}};n.tickFormat=function(e,n){return(0,Jl.Z)(t[0],(0,p.fj)(t),e==null?10:e,n)};n.copy=function(){return zu().domain(n.domain()).range(n.range())};return n}const Du=new Map;const Ru=Symbol("vega_scale");function Au(t){t[Ru]=true;return t}function $u(t){return t&&t[Ru]===true}function Ou(t,e,n){const i=function n(){const i=e();if(!i.invertRange){i.invertRange=i.invert?bu(i):i.invertExtent?xu(i):undefined}i.type=t;return Au(i)};i.metadata=(0,p.Rg)((0,p.IX)(n));return i}function Tu(t,e,n){if(arguments.length>1){Du.set(t,Ou(t,e,n));return this}else{return Cu(t)?Du.get(t):undefined}}Tu(Vl,el);Tu(Yl,Qs.Z,pu);Tu(Ql,pl,[pu,Ql]);Tu(tu,bl,pu);Tu(eu,xl,pu);Tu(nu,Ml,pu);Tu(iu,El.Z,[pu,vu]);Tu(ru,zl,[pu,vu]);Tu(ou,Ol,[pu,yu]);Tu(`${ou}-${Yl}`,Ol,[pu,yu]);Tu(`${ou}-${Ql}`,Tl,[pu,yu,Ql]);Tu(`${ou}-${tu}`,Nl,[pu,yu]);Tu(`${ou}-${eu}`,Ul,[pu,yu]);Tu(`${ou}-${nu}`,Cl,[pu,yu]);Tu(`${au}-${Yl}`,ql,[pu,yu]);Tu(`${au}-${Ql}`,Ll,[pu,yu,Ql]);Tu(`${au}-${tu}`,Fl,[pu,yu]);Tu(`${au}-${eu}`,Wl,[pu,yu]);Tu(`${au}-${nu}`,jl,[pu,yu]);Tu(su,Xl,[gu,su]);Tu(lu,Bl,gu);Tu(uu,Zl,gu);Tu(hu,zu,[mu,gu]);Tu(fu,Hl.Z,mu);Tu(du,_u,mu);Tu(cu,ku,mu);function Cu(t){return Du.has(t)}function Nu(t,e){const n=Du.get(t);return n&&n.metadata[e]}function Uu(t){return Nu(t,pu)}function Iu(t){return Nu(t,mu)}function Pu(t){return Nu(t,gu)}function qu(t){return Nu(t,Ql)}function Lu(t){return Nu(t,vu)}function ju(t){return Nu(t,yu)}function Fu(t){return Nu(t,su)}const Wu=["clamp","base","constant","exponent"];function Xu(t,e){const n=e[0],i=(0,p.fj)(e)-n;return function(e){return t(n+e*i)}}function Bu(t,e,n){return Il.Z(Gu(e||"rgb",n),t)}function Zu(t,e){const n=new Array(e),i=e+1;for(let r=0;r<e;)n[r]=t(++r/i);return n}function Hu(t){const e=t.type,n=t.copy();n.type=e;return n}function Ju(t,e,n){const i=n-e;let r,o,a;if(!i||!Number.isFinite(i)){return(0,p.a9)(.5)}else{r=(o=t.type).indexOf("-");o=r<0?o:o.slice(r+1);a=Tu(o)().domain([e,n]).range([0,1]);Wu.forEach((e=>t[e]?a[e](t[e]()):0));return a}}function Gu(t,e){const n=Gl[Ku(t)];return e!=null&&n&&n.gamma?n.gamma(e):n}function Ku(t){return"interpolate"+t.toLowerCase().split("-").map((t=>t[0].toUpperCase()+t.slice(1))).join("")}const Vu={blues:"cfe1f2bed8eca8cee58fc1de74b2d75ba3cf4592c63181bd206fb2125ca40a4a90",greens:"d3eecdc0e6baabdda594d3917bc77d60ba6c46ab5e329a512089430e7735036429",greys:"e2e2e2d4d4d4c4c4c4b1b1b19d9d9d8888887575756262624d4d4d3535351e1e1e",oranges:"fdd8b3fdc998fdb87bfda55efc9244f87f2cf06b18e4580bd14904b93d029f3303",purples:"e2e1efd4d4e8c4c5e0b4b3d6a3a0cc928ec3827cb97566ae684ea25c3696501f8c",reds:"fdc9b4fcb49afc9e80fc8767fa7051f6573fec3f2fdc2a25c81b1db21218970b13",blueGreen:"d5efedc1e8e0a7ddd18bd2be70c6a958ba9144ad77319c5d2089460e7736036429",bluePurple:"ccddecbad0e4a8c2dd9ab0d4919cc98d85be8b6db28a55a6873c99822287730f71",greenBlue:"d3eecec5e8c3b1e1bb9bd8bb82cec269c2ca51b2cd3c9fc7288abd1675b10b60a1",orangeRed:"fddcaffdcf9bfdc18afdad77fb9562f67d53ee6545e24932d32d1ebf130da70403",purpleBlue:"dbdaebc8cee4b1c3de97b7d87bacd15b9fc93a90c01e7fb70b70ab056199045281",purpleBlueGreen:"dbd8eac8cee4b0c3de93b7d872acd1549fc83892bb1c88a3097f8702736b016353",purpleRed:"dcc9e2d3b3d7ce9eccd186c0da6bb2e14da0e23189d91e6fc61159ab07498f023a",redPurple:"fccfccfcbec0faa9b8f98faff571a5ec539ddb3695c41b8aa908808d0179700174",yellowGreen:"e4f4acd1eca0b9e2949ed68880c97c62bb6e47aa5e3297502083440e723b036034",yellowOrangeBrown:"feeaa1fedd84fecc63feb746fca031f68921eb7215db5e0bc54c05ab3d038f3204",yellowOrangeRed:"fee087fed16ffebd59fea849fd903efc7335f9522bee3423de1b20ca0b22af0225",blueOrange:"134b852f78b35da2cb9dcae1d2e5eff2f0ebfce0bafbbf74e8932fc5690d994a07",brownBlueGreen:"704108a0651ac79548e3c78af3e6c6eef1eac9e9e48ed1c74da79e187a72025147",purpleGreen:"5b1667834792a67fb6c9aed3e6d6e8eff0efd9efd5aedda971bb75368e490e5e29",purpleOrange:"4114696647968f83b7b9b4d6dadbebf3eeeafce0bafbbf74e8932fc5690d994a07",redBlue:"8c0d25bf363adf745ef4ae91fbdbc9f2efeed2e5ef9dcae15da2cb2f78b3134b85",redGrey:"8c0d25bf363adf745ef4ae91fcdccbfaf4f1e2e2e2c0c0c0969696646464343434",yellowGreenBlue:"eff9bddbf1b4bde5b594d5b969c5be45b4c22c9ec02182b82163aa23479c1c3185",redYellowBlue:"a50026d4322cf16e43fcac64fedd90faf8c1dcf1ecabd6e875abd04a74b4313695",redYellowGreen:"a50026d4322cf16e43fcac63fedd8df9f7aed7ee8ea4d86e64bc6122964f006837",pinkYellowGreen:"8e0152c0267edd72adf0b3d6faddedf5f3efe1f2cab6de8780bb474f9125276419",spectral:"9e0142d13c4bf0704afcac63fedd8dfbf8b0e0f3a1a9dda269bda94288b55e4fa2",viridis:"440154470e61481a6c482575472f7d443a834144873d4e8a39568c35608d31688e2d708e2a788e27818e23888e21918d1f988b1fa08822a8842ab07f35b77943bf7154c56866cc5d7ad1518fd744a5db36bcdf27d2e21be9e51afde725",magma:"0000040404130b0924150e3720114b2c11603b0f704a107957157e651a80721f817f24828c29819a2e80a8327db6377ac43c75d1426fde4968e95462f1605df76f5cfa7f5efc8f65fe9f6dfeaf78febf84fece91fddea0fcedaffcfdbf",inferno:"0000040403130c0826170c3b240c4f330a5f420a68500d6c5d126e6b176e781c6d86216b932667a12b62ae305cbb3755c73e4cd24644dd513ae65c30ed6925f3771af8850ffb9506fca50afcb519fac62df6d645f2e661f3f484fcffa4",plasma:"0d088723069033059742039d5002a25d01a66a00a87801a88405a7900da49c179ea72198b12a90ba3488c33d80cb4779d35171da5a69e16462e76e5bed7953f2834cf68f44fa9a3dfca636fdb32ffec029fcce25f9dc24f5ea27f0f921",cividis:"00205100235800265d002961012b65042e670831690d346b11366c16396d1c3c6e213f6e26426e2c456e31476e374a6e3c4d6e42506e47536d4c566d51586e555b6e5a5e6e5e616e62646f66676f6a6a706e6d717270717573727976737c79747f7c75827f758682768985778c8877908b78938e789691789a94789e9778a19b78a59e77a9a177aea575b2a874b6ab73bbaf71c0b26fc5b66dc9b96acebd68d3c065d8c462ddc85fe2cb5ce7cf58ebd355f0d652f3da4ff7de4cfae249fce647",rainbow:"6e40aa883eb1a43db3bf3cafd83fa4ee4395fe4b83ff576eff6659ff7847ff8c38f3a130e2b72fcfcc36bee044aff05b8ff4576ff65b52f6673af27828ea8d1ddfa319d0b81cbecb23abd82f96e03d82e14c6edb5a5dd0664dbf6e40aa",sinebow:"ff4040fc582af47218e78d0bd5a703bfbf00a7d5038de70b72f41858fc2a40ff402afc5818f4720be78d03d5a700bfbf03a7d50b8de71872f42a58fc4040ff582afc7218f48d0be7a703d5bf00bfd503a7e70b8df41872fc2a58ff4040",turbo:"23171b32204a3e2a71453493493eae4b49c54a53d7485ee44569ee4074f53c7ff8378af93295f72e9ff42ba9ef28b3e926bce125c5d925cdcf27d5c629dcbc2de3b232e9a738ee9d3ff39347f68950f9805afc7765fd6e70fe667cfd5e88fc5795fb51a1f84badf545b9f140c5ec3cd0e637dae034e4d931ecd12ef4c92bfac029ffb626ffad24ffa223ff9821ff8d1fff821dff771cfd6c1af76118f05616e84b14df4111d5380fcb2f0dc0260ab61f07ac1805a313029b0f00950c00910b00",browns:"eedbbdecca96e9b97ae4a865dc9856d18954c7784cc0673fb85536ad44339f3632",tealBlues:"bce4d89dd3d181c3cb65b3c245a2b9368fae347da0306a932c5985",teals:"bbdfdfa2d4d58ac9c975bcbb61b0af4da5a43799982b8b8c1e7f7f127273006667",warmGreys:"dcd4d0cec5c1c0b8b4b3aaa7a59c9998908c8b827f7e7673726866665c5a59504e",goldGreen:"f4d166d5ca60b6c35c98bb597cb25760a6564b9c533f8f4f33834a257740146c36",goldOrange:"f4d166f8be5cf8aa4cf5983bf3852aef701be2621fd65322c54923b142239e3a26",goldRed:"f4d166f6be59f9aa51fc964ef6834bee734ae56249db5247cf4244c43141b71d3e",lightGreyRed:"efe9e6e1dad7d5cbc8c8bdb9bbaea9cd967ddc7b43e15f19df4011dc000b",lightGreyTeal:"e4eaead6dcddc8ced2b7c2c7a6b4bc64b0bf22a6c32295c11f85be1876bc",lightMulti:"e0f1f2c4e9d0b0de9fd0e181f6e072f6c053f3993ef77440ef4a3c",lightOrange:"f2e7daf7d5baf9c499fab184fa9c73f68967ef7860e8645bde515bd43d5b",lightTealBlue:"e3e9e0c0dccf9aceca7abfc859afc0389fb9328dad2f7ca0276b95255988",darkBlue:"3232322d46681a5c930074af008cbf05a7ce25c0dd38daed50f3faffffff",darkGold:"3c3c3c584b37725e348c7631ae8b2bcfa424ecc31ef9de30fff184ffffff",darkGreen:"3a3a3a215748006f4d048942489e4276b340a6c63dd2d836ffeb2cffffaa",darkMulti:"3737371f5287197d8c29a86995ce3fffe800ffffff",darkRed:"3434347036339e3c38cc4037e75d1eec8620eeab29f0ce32ffeb2c"};const Yu={category10:"1f77b4ff7f0e2ca02cd627289467bd8c564be377c27f7f7fbcbd2217becf",category20:"1f77b4aec7e8ff7f0effbb782ca02c98df8ad62728ff98969467bdc5b0d58c564bc49c94e377c2f7b6d27f7f7fc7c7c7bcbd22dbdb8d17becf9edae5",category20b:"393b795254a36b6ecf9c9ede6379398ca252b5cf6bcedb9c8c6d31bd9e39e7ba52e7cb94843c39ad494ad6616be7969c7b4173a55194ce6dbdde9ed6",category20c:"3182bd6baed69ecae1c6dbefe6550dfd8d3cfdae6bfdd0a231a35474c476a1d99bc7e9c0756bb19e9ac8bcbddcdadaeb636363969696bdbdbdd9d9d9",tableau10:"4c78a8f58518e4575672b7b254a24beeca3bb279a2ff9da69d755dbab0ac",tableau20:"4c78a89ecae9f58518ffbf7954a24b88d27ab79a20f2cf5b43989483bcb6e45756ff9d9879706ebab0acd67195fcbfd2b279a2d6a5c99e765fd8b5a5",accent:"7fc97fbeaed4fdc086ffff99386cb0f0027fbf5b17666666",dark2:"1b9e77d95f027570b3e7298a66a61ee6ab02a6761d666666",paired:"a6cee31f78b4b2df8a33a02cfb9a99e31a1cfdbf6fff7f00cab2d66a3d9affff99b15928",pastel1:"fbb4aeb3cde3ccebc5decbe4fed9a6ffffcce5d8bdfddaecf2f2f2",pastel2:"b3e2cdfdcdaccbd5e8f4cae4e6f5c9fff2aef1e2cccccccc",set1:"e41a1c377eb84daf4a984ea3ff7f00ffff33a65628f781bf999999",set2:"66c2a5fc8d628da0cbe78ac3a6d854ffd92fe5c494b3b3b3",set3:"8dd3c7ffffb3bebadafb807280b1d3fdb462b3de69fccde5d9d9d9bc80bdccebc5ffed6f"};function Qu(t){const e=t.length/6|0,n=new Array(e);for(let i=0;i<e;){n[i]="#"+t.slice(i*6,++i*6)}return n}function tf(t,e){for(const n in t)nf(n,e(t[n]))}const ef={};tf(Yu,Qu);tf(Vu,(t=>Bu(Qu(t))));function nf(t,e){t=t&&t.toLowerCase();if(arguments.length>1){ef[t]=e;return this}else{return ef[t]}}const rf="symbol";const of="discrete";const af="gradient";const sf=t=>(0,p.kJ)(t)?t.map((t=>String(t))):String(t);const lf=(t,e)=>t[1]-e[1];const uf=(t,e)=>e[1]-t[1];function ff(t,e,n){let i;if((0,p.hj)(e)){if(t.bins){e=Math.max(e,t.bins.length)}if(n!=null){e=Math.min(e,Math.floor((0,p.yP)(t.domain())/n||1))}}if((0,p.Kn)(e)){i=e.step;e=e.interval}if((0,p.HD)(e)){e=t.type===iu?Lt(e):t.type==ru?jt(e):(0,p.vU)("Only time and utc scales accept interval strings.");if(i)e=e.every(i)}return e}function cf(t,e,n){let i=t.range(),r=i[0],o=(0,p.fj)(i),a=lf;if(r>o){i=o;o=r;r=i;a=uf}r=Math.floor(r);o=Math.ceil(o);e=e.map((e=>[e,t(e)])).filter((t=>r<=t[1]&&t[1]<=o)).sort(a).map((t=>t[0]));if(n>0&&e.length>1){const t=[e[0],(0,p.fj)(e)];while(e.length>n&&e.length>=3){e=e.filter(((t,e)=>!(e%2)))}if(e.length<3){e=t}}return e}function df(t,e){return t.bins?cf(t,t.bins):t.ticks?t.ticks(e):t.domain()}function hf(t,e,n,i,r,o){const a=e.type;let s=sf;if(a===iu||r===iu){s=t.timeFormat(i)}else if(a===ru||r===ru){s=t.utcFormat(i)}else if(qu(a)){const r=t.formatFloat(i);if(o||e.bins){s=r}else{const t=pf(e,n,false);s=e=>t(e)?r(e):""}}else if(e.tickFormat){const r=e.domain();s=t.formatSpan(r[0],r[r.length-1],n,i)}else if(i){s=t.format(i)}return s}function pf(t,e,n){const i=df(t,e),r=t.base(),o=Math.log(r),a=Math.max(1,r*e/i.length);const s=t=>{let e=t/Math.pow(r,Math.round(Math.log(t)/o));if(e*r<r-.5)e*=r;return e<=a};return n?i.filter(s):s}const mf={[su]:"quantiles",[lu]:"thresholds",[uu]:"domain"};const gf={[su]:"quantiles",[lu]:"domain"};function yf(t,e){return t.bins?xf(t.bins):t.type===Ql?pf(t,e,true):mf[t.type]?bf(t[mf[t.type]]()):df(t,e)}function vf(t,e,n){const i=e[gf[e.type]](),r=i.length;let o=r>1?i[1]-i[0]:i[0],a;for(a=1;a<r;++a){o=Math.min(o,i[a]-i[a-1])}return t.formatSpan(0,o,3*10,n)}function bf(t){const e=[-Infinity].concat(t);e.max=+Infinity;return e}function xf(t){const e=t.slice(0,-1);e.max=(0,p.fj)(t);return e}const _f=t=>mf[t.type]||t.bins;function wf(t,e,n,i,r,o,a){const s=gf[e.type]&&o!==iu&&o!==ru?vf(t,e,r):hf(t,e,n,r,o,a);return i===rf&&_f(e)?kf(s):i===of?Ef(s):Sf(s)}const kf=t=>(e,n,i)=>{const r=Mf(i[n+1],Mf(i.max,+Infinity)),o=zf(e,t),a=zf(r,t);return o&&a?o+" – "+a:a?"< "+a:"≥ "+o};const Mf=(t,e)=>t!=null?t:e;const Ef=t=>(e,n)=>n?t(e):null;const Sf=t=>e=>t(e);const zf=(t,e)=>Number.isFinite(t)?e(t):null;function Df(t){const e=t.domain(),n=e.length-1;let i=+e[0],r=+(0,p.fj)(e),o=r-i;if(t.type===uu){const t=n?o/n:.1;i-=t;r+=t;o=r-i}return t=>(t-i)/o}function Rf(t,e,n,i){const r=i||e.type;if((0,p.HD)(n)&&Lu(r)){n=n.replace(/%a/g,"%A").replace(/%b/g,"%B")}return!n&&r===iu?t.timeFormat("%A, %d %B %Y, %X"):!n&&r===ru?t.utcFormat("%A, %d %B %Y, %X UTC"):wf(t,e,5,null,n,i,true)}function Af(t,e,n){n=n||{};const i=Math.max(3,n.maxlen||7),r=Rf(t,e,n.format,n.formatType);if(Pu(e.type)){const t=yf(e).slice(1).map(r),n=t.length;return`${n} boundar${n===1?"y":"ies"}: ${t.join(", ")}`}else if(Iu(e.type)){const t=e.domain(),n=t.length,o=n>i?t.slice(0,i-2).map(r).join(", ")+", ending with "+t.slice(-1).map(r):t.map(r).join(", ");return`${n} value${n===1?"":"s"}: ${o}`}else{const t=e.domain();return`values from ${r(t[0])} to ${r((0,p.fj)(t))}`}}let $f=0;function Of(){$f=0}const Tf="p_";function Cf(t){return t&&t.gradient}function Nf(t,e,n){const i=t.gradient;let r=t.id,o=i==="radial"?Tf:"";if(!r){r=t.id="gradient_"+$f++;if(i==="radial"){t.x1=Uf(t.x1,.5);t.y1=Uf(t.y1,.5);t.r1=Uf(t.r1,0);t.x2=Uf(t.x2,.5);t.y2=Uf(t.y2,.5);t.r2=Uf(t.r2,.5);o=Tf}else{t.x1=Uf(t.x1,0);t.y1=Uf(t.y1,0);t.x2=Uf(t.x2,1);t.y2=Uf(t.y2,0)}}e[r]=t;return"url("+(n||"")+"#"+o+r+")"}function Uf(t,e){return t!=null?t:e}function If(t,e){var n=[],i;return i={gradient:"linear",x1:t?t[0]:0,y1:t?t[1]:0,x2:e?e[0]:1,y2:e?e[1]:0,stops:n,stop:function(t,e){n.push({offset:t,color:e});return i}}}const Pf={basis:{curve:Va.ZP},"basis-closed":{curve:Ya.Z},"basis-open":{curve:Qa.Z},bundle:{curve:ts.Z,tension:"beta",value:.85},cardinal:{curve:es.ZP,tension:"tension",value:0},"cardinal-open":{curve:ns.Z,tension:"tension",value:0},"cardinal-closed":{curve:is.Z,tension:"tension",value:0},"catmull-rom":{curve:rs.Z,tension:"alpha",value:.5},"catmull-rom-closed":{curve:os.Z,tension:"alpha",value:.5},"catmull-rom-open":{curve:as.Z,tension:"alpha",value:.5},linear:{curve:ss.Z},"linear-closed":{curve:ls.Z},monotone:{horizontal:us.s,vertical:us.Z},natural:{curve:fs.Z},step:{curve:cs.ZP},"step-after":{curve:cs.cD},"step-before":{curve:cs.RN}};function qf(t,e,n){var i=(0,p.nr)(Pf,t)&&Pf[t],r=null;if(i){r=i.curve||i[e||"vertical"];if(i.tension&&n!=null){r=r[i.tension](n)}}return r}const Lf={m:2,l:2,h:1,v:1,z:0,c:6,s:4,q:4,t:2,a:7};const jf=/[mlhvzcsqta]([^mlhvzcsqta]+|$)/gi;const Ff=/^[+-]?(([0-9]*\.[0-9]+)|([0-9]+\.)|([0-9]+))([eE][+-]?[0-9]+)?/;const Wf=/^((\s+,?\s*)|(,\s*))/;const Xf=/^[01]/;function Bf(t){const e=[];const n=t.match(jf)||[];n.forEach((t=>{let n=t[0];const i=n.toLowerCase();const r=Lf[i];const o=Zf(i,r,t.slice(1).trim());const a=o.length;if(a<r||a&&a%r!==0){throw Error("Invalid SVG path, incorrect parameter count")}e.push([n,...o.slice(0,r)]);if(a===r){return}if(i==="m"){n=n==="M"?"L":"l"}for(let s=r;s<a;s+=r){e.push([n,...o.slice(s,s+r)])}}));return e}function Zf(t,e,n){const i=[];for(let r=0;e&&r<n.length;){for(let o=0;o<e;++o){const e=t==="a"&&(o===3||o===4)?Xf:Ff;const a=n.slice(r).match(e);if(a===null){throw Error("Invalid SVG path, incorrect parameter type")}r+=a[0].length;i.push(+a[0]);const s=n.slice(r).match(Wf);if(s!==null){r+=s[0].length}}}return i}const Hf=Math.PI/180;const Jf=1e-14;const Gf=Math.PI/2;const Kf=Math.PI*2;const Vf=Math.sqrt(3)/2;var Yf={};var Qf={};var tc=[].join;function ec(t,e,n,i,r,o,a,s,l){const u=tc.call(arguments);if(Yf[u]){return Yf[u]}const f=a*Hf;const c=Math.sin(f);const d=Math.cos(f);n=Math.abs(n);i=Math.abs(i);const h=d*(s-t)*.5+c*(l-e)*.5;const p=d*(l-e)*.5-c*(s-t)*.5;let m=h*h/(n*n)+p*p/(i*i);if(m>1){m=Math.sqrt(m);n*=m;i*=m}const g=d/n;const y=c/n;const v=-c/i;const b=d/i;const x=g*s+y*l;const _=v*s+b*l;const w=g*t+y*e;const k=v*t+b*e;const M=(w-x)*(w-x)+(k-_)*(k-_);let E=1/M-.25;if(E<0)E=0;let S=Math.sqrt(E);if(o==r)S=-S;const z=.5*(x+w)-S*(k-_);const D=.5*(_+k)+S*(w-x);const R=Math.atan2(_-D,x-z);const A=Math.atan2(k-D,w-z);let $=A-R;if($<0&&o===1){$+=Kf}else if($>0&&o===0){$-=Kf}const O=Math.ceil(Math.abs($/(Gf+.001)));const T=[];for(let C=0;C<O;++C){const t=R+C*$/O;const e=R+(C+1)*$/O;T[C]=[z,D,t,e,n,i,c,d]}return Yf[u]=T}function nc(t){const e=tc.call(t);if(Qf[e]){return Qf[e]}var n=t[0],i=t[1],r=t[2],o=t[3],a=t[4],s=t[5],l=t[6],u=t[7];const f=u*a;const c=-l*s;const d=l*a;const h=u*s;const p=Math.cos(r);const m=Math.sin(r);const g=Math.cos(o);const y=Math.sin(o);const v=.5*(o-r);const b=Math.sin(v*.5);const x=8/3*b*b/Math.sin(v);const _=n+p-x*m;const w=i+m+x*p;const k=n+g;const M=i+y;const E=k+x*y;const S=M-x*g;return Qf[e]=[f*_+c*w,d*_+h*w,f*E+c*S,d*E+h*S,f*k+c*M,d*k+h*M]}const ic=["l",0,0,0,0,0,0,0];function rc(t,e,n){const i=ic[0]=t[0];if(i==="a"||i==="A"){ic[1]=e*t[1];ic[2]=n*t[2];ic[3]=t[3];ic[4]=t[4];ic[5]=t[5];ic[6]=e*t[6];ic[7]=n*t[7]}else if(i==="h"||i==="H"){ic[1]=e*t[1]}else if(i==="v"||i==="V"){ic[1]=n*t[1]}else{for(var r=1,o=t.length;r<o;++r){ic[r]=(r%2==1?e:n)*t[r]}}return ic}function oc(t,e,n,i,r,o){var a,s=null,l=0,u=0,f=0,c=0,d,h,p,m,g=0,y=0;if(n==null)n=0;if(i==null)i=0;if(r==null)r=1;if(o==null)o=r;if(t.beginPath)t.beginPath();for(var v=0,b=e.length;v<b;++v){a=e[v];if(r!==1||o!==1){a=rc(a,r,o)}switch(a[0]){case"l":l+=a[1];u+=a[2];t.lineTo(l+n,u+i);break;case"L":l=a[1];u=a[2];t.lineTo(l+n,u+i);break;case"h":l+=a[1];t.lineTo(l+n,u+i);break;case"H":l=a[1];t.lineTo(l+n,u+i);break;case"v":u+=a[1];t.lineTo(l+n,u+i);break;case"V":u=a[1];t.lineTo(l+n,u+i);break;case"m":l+=a[1];u+=a[2];g=l;y=u;t.moveTo(l+n,u+i);break;case"M":l=a[1];u=a[2];g=l;y=u;t.moveTo(l+n,u+i);break;case"c":d=l+a[5];h=u+a[6];f=l+a[3];c=u+a[4];t.bezierCurveTo(l+a[1]+n,u+a[2]+i,f+n,c+i,d+n,h+i);l=d;u=h;break;case"C":l=a[5];u=a[6];f=a[3];c=a[4];t.bezierCurveTo(a[1]+n,a[2]+i,f+n,c+i,l+n,u+i);break;case"s":d=l+a[3];h=u+a[4];f=2*l-f;c=2*u-c;t.bezierCurveTo(f+n,c+i,l+a[1]+n,u+a[2]+i,d+n,h+i);f=l+a[1];c=u+a[2];l=d;u=h;break;case"S":d=a[3];h=a[4];f=2*l-f;c=2*u-c;t.bezierCurveTo(f+n,c+i,a[1]+n,a[2]+i,d+n,h+i);l=d;u=h;f=a[1];c=a[2];break;case"q":d=l+a[3];h=u+a[4];f=l+a[1];c=u+a[2];t.quadraticCurveTo(f+n,c+i,d+n,h+i);l=d;u=h;break;case"Q":d=a[3];h=a[4];t.quadraticCurveTo(a[1]+n,a[2]+i,d+n,h+i);l=d;u=h;f=a[1];c=a[2];break;case"t":d=l+a[1];h=u+a[2];if(s[0].match(/[QqTt]/)===null){f=l;c=u}else if(s[0]==="t"){f=2*l-p;c=2*u-m}else if(s[0]==="q"){f=2*l-f;c=2*u-c}p=f;m=c;t.quadraticCurveTo(f+n,c+i,d+n,h+i);l=d;u=h;f=l+a[1];c=u+a[2];break;case"T":d=a[1];h=a[2];f=2*l-f;c=2*u-c;t.quadraticCurveTo(f+n,c+i,d+n,h+i);l=d;u=h;break;case"a":ac(t,l+n,u+i,[a[1],a[2],a[3],a[4],a[5],a[6]+l+n,a[7]+u+i]);l+=a[6];u+=a[7];break;case"A":ac(t,l+n,u+i,[a[1],a[2],a[3],a[4],a[5],a[6]+n,a[7]+i]);l=a[6];u=a[7];break;case"z":case"Z":l=g;u=y;t.closePath();break}s=a}}function ac(t,e,n,i){const r=ec(i[5],i[6],i[0],i[1],i[3],i[4],i[2],e,n);for(let o=0;o<r.length;++o){const e=nc(r[o]);t.bezierCurveTo(e[0],e[1],e[2],e[3],e[4],e[5])}}const sc=.5773502691896257;const lc={circle:{draw:function(t,e){const n=Math.sqrt(e)/2;t.moveTo(n,0);t.arc(0,0,n,0,Kf)}},cross:{draw:function(t,e){var n=Math.sqrt(e)/2,i=n/2.5;t.moveTo(-n,-i);t.lineTo(-n,i);t.lineTo(-i,i);t.lineTo(-i,n);t.lineTo(i,n);t.lineTo(i,i);t.lineTo(n,i);t.lineTo(n,-i);t.lineTo(i,-i);t.lineTo(i,-n);t.lineTo(-i,-n);t.lineTo(-i,-i);t.closePath()}},diamond:{draw:function(t,e){const n=Math.sqrt(e)/2;t.moveTo(-n,0);t.lineTo(0,-n);t.lineTo(n,0);t.lineTo(0,n);t.closePath()}},square:{draw:function(t,e){var n=Math.sqrt(e),i=-n/2;t.rect(i,i,n,n)}},arrow:{draw:function(t,e){var n=Math.sqrt(e)/2,i=n/7,r=n/2.5,o=n/8;t.moveTo(-i,n);t.lineTo(i,n);t.lineTo(i,-o);t.lineTo(r,-o);t.lineTo(0,-n);t.lineTo(-r,-o);t.lineTo(-i,-o);t.closePath()}},wedge:{draw:function(t,e){var n=Math.sqrt(e)/2,i=Vf*n,r=i-n*sc,o=n/4;t.moveTo(0,-i-r);t.lineTo(-o,i-r);t.lineTo(o,i-r);t.closePath()}},triangle:{draw:function(t,e){var n=Math.sqrt(e)/2,i=Vf*n,r=i-n*sc;t.moveTo(0,-i-r);t.lineTo(-n,i-r);t.lineTo(n,i-r);t.closePath()}},"triangle-up":{draw:function(t,e){var n=Math.sqrt(e)/2,i=Vf*n;t.moveTo(0,-i);t.lineTo(-n,i);t.lineTo(n,i);t.closePath()}},"triangle-down":{draw:function(t,e){var n=Math.sqrt(e)/2,i=Vf*n;t.moveTo(0,i);t.lineTo(-n,-i);t.lineTo(n,-i);t.closePath()}},"triangle-right":{draw:function(t,e){var n=Math.sqrt(e)/2,i=Vf*n;t.moveTo(i,0);t.lineTo(-i,-n);t.lineTo(-i,n);t.closePath()}},"triangle-left":{draw:function(t,e){var n=Math.sqrt(e)/2,i=Vf*n;t.moveTo(-i,0);t.lineTo(i,-n);t.lineTo(i,n);t.closePath()}},stroke:{draw:function(t,e){const n=Math.sqrt(e)/2;t.moveTo(-n,0);t.lineTo(n,0)}}};function uc(t){return(0,p.nr)(lc,t)?lc[t]:cc(t)}var fc={};function cc(t){if(!(0,p.nr)(fc,t)){const e=Bf(t);fc[t]={draw:function(t,n){oc(t,e,0,0,Math.sqrt(n)/2)}}}return fc[t]}const dc=.448084975506;function hc(t){return t.x}function pc(t){return t.y}function mc(t){return t.width}function gc(t){return t.height}function yc(t){return typeof t==="function"?t:()=>+t}function vc(t,e,n){return Math.max(e,Math.min(t,n))}function bc(){var t=hc,e=pc,n=mc,i=gc,r=yc(0),o=r,a=r,s=r,l=null;function u(u,f,c){var d,h=f!=null?f:+t.call(this,u),p=c!=null?c:+e.call(this,u),m=+n.call(this,u),g=+i.call(this,u),y=Math.min(m,g)/2,v=vc(+r.call(this,u),0,y),b=vc(+o.call(this,u),0,y),x=vc(+a.call(this,u),0,y),_=vc(+s.call(this,u),0,y);if(!l)l=d=(0,Gs.ET)();if(v<=0&&b<=0&&x<=0&&_<=0){l.rect(h,p,m,g)}else{var w=h+m,k=p+g;l.moveTo(h+v,p);l.lineTo(w-b,p);l.bezierCurveTo(w-dc*b,p,w,p+dc*b,w,p+b);l.lineTo(w,k-_);l.bezierCurveTo(w,k-dc*_,w-dc*_,k,w-_,k);l.lineTo(h+x,k);l.bezierCurveTo(h+dc*x,k,h,k-dc*x,h,k-x);l.lineTo(h,p+v);l.bezierCurveTo(h,p+dc*v,h+dc*v,p,h+v,p);l.closePath()}if(d){l=null;return d+""||null}}u.x=function(e){if(arguments.length){t=yc(e);return u}else{return t}};u.y=function(t){if(arguments.length){e=yc(t);return u}else{return e}};u.width=function(t){if(arguments.length){n=yc(t);return u}else{return n}};u.height=function(t){if(arguments.length){i=yc(t);return u}else{return i}};u.cornerRadius=function(t,e,n,i){if(arguments.length){r=yc(t);o=e!=null?yc(e):r;s=n!=null?yc(n):r;a=i!=null?yc(i):o;return u}else{return r}};u.context=function(t){if(arguments.length){l=t==null?null:t;return u}else{return l}};return u}function xc(){var t,e,n,i,r=null,o,a,s,l;function u(t,e,n){const i=n/2;if(o){var u=s-e,f=t-a;if(u||f){var c=Math.sqrt(u*u+f*f),d=(u/=c)*l,h=(f/=c)*l,p=Math.atan2(f,u);r.moveTo(a-d,s-h);r.lineTo(t-u*i,e-f*i);r.arc(t,e,i,p-Math.PI,p);r.lineTo(a+d,s+h);r.arc(a,s,l,p,p+Math.PI)}else{r.arc(t,e,i,0,Kf)}r.closePath()}else{o=1}a=t;s=e;l=i}function f(a){var s,l=a.length,f,c=false,d;if(r==null)r=d=(0,Gs.ET)();for(s=0;s<=l;++s){if(!(s<l&&i(f=a[s],s,a))===c){if(c=!c)o=0}if(c)u(+t(f,s,a),+e(f,s,a),+n(f,s,a))}if(d){r=null;return d+""||null}}f.x=function(e){if(arguments.length){t=e;return f}else{return t}};f.y=function(t){if(arguments.length){e=t;return f}else{return e}};f.size=function(t){if(arguments.length){n=t;return f}else{return n}};f.defined=function(t){if(arguments.length){i=t;return f}else{return i}};f.context=function(t){if(arguments.length){if(t==null){r=null}else{r=t}return f}else{return r}};return f}function _c(t,e){return t!=null?t:e}const wc=t=>t.x||0,kc=t=>t.y||0,Mc=t=>t.width||0,Ec=t=>t.height||0,Sc=t=>(t.x||0)+(t.width||0),zc=t=>(t.y||0)+(t.height||0),Dc=t=>t.startAngle||0,Rc=t=>t.endAngle||0,Ac=t=>t.padAngle||0,$c=t=>t.innerRadius||0,Oc=t=>t.outerRadius||0,Tc=t=>t.cornerRadius||0,Cc=t=>_c(t.cornerRadiusTopLeft,t.cornerRadius)||0,Nc=t=>_c(t.cornerRadiusTopRight,t.cornerRadius)||0,Uc=t=>_c(t.cornerRadiusBottomRight,t.cornerRadius)||0,Ic=t=>_c(t.cornerRadiusBottomLeft,t.cornerRadius)||0,Pc=t=>_c(t.size,64),qc=t=>t.size||1,Lc=t=>!(t.defined===false),jc=t=>uc(t.shape||"circle");const Fc=(0,ds.Z)().startAngle(Dc).endAngle(Rc).padAngle(Ac).innerRadius($c).outerRadius(Oc).cornerRadius(Tc),Wc=vs().x(wc).y1(kc).y0(zc).defined(Lc),Xc=vs().y(kc).x1(wc).x0(Sc).defined(Lc),Bc=(0,ms.Z)().x(wc).y(kc).defined(Lc),Zc=bc().x(wc).y(kc).width(Mc).height(Ec).cornerRadius(Cc,Nc,Uc,Ic),Hc=Js().type(jc).size(Pc),Jc=xc().x(wc).y(kc).defined(Lc).size(qc);function Gc(t){return t.cornerRadius||t.cornerRadiusTopLeft||t.cornerRadiusTopRight||t.cornerRadiusBottomRight||t.cornerRadiusBottomLeft}function Kc(t,e){return Fc.context(t)(e)}function Vc(t,e){const n=e[0],i=n.interpolate||"linear";return(n.orient==="horizontal"?Xc:Wc).curve(qf(i,n.orient,n.tension)).context(t)(e)}function Yc(t,e){const n=e[0],i=n.interpolate||"linear";return Bc.curve(qf(i,n.orient,n.tension)).context(t)(e)}function Qc(t,e,n,i){return Zc.context(t)(e,n,i)}function td(t,e){return(e.mark.shape||e.shape).context(t)(e)}function ed(t,e){return Hc.context(t)(e)}function nd(t,e){return Jc.context(t)(e)}var id=1;function rd(){id=1}function od(t,e,n){var i=e.clip,r=t._defs,o=e.clip_id||(e.clip_id="clip"+id++),a=r.clipping[o]||(r.clipping[o]={id:o});if((0,p.mf)(i)){a.path=i(null)}else if(Gc(n)){a.path=Qc(null,n,0,0)}else{a.width=n.width||0;a.height=n.height||0}return"url(#"+o+")"}function ad(t){this.clear();if(t)this.union(t)}ad.prototype={clone(){return new ad(this)},clear(){this.x1=+Number.MAX_VALUE;this.y1=+Number.MAX_VALUE;this.x2=-Number.MAX_VALUE;this.y2=-Number.MAX_VALUE;return this},empty(){return this.x1===+Number.MAX_VALUE&&this.y1===+Number.MAX_VALUE&&this.x2===-Number.MAX_VALUE&&this.y2===-Number.MAX_VALUE},equals(t){return this.x1===t.x1&&this.y1===t.y1&&this.x2===t.x2&&this.y2===t.y2},set(t,e,n,i){if(n<t){this.x2=t;this.x1=n}else{this.x1=t;this.x2=n}if(i<e){this.y2=e;this.y1=i}else{this.y1=e;this.y2=i}return this},add(t,e){if(t<this.x1)this.x1=t;if(e<this.y1)this.y1=e;if(t>this.x2)this.x2=t;if(e>this.y2)this.y2=e;return this},expand(t){this.x1-=t;this.y1-=t;this.x2+=t;this.y2+=t;return this},round(){this.x1=Math.floor(this.x1);this.y1=Math.floor(this.y1);this.x2=Math.ceil(this.x2);this.y2=Math.ceil(this.y2);return this},scale(t){this.x1*=t;this.y1*=t;this.x2*=t;this.y2*=t;return this},translate(t,e){this.x1+=t;this.x2+=t;this.y1+=e;this.y2+=e;return this},rotate(t,e,n){const i=this.rotatedPoints(t,e,n);return this.clear().add(i[0],i[1]).add(i[2],i[3]).add(i[4],i[5]).add(i[6],i[7])},rotatedPoints(t,e,n){var{x1:i,y1:r,x2:o,y2:a}=this,s=Math.cos(t),l=Math.sin(t),u=e-e*s+n*l,f=n-e*l-n*s;return[s*i-l*r+u,l*i+s*r+f,s*i-l*a+u,l*i+s*a+f,s*o-l*r+u,l*o+s*r+f,s*o-l*a+u,l*o+s*a+f]},union(t){if(t.x1<this.x1)this.x1=t.x1;if(t.y1<this.y1)this.y1=t.y1;if(t.x2>this.x2)this.x2=t.x2;if(t.y2>this.y2)this.y2=t.y2;return this},intersect(t){if(t.x1>this.x1)this.x1=t.x1;if(t.y1>this.y1)this.y1=t.y1;if(t.x2<this.x2)this.x2=t.x2;if(t.y2<this.y2)this.y2=t.y2;return this},encloses(t){return t&&this.x1<=t.x1&&this.x2>=t.x2&&this.y1<=t.y1&&this.y2>=t.y2},alignsWith(t){return t&&(this.x1==t.x1||this.x2==t.x2||this.y1==t.y1||this.y2==t.y2)},intersects(t){return t&&!(this.x2<t.x1||this.x1>t.x2||this.y2<t.y1||this.y1>t.y2)},contains(t,e){return!(t<this.x1||t>this.x2||e<this.y1||e>this.y2)},width(){return this.x2-this.x1},height(){return this.y2-this.y1}};function sd(t){this.mark=t;this.bounds=this.bounds||new ad}function ld(t){sd.call(this,t);this.items=this.items||[]}(0,p.XW)(ld,sd);function ud(t){this._pending=0;this._loader=t||cn()}function fd(t){t._pending+=1}function cd(t){t._pending-=1}ud.prototype={pending(){return this._pending},sanitizeURL(t){const e=this;fd(e);return e._loader.sanitize(t,{context:"href"}).then((t=>{cd(e);return t})).catch((()=>{cd(e);return null}))},loadImage(t){const e=this,n=Vs();fd(e);return e._loader.sanitize(t,{context:"image"}).then((t=>{const i=t.href;if(!i||!n)throw{url:i};const r=new n;const o=(0,p.nr)(t,"crossOrigin")?t.crossOrigin:"anonymous";if(o!=null)r.crossOrigin=o;r.onload=()=>cd(e);r.onerror=()=>cd(e);r.src=i;return r})).catch((t=>{cd(e);return{complete:false,width:0,height:0,src:t&&t.url||""}}))},ready(){const t=this;return new Promise((e=>{function n(i){if(!t.pending())e(i);else setTimeout((()=>{n(true)}),10)}n(false)}))}};function dd(t,e,n){if(e.stroke&&e.opacity!==0&&e.strokeOpacity!==0){const i=e.strokeWidth!=null?+e.strokeWidth:1;t.expand(i+(n?hd(e,i):0))}return t}function hd(t,e){return t.strokeJoin&&t.strokeJoin!=="miter"?0:e}const pd=Kf-1e-8;let md,gd,yd,vd,bd,xd,_d,wd;const kd=(t,e)=>md.add(t,e);const Md=(t,e)=>kd(gd=t,yd=e);const Ed=t=>kd(t,md.y1);const Sd=t=>kd(md.x1,t);const zd=(t,e)=>bd*t+_d*e;const Dd=(t,e)=>xd*t+wd*e;const Rd=(t,e)=>kd(zd(t,e),Dd(t,e));const Ad=(t,e)=>Md(zd(t,e),Dd(t,e));function $d(t,e){md=t;if(e){vd=e*Hf;bd=wd=Math.cos(vd);xd=Math.sin(vd);_d=-xd}else{bd=wd=1;vd=xd=_d=0}return Od}const Od={beginPath(){},closePath(){},moveTo:Ad,lineTo:Ad,rect(t,e,n,i){if(vd){Rd(t+n,e);Rd(t+n,e+i);Rd(t,e+i);Ad(t,e)}else{kd(t+n,e+i);Md(t,e)}},quadraticCurveTo(t,e,n,i){const r=zd(t,e),o=Dd(t,e),a=zd(n,i),s=Dd(n,i);Td(gd,r,a,Ed);Td(yd,o,s,Sd);Md(a,s)},bezierCurveTo(t,e,n,i,r,o){const a=zd(t,e),s=Dd(t,e),l=zd(n,i),u=Dd(n,i),f=zd(r,o),c=Dd(r,o);Cd(gd,a,l,f,Ed);Cd(yd,s,u,c,Sd);Md(f,c)},arc(t,e,n,i,r,o){i+=vd;r+=vd;gd=n*Math.cos(r)+t;yd=n*Math.sin(r)+e;if(Math.abs(r-i)>pd){kd(t-n,e-n);kd(t+n,e+n)}else{const a=i=>kd(n*Math.cos(i)+t,n*Math.sin(i)+e);let s,l;a(i);a(r);if(r!==i){i=i%Kf;if(i<0)i+=Kf;r=r%Kf;if(r<0)r+=Kf;if(r<i){o=!o;s=i;i=r;r=s}if(o){r-=Kf;s=i-i%Gf;for(l=0;l<4&&s>r;++l,s-=Gf)a(s)}else{s=i-i%Gf+Gf;for(l=0;l<4&&s<r;++l,s=s+Gf)a(s)}}}}};function Td(t,e,n,i){const r=(t-e)/(t+n-2*e);if(0<r&&r<1)i(t+(e-t)*r)}function Cd(t,e,n,i,r){const o=i-t+3*e-3*n,a=t+n-2*e,s=t-e;let l=0,u=0,f;if(Math.abs(o)>Jf){f=a*a+s*o;if(f>=0){f=Math.sqrt(f);l=(-a+f)/o;u=(-a-f)/o}}else{l=.5*s/a}if(0<l&&l<1)r(Nd(l,t,e,n,i));if(0<u&&u<1)r(Nd(u,t,e,n,i))}function Nd(t,e,n,i,r){const o=1-t,a=o*o,s=t*t;return a*o*e+3*a*t*n+3*o*s*i+s*t*r}var Ud=(Ud=Ks(1,1))?Ud.getContext("2d"):null;const Id=new ad;function Pd(t){return function(e,n){if(!Ud)return true;t(Ud,e);Id.clear().union(e.bounds).intersect(n).round();const{x1:i,y1:r,x2:o,y2:a}=Id;for(let t=r;t<=a;++t){for(let e=i;e<=o;++e){if(Ud.isPointInPath(e,t)){return true}}}return false}}function qd(t,e){return e.contains(t.x||0,t.y||0)}function Ld(t,e){const n=t.x||0,i=t.y||0,r=t.width||0,o=t.height||0;return e.intersects(Id.set(n,i,n+r,i+o))}function jd(t,e){const n=t.x||0,i=t.y||0,r=t.x2!=null?t.x2:n,o=t.y2!=null?t.y2:i;return Fd(e,n,i,r,o)}function Fd(t,e,n,i,r){const{x1:o,y1:a,x2:s,y2:l}=t,u=i-e,f=r-n;let c=0,d=1,h,p,m,g;for(g=0;g<4;++g){if(g===0){h=-u;p=-(o-e)}if(g===1){h=u;p=s-e}if(g===2){h=-f;p=-(a-n)}if(g===3){h=f;p=l-n}if(Math.abs(h)<1e-10&&p<0)return false;m=p/h;if(h<0){if(m>d)return false;else if(m>c)c=m}else if(h>0){if(m<c)return false;else if(m<d)d=m}}return true}function Wd(t,e){t.globalCompositeOperation=e.blend||"source-over"}function Xd(t,e){return t==null?e:t}function Bd(t,e){const n=e.length;for(let i=0;i<n;++i){t.addColorStop(e[i].offset,e[i].color)}return t}function Zd(t,e,n){const i=n.width(),r=n.height();let o;if(e.gradient==="radial"){o=t.createRadialGradient(n.x1+Xd(e.x1,.5)*i,n.y1+Xd(e.y1,.5)*r,Math.max(i,r)*Xd(e.r1,0),n.x1+Xd(e.x2,.5)*i,n.y1+Xd(e.y2,.5)*r,Math.max(i,r)*Xd(e.r2,.5))}else{const a=Xd(e.x1,0),s=Xd(e.y1,0),l=Xd(e.x2,1),u=Xd(e.y2,0);if(a===l||s===u||i===r){o=t.createLinearGradient(n.x1+a*i,n.y1+s*r,n.x1+l*i,n.y1+u*r)}else{const n=Ks(Math.ceil(i),Math.ceil(r)),o=n.getContext("2d");o.scale(i,r);o.fillStyle=Bd(o.createLinearGradient(a,s,l,u),e.stops);o.fillRect(0,0,i,r);return t.createPattern(n,"no-repeat")}}return Bd(o,e.stops)}function Hd(t,e,n){return Cf(n)?Zd(t,n,e.bounds):n}function Jd(t,e,n){n*=e.fillOpacity==null?1:e.fillOpacity;if(n>0){t.globalAlpha=n;t.fillStyle=Hd(t,e,e.fill);return true}else{return false}}var Gd=[];function Kd(t,e,n){var i=(i=e.strokeWidth)!=null?i:1;if(i<=0)return false;n*=e.strokeOpacity==null?1:e.strokeOpacity;if(n>0){t.globalAlpha=n;t.strokeStyle=Hd(t,e,e.stroke);t.lineWidth=i;t.lineCap=e.strokeCap||"butt";t.lineJoin=e.strokeJoin||"miter";t.miterLimit=e.strokeMiterLimit||10;if(t.setLineDash){t.setLineDash(e.strokeDash||Gd);t.lineDashOffset=e.strokeDashOffset||0}return true}else{return false}}function Vd(t,e){return t.zindex-e.zindex||t.index-e.index}function Yd(t){if(!t.zdirty)return t.zitems;var e=t.items,n=[],i,r,o;for(r=0,o=e.length;r<o;++r){i=e[r];i.index=r;if(i.zindex)n.push(i)}t.zdirty=false;return t.zitems=n.sort(Vd)}function Qd(t,e){var n=t.items,i,r;if(!n||!n.length)return;const o=Yd(t);if(o&&o.length){for(i=0,r=n.length;i<r;++i){if(!n[i].zindex)e(n[i])}n=o}for(i=0,r=n.length;i<r;++i){e(n[i])}}function th(t,e){var n=t.items,i,r;if(!n||!n.length)return null;const o=Yd(t);if(o&&o.length)n=o;for(r=n.length;--r>=0;){if(i=e(n[r]))return i}if(n===o){for(n=t.items,r=n.length;--r>=0;){if(!n[r].zindex){if(i=e(n[r]))return i}}}return null}function eh(t){return function(e,n,i){Qd(n,(n=>{if(!i||i.intersects(n.bounds)){ih(t,e,n,n)}}))}}function nh(t){return function(e,n,i){if(n.items.length&&(!i||i.intersects(n.bounds))){ih(t,e,n.items[0],n.items)}}}function ih(t,e,n,i){var r=n.opacity==null?1:n.opacity;if(r===0)return;if(t(e,i))return;Wd(e,n);if(n.fill&&Jd(e,n,r)){e.fill()}if(n.stroke&&Kd(e,n,r)){e.stroke()}}function rh(t){t=t||p.yb;return function(e,n,i,r,o,a){i*=e.pixelRatio;r*=e.pixelRatio;return th(n,(n=>{const s=n.bounds;if(s&&!s.contains(o,a)||!s)return;if(t(e,n,i,r,o,a))return n}))}}function oh(t,e){return function(n,i,r,o){var a=Array.isArray(i)?i[0]:i,s=e==null?a.fill:e,l=a.stroke&&n.isPointInStroke,u,f;if(l){u=a.strokeWidth;f=a.strokeCap;n.lineWidth=u!=null?u:1;n.lineCap=f!=null?f:"butt"}return t(n,i)?false:s&&n.isPointInPath(r,o)||l&&n.isPointInStroke(r,o)}}function ah(t){return rh(oh(t))}function sh(t,e){return"translate("+t+","+e+")"}function lh(t){return"rotate("+t+")"}function uh(t,e){return"scale("+t+","+e+")"}function fh(t){return sh(t.x||0,t.y||0)}function ch(t){return sh(t.x||0,t.y||0)+(t.angle?" "+lh(t.angle):"")}function dh(t){return sh(t.x||0,t.y||0)+(t.angle?" "+lh(t.angle):"")+(t.scaleX||t.scaleY?" "+uh(t.scaleX||1,t.scaleY||1):"")}function hh(t,e,n){function i(t,n){t("transform",ch(n));t("d",e(null,n))}function r(t,n){e($d(t,n.angle),n);return dd(t,n).translate(n.x||0,n.y||0)}function o(t,n){var i=n.x||0,r=n.y||0,o=n.angle||0;t.translate(i,r);if(o)t.rotate(o*=Hf);t.beginPath();e(t,n);if(o)t.rotate(-o);t.translate(-i,-r)}return{type:t,tag:"path",nested:false,attr:i,bound:r,draw:eh(o),pick:ah(o),isect:n||Pd(o)}}var ph=hh("arc",Kc);function mh(t,e){var n=t[0].orient==="horizontal"?e[1]:e[0],i=t[0].orient==="horizontal"?"y":"x",r=t.length,o=+Infinity,a,s;while(--r>=0){if(t[r].defined===false)continue;s=Math.abs(t[r][i]-n);if(s<o){o=s;a=t[r]}}return a}function gh(t,e){var n=Math.pow(t[0].strokeWidth||1,2),i=t.length,r,o,a;while(--i>=0){if(t[i].defined===false)continue;r=t[i].x-e[0];o=t[i].y-e[1];a=r*r+o*o;if(a<n)return t[i]}return null}function yh(t,e){var n=t.length,i,r,o;while(--n>=0){if(t[n].defined===false)continue;i=t[n].x-e[0];r=t[n].y-e[1];o=i*i+r*r;i=t[n].size||1;if(o<i*i)return t[n]}return null}function vh(t,e,n){function i(t,n){var i=n.mark.items;if(i.length)t("d",e(null,i))}function r(t,n){var i=n.items;if(i.length===0){return t}else{e($d(t),i);return dd(t,i[0])}}function o(t,n){t.beginPath();e(t,n)}const a=oh(o);function s(t,e,n,i,r,o){var s=e.items,l=e.bounds;if(!s||!s.length||l&&!l.contains(r,o)){return null}n*=t.pixelRatio;i*=t.pixelRatio;return a(t,s,n,i)?s[0]:null}return{type:t,tag:"path",nested:true,attr:i,bound:r,draw:nh(o),pick:s,isect:qd,tip:n}}var bh=vh("area",Vc,mh);function xh(t,e){var n=e.clip;t.save();if((0,p.mf)(n)){t.beginPath();n(t);t.clip()}else{_h(t,e.group)}}function _h(t,e){t.beginPath();Gc(e)?Qc(t,e,0,0):t.rect(0,0,e.width||0,e.height||0);t.clip()}function wh(t){const e=Xd(t.strokeWidth,1);return t.strokeOffset!=null?t.strokeOffset:t.stroke&&e>.5&&e<1.5?.5-Math.abs(e-1):0}function kh(t,e){t("transform",fh(e))}function Mh(t,e){const n=wh(e);t("d",Qc(null,e,n,n))}function Eh(t,e){t("class","background");t("aria-hidden",true);Mh(t,e)}function Sh(t,e){t("class","foreground");t("aria-hidden",true);if(e.strokeForeground){Mh(t,e)}else{t("d","")}}function zh(t,e,n){const i=e.clip?od(n,e,e):null;t("clip-path",i)}function Dh(t,e){if(!e.clip&&e.items){const n=e.items,i=n.length;for(let e=0;e<i;++e){t.union(n[e].bounds)}}if((e.clip||e.width||e.height)&&!e.noBound){t.add(0,0).add(e.width||0,e.height||0)}dd(t,e);return t.translate(e.x||0,e.y||0)}function Rh(t,e,n,i){const r=wh(e);t.beginPath();Qc(t,e,(n||0)+r,(i||0)+r)}const Ah=oh(Rh);const $h=oh(Rh,false);const Oh=oh(Rh,true);function Th(t,e,n){Qd(e,(e=>{const i=e.x||0,r=e.y||0,o=e.strokeForeground,a=e.opacity==null?1:e.opacity;if((e.stroke||e.fill)&&a){Rh(t,e,i,r);Wd(t,e);if(e.fill&&Jd(t,e,a)){t.fill()}if(e.stroke&&!o&&Kd(t,e,a)){t.stroke()}}t.save();t.translate(i,r);if(e.clip)_h(t,e);if(n)n.translate(-i,-r);Qd(e,(e=>{this.draw(t,e,n)}));if(n)n.translate(i,r);t.restore();if(o&&e.stroke&&a){Rh(t,e,i,r);Wd(t,e);if(Kd(t,e,a)){t.stroke()}}}))}function Ch(t,e,n,i,r,o){if(e.bounds&&!e.bounds.contains(r,o)||!e.items){return null}const a=n*t.pixelRatio,s=i*t.pixelRatio;return th(e,(l=>{let u,f,c;const d=l.bounds;if(d&&!d.contains(r,o))return;f=l.x||0;c=l.y||0;const h=f+(l.width||0),p=c+(l.height||0),m=l.clip;if(m&&(r<f||r>h||o<c||o>p))return;t.save();t.translate(f,c);f=r-f;c=o-c;if(m&&Gc(l)&&!Oh(t,l,a,s)){t.restore();return null}const g=l.strokeForeground,y=e.interactive!==false;if(y&&g&&l.stroke&&$h(t,l,a,s)){t.restore();return l}u=th(l,(t=>Nh(t,f,c)?this.pick(t,n,i,f,c):null));if(!u&&y&&(l.fill||!g&&l.stroke)&&Ah(t,l,a,s)){u=l}t.restore();return u||null}))}function Nh(t,e,n){return(t.interactive!==false||t.marktype==="group")&&t.bounds&&t.bounds.contains(e,n)}var Uh={type:"group",tag:"g",nested:false,attr:kh,bound:Dh,draw:Th,pick:Ch,isect:Ld,content:zh,background:Eh,foreground:Sh};var Ih={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",version:"1.1"};function Ph(t,e){var n=t.image;if(!n||t.url&&t.url!==n.url){n={complete:false,width:0,height:0};e.loadImage(t.url).then((e=>{t.image=e;t.image.url=t.url}))}return n}function qh(t,e){return t.width!=null?t.width:!e||!e.width?0:t.aspect!==false&&t.height?t.height*e.width/e.height:e.width}function Lh(t,e){return t.height!=null?t.height:!e||!e.height?0:t.aspect!==false&&t.width?t.width*e.height/e.width:e.height}function jh(t,e){return t==="center"?e/2:t==="right"?e:0}function Fh(t,e){return t==="middle"?e/2:t==="bottom"?e:0}function Wh(t,e,n){const i=Ph(e,n),r=qh(e,i),o=Lh(e,i),a=(e.x||0)-jh(e.align,r),s=(e.y||0)-Fh(e.baseline,o),l=!i.src&&i.toDataURL?i.toDataURL():i.src||"";t("href",l,Ih["xmlns:xlink"],"xlink:href");t("transform",sh(a,s));t("width",r);t("height",o);t("preserveAspectRatio",e.aspect===false?"none":"xMidYMid")}function Xh(t,e){const n=e.image,i=qh(e,n),r=Lh(e,n),o=(e.x||0)-jh(e.align,i),a=(e.y||0)-Fh(e.baseline,r);return t.set(o,a,o+i,a+r)}function Bh(t,e,n){Qd(e,(e=>{if(n&&!n.intersects(e.bounds))return;const i=Ph(e,this);let r=qh(e,i);let o=Lh(e,i);if(r===0||o===0)return;let a=(e.x||0)-jh(e.align,r),s=(e.y||0)-Fh(e.baseline,o),l,u,f,c;if(e.aspect!==false){u=i.width/i.height;f=e.width/e.height;if(u===u&&f===f&&u!==f){if(f<u){c=r/u;s+=(o-c)/2;o=c}else{c=o*u;a+=(r-c)/2;r=c}}}if(i.complete||i.toDataURL){Wd(t,e);t.globalAlpha=(l=e.opacity)!=null?l:1;t.imageSmoothingEnabled=e.smooth!==false;t.drawImage(i,a,s,r,o)}}))}var Zh={type:"image",tag:"image",nested:false,attr:Wh,bound:Xh,draw:Bh,pick:rh(),isect:p.yb,get:Ph,xOffset:jh,yOffset:Fh};var Hh=vh("line",Yc,gh);function Jh(t,e){var n=e.scaleX||1,i=e.scaleY||1;if(n!==1||i!==1){t("vector-effect","non-scaling-stroke")}t("transform",dh(e));t("d",e.path)}function Gh(t,e){var n=e.path;if(n==null)return true;var i=e.x||0,r=e.y||0,o=e.scaleX||1,a=e.scaleY||1,s=(e.angle||0)*Hf,l=e.pathCache;if(!l||l.path!==n){(e.pathCache=l=Bf(n)).path=n}if(s&&t.rotate&&t.translate){t.translate(i,r);t.rotate(s);oc(t,l,0,0,o,a);t.rotate(-s);t.translate(-i,-r)}else{oc(t,l,i,r,o,a)}}function Kh(t,e){return Gh($d(t,e.angle),e)?t.set(0,0,0,0):dd(t,e,true)}var Vh={type:"path",tag:"path",nested:false,attr:Jh,bound:Kh,draw:eh(Gh),pick:ah(Gh),isect:Pd(Gh)};function Yh(t,e){t("d",Qc(null,e))}function Qh(t,e){var n,i;return dd(t.set(n=e.x||0,i=e.y||0,n+e.width||0,i+e.height||0),e)}function tp(t,e){t.beginPath();Qc(t,e)}var ep={type:"rect",tag:"path",nested:false,attr:Yh,bound:Qh,draw:eh(tp),pick:ah(tp),isect:Ld};function np(t,e){t("transform",fh(e));t("x2",e.x2!=null?e.x2-(e.x||0):0);t("y2",e.y2!=null?e.y2-(e.y||0):0)}function ip(t,e){var n,i;return dd(t.set(n=e.x||0,i=e.y||0,e.x2!=null?e.x2:n,e.y2!=null?e.y2:i),e)}function rp(t,e,n){var i,r,o,a;if(e.stroke&&Kd(t,e,n)){i=e.x||0;r=e.y||0;o=e.x2!=null?e.x2:i;a=e.y2!=null?e.y2:r;t.beginPath();t.moveTo(i,r);t.lineTo(o,a);return true}return false}function op(t,e,n){Qd(e,(e=>{if(n&&!n.intersects(e.bounds))return;var i=e.opacity==null?1:e.opacity;if(i&&rp(t,e,i)){Wd(t,e);t.stroke()}}))}function ap(t,e,n,i){if(!t.isPointInStroke)return false;return rp(t,e,1)&&t.isPointInStroke(n,i)}var sp={type:"rule",tag:"line",nested:false,attr:np,bound:ip,draw:op,pick:rh(ap),isect:jd};var lp=hh("shape",td);var up=hh("symbol",ed,qd);const fp=(0,p.$m)();var cp={height:yp,measureWidth:mp,estimateWidth:hp,width:hp,canvas:dp};dp(true);function dp(t){cp.width=t&&Ud?mp:hp}function hp(t,e){return pp(wp(t,e),yp(t))}function pp(t,e){return~~(.8*t.length*e)}function mp(t,e){return yp(t)<=0||!(e=wp(t,e))?0:gp(e,Sp(t))}function gp(t,e){const n=`(${e}) ${t}`;let i=fp.get(n);if(i===undefined){Ud.font=e;i=Ud.measureText(t).width;fp.set(n,i)}return i}function yp(t){return t.fontSize!=null?+t.fontSize||0:11}function vp(t){return t.lineHeight!=null?t.lineHeight:yp(t)+2}function bp(t){return(0,p.kJ)(t)?t.length>1?t:t[0]:t}function xp(t){return bp(t.lineBreak&&t.text&&!(0,p.kJ)(t.text)?t.text.split(t.lineBreak):t.text)}function _p(t){const e=xp(t);return((0,p.kJ)(e)?e.length-1:0)*vp(t)}function wp(t,e){const n=e==null?"":(e+"").trim();return t.limit>0&&n.length?Mp(t,n):n}function kp(t){if(cp.width===mp){const e=Sp(t);return t=>gp(t,e)}else{const e=yp(t);return t=>pp(t,e)}}function Mp(t,e){var n=+t.limit,i=kp(t);if(i(e)<n)return e;var r=t.ellipsis||"…",o=t.dir==="rtl",a=0,s=e.length,l;n-=i(r);if(o){while(a<s){l=a+s>>>1;if(i(e.slice(l))>n)a=l+1;else s=l}return r+e.slice(a)}else{while(a<s){l=1+(a+s>>>1);if(i(e.slice(0,l))<n)a=l;else s=l-1}return e.slice(0,a)+r}}function Ep(t,e){var n=t.font;return(e&&n?String(n).replace(/"/g,"'"):n)||"sans-serif"}function Sp(t,e){return""+(t.fontStyle?t.fontStyle+" ":"")+(t.fontVariant?t.fontVariant+" ":"")+(t.fontWeight?t.fontWeight+" ":"")+yp(t)+"px "+Ep(t,e)}function zp(t){var e=t.baseline,n=yp(t);return Math.round(e==="top"?.79*n:e==="middle"?.3*n:e==="bottom"?-.21*n:e==="line-top"?.29*n+.5*vp(t):e==="line-bottom"?.29*n-.5*vp(t):0)}const Dp={left:"start",center:"middle",right:"end"};const Rp=new ad;function Ap(t){var e=t.x||0,n=t.y||0,i=t.radius||0,r;if(i){r=(t.theta||0)-Gf;e+=i*Math.cos(r);n+=i*Math.sin(r)}Rp.x1=e;Rp.y1=n;return Rp}function $p(t,e){var n=e.dx||0,i=(e.dy||0)+zp(e),r=Ap(e),o=r.x1,a=r.y1,s=e.angle||0,l;t("text-anchor",Dp[e.align]||"start");if(s){l=sh(o,a)+" "+lh(s);if(n||i)l+=" "+sh(n,i)}else{l=sh(o+n,a+i)}t("transform",l)}function Op(t,e,n){var i=cp.height(e),r=e.align,o=Ap(e),a=o.x1,s=o.y1,l=e.dx||0,u=(e.dy||0)+zp(e)-Math.round(.8*i),f=xp(e),c;if((0,p.kJ)(f)){i+=vp(e)*(f.length-1);c=f.reduce(((t,n)=>Math.max(t,cp.width(e,n))),0)}else{c=cp.width(e,f)}if(r==="center"){l-=c/2}else if(r==="right"){l-=c}else;t.set(l+=a,u+=s,l+c,u+i);if(e.angle&&!n){t.rotate(e.angle*Hf,a,s)}else if(n===2){return t.rotatedPoints(e.angle*Hf,a,s)}return t}function Tp(t,e,n){Qd(e,(e=>{var i=e.opacity==null?1:e.opacity,r,o,a,s,l,u,f;if(n&&!n.intersects(e.bounds)||i===0||e.fontSize<=0||e.text==null||e.text.length===0)return;t.font=Sp(e);t.textAlign=e.align||"left";r=Ap(e);o=r.x1,a=r.y1;if(e.angle){t.save();t.translate(o,a);t.rotate(e.angle*Hf);o=a=0}o+=e.dx||0;a+=(e.dy||0)+zp(e);u=xp(e);Wd(t,e);if((0,p.kJ)(u)){l=vp(e);for(s=0;s<u.length;++s){f=wp(e,u[s]);if(e.fill&&Jd(t,e,i)){t.fillText(f,o,a)}if(e.stroke&&Kd(t,e,i)){t.strokeText(f,o,a)}a+=l}}else{f=wp(e,u);if(e.fill&&Jd(t,e,i)){t.fillText(f,o,a)}if(e.stroke&&Kd(t,e,i)){t.strokeText(f,o,a)}}if(e.angle)t.restore()}))}function Cp(t,e,n,i,r,o){if(e.fontSize<=0)return false;if(!e.angle)return true;var a=Ap(e),s=a.x1,l=a.y1,u=Op(Rp,e,1),f=-e.angle*Hf,c=Math.cos(f),d=Math.sin(f),h=c*r-d*o+(s-c*s+d*l),p=d*r+c*o+(l-d*s-c*l);return u.contains(h,p)}function Np(t,e){const n=Op(Rp,t,2);return Fd(e,n[0],n[1],n[2],n[3])||Fd(e,n[0],n[1],n[4],n[5])||Fd(e,n[4],n[5],n[6],n[7])||Fd(e,n[2],n[3],n[6],n[7])}var Up={type:"text",tag:"text",nested:false,attr:$p,bound:Op,draw:Tp,pick:rh(Cp),isect:Np};var Ip=vh("trail",nd,yh);var Pp={arc:ph,area:bh,group:Uh,image:Zh,line:Hh,path:Vh,rect:ep,rule:sp,shape:lp,symbol:up,text:Up,trail:Ip};function qp(t,e,n){var i=Pp[t.mark.marktype],r=e||i.bound;if(i.nested)t=t.mark;return r(t.bounds||(t.bounds=new ad),t,n)}var Lp={mark:null};function jp(t,e,n){var i=Pp[t.marktype],r=i.bound,o=t.items,a=o&&o.length,s,l,u,f;if(i.nested){if(a){u=o[0]}else{Lp.mark=t;u=Lp}f=qp(u,r,n);e=e&&e.union(f)||f;return e}e=e||t.bounds&&t.bounds.clear()||new ad;if(a){for(s=0,l=o.length;s<l;++s){e.union(qp(o[s],r,n))}}return t.bounds=e}const Fp=["marktype","name","role","interactive","clip","items","zindex","x","y","width","height","align","baseline","fill","fillOpacity","opacity","blend","stroke","strokeOpacity","strokeWidth","strokeCap","strokeDash","strokeDashOffset","strokeForeground","strokeOffset","startAngle","endAngle","innerRadius","outerRadius","cornerRadius","padAngle","cornerRadiusTopLeft","cornerRadiusTopRight","cornerRadiusBottomLeft","cornerRadiusBottomRight","interpolate","tension","orient","defined","url","aspect","smooth","path","scaleX","scaleY","x2","y2","size","shape","text","angle","theta","radius","dir","dx","dy","ellipsis","limit","lineBreak","lineHeight","font","fontSize","fontWeight","fontStyle","fontVariant","description","aria","ariaRole","ariaRoleDescription"];function Wp(t,e){return JSON.stringify(t,Fp,e)}function Xp(t){const e=typeof t==="string"?JSON.parse(t):t;return Bp(e)}function Bp(t){var e=t.marktype,n=t.items,i,r,o;if(n){for(r=0,o=n.length;r<o;++r){i=e?"mark":"group";n[r][i]=t;if(n[r].zindex)n[r][i].zdirty=true;if("group"===(e||i))Bp(n[r])}}if(e)jp(t);return t}function Zp(t){if(arguments.length){this.root=Xp(t)}else{this.root=Hp({marktype:"group",name:"root",role:"frame"});this.root.items=[new ld(this.root)]}}Zp.prototype={toJSON(t){return Wp(this.root,t||0)},mark(t,e,n){e=e||this.root.items[0];const i=Hp(t,e);e.items[n]=i;if(i.zindex)i.group.zdirty=true;return i}};function Hp(t,e){const n={bounds:new ad,clip:!!t.clip,group:e,interactive:t.interactive===false?false:true,items:[],marktype:t.marktype,name:t.name||undefined,role:t.role||undefined,zindex:t.zindex||0};if(t.aria!=null){n.aria=t.aria}if(t.description){n.description=t.description}return n}function Jp(t,e,n){if(!t&&typeof document!=="undefined"&&document.createElement){t=document}return t?n?t.createElementNS(n,e):t.createElement(e):null}function Gp(t,e){e=e.toLowerCase();var n=t.childNodes,i=0,r=n.length;for(;i<r;++i)if(n[i].tagName.toLowerCase()===e){return n[i]}}function Kp(t,e,n,i){var r=t.childNodes[e],o;if(!r||r.tagName.toLowerCase()!==n.toLowerCase()){o=r||null;r=Jp(t.ownerDocument,n,i);t.insertBefore(r,o)}return r}function Vp(t,e){var n=t.childNodes,i=n.length;while(i>e)t.removeChild(n[--i]);return t}function Yp(t){return"mark-"+t.marktype+(t.role?" role-"+t.role:"")+(t.name?" "+t.name:"")}function Qp(t,e){const n=e.getBoundingClientRect();return[t.clientX-n.left-(e.clientLeft||0),t.clientY-n.top-(e.clientTop||0)]}function tm(t,e,n,i){var r=t&&t.mark,o,a;if(r&&(o=Pp[r.marktype]).tip){a=Qp(e,n);a[0]-=i[0];a[1]-=i[1];while(t=t.mark.group){a[0]-=t.x||0;a[1]-=t.y||0}t=o.tip(r.items,a)}return t}function em(t,e){this._active=null;this._handlers={};this._loader=t||cn();this._tooltip=e||nm}function nm(t,e,n,i){t.element().setAttribute("title",i||"")}em.prototype={initialize(t,e,n){this._el=t;this._obj=n||null;return this.origin(e)},element(){return this._el},canvas(){return this._el&&this._el.firstChild},origin(t){if(arguments.length){this._origin=t||[0,0];return this}else{return this._origin.slice()}},scene(t){if(!arguments.length)return this._scene;this._scene=t;return this},on(){},off(){},_handlerIndex(t,e,n){for(let i=t?t.length:0;--i>=0;){if(t[i].type===e&&(!n||t[i].handler===n)){return i}}return-1},handlers(t){const e=this._handlers,n=[];if(t){n.push(...e[this.eventName(t)])}else{for(const t in e){n.push(...e[t])}}return n},eventName(t){const e=t.indexOf(".");return e<0?t:t.slice(0,e)},handleHref(t,e,n){this._loader.sanitize(n,{context:"href"}).then((e=>{const n=new MouseEvent(t.type,t),i=Jp(null,"a");for(const t in e)i.setAttribute(t,e[t]);i.dispatchEvent(n)})).catch((()=>{}))},handleTooltip(t,e,n){if(e&&e.tooltip!=null){e=tm(e,t,this.canvas(),this._origin);const i=n&&e&&e.tooltip||null;this._tooltip.call(this._obj,this,t,e,i)}},getItemBoundingClientRect(t){const e=this.canvas();if(!e)return;const n=e.getBoundingClientRect(),i=this._origin,r=t.bounds,o=r.width(),a=r.height();let s=r.x1+i[0]+n.left,l=r.y1+i[1]+n.top;while(t.mark&&(t=t.mark.group)){s+=t.x||0;l+=t.y||0}return{x:s,y:l,width:o,height:a,left:s,top:l,right:s+o,bottom:l+a}}};function im(t){this._el=null;this._bgcolor=null;this._loader=new ud(t)}im.prototype={initialize(t,e,n,i,r){this._el=t;return this.resize(e,n,i,r)},element(){return this._el},canvas(){return this._el&&this._el.firstChild},background(t){if(arguments.length===0)return this._bgcolor;this._bgcolor=t;return this},resize(t,e,n,i){this._width=t;this._height=e;this._origin=n||[0,0];this._scale=i||1;return this},dirty(){},render(t){const e=this;e._call=function(){e._render(t)};e._call();e._call=null;return e},_render(){},renderAsync(t){const e=this.render(t);return this._ready?this._ready.then((()=>e)):Promise.resolve(e)},_load(t,e){var n=this,i=n._loader[t](e);if(!n._ready){const t=n._call;n._ready=n._loader.ready().then((e=>{if(e)t();n._ready=null}))}return i},sanitizeURL(t){return this._load("sanitizeURL",t)},loadImage(t){return this._load("loadImage",t)}};const rm="keydown";const om="keypress";const am="keyup";const sm="dragenter";const lm="dragleave";const um="dragover";const fm="mousedown";const cm="mouseup";const dm="mousemove";const hm="mouseout";const pm="mouseover";const mm="click";const gm="dblclick";const ym="wheel";const vm="mousewheel";const bm="touchstart";const xm="touchmove";const _m="touchend";const wm=[rm,om,am,sm,lm,um,fm,cm,dm,hm,pm,mm,gm,ym,vm,bm,xm,_m];const km=dm;const Mm=hm;const Em=mm;function Sm(t,e){em.call(this,t,e);this._down=null;this._touch=null;this._first=true;this._events={}}const zm=t=>t===bm||t===xm||t===_m?[bm,xm,_m]:[t];function Dm(t,e){zm(e).forEach((e=>Rm(t,e)))}function Rm(t,e){const n=t.canvas();if(n&&!t._events[e]){t._events[e]=1;n.addEventListener(e,t[e]?n=>t[e](n):n=>t.fire(e,n))}}function Am(t,e,n){return function(i){const r=this._active,o=this.pickEvent(i);if(o===r){this.fire(t,i)}else{if(!r||!r.exit){this.fire(n,i)}this._active=o;this.fire(e,i);this.fire(t,i)}}}function $m(t){return function(e){this.fire(t,e);this._active=null}}(0,p.XW)(Sm,em,{initialize(t,e,n){this._canvas=t&&Gp(t,"canvas");[mm,fm,dm,hm,lm].forEach((t=>Dm(this,t)));return em.prototype.initialize.call(this,t,e,n)},canvas(){return this._canvas},context(){return this._canvas.getContext("2d")},events:wm,DOMMouseScroll(t){this.fire(vm,t)},mousemove:Am(dm,pm,hm),dragover:Am(um,sm,lm),mouseout:$m(hm),dragleave:$m(lm),mousedown(t){this._down=this._active;this.fire(fm,t)},click(t){if(this._down===this._active){this.fire(mm,t);this._down=null}},touchstart(t){this._touch=this.pickEvent(t.changedTouches[0]);if(this._first){this._active=this._touch;this._first=false}this.fire(bm,t,true)},touchmove(t){this.fire(xm,t,true)},touchend(t){this.fire(_m,t,true);this._touch=null},fire(t,e,n){const i=n?this._touch:this._active,r=this._handlers[t];e.vegaType=t;if(t===Em&&i&&i.href){this.handleHref(e,i,i.href)}else if(t===km||t===Mm){this.handleTooltip(e,i,t!==Mm)}if(r){for(let t=0,n=r.length;t<n;++t){r[t].handler.call(this._obj,e,i)}}},on(t,e){const n=this.eventName(t),i=this._handlers,r=this._handlerIndex(i[n],t,e);if(r<0){Dm(this,t);(i[n]||(i[n]=[])).push({type:t,handler:e})}return this},off(t,e){const n=this.eventName(t),i=this._handlers[n],r=this._handlerIndex(i,t,e);if(r>=0){i.splice(r,1)}return this},pickEvent(t){const e=Qp(t,this._canvas),n=this._origin;return this.pick(this._scene,e[0],e[1],e[0]-n[0],e[1]-n[1])},pick(t,e,n,i,r){const o=this.context(),a=Pp[t.marktype];return a.pick.call(this,o,t,e,n,i,r)}});function Om(){return typeof window!=="undefined"?window.devicePixelRatio||1:1}var Tm=Om();function Cm(t,e,n,i,r,o){const a=typeof HTMLElement!=="undefined"&&t instanceof HTMLElement&&t.parentNode!=null,s=t.getContext("2d"),l=a?Tm:r;t.width=e*l;t.height=n*l;for(const u in o){s[u]=o[u]}if(a&&l!==1){t.style.width=e+"px";t.style.height=n+"px"}s.pixelRatio=l;s.setTransform(l,0,0,l,l*i[0],l*i[1]);return t}function Nm(t){im.call(this,t);this._options={};this._redraw=false;this._dirty=new ad;this._tempb=new ad}const Um=im.prototype;const Im=(t,e,n)=>(new ad).set(0,0,e,n).translate(-t[0],-t[1]);function Pm(t,e,n){e.expand(1).round();if(t.pixelRatio%1){e.scale(t.pixelRatio).round().scale(1/t.pixelRatio)}e.translate(-(n[0]%1),-(n[1]%1));t.beginPath();t.rect(e.x1,e.y1,e.width(),e.height());t.clip();return e}(0,p.XW)(Nm,im,{initialize(t,e,n,i,r,o){this._options=o||{};this._canvas=this._options.externalContext?null:Ks(1,1,this._options.type);if(t&&this._canvas){Vp(t,0).appendChild(this._canvas);this._canvas.setAttribute("class","marks")}return Um.initialize.call(this,t,e,n,i,r)},resize(t,e,n,i){Um.resize.call(this,t,e,n,i);if(this._canvas){Cm(this._canvas,this._width,this._height,this._origin,this._scale,this._options.context)}else{const t=this._options.externalContext;if(!t)(0,p.vU)("CanvasRenderer is missing a valid canvas or context");t.scale(this._scale,this._scale);t.translate(this._origin[0],this._origin[1])}this._redraw=true;return this},canvas(){return this._canvas},context(){return this._options.externalContext||(this._canvas?this._canvas.getContext("2d"):null)},dirty(t){const e=this._tempb.clear().union(t.bounds);let n=t.mark.group;while(n){e.translate(n.x||0,n.y||0);n=n.mark.group}this._dirty.union(e)},_render(t){const e=this.context(),n=this._origin,i=this._width,r=this._height,o=this._dirty,a=Im(n,i,r);e.save();const s=this._redraw||o.empty()?(this._redraw=false,a.expand(1)):Pm(e,a.intersect(o),n);this.clear(-n[0],-n[1],i,r);this.draw(e,t,s);e.restore();o.clear();return this},draw(t,e,n){const i=Pp[e.marktype];if(e.clip)xh(t,e);i.draw.call(this,t,e,n);if(e.clip)t.restore()},clear(t,e,n,i){const r=this._options,o=this.context();if(r.type!=="pdf"&&!r.externalContext){o.clearRect(t,e,n,i)}if(this._bgcolor!=null){o.fillStyle=this._bgcolor;o.fillRect(t,e,n,i)}}});function qm(t,e){em.call(this,t,e);const n=this;n._hrefHandler=Lm(n,((t,e)=>{if(e&&e.href)n.handleHref(t,e,e.href)}));n._tooltipHandler=Lm(n,((t,e)=>{n.handleTooltip(t,e,t.type!==Mm)}))}const Lm=(t,e)=>n=>{let i=n.target.__data__;i=Array.isArray(i)?i[0]:i;n.vegaType=n.type;e.call(t._obj,n,i)};(0,p.XW)(qm,em,{initialize(t,e,n){let i=this._svg;if(i){i.removeEventListener(Em,this._hrefHandler);i.removeEventListener(km,this._tooltipHandler);i.removeEventListener(Mm,this._tooltipHandler)}this._svg=i=t&&Gp(t,"svg");if(i){i.addEventListener(Em,this._hrefHandler);i.addEventListener(km,this._tooltipHandler);i.addEventListener(Mm,this._tooltipHandler)}return em.prototype.initialize.call(this,t,e,n)},canvas(){return this._svg},on(t,e){const n=this.eventName(t),i=this._handlers,r=this._handlerIndex(i[n],t,e);if(r<0){const r={type:t,handler:e,listener:Lm(this,e)};(i[n]||(i[n]=[])).push(r);if(this._svg){this._svg.addEventListener(n,r.listener)}}return this},off(t,e){const n=this.eventName(t),i=this._handlers[n],r=this._handlerIndex(i,t,e);if(r>=0){if(this._svg){this._svg.removeEventListener(n,i[r].listener)}i.splice(r,1)}return this}});const jm="aria-hidden";const Fm="aria-label";const Wm="role";const Xm="aria-roledescription";const Bm="graphics-object";const Zm="graphics-symbol";const Hm=(t,e,n)=>({[Wm]:t,[Xm]:e,[Fm]:n||undefined});const Jm=(0,p.Rg)(["axis-domain","axis-grid","axis-label","axis-tick","axis-title","legend-band","legend-entry","legend-gradient","legend-label","legend-title","legend-symbol","title"]);const Gm={axis:{desc:"axis",caption:ng},legend:{desc:"legend",caption:ig},"title-text":{desc:"title",caption:t=>`Title text '${eg(t)}'`},"title-subtitle":{desc:"subtitle",caption:t=>`Subtitle text '${eg(t)}'`}};const Km={ariaRole:Wm,ariaRoleDescription:Xm,description:Fm};function Vm(t,e){const n=e.aria===false;t(jm,n||undefined);if(n||e.description==null){for(const e in Km){t(Km[e],undefined)}}else{const n=e.mark.marktype;t(Fm,e.description);t(Wm,e.ariaRole||(n==="group"?Bm:Zm));t(Xm,e.ariaRoleDescription||`${n} mark`)}}function Ym(t){return t.aria===false?{[jm]:true}:Jm[t.role]?null:Gm[t.role]?tg(t,Gm[t.role]):Qm(t)}function Qm(t){const e=t.marktype;const n=e==="group"||e==="text"||t.items.some((t=>t.description!=null&&t.aria!==false));return Hm(n?Bm:Zm,`${e} mark container`,t.description)}function tg(t,e){try{const n=t.items[0],i=e.caption||(()=>"");return Hm(e.role||Zm,e.desc,n.description||i(n))}catch(n){return null}}function eg(t){return(0,p.IX)(t.text).join(" ")}function ng(t){const e=t.datum,n=t.orient,i=e.title?rg(t):null,r=t.context,o=r.scales[e.scale].value,a=r.dataflow.locale(),s=o.type,l=n==="left"||n==="right"?"Y":"X";return`${l}-axis`+(i?` titled '${i}'`:"")+` for a ${Iu(s)?"discrete":s} scale`+` with ${Af(a,o,t)}`}function ig(t){const e=t.datum,n=e.title?rg(t):null,i=`${e.type||""} legend`.trim(),r=e.scales,o=Object.keys(r),a=t.context,s=a.scales[r[o[0]]].value,l=a.dataflow.locale();return ag(i)+(n?` titled '${n}'`:"")+` for ${og(o)}`+` with ${Af(l,s,t)}`}function rg(t){try{return(0,p.IX)((0,p.fj)(t.items).items[0].text).join(" ")}catch(e){return null}}function og(t){t=t.map((t=>t+(t==="fill"||t==="stroke"?" color":"")));return t.length<2?t[0]:t.slice(0,-1).join(", ")+" and "+(0,p.fj)(t)}function ag(t){return t.length?t[0].toUpperCase()+t.slice(1):t}const sg=t=>(t+"").replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;");const lg=t=>sg(t).replace(/"/g,"&quot;").replace(/\t/g,"&#x9;").replace(/\n/g,"&#xA;").replace(/\r/g,"&#xD;");function ug(){let t="",e="",n="";const i=[],r=()=>e=n="",o=o=>{if(e){t+=`${e}>${n}`;r()}i.push(o)},a=(t,n)=>{if(n!=null)e+=` ${t}="${lg(n)}"`;return s},s={open(t){o(t);e="<"+t;for(var n=arguments.length,i=new Array(n>1?n-1:0),r=1;r<n;r++){i[r-1]=arguments[r]}for(const e of i){for(const t in e)a(t,e[t])}return s},close(){const o=i.pop();if(e){t+=e+(n?`>${n}</${o}>`:"/>")}else{t+=`</${o}>`}r();return s},attr:a,text:t=>(n+=sg(t),s),toString:()=>t};return s}const fg=t=>cg(ug(),t)+"";function cg(t,e){t.open(e.tagName);if(e.hasAttributes()){const n=e.attributes,i=n.length;for(let e=0;e<i;++e){t.attr(n[e].name,n[e].value)}}if(e.hasChildNodes()){const n=e.childNodes;for(const e of n){e.nodeType===3?t.text(e.nodeValue):cg(t,e)}}return t.close()}const dg={fill:"fill",fillOpacity:"fill-opacity",stroke:"stroke",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",strokeCap:"stroke-linecap",strokeJoin:"stroke-linejoin",strokeDash:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeMiterLimit:"stroke-miterlimit",opacity:"opacity"};const hg={blend:"mix-blend-mode"};const pg={fill:"none","stroke-miterlimit":10};const mg=0,gg="http://www.w3.org/2000/xmlns/",yg=Ih.xmlns;function vg(t){im.call(this,t);this._dirtyID=0;this._dirty=[];this._svg=null;this._root=null;this._defs=null}const bg=im.prototype;(0,p.XW)(vg,im,{initialize(t,e,n,i,r){this._defs={};this._clearDefs();if(t){this._svg=Kp(t,0,"svg",yg);this._svg.setAttributeNS(gg,"xmlns",yg);this._svg.setAttributeNS(gg,"xmlns:xlink",Ih["xmlns:xlink"]);this._svg.setAttribute("version",Ih["version"]);this._svg.setAttribute("class","marks");Vp(t,1);this._root=Kp(this._svg,mg,"g",yg);$g(this._root,pg);Vp(this._svg,mg+1)}this.background(this._bgcolor);return bg.initialize.call(this,t,e,n,i,r)},background(t){if(arguments.length&&this._svg){this._svg.style.setProperty("background-color",t)}return bg.background.apply(this,arguments)},resize(t,e,n,i){bg.resize.call(this,t,e,n,i);if(this._svg){$g(this._svg,{width:this._width*this._scale,height:this._height*this._scale,viewBox:`0 0 ${this._width} ${this._height}`});this._root.setAttribute("transform",`translate(${this._origin})`)}this._dirty=[];return this},canvas(){return this._svg},svg(){const t=this._svg,e=this._bgcolor;if(!t)return null;let n;if(e){t.removeAttribute("style");n=Kp(t,mg,"rect",yg);$g(n,{width:this._width,height:this._height,fill:e})}const i=fg(t);if(e){t.removeChild(n);this._svg.style.setProperty("background-color",e)}return i},_render(t){if(this._dirtyCheck()){if(this._dirtyAll)this._clearDefs();this.mark(this._root,t);Vp(this._root,1)}this.defs();this._dirty=[];++this._dirtyID;return this},dirty(t){if(t.dirty!==this._dirtyID){t.dirty=this._dirtyID;this._dirty.push(t)}},isDirty(t){return this._dirtyAll||!t._svg||!t._svg.ownerSVGElement||t.dirty===this._dirtyID},_dirtyCheck(){this._dirtyAll=true;const t=this._dirty;if(!t.length||!this._dirtyID)return true;const e=++this._dirtyID;let n,i,r,o,a,s,l;for(a=0,s=t.length;a<s;++a){n=t[a];i=n.mark;if(i.marktype!==r){r=i.marktype;o=Pp[r]}if(i.zdirty&&i.dirty!==e){this._dirtyAll=false;xg(n,e);i.items.forEach((t=>{t.dirty=e}))}if(i.zdirty)continue;if(n.exit){if(o.nested&&i.items.length){l=i.items[0];if(l._svg)this._update(o,l._svg,l)}else if(n._svg){l=n._svg.parentNode;if(l)l.removeChild(n._svg)}n._svg=null;continue}n=o.nested?i.items[0]:n;if(n._update===e)continue;if(!n._svg||!n._svg.ownerSVGElement){this._dirtyAll=false;xg(n,e)}else{this._update(o,n._svg,n)}n._update=e}return!this._dirtyAll},mark(t,e,n){if(!this.isDirty(e)){return e._svg}const i=this._svg,r=Pp[e.marktype],o=e.interactive===false?"none":null,a=r.tag==="g";const s=Mg(e,t,n,"g",i);s.setAttribute("class",Yp(e));const l=Ym(e);for(const d in l)Og(s,d,l[d]);if(!a){Og(s,"pointer-events",o)}Og(s,"clip-path",e.clip?od(this,e,e.group):null);let u=null,f=0;const c=t=>{const e=this.isDirty(t),n=Mg(t,s,u,r.tag,i);if(e){this._update(r,n,t);if(a)kg(this,n,t)}u=n;++f};if(r.nested){if(e.items.length)c(e.items[0])}else{Qd(e,c)}Vp(s,f);return s},_update(t,e,n){Sg=e;zg=e.__values__;Vm(Rg,n);t.attr(Rg,n,this);const i=Dg[t.type];if(i)i.call(this,t,e,n);if(Sg)this.style(Sg,n)},style(t,e){if(e==null)return;for(const n in dg){let i=n==="font"?Ep(e):e[n];if(i===zg[n])continue;const r=dg[n];if(i==null){t.removeAttribute(r)}else{if(Cf(i)){i=Nf(i,this._defs.gradient,Cg())}t.setAttribute(r,i+"")}zg[n]=i}for(const n in hg){Ag(t,hg[n],e[n])}},defs(){const t=this._svg,e=this._defs;let n=e.el,i=0;for(const r in e.gradient){if(!n)e.el=n=Kp(t,mg+1,"defs",yg);i=_g(n,e.gradient[r],i)}for(const r in e.clipping){if(!n)e.el=n=Kp(t,mg+1,"defs",yg);i=wg(n,e.clipping[r],i)}if(n){i===0?(t.removeChild(n),e.el=null):Vp(n,i)}},_clearDefs(){const t=this._defs;t.gradient={};t.clipping={}}});function xg(t,e){for(;t&&t.dirty!==e;t=t.mark.group){t.dirty=e;if(t.mark&&t.mark.dirty!==e){t.mark.dirty=e}else return}}function _g(t,e,n){let i,r,o;if(e.gradient==="radial"){let i=Kp(t,n++,"pattern",yg);$g(i,{id:Tf+e.id,viewBox:"0,0,1,1",width:"100%",height:"100%",preserveAspectRatio:"xMidYMid slice"});i=Kp(i,0,"rect",yg);$g(i,{width:1,height:1,fill:`url(${Cg()}#${e.id})`});t=Kp(t,n++,"radialGradient",yg);$g(t,{id:e.id,fx:e.x1,fy:e.y1,fr:e.r1,cx:e.x2,cy:e.y2,r:e.r2})}else{t=Kp(t,n++,"linearGradient",yg);$g(t,{id:e.id,x1:e.x1,x2:e.x2,y1:e.y1,y2:e.y2})}for(i=0,r=e.stops.length;i<r;++i){o=Kp(t,i,"stop",yg);o.setAttribute("offset",e.stops[i].offset);o.setAttribute("stop-color",e.stops[i].color)}Vp(t,i);return n}function wg(t,e,n){let i;t=Kp(t,n,"clipPath",yg);t.setAttribute("id",e.id);if(e.path){i=Kp(t,0,"path",yg);i.setAttribute("d",e.path)}else{i=Kp(t,0,"rect",yg);$g(i,{x:0,y:0,width:e.width,height:e.height})}Vp(t,1);return n+1}function kg(t,e,n){e=e.lastChild.previousSibling;let i,r=0;Qd(n,(n=>{i=t.mark(e,n,i);++r}));Vp(e,1+r)}function Mg(t,e,n,i,r){let o=t._svg,a;if(!o){a=e.ownerDocument;o=Jp(a,i,yg);t._svg=o;if(t.mark){o.__data__=t;o.__values__={fill:"default"};if(i==="g"){const e=Jp(a,"path",yg);o.appendChild(e);e.__data__=t;const n=Jp(a,"g",yg);o.appendChild(n);n.__data__=t;const i=Jp(a,"path",yg);o.appendChild(i);i.__data__=t;i.__values__={fill:"default"}}}}if(o.ownerSVGElement!==r||Eg(o,n)){e.insertBefore(o,n?n.nextSibling:e.firstChild)}return o}function Eg(t,e){return t.parentNode&&t.parentNode.childNodes.length>1&&t.previousSibling!=e}let Sg=null,zg=null;const Dg={group(t,e,n){const i=Sg=e.childNodes[2];zg=i.__values__;t.foreground(Rg,n,this);zg=e.__values__;Sg=e.childNodes[1];t.content(Rg,n,this);const r=Sg=e.childNodes[0];t.background(Rg,n,this);const o=n.mark.interactive===false?"none":null;if(o!==zg.events){Og(i,"pointer-events",o);Og(r,"pointer-events",o);zg.events=o}if(n.strokeForeground&&n.stroke){const t=n.fill;Og(i,"display",null);this.style(r,n);Og(r,"stroke",null);if(t)n.fill=null;zg=i.__values__;this.style(i,n);if(t)n.fill=t;Sg=null}else{Og(i,"display","none")}},image(t,e,n){if(n.smooth===false){Ag(e,"image-rendering","optimizeSpeed");Ag(e,"image-rendering","pixelated")}else{Ag(e,"image-rendering",null)}},text(t,e,n){const i=xp(n);let r,o,a,s;if((0,p.kJ)(i)){o=i.map((t=>wp(n,t)));r=o.join("\n");if(r!==zg.text){Vp(e,0);a=e.ownerDocument;s=vp(n);o.forEach(((t,i)=>{const r=Jp(a,"tspan",yg);r.__data__=n;r.textContent=t;if(i){r.setAttribute("x",0);r.setAttribute("dy",s)}e.appendChild(r)}));zg.text=r}}else{o=wp(n,i);if(o!==zg.text){e.textContent=o;zg.text=o}}Og(e,"font-family",Ep(n));Og(e,"font-size",yp(n)+"px");Og(e,"font-style",n.fontStyle);Og(e,"font-variant",n.fontVariant);Og(e,"font-weight",n.fontWeight)}};function Rg(t,e,n){if(e===zg[t])return;if(n){Tg(Sg,t,e,n)}else{Og(Sg,t,e)}zg[t]=e}function Ag(t,e,n){if(n!==zg[e]){if(n==null){t.style.removeProperty(e)}else{t.style.setProperty(e,n+"")}zg[e]=n}}function $g(t,e){for(const n in e){Og(t,n,e[n])}}function Og(t,e,n){if(n!=null){t.setAttribute(e,n)}else{t.removeAttribute(e)}}function Tg(t,e,n,i){if(n!=null){t.setAttributeNS(i,e,n)}else{t.removeAttributeNS(i,e)}}function Cg(){let t;return typeof window==="undefined"?"":(t=window.location).hash?t.href.slice(0,-t.hash.length):t.href}function Ng(t){im.call(this,t);this._text=null;this._defs={gradient:{},clipping:{}}}(0,p.XW)(Ng,im,{svg(){return this._text},_render(t){const e=ug();e.open("svg",(0,p.l7)({},Ih,{class:"marks",width:this._width*this._scale,height:this._height*this._scale,viewBox:`0 0 ${this._width} ${this._height}`}));const n=this._bgcolor;if(n&&n!=="transparent"&&n!=="none"){e.open("rect",{width:this._width,height:this._height,fill:n}).close()}e.open("g",pg,{transform:"translate("+this._origin+")"});this.mark(e,t);e.close();this.defs(e);this._text=e.close()+"";return this},mark(t,e){const n=Pp[e.marktype],i=n.tag,r=[Vm,n.attr];t.open("g",{class:Yp(e),"clip-path":e.clip?od(this,e,e.group):null},Ym(e),{"pointer-events":i!=="g"&&e.interactive===false?"none":null});const o=o=>{const a=this.href(o);if(a)t.open("a",a);t.open(i,this.attr(e,o,r,i!=="g"?i:null));if(i==="text"){const e=xp(o);if((0,p.kJ)(e)){const n={x:0,dy:vp(o)};for(let i=0;i<e.length;++i){t.open("tspan",i?n:null).text(wp(o,e[i])).close()}}else{t.text(wp(o,e))}}else if(i==="g"){const i=o.strokeForeground,r=o.fill,a=o.stroke;if(i&&a){o.stroke=null}t.open("path",this.attr(e,o,n.background,"bgrect")).close();t.open("g",this.attr(e,o,n.content));Qd(o,(e=>this.mark(t,e)));t.close();if(i&&a){if(r)o.fill=null;o.stroke=a;t.open("path",this.attr(e,o,n.foreground,"bgrect")).close();if(r)o.fill=r}else{t.open("path",this.attr(e,o,n.foreground,"bgfore")).close()}}t.close();if(a)t.close()};if(n.nested){if(e.items&&e.items.length)o(e.items[0])}else{Qd(e,o)}return t.close()},href(t){const e=t.href;let n;if(e){if(n=this._hrefs&&this._hrefs[e]){return n}else{this.sanitizeURL(e).then((t=>{t["xlink:href"]=t.href;t.href=null;(this._hrefs||(this._hrefs={}))[e]=t}))}}return null},attr(t,e,n,i){const r={},o=(t,e,n,i)=>{r[i||t]=e};if(Array.isArray(n)){n.forEach((t=>t(o,e,this)))}else{n(o,e,this)}if(i){Ug(r,e,t,i,this._defs)}return r},defs(t){const e=this._defs.gradient,n=this._defs.clipping,i=Object.keys(e).length+Object.keys(n).length;if(i===0)return;t.open("defs");for(const r in e){const n=e[r],i=n.stops;if(n.gradient==="radial"){t.open("pattern",{id:Tf+r,viewBox:"0,0,1,1",width:"100%",height:"100%",preserveAspectRatio:"xMidYMid slice"});t.open("rect",{width:"1",height:"1",fill:"url(#"+r+")"}).close();t.close();t.open("radialGradient",{id:r,fx:n.x1,fy:n.y1,fr:n.r1,cx:n.x2,cy:n.y2,r:n.r2})}else{t.open("linearGradient",{id:r,x1:n.x1,x2:n.x2,y1:n.y1,y2:n.y2})}for(let e=0;e<i.length;++e){t.open("stop",{offset:i[e].offset,"stop-color":i[e].color}).close()}t.close()}for(const r in n){const e=n[r];t.open("clipPath",{id:r});if(e.path){t.open("path",{d:e.path}).close()}else{t.open("rect",{x:0,y:0,width:e.width,height:e.height}).close()}t.close()}t.close()}});function Ug(t,e,n,i,r){let o;if(e==null)return t;if(i==="bgrect"&&n.interactive===false){t["pointer-events"]="none"}if(i==="bgfore"){if(n.interactive===false){t["pointer-events"]="none"}t.display="none";if(e.fill!==null)return t}if(i==="image"&&e.smooth===false){o=["image-rendering: optimizeSpeed;","image-rendering: pixelated;"]}if(i==="text"){t["font-family"]=Ep(e);t["font-size"]=yp(e)+"px";t["font-style"]=e.fontStyle;t["font-variant"]=e.fontVariant;t["font-weight"]=e.fontWeight}for(const a in dg){let n=e[a];const i=dg[a];if(n==="transparent"&&(i==="fill"||i==="stroke"));else if(n!=null){if(Cf(n)){n=Nf(n,r.gradient,"")}t[i]=n}}for(const a in hg){const t=e[a];if(t!=null){o=o||[];o.push(`${hg[a]}: ${t};`)}}if(o){t.style=o.join(" ")}return t}const Ig="canvas";const Pg="png";const qg="svg";const Lg="none";const jg={Canvas:Ig,PNG:Pg,SVG:qg,None:Lg};const Fg={};Fg[Ig]=Fg[Pg]={renderer:Nm,headless:Nm,handler:Sm};Fg[qg]={renderer:vg,headless:Ng,handler:qm};Fg[Lg]={};function Wg(t,e){t=String(t||"").toLowerCase();if(arguments.length>1){Fg[t]=e;return this}else{return Fg[t]}}function Xg(t,e,n){const i=[],r=(new ad).union(e),o=t.marktype;return o?Bg(t,r,n,i):o==="group"?Hg(t,r,n,i):(0,p.vU)("Intersect scene must be mark node or group item.")}function Bg(t,e,n,i){if(Zg(t,e,n)){const r=t.items,o=t.marktype,a=r.length;let s=0;if(o==="group"){for(;s<a;++s){Hg(r[s],e,n,i)}}else{for(const t=Pp[o].isect;s<a;++s){const n=r[s];if(Jg(n,e,t))i.push(n)}}}return i}function Zg(t,e,n){return t.bounds&&e.intersects(t.bounds)&&(t.marktype==="group"||t.interactive!==false&&(!n||n(t)))}function Hg(t,e,n,i){if(n&&n(t.mark)&&Jg(t,e,Pp.group.isect)){i.push(t)}const r=t.items,o=r&&r.length;if(o){const a=t.x||0,s=t.y||0;e.translate(-a,-s);for(let t=0;t<o;++t){Bg(r[t],e,n,i)}e.translate(a,s)}return i}function Jg(t,e,n){const i=t.bounds;return e.encloses(i)||e.intersects(i)&&n(t,e)}const Gg=new ad;function Kg(t){const e=t.clip;if((0,p.mf)(e)){e($d(Gg.clear()))}else if(e){Gg.set(0,0,t.group.width,t.group.height)}else return;t.bounds.intersect(Gg)}const Vg=1e-9;function Yg(t,e,n){return t===e?true:n==="path"?Qg(t,e):t instanceof Date&&e instanceof Date?+t===+e:(0,p.hj)(t)&&(0,p.hj)(e)?Math.abs(t-e)<=Vg:!t||!e||!(0,p.Kn)(t)&&!(0,p.Kn)(e)?t==e:ty(t,e)}function Qg(t,e){return Yg(Bf(t),Bf(e))}function ty(t,e){var n=Object.keys(t),i=Object.keys(e),r,o;if(n.length!==i.length)return false;n.sort();i.sort();for(o=n.length-1;o>=0;o--){if(n[o]!=i[o])return false}for(o=n.length-1;o>=0;o--){r=n[o];if(!Yg(t[r],e[r],r))return false}return typeof t===typeof e}function ey(){rd();Of()}const ny="top";const iy="left";const ry="right";const oy="bottom";const ay="top-left";const sy="top-right";const ly="bottom-left";const uy="bottom-right";const fy="start";const cy="middle";const dy="end";const hy="x";const py="y";const my="group";const gy="axis";const yy="title";const vy="frame";const by="scope";const xy="legend";const _y="row-header";const wy="row-footer";const ky="row-title";const My="column-header";const Ey="column-footer";const Sy="column-title";const zy="padding";const Dy="symbol";const Ry="fit";const Ay="fit-x";const $y="fit-y";const Oy="pad";const Ty="none";const Cy="all";const Ny="each";const Uy="flush";const Iy="column";const Py="row";function qy(t){Oi.call(this,null,t)}(0,p.XW)(qy,Oi,{transform(t,e){const n=e.dataflow,i=t.mark,r=i.marktype,o=Pp[r],a=o.bound;let s=i.bounds,l;if(o.nested){if(i.items.length)n.dirty(i.items[0]);s=Ly(i,a);i.items.forEach((t=>{t.bounds.clear().union(s)}))}else if(r===my||t.modified()){e.visit(e.MOD,(t=>n.dirty(t)));s.clear();i.items.forEach((t=>s.union(Ly(t,a))));switch(i.role){case gy:case xy:case yy:e.reflow()}}else{l=e.changed(e.REM);e.visit(e.ADD,(t=>{s.union(Ly(t,a))}));e.visit(e.MOD,(t=>{l=l||s.alignsWith(t.bounds);n.dirty(t);s.union(Ly(t,a))}));if(l){s.clear();i.items.forEach((t=>s.union(t.bounds)))}}Kg(i);return e.modifies("bounds")}});function Ly(t,e,n){return e(t.bounds.clear(),t,n)}const jy=":vega_identifier:";function Fy(t){Oi.call(this,0,t)}Fy.Definition={type:"Identifier",metadata:{modifies:true},params:[{name:"as",type:"string",required:true}]};(0,p.XW)(Fy,Oi,{transform(t,e){const n=Wy(e.dataflow),i=t.as;let r=n.value;e.visit(e.ADD,(t=>t[i]=t[i]||++r));n.set(this.value=r);return e}});function Wy(t){return t._signals[jy]||(t._signals[jy]=t.add(0))}function Xy(t){Oi.call(this,null,t)}(0,p.XW)(Xy,Oi,{transform(t,e){let n=this.value;if(!n){n=e.dataflow.scenegraph().mark(t.markdef,By(t),t.index);n.group.context=t.context;if(!t.context.group)t.context.group=n.group;n.source=this.source;n.clip=t.clip;n.interactive=t.interactive;this.value=n}const i=n.marktype===my?ld:sd;e.visit(e.ADD,(t=>i.call(t,n)));if(t.modified("clip")||t.modified("interactive")){n.clip=t.clip;n.interactive=!!t.interactive;n.zdirty=true;e.reflow()}n.items=e.source;return e}});function By(t){const e=t.groups,n=t.parent;return e&&e.size===1?e.get(Object.keys(e.object)[0]):e&&n?e.lookup(n):null}function Zy(t){Oi.call(this,null,t)}const Hy={parity:t=>t.filter(((t,e)=>e%2?t.opacity=0:1)),greedy:(t,e)=>{let n;return t.filter(((t,i)=>!i||!Jy(n.bounds,t.bounds,e)?(n=t,1):t.opacity=0))}};const Jy=(t,e,n)=>n>Math.max(e.x1-t.x2,t.x1-e.x2,e.y1-t.y2,t.y1-e.y2);const Gy=(t,e)=>{for(var n=1,i=t.length,r=t[0].bounds,o;n<i;r=o,++n){if(Jy(r,o=t[n].bounds,e))return true}};const Ky=t=>{const e=t.bounds;return e.width()>1&&e.height()>1};const Vy=(t,e,n)=>{var i=t.range(),r=new ad;if(e===ny||e===oy){r.set(i[0],-Infinity,i[1],+Infinity)}else{r.set(-Infinity,i[0],+Infinity,i[1])}r.expand(n||1);return t=>r.encloses(t.bounds)};const Yy=t=>{t.forEach((t=>t.opacity=1));return t};const Qy=(t,e)=>t.reflow(e.modified()).modifies("opacity");(0,p.XW)(Zy,Oi,{transform(t,e){const n=Hy[t.method]||Hy.parity,i=t.separation||0;let r=e.materialize(e.SOURCE).source,o,a;if(!r||!r.length)return;if(!t.method){if(t.modified("method")){Yy(r);e=Qy(e,t)}return e}r=r.filter(Ky);if(!r.length)return;if(t.sort){r=r.slice().sort(t.sort)}o=Yy(r);e=Qy(e,t);if(o.length>=3&&Gy(o,i)){do{o=n(o,i)}while(o.length>=3&&Gy(o,i));if(o.length<3&&!(0,p.fj)(r).opacity){if(o.length>1)(0,p.fj)(o).opacity=0;(0,p.fj)(r).opacity=1}}if(t.boundScale&&t.boundTolerance>=0){a=Vy(t.boundScale,t.boundOrient,+t.boundTolerance);r.forEach((t=>{if(!a(t))t.opacity=0}))}const s=o[0].mark.bounds.clear();r.forEach((t=>{if(t.opacity)s.union(t.bounds)}));return e}});function tv(t){Oi.call(this,null,t)}(0,p.XW)(tv,Oi,{transform(t,e){const n=e.dataflow;e.visit(e.ALL,(t=>n.dirty(t)));if(e.fields&&e.fields["zindex"]){const t=e.source&&e.source[0];if(t)t.mark.zdirty=true}}});const ev=new ad;function nv(t,e,n){return t[e]===n?0:(t[e]=n,1)}function iv(t){var e=t.items[0].orient;return e===iy||e===ry}function rv(t){let e=+t.grid;return[t.ticks?e++:-1,t.labels?e++:-1,e+ +t.domain]}function ov(t,e,n,i){var r=e.items[0],o=r.datum,a=r.translate!=null?r.translate:.5,s=r.orient,l=rv(o),u=r.range,f=r.offset,c=r.position,d=r.minExtent,h=r.maxExtent,p=o.title&&r.items[l[2]].items[0],m=r.titlePadding,g=r.bounds,y=p&&_p(p),v=0,b=0,x,_;ev.clear().union(g);g.clear();if((x=l[0])>-1)g.union(r.items[x].bounds);if((x=l[1])>-1)g.union(r.items[x].bounds);switch(s){case ny:v=c||0;b=-f;_=Math.max(d,Math.min(h,-g.y1));g.add(0,-_).add(u,0);if(p)av(t,p,_,m,y,0,-1,g);break;case iy:v=-f;b=c||0;_=Math.max(d,Math.min(h,-g.x1));g.add(-_,0).add(0,u);if(p)av(t,p,_,m,y,1,-1,g);break;case ry:v=n+f;b=c||0;_=Math.max(d,Math.min(h,g.x2));g.add(0,0).add(_,u);if(p)av(t,p,_,m,y,1,1,g);break;case oy:v=c||0;b=i+f;_=Math.max(d,Math.min(h,g.y2));g.add(0,0).add(u,_);if(p)av(t,p,_,m,0,0,1,g);break;default:v=r.x;b=r.y}dd(g.translate(v,b),r);if(nv(r,"x",v+a)|nv(r,"y",b+a)){r.bounds=ev;t.dirty(r);r.bounds=g;t.dirty(r)}return r.mark.bounds.clear().union(g)}function av(t,e,n,i,r,o,a,s){const l=e.bounds;if(e.auto){const s=a*(n+r+i);let u=0,f=0;t.dirty(e);o?u=(e.x||0)-(e.x=s):f=(e.y||0)-(e.y=s);e.mark.bounds.clear().union(l.translate(-u,-f));t.dirty(e)}s.union(l)}const sv=(t,e)=>Math.floor(Math.min(t,e));const lv=(t,e)=>Math.ceil(Math.max(t,e));function uv(t){var e=t.items,n=e.length,i=0,r,o;const a={marks:[],rowheaders:[],rowfooters:[],colheaders:[],colfooters:[],rowtitle:null,coltitle:null};for(;i<n;++i){r=e[i];o=r.items;if(r.marktype===my){switch(r.role){case gy:case xy:case yy:break;case _y:a.rowheaders.push(...o);break;case wy:a.rowfooters.push(...o);break;case My:a.colheaders.push(...o);break;case Ey:a.colfooters.push(...o);break;case ky:a.rowtitle=o[0];break;case Sy:a.coltitle=o[0];break;default:a.marks.push(...o)}}}return a}function fv(t){return(new ad).set(0,0,t.width||0,t.height||0)}function cv(t){const e=t.bounds.clone();return e.empty()?e.set(0,0,0,0):e.translate(-(t.x||0),-(t.y||0))}function dv(t,e,n){const i=(0,p.Kn)(t)?t[e]:t;return i!=null?i:n!==undefined?n:0}function hv(t){return t<0?Math.ceil(-t):0}function pv(t,e,n){var i=!n.nodirty,r=n.bounds===Uy?fv:cv,o=ev.set(0,0,0,0),a=dv(n.align,Iy),s=dv(n.align,Py),l=dv(n.padding,Iy),u=dv(n.padding,Py),f=n.columns||e.length,c=f<=0?1:Math.ceil(e.length/f),d=e.length,h=Array(d),p=Array(f),m=0,g=Array(d),y=Array(c),v=0,b=Array(d),x=Array(d),_=Array(d),w,k,M,E,S,z,D,R,A,$,O;for(k=0;k<f;++k)p[k]=0;for(k=0;k<c;++k)y[k]=0;for(k=0;k<d;++k){z=e[k];S=_[k]=r(z);z.x=z.x||0;b[k]=0;z.y=z.y||0;x[k]=0;M=k%f;E=~~(k/f);m=Math.max(m,D=Math.ceil(S.x2));v=Math.max(v,R=Math.ceil(S.y2));p[M]=Math.max(p[M],D);y[E]=Math.max(y[E],R);h[k]=l+hv(S.x1);g[k]=u+hv(S.y1);if(i)t.dirty(e[k])}for(k=0;k<d;++k){if(k%f===0)h[k]=0;if(k<f)g[k]=0}if(a===Ny){for(M=1;M<f;++M){for(O=0,k=M;k<d;k+=f){if(O<h[k])O=h[k]}for(k=M;k<d;k+=f){h[k]=O+p[M-1]}}}else if(a===Cy){for(O=0,k=0;k<d;++k){if(k%f&&O<h[k])O=h[k]}for(k=0;k<d;++k){if(k%f)h[k]=O+m}}else{for(a=false,M=1;M<f;++M){for(k=M;k<d;k+=f){h[k]+=p[M-1]}}}if(s===Ny){for(E=1;E<c;++E){for(O=0,k=E*f,w=k+f;k<w;++k){if(O<g[k])O=g[k]}for(k=E*f;k<w;++k){g[k]=O+y[E-1]}}}else if(s===Cy){for(O=0,k=f;k<d;++k){if(O<g[k])O=g[k]}for(k=f;k<d;++k){g[k]=O+v}}else{for(s=false,E=1;E<c;++E){for(k=E*f,w=k+f;k<w;++k){g[k]+=y[E-1]}}}for(A=0,k=0;k<d;++k){A=h[k]+(k%f?A:0);b[k]+=A-e[k].x}for(M=0;M<f;++M){for($=0,k=M;k<d;k+=f){$+=g[k];x[k]+=$-e[k].y}}if(a&&dv(n.center,Iy)&&c>1){for(k=0;k<d;++k){S=a===Cy?m:p[k%f];A=S-_[k].x2-e[k].x-b[k];if(A>0)b[k]+=A/2}}if(s&&dv(n.center,Py)&&f!==1){for(k=0;k<d;++k){S=s===Cy?v:y[~~(k/f)];$=S-_[k].y2-e[k].y-x[k];if($>0)x[k]+=$/2}}for(k=0;k<d;++k){o.union(_[k].translate(b[k],x[k]))}A=dv(n.anchor,hy);$=dv(n.anchor,py);switch(dv(n.anchor,Iy)){case dy:A-=o.width();break;case cy:A-=o.width()/2}switch(dv(n.anchor,Py)){case dy:$-=o.height();break;case cy:$-=o.height()/2}A=Math.round(A);$=Math.round($);o.clear();for(k=0;k<d;++k){e[k].mark.bounds.clear()}for(k=0;k<d;++k){z=e[k];z.x+=b[k]+=A;z.y+=x[k]+=$;o.union(z.mark.bounds.union(z.bounds.translate(b[k],x[k])));if(i)t.dirty(z)}return o}function mv(t,e,n){var i=uv(e),r=i.marks,o=n.bounds===Uy?gv:yv,a=n.offset,s=n.columns||r.length,l=s<=0?1:Math.ceil(r.length/s),u=l*s,f,c,d,h,p,m,g;const y=pv(t,r,n);if(y.empty())y.set(0,0,0,0);if(i.rowheaders){m=dv(n.headerBand,Py,null);f=vv(t,i.rowheaders,r,s,l,-dv(a,"rowHeader"),sv,0,o,"x1",0,s,1,m)}if(i.colheaders){m=dv(n.headerBand,Iy,null);c=vv(t,i.colheaders,r,s,s,-dv(a,"columnHeader"),sv,1,o,"y1",0,1,s,m)}if(i.rowfooters){m=dv(n.footerBand,Py,null);d=vv(t,i.rowfooters,r,s,l,dv(a,"rowFooter"),lv,0,o,"x2",s-1,s,1,m)}if(i.colfooters){m=dv(n.footerBand,Iy,null);h=vv(t,i.colfooters,r,s,s,dv(a,"columnFooter"),lv,1,o,"y2",u-s,1,s,m)}if(i.rowtitle){p=dv(n.titleAnchor,Py);g=dv(a,"rowTitle");g=p===dy?d+g:f-g;m=dv(n.titleBand,Py,.5);bv(t,i.rowtitle,g,0,y,m)}if(i.coltitle){p=dv(n.titleAnchor,Iy);g=dv(a,"columnTitle");g=p===dy?h+g:c-g;m=dv(n.titleBand,Iy,.5);bv(t,i.coltitle,g,1,y,m)}}function gv(t,e){return e==="x1"?t.x||0:e==="y1"?t.y||0:e==="x2"?(t.x||0)+(t.width||0):e==="y2"?(t.y||0)+(t.height||0):undefined}function yv(t,e){return t.bounds[e]}function vv(t,e,n,i,r,o,a,s,l,u,f,c,d,h){var p=n.length,m=0,g=0,y,v,b,x,_,w,k,M,E;if(!p)return m;for(y=f;y<p;y+=c){if(n[y])m=a(m,l(n[y],u))}if(!e.length)return m;if(e.length>r){t.warn("Grid headers exceed limit: "+r);e=e.slice(0,r)}m+=o;for(v=0,x=e.length;v<x;++v){t.dirty(e[v]);e[v].mark.bounds.clear()}for(y=f,v=0,x=e.length;v<x;++v,y+=c){w=e[v];_=w.mark.bounds;for(b=y;b>=0&&(k=n[b])==null;b-=d);if(s){M=h==null?k.x:Math.round(k.bounds.x1+h*k.bounds.width());E=m}else{M=m;E=h==null?k.y:Math.round(k.bounds.y1+h*k.bounds.height())}_.union(w.bounds.translate(M-(w.x||0),E-(w.y||0)));w.x=M;w.y=E;t.dirty(w);g=a(g,_[u])}return g}function bv(t,e,n,i,r,o){if(!e)return;t.dirty(e);var a=n,s=n;i?a=Math.round(r.x1+o*r.width()):s=Math.round(r.y1+o*r.height());e.bounds.translate(a-(e.x||0),s-(e.y||0));e.mark.bounds.clear().union(e.bounds);e.x=a;e.y=s;t.dirty(e)}function xv(t,e){const n=t[e]||{};return(e,i)=>n[e]!=null?n[e]:t[e]!=null?t[e]:i}function _v(t,e){let n=-Infinity;t.forEach((t=>{if(t.offset!=null)n=Math.max(n,t.offset)}));return n>-Infinity?n:e}function wv(t,e,n,i,r,o,a){const s=xv(n,e),l=_v(t,s("offset",0)),u=s("anchor",fy),f=u===dy?1:u===cy?.5:0;const c={align:Ny,bounds:s("bounds",Uy),columns:s("direction")==="vertical"?1:t.length,padding:s("margin",8),center:s("center"),nodirty:true};switch(e){case iy:c.anchor={x:Math.floor(i.x1)-l,column:dy,y:f*(a||i.height()+2*i.y1),row:u};break;case ry:c.anchor={x:Math.ceil(i.x2)+l,y:f*(a||i.height()+2*i.y1),row:u};break;case ny:c.anchor={y:Math.floor(r.y1)-l,row:dy,x:f*(o||r.width()+2*r.x1),column:u};break;case oy:c.anchor={y:Math.ceil(r.y2)+l,x:f*(o||r.width()+2*r.x1),column:u};break;case ay:c.anchor={x:l,y:l};break;case sy:c.anchor={x:o-l,y:l,column:dy};break;case ly:c.anchor={x:l,y:a-l,row:dy};break;case uy:c.anchor={x:o-l,y:a-l,column:dy,row:dy};break}return c}function kv(t,e){var n=e.items[0],i=n.datum,r=n.orient,o=n.bounds,a=n.x,s=n.y,l,u;n._bounds?n._bounds.clear().union(o):n._bounds=o.clone();o.clear();Ev(t,n,n.items[0].items[0]);o=Mv(n,o);l=2*n.padding;u=2*n.padding;if(!o.empty()){l=Math.ceil(o.width()+l);u=Math.ceil(o.height()+u)}if(i.type===Dy){Dv(n.items[0].items[0].items[0].items)}if(r!==Ty){n.x=a=0;n.y=s=0}n.width=l;n.height=u;dd(o.set(a,s,a+l,s+u),n);n.mark.bounds.clear().union(o);return n}function Mv(t,e){t.items.forEach((t=>e.union(t.bounds)));e.x1=t.padding;e.y1=t.padding;return e}function Ev(t,e,n){var i=e.padding,r=i-n.x,o=i-n.y;if(!e.datum.title){if(r||o)zv(t,n,r,o)}else{var a=e.items[1].items[0],s=a.anchor,l=e.titlePadding||0,u=i-a.x,f=i-a.y;switch(a.orient){case iy:r+=Math.ceil(a.bounds.width())+l;break;case ry:case oy:break;default:o+=a.bounds.height()+l}if(r||o)zv(t,n,r,o);switch(a.orient){case iy:f+=Sv(e,n,a,s,1,1);break;case ry:u+=Sv(e,n,a,dy,0,0)+l;f+=Sv(e,n,a,s,1,1);break;case oy:u+=Sv(e,n,a,s,0,0);f+=Sv(e,n,a,dy,-1,0,1)+l;break;default:u+=Sv(e,n,a,s,0,0)}if(u||f)zv(t,a,u,f);if((u=Math.round(a.bounds.x1-i))<0){zv(t,n,-u,0);zv(t,a,-u,0)}}}function Sv(t,e,n,i,r,o,a){const s=t.datum.type!=="symbol",l=n.datum.vgrad,u=s&&(o||!l)&&!a?e.items[0]:e,f=u.bounds[r?"y2":"x2"]-t.padding,c=l&&o?f:0,d=l&&o?0:f,h=r<=0?0:_p(n);return Math.round(i===fy?c:i===dy?d-h:.5*(f-h))}function zv(t,e,n,i){e.x+=n;e.y+=i;e.bounds.translate(n,i);e.mark.bounds.translate(n,i);t.dirty(e)}function Dv(t){const e=t.reduce(((t,e)=>{t[e.column]=Math.max(e.bounds.x2-e.x,t[e.column]||0);return t}),{});t.forEach((t=>{t.width=e[t.column];t.height=t.bounds.y2-t.y}))}function Rv(t,e,n,i,r){var o=e.items[0],a=o.frame,s=o.orient,l=o.anchor,u=o.offset,f=o.padding,c=o.items[0].items[0],d=o.items[1]&&o.items[1].items[0],h=s===iy||s===ry?i:n,p=0,m=0,g=0,y=0,v=0,b;if(a!==my){s===iy?(p=r.y2,h=r.y1):s===ry?(p=r.y1,h=r.y2):(p=r.x1,h=r.x2)}else if(s===iy){p=i,h=0}b=l===fy?p:l===dy?h:(p+h)/2;if(d&&d.text){switch(s){case ny:case oy:v=c.bounds.height()+f;break;case iy:y=c.bounds.width()+f;break;case ry:y=-c.bounds.width()-f;break}ev.clear().union(d.bounds);ev.translate(y-(d.x||0),v-(d.y||0));if(nv(d,"x",y)|nv(d,"y",v)){t.dirty(d);d.bounds.clear().union(ev);d.mark.bounds.clear().union(ev);t.dirty(d)}ev.clear().union(d.bounds)}else{ev.clear()}ev.union(c.bounds);switch(s){case ny:m=b;g=r.y1-ev.height()-u;break;case iy:m=r.x1-ev.width()-u;g=b;break;case ry:m=r.x2+ev.width()+u;g=b;break;case oy:m=b;g=r.y2+u;break;default:m=o.x;g=o.y}if(nv(o,"x",m)|nv(o,"y",g)){ev.translate(m,g);t.dirty(o);o.bounds.clear().union(ev);e.bounds.clear().union(ev);t.dirty(o)}return o.bounds}function Av(t){Oi.call(this,null,t)}(0,p.XW)(Av,Oi,{transform(t,e){const n=e.dataflow;t.mark.items.forEach((e=>{if(t.layout)mv(n,e,t.layout);Ov(n,e,t)}));return $v(t.mark.group)?e.reflow():e}});function $v(t){return t&&t.mark.role!=="legend-entry"}function Ov(t,e,n){var i=e.items,r=Math.max(0,e.width||0),o=Math.max(0,e.height||0),a=(new ad).set(0,0,r,o),s=a.clone(),l=a.clone(),u=[],f,c,d,h,p,m;for(p=0,m=i.length;p<m;++p){c=i[p];switch(c.role){case gy:h=iv(c)?s:l;h.union(ov(t,c,r,o));break;case yy:f=c;break;case xy:u.push(kv(t,c));break;case vy:case by:case _y:case wy:case ky:case My:case Ey:case Sy:s.union(c.bounds);l.union(c.bounds);break;default:a.union(c.bounds)}}if(u.length){const e={};u.forEach((t=>{d=t.orient||ry;if(d!==Ty)(e[d]||(e[d]=[])).push(t)}));for(const i in e){const a=e[i];pv(t,a,wv(a,i,n.legends,s,l,r,o))}u.forEach((e=>{const i=e.bounds;if(!i.equals(e._bounds)){e.bounds=e._bounds;t.dirty(e);e.bounds=i;t.dirty(e)}if(n.autosize&&(n.autosize.type===Ry||n.autosize.type===Ay||n.autosize.type===$y)){switch(e.orient){case iy:case ry:a.add(i.x1,0).add(i.x2,0);break;case ny:case oy:a.add(0,i.y1).add(0,i.y2)}}else{a.union(i)}}))}a.union(s).union(l);if(f){a.union(Rv(t,f,r,o,a))}if(e.clip){a.set(0,0,e.width||0,e.height||0)}Tv(t,e,a,n)}function Tv(t,e,n,i){const r=i.autosize||{},o=r.type;if(t._autosize<1||!o)return;let a=t._width,s=t._height,l=Math.max(0,e.width||0),u=Math.max(0,Math.ceil(-n.x1)),f=Math.max(0,e.height||0),c=Math.max(0,Math.ceil(-n.y1));const d=Math.max(0,Math.ceil(n.x2-l)),h=Math.max(0,Math.ceil(n.y2-f));if(r.contains===zy){const e=t.padding();a-=e.left+e.right;s-=e.top+e.bottom}if(o===Ty){u=0;c=0;l=a;f=s}else if(o===Ry){l=Math.max(0,a-u-d);f=Math.max(0,s-c-h)}else if(o===Ay){l=Math.max(0,a-u-d);s=f+c+h}else if(o===$y){a=l+u+d;f=Math.max(0,s-c-h)}else if(o===Oy){a=l+u+d;s=f+c+h}t._resizeView(a,s,l,f,[u,c],r.resize)}function Cv(t,e){let n=0;if(e===undefined){for(let e of t){if(e=+e){n+=e}}}else{let i=-1;for(let r of t){if(r=+e(r,++i,t)){n+=r}}}return n}function Nv(t){Oi.call(this,null,t)}(0,p.XW)(Nv,Oi,{transform(t,e){if(this.value&&!t.modified()){return e.StopPropagation}var n=e.dataflow.locale(),i=e.fork(e.NO_SOURCE|e.NO_FIELDS),r=this.value,o=t.scale,a=t.count==null?t.values?t.values.length:10:t.count,s=ff(o,a,t.minstep),l=t.format||hf(n,o,s,t.formatSpecifier,t.formatType,!!t.values),u=t.values?cf(o,t.values,s):df(o,s);if(r)i.rem=r;r=u.map(((t,e)=>bn({index:e/(u.length-1||1),value:t,label:l(t)})));if(t.extra&&r.length){r.push(bn({index:-1,extra:{value:r[0].value},label:""}))}i.source=r;i.add=r;this.value=r;return i}});function Uv(t){Oi.call(this,null,t)}function Iv(){return bn({})}function Pv(t){const e=(0,p.Xr)().test((t=>t.exit));e.lookup=n=>e.get(t(n));return e}(0,p.XW)(Uv,Oi,{transform(t,e){var n=e.dataflow,i=e.fork(e.NO_SOURCE|e.NO_FIELDS),r=t.item||Iv,o=t.key||yn,a=this.value;if((0,p.kJ)(i.encode)){i.encode=null}if(a&&(t.modified("key")||e.modified(o))){(0,p.vU)("DataJoin does not support modified key function or fields.")}if(!a){e=e.addAll();this.value=a=Pv(o)}e.visit(e.ADD,(t=>{const e=o(t);let n=a.get(e);if(n){if(n.exit){a.empty--;i.add.push(n)}else{i.mod.push(n)}}else{n=r(t);a.set(e,n);i.add.push(n)}n.datum=t;n.exit=false}));e.visit(e.MOD,(t=>{const e=o(t),n=a.get(e);if(n){n.datum=t;i.mod.push(n)}}));e.visit(e.REM,(t=>{const e=o(t),n=a.get(e);if(t===n.datum&&!n.exit){i.rem.push(n);n.exit=true;++a.empty}}));if(e.changed(e.ADD_MOD))i.modifies("datum");if(e.clean()||t.clean&&a.empty>n.cleanThreshold){n.runAfter(a.clean)}return i}});function qv(t){Oi.call(this,null,t)}(0,p.XW)(qv,Oi,{transform(t,e){var n=e.fork(e.ADD_REM),i=t.mod||false,r=t.encoders,o=e.encode;if((0,p.kJ)(o)){if(n.changed()||o.every((t=>r[t]))){o=o[0];n.encode=null}else{return e.StopPropagation}}var a=o==="enter",s=r.update||p.k,l=r.enter||p.k,u=r.exit||p.k,f=(o&&!a?r[o]:s)||p.k;if(e.changed(e.ADD)){e.visit(e.ADD,(e=>{l(e,t);s(e,t)}));n.modifies(l.output);n.modifies(s.output);if(f!==p.k&&f!==s){e.visit(e.ADD,(e=>{f(e,t)}));n.modifies(f.output)}}if(e.changed(e.REM)&&u!==p.k){e.visit(e.REM,(e=>{u(e,t)}));n.modifies(u.output)}if(a||f!==p.k){const r=e.MOD|(t.modified()?e.REFLOW:0);if(a){e.visit(r,(e=>{const r=l(e,t)||i;if(f(e,t)||r)n.mod.push(e)}));if(n.mod.length)n.modifies(l.output)}else{e.visit(r,(e=>{if(f(e,t)||i)n.mod.push(e)}))}if(n.mod.length)n.modifies(f.output)}return n.changed()?n:e.StopPropagation}});function Lv(t){Oi.call(this,[],t)}(0,p.XW)(Lv,Oi,{transform(t,e){if(this.value!=null&&!t.modified()){return e.StopPropagation}var n=e.dataflow.locale(),i=e.fork(e.NO_SOURCE|e.NO_FIELDS),r=this.value,o=t.type||rf,a=t.scale,s=+t.limit,l=ff(a,t.count==null?5:t.count,t.minstep),u=!!t.values||o===rf,f=t.format||wf(n,a,l,o,t.formatSpecifier,t.formatType,u),c=t.values||yf(a,l),d,h,m,g,y;if(r)i.rem=r;if(o===rf){if(s&&c.length>s){e.dataflow.warn("Symbol legend count exceeds limit, filtering items.");r=c.slice(0,s-1);y=true}else{r=c}if((0,p.mf)(m=t.size)){if(!t.values&&a(r[0])===0){r=r.slice(1)}g=r.reduce(((e,n)=>Math.max(e,m(n,t))),0)}else{m=(0,p.a9)(g=m||8)}r=r.map(((e,n)=>bn({index:n,label:f(e,n,r),value:e,offset:g,size:m(e,t)})));if(y){y=c[r.length];r.push(bn({index:r.length,label:`…${c.length-r.length} entries`,value:y,offset:g,size:m(y,t)}))}}else if(o===af){d=a.domain(),h=Ju(a,d[0],(0,p.fj)(d));if(c.length<3&&!t.values&&d[0]!==(0,p.fj)(d)){c=[d[0],(0,p.fj)(d)]}r=c.map(((t,e)=>bn({index:e,label:f(t,e,c),value:t,perc:h(t)})))}else{m=c.length-1;h=Df(a);r=c.map(((t,e)=>bn({index:e,label:f(t,e,c),value:t,perc:e?h(t):0,perc2:e===m?1:h(c[e+1])})))}i.source=r;i.add=r;this.value=r;return i}});const jv=t=>t.source.x;const Fv=t=>t.source.y;const Wv=t=>t.target.x;const Xv=t=>t.target.y;function Bv(t){Oi.call(this,{},t)}Bv.Definition={type:"LinkPath",metadata:{modifies:true},params:[{name:"sourceX",type:"field",default:"source.x"},{name:"sourceY",type:"field",default:"source.y"},{name:"targetX",type:"field",default:"target.x"},{name:"targetY",type:"field",default:"target.y"},{name:"orient",type:"enum",default:"vertical",values:["horizontal","vertical","radial"]},{name:"shape",type:"enum",default:"line",values:["line","arc","curve","diagonal","orthogonal"]},{name:"require",type:"signal"},{name:"as",type:"string",default:"path"}]};(0,p.XW)(Bv,Oi,{transform(t,e){var n=t.sourceX||jv,i=t.sourceY||Fv,r=t.targetX||Wv,o=t.targetY||Xv,a=t.as||"path",s=t.orient||"vertical",l=t.shape||"line",u=rb.get(l+"-"+s)||rb.get(l);if(!u){(0,p.vU)("LinkPath unsupported type: "+t.shape+(t.orient?"-"+t.orient:""))}e.visit(e.SOURCE,(t=>{t[a]=u(n(t),i(t),r(t),o(t))}));return e.reflow(t.modified()).modifies(a)}});const Zv=(t,e,n,i)=>"M"+t+","+e+"L"+n+","+i;const Hv=(t,e,n,i)=>Zv(e*Math.cos(t),e*Math.sin(t),i*Math.cos(n),i*Math.sin(n));const Jv=(t,e,n,i)=>{var r=n-t,o=i-e,a=Math.sqrt(r*r+o*o)/2,s=180*Math.atan2(o,r)/Math.PI;return"M"+t+","+e+"A"+a+","+a+" "+s+" 0 1"+" "+n+","+i};const Gv=(t,e,n,i)=>Jv(e*Math.cos(t),e*Math.sin(t),i*Math.cos(n),i*Math.sin(n));const Kv=(t,e,n,i)=>{const r=n-t,o=i-e,a=.2*(r+o),s=.2*(o-r);return"M"+t+","+e+"C"+(t+a)+","+(e+s)+" "+(n+s)+","+(i-a)+" "+n+","+i};const Vv=(t,e,n,i)=>Kv(e*Math.cos(t),e*Math.sin(t),i*Math.cos(n),i*Math.sin(n));const Yv=(t,e,n,i)=>"M"+t+","+e+"V"+i+"H"+n;const Qv=(t,e,n,i)=>"M"+t+","+e+"H"+n+"V"+i;const tb=(t,e,n,i)=>{const r=Math.cos(t),o=Math.sin(t),a=Math.cos(n),s=Math.sin(n),l=Math.abs(n-t)>Math.PI?n<=t:n>t;return"M"+e*r+","+e*o+"A"+e+","+e+" 0 0,"+(l?1:0)+" "+e*a+","+e*s+"L"+i*a+","+i*s};const eb=(t,e,n,i)=>{const r=(t+n)/2;return"M"+t+","+e+"C"+r+","+e+" "+r+","+i+" "+n+","+i};const nb=(t,e,n,i)=>{const r=(e+i)/2;return"M"+t+","+e+"C"+t+","+r+" "+n+","+r+" "+n+","+i};const ib=(t,e,n,i)=>{const r=Math.cos(t),o=Math.sin(t),a=Math.cos(n),s=Math.sin(n),l=(e+i)/2;return"M"+e*r+","+e*o+"C"+l*r+","+l*o+" "+l*a+","+l*s+" "+i*a+","+i*s};const rb=(0,p.Xr)({line:Zv,"line-radial":Hv,arc:Jv,"arc-radial":Gv,curve:Kv,"curve-radial":Vv,"orthogonal-horizontal":Yv,"orthogonal-vertical":Qv,"orthogonal-radial":tb,"diagonal-horizontal":eb,"diagonal-vertical":nb,"diagonal-radial":ib});function ob(t){Oi.call(this,null,t)}ob.Definition={type:"Pie",metadata:{modifies:true},params:[{name:"field",type:"field"},{name:"startAngle",type:"number",default:0},{name:"endAngle",type:"number",default:6.283185307179586},{name:"sort",type:"boolean",default:false},{name:"as",type:"string",array:true,length:2,default:["startAngle","endAngle"]}]};(0,p.XW)(ob,Oi,{transform(t,e){var n=t.as||["startAngle","endAngle"],i=n[0],r=n[1],o=t.field||p.kX,a=t.startAngle||0,s=t.endAngle!=null?t.endAngle:2*Math.PI,l=e.source,u=l.map(o),f=u.length,c=a,d=(s-a)/Cv(u),h=(0,to.Z)(f),m,g,y;if(t.sort){h.sort(((t,e)=>u[t]-u[e]))}for(m=0;m<f;++m){y=u[h[m]];g=l[h[m]];g[i]=c;g[r]=c+=y*d}this.value=u;return e.reflow(t.modified()).modifies(n)}});const ab=5;function sb(t){const e=t.type;return!t.bins&&(e===Yl||e===tu||e===eu)}function lb(t){return Uu(t)&&t!==ou}const ub=(0,p.Rg)(["set","modified","clear","type","scheme","schemeExtent","schemeCount","domain","domainMin","domainMid","domainMax","domainRaw","domainImplicit","nice","zero","bins","range","rangeStep","round","reverse","interpolate","interpolateGamma"]);function fb(t){Oi.call(this,null,t);this.modified(true)}(0,p.XW)(fb,Oi,{transform(t,e){var n=e.dataflow,i=this.value,r=cb(t);if(!i||r!==i.type){this.value=i=Tu(r)()}for(r in t)if(!ub[r]){if(r==="padding"&&lb(i.type))continue;(0,p.mf)(i[r])?i[r](t[r]):n.warn("Unsupported scale property: "+r)}vb(i,t,yb(i,t,hb(i,t,n)));return e.fork(e.NO_SOURCE|e.NO_FIELDS)}});function cb(t){var e=t.type,n="",i;if(e===ou)return ou+"-"+Yl;if(db(t)){i=t.rawDomain?t.rawDomain.length:t.domain?t.domain.length+ +(t.domainMid!=null):0;n=i===2?ou+"-":i===3?au+"-":""}return(n+e||Yl).toLowerCase()}function db(t){const e=t.type;return Uu(e)&&e!==iu&&e!==ru&&(t.scheme||t.range&&t.range.length&&t.range.every(p.HD))}function hb(t,e,n){const i=pb(t,e.domainRaw,n);if(i>-1)return i;var r=e.domain,o=t.type,a=e.zero||e.zero===undefined&&sb(t),s,l;if(!r)return 0;if(lb(o)&&e.padding&&r[0]!==(0,p.fj)(r)){r=mb(o,r,e.range,e.padding,e.exponent,e.constant)}if(a||e.domainMin!=null||e.domainMax!=null||e.domainMid!=null){s=(r=r.slice()).length-1||1;if(a){if(r[0]>0)r[0]=0;if(r[s]<0)r[s]=0}if(e.domainMin!=null)r[0]=e.domainMin;if(e.domainMax!=null)r[s]=e.domainMax;if(e.domainMid!=null){l=e.domainMid;const t=l>r[s]?s+1:l<r[0]?0:s;if(t!==s)n.warn("Scale domainMid exceeds domain min or max.",l);r.splice(t,0,l)}}t.domain(gb(o,r,n));if(o===fu){t.unknown(e.domainImplicit?Hl.O:undefined)}if(e.nice&&t.nice){t.nice(e.nice!==true&&ff(t,e.nice)||null)}return r.length}function pb(t,e,n){if(e){t.domain(gb(t.type,e,n));return e.length}else{return-1}}function mb(t,e,n,i,r,o){var a=Math.abs((0,p.fj)(n)-n[0]),s=a/(a-2*i),l=t===Ql?(0,p.dH)(e,null,s):t===eu?(0,p.mK)(e,null,s,.5):t===tu?(0,p.mK)(e,null,s,r||1):t===nu?(0,p.bV)(e,null,s,o||1):(0,p.ay)(e,null,s);e=e.slice();e[0]=l[0];e[e.length-1]=l[1];return e}function gb(t,e,n){if(qu(t)){var i=Math.abs(e.reduce(((t,e)=>t+(e<0?-1:e>0?1:0)),0));if(i!==e.length){n.warn("Log scale domain includes zero: "+(0,p.m8)(e))}}return e}function yb(t,e,n){let i=e.bins;if(i&&!(0,p.kJ)(i)){const e=t.domain(),n=e[0],r=(0,p.fj)(e),o=i.step;let a=i.start==null?n:i.start,s=i.stop==null?r:i.stop;if(!o)(0,p.vU)("Scale bins parameter missing step property.");if(a<n)a=o*Math.ceil(n/o);if(s>r)s=o*Math.floor(r/o);i=(0,to.Z)(a,s+o/2,o)}if(i){t.bins=i}else if(t.bins){delete t.bins}if(t.type===hu){if(!i){t.bins=t.domain()}else if(!e.domain&&!e.domainRaw){t.domain(i);n=i.length}}return n}function vb(t,e,n){var i=t.type,r=e.round||false,o=e.range;if(e.rangeStep!=null){o=bb(i,e,n)}else if(e.scheme){o=xb(i,e,n);if((0,p.mf)(o)){if(t.interpolator){return t.interpolator(o)}else{(0,p.vU)(`Scale type ${i} does not support interpolating color schemes.`)}}}if(o&&ju(i)){return t.interpolator(Bu(wb(o,e.reverse),e.interpolate,e.interpolateGamma))}if(o&&e.interpolate&&t.interpolate){t.interpolate(Gu(e.interpolate,e.interpolateGamma))}else if((0,p.mf)(t.round)){t.round(r)}else if((0,p.mf)(t.rangeRound)){t.interpolate(r?Rl.Z:Dl.Z)}if(o)t.range(wb(o,e.reverse))}function bb(t,e,n){if(t!==du&&t!==cu){(0,p.vU)("Only band and point scales support rangeStep.")}var i=(e.paddingOuter!=null?e.paddingOuter:e.padding)||0,r=t===cu?1:(e.paddingInner!=null?e.paddingInner:e.padding)||0;return[0,e.rangeStep*Kl(n,r,i)]}function xb(t,e,n){var i=e.schemeExtent,r,o;if((0,p.kJ)(e.scheme)){o=Bu(e.scheme,e.interpolate,e.interpolateGamma)}else{r=e.scheme.toLowerCase();o=nf(r);if(!o)(0,p.vU)(`Unrecognized scheme name: ${e.scheme}`)}n=t===uu?n+1:t===hu?n-1:t===su||t===lu?+e.schemeCount||ab:n;return ju(t)?_b(o,i,e.reverse):(0,p.mf)(o)?Zu(_b(o,i),n):t===fu?o:o.slice(0,n)}function _b(t,e,n){return(0,p.mf)(t)&&(e||n)?Xu(t,wb(e||[0,1],n)):t}function wb(t,e){return e?t.slice().reverse():t}function kb(t){Oi.call(this,null,t)}(0,p.XW)(kb,Oi,{transform(t,e){const n=t.modified("sort")||e.changed(e.ADD)||e.modified(t.sort.fields)||e.modified("datum");if(n)e.source.sort(kn(t.sort));this.modified(n);return e}});const Mb="zero",Eb="center",Sb="normalize",zb=["y0","y1"];function Db(t){Oi.call(this,null,t)}Db.Definition={type:"Stack",metadata:{modifies:true},params:[{name:"field",type:"field"},{name:"groupby",type:"field",array:true},{name:"sort",type:"compare"},{name:"offset",type:"enum",default:Mb,values:[Mb,Eb,Sb]},{name:"as",type:"string",array:true,length:2,default:zb}]};(0,p.XW)(Db,Oi,{transform(t,e){var n=t.as||zb,i=n[0],r=n[1],o=kn(t.sort),a=t.field||p.kX,s=t.offset===Eb?Rb:t.offset===Sb?Ab:$b,l,u,f,c;l=Ob(e.source,t.groupby,o,a);for(u=0,f=l.length,c=l.max;u<f;++u){s(l[u],c,a,i,r)}return e.reflow(t.modified()).modifies(n)}});function Rb(t,e,n,i,r){var o=(e-t.sum)/2,a=t.length,s=0,l;for(;s<a;++s){l=t[s];l[i]=o;l[r]=o+=Math.abs(n(l))}}function Ab(t,e,n,i,r){var o=1/t.sum,a=0,s=t.length,l=0,u=0,f;for(;l<s;++l){f=t[l];f[i]=a;f[r]=a=o*(u+=Math.abs(n(f)))}}function $b(t,e,n,i,r){var o=0,a=0,s=t.length,l=0,u,f;for(;l<s;++l){f=t[l];u=+n(f);if(u<0){f[i]=a;f[r]=a+=u}else{f[i]=o;f[r]=o+=u}}}function Ob(t,e,n,i){var r=[],o=t=>t(f),a,s,l,u,f,c,d,h,p;if(e==null){r.push(t.slice())}else{for(a={},s=0,l=t.length;s<l;++s){f=t[s];c=e.map(o);d=a[c];if(!d){a[c]=d=[];r.push(d)}d.push(f)}}for(c=0,p=0,u=r.length;c<u;++c){d=r[c];for(s=0,h=0,l=d.length;s<l;++s){h+=Math.abs(i(d[s]))}d.sum=h;if(h>p)p=h;if(n)d.sort(n)}r.max=p;return r}const Tb=t=>t;function Cb(t,e){if(t&&Ub.hasOwnProperty(t.type)){Ub[t.type](t,e)}}var Nb={Feature:function(t,e){Cb(t.geometry,e)},FeatureCollection:function(t,e){var n=t.features,i=-1,r=n.length;while(++i<r)Cb(n[i].geometry,e)}};var Ub={Sphere:function(t,e){e.sphere()},Point:function(t,e){t=t.coordinates;e.point(t[0],t[1],t[2])},MultiPoint:function(t,e){var n=t.coordinates,i=-1,r=n.length;while(++i<r)t=n[i],e.point(t[0],t[1],t[2])},LineString:function(t,e){Ib(t.coordinates,e,0)},MultiLineString:function(t,e){var n=t.coordinates,i=-1,r=n.length;while(++i<r)Ib(n[i],e,0)},Polygon:function(t,e){Pb(t.coordinates,e)},MultiPolygon:function(t,e){var n=t.coordinates,i=-1,r=n.length;while(++i<r)Pb(n[i],e)},GeometryCollection:function(t,e){var n=t.geometries,i=-1,r=n.length;while(++i<r)Cb(n[i],e)}};function Ib(t,e,n){var i=-1,r=t.length-n,o;e.lineStart();while(++i<r)o=t[i],e.point(o[0],o[1],o[2]);e.lineEnd()}function Pb(t,e){var n=-1,i=t.length;e.polygonStart();while(++n<i)Ib(t[n],e,1);e.polygonEnd()}function qb(t,e){if(t&&Nb.hasOwnProperty(t.type)){Nb[t.type](t,e)}else{Cb(t,e)}}class Lb{constructor(){this._partials=new Float64Array(32);this._n=0}add(t){const e=this._partials;let n=0;for(let i=0;i<this._n&&i<32;i++){const r=e[i],o=t+r,a=Math.abs(t)<Math.abs(r)?t-(o-r):r-(o-t);if(a)e[n++]=a;t=o}e[n]=t;this._n=n+1;return this}valueOf(){const t=this._partials;let e=this._n,n,i,r,o=0;if(e>0){o=t[--e];while(e>0){n=o;i=t[--e];o=n+i;r=i-(o-n);if(r)break}if(e>0&&(r<0&&t[e-1]<0||r>0&&t[e-1]>0)){i=r*2;n=o+i;if(i==n-o)o=n}}return o}}function jb(t,e){const n=new Lb;if(e===undefined){for(let e of t){if(e=+e){n.add(e)}}}else{let i=-1;for(let r of t){if(r=+e(r,++i,t)){n.add(r)}}}return+n}function Fb(t,e){const n=new Lb;let i=-1;return Float64Array.from(t,e===undefined?t=>n.add(+t||0):r=>n.add(+e(r,++i,t)||0))}var Wb=1e-6;var Xb=1e-12;var Bb=Math.PI;var Zb=Bb/2;var Hb=Bb/4;var Jb=Bb*2;var Gb=180/Bb;var Kb=Bb/180;var Vb=Math.abs;var Yb=Math.atan;var Qb=Math.atan2;var tx=Math.cos;var ex=Math.ceil;var nx=Math.exp;var ix=Math.floor;var rx=Math.hypot;var ox=Math.log;var ax=Math.pow;var sx=Math.sin;var lx=Math.sign||function(t){return t>0?1:t<0?-1:0};var ux=Math.sqrt;var fx=Math.tan;function cx(t){return t>1?0:t<-1?Bb:Math.acos(t)}function dx(t){return t>1?Zb:t<-1?-Zb:Math.asin(t)}function hx(t){return(t=sx(t/2))*t}function px(){}var mx=new Lb,gx=new Lb,yx,vx,bx,xx;var _x={point:px,lineStart:px,lineEnd:px,polygonStart:function(){_x.lineStart=wx;_x.lineEnd=Ex},polygonEnd:function(){_x.lineStart=_x.lineEnd=_x.point=px;mx.add(Vb(gx));gx=new Lb},result:function(){var t=mx/2;mx=new Lb;return t}};function wx(){_x.point=kx}function kx(t,e){_x.point=Mx;yx=bx=t,vx=xx=e}function Mx(t,e){gx.add(xx*t-bx*e);bx=t,xx=e}function Ex(){Mx(yx,vx)}const Sx=_x;var zx=Infinity,Dx=zx,Rx=-zx,Ax=Rx;var $x={point:Ox,lineStart:px,lineEnd:px,polygonStart:px,polygonEnd:px,result:function(){var t=[[zx,Dx],[Rx,Ax]];Rx=Ax=-(Dx=zx=Infinity);return t}};function Ox(t,e){if(t<zx)zx=t;if(t>Rx)Rx=t;if(e<Dx)Dx=e;if(e>Ax)Ax=e}const Tx=$x;var Cx=0,Nx=0,Ux=0,Ix=0,Px=0,qx=0,Lx=0,jx=0,Fx=0,Wx,Xx,Bx,Zx;var Hx={point:Jx,lineStart:Gx,lineEnd:Yx,polygonStart:function(){Hx.lineStart=Qx;Hx.lineEnd=t_},polygonEnd:function(){Hx.point=Jx;Hx.lineStart=Gx;Hx.lineEnd=Yx},result:function(){var t=Fx?[Lx/Fx,jx/Fx]:qx?[Ix/qx,Px/qx]:Ux?[Cx/Ux,Nx/Ux]:[NaN,NaN];Cx=Nx=Ux=Ix=Px=qx=Lx=jx=Fx=0;return t}};function Jx(t,e){Cx+=t;Nx+=e;++Ux}function Gx(){Hx.point=Kx}function Kx(t,e){Hx.point=Vx;Jx(Bx=t,Zx=e)}function Vx(t,e){var n=t-Bx,i=e-Zx,r=ux(n*n+i*i);Ix+=r*(Bx+t)/2;Px+=r*(Zx+e)/2;qx+=r;Jx(Bx=t,Zx=e)}function Yx(){Hx.point=Jx}function Qx(){Hx.point=e_}function t_(){n_(Wx,Xx)}function e_(t,e){Hx.point=n_;Jx(Wx=Bx=t,Xx=Zx=e)}function n_(t,e){var n=t-Bx,i=e-Zx,r=ux(n*n+i*i);Ix+=r*(Bx+t)/2;Px+=r*(Zx+e)/2;qx+=r;r=Zx*t-Bx*e;Lx+=r*(Bx+t);jx+=r*(Zx+e);Fx+=r*3;Jx(Bx=t,Zx=e)}const i_=Hx;function r_(t){this._context=t}r_.prototype={_radius:4.5,pointRadius:function(t){return this._radius=t,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){if(this._line===0)this._context.closePath();this._point=NaN},point:function(t,e){switch(this._point){case 0:{this._context.moveTo(t,e);this._point=1;break}case 1:{this._context.lineTo(t,e);break}default:{this._context.moveTo(t+this._radius,e);this._context.arc(t,e,this._radius,0,Jb);break}}},result:px};var o_=new Lb,a_,s_,l_,u_,f_;var c_={point:px,lineStart:function(){c_.point=d_},lineEnd:function(){if(a_)h_(s_,l_);c_.point=px},polygonStart:function(){a_=true},polygonEnd:function(){a_=null},result:function(){var t=+o_;o_=new Lb;return t}};function d_(t,e){c_.point=h_;s_=u_=t,l_=f_=e}function h_(t,e){u_-=t,f_-=e;o_.add(ux(u_*u_+f_*f_));u_=t,f_=e}const p_=c_;let m_,g_,y_,v_;class b_{constructor(t){this._append=t==null?x_:__(t);this._radius=4.5;this._=""}pointRadius(t){this._radius=+t;return this}polygonStart(){this._line=0}polygonEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){if(this._line===0)this._+="Z";this._point=NaN}point(t,e){switch(this._point){case 0:{this._append`M${t},${e}`;this._point=1;break}case 1:{this._append`L${t},${e}`;break}default:{this._append`M${t},${e}`;if(this._radius!==y_||this._append!==g_){const t=this._radius;const e=this._;this._="";this._append`m0,${t}a${t},${t} 0 1,1 0,${-2*t}a${t},${t} 0 1,1 0,${2*t}z`;y_=t;g_=this._append;v_=this._;this._=e}this._+=v_;break}}}result(){const t=this._;this._="";return t.length?t:null}}function x_(t){let e=1;this._+=t[0];for(const n=t.length;e<n;++e){this._+=arguments[e]+t[e]}}function __(t){const e=Math.floor(t);if(!(e>=0))throw new RangeError(`invalid digits: ${t}`);if(e>15)return x_;if(e!==m_){const t=10**e;m_=e;g_=function e(n){let i=1;this._+=n[0];for(const r=n.length;i<r;++i){this._+=Math.round(arguments[i]*t)/t+n[i]}}}return g_}function w_(t,e){let n=3,i=4.5,r,o;function a(t){if(t){if(typeof i==="function")o.pointRadius(+i.apply(this,arguments));qb(t,r(o))}return o.result()}a.area=function(t){qb(t,r(Sx));return Sx.result()};a.measure=function(t){qb(t,r(p_));return p_.result()};a.bounds=function(t){qb(t,r(Tx));return Tx.result()};a.centroid=function(t){qb(t,r(i_));return i_.result()};a.projection=function(e){if(!arguments.length)return t;r=e==null?(t=null,Tb):(t=e).stream;return a};a.context=function(t){if(!arguments.length)return e;o=t==null?(e=null,new b_(n)):new r_(e=t);if(typeof i!=="function")o.pointRadius(i);return a};a.pointRadius=function(t){if(!arguments.length)return i;i=typeof t==="function"?t:(o.pointRadius(+t),+t);return a};a.digits=function(t){if(!arguments.length)return n;if(t==null)n=null;else{const e=Math.floor(t);if(!(e>=0))throw new RangeError(`invalid digits: ${t}`);n=e}if(e===null)o=new b_(n);return a};return a.projection(t).digits(n).context(e)}function k_(){var t=[],e;return{point:function(t,n,i){e.push([t,n,i])},lineStart:function(){t.push(e=[])},lineEnd:px,rejoin:function(){if(t.length>1)t.push(t.pop().concat(t.shift()))},result:function(){var n=t;t=[];e=null;return n}}}function M_(t,e){return Vb(t[0]-e[0])<Wb&&Vb(t[1]-e[1])<Wb}function E_(t,e,n,i){this.x=t;this.z=e;this.o=n;this.e=i;this.v=false;this.n=this.p=null}function S_(t,e,n,i,r){var o=[],a=[],s,l;t.forEach((function(t){if((e=t.length-1)<=0)return;var e,n=t[0],i=t[e],l;if(M_(n,i)){if(!n[2]&&!i[2]){r.lineStart();for(s=0;s<e;++s)r.point((n=t[s])[0],n[1]);r.lineEnd();return}i[0]+=2*Wb}o.push(l=new E_(n,t,null,true));a.push(l.o=new E_(n,null,l,false));o.push(l=new E_(i,t,null,false));a.push(l.o=new E_(i,null,l,true))}));if(!o.length)return;a.sort(e);z_(o);z_(a);for(s=0,l=a.length;s<l;++s){a[s].e=n=!n}var u=o[0],f,c;while(1){var d=u,h=true;while(d.v)if((d=d.n)===u)return;f=d.z;r.lineStart();do{d.v=d.o.v=true;if(d.e){if(h){for(s=0,l=f.length;s<l;++s)r.point((c=f[s])[0],c[1])}else{i(d.x,d.n.x,1,r)}d=d.n}else{if(h){f=d.p.z;for(s=f.length-1;s>=0;--s)r.point((c=f[s])[0],c[1])}else{i(d.x,d.p.x,-1,r)}d=d.p}d=d.o;f=d.z;h=!h}while(!d.v);r.lineEnd()}}function z_(t){if(!(e=t.length))return;var e,n=0,i=t[0],r;while(++n<e){i.n=r=t[n];r.p=i;i=r}i.n=r=t[0];r.p=i}function D_(t){return[Qb(t[1],t[0]),dx(t[2])]}function R_(t){var e=t[0],n=t[1],i=tx(n);return[i*tx(e),i*sx(e),sx(n)]}function A_(t,e){return t[0]*e[0]+t[1]*e[1]+t[2]*e[2]}function $_(t,e){return[t[1]*e[2]-t[2]*e[1],t[2]*e[0]-t[0]*e[2],t[0]*e[1]-t[1]*e[0]]}function O_(t,e){t[0]+=e[0],t[1]+=e[1],t[2]+=e[2]}function T_(t,e){return[t[0]*e,t[1]*e,t[2]*e]}function C_(t){var e=ux(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]);t[0]/=e,t[1]/=e,t[2]/=e}function N_(t){return Vb(t[0])<=Bb?t[0]:lx(t[0])*((Vb(t[0])+Bb)%Jb-Bb)}function U_(t,e){var n=N_(e),i=e[1],r=sx(i),o=[sx(n),-tx(n),0],a=0,s=0;var l=new Lb;if(r===1)i=Zb+Wb;else if(r===-1)i=-Zb-Wb;for(var u=0,f=t.length;u<f;++u){if(!(d=(c=t[u]).length))continue;var c,d,h=c[d-1],p=N_(h),m=h[1]/2+Hb,g=sx(m),y=tx(m);for(var v=0;v<d;++v,p=x,g=w,y=k,h=b){var b=c[v],x=N_(b),_=b[1]/2+Hb,w=sx(_),k=tx(_),M=x-p,E=M>=0?1:-1,S=E*M,z=S>Bb,D=g*w;l.add(Qb(D*E*sx(S),y*k+D*tx(S)));a+=z?M+E*Jb:M;if(z^p>=n^x>=n){var R=$_(R_(h),R_(b));C_(R);var A=$_(o,R);C_(A);var $=(z^M>=0?-1:1)*dx(A[2]);if(i>$||i===$&&(R[0]||R[1])){s+=z^M>=0?1:-1}}}}return(a<-Wb||a<Wb&&l<-Xb)^s&1}function*I_(t){for(const e of t){yield*e}}function P_(t){return Array.from(I_(t))}function q_(t,e,n,i){return function(r){var o=e(r),a=k_(),s=e(a),l=false,u,f,c;var d={point:h,lineStart:m,lineEnd:g,polygonStart:function(){d.point=y;d.lineStart=v;d.lineEnd=b;f=[];u=[]},polygonEnd:function(){d.point=h;d.lineStart=m;d.lineEnd=g;f=P_(f);var t=U_(u,i);if(f.length){if(!l)r.polygonStart(),l=true;S_(f,j_,t,n,r)}else if(t){if(!l)r.polygonStart(),l=true;r.lineStart();n(null,null,1,r);r.lineEnd()}if(l)r.polygonEnd(),l=false;f=u=null},sphere:function(){r.polygonStart();r.lineStart();n(null,null,1,r);r.lineEnd();r.polygonEnd()}};function h(e,n){if(t(e,n))r.point(e,n)}function p(t,e){o.point(t,e)}function m(){d.point=p;o.lineStart()}function g(){d.point=h;o.lineEnd()}function y(t,e){c.push([t,e]);s.point(t,e)}function v(){s.lineStart();c=[]}function b(){y(c[0][0],c[0][1]);s.lineEnd();var t=s.clean(),e=a.result(),n,i=e.length,o,d,h;c.pop();u.push(c);c=null;if(!i)return;if(t&1){d=e[0];if((o=d.length-1)>0){if(!l)r.polygonStart(),l=true;r.lineStart();for(n=0;n<o;++n)r.point((h=d[n])[0],h[1]);r.lineEnd()}return}if(i>1&&t&2)e.push(e.pop().concat(e.shift()));f.push(e.filter(L_))}return d}}function L_(t){return t.length>1}function j_(t,e){return((t=t.x)[0]<0?t[1]-Zb-Wb:Zb-t[1])-((e=e.x)[0]<0?e[1]-Zb-Wb:Zb-e[1])}const F_=q_((function(){return true}),W_,B_,[-Bb,-Zb]);function W_(t){var e=NaN,n=NaN,i=NaN,r;return{lineStart:function(){t.lineStart();r=1},point:function(o,a){var s=o>0?Bb:-Bb,l=Vb(o-e);if(Vb(l-Bb)<Wb){t.point(e,n=(n+a)/2>0?Zb:-Zb);t.point(i,n);t.lineEnd();t.lineStart();t.point(s,n);t.point(o,n);r=0}else if(i!==s&&l>=Bb){if(Vb(e-i)<Wb)e-=i*Wb;if(Vb(o-s)<Wb)o-=s*Wb;n=X_(e,n,o,a);t.point(i,n);t.lineEnd();t.lineStart();t.point(s,n);r=0}t.point(e=o,n=a);i=s},lineEnd:function(){t.lineEnd();e=n=NaN},clean:function(){return 2-r}}}function X_(t,e,n,i){var r,o,a=sx(t-n);return Vb(a)>Wb?Yb((sx(e)*(o=tx(i))*sx(n)-sx(i)*(r=tx(e))*sx(t))/(r*o*a)):(e+i)/2}function B_(t,e,n,i){var r;if(t==null){r=n*Zb;i.point(-Bb,r);i.point(0,r);i.point(Bb,r);i.point(Bb,0);i.point(Bb,-r);i.point(0,-r);i.point(-Bb,-r);i.point(-Bb,0);i.point(-Bb,r)}else if(Vb(t[0]-e[0])>Wb){var o=t[0]<e[0]?Bb:-Bb;r=n*o/2;i.point(-o,r);i.point(0,r);i.point(o,r)}else{i.point(e[0],e[1])}}function Z_(t,e,n,i,r,o){if(!n)return;var a=tx(e),s=sx(e),l=i*n;if(r==null){r=e+i*Jb;o=e-l/2}else{r=H_(a,r);o=H_(a,o);if(i>0?r<o:r>o)r+=i*Jb}for(var u,f=r;i>0?f>o:f<o;f-=l){u=D_([a,-s*tx(f),-s*sx(f)]);t.point(u[0],u[1])}}function H_(t,e){e=R_(e),e[0]-=t;C_(e);var n=cx(-e[1]);return((-e[2]<0?-n:n)+Jb-Wb)%Jb}function J_(){var t=constant([0,0]),e=constant(90),n=constant(6),i,r,o={point:a};function a(t,e){i.push(t=r(t,e));t[0]*=degrees,t[1]*=degrees}function s(){var a=t.apply(this,arguments),s=e.apply(this,arguments)*radians,l=n.apply(this,arguments)*radians;i=[];r=rotateRadians(-a[0]*radians,-a[1]*radians,0).invert;Z_(o,s,l,1);a={type:"Polygon",coordinates:[i]};i=r=null;return a}s.center=function(e){return arguments.length?(t=typeof e==="function"?e:constant([+e[0],+e[1]]),s):t};s.radius=function(t){return arguments.length?(e=typeof t==="function"?t:constant(+t),s):e};s.precision=function(t){return arguments.length?(n=typeof t==="function"?t:constant(+t),s):n};return s}function G_(t){var e=tx(t),n=6*Kb,i=e>0,r=Vb(e)>Wb;function o(e,i,r,o){Z_(o,t,n,r,e,i)}function a(t,n){return tx(t)*tx(n)>e}function s(t){var e,n,o,s,f;return{lineStart:function(){s=o=false;f=1},point:function(c,d){var h=[c,d],p,m=a(c,d),g=i?m?0:u(c,d):m?u(c+(c<0?Bb:-Bb),d):0;if(!e&&(s=o=m))t.lineStart();if(m!==o){p=l(e,h);if(!p||M_(e,p)||M_(h,p))h[2]=1}if(m!==o){f=0;if(m){t.lineStart();p=l(h,e);t.point(p[0],p[1])}else{p=l(e,h);t.point(p[0],p[1],2);t.lineEnd()}e=p}else if(r&&e&&i^m){var y;if(!(g&n)&&(y=l(h,e,true))){f=0;if(i){t.lineStart();t.point(y[0][0],y[0][1]);t.point(y[1][0],y[1][1]);t.lineEnd()}else{t.point(y[1][0],y[1][1]);t.lineEnd();t.lineStart();t.point(y[0][0],y[0][1],3)}}}if(m&&(!e||!M_(e,h))){t.point(h[0],h[1])}e=h,o=m,n=g},lineEnd:function(){if(o)t.lineEnd();e=null},clean:function(){return f|(s&&o)<<1}}}function l(t,n,i){var r=R_(t),o=R_(n);var a=[1,0,0],s=$_(r,o),l=A_(s,s),u=s[0],f=l-u*u;if(!f)return!i&&t;var c=e*l/f,d=-e*u/f,h=$_(a,s),p=T_(a,c),m=T_(s,d);O_(p,m);var g=h,y=A_(p,g),v=A_(g,g),b=y*y-v*(A_(p,p)-1);if(b<0)return;var x=ux(b),_=T_(g,(-y-x)/v);O_(_,p);_=D_(_);if(!i)return _;var w=t[0],k=n[0],M=t[1],E=n[1],S;if(k<w)S=w,w=k,k=S;var z=k-w,D=Vb(z-Bb)<Wb,R=D||z<Wb;if(!D&&E<M)S=M,M=E,E=S;if(R?D?M+E>0^_[1]<(Vb(_[0]-w)<Wb?M:E):M<=_[1]&&_[1]<=E:z>Bb^(w<=_[0]&&_[0]<=k)){var A=T_(g,(-y+x)/v);O_(A,p);return[_,D_(A)]}}function u(e,n){var r=i?t:Bb-t,o=0;if(e<-r)o|=1;else if(e>r)o|=2;if(n<-r)o|=4;else if(n>r)o|=8;return o}return q_(a,s,o,i?[0,-t]:[-Bb,t-Bb])}function K_(t,e,n,i,r,o){var a=t[0],s=t[1],l=e[0],u=e[1],f=0,c=1,d=l-a,h=u-s,p;p=n-a;if(!d&&p>0)return;p/=d;if(d<0){if(p<f)return;if(p<c)c=p}else if(d>0){if(p>c)return;if(p>f)f=p}p=r-a;if(!d&&p<0)return;p/=d;if(d<0){if(p>c)return;if(p>f)f=p}else if(d>0){if(p<f)return;if(p<c)c=p}p=i-s;if(!h&&p>0)return;p/=h;if(h<0){if(p<f)return;if(p<c)c=p}else if(h>0){if(p>c)return;if(p>f)f=p}p=o-s;if(!h&&p<0)return;p/=h;if(h<0){if(p>c)return;if(p>f)f=p}else if(h>0){if(p<f)return;if(p<c)c=p}if(f>0)t[0]=a+f*d,t[1]=s+f*h;if(c<1)e[0]=a+c*d,e[1]=s+c*h;return true}var V_=1e9,Y_=-V_;function Q_(t,e,n,i){function r(r,o){return t<=r&&r<=n&&e<=o&&o<=i}function o(r,o,s,u){var f=0,c=0;if(r==null||(f=a(r,s))!==(c=a(o,s))||l(r,o)<0^s>0){do{u.point(f===0||f===3?t:n,f>1?i:e)}while((f=(f+s+4)%4)!==c)}else{u.point(o[0],o[1])}}function a(i,r){return Vb(i[0]-t)<Wb?r>0?0:3:Vb(i[0]-n)<Wb?r>0?2:1:Vb(i[1]-e)<Wb?r>0?1:0:r>0?3:2}function s(t,e){return l(t.x,e.x)}function l(t,e){var n=a(t,1),i=a(e,1);return n!==i?n-i:n===0?e[1]-t[1]:n===1?t[0]-e[0]:n===2?t[1]-e[1]:e[0]-t[0]}return function(a){var l=a,u=k_(),f,c,d,h,p,m,g,y,v,b,x;var _={point:w,lineStart:S,lineEnd:z,polygonStart:M,polygonEnd:E};function w(t,e){if(r(t,e))l.point(t,e)}function k(){var e=0;for(var n=0,r=c.length;n<r;++n){for(var o=c[n],a=1,s=o.length,l=o[0],u,f,d=l[0],h=l[1];a<s;++a){u=d,f=h,l=o[a],d=l[0],h=l[1];if(f<=i){if(h>i&&(d-u)*(i-f)>(h-f)*(t-u))++e}else{if(h<=i&&(d-u)*(i-f)<(h-f)*(t-u))--e}}}return e}function M(){l=u,f=[],c=[],x=true}function E(){var t=k(),e=x&&t,n=(f=P_(f)).length;if(e||n){a.polygonStart();if(e){a.lineStart();o(null,null,1,a);a.lineEnd()}if(n){S_(f,s,t,o,a)}a.polygonEnd()}l=a,f=c=d=null}function S(){_.point=D;if(c)c.push(d=[]);b=true;v=false;g=y=NaN}function z(){if(f){D(h,p);if(m&&v)u.rejoin();f.push(u.result())}_.point=w;if(v)l.lineEnd()}function D(o,a){var s=r(o,a);if(c)d.push([o,a]);if(b){h=o,p=a,m=s;b=false;if(s){l.lineStart();l.point(o,a)}}else{if(s&&v)l.point(o,a);else{var u=[g=Math.max(Y_,Math.min(V_,g)),y=Math.max(Y_,Math.min(V_,y))],f=[o=Math.max(Y_,Math.min(V_,o)),a=Math.max(Y_,Math.min(V_,a))];if(K_(u,f,t,e,n,i)){if(!v){l.lineStart();l.point(u[0],u[1])}l.point(f[0],f[1]);if(!s)l.lineEnd();x=false}else if(s){l.lineStart();l.point(o,a);x=false}}}g=o,y=a,v=s}return _}}function tw(t,e){function n(n,i){return n=t(n,i),e(n[0],n[1])}if(t.invert&&e.invert)n.invert=function(n,i){return n=e.invert(n,i),n&&t.invert(n[0],n[1])};return n}function ew(t,e){if(Vb(t)>Bb)t-=Math.round(t/Jb)*Jb;return[t,e]}ew.invert=ew;function nw(t,e,n){return(t%=Jb)?e||n?tw(rw(t),ow(e,n)):rw(t):e||n?ow(e,n):ew}function iw(t){return function(e,n){e+=t;if(Vb(e)>Bb)e-=Math.round(e/Jb)*Jb;return[e,n]}}function rw(t){var e=iw(t);e.invert=iw(-t);return e}function ow(t,e){var n=tx(t),i=sx(t),r=tx(e),o=sx(e);function a(t,e){var a=tx(e),s=tx(t)*a,l=sx(t)*a,u=sx(e),f=u*n+s*i;return[Qb(l*r-f*o,s*n-u*i),dx(f*r+l*o)]}a.invert=function(t,e){var a=tx(e),s=tx(t)*a,l=sx(t)*a,u=sx(e),f=u*r-l*o;return[Qb(l*r+u*o,s*n+f*i),dx(f*n-s*i)]};return a}function aw(t){t=nw(t[0]*Kb,t[1]*Kb,t.length>2?t[2]*Kb:0);function e(e){e=t(e[0]*Kb,e[1]*Kb);return e[0]*=Gb,e[1]*=Gb,e}e.invert=function(e){e=t.invert(e[0]*Kb,e[1]*Kb);return e[0]*=Gb,e[1]*=Gb,e};return e}function sw(t){return{stream:lw(t)}}function lw(t){return function(e){var n=new uw;for(var i in t)n[i]=t[i];n.stream=e;return n}}function uw(){}uw.prototype={constructor:uw,point:function(t,e){this.stream.point(t,e)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};function fw(t,e,n){var i=t.clipExtent&&t.clipExtent();t.scale(150).translate([0,0]);if(i!=null)t.clipExtent(null);qb(n,t.stream(Tx));e(Tx.result());if(i!=null)t.clipExtent(i);return t}function cw(t,e,n){return fw(t,(function(n){var i=e[1][0]-e[0][0],r=e[1][1]-e[0][1],o=Math.min(i/(n[1][0]-n[0][0]),r/(n[1][1]-n[0][1])),a=+e[0][0]+(i-o*(n[1][0]+n[0][0]))/2,s=+e[0][1]+(r-o*(n[1][1]+n[0][1]))/2;t.scale(150*o).translate([a,s])}),n)}function dw(t,e,n){return cw(t,[[0,0],e],n)}function hw(t,e,n){return fw(t,(function(n){var i=+e,r=i/(n[1][0]-n[0][0]),o=(i-r*(n[1][0]+n[0][0]))/2,a=-r*n[0][1];t.scale(150*r).translate([o,a])}),n)}function pw(t,e,n){return fw(t,(function(n){var i=+e,r=i/(n[1][1]-n[0][1]),o=-r*n[0][0],a=(i-r*(n[1][1]+n[0][1]))/2;t.scale(150*r).translate([o,a])}),n)}var mw=16,gw=tx(30*Kb);function yw(t,e){return+e?bw(t,e):vw(t)}function vw(t){return lw({point:function(e,n){e=t(e,n);this.stream.point(e[0],e[1])}})}function bw(t,e){function n(i,r,o,a,s,l,u,f,c,d,h,p,m,g){var y=u-i,v=f-r,b=y*y+v*v;if(b>4*e&&m--){var x=a+d,_=s+h,w=l+p,k=ux(x*x+_*_+w*w),M=dx(w/=k),E=Vb(Vb(w)-1)<Wb||Vb(o-c)<Wb?(o+c)/2:Qb(_,x),S=t(E,M),z=S[0],D=S[1],R=z-i,A=D-r,$=v*R-y*A;if($*$/b>e||Vb((y*R+v*A)/b-.5)>.3||a*d+s*h+l*p<gw){n(i,r,o,a,s,l,z,D,E,x/=k,_/=k,w,m,g);g.point(z,D);n(z,D,E,x,_,w,u,f,c,d,h,p,m,g)}}}return function(e){var i,r,o,a,s,l,u,f,c,d,h,p;var m={point:g,lineStart:y,lineEnd:b,polygonStart:function(){e.polygonStart();m.lineStart=x},polygonEnd:function(){e.polygonEnd();m.lineStart=y}};function g(n,i){n=t(n,i);e.point(n[0],n[1])}function y(){f=NaN;m.point=v;e.lineStart()}function v(i,r){var o=R_([i,r]),a=t(i,r);n(f,c,u,d,h,p,f=a[0],c=a[1],u=i,d=o[0],h=o[1],p=o[2],mw,e);e.point(f,c)}function b(){m.point=g;e.lineEnd()}function x(){y();m.point=_;m.lineEnd=w}function _(t,e){v(i=t,e),r=f,o=c,a=d,s=h,l=p;m.point=v}function w(){n(f,c,u,d,h,p,r,o,i,a,s,l,mw,e);m.lineEnd=b;b()}return m}}var xw=lw({point:function(t,e){this.stream.point(t*Kb,e*Kb)}});function _w(t){return lw({point:function(e,n){var i=t(e,n);return this.stream.point(i[0],i[1])}})}function ww(t,e,n,i,r){function o(o,a){o*=i;a*=r;return[e+t*o,n-t*a]}o.invert=function(o,a){return[(o-e)/t*i,(n-a)/t*r]};return o}function kw(t,e,n,i,r,o){if(!o)return ww(t,e,n,i,r);var a=tx(o),s=sx(o),l=a*t,u=s*t,f=a/t,c=s/t,d=(s*n-a*e)/t,h=(s*e+a*n)/t;function p(t,o){t*=i;o*=r;return[l*t-u*o+e,n-u*t-l*o]}p.invert=function(t,e){return[i*(f*t-c*e+d),r*(h-c*t-f*e)]};return p}function Mw(t){return Ew((function(){return t}))()}function Ew(t){var e,n=150,i=480,r=250,o=0,a=0,s=0,l=0,u=0,f,c=0,d=1,h=1,p=null,m=F_,g=null,y,v,b,x=Tb,_=.5,w,k,M,E,S;function z(t){return M(t[0]*Kb,t[1]*Kb)}function D(t){t=M.invert(t[0],t[1]);return t&&[t[0]*Gb,t[1]*Gb]}z.stream=function(t){return E&&S===t?E:E=xw(_w(f)(m(w(x(S=t)))))};z.preclip=function(t){return arguments.length?(m=t,p=undefined,A()):m};z.postclip=function(t){return arguments.length?(x=t,g=y=v=b=null,A()):x};z.clipAngle=function(t){return arguments.length?(m=+t?G_(p=t*Kb):(p=null,F_),A()):p*Gb};z.clipExtent=function(t){return arguments.length?(x=t==null?(g=y=v=b=null,Tb):Q_(g=+t[0][0],y=+t[0][1],v=+t[1][0],b=+t[1][1]),A()):g==null?null:[[g,y],[v,b]]};z.scale=function(t){return arguments.length?(n=+t,R()):n};z.translate=function(t){return arguments.length?(i=+t[0],r=+t[1],R()):[i,r]};z.center=function(t){return arguments.length?(o=t[0]%360*Kb,a=t[1]%360*Kb,R()):[o*Gb,a*Gb]};z.rotate=function(t){return arguments.length?(s=t[0]%360*Kb,l=t[1]%360*Kb,u=t.length>2?t[2]%360*Kb:0,R()):[s*Gb,l*Gb,u*Gb]};z.angle=function(t){return arguments.length?(c=t%360*Kb,R()):c*Gb};z.reflectX=function(t){return arguments.length?(d=t?-1:1,R()):d<0};z.reflectY=function(t){return arguments.length?(h=t?-1:1,R()):h<0};z.precision=function(t){return arguments.length?(w=yw(k,_=t*t),A()):ux(_)};z.fitExtent=function(t,e){return cw(z,t,e)};z.fitSize=function(t,e){return dw(z,t,e)};z.fitWidth=function(t,e){return hw(z,t,e)};z.fitHeight=function(t,e){return pw(z,t,e)};function R(){var t=kw(n,0,0,d,h,c).apply(null,e(o,a)),p=kw(n,i-t[0],r-t[1],d,h,c);f=nw(s,l,u);k=tw(e,p);M=tw(f,k);w=yw(k,_);return A()}function A(){E=S=null;return z}return function(){e=t.apply(this,arguments);z.invert=e.invert&&D;return R()}}function Sw(t){var e=0,n=Bb/3,i=Ew(t),r=i(e,n);r.parallels=function(t){return arguments.length?i(e=t[0]*Kb,n=t[1]*Kb):[e*Gb,n*Gb]};return r}function zw(t){var e=tx(t);function n(t,n){return[t*e,sx(n)/e]}n.invert=function(t,n){return[t/e,dx(n*e)]};return n}function Dw(t,e){var n=sx(t),i=(n+sx(e))/2;if(Vb(i)<Wb)return zw(t);var r=1+n*(2*i-n),o=ux(r)/i;function a(t,e){var n=ux(r-2*i*sx(e))/i;return[n*sx(t*=i),o-n*tx(t)]}a.invert=function(t,e){var n=o-e,a=Qb(t,Vb(n))*lx(n);if(n*i<0)a-=Bb*lx(t)*lx(n);return[a/i,dx((r-(t*t+n*n)*i*i)/(2*i))]};return a}function Rw(){return Sw(Dw).scale(155.424).center([0,33.6442])}function Aw(){return Rw().parallels([29.5,45.5]).scale(1070).translate([480,250]).rotate([96,0]).center([-.6,38.7])}function $w(t){var e=t.length;return{point:function(n,i){var r=-1;while(++r<e)t[r].point(n,i)},sphere:function(){var n=-1;while(++n<e)t[n].sphere()},lineStart:function(){var n=-1;while(++n<e)t[n].lineStart()},lineEnd:function(){var n=-1;while(++n<e)t[n].lineEnd()},polygonStart:function(){var n=-1;while(++n<e)t[n].polygonStart()},polygonEnd:function(){var n=-1;while(++n<e)t[n].polygonEnd()}}}function Ow(){var t,e,n=Aw(),i,r=Rw().rotate([154,0]).center([-2,58.5]).parallels([55,65]),o,a=Rw().rotate([157,0]).center([-3,19.9]).parallels([8,18]),s,l,u={point:function(t,e){l=[t,e]}};function f(t){var e=t[0],n=t[1];return l=null,(i.point(e,n),l)||(o.point(e,n),l)||(s.point(e,n),l)}f.invert=function(t){var e=n.scale(),i=n.translate(),o=(t[0]-i[0])/e,s=(t[1]-i[1])/e;return(s>=.12&&s<.234&&o>=-.425&&o<-.214?r:s>=.166&&s<.234&&o>=-.214&&o<-.115?a:n).invert(t)};f.stream=function(i){return t&&e===i?t:t=$w([n.stream(e=i),r.stream(i),a.stream(i)])};f.precision=function(t){if(!arguments.length)return n.precision();n.precision(t),r.precision(t),a.precision(t);return c()};f.scale=function(t){if(!arguments.length)return n.scale();n.scale(t),r.scale(t*.35),a.scale(t);return f.translate(n.translate())};f.translate=function(t){if(!arguments.length)return n.translate();var e=n.scale(),l=+t[0],f=+t[1];i=n.translate(t).clipExtent([[l-.455*e,f-.238*e],[l+.455*e,f+.238*e]]).stream(u);o=r.translate([l-.307*e,f+.201*e]).clipExtent([[l-.425*e+Wb,f+.12*e+Wb],[l-.214*e-Wb,f+.234*e-Wb]]).stream(u);s=a.translate([l-.205*e,f+.212*e]).clipExtent([[l-.214*e+Wb,f+.166*e+Wb],[l-.115*e-Wb,f+.234*e-Wb]]).stream(u);return c()};f.fitExtent=function(t,e){return cw(f,t,e)};f.fitSize=function(t,e){return dw(f,t,e)};f.fitWidth=function(t,e){return hw(f,t,e)};f.fitHeight=function(t,e){return pw(f,t,e)};function c(){t=e=null;return f}return f.scale(1070)}function Tw(t){return function(e,n){var i=tx(e),r=tx(n),o=t(i*r);if(o===Infinity)return[2,0];return[o*r*sx(e),o*sx(n)]}}function Cw(t){return function(e,n){var i=ux(e*e+n*n),r=t(i),o=sx(r),a=tx(r);return[Qb(e*o,i*a),dx(i&&n*o/i)]}}var Nw=Tw((function(t){return ux(2/(1+t))}));Nw.invert=Cw((function(t){return 2*dx(t/2)}));function Uw(){return Mw(Nw).scale(124.75).clipAngle(180-.001)}var Iw=Tw((function(t){return(t=cx(t))&&t/sx(t)}));Iw.invert=Cw((function(t){return t}));function Pw(){return Mw(Iw).scale(79.4188).clipAngle(180-.001)}function qw(t,e){return[t,ox(fx((Zb+e)/2))]}qw.invert=function(t,e){return[t,2*Yb(nx(e))-Zb]};function Lw(){return jw(qw).scale(961/Jb)}function jw(t){var e=Mw(t),n=e.center,i=e.scale,r=e.translate,o=e.clipExtent,a=null,s,l,u;e.scale=function(t){return arguments.length?(i(t),f()):i()};e.translate=function(t){return arguments.length?(r(t),f()):r()};e.center=function(t){return arguments.length?(n(t),f()):n()};e.clipExtent=function(t){return arguments.length?(t==null?a=s=l=u=null:(a=+t[0][0],s=+t[0][1],l=+t[1][0],u=+t[1][1]),f()):a==null?null:[[a,s],[l,u]]};function f(){var n=Bb*i(),r=e(aw(e.rotate()).invert([0,0]));return o(a==null?[[r[0]-n,r[1]-n],[r[0]+n,r[1]+n]]:t===qw?[[Math.max(r[0]-n,a),s],[Math.min(r[0]+n,l),u]]:[[a,Math.max(r[1]-n,s)],[l,Math.min(r[1]+n,u)]])}return f()}function Fw(t){return fx((Zb+t)/2)}function Ww(t,e){var n=tx(t),i=t===e?sx(t):ox(n/tx(e))/ox(Fw(e)/Fw(t)),r=n*ax(Fw(t),i)/i;if(!i)return qw;function o(t,e){if(r>0){if(e<-Zb+Wb)e=-Zb+Wb}else{if(e>Zb-Wb)e=Zb-Wb}var n=r/ax(Fw(e),i);return[n*sx(i*t),r-n*tx(i*t)]}o.invert=function(t,e){var n=r-e,o=lx(i)*ux(t*t+n*n),a=Qb(t,Vb(n))*lx(n);if(n*i<0)a-=Bb*lx(t)*lx(n);return[a/i,2*Yb(ax(r/o,1/i))-Zb]};return o}function Xw(){return Sw(Ww).scale(109.5).parallels([30,30])}function Bw(t,e){return[t,e]}Bw.invert=Bw;function Zw(){return Mw(Bw).scale(152.63)}function Hw(t,e){var n=tx(t),i=t===e?sx(t):(n-tx(e))/(e-t),r=n/i+t;if(Vb(i)<Wb)return Bw;function o(t,e){var n=r-e,o=i*t;return[n*sx(o),r-n*tx(o)]}o.invert=function(t,e){var n=r-e,o=Qb(t,Vb(n))*lx(n);if(n*i<0)o-=Bb*lx(t)*lx(n);return[o/i,r-lx(i)*ux(t*t+n*n)]};return o}function Jw(){return Sw(Hw).scale(131.154).center([0,13.9389])}var Gw=1.340264,Kw=-.081106,Vw=893e-6,Yw=.003796,Qw=ux(3)/2,tk=12;function ek(t,e){var n=dx(Qw*sx(e)),i=n*n,r=i*i*i;return[t*tx(n)/(Qw*(Gw+3*Kw*i+r*(7*Vw+9*Yw*i))),n*(Gw+Kw*i+r*(Vw+Yw*i))]}ek.invert=function(t,e){var n=e,i=n*n,r=i*i*i;for(var o=0,a,s,l;o<tk;++o){s=n*(Gw+Kw*i+r*(Vw+Yw*i))-e;l=Gw+3*Kw*i+r*(7*Vw+9*Yw*i);n-=a=s/l,i=n*n,r=i*i*i;if(Vb(a)<Xb)break}return[Qw*t*(Gw+3*Kw*i+r*(7*Vw+9*Yw*i))/tx(n),dx(sx(n)/Qw)]};function nk(){return Mw(ek).scale(177.158)}function ik(t,e){var n=tx(e),i=tx(t)*n;return[n*sx(t)/i,sx(e)/i]}ik.invert=Cw(Yb);function rk(){return Mw(ik).scale(144.049).clipAngle(60)}function ok(){var t=1,e=0,n=0,i=1,r=1,o=0,a,s,l=null,u,f,c,d=1,h=1,p=lw({point:function(t,e){var n=b([t,e]);this.stream.point(n[0],n[1])}}),m=Tb,g,y;function v(){d=t*i;h=t*r;g=y=null;return b}function b(t){var i=t[0]*d,r=t[1]*h;if(o){var l=r*a-i*s;i=i*a+r*s;r=l}return[i+e,r+n]}b.invert=function(t){var i=t[0]-e,r=t[1]-n;if(o){var l=r*a+i*s;i=i*a-r*s;r=l}return[i/d,r/h]};b.stream=function(t){return g&&y===t?g:g=p(m(y=t))};b.postclip=function(t){return arguments.length?(m=t,l=u=f=c=null,v()):m};b.clipExtent=function(t){return arguments.length?(m=t==null?(l=u=f=c=null,Tb):Q_(l=+t[0][0],u=+t[0][1],f=+t[1][0],c=+t[1][1]),v()):l==null?null:[[l,u],[f,c]]};b.scale=function(e){return arguments.length?(t=+e,v()):t};b.translate=function(t){return arguments.length?(e=+t[0],n=+t[1],v()):[e,n]};b.angle=function(t){return arguments.length?(o=t%360*Kb,s=sx(o),a=tx(o),v()):o*Gb};b.reflectX=function(t){return arguments.length?(i=t?-1:1,v()):i<0};b.reflectY=function(t){return arguments.length?(r=t?-1:1,v()):r<0};b.fitExtent=function(t,e){return cw(b,t,e)};b.fitSize=function(t,e){return dw(b,t,e)};b.fitWidth=function(t,e){return hw(b,t,e)};b.fitHeight=function(t,e){return pw(b,t,e)};return b}function ak(t,e){var n=e*e,i=n*n;return[t*(.8707-.131979*n+i*(-.013791+i*(.003971*n-.001529*i))),e*(1.007226+n*(.015085+i*(-.044475+.028874*n-.005916*i)))]}ak.invert=function(t,e){var n=e,i=25,r;do{var o=n*n,a=o*o;n-=r=(n*(1.007226+o*(.015085+a*(-.044475+.028874*o-.005916*a)))-e)/(1.007226+o*(.015085*3+a*(-.044475*7+.028874*9*o-.005916*11*a)))}while(Vb(r)>Wb&&--i>0);return[t/(.8707+(o=n*n)*(-.131979+o*(-.013791+o*o*o*(.003971-.001529*o)))),n]};function sk(){return Mw(ak).scale(175.295)}function lk(t,e){return[tx(e)*sx(t),sx(e)]}lk.invert=Cw(dx);function uk(){return Mw(lk).scale(249.5).clipAngle(90+Wb)}function fk(t,e){var n=tx(e),i=1+tx(t)*n;return[n*sx(t)/i,sx(e)/i]}fk.invert=Cw((function(t){return 2*Yb(t)}));function ck(){return Mw(fk).scale(250).clipAngle(142)}function dk(t,e){return[ox(fx((Zb+e)/2)),-t]}dk.invert=function(t,e){return[-e,2*Yb(nx(t))-Zb]};function hk(){var t=jw(dk),e=t.center,n=t.rotate;t.center=function(t){return arguments.length?e([-t[1],t[0]]):(t=e(),[t[1],-t[0]])};t.rotate=function(t){return arguments.length?n([t[0],t[1],t.length>2?t[2]+90:90]):(t=n(),[t[0],t[1],t[2]-90])};return n([0,0,90]).scale(159.155)}var pk=Math.abs;var mk=Math.atan;var gk=Math.atan2;var yk=Math.ceil;var vk=Math.cos;var bk=Math.exp;var xk=Math.floor;var _k=Math.log;var wk=Math.max;var kk=Math.min;var Mk=Math.pow;var Ek=Math.round;var Sk=Math.sign||function(t){return t>0?1:t<0?-1:0};var zk=Math.sin;var Dk=Math.tan;var Rk=1e-6;var Ak=1e-12;var $k=Math.PI;var Ok=$k/2;var Tk=$k/4;var Ck=Math.SQRT1_2;var Nk=Wk(2);var Uk=Wk($k);var Ik=$k*2;var Pk=180/$k;var qk=$k/180;function Lk(t){return t?t/Math.sin(t):1}function jk(t){return t>1?Ok:t<-1?-Ok:Math.asin(t)}function Fk(t){return t>1?0:t<-1?$k:Math.acos(t)}function Wk(t){return t>0?Math.sqrt(t):0}function Xk(t){t=bk(2*t);return(t-1)/(t+1)}function Bk(t){return(bk(t)-bk(-t))/2}function Zk(t){return(bk(t)+bk(-t))/2}function Hk(t){return _k(t+Wk(t*t+1))}function Jk(t){return _k(t+Wk(t*t-1))}function Gk(t,e){var n=t*zk(e),i=30,r;do{e-=r=(e+zk(e)-n)/(1+vk(e))}while(pk(r)>Rk&&--i>0);return e/2}function Kk(t,e,n){function i(i,r){return[t*i*vk(r=Gk(n,r)),e*zk(r)]}i.invert=function(i,r){return r=jk(r/e),[i/(t*vk(r)),jk((2*r+zk(2*r))/n)]};return i}var Vk=Kk(Nk/Ok,Nk,$k);function Yk(){return Mw(Vk).scale(169.529)}const Qk=w_();const tM=["clipAngle","clipExtent","scale","translate","center","rotate","parallels","precision","reflectX","reflectY","coefficient","distance","fraction","lobes","parallel","radius","ratio","spacing","tilt"];function eM(t,e){return function n(){const i=e();i.type=t;i.path=w_().projection(i);i.copy=i.copy||function(){const t=n();tM.forEach((e=>{if(i[e])t[e](i[e]())}));t.path.pointRadius(i.path.pointRadius());return t};return Au(i)}}function nM(t,e){if(!t||typeof t!=="string"){throw new Error("Projection type must be a name string.")}t=t.toLowerCase();if(arguments.length>1){rM[t]=eM(t,e);return this}else{return rM[t]||null}}function iM(t){return t&&t.path||Qk}const rM={albers:Aw,albersusa:Ow,azimuthalequalarea:Uw,azimuthalequidistant:Pw,conicconformal:Xw,conicequalarea:Rw,conicequidistant:Jw,equalEarth:nk,equirectangular:Zw,gnomonic:rk,identity:ok,mercator:Lw,mollweide:Yk,naturalEarth1:sk,orthographic:uk,stereographic:ck,transversemercator:hk};for(const RW in rM){nM(RW,rM[RW])}function oM(t,e,n){var i=(0,to.Z)(t,e-Wb,n).concat(e);return function(t){return i.map((function(e){return[t,e]}))}}function aM(t,e,n){var i=(0,to.Z)(t,e-Wb,n).concat(e);return function(t){return i.map((function(e){return[e,t]}))}}function sM(){var t,e,n,i,r,o,a,s,l=10,u=l,f=90,c=360,d,h,p,m,g=2.5;function y(){return{type:"MultiLineString",coordinates:v()}}function v(){return(0,to.Z)(ex(i/f)*f,n,f).map(p).concat((0,to.Z)(ex(s/c)*c,a,c).map(m)).concat((0,to.Z)(ex(e/l)*l,t,l).filter((function(t){return Vb(t%f)>Wb})).map(d)).concat((0,to.Z)(ex(o/u)*u,r,u).filter((function(t){return Vb(t%c)>Wb})).map(h))}y.lines=function(){return v().map((function(t){return{type:"LineString",coordinates:t}}))};y.outline=function(){return{type:"Polygon",coordinates:[p(i).concat(m(a).slice(1),p(n).reverse().slice(1),m(s).reverse().slice(1))]}};y.extent=function(t){if(!arguments.length)return y.extentMinor();return y.extentMajor(t).extentMinor(t)};y.extentMajor=function(t){if(!arguments.length)return[[i,s],[n,a]];i=+t[0][0],n=+t[1][0];s=+t[0][1],a=+t[1][1];if(i>n)t=i,i=n,n=t;if(s>a)t=s,s=a,a=t;return y.precision(g)};y.extentMinor=function(n){if(!arguments.length)return[[e,o],[t,r]];e=+n[0][0],t=+n[1][0];o=+n[0][1],r=+n[1][1];if(e>t)n=e,e=t,t=n;if(o>r)n=o,o=r,r=n;return y.precision(g)};y.step=function(t){if(!arguments.length)return y.stepMinor();return y.stepMajor(t).stepMinor(t)};y.stepMajor=function(t){if(!arguments.length)return[f,c];f=+t[0],c=+t[1];return y};y.stepMinor=function(t){if(!arguments.length)return[l,u];l=+t[0],u=+t[1];return y};y.precision=function(l){if(!arguments.length)return g;g=+l;d=oM(o,r,90);h=aM(e,t,g);p=oM(s,a,90);m=aM(i,n,g);return y};return y.extentMajor([[-180,-90+Wb],[180,90-Wb]]).extentMinor([[-180,-80-Wb],[180,80+Wb]])}function lM(){return sM()()}var uM=n(16372);function fM(){}const cM=[[],[[[1,1.5],[.5,1]]],[[[1.5,1],[1,1.5]]],[[[1.5,1],[.5,1]]],[[[1,.5],[1.5,1]]],[[[1,1.5],[.5,1]],[[1,.5],[1.5,1]]],[[[1,.5],[1,1.5]]],[[[1,.5],[.5,1]]],[[[.5,1],[1,.5]]],[[[1,1.5],[1,.5]]],[[[.5,1],[1,.5]],[[1.5,1],[1,1.5]]],[[[1.5,1],[1,.5]]],[[[.5,1],[1.5,1]]],[[[1,1.5],[1.5,1]]],[[[.5,1],[1,1.5]]],[]];function dM(){var t=1,e=1,n=s;function i(t,e){return e.map((e=>r(t,e)))}function r(t,e){var i=[],r=[];o(t,e,(o=>{n(o,t,e);if(hM(o)>0)i.push([o]);else r.push(o)}));r.forEach((t=>{for(var e=0,n=i.length,r;e<n;++e){if(pM((r=i[e])[0],t)!==-1){r.push(t);return}}}));return{type:"MultiPolygon",value:e,coordinates:i}}function o(n,i,r){var o=new Array,s=new Array,l,u,f,c,d,h;l=u=-1;c=n[0]>=i;cM[c<<1].forEach(p);while(++l<t-1){f=c,c=n[l+1]>=i;cM[f|c<<1].forEach(p)}cM[c<<0].forEach(p);while(++u<e-1){l=-1;c=n[u*t+t]>=i;d=n[u*t]>=i;cM[c<<1|d<<2].forEach(p);while(++l<t-1){f=c,c=n[u*t+t+l+1]>=i;h=d,d=n[u*t+l+1]>=i;cM[f|c<<1|d<<2|h<<3].forEach(p)}cM[c|d<<3].forEach(p)}l=-1;d=n[u*t]>=i;cM[d<<2].forEach(p);while(++l<t-1){h=d,d=n[u*t+l+1]>=i;cM[d<<2|h<<3].forEach(p)}cM[d<<3].forEach(p);function p(t){var e=[t[0][0]+l,t[0][1]+u],n=[t[1][0]+l,t[1][1]+u],i=a(e),f=a(n),c,d;if(c=s[i]){if(d=o[f]){delete s[c.end];delete o[d.start];if(c===d){c.ring.push(n);r(c.ring)}else{o[c.start]=s[d.end]={start:c.start,end:d.end,ring:c.ring.concat(d.ring)}}}else{delete s[c.end];c.ring.push(n);s[c.end=f]=c}}else if(c=o[f]){if(d=s[i]){delete o[c.start];delete s[d.end];if(c===d){c.ring.push(n);r(c.ring)}else{o[d.start]=s[c.end]={start:d.start,end:c.end,ring:d.ring.concat(c.ring)}}}else{delete o[c.start];c.ring.unshift(e);o[c.start=i]=c}}else{o[i]=s[f]={start:i,end:f,ring:[e,n]}}}}function a(e){return e[0]*2+e[1]*(t+1)*4}function s(n,i,r){n.forEach((n=>{var o=n[0],a=n[1],s=o|0,l=a|0,u,f=i[l*t+s];if(o>0&&o<t&&s===o){u=i[l*t+s-1];n[0]=o+(r-u)/(f-u)-.5}if(a>0&&a<e&&l===a){u=i[(l-1)*t+s];n[1]=a+(r-u)/(f-u)-.5}}))}i.contour=r;i.size=function(n){if(!arguments.length)return[t,e];var r=Math.floor(n[0]),o=Math.floor(n[1]);if(!(r>=0&&o>=0))(0,p.vU)("invalid size");return t=r,e=o,i};i.smooth=function(t){return arguments.length?(n=t?s:fM,i):n===s};return i}function hM(t){var e=0,n=t.length,i=t[n-1][1]*t[0][0]-t[n-1][0]*t[0][1];while(++e<n)i+=t[e-1][1]*t[e][0]-t[e-1][0]*t[e][1];return i}function pM(t,e){var n=-1,i=e.length,r;while(++n<i)if(r=mM(t,e[n]))return r;return 0}function mM(t,e){var n=e[0],i=e[1],r=-1;for(var o=0,a=t.length,s=a-1;o<a;s=o++){var l=t[o],u=l[0],f=l[1],c=t[s],d=c[0],h=c[1];if(gM(l,c,e))return 0;if(f>i!==h>i&&n<(d-u)*(i-f)/(h-f)+u)r=-r}return r}function gM(t,e,n){var i;return yM(t,e,n)&&vM(t[i=+(t[0]===e[0])],n[i],e[i])}function yM(t,e,n){return(e[0]-t[0])*(n[1]-t[1])===(n[0]-t[0])*(e[1]-t[1])}function vM(t,e,n){return t<=e&&e<=n||n<=e&&e<=t}function bM(t,e,n){return function(i){var r=(0,p.We)(i),o=n?Math.min(r[0],0):r[0],a=r[1],s=a-o,l=e?(0,I.ly)(o,a,t):s/(t+1);return(0,to.Z)(o+l,a,l)}}function xM(t){Oi.call(this,null,t)}xM.Definition={type:"Isocontour",metadata:{generates:true},params:[{name:"field",type:"field"},{name:"thresholds",type:"number",array:true},{name:"levels",type:"number"},{name:"nice",type:"boolean",default:false},{name:"resolve",type:"enum",values:["shared","independent"],default:"independent"},{name:"zero",type:"boolean",default:true},{name:"smooth",type:"boolean",default:true},{name:"scale",type:"number",expr:true},{name:"translate",type:"number",array:true,expr:true},{name:"as",type:"string",null:true,default:"contour"}]};(0,p.XW)(xM,Oi,{transform(t,e){if(this.value&&!e.changed()&&!t.modified()){return e.StopPropagation}var n=e.fork(e.NO_SOURCE|e.NO_FIELDS),i=e.materialize(e.SOURCE).source,r=t.field||p.yR,o=dM().smooth(t.smooth!==false),a=t.thresholds||_M(i,r,t),s=t.as===null?null:t.as||"contour",l=[];i.forEach((e=>{const n=r(e);const i=o.size([n.width,n.height])(n.values,(0,p.kJ)(a)?a:a(n.values));wM(i,n,e,t);i.forEach((t=>{l.push(_n(e,bn(s!=null?{[s]:t}:t)))}))}));if(this.value)n.rem=this.value;this.value=n.source=n.add=l;return n}});function _M(t,e,n){const i=bM(n.levels||10,n.nice,n.zero!==false);return n.resolve!=="shared"?i:i(t.map((t=>(0,Ii.Z)(e(t).values))))}function wM(t,e,n,i){let r=i.scale||e.scale,o=i.translate||e.translate;if((0,p.mf)(r))r=r(n,i);if((0,p.mf)(o))o=o(n,i);if((r===1||r==null)&&!o)return;const a=((0,p.hj)(r)?r:r[0])||1,s=((0,p.hj)(r)?r:r[1])||1,l=o&&o[0]||0,u=o&&o[1]||0;t.forEach(kM(e,a,s,l,u))}function kM(t,e,n,i,r){const o=t.x1||0,a=t.y1||0,s=e*n<0;function l(t){t.forEach(u)}function u(t){if(s)t.reverse();t.forEach(f)}function f(t){t[0]=(t[0]-o)*e+i;t[1]=(t[1]-a)*n+r}return function(t){t.coordinates.forEach(l);return t}}function MM(t,e,n){const i=t>=0?t:er(e,n);return Math.round((Math.sqrt(4*i*i+1)-1)/2)}function EM(t){return(0,p.mf)(t)?t:(0,p.a9)(+t)}function SM(){var t=t=>t[0],e=t=>t[1],n=p.kX,i=[-1,-1],r=960,o=500,a=2;function s(s,l){const u=MM(i[0],s,t)>>a,f=MM(i[1],s,e)>>a,c=u?u+2:0,d=f?f+2:0,h=2*c+(r>>a),p=2*d+(o>>a),m=new Float32Array(h*p),g=new Float32Array(h*p);let y=m;s.forEach((i=>{const r=c+(+t(i)>>a),o=d+(+e(i)>>a);if(r>=0&&r<h&&o>=0&&o<p){m[r+o*h]+=+n(i)}}));if(u>0&&f>0){zM(h,p,m,g,u);DM(h,p,g,m,f);zM(h,p,m,g,u);DM(h,p,g,m,f);zM(h,p,m,g,u);DM(h,p,g,m,f)}else if(u>0){zM(h,p,m,g,u);zM(h,p,g,m,u);zM(h,p,m,g,u);y=g}else if(f>0){DM(h,p,m,g,f);DM(h,p,g,m,f);DM(h,p,m,g,f);y=g}const v=l?Math.pow(2,-2*a):1/Cv(y);for(let t=0,e=h*p;t<e;++t)y[t]*=v;return{values:y,scale:1<<a,width:h,height:p,x1:c,y1:d,x2:c+(r>>a),y2:d+(o>>a)}}s.x=function(e){return arguments.length?(t=EM(e),s):t};s.y=function(t){return arguments.length?(e=EM(t),s):e};s.weight=function(t){return arguments.length?(n=EM(t),s):n};s.size=function(t){if(!arguments.length)return[r,o];var e=+t[0],n=+t[1];if(!(e>=0&&n>=0))(0,p.vU)("invalid size");return r=e,o=n,s};s.cellSize=function(t){if(!arguments.length)return 1<<a;if(!((t=+t)>=1))(0,p.vU)("invalid cell size");a=Math.floor(Math.log(t)/Math.LN2);return s};s.bandwidth=function(t){if(!arguments.length)return i;t=(0,p.IX)(t);if(t.length===1)t=[+t[0],+t[0]];if(t.length!==2)(0,p.vU)("invalid bandwidth");return i=t,s};return s}function zM(t,e,n,i,r){const o=(r<<1)+1;for(let a=0;a<e;++a){for(let e=0,s=0;e<t+r;++e){if(e<t){s+=n[e+a*t]}if(e>=r){if(e>=o){s-=n[e-o+a*t]}i[e-r+a*t]=s/Math.min(e+1,t-1+o-e,o)}}}}function DM(t,e,n,i,r){const o=(r<<1)+1;for(let a=0;a<t;++a){for(let s=0,l=0;s<e+r;++s){if(s<e){l+=n[a+s*t]}if(s>=r){if(s>=o){l-=n[a+(s-o)*t]}i[a+(s-r)*t]=l/Math.min(s+1,e-1+o-s,o)}}}}function RM(t){Oi.call(this,null,t)}RM.Definition={type:"KDE2D",metadata:{generates:true},params:[{name:"size",type:"number",array:true,length:2,required:true},{name:"x",type:"field",required:true},{name:"y",type:"field",required:true},{name:"weight",type:"field"},{name:"groupby",type:"field",array:true},{name:"cellSize",type:"number"},{name:"bandwidth",type:"number",array:true,length:2},{name:"counts",type:"boolean",default:false},{name:"as",type:"string",default:"grid"}]};const AM=["x","y","weight","size","cellSize","bandwidth"];function $M(t,e){AM.forEach((n=>e[n]!=null?t[n](e[n]):0));return t}(0,p.XW)(RM,Oi,{transform(t,e){if(this.value&&!e.changed()&&!t.modified())return e.StopPropagation;var n=e.fork(e.NO_SOURCE|e.NO_FIELDS),i=e.materialize(e.SOURCE).source,r=OM(i,t.groupby),o=(t.groupby||[]).map(p.el),a=$M(SM(),t),s=t.as||"grid",l=[];function u(t,e){for(let n=0;n<o.length;++n)t[o[n]]=e[n];return t}l=r.map((e=>bn(u({[s]:a(e,t.counts)},e.dims))));if(this.value)n.rem=this.value;this.value=n.source=n.add=l;return n}});function OM(t,e){var n=[],i=t=>t(s),r,o,a,s,l,u;if(e==null){n.push(t)}else{for(r={},o=0,a=t.length;o<a;++o){s=t[o];l=e.map(i);u=r[l];if(!u){r[l]=u=[];u.dims=l;n.push(u)}u.push(s)}}return n}function TM(t){Oi.call(this,null,t)}TM.Definition={type:"Contour",metadata:{generates:true},params:[{name:"size",type:"number",array:true,length:2,required:true},{name:"values",type:"number",array:true},{name:"x",type:"field"},{name:"y",type:"field"},{name:"weight",type:"field"},{name:"cellSize",type:"number"},{name:"bandwidth",type:"number"},{name:"count",type:"number"},{name:"nice",type:"boolean",default:false},{name:"thresholds",type:"number",array:true},{name:"smooth",type:"boolean",default:true}]};(0,p.XW)(TM,Oi,{transform(t,e){if(this.value&&!e.changed()&&!t.modified()){return e.StopPropagation}var n=e.fork(e.NO_SOURCE|e.NO_FIELDS),i=dM().smooth(t.smooth!==false),r=t.values,o=t.thresholds||bM(t.count||10,t.nice,!!r),a=t.size,s,l;if(!r){r=e.materialize(e.SOURCE).source;s=$M(SM(),t)(r,true);l=kM(s,s.scale||1,s.scale||1,0,0);a=[s.width,s.height];r=s.values}o=(0,p.kJ)(o)?o:o(r);r=i.size(a)(r,o);if(l)r.forEach(l);if(this.value)n.rem=this.value;this.value=n.source=n.add=(r||[]).map(bn);return n}});const CM="Feature";const NM="FeatureCollection";const UM="MultiPoint";function IM(t){Oi.call(this,null,t)}IM.Definition={type:"GeoJSON",metadata:{},params:[{name:"fields",type:"field",array:true,length:2},{name:"geojson",type:"field"}]};(0,p.XW)(IM,Oi,{transform(t,e){var n=this._features,i=this._points,r=t.fields,o=r&&r[0],a=r&&r[1],s=t.geojson||!r&&p.yR,l=e.ADD,u;u=t.modified()||e.changed(e.REM)||e.modified((0,p.Oj)(s))||o&&e.modified((0,p.Oj)(o))||a&&e.modified((0,p.Oj)(a));if(!this.value||u){l=e.SOURCE;this._features=n=[];this._points=i=[]}if(s){e.visit(l,(t=>n.push(s(t))))}if(o&&a){e.visit(l,(t=>{var e=o(t),n=a(t);if(e!=null&&n!=null&&(e=+e)===e&&(n=+n)===n){i.push([e,n])}}));n=n.concat({type:CM,geometry:{type:UM,coordinates:i}})}this.value={type:NM,features:n}}});function PM(t){Oi.call(this,null,t)}PM.Definition={type:"GeoPath",metadata:{modifies:true},params:[{name:"projection",type:"projection"},{name:"field",type:"field"},{name:"pointRadius",type:"number",expr:true},{name:"as",type:"string",default:"path"}]};(0,p.XW)(PM,Oi,{transform(t,e){var n=e.fork(e.ALL),i=this.value,r=t.field||p.yR,o=t.as||"path",a=n.SOURCE;if(!i||t.modified()){this.value=i=iM(t.projection);n.materialize().reflow()}else{a=r===p.yR||e.modified(r.fields)?n.ADD_MOD:n.ADD}const s=qM(i,t.pointRadius);n.visit(a,(t=>t[o]=i(r(t))));i.pointRadius(s);return n.modifies(o)}});function qM(t,e){const n=t.pointRadius();t.context(null);if(e!=null){t.pointRadius(e)}return n}function LM(t){Oi.call(this,null,t)}LM.Definition={type:"GeoPoint",metadata:{modifies:true},params:[{name:"projection",type:"projection",required:true},{name:"fields",type:"field",array:true,required:true,length:2},{name:"as",type:"string",array:true,length:2,default:["x","y"]}]};(0,p.XW)(LM,Oi,{transform(t,e){var n=t.projection,i=t.fields[0],r=t.fields[1],o=t.as||["x","y"],a=o[0],s=o[1],l;function u(t){const e=n([i(t),r(t)]);if(e){t[a]=e[0];t[s]=e[1]}else{t[a]=undefined;t[s]=undefined}}if(t.modified()){e=e.materialize().reflow(true).visit(e.SOURCE,u)}else{l=e.modified(i.fields)||e.modified(r.fields);e.visit(l?e.ADD_MOD:e.ADD,u)}return e.modifies(o)}});function jM(t){Oi.call(this,null,t)}jM.Definition={type:"GeoShape",metadata:{modifies:true,nomod:true},params:[{name:"projection",type:"projection"},{name:"field",type:"field",default:"datum"},{name:"pointRadius",type:"number",expr:true},{name:"as",type:"string",default:"shape"}]};(0,p.XW)(jM,Oi,{transform(t,e){var n=e.fork(e.ALL),i=this.value,r=t.as||"shape",o=n.ADD;if(!i||t.modified()){this.value=i=FM(iM(t.projection),t.field||(0,p.EP)("datum"),t.pointRadius);n.materialize().reflow();o=n.SOURCE}n.visit(o,(t=>t[r]=i));return n.modifies(r)}});function FM(t,e,n){const i=n==null?n=>t(e(n)):i=>{var r=t.pointRadius(),o=t.pointRadius(n)(e(i));t.pointRadius(r);return o};i.context=e=>{t.context(e);return i};return i}function WM(t){Oi.call(this,[],t);this.generator=sM()}WM.Definition={type:"Graticule",metadata:{changes:true,generates:true},params:[{name:"extent",type:"array",array:true,length:2,content:{type:"number",array:true,length:2}},{name:"extentMajor",type:"array",array:true,length:2,content:{type:"number",array:true,length:2}},{name:"extentMinor",type:"array",array:true,length:2,content:{type:"number",array:true,length:2}},{name:"step",type:"number",array:true,length:2},{name:"stepMajor",type:"number",array:true,length:2,default:[90,360]},{name:"stepMinor",type:"number",array:true,length:2,default:[10,10]},{name:"precision",type:"number",default:2.5}]};(0,p.XW)(WM,Oi,{transform(t,e){var n=this.value,i=this.generator,r;if(!n.length||t.modified()){for(const e in t){if((0,p.mf)(i[e])){i[e](t[e])}}}r=i();if(n.length){e.mod.push(wn(n[0],r))}else{e.add.push(bn(r))}n[0]=r;return e}});function XM(t){Oi.call(this,null,t)}XM.Definition={type:"heatmap",metadata:{modifies:true},params:[{name:"field",type:"field"},{name:"color",type:"string",expr:true},{name:"opacity",type:"number",expr:true},{name:"resolve",type:"enum",values:["shared","independent"],default:"independent"},{name:"as",type:"string",default:"image"}]};(0,p.XW)(XM,Oi,{transform(t,e){if(!e.changed()&&!t.modified()){return e.StopPropagation}var n=e.materialize(e.SOURCE).source,i=t.resolve==="shared",r=t.field||p.yR,o=ZM(t.opacity,t),a=BM(t.color,t),s=t.as||"image",l={$x:0,$y:0,$value:0,$max:i?(0,Ii.Z)(n.map((t=>(0,Ii.Z)(r(t).values)))):0};n.forEach((t=>{const e=r(t);const n=(0,p.l7)({},t,l);if(!i)n.$max=(0,Ii.Z)(e.values||[]);t[s]=JM(e,n,a.dep?a:(0,p.a9)(a(n)),o.dep?o:(0,p.a9)(o(n)))}));return e.reflow(true).modifies(s)}});function BM(t,e){let n;if((0,p.mf)(t)){n=n=>(0,uM.B8)(t(n,e));n.dep=HM(t)}else{n=(0,p.a9)((0,uM.B8)(t||"#888"))}return n}function ZM(t,e){let n;if((0,p.mf)(t)){n=n=>t(n,e);n.dep=HM(t)}else if(t){n=(0,p.a9)(t)}else{n=t=>t.$value/t.$max||0;n.dep=true}return n}function HM(t){if(!(0,p.mf)(t))return false;const e=(0,p.Rg)((0,p.Oj)(t));return e.$x||e.$y||e.$value||e.$max}function JM(t,e,n,i){const r=t.width,o=t.height,a=t.x1||0,s=t.y1||0,l=t.x2||r,u=t.y2||o,f=t.values,c=f?t=>f[t]:p.bM,d=Ks(l-a,u-s),h=d.getContext("2d"),m=h.getImageData(0,0,l-a,u-s),g=m.data;for(let p=s,y=0;p<u;++p){e.$y=p-s;for(let t=a,o=p*r;t<l;++t,y+=4){e.$x=t-a;e.$value=c(t+o);const r=n(e);g[y+0]=r.r;g[y+1]=r.g;g[y+2]=r.b;g[y+3]=~~(255*i(e))}}h.putImageData(m,0,0);return d}function GM(t){Oi.call(this,null,t);this.modified(true)}(0,p.XW)(GM,Oi,{transform(t,e){let n=this.value;if(!n||t.modified("type")){this.value=n=VM(t.type);tM.forEach((e=>{if(t[e]!=null)YM(n,e,t[e])}))}else{tM.forEach((e=>{if(t.modified(e))YM(n,e,t[e])}))}if(t.pointRadius!=null)n.path.pointRadius(t.pointRadius);if(t.fit)KM(n,t);return e.fork(e.NO_SOURCE|e.NO_FIELDS)}});function KM(t,e){const n=QM(e.fit);e.extent?t.fitExtent(e.extent,n):e.size?t.fitSize(e.size,n):0}function VM(t){const e=nM((t||"mercator").toLowerCase());if(!e)(0,p.vU)("Unrecognized projection type: "+t);return e()}function YM(t,e,n){if((0,p.mf)(t[e]))t[e](n)}function QM(t){t=(0,p.IX)(t);return t.length===1?t[0]:{type:NM,features:t.reduce(((t,e)=>t.concat(tE(e))),[])}}function tE(t){return t.type===NM?t.features:(0,p.IX)(t).filter((t=>t!=null)).map((t=>t.type===CM?t:{type:CM,geometry:t}))}function eE(t,e){var n,i=1;if(t==null)t=0;if(e==null)e=0;function r(){var r,o=n.length,a,s=0,l=0;for(r=0;r<o;++r){a=n[r],s+=a.x,l+=a.y}for(s=(s/o-t)*i,l=(l/o-e)*i,r=0;r<o;++r){a=n[r],a.x-=s,a.y-=l}}r.initialize=function(t){n=t};r.x=function(e){return arguments.length?(t=+e,r):t};r.y=function(t){return arguments.length?(e=+t,r):e};r.strength=function(t){return arguments.length?(i=+t,r):i};return r}function nE(t){const e=+this._x.call(null,t),n=+this._y.call(null,t);return iE(this.cover(e,n),e,n,t)}function iE(t,e,n,i){if(isNaN(e)||isNaN(n))return t;var r,o=t._root,a={data:i},s=t._x0,l=t._y0,u=t._x1,f=t._y1,c,d,h,p,m,g,y,v;if(!o)return t._root=a,t;while(o.length){if(m=e>=(c=(s+u)/2))s=c;else u=c;if(g=n>=(d=(l+f)/2))l=d;else f=d;if(r=o,!(o=o[y=g<<1|m]))return r[y]=a,t}h=+t._x.call(null,o.data);p=+t._y.call(null,o.data);if(e===h&&n===p)return a.next=o,r?r[y]=a:t._root=a,t;do{r=r?r[y]=new Array(4):t._root=new Array(4);if(m=e>=(c=(s+u)/2))s=c;else u=c;if(g=n>=(d=(l+f)/2))l=d;else f=d}while((y=g<<1|m)===(v=(p>=d)<<1|h>=c));return r[v]=o,r[y]=a,t}function rE(t){var e,n,i=t.length,r,o,a=new Array(i),s=new Array(i),l=Infinity,u=Infinity,f=-Infinity,c=-Infinity;for(n=0;n<i;++n){if(isNaN(r=+this._x.call(null,e=t[n]))||isNaN(o=+this._y.call(null,e)))continue;a[n]=r;s[n]=o;if(r<l)l=r;if(r>f)f=r;if(o<u)u=o;if(o>c)c=o}if(l>f||u>c)return this;this.cover(l,u).cover(f,c);for(n=0;n<i;++n){iE(this,a[n],s[n],t[n])}return this}function oE(t,e){if(isNaN(t=+t)||isNaN(e=+e))return this;var n=this._x0,i=this._y0,r=this._x1,o=this._y1;if(isNaN(n)){r=(n=Math.floor(t))+1;o=(i=Math.floor(e))+1}else{var a=r-n||1,s=this._root,l,u;while(n>t||t>=r||i>e||e>=o){u=(e<i)<<1|t<n;l=new Array(4),l[u]=s,s=l,a*=2;switch(u){case 0:r=n+a,o=i+a;break;case 1:n=r-a,o=i+a;break;case 2:r=n+a,i=o-a;break;case 3:n=r-a,i=o-a;break}}if(this._root&&this._root.length)this._root=s}this._x0=n;this._y0=i;this._x1=r;this._y1=o;return this}function aE(){var t=[];this.visit((function(e){if(!e.length)do{t.push(e.data)}while(e=e.next)}));return t}function sE(t){return arguments.length?this.cover(+t[0][0],+t[0][1]).cover(+t[1][0],+t[1][1]):isNaN(this._x0)?undefined:[[this._x0,this._y0],[this._x1,this._y1]]}function lE(t,e,n,i,r){this.node=t;this.x0=e;this.y0=n;this.x1=i;this.y1=r}function uE(t,e,n){var i,r=this._x0,o=this._y0,a,s,l,u,f=this._x1,c=this._y1,d=[],h=this._root,p,m;if(h)d.push(new lE(h,r,o,f,c));if(n==null)n=Infinity;else{r=t-n,o=e-n;f=t+n,c=e+n;n*=n}while(p=d.pop()){if(!(h=p.node)||(a=p.x0)>f||(s=p.y0)>c||(l=p.x1)<r||(u=p.y1)<o)continue;if(h.length){var g=(a+l)/2,y=(s+u)/2;d.push(new lE(h[3],g,y,l,u),new lE(h[2],a,y,g,u),new lE(h[1],g,s,l,y),new lE(h[0],a,s,g,y));if(m=(e>=y)<<1|t>=g){p=d[d.length-1];d[d.length-1]=d[d.length-1-m];d[d.length-1-m]=p}}else{var v=t-+this._x.call(null,h.data),b=e-+this._y.call(null,h.data),x=v*v+b*b;if(x<n){var _=Math.sqrt(n=x);r=t-_,o=e-_;f=t+_,c=e+_;i=h.data}}}return i}function fE(t){if(isNaN(f=+this._x.call(null,t))||isNaN(c=+this._y.call(null,t)))return this;var e,n=this._root,i,r,o,a=this._x0,s=this._y0,l=this._x1,u=this._y1,f,c,d,h,p,m,g,y;if(!n)return this;if(n.length)while(true){if(p=f>=(d=(a+l)/2))a=d;else l=d;if(m=c>=(h=(s+u)/2))s=h;else u=h;if(!(e=n,n=n[g=m<<1|p]))return this;if(!n.length)break;if(e[g+1&3]||e[g+2&3]||e[g+3&3])i=e,y=g}while(n.data!==t)if(!(r=n,n=n.next))return this;if(o=n.next)delete n.next;if(r)return o?r.next=o:delete r.next,this;if(!e)return this._root=o,this;o?e[g]=o:delete e[g];if((n=e[0]||e[1]||e[2]||e[3])&&n===(e[3]||e[2]||e[1]||e[0])&&!n.length){if(i)i[y]=n;else this._root=n}return this}function cE(t){for(var e=0,n=t.length;e<n;++e)this.remove(t[e]);return this}function dE(){return this._root}function hE(){var t=0;this.visit((function(e){if(!e.length)do{++t}while(e=e.next)}));return t}function pE(t){var e=[],n,i=this._root,r,o,a,s,l;if(i)e.push(new lE(i,this._x0,this._y0,this._x1,this._y1));while(n=e.pop()){if(!t(i=n.node,o=n.x0,a=n.y0,s=n.x1,l=n.y1)&&i.length){var u=(o+s)/2,f=(a+l)/2;if(r=i[3])e.push(new lE(r,u,f,s,l));if(r=i[2])e.push(new lE(r,o,f,u,l));if(r=i[1])e.push(new lE(r,u,a,s,f));if(r=i[0])e.push(new lE(r,o,a,u,f))}}return this}function mE(t){var e=[],n=[],i;if(this._root)e.push(new lE(this._root,this._x0,this._y0,this._x1,this._y1));while(i=e.pop()){var r=i.node;if(r.length){var o,a=i.x0,s=i.y0,l=i.x1,u=i.y1,f=(a+l)/2,c=(s+u)/2;if(o=r[0])e.push(new lE(o,a,s,f,c));if(o=r[1])e.push(new lE(o,f,s,l,c));if(o=r[2])e.push(new lE(o,a,c,f,u));if(o=r[3])e.push(new lE(o,f,c,l,u))}n.push(i)}while(i=n.pop()){t(i.node,i.x0,i.y0,i.x1,i.y1)}return this}function gE(t){return t[0]}function yE(t){return arguments.length?(this._x=t,this):this._x}function vE(t){return t[1]}function bE(t){return arguments.length?(this._y=t,this):this._y}function xE(t,e,n){var i=new _E(e==null?gE:e,n==null?vE:n,NaN,NaN,NaN,NaN);return t==null?i:i.addAll(t)}function _E(t,e,n,i,r,o){this._x=t;this._y=e;this._x0=n;this._y0=i;this._x1=r;this._y1=o;this._root=undefined}function wE(t){var e={data:t.data},n=e;while(t=t.next)n=n.next={data:t.data};return e}var kE=xE.prototype=_E.prototype;kE.copy=function(){var t=new _E(this._x,this._y,this._x0,this._y0,this._x1,this._y1),e=this._root,n,i;if(!e)return t;if(!e.length)return t._root=wE(e),t;n=[{source:e,target:t._root=new Array(4)}];while(e=n.pop()){for(var r=0;r<4;++r){if(i=e.source[r]){if(i.length)n.push({source:i,target:e.target[r]=new Array(4)});else e.target[r]=wE(i)}}}return t};kE.add=nE;kE.addAll=rE;kE.cover=oE;kE.data=aE;kE.extent=sE;kE.find=uE;kE.remove=fE;kE.removeAll=cE;kE.root=dE;kE.size=hE;kE.visit=pE;kE.visitAfter=mE;kE.x=yE;kE.y=bE;function ME(t){return function(){return t}}function EE(t){return(t()-.5)*1e-6}function SE(t){return t.x+t.vx}function zE(t){return t.y+t.vy}function DE(t){var e,n,i,r=1,o=1;if(typeof t!=="function")t=ME(t==null?1:+t);function a(){var t,a=e.length,l,u,f,c,d,h;for(var p=0;p<o;++p){l=xE(e,SE,zE).visitAfter(s);for(t=0;t<a;++t){u=e[t];d=n[u.index],h=d*d;f=u.x+u.vx;c=u.y+u.vy;l.visit(m)}}function m(t,e,n,o,a){var s=t.data,l=t.r,p=d+l;if(s){if(s.index>u.index){var m=f-s.x-s.vx,g=c-s.y-s.vy,y=m*m+g*g;if(y<p*p){if(m===0)m=EE(i),y+=m*m;if(g===0)g=EE(i),y+=g*g;y=(p-(y=Math.sqrt(y)))/y*r;u.vx+=(m*=y)*(p=(l*=l)/(h+l));u.vy+=(g*=y)*p;s.vx-=m*(p=1-p);s.vy-=g*p}}return}return e>f+p||o<f-p||n>c+p||a<c-p}}function s(t){if(t.data)return t.r=n[t.data.index];for(var e=t.r=0;e<4;++e){if(t[e]&&t[e].r>t.r){t.r=t[e].r}}}function l(){if(!e)return;var i,r=e.length,o;n=new Array(r);for(i=0;i<r;++i)o=e[i],n[o.index]=+t(o,i,e)}a.initialize=function(t,n){e=t;i=n;l()};a.iterations=function(t){return arguments.length?(o=+t,a):o};a.strength=function(t){return arguments.length?(r=+t,a):r};a.radius=function(e){return arguments.length?(t=typeof e==="function"?e:ME(+e),l(),a):t};return a}var RE=n(92626);var AE=n(35374);const $E=1664525;const OE=1013904223;const TE=4294967296;function CE(){let t=1;return()=>(t=($E*t+OE)%TE)/TE}function NE(t){return t.x}function UE(t){return t.y}var IE=10,PE=Math.PI*(3-Math.sqrt(5));function qE(t){var e,n=1,i=.001,r=1-Math.pow(i,1/300),o=0,a=.6,s=new Map,l=(0,AE.HT)(c),u=(0,RE.Z)("tick","end"),f=CE();if(t==null)t=[];function c(){d();u.call("tick",e);if(n<i){l.stop();u.call("end",e)}}function d(i){var l,u=t.length,f;if(i===undefined)i=1;for(var c=0;c<i;++c){n+=(o-n)*r;s.forEach((function(t){t(n)}));for(l=0;l<u;++l){f=t[l];if(f.fx==null)f.x+=f.vx*=a;else f.x=f.fx,f.vx=0;if(f.fy==null)f.y+=f.vy*=a;else f.y=f.fy,f.vy=0}}return e}function h(){for(var e=0,n=t.length,i;e<n;++e){i=t[e],i.index=e;if(i.fx!=null)i.x=i.fx;if(i.fy!=null)i.y=i.fy;if(isNaN(i.x)||isNaN(i.y)){var r=IE*Math.sqrt(.5+e),o=e*PE;i.x=r*Math.cos(o);i.y=r*Math.sin(o)}if(isNaN(i.vx)||isNaN(i.vy)){i.vx=i.vy=0}}}function p(e){if(e.initialize)e.initialize(t,f);return e}h();return e={tick:d,restart:function(){return l.restart(c),e},stop:function(){return l.stop(),e},nodes:function(n){return arguments.length?(t=n,h(),s.forEach(p),e):t},alpha:function(t){return arguments.length?(n=+t,e):n},alphaMin:function(t){return arguments.length?(i=+t,e):i},alphaDecay:function(t){return arguments.length?(r=+t,e):+r},alphaTarget:function(t){return arguments.length?(o=+t,e):o},velocityDecay:function(t){return arguments.length?(a=1-t,e):1-a},randomSource:function(t){return arguments.length?(f=t,s.forEach(p),e):f},force:function(t,n){return arguments.length>1?(n==null?s.delete(t):s.set(t,p(n)),e):s.get(t)},find:function(e,n,i){var r=0,o=t.length,a,s,l,u,f;if(i==null)i=Infinity;else i*=i;for(r=0;r<o;++r){u=t[r];a=e-u.x;s=n-u.y;l=a*a+s*s;if(l<i)f=u,i=l}return f},on:function(t,n){return arguments.length>1?(u.on(t,n),e):u.on(t)}}}function LE(){var t,e,n,i,r=ME(-30),o,a=1,s=Infinity,l=.81;function u(n){var r,o=t.length,a=xE(t,NE,UE).visitAfter(c);for(i=n,r=0;r<o;++r)e=t[r],a.visit(d)}function f(){if(!t)return;var e,n=t.length,i;o=new Array(n);for(e=0;e<n;++e)i=t[e],o[i.index]=+r(i,e,t)}function c(t){var e=0,n,i,r=0,a,s,l;if(t.length){for(a=s=l=0;l<4;++l){if((n=t[l])&&(i=Math.abs(n.value))){e+=n.value,r+=i,a+=i*n.x,s+=i*n.y}}t.x=a/r;t.y=s/r}else{n=t;n.x=n.data.x;n.y=n.data.y;do{e+=o[n.data.index]}while(n=n.next)}t.value=e}function d(t,r,u,f){if(!t.value)return true;var c=t.x-e.x,d=t.y-e.y,h=f-r,p=c*c+d*d;if(h*h/l<p){if(p<s){if(c===0)c=EE(n),p+=c*c;if(d===0)d=EE(n),p+=d*d;if(p<a)p=Math.sqrt(a*p);e.vx+=c*t.value*i/p;e.vy+=d*t.value*i/p}return true}else if(t.length||p>=s)return;if(t.data!==e||t.next){if(c===0)c=EE(n),p+=c*c;if(d===0)d=EE(n),p+=d*d;if(p<a)p=Math.sqrt(a*p)}do{if(t.data!==e){h=o[t.data.index]*i/p;e.vx+=c*h;e.vy+=d*h}}while(t=t.next)}u.initialize=function(e,i){t=e;n=i;f()};u.strength=function(t){return arguments.length?(r=typeof t==="function"?t:ME(+t),f(),u):r};u.distanceMin=function(t){return arguments.length?(a=t*t,u):Math.sqrt(a)};u.distanceMax=function(t){return arguments.length?(s=t*t,u):Math.sqrt(s)};u.theta=function(t){return arguments.length?(l=t*t,u):Math.sqrt(l)};return u}function jE(t){return t.index}function FE(t,e){var n=t.get(e);if(!n)throw new Error("node not found: "+e);return n}function WE(t){var e=jE,n=c,i,r=ME(30),o,a,s,l,u,f=1;if(t==null)t=[];function c(t){return 1/Math.min(s[t.source.index],s[t.target.index])}function d(e){for(var n=0,r=t.length;n<f;++n){for(var a=0,s,c,d,h,p,m,g;a<r;++a){s=t[a],c=s.source,d=s.target;h=d.x+d.vx-c.x-c.vx||EE(u);p=d.y+d.vy-c.y-c.vy||EE(u);m=Math.sqrt(h*h+p*p);m=(m-o[a])/m*e*i[a];h*=m,p*=m;d.vx-=h*(g=l[a]);d.vy-=p*g;c.vx+=h*(g=1-g);c.vy+=p*g}}}function h(){if(!a)return;var n,r=a.length,u=t.length,f=new Map(a.map(((t,n)=>[e(t,n,a),t]))),c;for(n=0,s=new Array(r);n<u;++n){c=t[n],c.index=n;if(typeof c.source!=="object")c.source=FE(f,c.source);if(typeof c.target!=="object")c.target=FE(f,c.target);s[c.source.index]=(s[c.source.index]||0)+1;s[c.target.index]=(s[c.target.index]||0)+1}for(n=0,l=new Array(u);n<u;++n){c=t[n],l[n]=s[c.source.index]/(s[c.source.index]+s[c.target.index])}i=new Array(u),p();o=new Array(u),m()}function p(){if(!a)return;for(var e=0,r=t.length;e<r;++e){i[e]=+n(t[e],e,t)}}function m(){if(!a)return;for(var e=0,n=t.length;e<n;++e){o[e]=+r(t[e],e,t)}}d.initialize=function(t,e){a=t;u=e;h()};d.links=function(e){return arguments.length?(t=e,h(),d):t};d.id=function(t){return arguments.length?(e=t,d):e};d.iterations=function(t){return arguments.length?(f=+t,d):f};d.strength=function(t){return arguments.length?(n=typeof t==="function"?t:ME(+t),p(),d):n};d.distance=function(t){return arguments.length?(r=typeof t==="function"?t:ME(+t),m(),d):r};return d}function XE(t){var e=ME(.1),n,i,r;if(typeof t!=="function")t=ME(t==null?0:+t);function o(t){for(var e=0,o=n.length,a;e<o;++e){a=n[e],a.vx+=(r[e]-a.x)*i[e]*t}}function a(){if(!n)return;var o,a=n.length;i=new Array(a);r=new Array(a);for(o=0;o<a;++o){i[o]=isNaN(r[o]=+t(n[o],o,n))?0:+e(n[o],o,n)}}o.initialize=function(t){n=t;a()};o.strength=function(t){return arguments.length?(e=typeof t==="function"?t:ME(+t),a(),o):e};o.x=function(e){return arguments.length?(t=typeof e==="function"?e:ME(+e),a(),o):t};return o}function BE(t){var e=ME(.1),n,i,r;if(typeof t!=="function")t=ME(t==null?0:+t);function o(t){for(var e=0,o=n.length,a;e<o;++e){a=n[e],a.vy+=(r[e]-a.y)*i[e]*t}}function a(){if(!n)return;var o,a=n.length;i=new Array(a);r=new Array(a);for(o=0;o<a;++o){i[o]=isNaN(r[o]=+t(n[o],o,n))?0:+e(n[o],o,n)}}o.initialize=function(t){n=t;a()};o.strength=function(t){return arguments.length?(e=typeof t==="function"?t:ME(+t),a(),o):e};o.y=function(e){return arguments.length?(t=typeof e==="function"?e:ME(+e),a(),o):t};return o}const ZE={center:eE,collide:DE,nbody:LE,link:WE,x:XE,y:BE};const HE="forces",JE=["alpha","alphaMin","alphaTarget","velocityDecay","forces"],GE=["static","iterations"],KE=["x","y","vx","vy"];function VE(t){Oi.call(this,null,t)}VE.Definition={type:"Force",metadata:{modifies:true},params:[{name:"static",type:"boolean",default:false},{name:"restart",type:"boolean",default:false},{name:"iterations",type:"number",default:300},{name:"alpha",type:"number",default:1},{name:"alphaMin",type:"number",default:.001},{name:"alphaTarget",type:"number",default:0},{name:"velocityDecay",type:"number",default:.4},{name:"forces",type:"param",array:true,params:[{key:{force:"center"},params:[{name:"x",type:"number",default:0},{name:"y",type:"number",default:0}]},{key:{force:"collide"},params:[{name:"radius",type:"number",expr:true},{name:"strength",type:"number",default:.7},{name:"iterations",type:"number",default:1}]},{key:{force:"nbody"},params:[{name:"strength",type:"number",default:-30,expr:true},{name:"theta",type:"number",default:.9},{name:"distanceMin",type:"number",default:1},{name:"distanceMax",type:"number"}]},{key:{force:"link"},params:[{name:"links",type:"data"},{name:"id",type:"field"},{name:"distance",type:"number",default:30,expr:true},{name:"strength",type:"number",expr:true},{name:"iterations",type:"number",default:1}]},{key:{force:"x"},params:[{name:"strength",type:"number",default:.1},{name:"x",type:"field"}]},{key:{force:"y"},params:[{name:"strength",type:"number",default:.1},{name:"y",type:"field"}]}]},{name:"as",type:"string",array:true,modify:false,default:KE}]};(0,p.XW)(VE,Oi,{transform(t,e){var n=this.value,i=e.changed(e.ADD_REM),r=t.modified(JE),o=t.iterations||300;if(!n){this.value=n=QE(e.source,t);n.on("tick",YE(e.dataflow,this));if(!t.static){i=true;n.tick()}e.modifies("index")}else{if(i){e.modifies("index");n.nodes(e.source)}if(r||e.changed(e.MOD)){tS(n,t,0,e)}}if(r||i||t.modified(GE)||e.changed()&&t.restart){n.alpha(Math.max(n.alpha(),t.alpha||1)).alphaDecay(1-Math.pow(n.alphaMin(),1/o));if(t.static){for(n.stop();--o>=0;)n.tick()}else{if(n.stopped())n.restart();if(!i)return e.StopPropagation}}return this.finish(t,e)},finish(t,e){const n=e.dataflow;for(let s=this._argops,l=0,u=s.length,f;l<u;++l){f=s[l];if(f.name!==HE||f.op._argval.force!=="link"){continue}for(var i=f.op._argops,r=0,o=i.length,a;r<o;++r){if(i[r].name==="links"&&(a=i[r].op.source)){n.pulse(a,n.changeset().reflow());break}}}return e.reflow(t.modified()).modifies(KE)}});function YE(t,e){return()=>t.touch(e).run()}function QE(t,e){const n=qE(t),i=n.stop,r=n.restart;let o=false;n.stopped=()=>o;n.restart=()=>(o=false,r());n.stop=()=>(o=true,i());return tS(n,e,true).on("end",(()=>o=true))}function tS(t,e,n,i){var r=(0,p.IX)(e.forces),o,a,s,l;for(o=0,a=JE.length;o<a;++o){s=JE[o];if(s!==HE&&e.modified(s))t[s](e[s])}for(o=0,a=r.length;o<a;++o){l=HE+o;s=n||e.modified(HE,o)?nS(r[o]):i&&eS(r[o],i)?t.force(l):null;if(s)t.force(l,s)}for(a=t.numForces||0;o<a;++o){t.force(HE+o,null)}t.numForces=r.length;return t}function eS(t,e){var n,i;for(n in t){if((0,p.mf)(i=t[n])&&e.modified((0,p.Oj)(i)))return 1}return 0}function nS(t){var e,n;if(!(0,p.nr)(ZE,t.force)){(0,p.vU)("Unrecognized force: "+t.force)}e=ZE[t.force]();for(n in t){if((0,p.mf)(e[n]))iS(e[n],t[n],t)}return e}function iS(t,e,n){t((0,p.mf)(e)?t=>e(t,n):e)}function rS(t){var e=0,n=t.children,i=n&&n.length;if(!i)e=1;else while(--i>=0)e+=n[i].value;t.value=e}function oS(){return this.eachAfter(rS)}function aS(t,e){let n=-1;for(const i of this){t.call(e,i,++n,this)}return this}function sS(t,e){var n=this,i=[n],r,o,a=-1;while(n=i.pop()){t.call(e,n,++a,this);if(r=n.children){for(o=r.length-1;o>=0;--o){i.push(r[o])}}}return this}function lS(t,e){var n=this,i=[n],r=[],o,a,s,l=-1;while(n=i.pop()){r.push(n);if(o=n.children){for(a=0,s=o.length;a<s;++a){i.push(o[a])}}}while(n=r.pop()){t.call(e,n,++l,this)}return this}function uS(t,e){let n=-1;for(const i of this){if(t.call(e,i,++n,this)){return i}}}function fS(t){return this.eachAfter((function(e){var n=+t(e.data)||0,i=e.children,r=i&&i.length;while(--r>=0)n+=i[r].value;e.value=n}))}function cS(t){return this.eachBefore((function(e){if(e.children){e.children.sort(t)}}))}function dS(t){var e=this,n=hS(e,t),i=[e];while(e!==n){e=e.parent;i.push(e)}var r=i.length;while(t!==n){i.splice(r,0,t);t=t.parent}return i}function hS(t,e){if(t===e)return t;var n=t.ancestors(),i=e.ancestors(),r=null;t=n.pop();e=i.pop();while(t===e){r=t;t=n.pop();e=i.pop()}return r}function pS(){var t=this,e=[t];while(t=t.parent){e.push(t)}return e}function mS(){return Array.from(this)}function gS(){var t=[];this.eachBefore((function(e){if(!e.children){t.push(e)}}));return t}function yS(){var t=this,e=[];t.each((function(n){if(n!==t){e.push({source:n.parent,target:n})}}));return e}function*vS(){var t=this,e,n=[t],i,r,o;do{e=n.reverse(),n=[];while(t=e.pop()){yield t;if(i=t.children){for(r=0,o=i.length;r<o;++r){n.push(i[r])}}}}while(n.length)}function bS(t,e){if(t instanceof Map){t=[undefined,t];if(e===undefined)e=wS}else if(e===undefined){e=_S}var n=new ES(t),i,r=[n],o,a,s,l;while(i=r.pop()){if((a=e(i.data))&&(l=(a=Array.from(a)).length)){i.children=a;for(s=l-1;s>=0;--s){r.push(o=a[s]=new ES(a[s]));o.parent=i;o.depth=i.depth+1}}}return n.eachBefore(MS)}function xS(){return bS(this).eachBefore(kS)}function _S(t){return t.children}function wS(t){return Array.isArray(t)?t[1]:null}function kS(t){if(t.data.value!==undefined)t.value=t.data.value;t.data=t.data.data}function MS(t){var e=0;do{t.height=e}while((t=t.parent)&&t.height<++e)}function ES(t){this.data=t;this.depth=this.height=0;this.parent=null}ES.prototype=bS.prototype={constructor:ES,count:oS,each:aS,eachAfter:lS,eachBefore:sS,find:uS,sum:fS,sort:cS,path:dS,ancestors:pS,descendants:mS,leaves:gS,links:yS,copy:xS,[Symbol.iterator]:vS};function SS(t){return t==null?null:zS(t)}function zS(t){if(typeof t!=="function")throw new Error;return t}function DS(){return 0}function RS(t){return function(){return t}}const AS=1664525;const $S=1013904223;const OS=4294967296;function TS(){let t=1;return()=>(t=(AS*t+$S)%OS)/OS}function CS(t){return typeof t==="object"&&"length"in t?t:Array.from(t)}function NS(t,e){let n=t.length,i,r;while(n){r=e()*n--|0;i=t[n];t[n]=t[r];t[r]=i}return t}function US(t){return IS(t,lcg())}function IS(t,e){var n=0,i=(t=NS(Array.from(t),e)).length,r=[],o,a;while(n<i){o=t[n];if(a&&LS(a,o))++n;else a=FS(r=PS(r,o)),n=0}return a}function PS(t,e){var n,i;if(jS(e,t))return[e];for(n=0;n<t.length;++n){if(qS(e,t[n])&&jS(XS(t[n],e),t)){return[t[n],e]}}for(n=0;n<t.length-1;++n){for(i=n+1;i<t.length;++i){if(qS(XS(t[n],t[i]),e)&&qS(XS(t[n],e),t[i])&&qS(XS(t[i],e),t[n])&&jS(BS(t[n],t[i],e),t)){return[t[n],t[i],e]}}}throw new Error}function qS(t,e){var n=t.r-e.r,i=e.x-t.x,r=e.y-t.y;return n<0||n*n<i*i+r*r}function LS(t,e){var n=t.r-e.r+Math.max(t.r,e.r,1)*1e-9,i=e.x-t.x,r=e.y-t.y;return n>0&&n*n>i*i+r*r}function jS(t,e){for(var n=0;n<e.length;++n){if(!LS(t,e[n])){return false}}return true}function FS(t){switch(t.length){case 1:return WS(t[0]);case 2:return XS(t[0],t[1]);case 3:return BS(t[0],t[1],t[2])}}function WS(t){return{x:t.x,y:t.y,r:t.r}}function XS(t,e){var n=t.x,i=t.y,r=t.r,o=e.x,a=e.y,s=e.r,l=o-n,u=a-i,f=s-r,c=Math.sqrt(l*l+u*u);return{x:(n+o+l/c*f)/2,y:(i+a+u/c*f)/2,r:(c+r+s)/2}}function BS(t,e,n){var i=t.x,r=t.y,o=t.r,a=e.x,s=e.y,l=e.r,u=n.x,f=n.y,c=n.r,d=i-a,h=i-u,p=r-s,m=r-f,g=l-o,y=c-o,v=i*i+r*r-o*o,b=v-a*a-s*s+l*l,x=v-u*u-f*f+c*c,_=h*p-d*m,w=(p*x-m*b)/(_*2)-i,k=(m*g-p*y)/_,M=(h*b-d*x)/(_*2)-r,E=(d*y-h*g)/_,S=k*k+E*E-1,z=2*(o+w*k+M*E),D=w*w+M*M-o*o,R=-(Math.abs(S)>1e-6?(z+Math.sqrt(z*z-4*S*D))/(2*S):D/z);return{x:i+w+k*R,y:r+M+E*R,r:R}}function ZS(t,e,n){var i=t.x-e.x,r,o,a=t.y-e.y,s,l,u=i*i+a*a;if(u){o=e.r+n.r,o*=o;l=t.r+n.r,l*=l;if(o>l){r=(u+l-o)/(2*u);s=Math.sqrt(Math.max(0,l/u-r*r));n.x=t.x-r*i-s*a;n.y=t.y-r*a+s*i}else{r=(u+o-l)/(2*u);s=Math.sqrt(Math.max(0,o/u-r*r));n.x=e.x+r*i-s*a;n.y=e.y+r*a+s*i}}else{n.x=e.x+n.r;n.y=e.y}}function HS(t,e){var n=t.r+e.r-1e-6,i=e.x-t.x,r=e.y-t.y;return n>0&&n*n>i*i+r*r}function JS(t){var e=t._,n=t.next._,i=e.r+n.r,r=(e.x*n.r+n.x*e.r)/i,o=(e.y*n.r+n.y*e.r)/i;return r*r+o*o}function GS(t){this._=t;this.next=null;this.previous=null}function KS(t,e){if(!(o=(t=CS(t)).length))return 0;var n,i,r,o,a,s,l,u,f,c,d;n=t[0],n.x=0,n.y=0;if(!(o>1))return n.r;i=t[1],n.x=-i.r,i.x=n.r,i.y=0;if(!(o>2))return n.r+i.r;ZS(i,n,r=t[2]);n=new GS(n),i=new GS(i),r=new GS(r);n.next=r.previous=i;i.next=n.previous=r;r.next=i.previous=n;t:for(l=3;l<o;++l){ZS(n._,i._,r=t[l]),r=new GS(r);u=i.next,f=n.previous,c=i._.r,d=n._.r;do{if(c<=d){if(HS(u._,r._)){i=u,n.next=i,i.previous=n,--l;continue t}c+=u._.r,u=u.next}else{if(HS(f._,r._)){n=f,n.next=i,i.previous=n,--l;continue t}d+=f._.r,f=f.previous}}while(u!==f.next);r.previous=n,r.next=i,n.next=i.previous=i=r;a=JS(n);while((r=r.next)!==i){if((s=JS(r))<a){n=r,a=s}}i=n.next}n=[i._],r=i;while((r=r.next)!==i)n.push(r._);r=IS(n,e);for(l=0;l<o;++l)n=t[l],n.x-=r.x,n.y-=r.y;return r.r}function VS(t){KS(t,lcg());return t}function YS(t){return Math.sqrt(t.value)}function QS(){var t=null,e=1,n=1,i=DS;function r(r){const o=TS();r.x=e/2,r.y=n/2;if(t){r.eachBefore(tz(t)).eachAfter(ez(i,.5,o)).eachBefore(nz(1))}else{r.eachBefore(tz(YS)).eachAfter(ez(DS,1,o)).eachAfter(ez(i,r.r/Math.min(e,n),o)).eachBefore(nz(Math.min(e,n)/(2*r.r)))}return r}r.radius=function(e){return arguments.length?(t=SS(e),r):t};r.size=function(t){return arguments.length?(e=+t[0],n=+t[1],r):[e,n]};r.padding=function(t){return arguments.length?(i=typeof t==="function"?t:RS(+t),r):i};return r}function tz(t){return function(e){if(!e.children){e.r=Math.max(0,+t(e)||0)}}}function ez(t,e,n){return function(i){if(r=i.children){var r,o,a=r.length,s=t(i)*e||0,l;if(s)for(o=0;o<a;++o)r[o].r+=s;l=KS(r,n);if(s)for(o=0;o<a;++o)r[o].r-=s;i.r=l+s}}}function nz(t){return function(e){var n=e.parent;e.r*=t;if(n){e.x=n.x+t*e.x;e.y=n.y+t*e.y}}}function iz(t){t.x0=Math.round(t.x0);t.y0=Math.round(t.y0);t.x1=Math.round(t.x1);t.y1=Math.round(t.y1)}function rz(t,e,n,i,r){var o=t.children,a,s=-1,l=o.length,u=t.value&&(i-e)/t.value;while(++s<l){a=o[s],a.y0=n,a.y1=r;a.x0=e,a.x1=e+=a.value*u}}function oz(){var t=1,e=1,n=0,i=false;function r(r){var a=r.height+1;r.x0=r.y0=n;r.x1=t;r.y1=e/a;r.eachBefore(o(e,a));if(i)r.eachBefore(iz);return r}function o(t,e){return function(i){if(i.children){rz(i,i.x0,t*(i.depth+1)/e,i.x1,t*(i.depth+2)/e)}var r=i.x0,o=i.y0,a=i.x1-n,s=i.y1-n;if(a<r)r=a=(r+a)/2;if(s<o)o=s=(o+s)/2;i.x0=r;i.y0=o;i.x1=a;i.y1=s}}r.round=function(t){return arguments.length?(i=!!t,r):i};r.size=function(n){return arguments.length?(t=+n[0],e=+n[1],r):[t,e]};r.padding=function(t){return arguments.length?(n=+t,r):n};return r}var az={depth:-1},sz={},lz={};function uz(t){return t.id}function fz(t){return t.parentId}function cz(){var t=uz,e=fz,n;function i(i){var r=Array.from(i),o=t,a=e,s,l,u,f,c,d,h,p,m=new Map;if(n!=null){const t=r.map(((t,e)=>dz(n(t,e,i))));const e=t.map(hz);const s=new Set(t).add("");for(const n of e){if(!s.has(n)){s.add(n);t.push(n);e.push(hz(n));r.push(lz)}}o=(e,n)=>t[n];a=(t,n)=>e[n]}for(u=0,s=r.length;u<s;++u){l=r[u],d=r[u]=new ES(l);if((h=o(l,u,i))!=null&&(h+="")){p=d.id=h;m.set(p,m.has(p)?sz:d)}if((h=a(l,u,i))!=null&&(h+="")){d.parent=h}}for(u=0;u<s;++u){d=r[u];if(h=d.parent){c=m.get(h);if(!c)throw new Error("missing: "+h);if(c===sz)throw new Error("ambiguous: "+h);if(c.children)c.children.push(d);else c.children=[d];d.parent=c}else{if(f)throw new Error("multiple roots");f=d}}if(!f)throw new Error("no root");if(n!=null){while(f.data===lz&&f.children.length===1){f=f.children[0],--s}for(let t=r.length-1;t>=0;--t){d=r[t];if(d.data!==lz)break;d.data=null}}f.parent=az;f.eachBefore((function(t){t.depth=t.parent.depth+1;--s})).eachBefore(MS);f.parent=null;if(s>0)throw new Error("cycle");return f}i.id=function(e){return arguments.length?(t=SS(e),i):t};i.parentId=function(t){return arguments.length?(e=SS(t),i):e};i.path=function(t){return arguments.length?(n=SS(t),i):n};return i}function dz(t){t=`${t}`;let e=t.length;if(pz(t,e-1)&&!pz(t,e-2))t=t.slice(0,-1);return t[0]==="/"?t:`/${t}`}function hz(t){let e=t.length;if(e<2)return"";while(--e>1)if(pz(t,e))break;return t.slice(0,e)}function pz(t,e){if(t[e]==="/"){let n=0;while(e>0&&t[--e]==="\\")++n;if((n&1)===0)return true}return false}function mz(t,e){return t.parent===e.parent?1:2}function gz(t){var e=t.children;return e?e[0]:t.t}function yz(t){var e=t.children;return e?e[e.length-1]:t.t}function vz(t,e,n){var i=n/(e.i-t.i);e.c-=i;e.s+=n;t.c+=i;e.z+=n;e.m+=n}function bz(t){var e=0,n=0,i=t.children,r=i.length,o;while(--r>=0){o=i[r];o.z+=e;o.m+=e;e+=o.s+(n+=o.c)}}function xz(t,e,n){return t.a.parent===e.parent?t.a:n}function _z(t,e){this._=t;this.parent=null;this.children=null;this.A=null;this.a=this;this.z=0;this.m=0;this.c=0;this.s=0;this.t=null;this.i=e}_z.prototype=Object.create(ES.prototype);function wz(t){var e=new _z(t,0),n,i=[e],r,o,a,s;while(n=i.pop()){if(o=n._.children){n.children=new Array(s=o.length);for(a=s-1;a>=0;--a){i.push(r=n.children[a]=new _z(o[a],a));r.parent=n}}}(e.parent=new _z(null,0)).children=[e];return e}function kz(){var t=mz,e=1,n=1,i=null;function r(r){var s=wz(r);s.eachAfter(o),s.parent.m=-s.z;s.eachBefore(a);if(i)r.eachBefore(l);else{var u=r,f=r,c=r;r.eachBefore((function(t){if(t.x<u.x)u=t;if(t.x>f.x)f=t;if(t.depth>c.depth)c=t}));var d=u===f?1:t(u,f)/2,h=d-u.x,p=e/(f.x+d+h),m=n/(c.depth||1);r.eachBefore((function(t){t.x=(t.x+h)*p;t.y=t.depth*m}))}return r}function o(e){var n=e.children,i=e.parent.children,r=e.i?i[e.i-1]:null;if(n){bz(e);var o=(n[0].z+n[n.length-1].z)/2;if(r){e.z=r.z+t(e._,r._);e.m=e.z-o}else{e.z=o}}else if(r){e.z=r.z+t(e._,r._)}e.parent.A=s(e,r,e.parent.A||i[0])}function a(t){t._.x=t.z+t.parent.m;t.m+=t.parent.m}function s(e,n,i){if(n){var r=e,o=e,a=n,s=r.parent.children[0],l=r.m,u=o.m,f=a.m,c=s.m,d;while(a=yz(a),r=gz(r),a&&r){s=gz(s);o=yz(o);o.a=e;d=a.z+f-r.z-l+t(a._,r._);if(d>0){vz(xz(a,e,i),e,d);l+=d;u+=d}f+=a.m;l+=r.m;c+=s.m;u+=o.m}if(a&&!yz(o)){o.t=a;o.m+=f-u}if(r&&!gz(s)){s.t=r;s.m+=l-c;i=e}}return i}function l(t){t.x*=e;t.y=t.depth*n}r.separation=function(e){return arguments.length?(t=e,r):t};r.size=function(t){return arguments.length?(i=false,e=+t[0],n=+t[1],r):i?null:[e,n]};r.nodeSize=function(t){return arguments.length?(i=true,e=+t[0],n=+t[1],r):i?[e,n]:null};return r}function Mz(t,e){return t.parent===e.parent?1:2}function Ez(t){return t.reduce(Sz,0)/t.length}function Sz(t,e){return t+e.x}function zz(t){return 1+t.reduce(Dz,0)}function Dz(t,e){return Math.max(t,e.y)}function Rz(t){var e;while(e=t.children)t=e[0];return t}function Az(t){var e;while(e=t.children)t=e[e.length-1];return t}function $z(){var t=Mz,e=1,n=1,i=false;function r(r){var o,a=0;r.eachAfter((function(e){var n=e.children;if(n){e.x=Ez(n);e.y=zz(n)}else{e.x=o?a+=t(e,o):0;e.y=0;o=e}}));var s=Rz(r),l=Az(r),u=s.x-t(s,l)/2,f=l.x+t(l,s)/2;return r.eachAfter(i?function(t){t.x=(t.x-r.x)*e;t.y=(r.y-t.y)*n}:function(t){t.x=(t.x-u)/(f-u)*e;t.y=(1-(r.y?t.y/r.y:1))*n})}r.separation=function(e){return arguments.length?(t=e,r):t};r.size=function(t){return arguments.length?(i=false,e=+t[0],n=+t[1],r):i?null:[e,n]};r.nodeSize=function(t){return arguments.length?(i=true,e=+t[0],n=+t[1],r):i?[e,n]:null};return r}function Oz(t,e,n,i,r){var o=t.children,a,s=o.length,l,u=new Array(s+1);for(u[0]=l=a=0;a<s;++a){u[a+1]=l+=o[a].value}f(0,s,t.value,e,n,i,r);function f(t,e,n,i,r,a,s){if(t>=e-1){var l=o[t];l.x0=i,l.y0=r;l.x1=a,l.y1=s;return}var c=u[t],d=n/2+c,h=t+1,p=e-1;while(h<p){var m=h+p>>>1;if(u[m]<d)h=m+1;else p=m}if(d-u[h-1]<u[h]-d&&t+1<h)--h;var g=u[h]-c,y=n-g;if(a-i>s-r){var v=n?(i*y+a*g)/n:a;f(t,h,g,i,r,v,s);f(h,e,y,v,r,a,s)}else{var b=n?(r*y+s*g)/n:s;f(t,h,g,i,r,a,b);f(h,e,y,i,b,a,s)}}}function Tz(t,e,n,i,r){var o=t.children,a,s=-1,l=o.length,u=t.value&&(r-n)/t.value;while(++s<l){a=o[s],a.x0=e,a.x1=i;a.y0=n,a.y1=n+=a.value*u}}function Cz(t,e,n,i,r){(t.depth&1?Tz:rz)(t,e,n,i,r)}var Nz=(1+Math.sqrt(5))/2;function Uz(t,e,n,i,r,o){var a=[],s=e.children,l,u,f=0,c=0,d=s.length,h,p,m=e.value,g,y,v,b,x,_,w;while(f<d){h=r-n,p=o-i;do{g=s[c++].value}while(!g&&c<d);y=v=g;_=Math.max(p/h,h/p)/(m*t);w=g*g*_;x=Math.max(v/w,w/y);for(;c<d;++c){g+=u=s[c].value;if(u<y)y=u;if(u>v)v=u;w=g*g*_;b=Math.max(v/w,w/y);if(b>x){g-=u;break}x=b}a.push(l={value:g,dice:h<p,children:s.slice(f,c)});if(l.dice)rz(l,n,i,r,m?i+=p*g/m:o);else Tz(l,n,i,m?n+=h*g/m:r,o);m-=g,f=c}return a}const Iz=function t(e){function n(t,n,i,r,o){Uz(e,t,n,i,r,o)}n.ratio=function(e){return t((e=+e)>1?e:1)};return n}(Nz);const Pz=function t(e){function n(t,n,i,r,o){if((a=t._squarify)&&a.ratio===e){var a,s,l,u,f=-1,c,d=a.length,h=t.value;while(++f<d){s=a[f],l=s.children;for(u=s.value=0,c=l.length;u<c;++u)s.value+=l[u].value;if(s.dice)rz(s,n,i,r,h?i+=(o-i)*s.value/h:o);else Tz(s,n,i,h?n+=(r-n)*s.value/h:r,o);h-=s.value}}else{t._squarify=a=Uz(e,t,n,i,r,o);a.ratio=e}}n.ratio=function(e){return t((e=+e)>1?e:1)};return n}(Nz);function qz(){var t=Iz,e=false,n=1,i=1,r=[0],o=DS,a=DS,s=DS,l=DS,u=DS;function f(t){t.x0=t.y0=0;t.x1=n;t.y1=i;t.eachBefore(c);r=[0];if(e)t.eachBefore(iz);return t}function c(e){var n=r[e.depth],i=e.x0+n,f=e.y0+n,c=e.x1-n,d=e.y1-n;if(c<i)i=c=(i+c)/2;if(d<f)f=d=(f+d)/2;e.x0=i;e.y0=f;e.x1=c;e.y1=d;if(e.children){n=r[e.depth+1]=o(e)/2;i+=u(e)-n;f+=a(e)-n;c-=s(e)-n;d-=l(e)-n;if(c<i)i=c=(i+c)/2;if(d<f)f=d=(f+d)/2;t(e,i,f,c,d)}}f.round=function(t){return arguments.length?(e=!!t,f):e};f.size=function(t){return arguments.length?(n=+t[0],i=+t[1],f):[n,i]};f.tile=function(e){return arguments.length?(t=zS(e),f):t};f.padding=function(t){return arguments.length?f.paddingInner(t).paddingOuter(t):f.paddingInner()};f.paddingInner=function(t){return arguments.length?(o=typeof t==="function"?t:RS(+t),f):o};f.paddingOuter=function(t){return arguments.length?f.paddingTop(t).paddingRight(t).paddingBottom(t).paddingLeft(t):f.paddingTop()};f.paddingTop=function(t){return arguments.length?(a=typeof t==="function"?t:RS(+t),f):a};f.paddingRight=function(t){return arguments.length?(s=typeof t==="function"?t:RS(+t),f):s};f.paddingBottom=function(t){return arguments.length?(l=typeof t==="function"?t:RS(+t),f):l};f.paddingLeft=function(t){return arguments.length?(u=typeof t==="function"?t:RS(+t),f):u};return f}function Lz(t,e,n){const i={};t.each((t=>{const r=t.data;if(n(r))i[e(r)]=t}));t.lookup=i;return t}function jz(t){Oi.call(this,null,t)}jz.Definition={type:"Nest",metadata:{treesource:true,changes:true},params:[{name:"keys",type:"field",array:true},{name:"generate",type:"boolean"}]};const Fz=t=>t.values;(0,p.XW)(jz,Oi,{transform(t,e){if(!e.source){(0,p.vU)("Nest transform requires an upstream data source.")}var n=t.generate,i=t.modified(),r=e.clone(),o=this.value;if(!o||i||e.changed()){if(o){o.each((t=>{if(t.children&&gn(t.data)){r.rem.push(t.data)}}))}this.value=o=bS({values:(0,p.IX)(t.keys).reduce(((t,e)=>{t.key(e);return t}),Wz()).entries(r.source)},Fz);if(n){o.each((t=>{if(t.children){t=bn(t.data);r.add.push(t);r.source.push(t)}}))}Lz(o,yn,yn)}r.source.root=o;return r}});function Wz(){const t=[],e={entries:t=>i(n(t,0),0),key:n=>(t.push(n),e)};function n(e,i){if(i>=t.length){return e}const r=e.length,o=t[i++],a={},s={};let l=-1,u,f,c;while(++l<r){u=o(f=e[l])+"";if(c=a[u]){c.push(f)}else{a[u]=[f]}}for(u in a){s[u]=n(a[u],i)}return s}function i(e,n){if(++n>t.length)return e;const r=[];for(const t in e){r.push({key:t,values:i(e[t],n)})}return r}return e}function Xz(t){Oi.call(this,null,t)}const Bz=(t,e)=>t.parent===e.parent?1:2;(0,p.XW)(Xz,Oi,{transform(t,e){if(!e.source||!e.source.root){(0,p.vU)(this.constructor.name+" transform requires a backing tree data source.")}const n=this.layout(t.method),i=this.fields,r=e.source.root,o=t.as||i;if(t.field)r.sum(t.field);else r.count();if(t.sort)r.sort(kn(t.sort,(t=>t.data)));Zz(n,this.params,t);if(n.separation){n.separation(t.separation!==false?Bz:p.kX)}try{this.value=n(r)}catch(a){(0,p.vU)(a)}r.each((t=>Hz(t,i,o)));return e.reflow(t.modified()).modifies(o).modifies("leaf")}});function Zz(t,e,n){for(let i,r=0,o=e.length;r<o;++r){i=e[r];if(i in n)t[i](n[i])}}function Hz(t,e,n){const i=t.data,r=e.length-1;for(let o=0;o<r;++o){i[n[o]]=t[e[o]]}i[n[r]]=t.children?t.children.length:0}const Jz=["x","y","r","depth","children"];function Gz(t){Xz.call(this,t)}Gz.Definition={type:"Pack",metadata:{tree:true,modifies:true},params:[{name:"field",type:"field"},{name:"sort",type:"compare"},{name:"padding",type:"number",default:0},{name:"radius",type:"field",default:null},{name:"size",type:"number",array:true,length:2},{name:"as",type:"string",array:true,length:Jz.length,default:Jz}]};(0,p.XW)(Gz,Xz,{layout:QS,params:["radius","size","padding"],fields:Jz});const Kz=["x0","y0","x1","y1","depth","children"];function Vz(t){Xz.call(this,t)}Vz.Definition={type:"Partition",metadata:{tree:true,modifies:true},params:[{name:"field",type:"field"},{name:"sort",type:"compare"},{name:"padding",type:"number",default:0},{name:"round",type:"boolean",default:false},{name:"size",type:"number",array:true,length:2},{name:"as",type:"string",array:true,length:Kz.length,default:Kz}]};(0,p.XW)(Vz,Xz,{layout:oz,params:["size","round","padding"],fields:Kz});function Yz(t){Oi.call(this,null,t)}Yz.Definition={type:"Stratify",metadata:{treesource:true},params:[{name:"key",type:"field",required:true},{name:"parentKey",type:"field",required:true}]};(0,p.XW)(Yz,Oi,{transform(t,e){if(!e.source){(0,p.vU)("Stratify transform requires an upstream data source.")}let n=this.value;const i=t.modified(),r=e.fork(e.ALL).materialize(e.SOURCE),o=!n||i||e.changed(e.ADD_REM)||e.modified(t.key.fields)||e.modified(t.parentKey.fields);r.source=r.source.slice();if(o){n=r.source.length?Lz(cz().id(t.key).parentId(t.parentKey)(r.source),t.key,p.yb):Lz(cz()([{}]),t.key,t.key)}r.source.root=this.value=n;return r}});const Qz={tidy:kz,cluster:$z};const tD=["x","y","depth","children"];function eD(t){Xz.call(this,t)}eD.Definition={type:"Tree",metadata:{tree:true,modifies:true},params:[{name:"field",type:"field"},{name:"sort",type:"compare"},{name:"method",type:"enum",default:"tidy",values:["tidy","cluster"]},{name:"size",type:"number",array:true,length:2},{name:"nodeSize",type:"number",array:true,length:2},{name:"separation",type:"boolean",default:true},{name:"as",type:"string",array:true,length:tD.length,default:tD}]};(0,p.XW)(eD,Xz,{layout(t){const e=t||"tidy";if((0,p.nr)(Qz,e))return Qz[e]();else(0,p.vU)("Unrecognized Tree layout method: "+e)},params:["size","nodeSize"],fields:tD});function nD(t){Oi.call(this,[],t)}nD.Definition={type:"TreeLinks",metadata:{tree:true,generates:true,changes:true},params:[]};(0,p.XW)(nD,Oi,{transform(t,e){const n=this.value,i=e.source&&e.source.root,r=e.fork(e.NO_SOURCE),o={};if(!i)(0,p.vU)("TreeLinks transform requires a tree data source.");if(e.changed(e.ADD_REM)){r.rem=n;e.visit(e.SOURCE,(t=>o[yn(t)]=1));i.each((t=>{const e=t.data,n=t.parent&&t.parent.data;if(n&&o[yn(e)]&&o[yn(n)]){r.add.push(bn({source:n,target:e}))}}));this.value=r.add}else if(e.changed(e.MOD)){e.visit(e.MOD,(t=>o[yn(t)]=1));n.forEach((t=>{if(o[yn(t.source)]||o[yn(t.target)]){r.mod.push(t)}}))}return r}});const iD={binary:Oz,dice:rz,slice:Tz,slicedice:Cz,squarify:Iz,resquarify:Pz};const rD=["x0","y0","x1","y1","depth","children"];function oD(t){Xz.call(this,t)}oD.Definition={type:"Treemap",metadata:{tree:true,modifies:true},params:[{name:"field",type:"field"},{name:"sort",type:"compare"},{name:"method",type:"enum",default:"squarify",values:["squarify","resquarify","binary","dice","slice","slicedice"]},{name:"padding",type:"number",default:0},{name:"paddingInner",type:"number",default:0},{name:"paddingOuter",type:"number",default:0},{name:"paddingTop",type:"number",default:0},{name:"paddingRight",type:"number",default:0},{name:"paddingBottom",type:"number",default:0},{name:"paddingLeft",type:"number",default:0},{name:"ratio",type:"number",default:1.618033988749895},{name:"round",type:"boolean",default:false},{name:"size",type:"number",array:true,length:2},{name:"as",type:"string",array:true,length:rD.length,default:rD}]};(0,p.XW)(oD,Xz,{layout(){const t=qz();t.ratio=e=>{const n=t.tile();if(n.ratio)t.tile(n.ratio(e))};t.method=e=>{if((0,p.nr)(iD,e))t.tile(iD[e]);else(0,p.vU)("Unrecognized Treemap layout method: "+e)};return t},params:["method","ratio","size","round","padding","paddingInner","paddingOuter","paddingTop","paddingRight","paddingBottom","paddingLeft"],fields:rD});const aD=4278190080;function sD(t,e){const n=t.bitmap();(e||[]).forEach((e=>n.set(t(e.boundary[0]),t(e.boundary[3]))));return[n,undefined]}function lD(t,e,n,i,r){const o=t.width,a=t.height,s=i||r,l=Ks(o,a).getContext("2d"),u=Ks(o,a).getContext("2d"),f=s&&Ks(o,a).getContext("2d");n.forEach((t=>fD(l,t,false)));fD(u,e,false);if(s){fD(f,e,true)}const c=uD(l,o,a),d=uD(u,o,a),h=s&&uD(f,o,a),p=t.bitmap(),m=s&&t.bitmap();let g,y,v,b,x,_,w,k;for(y=0;y<a;++y){for(g=0;g<o;++g){x=y*o+g;_=c[x]&aD;k=d[x]&aD;w=s&&h[x]&aD;if(_||w||k){v=t(g);b=t(y);if(!r&&(_||k))p.set(v,b);if(s&&(_||w))m.set(v,b)}}}return[p,m]}function uD(t,e,n){return new Uint32Array(t.getImageData(0,0,e,n).data.buffer)}function fD(t,e,n){if(!e.length)return;const i=e[0].mark.marktype;if(i==="group"){e.forEach((e=>{e.items.forEach((e=>fD(t,e.items,n)))}))}else{Pp[i].draw(t,{items:n?e.map(cD):e})}}function cD(t){const e=_n(t,{});if(e.stroke&&e.strokeOpacity!==0||e.fill&&e.fillOpacity!==0){return{...e,strokeOpacity:1,stroke:"#000",fillOpacity:0}}return e}const dD=5,hD=31,pD=32,mD=new Uint32Array(pD+1),gD=new Uint32Array(pD+1);gD[0]=0;mD[0]=~gD[0];for(let RW=1;RW<=pD;++RW){gD[RW]=gD[RW-1]<<1|1;mD[RW]=~gD[RW]}function yD(t,e){const n=new Uint32Array(~~((t*e+pD)/pD));function i(t,e){n[t]|=e}function r(t,e){n[t]&=e}return{array:n,get:(e,i)=>{const r=i*t+e;return n[r>>>dD]&1<<(r&hD)},set:(e,n)=>{const r=n*t+e;i(r>>>dD,1<<(r&hD))},clear:(e,n)=>{const i=n*t+e;r(i>>>dD,~(1<<(i&hD)))},getRange:(e,i,r,o)=>{let a=o,s,l,u,f;for(;a>=i;--a){s=a*t+e;l=a*t+r;u=s>>>dD;f=l>>>dD;if(u===f){if(n[u]&mD[s&hD]&gD[(l&hD)+1]){return true}}else{if(n[u]&mD[s&hD])return true;if(n[f]&gD[(l&hD)+1])return true;for(let t=u+1;t<f;++t){if(n[t])return true}}}return false},setRange:(e,n,r,o)=>{let a,s,l,u,f;for(;n<=o;++n){a=n*t+e;s=n*t+r;l=a>>>dD;u=s>>>dD;if(l===u){i(l,mD[a&hD]&gD[(s&hD)+1])}else{i(l,mD[a&hD]);i(u,gD[(s&hD)+1]);for(f=l+1;f<u;++f)i(f,4294967295)}}},clearRange:(e,n,i,o)=>{let a,s,l,u,f;for(;n<=o;++n){a=n*t+e;s=n*t+i;l=a>>>dD;u=s>>>dD;if(l===u){r(l,gD[a&hD]|mD[(s&hD)+1])}else{r(l,gD[a&hD]);r(u,mD[(s&hD)+1]);for(f=l+1;f<u;++f)r(f,0)}}},outOfBounds:(n,i,r,o)=>n<0||i<0||o>=e||r>=t}}function vD(t,e,n){const i=Math.max(1,Math.sqrt(t*e/1e6)),r=~~((t+2*n+i)/i),o=~~((e+2*n+i)/i),a=t=>~~((t+n)/i);a.invert=t=>t*i-n;a.bitmap=()=>yD(r,o);a.ratio=i;a.padding=n;a.width=t;a.height=e;return a}function bD(t,e,n,i){const r=t.width,o=t.height;return function(t){const e=t.datum.datum.items[i].items,n=e.length,a=t.datum.fontSize,s=cp.width(t.datum,t.datum.text);let l=0,u,f,c,d,h,p,m;for(let i=0;i<n;++i){u=e[i].x;c=e[i].y;f=e[i].x2===undefined?u:e[i].x2;d=e[i].y2===undefined?c:e[i].y2;h=(u+f)/2;p=(c+d)/2;m=Math.abs(f-u+d-c);if(m>=l){l=m;t.x=h;t.y=p}}h=s/2;p=a/2;u=t.x-h;f=t.x+h;c=t.y-p;d=t.y+p;t.align="center";if(u<0&&f<=r){t.align="left"}else if(0<=u&&r<f){t.align="right"}t.baseline="middle";if(c<0&&d<=o){t.baseline="top"}else if(0<=c&&o<d){t.baseline="bottom"}return true}}function xD(t,e,n,i,r,o){let a=n/2;return t-a<0||t+a>r||e-(a=i/2)<0||e+a>o}function _D(t,e,n,i,r,o,a,s){const l=r*o/(i*2),u=t(e-l),f=t(e+l),c=t(n-(o=o/2)),d=t(n+o);return a.outOfBounds(u,c,f,d)||a.getRange(u,c,f,d)||s&&s.getRange(u,c,f,d)}function wD(t,e,n,i){const r=t.width,o=t.height,a=e[0],s=e[1];function l(e,n,i,l,u){const f=t.invert(e),c=t.invert(n);let d=i,h=o,p;if(!xD(f,c,l,u,r,o)&&!_D(t,f,c,u,l,d,a,s)&&!_D(t,f,c,u,l,u,a,null)){while(h-d>=1){p=(d+h)/2;if(_D(t,f,c,u,l,p,a,s)){h=p}else{d=p}}if(d>i){return[f,c,d,true]}}}return function(e){const s=e.datum.datum.items[i].items,u=s.length,f=e.datum.fontSize,c=cp.width(e.datum,e.datum.text);let d=n?f:0,h=false,p=false,m=0,g,y,v,b,x,_,w,k,M,E,S,z,D,R,A,$,O;for(let i=0;i<u;++i){g=s[i].x;v=s[i].y;y=s[i].x2===undefined?g:s[i].x2;b=s[i].y2===undefined?v:s[i].y2;if(g>y){O=g;g=y;y=O}if(v>b){O=v;v=b;b=O}M=t(g);S=t(y);E=~~((M+S)/2);z=t(v);R=t(b);D=~~((z+R)/2);for(w=E;w>=M;--w){for(k=D;k>=z;--k){$=l(w,k,d,c,f);if($){[e.x,e.y,d,h]=$}}}for(w=E;w<=S;++w){for(k=D;k<=R;++k){$=l(w,k,d,c,f);if($){[e.x,e.y,d,h]=$}}}if(!h&&!n){A=Math.abs(y-g+b-v);x=(g+y)/2;_=(v+b)/2;if(A>=m&&!xD(x,_,c,f,r,o)&&!_D(t,x,_,f,c,f,a,null)){m=A;e.x=x;e.y=_;p=true}}}if(h||p){x=c/2;_=f/2;a.setRange(t(e.x-x),t(e.y-_),t(e.x+x),t(e.y+_));e.align="center";e.baseline="middle";return true}else{return false}}}const kD=[-1,-1,1,1];const MD=[-1,1,-1,1];function ED(t,e,n,i){const r=t.width,o=t.height,a=e[0],s=e[1],l=t.bitmap();return function(e){const u=e.datum.datum.items[i].items,f=u.length,c=e.datum.fontSize,d=cp.width(e.datum,e.datum.text),h=[];let p=n?c:0,m=false,g=false,y=0,v,b,x,_,w,k,M,E,S,z,D,R;for(let i=0;i<f;++i){v=u[i].x;x=u[i].y;b=u[i].x2===undefined?v:u[i].x2;_=u[i].y2===undefined?x:u[i].y2;h.push([t((v+b)/2),t((x+_)/2)]);while(h.length){[M,E]=h.pop();if(a.get(M,E)||s.get(M,E)||l.get(M,E))continue;l.set(M,E);for(let t=0;t<4;++t){w=M+kD[t];k=E+MD[t];if(!l.outOfBounds(w,k,w,k))h.push([w,k])}w=t.invert(M);k=t.invert(E);S=p;z=o;if(!xD(w,k,d,c,r,o)&&!_D(t,w,k,c,d,S,a,s)&&!_D(t,w,k,c,d,c,a,null)){while(z-S>=1){D=(S+z)/2;if(_D(t,w,k,c,d,D,a,s)){z=D}else{S=D}}if(S>p){e.x=w;e.y=k;p=S;m=true}}}if(!m&&!n){R=Math.abs(b-v+_-x);w=(v+b)/2;k=(x+_)/2;if(R>=y&&!xD(w,k,d,c,r,o)&&!_D(t,w,k,c,d,c,a,null)){y=R;e.x=w;e.y=k;g=true}}}if(m||g){w=d/2;k=c/2;a.setRange(t(e.x-w),t(e.y-k),t(e.x+w),t(e.y+k));e.align="center";e.baseline="middle";return true}else{return false}}}const SD=["right","center","left"],zD=["bottom","middle","top"];function DD(t,e,n,i){const r=t.width,o=t.height,a=e[0],s=e[1],l=i.length;return function(e){const u=e.boundary,f=e.datum.fontSize;if(u[2]<0||u[5]<0||u[0]>r||u[3]>o){return false}let c=e.textWidth??0,d,h,p,m,g,y,v,b,x,_,w,k,M,E,S;for(let r=0;r<l;++r){d=(n[r]&3)-1;h=(n[r]>>>2&3)-1;p=d===0&&h===0||i[r]<0;m=d&&h?Math.SQRT1_2:1;g=i[r]<0?-1:1;y=u[1+d]+i[r]*d*m;w=u[4+h]+g*f*h/2+i[r]*h*m;b=w-f/2;x=w+f/2;k=t(y);E=t(b);S=t(x);if(!c){if(!RD(k,k,E,S,a,s,y,y,b,x,u,p)){continue}else{c=cp.width(e.datum,e.datum.text)}}_=y+g*c*d/2;y=_-c/2;v=_+c/2;k=t(y);M=t(v);if(RD(k,M,E,S,a,s,y,v,b,x,u,p)){e.x=!d?_:d*g<0?v:y;e.y=!h?w:h*g<0?x:b;e.align=SD[d*g+1];e.baseline=zD[h*g+1];a.setRange(k,E,M,S);return true}}return false}}function RD(t,e,n,i,r,o,a,s,l,u,f,c){return!(r.outOfBounds(t,n,e,i)||(c&&o||r).getRange(t,n,e,i))}const AD=0,$D=4,OD=8,TD=0,CD=1,ND=2;const UD={"top-left":AD+TD,top:AD+CD,"top-right":AD+ND,left:$D+TD,middle:$D+CD,right:$D+ND,"bottom-left":OD+TD,bottom:OD+CD,"bottom-right":OD+ND};const ID={naive:bD,"reduced-search":wD,floodfill:ED};function PD(t,e,n,i,r,o,a,s,l,u,f){if(!t.length)return t;const c=Math.max(i.length,r.length),d=qD(i,c),h=LD(r,c),p=jD(t[0].datum),m=p==="group"&&t[0].datum.items[l].marktype,g=m==="area",y=FD(p,m,s,l),v=u===null||u===Infinity,b=g&&f==="naive";let x=-1,_=-1;const w=t.map((t=>{const e=v?cp.width(t,t.text):undefined;x=Math.max(x,e);_=Math.max(_,t.fontSize);return{datum:t,opacity:0,x:undefined,y:undefined,align:undefined,baseline:undefined,boundary:y(t),textWidth:e}}));u=u===null||u===Infinity?Math.max(x,_)+Math.max(...i):u;const k=vD(e[0],e[1],u);let M;if(!b){if(n){w.sort(((t,e)=>n(t.datum,e.datum)))}let e=false;for(let t=0;t<h.length&&!e;++t){e=h[t]===5||d[t]<0}const i=(p&&a||g)&&t.map((t=>t.datum));M=o.length||i?lD(k,i||[],o,e,g):sD(k,a&&w)}const E=g?ID[f](k,M,a,l):DD(k,M,h,d);w.forEach((t=>t.opacity=+E(t)));return w}function qD(t,e){const n=new Float64Array(e),i=t.length;for(let r=0;r<i;++r)n[r]=t[r]||0;for(let r=i;r<e;++r)n[r]=n[i-1];return n}function LD(t,e){const n=new Int8Array(e),i=t.length;for(let r=0;r<i;++r)n[r]|=UD[t[r]];for(let r=i;r<e;++r)n[r]=n[i-1];return n}function jD(t){return t&&t.mark&&t.mark.marktype}function FD(t,e,n,i){const r=t=>[t.x,t.x,t.x,t.y,t.y,t.y];if(!t){return r}else if(t==="line"||t==="area"){return t=>r(t.datum)}else if(e==="line"){return t=>{const e=t.datum.items[i].items;return r(e.length?e[n==="start"?0:e.length-1]:{x:NaN,y:NaN})}}else{return t=>{const e=t.datum.bounds;return[e.x1,(e.x1+e.x2)/2,e.x2,e.y1,(e.y1+e.y2)/2,e.y2]}}}const WD=["x","y","opacity","align","baseline"];const XD=["top-left","left","bottom-left","top","bottom","top-right","right","bottom-right"];function BD(t){Oi.call(this,null,t)}BD.Definition={type:"Label",metadata:{modifies:true},params:[{name:"size",type:"number",array:true,length:2,required:true},{name:"sort",type:"compare"},{name:"anchor",type:"string",array:true,default:XD},{name:"offset",type:"number",array:true,default:[1]},{name:"padding",type:"number",default:0,null:true},{name:"lineAnchor",type:"string",values:["start","end"],default:"end"},{name:"markIndex",type:"number",default:0},{name:"avoidBaseMark",type:"boolean",default:true},{name:"avoidMarks",type:"data",array:true},{name:"method",type:"string",default:"naive"},{name:"as",type:"string",array:true,length:WD.length,default:WD}]};(0,p.XW)(BD,Oi,{transform(t,e){function n(n){const i=t[n];return(0,p.mf)(i)&&e.modified(i.fields)}const i=t.modified();if(!(i||e.changed(e.ADD_REM)||n("sort")))return;if(!t.size||t.size.length!==2){(0,p.vU)("Size parameter should be specified as a [width, height] array.")}const r=t.as||WD;PD(e.materialize(e.SOURCE).source||[],t.size,t.sort,(0,p.IX)(t.offset==null?1:t.offset),(0,p.IX)(t.anchor||XD),t.avoidMarks||[],t.avoidBaseMark!==false,t.lineAnchor||"end",t.markIndex||0,t.padding===undefined?0:t.padding,t.method||"naive").forEach((t=>{const e=t.datum;e[r[0]]=t.x;e[r[1]]=t.y;e[r[2]]=t.opacity;e[r[3]]=t.align;e[r[4]]=t.baseline}));return e.reflow(i).modifies(r)}});function ZD(t,e){var n=[],i=function(t){return t(s)},r,o,a,s,l,u;if(e==null){n.push(t)}else{for(r={},o=0,a=t.length;o<a;++o){s=t[o];l=e.map(i);u=r[l];if(!u){r[l]=u=[];u.dims=l;n.push(u)}u.push(s)}}return n}function HD(t){Oi.call(this,null,t)}HD.Definition={type:"Loess",metadata:{generates:true},params:[{name:"x",type:"field",required:true},{name:"y",type:"field",required:true},{name:"groupby",type:"field",array:true},{name:"bandwidth",type:"number",default:.3},{name:"as",type:"string",array:true}]};(0,p.XW)(HD,Oi,{transform(t,e){const n=e.fork(e.NO_SOURCE|e.NO_FIELDS);if(!this.value||e.changed()||t.modified()){const i=e.materialize(e.SOURCE).source,r=ZD(i,t.groupby),o=(t.groupby||[]).map(p.el),a=o.length,s=t.as||[(0,p.el)(t.x),(0,p.el)(t.y)],l=[];r.forEach((e=>{Br(e,t.x,t.y,t.bandwidth||.3).forEach((t=>{const n={};for(let i=0;i<a;++i){n[o[i]]=e.dims[i]}n[s[0]]=t[0];n[s[1]]=t[1];l.push(bn(n))}))}));if(this.value)n.rem=this.value;this.value=n.add=n.source=l}return n}});const JD={linear:Nr,log:Ur,exp:Ir,pow:Pr,quad:qr,poly:Lr};const GD=(t,e)=>t==="poly"?e:t==="quad"?2:1;function KD(t){Oi.call(this,null,t)}KD.Definition={type:"Regression",metadata:{generates:true},params:[{name:"x",type:"field",required:true},{name:"y",type:"field",required:true},{name:"groupby",type:"field",array:true},{name:"method",type:"string",default:"linear",values:Object.keys(JD)},{name:"order",type:"number",default:3},{name:"extent",type:"number",array:true,length:2},{name:"params",type:"boolean",default:false},{name:"as",type:"string",array:true}]};(0,p.XW)(KD,Oi,{transform(t,e){const n=e.fork(e.NO_SOURCE|e.NO_FIELDS);if(!this.value||e.changed()||t.modified()){const i=e.materialize(e.SOURCE).source,r=ZD(i,t.groupby),o=(t.groupby||[]).map(p.el),a=t.method||"linear",s=t.order||3,l=GD(a,s),u=t.as||[(0,p.el)(t.x),(0,p.el)(t.y)],f=JD[a],c=[];let d=t.extent;if(!(0,p.nr)(JD,a)){(0,p.vU)("Invalid regression method: "+a)}if(d!=null){if(a==="log"&&d[0]<=0){e.dataflow.warn("Ignoring extent with values <= 0 for log regression.");d=null}}r.forEach((n=>{const i=n.length;if(i<=l){e.dataflow.warn("Skipping regression with more parameters than data points.");return}const r=f(n,t.x,t.y,s);if(t.params){c.push(bn({keys:n.dims,coef:r.coef,rSquared:r.rSquared}));return}const h=d||(0,p.We)(n,t.x),m=t=>{const e={};for(let i=0;i<o.length;++i){e[o[i]]=n.dims[i]}e[u[0]]=t[0];e[u[1]]=t[1];c.push(bn(e))};if(a==="linear"){h.forEach((t=>m([t,r.predict(t)])))}else{Kr(r.predict,h,25,200).forEach(m)}}));if(this.value)n.rem=this.value;this.value=n.add=n.source=c}return n}});const VD=11102230246251565e-32;const YD=134217729;const QD=(3+8*VD)*VD;function tR(t,e,n,i,r){let o,a,s,l;let u=e[0];let f=i[0];let c=0;let d=0;if(f>u===f>-u){o=u;u=e[++c]}else{o=f;f=i[++d]}let h=0;if(c<t&&d<n){if(f>u===f>-u){a=u+o;s=o-(a-u);u=e[++c]}else{a=f+o;s=o-(a-f);f=i[++d]}o=a;if(s!==0){r[h++]=s}while(c<t&&d<n){if(f>u===f>-u){a=o+u;l=a-o;s=o-(a-l)+(u-l);u=e[++c]}else{a=o+f;l=a-o;s=o-(a-l)+(f-l);f=i[++d]}o=a;if(s!==0){r[h++]=s}}}while(c<t){a=o+u;l=a-o;s=o-(a-l)+(u-l);u=e[++c];o=a;if(s!==0){r[h++]=s}}while(d<n){a=o+f;l=a-o;s=o-(a-l)+(f-l);f=i[++d];o=a;if(s!==0){r[h++]=s}}if(o!==0||h===0){r[h++]=o}return h}function eR(t,e,n,i,r,o,a,s){return tR(tR(t,e,n,i,a),a,r,o,s)}function nR(t,e,n,i){let r,o,a,s,l;let u,f,c,d,h,p;f=YD*n;h=f-(f-n);p=n-h;let m=e[0];r=m*n;f=YD*m;c=f-(f-m);d=m-c;a=d*p-(r-c*h-d*h-c*p);let g=0;if(a!==0){i[g++]=a}for(let y=1;y<t;y++){m=e[y];s=m*n;f=YD*m;c=f-(f-m);d=m-c;l=d*p-(s-c*h-d*h-c*p);o=r+l;u=o-r;a=r-(o-u)+(l-u);if(a!==0){i[g++]=a}r=s+o;a=o-(r-s);if(a!==0){i[g++]=a}}if(r!==0||g===0){i[g++]=r}return g}function iR(t,e){for(let n=0;n<t;n++)e[n]=-e[n];return t}function rR(t,e){let n=e[0];for(let i=1;i<t;i++)n+=e[i];return n}function oR(t){return new Float64Array(t)}const aR=(3+16*VD)*VD;const sR=(2+12*VD)*VD;const lR=(9+64*VD)*VD*VD;const uR=oR(4);const fR=oR(8);const cR=oR(12);const dR=oR(16);const hR=oR(4);function pR(t,e,n,i,r,o,a){let s,l,u,f;let c,d,h,p,m,g,y,v,b,x,_,w,k,M;const E=t-r;const S=n-r;const z=e-o;const D=i-o;x=E*D;d=YD*E;h=d-(d-E);p=E-h;d=YD*D;m=d-(d-D);g=D-m;_=p*g-(x-h*m-p*m-h*g);w=z*S;d=YD*z;h=d-(d-z);p=z-h;d=YD*S;m=d-(d-S);g=S-m;k=p*g-(w-h*m-p*m-h*g);y=_-k;c=_-y;uR[0]=_-(y+c)+(c-k);v=x+y;c=v-x;b=x-(v-c)+(y-c);y=b-w;c=b-y;uR[1]=b-(y+c)+(c-w);M=v+y;c=M-v;uR[2]=v-(M-c)+(y-c);uR[3]=M;let R=rR(4,uR);let A=sR*a;if(R>=A||-R>=A){return R}c=t-E;s=t-(E+c)+(c-r);c=n-S;u=n-(S+c)+(c-r);c=e-z;l=e-(z+c)+(c-o);c=i-D;f=i-(D+c)+(c-o);if(s===0&&l===0&&u===0&&f===0){return R}A=lR*a+QD*Math.abs(R);R+=E*f+D*s-(z*u+S*l);if(R>=A||-R>=A)return R;x=s*D;d=YD*s;h=d-(d-s);p=s-h;d=YD*D;m=d-(d-D);g=D-m;_=p*g-(x-h*m-p*m-h*g);w=l*S;d=YD*l;h=d-(d-l);p=l-h;d=YD*S;m=d-(d-S);g=S-m;k=p*g-(w-h*m-p*m-h*g);y=_-k;c=_-y;hR[0]=_-(y+c)+(c-k);v=x+y;c=v-x;b=x-(v-c)+(y-c);y=b-w;c=b-y;hR[1]=b-(y+c)+(c-w);M=v+y;c=M-v;hR[2]=v-(M-c)+(y-c);hR[3]=M;const $=tR(4,uR,4,hR,fR);x=E*f;d=YD*E;h=d-(d-E);p=E-h;d=YD*f;m=d-(d-f);g=f-m;_=p*g-(x-h*m-p*m-h*g);w=z*u;d=YD*z;h=d-(d-z);p=z-h;d=YD*u;m=d-(d-u);g=u-m;k=p*g-(w-h*m-p*m-h*g);y=_-k;c=_-y;hR[0]=_-(y+c)+(c-k);v=x+y;c=v-x;b=x-(v-c)+(y-c);y=b-w;c=b-y;hR[1]=b-(y+c)+(c-w);M=v+y;c=M-v;hR[2]=v-(M-c)+(y-c);hR[3]=M;const O=tR($,fR,4,hR,cR);x=s*f;d=YD*s;h=d-(d-s);p=s-h;d=YD*f;m=d-(d-f);g=f-m;_=p*g-(x-h*m-p*m-h*g);w=l*u;d=YD*l;h=d-(d-l);p=l-h;d=YD*u;m=d-(d-u);g=u-m;k=p*g-(w-h*m-p*m-h*g);y=_-k;c=_-y;hR[0]=_-(y+c)+(c-k);v=x+y;c=v-x;b=x-(v-c)+(y-c);y=b-w;c=b-y;hR[1]=b-(y+c)+(c-w);M=v+y;c=M-v;hR[2]=v-(M-c)+(y-c);hR[3]=M;const T=tR(O,cR,4,hR,dR);return dR[T-1]}function mR(t,e,n,i,r,o){const a=(e-o)*(n-r);const s=(t-r)*(i-o);const l=a-s;if(a===0||s===0||a>0!==s>0)return l;const u=Math.abs(a+s);if(Math.abs(l)>=aR*u)return l;return-pR(t,e,n,i,r,o,u)}function gR(t,e,n,i,r,o){return(e-o)*(n-r)-(t-r)*(i-o)}const yR=(7+56*VD)*VD;const vR=(3+28*VD)*VD;const bR=(26+288*VD)*VD*VD;const xR=oR(4);const _R=oR(4);const wR=oR(4);const kR=oR(4);const MR=oR(4);const ER=oR(4);const SR=oR(4);const zR=oR(4);const DR=oR(4);const RR=oR(8);const AR=oR(8);const $R=oR(8);const OR=oR(4);const TR=oR(8);const CR=oR(8);const NR=oR(8);const UR=oR(12);let IR=oR(192);let PR=oR(192);function qR(t,e,n){t=sum(t,IR,e,n,PR);const i=IR;IR=PR;PR=i;return t}function LR(t,e,n,i,r,o,a,s){let l,u,f,c,d,h,p,m,g,y,v,b,x,_,w,k;if(t===0){if(e===0){a[0]=0;s[0]=0;return 1}else{k=-e;v=k*n;u=splitter*k;f=u-(u-k);c=k-f;u=splitter*n;d=u-(u-n);h=n-d;a[0]=c*h-(v-f*d-c*d-f*h);a[1]=v;v=e*r;u=splitter*e;f=u-(u-e);c=e-f;u=splitter*r;d=u-(u-r);h=r-d;s[0]=c*h-(v-f*d-c*d-f*h);s[1]=v;return 2}}else{if(e===0){v=t*i;u=splitter*t;f=u-(u-t);c=t-f;u=splitter*i;d=u-(u-i);h=i-d;a[0]=c*h-(v-f*d-c*d-f*h);a[1]=v;k=-t;v=k*o;u=splitter*k;f=u-(u-k);c=k-f;u=splitter*o;d=u-(u-o);h=o-d;s[0]=c*h-(v-f*d-c*d-f*h);s[1]=v;return 2}else{v=t*i;u=splitter*t;f=u-(u-t);c=t-f;u=splitter*i;d=u-(u-i);h=i-d;b=c*h-(v-f*d-c*d-f*h);x=e*n;u=splitter*e;f=u-(u-e);c=e-f;u=splitter*n;d=u-(u-n);h=n-d;_=c*h-(x-f*d-c*d-f*h);p=b-_;l=b-p;a[0]=b-(p+l)+(l-_);m=v+p;l=m-v;y=v-(m-l)+(p-l);p=y-x;l=y-p;a[1]=y-(p+l)+(l-x);w=m+p;l=w-m;a[2]=m-(w-l)+(p-l);a[3]=w;v=e*r;u=splitter*e;f=u-(u-e);c=e-f;u=splitter*r;d=u-(u-r);h=r-d;b=c*h-(v-f*d-c*d-f*h);x=t*o;u=splitter*t;f=u-(u-t);c=t-f;u=splitter*o;d=u-(u-o);h=o-d;_=c*h-(x-f*d-c*d-f*h);p=b-_;l=b-p;s[0]=b-(p+l)+(l-_);m=v+p;l=m-v;y=v-(m-l)+(p-l);p=y-x;l=y-p;s[1]=y-(p+l)+(l-x);w=m+p;l=w-m;s[2]=m-(w-l)+(p-l);s[3]=w;return 4}}}function jR(t,e,n,i,r){let o,a,s,l,u,f,c,d,h,p,m,g,y;m=e*n;a=splitter*e;s=a-(a-e);l=e-s;a=splitter*n;u=a-(a-n);f=n-u;g=l*f-(m-s*u-l*u-s*f);a=splitter*i;u=a-(a-i);f=i-u;c=g*i;a=splitter*g;s=a-(a-g);l=g-s;OR[0]=l*f-(c-s*u-l*u-s*f);d=m*i;a=splitter*m;s=a-(a-m);l=m-s;p=l*f-(d-s*u-l*u-s*f);h=c+p;o=h-c;OR[1]=c-(h-o)+(p-o);y=d+h;OR[2]=h-(y-d);OR[3]=y;t=qR(t,4,OR);if(r!==0){a=splitter*r;u=a-(a-r);f=r-u;c=g*r;a=splitter*g;s=a-(a-g);l=g-s;OR[0]=l*f-(c-s*u-l*u-s*f);d=m*r;a=splitter*m;s=a-(a-m);l=m-s;p=l*f-(d-s*u-l*u-s*f);h=c+p;o=h-c;OR[1]=c-(h-o)+(p-o);y=d+h;OR[2]=h-(y-d);OR[3]=y;t=qR(t,4,OR)}return t}function FR(t,e,n,i,r,o,a,s,l,u,f,c,d){let h;let p,m,g;let y,v,b;let x,_,w;let k,M,E,S,z,D,R,A,$,O,T,C,N,U,I;const P=t-u;const q=i-u;const L=a-u;const j=e-f;const F=r-f;const W=s-f;const X=n-c;const B=o-c;const Z=l-c;T=q*W;M=splitter*q;E=M-(M-q);S=q-E;M=splitter*W;z=M-(M-W);D=W-z;C=S*D-(T-E*z-S*z-E*D);N=L*F;M=splitter*L;E=M-(M-L);S=L-E;M=splitter*F;z=M-(M-F);D=F-z;U=S*D-(N-E*z-S*z-E*D);R=C-U;k=C-R;xR[0]=C-(R+k)+(k-U);A=T+R;k=A-T;O=T-(A-k)+(R-k);R=O-N;k=O-R;xR[1]=O-(R+k)+(k-N);I=A+R;k=I-A;xR[2]=A-(I-k)+(R-k);xR[3]=I;T=L*j;M=splitter*L;E=M-(M-L);S=L-E;M=splitter*j;z=M-(M-j);D=j-z;C=S*D-(T-E*z-S*z-E*D);N=P*W;M=splitter*P;E=M-(M-P);S=P-E;M=splitter*W;z=M-(M-W);D=W-z;U=S*D-(N-E*z-S*z-E*D);R=C-U;k=C-R;_R[0]=C-(R+k)+(k-U);A=T+R;k=A-T;O=T-(A-k)+(R-k);R=O-N;k=O-R;_R[1]=O-(R+k)+(k-N);I=A+R;k=I-A;_R[2]=A-(I-k)+(R-k);_R[3]=I;T=P*F;M=splitter*P;E=M-(M-P);S=P-E;M=splitter*F;z=M-(M-F);D=F-z;C=S*D-(T-E*z-S*z-E*D);N=q*j;M=splitter*q;E=M-(M-q);S=q-E;M=splitter*j;z=M-(M-j);D=j-z;U=S*D-(N-E*z-S*z-E*D);R=C-U;k=C-R;wR[0]=C-(R+k)+(k-U);A=T+R;k=A-T;O=T-(A-k)+(R-k);R=O-N;k=O-R;wR[1]=O-(R+k)+(k-N);I=A+R;k=I-A;wR[2]=A-(I-k)+(R-k);wR[3]=I;h=sum(sum(scale(4,xR,X,TR),TR,scale(4,_R,B,CR),CR,NR),NR,scale(4,wR,Z,TR),TR,IR);let H=estimate(h,IR);let J=vR*d;if(H>=J||-H>=J){return H}k=t-P;p=t-(P+k)+(k-u);k=i-q;m=i-(q+k)+(k-u);k=a-L;g=a-(L+k)+(k-u);k=e-j;y=e-(j+k)+(k-f);k=r-F;v=r-(F+k)+(k-f);k=s-W;b=s-(W+k)+(k-f);k=n-X;x=n-(X+k)+(k-c);k=o-B;_=o-(B+k)+(k-c);k=l-Z;w=l-(Z+k)+(k-c);if(p===0&&m===0&&g===0&&y===0&&v===0&&b===0&&x===0&&_===0&&w===0){return H}J=bR*d+resulterrbound*Math.abs(H);H+=X*(q*b+W*m-(F*g+L*v))+x*(q*W-F*L)+B*(L*y+j*g-(W*p+P*b))+_*(L*j-W*P)+Z*(P*v+F*p-(j*m+q*y))+w*(P*F-j*q);if(H>=J||-H>=J){return H}const G=LR(p,y,q,F,L,W,kR,MR);const K=LR(m,v,L,W,P,j,ER,SR);const V=LR(g,b,P,j,q,F,zR,DR);const Y=sum(K,ER,V,DR,RR);h=qR(h,scale(Y,RR,X,NR),NR);const Q=sum(V,zR,G,MR,AR);h=qR(h,scale(Q,AR,B,NR),NR);const tt=sum(G,kR,K,SR,$R);h=qR(h,scale(tt,$R,Z,NR),NR);if(x!==0){h=qR(h,scale(4,xR,x,UR),UR);h=qR(h,scale(Y,RR,x,NR),NR)}if(_!==0){h=qR(h,scale(4,_R,_,UR),UR);h=qR(h,scale(Q,AR,_,NR),NR)}if(w!==0){h=qR(h,scale(4,wR,w,UR),UR);h=qR(h,scale(tt,$R,w,NR),NR)}if(p!==0){if(v!==0){h=jR(h,p,v,Z,w)}if(b!==0){h=jR(h,-p,b,B,_)}}if(m!==0){if(b!==0){h=jR(h,m,b,X,x)}if(y!==0){h=jR(h,-m,y,Z,w)}}if(g!==0){if(y!==0){h=jR(h,g,y,B,_)}if(v!==0){h=jR(h,-g,v,X,x)}}return IR[h-1]}function WR(t,e,n,i,r,o,a,s,l,u,f,c){const d=t-u;const h=i-u;const p=a-u;const m=e-f;const g=r-f;const y=s-f;const v=n-c;const b=o-c;const x=l-c;const _=h*y;const w=p*g;const k=p*m;const M=d*y;const E=d*g;const S=h*m;const z=v*(_-w)+b*(k-M)+x*(E-S);const D=(Math.abs(_)+Math.abs(w))*Math.abs(v)+(Math.abs(k)+Math.abs(M))*Math.abs(b)+(Math.abs(E)+Math.abs(S))*Math.abs(x);const R=yR*D;if(z>R||-z>R){return z}return FR(t,e,n,i,r,o,a,s,l,u,f,c,D)}function XR(t,e,n,i,r,o,a,s,l,u,f,c){const d=t-u;const h=i-u;const p=a-u;const m=e-f;const g=r-f;const y=s-f;const v=n-c;const b=o-c;const x=l-c;return d*(g*x-b*y)+h*(y*v-x*m)+p*(m*b-v*g)}const BR=(10+96*VD)*VD;const ZR=(4+48*VD)*VD;const HR=(44+576*VD)*VD*VD;const JR=oR(4);const GR=oR(4);const KR=oR(4);const VR=oR(4);const YR=oR(4);const QR=oR(4);const tA=oR(4);const eA=oR(4);const nA=oR(8);const iA=oR(8);const rA=oR(8);const oA=oR(8);const aA=oR(8);const sA=oR(8);const lA=oR(8);const uA=oR(8);const fA=oR(8);const cA=oR(4);const dA=oR(4);const hA=oR(4);const pA=oR(8);const mA=oR(16);const gA=oR(16);const yA=oR(16);const vA=oR(32);const bA=oR(32);const xA=oR(48);const _A=oR(64);let wA=oR(1152);let kA=oR(1152);function MA(t,e,n){t=sum(t,wA,e,n,kA);const i=wA;wA=kA;kA=i;return t}function EA(t,e,n,i,r,o,a,s,l){let u;let f,c,d,h,p,m;let g,y,v,b,x,_;let w,k,M;let E,S,z;let D,R;let A,$,O,T,C,N,U,I,P,q,L,j,F,W;const X=t-a;const B=n-a;const Z=r-a;const H=e-s;const J=i-s;const G=o-s;q=B*G;$=splitter*B;O=$-($-B);T=B-O;$=splitter*G;C=$-($-G);N=G-C;L=T*N-(q-O*C-T*C-O*N);j=Z*J;$=splitter*Z;O=$-($-Z);T=Z-O;$=splitter*J;C=$-($-J);N=J-C;F=T*N-(j-O*C-T*C-O*N);U=L-F;A=L-U;JR[0]=L-(U+A)+(A-F);I=q+U;A=I-q;P=q-(I-A)+(U-A);U=P-j;A=P-U;JR[1]=P-(U+A)+(A-j);W=I+U;A=W-I;JR[2]=I-(W-A)+(U-A);JR[3]=W;q=Z*H;$=splitter*Z;O=$-($-Z);T=Z-O;$=splitter*H;C=$-($-H);N=H-C;L=T*N-(q-O*C-T*C-O*N);j=X*G;$=splitter*X;O=$-($-X);T=X-O;$=splitter*G;C=$-($-G);N=G-C;F=T*N-(j-O*C-T*C-O*N);U=L-F;A=L-U;GR[0]=L-(U+A)+(A-F);I=q+U;A=I-q;P=q-(I-A)+(U-A);U=P-j;A=P-U;GR[1]=P-(U+A)+(A-j);W=I+U;A=W-I;GR[2]=I-(W-A)+(U-A);GR[3]=W;q=X*J;$=splitter*X;O=$-($-X);T=X-O;$=splitter*J;C=$-($-J);N=J-C;L=T*N-(q-O*C-T*C-O*N);j=B*H;$=splitter*B;O=$-($-B);T=B-O;$=splitter*H;C=$-($-H);N=H-C;F=T*N-(j-O*C-T*C-O*N);U=L-F;A=L-U;KR[0]=L-(U+A)+(A-F);I=q+U;A=I-q;P=q-(I-A)+(U-A);U=P-j;A=P-U;KR[1]=P-(U+A)+(A-j);W=I+U;A=W-I;KR[2]=I-(W-A)+(U-A);KR[3]=W;u=sum(sum(sum(scale(scale(4,JR,X,pA),pA,X,mA),mA,scale(scale(4,JR,H,pA),pA,H,gA),gA,vA),vA,sum(scale(scale(4,GR,B,pA),pA,B,mA),mA,scale(scale(4,GR,J,pA),pA,J,gA),gA,bA),bA,_A),_A,sum(scale(scale(4,KR,Z,pA),pA,Z,mA),mA,scale(scale(4,KR,G,pA),pA,G,gA),gA,vA),vA,wA);let K=estimate(u,wA);let V=ZR*l;if(K>=V||-K>=V){return K}A=t-X;f=t-(X+A)+(A-a);A=e-H;h=e-(H+A)+(A-s);A=n-B;c=n-(B+A)+(A-a);A=i-J;p=i-(J+A)+(A-s);A=r-Z;d=r-(Z+A)+(A-a);A=o-G;m=o-(G+A)+(A-s);if(f===0&&c===0&&d===0&&h===0&&p===0&&m===0){return K}V=HR*l+resulterrbound*Math.abs(K);K+=(X*X+H*H)*(B*m+G*c-(J*d+Z*p))+2*(X*f+H*h)*(B*G-J*Z)+((B*B+J*J)*(Z*h+H*d-(G*f+X*m))+2*(B*c+J*p)*(Z*H-G*X))+((Z*Z+G*G)*(X*p+J*f-(H*c+B*h))+2*(Z*d+G*m)*(X*J-H*B));if(K>=V||-K>=V){return K}if(c!==0||p!==0||d!==0||m!==0){q=X*X;$=splitter*X;O=$-($-X);T=X-O;L=T*T-(q-O*O-(O+O)*T);j=H*H;$=splitter*H;O=$-($-H);T=H-O;F=T*T-(j-O*O-(O+O)*T);U=L+F;A=U-L;VR[0]=L-(U-A)+(F-A);I=q+U;A=I-q;P=q-(I-A)+(U-A);U=P+j;A=U-P;VR[1]=P-(U-A)+(j-A);W=I+U;A=W-I;VR[2]=I-(W-A)+(U-A);VR[3]=W}if(d!==0||m!==0||f!==0||h!==0){q=B*B;$=splitter*B;O=$-($-B);T=B-O;L=T*T-(q-O*O-(O+O)*T);j=J*J;$=splitter*J;O=$-($-J);T=J-O;F=T*T-(j-O*O-(O+O)*T);U=L+F;A=U-L;YR[0]=L-(U-A)+(F-A);I=q+U;A=I-q;P=q-(I-A)+(U-A);U=P+j;A=U-P;YR[1]=P-(U-A)+(j-A);W=I+U;A=W-I;YR[2]=I-(W-A)+(U-A);YR[3]=W}if(f!==0||h!==0||c!==0||p!==0){q=Z*Z;$=splitter*Z;O=$-($-Z);T=Z-O;L=T*T-(q-O*O-(O+O)*T);j=G*G;$=splitter*G;O=$-($-G);T=G-O;F=T*T-(j-O*O-(O+O)*T);U=L+F;A=U-L;QR[0]=L-(U-A)+(F-A);I=q+U;A=I-q;P=q-(I-A)+(U-A);U=P+j;A=U-P;QR[1]=P-(U-A)+(j-A);W=I+U;A=W-I;QR[2]=I-(W-A)+(U-A);QR[3]=W}if(f!==0){g=scale(4,JR,f,nA);u=MA(u,sum_three(scale(g,nA,2*X,mA),mA,scale(scale(4,QR,f,pA),pA,J,gA),gA,scale(scale(4,YR,f,pA),pA,-G,yA),yA,vA,xA),xA)}if(h!==0){y=scale(4,JR,h,iA);u=MA(u,sum_three(scale(y,iA,2*H,mA),mA,scale(scale(4,YR,h,pA),pA,Z,gA),gA,scale(scale(4,QR,h,pA),pA,-B,yA),yA,vA,xA),xA)}if(c!==0){v=scale(4,GR,c,rA);u=MA(u,sum_three(scale(v,rA,2*B,mA),mA,scale(scale(4,VR,c,pA),pA,G,gA),gA,scale(scale(4,QR,c,pA),pA,-H,yA),yA,vA,xA),xA)}if(p!==0){b=scale(4,GR,p,oA);u=MA(u,sum_three(scale(b,oA,2*J,mA),mA,scale(scale(4,QR,p,pA),pA,X,gA),gA,scale(scale(4,VR,p,pA),pA,-Z,yA),yA,vA,xA),xA)}if(d!==0){x=scale(4,KR,d,aA);u=MA(u,sum_three(scale(x,aA,2*Z,mA),mA,scale(scale(4,YR,d,pA),pA,H,gA),gA,scale(scale(4,VR,d,pA),pA,-J,yA),yA,vA,xA),xA)}if(m!==0){_=scale(4,KR,m,sA);u=MA(u,sum_three(scale(_,sA,2*G,mA),mA,scale(scale(4,VR,m,pA),pA,B,gA),gA,scale(scale(4,YR,m,pA),pA,-X,yA),yA,vA,xA),xA)}if(f!==0||h!==0){if(c!==0||p!==0||d!==0||m!==0){q=c*G;$=splitter*c;O=$-($-c);T=c-O;$=splitter*G;C=$-($-G);N=G-C;L=T*N-(q-O*C-T*C-O*N);j=B*m;$=splitter*B;O=$-($-B);T=B-O;$=splitter*m;C=$-($-m);N=m-C;F=T*N-(j-O*C-T*C-O*N);U=L+F;A=U-L;tA[0]=L-(U-A)+(F-A);I=q+U;A=I-q;P=q-(I-A)+(U-A);U=P+j;A=U-P;tA[1]=P-(U-A)+(j-A);W=I+U;A=W-I;tA[2]=I-(W-A)+(U-A);tA[3]=W;q=d*-J;$=splitter*d;O=$-($-d);T=d-O;$=splitter*-J;C=$-($- -J);N=-J-C;L=T*N-(q-O*C-T*C-O*N);j=Z*-p;$=splitter*Z;O=$-($-Z);T=Z-O;$=splitter*-p;C=$-($- -p);N=-p-C;F=T*N-(j-O*C-T*C-O*N);U=L+F;A=U-L;eA[0]=L-(U-A)+(F-A);I=q+U;A=I-q;P=q-(I-A)+(U-A);U=P+j;A=U-P;eA[1]=P-(U-A)+(j-A);W=I+U;A=W-I;eA[2]=I-(W-A)+(U-A);eA[3]=W;k=sum(4,tA,4,eA,uA);q=c*m;$=splitter*c;O=$-($-c);T=c-O;$=splitter*m;C=$-($-m);N=m-C;L=T*N-(q-O*C-T*C-O*N);j=d*p;$=splitter*d;O=$-($-d);T=d-O;$=splitter*p;C=$-($-p);N=p-C;F=T*N-(j-O*C-T*C-O*N);U=L-F;A=L-U;dA[0]=L-(U+A)+(A-F);I=q+U;A=I-q;P=q-(I-A)+(U-A);U=P-j;A=P-U;dA[1]=P-(U+A)+(A-j);W=I+U;A=W-I;dA[2]=I-(W-A)+(U-A);dA[3]=W;S=4}else{uA[0]=0;k=1;dA[0]=0;S=1}if(f!==0){const t=scale(k,uA,f,yA);u=MA(u,sum(scale(g,nA,f,mA),mA,scale(t,yA,2*X,vA),vA,xA),xA);const e=scale(S,dA,f,pA);u=MA(u,sum_three(scale(e,pA,2*X,mA),mA,scale(e,pA,f,gA),gA,scale(t,yA,f,vA),vA,bA,_A),_A);if(p!==0){u=MA(u,scale(scale(4,QR,f,pA),pA,p,mA),mA)}if(m!==0){u=MA(u,scale(scale(4,YR,-f,pA),pA,m,mA),mA)}}if(h!==0){const t=scale(k,uA,h,yA);u=MA(u,sum(scale(y,iA,h,mA),mA,scale(t,yA,2*H,vA),vA,xA),xA);const e=scale(S,dA,h,pA);u=MA(u,sum_three(scale(e,pA,2*H,mA),mA,scale(e,pA,h,gA),gA,scale(t,yA,h,vA),vA,bA,_A),_A)}}if(c!==0||p!==0){if(d!==0||m!==0||f!==0||h!==0){q=d*H;$=splitter*d;O=$-($-d);T=d-O;$=splitter*H;C=$-($-H);N=H-C;L=T*N-(q-O*C-T*C-O*N);j=Z*h;$=splitter*Z;O=$-($-Z);T=Z-O;$=splitter*h;C=$-($-h);N=h-C;F=T*N-(j-O*C-T*C-O*N);U=L+F;A=U-L;tA[0]=L-(U-A)+(F-A);I=q+U;A=I-q;P=q-(I-A)+(U-A);U=P+j;A=U-P;tA[1]=P-(U-A)+(j-A);W=I+U;A=W-I;tA[2]=I-(W-A)+(U-A);tA[3]=W;D=-G;R=-m;q=f*D;$=splitter*f;O=$-($-f);T=f-O;$=splitter*D;C=$-($-D);N=D-C;L=T*N-(q-O*C-T*C-O*N);j=X*R;$=splitter*X;O=$-($-X);T=X-O;$=splitter*R;C=$-($-R);N=R-C;F=T*N-(j-O*C-T*C-O*N);U=L+F;A=U-L;eA[0]=L-(U-A)+(F-A);I=q+U;A=I-q;P=q-(I-A)+(U-A);U=P+j;A=U-P;eA[1]=P-(U-A)+(j-A);W=I+U;A=W-I;eA[2]=I-(W-A)+(U-A);eA[3]=W;M=sum(4,tA,4,eA,fA);q=d*h;$=splitter*d;O=$-($-d);T=d-O;$=splitter*h;C=$-($-h);N=h-C;L=T*N-(q-O*C-T*C-O*N);j=f*m;$=splitter*f;O=$-($-f);T=f-O;$=splitter*m;C=$-($-m);N=m-C;F=T*N-(j-O*C-T*C-O*N);U=L-F;A=L-U;hA[0]=L-(U+A)+(A-F);I=q+U;A=I-q;P=q-(I-A)+(U-A);U=P-j;A=P-U;hA[1]=P-(U+A)+(A-j);W=I+U;A=W-I;hA[2]=I-(W-A)+(U-A);hA[3]=W;z=4}else{fA[0]=0;M=1;hA[0]=0;z=1}if(c!==0){const t=scale(M,fA,c,yA);u=MA(u,sum(scale(v,rA,c,mA),mA,scale(t,yA,2*B,vA),vA,xA),xA);const e=scale(z,hA,c,pA);u=MA(u,sum_three(scale(e,pA,2*B,mA),mA,scale(e,pA,c,gA),gA,scale(t,yA,c,vA),vA,bA,_A),_A);if(m!==0){u=MA(u,scale(scale(4,VR,c,pA),pA,m,mA),mA)}if(h!==0){u=MA(u,scale(scale(4,QR,-c,pA),pA,h,mA),mA)}}if(p!==0){const t=scale(M,fA,p,yA);u=MA(u,sum(scale(b,oA,p,mA),mA,scale(t,yA,2*J,vA),vA,xA),xA);const e=scale(z,hA,p,pA);u=MA(u,sum_three(scale(e,pA,2*J,mA),mA,scale(e,pA,p,gA),gA,scale(t,yA,p,vA),vA,bA,_A),_A)}}if(d!==0||m!==0){if(f!==0||h!==0||c!==0||p!==0){q=f*J;$=splitter*f;O=$-($-f);T=f-O;$=splitter*J;C=$-($-J);N=J-C;L=T*N-(q-O*C-T*C-O*N);j=X*p;$=splitter*X;O=$-($-X);T=X-O;$=splitter*p;C=$-($-p);N=p-C;F=T*N-(j-O*C-T*C-O*N);U=L+F;A=U-L;tA[0]=L-(U-A)+(F-A);I=q+U;A=I-q;P=q-(I-A)+(U-A);U=P+j;A=U-P;tA[1]=P-(U-A)+(j-A);W=I+U;A=W-I;tA[2]=I-(W-A)+(U-A);tA[3]=W;D=-H;R=-h;q=c*D;$=splitter*c;O=$-($-c);T=c-O;$=splitter*D;C=$-($-D);N=D-C;L=T*N-(q-O*C-T*C-O*N);j=B*R;$=splitter*B;O=$-($-B);T=B-O;$=splitter*R;C=$-($-R);N=R-C;F=T*N-(j-O*C-T*C-O*N);U=L+F;A=U-L;eA[0]=L-(U-A)+(F-A);I=q+U;A=I-q;P=q-(I-A)+(U-A);U=P+j;A=U-P;eA[1]=P-(U-A)+(j-A);W=I+U;A=W-I;eA[2]=I-(W-A)+(U-A);eA[3]=W;w=sum(4,tA,4,eA,lA);q=f*p;$=splitter*f;O=$-($-f);T=f-O;$=splitter*p;C=$-($-p);N=p-C;L=T*N-(q-O*C-T*C-O*N);j=c*h;$=splitter*c;O=$-($-c);T=c-O;$=splitter*h;C=$-($-h);N=h-C;F=T*N-(j-O*C-T*C-O*N);U=L-F;A=L-U;cA[0]=L-(U+A)+(A-F);I=q+U;A=I-q;P=q-(I-A)+(U-A);U=P-j;A=P-U;cA[1]=P-(U+A)+(A-j);W=I+U;A=W-I;cA[2]=I-(W-A)+(U-A);cA[3]=W;E=4}else{lA[0]=0;w=1;cA[0]=0;E=1}if(d!==0){const t=scale(w,lA,d,yA);u=MA(u,sum(scale(x,aA,d,mA),mA,scale(t,yA,2*Z,vA),vA,xA),xA);const e=scale(E,cA,d,pA);u=MA(u,sum_three(scale(e,pA,2*Z,mA),mA,scale(e,pA,d,gA),gA,scale(t,yA,d,vA),vA,bA,_A),_A);if(h!==0){u=MA(u,scale(scale(4,YR,d,pA),pA,h,mA),mA)}if(p!==0){u=MA(u,scale(scale(4,VR,-d,pA),pA,p,mA),mA)}}if(m!==0){const t=scale(w,lA,m,yA);u=MA(u,sum(scale(_,sA,m,mA),mA,scale(t,yA,2*G,vA),vA,xA),xA);const e=scale(E,cA,m,pA);u=MA(u,sum_three(scale(e,pA,2*G,mA),mA,scale(e,pA,m,gA),gA,scale(t,yA,m,vA),vA,bA,_A),_A)}}return wA[u-1]}function SA(t,e,n,i,r,o,a,s){const l=t-a;const u=n-a;const f=r-a;const c=e-s;const d=i-s;const h=o-s;const p=u*h;const m=f*d;const g=l*l+c*c;const y=f*c;const v=l*h;const b=u*u+d*d;const x=l*d;const _=u*c;const w=f*f+h*h;const k=g*(p-m)+b*(y-v)+w*(x-_);const M=(Math.abs(p)+Math.abs(m))*g+(Math.abs(y)+Math.abs(v))*b+(Math.abs(x)+Math.abs(_))*w;const E=BR*M;if(k>E||-k>E){return k}return EA(t,e,n,i,r,o,a,s,M)}function zA(t,e,n,i,r,o,a,s){const l=t-a;const u=e-s;const f=n-a;const c=i-s;const d=r-a;const h=o-s;const p=l*c-f*u;const m=f*h-d*c;const g=d*u-l*h;const y=l*l+u*u;const v=f*f+c*c;const b=d*d+h*h;return y*m+v*g+b*p}const DA=(16+224*VD)*VD;const RA=(5+72*VD)*VD;const AA=(71+1408*VD)*VD*VD;const $A=oR(4);const OA=oR(4);const TA=oR(4);const CA=oR(4);const NA=oR(4);const UA=oR(4);const IA=oR(4);const PA=oR(4);const qA=oR(4);const LA=oR(4);const jA=oR(24);const FA=oR(24);const WA=oR(24);const XA=oR(24);const BA=oR(24);const ZA=oR(24);const HA=oR(24);const JA=oR(24);const GA=oR(24);const KA=oR(24);const VA=oR(1152);const YA=oR(1152);const QA=oR(1152);const t$=oR(1152);const e$=oR(1152);const n$=oR(2304);const i$=oR(2304);const r$=oR(3456);const o$=oR(5760);const a$=oR(8);const s$=oR(8);const l$=oR(8);const u$=oR(16);const f$=oR(24);const c$=oR(48);const d$=oR(48);const h$=oR(96);const p$=oR(192);const m$=oR(384);const g$=oR(384);const y$=oR(384);const v$=oR(768);function b$(t,e,n,i,r,o,a){return sum_three(scale(4,t,i,a$),a$,scale(4,e,r,s$),s$,scale(4,n,o,l$),l$,u$,a)}function x$(t,e,n,i,r,o,a,s,l,u,f,c){const d=sum(sum(t,e,n,i,c$),c$,negate(sum(r,o,a,s,d$),d$),d$,h$);return sum_three(scale(scale(d,h$,l,p$),p$,l,m$),m$,scale(scale(d,h$,u,p$),p$,u,g$),g$,scale(scale(d,h$,f,p$),p$,f,y$),y$,v$,c)}function _$(t,e,n,i,r,o,a,s,l,u,f,c,d,h,p){let m,g,y,v,b,x,_,w,k,M,E,S,z,D;M=t*r;g=splitter*t;y=g-(g-t);v=t-y;g=splitter*r;b=g-(g-r);x=r-b;E=v*x-(M-y*b-v*b-y*x);S=i*e;g=splitter*i;y=g-(g-i);v=i-y;g=splitter*e;b=g-(g-e);x=e-b;z=v*x-(S-y*b-v*b-y*x);_=E-z;m=E-_;$A[0]=E-(_+m)+(m-z);w=M+_;m=w-M;k=M-(w-m)+(_-m);_=k-S;m=k-_;$A[1]=k-(_+m)+(m-S);D=w+_;m=D-w;$A[2]=w-(D-m)+(_-m);$A[3]=D;M=i*s;g=splitter*i;y=g-(g-i);v=i-y;g=splitter*s;b=g-(g-s);x=s-b;E=v*x-(M-y*b-v*b-y*x);S=a*r;g=splitter*a;y=g-(g-a);v=a-y;g=splitter*r;b=g-(g-r);x=r-b;z=v*x-(S-y*b-v*b-y*x);_=E-z;m=E-_;OA[0]=E-(_+m)+(m-z);w=M+_;m=w-M;k=M-(w-m)+(_-m);_=k-S;m=k-_;OA[1]=k-(_+m)+(m-S);D=w+_;m=D-w;OA[2]=w-(D-m)+(_-m);OA[3]=D;M=a*f;g=splitter*a;y=g-(g-a);v=a-y;g=splitter*f;b=g-(g-f);x=f-b;E=v*x-(M-y*b-v*b-y*x);S=u*s;g=splitter*u;y=g-(g-u);v=u-y;g=splitter*s;b=g-(g-s);x=s-b;z=v*x-(S-y*b-v*b-y*x);_=E-z;m=E-_;TA[0]=E-(_+m)+(m-z);w=M+_;m=w-M;k=M-(w-m)+(_-m);_=k-S;m=k-_;TA[1]=k-(_+m)+(m-S);D=w+_;m=D-w;TA[2]=w-(D-m)+(_-m);TA[3]=D;M=u*h;g=splitter*u;y=g-(g-u);v=u-y;g=splitter*h;b=g-(g-h);x=h-b;E=v*x-(M-y*b-v*b-y*x);S=d*f;g=splitter*d;y=g-(g-d);v=d-y;g=splitter*f;b=g-(g-f);x=f-b;z=v*x-(S-y*b-v*b-y*x);_=E-z;m=E-_;CA[0]=E-(_+m)+(m-z);w=M+_;m=w-M;k=M-(w-m)+(_-m);_=k-S;m=k-_;CA[1]=k-(_+m)+(m-S);D=w+_;m=D-w;CA[2]=w-(D-m)+(_-m);CA[3]=D;M=d*e;g=splitter*d;y=g-(g-d);v=d-y;g=splitter*e;b=g-(g-e);x=e-b;E=v*x-(M-y*b-v*b-y*x);S=t*h;g=splitter*t;y=g-(g-t);v=t-y;g=splitter*h;b=g-(g-h);x=h-b;z=v*x-(S-y*b-v*b-y*x);_=E-z;m=E-_;NA[0]=E-(_+m)+(m-z);w=M+_;m=w-M;k=M-(w-m)+(_-m);_=k-S;m=k-_;NA[1]=k-(_+m)+(m-S);D=w+_;m=D-w;NA[2]=w-(D-m)+(_-m);NA[3]=D;M=t*s;g=splitter*t;y=g-(g-t);v=t-y;g=splitter*s;b=g-(g-s);x=s-b;E=v*x-(M-y*b-v*b-y*x);S=a*e;g=splitter*a;y=g-(g-a);v=a-y;g=splitter*e;b=g-(g-e);x=e-b;z=v*x-(S-y*b-v*b-y*x);_=E-z;m=E-_;UA[0]=E-(_+m)+(m-z);w=M+_;m=w-M;k=M-(w-m)+(_-m);_=k-S;m=k-_;UA[1]=k-(_+m)+(m-S);D=w+_;m=D-w;UA[2]=w-(D-m)+(_-m);UA[3]=D;M=i*f;g=splitter*i;y=g-(g-i);v=i-y;g=splitter*f;b=g-(g-f);x=f-b;E=v*x-(M-y*b-v*b-y*x);S=u*r;g=splitter*u;y=g-(g-u);v=u-y;g=splitter*r;b=g-(g-r);x=r-b;z=v*x-(S-y*b-v*b-y*x);_=E-z;m=E-_;IA[0]=E-(_+m)+(m-z);w=M+_;m=w-M;k=M-(w-m)+(_-m);_=k-S;m=k-_;IA[1]=k-(_+m)+(m-S);D=w+_;m=D-w;IA[2]=w-(D-m)+(_-m);IA[3]=D;M=a*h;g=splitter*a;y=g-(g-a);v=a-y;g=splitter*h;b=g-(g-h);x=h-b;E=v*x-(M-y*b-v*b-y*x);S=d*s;g=splitter*d;y=g-(g-d);v=d-y;g=splitter*s;b=g-(g-s);x=s-b;z=v*x-(S-y*b-v*b-y*x);_=E-z;m=E-_;PA[0]=E-(_+m)+(m-z);w=M+_;m=w-M;k=M-(w-m)+(_-m);_=k-S;m=k-_;PA[1]=k-(_+m)+(m-S);D=w+_;m=D-w;PA[2]=w-(D-m)+(_-m);PA[3]=D;M=u*e;g=splitter*u;y=g-(g-u);v=u-y;g=splitter*e;b=g-(g-e);x=e-b;E=v*x-(M-y*b-v*b-y*x);S=t*f;g=splitter*t;y=g-(g-t);v=t-y;g=splitter*f;b=g-(g-f);x=f-b;z=v*x-(S-y*b-v*b-y*x);_=E-z;m=E-_;qA[0]=E-(_+m)+(m-z);w=M+_;m=w-M;k=M-(w-m)+(_-m);_=k-S;m=k-_;qA[1]=k-(_+m)+(m-S);D=w+_;m=D-w;qA[2]=w-(D-m)+(_-m);qA[3]=D;M=d*r;g=splitter*d;y=g-(g-d);v=d-y;g=splitter*r;b=g-(g-r);x=r-b;E=v*x-(M-y*b-v*b-y*x);S=i*h;g=splitter*i;y=g-(g-i);v=i-y;g=splitter*h;b=g-(g-h);x=h-b;z=v*x-(S-y*b-v*b-y*x);_=E-z;m=E-_;LA[0]=E-(_+m)+(m-z);w=M+_;m=w-M;k=M-(w-m)+(_-m);_=k-S;m=k-_;LA[1]=k-(_+m)+(m-S);D=w+_;m=D-w;LA[2]=w-(D-m)+(_-m);LA[3]=D;const R=b$($A,OA,UA,l,n,-o,jA);const A=b$(OA,TA,IA,c,o,-l,FA);const $=b$(TA,CA,PA,p,l,-c,WA);const O=b$(CA,NA,qA,n,c,-p,XA);const T=b$(NA,$A,LA,o,p,-n,BA);const C=b$($A,IA,qA,c,n,o,ZA);const N=b$(OA,PA,LA,p,o,l,HA);const U=b$(TA,qA,UA,n,l,c,JA);const I=b$(CA,LA,IA,o,c,p,GA);const P=b$(NA,UA,PA,l,p,n,KA);const q=sum_three(x$($,WA,N,HA,I,GA,A,FA,t,e,n,VA),VA,x$(O,XA,U,JA,P,KA,$,WA,i,r,o,YA),YA,sum_three(x$(T,BA,I,GA,C,ZA,O,XA,a,s,l,QA),QA,x$(R,jA,P,KA,N,HA,T,BA,u,f,c,t$),t$,x$(A,FA,C,ZA,U,JA,R,jA,d,h,p,e$),e$,i$,r$),r$,n$,o$);return o$[q-1]}const w$=oR(96);const k$=oR(96);const M$=oR(96);const E$=oR(1152);function S$(t,e,n,i,r,o,a,s,l,u){const f=b$(t,e,n,i,r,o,f$);return sum_three(scale(scale(f,f$,a,c$),c$,a,w$),w$,scale(scale(f,f$,s,c$),c$,s,k$),k$,scale(scale(f,f$,l,c$),c$,l,M$),M$,p$,u)}function z$(t,e,n,i,r,o,a,s,l,u,f,c,d,h,p,m){let g,y,v,b,x,_;let w,k,M,E;let S,z,D,R;let A,$,O,T;let C,N,U,I,P,q,L,j,F,W,X,B,Z;const H=t-d;const J=i-d;const G=a-d;const K=u-d;const V=e-h;const Y=r-h;const Q=s-h;const tt=f-h;const et=n-p;const nt=o-p;const it=l-p;const rt=c-p;W=H*Y;N=splitter*H;U=N-(N-H);I=H-U;N=splitter*Y;P=N-(N-Y);q=Y-P;X=I*q-(W-U*P-I*P-U*q);B=J*V;N=splitter*J;U=N-(N-J);I=J-U;N=splitter*V;P=N-(N-V);q=V-P;Z=I*q-(B-U*P-I*P-U*q);L=X-Z;C=X-L;$A[0]=X-(L+C)+(C-Z);j=W+L;C=j-W;F=W-(j-C)+(L-C);L=F-B;C=F-L;$A[1]=F-(L+C)+(C-B);g=j+L;C=g-j;$A[2]=j-(g-C)+(L-C);$A[3]=g;W=J*Q;N=splitter*J;U=N-(N-J);I=J-U;N=splitter*Q;P=N-(N-Q);q=Q-P;X=I*q-(W-U*P-I*P-U*q);B=G*Y;N=splitter*G;U=N-(N-G);I=G-U;N=splitter*Y;P=N-(N-Y);q=Y-P;Z=I*q-(B-U*P-I*P-U*q);L=X-Z;C=X-L;OA[0]=X-(L+C)+(C-Z);j=W+L;C=j-W;F=W-(j-C)+(L-C);L=F-B;C=F-L;OA[1]=F-(L+C)+(C-B);y=j+L;C=y-j;OA[2]=j-(y-C)+(L-C);OA[3]=y;W=G*tt;N=splitter*G;U=N-(N-G);I=G-U;N=splitter*tt;P=N-(N-tt);q=tt-P;X=I*q-(W-U*P-I*P-U*q);B=K*Q;N=splitter*K;U=N-(N-K);I=K-U;N=splitter*Q;P=N-(N-Q);q=Q-P;Z=I*q-(B-U*P-I*P-U*q);L=X-Z;C=X-L;TA[0]=X-(L+C)+(C-Z);j=W+L;C=j-W;F=W-(j-C)+(L-C);L=F-B;C=F-L;TA[1]=F-(L+C)+(C-B);v=j+L;C=v-j;TA[2]=j-(v-C)+(L-C);TA[3]=v;W=K*V;N=splitter*K;U=N-(N-K);I=K-U;N=splitter*V;P=N-(N-V);q=V-P;X=I*q-(W-U*P-I*P-U*q);B=H*tt;N=splitter*H;U=N-(N-H);I=H-U;N=splitter*tt;P=N-(N-tt);q=tt-P;Z=I*q-(B-U*P-I*P-U*q);L=X-Z;C=X-L;qA[0]=X-(L+C)+(C-Z);j=W+L;C=j-W;F=W-(j-C)+(L-C);L=F-B;C=F-L;qA[1]=F-(L+C)+(C-B);b=j+L;C=b-j;qA[2]=j-(b-C)+(L-C);qA[3]=b;W=H*Q;N=splitter*H;U=N-(N-H);I=H-U;N=splitter*Q;P=N-(N-Q);q=Q-P;X=I*q-(W-U*P-I*P-U*q);B=G*V;N=splitter*G;U=N-(N-G);I=G-U;N=splitter*V;P=N-(N-V);q=V-P;Z=I*q-(B-U*P-I*P-U*q);L=X-Z;C=X-L;UA[0]=X-(L+C)+(C-Z);j=W+L;C=j-W;F=W-(j-C)+(L-C);L=F-B;C=F-L;UA[1]=F-(L+C)+(C-B);x=j+L;C=x-j;UA[2]=j-(x-C)+(L-C);UA[3]=x;W=J*tt;N=splitter*J;U=N-(N-J);I=J-U;N=splitter*tt;P=N-(N-tt);q=tt-P;X=I*q-(W-U*P-I*P-U*q);B=K*Y;N=splitter*K;U=N-(N-K);I=K-U;N=splitter*Y;P=N-(N-Y);q=Y-P;Z=I*q-(B-U*P-I*P-U*q);L=X-Z;C=X-L;IA[0]=X-(L+C)+(C-Z);j=W+L;C=j-W;F=W-(j-C)+(L-C);L=F-B;C=F-L;IA[1]=F-(L+C)+(C-B);_=j+L;C=_-j;IA[2]=j-(_-C)+(L-C);IA[3]=_;const ot=sum(sum(negate(S$(OA,TA,IA,rt,nt,-it,H,V,et,VA),VA),VA,S$(TA,qA,UA,et,it,rt,J,Y,nt,YA),YA,n$),n$,sum(negate(S$(qA,$A,IA,nt,rt,et,G,Q,it,QA),QA),QA,S$($A,OA,UA,it,et,-nt,K,tt,rt,t$),t$,i$),i$,E$);let at=estimate(ot,E$);let st=RA*m;if(at>=st||-at>=st){return at}C=t-H;w=t-(H+C)+(C-d);C=e-V;S=e-(V+C)+(C-h);C=n-et;A=n-(et+C)+(C-p);C=i-J;k=i-(J+C)+(C-d);C=r-Y;z=r-(Y+C)+(C-h);C=o-nt;$=o-(nt+C)+(C-p);C=a-G;M=a-(G+C)+(C-d);C=s-Q;D=s-(Q+C)+(C-h);C=l-it;O=l-(it+C)+(C-p);C=u-K;E=u-(K+C)+(C-d);C=f-tt;R=f-(tt+C)+(C-h);C=c-rt;T=c-(rt+C)+(C-p);if(w===0&&S===0&&A===0&&k===0&&z===0&&$===0&&M===0&&D===0&&O===0&&E===0&&R===0&&T===0){return at}st=AA*m+resulterrbound*Math.abs(at);const lt=H*z+Y*w-(V*k+J*S);const ut=J*D+Q*k-(Y*M+G*z);const ft=G*R+tt*M-(Q*E+K*D);const ct=K*S+V*E-(tt*w+H*R);const dt=H*D+Q*w-(V*M+G*S);const ht=J*R+tt*k-(Y*E+K*z);at+=(J*J+Y*Y+nt*nt)*(it*ct+rt*dt+et*ft+(O*b+T*x+A*v))+(K*K+tt*tt+rt*rt)*(et*ut-nt*dt+it*lt+(A*y-$*x+O*g))-((H*H+V*V+et*et)*(nt*ft-it*ht+rt*ut+($*v-O*_+T*y))+(G*G+Q*Q+it*it)*(rt*lt+et*ht+nt*ct+(T*g+A*_+$*b)))+2*((J*k+Y*z+nt*$)*(it*b+rt*x+et*v)+(K*E+tt*R+rt*T)*(et*y-nt*x+it*g)-((H*w+V*S+et*A)*(nt*v-it*_+rt*y)+(G*M+Q*D+it*O)*(rt*g+et*_+nt*b)));if(at>=st||-at>=st){return at}return _$(t,e,n,i,r,o,a,s,l,u,f,c,d,h,p)}function D$(t,e,n,i,r,o,a,s,l,u,f,c,d,h,p){const m=t-d;const g=i-d;const y=a-d;const v=u-d;const b=e-h;const x=r-h;const _=s-h;const w=f-h;const k=n-p;const M=o-p;const E=l-p;const S=c-p;const z=m*x;const D=g*b;const R=z-D;const A=g*_;const $=y*x;const O=A-$;const T=y*w;const C=v*_;const N=T-C;const U=v*b;const I=m*w;const P=U-I;const q=m*_;const L=y*b;const j=q-L;const F=g*w;const W=v*x;const X=F-W;const B=k*O-M*j+E*R;const Z=M*N-E*X+S*O;const H=E*P+S*j+k*N;const J=S*R+k*X+M*P;const G=m*m+b*b+k*k;const K=g*g+x*x+M*M;const V=y*y+_*_+E*E;const Y=v*v+w*w+S*S;const Q=V*J-Y*B+(G*Z-K*H);const tt=Math.abs(k);const et=Math.abs(M);const nt=Math.abs(E);const it=Math.abs(S);const rt=Math.abs(z);const ot=Math.abs(D);const at=Math.abs(A);const st=Math.abs($);const lt=Math.abs(T);const ut=Math.abs(C);const ft=Math.abs(U);const ct=Math.abs(I);const dt=Math.abs(q);const ht=Math.abs(L);const pt=Math.abs(F);const mt=Math.abs(W);const gt=((lt+ut)*et+(mt+pt)*nt+(at+st)*it)*G+((ft+ct)*nt+(dt+ht)*it+(lt+ut)*tt)*K+((rt+ot)*it+(pt+mt)*tt+(ft+ct)*et)*V+((at+st)*tt+(ht+dt)*et+(rt+ot)*nt)*Y;const yt=DA*gt;if(Q>yt||-Q>yt){return Q}return-z$(t,e,n,i,r,o,a,s,l,u,f,c,d,h,p,gt)}function R$(t,e,n,i,r,o,a,s,l,u,f,c,d,h,p){const m=t-d;const g=i-d;const y=a-d;const v=u-d;const b=e-h;const x=r-h;const _=s-h;const w=f-h;const k=n-p;const M=o-p;const E=l-p;const S=c-p;const z=m*x-g*b;const D=g*_-y*x;const R=y*w-v*_;const A=v*b-m*w;const $=m*_-y*b;const O=g*w-v*x;const T=k*D-M*$+E*z;const C=M*R-E*O+S*D;const N=E*A+S*$+k*R;const U=S*z+k*O+M*A;const I=m*m+b*b+k*k;const P=g*g+x*x+M*M;const q=y*y+_*_+E*E;const L=v*v+w*w+S*S;return q*U-L*T+(I*C-P*N)}const A$=Math.pow(2,-52);const $$=new Uint32Array(512);class O${static from(t,e=L$,n=j$){const i=t.length;const r=new Float64Array(i*2);for(let o=0;o<i;o++){const i=t[o];r[2*o]=e(i);r[2*o+1]=n(i)}return new O$(r)}constructor(t){const e=t.length>>1;if(e>0&&typeof t[0]!=="number")throw new Error("Expected coords to contain numbers.");this.coords=t;const n=Math.max(2*e-5,0);this._triangles=new Uint32Array(n*3);this._halfedges=new Int32Array(n*3);this._hashSize=Math.ceil(Math.sqrt(e));this._hullPrev=new Uint32Array(e);this._hullNext=new Uint32Array(e);this._hullTri=new Uint32Array(e);this._hullHash=new Int32Array(this._hashSize).fill(-1);this._ids=new Uint32Array(e);this._dists=new Float64Array(e);this.update()}update(){const{coords:t,_hullPrev:e,_hullNext:n,_hullTri:i,_hullHash:r}=this;const o=t.length>>1;let a=Infinity;let s=Infinity;let l=-Infinity;let u=-Infinity;for(let E=0;E<o;E++){const e=t[2*E];const n=t[2*E+1];if(e<a)a=e;if(n<s)s=n;if(e>l)l=e;if(n>u)u=n;this._ids[E]=E}const f=(a+l)/2;const c=(s+u)/2;let d=Infinity;let h,p,m;for(let E=0;E<o;E++){const e=C$(f,c,t[2*E],t[2*E+1]);if(e<d){h=E;d=e}}const g=t[2*h];const y=t[2*h+1];d=Infinity;for(let E=0;E<o;E++){if(E===h)continue;const e=C$(g,y,t[2*E],t[2*E+1]);if(e<d&&e>0){p=E;d=e}}let v=t[2*p];let b=t[2*p+1];let x=Infinity;for(let E=0;E<o;E++){if(E===h||E===p)continue;const e=U$(g,y,v,b,t[2*E],t[2*E+1]);if(e<x){m=E;x=e}}let _=t[2*m];let w=t[2*m+1];if(x===Infinity){for(let i=0;i<o;i++){this._dists[i]=t[2*i]-t[0]||t[2*i+1]-t[1]}P$(this._ids,this._dists,0,o-1);const e=new Uint32Array(o);let n=0;for(let t=0,i=-Infinity;t<o;t++){const r=this._ids[t];if(this._dists[r]>i){e[n++]=r;i=this._dists[r]}}this.hull=e.subarray(0,n);this.triangles=new Uint32Array(0);this.halfedges=new Uint32Array(0);return}if(mR(g,y,v,b,_,w)<0){const t=p;const e=v;const n=b;p=m;v=_;b=w;m=t;_=e;w=n}const k=I$(g,y,v,b,_,w);this._cx=k.x;this._cy=k.y;for(let E=0;E<o;E++){this._dists[E]=C$(t[2*E],t[2*E+1],k.x,k.y)}P$(this._ids,this._dists,0,o-1);this._hullStart=h;let M=3;n[h]=e[m]=p;n[p]=e[h]=m;n[m]=e[p]=h;i[h]=0;i[p]=1;i[m]=2;r.fill(-1);r[this._hashKey(g,y)]=h;r[this._hashKey(v,b)]=p;r[this._hashKey(_,w)]=m;this.trianglesLen=0;this._addTriangle(h,p,m,-1,-1,-1);for(let E=0,S,z;E<this._ids.length;E++){const o=this._ids[E];const a=t[2*o];const s=t[2*o+1];if(E>0&&Math.abs(a-S)<=A$&&Math.abs(s-z)<=A$)continue;S=a;z=s;if(o===h||o===p||o===m)continue;let l=0;for(let t=0,e=this._hashKey(a,s);t<this._hashSize;t++){l=r[(e+t)%this._hashSize];if(l!==-1&&l!==n[l])break}l=e[l];let u=l,f;while(f=n[u],mR(a,s,t[2*u],t[2*u+1],t[2*f],t[2*f+1])>=0){u=f;if(u===l){u=-1;break}}if(u===-1)continue;let c=this._addTriangle(u,o,n[u],-1,-1,i[u]);i[o]=this._legalize(c+2);i[u]=c;M++;let d=n[u];while(f=n[d],mR(a,s,t[2*d],t[2*d+1],t[2*f],t[2*f+1])<0){c=this._addTriangle(d,o,f,i[o],-1,i[d]);i[o]=this._legalize(c+2);n[d]=d;M--;d=f}if(u===l){while(f=e[u],mR(a,s,t[2*f],t[2*f+1],t[2*u],t[2*u+1])<0){c=this._addTriangle(f,o,u,-1,i[u],i[f]);this._legalize(c+2);i[f]=c;n[u]=u;M--;u=f}}this._hullStart=e[o]=u;n[u]=e[d]=o;n[o]=d;r[this._hashKey(a,s)]=o;r[this._hashKey(t[2*u],t[2*u+1])]=u}this.hull=new Uint32Array(M);for(let E=0,S=this._hullStart;E<M;E++){this.hull[E]=S;S=n[S]}this.triangles=this._triangles.subarray(0,this.trianglesLen);this.halfedges=this._halfedges.subarray(0,this.trianglesLen)}_hashKey(t,e){return Math.floor(T$(t-this._cx,e-this._cy)*this._hashSize)%this._hashSize}_legalize(t){const{_triangles:e,_halfedges:n,coords:i}=this;let r=0;let o=0;while(true){const a=n[t];const s=t-t%3;o=s+(t+2)%3;if(a===-1){if(r===0)break;t=$$[--r];continue}const l=a-a%3;const u=s+(t+1)%3;const f=l+(a+2)%3;const c=e[o];const d=e[t];const h=e[u];const p=e[f];const m=N$(i[2*c],i[2*c+1],i[2*d],i[2*d+1],i[2*h],i[2*h+1],i[2*p],i[2*p+1]);if(m){e[t]=p;e[a]=c;const i=n[f];if(i===-1){let e=this._hullStart;do{if(this._hullTri[e]===f){this._hullTri[e]=t;break}e=this._hullPrev[e]}while(e!==this._hullStart)}this._link(t,i);this._link(a,n[o]);this._link(o,f);const s=l+(a+1)%3;if(r<$$.length){$$[r++]=s}}else{if(r===0)break;t=$$[--r]}}return o}_link(t,e){this._halfedges[t]=e;if(e!==-1)this._halfedges[e]=t}_addTriangle(t,e,n,i,r,o){const a=this.trianglesLen;this._triangles[a]=t;this._triangles[a+1]=e;this._triangles[a+2]=n;this._link(a,i);this._link(a+1,r);this._link(a+2,o);this.trianglesLen+=3;return a}}function T$(t,e){const n=t/(Math.abs(t)+Math.abs(e));return(e>0?3-n:1+n)/4}function C$(t,e,n,i){const r=t-n;const o=e-i;return r*r+o*o}function N$(t,e,n,i,r,o,a,s){const l=t-a;const u=e-s;const f=n-a;const c=i-s;const d=r-a;const h=o-s;const p=l*l+u*u;const m=f*f+c*c;const g=d*d+h*h;return l*(c*g-m*h)-u*(f*g-m*d)+p*(f*h-c*d)<0}function U$(t,e,n,i,r,o){const a=n-t;const s=i-e;const l=r-t;const u=o-e;const f=a*a+s*s;const c=l*l+u*u;const d=.5/(a*u-s*l);const h=(u*f-s*c)*d;const p=(a*c-l*f)*d;return h*h+p*p}function I$(t,e,n,i,r,o){const a=n-t;const s=i-e;const l=r-t;const u=o-e;const f=a*a+s*s;const c=l*l+u*u;const d=.5/(a*u-s*l);const h=t+(u*f-s*c)*d;const p=e+(a*c-l*f)*d;return{x:h,y:p}}function P$(t,e,n,i){if(i-n<=20){for(let r=n+1;r<=i;r++){const i=t[r];const o=e[i];let a=r-1;while(a>=n&&e[t[a]]>o)t[a+1]=t[a--];t[a+1]=i}}else{const r=n+i>>1;let o=n+1;let a=i;q$(t,r,o);if(e[t[n]]>e[t[i]])q$(t,n,i);if(e[t[o]]>e[t[i]])q$(t,o,i);if(e[t[n]]>e[t[o]])q$(t,n,o);const s=t[o];const l=e[s];while(true){do{o++}while(e[t[o]]<l);do{a--}while(e[t[a]]>l);if(a<o)break;q$(t,o,a)}t[n+1]=t[a];t[a]=s;if(i-o+1>=a-n){P$(t,e,o,i);P$(t,e,n,a-1)}else{P$(t,e,n,a-1);P$(t,e,o,i)}}}function q$(t,e,n){const i=t[e];t[e]=t[n];t[n]=i}function L$(t){return t[0]}function j$(t){return t[1]}const F$=1e-6;class W${constructor(){this._x0=this._y0=this._x1=this._y1=null;this._=""}moveTo(t,e){this._+=`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){if(this._x1!==null){this._x1=this._x0,this._y1=this._y0;this._+="Z"}}lineTo(t,e){this._+=`L${this._x1=+t},${this._y1=+e}`}arc(t,e,n){t=+t,e=+e,n=+n;const i=t+n;const r=e;if(n<0)throw new Error("negative radius");if(this._x1===null)this._+=`M${i},${r}`;else if(Math.abs(this._x1-i)>F$||Math.abs(this._y1-r)>F$)this._+="L"+i+","+r;if(!n)return;this._+=`A${n},${n},0,1,1,${t-n},${e}A${n},${n},0,1,1,${this._x1=i},${this._y1=r}`}rect(t,e,n,i){this._+=`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${+n}v${+i}h${-n}Z`}value(){return this._||null}}class X${constructor(){this._=[]}moveTo(t,e){this._.push([t,e])}closePath(){this._.push(this._[0].slice())}lineTo(t,e){this._.push([t,e])}value(){return this._.length?this._:null}}class B${constructor(t,[e,n,i,r]=[0,0,960,500]){if(!((i=+i)>=(e=+e))||!((r=+r)>=(n=+n)))throw new Error("invalid bounds");this.delaunay=t;this._circumcenters=new Float64Array(t.points.length*2);this.vectors=new Float64Array(t.points.length*2);this.xmax=i,this.xmin=e;this.ymax=r,this.ymin=n;this._init()}update(){this.delaunay.update();this._init();return this}_init(){const{delaunay:{points:t,hull:e,triangles:n},vectors:i}=this;let r,o;const a=this.circumcenters=this._circumcenters.subarray(0,n.length/3*2);for(let p=0,m=0,g=n.length,y,v;p<g;p+=3,m+=2){const i=n[p]*2;const s=n[p+1]*2;const l=n[p+2]*2;const u=t[i];const f=t[i+1];const c=t[s];const d=t[s+1];const h=t[l];const g=t[l+1];const b=c-u;const x=d-f;const _=h-u;const w=g-f;const k=(b*w-x*_)*2;if(Math.abs(k)<1e-9){if(r===undefined){r=o=0;for(const n of e)r+=t[n*2],o+=t[n*2+1];r/=e.length,o/=e.length}const n=1e9*Math.sign((r-u)*w-(o-f)*_);y=(u+h)/2-n*w;v=(f+g)/2+n*_}else{const t=1/k;const e=b*b+x*x;const n=_*_+w*w;y=u+(w*e-x*n)*t;v=f+(b*n-_*e)*t}a[m]=y;a[m+1]=v}let s=e[e.length-1];let l,u=s*4;let f,c=t[2*s];let d,h=t[2*s+1];i.fill(0);for(let p=0;p<e.length;++p){s=e[p];l=u,f=c,d=h;u=s*4,c=t[2*s],h=t[2*s+1];i[l+2]=i[u]=d-h;i[l+3]=i[u+1]=c-f}}render(t){const e=t==null?t=new W$:undefined;const{delaunay:{halfedges:n,inedges:i,hull:r},circumcenters:o,vectors:a}=this;if(r.length<=1)return null;for(let u=0,f=n.length;u<f;++u){const e=n[u];if(e<u)continue;const i=Math.floor(u/3)*2;const r=Math.floor(e/3)*2;const a=o[i];const s=o[i+1];const l=o[r];const f=o[r+1];this._renderSegment(a,s,l,f,t)}let s,l=r[r.length-1];for(let u=0;u<r.length;++u){s=l,l=r[u];const e=Math.floor(i[l]/3)*2;const n=o[e];const f=o[e+1];const c=s*4;const d=this._project(n,f,a[c+2],a[c+3]);if(d)this._renderSegment(n,f,d[0],d[1],t)}return e&&e.value()}renderBounds(t){const e=t==null?t=new W$:undefined;t.rect(this.xmin,this.ymin,this.xmax-this.xmin,this.ymax-this.ymin);return e&&e.value()}renderCell(t,e){const n=e==null?e=new W$:undefined;const i=this._clip(t);if(i===null||!i.length)return;e.moveTo(i[0],i[1]);let r=i.length;while(i[0]===i[r-2]&&i[1]===i[r-1]&&r>1)r-=2;for(let o=2;o<r;o+=2){if(i[o]!==i[o-2]||i[o+1]!==i[o-1])e.lineTo(i[o],i[o+1])}e.closePath();return n&&n.value()}*cellPolygons(){const{delaunay:{points:t}}=this;for(let e=0,n=t.length/2;e<n;++e){const t=this.cellPolygon(e);if(t)t.index=e,yield t}}cellPolygon(t){const e=new X$;this.renderCell(t,e);return e.value()}_renderSegment(t,e,n,i,r){let o;const a=this._regioncode(t,e);const s=this._regioncode(n,i);if(a===0&&s===0){r.moveTo(t,e);r.lineTo(n,i)}else if(o=this._clipSegment(t,e,n,i,a,s)){r.moveTo(o[0],o[1]);r.lineTo(o[2],o[3])}}contains(t,e,n){if((e=+e,e!==e)||(n=+n,n!==n))return false;return this.delaunay._step(t,e,n)===t}*neighbors(t){const e=this._clip(t);if(e)for(const n of this.delaunay.neighbors(t)){const t=this._clip(n);if(t)t:for(let i=0,r=e.length;i<r;i+=2){for(let o=0,a=t.length;o<a;o+=2){if(e[i]===t[o]&&e[i+1]===t[o+1]&&e[(i+2)%r]===t[(o+a-2)%a]&&e[(i+3)%r]===t[(o+a-1)%a]){yield n;break t}}}}}_cell(t){const{circumcenters:e,delaunay:{inedges:n,halfedges:i,triangles:r}}=this;const o=n[t];if(o===-1)return null;const a=[];let s=o;do{const n=Math.floor(s/3);a.push(e[n*2],e[n*2+1]);s=s%3===2?s-2:s+1;if(r[s]!==t)break;s=i[s]}while(s!==o&&s!==-1);return a}_clip(t){if(t===0&&this.delaunay.hull.length===1){return[this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax,this.xmin,this.ymin]}const e=this._cell(t);if(e===null)return null;const{vectors:n}=this;const i=t*4;return this._simplify(n[i]||n[i+1]?this._clipInfinite(t,e,n[i],n[i+1],n[i+2],n[i+3]):this._clipFinite(t,e))}_clipFinite(t,e){const n=e.length;let i=null;let r,o,a=e[n-2],s=e[n-1];let l,u=this._regioncode(a,s);let f,c=0;for(let d=0;d<n;d+=2){r=a,o=s,a=e[d],s=e[d+1];l=u,u=this._regioncode(a,s);if(l===0&&u===0){f=c,c=0;if(i)i.push(a,s);else i=[a,s]}else{let e,n,d,h,p;if(l===0){if((e=this._clipSegment(r,o,a,s,l,u))===null)continue;[n,d,h,p]=e}else{if((e=this._clipSegment(a,s,r,o,u,l))===null)continue;[h,p,n,d]=e;f=c,c=this._edgecode(n,d);if(f&&c)this._edge(t,f,c,i,i.length);if(i)i.push(n,d);else i=[n,d]}f=c,c=this._edgecode(h,p);if(f&&c)this._edge(t,f,c,i,i.length);if(i)i.push(h,p);else i=[h,p]}}if(i){f=c,c=this._edgecode(i[0],i[1]);if(f&&c)this._edge(t,f,c,i,i.length)}else if(this.contains(t,(this.xmin+this.xmax)/2,(this.ymin+this.ymax)/2)){return[this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax,this.xmin,this.ymin]}return i}_clipSegment(t,e,n,i,r,o){const a=r<o;if(a)[t,e,n,i,r,o]=[n,i,t,e,o,r];while(true){if(r===0&&o===0)return a?[n,i,t,e]:[t,e,n,i];if(r&o)return null;let s,l,u=r||o;if(u&8)s=t+(n-t)*(this.ymax-e)/(i-e),l=this.ymax;else if(u&4)s=t+(n-t)*(this.ymin-e)/(i-e),l=this.ymin;else if(u&2)l=e+(i-e)*(this.xmax-t)/(n-t),s=this.xmax;else l=e+(i-e)*(this.xmin-t)/(n-t),s=this.xmin;if(r)t=s,e=l,r=this._regioncode(t,e);else n=s,i=l,o=this._regioncode(n,i)}}_clipInfinite(t,e,n,i,r,o){let a=Array.from(e),s;if(s=this._project(a[0],a[1],n,i))a.unshift(s[0],s[1]);if(s=this._project(a[a.length-2],a[a.length-1],r,o))a.push(s[0],s[1]);if(a=this._clipFinite(t,a)){for(let e=0,n=a.length,i,r=this._edgecode(a[n-2],a[n-1]);e<n;e+=2){i=r,r=this._edgecode(a[e],a[e+1]);if(i&&r)e=this._edge(t,i,r,a,e),n=a.length}}else if(this.contains(t,(this.xmin+this.xmax)/2,(this.ymin+this.ymax)/2)){a=[this.xmin,this.ymin,this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax]}return a}_edge(t,e,n,i,r){while(e!==n){let n,o;switch(e){case 5:e=4;continue;case 4:e=6,n=this.xmax,o=this.ymin;break;case 6:e=2;continue;case 2:e=10,n=this.xmax,o=this.ymax;break;case 10:e=8;continue;case 8:e=9,n=this.xmin,o=this.ymax;break;case 9:e=1;continue;case 1:e=5,n=this.xmin,o=this.ymin;break}if((i[r]!==n||i[r+1]!==o)&&this.contains(t,n,o)){i.splice(r,0,n,o),r+=2}}return r}_project(t,e,n,i){let r=Infinity,o,a,s;if(i<0){if(e<=this.ymin)return null;if((o=(this.ymin-e)/i)<r)s=this.ymin,a=t+(r=o)*n}else if(i>0){if(e>=this.ymax)return null;if((o=(this.ymax-e)/i)<r)s=this.ymax,a=t+(r=o)*n}if(n>0){if(t>=this.xmax)return null;if((o=(this.xmax-t)/n)<r)a=this.xmax,s=e+(r=o)*i}else if(n<0){if(t<=this.xmin)return null;if((o=(this.xmin-t)/n)<r)a=this.xmin,s=e+(r=o)*i}return[a,s]}_edgecode(t,e){return(t===this.xmin?1:t===this.xmax?2:0)|(e===this.ymin?4:e===this.ymax?8:0)}_regioncode(t,e){return(t<this.xmin?1:t>this.xmax?2:0)|(e<this.ymin?4:e>this.ymax?8:0)}_simplify(t){if(t&&t.length>4){for(let e=0;e<t.length;e+=2){const n=(e+2)%t.length,i=(e+4)%t.length;if(t[e]===t[n]&&t[n]===t[i]||t[e+1]===t[n+1]&&t[n+1]===t[i+1]){t.splice(n,2),e-=2}}if(!t.length)t=null}return t}}const Z$=2*Math.PI,H$=Math.pow;function J$(t){return t[0]}function G$(t){return t[1]}function K$(t){const{triangles:e,coords:n}=t;for(let i=0;i<e.length;i+=3){const t=2*e[i],r=2*e[i+1],o=2*e[i+2],a=(n[o]-n[t])*(n[r+1]-n[t+1])-(n[r]-n[t])*(n[o+1]-n[t+1]);if(a>1e-10)return false}return true}function V$(t,e,n){return[t+Math.sin(t+e)*n,e+Math.cos(t-e)*n]}class Y${static from(t,e=J$,n=G$,i){return new Y$("length"in t?Q$(t,e,n,i):Float64Array.from(tO(t,e,n,i)))}constructor(t){this._delaunator=new O$(t);this.inedges=new Int32Array(t.length/2);this._hullIndex=new Int32Array(t.length/2);this.points=this._delaunator.coords;this._init()}update(){this._delaunator.update();this._init();return this}_init(){const t=this._delaunator,e=this.points;if(t.hull&&t.hull.length>2&&K$(t)){this.collinear=Int32Array.from({length:e.length/2},((t,e)=>e)).sort(((t,n)=>e[2*t]-e[2*n]||e[2*t+1]-e[2*n+1]));const t=this.collinear[0],n=this.collinear[this.collinear.length-1],i=[e[2*t],e[2*t+1],e[2*n],e[2*n+1]],r=1e-8*Math.hypot(i[3]-i[1],i[2]-i[0]);for(let o=0,a=e.length/2;o<a;++o){const t=V$(e[2*o],e[2*o+1],r);e[2*o]=t[0];e[2*o+1]=t[1]}this._delaunator=new O$(e)}else{delete this.collinear}const n=this.halfedges=this._delaunator.halfedges;const i=this.hull=this._delaunator.hull;const r=this.triangles=this._delaunator.triangles;const o=this.inedges.fill(-1);const a=this._hullIndex.fill(-1);for(let s=0,l=n.length;s<l;++s){const t=r[s%3===2?s-2:s+1];if(n[s]===-1||o[t]===-1)o[t]=s}for(let s=0,l=i.length;s<l;++s){a[i[s]]=s}if(i.length<=2&&i.length>0){this.triangles=new Int32Array(3).fill(-1);this.halfedges=new Int32Array(3).fill(-1);this.triangles[0]=i[0];o[i[0]]=1;if(i.length===2){o[i[1]]=0;this.triangles[1]=i[1];this.triangles[2]=i[1]}}}voronoi(t){return new B$(this,t)}*neighbors(t){const{inedges:e,hull:n,_hullIndex:i,halfedges:r,triangles:o,collinear:a}=this;if(a){const e=a.indexOf(t);if(e>0)yield a[e-1];if(e<a.length-1)yield a[e+1];return}const s=e[t];if(s===-1)return;let l=s,u=-1;do{yield u=o[l];l=l%3===2?l-2:l+1;if(o[l]!==t)return;l=r[l];if(l===-1){const e=n[(i[t]+1)%n.length];if(e!==u)yield e;return}}while(l!==s)}find(t,e,n=0){if((t=+t,t!==t)||(e=+e,e!==e))return-1;const i=n;let r;while((r=this._step(n,t,e))>=0&&r!==n&&r!==i)n=r;return r}_step(t,e,n){const{inedges:i,hull:r,_hullIndex:o,halfedges:a,triangles:s,points:l}=this;if(i[t]===-1||!l.length)return(t+1)%(l.length>>1);let u=t;let f=H$(e-l[t*2],2)+H$(n-l[t*2+1],2);const c=i[t];let d=c;do{let i=s[d];const c=H$(e-l[i*2],2)+H$(n-l[i*2+1],2);if(c<f)f=c,u=i;d=d%3===2?d-2:d+1;if(s[d]!==t)break;d=a[d];if(d===-1){d=r[(o[t]+1)%r.length];if(d!==i){if(H$(e-l[d*2],2)+H$(n-l[d*2+1],2)<f)return d}break}}while(d!==c);return u}render(t){const e=t==null?t=new W$:undefined;const{points:n,halfedges:i,triangles:r}=this;for(let o=0,a=i.length;o<a;++o){const e=i[o];if(e<o)continue;const a=r[o]*2;const s=r[e]*2;t.moveTo(n[a],n[a+1]);t.lineTo(n[s],n[s+1])}this.renderHull(t);return e&&e.value()}renderPoints(t,e){if(e===undefined&&(!t||typeof t.moveTo!=="function"))e=t,t=null;e=e==undefined?2:+e;const n=t==null?t=new W$:undefined;const{points:i}=this;for(let r=0,o=i.length;r<o;r+=2){const n=i[r],o=i[r+1];t.moveTo(n+e,o);t.arc(n,o,e,0,Z$)}return n&&n.value()}renderHull(t){const e=t==null?t=new W$:undefined;const{hull:n,points:i}=this;const r=n[0]*2,o=n.length;t.moveTo(i[r],i[r+1]);for(let a=1;a<o;++a){const e=2*n[a];t.lineTo(i[e],i[e+1])}t.closePath();return e&&e.value()}hullPolygon(){const t=new X$;this.renderHull(t);return t.value()}renderTriangle(t,e){const n=e==null?e=new W$:undefined;const{points:i,triangles:r}=this;const o=r[t*=3]*2;const a=r[t+1]*2;const s=r[t+2]*2;e.moveTo(i[o],i[o+1]);e.lineTo(i[a],i[a+1]);e.lineTo(i[s],i[s+1]);e.closePath();return n&&n.value()}*trianglePolygons(){const{triangles:t}=this;for(let e=0,n=t.length/3;e<n;++e){yield this.trianglePolygon(e)}}trianglePolygon(t){const e=new X$;this.renderTriangle(t,e);return e.value()}}function Q$(t,e,n,i){const r=t.length;const o=new Float64Array(r*2);for(let a=0;a<r;++a){const r=t[a];o[a*2]=e.call(i,r,a,t);o[a*2+1]=n.call(i,r,a,t)}return o}function*tO(t,e,n,i){let r=0;for(const o of t){yield e.call(i,o,r,t);yield n.call(i,o,r,t);++r}}function eO(t){Oi.call(this,null,t)}eO.Definition={type:"Voronoi",metadata:{modifies:true},params:[{name:"x",type:"field",required:true},{name:"y",type:"field",required:true},{name:"size",type:"number",array:true,length:2},{name:"extent",type:"array",array:true,length:2,default:[[-1e5,-1e5],[1e5,1e5]],content:{type:"number",array:true,length:2}},{name:"as",type:"string",default:"path"}]};const nO=[-1e5,-1e5,1e5,1e5];(0,p.XW)(eO,Oi,{transform(t,e){const n=t.as||"path",i=e.source;if(!i||!i.length)return e;let r=t.size;r=r?[0,0,r[0],r[1]]:(r=t.extent)?[r[0][0],r[0][1],r[1][0],r[1][1]]:nO;const o=this.value=Y$.from(i,t.x,t.y).voronoi(r);for(let a=0,s=i.length;a<s;++a){const t=o.cellPolygon(a);i[a][n]=t?iO(t):null}return e.reflow(t.modified()).modifies(n)}});function iO(t){const e=t[0][0],n=t[0][1];let i=t.length-1;for(;t[i][0]===e&&t[i][1]===n;--i);return"M"+t.slice(0,i+1).join("L")+"Z"}var rO=Math.PI/180,oO=1<<11>>5,aO=1<<11;function sO(){var t=[256,256],e,n,i,r,o,a,s,l=dO,u=[],f=Math.random,c={};c.layout=function(){var l=d(Ks()),c=pO((t[0]>>5)*t[1]),p=null,m=u.length,g=-1,y=[],v=u.map((t=>({text:e(t),font:n(t),style:r(t),weight:o(t),rotate:a(t),size:~~(i(t)+1e-14),padding:s(t),xoff:0,yoff:0,x1:0,y1:0,x0:0,y0:0,hasText:false,sprite:null,datum:t}))).sort(((t,e)=>e.size-t.size));while(++g<m){var b=v[g];b.x=t[0]*(f()+.5)>>1;b.y=t[1]*(f()+.5)>>1;lO(l,b,v,g);if(b.hasText&&h(c,b,p)){y.push(b);if(p)fO(p,b);else p=[{x:b.x+b.x0,y:b.y+b.y0},{x:b.x+b.x1,y:b.y+b.y1}];b.x-=t[0]>>1;b.y-=t[1]>>1}}return y};function d(t){t.width=t.height=1;var e=Math.sqrt(t.getContext("2d").getImageData(0,0,1,1).data.length>>2);t.width=(oO<<5)/e;t.height=aO/e;var n=t.getContext("2d");n.fillStyle=n.strokeStyle="red";n.textAlign="center";return{context:n,ratio:e}}function h(e,n,i){var r=n.x,o=n.y,a=Math.sqrt(t[0]*t[0]+t[1]*t[1]),s=l(t),u=f()<.5?1:-1,c=-u,d,h,p;while(d=s(c+=u)){h=~~d[0];p=~~d[1];if(Math.min(Math.abs(h),Math.abs(p))>=a)break;n.x=r+h;n.y=o+p;if(n.x+n.x0<0||n.y+n.y0<0||n.x+n.x1>t[0]||n.y+n.y1>t[1])continue;if(!i||!uO(n,e,t[0])){if(!i||cO(n,i)){var m=n.sprite,g=n.width>>5,y=t[0]>>5,v=n.x-(g<<4),b=v&127,x=32-b,_=n.y1-n.y0,w=(n.y+n.y0)*y+(v>>5),k;for(var M=0;M<_;M++){k=0;for(var E=0;E<=g;E++){e[w+E]|=k<<x|(E<g?(k=m[M*g+E])>>>b:0)}w+=y}n.sprite=null;return true}}}return false}c.words=function(t){if(arguments.length){u=t;return c}else{return u}};c.size=function(e){if(arguments.length){t=[+e[0],+e[1]];return c}else{return t}};c.font=function(t){if(arguments.length){n=mO(t);return c}else{return n}};c.fontStyle=function(t){if(arguments.length){r=mO(t);return c}else{return r}};c.fontWeight=function(t){if(arguments.length){o=mO(t);return c}else{return o}};c.rotate=function(t){if(arguments.length){a=mO(t);return c}else{return a}};c.text=function(t){if(arguments.length){e=mO(t);return c}else{return e}};c.spiral=function(t){if(arguments.length){l=gO[t]||t;return c}else{return l}};c.fontSize=function(t){if(arguments.length){i=mO(t);return c}else{return i}};c.padding=function(t){if(arguments.length){s=mO(t);return c}else{return s}};c.random=function(t){if(arguments.length){f=t;return c}else{return f}};return c}function lO(t,e,n,i){if(e.sprite)return;var r=t.context,o=t.ratio;r.clearRect(0,0,(oO<<5)/o,aO/o);var a=0,s=0,l=0,u=n.length,f,c,d,h,p;--i;while(++i<u){e=n[i];r.save();r.font=e.style+" "+e.weight+" "+~~((e.size+1)/o)+"px "+e.font;f=r.measureText(e.text+"m").width*o;d=e.size<<1;if(e.rotate){var m=Math.sin(e.rotate*rO),g=Math.cos(e.rotate*rO),y=f*g,v=f*m,b=d*g,x=d*m;f=Math.max(Math.abs(y+x),Math.abs(y-x))+31>>5<<5;d=~~Math.max(Math.abs(v+b),Math.abs(v-b))}else{f=f+31>>5<<5}if(d>l)l=d;if(a+f>=oO<<5){a=0;s+=l;l=0}if(s+d>=aO)break;r.translate((a+(f>>1))/o,(s+(d>>1))/o);if(e.rotate)r.rotate(e.rotate*rO);r.fillText(e.text,0,0);if(e.padding){r.lineWidth=2*e.padding;r.strokeText(e.text,0,0)}r.restore();e.width=f;e.height=d;e.xoff=a;e.yoff=s;e.x1=f>>1;e.y1=d>>1;e.x0=-e.x1;e.y0=-e.y1;e.hasText=true;a+=f}var _=r.getImageData(0,0,(oO<<5)/o,aO/o).data,w=[];while(--i>=0){e=n[i];if(!e.hasText)continue;f=e.width;c=f>>5;d=e.y1-e.y0;for(h=0;h<d*c;h++)w[h]=0;a=e.xoff;if(a==null)return;s=e.yoff;var k=0,M=-1;for(p=0;p<d;p++){for(h=0;h<f;h++){var E=c*p+(h>>5),S=_[(s+p)*(oO<<5)+(a+h)<<2]?1<<31-h%32:0;w[E]|=S;k|=S}if(k)M=p;else{e.y0++;d--;p--;s++}}e.y1=e.y0+M;e.sprite=w.slice(0,(e.y1-e.y0)*c)}}function uO(t,e,n){n>>=5;var i=t.sprite,r=t.width>>5,o=t.x-(r<<4),a=o&127,s=32-a,l=t.y1-t.y0,u=(t.y+t.y0)*n+(o>>5),f;for(var c=0;c<l;c++){f=0;for(var d=0;d<=r;d++){if((f<<s|(d<r?(f=i[c*r+d])>>>a:0))&e[u+d])return true}u+=n}return false}function fO(t,e){var n=t[0],i=t[1];if(e.x+e.x0<n.x)n.x=e.x+e.x0;if(e.y+e.y0<n.y)n.y=e.y+e.y0;if(e.x+e.x1>i.x)i.x=e.x+e.x1;if(e.y+e.y1>i.y)i.y=e.y+e.y1}function cO(t,e){return t.x+t.x1>e[0].x&&t.x+t.x0<e[1].x&&t.y+t.y1>e[0].y&&t.y+t.y0<e[1].y}function dO(t){var e=t[0]/t[1];return function(t){return[e*(t*=.1)*Math.cos(t),t*Math.sin(t)]}}function hO(t){var e=4,n=e*t[0]/t[1],i=0,r=0;return function(t){var o=t<0?-1:1;switch(Math.sqrt(1+4*o*t)-o&3){case 0:i+=n;break;case 1:r+=e;break;case 2:i-=n;break;default:r-=e;break}return[i,r]}}function pO(t){var e=[],n=-1;while(++n<t)e[n]=0;return e}function mO(t){return typeof t==="function"?t:function(){return t}}var gO={archimedean:dO,rectangular:hO};const yO=["x","y","font","fontSize","fontStyle","fontWeight","angle"];const vO=["text","font","rotate","fontSize","fontStyle","fontWeight"];function bO(t){Oi.call(this,sO(),t)}bO.Definition={type:"Wordcloud",metadata:{modifies:true},params:[{name:"size",type:"number",array:true,length:2},{name:"font",type:"string",expr:true,default:"sans-serif"},{name:"fontStyle",type:"string",expr:true,default:"normal"},{name:"fontWeight",type:"string",expr:true,default:"normal"},{name:"fontSize",type:"number",expr:true,default:14},{name:"fontSizeRange",type:"number",array:"nullable",default:[10,50]},{name:"rotate",type:"number",expr:true,default:0},{name:"text",type:"field"},{name:"spiral",type:"string",values:["archimedean","rectangular"]},{name:"padding",type:"number",expr:true},{name:"as",type:"string",array:true,length:7,default:yO}]};(0,p.XW)(bO,Oi,{transform(t,e){if(t.size&&!(t.size[0]&&t.size[1])){(0,p.vU)("Wordcloud size dimensions must be non-zero.")}function n(n){const i=t[n];return(0,p.mf)(i)&&e.modified(i.fields)}const i=t.modified();if(!(i||e.changed(e.ADD_REM)||vO.some(n)))return;const r=e.materialize(e.SOURCE).source,o=this.value,a=t.as||yO;let s=t.fontSize||14,l;(0,p.mf)(s)?l=t.fontSizeRange:s=(0,p.a9)(s);if(l){const t=s,e=Tu("sqrt")().domain((0,p.We)(r,t)).range(l);s=n=>e(t(n))}r.forEach((t=>{t[a[0]]=NaN;t[a[1]]=NaN;t[a[3]]=0}));const u=o.words(r).text(t.text).size(t.size||[500,500]).padding(t.padding||1).spiral(t.spiral||"archimedean").rotate(t.rotate||0).font(t.font||"sans-serif").fontStyle(t.fontStyle||"normal").fontWeight(t.fontWeight||"normal").fontSize(s).random(ir).layout();const f=o.size(),c=f[0]>>1,d=f[1]>>1,h=u.length;for(let p=0,m,g;p<h;++p){m=u[p];g=m.datum;g[a[0]]=m.x+c;g[a[1]]=m.y+d;g[a[2]]=m.font;g[a[3]]=m.size;g[a[4]]=m.style;g[a[5]]=m.weight;g[a[6]]=m.rotate}return e.reflow(i).modifies(a)}});function xO(t,e){return Array.from(e,(e=>t[e]))}const _O=t=>new Uint8Array(t);const wO=t=>new Uint16Array(t);const kO=t=>new Uint32Array(t);function MO(){let t=8,e=[],n=kO(0),i=SO(0,t),r=SO(0,t);return{data:()=>e,seen:()=>n=EO(n,e.length),add(t){for(let n=0,i=e.length,r=t.length,o;n<r;++n){o=t[n];o._index=i++;e.push(o)}},remove(t,n){const o=e.length,a=Array(o-t),s=e;let l,u,f;for(u=0;!n[u]&&u<o;++u){a[u]=e[u];s[u]=u}for(f=u;u<o;++u){l=e[u];if(!n[u]){s[u]=f;i[f]=i[u];r[f]=r[u];a[f]=l;l._index=f++}else{s[u]=-1}i[u]=0}e=a;return s},size:()=>e.length,curr:()=>i,prev:()=>r,reset:t=>r[t]=i[t],all:()=>t<257?255:t<65537?65535:4294967295,set(t,e){i[t]|=e},clear(t,e){i[t]&=~e},resize(e,n){const o=i.length;if(e>o||n>t){t=Math.max(n,t);i=SO(e,t,i);r=SO(e,t)}}}}function EO(t,e,n){if(t.length>=e)return t;n=n||new t.constructor(e);n.set(t);return n}function SO(t,e,n){const i=(e<257?_O:e<65537?wO:kO)(t);if(n)i.set(n);return i}function zO(t,e,n){const i=1<<e;return{one:i,zero:~i,range:n.slice(),bisect:t.bisect,index:t.index,size:t.size,onAdd(t,e){const n=this,r=n.bisect(n.range,t.value),o=t.index,a=r[0],s=r[1],l=o.length;let u;for(u=0;u<a;++u)e[o[u]]|=i;for(u=s;u<l;++u)e[o[u]]|=i;return n}}}function DO(){let t=kO(0),e=[],n=0;function i(i,r,o){if(!r.length)return[];const a=n,s=r.length,l=kO(s);let u=Array(s),f,c,d;for(d=0;d<s;++d){u[d]=i(r[d]);l[d]=d}u=RO(u,l);if(a){f=e;c=t;e=Array(a+s);t=kO(a+s);AO(o,f,c,a,u,l,s,e,t)}else{if(o>0)for(d=0;d<s;++d){l[d]+=o}e=u;t=l}n=a+s;return{index:l,value:u}}function r(i,r){const o=n;let a,s,l;for(s=0;!r[t[s]]&&s<o;++s);for(l=s;s<o;++s){if(!r[a=t[s]]){t[l]=a;e[l]=e[s];++l}}n=o-i}function o(e){for(let i=0,r=n;i<r;++i){t[i]=e[t[i]]}}function a(t,i){let r;if(i){r=i.length}else{i=e;r=n}return[(0,Ys.Nw)(i,t[0],0,r),(0,Ys.ml)(i,t[1],0,r)]}return{insert:i,remove:r,bisect:a,reindex:o,index:()=>t,size:()=>n}}function RO(t,e){t.sort.call(e,((e,n)=>{const i=t[e],r=t[n];return i<r?-1:i>r?1:0}));return xO(t,e)}function AO(t,e,n,i,r,o,a,s,l){let u=0,f=0,c;for(c=0;u<i&&f<a;++c){if(e[u]<r[f]){s[c]=e[u];l[c]=n[u++]}else{s[c]=r[f];l[c]=o[f++]+t}}for(;u<i;++u,++c){s[c]=e[u];l[c]=n[u]}for(;f<a;++f,++c){s[c]=r[f];l[c]=o[f]+t}}function $O(t){Oi.call(this,MO(),t);this._indices=null;this._dims=null}$O.Definition={type:"CrossFilter",metadata:{},params:[{name:"fields",type:"field",array:true,required:true},{name:"query",type:"array",array:true,required:true,content:{type:"number",array:true,length:2}}]};(0,p.XW)($O,Oi,{transform(t,e){if(!this._dims){return this.init(t,e)}else{var n=t.modified("fields")||t.fields.some((t=>e.modified(t.fields)));return n?this.reinit(t,e):this.eval(t,e)}},init(t,e){const n=t.fields,i=t.query,r=this._indices={},o=this._dims=[],a=i.length;let s=0,l,u;for(;s<a;++s){l=n[s].fname;u=r[l]||(r[l]=DO());o.push(zO(u,s,i[s]))}return this.eval(t,e)},reinit(t,e){const n=e.materialize().fork(),i=t.fields,r=t.query,o=this._indices,a=this._dims,s=this.value,l=s.curr(),u=s.prev(),f=s.all(),c=n.rem=n.add,d=n.mod,h=r.length,p={};let m,g,y,v,b,x,_,w,k;u.set(l);if(e.rem.length){b=this.remove(t,e,n)}if(e.add.length){s.add(e.add)}if(e.mod.length){x={};for(v=e.mod,_=0,w=v.length;_<w;++_){x[v[_]._index]=1}}for(_=0;_<h;++_){k=i[_];if(!a[_]||t.modified("fields",_)||e.modified(k.fields)){y=k.fname;if(!(m=p[y])){o[y]=g=DO();p[y]=m=g.insert(k,e.source,0)}a[_]=zO(g,_,r[_]).onAdd(m,l)}}for(_=0,w=s.data().length;_<w;++_){if(b[_]){continue}else if(u[_]!==l[_]){c.push(_)}else if(x[_]&&l[_]!==f){d.push(_)}}s.mask=(1<<h)-1;return n},eval(t,e){const n=e.materialize().fork(),i=this._dims.length;let r=0;if(e.rem.length){this.remove(t,e,n);r|=(1<<i)-1}if(t.modified("query")&&!t.modified("fields")){r|=this.update(t,e,n)}if(e.add.length){this.insert(t,e,n);r|=(1<<i)-1}if(e.mod.length){this.modify(e,n);r|=(1<<i)-1}this.value.mask=r;return n},insert(t,e,n){const i=e.add,r=this.value,o=this._dims,a=this._indices,s=t.fields,l={},u=n.add,f=r.size()+i.length,c=o.length;let d=r.size(),h,p,m;r.resize(f,c);r.add(i);const g=r.curr(),y=r.prev(),v=r.all();for(h=0;h<c;++h){p=s[h].fname;m=l[p]||(l[p]=a[p].insert(s[h],i,d));o[h].onAdd(m,g)}for(;d<f;++d){y[d]=v;if(g[d]!==v)u.push(d)}},modify(t,e){const n=e.mod,i=this.value,r=i.curr(),o=i.all(),a=t.mod;let s,l,u;for(s=0,l=a.length;s<l;++s){u=a[s]._index;if(r[u]!==o)n.push(u)}},remove(t,e,n){const i=this._indices,r=this.value,o=r.curr(),a=r.prev(),s=r.all(),l={},u=n.rem,f=e.rem;let c,d,h,p;for(c=0,d=f.length;c<d;++c){h=f[c]._index;l[h]=1;a[h]=p=o[h];o[h]=s;if(p!==s)u.push(h)}for(h in i){i[h].remove(d,l)}this.reindex(e,d,l);return l},reindex(t,e,n){const i=this._indices,r=this.value;t.runAfter((()=>{const t=r.remove(e,n);for(const e in i)i[e].reindex(t)}))},update(t,e,n){const i=this._dims,r=t.query,o=e.stamp,a=i.length;let s=0,l,u;n.filters=0;for(u=0;u<a;++u){if(t.modified("query",u)){l=u;++s}}if(s===1){s=i[l].one;this.incrementOne(i[l],r[l],n.add,n.rem)}else{for(u=0,s=0;u<a;++u){if(!t.modified("query",u))continue;s|=i[u].one;this.incrementAll(i[u],r[u],o,n.add);n.rem=n.add}}return s},incrementAll(t,e,n,i){const r=this.value,o=r.seen(),a=r.curr(),s=r.prev(),l=t.index(),u=t.bisect(t.range),f=t.bisect(e),c=f[0],d=f[1],h=u[0],p=u[1],m=t.one;let g,y,v;if(c<h){for(g=c,y=Math.min(h,d);g<y;++g){v=l[g];if(o[v]!==n){s[v]=a[v];o[v]=n;i.push(v)}a[v]^=m}}else if(c>h){for(g=h,y=Math.min(c,p);g<y;++g){v=l[g];if(o[v]!==n){s[v]=a[v];o[v]=n;i.push(v)}a[v]^=m}}if(d>p){for(g=Math.max(c,p),y=d;g<y;++g){v=l[g];if(o[v]!==n){s[v]=a[v];o[v]=n;i.push(v)}a[v]^=m}}else if(d<p){for(g=Math.max(h,d),y=p;g<y;++g){v=l[g];if(o[v]!==n){s[v]=a[v];o[v]=n;i.push(v)}a[v]^=m}}t.range=e.slice()},incrementOne(t,e,n,i){const r=this.value,o=r.curr(),a=t.index(),s=t.bisect(t.range),l=t.bisect(e),u=l[0],f=l[1],c=s[0],d=s[1],h=t.one;let p,m,g;if(u<c){for(p=u,m=Math.min(c,f);p<m;++p){g=a[p];o[g]^=h;n.push(g)}}else if(u>c){for(p=c,m=Math.min(u,d);p<m;++p){g=a[p];o[g]^=h;i.push(g)}}if(f>d){for(p=Math.max(u,d),m=f;p<m;++p){g=a[p];o[g]^=h;n.push(g)}}else if(f<d){for(p=Math.max(c,f),m=d;p<m;++p){g=a[p];o[g]^=h;i.push(g)}}t.range=e.slice()}});function OO(t){Oi.call(this,null,t)}OO.Definition={type:"ResolveFilter",metadata:{},params:[{name:"ignore",type:"number",required:true,description:"A bit mask indicating which filters to ignore."},{name:"filter",type:"object",required:true,description:"Per-tuple filter bitmaps from a CrossFilter transform."}]};(0,p.XW)(OO,Oi,{transform(t,e){const n=~(t.ignore||0),i=t.filter,r=i.mask;if((r&n)===0)return e.StopPropagation;const o=e.fork(e.ALL),a=i.data(),s=i.curr(),l=i.prev(),u=t=>!(s[t]&n)?a[t]:null;o.filter(o.MOD,u);if(!(r&r-1)){o.filter(o.ADD,u);o.filter(o.REM,(t=>(s[t]&n)===r?a[t]:null))}else{o.filter(o.ADD,(t=>{const e=s[t]&n,i=!e&&e^l[t]&n;return i?a[t]:null}));o.filter(o.REM,(t=>{const e=s[t]&n,i=e&&!(e^(e^l[t]&n));return i?a[t]:null}))}return o.filter(o.SOURCE,(t=>u(t._index)))}});var TO=n(25693);var CO=new Lb;var NO=new Lb,UO,IO,PO,qO,LO;var jO={point:px,lineStart:px,lineEnd:px,polygonStart:function(){CO=new Lb;jO.lineStart=FO;jO.lineEnd=WO},polygonEnd:function(){var t=+CO;NO.add(t<0?Jb+t:t);this.lineStart=this.lineEnd=this.point=px},sphere:function(){NO.add(Jb)}};function FO(){jO.point=XO}function WO(){BO(UO,IO)}function XO(t,e){jO.point=BO;UO=t,IO=e;t*=Kb,e*=Kb;PO=t,qO=tx(e=e/2+Hb),LO=sx(e)}function BO(t,e){t*=Kb,e*=Kb;e=e/2+Hb;var n=t-PO,i=n>=0?1:-1,r=i*n,o=tx(e),a=sx(e),s=LO*a,l=qO*o+s*tx(r),u=s*i*sx(r);CO.add(Qb(u,l));PO=t,qO=o,LO=a}function ZO(t){NO=new Lb;qb(t,jO);return NO*2}var HO,JO,GO,KO,VO,YO,QO,tT,eT,nT,iT;var rT={point:oT,lineStart:sT,lineEnd:lT,polygonStart:function(){rT.point=uT;rT.lineStart=fT;rT.lineEnd=cT;eT=new Lb;jO.polygonStart()},polygonEnd:function(){jO.polygonEnd();rT.point=oT;rT.lineStart=sT;rT.lineEnd=lT;if(CO<0)HO=-(GO=180),JO=-(KO=90);else if(eT>Wb)KO=90;else if(eT<-Wb)JO=-90;iT[0]=HO,iT[1]=GO},sphere:function(){HO=-(GO=180),JO=-(KO=90)}};function oT(t,e){nT.push(iT=[HO=t,GO=t]);if(e<JO)JO=e;if(e>KO)KO=e}function aT(t,e){var n=R_([t*Kb,e*Kb]);if(tT){var i=$_(tT,n),r=[i[1],-i[0],0],o=$_(r,i);C_(o);o=D_(o);var a=t-VO,s=a>0?1:-1,l=o[0]*Gb*s,u,f=Vb(a)>180;if(f^(s*VO<l&&l<s*t)){u=o[1]*Gb;if(u>KO)KO=u}else if(l=(l+360)%360-180,f^(s*VO<l&&l<s*t)){u=-o[1]*Gb;if(u<JO)JO=u}else{if(e<JO)JO=e;if(e>KO)KO=e}if(f){if(t<VO){if(dT(HO,t)>dT(HO,GO))GO=t}else{if(dT(t,GO)>dT(HO,GO))HO=t}}else{if(GO>=HO){if(t<HO)HO=t;if(t>GO)GO=t}else{if(t>VO){if(dT(HO,t)>dT(HO,GO))GO=t}else{if(dT(t,GO)>dT(HO,GO))HO=t}}}}else{nT.push(iT=[HO=t,GO=t])}if(e<JO)JO=e;if(e>KO)KO=e;tT=n,VO=t}function sT(){rT.point=aT}function lT(){iT[0]=HO,iT[1]=GO;rT.point=oT;tT=null}function uT(t,e){if(tT){var n=t-VO;eT.add(Vb(n)>180?n+(n>0?360:-360):n)}else{YO=t,QO=e}jO.point(t,e);aT(t,e)}function fT(){jO.lineStart()}function cT(){uT(YO,QO);jO.lineEnd();if(Vb(eT)>Wb)HO=-(GO=180);iT[0]=HO,iT[1]=GO;tT=null}function dT(t,e){return(e-=t)<0?e+360:e}function hT(t,e){return t[0]-e[0]}function pT(t,e){return t[0]<=t[1]?t[0]<=e&&e<=t[1]:e<t[0]||t[1]<e}function mT(t){var e,n,i,r,o,a,s;KO=GO=-(HO=JO=Infinity);nT=[];qb(t,rT);if(n=nT.length){nT.sort(hT);for(e=1,i=nT[0],o=[i];e<n;++e){r=nT[e];if(pT(i,r[0])||pT(i,r[1])){if(dT(i[0],r[1])>dT(i[0],i[1]))i[1]=r[1];if(dT(r[0],i[1])>dT(i[0],i[1]))i[0]=r[0]}else{o.push(i=r)}}for(a=-Infinity,n=o.length-1,e=0,i=o[n];e<=n;i=r,++e){r=o[e];if((s=dT(i[1],r[0]))>a)a=s,HO=r[0],GO=i[1]}}nT=iT=null;return HO===Infinity||JO===Infinity?[[NaN,NaN],[NaN,NaN]]:[[HO,JO],[GO,KO]]}var gT,yT,vT,bT,xT,_T,wT,kT,MT,ET,ST,zT,DT,RT,AT,$T;var OT={sphere:px,point:TT,lineStart:NT,lineEnd:PT,polygonStart:function(){OT.lineStart=qT;OT.lineEnd=LT},polygonEnd:function(){OT.lineStart=NT;OT.lineEnd=PT}};function TT(t,e){t*=Kb,e*=Kb;var n=tx(e);CT(n*tx(t),n*sx(t),sx(e))}function CT(t,e,n){++gT;vT+=(t-vT)/gT;bT+=(e-bT)/gT;xT+=(n-xT)/gT}function NT(){OT.point=UT}function UT(t,e){t*=Kb,e*=Kb;var n=tx(e);RT=n*tx(t);AT=n*sx(t);$T=sx(e);OT.point=IT;CT(RT,AT,$T)}function IT(t,e){t*=Kb,e*=Kb;var n=tx(e),i=n*tx(t),r=n*sx(t),o=sx(e),a=Qb(ux((a=AT*o-$T*r)*a+(a=$T*i-RT*o)*a+(a=RT*r-AT*i)*a),RT*i+AT*r+$T*o);yT+=a;_T+=a*(RT+(RT=i));wT+=a*(AT+(AT=r));kT+=a*($T+($T=o));CT(RT,AT,$T)}function PT(){OT.point=TT}function qT(){OT.point=jT}function LT(){FT(zT,DT);OT.point=TT}function jT(t,e){zT=t,DT=e;t*=Kb,e*=Kb;OT.point=FT;var n=tx(e);RT=n*tx(t);AT=n*sx(t);$T=sx(e);CT(RT,AT,$T)}function FT(t,e){t*=Kb,e*=Kb;var n=tx(e),i=n*tx(t),r=n*sx(t),o=sx(e),a=AT*o-$T*r,s=$T*i-RT*o,l=RT*r-AT*i,u=rx(a,s,l),f=dx(u),c=u&&-f/u;MT.add(c*a);ET.add(c*s);ST.add(c*l);yT+=f;_T+=f*(RT+(RT=i));wT+=f*(AT+(AT=r));kT+=f*($T+($T=o));CT(RT,AT,$T)}function WT(t){gT=yT=vT=bT=xT=_T=wT=kT=0;MT=new Lb;ET=new Lb;ST=new Lb;qb(t,OT);var e=+MT,n=+ET,i=+ST,r=rx(e,n,i);if(r<Xb){e=_T,n=wT,i=kT;if(yT<Wb)e=vT,n=bT,i=xT;r=rx(e,n,i);if(r<Xb)return[NaN,NaN]}return[Qb(n,e)*Gb,dx(i/r)*Gb]}var XT=n(80241);var BT=n(28419);function ZT(...t){const e=new BT.H;for(const n of t){for(const t of n){e.add(t)}}return e}function HT(t,...e){t=new BT.H(t);e=e.map(JT);t:for(const n of t){for(const i of e){if(!i.has(n)){t.delete(n);continue t}}}return t}function JT(t){return t instanceof BT.H?t:new BT.H(t)}const GT="intersect";const KT="union";const VT="vlMulti";const YT="vlPoint";const QT="or";const tC="and";const eC="_vgsid_";const nC=(0,p.EP)(eC);const iC="E",rC="R",oC="R-E",aC="R-LE",sC="R-RE",lC="index:unit";function uC(t,e){var n=e.fields,i=e.values,r=n.length,o=0,a,s;for(;o<r;++o){s=n[o];s.getter=p.EP.getter||(0,p.EP)(s.field);a=s.getter(t);if((0,p.J_)(a))a=(0,p.He)(a);if((0,p.J_)(i[o]))i[o]=(0,p.He)(i[o]);if((0,p.J_)(i[o][0]))i[o]=i[o].map(p.He);if(s.type===iC){if((0,p.kJ)(i[o])?i[o].indexOf(a)<0:a!==i[o]){return false}}else{if(s.type===rC){if(!(0,p.u5)(a,i[o]))return false}else if(s.type===sC){if(!(0,p.u5)(a,i[o],true,false))return false}else if(s.type===oC){if(!(0,p.u5)(a,i[o],false,false))return false}else if(s.type===aC){if(!(0,p.u5)(a,i[o],false,true))return false}}}return true}function fC(t,e,n){var i=this.context.data[t],r=i?i.values.value:[],o=i?i[lC]&&i[lC].value:undefined,a=n===GT,s=r.length,l=0,u,f,c,d,h;for(;l<s;++l){u=r[l];if(o&&a){f=f||{};c=f[d=u.unit]||0;if(c===-1)continue;h=uC(e,u);f[d]=h?-1:++c;if(h&&o.size===1)return true;if(!h&&c===o.get(d).count)return false}else{h=uC(e,u);if(a^h)return h}}return s&&a}const cC=(0,Y.Z)(nC),dC=cC.left,hC=cC.right;function pC(t,e,n){const i=this.context.data[t],r=i?i.values.value:[],o=i?i[lC]&&i[lC].value:undefined,a=n===GT,s=nC(e),l=dC(r,s);if(l===r.length)return false;if(nC(r[l])!==s)return false;if(o&&a){if(o.size===1)return true;if(hC(r,s)-l<o.size)return false}return true}function mC(t,e){return t.map((t=>(0,p.l7)(e.fields?{values:e.fields.map((e=>(e.getter||(e.getter=(0,p.EP)(e.field)))(t.datum)))}:{[eC]:nC(t.datum)},e)))}function gC(t,e,n,i){var r=this.context.data[t],o=r?r.values.value:[],a={},s={},l={},u,f,c,d,h,m,g,y,v,b,x=o.length,_=0,w,k;for(;_<x;++_){u=o[_];d=u.unit;f=u.fields;c=u.values;if(f&&c){for(w=0,k=f.length;w<k;++w){h=f[w];g=a[h.field]||(a[h.field]={});y=g[d]||(g[d]=[]);l[h.field]=v=h.type.charAt(0);b=yC[`${v}_union`];g[d]=b(y,(0,p.IX)(c[w]))}if(n){y=s[d]||(s[d]=[]);y.push((0,p.IX)(c).reduce(((t,e,n)=>(t[f[n].field]=e,t)),{}))}}else{h=eC;m=nC(u);g=a[h]||(a[h]={});y=g[d]||(g[d]=[]);y.push(m);if(n){y=s[d]||(s[d]=[]);y.push({[eC]:m})}}}e=e||KT;if(a[eC]){a[eC]=yC[`${eC}_${e}`](...Object.values(a[eC]))}else{Object.keys(a).forEach((t=>{a[t]=Object.keys(a[t]).map((e=>a[t][e])).reduce(((n,i)=>n===undefined?i:yC[`${l[t]}_${e}`](n,i)))}))}o=Object.keys(s);if(n&&o.length){const t=i?YT:VT;a[t]=e===KT?{[QT]:o.reduce(((t,e)=>(t.push(...s[e]),t)),[])}:{[tC]:o.map((t=>({[QT]:s[t]})))}}return a}var yC={[`${eC}_union`]:ZT,[`${eC}_intersect`]:HT,E_union:function(t,e){if(!t.length)return e;var n=0,i=e.length;for(;n<i;++n)if(t.indexOf(e[n])<0)t.push(e[n]);return t},E_intersect:function(t,e){return!t.length?e:t.filter((t=>e.indexOf(t)>=0))},R_union:function(t,e){var n=(0,p.He)(e[0]),i=(0,p.He)(e[1]);if(n>i){n=e[1];i=e[0]}if(!t.length)return[n,i];if(t[0]>n)t[0]=n;if(t[1]<i)t[1]=i;return t},R_intersect:function(t,e){var n=(0,p.He)(e[0]),i=(0,p.He)(e[1]);if(n>i){n=e[1];i=e[0]}if(!t.length)return[n,i];if(i<t[0]||t[1]<n){return[]}else{if(t[0]<n)t[0]=n;if(t[1]>i)t[1]=i}return t}};const vC=":",bC="@";function xC(t,e,n,i){if(e[0].type!==TO.t$)(0,p.vU)("First argument to selection functions must be a string literal.");const r=e[0].value,o=e.length>=2&&(0,p.fj)(e).value,a="unit",s=bC+a,l=vC+r;if(o===GT&&!(0,p.nr)(i,s)){i[s]=n.getData(r).indataRef(n,a)}if(!(0,p.nr)(i,l)){i[l]=n.getData(r).tuplesRef()}}function _C(t){const e=this.context.data[t];return e?e.values.value:[]}function wC(t,e,n){const i=this.context.data[t]["index:"+e],r=i?i.value.get(n):undefined;return r?r.count:r}function kC(t,e){const n=this.context.dataflow,i=this.context.data[t],r=i.input;n.pulse(r,n.changeset().remove(p.yb).insert(e));return 1}function MC(t,e,n){if(t){const n=this.context.dataflow,i=t.mark.source;n.pulse(i,n.changeset().encode(t,e))}return n!==undefined?n:t}const EC=t=>function(e,n){const i=this.context.dataflow.locale();return i[t](n)(e)};const SC=EC("format");const zC=EC("timeFormat");const DC=EC("utcFormat");const RC=EC("timeParse");const AC=EC("utcParse");const $C=new Date(2e3,0,1);function OC(t,e,n){if(!Number.isInteger(t)||!Number.isInteger(e))return"";$C.setYear(2e3);$C.setMonth(t);$C.setDate(e);return zC.call(this,$C,n)}function TC(t){return OC.call(this,t,1,"%B")}function CC(t){return OC.call(this,t,1,"%b")}function NC(t){return OC.call(this,0,2+t,"%A")}function UC(t){return OC.call(this,0,2+t,"%a")}const IC=":";const PC="@";const qC="%";const LC="$";function jC(t,e,n,i){if(e[0].type!==TO.t$){(0,p.vU)("First argument to data functions must be a string literal.")}const r=e[0].value,o=IC+r;if(!(0,p.nr)(o,i)){try{i[o]=n.getData(r).tuplesRef()}catch(a){}}}function FC(t,e,n,i){if(e[0].type!==TO.t$)(0,p.vU)("First argument to indata must be a string literal.");if(e[1].type!==TO.t$)(0,p.vU)("Second argument to indata must be a string literal.");const r=e[0].value,o=e[1].value,a=PC+o;if(!(0,p.nr)(a,i)){i[a]=n.getData(r).indataRef(n,o)}}function WC(t,e,n,i){if(e[0].type===TO.t$){XC(n,i,e[0].value)}else{for(t in n.scales){XC(n,i,t)}}}function XC(t,e,n){const i=qC+n;if(!(0,p.nr)(e,i)){try{e[i]=t.scaleRef(n)}catch(r){}}}function BC(t,e){if((0,p.mf)(t)){return t}if((0,p.HD)(t)){const n=e.scales[t];return n&&$u(n.value)?n.value:undefined}return undefined}function ZC(t,e,n){e.__bandwidth=t=>t&&t.bandwidth?t.bandwidth():0;n._bandwidth=WC;n._range=WC;n._scale=WC;const i=e=>"_["+(e.type===TO.t$?(0,p.m8)(qC+e.value):(0,p.m8)(qC)+"+"+t(e))+"]";return{_bandwidth:t=>`this.__bandwidth(${i(t[0])})`,_range:t=>`${i(t[0])}.range()`,_scale:e=>`${i(e[0])}(${t(e[1])})`}}function HC(t,e){return function(n,i,r){if(n){const e=BC(n,(r||this).context);return e&&e.path[t](i)}else{return e(i)}}}const JC=HC("area",ZO);const GC=HC("bounds",mT);const KC=HC("centroid",WT);function VC(t){const e=this.context.group;let n=false;if(e)while(t){if(t===e){n=true;break}t=t.mark.group}return n}function YC(t,e,n){try{t[e].apply(t,["EXPRESSION"].concat([].slice.call(n)))}catch(i){t.warn(i)}return n[n.length-1]}function QC(){return YC(this.context.dataflow,"warn",arguments)}function tN(){return YC(this.context.dataflow,"info",arguments)}function eN(){return YC(this.context.dataflow,"debug",arguments)}function nN(t){const e=t/255;if(e<=.03928){return e/12.92}return Math.pow((e+.055)/1.055,2.4)}function iN(t){const e=(0,uM.B8)(t),n=nN(e.r),i=nN(e.g),r=nN(e.b);return.2126*n+.7152*i+.0722*r}function rN(t,e){const n=iN(t),i=iN(e),r=Math.max(n,i),o=Math.min(n,i);return(r+.05)/(o+.05)}function oN(){const t=[].slice.call(arguments);t.unshift({});return(0,p.l7)(...t)}function aN(t,e){return t===e||t!==t&&e!==e?true:(0,p.kJ)(t)?(0,p.kJ)(e)&&t.length===e.length?sN(t,e):false:(0,p.Kn)(t)&&(0,p.Kn)(e)?lN(t,e):false}function sN(t,e){for(let n=0,i=t.length;n<i;++n){if(!aN(t[n],e[n]))return false}return true}function lN(t,e){for(const n in t){if(!aN(t[n],e[n]))return false}return true}function uN(t){return e=>lN(t,e)}function fN(t,e,n,i,r,o){const a=this.context.dataflow,s=this.context.data[t],l=s.input,u=a.stamp();let f=s.changes,c,d;if(a._trigger===false||!(l.value.length||e||i)){return 0}if(!f||f.stamp<u){s.changes=f=a.changeset();f.stamp=u;a.runAfter((()=>{s.modified=true;a.pulse(l,f).run()}),true,1)}if(n){c=n===true?p.yb:(0,p.kJ)(n)||gn(n)?n:uN(n);f.remove(c)}if(e){f.insert(e)}if(i){c=uN(i);if(l.value.some(c)){f.remove(c)}else{f.insert(i)}}if(r){for(d in o){f.modify(r,d,o[d])}}return 1}function cN(t){const e=t.touches,n=e[0].clientX-e[1].clientX,i=e[0].clientY-e[1].clientY;return Math.sqrt(n*n+i*i)}function dN(t){const e=t.touches;return Math.atan2(e[0].clientY-e[1].clientY,e[0].clientX-e[1].clientX)}const hN={};function pN(t,e){const n=hN[e]||(hN[e]=(0,p.EP)(e));return(0,p.kJ)(t)?t.map(n):n(t)}function mN(t){return(0,p.kJ)(t)||ArrayBuffer.isView(t)?t:null}function gN(t){return mN(t)||((0,p.HD)(t)?t:null)}function yN(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),i=1;i<e;i++){n[i-1]=arguments[i]}return mN(t).join(...n)}function vN(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),i=1;i<e;i++){n[i-1]=arguments[i]}return gN(t).indexOf(...n)}function bN(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),i=1;i<e;i++){n[i-1]=arguments[i]}return gN(t).lastIndexOf(...n)}function xN(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),i=1;i<e;i++){n[i-1]=arguments[i]}return gN(t).slice(...n)}function _N(t,e,n){if((0,p.mf)(n))(0,p.vU)("Function argument passed to replace.");return String(t).replace(e,n)}function wN(t){return mN(t).slice().reverse()}function kN(t,e,n){return Kl(t||0,e||0,n||0)}function MN(t,e){const n=BC(t,(e||this).context);return n&&n.bandwidth?n.bandwidth():0}function EN(t,e){const n=BC(t,(e||this).context);return n?n.copy():undefined}function SN(t,e){const n=BC(t,(e||this).context);return n?n.domain():[]}function zN(t,e,n){const i=BC(t,(n||this).context);return!i?undefined:(0,p.kJ)(e)?(i.invertRange||i.invert)(e):(i.invert||i.invertExtent)(e)}function DN(t,e){const n=BC(t,(e||this).context);return n&&n.range?n.range():[]}function RN(t,e,n){const i=BC(t,(n||this).context);return i?i(e):undefined}function AN(t,e,n,i,r){t=BC(t,(r||this).context);const o=If(e,n);let a=t.domain(),s=a[0],l=(0,p.fj)(a),u=p.yR;if(!(l-s)){t=(t.interpolator?Tu("sequential")().interpolator(t.interpolator()):Tu("linear")().interpolate(t.interpolate()).range(t.range())).domain([s=0,l=1])}else{u=Ju(t,s,l)}if(t.ticks){a=t.ticks(+i||15);if(s!==a[0])a.unshift(s);if(l!==(0,p.fj)(a))a.push(l)}a.forEach((e=>o.stop(u(e),t(e))));return o}function $N(t,e,n){const i=BC(t,(n||this).context);return function(t){return i?i.path.context(t)(e):""}}function ON(t){let e=null;return function(n){return n?oc(n,e=e||Bf(t)):t}}const TN=t=>t.data;function CN(t,e){const n=_C.call(e,t);return n.root&&n.root.lookup||{}}function NN(t,e,n){const i=CN(t,this),r=i[e],o=i[n];return r&&o?r.path(o).map(TN):undefined}function UN(t,e){const n=CN(t,this)[e];return n?n.ancestors().map(TN):undefined}const IN=()=>typeof window!=="undefined"&&window||null;function PN(){const t=IN();return t?t.screen:{}}function qN(){const t=IN();return t?[t.innerWidth,t.innerHeight]:[undefined,undefined]}function LN(){const t=this.context.dataflow,e=t.container&&t.container();return e?[e.clientWidth,e.clientHeight]:[undefined,undefined]}function jN(t,e,n){if(!t)return[];const[i,r]=t,o=(new ad).set(i[0],i[1],r[0],r[1]),a=n||this.context.dataflow.scenegraph().root;return Xg(a,o,FN(e))}function FN(t){let e=null;if(t){const n=(0,p.IX)(t.marktype),i=(0,p.IX)(t.markname);e=t=>(!n.length||n.some((e=>t.marktype===e)))&&(!i.length||i.some((e=>t.name===e)))}return e}function WN(t,e,n){let i=arguments.length>3&&arguments[3]!==undefined?arguments[3]:5;t=(0,p.IX)(t);const r=t[t.length-1];return r===undefined||Math.sqrt((r[0]-e)**2+(r[1]-n)**2)>i?[...t,[e,n]]:t}function XN(t){return(0,p.IX)(t).reduce(((e,n,i)=>{let[r,o]=n;return e+=i==0?`M ${r},${o} `:i===t.length-1?" Z":`L ${r},${o} `}),"")}function BN(t,e,n){const{x:i,y:r,mark:o}=n;const a=(new ad).set(Number.MAX_SAFE_INTEGER,Number.MAX_SAFE_INTEGER,Number.MIN_SAFE_INTEGER,Number.MIN_SAFE_INTEGER);for(const[l,u]of e){if(l<a.x1)a.x1=l;if(l>a.x2)a.x2=l;if(u<a.y1)a.y1=u;if(u>a.y2)a.y2=u}a.translate(i,r);const s=jN([[a.x1,a.y1],[a.x2,a.y2]],t,o);return s.filter((t=>ZN(t.x,t.y,e)))}function ZN(t,e,n){let i=0;for(let r=0,o=n.length-1;r<n.length;o=r++){const[a,s]=n[o];const[l,u]=n[r];if(u>e!=s>e&&t<(a-l)*(e-u)/(s-u)+l){i++}}return i&1}const HN={random(){return ir()},cumulativeNormal:mr,cumulativeLogNormal:wr,cumulativeUniform:Dr,densityNormal:pr,densityLogNormal:_r,densityUniform:zr,quantileNormal:gr,quantileLogNormal:kr,quantileUniform:Rr,sampleNormal:hr,sampleLogNormal:xr,sampleUniform:Sr,isArray:p.kJ,isBoolean:p.jn,isDate:p.J_,isDefined(t){return t!==undefined},isNumber:p.hj,isObject:p.Kn,isRegExp:p.Kj,isString:p.HD,isTuple:gn,isValid(t){return t!=null&&t===t},toBoolean:p.sw,toDate(t){return(0,p.ZU)(t)},toNumber:p.He,toString:p.BB,indexof:vN,join:yN,lastindexof:bN,replace:_N,reverse:wN,slice:xN,flush:p.yl,lerp:p.t7,merge:oN,pad:p.vk,peek:p.fj,pluck:pN,span:p.yP,inrange:p.u5,truncate:p.$G,rgb:uM.B8,lab:XT.ZP,hcl:XT.Uc,hsl:uM.Ym,luminance:iN,contrast:rN,sequence:to.Z,format:SC,utcFormat:DC,utcParse:AC,utcOffset:Xt,utcSequence:Ht,timeFormat:zC,timeParse:RC,timeOffset:Wt,timeSequence:Zt,timeUnitSpecifier:pt,monthFormat:TC,monthAbbrevFormat:CC,dayFormat:NC,dayAbbrevFormat:UC,quarter:p.mS,utcquarter:p.N3,week:vt,utcweek:Mt,dayofyear:yt,utcdayofyear:kt,warn:QC,info:tN,debug:eN,extent(t){return(0,p.We)(t)},inScope:VC,intersect:jN,clampRange:p.l$,pinchDistance:cN,pinchAngle:dN,screen:PN,containerSize:LN,windowSize:qN,bandspace:kN,setdata:kC,pathShape:ON,panLinear:p.Dw,panLog:p.mJ,panPow:p.QA,panSymlog:p.Zw,zoomLinear:p.ay,zoomLog:p.dH,zoomPow:p.mK,zoomSymlog:p.bV,encode:MC,modify:fN,lassoAppend:WN,lassoPath:XN,intersectLasso:BN};const JN=["view","item","group","xy","x","y"],GN="event.vega.",KN="this.",VN={};const YN={forbidden:["_"],allowed:["datum","event","item"],fieldvar:"datum",globalvar:t=>`_[${(0,p.m8)(LC+t)}]`,functions:tU,constants:TO._G,visitors:VN};const QN=(0,TO.YP)(YN);function tU(t){const e=(0,TO.wk)(t);JN.forEach((t=>e[t]=GN+t));for(const n in HN){e[n]=KN+n}(0,p.l7)(e,ZC(t,HN,VN));return e}function eU(t,e,n){if(arguments.length===1){return HN[t]}HN[t]=e;if(n)VN[t]=n;if(QN)QN.functions[t]=KN+t;return this}eU("bandwidth",MN,WC);eU("copy",EN,WC);eU("domain",SN,WC);eU("range",DN,WC);eU("invert",zN,WC);eU("scale",RN,WC);eU("gradient",AN,WC);eU("geoArea",JC,WC);eU("geoBounds",GC,WC);eU("geoCentroid",KC,WC);eU("geoShape",$N,WC);eU("indata",wC,FC);eU("data",_C,jC);eU("treePath",NN,jC);eU("treeAncestors",UN,jC);eU("vlSelectionTest",fC,xC);eU("vlSelectionIdTest",pC,xC);eU("vlSelectionResolve",gC,xC);eU("vlSelectionTuples",mC);function nU(t,e){const n={};let i;try{t=(0,p.HD)(t)?t:(0,p.m8)(t)+"";i=(0,TO.BJ)(t)}catch(o){(0,p.vU)("Expression parse error: "+t)}i.visit((t=>{if(t.type!==TO.Lt)return;const i=t.callee.name,r=YN.visitors[i];if(r)r(i,t.arguments,e,n)}));const r=QN(i);r.globals.forEach((t=>{const i=LC+t;if(!(0,p.nr)(n,i)&&e.getSignal(t)){n[i]=e.signalRef(t)}}));return{$expr:(0,p.l7)({code:r.code},e.options.ast?{ast:i}:null),$fields:r.fields,$params:n}}function iU(t){const e=this,n=t.operators||[];if(t.background){e.background=t.background}if(t.eventConfig){e.eventConfig=t.eventConfig}if(t.locale){e.locale=t.locale}n.forEach((t=>e.parseOperator(t)));n.forEach((t=>e.parseOperatorParameters(t)));(t.streams||[]).forEach((t=>e.parseStream(t)));(t.updates||[]).forEach((t=>e.parseUpdate(t)));return e.resolve()}const rU=(0,p.Rg)(["rule"]),oU=(0,p.Rg)(["group","image","rect"]);function aU(t,e){let n="";if(rU[e])return n;if(t.x2){if(t.x){if(oU[e]){n+="if(o.x>o.x2)$=o.x,o.x=o.x2,o.x2=$;"}n+="o.width=o.x2-o.x;"}else{n+="o.x=o.x2-(o.width||0);"}}if(t.xc){n+="o.x=o.xc-(o.width||0)/2;"}if(t.y2){if(t.y){if(oU[e]){n+="if(o.y>o.y2)$=o.y,o.y=o.y2,o.y2=$;"}n+="o.height=o.y2-o.y;"}else{n+="o.y=o.y2-(o.height||0);"}}if(t.yc){n+="o.y=o.yc-(o.height||0)/2;"}return n}function sU(t){return(t+"").toLowerCase()}function lU(t){return sU(t)==="operator"}function uU(t){return sU(t)==="collect"}function fU(t,e,n){if(!n.endsWith(";")){n="return("+n+");"}const i=Function(...e.concat(n));return t&&t.functions?i.bind(t.functions):i}function cU(t,e,n,i){return`((u = ${t}) < (v = ${e}) || u == null) && v != null ? ${n}\n  : (u > v || v == null) && u != null ? ${i}\n  : ((v = v instanceof Date ? +v : v), (u = u instanceof Date ? +u : u)) !== u && v === v ? ${n}\n  : v !== v && u === u ? ${i} : `}var dU={operator:(t,e)=>fU(t,["_"],e.code),parameter:(t,e)=>fU(t,["datum","_"],e.code),event:(t,e)=>fU(t,["event"],e.code),handler:(t,e)=>{const n=`var datum=event.item&&event.item.datum;return ${e.code};`;return fU(t,["_","event"],n)},encode:(t,e)=>{const{marktype:n,channels:i}=e;let r="var o=item,datum=o.datum,m=0,$;";for(const o in i){const t="o["+(0,p.m8)(o)+"]";r+=`$=${i[o].code};if(${t}!==$)${t}=$,m=1;`}r+=aU(i,n);r+="return m;";return fU(t,["item","_"],r)},codegen:{get(t){const e=`[${t.map(p.m8).join("][")}]`;const n=Function("_",`return _${e};`);n.path=e;return n},comparator(t,e){let n;const i=(t,i)=>{const r=e[i];let o,a;if(t.path){o=`a${t.path}`;a=`b${t.path}`}else{(n=n||{})["f"+i]=t;o=`this.f${i}(a)`;a=`this.f${i}(b)`}return cU(o,a,-r,r)};const r=Function("a","b","var u, v; return "+t.map(i).join("")+"0;");return n?r.bind(n):r}}};function hU(t){const e=this;if(lU(t.type)||!t.type){e.operator(t,t.update?e.operatorExpression(t.update):null)}else{e.transform(t,t.type)}}function pU(t){const e=this;if(t.params){const n=e.get(t.id);if(!n)(0,p.vU)("Invalid operator id: "+t.id);e.dataflow.connect(n,n.parameters(e.parseParameters(t.params),t.react,t.initonly))}}function mU(t,e){e=e||{};const n=this;for(const i in t){const r=t[i];e[i]=(0,p.kJ)(r)?r.map((t=>gU(t,n,e))):gU(r,n,e)}return e}function gU(t,e,n){if(!t||!(0,p.Kn)(t))return t;for(let i=0,r=yU.length,o;i<r;++i){o=yU[i];if((0,p.nr)(t,o.key)){return o.parse(t,e,n)}}return t}var yU=[{key:"$ref",parse:vU},{key:"$key",parse:xU},{key:"$expr",parse:bU},{key:"$field",parse:_U},{key:"$encode",parse:kU},{key:"$compare",parse:wU},{key:"$context",parse:MU},{key:"$subflow",parse:EU},{key:"$tupleid",parse:SU}];function vU(t,e){return e.get(t.$ref)||(0,p.vU)("Operator not defined: "+t.$ref)}function bU(t,e,n){if(t.$params){e.parseParameters(t.$params,n)}const i="e:"+t.$expr.code;return e.fn[i]||(e.fn[i]=(0,p.ZE)(e.parameterExpression(t.$expr),t.$fields))}function xU(t,e){const n="k:"+t.$key+"_"+!!t.$flat;return e.fn[n]||(e.fn[n]=(0,p.Jy)(t.$key,t.$flat,e.expr.codegen))}function _U(t,e){if(!t.$field)return null;const n="f:"+t.$field+"_"+t.$name;return e.fn[n]||(e.fn[n]=(0,p.EP)(t.$field,t.$name,e.expr.codegen))}function wU(t,e){const n="c:"+t.$compare+"_"+t.$order,i=(0,p.IX)(t.$compare).map((t=>t&&t.$tupleid?yn:t));return e.fn[n]||(e.fn[n]=(0,p.qu)(i,t.$order,e.expr.codegen))}function kU(t,e){const n=t.$encode,i={};for(const r in n){const t=n[r];i[r]=(0,p.ZE)(e.encodeExpression(t.$expr),t.$fields);i[r].output=t.$output}return i}function MU(t,e){return e}function EU(t,e){const n=t.$subflow;return function(t,i,r){const o=e.fork().parse(n),a=o.get(n.operators[0].id),s=o.signals.parent;if(s)s.set(r);a.detachSubflow=()=>e.detach(o);return a}}function SU(){return yn}function zU(t){var e=this,n=t.filter!=null?e.eventExpression(t.filter):undefined,i=t.stream!=null?e.get(t.stream):undefined,r;if(t.source){i=e.events(t.source,t.type,n)}else if(t.merge){r=t.merge.map((t=>e.get(t)));i=r[0].merge.apply(r[0],r.slice(1))}if(t.between){r=t.between.map((t=>e.get(t)));i=i.between(r[0],r[1])}if(t.filter){i=i.filter(n)}if(t.throttle!=null){i=i.throttle(+t.throttle)}if(t.debounce!=null){i=i.debounce(+t.debounce)}if(i==null){(0,p.vU)("Invalid stream definition: "+JSON.stringify(t))}if(t.consume)i.consume(true);e.stream(t,i)}function DU(t){var e=this,n=(0,p.Kn)(n=t.source)?n.$ref:n,i=e.get(n),r=null,o=t.update,a=undefined;if(!i)(0,p.vU)("Source not defined: "+t.source);r=t.target&&t.target.$expr?e.eventExpression(t.target.$expr):e.get(t.target);if(o&&o.$expr){if(o.$params){a=e.parseParameters(o.$params)}o=e.handlerExpression(o.$expr)}e.update(t,i,r,o,a)}const RU={skip:true};function AU(t){var e=this,n={};if(t.signals){var i=n.signals={};Object.keys(e.signals).forEach((n=>{const r=e.signals[n];if(t.signals(n,r)){i[n]=r.value}}))}if(t.data){var r=n.data={};Object.keys(e.data).forEach((n=>{const i=e.data[n];if(t.data(n,i)){r[n]=i.input.value}}))}if(e.subcontext&&t.recurse!==false){n.subcontext=e.subcontext.map((e=>e.getState(t)))}return n}function $U(t){var e=this,n=e.dataflow,i=t.data,r=t.signals;Object.keys(r||{}).forEach((t=>{n.update(e.signals[t],r[t],RU)}));Object.keys(i||{}).forEach((t=>{n.pulse(e.data[t].input,n.changeset().remove(p.yb).insert(i[t]))}));(t.subcontext||[]).forEach(((t,n)=>{const i=e.subcontext[n];if(i)i.setState(t)}))}function OU(t,e,n,i){return new TU(t,e,n,i)}function TU(t,e,n,i){this.dataflow=t;this.transforms=e;this.events=t.events.bind(t);this.expr=i||dU,this.signals={};this.scales={};this.nodes={};this.data={};this.fn={};if(n){this.functions=Object.create(n);this.functions.context=this}}function CU(t){this.dataflow=t.dataflow;this.transforms=t.transforms;this.events=t.events;this.expr=t.expr;this.signals=Object.create(t.signals);this.scales=Object.create(t.scales);this.nodes=Object.create(t.nodes);this.data=Object.create(t.data);this.fn=Object.create(t.fn);if(t.functions){this.functions=Object.create(t.functions);this.functions.context=this}}TU.prototype=CU.prototype={fork(){const t=new CU(this);(this.subcontext||(this.subcontext=[])).push(t);return t},detach(t){this.subcontext=this.subcontext.filter((e=>e!==t));const e=Object.keys(t.nodes);for(const n of e)t.nodes[n]._targets=null;for(const n of e)t.nodes[n].detach();t.nodes=null},get(t){return this.nodes[t]},set(t,e){return this.nodes[t]=e},add(t,e){const n=this,i=n.dataflow,r=t.value;n.set(t.id,e);if(uU(t.type)&&r){if(r.$ingest){i.ingest(e,r.$ingest,r.$format)}else if(r.$request){i.preload(e,r.$request,r.$format)}else{i.pulse(e,i.changeset().insert(r))}}if(t.root){n.root=e}if(t.parent){let r=n.get(t.parent.$ref);if(r){i.connect(r,[e]);e.targets().add(r)}else{(n.unresolved=n.unresolved||[]).push((()=>{r=n.get(t.parent.$ref);i.connect(r,[e]);e.targets().add(r)}))}}if(t.signal){n.signals[t.signal]=e}if(t.scale){n.scales[t.scale]=e}if(t.data){for(const i in t.data){const r=n.data[i]||(n.data[i]={});t.data[i].forEach((t=>r[t]=e))}}},resolve(){(this.unresolved||[]).forEach((t=>t()));delete this.unresolved;return this},operator(t,e){this.add(t,this.dataflow.add(t.value,e))},transform(t,e){this.add(t,this.dataflow.add(this.transforms[sU(e)]))},stream(t,e){this.set(t.id,e)},update(t,e,n,i,r){this.dataflow.on(e,n,i,r,t.options)},operatorExpression(t){return this.expr.operator(this,t)},parameterExpression(t){return this.expr.parameter(this,t)},eventExpression(t){return this.expr.event(this,t)},handlerExpression(t){return this.expr.handler(this,t)},encodeExpression(t){return this.expr.encode(this,t)},parse:iU,parseOperator:hU,parseOperatorParameters:pU,parseParameters:mU,parseStream:zU,parseUpdate:DU,getState:AU,setState:$U};function NU(t,e,n){var i=new AE.B7,r=e;if(e==null)return i.restart(t,e,n),i;i._restart=i.restart;i.restart=function(t,e,n){e=+e,n=n==null?(0,AE.zO)():+n;i._restart((function o(a){a+=r;i._restart(o,r+=e,n);t(a)}),e,n)};i.restart(t,e,n);return i}function UU(t){const e=t.container();if(e){e.setAttribute("role","graphics-document");e.setAttribute("aria-roleDescription","visualization");IU(e,t.description())}}function IU(t,e){if(t)e==null?t.removeAttribute("aria-label"):t.setAttribute("aria-label",e)}function PU(t){t.add(null,(e=>{t._background=e.bg;t._resize=1;return e.bg}),{bg:t._signals.background})}const qU="default";function LU(t){const e=t._signals.cursor||(t._signals.cursor=t.add({user:qU,item:null}));t.on(t.events("view","mousemove"),e,((t,n)=>{const i=e.value,r=i?(0,p.HD)(i)?i:i.user:qU,o=n.item&&n.item.cursor||null;return i&&r===i.user&&o==i.item?i:{user:r,item:o}}));t.add(null,(function(e){let n=e.cursor,i=this.value;if(!(0,p.HD)(n)){i=n.item;n=n.user}jU(t,n&&n!==qU?n:i||n);return i}),{cursor:e})}function jU(t,e){const n=t.globalCursor()?typeof document!=="undefined"&&document.body:t.container();if(n){return e==null?n.style.removeProperty("cursor"):n.style.cursor=e}}function FU(t,e){var n=t._runtime.data;if(!(0,p.nr)(n,e)){(0,p.vU)("Unrecognized data set: "+e)}return n[e]}function WU(t,e){return arguments.length<2?FU(this,t).values.value:XU.call(this,t,En().remove(p.yb).insert(e))}function XU(t,e){if(!Mn(e)){(0,p.vU)("Second argument to changes must be a changeset.")}const n=FU(this,t);n.modified=true;return this.pulse(n.input,e)}function BU(t,e){return XU.call(this,t,En().insert(e))}function ZU(t,e){return XU.call(this,t,En().remove(e))}function HU(t){var e=t.padding();return Math.max(0,t._viewWidth+e.left+e.right)}function JU(t){var e=t.padding();return Math.max(0,t._viewHeight+e.top+e.bottom)}function GU(t){var e=t.padding(),n=t._origin;return[e.left+n[0],e.top+n[1]]}function KU(t){var e=GU(t),n=HU(t),i=JU(t);t._renderer.background(t.background());t._renderer.resize(n,i,e);t._handler.origin(e);t._resizeListeners.forEach((e=>{try{e(n,i)}catch(r){t.error(r)}}))}function VU(t,e,n){var i=t._renderer,r=i&&i.canvas(),o,a,s;if(r){s=GU(t);a=e.changedTouches?e.changedTouches[0]:e;o=Qp(a,r);o[0]-=s[0];o[1]-=s[1]}e.dataflow=t;e.item=n;e.vega=YU(t,n,o);return e}function YU(t,e,n){const i=e?e.mark.marktype==="group"?e:e.mark.group:null;function r(t){var n=i,r;if(t)for(r=e;r;r=r.mark.group){if(r.mark.name===t){n=r;break}}return n&&n.mark&&n.mark.interactive?n:{}}function o(t){if(!t)return n;if((0,p.HD)(t))t=r(t);const e=n.slice();while(t){e[0]-=t.x||0;e[1]-=t.y||0;t=t.mark&&t.mark.group}return e}return{view:(0,p.a9)(t),item:(0,p.a9)(e||{}),group:r,xy:o,x:t=>o(t)[0],y:t=>o(t)[1]}}const QU="view",tI="timer",eI="window",nI={trap:false};function iI(t){const e=(0,p.l7)({defaults:{}},t);const n=(t,e)=>{e.forEach((e=>{if((0,p.kJ)(t[e]))t[e]=(0,p.Rg)(t[e])}))};n(e.defaults,["prevent","allow"]);n(e,["view","window","selector"]);return e}function rI(t,e,n,i){t._eventListeners.push({type:n,sources:(0,p.IX)(e),handler:i})}function oI(t,e){var n=t._eventConfig.defaults,i=n.prevent,r=n.allow;return i===false||r===true?false:i===true||r===false?true:i?i[e]:r?!r[e]:t.preventDefault()}function aI(t,e,n){const i=t._eventConfig&&t._eventConfig[e];if(i===false||(0,p.Kn)(i)&&!i[n]){t.warn(`Blocked ${e} ${n} event listener.`);return false}return true}function sI(t,e,n){var i=this,r=new Pn(n),o=function(n,o){i.runAsync(null,(()=>{if(t===QU&&oI(i,e)){n.preventDefault()}r.receive(VU(i,n,o))}))},a;if(t===tI){if(aI(i,"timer",e)){i.timer(o,e)}}else if(t===QU){if(aI(i,"view",e)){i.addEventListener(e,o,nI)}}else{if(t===eI){if(aI(i,"window",e)&&typeof window!=="undefined"){a=[window]}}else if(typeof document!=="undefined"){if(aI(i,"selector",e)){a=Array.from(document.querySelectorAll(t))}}if(!a){i.warn("Can not resolve event source: "+t)}else{for(var s=0,l=a.length;s<l;++s){a[s].addEventListener(e,o)}rI(i,a,e,o)}}return r}function lI(t){return t.item}function uI(t){return t.item.mark.source}function fI(t){return function(e,n){return n.vega.view().changeset().encode(n.item,t)}}function cI(t,e){t=[t||"hover"];e=[e||"update",t[0]];this.on(this.events("view","mouseover",lI),uI,fI(t));this.on(this.events("view","mouseout",lI),uI,fI(e));return this}function dI(){var t=this._tooltip,e=this._timers,n=this._eventListeners,i,r,o;i=e.length;while(--i>=0){e[i].stop()}i=n.length;while(--i>=0){o=n[i];r=o.sources.length;while(--r>=0){o.sources[r].removeEventListener(o.type,o.handler)}}if(t){t.call(this,this._handler,null,null,null)}return this}function hI(t,e,n){const i=document.createElement(t);for(const r in e)i.setAttribute(r,e[r]);if(n!=null)i.textContent=n;return i}const pI="vega-bind",mI="vega-bind-name",gI="vega-bind-radio";function yI(t,e,n){if(!e)return;const i=n.param;let r=n.state;if(!r){r=n.state={elements:null,active:false,set:null,update:e=>{if(e!=t.signal(i.signal)){t.runAsync(null,(()=>{r.source=true;t.signal(i.signal,e)}))}}};if(i.debounce){r.update=(0,p.Ds)(i.debounce,r.update)}}const o=i.input==null&&i.element?vI:xI;o(r,e,i,t);if(!r.active){t.on(t._signals[i.signal],null,(()=>{r.source?r.source=false:r.set(t.signal(i.signal))}));r.active=true}return r}function vI(t,e,n,i){const r=n.event||"input";const o=()=>t.update(e.value);i.signal(n.signal,e.value);e.addEventListener(r,o);rI(i,e,r,o);t.set=t=>{e.value=t;e.dispatchEvent(bI(r))}}function bI(t){return typeof Event!=="undefined"?new Event(t):{type:t}}function xI(t,e,n,i){const r=i.signal(n.signal);const o=hI("div",{class:pI});const a=n.input==="radio"?o:o.appendChild(hI("label"));a.appendChild(hI("span",{class:mI},n.name||n.signal));e.appendChild(o);let s=_I;switch(n.input){case"checkbox":s=wI;break;case"select":s=kI;break;case"radio":s=MI;break;case"range":s=EI;break}s(t,a,n,r)}function _I(t,e,n,i){const r=hI("input");for(const o in n){if(o!=="signal"&&o!=="element"){r.setAttribute(o==="input"?"type":o,n[o])}}r.setAttribute("name",n.signal);r.value=i;e.appendChild(r);r.addEventListener("input",(()=>t.update(r.value)));t.elements=[r];t.set=t=>r.value=t}function wI(t,e,n,i){const r={type:"checkbox",name:n.signal};if(i)r.checked=true;const o=hI("input",r);e.appendChild(o);o.addEventListener("change",(()=>t.update(o.checked)));t.elements=[o];t.set=t=>o.checked=!!t||null}function kI(t,e,n,i){const r=hI("select",{name:n.signal}),o=n.labels||[];n.options.forEach(((t,e)=>{const n={value:t};if(SI(t,i))n.selected=true;r.appendChild(hI("option",n,(o[e]||t)+""))}));e.appendChild(r);r.addEventListener("change",(()=>{t.update(n.options[r.selectedIndex])}));t.elements=[r];t.set=t=>{for(let e=0,i=n.options.length;e<i;++e){if(SI(n.options[e],t)){r.selectedIndex=e;return}}}}function MI(t,e,n,i){const r=hI("span",{class:gI}),o=n.labels||[];e.appendChild(r);t.elements=n.options.map(((e,a)=>{const s={type:"radio",name:n.signal,value:e};if(SI(e,i))s.checked=true;const l=hI("input",s);l.addEventListener("change",(()=>t.update(e)));const u=hI("label",{},(o[a]||e)+"");u.prepend(l);r.appendChild(u);return l}));t.set=e=>{const n=t.elements,i=n.length;for(let t=0;t<i;++t){if(SI(n[t].value,e))n[t].checked=true}}}function EI(t,e,n,i){i=i!==undefined?i:(+n.max+ +n.min)/2;const r=n.max!=null?n.max:Math.max(100,+i)||100,o=n.min||Math.min(0,r,+i)||0,a=n.step||(0,I.ly)(o,r,100);const s=hI("input",{type:"range",name:n.signal,min:o,max:r,step:a});s.value=i;const l=hI("span",{},+i);e.appendChild(s);e.appendChild(l);const u=()=>{l.textContent=s.value;t.update(+s.value)};s.addEventListener("input",u);s.addEventListener("change",u);t.elements=[s];t.set=t=>{s.value=t;l.textContent=t}}function SI(t,e){return t===e||t+""===e+""}function zI(t,e,n,i,r,o){e=e||new i(t.loader());return e.initialize(n,HU(t),JU(t),GU(t),r,o).background(t.background())}function DI(t,e){return!e?null:function(){try{e.apply(this,arguments)}catch(n){t.error(n)}}}function RI(t,e,n,i){const r=new i(t.loader(),DI(t,t.tooltip())).scene(t.scenegraph().root).initialize(n,GU(t),t);if(e){e.handlers().forEach((t=>{r.on(t.type,t.handler)}))}return r}function AI(t,e){const n=this,i=n._renderType,r=n._eventConfig.bind,o=Wg(i);t=n._el=t?$I(n,t,true):null;UU(n);if(!o)n.error("Unrecognized renderer type: "+i);const a=o.handler||Sm,s=t?o.renderer:o.headless;n._renderer=!s?null:zI(n,n._renderer,t,s);n._handler=RI(n,n._handler,t,a);n._redraw=true;if(t&&r!=="none"){e=e?n._elBind=$I(n,e,true):t.appendChild(hI("form",{class:"vega-bindings"}));n._bind.forEach((t=>{if(t.param.element&&r!=="container"){t.element=$I(n,t.param.element,!!t.param.input)}}));n._bind.forEach((t=>{yI(n,t.element||e,t)}))}return n}function $I(t,e,n){if(typeof e==="string"){if(typeof document!=="undefined"){e=document.querySelector(e);if(!e){t.error("Signal bind element not found: "+e);return null}}else{t.error("DOM document instance not found.");return null}}if(e&&n){try{e.textContent=""}catch(i){e=null;t.error(i)}}return e}const OI=t=>+t||0;const TI=t=>({top:t,bottom:t,left:t,right:t});function CI(t){return(0,p.Kn)(t)?{top:OI(t.top),bottom:OI(t.bottom),left:OI(t.left),right:OI(t.right)}:TI(OI(t))}async function NI(t,e,n,i){const r=Wg(e),o=r&&r.headless;if(!o)(0,p.vU)("Unrecognized renderer type: "+e);await t.runAsync();return zI(t,null,null,o,n,i).renderAsync(t._scenegraph.root)}async function UI(t,e){if(t!==jg.Canvas&&t!==jg.SVG&&t!==jg.PNG){(0,p.vU)("Unrecognized image type: "+t)}const n=await NI(this,t,e);return t===jg.SVG?II(n.svg(),"image/svg+xml"):n.canvas().toDataURL("image/png")}function II(t,e){const n=new Blob([t],{type:e});return window.URL.createObjectURL(n)}async function PI(t,e){const n=await NI(this,jg.Canvas,t,e);return n.canvas()}async function qI(t){const e=await NI(this,jg.SVG,t);return e.svg()}function LI(t,e,n){return OU(t,Ti,HN,n).parse(e)}function jI(t){var e=this._runtime.scales;if(!(0,p.nr)(e,t)){(0,p.vU)("Unrecognized scale or projection: "+t)}return e[t].value}var FI="width",WI="height",XI="padding",BI={skip:true};function ZI(t,e){var n=t.autosize(),i=t.padding();return e-(n&&n.contains===XI?i.left+i.right:0)}function HI(t,e){var n=t.autosize(),i=t.padding();return e-(n&&n.contains===XI?i.top+i.bottom:0)}function JI(t){var e=t._signals,n=e[FI],i=e[WI],r=e[XI];function o(){t._autosize=t._resize=1}t._resizeWidth=t.add(null,(e=>{t._width=e.size;t._viewWidth=ZI(t,e.size);o()}),{size:n});t._resizeHeight=t.add(null,(e=>{t._height=e.size;t._viewHeight=HI(t,e.size);o()}),{size:i});const a=t.add(null,o,{pad:r});t._resizeWidth.rank=n.rank+1;t._resizeHeight.rank=i.rank+1;a.rank=r.rank+1}function GI(t,e,n,i,r,o){this.runAfter((a=>{let s=0;a._autosize=0;if(a.width()!==n){s=1;a.signal(FI,n,BI);a._resizeWidth.skip(true)}if(a.height()!==i){s=1;a.signal(WI,i,BI);a._resizeHeight.skip(true)}if(a._viewWidth!==t){a._resize=1;a._viewWidth=t}if(a._viewHeight!==e){a._resize=1;a._viewHeight=e}if(a._origin[0]!==r[0]||a._origin[1]!==r[1]){a._resize=1;a._origin=r}if(s)a.run("enter");if(o)a.runAfter((t=>t.resize()))}),false,1)}function KI(t){return this._runtime.getState(t||{data:VI,signals:YI,recurse:true})}function VI(t,e){return e.modified&&(0,p.kJ)(e.input.value)&&t.indexOf("_:vega:_")}function YI(t,e){return!(t==="parent"||e instanceof Ti.proxy)}function QI(t){this.runAsync(null,(e=>{e._trigger=false;e._runtime.setState(t)}),(t=>{t._trigger=true}));return this}function tP(t,e){function n(e){t({timestamp:Date.now(),elapsed:e})}this._timers.push(NU(n,e))}function eP(t,e,n,i){const r=t.element();if(r)r.setAttribute("title",nP(i))}function nP(t){return t==null?"":(0,p.kJ)(t)?rP(t):(0,p.Kn)(t)&&!(0,p.J_)(t)?iP(t):t+""}function iP(t){return Object.keys(t).map((e=>{const n=t[e];return e+": "+((0,p.kJ)(n)?rP(n):oP(n))})).join("\n")}function rP(t){return"["+t.map(oP).join(", ")+"]"}function oP(t){return(0,p.kJ)(t)?"[…]":(0,p.Kn)(t)&&!(0,p.J_)(t)?"{…}":t}function aP(t,e){const n=this;e=e||{};Ai.call(n);if(e.loader)n.loader(e.loader);if(e.logger)n.logger(e.logger);if(e.logLevel!=null)n.logLevel(e.logLevel);if(e.locale||t.locale){const i=(0,p.l7)({},t.locale,e.locale);n.locale(De(i.number,i.time))}n._el=null;n._elBind=null;n._renderType=e.renderer||jg.Canvas;n._scenegraph=new Zp;const i=n._scenegraph.root;n._renderer=null;n._tooltip=e.tooltip||eP,n._redraw=true;n._handler=(new Sm).scene(i);n._globalCursor=false;n._preventDefault=false;n._timers=[];n._eventListeners=[];n._resizeListeners=[];n._eventConfig=iI(t.eventConfig);n.globalCursor(n._eventConfig.globalCursor);const r=LI(n,t,e.expr);n._runtime=r;n._signals=r.signals;n._bind=(t.bindings||[]).map((t=>({state:null,param:(0,p.l7)({},t)})));if(r.root)r.root.set(i);i.source=r.data.root.input;n.pulse(r.data.root.input,n.changeset().insert(i.items));n._width=n.width();n._height=n.height();n._viewWidth=ZI(n,n._width);n._viewHeight=HI(n,n._height);n._origin=[0,0];n._resize=0;n._autosize=1;JI(n);PU(n);LU(n);n.description(t.description);if(e.hover)n.hover();if(e.container)n.initialize(e.container,e.bind)}function sP(t,e){return(0,p.nr)(t._signals,e)?t._signals[e]:(0,p.vU)("Unrecognized signal name: "+(0,p.m8)(e))}function lP(t,e){const n=(t._targets||[]).filter((t=>t._update&&t._update.handler===e));return n.length?n[0]:null}function uP(t,e,n,i){let r=lP(n,i);if(!r){r=DI(t,(()=>i(e,n.value)));r.handler=i;t.on(n,null,r)}return t}function fP(t,e,n){const i=lP(e,n);if(i)e._targets.remove(i);return t}(0,p.XW)(aP,Ai,{async evaluate(t,e,n){await Ai.prototype.evaluate.call(this,t,e);if(this._redraw||this._resize){try{if(this._renderer){if(this._resize){this._resize=0;KU(this)}await this._renderer.renderAsync(this._scenegraph.root)}this._redraw=false}catch(i){this.error(i)}}if(n)hn(this,n);return this},dirty(t){this._redraw=true;this._renderer&&this._renderer.dirty(t)},description(t){if(arguments.length){const e=t!=null?t+"":null;if(e!==this._desc)IU(this._el,this._desc=e);return this}return this._desc},container(){return this._el},scenegraph(){return this._scenegraph},origin(){return this._origin.slice()},signal(t,e,n){const i=sP(this,t);return arguments.length===1?i.value:this.update(i,e,n)},width(t){return arguments.length?this.signal("width",t):this.signal("width")},height(t){return arguments.length?this.signal("height",t):this.signal("height")},padding(t){return arguments.length?this.signal("padding",CI(t)):CI(this.signal("padding"))},autosize(t){return arguments.length?this.signal("autosize",t):this.signal("autosize")},background(t){return arguments.length?this.signal("background",t):this.signal("background")},renderer(t){if(!arguments.length)return this._renderType;if(!Wg(t))(0,p.vU)("Unrecognized renderer type: "+t);if(t!==this._renderType){this._renderType=t;this._resetRenderer()}return this},tooltip(t){if(!arguments.length)return this._tooltip;if(t!==this._tooltip){this._tooltip=t;this._resetRenderer()}return this},loader(t){if(!arguments.length)return this._loader;if(t!==this._loader){Ai.prototype.loader.call(this,t);this._resetRenderer()}return this},resize(){this._autosize=1;return this.touch(sP(this,"autosize"))},_resetRenderer(){if(this._renderer){this._renderer=null;this.initialize(this._el,this._elBind)}},_resizeView:GI,addEventListener(t,e,n){let i=e;if(!(n&&n.trap===false)){i=DI(this,e);i.raw=e}this._handler.on(t,i);return this},removeEventListener(t,e){var n=this._handler.handlers(t),i=n.length,r,o;while(--i>=0){o=n[i].type;r=n[i].handler;if(t===o&&(e===r||e===r.raw)){this._handler.off(o,r);break}}return this},addResizeListener(t){const e=this._resizeListeners;if(e.indexOf(t)<0){e.push(t)}return this},removeResizeListener(t){var e=this._resizeListeners,n=e.indexOf(t);if(n>=0){e.splice(n,1)}return this},addSignalListener(t,e){return uP(this,t,sP(this,t),e)},removeSignalListener(t,e){return fP(this,sP(this,t),e)},addDataListener(t,e){return uP(this,t,FU(this,t).values,e)},removeDataListener(t,e){return fP(this,FU(this,t).values,e)},globalCursor(t){if(arguments.length){if(this._globalCursor!==!!t){const e=jU(this,null);this._globalCursor=!!t;if(e)jU(this,e)}return this}else{return this._globalCursor}},preventDefault(t){if(arguments.length){this._preventDefault=t;return this}else{return this._preventDefault}},timer:tP,events:sI,finalize:dI,hover:cI,data:WU,change:XU,insert:BU,remove:ZU,scale:jI,initialize:AI,toImageURL:UI,toCanvas:PI,toSVG:qI,getState:KI,setState:QI});var cP=n(83082);function dP(t){return(0,p.Kn)(t)?t:{type:t||"pad"}}const hP=t=>+t||0;const pP=t=>({top:t,bottom:t,left:t,right:t});function mP(t){return!(0,p.Kn)(t)?pP(hP(t)):t.signal?t:{top:hP(t.top),bottom:hP(t.bottom),left:hP(t.left),right:hP(t.right)}}const gP=t=>(0,p.Kn)(t)&&!(0,p.kJ)(t)?(0,p.l7)({},t):{value:t};function yP(t,e,n,i){if(n!=null){const r=(0,p.Kn)(n)&&!(0,p.kJ)(n)||(0,p.kJ)(n)&&n.length&&(0,p.Kn)(n[0]);if(r){t.update[e]=n}else{t[i||"enter"][e]={value:n}}return 1}else{return 0}}function vP(t,e,n){for(const i in e){yP(t,i,e[i])}for(const i in n){yP(t,i,n[i],"update")}}function bP(t,e,n){for(const i in e){if(n&&(0,p.nr)(n,i))continue;t[i]=(0,p.l7)(t[i]||{},e[i])}return t}function xP(t,e){return e&&(e.enter&&e.enter[t]||e.update&&e.update[t])}const _P="mark";const wP="frame";const kP="scope";const MP="axis";const EP="axis-domain";const SP="axis-grid";const zP="axis-label";const DP="axis-tick";const RP="axis-title";const AP="legend";const $P="legend-band";const OP="legend-entry";const TP="legend-gradient";const CP="legend-label";const NP="legend-symbol";const UP="legend-title";const IP="title";const PP="title-text";const qP="title-subtitle";function LP(t,e,n,i,r){const o={},a={};let s,l,u,f;l="lineBreak";if(e==="text"&&r[l]!=null&&!xP(l,t)){jP(o,l,r[l])}if(n=="legend"||String(n).startsWith("axis")){n=null}f=n===wP?r.group:n===_P?(0,p.l7)({},r.mark,r[e]):null;for(l in f){u=xP(l,t)||(l==="fill"||l==="stroke")&&(xP("fill",t)||xP("stroke",t));if(!u)jP(o,l,f[l])}(0,p.IX)(i).forEach((e=>{const n=r.style&&r.style[e];for(const i in n){if(!xP(i,t)){jP(o,i,n[i])}}}));t=(0,p.l7)({},t);for(l in o){f=o[l];if(f.signal){(s=s||{})[l]=f}else{a[l]=f}}t.enter=(0,p.l7)(a,t.enter);if(s)t.update=(0,p.l7)(s,t.update);return t}function jP(t,e,n){t[e]=n&&n.signal?{signal:n.signal}:{value:n}}const FP=t=>(0,p.HD)(t)?(0,p.m8)(t):t.signal?`(${t.signal})`:JP(t);function WP(t){if(t.gradient!=null){return ZP(t)}let e=t.signal?`(${t.signal})`:t.color?BP(t.color):t.field!=null?JP(t.field):t.value!==undefined?(0,p.m8)(t.value):undefined;if(t.scale!=null){e=KP(t,e)}if(e===undefined){e=null}if(t.exponent!=null){e=`pow(${e},${HP(t.exponent)})`}if(t.mult!=null){e+=`*${HP(t.mult)}`}if(t.offset!=null){e+=`+${HP(t.offset)}`}if(t.round){e=`round(${e})`}return e}const XP=(t,e,n,i)=>`(${t}(${[e,n,i].map(WP).join(",")})+'')`;function BP(t){return t.c?XP("hcl",t.h,t.c,t.l):t.h||t.s?XP("hsl",t.h,t.s,t.l):t.l||t.a?XP("lab",t.l,t.a,t.b):t.r||t.g||t.b?XP("rgb",t.r,t.g,t.b):null}function ZP(t){const e=[t.start,t.stop,t.count].map((t=>t==null?null:(0,p.m8)(t)));while(e.length&&(0,p.fj)(e)==null)e.pop();e.unshift(FP(t.gradient));return`gradient(${e.join(",")})`}function HP(t){return(0,p.Kn)(t)?"("+WP(t)+")":t}function JP(t){return GP((0,p.Kn)(t)?t:{datum:t})}function GP(t){let e,n,i;if(t.signal){e="datum";i=t.signal}else if(t.group||t.parent){n=Math.max(1,t.level||1);e="item";while(n-- >0){e+=".mark.group"}if(t.parent){i=t.parent;e+=".datum"}else{i=t.group}}else if(t.datum){e="datum";i=t.datum}else{(0,p.vU)("Invalid field reference: "+(0,p.m8)(t))}if(!t.signal){i=(0,p.HD)(i)?(0,p._k)(i).map(p.m8).join("]["):GP(i)}return e+"["+i+"]"}function KP(t,e){const n=FP(t.scale);if(t.range!=null){e=`lerp(_range(${n}), ${+t.range})`}else{if(e!==undefined)e=`_scale(${n}, ${e})`;if(t.band){e=(e?e+"+":"")+`_bandwidth(${n})`+(+t.band===1?"":"*"+HP(t.band));if(t.extra){e=`(datum.extra ? _scale(${n}, datum.extra.value) : ${e})`}}if(e==null)e="0"}return e}function VP(t){let e="";t.forEach((t=>{const n=WP(t);e+=t.test?`(${t.test})?${n}:`:n}));if((0,p.fj)(e)===":"){e+="null"}return e}function YP(t,e,n,i,r,o){const a={};o=o||{};o.encoders={$encode:a};t=LP(t,e,n,i,r.config);for(const s in t){a[s]=QP(t[s],e,o,r)}return o}function QP(t,e,n,i){const r={},o={};for(const a in t){if(t[a]!=null){r[a]=eq(tq(t[a]),i,n,o)}}return{$expr:{marktype:e,channels:r},$fields:Object.keys(o),$output:Object.keys(t)}}function tq(t){return(0,p.kJ)(t)?VP(t):WP(t)}function eq(t,e,n,i){const r=nU(t,e);r.$fields.forEach((t=>i[t]=1));(0,p.l7)(n,r.$params);return r.$expr}const nq="outer",iq=["value","update","init","react","bind"];function rq(t,e){(0,p.vU)(t+' for "outer" push: '+(0,p.m8)(e))}function oq(t,e){const n=t.name;if(t.push===nq){if(!e.signals[n])rq("No prior signal definition",n);iq.forEach((e=>{if(t[e]!==undefined)rq("Invalid property ",e)}))}else{const i=e.addSignal(n,t.value);if(t.react===false)i.react=false;if(t.bind)e.addBinding(n,t.bind)}}function aq(t,e,n,i){this.id=-1;this.type=t;this.value=e;this.params=n;if(i)this.parent=i}function sq(t,e,n,i){return new aq(t,e,n,i)}function lq(t,e){return sq("operator",t,e)}function uq(t){const e={$ref:t.id};if(t.id<0)(t.refs=t.refs||[]).push(e);return e}function fq(t,e){return e?{$field:t,$name:e}:{$field:t}}const cq=fq("key");function dq(t,e){return{$compare:t,$order:e}}function hq(t,e){const n={$key:t};if(e)n.$flat=true;return n}const pq="ascending";const mq="descending";function gq(t){return!(0,p.Kn)(t)?"":(t.order===mq?"-":"+")+yq(t.op,t.field)}function yq(t,e){return(t&&t.signal?"$"+t.signal:t||"")+(t&&e?"_":"")+(e&&e.signal?"$"+e.signal:e||"")}const vq="scope";const bq="view";function xq(t){return t&&t.signal}function _q(t){return t&&t.expr}function wq(t){if(xq(t))return true;if((0,p.Kn)(t))for(const e in t){if(wq(t[e]))return true}return false}function kq(t,e){return t!=null?t:e}function Mq(t){return t&&t.signal||t}const Eq="timer";function Sq(t,e){const n=t.merge?Dq:t.stream?Rq:t.type?Aq:(0,p.vU)("Invalid stream specification: "+(0,p.m8)(t));return n(t,e)}function zq(t){return t===vq?bq:t||bq}function Dq(t,e){const n=t.merge.map((t=>Sq(t,e))),i=$q({merge:n},t,e);return e.addStream(i).id}function Rq(t,e){const n=Sq(t.stream,e),i=$q({stream:n},t,e);return e.addStream(i).id}function Aq(t,e){let n;if(t.type===Eq){n=e.event(Eq,t.throttle);t={between:t.between,filter:t.filter}}else{n=e.event(zq(t.source),t.type)}const i=$q({stream:n},t,e);return Object.keys(i).length===1?n:e.addStream(i).id}function $q(t,e,n){let i=e.between;if(i){if(i.length!==2){(0,p.vU)('Stream "between" parameter must have 2 entries: '+(0,p.m8)(e))}t.between=[Sq(i[0],n),Sq(i[1],n)]}i=e.filter?[].concat(e.filter):[];if(e.marktype||e.markname||e.markrole){i.push(Oq(e.marktype,e.markname,e.markrole))}if(e.source===vq){i.push("inScope(event.item)")}if(i.length){t.filter=nU("("+i.join(")&&(")+")",n).$expr}if((i=e.throttle)!=null){t.throttle=+i}if((i=e.debounce)!=null){t.debounce=+i}if(e.consume){t.consume=true}return t}function Oq(t,e,n){const i="event.item";return i+(t&&t!=="*"?"&&"+i+".mark.marktype==='"+t+"'":"")+(n?"&&"+i+".mark.role==='"+n+"'":"")+(e?"&&"+i+".mark.name==='"+e+"'":"")}const Tq={code:"_.$value",ast:{type:"Identifier",value:"value"}};function Cq(t,e,n){const i=t.encode,r={target:n};let o=t.events,a=t.update,s=[];if(!o){(0,p.vU)("Signal update missing events specification.")}if((0,p.HD)(o)){o=(0,cP.r)(o,e.isSubscope()?vq:bq)}o=(0,p.IX)(o).filter((t=>t.signal||t.scale?(s.push(t),0):1));if(s.length>1){s=[Uq(s)]}if(o.length){s.push(o.length>1?{merge:o}:o[0])}if(i!=null){if(a)(0,p.vU)("Signal encode and update are mutually exclusive.");a="encode(item(),"+(0,p.m8)(i)+")"}r.update=(0,p.HD)(a)?nU(a,e):a.expr!=null?nU(a.expr,e):a.value!=null?a.value:a.signal!=null?{$expr:Tq,$params:{$value:e.signalRef(a.signal)}}:(0,p.vU)("Invalid signal update specification.");if(t.force){r.options={force:true}}s.forEach((t=>e.addUpdate((0,p.l7)(Nq(t,e),r))))}function Nq(t,e){return{source:t.signal?e.signalRef(t.signal):t.scale?e.scaleRef(t.scale):Sq(t,e)}}function Uq(t){return{signal:"["+t.map((t=>t.scale?'scale("'+t.scale+'")':t.signal))+"]"}}function Iq(t,e){const n=e.getSignal(t.name);let i=t.update;if(t.init){if(i){(0,p.vU)("Signals can not include both init and update expressions.")}else{i=t.init;n.initonly=true}}if(i){i=nU(i,e);n.update=i.$expr;n.params=i.$params}if(t.on){t.on.forEach((t=>Cq(t,e,n.id)))}}const Pq=t=>(e,n,i)=>sq(t,n,e||undefined,i);const qq=Pq("aggregate");const Lq=Pq("axisticks");const jq=Pq("bound");const Fq=Pq("collect");const Wq=Pq("compare");const Xq=Pq("datajoin");const Bq=Pq("encode");const Zq=Pq("expression");const Hq=Pq("facet");const Jq=Pq("field");const Gq=Pq("key");const Kq=Pq("legendentries");const Vq=Pq("load");const Yq=Pq("mark");const Qq=Pq("multiextent");const tL=Pq("multivalues");const eL=Pq("overlap");const nL=Pq("params");const iL=Pq("prefacet");const rL=Pq("projection");const oL=Pq("proxy");const aL=Pq("relay");const sL=Pq("render");const lL=Pq("scale");const uL=Pq("sieve");const fL=Pq("sortitems");const cL=Pq("viewlayout");const dL=Pq("values");let hL=0;const pL={min:"min",max:"max",count:"sum"};function mL(t,e){const n=t.type||"linear";if(!Cu(n)){(0,p.vU)("Unrecognized scale type: "+(0,p.m8)(n))}e.addScale(t.name,{type:n,domain:undefined})}function gL(t,e){const n=e.getScale(t.name).params;let i;n.domain=xL(t.domain,t,e);if(t.range!=null){n.range=OL(t,e,n)}if(t.interpolate!=null){$L(t.interpolate,n)}if(t.nice!=null){n.nice=AL(t.nice)}if(t.bins!=null){n.bins=RL(t.bins,e)}for(i in t){if((0,p.nr)(n,i)||i==="name")continue;n[i]=yL(t[i],e)}}function yL(t,e){return!(0,p.Kn)(t)?t:t.signal?e.signalRef(t.signal):(0,p.vU)("Unsupported object: "+(0,p.m8)(t))}function vL(t,e){return t.signal?e.signalRef(t.signal):t.map((t=>yL(t,e)))}function bL(t){(0,p.vU)("Can not find data set: "+(0,p.m8)(t))}function xL(t,e,n){if(!t){if(e.domainMin!=null||e.domainMax!=null){(0,p.vU)("No scale domain defined for domainMin/domainMax to override.")}return}return t.signal?n.signalRef(t.signal):((0,p.kJ)(t)?_L:t.fields?kL:wL)(t,e,n)}function _L(t,e,n){return t.map((t=>yL(t,n)))}function wL(t,e,n){const i=n.getData(t.data);if(!i)bL(t.data);return Iu(e.type)?i.valuesRef(n,t.field,SL(t.sort,false)):Fu(e.type)?i.domainRef(n,t.field):i.extentRef(n,t.field)}function kL(t,e,n){const i=t.data,r=t.fields.reduce(((t,e)=>{e=(0,p.HD)(e)?{data:i,field:e}:(0,p.kJ)(e)||e.signal?ML(e,n):e;t.push(e);return t}),[]);return(Iu(e.type)?EL:Fu(e.type)?zL:DL)(t,n,r)}function ML(t,e){const n="_:vega:_"+hL++,i=Fq({});if((0,p.kJ)(t)){i.value={$ingest:t}}else if(t.signal){const r="setdata("+(0,p.m8)(n)+","+t.signal+")";i.params.input=e.signalRef(r)}e.addDataPipeline(n,[i,uL({})]);return{data:n,field:"data"}}function EL(t,e,n){const i=SL(t.sort,true);let r,o;const a=n.map((t=>{const n=e.getData(t.data);if(!n)bL(t.data);return n.countsRef(e,t.field,i)}));const s={groupby:cq,pulse:a};if(i){r=i.op||"count";o=i.field?yq(r,i.field):"count";s.ops=[pL[r]];s.fields=[e.fieldRef(o)];s.as=[o]}r=e.add(qq(s));const l=e.add(Fq({pulse:uq(r)}));o=e.add(dL({field:cq,sort:e.sortRef(i),pulse:uq(l)}));return uq(o)}function SL(t,e){if(t){if(!t.field&&!t.op){if((0,p.Kn)(t))t.field="key";else t={field:"key"}}else if(!t.field&&t.op!=="count"){(0,p.vU)("No field provided for sort aggregate op: "+t.op)}else if(e&&t.field){if(t.op&&!pL[t.op]){(0,p.vU)("Multiple domain scales can not be sorted using "+t.op)}}}return t}function zL(t,e,n){const i=n.map((t=>{const n=e.getData(t.data);if(!n)bL(t.data);return n.domainRef(e,t.field)}));return uq(e.add(tL({values:i})))}function DL(t,e,n){const i=n.map((t=>{const n=e.getData(t.data);if(!n)bL(t.data);return n.extentRef(e,t.field)}));return uq(e.add(Qq({extents:i})))}function RL(t,e){return t.signal||(0,p.kJ)(t)?vL(t,e):e.objectProperty(t)}function AL(t){return(0,p.Kn)(t)?{interval:yL(t.interval),step:yL(t.step)}:yL(t)}function $L(t,e){e.interpolate=yL(t.type||t);if(t.gamma!=null){e.interpolateGamma=yL(t.gamma)}}function OL(t,e,n){const i=e.config.range;let r=t.range;if(r.signal){return e.signalRef(r.signal)}else if((0,p.HD)(r)){if(i&&(0,p.nr)(i,r)){t=(0,p.l7)({},t,{range:i[r]});return OL(t,e,n)}else if(r==="width"){r=[0,{signal:"width"}]}else if(r==="height"){r=Iu(t.type)?[0,{signal:"height"}]:[{signal:"height"},0]}else{(0,p.vU)("Unrecognized scale range value: "+(0,p.m8)(r))}}else if(r.scheme){n.scheme=(0,p.kJ)(r.scheme)?vL(r.scheme,e):yL(r.scheme,e);if(r.extent)n.schemeExtent=vL(r.extent,e);if(r.count)n.schemeCount=yL(r.count,e);return}else if(r.step){n.rangeStep=yL(r.step,e);return}else if(Iu(t.type)&&!(0,p.kJ)(r)){return xL(r,t,e)}else if(!(0,p.kJ)(r)){(0,p.vU)("Unsupported range type: "+(0,p.m8)(r))}return r.map((t=>((0,p.kJ)(t)?vL:yL)(t,e)))}function TL(t,e){const n=e.config.projection||{},i={};for(const r in t){if(r==="name")continue;i[r]=CL(t[r],r,e)}for(const r in n){if(i[r]==null){i[r]=CL(n[r],r,e)}}e.addProjection(t.name,i)}function CL(t,e,n){return(0,p.kJ)(t)?t.map((t=>CL(t,e,n))):!(0,p.Kn)(t)?t:t.signal?n.signalRef(t.signal):e==="fit"?t:(0,p.vU)("Unsupported parameter object: "+(0,p.m8)(t))}const NL="top";const UL="left";const IL="right";const PL="bottom";const qL="center";const LL="vertical";const jL="start";const FL="middle";const WL="end";const XL="index";const BL="label";const ZL="offset";const HL="perc";const JL="perc2";const GL="value";const KL="guide-label";const VL="guide-title";const YL="group-title";const QL="group-subtitle";const tj="symbol";const ej="gradient";const nj="discrete";const ij="size";const rj="shape";const oj="fill";const aj="stroke";const sj="strokeWidth";const lj="strokeDash";const uj="opacity";const fj=[ij,rj,oj,aj,sj,lj,uj];const cj={name:1,style:1,interactive:1};const dj={value:0};const hj={value:1};const pj="group";const mj="rect";const gj="rule";const yj="symbol";const vj="text";function bj(t){t.type=pj;t.interactive=t.interactive||false;return t}function xj(t,e){const n=(n,i)=>kq(t[n],kq(e[n],i));n.isVertical=n=>LL===kq(t.direction,e.direction||(n?e.symbolDirection:e.gradientDirection));n.gradientLength=()=>kq(t.gradientLength,e.gradientLength||e.gradientWidth);n.gradientThickness=()=>kq(t.gradientThickness,e.gradientThickness||e.gradientHeight);n.entryColumns=()=>kq(t.columns,kq(e.columns,+n.isVertical(true)));return n}function _j(t,e){const n=e&&(e.update&&e.update[t]||e.enter&&e.enter[t]);return n&&n.signal?n:n?n.value:null}function wj(t,e,n){const i=e.config.style[n];return i&&i[t]}function kj(t,e,n){return`item.anchor === '${jL}' ? ${t} : item.anchor === '${WL}' ? ${e} : ${n}`}const Mj=kj((0,p.m8)(UL),(0,p.m8)(IL),(0,p.m8)(qL));function Ej(t){const e=t("tickBand");let n=t("tickOffset"),i,r;if(!e){i=t("bandPosition");r=t("tickExtra")}else if(e.signal){i={signal:`(${e.signal}) === 'extent' ? 1 : 0.5`};r={signal:`(${e.signal}) === 'extent'`};if(!(0,p.Kn)(n)){n={signal:`(${e.signal}) === 'extent' ? 0 : ${n}`}}}else if(e==="extent"){i=1;r=true;n=0}else{i=.5;r=false}return{extra:r,band:i,offset:n}}function Sj(t,e){return!e?t:!t?e:!(0,p.Kn)(t)?{value:t,offset:e}:Object.assign({},t,{offset:Sj(t.offset,e)})}function zj(t,e){if(e){t.name=e.name;t.style=e.style||t.style;t.interactive=!!e.interactive;t.encode=bP(t.encode,e,cj)}else{t.interactive=false}return t}function Dj(t,e,n,i){const r=xj(t,n),o=r.isVertical(),a=r.gradientThickness(),s=r.gradientLength();let l,u,f,c,d;if(o){u=[0,1];f=[0,0];c=a;d=s}else{u=[0,0];f=[1,0];c=s;d=a}const h={enter:l={opacity:dj,x:dj,y:dj,width:gP(c),height:gP(d)},update:(0,p.l7)({},l,{opacity:hj,fill:{gradient:e,start:u,stop:f}}),exit:{opacity:dj}};vP(h,{stroke:r("gradientStrokeColor"),strokeWidth:r("gradientStrokeWidth")},{opacity:r("gradientOpacity")});return zj({type:mj,role:TP,encode:h},i)}function Rj(t,e,n,i,r){const o=xj(t,n),a=o.isVertical(),s=o.gradientThickness(),l=o.gradientLength();let u,f,c,d,h="";a?(u="y",c="y2",f="x",d="width",h="1-"):(u="x",c="x2",f="y",d="height");const m={opacity:dj,fill:{scale:e,field:GL}};m[u]={signal:h+"datum."+HL,mult:l};m[f]=dj;m[c]={signal:h+"datum."+JL,mult:l};m[d]=gP(s);const g={enter:m,update:(0,p.l7)({},m,{opacity:hj}),exit:{opacity:dj}};vP(g,{stroke:o("gradientStrokeColor"),strokeWidth:o("gradientStrokeWidth")},{opacity:o("gradientOpacity")});return zj({type:mj,role:$P,key:GL,from:r,encode:g},i)}const Aj=`datum.${HL}<=0?"${UL}":datum.${HL}>=1?"${IL}":"${qL}"`,$j=`datum.${HL}<=0?"${PL}":datum.${HL}>=1?"${NL}":"${FL}"`;function Oj(t,e,n,i){const r=xj(t,e),o=r.isVertical(),a=gP(r.gradientThickness()),s=r.gradientLength();let l=r("labelOverlap"),u,f,c,d,h="";const p={enter:u={opacity:dj},update:f={opacity:hj,text:{field:BL}},exit:{opacity:dj}};vP(p,{fill:r("labelColor"),fillOpacity:r("labelOpacity"),font:r("labelFont"),fontSize:r("labelFontSize"),fontStyle:r("labelFontStyle"),fontWeight:r("labelFontWeight"),limit:kq(t.labelLimit,e.gradientLabelLimit)});if(o){u.align={value:"left"};u.baseline=f.baseline={signal:$j};c="y";d="x";h="1-"}else{u.align=f.align={signal:Aj};u.baseline={value:"top"};c="x";d="y"}u[c]=f[c]={signal:h+"datum."+HL,mult:s};u[d]=f[d]=a;a.offset=kq(t.labelOffset,e.gradientLabelOffset)||0;l=l?{separation:r("labelSeparation"),method:l,order:"datum."+XL}:undefined;return zj({type:vj,role:CP,style:KL,key:GL,from:i,encode:p,overlap:l},n)}function Tj(t,e,n,i,r){const o=xj(t,e),a=n.entries,s=!!(a&&a.interactive),l=a?a.name:undefined,u=o("clipHeight"),f=o("symbolOffset"),c={data:"value"},d=`(${r}) ? datum.${ZL} : datum.${ij}`,h=u?gP(u):{field:ij},p=`datum.${XL}`,m=`max(1, ${r})`;let g,y,v,b,x;h.mult=.5;g={enter:y={opacity:dj,x:{signal:d,mult:.5,offset:f},y:h},update:v={opacity:hj,x:y.x,y:y.y},exit:{opacity:dj}};let _=null,w=null;if(!t.fill){_=e.symbolBaseFillColor;w=e.symbolBaseStrokeColor}vP(g,{fill:o("symbolFillColor",_),shape:o("symbolType"),size:o("symbolSize"),stroke:o("symbolStrokeColor",w),strokeDash:o("symbolDash"),strokeDashOffset:o("symbolDashOffset"),strokeWidth:o("symbolStrokeWidth")},{opacity:o("symbolOpacity")});fj.forEach((e=>{if(t[e]){v[e]=y[e]={scale:t[e],field:GL}}}));const k=zj({type:yj,role:NP,key:GL,from:c,clip:u?true:undefined,encode:g},n.symbols);const M=gP(f);M.offset=o("labelOffset");g={enter:y={opacity:dj,x:{signal:d,offset:M},y:h},update:v={opacity:hj,text:{field:BL},x:y.x,y:y.y},exit:{opacity:dj}};vP(g,{align:o("labelAlign"),baseline:o("labelBaseline"),fill:o("labelColor"),fillOpacity:o("labelOpacity"),font:o("labelFont"),fontSize:o("labelFontSize"),fontStyle:o("labelFontStyle"),fontWeight:o("labelFontWeight"),limit:o("labelLimit")});const E=zj({type:vj,role:CP,style:KL,key:GL,from:c,encode:g},n.labels);g={enter:{noBound:{value:!u},width:dj,height:u?gP(u):dj,opacity:dj},exit:{opacity:dj},update:v={opacity:hj,row:{signal:null},column:{signal:null}}};if(o.isVertical(true)){b=`ceil(item.mark.items.length / ${m})`;v.row.signal=`${p}%${b}`;v.column.signal=`floor(${p} / ${b})`;x={field:["row",p]}}else{v.row.signal=`floor(${p} / ${m})`;v.column.signal=`${p} % ${m}`;x={field:p}}v.column.signal=`(${r})?${v.column.signal}:${p}`;i={facet:{data:i,name:"value",groupby:XL}};return bj({role:kP,from:i,encode:bP(g,a,cj),marks:[k,E],name:l,interactive:s,sort:x})}function Cj(t,e){const n=xj(t,e);return{align:n("gridAlign"),columns:n.entryColumns(),center:{row:true,column:false},padding:{row:n("rowPadding"),column:n("columnPadding")}}}const Nj='item.orient === "left"',Uj='item.orient === "right"',Ij=`(${Nj} || ${Uj})`,Pj=`datum.vgrad && ${Ij}`,qj=kj('"top"','"bottom"','"middle"'),Lj=kj('"right"','"left"','"center"'),jj=`datum.vgrad && ${Uj} ? (${Lj}) : (${Ij} && !(datum.vgrad && ${Nj})) ? "left" : ${Mj}`,Fj=`item._anchor || (${Ij} ? "middle" : "start")`,Wj=`${Pj} ? (${Nj} ? -90 : 90) : 0`,Xj=`${Ij} ? (datum.vgrad ? (${Uj} ? "bottom" : "top") : ${qj}) : "top"`;function Bj(t,e,n,i){const r=xj(t,e);const o={enter:{opacity:dj},update:{opacity:hj,x:{field:{group:"padding"}},y:{field:{group:"padding"}}},exit:{opacity:dj}};vP(o,{orient:r("titleOrient"),_anchor:r("titleAnchor"),anchor:{signal:Fj},angle:{signal:Wj},align:{signal:jj},baseline:{signal:Xj},text:t.title,fill:r("titleColor"),fillOpacity:r("titleOpacity"),font:r("titleFont"),fontSize:r("titleFontSize"),fontStyle:r("titleFontStyle"),fontWeight:r("titleFontWeight"),limit:r("titleLimit"),lineHeight:r("titleLineHeight")},{align:r("titleAlign"),baseline:r("titleBaseline")});return zj({type:vj,role:UP,style:VL,from:i,encode:o},n)}function Zj(t,e){let n;if((0,p.Kn)(t)){if(t.signal){n=t.signal}else if(t.path){n="pathShape("+Hj(t.path)+")"}else if(t.sphere){n="geoShape("+Hj(t.sphere)+', {type: "Sphere"})'}}return n?e.signalRef(n):!!t}function Hj(t){return(0,p.Kn)(t)&&t.signal?t.signal:(0,p.m8)(t)}function Jj(t){const e=t.role||"";return!e.indexOf("axis")||!e.indexOf("legend")||!e.indexOf("title")?e:t.type===pj?kP:e||_P}function Gj(t){return{marktype:t.type,name:t.name||undefined,role:t.role||Jj(t),zindex:+t.zindex||undefined,aria:t.aria,description:t.description}}function Kj(t,e){return t&&t.signal?e.signalRef(t.signal):t===false?false:true}function Vj(t,e){const n=Ci(t.type);if(!n)(0,p.vU)("Unrecognized transform type: "+(0,p.m8)(t.type));const i=sq(n.type.toLowerCase(),null,Yj(n,t,e));if(t.signal)e.addSignal(t.signal,e.proxy(i));i.metadata=n.metadata||{};return i}function Yj(t,e,n){const i={},r=t.params.length;for(let o=0;o<r;++o){const r=t.params[o];i[r.name]=Qj(r,e,n)}return i}function Qj(t,e,n){const i=t.type,r=e[t.name];if(i==="index"){return eF(t,e,n)}else if(r===undefined){if(t.required){(0,p.vU)("Missing required "+(0,p.m8)(e.type)+" parameter: "+(0,p.m8)(t.name))}return}else if(i==="param"){return nF(t,e,n)}else if(i==="projection"){return n.projectionRef(e[t.name])}return t.array&&!xq(r)?r.map((e=>tF(t,e,n))):tF(t,r,n)}function tF(t,e,n){const i=t.type;if(xq(e)){return sF(i)?(0,p.vU)("Expression references can not be signals."):lF(i)?n.fieldRef(e):uF(i)?n.compareRef(e):n.signalRef(e.signal)}else{const r=t.expr||lF(i);return r&&rF(e)?n.exprRef(e.expr,e.as):r&&oF(e)?fq(e.field,e.as):sF(i)?nU(e,n):aF(i)?uq(n.getData(e).values):lF(i)?fq(e):uF(i)?n.compareRef(e):e}}function eF(t,e,n){if(!(0,p.HD)(e.from)){(0,p.vU)('Lookup "from" parameter must be a string literal.')}return n.getData(e.from).lookupRef(n,e.key)}function nF(t,e,n){const i=e[t.name];if(t.array){if(!(0,p.kJ)(i)){(0,p.vU)("Expected an array of sub-parameters. Instead: "+(0,p.m8)(i))}return i.map((e=>iF(t,e,n)))}else{return iF(t,i,n)}}function iF(t,e,n){const i=t.params.length;let r;for(let a=0;a<i;++a){r=t.params[a];for(const t in r.key){if(r.key[t]!==e[t]){r=null;break}}if(r)break}if(!r)(0,p.vU)("Unsupported parameter: "+(0,p.m8)(e));const o=(0,p.l7)(Yj(r,e,n),r.key);return uq(n.add(nL(o)))}const rF=t=>t&&t.expr;const oF=t=>t&&t.field;const aF=t=>t==="data";const sF=t=>t==="expr";const lF=t=>t==="field";const uF=t=>t==="compare";function fF(t,e,n){let i,r,o,a,s;if(!t){a=uq(n.add(Fq(null,[{}])))}else if(i=t.facet){if(!e)(0,p.vU)("Only group marks can be faceted.");if(i.field!=null){a=s=cF(i,n)}else{if(!t.data){o=Vj((0,p.l7)({type:"aggregate",groupby:(0,p.IX)(i.groupby)},i.aggregate),n);o.params.key=n.keyRef(i.groupby);o.params.pulse=cF(i,n);a=s=uq(n.add(o))}else{s=uq(n.getData(t.data).aggregate)}r=n.keyRef(i.groupby,true)}}if(!a){a=cF(t,n)}return{key:r,pulse:a,parent:s}}function cF(t,e){return t.$ref?t:t.data&&t.data.$ref?t.data:uq(e.getData(t.data).output)}function dF(t,e,n,i,r){this.scope=t;this.input=e;this.output=n;this.values=i;this.aggregate=r;this.index={}}dF.fromEntries=function(t,e){const n=e.length,i=e[n-1],r=e[n-2];let o=e[0],a=null,s=1;if(o&&o.type==="load"){o=e[1]}t.add(e[0]);for(;s<n;++s){e[s].params.pulse=uq(e[s-1]);t.add(e[s]);if(e[s].type==="aggregate")a=e[s]}return new dF(t,o,r,i,a)};function hF(t){return(0,p.HD)(t)?t:null}function pF(t,e,n){const i=yq(n.op,n.field);let r;if(e.ops){for(let t=0,n=e.as.length;t<n;++t){if(e.as[t]===i)return}}else{e.ops=["count"];e.fields=[null];e.as=["count"]}if(n.op){e.ops.push((r=n.op.signal)?t.signalRef(r):n.op);e.fields.push(t.fieldRef(n.field));e.as.push(i)}}function mF(t,e,n,i,r,o,a){const s=e[n]||(e[n]={}),l=gq(o);let u=hF(r),f,c;if(u!=null){t=e.scope;u=u+(l?"|"+l:"");f=s[u]}if(!f){const n=o?{field:cq,pulse:e.countsRef(t,r,o)}:{field:t.fieldRef(r),pulse:uq(e.output)};if(l)n.sort=t.sortRef(o);c=t.add(sq(i,undefined,n));if(a)e.index[r]=c;f=uq(c);if(u!=null)s[u]=f}return f}dF.prototype={countsRef(t,e,n){const i=this,r=i.counts||(i.counts={}),o=hF(e);let a,s,l;if(o!=null){t=i.scope;a=r[o]}if(!a){l={groupby:t.fieldRef(e,"key"),pulse:uq(i.output)};if(n&&n.field)pF(t,l,n);s=t.add(qq(l));a=t.add(Fq({pulse:uq(s)}));a={agg:s,ref:uq(a)};if(o!=null)r[o]=a}else if(n&&n.field){pF(t,a.agg.params,n)}return a.ref},tuplesRef(){return uq(this.values)},extentRef(t,e){return mF(t,this,"extent","extent",e,false)},domainRef(t,e){return mF(t,this,"domain","values",e,false)},valuesRef(t,e,n){return mF(t,this,"vals","values",e,n||true)},lookupRef(t,e){return mF(t,this,"lookup","tupleindex",e,false)},indataRef(t,e){return mF(t,this,"indata","tupleindex",e,true,true)}};function gF(t,e,n){const i=t.from.facet,r=i.name,o=cF(i,e);let a;if(!i.name){(0,p.vU)("Facet must have a name: "+(0,p.m8)(i))}if(!i.data){(0,p.vU)("Facet must reference a data set: "+(0,p.m8)(i))}if(i.field){a=e.add(iL({field:e.fieldRef(i.field),pulse:o}))}else if(i.groupby){a=e.add(Hq({key:e.keyRef(i.groupby),group:uq(e.proxy(n.parent)),pulse:o}))}else{(0,p.vU)("Facet must specify groupby or field: "+(0,p.m8)(i))}const s=e.fork(),l=s.add(Fq()),u=s.add(uL({pulse:uq(l)}));s.addData(r,new dF(s,l,l,u));s.addSignal("parent",null);a.params.subflow={$subflow:s.parse(t).toRuntime()}}function yF(t,e,n){const i=e.add(iL({pulse:n.pulse})),r=e.fork();r.add(uL());r.addSignal("parent",null);i.params.subflow={$subflow:r.parse(t).toRuntime()}}function vF(t,e,n){const i=t.remove,r=t.insert,o=t.toggle,a=t.modify,s=t.values,l=e.add(lq());const u="if("+t.trigger+',modify("'+n+'",'+[r,i,o,a,s].map((t=>t==null?"null":t)).join(",")+"),0)";const f=nU(u,e);l.update=f.$expr;l.params=f.$params}function bF(t,e){const n=Jj(t),i=t.type===pj,r=t.from&&t.from.facet,o=t.overlap;let a=t.layout||n===kP||n===wP,s,l,u,f,c,d,h;const m=n===_P||a||r;const g=fF(t.from,i,e);l=e.add(Xq({key:g.key||(t.key?fq(t.key):undefined),pulse:g.pulse,clean:!i}));const y=uq(l);l=u=e.add(Fq({pulse:y}));l=e.add(Yq({markdef:Gj(t),interactive:Kj(t.interactive,e),clip:Zj(t.clip,e),context:{$context:true},groups:e.lookup(),parent:e.signals.parent?e.signalRef("parent"):null,index:e.markpath(),pulse:uq(l)}));const v=uq(l);l=f=e.add(Bq(YP(t.encode,t.type,n,t.style,e,{mod:false,pulse:v})));l.params.parent=e.encode();if(t.transform){t.transform.forEach((t=>{const n=Vj(t,e),i=n.metadata;if(i.generates||i.changes){(0,p.vU)("Mark transforms should not generate new data.")}if(!i.nomod)f.params.mod=true;n.params.pulse=uq(l);e.add(l=n)}))}if(t.sort){l=e.add(fL({sort:e.compareRef(t.sort),pulse:uq(l)}))}const b=uq(l);if(r||a){a=e.add(cL({layout:e.objectProperty(t.layout),legends:e.legends,mark:v,pulse:b}));d=uq(a)}const x=e.add(jq({mark:v,pulse:d||b}));h=uq(x);if(i){if(m){s=e.operators;s.pop();if(a)s.pop()}e.pushState(b,d||h,y);r?gF(t,e,g):m?yF(t,e,g):e.parse(t);e.popState();if(m){if(a)s.push(a);s.push(x)}}if(o){h=xF(o,h,e)}const _=e.add(sL({pulse:h})),w=e.add(uL({pulse:uq(_)},undefined,e.parent()));if(t.name!=null){c=t.name;e.addData(c,new dF(e,u,_,w));if(t.on)t.on.forEach((t=>{if(t.insert||t.remove||t.toggle){(0,p.vU)("Marks only support modify triggers.")}vF(t,e,c)}))}}function xF(t,e,n){const i=t.method,r=t.bound,o=t.separation;const a={separation:xq(o)?n.signalRef(o.signal):o,method:xq(i)?n.signalRef(i.signal):i,pulse:e};if(t.order){a.sort=n.compareRef({field:t.order})}if(r){const t=r.tolerance;a.boundTolerance=xq(t)?n.signalRef(t.signal):+t;a.boundScale=n.scaleRef(r.scale);a.boundOrient=r.orient}return uq(n.add(eL(a)))}function _F(t,e){const n=e.config.legend,i=t.encode||{},r=xj(t,n),o=i.legend||{},a=o.name||undefined,s=o.interactive,l=o.style,u={};let f=0,c,d,h;fj.forEach((e=>t[e]?(u[e]=t[e],f=f||t[e]):0));if(!f)(0,p.vU)("Missing valid scale for legend.");const m=wF(t,e.scaleType(f));const g={title:t.title!=null,scales:u,type:m,vgrad:m!=="symbol"&&r.isVertical()};const y=uq(e.add(Fq(null,[g])));const v={enter:{x:{value:0},y:{value:0}}};const b=uq(e.add(Kq(d={type:m,scale:e.scaleRef(f),count:e.objectProperty(r("tickCount")),limit:e.property(r("symbolLimit")),values:e.objectProperty(t.values),minstep:e.property(t.tickMinStep),formatType:e.property(t.formatType),formatSpecifier:e.property(t.format)})));if(m===ej){h=[Dj(t,f,n,i.gradient),Oj(t,n,i.labels,b)];d.count=d.count||e.signalRef(`max(2,2*floor((${Mq(r.gradientLength())})/100))`)}else if(m===nj){h=[Rj(t,f,n,i.gradient,b),Oj(t,n,i.labels,b)]}else{c=Cj(t,n);h=[Tj(t,n,i,b,Mq(c.columns))];d.size=EF(t,e,h[0].marks)}h=[bj({role:OP,from:y,encode:v,marks:h,layout:c,interactive:s})];if(g.title){h.push(Bj(t,n,i.title,y))}return bF(bj({role:AP,from:y,encode:bP(MF(r,t,n),o,cj),marks:h,aria:r("aria"),description:r("description"),zindex:r("zindex"),name:a,interactive:s,style:l}),e)}function wF(t,e){let n=t.type||tj;if(!t.type&&kF(t)===1&&(t.fill||t.stroke)){n=Uu(e)?ej:Pu(e)?nj:tj}return n!==ej?n:Pu(e)?nj:ej}function kF(t){return fj.reduce(((e,n)=>e+(t[n]?1:0)),0)}function MF(t,e,n){const i={enter:{},update:{}};vP(i,{orient:t("orient"),offset:t("offset"),padding:t("padding"),titlePadding:t("titlePadding"),cornerRadius:t("cornerRadius"),fill:t("fillColor"),stroke:t("strokeColor"),strokeWidth:n.strokeWidth,strokeDash:n.strokeDash,x:t("legendX"),y:t("legendY"),format:e.format,formatType:e.formatType});return i}function EF(t,e,n){const i=Mq(SF("size",t,n)),r=Mq(SF("strokeWidth",t,n)),o=Mq(zF(n[1].encode,e,KL));return nU(`max(ceil(sqrt(${i})+${r}),${o})`,e)}function SF(t,e,n){return e[t]?`scale("${e[t]}",datum)`:_j(t,n[0].encode)}function zF(t,e,n){return _j("fontSize",t)||wj("fontSize",e,n)}const DF=`item.orient==="${UL}"?-90:item.orient==="${IL}"?90:0`;function RF(t,e){t=(0,p.HD)(t)?{text:t}:t;const n=xj(t,e.config.title),i=t.encode||{},r=i.group||{},o=r.name||undefined,a=r.interactive,s=r.style,l=[];const u={},f=uq(e.add(Fq(null,[u])));l.push(OF(t,n,AF(t),f));if(t.subtitle){l.push(TF(t,n,i.subtitle,f))}return bF(bj({role:IP,from:f,encode:$F(n,r),marks:l,aria:n("aria"),description:n("description"),zindex:n("zindex"),name:o,interactive:a,style:s}),e)}function AF(t){const e=t.encode;return e&&e.title||(0,p.l7)({name:t.name,interactive:t.interactive,style:t.style},e)}function $F(t,e){const n={enter:{},update:{}};vP(n,{orient:t("orient"),anchor:t("anchor"),align:{signal:Mj},angle:{signal:DF},limit:t("limit"),frame:t("frame"),offset:t("offset")||0,padding:t("subtitlePadding")});return bP(n,e,cj)}function OF(t,e,n,i){const r={value:0},o=t.text,a={enter:{opacity:r},update:{opacity:{value:1}},exit:{opacity:r}};vP(a,{text:o,align:{signal:"item.mark.group.align"},angle:{signal:"item.mark.group.angle"},limit:{signal:"item.mark.group.limit"},baseline:"top",dx:e("dx"),dy:e("dy"),fill:e("color"),font:e("font"),fontSize:e("fontSize"),fontStyle:e("fontStyle"),fontWeight:e("fontWeight"),lineHeight:e("lineHeight")},{align:e("align"),angle:e("angle"),baseline:e("baseline")});return zj({type:vj,role:PP,style:YL,from:i,encode:a},n)}function TF(t,e,n,i){const r={value:0},o=t.subtitle,a={enter:{opacity:r},update:{opacity:{value:1}},exit:{opacity:r}};vP(a,{text:o,align:{signal:"item.mark.group.align"},angle:{signal:"item.mark.group.angle"},limit:{signal:"item.mark.group.limit"},baseline:"top",dx:e("dx"),dy:e("dy"),fill:e("subtitleColor"),font:e("subtitleFont"),fontSize:e("subtitleFontSize"),fontStyle:e("subtitleFontStyle"),fontWeight:e("subtitleFontWeight"),lineHeight:e("subtitleLineHeight")},{align:e("align"),angle:e("angle"),baseline:e("baseline")});return zj({type:vj,role:qP,style:QL,from:i,encode:a},n)}function CF(t,e){const n=[];if(t.transform){t.transform.forEach((t=>{n.push(Vj(t,e))}))}if(t.on){t.on.forEach((n=>{vF(n,e,t.name)}))}e.addDataPipeline(t.name,NF(t,e,n))}function NF(t,e,n){const i=[];let r=null,o=false,a=false,s,l,u,f,c;if(t.values){if(xq(t.values)||wq(t.format)){i.push(IF(e,t));i.push(r=UF())}else{i.push(r=UF({$ingest:t.values,$format:t.format}))}}else if(t.url){if(wq(t.url)||wq(t.format)){i.push(IF(e,t));i.push(r=UF())}else{i.push(r=UF({$request:t.url,$format:t.format}))}}else if(t.source){r=s=(0,p.IX)(t.source).map((t=>uq(e.getData(t).output)));i.push(null)}for(l=0,u=n.length;l<u;++l){f=n[l];c=f.metadata;if(!r&&!c.source){i.push(r=UF())}i.push(f);if(c.generates)a=true;if(c.modifies&&!a)o=true;if(c.source)r=f;else if(c.changes)r=null}if(s){u=s.length-1;i[0]=aL({derive:o,pulse:u?s:s[0]});if(o||u){i.splice(1,0,UF())}}if(!r)i.push(UF());i.push(uL({}));return i}function UF(t){const e=Fq({},t);e.metadata={source:true};return e}function IF(t,e){return Vq({url:e.url?t.property(e.url):undefined,async:e.async?t.property(e.async):undefined,values:e.values?t.property(e.values):undefined,format:t.objectProperty(e.format)})}const PF=t=>t===PL||t===NL;const qF=(t,e,n)=>xq(t)?ZF(t.signal,e,n):t===UL||t===NL?e:n;const LF=(t,e,n)=>xq(t)?XF(t.signal,e,n):PF(t)?e:n;const jF=(t,e,n)=>xq(t)?BF(t.signal,e,n):PF(t)?n:e;const FF=(t,e,n)=>xq(t)?HF(t.signal,e,n):t===NL?{value:e}:{value:n};const WF=(t,e,n)=>xq(t)?JF(t.signal,e,n):t===IL?{value:e}:{value:n};const XF=(t,e,n)=>GF(`${t} === '${NL}' || ${t} === '${PL}'`,e,n);const BF=(t,e,n)=>GF(`${t} !== '${NL}' && ${t} !== '${PL}'`,e,n);const ZF=(t,e,n)=>VF(`${t} === '${UL}' || ${t} === '${NL}'`,e,n);const HF=(t,e,n)=>VF(`${t} === '${NL}'`,e,n);const JF=(t,e,n)=>VF(`${t} === '${IL}'`,e,n);const GF=(t,e,n)=>{e=e!=null?gP(e):e;n=n!=null?gP(n):n;if(KF(e)&&KF(n)){e=e?e.signal||(0,p.m8)(e.value):null;n=n?n.signal||(0,p.m8)(n.value):null;return{signal:`${t} ? (${e}) : (${n})`}}else{return[(0,p.l7)({test:t},e)].concat(n||[])}};const KF=t=>t==null||Object.keys(t).length===1;const VF=(t,e,n)=>({signal:`${t} ? (${QF(e)}) : (${QF(n)})`});const YF=(t,e,n,i,r)=>({signal:(i!=null?`${t} === '${UL}' ? (${QF(i)}) : `:"")+(n!=null?`${t} === '${PL}' ? (${QF(n)}) : `:"")+(r!=null?`${t} === '${IL}' ? (${QF(r)}) : `:"")+(e!=null?`${t} === '${NL}' ? (${QF(e)}) : `:"")+"(null)"});const QF=t=>xq(t)?t.signal:t==null?null:(0,p.m8)(t);const tW=(t,e)=>e===0?0:xq(t)?{signal:`(${t.signal}) * ${e}`}:{value:t*e};const eW=(t,e)=>{const n=t.signal;return n&&n.endsWith("(null)")?{signal:n.slice(0,-6)+e.signal}:t};function nW(t,e,n,i){let r;if(e&&(0,p.nr)(e,t)){return e[t]}else if((0,p.nr)(n,t)){return n[t]}else if(t.startsWith("title")){switch(t){case"titleColor":r="fill";break;case"titleFont":case"titleFontSize":case"titleFontWeight":r=t[5].toLowerCase()+t.slice(6)}return i[VL][r]}else if(t.startsWith("label")){switch(t){case"labelColor":r="fill";break;case"labelFont":case"labelFontSize":r=t[5].toLowerCase()+t.slice(6)}return i[KL][r]}return null}function iW(t){const e={};for(const n of t){if(!n)continue;for(const t in n)e[t]=1}return Object.keys(e)}function rW(t,e){var n=e.config,i=n.style,r=n.axis,o=e.scaleType(t.scale)==="band"&&n.axisBand,a=t.orient,s,l,u;if(xq(a)){const t=iW([n.axisX,n.axisY]),e=iW([n.axisTop,n.axisBottom,n.axisLeft,n.axisRight]);s={};for(u of t){s[u]=LF(a,nW(u,n.axisX,r,i),nW(u,n.axisY,r,i))}l={};for(u of e){l[u]=YF(a.signal,nW(u,n.axisTop,r,i),nW(u,n.axisBottom,r,i),nW(u,n.axisLeft,r,i),nW(u,n.axisRight,r,i))}}else{s=a===NL||a===PL?n.axisX:n.axisY;l=n["axis"+a[0].toUpperCase()+a.slice(1)]}const f=s||l||o?(0,p.l7)({},r,s,l,o):r;return f}function oW(t,e,n,i){const r=xj(t,e),o=t.orient;let a,s;const l={enter:a={opacity:dj},update:s={opacity:hj},exit:{opacity:dj}};vP(l,{stroke:r("domainColor"),strokeCap:r("domainCap"),strokeDash:r("domainDash"),strokeDashOffset:r("domainDashOffset"),strokeWidth:r("domainWidth"),strokeOpacity:r("domainOpacity")});const u=aW(t,0);const f=aW(t,1);a.x=s.x=LF(o,u,dj);a.x2=s.x2=LF(o,f);a.y=s.y=jF(o,u,dj);a.y2=s.y2=jF(o,f);return zj({type:gj,role:EP,from:i,encode:l},n)}function aW(t,e){return{scale:t.scale,range:e}}function sW(t,e,n,i,r){const o=xj(t,e),a=t.orient,s=t.gridScale,l=qF(a,1,-1),u=lW(t.offset,l);let f,c,d;const h={enter:f={opacity:dj},update:d={opacity:hj},exit:c={opacity:dj}};vP(h,{stroke:o("gridColor"),strokeCap:o("gridCap"),strokeDash:o("gridDash"),strokeDashOffset:o("gridDashOffset"),strokeOpacity:o("gridOpacity"),strokeWidth:o("gridWidth")});const m={scale:t.scale,field:GL,band:r.band,extra:r.extra,offset:r.offset,round:o("tickRound")};const g=LF(a,{signal:"height"},{signal:"width"});const y=s?{scale:s,range:0,mult:l,offset:u}:{value:0,offset:u};const v=s?{scale:s,range:1,mult:l,offset:u}:(0,p.l7)(g,{mult:l,offset:u});f.x=d.x=LF(a,m,y);f.y=d.y=jF(a,m,y);f.x2=d.x2=jF(a,v);f.y2=d.y2=LF(a,v);c.x=LF(a,m);c.y=jF(a,m);return zj({type:gj,role:SP,key:GL,from:i,encode:h},n)}function lW(t,e){if(e===1);else if(!(0,p.Kn)(t)){t=xq(e)?{signal:`(${e.signal}) * (${t||0})`}:e*(t||0)}else{let n=t=(0,p.l7)({},t);while(n.mult!=null){if(!(0,p.Kn)(n.mult)){n.mult=xq(e)?{signal:`(${n.mult}) * (${e.signal})`}:n.mult*e;return t}else{n=n.mult=(0,p.l7)({},n.mult)}}n.mult=e}return t}function uW(t,e,n,i,r,o){const a=xj(t,e),s=t.orient,l=qF(s,-1,1);let u,f,c;const d={enter:u={opacity:dj},update:c={opacity:hj},exit:f={opacity:dj}};vP(d,{stroke:a("tickColor"),strokeCap:a("tickCap"),strokeDash:a("tickDash"),strokeDashOffset:a("tickDashOffset"),strokeOpacity:a("tickOpacity"),strokeWidth:a("tickWidth")});const h=gP(r);h.mult=l;const p={scale:t.scale,field:GL,band:o.band,extra:o.extra,offset:o.offset,round:a("tickRound")};c.y=u.y=LF(s,dj,p);c.y2=u.y2=LF(s,h);f.x=LF(s,p);c.x=u.x=jF(s,dj,p);c.x2=u.x2=jF(s,h);f.y=jF(s,p);return zj({type:gj,role:DP,key:GL,from:i,encode:d},n)}function fW(t,e,n,i,r){return{signal:'flush(range("'+t+'"), '+'scale("'+t+'", datum.value), '+e+","+n+","+i+","+r+")"}}function cW(t,e,n,i,r,o){const a=xj(t,e),s=t.orient,l=t.scale,u=qF(s,-1,1),f=Mq(a("labelFlush")),c=Mq(a("labelFlushOffset")),d=a("labelAlign"),h=a("labelBaseline");let p=f===0||!!f,m;const g=gP(r);g.mult=u;g.offset=gP(a("labelPadding")||0);g.offset.mult=u;const y={scale:l,field:GL,band:.5,offset:Sj(o.offset,a("labelOffset"))};const v=LF(s,p?fW(l,f,'"left"','"right"','"center"'):{value:"center"},WF(s,"left","right"));const b=LF(s,FF(s,"bottom","top"),p?fW(l,f,'"top"','"bottom"','"middle"'):{value:"middle"});const x=fW(l,f,`-(${c})`,c,0);p=p&&c;const _={opacity:dj,x:LF(s,y,g),y:jF(s,y,g)};const w={enter:_,update:m={opacity:hj,text:{field:BL},x:_.x,y:_.y,align:v,baseline:b},exit:{opacity:dj,x:_.x,y:_.y}};vP(w,{dx:!d&&p?LF(s,x):null,dy:!h&&p?jF(s,x):null});vP(w,{angle:a("labelAngle"),fill:a("labelColor"),fillOpacity:a("labelOpacity"),font:a("labelFont"),fontSize:a("labelFontSize"),fontWeight:a("labelFontWeight"),fontStyle:a("labelFontStyle"),limit:a("labelLimit"),lineHeight:a("labelLineHeight")},{align:d,baseline:h});const k=a("labelBound");let M=a("labelOverlap");M=M||k?{separation:a("labelSeparation"),method:M,order:"datum.index",bound:k?{scale:l,orient:s,tolerance:k}:null}:undefined;if(m.align!==v){m.align=eW(m.align,v)}if(m.baseline!==b){m.baseline=eW(m.baseline,b)}return zj({type:vj,role:zP,style:KL,key:GL,from:i,encode:w,overlap:M},n)}function dW(t,e,n,i){const r=xj(t,e),o=t.orient,a=qF(o,-1,1);let s,l;const u={enter:s={opacity:dj,anchor:gP(r("titleAnchor",null)),align:{signal:Mj}},update:l=(0,p.l7)({},s,{opacity:hj,text:gP(t.title)}),exit:{opacity:dj}};const f={signal:`lerp(range("${t.scale}"), ${kj(0,1,.5)})`};l.x=LF(o,f);l.y=jF(o,f);s.angle=LF(o,dj,tW(a,90));s.baseline=LF(o,FF(o,PL,NL),{value:PL});l.angle=s.angle;l.baseline=s.baseline;vP(u,{fill:r("titleColor"),fillOpacity:r("titleOpacity"),font:r("titleFont"),fontSize:r("titleFontSize"),fontStyle:r("titleFontStyle"),fontWeight:r("titleFontWeight"),limit:r("titleLimit"),lineHeight:r("titleLineHeight")},{align:r("titleAlign"),angle:r("titleAngle"),baseline:r("titleBaseline")});hW(r,o,u,n);u.update.align=eW(u.update.align,s.align);u.update.angle=eW(u.update.angle,s.angle);u.update.baseline=eW(u.update.baseline,s.baseline);return zj({type:vj,role:RP,style:VL,from:i,encode:u},n)}function hW(t,e,n,i){const r=(t,e)=>t!=null?(n.update[e]=eW(gP(t),n.update[e]),false):!xP(e,i)?true:false;const o=r(t("titleX"),"x"),a=r(t("titleY"),"y");n.enter.auto=a===o?gP(a):LF(e,gP(a),gP(o))}function pW(t,e){const n=rW(t,e),i=t.encode||{},r=i.axis||{},o=r.name||undefined,a=r.interactive,s=r.style,l=xj(t,n),u=Ej(l);const f={scale:t.scale,ticks:!!l("ticks"),labels:!!l("labels"),grid:!!l("grid"),domain:!!l("domain"),title:t.title!=null};const c=uq(e.add(Fq({},[f])));const d=uq(e.add(Lq({scale:e.scaleRef(t.scale),extra:e.property(u.extra),count:e.objectProperty(t.tickCount),values:e.objectProperty(t.values),minstep:e.property(t.tickMinStep),formatType:e.property(t.formatType),formatSpecifier:e.property(t.format)})));const h=[];let p;if(f.grid){h.push(sW(t,n,i.grid,d,u))}if(f.ticks){p=l("tickSize");h.push(uW(t,n,i.ticks,d,p,u))}if(f.labels){p=f.ticks?p:0;h.push(cW(t,n,i.labels,d,p,u))}if(f.domain){h.push(oW(t,n,i.domain,c))}if(f.title){h.push(dW(t,n,i.title,c))}return bF(bj({role:MP,from:c,encode:bP(mW(l,t),r,cj),marks:h,aria:l("aria"),description:l("description"),zindex:l("zindex"),name:o,interactive:a,style:s}),e)}function mW(t,e){const n={enter:{},update:{}};vP(n,{orient:t("orient"),offset:t("offset")||0,position:kq(e.position,0),titlePadding:t("titlePadding"),minExtent:t("minExtent"),maxExtent:t("maxExtent"),range:{signal:`abs(span(range("${e.scale}")))`},translate:t("translate"),format:e.format,formatType:e.formatType});return n}function gW(t,e,n){const i=(0,p.IX)(t.signals),r=(0,p.IX)(t.scales);if(!n)i.forEach((t=>oq(t,e)));(0,p.IX)(t.projections).forEach((t=>TL(t,e)));r.forEach((t=>mL(t,e)));(0,p.IX)(t.data).forEach((t=>CF(t,e)));r.forEach((t=>gL(t,e)));(n||i).forEach((t=>Iq(t,e)));(0,p.IX)(t.axes).forEach((t=>pW(t,e)));(0,p.IX)(t.marks).forEach((t=>bF(t,e)));(0,p.IX)(t.legends).forEach((t=>_F(t,e)));if(t.title)RF(t.title,e);e.parseLambdas();return e}const yW=t=>bP({enter:{x:{value:0},y:{value:0}},update:{width:{signal:"width"},height:{signal:"height"}}},t);function vW(t,e){const n=e.config;const i=uq(e.root=e.add(lq()));const r=xW(t,n);r.forEach((t=>oq(t,e)));e.description=t.description||n.description;e.eventConfig=n.events;e.legends=e.objectProperty(n.legend&&n.legend.layout);e.locale=n.locale;const o=e.add(Fq());const a=e.add(Bq(YP(yW(t.encode),pj,wP,t.style,e,{pulse:uq(o)})));const s=e.add(cL({layout:e.objectProperty(t.layout),legends:e.legends,autosize:e.signalRef("autosize"),mark:i,pulse:uq(a)}));e.operators.pop();e.pushState(uq(a),uq(s),null);gW(t,e,r);e.operators.push(s);let l=e.add(jq({mark:i,pulse:uq(s)}));l=e.add(sL({pulse:uq(l)}));l=e.add(uL({pulse:uq(l)}));e.addData("root",new dF(e,o,o,l));return e}function bW(t,e){return e&&e.signal?{name:t,update:e.signal}:{name:t,value:e}}function xW(t,e){const n=n=>kq(t[n],e[n]),i=[bW("background",n("background")),bW("autosize",dP(n("autosize"))),bW("padding",mP(n("padding"))),bW("width",n("width")||0),bW("height",n("height")||0)],r=i.reduce(((t,e)=>(t[e.name]=e,t)),{}),o={};(0,p.IX)(t.signals).forEach((t=>{if((0,p.nr)(r,t.name)){t=(0,p.l7)(r[t.name],t)}else{i.push(t)}o[t.name]=t}));(0,p.IX)(e.signals).forEach((t=>{if(!(0,p.nr)(o,t.name)&&!(0,p.nr)(r,t.name)){i.push(t)}}));return i}function _W(t,e){this.config=t||{};this.options=e||{};this.bindings=[];this.field={};this.signals={};this.lambdas={};this.scales={};this.events={};this.data={};this.streams=[];this.updates=[];this.operators=[];this.eventConfig=null;this.locale=null;this._id=0;this._subid=0;this._nextsub=[0];this._parent=[];this._encode=[];this._lookup=[];this._markpath=[]}function wW(t){this.config=t.config;this.options=t.options;this.legends=t.legends;this.field=Object.create(t.field);this.signals=Object.create(t.signals);this.lambdas=Object.create(t.lambdas);this.scales=Object.create(t.scales);this.events=Object.create(t.events);this.data=Object.create(t.data);this.streams=[];this.updates=[];this.operators=[];this._id=0;this._subid=++t._nextsub[0];this._nextsub=t._nextsub;this._parent=t._parent.slice();this._encode=t._encode.slice();this._lookup=t._lookup.slice();this._markpath=t._markpath}_W.prototype=wW.prototype={parse(t){return gW(t,this)},fork(){return new wW(this)},isSubscope(){return this._subid>0},toRuntime(){this.finish();return{description:this.description,operators:this.operators,streams:this.streams,updates:this.updates,bindings:this.bindings,eventConfig:this.eventConfig,locale:this.locale}},id(){return(this._subid?this._subid+":":0)+this._id++},add(t){this.operators.push(t);t.id=this.id();if(t.refs){t.refs.forEach((e=>{e.$ref=t.id}));t.refs=null}return t},proxy(t){const e=t instanceof aq?uq(t):t;return this.add(oL({value:e}))},addStream(t){this.streams.push(t);t.id=this.id();return t},addUpdate(t){this.updates.push(t);return t},finish(){let t,e;if(this.root)this.root.root=true;for(t in this.signals){this.signals[t].signal=t}for(t in this.scales){this.scales[t].scale=t}function n(t,e,n){let i,r;if(t){i=t.data||(t.data={});r=i[e]||(i[e]=[]);r.push(n)}}for(t in this.data){e=this.data[t];n(e.input,t,"input");n(e.output,t,"output");n(e.values,t,"values");for(const i in e.index){n(e.index[i],t,"index:"+i)}}return this},pushState(t,e,n){this._encode.push(uq(this.add(uL({pulse:t}))));this._parent.push(e);this._lookup.push(n?uq(this.proxy(n)):null);this._markpath.push(-1)},popState(){this._encode.pop();this._parent.pop();this._lookup.pop();this._markpath.pop()},parent(){return(0,p.fj)(this._parent)},encode(){return(0,p.fj)(this._encode)},lookup(){return(0,p.fj)(this._lookup)},markpath(){const t=this._markpath;return++t[t.length-1]},fieldRef(t,e){if((0,p.HD)(t))return fq(t,e);if(!t.signal){(0,p.vU)("Unsupported field reference: "+(0,p.m8)(t))}const n=t.signal;let i=this.field[n];if(!i){const t={name:this.signalRef(n)};if(e)t.as=e;this.field[n]=i=uq(this.add(Jq(t)))}return i},compareRef(t){let e=false;const n=t=>xq(t)?(e=true,this.signalRef(t.signal)):_q(t)?(e=true,this.exprRef(t.expr)):t;const i=(0,p.IX)(t.field).map(n),r=(0,p.IX)(t.order).map(n);return e?uq(this.add(Wq({fields:i,orders:r}))):dq(i,r)},keyRef(t,e){let n=false;const i=t=>xq(t)?(n=true,uq(r[t.signal])):t;const r=this.signals;t=(0,p.IX)(t).map(i);return n?uq(this.add(Gq({fields:t,flat:e}))):hq(t,e)},sortRef(t){if(!t)return t;const e=yq(t.op,t.field),n=t.order||pq;return n.signal?uq(this.add(Wq({fields:e,orders:this.signalRef(n.signal)}))):dq(e,n)},event(t,e){const n=t+":"+e;if(!this.events[n]){const i=this.id();this.streams.push({id:i,source:t,type:e});this.events[n]=i}return this.events[n]},hasOwnSignal(t){return(0,p.nr)(this.signals,t)},addSignal(t,e){if(this.hasOwnSignal(t)){(0,p.vU)("Duplicate signal name: "+(0,p.m8)(t))}const n=e instanceof aq?e:this.add(lq(e));return this.signals[t]=n},getSignal(t){if(!this.signals[t]){(0,p.vU)("Unrecognized signal name: "+(0,p.m8)(t))}return this.signals[t]},signalRef(t){if(this.signals[t]){return uq(this.signals[t])}else if(!(0,p.nr)(this.lambdas,t)){this.lambdas[t]=this.add(lq(null))}return uq(this.lambdas[t])},parseLambdas(){const t=Object.keys(this.lambdas);for(let e=0,n=t.length;e<n;++e){const n=t[e],i=nU(n,this),r=this.lambdas[n];r.params=i.$params;r.update=i.$expr}},property(t){return t&&t.signal?this.signalRef(t.signal):t},objectProperty(t){return!t||!(0,p.Kn)(t)?t:this.signalRef(t.signal||kW(t))},exprRef(t,e){const n={expr:nU(t,this)};if(e)n.expr.$name=e;return uq(this.add(Zq(n)))},addBinding(t,e){if(!this.bindings){(0,p.vU)("Nested signals do not support binding: "+(0,p.m8)(t))}this.bindings.push((0,p.l7)({signal:t},e))},addScaleProj(t,e){if((0,p.nr)(this.scales,t)){(0,p.vU)("Duplicate scale or projection name: "+(0,p.m8)(t))}this.scales[t]=this.add(e)},addScale(t,e){this.addScaleProj(t,lL(e))},addProjection(t,e){this.addScaleProj(t,rL(e))},getScale(t){if(!this.scales[t]){(0,p.vU)("Unrecognized scale name: "+(0,p.m8)(t))}return this.scales[t]},scaleRef(t){return uq(this.getScale(t))},scaleType(t){return this.getScale(t).params.type},projectionRef(t){return this.scaleRef(t)},projectionType(t){return this.scaleType(t)},addData(t,e){if((0,p.nr)(this.data,t)){(0,p.vU)("Duplicate data set name: "+(0,p.m8)(t))}return this.data[t]=e},getData(t){if(!this.data[t]){(0,p.vU)("Undefined data set name: "+(0,p.m8)(t))}return this.data[t]},addDataPipeline(t,e){if((0,p.nr)(this.data,t)){(0,p.vU)("Duplicate data set name: "+(0,p.m8)(t))}return this.addData(t,dF.fromEntries(this,e))}};function kW(t){return((0,p.kJ)(t)?MW:EW)(t)}function MW(t){const e=t.length;let n="[";for(let i=0;i<e;++i){const e=t[i];n+=(i>0?",":"")+((0,p.Kn)(e)?e.signal||kW(e):(0,p.m8)(e))}return n+"]"}function EW(t){let e="{",n=0,i,r;for(i in t){r=t[i];e+=(++n>1?",":"")+(0,p.m8)(i)+":"+((0,p.Kn)(r)?r.signal||kW(r):(0,p.m8)(r))}return e+"}"}function SW(){const t="sans-serif",e=30,n=2,i="#4c78a8",r="#000",o="#888",a="#ddd";return{description:"Vega visualization",padding:0,autosize:"pad",background:null,events:{defaults:{allow:["wheel"]}},group:null,mark:null,arc:{fill:i},area:{fill:i},image:null,line:{stroke:i,strokeWidth:n},path:{stroke:i},rect:{fill:i},rule:{stroke:r},shape:{stroke:i},symbol:{fill:i,size:64},text:{fill:r,font:t,fontSize:11},trail:{fill:i,size:n},style:{"guide-label":{fill:r,font:t,fontSize:10},"guide-title":{fill:r,font:t,fontSize:11,fontWeight:"bold"},"group-title":{fill:r,font:t,fontSize:13,fontWeight:"bold"},"group-subtitle":{fill:r,font:t,fontSize:12},point:{size:e,strokeWidth:n,shape:"circle"},circle:{size:e,strokeWidth:n},square:{size:e,strokeWidth:n,shape:"square"},cell:{fill:"transparent",stroke:a},view:{fill:"transparent"}},title:{orient:"top",anchor:"middle",offset:4,subtitlePadding:3},axis:{minExtent:0,maxExtent:200,bandPosition:.5,domain:true,domainWidth:1,domainColor:o,grid:false,gridWidth:1,gridColor:a,labels:true,labelAngle:0,labelLimit:180,labelOffset:0,labelPadding:2,ticks:true,tickColor:o,tickOffset:0,tickRound:true,tickSize:5,tickWidth:1,titlePadding:4},axisBand:{tickOffset:-.5},projection:{type:"mercator"},legend:{orient:"right",padding:0,gridAlign:"each",columnPadding:10,rowPadding:2,symbolDirection:"vertical",gradientDirection:"vertical",gradientLength:200,gradientThickness:16,gradientStrokeColor:a,gradientStrokeWidth:0,gradientLabelOffset:2,labelAlign:"left",labelBaseline:"middle",labelLimit:160,labelOffset:4,labelOverlap:true,symbolLimit:30,symbolType:"circle",symbolSize:100,symbolOffset:0,symbolStrokeWidth:1.5,symbolBaseFillColor:"transparent",symbolBaseStrokeColor:o,titleLimit:180,titleOrient:"top",titlePadding:5,layout:{offset:18,direction:"horizontal",left:{direction:"vertical"},right:{direction:"vertical"}}},range:{category:{scheme:"tableau10"},ordinal:{scheme:"blues"},heatmap:{scheme:"yellowgreenblue"},ramp:{scheme:"blues"},diverging:{scheme:"blueorange",extent:[1,0]},symbol:["circle","square","triangle-up","cross","diamond","triangle-right","triangle-down","triangle-left"]}}}function zW(t,e,n){if(!(0,p.Kn)(t)){(0,p.vU)("Input Vega specification must be an object.")}e=(0,p.fE)(SW(),e,t.config);return vW(t,new _W(e,n)).toRuntime()}var DW="5.24.0";(0,p.l7)(Ti,i,r,o,a,s,u,l,f,c,d,h)}}]);
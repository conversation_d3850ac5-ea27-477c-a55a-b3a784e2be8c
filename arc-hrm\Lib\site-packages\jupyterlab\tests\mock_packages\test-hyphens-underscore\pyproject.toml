# Copyright (c) Jupyter Development Team.
# Distributed under the terms of the Modified BSD License.

[build-system]
requires = ["jupyter_packaging~=0.10,<2", "jupyterlab~=3.0"]
build-backend = "jupyter_packaging.build_api"

[tool.jupyter-packaging.options]
ensured-targets = ["test_hyphens_underscore/labextension/package.json"]

[tool.jupyter-packaging.builder]
factory = "jupyter_packaging.npm_builder"

[tool.jupyter-packaging.build-args]
build_cmd = "build:labextension"
npm = ["jlpm"]

[tool.check-manifest]
ignore = ["test_hyphens_underscore/labextension/**", "yarn.lock", ".*", "package-lock.json"]

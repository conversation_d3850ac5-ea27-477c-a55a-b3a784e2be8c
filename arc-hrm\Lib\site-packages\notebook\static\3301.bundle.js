"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[3301],{43301:(e,t,n)=>{n.r(t),n.d(t,{CommandRegistry:()=>d});var i,s=n(33625),r=n(20998),a=n(2549),o=n(18395),l=n(80554),c=n(81997);class d{constructor(){this._timerID=0,this._replaying=!1,this._keystrokes=[],this._keydownEvents=[],this._keyBindings=[],this._exactKeyMatch=null,this._commands=new Map,this._commandChanged=new c.Signal(this),this._commandExecuted=new c.Signal(this),this._keyBindingChanged=new c.Signal(this)}get commandChanged(){return this._commandChanged}get commandExecuted(){return this._commandExecuted}get keyBindingChanged(){return this._keyBindingChanged}get keyBindings(){return this._keyBindings}listCommands(){return Array.from(this._commands.keys())}hasCommand(e){return this._commands.has(e)}addCommand(e,t){if(this._commands.has(e))throw new Error(`Command '${e}' already registered.`);return this._commands.set(e,i.createCommand(t)),this._commandChanged.emit({id:e,type:"added"}),new a.DisposableDelegate((()=>{this._commands.delete(e),this._commandChanged.emit({id:e,type:"removed"})}))}notifyCommandChanged(e){if(void 0!==e&&!this._commands.has(e))throw new Error(`Command '${e}' is not registered.`);this._commandChanged.emit({id:e,type:e?"changed":"many-changed"})}describedBy(e,t=r.JSONExt.emptyObject){var n;let i=this._commands.get(e);return Promise.resolve(null!==(n=null==i?void 0:i.describedBy.call(void 0,t))&&void 0!==n?n:{args:null})}label(e,t=r.JSONExt.emptyObject){var n;let i=this._commands.get(e);return null!==(n=null==i?void 0:i.label.call(void 0,t))&&void 0!==n?n:""}mnemonic(e,t=r.JSONExt.emptyObject){let n=this._commands.get(e);return n?n.mnemonic.call(void 0,t):-1}icon(e,t=r.JSONExt.emptyObject){var n;return null===(n=this._commands.get(e))||void 0===n?void 0:n.icon.call(void 0,t)}iconClass(e,t=r.JSONExt.emptyObject){let n=this._commands.get(e);return n?n.iconClass.call(void 0,t):""}iconLabel(e,t=r.JSONExt.emptyObject){let n=this._commands.get(e);return n?n.iconLabel.call(void 0,t):""}caption(e,t=r.JSONExt.emptyObject){let n=this._commands.get(e);return n?n.caption.call(void 0,t):""}usage(e,t=r.JSONExt.emptyObject){let n=this._commands.get(e);return n?n.usage.call(void 0,t):""}className(e,t=r.JSONExt.emptyObject){let n=this._commands.get(e);return n?n.className.call(void 0,t):""}dataset(e,t=r.JSONExt.emptyObject){let n=this._commands.get(e);return n?n.dataset.call(void 0,t):{}}isEnabled(e,t=r.JSONExt.emptyObject){let n=this._commands.get(e);return!!n&&n.isEnabled.call(void 0,t)}isToggled(e,t=r.JSONExt.emptyObject){let n=this._commands.get(e);return!!n&&n.isToggled.call(void 0,t)}isToggleable(e,t=r.JSONExt.emptyObject){let n=this._commands.get(e);return!!n&&n.isToggleable}isVisible(e,t=r.JSONExt.emptyObject){let n=this._commands.get(e);return!!n&&n.isVisible.call(void 0,t)}execute(e,t=r.JSONExt.emptyObject){let n,i=this._commands.get(e);if(!i)return Promise.reject(new Error(`Command '${e}' not registered.`));try{n=i.execute.call(void 0,t)}catch(e){n=Promise.reject(e)}let s=Promise.resolve(n);return this._commandExecuted.emit({id:e,args:t,result:s}),s}addKeyBinding(e){let t=i.createKeyBinding(e);return this._keyBindings.push(t),this._keyBindingChanged.emit({binding:t,type:"added"}),new a.DisposableDelegate((()=>{s.ArrayExt.removeFirstOf(this._keyBindings,t),this._keyBindingChanged.emit({binding:t,type:"removed"})}))}processKeydownEvent(e){if(this._replaying||d.isModifierKeyPressed(e))return;let t=d.keystrokeForKeydownEvent(e);if(!t)return this._replayKeydownEvents(),void this._clearPendingState();this._keystrokes.push(t);let{exact:n,partial:s}=i.matchKeyBinding(this._keyBindings,this._keystrokes,e);return n||s?(e.preventDefault(),e.stopPropagation(),n&&!s?(this._executeKeyBinding(n),void this._clearPendingState()):(n&&(this._exactKeyMatch=n),this._keydownEvents.push(e),void this._startTimer())):(this._replayKeydownEvents(),void this._clearPendingState())}_startTimer(){this._clearTimer(),this._timerID=window.setTimeout((()=>{this._onPendingTimeout()}),i.CHORD_TIMEOUT)}_clearTimer(){0!==this._timerID&&(clearTimeout(this._timerID),this._timerID=0)}_replayKeydownEvents(){0!==this._keydownEvents.length&&(this._replaying=!0,this._keydownEvents.forEach(i.replayKeyEvent),this._replaying=!1)}_executeKeyBinding(e){let{command:t,args:n}=e,i={_luminoEvent:{type:"keybinding",keys:e.keys},...n};if(this.hasCommand(t)&&this.isEnabled(t,i))this.execute(t,i);else{let n=this.hasCommand(t)?"enabled":"registered",i=`Cannot execute key binding '${e.keys.join(", ")}':`,s=`command '${t}' is not ${n}.`;console.warn(`${i} ${s}`)}}_clearPendingState(){this._clearTimer(),this._exactKeyMatch=null,this._keystrokes.length=0,this._keydownEvents.length=0}_onPendingTimeout(){this._timerID=0,this._exactKeyMatch?this._executeKeyBinding(this._exactKeyMatch):this._replayKeydownEvents(),this._clearPendingState()}}!function(e){function t(e){let t="",n=!1,i=!1,s=!1,r=!1;for(let a of e.split(/\s+/))"Accel"===a?o.Platform.IS_MAC?i=!0:s=!0:"Alt"===a?n=!0:"Cmd"===a?i=!0:"Ctrl"===a?s=!0:"Shift"===a?r=!0:a.length>0&&(t=a);return{cmd:i,ctrl:s,alt:n,shift:r,key:t}}function n(e){let n="",i=t(e);return i.ctrl&&(n+="Ctrl "),i.alt&&(n+="Alt "),i.shift&&(n+="Shift "),i.cmd&&o.Platform.IS_MAC&&(n+="Cmd "),n+i.key}e.parseKeystroke=t,e.normalizeKeystroke=n,e.normalizeKeys=function(e){let t;return t=o.Platform.IS_WIN?e.winKeys||e.keys:o.Platform.IS_MAC?e.macKeys||e.keys:e.linuxKeys||e.keys,t.map(n)},e.formatKeystroke=function(e){return"string"==typeof e?n(e):e.map(n).join(", ");function n(e){let n=[],s=o.Platform.IS_MAC?" ":"+",r=t(e);return r.ctrl&&n.push("Ctrl"),r.alt&&n.push("Alt"),r.shift&&n.push("Shift"),o.Platform.IS_MAC&&r.cmd&&n.push("Cmd"),n.push(r.key),n.map(i.formatKey).join(s)}},e.isModifierKeyPressed=function(e){let t=(0,l.getKeyboardLayout)(),n=t.keyForKeydownEvent(e);return t.isModifierKey(n)},e.keystrokeForKeydownEvent=function(e){let t=(0,l.getKeyboardLayout)(),n=t.keyForKeydownEvent(e);if(!n||t.isModifierKey(n))return"";let i=[];return e.ctrlKey&&i.push("Ctrl"),e.altKey&&i.push("Alt"),e.shiftKey&&i.push("Shift"),e.metaKey&&o.Platform.IS_MAC&&i.push("Cmd"),i.push(n),i.join(" ")}}(d||(d={})),function(e){e.CHORD_TIMEOUT=1e3,e.createCommand=function(e){return{execute:e.execute,describedBy:h("function"==typeof e.describedBy?e.describedBy:{args:null,...e.describedBy},(()=>({args:null}))),label:h(e.label,i),mnemonic:h(e.mnemonic,s),icon:h(e.icon,m),iconClass:h(e.iconClass,i),iconLabel:h(e.iconLabel,i),caption:h(e.caption,i),usage:h(e.usage,i),className:h(e.className,i),dataset:h(e.dataset,c),isEnabled:e.isEnabled||a,isToggled:e.isToggled||l,isToggleable:e.isToggleable||!!e.isToggled,isVisible:e.isVisible||a}},e.createKeyBinding=function(e){return{keys:d.normalizeKeys(e),selector:y(e),command:e.command,args:e.args||r.JSONExt.emptyObject}},e.matchKeyBinding=function(e,t,n){let i=null,s=!1,r=1/0,a=0;for(let l=0,c=e.length;l<c;++l){let c=e[l],d=u(c.keys,t);if(0===d)continue;if(2===d){s||-1===g(c.selector,n)||(s=!0);continue}let m=g(c.selector,n);if(-1===m||m>r)continue;let h=o.Selector.calculateSpecificity(c.selector);(!i||m<r||h>=a)&&(i=c,r=m,a=h)}return{exact:i,partial:s}},e.replayKeyEvent=function(e){e.target.dispatchEvent(function(e){let t=document.createEvent("Event"),n=e.bubbles||!0,i=e.cancelable||!0;return t.initEvent(e.type||"keydown",n,i),t.key=e.key||"",t.keyCode=e.keyCode||0,t.which=e.keyCode||0,t.ctrlKey=e.ctrlKey||!1,t.altKey=e.altKey||!1,t.shiftKey=e.shiftKey||!1,t.metaKey=e.metaKey||!1,t.view=e.view||window,t}(e))},e.formatKey=function(e){return o.Platform.IS_MAC?t.hasOwnProperty(e)?t[e]:e:n.hasOwnProperty(e)?n[e]:e};const t={Backspace:"⌫",Tab:"⇥",Enter:"⏎",Shift:"⇧",Ctrl:"⌃",Alt:"⌥",Escape:"⎋",PageUp:"⇞",PageDown:"⇟",End:"↘",Home:"↖",ArrowLeft:"←",ArrowUp:"↑",ArrowRight:"→",ArrowDown:"↓",Delete:"⌦",Cmd:"⌘"},n={Escape:"Esc",PageUp:"Page Up",PageDown:"Page Down",ArrowLeft:"Left",ArrowUp:"Up",ArrowRight:"Right",ArrowDown:"Down",Delete:"Del"},i=()=>"",s=()=>-1,a=()=>!0,l=()=>!1,c=()=>({}),m=()=>{};function h(e,t){return void 0===e?t:"function"==typeof e?e:()=>e}function y(e){if(-1!==e.selector.indexOf(","))throw new Error(`Selector cannot contain commas: ${e.selector}`);if(!o.Selector.isValid(e.selector))throw new Error(`Invalid selector: ${e.selector}`);return e.selector}function u(e,t){if(e.length<t.length)return 0;for(let n=0,i=t.length;n<i;++n)if(e[n]!==t[n])return 0;return e.length>t.length?2:1}function g(e,t){let n=t.target,i=t.currentTarget;for(let t=0;null!==n;n=n.parentElement,++t){if(n.hasAttribute("data-lm-suppress-shortcuts"))return-1;if(o.Selector.matches(n,e))return t;if(n===i)return-1}return-1}}(i||(i={}))}}]);
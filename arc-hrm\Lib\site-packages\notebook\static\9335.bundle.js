"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[9335],{89335:(e,a,t)=>{t.r(a),t.d(a,{default:()=>c});var r=t(43833),d=t(36768),o=t(71677),i=t(68239),m=t(20998),s=t(55721);const n="@jupyterlab/metadataform-extension:metadataforms";var l;!function(e){e.loadSettingsMetadataForm=async function(e,a,t,r,d){var o;let i,l={};function c(e){l={},e.properties.metadataforms.default=Object.keys(a.plugins).map((e=>{var t;const r=null!==(t=a.plugins[e].schema["jupyter.lab.metadataforms"])&&void 0!==t?t:[];return r.forEach((a=>{a._origin=e})),l[e]=r,r})).concat([e["jupyter.lab.metadataforms"]]).reduce(((e,a)=>(a.forEach((a=>{const t=e.find((e=>e.id===a.id));if(t){for(let[e,r]of Object.entries(a.metadataSchema.properties))t.metadataSchema.properties[e]=r;if(a.metadataSchema.required&&(t.metadataSchema.required?t.metadataSchema.required.concat(a.metadataSchema.required):t.metadataSchema.required=a.metadataSchema.required),a.metadataSchema.allOf&&(t.metadataSchema.allOf?t.metadataSchema.allOf.concat(a.metadataSchema.allOf):t.metadataSchema.allOf=a.metadataSchema.allOf),a.uiSchema){t.uiSchema||(t.uiSchema={});for(let[e,r]of Object.entries(a.uiSchema))t.uiSchema[e]=r}if(a.metadataOptions){t.metadataOptions||(t.metadataOptions={});for(let[e,r]of Object.entries(a.metadataOptions))t.metadataOptions[e]=r}}else e.push(a)})),e)),[])}a.transform(n,{compose:e=>{var a,t,r,d;i||(i=m.JSONExt.deepCopy(e.schema),c(i));const o=null!==(r=null===(t=null===(a=i.properties)||void 0===a?void 0:a.metadataforms)||void 0===t?void 0:t.default)&&void 0!==r?r:[],s={metadataforms:null!==(d=e.data.user.metadataforms)&&void 0!==d?d:[]},n={metadataforms:o.concat(s.metadataforms)};return e.data={composite:n,user:s},e},fetch:e=>(i||(i=m.JSONExt.deepCopy(e.schema),c(i)),{data:e.data,id:e.id,raw:e.raw,schema:i,version:e.version})}),i=null;const f=await a.load(n),u=new s.MetadataFormProvider;for(let e of f.composite.metadataforms){let a={},i=m.JSONExt.deepCopy(e.metadataSchema),n={};e.uiSchema&&(n=m.JSONExt.deepCopy(e.uiSchema));for(let[e,t]of Object.entries(i.properties))t.default&&(a[e]||(a[e]={}),a[e].default=t.default);if(e.metadataOptions)for(let[t,r]of Object.entries(e.metadataOptions))if(r.cellTypes&&(a[t]||(a[t]={}),a[t].cellTypes=r.cellTypes),r.metadataLevel&&(a[t]||(a[t]={}),a[t].level=r.metadataLevel),void 0!==r.writeDefault&&(a[t]||(a[t]={}),a[t].writeDefault=r.writeDefault),r.customRenderer){const e=d.getRenderer(r.customRenderer);void 0!==e&&(n[t]||(n[t]={}),e.fieldRenderer?n[t]["ui:field"]=e.fieldRenderer:n[t]["ui:widget"]=e.widgetRenderer)}t.addSection({sectionName:e.id,rank:e.rank,label:null!==(o=e.label)&&void 0!==o?o:e.id});const l=new s.MetadataFormWidget({metadataSchema:i,metaInformation:a,uiSchema:n,pluginId:e._origin,translator:r,showModified:e.showModified});t.addItem({section:e.id,tool:l}),u.add(e.id,l)}return u}}(l||(l={}));const c={id:n,description:"Provides the metadata form registry.",autoStart:!0,requires:[r.INotebookTools,o.ITranslator,i.IFormRendererRegistry,d.ISettingRegistry],provides:s.IMetadataFormProvider,activate:async(e,a,t,r,d)=>await l.loadSettingsMetadataForm(e,d,a,t,r)}}}]);
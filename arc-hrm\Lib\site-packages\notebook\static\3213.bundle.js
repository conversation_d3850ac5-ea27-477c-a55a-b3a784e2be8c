"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[3213],{53213:(e,t,s)=>{s.r(t),s.d(t,{ITerminal:()=>o,ITerminalTracker:()=>n,Terminal:()=>l});var i=s(20998);const n=new i.Token("@jupyterlab/terminal:ITerminalTracker","A widget tracker for terminals.\n  Use this if you want to be able to iterate over and interact with terminals\n  created by the application.");var o;!function(e){e.defaultOptions={theme:"inherit",fontFamily:'<PERSON><PERSON>, Consolas, "DejaVu Sans Mono", monospace',fontSize:13,lineHeight:1,scrollback:1e3,shutdownOnClose:!1,closeOnExit:!0,cursorBlink:!0,initialCommand:"",screenReaderMode:!1,pasteWithCtrlV:!0,autoFit:!0,macOptionIsMeta:!1}}(o||(o={}));var r,a=s(71677),h=s(18395),c=s(49503),d=s(31516);class l extends d.Widget{constructor(e,t={},s){super(),this._needsResize=!0,this._offsetWidth=-1,this._offsetHeight=-1,this._isReady=!1,this._ready=new i.PromiseDelegate,this._termOpened=!1,s=s||a.nullTranslator,this._trans=s.load("jupyterlab"),this.session=e,this._options={...o.defaultOptions,...t};const{theme:n,...h}=this._options,c={theme:r.getXTermTheme(n),...h};this.addClass("jp-Terminal"),this._setThemeAttribute(n);let d="";const l=(e,t)=>{"stdout"===t.type&&t.content&&(d+=t.content[0])};e.messageReceived.connect(l),e.disposed.connect((()=>{this.getOption("closeOnExit")&&this.dispose()}),this),r.createTerminal(c).then((([t,s])=>{this._term=t,this._fitAddon=s,this._initializeTerm(),this.id="jp-Terminal-"+r.id++,this.title.label=this._trans.__("Terminal"),this._isReady=!0,this._ready.resolve(),d&&this._term.write(d),e.messageReceived.disconnect(l),e.messageReceived.connect(this._onMessage,this),"connected"===e.connectionStatus?this._initialConnection():e.connectionStatusChanged.connect(this._initialConnection,this),this.update()})).catch((e=>{console.error("Failed to create a terminal.\n",e),this._ready.reject(e)}))}get ready(){return this._ready.promise}getOption(e){return this._options[e]}setOption(e,t){if("theme"===e||this._options[e]!==t&&"initialCommand"!==e){switch(this._options[e]=t,e){case"fontFamily":this._term.options.fontFamily=t;break;case"fontSize":this._term.options.fontSize=t;break;case"lineHeight":this._term.options.lineHeight=t;break;case"screenReaderMode":this._term.options.screenReaderMode=t;break;case"scrollback":this._term.options.scrollback=t;break;case"theme":this._term.options.theme={...r.getXTermTheme(t)},this._setThemeAttribute(t);break;case"macOptionIsMeta":this._term.options.macOptionIsMeta=t}this._needsResize=!0,this.update()}}dispose(){this.session.isDisposed||this.getOption("shutdownOnClose")&&this.session.shutdown().catch((e=>{console.error(`Terminal not shut down: ${e}`)})),this.ready.then((()=>{this._term.dispose()})),super.dispose()}async refresh(){!this.isDisposed&&this._isReady&&(await this.session.reconnect(),this._term.clear())}hasSelection(){return!(this.isDisposed||!this._isReady)&&this._term.hasSelection()}paste(e){if(!this.isDisposed&&this._isReady)return this._term.paste(e)}getSelection(){return!this.isDisposed&&this._isReady?this._term.getSelection():null}processMessage(e){super.processMessage(e),"fit-request"===e.type&&this.onFitRequest(e)}onAfterAttach(e){this.update()}onAfterShow(e){this.update()}onResize(e){this._offsetWidth=e.width,this._offsetHeight=e.height,this._needsResize=!0,this.update()}onUpdateRequest(e){var t;this.isVisible&&this.isAttached&&this._isReady&&(this._termOpened||(this._term.open(this.node),null===(t=this._term.element)||void 0===t||t.classList.add("jp-Terminal-body"),this._termOpened=!0),this._needsResize&&this._resizeTerminal())}onFitRequest(e){const t=d.Widget.ResizeMessage.UnknownSize;c.MessageLoop.sendMessage(this,t)}onActivateRequest(e){var t;null===(t=this._term)||void 0===t||t.focus()}_initialConnection(){this.isDisposed||"connected"===this.session.connectionStatus&&(this.title.label=this._trans.__("Terminal %1",this.session.name),this._setSessionSize(),this._options.initialCommand&&this.session.send({type:"stdin",content:[this._options.initialCommand+"\r"]}),this.session.connectionStatusChanged.disconnect(this._initialConnection,this))}_initializeTerm(){const e=this._term;e.onData((e=>{this.isDisposed||this.session.send({type:"stdin",content:[e]})})),e.onTitleChange((e=>{this.title.label=e})),h.Platform.IS_MAC||e.attachCustomKeyEventHandler((t=>!(t.ctrlKey&&"c"===t.key&&e.hasSelection()||t.ctrlKey&&"v"===t.key&&this._options.pasteWithCtrlV)))}_onMessage(e,t){switch(t.type){case"stdout":t.content&&this._term.write(t.content[0]);break;case"disconnect":this._term.write("\r\n\r\n[Finished… Term Session]\r\n")}}_resizeTerminal(){this._options.autoFit&&this._fitAddon.fit(),-1===this._offsetWidth&&(this._offsetWidth=this.node.offsetWidth),-1===this._offsetHeight&&(this._offsetHeight=this.node.offsetHeight),this._setSessionSize(),this._needsResize=!1}_setSessionSize(){const e=[this._term.rows,this._term.cols,this._offsetHeight,this._offsetWidth];this.isDisposed||this.session.send({type:"set_size",content:e})}_setThemeAttribute(e){this.isDisposed||this.node.setAttribute("data-term-theme",e?e.toLowerCase():"inherit")}}!function(e){e.id=0,e.lightTheme={foreground:"#000",background:"#fff",cursor:"#616161",cursorAccent:"#F5F5F5",selectionBackground:"rgba(97, 97, 97, 0.3)",selectionInactiveBackground:"rgba(189, 189, 189, 0.3)"},e.darkTheme={foreground:"#fff",background:"#000",cursor:"#fff",cursorAccent:"#000",selectionBackground:"rgba(255, 255, 255, 0.3)",selectionInactiveBackground:"rgba(238, 238, 238, 0.3)"},e.inheritTheme=()=>({foreground:getComputedStyle(document.body).getPropertyValue("--jp-ui-font-color0").trim(),background:getComputedStyle(document.body).getPropertyValue("--jp-layout-color0").trim(),cursor:getComputedStyle(document.body).getPropertyValue("--jp-ui-font-color1").trim(),cursorAccent:getComputedStyle(document.body).getPropertyValue("--jp-ui-inverse-font-color0").trim(),selectionBackground:getComputedStyle(document.body).getPropertyValue("--jp-layout-color3").trim(),selectionInactiveBackground:getComputedStyle(document.body).getPropertyValue("--jp-layout-color2").trim()}),e.getXTermTheme=function(t){switch(t){case"light":return e.lightTheme;case"dark":return e.darkTheme;default:return e.inheritTheme()}}}(r||(r={})),function(e){let t,i,n,o,r=!1;function a(e){let t=new o;e.loadAddon(t),r&&t.onContextLoss((s=>{console.debug("WebGL context lost - reinitialize Xtermjs renderer."),t.dispose(),a(e)}))}e.createTerminal=async function(e){var h;if(!t){r=function(){const e=document.createElement("canvas"),t=e.getContext("webgl")||e.getContext("experimental-webgl");try{return t instanceof WebGLRenderingContext}catch(e){return!1}}();const[e,a,c,d]=await Promise.all([s.e(4997).then(s.t.bind(s,94997,23)),s.e(2758).then(s.t.bind(s,72758,23)),r?s.e(6467).then(s.t.bind(s,6467,23)):s.e(2094).then(s.t.bind(s,12094,23)),s.e(4928).then(s.t.bind(s,44928,23))]);t=e.Terminal,i=a.FitAddon,o=null!==(h=c.WebglAddon)&&void 0!==h?h:c.CanvasAddon,n=d.WebLinksAddon}const c=new t(e);a(c);const d=new i;return c.loadAddon(d),c.loadAddon(new n),[c,d]}}(r||(r={}))}}]);
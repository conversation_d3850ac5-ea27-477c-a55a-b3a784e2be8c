"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[7603],{97603:(e,t,n)=>{n.r(t),n.d(t,{Commands:()=>S,default:()=>R,tabSpaceStatus:()=>L});var o=n(3053),r=n(12982),a=n(31536),i=n(52337),d=n(76867),c=n(46122),l=n(3486),s=n(97713),u=n(89421),m=n(389),g=n(12729),f=n(97104),p=n(36768),v=n(34276),h=n(87749),C=n(71677),b=n(68239),y=n(33625),w=n(83861),_=n(35260),x=n(38639);const k="notebook:toggle-autoclosing-brackets",T="console:toggle-autoclosing-brackets";var I;!function(e){e.createNew="fileeditor:create-new",e.createNewMarkdown="fileeditor:create-new-markdown-file",e.changeFontSize="fileeditor:change-font-size",e.lineNumbers="fileeditor:toggle-line-numbers",e.currentLineNumbers="fileeditor:toggle-current-line-numbers",e.lineWrap="fileeditor:toggle-line-wrap",e.currentLineWrap="fileeditor:toggle-current-line-wrap",e.changeTabs="fileeditor:change-tabs",e.matchBrackets="fileeditor:toggle-match-brackets",e.currentMatchBrackets="fileeditor:toggle-current-match-brackets",e.autoClosingBrackets="fileeditor:toggle-autoclosing-brackets",e.autoClosingBracketsUniversal="fileeditor:toggle-autoclosing-brackets-universal",e.createConsole="fileeditor:create-console",e.replaceSelection="fileeditor:replace-selection",e.restartConsole="fileeditor:restart-console",e.runCode="fileeditor:run-code",e.runAllCode="fileeditor:run-all",e.markdownPreview="fileeditor:markdown-preview",e.undo="fileeditor:undo",e.redo="fileeditor:redo",e.cut="fileeditor:cut",e.copy="fileeditor:copy",e.paste="fileeditor:paste",e.selectAll="fileeditor:select-all",e.invokeCompleter="completer:invoke-file",e.selectCompleter="completer:select-file",e.openCodeViewer="code-viewer:open",e.changeTheme="fileeditor:change-theme",e.changeLanguage="fileeditor:change-language",e.find="fileeditor:find",e.goToLine="fileeditor:go-to-line"}(I||(I={}));const E="Editor";var S;!function(e){let t={},n=!0;function o(e){e.editor.setOptions({...t,scrollPastEnd:n})}function i(e){const t=e.getSelection(),{start:n,end:o}=t;return n.column!==o.column||n.line!==o.line}function d(e){const t=e.getSelection(),n=e.getOffsetAt(t.start),o=e.getOffsetAt(t.end);return e.model.sharedModel.getSource().substring(n,o)}async function c(e,t,n="txt"){const o=await e.execute("docmanager:new-untitled",{path:t,type:"file",ext:n});if(null!=o){const t=await e.execute("docmanager:open",{path:o.path,factory:E});return t.isUntitled=!0,t}}function l(e,t){e.add({command:I.createNew,category:t.__("Other"),rank:1})}function s(e,t){e.add({command:I.createNewMarkdown,category:t.__("Other"),rank:2})}function u(e,t){const n=t.__("Text Editor"),o=I.changeTabs;e.addItem({command:o,args:{size:4},category:n});for(const t of[1,2,4,8]){const r={size:t};e.addItem({command:o,args:r,category:n})}}function m(e,t){const n=t.__("Text Editor");e.addItem({command:I.createNew,args:{isPalette:!0},category:n})}function g(e,t){const n=t.__("Text Editor");e.addItem({command:I.createNewMarkdown,args:{isPalette:!0},category:n})}function f(e,t){const n=t.__("Text Editor"),o=I.changeFontSize;let r={delta:1};e.addItem({command:o,args:r,category:n}),r={delta:-1},e.addItem({command:o,args:r,category:n})}function p(e,t,n){const o=e=>n()&&e.context&&!!t.find((t=>{var n;return(null===(n=t.sessionContext.session)||void 0===n?void 0:n.path)===e.context.path}));e.runMenu.codeRunners.restart.add({id:I.restartConsole,isEnabled:o}),e.runMenu.codeRunners.run.add({id:I.runCode,isEnabled:o}),e.runMenu.codeRunners.runAll.add({id:I.runAllCode,isEnabled:o})}e.updateSettings=function(e,o){var r;t=null!==(r=e.get("editorConfig").composite)&&void 0!==r?r:{},n=e.get("scrollPasteEnd").composite,o.notifyCommandChanged(I.lineNumbers),o.notifyCommandChanged(I.currentLineNumbers),o.notifyCommandChanged(I.lineWrap),o.notifyCommandChanged(I.currentLineWrap),o.notifyCommandChanged(I.changeTabs),o.notifyCommandChanged(I.matchBrackets),o.notifyCommandChanged(I.currentMatchBrackets),o.notifyCommandChanged(I.autoClosingBrackets),o.notifyCommandChanged(I.changeLanguage)},e.updateTracker=function(e){e.forEach((e=>{o(e.content)}))},e.updateWidget=o,e.addCommands=function(e,n,o,l,s,u,m,g,f,p,v){e.addCommand(I.changeFontSize,{execute:e=>{var o;const r=Number(e.delta);if(Number.isNaN(r))return void console.error(`${I.changeFontSize}: delta arg must be a number`);const a=window.getComputedStyle(document.documentElement),i=parseInt(a.getPropertyValue("--jp-code-font-size"),10),d=(null!==(o=t.customStyles.fontSize)&&void 0!==o?o:g.baseConfiguration.customStyles.fontSize)||i;return t.fontSize=d+r,n.set(l,"editorConfig",t).catch((e=>{console.error(`Failed to set ${l}: ${e.message}`)}))},label:e=>{const t=Number(e.delta);return Number.isNaN(t)&&console.error(`${I.changeFontSize}: delta arg must be a number`),t>0?e.isMenu?o.__("Increase Text Editor Font Size"):o.__("Increase Font Size"):e.isMenu?o.__("Decrease Text Editor Font Size"):o.__("Decrease Font Size")}}),e.addCommand(I.lineNumbers,{execute:async()=>{var e;t.lineNumbers=!(null!==(e=t.lineNumbers)&&void 0!==e?e:g.baseConfiguration.lineNumbers);try{return await n.set(l,"editorConfig",t)}catch(e){console.error(`Failed to set ${l}: ${e.message}`)}},isEnabled:s,isToggled:()=>{var e;return null!==(e=t.lineNumbers)&&void 0!==e?e:g.baseConfiguration.lineNumbers},label:o.__("Show Line Numbers")}),e.addCommand(I.currentLineNumbers,{label:o.__("Show Line Numbers"),caption:o.__("Show the line numbers for the current file."),execute:()=>{const e=u.currentWidget;if(!e)return;const t=!e.content.editor.getOption("lineNumbers");e.content.editor.setOption("lineNumbers",t)},isEnabled:s,isToggled:()=>{var e;const t=u.currentWidget;return null!==(e=null==t?void 0:t.content.editor.getOption("lineNumbers"))&&void 0!==e&&e}}),e.addCommand(I.lineWrap,{execute:async e=>{var o;t.lineWrap=null!==(o=e.mode)&&void 0!==o&&o;try{return await n.set(l,"editorConfig",t)}catch(e){console.error(`Failed to set ${l}: ${e.message}`)}},isEnabled:s,isToggled:e=>{var n,o;return(null!==(n=e.mode)&&void 0!==n&&n)===(null!==(o=t.lineWrap)&&void 0!==o?o:g.baseConfiguration.lineWrap)},label:o.__("Word Wrap")}),e.addCommand(I.currentLineWrap,{label:o.__("Wrap Words"),caption:o.__("Wrap words for the current file."),execute:()=>{const e=u.currentWidget;if(!e)return;const t=e.content.editor.getOption("lineWrap");e.content.editor.setOption("lineWrap",!t)},isEnabled:s,isToggled:()=>{var e;const t=u.currentWidget;return null!==(e=null==t?void 0:t.content.editor.getOption("lineWrap"))&&void 0!==e&&e}}),e.addCommand(I.changeTabs,{label:e=>{var t;return e.size?o._p("v4","Spaces: %1",null!==(t=e.size)&&void 0!==t?t:""):o.__("Indent with Tab")},execute:async e=>{var o;t.indentUnit=void 0!==e.size?(null!==(o=e.size)&&void 0!==o?o:"4").toString():"Tab";try{return await n.set(l,"editorConfig",t)}catch(e){console.error(`Failed to set ${l}: ${e.message}`)}},isToggled:e=>{var n;const o=null!==(n=t.indentUnit)&&void 0!==n?n:g.baseConfiguration.indentUnit;return e.size?e.size===o:"Tab"==o}}),e.addCommand(I.matchBrackets,{execute:async()=>{var e;t.matchBrackets=!(null!==(e=t.matchBrackets)&&void 0!==e?e:g.baseConfiguration.matchBrackets);try{return await n.set(l,"editorConfig",t)}catch(e){console.error(`Failed to set ${l}: ${e.message}`)}},label:o.__("Match Brackets"),isEnabled:s,isToggled:()=>{var e;return null!==(e=t.matchBrackets)&&void 0!==e?e:g.baseConfiguration.matchBrackets}}),e.addCommand(I.currentMatchBrackets,{label:o.__("Match Brackets"),caption:o.__("Change match brackets for the current file."),execute:()=>{const e=u.currentWidget;if(!e)return;const t=!e.content.editor.getOption("matchBrackets");e.content.editor.setOption("matchBrackets",t)},isEnabled:s,isToggled:()=>{var e;const t=u.currentWidget;return null!==(e=null==t?void 0:t.content.editor.getOption("matchBrackets"))&&void 0!==e&&e}}),e.addCommand(I.autoClosingBrackets,{execute:async e=>{var o,r;t.autoClosingBrackets=!!(null!==(o=e.force)&&void 0!==o?o:!(null!==(r=t.autoClosingBrackets)&&void 0!==r?r:g.baseConfiguration.autoClosingBrackets));try{return await n.set(l,"editorConfig",t)}catch(e){console.error(`Failed to set ${l}: ${e.message}`)}},label:o.__("Auto Close Brackets in Text Editor"),isToggled:()=>{var e;return null!==(e=t.autoClosingBrackets)&&void 0!==e?e:g.baseConfiguration.autoClosingBrackets}}),e.addCommand(I.autoClosingBracketsUniversal,{execute:()=>{e.isToggled(I.autoClosingBrackets)||e.isToggled(k)||e.isToggled(T)?(e.execute(I.autoClosingBrackets,{force:!1}),e.execute(k,{force:!1}),e.execute(T,{force:!1})):(e.execute(I.autoClosingBrackets,{force:!0}),e.execute(k,{force:!0}),e.execute(T,{force:!0}))},label:o.__("Auto Close Brackets"),isToggled:()=>e.isToggled(I.autoClosingBrackets)||e.isToggled(k)||e.isToggled(T)}),e.addCommand(I.changeTheme,{label:e=>{var n,r,a,i;return null!==(i=null!==(a=null!==(r=null!==(n=e.displayName)&&void 0!==n?n:e.theme)&&void 0!==r?r:t.theme)&&void 0!==a?a:g.baseConfiguration.theme)&&void 0!==i?i:o.__("Editor Theme")},execute:async e=>{var o;t.theme=null!==(o=e.theme)&&void 0!==o?o:t.theme;try{return await n.set(l,"editorConfig",t)}catch(e){console.error(`Failed to set theme - ${e.message}`)}},isToggled:e=>{var n;return e.theme===(null!==(n=t.theme)&&void 0!==n?n:g.baseConfiguration.theme)}}),e.addCommand(I.find,{label:o.__("Find…"),execute:()=>{const e=u.currentWidget;e&&e.content.editor.execCommand(_.findNext)},isEnabled:s}),e.addCommand(I.goToLine,{label:o.__("Go to Line…"),execute:e=>{const t=u.currentWidget;if(!t)return;const n=t.content.editor,o=e.line,r=e.column;void 0!==o||void 0!==r?n.setCursorPosition({line:(null!=o?o:1)-1,column:(null!=r?r:1)-1}):n.execCommand(_.gotoLine)},isEnabled:s}),e.addCommand(I.changeLanguage,{label:e=>{var t,n;return null!==(n=null!==(t=e.displayName)&&void 0!==t?t:e.name)&&void 0!==n?n:o.__("Change editor language.")},execute:e=>{var t;const n=e.name,o=u.currentWidget;if(n&&o){const e=f.findByName(n);e&&(Array.isArray(e.mime)?o.content.model.mimeType=null!==(t=e.mime[0])&&void 0!==t?t:a.IEditorMimeTypeService.defaultMimeType:o.content.model.mimeType=e.mime)}},isEnabled:s,isToggled:e=>{const t=u.currentWidget;if(!t)return!1;const n=t.content.model.mimeType,o=f.findByMIME(n),r=o&&o.name;return e.name===r}}),e.addCommand(I.replaceSelection,{execute:e=>{var t,n;const o=e.text||"",r=u.currentWidget;r&&(null===(n=(t=r.content.editor).replaceSelection)||void 0===n||n.call(t,o))},isEnabled:s,label:o.__("Replace Selection in Editor")}),e.addCommand(I.createConsole,{execute:t=>{const n=u.currentWidget;if(n)return function(e,t){return async function(n,o){var r,a,i;const d=o||{},c=await e.execute("console:create",{activate:d.activate,name:null===(r=n.context.contentsModel)||void 0===r?void 0:r.name,path:n.context.path,preferredLanguage:n.context.model.defaultKernelLanguage||(null!==(i=null===(a=t.findByFileName(n.context.path))||void 0===a?void 0:a.name)&&void 0!==i?i:""),ref:n.id,insertMode:"split-bottom"});n.context.pathChanged.connect(((e,t)=>{var o;c.session.setPath(t),c.session.setName(null===(o=n.context.contentsModel)||void 0===o?void 0:o.name)}))}}(e,f)(n,t)},isEnabled:s,icon:b.consoleIcon,label:o.__("Create Console for Editor")}),e.addCommand(I.restartConsole,{execute:async()=>{var e;const t=null===(e=u.currentWidget)||void 0===e?void 0:e.content;if(!t||null===p)return;const n=p.find((e=>{var n;return(null===(n=e.sessionContext.session)||void 0===n?void 0:n.path)===t.context.path}));return n?v.restart(n.sessionContext):void 0},label:o.__("Restart Kernel"),isEnabled:()=>null!==p&&s()}),e.addCommand(I.runCode,{execute:()=>{var t;const n=null===(t=u.currentWidget)||void 0===t?void 0:t.content;if(!n)return;let o="";const r=n.editor,a=n.context.path,i=x.PathExt.extname(a),d=r.getSelection(),{start:c,end:l}=d;let s=c.column!==l.column||c.line!==l.line;if(s){const e=r.getOffsetAt(d.start),t=r.getOffsetAt(d.end);o=r.model.sharedModel.getSource().substring(e,t)}else if(x.MarkdownCodeBlocks.isMarkdown(i)){const e=r.model.sharedModel.getSource(),t=x.MarkdownCodeBlocks.findMarkdownCodeBlocks(e);for(const e of t)if(e.startLine<=c.line&&c.line<=e.endLine){o=e.code,s=!0;break}}if(!s){o=r.getLine(d.start.line);const e=r.getCursorPosition();if(e.line+1===r.lineCount){const e=r.model.sharedModel.getSource();r.model.sharedModel.setSource(e+"\n")}r.setCursorPosition({line:e.line+1,column:e.column})}return o?e.execute("console:inject",{activate:!1,code:o,path:a}):Promise.resolve(void 0)},isEnabled:s,label:o.__("Run Selected Code")}),e.addCommand(I.runAllCode,{execute:()=>{var t;const n=null===(t=u.currentWidget)||void 0===t?void 0:t.content;if(!n)return;let o="";const r=n.editor.model.sharedModel.getSource(),a=n.context.path,i=x.PathExt.extname(a);if(x.MarkdownCodeBlocks.isMarkdown(i)){const e=x.MarkdownCodeBlocks.findMarkdownCodeBlocks(r);for(const t of e)o+=t.code}else o=r;return o?e.execute("console:inject",{activate:!1,code:o,path:a}):Promise.resolve(void 0)},isEnabled:s,label:o.__("Run All Code")}),e.addCommand(I.markdownPreview,{execute:()=>{const t=u.currentWidget;if(!t)return;const n=t.context.path;return e.execute("markdownviewer:open",{path:n,options:{mode:"split-right"}})},isVisible:()=>{const e=u.currentWidget;return e&&".md"===x.PathExt.extname(e.context.path)||!1},icon:b.markdownIcon,label:o.__("Show Markdown Preview")}),e.addCommand(I.createNew,{label:e=>{var t,n;return e.isPalette?null!==(t=e.paletteLabel)&&void 0!==t?t:o.__("New Text File"):null!==(n=e.launcherLabel)&&void 0!==n?n:o.__("Text File")},caption:e=>{var t;return null!==(t=e.caption)&&void 0!==t?t:o.__("Create a new text file")},icon:e=>{var t;return e.isPalette?void 0:b.LabIcon.resolve({icon:null!==(t=e.iconName)&&void 0!==t?t:b.textEditorIcon})},execute:t=>{var n;const o=t.cwd||m.model.path;return c(e,o,null!==(n=t.fileExt)&&void 0!==n?n:"txt")}}),e.addCommand(I.createNewMarkdown,{label:e=>e.isPalette?o.__("New Markdown File"):o.__("Markdown File"),caption:o.__("Create a new markdown file"),icon:e=>e.isPalette?void 0:b.markdownIcon,execute:t=>{const n=t.cwd||m.model.path;return c(e,n,"md")}}),e.addCommand(I.undo,{execute:()=>{var e;const t=null===(e=u.currentWidget)||void 0===e?void 0:e.content;t&&t.editor.undo()},isEnabled:()=>{var e;if(!s())return!1;const t=null===(e=u.currentWidget)||void 0===e?void 0:e.content;return!!t&&t.editor.model.sharedModel.canUndo()},icon:b.undoIcon.bindprops({stylesheet:"menuItem"}),label:o.__("Undo")}),e.addCommand(I.redo,{execute:()=>{var e;const t=null===(e=u.currentWidget)||void 0===e?void 0:e.content;t&&t.editor.redo()},isEnabled:()=>{var e;if(!s())return!1;const t=null===(e=u.currentWidget)||void 0===e?void 0:e.content;return!!t&&t.editor.model.sharedModel.canRedo()},icon:b.redoIcon.bindprops({stylesheet:"menuItem"}),label:o.__("Redo")}),e.addCommand(I.cut,{execute:()=>{var e;const t=null===(e=u.currentWidget)||void 0===e?void 0:e.content;if(!t)return;const n=t.editor,o=d(n);r.Clipboard.copyToSystem(o),n.replaceSelection&&n.replaceSelection("")},isEnabled:()=>{var e;if(!s())return!1;const t=null===(e=u.currentWidget)||void 0===e?void 0:e.content;return!!t&&i(t.editor)},icon:b.cutIcon.bindprops({stylesheet:"menuItem"}),label:o.__("Cut")}),e.addCommand(I.copy,{execute:()=>{var e;const t=null===(e=u.currentWidget)||void 0===e?void 0:e.content;if(!t)return;const n=d(t.editor);r.Clipboard.copyToSystem(n)},isEnabled:()=>{var e;if(!s())return!1;const t=null===(e=u.currentWidget)||void 0===e?void 0:e.content;return!!t&&i(t.editor)},icon:b.copyIcon.bindprops({stylesheet:"menuItem"}),label:o.__("Copy")}),e.addCommand(I.paste,{execute:async()=>{var e;const t=null===(e=u.currentWidget)||void 0===e?void 0:e.content;if(!t)return;const n=t.editor,o=window.navigator.clipboard,r=await o.readText();r&&n.replaceSelection&&n.replaceSelection(r)},isEnabled:()=>{var e;return Boolean(s()&&(null===(e=u.currentWidget)||void 0===e?void 0:e.content))},icon:b.pasteIcon.bindprops({stylesheet:"menuItem"}),label:o.__("Paste")}),e.addCommand(I.selectAll,{execute:()=>{var e;const t=null===(e=u.currentWidget)||void 0===e?void 0:e.content;t&&t.editor.execCommand(w.selectAll)},isEnabled:()=>{var e;return Boolean(s()&&(null===(e=u.currentWidget)||void 0===e?void 0:e.content))},label:o.__("Select All")})},e.addCompleterCommands=function(e,t,n,o){const r=(null!=o?o:C.nullTranslator).load("jupyterlab");e.addCommand(I.invokeCompleter,{label:r.__("Display the completion helper."),execute:()=>{const e=t.currentWidget&&t.currentWidget.id;if(e)return n.invoke(e)}}),e.addCommand(I.selectCompleter,{label:r.__("Select the completion suggestion."),execute:()=>{const e=t.currentWidget&&t.currentWidget.id;if(e)return n.select(e)}}),e.addKeyBinding({command:I.selectCompleter,keys:["Enter"],selector:".jp-FileEditor .jp-mod-completer-active"})},e.addLauncherItems=function(e,t){l(e,t),s(e,t)},e.addCreateNewToLauncher=l,e.addCreateNewMarkdownToLauncher=s,e.addKernelLanguageLauncherItems=function(e,t,n){for(let o of n)e.add({command:I.createNew,category:t.__("Other"),rank:3,args:o})},e.addPaletteItems=function(e,t){u(e,t),m(e,t),g(e,t),f(e,t)},e.addChangeTabsCommandsToPalette=u,e.addCreateNewCommandToPalette=m,e.addCreateNewMarkdownCommandToPalette=g,e.addChangeFontSizeCommandsToPalette=f,e.addKernelLanguagePaletteItems=function(e,t,n){const o=t.__("Text Editor");for(let t of n)e.addItem({command:I.createNew,args:{...t,isPalette:!0},category:o})},e.addMenuItems=function(e,t,n,o){e.editMenu.undoers.redo.add({id:I.redo,isEnabled:o}),e.editMenu.undoers.undo.add({id:I.undo,isEnabled:o}),e.viewMenu.editorViewers.toggleLineNumbers.add({id:I.currentLineNumbers,isEnabled:o}),e.viewMenu.editorViewers.toggleMatchBrackets.add({id:I.currentMatchBrackets,isEnabled:o}),e.viewMenu.editorViewers.toggleWordWrap.add({id:I.currentLineWrap,isEnabled:o}),e.fileMenu.consoleCreators.add({id:I.createConsole,isEnabled:o}),n&&p(e,n,o)},e.addKernelLanguageMenuItems=function(e,t){for(let n of t)e.fileMenu.newMenu.addItem({command:I.createNew,args:n,rank:31})},e.addCodeRunnersToRunMenu=p,e.addOpenCodeViewerCommand=function(e,t,n,o){e.commands.addCommand(I.openCodeViewer,{label:o.__("Open Code Viewer"),execute:i=>(async i=>{var d;const c=t.factoryService.newDocumentEditor;let l=i.mimeType;!l&&i.extension&&(l=t.mimeTypeService.getMimeTypeByFilePath(`temp.${i.extension.replace(/\\.$/,"")}`));const s=a.CodeViewerWidget.createCodeViewer({factory:e=>c(e),content:i.content,mimeType:l});s.title.label=i.label||o.__("Code Viewer"),s.title.caption=s.title.label;const u=(0,y.find)(e.docRegistry.fileTypes(),(e=>!!l&&e.mimeTypes.includes(l)));s.title.icon=null!==(d=null==u?void 0:u.icon)&&void 0!==d?d:b.textEditorIcon,i.widgetId&&(s.id=i.widgetId);const m=new r.MainAreaWidget({content:s});return await n.add(m),e.shell.add(m,"main"),s})(i)})}}(S||(S={}));const M={id:"@jupyterlab/fileeditor-extension:editor-syntax-status",description:"Adds a file editor syntax status widget.",autoStart:!0,requires:[u.IEditorTracker,i.IEditorLanguageRegistry,o.ILabShell,C.ITranslator],optional:[v.IStatusBar],activate:(e,t,n,o,r,a)=>{if(!a)return;const i=new u.EditorSyntaxStatus({commands:e.commands,languages:n,translator:r});o.currentChanged.connect((()=>{const e=o.currentWidget;e&&t.has(e)&&i.model&&(i.model.editor=e.content.editor)})),a.registerStatusItem(M.id,{item:i,align:"left",rank:0,isActive:()=>!!o.currentWidget&&!!t.currentWidget&&o.currentWidget===t.currentWidget})}},W={activate:function(e,t,n,o,a,i,d,c,l,s,m,g,f,p,v,h,b){const y=W.id,w=null!=h?h:C.nullTranslator,_=null!=f?f:new r.SessionContextDialogs({translator:w}),x=w.load("jupyterlab");let k;v&&(k=(0,r.createToolbarFactory)(v,d,E,y,w));const T=new u.FileEditorFactory({editorServices:t,factoryOptions:{name:E,label:x.__("Editor"),fileTypes:["markdown","*"],defaultFor:["markdown","*"],toolbarFactory:k,translator:w}}),{commands:M,restored:L,shell:N}=e,P=new r.WidgetTracker({namespace:"editor"}),B=()=>null!==P.currentWidget&&P.currentWidget===N.currentWidget,F=new Map([["python",[{fileExt:"py",iconName:"ui-components:python",launcherLabel:x.__("Python File"),paletteLabel:x.__("New Python File"),caption:x.__("Create a new Python file")}]],["julia",[{fileExt:"jl",iconName:"ui-components:julia",launcherLabel:x.__("Julia File"),paletteLabel:x.__("New Julia File"),caption:x.__("Create a new Julia file")}]],["R",[{fileExt:"r",iconName:"ui-components:r-kernel",launcherLabel:x.__("R File"),paletteLabel:x.__("New R File"),caption:x.__("Create a new R file")}]]]);if(g&&g.restore(P,{command:"docmanager:open",args:e=>({path:e.context.path,factory:E}),name:e=>e.context.path}),Promise.all([d.load(y),L]).then((([e])=>{var t,n,r;if(m){const e=null===(t=m.viewMenu.items.find((e=>{var t;return"submenu"===e.type&&"jp-mainmenu-view-codemirror-language"===(null===(t=e.submenu)||void 0===t?void 0:t.id)})))||void 0===t?void 0:t.submenu;e&&o.getLanguages().sort(((e,t)=>{const n=e.name,o=t.name;return n.localeCompare(o)})).forEach((t=>{0!==t.name.toLowerCase().indexOf("brainf")&&e.addItem({command:I.changeLanguage,args:{...t}})}));const i=null===(n=m.settingsMenu.items.find((e=>{var t;return"submenu"===e.type&&"jp-mainmenu-settings-codemirror-theme"===(null===(t=e.submenu)||void 0===t?void 0:t.id)})))||void 0===n?void 0:n.submenu;if(i)for(const e of a.themes)i.addItem({command:I.changeTheme,args:{theme:e.name,displayName:null!==(r=e.displayName)&&void 0!==r?r:e.name}});m.editMenu.goToLiners.add({id:I.goToLine,isEnabled:e=>null!==P.currentWidget&&P.has(e)})}S.updateSettings(e,M),S.updateTracker(P),e.changed.connect((()=>{S.updateSettings(e,M),S.updateTracker(P)}))})).catch((e=>{console.error(e.message),S.updateTracker(P)})),b){const e=b.getRenderer("@jupyterlab/codemirror-extension:plugin.defaultConfig");e&&b.addRenderer("@jupyterlab/fileeditor-extension:plugin.editorConfig",e)}T.widgetCreated.connect(((e,t)=>{t.context.pathChanged.connect((()=>{P.save(t)})),P.add(t),S.updateWidget(t.content)})),e.docRegistry.addWidgetFactory(T),P.widgetAdded.connect(((e,t)=>{S.updateWidget(t.content)})),S.addCommands(e.commands,d,x,y,B,P,i,n,o,c,_);const R=new r.WidgetTracker({namespace:"codeviewer"});return g&&g.restore(R,{command:I.openCodeViewer,args:e=>({content:e.content.content,label:e.content.title.label,mimeType:e.content.mimeType,widgetId:e.content.id}),name:e=>e.content.id}),S.addOpenCodeViewerCommand(e,t,R,x),s&&S.addLauncherItems(s,x),l&&S.addPaletteItems(l,x),m&&S.addMenuItems(m,P,c,B),(async()=>{var t,n;const o=e.serviceManager.kernelspecs;await o.ready;let r=new Set;const a=null!==(n=null===(t=o.specs)||void 0===t?void 0:t.kernelspecs)&&void 0!==n?n:{};return Object.keys(a).forEach((e=>{const t=a[e];if(t){const e=F.get(t.language);null==e||e.forEach((e=>r.add(e)))}})),r})().then((e=>{s&&S.addKernelLanguageLauncherItems(s,x,e),l&&S.addKernelLanguagePaletteItems(l,x,e),m&&S.addKernelLanguageMenuItems(m,e)})).catch((e=>{console.error(e.message)})),p&&(p.add(new u.LaTeXTableOfContentsFactory(P)),p.add(new u.MarkdownTableOfContentsFactory(P)),p.add(new u.PythonTableOfContentsFactory(P))),P},id:"@jupyterlab/fileeditor-extension:plugin",description:"Provides the file editor widget tracker.",requires:[a.IEditorServices,i.IEditorExtensionRegistry,i.IEditorLanguageRegistry,i.IEditorThemeRegistry,s.IDefaultFileBrowser,p.ISettingRegistry],optional:[c.IConsoleTracker,r.ICommandPalette,m.ILauncher,f.IMainMenu,o.ILayoutRestorer,r.ISessionContextDialogs,h.ITableOfContentsRegistry,r.IToolbarWidgetRegistry,C.ITranslator,b.IFormRendererRegistry],provides:u.IEditorTracker,autoStart:!0},L={id:"@jupyterlab/fileeditor-extension:tab-space-status",description:"Adds a file editor indentation status widget.",autoStart:!0,requires:[u.IEditorTracker,i.IEditorExtensionRegistry,p.ISettingRegistry,C.ITranslator],optional:[v.IStatusBar],activate:(e,t,n,o,r,a)=>{const i=r.load("jupyterlab");if(!a)return;const d=new b.MenuSvg({commands:e.commands}),c="fileeditor:change-tabs",{shell:l}=e,s={name:i.__("Indent with Tab")};d.addItem({command:c,args:s});for(const e of["1","2","4","8"]){const t={size:e,name:i._p("v4","Spaces: %1",e)};d.addItem({command:c,args:t})}const m=new u.TabSpaceStatus({menu:d,translator:r}),g=e=>{var t,o,r;m.model.indentUnit=null!==(r=null!==(o=null===(t=e.get("editorConfig").composite)||void 0===t?void 0:t.indentUnit)&&void 0!==o?o:n.baseConfiguration.indentUnit)&&void 0!==r?r:null};Promise.all([o.load("@jupyterlab/fileeditor-extension:plugin"),e.restored]).then((([e])=>{g(e),e.changed.connect(g)})),a.registerStatusItem("@jupyterlab/fileeditor-extension:tab-space-status",{item:m,align:"right",rank:1,isActive:()=>!!l.currentWidget&&t.has(l.currentWidget)})}},N={id:"@jupyterlab/fileeditor-extension:cursor-position",description:"Adds a file editor cursor position status widget.",activate:(e,t,n)=>{n.addEditorProvider((e=>Promise.resolve(e&&t.has(e)?e.content.editor:null)))},requires:[u.IEditorTracker,a.IPositionModel],autoStart:!0},P={id:"@jupyterlab/fileeditor-extension:completer",description:"Adds the completer capability to the file editor.",requires:[u.IEditorTracker],optional:[d.ICompletionProviderManager,C.ITranslator,r.ISanitizer],activate:function(e,t,n,o,a){if(!n)return;S.addCompleterCommands(e.commands,t,n,o);const i=e.serviceManager.sessions,d=null!=a?a:new r.Sanitizer,c=new Map,l=async(e,t)=>{const o={editor:t.content.editor,widget:t};await n.updateCompleter(o);const r=(e,o)=>{const r=c.get(t.id),a=(0,y.find)(o,(e=>e.path===t.context.path));if(a){if(r&&r.id===a.id)return;r&&(c.delete(t.id),r.dispose());const e=i.connectTo({model:a}),o={editor:t.content.editor,widget:t,session:e,sanitizer:d};n.updateCompleter(o).catch(console.error),c.set(t.id,e)}else r&&(c.delete(t.id),r.dispose())};r(0,Array.from(i.running())),i.runningChanged.connect(r),t.disposed.connect((()=>{i.runningChanged.disconnect(r);const e=c.get(t.id);e&&(c.delete(t.id),e.dispose())}))};t.widgetAdded.connect(l),n.activeProvidersChanged.connect((()=>{t.forEach((e=>{l(0,e).catch(console.error)}))}))},autoStart:!0},B={id:"@jupyterlab/fileeditor-extension:search",description:"Adds search capability to the file editor.",requires:[l.ISearchProviderRegistry],autoStart:!0,activate:(e,t)=>{t.add("jp-fileeditorSearchProvider",u.FileEditorSearchProvider)}},F={id:"@jupyterlab/fileeditor-extension:language-server",description:"Adds Language Server capability to the file editor.",requires:[u.IEditorTracker,g.ILSPDocumentConnectionManager,g.ILSPFeatureManager,g.ILSPCodeExtractorsManager,g.IWidgetLSPAdapterTracker],activate:function(e,t,n,o,r,a){t.widgetAdded.connect((async(t,i)=>{const d=new u.FileEditorAdapter(i,{connectionManager:n,featureManager:o,foreignCodeExtractorsManager:r,docRegistry:e.docRegistry});a.add(d)}))},autoStart:!0},R=[W,N,P,F,B,M,L]}}]);
(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[2866],{24598:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ActivityMonitor=void 0;const o=n(81997);t.ActivityMonitor=class{constructor(e){this._timer=-1,this._timeout=-1,this._isDisposed=!1,this._activityStopped=new o.Signal(this),e.signal.connect(this._onSignalFired,this),this._timeout=e.timeout||1e3}get activityStopped(){return this._activityStopped}get timeout(){return this._timeout}set timeout(e){this._timeout=e}get isDisposed(){return this._isDisposed}dispose(){this._isDisposed||(this._isDisposed=!0,o.Signal.clearData(this))}_onSignalFired(e,t){clearTimeout(this._timer),this._sender=e,this._args=t,this._timer=setTimeout((()=>{this._activityStopped.emit({sender:this._sender,args:this._args})}),this._timeout)}}},2866:function(e,t,n){"use strict";var o=this&&this.__createBinding||(Object.create?function(e,t,n,o){void 0===o&&(o=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,o,r)}:function(e,t,n,o){void 0===o&&(o=n),e[o]=t[n]}),r=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||o(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),r(n(24598),t),r(n(51632),t),r(n(79844),t),r(n(94741),t),r(n(86083),t),r(n(12361),t),r(n(82053),t),r(n(84157),t),r(n(59635),t),r(n(34410),t)},51632:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},79844:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.LruCache=void 0,t.LruCache=class{constructor(e={}){this._map=new Map,this._maxSize=(null==e?void 0:e.maxSize)||128}get size(){return this._map.size}clear(){this._map.clear()}get(e){const t=this._map.get(e)||null;return null!=t&&(this._map.delete(e),this._map.set(e,t)),t}set(e,t){this._map.size>=this._maxSize&&this._map.delete(this._map.keys().next().value),this._map.set(e,t)}}},94741:(e,t)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.MarkdownCodeBlocks=void 0,function(e){e.CODE_BLOCK_MARKER="```";const t=[".markdown",".mdown",".mkdn",".md",".mkd",".mdwn",".mdtxt",".mdtext",".text",".txt",".Rmd"];class n{constructor(e){this.startLine=e,this.code="",this.endLine=-1}}e.MarkdownCodeBlock=n,e.isMarkdown=function(e){return t.indexOf(e)>-1},e.findMarkdownCodeBlocks=function(t){if(!t||""===t)return[];const o=t.split("\n"),r=[];let i=null;for(let t=0;t<o.length;t++){const s=o[t],a=0===s.indexOf(e.CODE_BLOCK_MARKER),c=null!=i;if(a||c)if(c)i&&(a?(i.endLine=t-1,r.push(i),i=null):i.code+=s+"\n");else{i=new n(t);const o=s.indexOf(e.CODE_BLOCK_MARKER),a=s.lastIndexOf(e.CODE_BLOCK_MARKER);o!==a&&(i.code=s.substring(o+e.CODE_BLOCK_MARKER.length,a),i.endLine=t,r.push(i),i=null)}}return r}}(n||(t.MarkdownCodeBlocks=n={}))},86083:function(__unused_webpack_module,exports,__webpack_require__){"use strict";var process=__webpack_require__(27061),__importDefault=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(exports,"__esModule",{value:!0}),exports.PageConfig=void 0;const coreutils_1=__webpack_require__(20998),minimist_1=__importDefault(__webpack_require__(66161)),url_1=__webpack_require__(34410);var PageConfig;(function(PageConfig){function getOption(name){if(configData)return configData[name]||getBodyData(name);configData=Object.create(null);let found=!1;if("undefined"!=typeof document&&document){const e=document.getElementById("jupyter-config-data");e&&(configData=JSON.parse(e.textContent||""),found=!0)}if(!found&&void 0!==process&&process.argv)try{const cli=(0,minimist_1.default)(process.argv.slice(2)),path=__webpack_require__(67425);let fullPath="";"jupyter-config-data"in cli?fullPath=path.resolve(cli["jupyter-config-data"]):"JUPYTER_CONFIG_DATA"in process.env&&(fullPath=path.resolve(process.env.JUPYTER_CONFIG_DATA)),fullPath&&(configData=eval("require")(fullPath))}catch(e){console.error(e)}if(coreutils_1.JSONExt.isObject(configData))for(const e in configData)"string"!=typeof configData[e]&&(configData[e]=JSON.stringify(configData[e]));else configData=Object.create(null);return configData[name]||getBodyData(name)}function setOption(e,t){const n=getOption(e);return configData[e]=t,n}function getBaseUrl(){return url_1.URLExt.normalize(getOption("baseUrl")||"/")}function getTreeUrl(){return url_1.URLExt.join(getBaseUrl(),getOption("treeUrl"))}function getShareUrl(){return url_1.URLExt.normalize(getOption("shareUrl")||getBaseUrl())}function getTreeShareUrl(){return url_1.URLExt.normalize(url_1.URLExt.join(getShareUrl(),getOption("treeUrl")))}function getUrl(e){var t,n,o,r;let i=e.toShare?getShareUrl():getBaseUrl();const s=null!==(t=e.mode)&&void 0!==t?t:getOption("mode"),a=null!==(n=e.workspace)&&void 0!==n?n:getOption("workspace"),c="single-document"===s?"doc":"lab";i=url_1.URLExt.join(i,c),a!==PageConfig.defaultWorkspace&&(i=url_1.URLExt.join(i,"workspaces",encodeURIComponent(null!==(o=getOption("workspace"))&&void 0!==o?o:PageConfig.defaultWorkspace)));const l=null!==(r=e.treePath)&&void 0!==r?r:getOption("treePath");return l&&(i=url_1.URLExt.join(i,"tree",url_1.URLExt.encodeParts(l))),i}function getWsUrl(e){let t=getOption("wsUrl");if(!t){if(0!==(e=e?url_1.URLExt.normalize(e):getBaseUrl()).indexOf("http"))return"";t="ws"+e.slice(4)}return url_1.URLExt.normalize(t)}function getNBConvertURL({path:e,format:t,download:n}){const o=url_1.URLExt.encodeParts(e),r=url_1.URLExt.join(getBaseUrl(),"nbconvert",t,o);return n?r+"?download=true":r}function getToken(){return getOption("token")||getBodyData("jupyterApiToken")}function getNotebookVersion(){const e=getOption("notebookVersion");return""===e?[0,0,0]:JSON.parse(e)}PageConfig.getOption=getOption,PageConfig.setOption=setOption,PageConfig.getBaseUrl=getBaseUrl,PageConfig.getTreeUrl=getTreeUrl,PageConfig.getShareUrl=getShareUrl,PageConfig.getTreeShareUrl=getTreeShareUrl,PageConfig.getUrl=getUrl,PageConfig.defaultWorkspace="default",PageConfig.getWsUrl=getWsUrl,PageConfig.getNBConvertURL=getNBConvertURL,PageConfig.getToken=getToken,PageConfig.getNotebookVersion=getNotebookVersion;let configData=null,Extension;function getBodyData(e){if("undefined"==typeof document||!document.body)return"";const t=document.body.dataset[e];return void 0===t?"":decodeURIComponent(t)}!function(e){function t(e){try{const t=getOption(e);if(t)return JSON.parse(t)}catch(t){console.warn(`Unable to parse ${e}.`,t)}return[]}e.deferred=t("deferredExtensions"),e.disabled=t("disabledExtensions"),e.isDeferred=function(t){const n=t.indexOf(":");let o="";return-1!==n&&(o=t.slice(0,n)),e.deferred.some((e=>e===t||o&&e===o))},e.isDisabled=function(t){const n=t.indexOf(":");let o="";return-1!==n&&(o=t.slice(0,n)),e.disabled.some((e=>e===t||o&&e===o))}}(Extension=PageConfig.Extension||(PageConfig.Extension={}))})(PageConfig||(exports.PageConfig=PageConfig={}))},12361:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PathExt=void 0;const o=n(67425);var r;!function(e){function t(e){return 0===e.indexOf("/")&&(e=e.slice(1)),e}e.join=function(...e){const n=o.posix.join(...e);return"."===n?"":t(n)},e.joinWithLeadingSlash=function(...e){const t=o.posix.join(...e);return"."===t?"":t},e.basename=function(e,t){return o.posix.basename(e,t)},e.dirname=function(e){const n=t(o.posix.dirname(e));return"."===n?"":n},e.extname=function(e){return o.posix.extname(e)},e.normalize=function(e){return""===e?"":t(o.posix.normalize(e))},e.resolve=function(...e){return t(o.posix.resolve(...e))},e.relative=function(e,n){return t(o.posix.relative(e,n))},e.normalizeExtension=function(e){return e.length>0&&0!==e.indexOf(".")&&(e=`.${e}`),e},e.removeSlash=t}(r||(t.PathExt=r={}))},82053:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.signalToPromise=void 0;const o=n(20998);t.signalToPromise=function(e,t){const n=new o.PromiseDelegate;function r(){e.disconnect(i)}function i(e,t){r(),n.resolve([e,t])}return e.connect(i),(null!=t?t:0)>0&&setTimeout((()=>{r(),n.reject(`Signal not emitted within ${t} ms.`)}),t),n.promise}},84157:(e,t)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.Text=void 0,function(e){e.jsIndexToCharIndex=function(e,t){return e},e.charIndexToJsIndex=function(e,t){return e},e.camelCase=function(e,t=!1){return e.replace(/^(\w)|[\s-_:]+(\w)/g,(function(e,n,o){return o?o.toUpperCase():t?n.toUpperCase():n.toLowerCase()}))},e.titleCase=function(e){return(e||"").toLowerCase().split(" ").map((e=>e.charAt(0).toUpperCase()+e.slice(1))).join(" ")}}(n||(t.Text=n={}))},59635:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Time=void 0;const n=[{name:"years",milliseconds:31536e6},{name:"months",milliseconds:2592e6},{name:"days",milliseconds:864e5},{name:"hours",milliseconds:36e5},{name:"minutes",milliseconds:6e4},{name:"seconds",milliseconds:1e3}];var o;!function(e){e.formatHuman=function(e){const t=document.documentElement.lang||"en",o=new Intl.RelativeTimeFormat(t,{numeric:"auto"}),r=new Date(e).getTime()-Date.now();for(let e of n){const t=Math.ceil(r/e.milliseconds);if(0!==t)return o.format(t,e.name)}return o.format(0,"seconds")},e.format=function(e){const t=document.documentElement.lang||"en";return new Intl.DateTimeFormat(t,{dateStyle:"short",timeStyle:"short"}).format(new Date(e))}}(o||(t.Time=o={}))},34410:function(e,t,n){"use strict";var o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.URLExt=void 0;const r=n(67425),i=o(n(80899));var s;!function(e){function t(e){if("undefined"!=typeof document&&document){const t=document.createElement("a");return t.href=e,t}return(0,i.default)(e)}function n(...e){let t=(0,i.default)(e[0],{});const n=""===t.protocol&&t.slashes;n&&(t=(0,i.default)(e[0],"https:"+e[0]));const o=`${n?"":t.protocol}${t.slashes?"//":""}${t.auth}${t.auth?"@":""}${t.host}`,s=r.posix.join(`${o&&"/"!==t.pathname[0]?"/":""}${t.pathname}`,...e.slice(1));return`${o}${"."===s?"":s}`}e.parse=t,e.getHostName=function(e){return(0,i.default)(e).hostname},e.normalize=function(e){return e&&t(e).toString()},e.join=n,e.encodeParts=function(e){return n(...e.split("/").map(encodeURIComponent))},e.objectToQueryString=function(e){const t=Object.keys(e).filter((e=>e.length>0));return t.length?"?"+t.map((t=>{const n=encodeURIComponent(String(e[t]));return t+(n?"="+n:"")})).join("&"):""},e.queryStringToObject=function(e){return e.replace(/^\?/,"").split("&").reduce(((e,t)=>{const[n,o]=t.split("=");return n.length>0&&(e[n]=decodeURIComponent(o||"")),e}),{})},e.isLocal=function(e,n=!1){const{protocol:o}=t(e);return(!o||0!==e.toLowerCase().indexOf(o))&&(n?0!==e.indexOf("//"):0!==e.indexOf("/"))}}(s||(t.URLExt=s={}))},66161:e=>{"use strict";function t(e){return"number"==typeof e||!!/^0x[0-9a-f]+$/i.test(e)||/^[-+]?(?:\d+(?:\.\d*)?|\.\d+)(e[-+]?\d+)?$/.test(e)}function n(e,t){return"constructor"===t&&"function"==typeof e[t]||"__proto__"===t}e.exports=function(e,o){o||(o={});var r={bools:{},strings:{},unknownFn:null};"function"==typeof o.unknown&&(r.unknownFn=o.unknown),"boolean"==typeof o.boolean&&o.boolean?r.allBools=!0:[].concat(o.boolean).filter(Boolean).forEach((function(e){r.bools[e]=!0}));var i={};function s(e){return i[e].some((function(e){return r.bools[e]}))}Object.keys(o.alias||{}).forEach((function(e){i[e]=[].concat(o.alias[e]),i[e].forEach((function(t){i[t]=[e].concat(i[e].filter((function(e){return t!==e})))}))})),[].concat(o.string).filter(Boolean).forEach((function(e){r.strings[e]=!0,i[e]&&[].concat(i[e]).forEach((function(e){r.strings[e]=!0}))}));var a=o.default||{},c={_:[]};function l(e,t,o){for(var i=e,s=0;s<t.length-1;s++){var a=t[s];if(n(i,a))return;void 0===i[a]&&(i[a]={}),i[a]!==Object.prototype&&i[a]!==Number.prototype&&i[a]!==String.prototype||(i[a]={}),i[a]===Array.prototype&&(i[a]=[]),i=i[a]}var c=t[t.length-1];n(i,c)||(i!==Object.prototype&&i!==Number.prototype&&i!==String.prototype||(i={}),i===Array.prototype&&(i=[]),void 0===i[c]||r.bools[c]||"boolean"==typeof i[c]?i[c]=o:Array.isArray(i[c])?i[c].push(o):i[c]=[i[c],o])}function u(e,n,o){if(!o||!r.unknownFn||function(e,t){return r.allBools&&/^--[^=]+$/.test(t)||r.strings[e]||r.bools[e]||i[e]}(e,o)||!1!==r.unknownFn(o)){var s=!r.strings[e]&&t(n)?Number(n):n;l(c,e.split("."),s),(i[e]||[]).forEach((function(e){l(c,e.split("."),s)}))}}Object.keys(r.bools).forEach((function(e){u(e,void 0!==a[e]&&a[e])}));var f=[];-1!==e.indexOf("--")&&(f=e.slice(e.indexOf("--")+1),e=e.slice(0,e.indexOf("--")));for(var p=0;p<e.length;p++){var h,d,g=e[p];if(/^--.+=/.test(g)){var m=g.match(/^--([^=]+)=([\s\S]*)$/);h=m[1];var _=m[2];r.bools[h]&&(_="false"!==_),u(h,_,g)}else if(/^--no-.+/.test(g))u(h=g.match(/^--no-(.+)/)[1],!1,g);else if(/^--.+/.test(g))h=g.match(/^--(.+)/)[1],void 0===(d=e[p+1])||/^(-|--)[^-]/.test(d)||r.bools[h]||r.allBools||i[h]&&s(h)?/^(true|false)$/.test(d)?(u(h,"true"===d,g),p+=1):u(h,!r.strings[h]||"",g):(u(h,d,g),p+=1);else if(/^-[^-]+/.test(g)){for(var v=g.slice(1,-1).split(""),b=!1,y=0;y<v.length;y++)if("-"!==(d=g.slice(y+2))){if(/[A-Za-z]/.test(v[y])&&"="===d[0]){u(v[y],d.slice(1),g),b=!0;break}if(/[A-Za-z]/.test(v[y])&&/-?\d+(\.\d*)?(e-?\d+)?$/.test(d)){u(v[y],d,g),b=!0;break}if(v[y+1]&&v[y+1].match(/\W/)){u(v[y],g.slice(y+2),g),b=!0;break}u(v[y],!r.strings[v[y]]||"",g)}else u(v[y],d,g);h=g.slice(-1)[0],b||"-"===h||(!e[p+1]||/^(-|--)[^-]/.test(e[p+1])||r.bools[h]||i[h]&&s(h)?e[p+1]&&/^(true|false)$/.test(e[p+1])?(u(h,"true"===e[p+1],g),p+=1):u(h,!r.strings[h]||"",g):(u(h,e[p+1],g),p+=1))}else if(r.unknownFn&&!1===r.unknownFn(g)||c._.push(r.strings._||!t(g)?g:Number(g)),o.stopEarly){c._.push.apply(c._,e.slice(p+1));break}}return Object.keys(a).forEach((function(e){var t,n,o;t=c,n=e.split("."),o=t,n.slice(0,-1).forEach((function(e){o=o[e]||{}})),n[n.length-1]in o||(l(c,e.split("."),a[e]),(i[e]||[]).forEach((function(t){l(c,t.split("."),a[e])})))})),o["--"]?c["--"]=f.slice():f.forEach((function(e){c._.push(e)})),c}},67425:(e,t,n)=>{"use strict";var o=n(27061);function r(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function i(e,t){for(var n,o="",r=0,i=-1,s=0,a=0;a<=e.length;++a){if(a<e.length)n=e.charCodeAt(a);else{if(47===n)break;n=47}if(47===n){if(i===a-1||1===s);else if(i!==a-1&&2===s){if(o.length<2||2!==r||46!==o.charCodeAt(o.length-1)||46!==o.charCodeAt(o.length-2))if(o.length>2){var c=o.lastIndexOf("/");if(c!==o.length-1){-1===c?(o="",r=0):r=(o=o.slice(0,c)).length-1-o.lastIndexOf("/"),i=a,s=0;continue}}else if(2===o.length||1===o.length){o="",r=0,i=a,s=0;continue}t&&(o.length>0?o+="/..":o="..",r=2)}else o.length>0?o+="/"+e.slice(i+1,a):o=e.slice(i+1,a),r=a-i-1;i=a,s=0}else 46===n&&-1!==s?++s:s=-1}return o}var s={resolve:function(){for(var e,t="",n=!1,s=arguments.length-1;s>=-1&&!n;s--){var a;s>=0?a=arguments[s]:(void 0===e&&(e=o.cwd()),a=e),r(a),0!==a.length&&(t=a+"/"+t,n=47===a.charCodeAt(0))}return t=i(t,!n),n?t.length>0?"/"+t:"/":t.length>0?t:"."},normalize:function(e){if(r(e),0===e.length)return".";var t=47===e.charCodeAt(0),n=47===e.charCodeAt(e.length-1);return 0!==(e=i(e,!t)).length||t||(e="."),e.length>0&&n&&(e+="/"),t?"/"+e:e},isAbsolute:function(e){return r(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var e,t=0;t<arguments.length;++t){var n=arguments[t];r(n),n.length>0&&(void 0===e?e=n:e+="/"+n)}return void 0===e?".":s.normalize(e)},relative:function(e,t){if(r(e),r(t),e===t)return"";if((e=s.resolve(e))===(t=s.resolve(t)))return"";for(var n=1;n<e.length&&47===e.charCodeAt(n);++n);for(var o=e.length,i=o-n,a=1;a<t.length&&47===t.charCodeAt(a);++a);for(var c=t.length-a,l=i<c?i:c,u=-1,f=0;f<=l;++f){if(f===l){if(c>l){if(47===t.charCodeAt(a+f))return t.slice(a+f+1);if(0===f)return t.slice(a+f)}else i>l&&(47===e.charCodeAt(n+f)?u=f:0===f&&(u=0));break}var p=e.charCodeAt(n+f);if(p!==t.charCodeAt(a+f))break;47===p&&(u=f)}var h="";for(f=n+u+1;f<=o;++f)f!==o&&47!==e.charCodeAt(f)||(0===h.length?h+="..":h+="/..");return h.length>0?h+t.slice(a+u):(a+=u,47===t.charCodeAt(a)&&++a,t.slice(a))},_makeLong:function(e){return e},dirname:function(e){if(r(e),0===e.length)return".";for(var t=e.charCodeAt(0),n=47===t,o=-1,i=!0,s=e.length-1;s>=1;--s)if(47===(t=e.charCodeAt(s))){if(!i){o=s;break}}else i=!1;return-1===o?n?"/":".":n&&1===o?"//":e.slice(0,o)},basename:function(e,t){if(void 0!==t&&"string"!=typeof t)throw new TypeError('"ext" argument must be a string');r(e);var n,o=0,i=-1,s=!0;if(void 0!==t&&t.length>0&&t.length<=e.length){if(t.length===e.length&&t===e)return"";var a=t.length-1,c=-1;for(n=e.length-1;n>=0;--n){var l=e.charCodeAt(n);if(47===l){if(!s){o=n+1;break}}else-1===c&&(s=!1,c=n+1),a>=0&&(l===t.charCodeAt(a)?-1==--a&&(i=n):(a=-1,i=c))}return o===i?i=c:-1===i&&(i=e.length),e.slice(o,i)}for(n=e.length-1;n>=0;--n)if(47===e.charCodeAt(n)){if(!s){o=n+1;break}}else-1===i&&(s=!1,i=n+1);return-1===i?"":e.slice(o,i)},extname:function(e){r(e);for(var t=-1,n=0,o=-1,i=!0,s=0,a=e.length-1;a>=0;--a){var c=e.charCodeAt(a);if(47!==c)-1===o&&(i=!1,o=a+1),46===c?-1===t?t=a:1!==s&&(s=1):-1!==t&&(s=-1);else if(!i){n=a+1;break}}return-1===t||-1===o||0===s||1===s&&t===o-1&&t===n+1?"":e.slice(t,o)},format:function(e){if(null===e||"object"!=typeof e)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return function(e,t){var n=t.dir||t.root,o=t.base||(t.name||"")+(t.ext||"");return n?n===t.root?n+o:n+"/"+o:o}(0,e)},parse:function(e){r(e);var t={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return t;var n,o=e.charCodeAt(0),i=47===o;i?(t.root="/",n=1):n=0;for(var s=-1,a=0,c=-1,l=!0,u=e.length-1,f=0;u>=n;--u)if(47!==(o=e.charCodeAt(u)))-1===c&&(l=!1,c=u+1),46===o?-1===s?s=u:1!==f&&(f=1):-1!==s&&(f=-1);else if(!l){a=u+1;break}return-1===s||-1===c||0===f||1===f&&s===c-1&&s===a+1?-1!==c&&(t.base=t.name=0===a&&i?e.slice(1,c):e.slice(a,c)):(0===a&&i?(t.name=e.slice(1,s),t.base=e.slice(1,c)):(t.name=e.slice(a,s),t.base=e.slice(a,c)),t.ext=e.slice(s,c)),a>0?t.dir=e.slice(0,a-1):i&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};s.posix=s,e.exports=s},27061:e=>{var t,n,o=e.exports={};function r(){throw new Error("setTimeout has not been defined")}function i(){throw new Error("clearTimeout has not been defined")}function s(e){if(t===setTimeout)return setTimeout(e,0);if((t===r||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(n){try{return t.call(null,e,0)}catch(n){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:r}catch(e){t=r}try{n="function"==typeof clearTimeout?clearTimeout:i}catch(e){n=i}}();var a,c=[],l=!1,u=-1;function f(){l&&a&&(l=!1,a.length?c=a.concat(c):u=-1,c.length&&p())}function p(){if(!l){var e=s(f);l=!0;for(var t=c.length;t;){for(a=c,c=[];++u<t;)a&&a[u].run();u=-1,t=c.length}a=null,l=!1,function(e){if(n===clearTimeout)return clearTimeout(e);if((n===i||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(e);try{return n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}(e)}}function h(e,t){this.fun=e,this.array=t}function d(){}o.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];c.push(new h(e,t)),1!==c.length||l||s(p)},h.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=d,o.addListener=d,o.once=d,o.off=d,o.removeListener=d,o.removeAllListeners=d,o.emit=d,o.prependListener=d,o.prependOnceListener=d,o.listeners=function(e){return[]},o.binding=function(e){throw new Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(e){throw new Error("process.chdir is not supported")},o.umask=function(){return 0}},88157:(e,t)=>{"use strict";var n=Object.prototype.hasOwnProperty;function o(e){try{return decodeURIComponent(e.replace(/\+/g," "))}catch(e){return null}}function r(e){try{return encodeURIComponent(e)}catch(e){return null}}t.stringify=function(e,t){t=t||"";var o,i,s=[];for(i in"string"!=typeof t&&(t="?"),e)if(n.call(e,i)){if((o=e[i])||null!=o&&!isNaN(o)||(o=""),i=r(i),o=r(o),null===i||null===o)continue;s.push(i+"="+o)}return s.length?t+s.join("&"):""},t.parse=function(e){for(var t,n=/([^=?#&]+)=?([^&]*)/g,r={};t=n.exec(e);){var i=o(t[1]),s=o(t[2]);null===i||null===s||i in r||(r[i]=s)}return r}},53096:e=>{"use strict";e.exports=function(e,t){if(t=t.split(":")[0],!(e=+e))return!1;switch(t){case"http":case"ws":return 80!==e;case"https":case"wss":return 443!==e;case"ftp":return 21!==e;case"gopher":return 70!==e;case"file":return!1}return 0!==e}},80899:(e,t,n)=>{"use strict";var o=n(53096),r=n(88157),i=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,s=/[\n\r\t]/g,a=/^[A-Za-z][A-Za-z0-9+-.]*:\/\//,c=/:\d+$/,l=/^([a-z][a-z0-9.+-]*:)?(\/\/)?([\\/]+)?([\S\s]*)/i,u=/^[a-zA-Z]:/;function f(e){return(e||"").toString().replace(i,"")}var p=[["#","hash"],["?","query"],function(e,t){return g(t.protocol)?e.replace(/\\/g,"/"):e},["/","pathname"],["@","auth",1],[NaN,"host",void 0,1,1],[/:(\d*)$/,"port",void 0,1],[NaN,"hostname",void 0,1,1]],h={hash:1,query:1};function d(e){var t,o=("undefined"!=typeof window?window:void 0!==n.g?n.g:"undefined"!=typeof self?self:{}).location||{},r={},i=typeof(e=e||o);if("blob:"===e.protocol)r=new _(unescape(e.pathname),{});else if("string"===i)for(t in r=new _(e,{}),h)delete r[t];else if("object"===i){for(t in e)t in h||(r[t]=e[t]);void 0===r.slashes&&(r.slashes=a.test(e.href))}return r}function g(e){return"file:"===e||"ftp:"===e||"http:"===e||"https:"===e||"ws:"===e||"wss:"===e}function m(e,t){e=(e=f(e)).replace(s,""),t=t||{};var n,o=l.exec(e),r=o[1]?o[1].toLowerCase():"",i=!!o[2],a=!!o[3],c=0;return i?a?(n=o[2]+o[3]+o[4],c=o[2].length+o[3].length):(n=o[2]+o[4],c=o[2].length):a?(n=o[3]+o[4],c=o[3].length):n=o[4],"file:"===r?c>=2&&(n=n.slice(2)):g(r)?n=o[4]:r?i&&(n=n.slice(2)):c>=2&&g(t.protocol)&&(n=o[4]),{protocol:r,slashes:i||g(r),slashesCount:c,rest:n}}function _(e,t,n){if(e=(e=f(e)).replace(s,""),!(this instanceof _))return new _(e,t,n);var i,a,c,l,h,v,b=p.slice(),y=typeof t,w=this,C=0;for("object"!==y&&"string"!==y&&(n=t,t=null),n&&"function"!=typeof n&&(n=r.parse),i=!(a=m(e||"",t=d(t))).protocol&&!a.slashes,w.slashes=a.slashes||i&&t.slashes,w.protocol=a.protocol||t.protocol||"",e=a.rest,("file:"===a.protocol&&(2!==a.slashesCount||u.test(e))||!a.slashes&&(a.protocol||a.slashesCount<2||!g(w.protocol)))&&(b[3]=[/(.*)/,"pathname"]);C<b.length;C++)"function"!=typeof(l=b[C])?(c=l[0],v=l[1],c!=c?w[v]=e:"string"==typeof c?~(h="@"===c?e.lastIndexOf(c):e.indexOf(c))&&("number"==typeof l[2]?(w[v]=e.slice(0,h),e=e.slice(h+l[2])):(w[v]=e.slice(h),e=e.slice(0,h))):(h=c.exec(e))&&(w[v]=h[1],e=e.slice(0,h.index)),w[v]=w[v]||i&&l[3]&&t[v]||"",l[4]&&(w[v]=w[v].toLowerCase())):e=l(e,w);n&&(w.query=n(w.query)),i&&t.slashes&&"/"!==w.pathname.charAt(0)&&(""!==w.pathname||""!==t.pathname)&&(w.pathname=function(e,t){if(""===e)return t;for(var n=(t||"/").split("/").slice(0,-1).concat(e.split("/")),o=n.length,r=n[o-1],i=!1,s=0;o--;)"."===n[o]?n.splice(o,1):".."===n[o]?(n.splice(o,1),s++):s&&(0===o&&(i=!0),n.splice(o,1),s--);return i&&n.unshift(""),"."!==r&&".."!==r||n.push(""),n.join("/")}(w.pathname,t.pathname)),"/"!==w.pathname.charAt(0)&&g(w.protocol)&&(w.pathname="/"+w.pathname),o(w.port,w.protocol)||(w.host=w.hostname,w.port=""),w.username=w.password="",w.auth&&(~(h=w.auth.indexOf(":"))?(w.username=w.auth.slice(0,h),w.username=encodeURIComponent(decodeURIComponent(w.username)),w.password=w.auth.slice(h+1),w.password=encodeURIComponent(decodeURIComponent(w.password))):w.username=encodeURIComponent(decodeURIComponent(w.auth)),w.auth=w.password?w.username+":"+w.password:w.username),w.origin="file:"!==w.protocol&&g(w.protocol)&&w.host?w.protocol+"//"+w.host:"null",w.href=w.toString()}_.prototype={set:function(e,t,n){var i=this;switch(e){case"query":"string"==typeof t&&t.length&&(t=(n||r.parse)(t)),i[e]=t;break;case"port":i[e]=t,o(t,i.protocol)?t&&(i.host=i.hostname+":"+t):(i.host=i.hostname,i[e]="");break;case"hostname":i[e]=t,i.port&&(t+=":"+i.port),i.host=t;break;case"host":i[e]=t,c.test(t)?(t=t.split(":"),i.port=t.pop(),i.hostname=t.join(":")):(i.hostname=t,i.port="");break;case"protocol":i.protocol=t.toLowerCase(),i.slashes=!n;break;case"pathname":case"hash":if(t){var s="pathname"===e?"/":"#";i[e]=t.charAt(0)!==s?s+t:t}else i[e]=t;break;case"username":case"password":i[e]=encodeURIComponent(t);break;case"auth":var a=t.indexOf(":");~a?(i.username=t.slice(0,a),i.username=encodeURIComponent(decodeURIComponent(i.username)),i.password=t.slice(a+1),i.password=encodeURIComponent(decodeURIComponent(i.password))):i.username=encodeURIComponent(decodeURIComponent(t))}for(var l=0;l<p.length;l++){var u=p[l];u[4]&&(i[u[1]]=i[u[1]].toLowerCase())}return i.auth=i.password?i.username+":"+i.password:i.username,i.origin="file:"!==i.protocol&&g(i.protocol)&&i.host?i.protocol+"//"+i.host:"null",i.href=i.toString(),i},toString:function(e){e&&"function"==typeof e||(e=r.stringify);var t,n=this,o=n.host,i=n.protocol;i&&":"!==i.charAt(i.length-1)&&(i+=":");var s=i+(n.protocol&&n.slashes||g(n.protocol)?"//":"");return n.username?(s+=n.username,n.password&&(s+=":"+n.password),s+="@"):n.password?(s+=":"+n.password,s+="@"):"file:"!==n.protocol&&g(n.protocol)&&!o&&"/"!==n.pathname&&(s+="@"),(":"===o[o.length-1]||c.test(n.hostname)&&!n.port)&&(o+=":"),s+=o+n.pathname,(t="object"==typeof n.query?e(n.query):n.query)&&(s+="?"!==t.charAt(0)?"?"+t:t),n.hash&&(s+=n.hash),s}},_.extractProtocol=m,_.location=d,_.trimLeft=f,_.qs=r,e.exports=_}}]);
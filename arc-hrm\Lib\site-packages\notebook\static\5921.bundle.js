"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[5921],{75921:(e,t,n)=>{n.r(t),n.d(t,{ITableOfContentsRegistry:()=>h,ITableOfContentsTracker:()=>d,TableOfContents:()=>g,TableOfContentsFactory:()=>l,TableOfContentsItem:()=>v,TableOfContentsModel:()=>p,TableOfContentsPanel:()=>b,TableOfContentsRegistry:()=>x,TableOfContentsTracker:()=>y,TableOfContentsTree:()=>C,TableOfContentsUtils:()=>i,TableOfContentsWidget:()=>T});var s={};n.r(s),n.d(s,{getHeadingId:()=>P,getHeadings:()=>I,isMarkdown:()=>$});var i={};n.r(i),n.d(i,{Markdown:()=>s,NUMBERING_CLASS:()=>H,addPrefix:()=>O,clearNumbering:()=>j,filterHeadings:()=>A,getHTMLHeadings:()=>E,getPrefix:()=>S,isHTML:()=>k});var a=n(38639);class l{constructor(e){this.tracker=e}isApplicable(e){return!!this.tracker.has(e)}createNew(e,t){const n=this._createNew(e,t),s=e.context,i=()=>{n.refresh().catch((e=>{console.error("Failed to update the table of contents.",e)}))},l=new a.ActivityMonitor({signal:s.model.contentChanged,timeout:1e3});l.activityStopped.connect(i);const o=()=>{n.title=a.PathExt.basename(s.localPath)};return s.pathChanged.connect(o),s.ready.then((()=>{o(),i()})).catch((e=>{console.error(`Failed to initiate headings for ${s.localPath}.`)})),e.disposed.connect((()=>{l.activityStopped.disconnect(i),s.pathChanged.disconnect(o)})),n}}var o=n(68239),r=n(20998),c=n(81997);const h=new r.Token("@jupyterlab/toc:ITableOfContentsRegistry","A service to register table of content factory."),d=new r.Token("@jupyterlab/toc:ITableOfContentsTracker","A widget tracker for table of contents.");var g,u;!function(e){e.defaultConfig={baseNumbering:1,maximalDepth:4,numberingH1:!0,numberHeaders:!1,includeOutput:!0,syncCollapseState:!1}}(g||(g={}));class p extends o.VDomModel{constructor(e,t){super(),this.widget=e,this._activeHeading=null,this._activeHeadingChanged=new c.Signal(this),this._collapseChanged=new c.Signal(this),this._configuration=null!=t?t:{...g.defaultConfig},this._headings=new Array,this._headingsChanged=new c.Signal(this),this._isActive=!1,this._isRefreshing=!1,this._needsRefreshing=!1}get activeHeading(){return this._activeHeading}get activeHeadingChanged(){return this._activeHeadingChanged}get collapseChanged(){return this._collapseChanged}get configuration(){return this._configuration}get headings(){return this._headings}get headingsChanged(){return this._headingsChanged}get isActive(){return this._isActive}set isActive(e){this._isActive=e,this._isActive&&!this.isAlwaysActive&&this.refresh().catch((e=>{console.error("Failed to refresh ToC model.",e)}))}get isAlwaysActive(){return!1}get supportedOptions(){return["maximalDepth"]}get title(){return this._title}set title(e){e!==this._title&&(this._title=e,this.stateChanged.emit())}async refresh(){if(this._isRefreshing)return this._needsRefreshing=!0,Promise.resolve();this._isRefreshing=!0;try{const e=await this.getHeadings();if(this._needsRefreshing)return this._needsRefreshing=!1,this._isRefreshing=!1,this.refresh();e&&!u.areHeadingsEqual(e,this._headings)&&(this._headings=e,this.stateChanged.emit(),this._headingsChanged.emit())}finally{this._isRefreshing=!1}}setActiveHeading(e,t=!0){this._activeHeading!==e&&(this._activeHeading=e,this.stateChanged.emit()),t&&this._activeHeadingChanged.emit(this._activeHeading)}setConfiguration(e){const t={...this._configuration,...e};r.JSONExt.deepEqual(this._configuration,t)||(this._configuration=t,this.refresh().catch((e=>{console.error("Failed to update the table of contents.",e)})))}toggleCollapse(e){var t,n;if(e.heading)e.heading.collapsed=null!==(t=e.collapsed)&&void 0!==t?t:!e.heading.collapsed,this.stateChanged.emit(),this._collapseChanged.emit(e.heading);else{const t=null!==(n=e.collapsed)&&void 0!==n?n:!this.headings.some((e=>{var t;return!(null!==(t=e.collapsed)&&void 0!==t&&t)}));this.headings.forEach((e=>e.collapsed=t)),this.stateChanged.emit(),this._collapseChanged.emit(null)}}}!function(e){e.areHeadingsEqual=function(e,t){if(e.length===t.length){for(let n=0;n<e.length;n++)if(e[n].level!==t[n].level||e[n].text!==t[n].text||e[n].prefix!==t[n].prefix)return!1;return!0}return!1}}(u||(u={}));var f,m=n(31516),_=n(78156);class v extends _.PureComponent{render(){const{children:e,isActive:t,heading:n,onCollapse:s,onMouseDown:i}=this.props;return _.createElement("li",{className:"jp-tocItem"},_.createElement("div",{className:"jp-tocItem-heading "+(t?"jp-tocItem-active":""),onMouseDown:e=>{e.defaultPrevented||(e.preventDefault(),i(n))}},_.createElement("button",{className:"jp-tocItem-collapser",onClick:e=>{e.preventDefault(),s(n)},style:{visibility:e?"visible":"hidden"}},n.collapsed?_.createElement(o.caretRightIcon.react,{tag:"span",width:"20px"}):_.createElement(o.caretDownIcon.react,{tag:"span",width:"20px"})),_.createElement("span",{className:"jp-tocItem-content",title:n.text,...n.dataset},n.prefix,n.text)),e&&!n.collapsed&&_.createElement("ol",null,e))}}class C extends _.PureComponent{render(){const{documentType:e}=this.props;return _.createElement("ol",{className:"jp-TableOfContents-content","data-document-type":e},this.buildTree())}buildTree(){if(0===this.props.headings.length)return[];const e=t=>{const n=this.props.headings,s=new Array,i=n[t];let a=t+1;for(;a<n.length&&!(n[a].level<=i.level);){const[t,n]=e(a);s.push(t),a=n}return[_.createElement(v,{key:`${i.level}-${t}-${i.text}`,isActive:!!this.props.activeHeading&&i===this.props.activeHeading,heading:i,onMouseDown:this.props.setActiveHeading,onCollapse:this.props.onCollapseChange},s.length?s:null),a]},t=new Array;let n=0;for(;n<this.props.headings.length;){const[s,i]=e(n);t.push(s),n=i}return t}}class T extends o.VDomRenderer{constructor(e){super(e.model),this._placeholderHeadline=e.placeholderHeadline,this._placeholderText=e.placeholderText}render(){return this.model&&0!==this.model.headings.length?_.createElement(C,{activeHeading:this.model.activeHeading,documentType:this.model.documentType,headings:this.model.headings,onCollapseChange:e=>{this.model.toggleCollapse({heading:e})},setActiveHeading:e=>{this.model.setActiveHeading(e)}}):_.createElement("div",{className:"jp-TableOfContents-placeholder"},_.createElement("div",{className:"jp-TableOfContents-placeholderContent"},_.createElement("h3",null,this._placeholderHeadline),_.createElement("p",null,this._placeholderText)))}}class b extends o.SidePanel{constructor(e){super({content:new m.Panel,translator:e}),this._model=null,this.addClass("jp-TableOfContents"),this._title=new f.Header(this._trans.__("Table of Contents")),this.header.addWidget(this._title),this._treeview=new T({placeholderHeadline:this._trans.__("No Headings"),placeholderText:this._trans.__("The table of contents shows headings in notebooks and supported files.")}),this._treeview.addClass("jp-TableOfContents-tree"),this.content.addWidget(this._treeview)}get model(){return this._model}set model(e){var t,n;this._model!==e&&(null===(t=this._model)||void 0===t||t.stateChanged.disconnect(this._onTitleChanged,this),this._model=e,this._model&&(this._model.isActive=this.isVisible),null===(n=this._model)||void 0===n||n.stateChanged.connect(this._onTitleChanged,this),this._onTitleChanged(),this._treeview.model=this._model)}onAfterHide(e){super.onAfterHide(e),this._model&&(this._model.isActive=!1)}onBeforeShow(e){super.onBeforeShow(e),this._model&&(this._model.isActive=!0)}_onTitleChanged(){var e,t;this._title.setTitle(null!==(t=null===(e=this._model)||void 0===e?void 0:e.title)&&void 0!==t?t:this._trans.__("Table of Contents"))}}!function(e){class t extends m.Widget{constructor(e){const t=document.createElement("h2");t.textContent=e,t.classList.add("jp-text-truncated"),super({node:t}),this._title=t}setTitle(e){this._title.textContent=e}}e.Header=t}(f||(f={}));var w=n(2549);class x{constructor(){this._generators=new Map,this._idCounter=0}getModel(e,t){for(const n of this._generators.values())if(n.isApplicable(e))return n.createNew(e,t)}add(e){const t=this._idCounter++;return this._generators.set(t,e),new w.DisposableDelegate((()=>{this._generators.delete(t)}))}}class y{constructor(){this.modelMapping=new WeakMap}add(e,t){this.modelMapping.set(e,t)}get(e){const t=this.modelMapping.get(e);return!t||t.isDisposed?null:t}}const H="numbering-entry";function A(e,t,n=[]){const s={...g.defaultConfig,...t},i=n;let a=i.length;const l=new Array;for(const t of e){if(t.skip)continue;const e=t.level;if(e>0&&e<=s.maximalDepth){const n=S(e,a,i,s);a=e,l.push({...t,prefix:n})}}return l}function k(e){return"text/html"===e}function E(e,t=!0){var n;const s=document.createElement("div");s.innerHTML=e;const i=new Array,a=s.querySelectorAll("h1, h2, h3, h4, h5, h6");for(const e of a){const t=parseInt(e.tagName[1],10);i.push({text:null!==(n=e.textContent)&&void 0!==n?n:"",level:t,id:null==e?void 0:e.getAttribute("id"),skip:e.classList.contains("jp-toc-ignore")||e.classList.contains("tocSkip")})}return i}function O(e,t,n){let s=e.querySelector(t);if(!s)return null;if(s.querySelector(`span.${H}`)){const i=e.querySelectorAll(t);for(const e of i)if(!e.querySelector(`span.${H}`)){s=e,M(e,n);break}}else M(s,n);return s}function S(e,t,n,s){const{baseNumbering:i,numberingH1:a,numberHeaders:l}=s;let o="";if(l){const s=a?1:2;if(e>t){for(let s=t;s<e-1;s++)n[s]=0;n[e-1]=e===s?i:1}else n[e-1]+=1,e<t&&n.splice(e);a?o=n.map((e=>null!=e?e:0)).join(".")+". ":n.length>1&&(o=n.slice(1).map((e=>null!=e?e:0)).join(".")+". ")}return o}function M(e,t){e.insertAdjacentHTML("afterbegin",`<span class="${H}">${t}</span>`)}function j(e){null==e||e.querySelectorAll(`span.${H}`).forEach((e=>{e.remove()}))}var R=n(12982),N=n(70856);async function P(e,t,n,s){try{const i=document.createElement("div");await(0,N.renderMarkdown)({markdownParser:e,host:i,source:t,trusted:!1,sanitizer:null!=s?s:new R.Sanitizer,shouldTypeset:!1,resolver:null,linkHandler:null,latexTypesetter:null});const a=i.querySelector(`h${n}`);return a?a.id:null}catch(e){console.error("Failed to parse a heading.",e)}return null}function I(e){const t=e.split("\n"),n=new Array;let s,i=0;if("---"===t[i])for(let e=i+1;e<t.length;e++)if("---"===t[e]){i=e+1;break}for(;i<t.length;i++){const e=t[i];if(""===e)continue;if(e.startsWith("```")&&(s=!s),s)continue;const a=q(e,t[i+1]);a&&n.push({...a,line:i})}return n}const D=["text/x-ipythongfm","text/x-markdown","text/x-gfm","text/markdown"];function $(e){return D.includes(e)}function q(e,t){let n=e.match(/^([#]{1,6}) (.*)/);return n?{text:L(n[2]),level:n[1].length,raw:e,skip:U.test(n[0])}:t&&(n=t.match(/^ {0,3}([=]{2,}|[-]{2,})\s*$/),n)?{text:L(e),level:"="===n[1][0]?1:2,raw:[e,t].join("\n"),skip:U.test(e)}:(n=e.match(/<h([1-6]).*>(.*)<\/h\1>/i),n?{text:n[2],level:parseInt(n[1],10),skip:U.test(n[0]),raw:e}:null)}function L(e){return e.replace(/\[(.+)\]\(.+\)/g,"$1")}const U=/<\w+\s(.*?\s)?class="(.*?\s)?(jp-toc-ignore|tocSkip)(\s.*?)?"(\s.*?)?>/}}]);
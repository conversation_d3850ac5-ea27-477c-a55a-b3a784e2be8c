(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[7957],{79504:(t,e,n)=>{"use strict";n.d(e,{Dp:()=>i,G:()=>o,JJ:()=>c,Z$:()=>r,kJ:()=>l,s7:()=>s});const r=t=>t[t.length-1],s=(t,e)=>{for(let n=0;n<e.length;n++)t.push(e[n])},i=Array.from,o=(t,e)=>{for(let n=0;n<t.length;n++)if(e(t[n],n,t))return!0;return!1},c=(t,e)=>{const n=new Array(t);for(let r=0;r<t;r++)n[r]=e(r,n);return n},l=Array.isArray},38828:(t,e,n)=>{"use strict";n.d(e,{Hi:()=>c,PP:()=>i,gB:()=>l,id:()=>o});var r=n(79504),s=n(36498);const i=(t,e,n=0)=>{try{for(;n<t.length;n++)t[n](...e)}finally{n<t.length&&i(t,e,n+1)}},o=t=>t,c=(t,e)=>{if(null==t||null==e)return((t,e)=>t===e)(t,e);if(t.constructor!==e.constructor)return!1;if(t===e)return!0;switch(t.constructor){case ArrayBuffer:t=new Uint8Array(t),e=new Uint8Array(e);case Uint8Array:if(t.byteLength!==e.byteLength)return!1;for(let n=0;n<t.length;n++)if(t[n]!==e[n])return!1;break;case Set:if(t.size!==e.size)return!1;for(const n of t)if(!e.has(n))return!1;break;case Map:if(t.size!==e.size)return!1;for(const n of t.keys())if(!e.has(n)||!c(t.get(n),e.get(n)))return!1;break;case Object:if(s.kE(t)!==s.kE(e))return!1;for(const n in t)if(!s.l$(t,n)||!c(t[n],e[n]))return!1;break;case Array:if(t.length!==e.length)return!1;for(let n=0;n<t.length;n++)if(!c(t[n],e[n]))return!1;break;default:return!1}return!0},l=(t,e)=>e.includes(t);r.kJ},22592:(t,e,n)=>{"use strict";n.d(e,{JG:()=>s,UI:()=>o,Ue:()=>r,Yj:()=>c,Yu:()=>i});const r=()=>new Map,s=t=>{const e=r();return t.forEach(((t,n)=>{e.set(n,t)})),e},i=(t,e,n)=>{let r=t.get(e);return void 0===r&&t.set(e,r=n()),r},o=(t,e)=>{const n=[];for(const[r,s]of t)n.push(e(s,r));return n},c=(t,e)=>{for(const[n,r]of t)if(e(r,n))return!0;return!1}},11182:(t,e,n)=>{"use strict";n.d(e,{Fp:()=>o,GR:()=>c,GW:()=>r,VV:()=>i,Wn:()=>s});const r=Math.floor,s=(Math.ceil,Math.abs),i=(Math.imul,Math.round,Math.log10,Math.log2,Math.log,Math.sqrt,(t,e)=>t<e?t:e),o=(t,e)=>t>e?t:e,c=(Number.isNaN,Math.pow,Math.sign,t=>0!==t?t<0:1/t<0)},36498:(t,e,n)=>{"use strict";n.d(e,{$m:()=>h,Ed:()=>i,f0:()=>r,kE:()=>o,l$:()=>l,xb:()=>c});const r=Object.assign,s=Object.keys,i=(t,e)=>{for(const n in t)e(t[n],n)},o=t=>s(t).length,c=t=>{for(const e in t)return!1;return!0},l=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),h=(t,e)=>t===e||o(t)===o(e)&&((t,e)=>{for(const n in t)if(!e(t[n],n))return!1;return!0})(t,((t,n)=>(void 0!==t||l(e,n))&&e[n]===t))},12330:(t,e,n)=>{"use strict";n.d(e,{y:()=>o});var r=n(22592),s=n(79287),i=n(79504);class o{constructor(){this._observers=r.Ue()}on(t,e){r.Yu(this._observers,t,s.Ue).add(e)}once(t,e){const n=(...r)=>{this.off(t,n),e(...r)};this.on(t,n)}off(t,e){const n=this._observers.get(t);void 0!==n&&(n.delete(e),0===n.size&&this._observers.delete(t))}emit(t,e){return i.Dp((this._observers.get(t)||r.Ue()).values()).forEach((t=>t(...e)))}destroy(){this._observers=r.Ue()}}},79287:(t,e,n)=>{"use strict";n.d(e,{Ue:()=>r});const r=()=>new Set},2431:(t,e,n)=>{"use strict";n.d(e,{ZG:()=>r});const r=Date.now},27061:t=>{var e,n,r=t.exports={};function s(){throw new Error("setTimeout has not been defined")}function i(){throw new Error("clearTimeout has not been defined")}function o(t){if(e===setTimeout)return setTimeout(t,0);if((e===s||!e)&&setTimeout)return e=setTimeout,setTimeout(t,0);try{return e(t,0)}catch(n){try{return e.call(null,t,0)}catch(n){return e.call(this,t,0)}}}!function(){try{e="function"==typeof setTimeout?setTimeout:s}catch(t){e=s}try{n="function"==typeof clearTimeout?clearTimeout:i}catch(t){n=i}}();var c,l=[],h=!1,a=-1;function d(){h&&c&&(h=!1,c.length?l=c.concat(l):a=-1,l.length&&u())}function u(){if(!h){var t=o(d);h=!0;for(var e=l.length;e;){for(c=l,l=[];++a<e;)c&&c[a].run();a=-1,e=l.length}c=null,h=!1,function(t){if(n===clearTimeout)return clearTimeout(t);if((n===i||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(t);try{return n(t)}catch(e){try{return n.call(null,t)}catch(e){return n.call(this,t)}}}(t)}}function g(t,e){this.fun=t,this.array=e}function f(){}r.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)e[n-1]=arguments[n];l.push(new g(t,e)),1!==l.length||h||o(u)},g.prototype.run=function(){this.fun.apply(null,this.array)},r.title="browser",r.browser=!0,r.env={},r.argv=[],r.version="",r.versions={},r.on=f,r.addListener=f,r.once=f,r.off=f,r.removeListener=f,r.removeAllListeners=f,r.emit=f,r.prependListener=f,r.prependOnceListener=f,r.listeners=function(t){return[]},r.binding=function(t){throw new Error("process.binding is not supported")},r.cwd=function(){return"/"},r.chdir=function(t){throw new Error("process.chdir is not supported")},r.umask=function(){return 0}},67957:(t,e,n)=>{"use strict";n.r(e),n.d(e,{AbsolutePosition:()=>Ge,AbstractConnector:()=>$t,AbstractStruct:()=>is,AbstractType:()=>fr,Array:()=>Nr,ContentAny:()=>fs,ContentBinary:()=>cs,ContentDeleted:()=>ls,ContentEmbed:()=>ds,ContentFormat:()=>us,ContentJSON:()=>gs,ContentString:()=>ps,ContentType:()=>Cs,Doc:()=>ce,GC:()=>os,ID:()=>Oe,Item:()=>Ms,Map:()=>Rr,PermanentUserData:()=>Je,RelativePosition:()=>We,Snapshot:()=>Ze,Text:()=>qr,Transaction:()=>_n,UndoManager:()=>In,UpdateEncoderV1:()=>ge,XmlElement:()=>es,XmlFragment:()=>ts,XmlHook:()=>rs,XmlText:()=>ss,YArrayEvent:()=>Ir,YEvent:()=>ir,YMapEvent:()=>Lr,YTextEvent:()=>Zr,YXmlEvent:()=>ns,applyUpdate:()=>be,applyUpdateV2:()=>ye,cleanupYTextFormatting:()=>Hr,compareIDs:()=>Ie,compareRelativePositions:()=>Xe,convertUpdateFormatV1ToV2:()=>nr,convertUpdateFormatV2ToV1:()=>rr,createAbsolutePositionFromRelativePosition:()=>Ke,createDeleteSet:()=>te,createDeleteSetFromStructStore:()=>ee,createDocFromSnapshot:()=>hn,createID:()=>Ne,createRelativePositionFromJSON:()=>je,createRelativePositionFromTypeIndex:()=>$e,createSnapshot:()=>rn,decodeRelativePosition:()=>He,decodeSnapshot:()=>nn,decodeSnapshotV2:()=>en,decodeStateVector:()=>Ce,decodeUpdate:()=>Pn,decodeUpdateV2:()=>Vn,diffUpdate:()=>Hn,diffUpdateV2:()=>Bn,emptySnapshot:()=>sn,encodeRelativePosition:()=>Be,encodeSnapshot:()=>tn,encodeSnapshotV2:()=>Qe,encodeStateAsUpdate:()=>Se,encodeStateAsUpdateV2:()=>_e,encodeStateVector:()=>ve,encodeStateVectorFromUpdate:()=>Yn,encodeStateVectorFromUpdateV2:()=>Wn,equalDeleteSets:()=>ie,equalSnapshots:()=>qe,findIndexSS:()=>pn,findRootTypeKey:()=>Pe,getItem:()=>wn,getState:()=>gn,getTypeChildren:()=>ur,isDeleted:()=>Xt,isParentOf:()=>Ve,iterateDeletedStructs:()=>Kt,logType:()=>Fe,logUpdate:()=>Ln,logUpdateV2:()=>Rn,mergeUpdates:()=>Jn,mergeUpdatesV2:()=>$n,obfuscateUpdate:()=>tr,obfuscateUpdateV2:()=>er,parseUpdateMeta:()=>Gn,parseUpdateMetaV2:()=>jn,readUpdate:()=>ke,readUpdateV2:()=>me,relativePositionToJSON:()=>Ye,snapshot:()=>on,snapshotContainsUpdate:()=>an,transact:()=>Tn,tryGc:()=>An,typeListToArraySnapshot:()=>mr,typeMapGetSnapshot:()=>xr});var r=n(12330),s=n(79504),i=n(11182),o=n(22592);const c=String.fromCharCode,l=(String.fromCodePoint,c(65535),/^\s*/g),h=/([A-Z])/g,a=(t,e)=>(t=>t.replace(l,""))(t.replace(h,(t=>`${e}${(t=>t.toLowerCase())(t)}`))),d="undefined"!=typeof TextEncoder?new TextEncoder:null,u=d?t=>d.encode(t):t=>{const e=unescape(encodeURIComponent(t)),n=e.length,r=new Uint8Array(n);for(let t=0;t<n;t++)r[t]=e.codePointAt(t);return r};let g="undefined"==typeof TextDecoder?null:new TextDecoder("utf-8",{fatal:!0,ignoreBOM:!0});g&&1===g.decode(new Uint8Array).length&&(g=null);let f=new class{constructor(){this.map=new Map}setItem(t,e){this.map.set(t,e)}getItem(t){return this.map.get(t)}},p=!0;try{"undefined"!=typeof localStorage&&(f=localStorage,p=!1)}catch(t){}const w=f;var m=n(38828),k=n(27061);const y=void 0!==k&&k.release&&/node|io\.js/.test(k.release.name);let b;"undefined"!=typeof navigator&&/Mac/.test(navigator.platform);const _=[],S=t=>(()=>{if(void 0===b)if(y){b=o.Ue();const t=k.argv;let e=null;for(let n=0;n<t.length;n++){const r=t[n];"-"===r[0]?(null!==e&&b.set(e,""),e=r):null!==e?(b.set(e,r),e=null):_.push(r)}null!==e&&b.set(e,"")}else"object"==typeof location?(b=o.Ue(),(location.search||"?").slice(1).split("&").forEach((t=>{if(0!==t.length){const[e,n]=t.split("=");b.set(`--${a(e,"-")}`,n),b.set(`-${a(e,"-")}`,n)}}))):b=o.Ue();return b})().has(t),E=t=>{return void 0===(e=y?k.env[t.toUpperCase()]:w.getItem(t))?null:e;var e};S("--"+"production")||E("production");const C=y&&m.gB(k.env.FORCE_COLOR,["true","1","2"]),D=!S("no-colors")&&(!y||k.stdout.isTTY||C)&&(!y||S("color")||C||null!==E("COLORTERM")||(E("TERM")||"").includes("color")),v=(t,e,n)=>new Uint8Array(t,e,n),A=64,U=128,T=127,M=Number.MAX_SAFE_INTEGER,x=(Number.MIN_SAFE_INTEGER,Number.isInteger||(t=>"number"==typeof t&&isFinite(t)&&i.GW(t)===t));Number.isNaN,Number.parseInt;class O{constructor(){this.cpos=0,this.cbuf=new Uint8Array(100),this.bufs=[]}}const I=()=>new O,N=t=>{const e=new Uint8Array((t=>{let e=t.cpos;for(let n=0;n<t.bufs.length;n++)e+=t.bufs[n].length;return e})(t));let n=0;for(let r=0;r<t.bufs.length;r++){const s=t.bufs[r];e.set(s,n),n+=s.length}return e.set(v(t.cbuf.buffer,0,t.cpos),n),e},L=(t,e)=>{const n=t.cbuf.length;t.cpos===n&&(t.bufs.push(t.cbuf),t.cbuf=new Uint8Array(2*n),t.cpos=0),t.cbuf[t.cpos++]=e},R=L,P=(t,e)=>{for(;e>T;)L(t,U|T&e),e=i.GW(e/128);L(t,T&e)},V=(t,e)=>{const n=i.GR(e);for(n&&(e=-e),L(t,(e>63?U:0)|(n?A:0)|63&e),e=i.GW(e/64);e>0;)L(t,(e>T?U:0)|T&e),e=i.GW(e/128)},F=new Uint8Array(3e4),J=F.length/3,W=d&&d.encodeInto?(t,e)=>{if(e.length<J){const n=d.encodeInto(e,F).written||0;P(t,n);for(let e=0;e<n;e++)L(t,F[e])}else j(t,u(e))}:(t,e)=>{const n=unescape(encodeURIComponent(e)),r=n.length;P(t,r);for(let e=0;e<r;e++)L(t,n.codePointAt(e))},Y=(t,e)=>{const n=t.cbuf.length,r=t.cpos,s=i.VV(n-r,e.length),o=e.length-s;t.cbuf.set(e.subarray(0,s),r),t.cpos+=s,o>0&&(t.bufs.push(t.cbuf),t.cbuf=new Uint8Array(i.Fp(2*n,o)),t.cbuf.set(e.subarray(s)),t.cpos=o)},j=(t,e)=>{P(t,e.byteLength),Y(t,e)},G=(t,e)=>{((t,e)=>{const n=t.cbuf.length;n-t.cpos<e&&(t.bufs.push(v(t.cbuf.buffer,0,t.cpos)),t.cbuf=new Uint8Array(2*i.Fp(n,e)),t.cpos=0)})(t,e);const n=new DataView(t.cbuf.buffer,t.cpos,e);return t.cpos+=e,n},z=new DataView(new ArrayBuffer(4)),$=(t,e)=>{switch(typeof e){case"string":L(t,119),W(t,e);break;case"number":x(e)&&i.Wn(e)<=2147483647?(L(t,125),V(t,e)):(n=e,z.setFloat32(0,n),z.getFloat32(0)===n?(L(t,124),((t,e)=>{G(t,4).setFloat32(0,e,!1)})(t,e)):(L(t,123),((t,e)=>{G(t,8).setFloat64(0,e,!1)})(t,e)));break;case"bigint":L(t,122),((t,e)=>{G(t,8).setBigInt64(0,e,!1)})(t,e);break;case"object":if(null===e)L(t,126);else if(s.kJ(e)){L(t,117),P(t,e.length);for(let n=0;n<e.length;n++)$(t,e[n])}else if(e instanceof Uint8Array)L(t,116),j(t,e);else{L(t,118);const n=Object.keys(e);P(t,n.length);for(let r=0;r<n.length;r++){const s=n[r];W(t,s),$(t,e[s])}}break;case"boolean":L(t,e?120:121);break;default:L(t,127)}var n};class B extends O{constructor(t){super(),this.w=t,this.s=null,this.count=0}write(t){this.s===t?this.count++:(this.count>0&&P(this,this.count-1),this.count=1,this.w(this,t),this.s=t)}}const H=t=>{t.count>0&&(V(t.encoder,1===t.count?t.s:-t.s),t.count>1&&P(t.encoder,t.count-2))};class K{constructor(){this.encoder=new O,this.s=0,this.count=0}write(t){this.s===t?this.count++:(H(this),this.count=1,this.s=t)}toUint8Array(){return H(this),N(this.encoder)}}const X=t=>{if(t.count>0){const e=2*t.diff+(1===t.count?0:1);V(t.encoder,e),t.count>1&&P(t.encoder,t.count-2)}};class Z{constructor(){this.encoder=new O,this.s=0,this.count=0,this.diff=0}write(t){this.diff===t-this.s?(this.s=t,this.count++):(X(this),this.count=1,this.diff=t-this.s,this.s=t)}toUint8Array(){return X(this),N(this.encoder)}}class q{constructor(){this.sarr=[],this.s="",this.lensE=new K}write(t){this.s+=t,this.s.length>19&&(this.sarr.push(this.s),this.s=""),this.lensE.write(t.length)}toUint8Array(){const t=new O;return this.sarr.push(this.s),this.s="",W(t,this.sarr.join("")),Y(t,this.lensE.toUint8Array()),N(t)}}const Q=t=>new Error(t),tt=()=>{throw Q("Method unimplemented")},et=()=>{throw Q("Unexpected case")},nt=Q("Unexpected end of array"),rt=Q("Integer out of Range");class st{constructor(t){this.arr=t,this.pos=0}}const it=t=>new st(t),ot=t=>t.pos!==t.arr.length,ct=t=>((t,e)=>{const n=v(t.arr.buffer,t.pos+t.arr.byteOffset,e);return t.pos+=e,n})(t,ht(t)),lt=t=>t.arr[t.pos++],ht=t=>{let e=0,n=1;const r=t.arr.length;for(;t.pos<r;){const r=t.arr[t.pos++];if(e+=(r&T)*n,n*=128,r<U)return e;if(e>M)throw rt}throw nt},at=t=>{let e=t.arr[t.pos++],n=63&e,r=64;const s=(e&A)>0?-1:1;if(0==(e&U))return s*n;const i=t.arr.length;for(;t.pos<i;){if(e=t.arr[t.pos++],n+=(e&T)*r,r*=128,e<U)return s*n;if(n>M)throw rt}throw nt},dt=g?t=>g.decode(ct(t)):t=>{let e=ht(t);if(0===e)return"";{let n=String.fromCodePoint(lt(t));if(--e<100)for(;e--;)n+=String.fromCodePoint(lt(t));else for(;e>0;){const r=e<1e4?e:1e4,s=t.arr.subarray(t.pos,t.pos+r);t.pos+=r,n+=String.fromCodePoint.apply(null,s),e-=r}return decodeURIComponent(escape(n))}},ut=(t,e)=>{const n=new DataView(t.arr.buffer,t.arr.byteOffset+t.pos,e);return t.pos+=e,n},gt=[t=>{},t=>null,at,t=>ut(t,4).getFloat32(0,!1),t=>ut(t,8).getFloat64(0,!1),t=>ut(t,8).getBigInt64(0,!1),t=>!1,t=>!0,dt,t=>{const e=ht(t),n={};for(let r=0;r<e;r++)n[dt(t)]=ft(t);return n},t=>{const e=ht(t),n=[];for(let r=0;r<e;r++)n.push(ft(t));return n},ct],ft=t=>gt[127-lt(t)](t);class pt extends st{constructor(t,e){super(t),this.reader=e,this.s=null,this.count=0}read(){return 0===this.count&&(this.s=this.reader(this),ot(this)?this.count=ht(this)+1:this.count=-1),this.count--,this.s}}class wt extends st{constructor(t){super(t),this.s=0,this.count=0}read(){if(0===this.count){this.s=at(this);const t=i.GR(this.s);this.count=1,t&&(this.s=-this.s,this.count=ht(this)+2)}return this.count--,this.s}}class mt extends st{constructor(t){super(t),this.s=0,this.count=0,this.diff=0}read(){if(0===this.count){const t=at(this),e=1&t;this.diff=i.GW(t/2),this.count=1,e&&(this.count=ht(this)+2)}return this.s+=this.diff,this.count--,this.s}}class kt{constructor(t){this.decoder=new wt(t),this.str=dt(this.decoder),this.spos=0}read(){const t=this.spos+this.decoder.read(),e=this.str.slice(this.spos,t);return this.spos=t,e}}crypto.subtle;const yt=crypto.getRandomValues.bind(crypto),bt=(Math.random,()=>yt(new Uint32Array(1))[0]),_t=[1e7]+-1e3+-4e3+-8e3+-1e11,St=()=>_t.replace(/[018]/g,(t=>(t^bt()&15>>t/4).toString(16))),Et=t=>new Promise(t);Promise.all.bind(Promise);var Ct=n(79287);class Dt{constructor(t,e){this.left=t,this.right=e}}const vt=(t,e)=>new Dt(t,e),At="undefined"!=typeof document?document:{},Ut=("undefined"!=typeof DOMParser&&new DOMParser,At.ELEMENT_NODE,At.TEXT_NODE,At.CDATA_SECTION_NODE,At.COMMENT_NODE,At.DOCUMENT_NODE,At.DOCUMENT_TYPE_NODE,At.DOCUMENT_FRAGMENT_NODE,Symbol);var Tt=n(2431);const Mt=Ut(),xt=Ut(),Ot=Ut(),It=Ut(),Nt=Ut(),Lt=Ut(),Rt=Ut(),Pt=Ut(),Vt=Ut();Tt.ZG();const Ft={[Mt]:vt("font-weight","bold"),[xt]:vt("font-weight","normal"),[Ot]:vt("color","blue"),[Nt]:vt("color","green"),[It]:vt("color","grey"),[Lt]:vt("color","red"),[Rt]:vt("color","purple"),[Pt]:vt("color","orange"),[Vt]:vt("color","black")},Jt=D?t=>{const e=[],n=[],r=o.Ue();let s=[],i=0;for(;i<t.length;i++){const s=t[i],l=Ft[s];if(void 0!==l)r.set(l.left,l.right);else{if(s.constructor!==String&&s.constructor!==Number)break;{const t=(c=r,o.UI(c,((t,e)=>`${e}:${t};`)).join(""));i>0||t.length>0?(e.push("%c"+s),n.push(t)):e.push(s)}}}var c;for(i>0&&(s=n,s.unshift(e.join("")));i<t.length;i++){const e=t[i];e instanceof Symbol||s.push(e)}return s}:t=>{const e=[],n=[];let r=0;for(;r<t.length;r++){const s=t[r];s.constructor===String||s.constructor===Number?e.push(s):s.constructor===Object&&n.push(JSON.stringify(s))}return n},Wt=(...t)=>{console.log(...Jt(t)),Yt.forEach((e=>e.print(t)))},Yt=Ct.Ue(),jt=t=>({[Symbol.iterator](){return this},next:t}),Gt=(t,e)=>jt((()=>{const{done:n,value:r}=t.next();return{done:n,value:n?void 0:e(r)}}));var zt=n(36498);class $t extends r.y{constructor(t,e){super(),this.doc=t,this.awareness=e}}class Bt{constructor(t,e){this.clock=t,this.len=e}}class Ht{constructor(){this.clients=new Map}}const Kt=(t,e,n)=>e.clients.forEach(((e,r)=>{const s=t.doc.store.clients.get(r);for(let r=0;r<e.length;r++){const i=e[r];bn(t,s,i.clock,i.len,n)}})),Xt=(t,e)=>{const n=t.clients.get(e.client);return void 0!==n&&null!==((t,e)=>{let n=0,r=t.length-1;for(;n<=r;){const s=i.GW((n+r)/2),o=t[s],c=o.clock;if(c<=e){if(e<c+o.len)return s;n=s+1}else r=s-1}return null})(n,e.clock)},Zt=t=>{t.clients.forEach((t=>{let e,n;for(t.sort(((t,e)=>t.clock-e.clock)),e=1,n=1;e<t.length;e++){const r=t[n-1],s=t[e];r.clock+r.len>=s.clock?r.len=i.Fp(r.len,s.clock+s.len-r.clock):(n<e&&(t[n]=s),n++)}t.length=n}))},qt=t=>{const e=new Ht;for(let n=0;n<t.length;n++)t[n].clients.forEach(((r,i)=>{if(!e.clients.has(i)){const o=r.slice();for(let e=n+1;e<t.length;e++)s.s7(o,t[e].clients.get(i)||[]);e.clients.set(i,o)}}));return Zt(e),e},Qt=(t,e,n,r)=>{o.Yu(t.clients,e,(()=>[])).push(new Bt(n,r))},te=()=>new Ht,ee=t=>{const e=te();return t.clients.forEach(((t,n)=>{const r=[];for(let e=0;e<t.length;e++){const n=t[e];if(n.deleted){const s=n.id.clock;let i=n.length;if(e+1<t.length)for(let n=t[e+1];e+1<t.length&&n.deleted;n=t[1+ ++e])i+=n.length;r.push(new Bt(s,i))}}r.length>0&&e.clients.set(n,r)})),e},ne=(t,e)=>{P(t.restEncoder,e.clients.size),s.Dp(e.clients.entries()).sort(((t,e)=>e[0]-t[0])).forEach((([e,n])=>{t.resetDsCurVal(),P(t.restEncoder,e);const r=n.length;P(t.restEncoder,r);for(let e=0;e<r;e++){const r=n[e];t.writeDsClock(r.clock),t.writeDsLen(r.len)}}))},re=t=>{const e=new Ht,n=ht(t.restDecoder);for(let r=0;r<n;r++){t.resetDsCurVal();const n=ht(t.restDecoder),r=ht(t.restDecoder);if(r>0){const s=o.Yu(e.clients,n,(()=>[]));for(let e=0;e<r;e++)s.push(new Bt(t.readDsClock(),t.readDsLen()))}}return e},se=(t,e,n)=>{const r=new Ht,s=ht(t.restDecoder);for(let i=0;i<s;i++){t.resetDsCurVal();const s=ht(t.restDecoder),i=ht(t.restDecoder),o=n.clients.get(s)||[],c=gn(n,s);for(let n=0;n<i;n++){const n=t.readDsClock(),i=n+t.readDsLen();if(n<c){c<i&&Qt(r,s,c,i-c);let t=pn(o,n),l=o[t];for(!l.deleted&&l.id.clock<n&&(o.splice(t+1,0,As(e,l,n-l.id.clock)),t++);t<o.length&&(l=o[t++],l.id.clock<i);)l.deleted||(i<l.id.clock+l.length&&o.splice(t,0,As(e,l,i-l.id.clock)),l.delete(e))}else Qt(r,s,n,i-n)}}if(r.clients.size>0){const t=new pe;return P(t.restEncoder,0),ne(t,r),t.toUint8Array()}return null},ie=(t,e)=>{if(t.clients.size!==e.clients.size)return!1;for(const[n,r]of t.clients.entries()){const t=e.clients.get(n);if(void 0===t||r.length!==t.length)return!1;for(let e=0;e<r.length;e++){const n=r[e],s=t[e];if(n.clock!==s.clock||n.len!==s.len)return!1}}return!0},oe=bt;class ce extends r.y{constructor({guid:t=St(),collectionid:e=null,gc:n=!0,gcFilter:r=(()=>!0),meta:s=null,autoLoad:i=!1,shouldLoad:o=!0}={}){super(),this.gc=n,this.gcFilter=r,this.clientID=oe(),this.guid=t,this.collectionid=e,this.share=new Map,this.store=new dn,this._transaction=null,this._transactionCleanups=[],this.subdocs=new Set,this._item=null,this.shouldLoad=o,this.autoLoad=i,this.meta=s,this.isLoaded=!1,this.isSynced=!1,this.whenLoaded=Et((t=>{this.on("load",(()=>{this.isLoaded=!0,t(this)}))}));const c=()=>Et((t=>{const e=n=>{void 0!==n&&!0!==n||(this.off("sync",e),t())};this.on("sync",e)}));this.on("sync",(t=>{!1===t&&this.isSynced&&(this.whenSynced=c()),this.isSynced=void 0===t||!0===t,this.isLoaded||this.emit("load",[])})),this.whenSynced=c()}load(){const t=this._item;null===t||this.shouldLoad||Tn(t.parent.doc,(t=>{t.subdocsLoaded.add(this)}),null,!0),this.shouldLoad=!0}getSubdocs(){return this.subdocs}getSubdocGuids(){return new Set(s.Dp(this.subdocs).map((t=>t.guid)))}transact(t,e=null){return Tn(this,t,e)}get(t,e=fr){const n=o.Yu(this.share,t,(()=>{const t=new e;return t._integrate(this,null),t})),r=n.constructor;if(e!==fr&&r!==e){if(r===fr){const r=new e;r._map=n._map,n._map.forEach((t=>{for(;null!==t;t=t.left)t.parent=r})),r._start=n._start;for(let t=r._start;null!==t;t=t.right)t.parent=r;return r._length=n._length,this.share.set(t,r),r._integrate(this,null),r}throw new Error(`Type with the name ${t} has already been defined with a different constructor`)}return n}getArray(t=""){return this.get(t,Nr)}getText(t=""){return this.get(t,qr)}getMap(t=""){return this.get(t,Rr)}getXmlFragment(t=""){return this.get(t,ts)}toJSON(){const t={};return this.share.forEach(((e,n)=>{t[n]=e.toJSON()})),t}destroy(){s.Dp(this.subdocs).forEach((t=>t.destroy()));const t=this._item;if(null!==t){this._item=null;const e=t.content;e.doc=new ce({guid:this.guid,...e.opts,shouldLoad:!1}),e.doc._item=t,Tn(t.parent.doc,(n=>{const r=e.doc;t.deleted||n.subdocsAdded.add(r),n.subdocsRemoved.add(this)}),null,!0)}this.emit("destroyed",[!0]),this.emit("destroy",[this]),super.destroy()}on(t,e){super.on(t,e)}off(t,e){super.off(t,e)}}class le{constructor(t){this.restDecoder=t}resetDsCurVal(){}readDsClock(){return ht(this.restDecoder)}readDsLen(){return ht(this.restDecoder)}}class he extends le{readLeftID(){return Ne(ht(this.restDecoder),ht(this.restDecoder))}readRightID(){return Ne(ht(this.restDecoder),ht(this.restDecoder))}readClient(){return ht(this.restDecoder)}readInfo(){return lt(this.restDecoder)}readString(){return dt(this.restDecoder)}readParentInfo(){return 1===ht(this.restDecoder)}readTypeRef(){return ht(this.restDecoder)}readLen(){return ht(this.restDecoder)}readAny(){return ft(this.restDecoder)}readBuf(){return(t=>{const e=(n=t.byteLength,new Uint8Array(n));var n;return e.set(t),e})(ct(this.restDecoder))}readJSON(){return JSON.parse(dt(this.restDecoder))}readKey(){return dt(this.restDecoder)}}class ae{constructor(t){this.dsCurrVal=0,this.restDecoder=t}resetDsCurVal(){this.dsCurrVal=0}readDsClock(){return this.dsCurrVal+=ht(this.restDecoder),this.dsCurrVal}readDsLen(){const t=ht(this.restDecoder)+1;return this.dsCurrVal+=t,t}}class de extends ae{constructor(t){super(t),this.keys=[],ht(t),this.keyClockDecoder=new mt(ct(t)),this.clientDecoder=new wt(ct(t)),this.leftClockDecoder=new mt(ct(t)),this.rightClockDecoder=new mt(ct(t)),this.infoDecoder=new pt(ct(t),lt),this.stringDecoder=new kt(ct(t)),this.parentInfoDecoder=new pt(ct(t),lt),this.typeRefDecoder=new wt(ct(t)),this.lenDecoder=new wt(ct(t))}readLeftID(){return new Oe(this.clientDecoder.read(),this.leftClockDecoder.read())}readRightID(){return new Oe(this.clientDecoder.read(),this.rightClockDecoder.read())}readClient(){return this.clientDecoder.read()}readInfo(){return this.infoDecoder.read()}readString(){return this.stringDecoder.read()}readParentInfo(){return 1===this.parentInfoDecoder.read()}readTypeRef(){return this.typeRefDecoder.read()}readLen(){return this.lenDecoder.read()}readAny(){return ft(this.restDecoder)}readBuf(){return ct(this.restDecoder)}readJSON(){return ft(this.restDecoder)}readKey(){const t=this.keyClockDecoder.read();if(t<this.keys.length)return this.keys[t];{const t=this.stringDecoder.read();return this.keys.push(t),t}}}class ue{constructor(){this.restEncoder=I()}toUint8Array(){return N(this.restEncoder)}resetDsCurVal(){}writeDsClock(t){P(this.restEncoder,t)}writeDsLen(t){P(this.restEncoder,t)}}class ge extends ue{writeLeftID(t){P(this.restEncoder,t.client),P(this.restEncoder,t.clock)}writeRightID(t){P(this.restEncoder,t.client),P(this.restEncoder,t.clock)}writeClient(t){P(this.restEncoder,t)}writeInfo(t){R(this.restEncoder,t)}writeString(t){W(this.restEncoder,t)}writeParentInfo(t){P(this.restEncoder,t?1:0)}writeTypeRef(t){P(this.restEncoder,t)}writeLen(t){P(this.restEncoder,t)}writeAny(t){$(this.restEncoder,t)}writeBuf(t){j(this.restEncoder,t)}writeJSON(t){W(this.restEncoder,JSON.stringify(t))}writeKey(t){W(this.restEncoder,t)}}class fe{constructor(){this.restEncoder=I(),this.dsCurrVal=0}toUint8Array(){return N(this.restEncoder)}resetDsCurVal(){this.dsCurrVal=0}writeDsClock(t){const e=t-this.dsCurrVal;this.dsCurrVal=t,P(this.restEncoder,e)}writeDsLen(t){0===t&&et(),P(this.restEncoder,t-1),this.dsCurrVal+=t}}class pe extends fe{constructor(){super(),this.keyMap=new Map,this.keyClock=0,this.keyClockEncoder=new Z,this.clientEncoder=new K,this.leftClockEncoder=new Z,this.rightClockEncoder=new Z,this.infoEncoder=new B(R),this.stringEncoder=new q,this.parentInfoEncoder=new B(R),this.typeRefEncoder=new K,this.lenEncoder=new K}toUint8Array(){const t=I();return P(t,0),j(t,this.keyClockEncoder.toUint8Array()),j(t,this.clientEncoder.toUint8Array()),j(t,this.leftClockEncoder.toUint8Array()),j(t,this.rightClockEncoder.toUint8Array()),j(t,N(this.infoEncoder)),j(t,this.stringEncoder.toUint8Array()),j(t,N(this.parentInfoEncoder)),j(t,this.typeRefEncoder.toUint8Array()),j(t,this.lenEncoder.toUint8Array()),Y(t,N(this.restEncoder)),N(t)}writeLeftID(t){this.clientEncoder.write(t.client),this.leftClockEncoder.write(t.clock)}writeRightID(t){this.clientEncoder.write(t.client),this.rightClockEncoder.write(t.clock)}writeClient(t){this.clientEncoder.write(t)}writeInfo(t){this.infoEncoder.write(t)}writeString(t){this.stringEncoder.write(t)}writeParentInfo(t){this.parentInfoEncoder.write(t?1:0)}writeTypeRef(t){this.typeRefEncoder.write(t)}writeLen(t){this.lenEncoder.write(t)}writeAny(t){$(this.restEncoder,t)}writeBuf(t){j(this.restEncoder,t)}writeJSON(t){$(this.restEncoder,t)}writeKey(t){const e=this.keyMap.get(t);void 0===e?(this.keyClockEncoder.write(this.keyClock++),this.stringEncoder.write(t)):this.keyClockEncoder.write(e)}}const we=(t,e,n)=>{const r=new Map;n.forEach(((t,n)=>{gn(e,n)>t&&r.set(n,t)})),un(e).forEach(((t,e)=>{n.has(e)||r.set(e,0)})),P(t.restEncoder,r.size),s.Dp(r.entries()).sort(((t,e)=>e[0]-t[0])).forEach((([n,r])=>{((t,e,n,r)=>{r=i.Fp(r,e[0].id.clock);const s=pn(e,r);P(t.restEncoder,e.length-s),t.writeClient(n),P(t.restEncoder,r);const o=e[s];o.write(t,r-o.id.clock);for(let n=s+1;n<e.length;n++)e[n].write(t,0)})(t,e.clients.get(n),n,r)}))},me=(t,e,n,r=new de(t))=>Tn(e,(t=>{t.local=!1;let e=!1;const n=t.doc,i=n.store,c=((t,e)=>{const n=o.Ue(),r=ht(t.restDecoder);for(let s=0;s<r;s++){const r=ht(t.restDecoder),s=new Array(r),i=t.readClient();let o=ht(t.restDecoder);n.set(i,{i:0,refs:s});for(let n=0;n<r;n++){const r=t.readInfo();switch(31&r){case 0:{const e=t.readLen();s[n]=new os(Ne(i,o),e),o+=e;break}case 10:{const e=ht(t.restDecoder);s[n]=new Is(Ne(i,o),e),o+=e;break}default:{const c=0==(192&r),l=new Ms(Ne(i,o),null,(r&U)===U?t.readLeftID():null,null,(r&A)===A?t.readRightID():null,c?t.readParentInfo()?e.get(t.readString()):t.readLeftID():null,c&&32==(32&r)?t.readString():null,xs(t,r));s[n]=l,o+=l.length}}}}return n})(r,n),l=((t,e,n)=>{const r=[];let i=s.Dp(n.keys()).sort(((t,e)=>t-e));if(0===i.length)return null;const c=()=>{if(0===i.length)return null;let t=n.get(i[i.length-1]);for(;t.refs.length===t.i;){if(i.pop(),!(i.length>0))return null;t=n.get(i[i.length-1])}return t};let l=c();if(null===l&&0===r.length)return null;const h=new dn,a=new Map,d=(t,e)=>{const n=a.get(t);(null==n||n>e)&&a.set(t,e)};let u=l.refs[l.i++];const g=new Map,f=()=>{for(const t of r){const e=t.id.client,r=n.get(e);r?(r.i--,h.clients.set(e,r.refs.slice(r.i)),n.delete(e),r.i=0,r.refs=[]):h.clients.set(e,[t]),i=i.filter((t=>t!==e))}r.length=0};for(;;){if(u.constructor!==Is){const s=o.Yu(g,u.id.client,(()=>gn(e,u.id.client)))-u.id.clock;if(s<0)r.push(u),d(u.id.client,u.id.clock-1),f();else{const i=u.getMissing(t,e);if(null!==i){r.push(u);const t=n.get(i)||{refs:[],i:0};if(t.refs.length!==t.i){u=t.refs[t.i++];continue}d(i,gn(e,i)),f()}else(0===s||s<u.length)&&(u.integrate(t,s),g.set(u.id.client,u.id.clock+u.length))}}if(r.length>0)u=r.pop();else if(null!==l&&l.i<l.refs.length)u=l.refs[l.i++];else{if(l=c(),null===l)break;u=l.refs[l.i++]}}if(h.clients.size>0){const t=new pe;return we(t,h,new Map),P(t.restEncoder,0),{missing:a,update:t.toUint8Array()}}return null})(t,i,c),h=i.pendingStructs;if(h){for(const[t,n]of h.missing)if(n<gn(i,t)){e=!0;break}if(l){for(const[t,e]of l.missing){const n=h.missing.get(t);(null==n||n>e)&&h.missing.set(t,e)}h.update=$n([h.update,l.update])}}else i.pendingStructs=l;const a=se(r,t,i);if(i.pendingDs){const e=new de(it(i.pendingDs));ht(e.restDecoder);const n=se(e,t,i);i.pendingDs=a&&n?$n([a,n]):a||n}else i.pendingDs=a;if(e){const e=i.pendingStructs.update;i.pendingStructs=null,ye(t.doc,e)}}),n,!1),ke=(t,e,n)=>me(t,e,n,new he(t)),ye=(t,e,n,r=de)=>{const s=it(e);me(s,t,n,new r(s))},be=(t,e,n)=>ye(t,e,n,he),_e=(t,e=new Uint8Array([0]),n=new pe)=>{((t,e,n=new Map)=>{we(t,e.store,n),ne(t,ee(e.store))})(n,t,Ce(e));const r=[n.toUint8Array()];if(t.store.pendingDs&&r.push(t.store.pendingDs),t.store.pendingStructs&&r.push(Bn(t.store.pendingStructs.update,e)),r.length>1){if(n.constructor===ge)return Jn(r.map(((t,e)=>0===e?t:rr(t))));if(n.constructor===pe)return $n(r)}return r[0]},Se=(t,e)=>_e(t,e,new ge),Ee=t=>{const e=new Map,n=ht(t.restDecoder);for(let r=0;r<n;r++){const n=ht(t.restDecoder),r=ht(t.restDecoder);e.set(n,r)}return e},Ce=t=>Ee(new le(it(t))),De=(t,e)=>(P(t.restEncoder,e.size),s.Dp(e.entries()).sort(((t,e)=>e[0]-t[0])).forEach((([e,n])=>{P(t.restEncoder,e),P(t.restEncoder,n)})),t),ve=t=>((t,e=new fe)=>(t instanceof Map?De(e,t):((t,e)=>{De(t,un(e.store))})(e,t),e.toUint8Array()))(t,new ue);class Ae{constructor(){this.l=[]}}const Ue=()=>new Ae,Te=(t,e)=>t.l.push(e),Me=(t,e)=>{const n=t.l,r=n.length;t.l=n.filter((t=>e!==t)),r===t.l.length&&console.error("[yjs] Tried to remove event handler that doesn't exist.")},xe=(t,e,n)=>m.PP(t.l,[e,n]);class Oe{constructor(t,e){this.client=t,this.clock=e}}const Ie=(t,e)=>t===e||null!==t&&null!==e&&t.client===e.client&&t.clock===e.clock,Ne=(t,e)=>new Oe(t,e),Le=(t,e)=>{P(t,e.client),P(t,e.clock)},Re=t=>Ne(ht(t),ht(t)),Pe=t=>{for(const[e,n]of t.doc.share.entries())if(n===t)return e;throw et()},Ve=(t,e)=>{for(;null!==e;){if(e.parent===t)return!0;e=e.parent._item}return!1},Fe=t=>{const e=[];let n=t._start;for(;n;)e.push(n),n=n.right;console.log("Children: ",e),console.log("Children content: ",e.filter((t=>!t.deleted)).map((t=>t.content)))};class Je{constructor(t,e=t.getMap("users")){const n=new Map;this.yusers=e,this.doc=t,this.clients=new Map,this.dss=n;const r=(t,e)=>{const n=t.get("ds"),r=t.get("ids"),s=t=>this.clients.set(t,e);n.observe((t=>{t.changes.added.forEach((t=>{t.content.getContent().forEach((t=>{t instanceof Uint8Array&&this.dss.set(e,qt([this.dss.get(e)||te(),re(new le(it(t)))]))}))}))})),this.dss.set(e,qt(n.map((t=>re(new le(it(t))))))),r.observe((t=>t.changes.added.forEach((t=>t.content.getContent().forEach(s))))),r.forEach(s)};e.observe((t=>{t.keysChanged.forEach((t=>r(e.get(t),t)))})),e.forEach(r)}setUserMapping(t,e,n,{filter:r=(()=>!0)}={}){const s=this.yusers;let i=s.get(n);i||(i=new Rr,i.set("ids",new Nr),i.set("ds",new Nr),s.set(n,i)),i.get("ids").push([e]),s.observe((t=>{setTimeout((()=>{const t=s.get(n);if(t!==i){i=t,this.clients.forEach(((t,e)=>{n===t&&i.get("ids").push([e])}));const e=new ue,r=this.dss.get(n);r&&(ne(e,r),i.get("ds").push([e.toUint8Array()]))}}),0)})),t.on("afterTransaction",(t=>{setTimeout((()=>{const e=i.get("ds"),n=t.deleteSet;if(t.local&&n.clients.size>0&&r(t,n)){const t=new ue;ne(t,n),e.push([t.toUint8Array()])}}))}))}getUserByClientId(t){return this.clients.get(t)||null}getUserByDeletedId(t){for(const[e,n]of this.dss.entries())if(Xt(n,t))return e;return null}}class We{constructor(t,e,n,r=0){this.type=t,this.tname=e,this.item=n,this.assoc=r}}const Ye=t=>{const e={};return t.type&&(e.type=t.type),t.tname&&(e.tname=t.tname),t.item&&(e.item=t.item),null!=t.assoc&&(e.assoc=t.assoc),e},je=t=>new We(null==t.type?null:Ne(t.type.client,t.type.clock),t.tname||null,null==t.item?null:Ne(t.item.client,t.item.clock),null==t.assoc?0:t.assoc);class Ge{constructor(t,e,n=0){this.type=t,this.index=e,this.assoc=n}}const ze=(t,e,n)=>{let r=null,s=null;return null===t._item?s=Pe(t):r=Ne(t._item.id.client,t._item.id.clock),new We(r,s,e,n)},$e=(t,e,n=0)=>{let r=t._start;if(n<0){if(0===e)return ze(t,null,n);e--}for(;null!==r;){if(!r.deleted&&r.countable){if(r.length>e)return ze(t,Ne(r.id.client,r.id.clock+e),n);e-=r.length}if(null===r.right&&n<0)return ze(t,r.lastId,n);r=r.right}return ze(t,null,n)},Be=t=>{const e=I();return((t,e)=>{const{type:n,tname:r,item:s,assoc:i}=e;if(null!==s)P(t,0),Le(t,s);else if(null!==r)R(t,1),W(t,r);else{if(null===n)throw et();R(t,2),Le(t,n)}V(t,i)})(e,t),N(e)},He=t=>(t=>{let e=null,n=null,r=null;switch(ht(t)){case 0:r=Re(t);break;case 1:n=dt(t);break;case 2:e=Re(t)}const s=ot(t)?at(t):0;return new We(e,n,r,s)})(it(t)),Ke=(t,e)=>{const n=e.store,r=t.item,s=t.type,i=t.tname,o=t.assoc;let c=null,l=0;if(null!==r){if(gn(n,r.client)<=r.clock)return null;const t=Ds(n,r),e=t.item;if(!(e instanceof Ms))return null;if(c=e.parent,null===c._item||!c._item.deleted){l=e.deleted||!e.countable?0:t.diff+(o>=0?0:1);let n=e.left;for(;null!==n;)!n.deleted&&n.countable&&(l+=n.length),n=n.left}}else{if(null!==i)c=e.get(i);else{if(null===s)throw et();{if(gn(n,s.client)<=s.clock)return null;const{item:t}=Ds(n,s);if(!(t instanceof Ms&&t.content instanceof Cs))return null;c=t.content.type}}l=o>=0?c._length:0}return((t,e,n=0)=>new Ge(t,e,n))(c,l,t.assoc)},Xe=(t,e)=>t===e||null!==t&&null!==e&&t.tname===e.tname&&Ie(t.item,e.item)&&Ie(t.type,e.type)&&t.assoc===e.assoc;class Ze{constructor(t,e){this.ds=t,this.sv=e}}const qe=(t,e)=>{const n=t.ds.clients,r=e.ds.clients,s=t.sv,i=e.sv;if(s.size!==i.size||n.size!==r.size)return!1;for(const[t,e]of s.entries())if(i.get(t)!==e)return!1;for(const[t,e]of n.entries()){const n=r.get(t)||[];if(e.length!==n.length)return!1;for(let t=0;t<e.length;t++){const r=e[t],s=n[t];if(r.clock!==s.clock||r.len!==s.len)return!1}}return!0},Qe=(t,e=new fe)=>(ne(e,t.ds),De(e,t.sv),e.toUint8Array()),tn=t=>Qe(t,new ue),en=(t,e=new ae(it(t)))=>new Ze(re(e),Ee(e)),nn=t=>en(t,new le(it(t))),rn=(t,e)=>new Ze(t,e),sn=rn(te(),new Map),on=t=>rn(ee(t.store),un(t.store)),cn=(t,e)=>void 0===e?!t.deleted:e.sv.has(t.id.client)&&(e.sv.get(t.id.client)||0)>t.id.clock&&!Xt(e.ds,t.id),ln=(t,e)=>{const n=o.Yu(t.meta,ln,Ct.Ue),r=t.doc.store;n.has(e)||(e.sv.forEach(((e,n)=>{e<gn(r,n)&&kn(t,Ne(n,e))})),Kt(t,e.ds,(t=>{})),n.add(e))},hn=(t,e,n=new ce)=>{if(t.gc)throw new Error("Garbage-collection must be disabled in `originDoc`!");const{sv:r,ds:s}=e,i=new pe;return t.transact((e=>{let n=0;r.forEach((t=>{t>0&&n++})),P(i.restEncoder,n);for(const[n,s]of r){if(0===s)continue;s<gn(t.store,n)&&kn(e,Ne(n,s));const r=t.store.clients.get(n)||[],o=pn(r,s-1);P(i.restEncoder,o+1),i.writeClient(n),P(i.restEncoder,0);for(let t=0;t<=o;t++)r[t].write(i,0)}ne(i,s)})),ye(n,i.toUint8Array(),"snapshot"),n},an=(t,e)=>((t,e,n=de)=>{const r=new n(it(e)),s=new Nn(r,!1);for(let e=s.curr;null!==e;e=s.next())if((t.sv.get(e.id.client)||0)<e.id.clock+e.length)return!1;const i=qt([t.ds,re(r)]);return ie(t.ds,i)})(t,e,he);class dn{constructor(){this.clients=new Map,this.pendingStructs=null,this.pendingDs=null}}const un=t=>{const e=new Map;return t.clients.forEach(((t,n)=>{const r=t[t.length-1];e.set(n,r.id.clock+r.length)})),e},gn=(t,e)=>{const n=t.clients.get(e);if(void 0===n)return 0;const r=n[n.length-1];return r.id.clock+r.length},fn=(t,e)=>{let n=t.clients.get(e.id.client);if(void 0===n)n=[],t.clients.set(e.id.client,n);else{const t=n[n.length-1];if(t.id.clock+t.length!==e.id.clock)throw et()}n.push(e)},pn=(t,e)=>{let n=0,r=t.length-1,s=t[r],o=s.id.clock;if(o===e)return r;let c=i.GW(e/(o+s.length-1)*r);for(;n<=r;){if(s=t[c],o=s.id.clock,o<=e){if(e<o+s.length)return c;n=c+1}else r=c-1;c=i.GW((n+r)/2)}throw et()},wn=(t,e)=>{const n=t.clients.get(e.client);return n[pn(n,e.clock)]},mn=(t,e,n)=>{const r=pn(e,n),s=e[r];return s.id.clock<n&&s instanceof Ms?(e.splice(r+1,0,As(t,s,n-s.id.clock)),r+1):r},kn=(t,e)=>{const n=t.doc.store.clients.get(e.client);return n[mn(t,n,e.clock)]},yn=(t,e,n)=>{const r=e.clients.get(n.client),s=pn(r,n.clock),i=r[s];return n.clock!==i.id.clock+i.length-1&&i.constructor!==os&&r.splice(s+1,0,As(t,i,n.clock-i.id.clock+1)),i},bn=(t,e,n,r,s)=>{if(0===r)return;const i=n+r;let o,c=mn(t,e,n);do{o=e[c++],i<o.id.clock+o.length&&mn(t,e,i),s(o)}while(c<e.length&&e[c].id.clock<i)};class _n{constructor(t,e,n){this.doc=t,this.deleteSet=new Ht,this.beforeState=un(t.store),this.afterState=new Map,this.changed=new Map,this.changedParentTypes=new Map,this._mergeStructs=[],this.origin=e,this.meta=new Map,this.local=n,this.subdocsAdded=new Set,this.subdocsRemoved=new Set,this.subdocsLoaded=new Set,this._needFormattingCleanup=!1}}const Sn=(t,e)=>!(0===e.deleteSet.clients.size&&!o.Yj(e.afterState,((t,n)=>e.beforeState.get(n)!==t))||(Zt(e.deleteSet),((t,e)=>{we(t,e.doc.store,e.beforeState)})(t,e),ne(t,e.deleteSet),0)),En=(t,e,n)=>{const r=e._item;(null===r||r.id.clock<(t.beforeState.get(r.id.client)||0)&&!r.deleted)&&o.Yu(t.changed,e,Ct.Ue).add(n)},Cn=(t,e)=>{let n=t[e],r=t[e-1],s=e;for(;s>0&&r.deleted===n.deleted&&r.constructor===n.constructor&&r.mergeWith(n);n=r,r=t[--s-1])n instanceof Ms&&null!==n.parentSub&&n.parent._map.get(n.parentSub)===n&&n.parent._map.set(n.parentSub,r);const i=e-s;return i&&t.splice(e+1-i,i),i},Dn=(t,e,n)=>{for(const[r,s]of t.clients.entries()){const t=e.clients.get(r);for(let r=s.length-1;r>=0;r--){const i=s[r],o=i.clock+i.len;for(let r=pn(t,i.clock),s=t[r];r<t.length&&s.id.clock<o;s=t[++r]){const s=t[r];if(i.clock+i.len<=s.id.clock)break;s instanceof Ms&&s.deleted&&!s.keep&&n(s)&&s.gc(e,!1)}}}},vn=(t,e)=>{t.clients.forEach(((t,n)=>{const r=e.clients.get(n);for(let e=t.length-1;e>=0;e--){const n=t[e];for(let t=i.VV(r.length-1,1+pn(r,n.clock+n.len-1)),e=r[t];t>0&&e.id.clock>=n.clock;e=r[t])t-=1+Cn(r,t)}}))},An=(t,e,n)=>{Dn(t,e,n),vn(t,e)},Un=(t,e)=>{if(e<t.length){const n=t[e],r=n.doc,s=r.store,o=n.deleteSet,c=n._mergeStructs;try{Zt(o),n.afterState=un(n.doc.store),r.emit("beforeObserverCalls",[n,r]);const t=[];n.changed.forEach(((e,r)=>t.push((()=>{null!==r._item&&r._item.deleted||r._callObserver(n,e)})))),t.push((()=>{n.changedParentTypes.forEach(((t,e)=>{e._dEH.l.length>0&&(null===e._item||!e._item.deleted)&&((t=t.filter((t=>null===t.target._item||!t.target._item.deleted))).forEach((t=>{t.currentTarget=e,t._path=null})),t.sort(((t,e)=>t.path.length-e.path.length)),xe(e._dEH,t,n))}))})),t.push((()=>r.emit("afterTransaction",[n,r]))),(0,m.PP)(t,[]),n._needFormattingCleanup&&Kr(n)}finally{r.gc&&Dn(o,s,r.gcFilter),vn(o,s),n.afterState.forEach(((t,e)=>{const r=n.beforeState.get(e)||0;if(r!==t){const t=s.clients.get(e),n=i.Fp(pn(t,r),1);for(let e=t.length-1;e>=n;)e-=1+Cn(t,e)}}));for(let t=c.length-1;t>=0;t--){const{client:e,clock:n}=c[t].id,r=s.clients.get(e),i=pn(r,n);i+1<r.length&&Cn(r,i+1)>1||i>0&&Cn(r,i)}if(n.local||n.afterState.get(r.clientID)===n.beforeState.get(r.clientID)||(Wt(Pt,Mt,"[yjs] ",xt,Lt,"Changed the client-id because another client seems to be using it."),r.clientID=oe()),r.emit("afterTransactionCleanup",[n,r]),r._observers.has("update")){const t=new ge;Sn(t,n)&&r.emit("update",[t.toUint8Array(),n.origin,r,n])}if(r._observers.has("updateV2")){const t=new pe;Sn(t,n)&&r.emit("updateV2",[t.toUint8Array(),n.origin,r,n])}const{subdocsAdded:l,subdocsLoaded:h,subdocsRemoved:a}=n;(l.size>0||a.size>0||h.size>0)&&(l.forEach((t=>{t.clientID=r.clientID,null==t.collectionid&&(t.collectionid=r.collectionid),r.subdocs.add(t)})),a.forEach((t=>r.subdocs.delete(t))),r.emit("subdocs",[{loaded:h,added:l,removed:a},r,n]),a.forEach((t=>t.destroy()))),t.length<=e+1?(r._transactionCleanups=[],r.emit("afterAllTransactions",[r,t])):Un(t,e+1)}}},Tn=(t,e,n=null,r=!0)=>{const s=t._transactionCleanups;let i=!1,o=null;null===t._transaction&&(i=!0,t._transaction=new _n(t,n,r),s.push(t._transaction),1===s.length&&t.emit("beforeAllTransactions",[t]),t.emit("beforeTransaction",[t._transaction,t]));try{o=e(t._transaction)}finally{if(i){const e=t._transaction===s[0];t._transaction=null,e&&Un(s,0)}}return o};class Mn{constructor(t,e){this.insertions=e,this.deletions=t,this.meta=new Map}}const xn=(t,e,n)=>{Kt(t,n.deletions,(t=>{t instanceof Ms&&e.scope.some((e=>Ve(e,t)))&&vs(t,!1)}))},On=(t,e,n)=>{let r=null,s=null;const i=t.doc,o=t.scope;if(Tn(i,(n=>{for(;e.length>0&&null===r;){const s=i.store,c=e.pop(),l=new Set,h=[];let a=!1;Kt(n,c.insertions,(t=>{if(t instanceof Ms){if(null!==t.redone){let{item:e,diff:r}=Ds(s,t.id);r>0&&(e=kn(n,Ne(e.id.client,e.id.clock+r))),t=e}!t.deleted&&o.some((e=>Ve(e,t)))&&h.push(t)}})),Kt(n,c.deletions,(t=>{t instanceof Ms&&o.some((e=>Ve(e,t)))&&!Xt(c.insertions,t.id)&&l.add(t)})),l.forEach((e=>{a=null!==Ts(n,e,l,c.insertions,t.ignoreRemoteMapChanges,t)||a}));for(let e=h.length-1;e>=0;e--){const r=h[e];t.deleteFilter(r)&&(r.delete(n),a=!0)}r=a?c:null}n.changed.forEach(((t,e)=>{t.has(null)&&e._searchMarker&&(e._searchMarker.length=0)})),s=n}),t),null!=r){const e=s.changedParentTypes;t.emit("stack-item-popped",[{stackItem:r,type:n,changedParentTypes:e},t])}return r};class In extends r.y{constructor(t,{captureTimeout:e=500,captureTransaction:n=(t=>!0),deleteFilter:r=(()=>!0),trackedOrigins:i=new Set([null]),ignoreRemoteMapChanges:o=!1,doc:c=(s.kJ(t)?t[0].doc:t.doc)}={}){super(),this.scope=[],this.doc=c,this.addToScope(t),this.deleteFilter=r,i.add(this),this.trackedOrigins=i,this.captureTransaction=n,this.undoStack=[],this.redoStack=[],this.undoing=!1,this.redoing=!1,this.lastChange=0,this.ignoreRemoteMapChanges=o,this.captureTimeout=e,this.afterTransactionHandler=t=>{if(!(this.captureTransaction(t)&&this.scope.some((e=>t.changedParentTypes.has(e)))&&(this.trackedOrigins.has(t.origin)||t.origin&&this.trackedOrigins.has(t.origin.constructor))))return;const e=this.undoing,n=this.redoing,r=e?this.redoStack:this.undoStack;e?this.stopCapturing():n||this.clear(!1,!0);const s=new Ht;t.afterState.forEach(((e,n)=>{const r=t.beforeState.get(n)||0,i=e-r;i>0&&Qt(s,n,r,i)}));const i=Tt.ZG();let o=!1;if(this.lastChange>0&&i-this.lastChange<this.captureTimeout&&r.length>0&&!e&&!n){const e=r[r.length-1];e.deletions=qt([e.deletions,t.deleteSet]),e.insertions=qt([e.insertions,s])}else r.push(new Mn(t.deleteSet,s)),o=!0;e||n||(this.lastChange=i),Kt(t,t.deleteSet,(t=>{t instanceof Ms&&this.scope.some((e=>Ve(e,t)))&&vs(t,!0)}));const c=[{stackItem:r[r.length-1],origin:t.origin,type:e?"redo":"undo",changedParentTypes:t.changedParentTypes},this];o?this.emit("stack-item-added",c):this.emit("stack-item-updated",c)},this.doc.on("afterTransaction",this.afterTransactionHandler),this.doc.on("destroy",(()=>{this.destroy()}))}addToScope(t){(t=s.kJ(t)?t:[t]).forEach((t=>{this.scope.every((e=>e!==t))&&(t.doc!==this.doc&&((...t)=>{console.warn(...Jt(t)),t.unshift(Pt),Yt.forEach((e=>e.print(t)))})("[yjs#509] Not same Y.Doc"),this.scope.push(t))}))}addTrackedOrigin(t){this.trackedOrigins.add(t)}removeTrackedOrigin(t){this.trackedOrigins.delete(t)}clear(t=!0,e=!0){(t&&this.canUndo()||e&&this.canRedo())&&this.doc.transact((n=>{t&&(this.undoStack.forEach((t=>xn(n,this,t))),this.undoStack=[]),e&&(this.redoStack.forEach((t=>xn(n,this,t))),this.redoStack=[]),this.emit("stack-cleared",[{undoStackCleared:t,redoStackCleared:e}])}))}stopCapturing(){this.lastChange=0}undo(){let t;this.undoing=!0;try{t=On(this,this.undoStack,"undo")}finally{this.undoing=!1}return t}redo(){let t;this.redoing=!0;try{t=On(this,this.redoStack,"redo")}finally{this.redoing=!1}return t}canUndo(){return this.undoStack.length>0}canRedo(){return this.redoStack.length>0}destroy(){this.trackedOrigins.delete(this),this.doc.off("afterTransaction",this.afterTransactionHandler),super.destroy()}}class Nn{constructor(t,e){this.gen=function*(t){const e=ht(t.restDecoder);for(let n=0;n<e;n++){const e=ht(t.restDecoder),n=t.readClient();let r=ht(t.restDecoder);for(let s=0;s<e;s++){const e=t.readInfo();if(10===e){const e=ht(t.restDecoder);yield new Is(Ne(n,r),e),r+=e}else if(0!=(31&e)){const s=0==(192&e),i=new Ms(Ne(n,r),null,(e&U)===U?t.readLeftID():null,null,(e&A)===A?t.readRightID():null,s?t.readParentInfo()?t.readString():t.readLeftID():null,s&&32==(32&e)?t.readString():null,xs(t,e));yield i,r+=i.length}else{const e=t.readLen();yield new os(Ne(n,r),e),r+=e}}}}(t),this.curr=null,this.done=!1,this.filterSkips=e,this.next()}next(){do{this.curr=this.gen.next().value||null}while(this.filterSkips&&null!==this.curr&&this.curr.constructor===Is);return this.curr}}const Ln=t=>Rn(t,he),Rn=(t,e=de)=>{const n=[],r=new e(it(t)),s=new Nn(r,!1);for(let t=s.curr;null!==t;t=s.next())n.push(t);Wt("Structs: ",n);const i=re(r);Wt("DeleteSet: ",i)},Pn=t=>Vn(t,he),Vn=(t,e=de)=>{const n=[],r=new e(it(t)),s=new Nn(r,!1);for(let t=s.curr;null!==t;t=s.next())n.push(t);return{structs:n,ds:re(r)}};class Fn{constructor(t){this.currClient=0,this.startClock=0,this.written=0,this.encoder=t,this.clientStructs=[]}}const Jn=t=>$n(t,he,ge),Wn=(t,e=fe,n=de)=>{const r=new e,s=new Nn(new n(it(t)),!1);let i=s.curr;if(null!==i){let t=0,e=i.id.client,n=0!==i.id.clock,o=n?0:i.id.clock+i.length;for(;null!==i;i=s.next())e!==i.id.client&&(0!==o&&(t++,P(r.restEncoder,e),P(r.restEncoder,o)),e=i.id.client,o=0,n=0!==i.id.clock),i.constructor===Is&&(n=!0),n||(o=i.id.clock+i.length);0!==o&&(t++,P(r.restEncoder,e),P(r.restEncoder,o));const c=I();return P(c,t),((t,e)=>{Y(t,N(e))})(c,r.restEncoder),r.restEncoder=c,r.toUint8Array()}return P(r.restEncoder,0),r.toUint8Array()},Yn=t=>Wn(t,ue,he),jn=(t,e=de)=>{const n=new Map,r=new Map,s=new Nn(new e(it(t)),!1);let i=s.curr;if(null!==i){let t=i.id.client,e=i.id.clock;for(n.set(t,e);null!==i;i=s.next())t!==i.id.client&&(r.set(t,e),n.set(i.id.client,i.id.clock),t=i.id.client),e=i.id.clock+i.length;r.set(t,e)}return{from:n,to:r}},Gn=t=>jn(t,he),zn=(t,e)=>{if(t.constructor===os){const{client:n,clock:r}=t.id;return new os(Ne(n,r+e),t.length-e)}if(t.constructor===Is){const{client:n,clock:r}=t.id;return new Is(Ne(n,r+e),t.length-e)}{const n=t,{client:r,clock:s}=n.id;return new Ms(Ne(r,s+e),null,Ne(r,s+e-1),null,n.rightOrigin,n.parent,n.parentSub,n.content.splice(e))}},$n=(t,e=de,n=pe)=>{if(1===t.length)return t[0];const r=t.map((t=>new e(it(t))));let s=r.map((t=>new Nn(t,!0))),i=null;const o=new n,c=new Fn(o);for(;s=s.filter((t=>null!==t.curr)),s.sort(((t,e)=>{if(t.curr.id.client===e.curr.id.client){const n=t.curr.id.clock-e.curr.id.clock;return 0===n?t.curr.constructor===e.curr.constructor?0:t.curr.constructor===Is?1:-1:n}return e.curr.id.client-t.curr.id.client})),0!==s.length;){const t=s[0],e=t.curr.id.client;if(null!==i){let n=t.curr,r=!1;for(;null!==n&&n.id.clock+n.length<=i.struct.id.clock+i.struct.length&&n.id.client>=i.struct.id.client;)n=t.next(),r=!0;if(null===n||n.id.client!==e||r&&n.id.clock>i.struct.id.clock+i.struct.length)continue;if(e!==i.struct.id.client)Xn(c,i.struct,i.offset),i={struct:n,offset:0},t.next();else if(i.struct.id.clock+i.struct.length<n.id.clock)if(i.struct.constructor===Is)i.struct.length=n.id.clock+n.length-i.struct.id.clock;else{Xn(c,i.struct,i.offset);const t=n.id.clock-i.struct.id.clock-i.struct.length;i={struct:new Is(Ne(e,i.struct.id.clock+i.struct.length),t),offset:0}}else{const e=i.struct.id.clock+i.struct.length-n.id.clock;e>0&&(i.struct.constructor===Is?i.struct.length-=e:n=zn(n,e)),i.struct.mergeWith(n)||(Xn(c,i.struct,i.offset),i={struct:n,offset:0},t.next())}}else i={struct:t.curr,offset:0},t.next();for(let n=t.curr;null!==n&&n.id.client===e&&n.id.clock===i.struct.id.clock+i.struct.length&&n.constructor!==Is;n=t.next())Xn(c,i.struct,i.offset),i={struct:n,offset:0}}null!==i&&(Xn(c,i.struct,i.offset),i=null),Zn(c);const l=r.map((t=>re(t))),h=qt(l);return ne(o,h),o.toUint8Array()},Bn=(t,e,n=de,r=pe)=>{const s=Ce(e),o=new r,c=new Fn(o),l=new n(it(t)),h=new Nn(l,!1);for(;h.curr;){const t=h.curr,e=t.id.client,n=s.get(e)||0;if(h.curr.constructor!==Is)if(t.id.clock+t.length>n)for(Xn(c,t,i.Fp(n-t.id.clock,0)),h.next();h.curr&&h.curr.id.client===e;)Xn(c,h.curr,0),h.next();else for(;h.curr&&h.curr.id.client===e&&h.curr.id.clock+h.curr.length<=n;)h.next();else h.next()}Zn(c);const a=re(l);return ne(o,a),o.toUint8Array()},Hn=(t,e)=>Bn(t,e,he,ge),Kn=t=>{t.written>0&&(t.clientStructs.push({written:t.written,restEncoder:N(t.encoder.restEncoder)}),t.encoder.restEncoder=I(),t.written=0)},Xn=(t,e,n)=>{t.written>0&&t.currClient!==e.id.client&&Kn(t),0===t.written&&(t.currClient=e.id.client,t.encoder.writeClient(e.id.client),P(t.encoder.restEncoder,e.id.clock+n)),e.write(t.encoder,n),t.written++},Zn=t=>{Kn(t);const e=t.encoder.restEncoder;P(e,t.clientStructs.length);for(let n=0;n<t.clientStructs.length;n++){const r=t.clientStructs[n];P(e,r.written),Y(e,r.restEncoder)}},qn=(t,e,n,r)=>{const s=new n(it(t)),i=new Nn(s,!1),o=new r,c=new Fn(o);for(let t=i.curr;null!==t;t=i.next())Xn(c,e(t),0);Zn(c);const l=re(s);return ne(o,l),o.toUint8Array()},Qn=({formatting:t=!0,subdocs:e=!0,yxml:n=!0}={})=>{let r=0;const i=o.Ue(),c=o.Ue(),l=o.Ue(),h=o.Ue();return h.set(null,null),a=>{switch(a.constructor){case os:case Is:return a;case Ms:{const g=a,f=g.content;switch(f.constructor){case ls:break;case Cs:if(n){const t=f.type;t instanceof es&&(t.nodeName=o.Yu(c,t.nodeName,(()=>"node-"+r))),t instanceof rs&&(t.hookName=o.Yu(c,t.hookName,(()=>"hook-"+r)))}break;case fs:{const t=f;t.arr=t.arr.map((()=>r));break}case cs:f.content=new Uint8Array([r]);break;case as:{const t=f;e&&(t.opts={},t.doc.guid=r+"");break}case ds:f.embed={};break;case us:{const e=f;t&&(e.key=o.Yu(l,e.key,(()=>r+"")),e.value=o.Yu(h,e.value,(()=>({i:r}))));break}case gs:{const t=f;t.arr=t.arr.map((()=>r));break}case ps:{const t=f;t.str=(d=r%10+"",u=t.str.length,s.JJ(u,(()=>d)).join(""));break}default:et()}return g.parentSub&&(g.parentSub=o.Yu(i,g.parentSub,(()=>r+""))),r++,a}default:et()}var d,u}},tr=(t,e)=>qn(t,Qn(e),he,ge),er=(t,e)=>qn(t,Qn(e),de,pe),nr=t=>qn(t,m.id,he,pe),rr=t=>qn(t,m.id,de,ge),sr="You must not compute changes after the event-handler fired.";class ir{constructor(t,e){this.target=t,this.currentTarget=t,this.transaction=e,this._changes=null,this._keys=null,this._delta=null,this._path=null}get path(){return this._path||(this._path=or(this.currentTarget,this.target))}deletes(t){return Xt(this.transaction.deleteSet,t.id)}get keys(){if(null===this._keys){if(0===this.transaction.doc._transactionCleanups.length)throw Q(sr);const t=new Map,e=this.target;this.transaction.changed.get(e).forEach((n=>{if(null!==n){const r=e._map.get(n);let i,o;if(this.adds(r)){let t=r.left;for(;null!==t&&this.adds(t);)t=t.left;if(this.deletes(r)){if(null===t||!this.deletes(t))return;i="delete",o=s.Z$(t.content.getContent())}else null!==t&&this.deletes(t)?(i="update",o=s.Z$(t.content.getContent())):(i="add",o=void 0)}else{if(!this.deletes(r))return;i="delete",o=s.Z$(r.content.getContent())}t.set(n,{action:i,oldValue:o})}})),this._keys=t}return this._keys}get delta(){return this.changes.delta}adds(t){return t.id.clock>=(this.transaction.beforeState.get(t.id.client)||0)}get changes(){let t=this._changes;if(null===t){if(0===this.transaction.doc._transactionCleanups.length)throw Q(sr);const e=this.target,n=Ct.Ue(),r=Ct.Ue(),s=[];if(t={added:n,deleted:r,delta:s,keys:this.keys},this.transaction.changed.get(e).has(null)){let t=null;const i=()=>{t&&s.push(t)};for(let s=e._start;null!==s;s=s.right)s.deleted?this.deletes(s)&&!this.adds(s)&&(null!==t&&void 0!==t.delete||(i(),t={delete:0}),t.delete+=s.length,r.add(s)):this.adds(s)?(null!==t&&void 0!==t.insert||(i(),t={insert:[]}),t.insert=t.insert.concat(s.content.getContent()),n.add(s)):(null!==t&&void 0!==t.retain||(i(),t={retain:0}),t.retain+=s.length);null!==t&&void 0===t.retain&&i()}this._changes=t}return t}}const or=(t,e)=>{const n=[];for(;null!==e._item&&e!==t;){if(null!==e._item.parentSub)n.unshift(e._item.parentSub);else{let t=0,r=e._item.parent._start;for(;r!==e._item&&null!==r;)r.deleted||t++,r=r.right;n.unshift(t)}e=e._item.parent}return n};let cr=0;class lr{constructor(t,e){t.marker=!0,this.p=t,this.index=e,this.timestamp=cr++}}const hr=(t,e,n)=>{t.p.marker=!1,t.p=e,e.marker=!0,t.index=n,t.timestamp=cr++},ar=(t,e)=>{if(null===t._start||0===e||null===t._searchMarker)return null;const n=0===t._searchMarker.length?null:t._searchMarker.reduce(((t,n)=>i.Wn(e-t.index)<i.Wn(e-n.index)?t:n));let r=t._start,s=0;for(null!==n&&(r=n.p,s=n.index,(t=>{t.timestamp=cr++})(n));null!==r.right&&s<e;){if(!r.deleted&&r.countable){if(e<s+r.length)break;s+=r.length}r=r.right}for(;null!==r.left&&s>e;)r=r.left,!r.deleted&&r.countable&&(s-=r.length);for(;null!==r.left&&r.left.id.client===r.id.client&&r.left.id.clock+r.left.length===r.id.clock;)r=r.left,!r.deleted&&r.countable&&(s-=r.length);return null!==n&&i.Wn(n.index-s)<r.parent.length/80?(hr(n,r,s),n):((t,e,n)=>{if(t.length>=80){const r=t.reduce(((t,e)=>t.timestamp<e.timestamp?t:e));return hr(r,e,n),r}{const r=new lr(e,n);return t.push(r),r}})(t._searchMarker,r,s)},dr=(t,e,n)=>{for(let r=t.length-1;r>=0;r--){const s=t[r];if(n>0){let e=s.p;for(e.marker=!1;e&&(e.deleted||!e.countable);)e=e.left,e&&!e.deleted&&e.countable&&(s.index-=e.length);if(null===e||!0===e.marker){t.splice(r,1);continue}s.p=e,e.marker=!0}(e<s.index||n>0&&e===s.index)&&(s.index=i.Fp(e,s.index+n))}},ur=t=>{let e=t._start;const n=[];for(;e;)n.push(e),e=e.right;return n},gr=(t,e,n)=>{const r=t,s=e.changedParentTypes;for(;o.Yu(s,t,(()=>[])).push(n),null!==t._item;)t=t._item.parent;xe(r._eH,n,e)};class fr{constructor(){this._item=null,this._map=new Map,this._start=null,this.doc=null,this._length=0,this._eH=Ue(),this._dEH=Ue(),this._searchMarker=null}get parent(){return this._item?this._item.parent:null}_integrate(t,e){this.doc=t,this._item=e}_copy(){throw tt()}clone(){throw tt()}_write(t){}get _first(){let t=this._start;for(;null!==t&&t.deleted;)t=t.right;return t}_callObserver(t,e){!t.local&&this._searchMarker&&(this._searchMarker.length=0)}observe(t){Te(this._eH,t)}observeDeep(t){Te(this._dEH,t)}unobserve(t){Me(this._eH,t)}unobserveDeep(t){Me(this._dEH,t)}toJSON(){}}const pr=(t,e,n)=>{e<0&&(e=t._length+e),n<0&&(n=t._length+n);let r=n-e;const s=[];let i=t._start;for(;null!==i&&r>0;){if(i.countable&&!i.deleted){const t=i.content.getContent();if(t.length<=e)e-=t.length;else{for(let n=e;n<t.length&&r>0;n++)s.push(t[n]),r--;e=0}}i=i.right}return s},wr=t=>{const e=[];let n=t._start;for(;null!==n;){if(n.countable&&!n.deleted){const t=n.content.getContent();for(let n=0;n<t.length;n++)e.push(t[n])}n=n.right}return e},mr=(t,e)=>{const n=[];let r=t._start;for(;null!==r;){if(r.countable&&cn(r,e)){const t=r.content.getContent();for(let e=0;e<t.length;e++)n.push(t[e])}r=r.right}return n},kr=(t,e)=>{let n=0,r=t._start;for(;null!==r;){if(r.countable&&!r.deleted){const s=r.content.getContent();for(let r=0;r<s.length;r++)e(s[r],n++,t)}r=r.right}},yr=(t,e)=>{const n=[];return kr(t,((r,s)=>{n.push(e(r,s,t))})),n},br=t=>{let e=t._start,n=null,r=0;return{[Symbol.iterator](){return this},next:()=>{if(null===n){for(;null!==e&&e.deleted;)e=e.right;if(null===e)return{done:!0,value:void 0};n=e.content.getContent(),r=0,e=e.right}const t=n[r++];return n.length<=r&&(n=null),{done:!1,value:t}}}},_r=(t,e)=>{const n=ar(t,e);let r=t._start;for(null!==n&&(r=n.p,e-=n.index);null!==r;r=r.right)if(!r.deleted&&r.countable){if(e<r.length)return r.content.getContent()[e];e-=r.length}},Sr=(t,e,n,r)=>{let s=n;const i=t.doc,o=i.clientID,c=i.store,l=null===n?e._start:n.right;let h=[];const a=()=>{h.length>0&&(s=new Ms(Ne(o,gn(c,o)),s,s&&s.lastId,l,l&&l.id,e,null,new fs(h)),s.integrate(t,0),h=[])};r.forEach((n=>{if(null===n)h.push(n);else switch(n.constructor){case Number:case Object:case Boolean:case Array:case String:h.push(n);break;default:switch(a(),n.constructor){case Uint8Array:case ArrayBuffer:s=new Ms(Ne(o,gn(c,o)),s,s&&s.lastId,l,l&&l.id,e,null,new cs(new Uint8Array(n))),s.integrate(t,0);break;case ce:s=new Ms(Ne(o,gn(c,o)),s,s&&s.lastId,l,l&&l.id,e,null,new as(n)),s.integrate(t,0);break;default:if(!(n instanceof fr))throw new Error("Unexpected content type in insert operation");s=new Ms(Ne(o,gn(c,o)),s,s&&s.lastId,l,l&&l.id,e,null,new Cs(n)),s.integrate(t,0)}}})),a()},Er=()=>Q("Length exceeded!"),Cr=(t,e,n,r)=>{if(n>e._length)throw Er();if(0===n)return e._searchMarker&&dr(e._searchMarker,n,r.length),Sr(t,e,null,r);const s=n,i=ar(e,n);let o=e._start;for(null!==i&&(o=i.p,0==(n-=i.index)&&(o=o.prev,n+=o&&o.countable&&!o.deleted?o.length:0));null!==o;o=o.right)if(!o.deleted&&o.countable){if(n<=o.length){n<o.length&&kn(t,Ne(o.id.client,o.id.clock+n));break}n-=o.length}return e._searchMarker&&dr(e._searchMarker,s,r.length),Sr(t,e,o,r)},Dr=(t,e,n,r)=>{if(0===r)return;const s=n,i=r,o=ar(e,n);let c=e._start;for(null!==o&&(c=o.p,n-=o.index);null!==c&&n>0;c=c.right)!c.deleted&&c.countable&&(n<c.length&&kn(t,Ne(c.id.client,c.id.clock+n)),n-=c.length);for(;r>0&&null!==c;)c.deleted||(r<c.length&&kn(t,Ne(c.id.client,c.id.clock+r)),c.delete(t),r-=c.length),c=c.right;if(r>0)throw Er();e._searchMarker&&dr(e._searchMarker,s,-i+r)},vr=(t,e,n)=>{const r=e._map.get(n);void 0!==r&&r.delete(t)},Ar=(t,e,n,r)=>{const s=e._map.get(n)||null,i=t.doc,o=i.clientID;let c;if(null==r)c=new fs([r]);else switch(r.constructor){case Number:case Object:case Boolean:case Array:case String:c=new fs([r]);break;case Uint8Array:c=new cs(r);break;case ce:c=new as(r);break;default:if(!(r instanceof fr))throw new Error("Unexpected content type");c=new Cs(r)}new Ms(Ne(o,gn(i.store,o)),s,s&&s.lastId,null,null,e,n,c).integrate(t,0)},Ur=(t,e)=>{const n=t._map.get(e);return void 0===n||n.deleted?void 0:n.content.getContent()[n.length-1]},Tr=t=>{const e={};return t._map.forEach(((t,n)=>{t.deleted||(e[n]=t.content.getContent()[t.length-1])})),e},Mr=(t,e)=>{const n=t._map.get(e);return void 0!==n&&!n.deleted},xr=(t,e,n)=>{let r=t._map.get(e)||null;for(;null!==r&&(!n.sv.has(r.id.client)||r.id.clock>=(n.sv.get(r.id.client)||0));)r=r.left;return null!==r&&cn(r,n)?r.content.getContent()[r.length-1]:void 0},Or=t=>{return e=t.entries(),n=t=>!t[1].deleted,jt((()=>{let t;do{t=e.next()}while(!t.done&&!n(t.value));return t}));var e,n};class Ir extends ir{constructor(t,e){super(t,e),this._transaction=e}}class Nr extends fr{constructor(){super(),this._prelimContent=[],this._searchMarker=[]}static from(t){const e=new Nr;return e.push(t),e}_integrate(t,e){super._integrate(t,e),this.insert(0,this._prelimContent),this._prelimContent=null}_copy(){return new Nr}clone(){const t=new Nr;return t.insert(0,this.toArray().map((t=>t instanceof fr?t.clone():t))),t}get length(){return null===this._prelimContent?this._length:this._prelimContent.length}_callObserver(t,e){super._callObserver(t,e),gr(this,t,new Ir(this,t))}insert(t,e){null!==this.doc?Tn(this.doc,(n=>{Cr(n,this,t,e)})):this._prelimContent.splice(t,0,...e)}push(t){null!==this.doc?Tn(this.doc,(e=>{((t,e,n)=>{let r=(e._searchMarker||[]).reduce(((t,e)=>e.index>t.index?e:t),{index:0,p:e._start}).p;if(r)for(;r.right;)r=r.right;Sr(t,e,r,n)})(e,this,t)})):this._prelimContent.push(...t)}unshift(t){this.insert(0,t)}delete(t,e=1){null!==this.doc?Tn(this.doc,(n=>{Dr(n,this,t,e)})):this._prelimContent.splice(t,e)}get(t){return _r(this,t)}toArray(){return wr(this)}slice(t=0,e=this.length){return pr(this,t,e)}toJSON(){return this.map((t=>t instanceof fr?t.toJSON():t))}map(t){return yr(this,t)}forEach(t){kr(this,t)}[Symbol.iterator](){return br(this)}_write(t){t.writeTypeRef(ms)}}class Lr extends ir{constructor(t,e,n){super(t,e),this.keysChanged=n}}class Rr extends fr{constructor(t){super(),this._prelimContent=null,this._prelimContent=void 0===t?new Map:new Map(t)}_integrate(t,e){super._integrate(t,e),this._prelimContent.forEach(((t,e)=>{this.set(e,t)})),this._prelimContent=null}_copy(){return new Rr}clone(){const t=new Rr;return this.forEach(((e,n)=>{t.set(n,e instanceof fr?e.clone():e)})),t}_callObserver(t,e){gr(this,t,new Lr(this,t,e))}toJSON(){const t={};return this._map.forEach(((e,n)=>{if(!e.deleted){const r=e.content.getContent()[e.length-1];t[n]=r instanceof fr?r.toJSON():r}})),t}get size(){return[...Or(this._map)].length}keys(){return Gt(Or(this._map),(t=>t[0]))}values(){return Gt(Or(this._map),(t=>t[1].content.getContent()[t[1].length-1]))}entries(){return Gt(Or(this._map),(t=>[t[0],t[1].content.getContent()[t[1].length-1]]))}forEach(t){this._map.forEach(((e,n)=>{e.deleted||t(e.content.getContent()[e.length-1],n,this)}))}[Symbol.iterator](){return this.entries()}delete(t){null!==this.doc?Tn(this.doc,(e=>{vr(e,this,t)})):this._prelimContent.delete(t)}set(t,e){return null!==this.doc?Tn(this.doc,(n=>{Ar(n,this,t,e)})):this._prelimContent.set(t,e),e}get(t){return Ur(this,t)}has(t){return Mr(this,t)}clear(){null!==this.doc?Tn(this.doc,(t=>{this.forEach((function(e,n,r){vr(t,r,n)}))})):this._prelimContent.clear()}_write(t){t.writeTypeRef(ks)}}const Pr=(t,e)=>t===e||"object"==typeof t&&"object"==typeof e&&t&&e&&zt.$m(t,e);class Vr{constructor(t,e,n,r){this.left=t,this.right=e,this.index=n,this.currentAttributes=r}forward(){null===this.right&&et(),this.right.content.constructor===us?this.right.deleted||Yr(this.currentAttributes,this.right.content):this.right.deleted||(this.index+=this.right.length),this.left=this.right,this.right=this.right.right}}const Fr=(t,e,n)=>{for(;null!==e.right&&n>0;)e.right.content.constructor===us?e.right.deleted||Yr(e.currentAttributes,e.right.content):e.right.deleted||(n<e.right.length&&kn(t,Ne(e.right.id.client,e.right.id.clock+n)),e.index+=e.right.length,n-=e.right.length),e.left=e.right,e.right=e.right.right;return e},Jr=(t,e,n)=>{const r=new Map,s=ar(e,n);if(s){const e=new Vr(s.p.left,s.p,s.index,r);return Fr(t,e,n-s.index)}{const s=new Vr(null,e._start,0,r);return Fr(t,s,n)}},Wr=(t,e,n,r)=>{for(;null!==n.right&&(!0===n.right.deleted||n.right.content.constructor===us&&Pr(r.get(n.right.content.key),n.right.content.value));)n.right.deleted||r.delete(n.right.content.key),n.forward();const s=t.doc,i=s.clientID;r.forEach(((r,o)=>{const c=n.left,l=n.right,h=new Ms(Ne(i,gn(s.store,i)),c,c&&c.lastId,l,l&&l.id,e,null,new us(o,r));h.integrate(t,0),n.right=h,n.forward()}))},Yr=(t,e)=>{const{key:n,value:r}=e;null===r?t.delete(n):t.set(n,r)},jr=(t,e)=>{for(;null!==t.right&&(t.right.deleted||t.right.content.constructor===us&&Pr(e[t.right.content.key]||null,t.right.content.value));)t.forward()},Gr=(t,e,n,r)=>{const s=t.doc,i=s.clientID,o=new Map;for(const c in r){const l=r[c],h=n.currentAttributes.get(c)||null;if(!Pr(h,l)){o.set(c,h);const{left:r,right:a}=n;n.right=new Ms(Ne(i,gn(s.store,i)),r,r&&r.lastId,a,a&&a.id,e,null,new us(c,l)),n.right.integrate(t,0),n.forward()}}return o},zr=(t,e,n,r,s)=>{n.currentAttributes.forEach(((t,e)=>{void 0===s[e]&&(s[e]=null)}));const i=t.doc,o=i.clientID;jr(n,s);const c=Gr(t,e,n,s),l=r.constructor===String?new ps(r):r instanceof fr?new Cs(r):new ds(r);let{left:h,right:a,index:d}=n;e._searchMarker&&dr(e._searchMarker,n.index,l.getLength()),a=new Ms(Ne(o,gn(i.store,o)),h,h&&h.lastId,a,a&&a.id,e,null,l),a.integrate(t,0),n.right=a,n.index=d,n.forward(),Wr(t,e,n,c)},$r=(t,e,n,r,s)=>{const i=t.doc,o=i.clientID;jr(n,s);const c=Gr(t,e,n,s);t:for(;null!==n.right&&(r>0||c.size>0&&(n.right.deleted||n.right.content.constructor===us));){if(!n.right.deleted)switch(n.right.content.constructor){case us:{const{key:e,value:i}=n.right.content,o=s[e];if(void 0!==o){if(Pr(o,i))c.delete(e);else{if(0===r)break t;c.set(e,i)}n.right.delete(t)}else n.currentAttributes.set(e,i);break}default:r<n.right.length&&kn(t,Ne(n.right.id.client,n.right.id.clock+r)),r-=n.right.length}n.forward()}if(r>0){let s="";for(;r>0;r--)s+="\n";n.right=new Ms(Ne(o,gn(i.store,o)),n.left,n.left&&n.left.lastId,n.right,n.right&&n.right.id,e,null,new ps(s)),n.right.integrate(t,0),n.forward()}Wr(t,e,n,c)},Br=(t,e,n,r,s)=>{let i=e;const c=o.Ue();for(;i&&(!i.countable||i.deleted);){if(!i.deleted&&i.content.constructor===us){const t=i.content;c.set(t.key,t)}i=i.right}let l=0,h=!1;for(;e!==i;){if(n===e&&(h=!0),!e.deleted){const n=e.content;switch(n.constructor){case us:{const{key:i,value:o}=n,a=r.get(i)||null;c.get(i)===n&&a!==o||(e.delete(t),l++,h||(s.get(i)||null)!==o||a===o||(null===a?s.delete(i):s.set(i,a))),h||e.deleted||Yr(s,n);break}}}e=e.right}return l},Hr=t=>{let e=0;return Tn(t.doc,(n=>{let r=t._start,s=t._start,i=o.Ue();const c=o.JG(i);for(;s;)!1===s.deleted&&(s.content.constructor===us?Yr(c,s.content):(e+=Br(n,r,s,i,c),i=o.JG(c),r=s)),s=s.right})),e},Kr=t=>{const e=new Set,n=t.doc;for(const[r,s]of t.afterState.entries()){const i=t.beforeState.get(r)||0;s!==i&&bn(t,n.store.clients.get(r),i,s,(t=>{t.deleted||t.content.constructor!==us||t.constructor===os||e.add(t.parent)}))}Tn(n,(n=>{Kt(t,t.deleteSet,(t=>{if(t instanceof os||!t.parent._hasFormatting||e.has(t.parent))return;const r=t.parent;t.content.constructor===us?e.add(r):((t,e)=>{for(;e&&e.right&&(e.right.deleted||!e.right.countable);)e=e.right;const n=new Set;for(;e&&(e.deleted||!e.countable);){if(!e.deleted&&e.content.constructor===us){const r=e.content.key;n.has(r)?e.delete(t):n.add(r)}e=e.left}})(n,t)}));for(const t of e)Hr(t)}))},Xr=(t,e,n)=>{const r=n,s=o.JG(e.currentAttributes),i=e.right;for(;n>0&&null!==e.right;){if(!1===e.right.deleted)switch(e.right.content.constructor){case Cs:case ds:case ps:n<e.right.length&&kn(t,Ne(e.right.id.client,e.right.id.clock+n)),n-=e.right.length,e.right.delete(t)}e.forward()}i&&Br(t,i,e.right,s,e.currentAttributes);const c=(e.left||e.right).parent;return c._searchMarker&&dr(c._searchMarker,e.index,-r+n),e};class Zr extends ir{constructor(t,e,n){super(t,e),this.childListChanged=!1,this.keysChanged=new Set,n.forEach((t=>{null===t?this.childListChanged=!0:this.keysChanged.add(t)}))}get changes(){if(null===this._changes){const t={keys:this.keys,delta:this.delta,added:new Set,deleted:new Set};this._changes=t}return this._changes}get delta(){if(null===this._delta){const t=this.target.doc,e=[];Tn(t,(t=>{const n=new Map,r=new Map;let s=this.target._start,i=null;const o={};let c="",l=0,h=0;const a=()=>{if(null!==i){let t=null;switch(i){case"delete":h>0&&(t={delete:h}),h=0;break;case"insert":("object"==typeof c||c.length>0)&&(t={insert:c},n.size>0&&(t.attributes={},n.forEach(((e,n)=>{null!==e&&(t.attributes[n]=e)})))),c="";break;case"retain":l>0&&(t={retain:l},zt.xb(o)||(t.attributes=zt.f0({},o))),l=0}t&&e.push(t),i=null}};for(;null!==s;){switch(s.content.constructor){case Cs:case ds:this.adds(s)?this.deletes(s)||(a(),i="insert",c=s.content.getContent()[0],a()):this.deletes(s)?("delete"!==i&&(a(),i="delete"),h+=1):s.deleted||("retain"!==i&&(a(),i="retain"),l+=1);break;case ps:this.adds(s)?this.deletes(s)||("insert"!==i&&(a(),i="insert"),c+=s.content.str):this.deletes(s)?("delete"!==i&&(a(),i="delete"),h+=s.length):s.deleted||("retain"!==i&&(a(),i="retain"),l+=s.length);break;case us:{const{key:e,value:c}=s.content;if(this.adds(s)){if(!this.deletes(s)){const l=n.get(e)||null;Pr(l,c)?null!==c&&s.delete(t):("retain"===i&&a(),Pr(c,r.get(e)||null)?delete o[e]:o[e]=c)}}else if(this.deletes(s)){r.set(e,c);const t=n.get(e)||null;Pr(t,c)||("retain"===i&&a(),o[e]=t)}else if(!s.deleted){r.set(e,c);const n=o[e];void 0!==n&&(Pr(n,c)?null!==n&&s.delete(t):("retain"===i&&a(),null===c?delete o[e]:o[e]=c))}s.deleted||("insert"===i&&a(),Yr(n,s.content));break}}s=s.right}for(a();e.length>0;){const t=e[e.length-1];if(void 0===t.retain||void 0!==t.attributes)break;e.pop()}})),this._delta=e}return this._delta}}class qr extends fr{constructor(t){super(),this._pending=void 0!==t?[()=>this.insert(0,t)]:[],this._searchMarker=[],this._hasFormatting=!1}get length(){return this._length}_integrate(t,e){super._integrate(t,e);try{this._pending.forEach((t=>t()))}catch(t){console.error(t)}this._pending=null}_copy(){return new qr}clone(){const t=new qr;return t.applyDelta(this.toDelta()),t}_callObserver(t,e){super._callObserver(t,e);const n=new Zr(this,t,e);gr(this,t,n),!t.local&&this._hasFormatting&&(t._needFormattingCleanup=!0)}toString(){let t="",e=this._start;for(;null!==e;)!e.deleted&&e.countable&&e.content.constructor===ps&&(t+=e.content.str),e=e.right;return t}toJSON(){return this.toString()}applyDelta(t,{sanitize:e=!0}={}){null!==this.doc?Tn(this.doc,(n=>{const r=new Vr(null,this._start,0,new Map);for(let s=0;s<t.length;s++){const i=t[s];if(void 0!==i.insert){const o=e||"string"!=typeof i.insert||s!==t.length-1||null!==r.right||"\n"!==i.insert.slice(-1)?i.insert:i.insert.slice(0,-1);("string"!=typeof o||o.length>0)&&zr(n,this,r,o,i.attributes||{})}else void 0!==i.retain?$r(n,this,r,i.retain,i.attributes||{}):void 0!==i.delete&&Xr(n,r,i.delete)}})):this._pending.push((()=>this.applyDelta(t)))}toDelta(t,e,n){const r=[],s=new Map,i=this.doc;let o="",c=this._start;function l(){if(o.length>0){const t={};let e=!1;s.forEach(((n,r)=>{e=!0,t[r]=n}));const n={insert:o};e&&(n.attributes=t),r.push(n),o=""}}const h=()=>{for(;null!==c;){if(cn(c,t)||void 0!==e&&cn(c,e))switch(c.content.constructor){case ps:{const r=s.get("ychange");void 0===t||cn(c,t)?void 0===e||cn(c,e)?void 0!==r&&(l(),s.delete("ychange")):void 0!==r&&r.user===c.id.client&&"added"===r.type||(l(),s.set("ychange",n?n("added",c.id):{type:"added"})):void 0!==r&&r.user===c.id.client&&"removed"===r.type||(l(),s.set("ychange",n?n("removed",c.id):{type:"removed"})),o+=c.content.str;break}case Cs:case ds:{l();const t={insert:c.content.getContent()[0]};if(s.size>0){const e={};t.attributes=e,s.forEach(((t,n)=>{e[n]=t}))}r.push(t);break}case us:cn(c,t)&&(l(),Yr(s,c.content))}c=c.right}l()};return t||e?Tn(i,(n=>{t&&ln(n,t),e&&ln(n,e),h()}),"cleanup"):h(),r}insert(t,e,n){if(e.length<=0)return;const r=this.doc;null!==r?Tn(r,(r=>{const s=Jr(r,this,t);n||(n={},s.currentAttributes.forEach(((t,e)=>{n[e]=t}))),zr(r,this,s,e,n)})):this._pending.push((()=>this.insert(t,e,n)))}insertEmbed(t,e,n={}){const r=this.doc;null!==r?Tn(r,(r=>{const s=Jr(r,this,t);zr(r,this,s,e,n)})):this._pending.push((()=>this.insertEmbed(t,e,n)))}delete(t,e){if(0===e)return;const n=this.doc;null!==n?Tn(n,(n=>{Xr(n,Jr(n,this,t),e)})):this._pending.push((()=>this.delete(t,e)))}format(t,e,n){if(0===e)return;const r=this.doc;null!==r?Tn(r,(r=>{const s=Jr(r,this,t);null!==s.right&&$r(r,this,s,e,n)})):this._pending.push((()=>this.format(t,e,n)))}removeAttribute(t){null!==this.doc?Tn(this.doc,(e=>{vr(e,this,t)})):this._pending.push((()=>this.removeAttribute(t)))}setAttribute(t,e){null!==this.doc?Tn(this.doc,(n=>{Ar(n,this,t,e)})):this._pending.push((()=>this.setAttribute(t,e)))}getAttribute(t){return Ur(this,t)}getAttributes(){return Tr(this)}_write(t){t.writeTypeRef(ys)}}class Qr{constructor(t,e=(()=>!0)){this._filter=e,this._root=t,this._currentNode=t._start,this._firstCall=!0}[Symbol.iterator](){return this}next(){let t=this._currentNode,e=t&&t.content&&t.content.type;if(null!==t&&(!this._firstCall||t.deleted||!this._filter(e)))do{if(e=t.content.type,t.deleted||e.constructor!==es&&e.constructor!==ts||null===e._start)for(;null!==t;){if(null!==t.right){t=t.right;break}t=t.parent===this._root?null:t.parent._item}else t=e._start}while(null!==t&&(t.deleted||!this._filter(t.content.type)));return this._firstCall=!1,null===t?{value:void 0,done:!0}:(this._currentNode=t,{value:t.content.type,done:!1})}}class ts extends fr{constructor(){super(),this._prelimContent=[]}get firstChild(){const t=this._first;return t?t.content.getContent()[0]:null}_integrate(t,e){super._integrate(t,e),this.insert(0,this._prelimContent),this._prelimContent=null}_copy(){return new ts}clone(){const t=new ts;return t.insert(0,this.toArray().map((t=>t instanceof fr?t.clone():t))),t}get length(){return null===this._prelimContent?this._length:this._prelimContent.length}createTreeWalker(t){return new Qr(this,t)}querySelector(t){t=t.toUpperCase();const e=new Qr(this,(e=>e.nodeName&&e.nodeName.toUpperCase()===t)).next();return e.done?null:e.value}querySelectorAll(t){return t=t.toUpperCase(),s.Dp(new Qr(this,(e=>e.nodeName&&e.nodeName.toUpperCase()===t)))}_callObserver(t,e){gr(this,t,new ns(this,e,t))}toString(){return yr(this,(t=>t.toString())).join("")}toJSON(){return this.toString()}toDOM(t=document,e={},n){const r=t.createDocumentFragment();return void 0!==n&&n._createAssociation(r,this),kr(this,(s=>{r.insertBefore(s.toDOM(t,e,n),null)})),r}insert(t,e){null!==this.doc?Tn(this.doc,(n=>{Cr(n,this,t,e)})):this._prelimContent.splice(t,0,...e)}insertAfter(t,e){if(null!==this.doc)Tn(this.doc,(n=>{const r=t&&t instanceof fr?t._item:t;Sr(n,this,r,e)}));else{const n=this._prelimContent,r=null===t?0:n.findIndex((e=>e===t))+1;if(0===r&&null!==t)throw Q("Reference item not found");n.splice(r,0,...e)}}delete(t,e=1){null!==this.doc?Tn(this.doc,(n=>{Dr(n,this,t,e)})):this._prelimContent.splice(t,e)}toArray(){return wr(this)}push(t){this.insert(this.length,t)}unshift(t){this.insert(0,t)}get(t){return _r(this,t)}slice(t=0,e=this.length){return pr(this,t,e)}forEach(t){kr(this,t)}_write(t){t.writeTypeRef(_s)}}class es extends ts{constructor(t="UNDEFINED"){super(),this.nodeName=t,this._prelimAttrs=new Map}get nextSibling(){const t=this._item?this._item.next:null;return t?t.content.type:null}get prevSibling(){const t=this._item?this._item.prev:null;return t?t.content.type:null}_integrate(t,e){super._integrate(t,e),this._prelimAttrs.forEach(((t,e)=>{this.setAttribute(e,t)})),this._prelimAttrs=null}_copy(){return new es(this.nodeName)}clone(){const t=new es(this.nodeName),e=this.getAttributes();return zt.Ed(e,((e,n)=>{"string"==typeof e&&t.setAttribute(n,e)})),t.insert(0,this.toArray().map((t=>t instanceof fr?t.clone():t))),t}toString(){const t=this.getAttributes(),e=[],n=[];for(const e in t)n.push(e);n.sort();const r=n.length;for(let s=0;s<r;s++){const r=n[s];e.push(r+'="'+t[r]+'"')}const s=this.nodeName.toLocaleLowerCase();return`<${s}${e.length>0?" "+e.join(" "):""}>${super.toString()}</${s}>`}removeAttribute(t){null!==this.doc?Tn(this.doc,(e=>{vr(e,this,t)})):this._prelimAttrs.delete(t)}setAttribute(t,e){null!==this.doc?Tn(this.doc,(n=>{Ar(n,this,t,e)})):this._prelimAttrs.set(t,e)}getAttribute(t){return Ur(this,t)}hasAttribute(t){return Mr(this,t)}getAttributes(){return Tr(this)}toDOM(t=document,e={},n){const r=t.createElement(this.nodeName),s=this.getAttributes();for(const t in s){const e=s[t];"string"==typeof e&&r.setAttribute(t,e)}return kr(this,(s=>{r.appendChild(s.toDOM(t,e,n))})),void 0!==n&&n._createAssociation(r,this),r}_write(t){t.writeTypeRef(bs),t.writeKey(this.nodeName)}}class ns extends ir{constructor(t,e,n){super(t,n),this.childListChanged=!1,this.attributesChanged=new Set,e.forEach((t=>{null===t?this.childListChanged=!0:this.attributesChanged.add(t)}))}}class rs extends Rr{constructor(t){super(),this.hookName=t}_copy(){return new rs(this.hookName)}clone(){const t=new rs(this.hookName);return this.forEach(((e,n)=>{t.set(n,e)})),t}toDOM(t=document,e={},n){const r=e[this.hookName];let s;return s=void 0!==r?r.createDom(this):document.createElement(this.hookName),s.setAttribute("data-yjs-hook",this.hookName),void 0!==n&&n._createAssociation(s,this),s}_write(t){t.writeTypeRef(Ss),t.writeKey(this.hookName)}}class ss extends qr{get nextSibling(){const t=this._item?this._item.next:null;return t?t.content.type:null}get prevSibling(){const t=this._item?this._item.prev:null;return t?t.content.type:null}_copy(){return new ss}clone(){const t=new ss;return t.applyDelta(this.toDelta()),t}toDOM(t=document,e,n){const r=t.createTextNode(this.toString());return void 0!==n&&n._createAssociation(r,this),r}toString(){return this.toDelta().map((t=>{const e=[];for(const n in t.attributes){const r=[];for(const e in t.attributes[n])r.push({key:e,value:t.attributes[n][e]});r.sort(((t,e)=>t.key<e.key?-1:1)),e.push({nodeName:n,attrs:r})}e.sort(((t,e)=>t.nodeName<e.nodeName?-1:1));let n="";for(let t=0;t<e.length;t++){const r=e[t];n+=`<${r.nodeName}`;for(let t=0;t<r.attrs.length;t++){const e=r.attrs[t];n+=` ${e.key}="${e.value}"`}n+=">"}n+=t.insert;for(let t=e.length-1;t>=0;t--)n+=`</${e[t].nodeName}>`;return n})).join("")}toJSON(){return this.toString()}_write(t){t.writeTypeRef(Es)}}class is{constructor(t,e){this.id=t,this.length=e}get deleted(){throw tt()}mergeWith(t){return!1}write(t,e,n){throw tt()}integrate(t,e){throw tt()}}class os extends is{get deleted(){return!0}delete(){}mergeWith(t){return this.constructor===t.constructor&&(this.length+=t.length,!0)}integrate(t,e){e>0&&(this.id.clock+=e,this.length-=e),fn(t.doc.store,this)}write(t,e){t.writeInfo(0),t.writeLen(this.length-e)}getMissing(t,e){return null}}class cs{constructor(t){this.content=t}getLength(){return 1}getContent(){return[this.content]}isCountable(){return!0}copy(){return new cs(this.content)}splice(t){throw tt()}mergeWith(t){return!1}integrate(t,e){}delete(t){}gc(t){}write(t,e){t.writeBuf(this.content)}getRef(){return 3}}class ls{constructor(t){this.len=t}getLength(){return this.len}getContent(){return[]}isCountable(){return!1}copy(){return new ls(this.len)}splice(t){const e=new ls(this.len-t);return this.len=t,e}mergeWith(t){return this.len+=t.len,!0}integrate(t,e){Qt(t.deleteSet,e.id.client,e.id.clock,this.len),e.markDeleted()}delete(t){}gc(t){}write(t,e){t.writeLen(this.len-e)}getRef(){return 1}}const hs=(t,e)=>new ce({guid:t,...e,shouldLoad:e.shouldLoad||e.autoLoad||!1});class as{constructor(t){t._item&&console.error("This document was already integrated as a sub-document. You should create a second instance instead with the same guid."),this.doc=t;const e={};this.opts=e,t.gc||(e.gc=!1),t.autoLoad&&(e.autoLoad=!0),null!==t.meta&&(e.meta=t.meta)}getLength(){return 1}getContent(){return[this.doc]}isCountable(){return!0}copy(){return new as(hs(this.doc.guid,this.opts))}splice(t){throw tt()}mergeWith(t){return!1}integrate(t,e){this.doc._item=e,t.subdocsAdded.add(this.doc),this.doc.shouldLoad&&t.subdocsLoaded.add(this.doc)}delete(t){t.subdocsAdded.has(this.doc)?t.subdocsAdded.delete(this.doc):t.subdocsRemoved.add(this.doc)}gc(t){}write(t,e){t.writeString(this.doc.guid),t.writeAny(this.opts)}getRef(){return 9}}class ds{constructor(t){this.embed=t}getLength(){return 1}getContent(){return[this.embed]}isCountable(){return!0}copy(){return new ds(this.embed)}splice(t){throw tt()}mergeWith(t){return!1}integrate(t,e){}delete(t){}gc(t){}write(t,e){t.writeJSON(this.embed)}getRef(){return 5}}class us{constructor(t,e){this.key=t,this.value=e}getLength(){return 1}getContent(){return[]}isCountable(){return!1}copy(){return new us(this.key,this.value)}splice(t){throw tt()}mergeWith(t){return!1}integrate(t,e){const n=e.parent;n._searchMarker=null,n._hasFormatting=!0}delete(t){}gc(t){}write(t,e){t.writeKey(this.key),t.writeJSON(this.value)}getRef(){return 6}}class gs{constructor(t){this.arr=t}getLength(){return this.arr.length}getContent(){return this.arr}isCountable(){return!0}copy(){return new gs(this.arr)}splice(t){const e=new gs(this.arr.slice(t));return this.arr=this.arr.slice(0,t),e}mergeWith(t){return this.arr=this.arr.concat(t.arr),!0}integrate(t,e){}delete(t){}gc(t){}write(t,e){const n=this.arr.length;t.writeLen(n-e);for(let r=e;r<n;r++){const e=this.arr[r];t.writeString(void 0===e?"undefined":JSON.stringify(e))}}getRef(){return 2}}class fs{constructor(t){this.arr=t}getLength(){return this.arr.length}getContent(){return this.arr}isCountable(){return!0}copy(){return new fs(this.arr)}splice(t){const e=new fs(this.arr.slice(t));return this.arr=this.arr.slice(0,t),e}mergeWith(t){return this.arr=this.arr.concat(t.arr),!0}integrate(t,e){}delete(t){}gc(t){}write(t,e){const n=this.arr.length;t.writeLen(n-e);for(let r=e;r<n;r++){const e=this.arr[r];t.writeAny(e)}}getRef(){return 8}}class ps{constructor(t){this.str=t}getLength(){return this.str.length}getContent(){return this.str.split("")}isCountable(){return!0}copy(){return new ps(this.str)}splice(t){const e=new ps(this.str.slice(t));this.str=this.str.slice(0,t);const n=this.str.charCodeAt(t-1);return n>=55296&&n<=56319&&(this.str=this.str.slice(0,t-1)+"�",e.str="�"+e.str.slice(1)),e}mergeWith(t){return this.str+=t.str,!0}integrate(t,e){}delete(t){}gc(t){}write(t,e){t.writeString(0===e?this.str:this.str.slice(e))}getRef(){return 4}}const ws=[t=>new Nr,t=>new Rr,t=>new qr,t=>new es(t.readKey()),t=>new ts,t=>new rs(t.readKey()),t=>new ss],ms=0,ks=1,ys=2,bs=3,_s=4,Ss=5,Es=6;class Cs{constructor(t){this.type=t}getLength(){return 1}getContent(){return[this.type]}isCountable(){return!0}copy(){return new Cs(this.type._copy())}splice(t){throw tt()}mergeWith(t){return!1}integrate(t,e){this.type._integrate(t.doc,e)}delete(t){let e=this.type._start;for(;null!==e;)e.deleted?e.id.clock<(t.beforeState.get(e.id.client)||0)&&t._mergeStructs.push(e):e.delete(t),e=e.right;this.type._map.forEach((e=>{e.deleted?e.id.clock<(t.beforeState.get(e.id.client)||0)&&t._mergeStructs.push(e):e.delete(t)})),t.changed.delete(this.type)}gc(t){let e=this.type._start;for(;null!==e;)e.gc(t,!0),e=e.right;this.type._start=null,this.type._map.forEach((e=>{for(;null!==e;)e.gc(t,!0),e=e.left})),this.type._map=new Map}write(t,e){this.type._write(t)}getRef(){return 7}}const Ds=(t,e)=>{let n,r=e,s=0;do{s>0&&(r=Ne(r.client,r.clock+s)),n=wn(t,r),s=r.clock-n.id.clock,r=n.redone}while(null!==r&&n instanceof Ms);return{item:n,diff:s}},vs=(t,e)=>{for(;null!==t&&t.keep!==e;)t.keep=e,t=t.parent._item},As=(t,e,n)=>{const{client:r,clock:s}=e.id,i=new Ms(Ne(r,s+n),e,Ne(r,s+n-1),e.right,e.rightOrigin,e.parent,e.parentSub,e.content.splice(n));return e.deleted&&i.markDeleted(),e.keep&&(i.keep=!0),null!==e.redone&&(i.redone=Ne(e.redone.client,e.redone.clock+n)),e.right=i,null!==i.right&&(i.right.left=i),t._mergeStructs.push(i),null!==i.parentSub&&null===i.right&&i.parent._map.set(i.parentSub,i),e.length=n,i},Us=(t,e)=>s.G(t,(t=>Xt(t.deletions,e))),Ts=(t,e,n,r,s,i)=>{const o=t.doc,c=o.store,l=o.clientID,h=e.redone;if(null!==h)return kn(t,h);let a,d=e.parent._item,u=null;if(null!==d&&!0===d.deleted){if(null===d.redone&&(!n.has(d)||null===Ts(t,d,n,r,s,i)))return null;for(;null!==d.redone;)d=kn(t,d.redone)}const g=null===d?e.parent:d.content.type;if(null===e.parentSub){for(u=e.left,a=e;null!==u;){let e=u;for(;null!==e&&e.parent._item!==d;)e=null===e.redone?null:kn(t,e.redone);if(null!==e&&e.parent._item===d){u=e;break}u=u.left}for(;null!==a;){let e=a;for(;null!==e&&e.parent._item!==d;)e=null===e.redone?null:kn(t,e.redone);if(null!==e&&e.parent._item===d){a=e;break}a=a.right}}else if(a=null,e.right&&!s){for(u=e;null!==u&&null!==u.right&&(u.right.redone||Xt(r,u.right.id)||Us(i.undoStack,u.right.id)||Us(i.redoStack,u.right.id));)for(u=u.right;u.redone;)u=kn(t,u.redone);if(u&&null!==u.right)return null}else u=g._map.get(e.parentSub)||null;const f=gn(c,l),p=Ne(l,f),w=new Ms(p,u,u&&u.lastId,a,a&&a.id,g,e.parentSub,e.content.copy());return e.redone=p,vs(w,!0),w.integrate(t,0),w};class Ms extends is{constructor(t,e,n,r,s,i,o,c){super(t,c.getLength()),this.origin=n,this.left=e,this.right=r,this.rightOrigin=s,this.parent=i,this.parentSub=o,this.redone=null,this.content=c,this.info=this.content.isCountable()?2:0}set marker(t){(8&this.info)>0!==t&&(this.info^=8)}get marker(){return(8&this.info)>0}get keep(){return(1&this.info)>0}set keep(t){this.keep!==t&&(this.info^=1)}get countable(){return(2&this.info)>0}get deleted(){return(4&this.info)>0}set deleted(t){this.deleted!==t&&(this.info^=4)}markDeleted(){this.info|=4}getMissing(t,e){if(this.origin&&this.origin.client!==this.id.client&&this.origin.clock>=gn(e,this.origin.client))return this.origin.client;if(this.rightOrigin&&this.rightOrigin.client!==this.id.client&&this.rightOrigin.clock>=gn(e,this.rightOrigin.client))return this.rightOrigin.client;if(this.parent&&this.parent.constructor===Oe&&this.id.client!==this.parent.client&&this.parent.clock>=gn(e,this.parent.client))return this.parent.client;if(this.origin&&(this.left=yn(t,e,this.origin),this.origin=this.left.lastId),this.rightOrigin&&(this.right=kn(t,this.rightOrigin),this.rightOrigin=this.right.id),this.left&&this.left.constructor===os||this.right&&this.right.constructor===os)this.parent=null;else if(this.parent){if(this.parent.constructor===Oe){const t=wn(e,this.parent);t.constructor===os?this.parent=null:this.parent=t.content.type}}else this.left&&this.left.constructor===Ms&&(this.parent=this.left.parent,this.parentSub=this.left.parentSub),this.right&&this.right.constructor===Ms&&(this.parent=this.right.parent,this.parentSub=this.right.parentSub);return null}integrate(t,e){if(e>0&&(this.id.clock+=e,this.left=yn(t,t.doc.store,Ne(this.id.client,this.id.clock-1)),this.origin=this.left.lastId,this.content=this.content.splice(e),this.length-=e),this.parent){if(!this.left&&(!this.right||null!==this.right.left)||this.left&&this.left.right!==this.right){let e,n=this.left;if(null!==n)e=n.right;else if(null!==this.parentSub)for(e=this.parent._map.get(this.parentSub)||null;null!==e&&null!==e.left;)e=e.left;else e=this.parent._start;const r=new Set,s=new Set;for(;null!==e&&e!==this.right;){if(s.add(e),r.add(e),Ie(this.origin,e.origin)){if(e.id.client<this.id.client)n=e,r.clear();else if(Ie(this.rightOrigin,e.rightOrigin))break}else{if(null===e.origin||!s.has(wn(t.doc.store,e.origin)))break;r.has(wn(t.doc.store,e.origin))||(n=e,r.clear())}e=e.right}this.left=n}if(null!==this.left){const t=this.left.right;this.right=t,this.left.right=this}else{let t;if(null!==this.parentSub)for(t=this.parent._map.get(this.parentSub)||null;null!==t&&null!==t.left;)t=t.left;else t=this.parent._start,this.parent._start=this;this.right=t}null!==this.right?this.right.left=this:null!==this.parentSub&&(this.parent._map.set(this.parentSub,this),null!==this.left&&this.left.delete(t)),null===this.parentSub&&this.countable&&!this.deleted&&(this.parent._length+=this.length),fn(t.doc.store,this),this.content.integrate(t,this),En(t,this.parent,this.parentSub),(null!==this.parent._item&&this.parent._item.deleted||null!==this.parentSub&&null!==this.right)&&this.delete(t)}else new os(this.id,this.length).integrate(t,0)}get next(){let t=this.right;for(;null!==t&&t.deleted;)t=t.right;return t}get prev(){let t=this.left;for(;null!==t&&t.deleted;)t=t.left;return t}get lastId(){return 1===this.length?this.id:Ne(this.id.client,this.id.clock+this.length-1)}mergeWith(t){if(this.constructor===t.constructor&&Ie(t.origin,this.lastId)&&this.right===t&&Ie(this.rightOrigin,t.rightOrigin)&&this.id.client===t.id.client&&this.id.clock+this.length===t.id.clock&&this.deleted===t.deleted&&null===this.redone&&null===t.redone&&this.content.constructor===t.content.constructor&&this.content.mergeWith(t.content)){const e=this.parent._searchMarker;return e&&e.forEach((e=>{e.p===t&&(e.p=this,!this.deleted&&this.countable&&(e.index-=this.length))})),t.keep&&(this.keep=!0),this.right=t.right,null!==this.right&&(this.right.left=this),this.length+=t.length,!0}return!1}delete(t){if(!this.deleted){const e=this.parent;this.countable&&null===this.parentSub&&(e._length-=this.length),this.markDeleted(),Qt(t.deleteSet,this.id.client,this.id.clock,this.length),En(t,e,this.parentSub),this.content.delete(t)}}gc(t,e){if(!this.deleted)throw et();this.content.gc(t),e?((t,e,n)=>{const r=t.clients.get(e.id.client);r[pn(r,e.id.clock)]=n})(t,this,new os(this.id,this.length)):this.content=new ls(this.length)}write(t,e){const n=e>0?Ne(this.id.client,this.id.clock+e-1):this.origin,r=this.rightOrigin,s=this.parentSub,i=31&this.content.getRef()|(null===n?0:U)|(null===r?0:A)|(null===s?0:32);if(t.writeInfo(i),null!==n&&t.writeLeftID(n),null!==r&&t.writeRightID(r),null===n&&null===r){const e=this.parent;if(void 0!==e._item){const n=e._item;if(null===n){const n=Pe(e);t.writeParentInfo(!0),t.writeString(n)}else t.writeParentInfo(!1),t.writeLeftID(n.id)}else e.constructor===String?(t.writeParentInfo(!0),t.writeString(e)):e.constructor===Oe?(t.writeParentInfo(!1),t.writeLeftID(e)):et();null!==s&&t.writeString(s)}this.content.write(t,e)}}const xs=(t,e)=>Os[31&e](t),Os=[()=>{et()},t=>new ls(t.readLen()),t=>{const e=t.readLen(),n=[];for(let r=0;r<e;r++){const e=t.readString();"undefined"===e?n.push(void 0):n.push(JSON.parse(e))}return new gs(n)},t=>new cs(t.readBuf()),t=>new ps(t.readString()),t=>new ds(t.readJSON()),t=>new us(t.readKey(),t.readJSON()),t=>new Cs(ws[t.readTypeRef()](t)),t=>{const e=t.readLen(),n=[];for(let r=0;r<e;r++)n.push(t.readAny());return new fs(n)},t=>new as(hs(t.readString(),t.readAny())),()=>{et()}];class Is extends is{get deleted(){return!0}delete(){}mergeWith(t){return this.constructor===t.constructor&&(this.length+=t.length,!0)}integrate(t,e){et()}write(t,e){t.writeInfo(10),P(t.restEncoder,this.length-e)}getMissing(t,e){return null}}const Ns="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0!==n.g?n.g:{},Ls="__ $YJS$ __";!0===Ns[Ls]&&console.error("Yjs was already imported. This breaks constructor checks and will lead to issues! - https://github.com/yjs/yjs/issues/438"),Ns[Ls]=!0}}]);
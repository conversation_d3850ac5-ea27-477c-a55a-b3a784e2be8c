"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[5313,8060],{65313:(e,t,i)=>{i.r(t),i.d(t,{CSVDelimiter:()=>n.p,CSVDocumentWidget:()=>o.kw,CSVViewer:()=>o.A9,CSVViewerFactory:()=>o.LT,DSVModel:()=>r.DSVModel,GridSearchService:()=>o.JZ,TSVViewerFactory:()=>o._d,TextRenderConfig:()=>o.B0,parseDSV:()=>s.G,parseDSVNoQuotes:()=>s.z});var r=i(18032),s=i(48234),n=i(40930),o=i(21058)},40930:(e,t,i)=>{i.d(t,{p:()=>a});var r,s=i(71677),n=i(68239),o=i(31516);class a extends o.Widget{constructor(e){super({node:r.createNode(e.widget.delimiter,e.translator)}),this._widget=e.widget,this.addClass("jp-CSVDelimiter")}get selectNode(){return this.node.getElementsByTagName("select")[0]}handleEvent(e){"change"===e.type&&(this._widget.delimiter=this.selectNode.value)}onAfterAttach(e){this.selectNode.addEventListener("change",this)}onBeforeDetach(e){this.selectNode.removeEventListener("change",this)}}!function(e){e.createNode=function(e,t){const i=null==(t=t||s.nullTranslator)?void 0:t.load("jupyterlab"),r=[[",",","],[";",";"],["\t",i.__("tab")],["|",i.__("pipe")],["#",i.__("hash")]],o=document.createElement("div"),a=document.createElement("span"),d=document.createElement("select");a.textContent=i.__("Delimiter: "),a.className="jp-CSVDelimiter-label";for(const[t,i]of r){const r=document.createElement("option");r.value=t,r.textContent=i,t===e&&(r.selected=!0),d.appendChild(r)}o.appendChild(a);const l=n.Styling.wrapSelect(d);return l.classList.add("jp-CSVDelimiter-dropdown"),o.appendChild(l),o}}(r||(r={}))},21058:(e,t,i)=>{i.d(t,{A9:()=>u,B0:()=>h,JZ:()=>c,LT:()=>g,_d:()=>m,kw:()=>_});var r,s=i(38639),n=i(35312),o=i(20998),a=i(81997),d=i(31516),l=i(40930);class h{}class c{constructor(e){this._looping=!0,this._changed=new a.Signal(this),this._grid=e,this._query=null,this._row=0,this._column=-1}get changed(){return this._changed}cellBackgroundColorRendererFunc(e){return({value:t,row:i,column:r})=>this._query&&t.match(this._query)?this._row===i&&this._column===r?e.currentMatchBackgroundColor:e.matchBackgroundColor:""}clear(){this._query=null,this._row=0,this._column=-1,this._changed.emit(void 0)}find(e,t=!1){const i=this._grid.dataModel,r=i.rowCount("body"),s=i.columnCount("body");this._query!==e&&(this._row=0,this._column=-1),this._query=e;const n=this._grid.scrollY/this._grid.defaultSizes.rowHeight,o=(this._grid.scrollY+this._grid.pageHeight)/this._grid.defaultSizes.rowHeight,a=this._grid.scrollX/this._grid.defaultSizes.columnHeaderHeight,d=(this._grid.scrollX+this._grid.pageWidth)/this._grid.defaultSizes.columnHeaderHeight,l=(e,t)=>e>=n&&e<=o&&t>=a&&t<=d,h=t?-1:1;this._column+=h;for(let n=this._row;t?n>=0:n<r;n+=h){for(let r=this._column;t?r>=0:r<s;r+=h)if(i.data("body",n,r).match(e))return this._changed.emit(void 0),l(n,r)||this._grid.scrollToRow(n),this._row=n,this._column=r,!0;this._column=t?s-1:0}if(this._looping){this._looping=!1,this._row=t?0:r-1,this._wrapRows(t);try{return this.find(e,t)}finally{this._looping=!0}}return!1}_wrapRows(e=!1){const t=this._grid.dataModel,i=t.rowCount("body"),r=t.columnCount("body");e&&this._row<=0?(this._row=i-1,this._column=r):!e&&this._row>=i-1&&(this._row=0,this._column=-1)}get query(){return this._query}}class u extends d.Widget{constructor(e){super(),this._monitor=null,this._delimiter=",",this._revealed=new o.PromiseDelegate,this._baseRenderer=null,this._context=e.context,this.layout=new d.PanelLayout,this.addClass("jp-CSVViewer"),this._ready=this.initialize()}get ready(){return this._ready}async initialize(){const e=this.layout;if(this.isDisposed||!e)return;const{BasicKeyHandler:t,BasicMouseHandler:i,DataGrid:n}=await r.ensureDataGrid();this._defaultStyle=n.defaultStyle,this._grid=new n({defaultSizes:{rowHeight:24,columnWidth:144,rowHeaderWidth:64,columnHeaderHeight:36}}),this._grid.addClass("jp-CSVViewer-grid"),this._grid.headerVisibility="all",this._grid.keyHandler=new t,this._grid.mouseHandler=new i,this._grid.copyConfig={separator:"\t",format:n.copyFormatGeneric,headers:"all",warningThreshold:1e6},e.addWidget(this._grid),this._searchService=new c(this._grid),this._searchService.changed.connect(this._updateRenderer,this),await this._context.ready,await this._updateGrid(),this._revealed.resolve(void 0),this._monitor=new s.ActivityMonitor({signal:this._context.model.contentChanged,timeout:1e3}),this._monitor.activityStopped.connect(this._updateGrid,this)}get context(){return this._context}get revealed(){return this._revealed.promise}get delimiter(){return this._delimiter}set delimiter(e){e!==this._delimiter&&(this._delimiter=e,this._updateGrid())}get style(){return this._grid.style}set style(e){this._grid.style={...this._defaultStyle,...e}}set rendererConfig(e){this._baseRenderer=e,this._updateRenderer()}get searchService(){return this._searchService}dispose(){this._monitor&&this._monitor.dispose(),super.dispose()}goToLine(e){this._grid.scrollToRow(e)}onActivateRequest(e){this.node.tabIndex=-1,this.node.focus()}async _updateGrid(){const{BasicSelectionModel:e}=await r.ensureDataGrid(),{DSVModel:t}=await r.ensureDSVModel(),i=this._context.model.toString(),s=this._delimiter,n=this._grid.dataModel,o=this._grid.dataModel=new t({data:i,delimiter:s});this._grid.selectionModel=new e({dataModel:o}),n&&n.dispose()}async _updateRenderer(){if(null===this._baseRenderer)return;const{TextRenderer:e}=await r.ensureDataGrid(),t=this._baseRenderer,i=new e({textColor:t.textColor,horizontalAlignment:t.horizontalAlignment,backgroundColor:this._searchService.cellBackgroundColorRendererFunc(t)});this._grid.cellRenderers.update({body:i,"column-header":i,"corner-header":i,"row-header":i})}}class _ extends n.DocumentWidget{constructor(e){let{content:t,context:i,delimiter:s,reveal:n,...o}=e;t=t||r.createContent(i),n=Promise.all([n,t.revealed]),super({content:t,context:i,reveal:n,...o}),s&&(t.delimiter=s)}setFragment(e){const t=e.split("=");if("#row"!==t[0])return;let i=t[1].split(";")[0];i=i.split("-")[0],this.context.ready.then((()=>{this.content.goToLine(Number(i))}))}}class g extends n.ABCWidgetFactory{createNewWidget(e){const t=this.translator;return new _({context:e,translator:t})}defaultToolbarFactory(e){return[{name:"delimiter",widget:new l.p({widget:e.content,translator:this.translator})}]}}class m extends g{createNewWidget(e){return new _({context:e,delimiter:"\t",translator:this.translator})}}!function(e){let t=null,r=null;e.ensureDataGrid=async function(){return null==t&&(t=new o.PromiseDelegate,t.resolve(await i.e(5585).then(i.t.bind(i,95585,23)))),t.promise},e.ensureDSVModel=async function(){return null==r&&(r=new o.PromiseDelegate,r.resolve(await Promise.all([i.e(8032),i.e(5585)]).then(i.bind(i,18032)))),r.promise},e.createContent=function(e){return new u({context:e})}}(r||(r={}))}}]);
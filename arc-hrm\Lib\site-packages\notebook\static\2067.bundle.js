"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[2067],{32067:(e,t,s)=>{s.r(t),s.d(t,{CodeConsole:()=>x,ConsoleHistory:()=>l,ConsolePanel:()=>k,ForeignHandler:()=>i,IConsoleTracker:()=>S});var o,n=s(81997);class i{constructor(e){this._enabled=!1,this._isDisposed=!1,this.sessionContext=e.sessionContext,this.sessionContext.iopubMessage.connect(this.onIOPubMessage,this),this._parent=e.parent}get enabled(){return this._enabled}set enabled(e){this._enabled=e}get parent(){return this._parent}get isDisposed(){return this._isDisposed}dispose(){this.isDisposed||(this._isDisposed=!0,n.Signal.clearData(this))}onIOPubMessage(e,t){var s;if(!this._enabled)return!1;const o=null===(s=this.sessionContext.session)||void 0===s?void 0:s.kernel;if(!o)return!1;const n=this._parent;if(t.parent_header.session===o.clientId)return!1;const i=t.header.msg_type,l=t.parent_header.msg_id;let r;switch(i){case"execute_input":{const e=t;r=this._newCell(l);const s=r.model;return s.executionCount=e.content.execution_count,s.sharedModel.setSource(e.content.code),s.trusted=!0,n.update(),!0}case"execute_result":case"display_data":case"stream":case"error":{if(r=this._parent.getCell(l),!r)return!1;const e={...t.content,output_type:i};return r.model.outputs.add(e),n.update(),!0}case"clear_output":{const e=t.content.wait;return r=this._parent.getCell(l),r&&r.model.outputs.clear(e),!0}default:return!1}}_newCell(e){const t=this.parent.createCodeCell();return t.addClass("jp-CodeConsole-foreignCell"),this._parent.addCell(t,e),t}}class l{constructor(e){this._cursor=0,this._hasSession=!1,this._history=[],this._placeholder="",this._setByHistory=!1,this._isDisposed=!1,this._editor=null,this._filtered=[];const{sessionContext:t}=e;t&&(this.sessionContext=t,this._handleKernel(),this.sessionContext.kernelChanged.connect(this._handleKernel,this))}get editor(){return this._editor}set editor(e){if(this._editor===e)return;const t=this._editor;t&&(t.edgeRequested.disconnect(this.onEdgeRequest,this),t.model.sharedModel.changed.disconnect(this.onTextChange,this)),this._editor=e,e&&(e.edgeRequested.connect(this.onEdgeRequest,this),e.model.sharedModel.changed.connect(this.onTextChange,this))}get placeholder(){return this._placeholder}get isDisposed(){return this._isDisposed}dispose(){this._isDisposed=!0,this._history.length=0,n.Signal.clearData(this)}back(e){this._hasSession||(this._hasSession=!0,this._placeholder=e,this.setFilter(e),this._cursor=this._filtered.length-1),--this._cursor,this._cursor=Math.max(0,this._cursor);const t=this._filtered[this._cursor];return Promise.resolve(t)}forward(e){this._hasSession||(this._hasSession=!0,this._placeholder=e,this.setFilter(e),this._cursor=this._filtered.length),++this._cursor,this._cursor=Math.min(this._filtered.length-1,this._cursor);const t=this._filtered[this._cursor];return Promise.resolve(t)}push(e){e&&e!==this._history[this._history.length-1]&&this._history.push(e),this.reset()}reset(){this._cursor=this._history.length,this._hasSession=!1,this._placeholder=""}onHistory(e){this._history.length=0;let t="",s="";if("ok"===e.content.status)for(let o=0;o<e.content.history.length;o++)s=e.content.history[o][2],s!==t&&this._history.push(t=s);this._cursor=this._history.length}onTextChange(){this._setByHistory?this._setByHistory=!1:this.reset()}onEdgeRequest(e,t){const s=e.model.sharedModel,o=s.getSource();"top"===t||"topLine"===t?this.back(o).then((t=>{if(this.isDisposed||!t)return;if(s.getSource()===t)return;this._setByHistory=!0,s.setSource(t);let o=0;o=t.indexOf("\n"),o<0&&(o=t.length),e.setCursorPosition({line:0,column:o})})):this.forward(o).then((t=>{if(this.isDisposed)return;const o=t||this.placeholder;if(s.getSource()===o)return;this._setByHistory=!0,s.setSource(o);const n=e.getPositionAt(o.length);n&&e.setCursorPosition(n)}))}async _handleKernel(){var e,t;const s=null===(t=null===(e=this.sessionContext)||void 0===e?void 0:e.session)||void 0===t?void 0:t.kernel;if(s)return s.requestHistory(o.initialRequest).then((e=>{this.onHistory(e)}));this._history.length=0}setFilter(e=""){this._filtered.length=0;let t="",s="";for(let o=0;o<this._history.length;o++)s=this._history[o],s!==t&&e===s.slice(0,e.length)&&this._filtered.push(t=s);this._filtered.push(e)}}!function(e){e.initialRequest={output:!1,raw:!0,hist_access_type:"tail",n:500}}(o||(o={}));var r=s(12982),a=s(38639),h=s(70856),d=s(71677),c=s(68239),u=s(20998),p=s(31516),_=s(17811),C=s(30890),m=s(23781),g=s(24912),y=s(11351),f=s(99615);const v="jp-Console-cell",w="jp-CodeConsole-promptCell";class x extends p.Widget{constructor(e){var t,s;super(),this.editorConfig=x.defaultEditorConfig,this._banner=null,this._executed=new n.Signal(this),this._mimetype="text/x-ipython",this._msgIds=new Map,this._msgIdCells=new Map,this._promptCellCreated=new n.Signal(this),this._dragData=null,this._drag=null,this._focusedCell=null,this._translator=null!==(t=e.translator)&&void 0!==t?t:d.nullTranslator,this.addClass("jp-CodeConsole"),this.node.dataset.jpKernelUser="true",this.node.dataset.jpCodeRunner="true",this.node.dataset.jpUndoer="true",this.node.tabIndex=-1;const o=this.layout=new p.PanelLayout;this._cells=new y.ObservableList,this._content=new p.Panel,this._input=new p.Panel,this.contentFactory=e.contentFactory,this.modelFactory=null!==(s=e.modelFactory)&&void 0!==s?s:x.defaultModelFactory,this.rendermime=e.rendermime,this.sessionContext=e.sessionContext,this._mimeTypeService=e.mimeTypeService,this._content.addClass("jp-CodeConsole-content"),this._input.addClass("jp-CodeConsole-input"),o.addWidget(this._content),o.addWidget(this._input),this._history=new l({sessionContext:this.sessionContext}),this._onKernelChanged(),this.sessionContext.kernelChanged.connect(this._onKernelChanged,this),this.sessionContext.statusChanged.connect(this._onKernelStatusChanged,this)}get executed(){return this._executed}get promptCellCreated(){return this._promptCellCreated}get cells(){return this._cells}get promptCell(){return this._input.layout.widgets[0]||null}addCell(e,t){e.addClass(v),this._content.addWidget(e),this._cells.push(e),t&&(this._msgIds.set(t,e),this._msgIdCells.set(e,t)),e.disposed.connect(this._onCellDisposed,this),this.update()}addBanner(){if(this._banner){const e=this._banner;this._cells.push(this._banner),e.disposed.connect(this._onCellDisposed,this)}const e=this.modelFactory.createRawCell({sharedModel:(0,m.createStandaloneCell)({cell_type:"raw",source:"..."})}),t=(this._banner=new g.RawCell({model:e,contentFactory:this.contentFactory,placeholder:!1,editorConfig:{autoClosingBrackets:!1,codeFolding:!1,highlightActiveLine:!1,highlightTrailingWhitespace:!1,highlightWhitespace:!1,indentUnit:"4",lineNumbers:!1,lineWrap:!0,matchBrackets:!1,readOnly:!0,rulers:[],scrollPastEnd:!1,smartIndent:!1,tabSize:4,theme:"jupyter"}})).initializeState();t.addClass("jp-CodeConsole-banner"),t.readOnly=!0,this._content.addWidget(t)}clear(){const e=this._cells;for(;e.length>0;)e.get(0).dispose()}createCodeCell(){const e=this.contentFactory,t=this._createCodeCellOptions(),s=e.createCodeCell(t);return s.readOnly=!0,s.model.mimeType=this._mimetype,s}dispose(){this.isDisposed||(this._msgIdCells=null,this._msgIds=null,this._history.dispose(),super.dispose())}async execute(e=!1,t=250){var s,o;if("dead"===(null===(o=null===(s=this.sessionContext.session)||void 0===s?void 0:s.kernel)||void 0===o?void 0:o.status))return;const n=this.promptCell;if(!n)throw new Error("Cannot execute without a prompt cell");if(n.model.trusted=!0,e)return this.newPromptCell(),void await this._execute(n);const i=await this._shouldExecute(t);this.isDisposed||(i?(this.newPromptCell(),this.promptCell.editor.focus(),await this._execute(n)):n.editor.newIndentedLine())}getCell(e){return this._msgIds.get(e)}inject(e,t={}){const s=this.createCodeCell();s.model.sharedModel.setSource(e);for(const e of Object.keys(t))s.model.setMetadata(e,t[e]);return this.addCell(s),this._execute(s)}insertLinebreak(){const e=this.promptCell;e&&e.editor.newIndentedLine()}replaceSelection(e){var t,s;const o=this.promptCell;o&&(null===(s=(t=o.editor).replaceSelection)||void 0===s||s.call(t,e))}serialize(){const e=[];for(const t of this._cells){const s=t.model;(0,g.isCodeCellModel)(s)&&e.push(s.toJSON())}return this.promptCell&&e.push(this.promptCell.model.toJSON()),e}_evtMouseDown(e){const{button:t,shiftKey:s}=e;if(0!==t&&2!==t||s&&2===t)return;let o=e.target;const n=e=>e.classList.contains(v);let i=g.CellDragUtils.findCell(o,this._cells,n);if(-1===i&&(o=document.elementFromPoint(e.clientX,e.clientY),i=g.CellDragUtils.findCell(o,this._cells,n)),-1===i)return;const l=this._cells.get(i);"prompt"===g.CellDragUtils.detectTargetArea(l,e.target)&&(this._dragData={pressX:e.clientX,pressY:e.clientY,index:i},this._focusedCell=l,document.addEventListener("mouseup",this,!0),document.addEventListener("mousemove",this,!0),e.preventDefault())}_evtMouseMove(e){const t=this._dragData;t&&g.CellDragUtils.shouldStartDrag(t.pressX,t.pressY,e.clientX,e.clientY)&&this._startDrag(t.index,e.clientX,e.clientY)}_startDrag(e,t,s){const o=this._focusedCell.model,n=[o.toJSON()],i=g.CellDragUtils.createCellDragImage(this._focusedCell,n);this._drag=new f.Drag({mimeData:new u.MimeData,dragImage:i,proposedAction:"copy",supportedActions:"copy",source:this}),this._drag.mimeData.setData("application/vnd.jupyter.cells",n);const l=o.sharedModel.getSource();return this._drag.mimeData.setData("text/plain",l),this._focusedCell=null,document.removeEventListener("mousemove",this,!0),document.removeEventListener("mouseup",this,!0),this._drag.start(t,s).then((()=>{this.isDisposed||(this._drag=null,this._dragData=null)}))}handleEvent(e){switch(e.type){case"keydown":this._evtKeyDown(e);break;case"mousedown":this._evtMouseDown(e);break;case"mousemove":this._evtMouseMove(e);break;case"mouseup":this._evtMouseUp(e);break;case"focusin":this._evtFocusIn(e);break;case"focusout":this._evtFocusOut(e)}}onAfterAttach(e){const t=this.node;t.addEventListener("keydown",this,!0),t.addEventListener("click",this),t.addEventListener("mousedown",this),t.addEventListener("focusin",this),t.addEventListener("focusout",this),this.promptCell?(this.promptCell.editor.focus(),this.update()):this.newPromptCell()}onBeforeDetach(e){const t=this.node;t.removeEventListener("keydown",this,!0),t.removeEventListener("click",this),t.removeEventListener("focusin",this),t.removeEventListener("focusout",this)}onActivateRequest(e){const t=this.promptCell&&this.promptCell.editor;t&&t.focus(),this.update()}newPromptCell(){var e;let t=this.promptCell;const s=this._input;if(t){t.readOnly=!0,t.removeClass(w);const o=t;requestIdleCallback((()=>{n.Signal.clearData(o.editor)})),null===(e=t.editor)||void 0===e||e.blur(),s.widgets[0].parent=null,this.addCell(t)}const o=this.contentFactory,i=this._createCodeCellOptions();t=o.createCodeCell(i),t.model.mimeType=this._mimetype,t.addClass(w),this._input.addWidget(t),this._history.editor=t.editor,this._promptCellCreated.emit(t)}onUpdateRequest(e){D.scrollToBottom(this._content.node)}_evtKeyDown(e){const t=this.promptCell&&this.promptCell.editor;t&&(13!==e.keyCode||t.hasFocus()?27===e.keyCode&&t.hasFocus()&&(e.preventDefault(),e.stopPropagation(),this.node.focus()):(e.preventDefault(),t.focus()))}_evtMouseUp(e){this.promptCell&&this.promptCell.node.contains(e.target)&&this.promptCell.editor.focus()}_evtFocusIn(e){this._updateReadWrite()}_evtFocusOut(e){this._updateReadWrite()}_execute(e){const t=e.model.sharedModel.getSource();return this._history.push(t),"clear"===t||"%clear"===t?(this.clear(),Promise.resolve(void 0)):(e.model.contentChanged.connect(this.update,this),g.CodeCell.execute(e,this.sessionContext).then((t=>{if(!this.isDisposed){if(t&&"ok"===t.content.status){const s=t.content;if(s.payload&&s.payload.length){const t=s.payload.filter((e=>"set_next_input"===e.source))[0];if(t){const s=t.text;e.model.sharedModel.setSource(s)}}}else if(t&&"error"===t.content.status)for(const e of this._cells)null===e.model.executionCount&&e.setPrompt("");e.model.contentChanged.disconnect(this.update,this),this.update(),this._executed.emit(new Date)}}),(()=>{this.isDisposed||(e.model.contentChanged.disconnect(this.update,this),this.update())})))}_handleInfo(e){if("ok"!==e.status)return void this._banner.model.sharedModel.setSource("Error in getting kernel banner");this._banner.model.sharedModel.setSource(e.banner);const t=e.language_info;this._mimetype=this._mimeTypeService.getMimeTypeByLanguage(t),this.promptCell&&(this.promptCell.model.mimeType=this._mimetype)}_createCodeCellOptions(){const{node:e}=this,t=this.contentFactory,s=this.modelFactory.createCodeCell({}),o=this.rendermime,n=this.editorConfig,i=C.EditorView.domEventHandlers({keydown:(t,s)=>13===t.keyCode&&"terminal"===e.dataset.jpInteractionMode&&(t.preventDefault(),!0)});return{model:s,rendermime:o,contentFactory:t,editorConfig:n,editorExtensions:[_.Prec.high(i)],placeholder:!1,translator:this._translator}}_onCellDisposed(e,t){if(!this.isDisposed){this._cells.removeValue(e);const t=this._msgIdCells.get(e);t&&(this._msgIdCells.delete(e),this._msgIds.delete(t))}}_shouldExecute(e){const t=this.promptCell;if(!t)return Promise.resolve(!1);const s=t.model.sharedModel.getSource();return new Promise(((t,o)=>{var n;const i=setTimeout((()=>{t(!0)}),e),l=null===(n=this.sessionContext.session)||void 0===n?void 0:n.kernel;l?l.requestIsComplete({code:s}).then((e=>{clearTimeout(i),this.isDisposed&&t(!1),"incomplete"===e.content.status?t(!1):t(!0)})).catch((()=>{t(!0)})):t(!1)}))}async _onKernelChanged(){var e;this.clear(),this._banner&&(this._banner.dispose(),this._banner=null),this.addBanner(),(null===(e=this.sessionContext.session)||void 0===e?void 0:e.kernel)&&this._handleInfo(await this.sessionContext.session.kernel.info)}async _onKernelStatusChanged(){var e;const t=null===(e=this.sessionContext.session)||void 0===e?void 0:e.kernel;"restarting"===(null==t?void 0:t.status)&&(this.addBanner(),this._handleInfo(await(null==t?void 0:t.info)))}_updateReadWrite(){const e=r.DOMUtils.hasActiveEditableElement(this.node);this.node.classList.toggle("jp-mod-readWrite",e)}}var D,b;!function(e){e.defaultEditorConfig={codeFolding:!1,lineNumbers:!1};class t extends g.Cell.ContentFactory{createCodeCell(e){return new g.CodeCell(e).initializeState()}createRawCell(e){return new g.RawCell(e).initializeState()}}e.ContentFactory=t;class s{constructor(e={}){this.codeCellContentFactory=e.codeCellContentFactory||g.CodeCellModel.defaultContentFactory}createCodeCell(e={}){return e.contentFactory||(e.contentFactory=this.codeCellContentFactory),new g.CodeCellModel(e)}createRawCell(e){return new g.RawCellModel(e)}}e.ModelFactory=s,e.defaultModelFactory=new s({})}(x||(x={})),function(e){e.scrollToBottom=function(e){e.scrollTop=e.scrollHeight-e.clientHeight}}(D||(D={}));class k extends r.MainAreaWidget{constructor(e){super({content:new p.Panel}),this._executed=null,this._connected=null,this.addClass("jp-ConsolePanel");let{rendermime:t,mimeTypeService:s,path:o,basePath:n,name:i,manager:l,modelFactory:_,sessionContext:C,translator:m}=e;this.translator=null!=m?m:d.nullTranslator;const g=this.translator.load("jupyterlab"),y=this.contentFactory=e.contentFactory,f=b.count++;o||(o=a.PathExt.join(n||"",`console-${f}-${u.UUID.uuid4()}`)),C=this._sessionContext=null!=C?C:new r.SessionContext({sessionManager:l.sessions,specsManager:l.kernelspecs,path:l.contents.localPath(o),name:i||g.__("Console %1",f),type:"console",kernelPreference:e.kernelPreference,setBusy:e.setBusy});const v=new h.RenderMimeRegistry.UrlResolver({path:o,contents:l.contents});t=t.clone({resolver:v}),this.console=y.createConsole({rendermime:t,sessionContext:C,mimeTypeService:s,contentFactory:y,modelFactory:_,translator:m}),this.content.addWidget(this.console),C.initialize().then((async t=>{var s;t&&await(null!==(s=e.sessionDialogs)&&void 0!==s?s:new r.SessionContextDialogs({translator:m})).selectKernel(C),this._connected=new Date,this._updateTitlePanel()})),this.console.executed.connect(this._onExecuted,this),this._updateTitlePanel(),C.kernelChanged.connect(this._updateTitlePanel,this),C.propertyChanged.connect(this._updateTitlePanel,this),this.title.icon=c.consoleIcon,this.title.closable=!0,this.id=`console-${f}`}get sessionContext(){return this._sessionContext}dispose(){this.sessionContext.dispose(),this.console.dispose(),super.dispose()}onActivateRequest(e){const t=this.console.promptCell;t&&t.editor.focus()}onCloseRequest(e){super.onCloseRequest(e),this.dispose()}_onExecuted(e,t){this._executed=t,this._updateTitlePanel()}_updateTitlePanel(){b.updateTitle(this,this._connected,this._executed,this.translator)}}!function(e){class t extends x.ContentFactory{createConsole(e){return new x(e)}}e.ContentFactory=t,e.IContentFactory=new u.Token("@jupyterlab/console:IContentFactory","A factory object that creates new code consoles. Use this if you want to create and host code consoles in your own UI elements.")}(k||(k={})),function(e){e.count=1,e.updateTitle=function(e,t,s,o){const n=(o=o||d.nullTranslator).load("jupyterlab"),i=e.console.sessionContext.session;if(i){let o=n.__("Name: %1\n",i.name)+n.__("Directory: %1\n",a.PathExt.dirname(i.path))+n.__("Kernel: %1",e.console.sessionContext.kernelDisplayName);t&&(o+=n.__("\nConnected: %1",a.Time.format(t.toISOString()))),s&&(o+=n.__("\nLast Execution: %1")),e.title.label=i.name,e.title.caption=o}else e.title.label=n.__("Console"),e.title.caption=""}}(b||(b={}));const S=new u.Token("@jupyterlab/console:IConsoleTracker","A widget tracker for code consoles.\n  Use this if you want to be able to iterate over and interact with code consoles\n  created by the application.")}}]);
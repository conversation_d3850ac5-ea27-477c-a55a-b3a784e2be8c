"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[8771],{28771:(e,t,a)=>{a.r(t);a.d(t,{Cassandra:()=>qe,MSSQL:()=>Se,MariaSQL:()=>Ce,MySQL:()=>Qe,PLSQL:()=>Te,PostgreSQL:()=>ke,SQLDialect:()=>fe,SQLite:()=>Pe,StandardSQL:()=>ye,keywordCompletion:()=>he,keywordCompletionSource:()=>ge,schemaCompletion:()=>be,schemaCompletionSource:()=>_e,sql:()=>ve});var n=a(24104);var r=a.n(n);var i=a(6016);var s=a.n(i);var o=a(11705);var l=a(1065);const c=36,d=1,u=2,m=3,p=4,f=5,g=6,h=7,_=8,b=9,v=10,y=11,k=12,O=13,x=14,w=15,Q=16,C=17,S=18,P=19,q=20,T=21,U=22,z=23,X=24;function j(e){return e>=65&&e<=90||e>=97&&e<=122||e>=48&&e<=57}function B(e){return e>=48&&e<=57||e>=97&&e<=102||e>=65&&e<=70}function R(e,t,a){for(let n=false;;){if(e.next<0)return;if(e.next==t&&!n){e.advance();return}n=a&&!n&&e.next==92;e.advance()}}function L(e){for(;;){if(e.next<0||e.peek(1)<0)return;if(e.next==36&&e.peek(1)==36){e.advance(2);return}e.advance()}}function Z(e,t){for(;;){if(e.next!=95&&!j(e.next))break;if(t!=null)t+=String.fromCharCode(e.next);e.advance()}return t}function V(e){if(e.next==39||e.next==34||e.next==96){let t=e.next;e.advance();R(e,t,false)}else{Z(e)}}function D(e,t){while(e.next==48||e.next==49)e.advance();if(t&&e.next==t)e.advance()}function I(e,t){for(;;){if(e.next==46){if(t)break;t=true}else if(e.next<48||e.next>57){break}e.advance()}if(e.next==69||e.next==101){e.advance();if(e.next==43||e.next==45)e.advance();while(e.next>=48&&e.next<=57)e.advance()}}function $(e){while(!(e.next<0||e.next==10))e.advance()}function N(e,t){for(let a=0;a<t.length;a++)if(t.charCodeAt(a)==e)return true;return false}const E=" \t\r\n";function W(e,t,a){let n=Object.create(null);n["true"]=n["false"]=f;n["null"]=n["unknown"]=g;for(let r of e.split(" "))if(r)n[r]=q;for(let r of t.split(" "))if(r)n[r]=T;for(let r of(a||"").split(" "))if(r)n[r]=X;return n}const G="array binary bit boolean char character clob date decimal double float int integer interval large national nchar nclob numeric object precision real smallint time timestamp varchar varying ";const A="absolute action add after all allocate alter and any are as asc assertion at authorization before begin between both breadth by call cascade cascaded case cast catalog check close collate collation column commit condition connect connection constraint constraints constructor continue corresponding count create cross cube current current_date current_default_transform_group current_transform_group_for_type current_path current_role current_time current_timestamp current_user cursor cycle data day deallocate declare default deferrable deferred delete depth deref desc describe descriptor deterministic diagnostics disconnect distinct do domain drop dynamic each else elseif end end-exec equals escape except exception exec execute exists exit external fetch first for foreign found from free full function general get global go goto grant group grouping handle having hold hour identity if immediate in indicator initially inner inout input insert intersect into is isolation join key language last lateral leading leave left level like limit local localtime localtimestamp locator loop map match method minute modifies module month names natural nesting new next no none not of old on only open option or order ordinality out outer output overlaps pad parameter partial path prepare preserve primary prior privileges procedure public read reads recursive redo ref references referencing relative release repeat resignal restrict result return returns revoke right role rollback rollup routine row rows savepoint schema scroll search second section select session session_user set sets signal similar size some space specific specifictype sql sqlexception sqlstate sqlwarning start state static system_user table temporary then timezone_hour timezone_minute to trailing transaction translation treat trigger under undo union unique unnest until update usage user using value values view when whenever where while with without work write year zone ";const Y={backslashEscapes:false,hashComments:false,spaceAfterDashes:false,slashComments:false,doubleQuotedStrings:false,doubleDollarQuotedStrings:false,unquotedBitLiterals:false,treatBitsAsBytes:false,charSetCasts:false,operatorChars:"*+-%<>!=&|~^/",specialVar:"?",identifierQuotes:'"',words:W(A,G)};function K(e,t,a,n){let r={};for(let i in Y)r[i]=(e.hasOwnProperty(i)?e:Y)[i];if(t)r.words=W(t,a||"",n);return r}function M(e){return new o.Jq((t=>{var a;let{next:n}=t;t.advance();if(N(n,E)){while(N(t.next,E))t.advance();t.acceptToken(c)}else if(n==36&&t.next==36&&e.doubleDollarQuotedStrings){L(t);t.acceptToken(m)}else if(n==39||n==34&&e.doubleQuotedStrings){R(t,n,e.backslashEscapes);t.acceptToken(m)}else if(n==35&&e.hashComments||n==47&&t.next==47&&e.slashComments){$(t);t.acceptToken(d)}else if(n==45&&t.next==45&&(!e.spaceAfterDashes||t.peek(1)==32)){$(t);t.acceptToken(d)}else if(n==47&&t.next==42){t.advance();for(let e=1;;){let a=t.next;if(t.next<0)break;t.advance();if(a==42&&t.next==47){e--;t.advance();if(!e)break}else if(a==47&&t.next==42){e++;t.advance()}}t.acceptToken(u)}else if((n==101||n==69)&&t.next==39){t.advance();R(t,39,true)}else if((n==110||n==78)&&t.next==39&&e.charSetCasts){t.advance();R(t,39,e.backslashEscapes);t.acceptToken(m)}else if(n==95&&e.charSetCasts){for(let a=0;;a++){if(t.next==39&&a>1){t.advance();R(t,39,e.backslashEscapes);t.acceptToken(m);break}if(!j(t.next))break;t.advance()}}else if(n==40){t.acceptToken(h)}else if(n==41){t.acceptToken(_)}else if(n==123){t.acceptToken(b)}else if(n==125){t.acceptToken(v)}else if(n==91){t.acceptToken(y)}else if(n==93){t.acceptToken(k)}else if(n==59){t.acceptToken(O)}else if(e.unquotedBitLiterals&&n==48&&t.next==98){t.advance();D(t);t.acceptToken(U)}else if((n==98||n==66)&&(t.next==39||t.next==34)){const a=t.next;t.advance();if(e.treatBitsAsBytes){R(t,a,e.backslashEscapes);t.acceptToken(z)}else{D(t,a);t.acceptToken(U)}}else if(n==48&&(t.next==120||t.next==88)||(n==120||n==88)&&t.next==39){let e=t.next==39;t.advance();while(B(t.next))t.advance();if(e&&t.next==39)t.advance();t.acceptToken(p)}else if(n==46&&t.next>=48&&t.next<=57){I(t,true);t.acceptToken(p)}else if(n==46){t.acceptToken(x)}else if(n>=48&&n<=57){I(t,false);t.acceptToken(p)}else if(N(n,e.operatorChars)){while(N(t.next,e.operatorChars))t.advance();t.acceptToken(w)}else if(N(n,e.specialVar)){if(t.next==n)t.advance();V(t);t.acceptToken(C)}else if(N(n,e.identifierQuotes)){R(t,n,false);t.acceptToken(P)}else if(n==58||n==44){t.acceptToken(Q)}else if(j(n)){let r=Z(t,String.fromCharCode(n));t.acceptToken(t.next==46?S:(a=e.words[r.toLowerCase()])!==null&&a!==void 0?a:S)}}))}const F=M(Y);const J=o.WQ.deserialize({version:14,states:"%vQ]QQOOO#wQRO'#DSO$OQQO'#CwO%eQQO'#CxO%lQQO'#CyO%sQQO'#CzOOQQ'#DS'#DSOOQQ'#C}'#C}O'UQRO'#C{OOQQ'#Cv'#CvOOQQ'#C|'#C|Q]QQOOQOQQOOO'`QQO'#DOO(xQRO,59cO)PQQO,59cO)UQQO'#DSOOQQ,59d,59dO)cQQO,59dOOQQ,59e,59eO)jQQO,59eOOQQ,59f,59fO)qQQO,59fOOQQ-E6{-E6{OOQQ,59b,59bOOQQ-E6z-E6zOOQQ,59j,59jOOQQ-E6|-E6|O+VQRO1G.}O+^QQO,59cOOQQ1G/O1G/OOOQQ1G/P1G/POOQQ1G/Q1G/QP+kQQO'#C}O+rQQO1G.}O)PQQO,59cO,PQQO'#Cw",stateData:",[~OtOSPOSQOS~ORUOSUOTUOUUOVROXSOZTO]XO^QO_UO`UOaPObPOcPOdUOeUOfUOgUOhUO~O^]ORvXSvXTvXUvXVvXXvXZvX]vX_vX`vXavXbvXcvXdvXevXfvXgvXhvX~OsvX~P!jOa_Ob_Oc_O~ORUOSUOTUOUUOVROXSOZTO^tO_UO`UOa`Ob`Oc`OdUOeUOfUOgUOhUO~OWaO~P$ZOYcO~P$ZO[eO~P$ZORUOSUOTUOUUOVROXSOZTO^QO_UO`UOaPObPOcPOdUOeUOfUOgUOhUO~O]hOsoX~P%zOajObjOcjO~O^]ORkaSkaTkaUkaVkaXkaZka]ka_ka`kaakabkackadkaekafkagkahka~Oska~P'kO^]O~OWvXYvX[vX~P!jOWnO~P$ZOYoO~P$ZO[pO~P$ZO^]ORkiSkiTkiUkiVkiXkiZki]ki_ki`kiakibkickidkiekifkigkihki~Oski~P)xOWkaYka[ka~P'kO]hO~P$ZOWkiYki[ki~P)xOasObsOcsO~O",goto:"#hwPPPPPPPPPPPPPPPPPPPPPPPPPPx||||!Y!^!d!xPPP#[TYOZeUORSTWZbdfqT[OZQZORiZSWOZQbRQdSQfTZgWbdfqQ^PWk^lmrQl_Qm`RrseVORSTWZbdfq",nodeNames:"⚠ LineComment BlockComment String Number Bool Null ( ) { } [ ] ; . Operator Punctuation SpecialVar Identifier QuotedIdentifier Keyword Type Bits Bytes Builtin Script Statement CompositeIdentifier Parens Braces Brackets Statement",maxTerm:38,skippedNodes:[0,1,2],repeatNodeCount:3,tokenData:"RORO",tokenizers:[0,F],topRules:{Script:[0,25]},tokenPrec:0});function H(e){let t=e.cursor().moveTo(e.from,-1);while(/Comment/.test(t.name))t.moveTo(t.from,-1);return t.node}function ee(e,t){let a=e.sliceString(t.from,t.to);let n=/^([`'"])(.*)\1$/.exec(a);return n?n[2]:a}function te(e){return e&&(e.name=="Identifier"||e.name=="QuotedIdentifier")}function ae(e,t){if(t.name=="CompositeIdentifier"){let a=[];for(let n=t.firstChild;n;n=n.nextSibling)if(te(n))a.push(ee(e,n));return a}return[ee(e,t)]}function ne(e,t){for(let a=[];;){if(!t||t.name!=".")return a;let n=H(t);if(!te(n))return a;a.unshift(ee(e,n));t=H(n)}}function re(e,t){let a=(0,n.syntaxTree)(e).resolveInner(t,-1);let r=se(e.doc,a);if(a.name=="Identifier"||a.name=="QuotedIdentifier"||a.name=="Keyword"){return{from:a.from,quoted:a.name=="QuotedIdentifier"?e.doc.sliceString(a.from,a.from+1):null,parents:ne(e.doc,H(a)),aliases:r}}if(a.name=="."){return{from:t,quoted:null,parents:ne(e.doc,a),aliases:r}}else{return{from:t,quoted:null,parents:[],empty:true,aliases:r}}}const ie=new Set("where group having order union intersect except all distinct limit offset fetch for".split(" "));function se(e,t){let a;for(let r=t;!a;r=r.parent){if(!r)return null;if(r.name=="Statement")a=r}let n=null;for(let r=a.firstChild,i=false,s=null;r;r=r.nextSibling){let t=r.name=="Keyword"?e.sliceString(r.from,r.to).toLowerCase():null;let a=null;if(!i){i=t=="from"}else if(t=="as"&&s&&te(r.nextSibling)){a=ee(e,r.nextSibling)}else if(t&&ie.has(t)){break}else if(s&&te(r)){a=ee(e,r)}if(a){if(!n)n=Object.create(null);n[a]=ae(e,s)}s=/Identifier$/.test(r.name)?r:null}return n}function oe(e,t){if(!e)return t;return t.map((t=>Object.assign(Object.assign({},t),{label:e+t.label+e,apply:undefined})))}const le=/^\w*$/,ce=/^[`'"]?\w*[`'"]?$/;class de{constructor(){this.list=[];this.children=undefined}child(e){let t=this.children||(this.children=Object.create(null));return t[e]||(t[e]=new de)}childCompletions(e){return this.children?Object.keys(this.children).filter((e=>e)).map((t=>({label:t,type:e}))):[]}}function ue(e,t,a,n,r){let i=new de;let s=i.child(r||"");for(let o in e){let t=o.indexOf(".");let a=t>-1?i.child(o.slice(0,t)):s;let n=a.child(t>-1?o.slice(t+1):o);n.list=e[o].map((e=>typeof e=="string"?{label:e,type:"property"}:e))}s.list=(t||s.childCompletions("type")).concat(n?s.child(n).list:[]);for(let o in i.children){let e=i.child(o);if(!e.list.length)e.list=e.childCompletions("type")}i.list=s.list.concat(a||i.childCompletions("type"));return e=>{let{parents:t,from:a,quoted:r,empty:o,aliases:l}=re(e.state,e.pos);if(o&&!e.explicit)return null;if(l&&t.length==1)t=l[t[0]]||t;let c=i;for(let m of t){while(!c.children||!c.children[m]){if(c==i)c=s;else if(c==s&&n)c=c.child(n);else return null}c=c.child(m)}let d=r&&e.state.sliceDoc(e.pos,e.pos+1)==r;let u=c.list;if(c==i&&l)u=u.concat(Object.keys(l).map((e=>({label:e,type:"constant"}))));return{from:a,to:d?e.pos+1:undefined,options:oe(r,u),validFor:r?ce:le}}}function me(e,t){let a=Object.keys(e).map((a=>({label:t?a.toUpperCase():a,type:e[a]==T?"type":e[a]==q?"keyword":"variable",boost:-1})));return(0,l.eC)(["QuotedIdentifier","SpecialVar","String","LineComment","BlockComment","."],(0,l.Mb)(a))}let pe=J.configure({props:[n.indentNodeProp.add({Statement:(0,n.continuedIndent)()}),n.foldNodeProp.add({Statement(e){return{from:e.firstChild.to,to:e.to}},BlockComment(e){return{from:e.from+2,to:e.to-2}}}),(0,i.styleTags)({Keyword:i.tags.keyword,Type:i.tags.typeName,Builtin:i.tags.standard(i.tags.name),Bits:i.tags.number,Bytes:i.tags.string,Bool:i.tags.bool,Null:i.tags["null"],Number:i.tags.number,String:i.tags.string,Identifier:i.tags.name,QuotedIdentifier:i.tags.special(i.tags.string),SpecialVar:i.tags.special(i.tags.name),LineComment:i.tags.lineComment,BlockComment:i.tags.blockComment,Operator:i.tags.operator,"Semi Punctuation":i.tags.punctuation,"( )":i.tags.paren,"{ }":i.tags.brace,"[ ]":i.tags.squareBracket})]});class fe{constructor(e,t){this.dialect=e;this.language=t}get extension(){return this.language.extension}static define(e){let t=K(e,e.keywords,e.types,e.builtin);let a=n.LRLanguage.define({name:"sql",parser:pe.configure({tokenizers:[{from:F,to:M(t)}]}),languageData:{commentTokens:{line:"--",block:{open:"/*",close:"*/"}},closeBrackets:{brackets:["(","[","{","'",'"',"`"]}}});return new fe(t,a)}}function ge(e,t=false){return me(e.dialect.words,t)}function he(e,t=false){return e.language.data.of({autocomplete:ge(e,t)})}function _e(e){return e.schema?ue(e.schema,e.tables,e.schemas,e.defaultTable,e.defaultSchema):()=>null}function be(e){return e.schema?(e.dialect||ye).language.data.of({autocomplete:_e(e)}):[]}function ve(e={}){let t=e.dialect||ye;return new n.LanguageSupport(t.language,[be(e),he(t,!!e.upperCaseKeywords)])}const ye=fe.define({});const ke=fe.define({charSetCasts:true,doubleDollarQuotedStrings:true,operatorChars:"+-*/<>=~!@#%^&|`?",specialVar:"",keywords:A+"a abort abs absent access according ada admin aggregate alias also always analyse analyze array_agg array_max_cardinality asensitive assert assignment asymmetric atomic attach attribute attributes avg backward base64 begin_frame begin_partition bernoulli bit_length blocked bom c cache called cardinality catalog_name ceil ceiling chain char_length character_length character_set_catalog character_set_name character_set_schema characteristics characters checkpoint class class_origin cluster coalesce cobol collation_catalog collation_name collation_schema collect column_name columns command_function command_function_code comment comments committed concurrently condition_number configuration conflict connection_name constant constraint_catalog constraint_name constraint_schema contains content control conversion convert copy corr cost covar_pop covar_samp csv cume_dist current_catalog current_row current_schema cursor_name database datalink datatype datetime_interval_code datetime_interval_precision db debug defaults defined definer degree delimiter delimiters dense_rank depends derived detach detail dictionary disable discard dispatch dlnewcopy dlpreviouscopy dlurlcomplete dlurlcompleteonly dlurlcompletewrite dlurlpath dlurlpathonly dlurlpathwrite dlurlscheme dlurlserver dlvalue document dump dynamic_function dynamic_function_code element elsif empty enable encoding encrypted end_frame end_partition endexec enforced enum errcode error event every exclude excluding exclusive exp explain expression extension extract family file filter final first_value flag floor following force foreach fortran forward frame_row freeze fs functions fusion g generated granted greatest groups handler header hex hierarchy hint id ignore ilike immediately immutable implementation implicit import include including increment indent index indexes info inherit inherits inline insensitive instance instantiable instead integrity intersection invoker isnull k key_member key_type label lag last_value lead leakproof least length library like_regex link listen ln load location lock locked log logged lower m mapping matched materialized max max_cardinality maxvalue member merge message message_length message_octet_length message_text min minvalue mod mode more move multiset mumps name namespace nfc nfd nfkc nfkd nil normalize normalized nothing notice notify notnull nowait nth_value ntile nullable nullif nulls number occurrences_regex octet_length octets off offset oids operator options ordering others over overlay overriding owned owner p parallel parameter_mode parameter_name parameter_ordinal_position parameter_specific_catalog parameter_specific_name parameter_specific_schema parser partition pascal passing passthrough password percent percent_rank percentile_cont percentile_disc perform period permission pg_context pg_datatype_name pg_exception_context pg_exception_detail pg_exception_hint placing plans pli policy portion position position_regex power precedes preceding prepared print_strict_params procedural procedures program publication query quote raise range rank reassign recheck recovery refresh regr_avgx regr_avgy regr_count regr_intercept regr_r2 regr_slope regr_sxx regr_sxy regr_syy reindex rename repeatable replace replica requiring reset respect restart restore result_oid returned_cardinality returned_length returned_octet_length returned_sqlstate returning reverse routine_catalog routine_name routine_schema routines row_count row_number rowtype rule scale schema_name schemas scope scope_catalog scope_name scope_schema security selective self sensitive sequence sequences serializable server server_name setof share show simple skip slice snapshot source specific_name sqlcode sqlerror sqrt stable stacked standalone statement statistics stddev_pop stddev_samp stdin stdout storage strict strip structure style subclass_origin submultiset subscription substring substring_regex succeeds sum symmetric sysid system system_time t table_name tables tablesample tablespace temp template ties token top_level_count transaction_active transactions_committed transactions_rolled_back transform transforms translate translate_regex trigger_catalog trigger_name trigger_schema trim trim_array truncate trusted type types uescape unbounded uncommitted unencrypted unlink unlisten unlogged unnamed untyped upper uri use_column use_variable user_defined_type_catalog user_defined_type_code user_defined_type_name user_defined_type_schema vacuum valid validate validator value_of var_pop var_samp varbinary variable_conflict variadic verbose version versioning views volatile warning whitespace width_bucket window within wrapper xmlagg xmlattributes xmlbinary xmlcast xmlcomment xmlconcat xmldeclaration xmldocument xmlelement xmlexists xmlforest xmliterate xmlnamespaces xmlparse xmlpi xmlquery xmlroot xmlschema xmlserialize xmltable xmltext xmlvalidate yes",types:G+"bigint int8 bigserial serial8 varbit bool box bytea cidr circle precision float8 inet int4 json jsonb line lseg macaddr macaddr8 money numeric pg_lsn point polygon float4 int2 smallserial serial2 serial serial4 text timetz timestamptz tsquery tsvector txid_snapshot uuid xml"});const Oe="accessible algorithm analyze asensitive authors auto_increment autocommit avg avg_row_length binlog btree cache catalog_name chain change changed checkpoint checksum class_origin client_statistics coalesce code collations columns comment committed completion concurrent consistent contains contributors convert database databases day_hour day_microsecond day_minute day_second delay_key_write delayed delimiter des_key_file dev_pop dev_samp deviance directory disable discard distinctrow div dual dumpfile enable enclosed ends engine engines enum errors escaped even event events every explain extended fast field fields flush force found_rows fulltext grants handler hash high_priority hosts hour_microsecond hour_minute hour_second ignore ignore_server_ids import index index_statistics infile innodb insensitive insert_method install invoker iterate keys kill linear lines list load lock logs low_priority master master_heartbeat_period master_ssl_verify_server_cert masters max max_rows maxvalue message_text middleint migrate min min_rows minute_microsecond minute_second mod mode modify mutex mysql_errno no_write_to_binlog offline offset one online optimize optionally outfile pack_keys parser partition partitions password phase plugin plugins prev processlist profile profiles purge query quick range read_write rebuild recover regexp relaylog remove rename reorganize repair repeatable replace require resume rlike row_format rtree schedule schema_name schemas second_microsecond security sensitive separator serializable server share show slave slow snapshot soname spatial sql_big_result sql_buffer_result sql_cache sql_calc_found_rows sql_no_cache sql_small_result ssl starting starts std stddev stddev_pop stddev_samp storage straight_join subclass_origin sum suspend table_name table_statistics tables tablespace terminated triggers truncate uncommitted uninstall unlock upgrade use use_frm user_resources user_statistics utc_date utc_time utc_timestamp variables views warnings xa xor year_month zerofill";const xe=G+"bool blob long longblob longtext medium mediumblob mediumint mediumtext tinyblob tinyint tinytext text bigint int1 int2 int3 int4 int8 float4 float8 varbinary varcharacter precision datetime unsigned signed";const we="charset clear edit ego help nopager notee nowarning pager print prompt quit rehash source status system tee";const Qe=fe.define({operatorChars:"*+-%<>!=&|^",charSetCasts:true,doubleQuotedStrings:true,unquotedBitLiterals:true,hashComments:true,spaceAfterDashes:true,specialVar:"@?",identifierQuotes:"`",keywords:A+"group_concat "+Oe,types:xe,builtin:we});const Ce=fe.define({operatorChars:"*+-%<>!=&|^",charSetCasts:true,doubleQuotedStrings:true,unquotedBitLiterals:true,hashComments:true,spaceAfterDashes:true,specialVar:"@?",identifierQuotes:"`",keywords:A+"always generated groupby_concat hard persistent shutdown soft virtual "+Oe,types:xe,builtin:we});const Se=fe.define({keywords:A+"trigger proc view index for add constraint key primary foreign collate clustered nonclustered declare exec go if use index holdlock nolock nowait paglock pivot readcommitted readcommittedlock readpast readuncommitted repeatableread rowlock serializable snapshot tablock tablockx unpivot updlock with",types:G+"bigint smallint smallmoney tinyint money real text nvarchar ntext varbinary image hierarchyid uniqueidentifier sql_variant xml",builtin:"binary_checksum checksum connectionproperty context_info current_request_id error_line error_message error_number error_procedure error_severity error_state formatmessage get_filestream_transaction_context getansinull host_id host_name isnull isnumeric min_active_rowversion newid newsequentialid rowcount_big xact_state object_id",operatorChars:"*+-%<>!=^&|/",specialVar:"@"});const Pe=fe.define({keywords:A+"abort analyze attach autoincrement conflict database detach exclusive fail glob ignore index indexed instead isnull notnull offset plan pragma query raise regexp reindex rename replace temp vacuum virtual",types:G+"bool blob long longblob longtext medium mediumblob mediumint mediumtext tinyblob tinyint tinytext text bigint int2 int8 unsigned signed real",builtin:"auth backup bail changes clone databases dbinfo dump echo eqp explain fullschema headers help import imposter indexes iotrace lint load log mode nullvalue once print prompt quit restore save scanstats separator shell show stats system tables testcase timeout timer trace vfsinfo vfslist vfsname width",operatorChars:"*+-%<>!=&|/~",identifierQuotes:'`"',specialVar:"@:?$"});const qe=fe.define({keywords:"add all allow alter and any apply as asc authorize batch begin by clustering columnfamily compact consistency count create custom delete desc distinct drop each_quorum exists filtering from grant if in index insert into key keyspace keyspaces level limit local_one local_quorum modify nan norecursive nosuperuser not of on one order password permission permissions primary quorum rename revoke schema select set storage superuser table three to token truncate ttl two type unlogged update use user users using values where with writetime infinity NaN",types:G+"ascii bigint blob counter frozen inet list map static text timeuuid tuple uuid varint",slashComments:true});const Te=fe.define({keywords:A+"abort accept access add all alter and any arraylen as asc assert assign at attributes audit authorization avg base_table begin between binary_integer body by case cast char_base check close cluster clusters colauth column comment commit compress connected constant constraint crash create current currval cursor data_base database dba deallocate debugoff debugon declare default definition delay delete desc digits dispose distinct do drop else elseif elsif enable end entry exception exception_init exchange exclusive exists external fast fetch file for force form from function generic goto grant group having identified if immediate in increment index indexes indicator initial initrans insert interface intersect into is key level library like limited local lock log logging loop master maxextents maxtrans member minextents minus mislabel mode modify multiset new next no noaudit nocompress nologging noparallel not nowait number_base of off offline on online only option or order out package parallel partition pctfree pctincrease pctused pls_integer positive positiven pragma primary prior private privileges procedure public raise range raw rebuild record ref references refresh rename replace resource restrict return returning returns reverse revoke rollback row rowid rowlabel rownum rows run savepoint schema segment select separate set share snapshot some space split sql start statement storage subtype successful synonym tabauth table tables tablespace task terminate then to trigger truncate type union unique unlimited unrecoverable unusable update use using validate value values variable view views when whenever where while with work",builtin:"appinfo arraysize autocommit autoprint autorecovery autotrace blockterminator break btitle cmdsep colsep compatibility compute concat copycommit copytypecheck define echo editfile embedded feedback flagger flush heading headsep instance linesize lno loboffset logsource longchunksize markup native newpage numformat numwidth pagesize pause pno recsep recsepchar repfooter repheader serveroutput shiftinout show showmode spool sqlblanklines sqlcase sqlcode sqlcontinue sqlnumber sqlpluscompatibility sqlprefix sqlprompt sqlterminator suffix tab term termout timing trimout trimspool ttitle underline verify version wrap",types:G+"ascii bfile bfilename bigserial bit blob dec long number nvarchar nvarchar2 serial smallint string text uid varchar2 xml",operatorChars:"*/+-%<>!=~",doubleQuotedStrings:true,charSetCasts:true})}}]);
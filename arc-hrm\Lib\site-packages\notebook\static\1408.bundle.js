"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[1408],{11408:(e,t,a)=>{a.r(t),a.d(t,{MathJaxTypesetter:()=>o,default:()=>r});var n,i=a(20998),s=a(70856);!function(e){e.copy="mathjax:clipboard",e.scale="mathjax:scale"}(n||(n={}));class o{constructor(){this._initialized=!1}async _ensureInitialized(){this._initialized||(this._mathDocument=await c.ensureMathDocument(),this._initialized=!0)}async mathDocument(){return await this._ensureInitialized(),this._mathDocument}async typeset(e){try{await this._ensureInitialized()}catch(e){return void console.error(e)}this._mathDocument.options.elements=[e],this._mathDocument.clear().render(),delete this._mathDocument.options.elements}}const r={id:"@jupyterlab/mathjax-extension:plugin",description:"Provides the LaTeX mathematical expression interpreter.",provides:s.ILatexTypesetter,activate:e=>{const t=new o;return e.commands.addCommand(n.copy,{execute:async()=>{const e=(await t.mathDocument()).outputJax;await navigator.clipboard.writeText(e.math.math)},label:"MathJax Copy Latex"}),e.commands.addCommand(n.scale,{execute:async e=>{const a=await t.mathDocument(),n=e.scale||1;a.outputJax.options.scale=n,a.rerender()},label:e=>"Mathjax Scale "+(e.scale?`x${e.scale}`:"Reset")}),t},autoStart:!0};var c;!function(e){let t=null;e.ensureMathDocument=async function(){if(!t){t=new i.PromiseDelegate,Promise.all([a.e(7969),a.e(5115),a.e(792)]).then(a.t.bind(a,20792,23));const[{mathjax:e},{CHTML:n},{TeX:s},{TeXFont:o},{AllPackages:r},{SafeHandler:c},{HTMLHandler:l},{browserAdaptor:m},{AssistiveMmlHandler:h}]=await Promise.all([a.e(4971).then(a.bind(a,44971)),Promise.all([a.e(7969),a.e(28),a.e(2065),a.e(7369)]).then(a.t.bind(a,57369,23)),Promise.all([a.e(7969),a.e(5115),a.e(7154),a.e(7582)]).then(a.t.bind(a,27582,23)),Promise.all([a.e(2065),a.e(4498)]).then(a.t.bind(a,42065,23)),Promise.all([a.e(7969),a.e(5115),a.e(7154),a.e(8845)]).then(a.bind(a,78845)),a.e(8285).then(a.t.bind(a,78285,23)),Promise.all([a.e(7969),a.e(28),a.e(2666),a.e(7471)]).then(a.t.bind(a,97471,23)),a.e(270).then(a.bind(a,90270)),Promise.all([a.e(7969),a.e(28),a.e(2666),a.e(4105)]).then(a.t.bind(a,74105,23))]);e.handlers.register(h(c(new l(m()))));class d extends o{}d.defaultFonts={};const u=new n({font:new d}),p=new s({packages:r.concat("require"),inlineMath:[["$","$"],["\\(","\\)"]],displayMath:[["$$","$$"],["\\[","\\]"]],processEscapes:!0,processEnvironments:!0}),x=e.document(window.document,{InputJax:p,OutputJax:u});t.resolve(x)}return t.promise}}(c||(c={}))}}]);
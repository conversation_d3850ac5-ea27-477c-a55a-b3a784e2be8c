"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[2512],{32512:(e,t,o)=>{o.r(t),o.d(t,{commandEditItem:()=>Y,default:()=>be,executionIndicator:()=>G,exportPlugin:()=>X,notebookTrustItem:()=>Q});var n=o(3053),l=o(12982),a=o(24912),r=o(31536),d=o(38639),i=o(52337),s=o(76867),c=o(90157),u=o(96313),b=o(3486),m=o(97713),p=o(389),g=o(12729),C=o(97104),k=o(55721),v=o(43833),h=o(98037),f=o(70856),w=o(36768),x=o(66853),_=o(34276),A=o(87749),y=o(71677),S=o(68239),E=o(33625),N=o(20998),I=o(2549),M=o(49503),T=o(31516),R=o(51537),O=o(16954);const B={activate:function(e,t,o){function n(e){function t(t,n,l){if(O.KernelMessage.isDisplayDataMsg(t)||O.KernelMessage.isStreamMsg(t)||O.KernelMessage.isErrorMsg(t)||O.KernelMessage.isExecuteResultMsg(t)){const a=o.getLogger(e.context.path);a.rendermime=e.content.rendermime;const r={...t.content,output_type:t.header.msg_type};let d=n;(O.KernelMessage.isErrorMsg(t)||O.KernelMessage.isStreamMsg(t)&&"stderr"===t.content.name)&&(d=l),a.log({type:"output",data:r,level:d})}}e.context.sessionContext.iopubMessage.connect(((e,o)=>t(o,"info","info"))),e.context.sessionContext.unhandledMessage.connect(((e,o)=>t(o,"warning","error")))}o&&(t.forEach((e=>n(e))),t.widgetAdded.connect(((e,t)=>n(t))))},id:"@jupyterlab/notebook-extension:log-output",description:"Adds cell outputs log to the application logger.",requires:[v.INotebookTracker],optional:[R.ILoggerRegistry],autoStart:!0};var P=o(78156),H=o.n(P),j=o(97934);class L extends v.NotebookTools.Tool{constructor(e){super();const{languages:t}=e;this._tracker=e.tracker,this.addClass("jp-ActiveCellTool"),this.layout=new T.PanelLayout,this._inputPrompt=new a.InputPrompt,this.layout.addWidget(this._inputPrompt);const o=document.createElement("div");o.classList.add("jp-ActiveCellTool-Content");const n=o.appendChild(document.createElement("div")),l=n.appendChild(document.createElement("pre"));n.className="jp-ActiveCellTool-CellContent",this._editorEl=l,this.layout.addWidget(new T.Widget({node:o})),this._refreshDebouncer=new j.Debouncer((async()=>{var e,o;this._editorEl.innerHTML="","code"===(null===(e=this._cellModel)||void 0===e?void 0:e.type)?(this._inputPrompt.executionCount=`${null!==(o=this._cellModel.executionCount)&&void 0!==o?o:""}`,this._inputPrompt.show()):(this._inputPrompt.executionCount=null,this._inputPrompt.hide()),this._cellModel&&await t.highlight(this._cellModel.sharedModel.getSource().split("\n")[0],t.findByMIME(this._cellModel.mimeType),this._editorEl)}),150)}render(e){var t,o;const n=this._tracker.activeCell;return n&&(this._cellModel=(null==n?void 0:n.model)||null),(null===(t=this._cellModel)||void 0===t?void 0:t.sharedModel).changed.connect(this.refresh,this),null===(o=this._cellModel)||void 0===o||o.mimeTypeChanged.connect(this.refresh,this),this.refresh().then((()=>{})).catch((()=>{})),H().createElement("div",{ref:e=>null==e?void 0:e.appendChild(this.node)})}async refresh(){await this._refreshDebouncer.invoke()}}var D,W=o(11351);class K extends v.NotebookTools.MetadataEditorTool{constructor(e){super(e),this._tracker=e.tracker,this.editor.editorHostNode.addEventListener("blur",this.editor,!0),this.editor.editorHostNode.addEventListener("click",this.editor,!0),this.editor.headerNode.addEventListener("click",this.editor)}_onSourceChanged(){var e;this.editor.source&&(null===(e=this._tracker.activeCell)||void 0===e||e.model.sharedModel.setMetadata(this.editor.source.toJSON()))}render(e){var t;const o=this._tracker.activeCell;return this.editor.source=o?new W.ObservableJSON({values:o.model.metadata}):null,null===(t=this.editor.source)||void 0===t||t.changed.connect(this._onSourceChanged,this),H().createElement("div",{className:"jp-CellMetadataEditor"},H().createElement("div",{ref:e=>null==e?void 0:e.appendChild(this.node)}))}}class F extends v.NotebookTools.MetadataEditorTool{constructor(e){super(e),this._tracker=e.tracker,this.editor.editorHostNode.addEventListener("blur",this.editor,!0),this.editor.editorHostNode.addEventListener("click",this.editor,!0),this.editor.headerNode.addEventListener("click",this.editor)}_onSourceChanged(){var e,t;this.editor.source&&(null===(t=null===(e=this._tracker.currentWidget)||void 0===e?void 0:e.model)||void 0===t||t.sharedModel.setMetadata(this.editor.source.toJSON()))}render(e){var t,o;const n=this._tracker.currentWidget;return this.editor.source=n?new W.ObservableJSON({values:null===(t=n.model)||void 0===t?void 0:t.metadata}):null,null===(o=this.editor.source)||void 0===o||o.changed.connect(this._onSourceChanged,this),H().createElement("div",{className:"jp-NotebookMetadataEditor"},H().createElement("div",{ref:e=>null==e?void 0:e.appendChild(this.node)}))}}!function(e){e.createNew="notebook:create-new",e.interrupt="notebook:interrupt-kernel",e.restart="notebook:restart-kernel",e.restartClear="notebook:restart-clear-output",e.restartAndRunToSelected="notebook:restart-and-run-to-selected",e.restartRunAll="notebook:restart-run-all",e.reconnectToKernel="notebook:reconnect-to-kernel",e.changeKernel="notebook:change-kernel",e.getKernel="notebook:get-kernel",e.createConsole="notebook:create-console",e.createOutputView="notebook:create-output-view",e.clearAllOutputs="notebook:clear-all-cell-outputs",e.shutdown="notebook:shutdown-kernel",e.closeAndShutdown="notebook:close-and-shutdown",e.trust="notebook:trust",e.exportToFormat="notebook:export-to-format",e.run="notebook:run-cell",e.runAndAdvance="notebook:run-cell-and-select-next",e.runAndInsert="notebook:run-cell-and-insert-below",e.runInConsole="notebook:run-in-console",e.runAll="notebook:run-all-cells",e.runAllAbove="notebook:run-all-above",e.runAllBelow="notebook:run-all-below",e.renderAllMarkdown="notebook:render-all-markdown",e.toCode="notebook:change-cell-to-code",e.toMarkdown="notebook:change-cell-to-markdown",e.toRaw="notebook:change-cell-to-raw",e.cut="notebook:cut-cell",e.copy="notebook:copy-cell",e.pasteAbove="notebook:paste-cell-above",e.pasteBelow="notebook:paste-cell-below",e.duplicateBelow="notebook:duplicate-below",e.pasteAndReplace="notebook:paste-and-replace-cell",e.moveUp="notebook:move-cell-up",e.moveDown="notebook:move-cell-down",e.clearOutputs="notebook:clear-cell-output",e.deleteCell="notebook:delete-cell",e.insertAbove="notebook:insert-cell-above",e.insertBelow="notebook:insert-cell-below",e.selectAbove="notebook:move-cursor-up",e.selectBelow="notebook:move-cursor-down",e.selectHeadingAboveOrCollapse="notebook:move-cursor-heading-above-or-collapse",e.selectHeadingBelowOrExpand="notebook:move-cursor-heading-below-or-expand",e.insertHeadingAbove="notebook:insert-heading-above",e.insertHeadingBelow="notebook:insert-heading-below",e.extendAbove="notebook:extend-marked-cells-above",e.extendTop="notebook:extend-marked-cells-top",e.extendBelow="notebook:extend-marked-cells-below",e.extendBottom="notebook:extend-marked-cells-bottom",e.selectAll="notebook:select-all",e.deselectAll="notebook:deselect-all",e.editMode="notebook:enter-edit-mode",e.merge="notebook:merge-cells",e.mergeAbove="notebook:merge-cell-above",e.mergeBelow="notebook:merge-cell-below",e.split="notebook:split-cell-at-cursor",e.commandMode="notebook:enter-command-mode",e.toggleAllLines="notebook:toggle-all-cell-line-numbers",e.undoCellAction="notebook:undo-cell-action",e.redoCellAction="notebook:redo-cell-action",e.redo="notebook:redo",e.undo="notebook:undo",e.markdown1="notebook:change-cell-to-heading-1",e.markdown2="notebook:change-cell-to-heading-2",e.markdown3="notebook:change-cell-to-heading-3",e.markdown4="notebook:change-cell-to-heading-4",e.markdown5="notebook:change-cell-to-heading-5",e.markdown6="notebook:change-cell-to-heading-6",e.hideCode="notebook:hide-cell-code",e.showCode="notebook:show-cell-code",e.hideAllCode="notebook:hide-all-cell-code",e.showAllCode="notebook:show-all-cell-code",e.hideOutput="notebook:hide-cell-outputs",e.showOutput="notebook:show-cell-outputs",e.hideAllOutputs="notebook:hide-all-cell-outputs",e.showAllOutputs="notebook:show-all-cell-outputs",e.toggleRenderSideBySideCurrentNotebook="notebook:toggle-render-side-by-side-current",e.setSideBySideRatio="notebook:set-side-by-side-ratio",e.enableOutputScrolling="notebook:enable-output-scrolling",e.disableOutputScrolling="notebook:disable-output-scrolling",e.selectLastRunCell="notebook:select-last-run-cell",e.replaceSelection="notebook:replace-selection",e.autoClosingBrackets="notebook:toggle-autoclosing-brackets",e.toggleCollapseCmd="notebook:toggle-heading-collapse",e.collapseAllCmd="notebook:collapse-all-headings",e.expandAllCmd="notebook:expand-all-headings",e.copyToClipboard="notebook:copy-to-clipboard",e.invokeCompleter="completer:invoke-notebook",e.selectCompleter="completer:select-notebook",e.tocRunCells="toc:run-cells",e.accessPreviousHistory="notebook:access-previous-history-entry",e.accessNextHistory="notebook:access-next-history-entry",e.virtualScrollbar="notebook:toggle-virtual-scrollbar"}(D||(D={}));const U="Notebook",q=["notebook","python","custom"],V="jp-NotebookExtension-sideBySideMargins",$={id:"@jupyterlab/notebook-extension:tracker",description:"Provides the notebook widget tracker.",provides:v.INotebookTracker,requires:[v.INotebookWidgetFactory,i.IEditorExtensionRegistry],optional:[l.ICommandPalette,m.IDefaultFileBrowser,p.ILauncher,n.ILayoutRestorer,C.IMainMenu,n.IRouter,w.ISettingRegistry,l.ISessionContextDialogs,y.ITranslator,S.IFormRendererRegistry,m.IFileBrowserFactory],activate:function(e,t,o,n,a,r,d,i,s,c,u,b,m,p){const g=null!=b?b:y.nullTranslator,C=null!=u?u:new l.SessionContextDialogs({translator:g}),k=g.load("jupyterlab"),h=e.serviceManager,{commands:f,shell:w}=e,x=new v.NotebookTracker({namespace:"notebook"});null==s||s.routed.connect((function(e,t){t.hash&&x.currentWidget&&x.currentWidget.setFragment(t.hash)}));const _=()=>ge.isEnabled(w,x),A=e=>document.documentElement.style.setProperty("--jp-side-by-side-output-size",`${e}fr`);if((c?c.load($.id):Promise.reject(new Error(`No setting registry for ${$.id}`))).then((t=>{B(t),t.changed.connect((()=>{B(t),f.notifyCommandChanged(D.virtualScrollbar)}));const n=(e,o)=>{const{newValue:n,oldValue:l}=o,a=n.autoStartDefault;"boolean"==typeof a&&a!==l.autoStartDefault&&a!==t.get("autoStartDefaultKernel").composite&&t.set("autoStartDefaultKernel",a).catch((e=>{console.error(`Failed to set ${t.id}.autoStartDefaultKernel`)}))},a=new WeakSet,r=e=>{const t=e.context.sessionContext;t.isDisposed||a.has(t)||(a.add(t),t.kernelPreferenceChanged.connect(n),t.disposed.connect((()=>{t.kernelPreferenceChanged.disconnect(n)})))};x.forEach(r),x.widgetAdded.connect(((e,t)=>{r(t)})),f.addCommand(D.autoClosingBrackets,{execute:e=>{var o;const n=t.get("codeCellConfig").composite,l=t.get("markdownCellConfig").composite,a=t.get("rawCellConfig").composite,r=n.autoClosingBrackets||l.autoClosingBrackets||a.autoClosingBrackets,d=!!(null!==(o=e.force)&&void 0!==o?o:!r);[n.autoClosingBrackets,l.autoClosingBrackets,a.autoClosingBrackets]=[d,d,d],t.set("codeCellConfig",n),t.set("markdownCellConfig",l),t.set("rawCellConfig",a)},label:k.__("Auto Close Brackets for All Notebook Cell Types"),isToggled:()=>["codeCellConfig","markdownCellConfig","rawCellConfig"].some((e=>{var n;return!0===(null!==(n=t.get(e).composite.autoClosingBrackets)&&void 0!==n?n:o.baseConfiguration.autoClosingBrackets)}))}),f.addCommand(D.setSideBySideRatio,{label:k.__("Set side-by-side ratio"),execute:e=>{l.InputDialog.getNumber({title:k.__("Width of the output in side-by-side mode"),value:t.get("sideBySideOutputRatio").composite}).then((e=>{A(e.value),e.value&&t.set("sideBySideOutputRatio",e.value)})).catch(console.error)}}),pe(e,x,g,C,t,_)})).catch((o=>{console.warn(o.message),O({editorConfig:t.editorConfig,notebookConfig:t.notebookConfig,kernelShutdown:t.shutdownOnClose,autoStartDefault:t.autoStartDefault}),pe(e,x,g,C,null,_)})),m){const e=m.getRenderer("@jupyterlab/codemirror-extension:plugin.defaultConfig");e&&(m.addRenderer("@jupyterlab/notebook-extension:tracker.codeCellConfig",e),m.addRenderer("@jupyterlab/notebook-extension:tracker.markdownCellConfig",e),m.addRenderer("@jupyterlab/notebook-extension:tracker.rawCellConfig",e))}d&&d.restore(x,{command:"docmanager:open",args:e=>({path:e.context.path,factory:U}),name:e=>e.context.path,when:h.ready});const E=e.docRegistry,M=new v.NotebookModelFactory({disableDocumentWideUndoRedo:t.notebookConfig.disableDocumentWideUndoRedo,collaborative:!0});E.addModelFactory(M),n&&function(e,t){const o=t.load("jupyterlab");let n=o.__("Notebook Operations");[D.interrupt,D.restart,D.restartClear,D.restartRunAll,D.runAll,D.renderAllMarkdown,D.runAllAbove,D.runAllBelow,D.restartAndRunToSelected,D.selectAll,D.deselectAll,D.clearAllOutputs,D.toggleAllLines,D.editMode,D.commandMode,D.changeKernel,D.reconnectToKernel,D.createConsole,D.closeAndShutdown,D.trust,D.toggleCollapseCmd,D.collapseAllCmd,D.expandAllCmd,D.accessPreviousHistory,D.accessNextHistory].forEach((t=>{e.addItem({command:t,category:n})})),e.addItem({command:D.createNew,category:n,args:{isPalette:!0}}),n=o.__("Notebook Cell Operations"),[D.run,D.runAndAdvance,D.runAndInsert,D.runInConsole,D.clearOutputs,D.toCode,D.toMarkdown,D.toRaw,D.cut,D.copy,D.pasteBelow,D.pasteAbove,D.pasteAndReplace,D.deleteCell,D.split,D.merge,D.mergeAbove,D.mergeBelow,D.insertAbove,D.insertBelow,D.selectAbove,D.selectBelow,D.selectHeadingAboveOrCollapse,D.selectHeadingBelowOrExpand,D.insertHeadingAbove,D.insertHeadingBelow,D.extendAbove,D.extendTop,D.extendBelow,D.extendBottom,D.moveDown,D.moveUp,D.undoCellAction,D.redoCellAction,D.markdown1,D.markdown2,D.markdown3,D.markdown4,D.markdown5,D.markdown6,D.hideCode,D.showCode,D.hideAllCode,D.showAllCode,D.hideOutput,D.showOutput,D.hideAllOutputs,D.showAllOutputs,D.toggleRenderSideBySideCurrentNotebook,D.setSideBySideRatio,D.enableOutputScrolling,D.disableOutputScrolling].forEach((t=>{e.addItem({command:t,category:n})}))}(n,g);let T=0;const R=e.docRegistry.getFileType("notebook");function O(e){x.forEach((t=>{t.setConfig(e)})),"full"!==e.notebookConfig.windowingMode&&x.forEach((e=>{e.content.scrollbar&&(e.content.scrollbar=!1)}))}function B(e){const o={...v.StaticNotebook.defaultEditorConfig.code,...e.get("codeCellConfig").composite},n={...v.StaticNotebook.defaultEditorConfig.markdown,...e.get("markdownCellConfig").composite},l={...v.StaticNotebook.defaultEditorConfig.raw,...e.get("rawCellConfig").composite};t.editorConfig={code:o,markdown:n,raw:l},t.notebookConfig={enableKernelInitNotification:e.get("enableKernelInitNotification").composite,showHiddenCellsButton:e.get("showHiddenCellsButton").composite,scrollPastEnd:e.get("scrollPastEnd").composite,defaultCell:e.get("defaultCell").composite,recordTiming:e.get("recordTiming").composite,overscanCount:e.get("overscanCount").composite,inputHistoryScope:e.get("inputHistoryScope").composite,maxNumberOutputs:e.get("maxNumberOutputs").composite,showEditorForReadOnlyMarkdown:e.get("showEditorForReadOnlyMarkdown").composite,disableDocumentWideUndoRedo:!e.get("documentWideUndoRedo").composite,renderingLayout:e.get("renderingLayout").composite,sideBySideLeftMarginOverride:e.get("sideBySideLeftMarginOverride").composite,sideBySideRightMarginOverride:e.get("sideBySideRightMarginOverride").composite,sideBySideOutputRatio:e.get("sideBySideOutputRatio").composite,windowingMode:e.get("windowingMode").composite,accessKernelHistory:e.get("accessKernelHistory").composite},A(t.notebookConfig.sideBySideOutputRatio);const a=`.jp-mod-sideBySide.jp-Notebook .jp-Notebook-cell {\n      margin-left: ${t.notebookConfig.sideBySideLeftMarginOverride} !important;\n      margin-right: ${t.notebookConfig.sideBySideRightMarginOverride} !important;`,r=document.getElementById(V);r?r.innerText=a:document.head.insertAdjacentHTML("beforeend",`<style id="${V}">${a}}</style>`),t.autoStartDefault=e.get("autoStartDefaultKernel").composite,t.shutdownOnClose=e.get("kernelShutdown").composite,M.disableDocumentWideUndoRedo=!e.get("documentWideUndoRedo").composite,O({editorConfig:t.editorConfig,notebookConfig:t.notebookConfig,kernelShutdown:t.shutdownOnClose,autoStartDefault:t.autoStartDefault})}return t.widgetCreated.connect(((e,t)=>{var o,n;t.id=t.id||"notebook-"+ ++T,t.title.icon=null==R?void 0:R.icon,t.title.iconClass=null!==(o=null==R?void 0:R.iconClass)&&void 0!==o?o:"",t.title.iconLabel=null!==(n=null==R?void 0:R.iconLabel)&&void 0!==n?n:"",t.context.pathChanged.connect((()=>{x.save(t)})),x.add(t)})),i&&function(e,t){e.editMenu.undoers.redo.add({id:D.redo,isEnabled:t}),e.editMenu.undoers.undo.add({id:D.undo,isEnabled:t}),e.editMenu.clearers.clearAll.add({id:D.clearAllOutputs,isEnabled:t}),e.editMenu.clearers.clearCurrent.add({id:D.clearOutputs,isEnabled:t}),e.fileMenu.consoleCreators.add({id:D.createConsole,isEnabled:t}),e.fileMenu.closeAndCleaners.add({id:D.closeAndShutdown,isEnabled:t}),e.kernelMenu.kernelUsers.changeKernel.add({id:D.changeKernel,isEnabled:t}),e.kernelMenu.kernelUsers.clearWidget.add({id:D.clearAllOutputs,isEnabled:t}),e.kernelMenu.kernelUsers.interruptKernel.add({id:D.interrupt,isEnabled:t}),e.kernelMenu.kernelUsers.reconnectToKernel.add({id:D.reconnectToKernel,isEnabled:t}),e.kernelMenu.kernelUsers.restartKernel.add({id:D.restart,isEnabled:t}),e.kernelMenu.kernelUsers.shutdownKernel.add({id:D.shutdown,isEnabled:t}),e.viewMenu.editorViewers.toggleLineNumbers.add({id:D.toggleAllLines,isEnabled:t}),e.runMenu.codeRunners.restart.add({id:D.restart,isEnabled:t}),e.runMenu.codeRunners.run.add({id:D.runAndAdvance,isEnabled:t}),e.runMenu.codeRunners.runAll.add({id:D.runAll,isEnabled:t}),e.helpMenu.getKernel.add({id:D.getKernel,isEnabled:t})}(i,_),f.addCommand(D.createNew,{label:e=>{var t,o,n;const l=e.kernelName||"";return e.isLauncher&&e.kernelName&&h.kernelspecs?null!==(n=null===(o=null===(t=h.kernelspecs.specs)||void 0===t?void 0:t.kernelspecs[l])||void 0===o?void 0:o.display_name)&&void 0!==n?n:"":e.isPalette||e.isContextMenu?k.__("New Notebook"):k.__("Notebook")},caption:k.__("Create a new notebook"),icon:e=>e.isPalette?void 0:S.notebookIcon,execute:e=>{var t,o;const n=null!==(t=null==p?void 0:p.tracker.currentWidget)&&void 0!==t?t:a;return(async(e,t,o)=>{const n=await f.execute("docmanager:new-untitled",{path:e,type:"notebook"});if(void 0!==n){const e=await f.execute("docmanager:open",{path:n.path,factory:U,kernel:{id:t,name:o}});return e.isUntitled=!0,e}})(e.cwd||(null!==(o=null==n?void 0:n.model.path)&&void 0!==o?o:""),e.kernelId||"",e.kernelName||"")}}),r&&h.ready.then((()=>{let e=null;const t=()=>{e&&(e.dispose(),e=null);const t=h.kernelspecs.specs;if(t){e=new I.DisposableSet;for(const o in t.kernelspecs){const n=o===t.default?0:1/0,l=t.kernelspecs[o],a=l.resources["logo-svg"]||l.resources["logo-64x64"];e.add(r.add({command:D.createNew,args:{isLauncher:!0,kernelName:o},category:k.__("Notebook"),rank:n,kernelIconUrl:a,metadata:{kernel:N.JSONExt.deepCopy(l.metadata||{})}}))}}};t(),h.kernelspecs.specsChanged.connect(t)})),x},autoStart:!0},J={id:"@jupyterlab/notebook-extension:factory",description:"Provides the notebook cell factory.",provides:v.NotebookPanel.IContentFactory,requires:[r.IEditorServices],autoStart:!0,activate:(e,t)=>{const o=t.factoryService.newInlineEditor;return new v.NotebookPanel.ContentFactory({editorFactory:o})}},z={activate:function(e,t,o,n,l,a,r){const d=a.load("jupyterlab"),i="notebook-tools",s=new v.NotebookTools({tracker:t,translator:a});return s.title.icon=S.buildIcon,s.title.caption=d.__("Notebook Tools"),s.id=i,M.MessageLoop.installMessageHook(s,((e,t)=>{switch(t.type){case"activate-request":l.save(i,{open:!0});break;case"after-hide":case"close-request":l.remove(i)}return!0})),r&&t.widgetAdded.connect(((e,t)=>{r.register(t).render(s)})),s},provides:v.INotebookTools,id:"@jupyterlab/notebook-extension:tools",description:"Provides the notebook tools.",autoStart:!0,requires:[v.INotebookTracker,r.IEditorServices,i.IEditorLanguageRegistry,x.IStateDB,y.ITranslator],optional:[h.IPropertyInspectorProvider]},Y={id:"@jupyterlab/notebook-extension:mode-status",description:"Adds a notebook mode status widget.",autoStart:!0,requires:[v.INotebookTracker,y.ITranslator],optional:[_.IStatusBar],activate:(e,t,o,n)=>{if(!n)return;const{shell:l}=e,a=new v.CommandEditStatus(o);t.currentChanged.connect((()=>{const e=t.currentWidget;a.model.notebook=e&&e.content})),n.registerStatusItem("@jupyterlab/notebook-extension:mode-status",{priority:1,item:a,align:"right",rank:4,isActive:()=>!!l.currentWidget&&!!t.currentWidget&&l.currentWidget===t.currentWidget})}},G={id:"@jupyterlab/notebook-extension:execution-indicator",description:"Adds a notebook execution status widget.",autoStart:!0,requires:[v.INotebookTracker,n.ILabShell,y.ITranslator],optional:[_.IStatusBar,w.ISettingRegistry],activate:(e,t,o,n,l,a)=>{let r,d,i;const s=e=>{var a,s;let{showOnToolBar:c,showProgress:u}=e;if(c)i&&(o.currentChanged.disconnect(d),i.dispose());else{if(!l)return;(null==r?void 0:r.model)||(r=new v.ExecutionIndicator(n),d=(e,o)=>{const{newValue:n}=o;if(n&&t.has(n)){const e=n;r.model.attachNotebook({content:e.content,context:e.sessionContext})}},i=l.registerStatusItem("@jupyterlab/notebook-extension:execution-indicator",{item:r,align:"left",rank:3,isActive:()=>{const e=o.currentWidget;return!!e&&t.has(e)}}),r.model.attachNotebook({content:null===(a=t.currentWidget)||void 0===a?void 0:a.content,context:null===(s=t.currentWidget)||void 0===s?void 0:s.sessionContext}),o.currentChanged.connect(d),r.disposed.connect((()=>{o.currentChanged.disconnect(d)}))),r.model.displayOption={showOnToolBar:c,showProgress:u}}};if(a){const t=a.load($.id);Promise.all([t,e.restored]).then((([e])=>{s(v.ExecutionIndicator.getSettingValue(e)),e.changed.connect((e=>s(v.ExecutionIndicator.getSettingValue(e))))})).catch((e=>{console.error(e.message)}))}}},X={id:"@jupyterlab/notebook-extension:export",description:"Adds the export notebook commands.",autoStart:!0,requires:[y.ITranslator,v.INotebookTracker],optional:[C.IMainMenu,l.ICommandPalette],activate:(e,t,o,n,l)=>{var a;const r=t.load("jupyterlab"),{commands:i,shell:s}=e,c=e.serviceManager;let u;i.addCommand(D.exportToFormat,{label:e=>{if(void 0===e.label)return r.__("Save and Export Notebook to the given `format`.");const t=e.label;return e.isPalette?r.__("Save and Export Notebook: %1",t):t},execute:e=>{const t=me(o,s,e);if(!t)return;const n=d.PageConfig.getNBConvertURL({format:e.format,download:!0,path:t.context.path}),{context:l}=t;return l.model.dirty&&!l.model.readOnly?l.save().then((()=>{window.open(n,"_blank","noopener")})):new Promise((e=>{window.open(n,"_blank","noopener"),e(void 0)}))},isEnabled:()=>ge.isEnabled(s,o)}),n&&(u=null===(a=n.fileMenu.items.find((e=>{var t;return"submenu"===e.type&&"jp-mainmenu-file-notebookexport"===(null===(t=e.submenu)||void 0===t?void 0:t.id)})))||void 0===a?void 0:a.submenu);let b=!1;const m=async()=>{if(b)return;o.widgetAdded.disconnect(m),b=!0;const e=await c.nbconvert.getExportFormats(!1);if(!e)return;const n=ge.getFormatLabels(t);Object.keys(e).forEach((function(e){const t=r.__(e[0].toUpperCase()+e.substr(1)),o=n[e]?n[e]:t;let a={format:e,label:o,isPalette:!1};if(-1===q.indexOf(e)&&(u&&u.addItem({command:D.exportToFormat,args:a}),l)){a={format:e,label:o,isPalette:!0};const t=r.__("Notebook Operations");l.addItem({command:D.exportToFormat,category:t,args:a})}}))};o.widgetAdded.connect(m)}},Q={id:"@jupyterlab/notebook-extension:trust-status",description:"Adds the notebook trusted status widget.",autoStart:!0,requires:[v.INotebookTracker,y.ITranslator],optional:[_.IStatusBar],activate:(e,t,o,n)=>{if(!n)return;const{shell:l}=e,a=new v.NotebookTrustStatus(o);t.currentChanged.connect((()=>{const e=t.currentWidget;a.model.notebook=e&&e.content})),n.registerStatusItem("@jupyterlab/notebook-extension:trust-status",{item:a,align:"right",rank:3,isActive:()=>!!l.currentWidget&&!!t.currentWidget&&l.currentWidget===t.currentWidget})}},Z={id:"@jupyterlab/notebook-extension:widget-factory",description:"Provides the notebook widget factory.",provides:v.INotebookWidgetFactory,requires:[v.NotebookPanel.IContentFactory,r.IEditorServices,f.IRenderMimeRegistry,l.IToolbarWidgetRegistry],optional:[w.ISettingRegistry,l.ISessionContextDialogs,y.ITranslator],activate:function(e,t,o,n,a,r,i,s){const c=null!=s?s:y.nullTranslator,b=null!=i?i:new l.SessionContextDialogs({translator:c}),m=d.PageConfig.getOption("notebookStartsKernel"),p=""===m||"true"===m.toLowerCase(),{commands:g}=e;let C;a.addFactory(U,"save",(e=>u.ToolbarItems.createSaveButton(g,e.context.fileChanged))),a.addFactory(U,"cellType",(e=>v.ToolbarItems.createCellTypeItem(e,c))),a.addFactory(U,"kernelName",(e=>l.Toolbar.createKernelNameItem(e.sessionContext,b,c))),a.addFactory(U,"executionProgress",(e=>{const t=null==r?void 0:r.load($.id),o=v.ExecutionIndicator.createExecutionIndicatorItem(e,c,t);return null==t||t.then((t=>{e.disposed.connect((()=>{t.dispose()}))})),o})),r&&(C=(0,l.createToolbarFactory)(a,r,U,"@jupyterlab/notebook-extension:panel",c));const k=c.load("jupyterlab"),h=new v.NotebookWidgetFactory({name:U,label:k.__("Notebook"),fileTypes:["notebook"],modelName:"notebook",defaultFor:["notebook"],preferKernel:p,canStartKernel:!0,rendermime:n,contentFactory:t,editorConfig:v.StaticNotebook.defaultEditorConfig,notebookConfig:v.StaticNotebook.defaultNotebookConfig,mimeTypeService:o.mimeTypeService,toolbarFactory:C,translator:c});return e.docRegistry.addWidgetFactory(h),h},autoStart:!0},ee={id:"@jupyterlab/notebook-extension:cloned-outputs",description:"Adds the clone output feature.",requires:[c.IDocumentManager,v.INotebookTracker,y.ITranslator],optional:[n.ILayoutRestorer],activate:function(e,t,o,n,a){const r=n.load("jupyterlab"),d=new l.WidgetTracker({namespace:"cloned-outputs"});a&&a.restore(d,{command:D.createOutputView,args:e=>({path:e.content.path,index:e.content.index}),name:e=>`${e.content.path}:${e.content.index}`,when:o.restored});const{commands:i,shell:s}=e;i.addCommand(D.createOutputView,{label:r.__("Create New View for Cell Output"),execute:async e=>{var a;let r,i;const s=e.path;let c=e.index;if(s&&null!=c){if(i=t.findWidget(s,U),!i)return}else{if(i=o.currentWidget,!i)return;r=i.content.activeCell,c=i.content.activeCellIndex}const u=new ge.ClonedOutputArea({notebook:i,cell:r,index:c,translator:n}),b=new l.MainAreaWidget({content:u});i.context.addSibling(b,{ref:i.id,mode:"split-bottom",type:"Cloned Output"});const m=()=>{d.save(b)};i.context.pathChanged.connect(m),null===(a=i.context.model)||void 0===a||a.cells.changed.connect(m),d.add(b),i.content.disposed.connect((()=>{var e;i.context.pathChanged.disconnect(m),null===(e=i.context.model)||void 0===e||e.cells.changed.disconnect(m),b.dispose()}))},isEnabled:()=>ge.isEnabledAndSingleSelected(s,o)})},autoStart:!0},te={id:"@jupyterlab/notebook-extension:code-console",description:"Adds the notebook code consoles features.",requires:[v.INotebookTracker,y.ITranslator],activate:function(e,t,o){const n=o.load("jupyterlab"),{commands:l,shell:a}=e,r=()=>ge.isEnabled(a,t);l.addCommand(D.createConsole,{label:n.__("New Console for Notebook"),execute:e=>{const o=t.currentWidget;if(o)return ge.createConsole(l,o,e.activate)},isEnabled:r}),l.addCommand(D.runInConsole,{label:n.__("Run Selected Text or Current Line in Console"),execute:async e=>{var o,n;const a=t.currentWidget;if(!a)return;const{context:r,content:d}=a,i=d.activeCell,s=null==i?void 0:i.model.metadata,c=r.path;if(!i||"code"!==i.model.type)return;let u;const b=i.editor;if(!b)return;const m=b.getSelection(),{start:p,end:g}=m;if(p.column!==g.column||p.line!==g.line){const e=b.getOffsetAt(m.start),t=b.getOffsetAt(m.end);u=b.model.sharedModel.getSource().substring(e,t)}else{const e=b.getCursorPosition(),t=b.model.sharedModel.getSource().split("\n");let l=m.start.line;for(;l<b.lineCount&&!t[l].replace(/\s/g,"").length;)l+=1;let r=l>0,d=0,i=d+1;for(;;){u=t.slice(d,i).join("\n");const s=await(null===(n=null===(o=a.context.sessionContext.session)||void 0===o?void 0:o.kernel)||void 0===n?void 0:n.requestIsComplete({code:u+"\n\n"}));if("complete"===(null==s?void 0:s.content.status)){if(l<i){for(;i<b.lineCount&&!t[i].replace(/\s/g,"").length;)i+=1;b.setCursorPosition({line:i,column:e.column});break}d=i,i=d+1}else if(i<b.lineCount)i+=1;else{if(!r){for(u=t[l];l+1<b.lineCount&&!t[l+1].replace(/\s/g,"").length;)l+=1;b.setCursorPosition({line:l+1,column:e.column});break}d=l,i=l+1,r=!1}}}u&&(await l.execute("console:open",{activate:!1,insertMode:"split-bottom",path:c}),await l.execute("console:inject",{activate:!1,code:u,path:c,metadata:s}))},isEnabled:r})},autoStart:!0},oe={id:"@jupyterlab/notebook-extension:copy-output",description:"Adds the copy cell outputs feature.",activate:function(e,t,o){const n=t.load("jupyterlab");e.commands.addCommand(D.copyToClipboard,{label:n.__("Copy Output to Clipboard"),execute:e=>{var t;const n=null===(t=o.currentWidget)||void 0===t?void 0:t.content.activeCell;if(null==n)return;const l=n.outputArea.outputTracker.currentWidget;if(null==l)return;const a=l.node.getElementsByClassName("jp-OutputArea-output");a.length>0&&function(e){const t=window.getSelection();if(null==t)return;const o=[];for(let e=0;e<t.rangeCount;++e)o[e]=t.getRangeAt(e).cloneRange();const n=document.createRange();n.selectNodeContents(e),t.removeAllRanges(),t.addRange(n),document.execCommand("copy"),t.removeAllRanges(),o.forEach((e=>t.addRange(e)))}(a[0])}}),e.contextMenu.addItem({command:D.copyToClipboard,selector:".jp-OutputArea-child",rank:0})},requires:[y.ITranslator,v.INotebookTracker],autoStart:!0},ne={id:"@jupyterlab/notebook-extension:kernel-status",description:"Adds the notebook kernel status.",activate:(e,t,o)=>{o.addSessionProvider((e=>e&&t.has(e)?e.sessionContext:null))},requires:[v.INotebookTracker,l.IKernelStatusModel],autoStart:!0},le={id:"@jupyterlab/notebook-extension:cursor-position",description:"Adds the notebook cursor position status.",activate:(e,t,o)=>{let n=null;o.addEditorProvider((async e=>{let l=null;if(e!==n){if(null==n||n.content.activeCellChanged.disconnect(o.update),n=null,e&&t.has(e)){e.content.activeCellChanged.connect(o.update);const t=e.content.activeCell;l=null,t&&(await t.ready,l=t.editor),n=e}}else if(e){const t=e.content.activeCell;l=null,t&&(await t.ready,l=t.editor)}return l}))},requires:[v.INotebookTracker,r.IPositionModel],autoStart:!0},ae={id:"@jupyterlab/notebook-extension:completer",description:"Adds the code completion capability to notebooks.",requires:[v.INotebookTracker],optional:[s.ICompletionProviderManager,y.ITranslator,l.ISanitizer],activate:function(e,t,o,n,a){if(!o)return;const r=(null!=n?n:y.nullTranslator).load("jupyterlab"),d=null!=a?a:new l.Sanitizer;e.commands.addCommand(D.invokeCompleter,{label:r.__("Display the completion helper."),execute:e=>{var n;const l=t.currentWidget;l&&"code"===(null===(n=l.content.activeCell)||void 0===n?void 0:n.model.type)&&o.invoke(l.id)}}),e.commands.addCommand(D.selectCompleter,{label:r.__("Select the completion suggestion."),execute:()=>{const e=t.currentWidget&&t.currentWidget.id;if(e)return o.select(e)}}),e.commands.addKeyBinding({command:D.selectCompleter,keys:["Enter"],selector:".jp-Notebook .jp-mod-completer-active"});const i=async(e,t)=>{var n,l;const a={editor:null!==(l=null===(n=t.content.activeCell)||void 0===n?void 0:n.editor)&&void 0!==l?l:null,session:t.sessionContext.session,widget:t,sanitizer:d};await o.updateCompleter(a),t.content.activeCellChanged.connect(((e,n)=>{null==n||n.ready.then((()=>{const e={editor:n.editor,session:t.sessionContext.session,widget:t,sanitizer:d};return o.updateCompleter(e)})).catch(console.error)})),t.sessionContext.sessionChanged.connect((()=>{var e;null===(e=t.content.activeCell)||void 0===e||e.ready.then((()=>{var e,n;const l={editor:null!==(n=null===(e=t.content.activeCell)||void 0===e?void 0:e.editor)&&void 0!==n?n:null,session:t.sessionContext.session,widget:t};return o.updateCompleter(l)})).catch(console.error)}))};t.widgetAdded.connect(i),o.activeProvidersChanged.connect((()=>{t.forEach((e=>{i(0,e).catch((e=>console.error(e)))}))}))},autoStart:!0},re={id:"@jupyterlab/notebook-extension:search",description:"Adds search capability to notebooks.",requires:[b.ISearchProviderRegistry],autoStart:!0,activate:(e,t)=>{t.add("jp-notebookSearchProvider",v.NotebookSearchProvider)}},de={id:"@jupyterlab/notebook-extension:toc",description:"Adds table of content capability to the notebooks",requires:[v.INotebookTracker,A.ITableOfContentsRegistry,l.ISanitizer],optional:[f.IMarkdownParser,w.ISettingRegistry],autoStart:!0,activate:(e,t,o,n,l,a)=>{const r=new v.NotebookToCFactory(t,l,n);o.add(r),a&&Promise.all([e.restored,a.load($.id)]).then((([e,t])=>{const o=()=>{var e;r.scrollToTop=null===(e=t.composite.scrollHeadingToTop)||void 0===e||e};o(),t.changed.connect(o)})).catch((e=>{console.error("Failed to load notebook table of content settings.",e)}))}},ie={id:"@jupyterlab/notebook-extension:language-server",description:"Adds language server capability to the notebooks.",requires:[v.INotebookTracker,g.ILSPDocumentConnectionManager,g.ILSPFeatureManager,g.ILSPCodeExtractorsManager,g.IWidgetLSPAdapterTracker],activate:function(e,t,o,n,l,a){t.widgetAdded.connect((async(e,t)=>{const r=new v.NotebookAdapter(t,{connectionManager:o,featureManager:n,foreignCodeExtractorsManager:l});a.add(r)}))},autoStart:!0},se={id:"@jupyterlab/notebook-extension:update-raw-mimetype",description:"Adds metadata form editor for raw cell mimetype.",autoStart:!0,requires:[v.INotebookTracker,k.IMetadataFormProvider,y.ITranslator],activate:(e,t,o,n)=>{const l=n.load("jupyterlab");let a=!1;t.widgetAdded.connect((async function r(){if(a)return;if(!o.get("commonToolsSection"))return;const d=o.get("commonToolsSection").getProperties("/raw_mimetype");if(!d)return;t.widgetAdded.disconnect(r),a=!0;const i=e.serviceManager,s=await i.nbconvert.getExportFormats(!1);if(!s)return;const c=Object.keys(s),u=ge.getFormatLabels(n);c.forEach((function(e){var t;if(!((null===(t=d.oneOf)||void 0===t?void 0:t.filter((t=>t.const===e)).length)>0)){const t=l.__(e[0].toUpperCase()+e.substr(1)),o=u[e]?u[e]:t,n=s[e].output_mimetype;d.oneOf.push({const:n,title:o})}})),o.get("commonToolsSection").setProperties("/raw_mimetype",d)}))}},ce={id:"@jupyterlab/notebook-extension:metadata-editor",description:"Adds metadata form for full metadata editor.",autoStart:!0,requires:[v.INotebookTracker,r.IEditorServices,S.IFormRendererRegistry],optional:[y.ITranslator],activate:(e,t,o,n,l)=>{const a=e=>o.factoryService.newInlineEditor(e),r={fieldRenderer:e=>new K({editorFactory:a,tracker:t,label:"Cell metadata",translator:l}).render(e)};n.addRenderer("@jupyterlab/notebook-extension:metadata-editor.cell-metadata",r);const d={fieldRenderer:e=>new F({editorFactory:a,tracker:t,label:"Notebook metadata",translator:l}).render(e)};n.addRenderer("@jupyterlab/notebook-extension:metadata-editor.notebook-metadata",d)}},ue={id:"@jupyterlab/notebook-extension:active-cell-tool",description:"Adds active cell field in the metadata editor tab.",autoStart:!0,requires:[v.INotebookTracker,S.IFormRendererRegistry,i.IEditorLanguageRegistry],activate:(e,t,o,n)=>{const l={fieldRenderer:e=>new L({tracker:t,languages:n}).render(e)};o.addRenderer("@jupyterlab/notebook-extension:active-cell-tool.renderer",l)}},be=[J,$,G,X,z,Y,Q,Z,B,ee,te,oe,ne,le,ae,re,de,ie,se,ce,ue];function me(e,t,o){const n=e.currentWidget;return!1!==o.activate&&n&&t.activateById(n.id),n}function pe(e,t,o,n,r,d){const i=o.load("jupyterlab"),{commands:s,shell:c}=e,u=()=>ge.isEnabledAndSingleSelected(c,t);t.currentChanged.connect(((e,t)=>{var o,n;(null===(n=null===(o=null==t?void 0:t.content)||void 0===o?void 0:o.model)||void 0===n?void 0:n.cells)&&(t.content.model.cells.changed.connect(((e,o)=>{(e=>{var t,o;for(const n of e.widgets)n instanceof a.MarkdownCell&&n.headingCollapsed&&v.NotebookActions.setHeadingCollapse(n,!0,e),n.model.id===(null===(o=null===(t=e.activeCell)||void 0===t?void 0:t.model)||void 0===o?void 0:o.id)&&v.NotebookActions.expandParent(n,e)})(t.content)})),t.content.activeCellChanged.connect(((e,t)=>{v.NotebookActions.expandParent(t,e)})))})),t.selectionChanged.connect((()=>{s.notifyCommandChanged(D.duplicateBelow),s.notifyCommandChanged(D.deleteCell),s.notifyCommandChanged(D.copy),s.notifyCommandChanged(D.cut),s.notifyCommandChanged(D.pasteBelow),s.notifyCommandChanged(D.pasteAbove),s.notifyCommandChanged(D.pasteAndReplace),s.notifyCommandChanged(D.moveUp),s.notifyCommandChanged(D.moveDown),s.notifyCommandChanged(D.run),s.notifyCommandChanged(D.runAll),s.notifyCommandChanged(D.runAndAdvance),s.notifyCommandChanged(D.runAndInsert)})),t.activeCellChanged.connect((()=>{s.notifyCommandChanged(D.moveUp),s.notifyCommandChanged(D.moveDown)})),s.addCommand(D.runAndAdvance,{label:e=>{var o;const n=me(t,c,{...e,activate:!1});return i._n("Run Selected Cell","Run Selected Cells",null!==(o=null==n?void 0:n.content.selectedCells.length)&&void 0!==o?o:1)},caption:e=>{var o;const n=me(t,c,{...e,activate:!1});return i._n("Run this cell and advance","Run these %1 cells and advance",null!==(o=null==n?void 0:n.content.selectedCells.length)&&void 0!==o?o:1)},execute:e=>{const l=me(t,c,e);if(l){const{context:e,content:t}=l;return v.NotebookActions.runAndAdvance(t,e.sessionContext,n,o)}},isEnabled:e=>!!e.toolbar||d(),icon:e=>e.toolbar?S.runIcon:void 0}),s.addCommand(D.run,{label:e=>{var o;const n=me(t,c,{...e,activate:!1});return i._n("Run Selected Cell and Do not Advance","Run Selected Cells and Do not Advance",null!==(o=null==n?void 0:n.content.selectedCells.length)&&void 0!==o?o:1)},execute:e=>{const l=me(t,c,e);if(l){const{context:e,content:t}=l;return v.NotebookActions.run(t,e.sessionContext,n,o)}},isEnabled:d}),s.addCommand(D.runAndInsert,{label:e=>{var o;const n=me(t,c,{...e,activate:!1});return i._n("Run Selected Cell and Insert Below","Run Selected Cells and Insert Below",null!==(o=null==n?void 0:n.content.selectedCells.length)&&void 0!==o?o:1)},execute:e=>{const l=me(t,c,e);if(l){const{context:e,content:t}=l;return v.NotebookActions.runAndInsert(t,e.sessionContext,n,o)}},isEnabled:d}),s.addCommand(D.runAll,{label:i.__("Run All Cells"),caption:i.__("Run all cells"),execute:e=>{const l=me(t,c,e);if(l){const{context:e,content:t}=l;return v.NotebookActions.runAll(t,e.sessionContext,n,o)}},isEnabled:d}),s.addCommand(D.runAllAbove,{label:i.__("Run All Above Selected Cell"),execute:e=>{const l=me(t,c,e);if(l){const{context:e,content:t}=l;return v.NotebookActions.runAllAbove(t,e.sessionContext,n,o)}},isEnabled:()=>u()&&0!==t.currentWidget.content.activeCellIndex}),s.addCommand(D.runAllBelow,{label:i.__("Run Selected Cell and All Below"),execute:e=>{const l=me(t,c,e);if(l){const{context:e,content:t}=l;return v.NotebookActions.runAllBelow(t,e.sessionContext,n,o)}},isEnabled:()=>u()&&t.currentWidget.content.activeCellIndex!==t.currentWidget.content.widgets.length-1}),s.addCommand(D.renderAllMarkdown,{label:i.__("Render All Markdown Cells"),execute:e=>{const o=me(t,c,e);if(o){const{content:e}=o;return v.NotebookActions.renderAllMarkdown(e)}},isEnabled:d}),s.addCommand(D.restart,{label:i.__("Restart Kernel…"),caption:i.__("Restart the kernel"),execute:e=>{const o=me(t,c,e);if(o)return n.restart(o.sessionContext)},isEnabled:e=>!!e.toolbar||d(),icon:e=>e.toolbar?S.refreshIcon:void 0}),s.addCommand(D.shutdown,{label:i.__("Shut Down Kernel"),execute:e=>{const o=me(t,c,e);if(o)return o.context.sessionContext.shutdown()},isEnabled:d}),s.addCommand(D.closeAndShutdown,{label:i.__("Close and Shut Down Notebook"),execute:e=>{const o=me(t,c,e);if(!o)return;const n=o.title.label;return(0,l.showDialog)({title:i.__("Shut down the notebook?"),body:i.__('Are you sure you want to close "%1"?',n),buttons:[l.Dialog.cancelButton(),l.Dialog.warnButton()]}).then((e=>{if(e.button.accept)return s.execute(D.shutdown,{activate:!1}).then((()=>{o.dispose()}))}))},isEnabled:d}),s.addCommand(D.trust,{label:()=>i.__("Trust Notebook"),execute:e=>{const o=me(t,c,e);if(o){const{context:e,content:t}=o;return v.NotebookActions.trust(t).then((()=>e.save()))}},isEnabled:d}),s.addCommand(D.restartClear,{label:i.__("Restart Kernel and Clear Outputs of All Cells…"),caption:i.__("Restart the kernel and clear all outputs of all cells"),execute:async()=>{await s.execute(D.restart,{activate:!1})&&await s.execute(D.clearAllOutputs)},isEnabled:d}),s.addCommand(D.restartAndRunToSelected,{label:i.__("Restart Kernel and Run up to Selected Cell…"),execute:async e=>{const l=me(t,c,{activate:!1,...e});if(!l)return;const{context:a,content:r}=l,d=r.widgets.slice(0,r.activeCellIndex+1);return await n.restart(l.sessionContext)?v.NotebookActions.runCells(r,d,a.sessionContext,n,o):void 0},isEnabled:u}),s.addCommand(D.restartRunAll,{label:i.__("Restart Kernel and Run All Cells…"),caption:i.__("Restart the kernel and run all cells"),execute:async e=>{const l=me(t,c,{activate:!1,...e});if(!l)return;const{context:a,content:r}=l,d=r.widgets;return await n.restart(l.sessionContext)?v.NotebookActions.runCells(r,d,a.sessionContext,n,o):void 0},isEnabled:e=>!!e.toolbar||d(),icon:e=>e.toolbar?S.fastForwardIcon:void 0}),s.addCommand(D.clearAllOutputs,{label:i.__("Clear Outputs of All Cells"),caption:i.__("Clear all outputs of all cells"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.clearAllOutputs(o.content)},isEnabled:d}),s.addCommand(D.clearOutputs,{label:i.__("Clear Cell Output"),caption:i.__("Clear outputs for the selected cells"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.clearOutputs(o.content)},isEnabled:d}),s.addCommand(D.interrupt,{label:i.__("Interrupt Kernel"),caption:i.__("Interrupt the kernel"),execute:e=>{var o;const n=me(t,c,e);if(!n)return;const l=null===(o=n.context.sessionContext.session)||void 0===o?void 0:o.kernel;return l?l.interrupt():void 0},isEnabled:e=>!!e.toolbar||d(),icon:e=>e.toolbar?S.stopIcon:void 0}),s.addCommand(D.toCode,{label:i.__("Change to Code Cell Type"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.changeCellType(o.content,"code")},isEnabled:d}),s.addCommand(D.toMarkdown,{label:i.__("Change to Markdown Cell Type"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.changeCellType(o.content,"markdown")},isEnabled:d}),s.addCommand(D.toRaw,{label:i.__("Change to Raw Cell Type"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.changeCellType(o.content,"raw")},isEnabled:d}),s.addCommand(D.cut,{label:e=>{var o;const n=me(t,c,{...e,activate:!1});return i._n("Cut Cell","Cut Cells",null!==(o=null==n?void 0:n.content.selectedCells.length)&&void 0!==o?o:1)},caption:e=>{var o;const n=me(t,c,{...e,activate:!1});return i._n("Cut this cell","Cut these %1 cells",null!==(o=null==n?void 0:n.content.selectedCells.length)&&void 0!==o?o:1)},execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.cut(o.content)},icon:e=>e.toolbar?S.cutIcon:void 0,isEnabled:e=>!!e.toolbar||d()}),s.addCommand(D.copy,{label:e=>{var o;const n=me(t,c,{...e,activate:!1});return i._n("Copy Cell","Copy Cells",null!==(o=null==n?void 0:n.content.selectedCells.length)&&void 0!==o?o:1)},caption:e=>{var o;const n=me(t,c,{...e,activate:!1});return i._n("Copy this cell","Copy these %1 cells",null!==(o=null==n?void 0:n.content.selectedCells.length)&&void 0!==o?o:1)},execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.copy(o.content)},icon:e=>e.toolbar?S.copyIcon:void 0,isEnabled:e=>!!e.toolbar||d()}),s.addCommand(D.pasteBelow,{label:e=>{var o;const n=me(t,c,{...e,activate:!1});return i._n("Paste Cell Below","Paste Cells Below",null!==(o=null==n?void 0:n.content.selectedCells.length)&&void 0!==o?o:1)},caption:e=>{var o;const n=me(t,c,{...e,activate:!1});return i._n("Paste this cell from the clipboard","Paste these %1 cells from the clipboard",null!==(o=null==n?void 0:n.content.selectedCells.length)&&void 0!==o?o:1)},execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.paste(o.content,"below")},icon:e=>e.toolbar?S.pasteIcon:void 0,isEnabled:e=>!!e.toolbar||d()}),s.addCommand(D.pasteAbove,{label:e=>{var o;const n=me(t,c,{...e,activate:!1});return i._n("Paste Cell Above","Paste Cells Above",null!==(o=null==n?void 0:n.content.selectedCells.length)&&void 0!==o?o:1)},caption:e=>{var o;const n=me(t,c,{...e,activate:!1});return i._n("Paste this cell from the clipboard","Paste these %1 cells from the clipboard",null!==(o=null==n?void 0:n.content.selectedCells.length)&&void 0!==o?o:1)},execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.paste(o.content,"above")},isEnabled:d}),s.addCommand(D.duplicateBelow,{label:e=>{var o;const n=me(t,c,{...e,activate:!1});return i._n("Duplicate Cell Below","Duplicate Cells Below",null!==(o=null==n?void 0:n.content.selectedCells.length)&&void 0!==o?o:1)},caption:e=>{var o;const n=me(t,c,{...e,activate:!1});return i._n("Create a duplicate of this cell below","Create duplicates of %1 cells below",null!==(o=null==n?void 0:n.content.selectedCells.length)&&void 0!==o?o:1)},execute:e=>{const o=me(t,c,e);o&&v.NotebookActions.duplicate(o.content,"belowSelected")},icon:e=>e.toolbar?S.duplicateIcon:void 0,isEnabled:e=>!!e.toolbar||d()}),s.addCommand(D.pasteAndReplace,{label:e=>{var o;const n=me(t,c,{...e,activate:!1});return i._n("Paste Cell and Replace","Paste Cells and Replace",null!==(o=null==n?void 0:n.content.selectedCells.length)&&void 0!==o?o:1)},execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.paste(o.content,"replace")},isEnabled:d}),s.addCommand(D.deleteCell,{label:e=>{var o;const n=me(t,c,{...e,activate:!1});return i._n("Delete Cell","Delete Cells",null!==(o=null==n?void 0:n.content.selectedCells.length)&&void 0!==o?o:1)},caption:e=>{var o;const n=me(t,c,{...e,activate:!1});return i._n("Delete this cell","Delete these %1 cells",null!==(o=null==n?void 0:n.content.selectedCells.length)&&void 0!==o?o:1)},execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.deleteCells(o.content)},isEnabled:e=>!!e.toolbar||d()}),s.addCommand(D.split,{label:i.__("Split Cell"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.splitCell(o.content)},isEnabled:d}),s.addCommand(D.merge,{label:i.__("Merge Selected Cells"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.mergeCells(o.content)},isEnabled:d}),s.addCommand(D.mergeAbove,{label:i.__("Merge Cell Above"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.mergeCells(o.content,!0)},isEnabled:d}),s.addCommand(D.mergeBelow,{label:i.__("Merge Cell Below"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.mergeCells(o.content,!1)},isEnabled:d}),s.addCommand(D.insertAbove,{label:i.__("Insert Cell Above"),caption:i.__("Insert a cell above"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.insertAbove(o.content)},icon:e=>e.toolbar?S.addAboveIcon:void 0,isEnabled:e=>!!e.toolbar||d()}),s.addCommand(D.insertBelow,{label:i.__("Insert Cell Below"),caption:i.__("Insert a cell below"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.insertBelow(o.content)},icon:e=>e.toolbar?S.addBelowIcon:void 0,isEnabled:e=>!!e.toolbar||d()}),s.addCommand(D.selectAbove,{label:i.__("Select Cell Above"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.selectAbove(o.content)},isEnabled:d}),s.addCommand(D.selectBelow,{label:i.__("Select Cell Below"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.selectBelow(o.content)},isEnabled:d}),s.addCommand(D.insertHeadingAbove,{label:i.__("Insert Heading Above Current Heading"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.insertSameLevelHeadingAbove(o.content)},isEnabled:d}),s.addCommand(D.insertHeadingBelow,{label:i.__("Insert Heading Below Current Heading"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.insertSameLevelHeadingBelow(o.content)},isEnabled:d}),s.addCommand(D.selectHeadingAboveOrCollapse,{label:i.__("Select Heading Above or Collapse Heading"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.selectHeadingAboveOrCollapseHeading(o.content)},isEnabled:d}),s.addCommand(D.selectHeadingBelowOrExpand,{label:i.__("Select Heading Below or Expand Heading"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.selectHeadingBelowOrExpandHeading(o.content)},isEnabled:d}),s.addCommand(D.extendAbove,{label:i.__("Extend Selection Above"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.extendSelectionAbove(o.content)},isEnabled:d}),s.addCommand(D.extendTop,{label:i.__("Extend Selection to Top"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.extendSelectionAbove(o.content,!0)},isEnabled:d}),s.addCommand(D.extendBelow,{label:i.__("Extend Selection Below"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.extendSelectionBelow(o.content)},isEnabled:d}),s.addCommand(D.extendBottom,{label:i.__("Extend Selection to Bottom"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.extendSelectionBelow(o.content,!0)},isEnabled:d}),s.addCommand(D.selectAll,{label:i.__("Select All Cells"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.selectAll(o.content)},isEnabled:d}),s.addCommand(D.deselectAll,{label:i.__("Deselect All Cells"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.deselectAll(o.content)},isEnabled:d}),s.addCommand(D.moveUp,{label:e=>{var o;const n=me(t,c,{...e,activate:!1});return i._n("Move Cell Up","Move Cells Up",null!==(o=null==n?void 0:n.content.selectedCells.length)&&void 0!==o?o:1)},caption:e=>{var o;const n=me(t,c,{...e,activate:!1});return i._n("Move this cell up","Move these %1 cells up",null!==(o=null==n?void 0:n.content.selectedCells.length)&&void 0!==o?o:1)},execute:e=>{const o=me(t,c,e);o&&(v.NotebookActions.moveUp(o.content),ge.raiseSilentNotification(i.__("Notebook cell shifted up successfully"),o.node))},isEnabled:e=>{const o=me(t,c,{...e,activate:!1});return!!o&&o.content.activeCellIndex>=1},icon:e=>e.toolbar?S.moveUpIcon:void 0}),s.addCommand(D.moveDown,{label:e=>{var o;const n=me(t,c,{...e,activate:!1});return i._n("Move Cell Down","Move Cells Down",null!==(o=null==n?void 0:n.content.selectedCells.length)&&void 0!==o?o:1)},caption:e=>{var o;const n=me(t,c,{...e,activate:!1});return i._n("Move this cell down","Move these %1 cells down",null!==(o=null==n?void 0:n.content.selectedCells.length)&&void 0!==o?o:1)},execute:e=>{const o=me(t,c,e);o&&(v.NotebookActions.moveDown(o.content),ge.raiseSilentNotification(i.__("Notebook cell shifted down successfully"),o.node))},isEnabled:e=>{const o=me(t,c,{...e,activate:!1});if(!o||!o.content.model)return!1;const n=o.content.model.cells.length;return o.content.activeCellIndex<n-1},icon:e=>e.toolbar?S.moveDownIcon:void 0}),s.addCommand(D.toggleAllLines,{label:i.__("Show Line Numbers"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.toggleAllLineNumbers(o.content)},isEnabled:d,isToggled:e=>{const o=me(t,c,{...e,activate:!1});if(o){const e=o.content.editorConfig;return!!(e.code.lineNumbers&&e.markdown.lineNumbers&&e.raw.lineNumbers)}return!1}}),s.addCommand(D.commandMode,{label:i.__("Enter Command Mode"),execute:e=>{const o=me(t,c,e);o&&(o.content.mode="command")},isEnabled:d}),s.addCommand(D.editMode,{label:i.__("Enter Edit Mode"),execute:e=>{const o=me(t,c,e);o&&(o.content.mode="edit")},isEnabled:d}),s.addCommand(D.undoCellAction,{label:i.__("Undo Cell Operation"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.undo(o.content)},isEnabled:d}),s.addCommand(D.redoCellAction,{label:i.__("Redo Cell Operation"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.redo(o.content)},isEnabled:d}),s.addCommand(D.redo,{label:i.__("Redo"),execute:e=>{var o;const n=me(t,c,e);if(n){const e=n.content.activeCell;if(e)return e.inputHidden=!1,null===(o=e.editor)||void 0===o?void 0:o.redo()}}}),s.addCommand(D.undo,{label:i.__("Undo"),execute:e=>{var o;const n=me(t,c,e);if(n){const e=n.content.activeCell;if(e)return e.inputHidden=!1,null===(o=e.editor)||void 0===o?void 0:o.undo()}}}),s.addCommand(D.changeKernel,{label:i.__("Change Kernel…"),execute:e=>{const o=me(t,c,e);if(o)return n.selectKernel(o.context.sessionContext)},isEnabled:d}),s.addCommand(D.getKernel,{label:i.__("Get Kernel"),execute:e=>{var o;const n=me(t,c,{activate:!1,...e});if(n)return null===(o=n.sessionContext.session)||void 0===o?void 0:o.kernel},isEnabled:d}),s.addCommand(D.reconnectToKernel,{label:i.__("Reconnect to Kernel"),execute:e=>{var o;const n=me(t,c,e);if(!n)return;const l=null===(o=n.context.sessionContext.session)||void 0===o?void 0:o.kernel;return l?l.reconnect():void 0},isEnabled:d}),s.addCommand(D.markdown1,{label:i.__("Change to Heading 1"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.setMarkdownHeader(o.content,1)},isEnabled:d}),s.addCommand(D.markdown2,{label:i.__("Change to Heading 2"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.setMarkdownHeader(o.content,2)},isEnabled:d}),s.addCommand(D.markdown3,{label:i.__("Change to Heading 3"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.setMarkdownHeader(o.content,3)},isEnabled:d}),s.addCommand(D.markdown4,{label:i.__("Change to Heading 4"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.setMarkdownHeader(o.content,4)},isEnabled:d}),s.addCommand(D.markdown5,{label:i.__("Change to Heading 5"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.setMarkdownHeader(o.content,5)},isEnabled:d}),s.addCommand(D.markdown6,{label:i.__("Change to Heading 6"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.setMarkdownHeader(o.content,6)},isEnabled:d}),s.addCommand(D.hideCode,{label:i.__("Collapse Selected Code"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.hideCode(o.content)},isEnabled:d}),s.addCommand(D.showCode,{label:i.__("Expand Selected Code"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.showCode(o.content)},isEnabled:d}),s.addCommand(D.hideAllCode,{label:i.__("Collapse All Code"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.hideAllCode(o.content)},isEnabled:d}),s.addCommand(D.showAllCode,{label:i.__("Expand All Code"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.showAllCode(o.content)},isEnabled:d}),s.addCommand(D.hideOutput,{label:i.__("Collapse Selected Outputs"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.hideOutput(o.content)},isEnabled:d}),s.addCommand(D.showOutput,{label:i.__("Expand Selected Outputs"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.showOutput(o.content)},isEnabled:d}),s.addCommand(D.hideAllOutputs,{label:i.__("Collapse All Outputs"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.hideAllOutputs(o.content)},isEnabled:d}),s.addCommand(D.toggleRenderSideBySideCurrentNotebook,{label:i.__("Render Side-by-Side"),execute:e=>{const o=me(t,c,e);if(o)return"side-by-side"===o.content.renderingLayout?v.NotebookActions.renderDefault(o.content):v.NotebookActions.renderSideBySide(o.content)},isEnabled:d,isToggled:e=>{const o=me(t,c,{...e,activate:!1});return!!o&&"side-by-side"===o.content.renderingLayout}}),s.addCommand(D.showAllOutputs,{label:i.__("Expand All Outputs"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.showAllOutputs(o.content)},isEnabled:d}),s.addCommand(D.enableOutputScrolling,{label:i.__("Enable Scrolling for Outputs"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.enableOutputScrolling(o.content)},isEnabled:d}),s.addCommand(D.disableOutputScrolling,{label:i.__("Disable Scrolling for Outputs"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.disableOutputScrolling(o.content)},isEnabled:d}),s.addCommand(D.selectLastRunCell,{label:i.__("Select current running or last run cell"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.selectLastRunCell(o.content)},isEnabled:d}),s.addCommand(D.replaceSelection,{label:i.__("Replace Selection in Notebook Cell"),execute:e=>{const o=me(t,c,e),n=e.text||"";if(o)return v.NotebookActions.replaceSelection(o.content,n)},isEnabled:d}),s.addCommand(D.toggleCollapseCmd,{label:i.__("Toggle Collapse Notebook Heading"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.toggleCurrentHeadingCollapse(o.content)},isEnabled:()=>ge.isEnabledAndHeadingSelected(c,t)}),s.addCommand(D.collapseAllCmd,{label:i.__("Collapse All Headings"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.collapseAllHeadings(o.content)}}),s.addCommand(D.expandAllCmd,{label:i.__("Expand All Headings"),execute:e=>{const o=me(t,c,e);if(o)return v.NotebookActions.expandAllHeadings(o.content)}}),s.addCommand(D.tocRunCells,{label:i.__("Select and Run Cell(s) for this Heading"),execute:e=>{const l=me(t,c,{activate:!1,...e});if(null===l)return;const r=l.content.activeCell;let d=l.content.activeCellIndex;if(r instanceof a.MarkdownCell){const e=l.content.widgets,t=r.headingInfo.level;for(let o=l.content.activeCellIndex+1;o<e.length;o++){const n=e[o];if(n instanceof a.MarkdownCell&&n.headingInfo.level>=0&&n.headingInfo.level<=t)break;d=o}}l.content.extendContiguousSelectionTo(d),v.NotebookActions.run(l.content,l.sessionContext,n,o)}}),s.addCommand(D.accessPreviousHistory,{label:i.__("Access Previous Kernel History Entry"),execute:async e=>{const o=me(t,c,e);if(o)return await v.NotebookActions.accessPreviousHistory(o.content)}}),s.addCommand(D.accessNextHistory,{label:i.__("Access Next Kernel History Entry"),execute:async e=>{const o=me(t,c,e);if(o)return await v.NotebookActions.accessNextHistory(o.content)}}),s.addCommand(D.virtualScrollbar,{label:i.__("Virtual Scrollbar"),caption:i.__("Toggle virtual scrollbar (enabled with windowing mode: full)"),execute:e=>{const o=me(t,c,e);o&&(o.content.scrollbar=!o.content.scrollbar)},icon:e=>e.toolbar?S.tableRowsIcon:void 0,isEnabled:e=>{var t;return(!!e.toolbar||d())&&null!==(t="full"===(null==r?void 0:r.composite.windowingMode))&&void 0!==t&&t},isVisible:e=>{var t;return(!!e.toolbar||d())&&null!==(t="full"===(null==r?void 0:r.composite.windowingMode))&&void 0!==t&&t}})}var ge;!function(e){e.createConsole=function(e,t,o){const n={path:t.context.path,preferredLanguage:t.context.model.defaultKernelLanguage,activate:o,ref:t.id,insertMode:"split-bottom",type:"Linked Console"};return e.execute("console:create",n)},e.isEnabled=function(e,t){return null!==t.currentWidget&&t.currentWidget===e.currentWidget},e.isEnabledAndSingleSelected=function(t,o){if(!e.isEnabled(t,o))return!1;const{content:n}=o.currentWidget,l=n.activeCellIndex;for(let e=0;e<n.widgets.length;++e)if(n.isSelected(n.widgets[e])&&e!==l)return!1;return!0},e.isEnabledAndHeadingSelected=function(t,o){if(!e.isEnabled(t,o))return!1;const{content:n}=o.currentWidget,l=n.activeCellIndex;if(!(n.activeCell instanceof a.MarkdownCell))return!1;for(let e=0;e<n.widgets.length;++e)if(n.isSelected(n.widgets[e])&&e!==l)return!1;return!0},e.getFormatLabels=function(e){const t=(e=e||y.nullTranslator).load("jupyterlab");return{html:t.__("HTML"),latex:t.__("LaTeX"),markdown:t.__("Markdown"),pdf:t.__("PDF"),rst:t.__("ReStructured Text"),script:t.__("Executable Script"),slides:t.__("Reveal.js Slides")}},e.raiseSilentNotification=function(e,t){const o=`sr-message-container-${t.id}`,n=document.getElementById(o)||document.createElement("div");n.getAttribute("id")!==o&&(n.classList.add("sr-only"),n.setAttribute("id",o),n.setAttribute("role","alert"),n.hidden=!0,t.appendChild(n)),n.innerText=e};class t extends T.Panel{constructor(e){super(),this._cell=null;const t=(e.translator||y.nullTranslator).load("jupyterlab");this._notebook=e.notebook,this._index=void 0!==e.index?e.index:-1,this._cell=e.cell||null,this.id=`LinkedOutputView-${N.UUID.uuid4()}`,this.title.label="Output View",this.title.icon=S.notebookIcon,this.title.caption=this._notebook.title.label?t.__("For Notebook: %1",this._notebook.title.label):t.__("For Notebook:"),this.addClass("jp-LinkedOutputView"),this._notebook.context.ready.then((()=>{if(this._cell||(this._cell=this._notebook.content.widgets[this._index]),!this._cell||"code"!==this._cell.model.type)return void this.dispose();const e=this._cell.cloneOutputArea();this.addWidget(e)}))}get index(){return this._cell?E.ArrayExt.findFirstIndex(this._notebook.content.widgets,(e=>e===this._cell)):this._index}get path(){return this._notebook.context.path}}e.ClonedOutputArea=t}(ge||(ge={}))}}]);
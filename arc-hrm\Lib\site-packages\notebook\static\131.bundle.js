(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[131],{70131:(e,t,r)=>{"use strict";r.r(t),r.d(t,{createPrecompiledValidator:()=>w,customizeValidator:()=>$,default:()=>b});var a=r(24885),s=r(20151),o=r.n(s),n=r(58218),i=r.n(n),c=r(11611),d=r.n(c);const l={allErrors:!0,multipleOfPrecision:8,strict:!1,verbose:!0},u=/^(#?([0-9A-Fa-f]{3}){1,2}\b|aqua|black|blue|fuchsia|gray|green|lime|maroon|navy|olive|orange|purple|red|silver|teal|white|yellow|(rgb\(\s*\b([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\b\s*,\s*\b([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\b\s*,\s*\b([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\b\s*\))|(rgb\(\s*(\d?\d%|100%)+\s*,\s*(\d?\d%|100%)+\s*,\s*(\d?\d%|100%)+\s*\)))$/,f=/^data:([a-z]+\/[a-z0-9-+.]+)?;(?:name=(.*);)?base64,(.*)$/;var h=r(99729),m=r.n(h);function p(e,t,r,s,o,n,i){const{validationError:c}=t;let d=function(e=[],t){return e.map((e=>{const{instancePath:r,keyword:s,params:o,schemaPath:n,parentSchema:i,...c}=e;let{message:d=""}=c,l=r.replace(/\//g,"."),u=`${l} ${d}`.trim();if("missingProperty"in o){l=l?`${l}.${o.missingProperty}`:o.missingProperty;const e=o.missingProperty,r=(0,a.getUiOptions)(m()(t,`${l.replace(/^\./,"")}`)).title;if(r)d=d.replace(e,r);else{const t=m()(i,[a.PROPERTIES_KEY,e,"title"]);t&&(d=d.replace(e,t))}u=d}else{const e=(0,a.getUiOptions)(m()(t,`${l.replace(/^\./,"")}`)).title;if(e)u=`'${e}' ${d}`.trim();else{const e=null==i?void 0:i.title;e&&(u=`'${e}' ${d}`.trim())}}return{name:s,property:l,message:d,params:o,stack:u,schemaPath:n}}))}(t.errors,i);c&&(d=[...d,{stack:c.message}]),"function"==typeof n&&(d=n(d,i));let l=(0,a.toErrorSchema)(d);if(c&&(l={...l,$schema:{__errors:[c.message]}}),"function"!=typeof o)return{errors:d,errorSchema:l};const u=(0,a.getDefaultFormState)(e,s,r,s,!0),f=o(u,(0,a.createErrorHandler)(u),i),h=(0,a.unwrapErrorHandler)(f);return(0,a.validationDataMerge)({errors:d,errorSchema:l},h)}class y{constructor(e,t){const{additionalMetaSchemas:r,customFormats:s,ajvOptionsOverrides:n,ajvFormatOptions:c,AjvClass:h}=e;this.ajv=function(e,t,r={},s,n=o()){const c=new n({...l,...r});return s?i()(c,s):!1!==s&&i()(c),c.addFormat("data-url",f),c.addFormat("color",u),c.addKeyword(a.ADDITIONAL_PROPERTY_FLAG),c.addKeyword(a.RJSF_ADDITONAL_PROPERTIES_FLAG),Array.isArray(e)&&c.addMetaSchema(e),d()(t)&&Object.keys(t).forEach((e=>{c.addFormat(e,t[e])})),c}(r,s,n,c,h),this.localizer=t}toErrorList(e,t=[]){return(0,a.toErrorList)(e,t)}rawValidation(e,t){let r,s,o;e[a.ID_KEY]&&(s=this.ajv.getSchema(e[a.ID_KEY]));try{void 0===s&&(s=this.ajv.compile(e)),s(t)}catch(e){r=e}return s&&("function"==typeof this.localizer&&this.localizer(s.errors),o=s.errors||void 0,s.errors=null),{errors:o,validationError:r}}validateFormData(e,t,r,a,s){return p(this,this.rawValidation(t,e),e,t,r,a,s)}isValid(e,t,r){var s,o;const n=null!==(s=r[a.ID_KEY])&&void 0!==s?s:a.ROOT_SCHEMA_PREFIX;try{this.ajv.addSchema(r,n);const s=(0,a.withIdRefPrefix)(e),i=null!==(o=s[a.ID_KEY])&&void 0!==o?o:(0,a.hashForSchema)(s);let c;return c=this.ajv.getSchema(i),void 0===c&&(c=this.ajv.addSchema(s,i).getSchema(i)||this.ajv.compile(s)),c(t)}catch(e){return console.warn("Error encountered compiling schema:",e),!1}finally{this.ajv.removeSchema(n)}}}function $(e={},t){return new y(e,t)}var v=r(56141),g=r.n(v);class _{constructor(e,t,r){this.rootSchema=t,this.validateFns=e,this.localizer=r,this.mainValidator=this.getValidator(t)}getValidator(e){const t=m()(e,a.ID_KEY)||(0,a.hashForSchema)(e),r=this.validateFns[t];if(!r)throw new Error(`No precompiled validator function was found for the given schema for "${t}"`);return r}ensureSameRootSchema(e,t){if(!g()(e,this.rootSchema)){const r=(0,a.retrieveSchema)(this,this.rootSchema,this.rootSchema,t);if(!g()(e,r))throw new Error("The schema associated with the precompiled validator differs from the rootSchema provided for validation")}return!0}toErrorList(e,t=[]){return(0,a.toErrorList)(e,t)}rawValidation(e,t){this.ensureSameRootSchema(e,t),this.mainValidator(t),"function"==typeof this.localizer&&this.localizer(this.mainValidator.errors);const r=this.mainValidator.errors||void 0;return this.mainValidator.errors=null,{errors:r}}validateFormData(e,t,r,a,s){return p(this,this.rawValidation(t,e),e,t,r,a,s)}isValid(e,t,r){return this.ensureSameRootSchema(r,t),m()(e,a.ID_KEY)!==a.JUNK_OPTION_ID&&this.getValidator(e)(t)}}function w(e,t,r){return new _(e,t,r)}const b=$()},67796:(e,t)=>{"use strict";function r(e,t){return{validate:e,compare:t}}Object.defineProperty(t,"__esModule",{value:!0}),t.formatNames=t.fastFormats=t.fullFormats=void 0,t.fullFormats={date:r(o,n),time:r(c,d),"date-time":r((function(e){const t=e.split(l);return 2===t.length&&o(t[0])&&c(t[1],!0)}),u),duration:/^P(?!$)((\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+S)?)?|(\d+W)?)$/,uri:function(e){return f.test(e)&&h.test(e)},"uri-reference":/^(?:[a-z][a-z0-9+\-.]*:)?(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'"()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?(?:\?(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i,"uri-template":/^(?:(?:[^\x00-\x20"'<>%\\^`{|}]|%[0-9a-f]{2})|\{[+#./;?&=,!@|]?(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?(?:,(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?)*\})*$/i,url:/^(?:https?|ftp):\/\/(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z0-9\u{00a1}-\u{ffff}]+-)*[a-z0-9\u{00a1}-\u{ffff}]+)(?:\.(?:[a-z0-9\u{00a1}-\u{ffff}]+-)*[a-z0-9\u{00a1}-\u{ffff}]+)*(?:\.(?:[a-z\u{00a1}-\u{ffff}]{2,})))(?::\d{2,5})?(?:\/[^\s]*)?$/iu,email:/^[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$/i,hostname:/^(?=.{1,253}\.?$)[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[-0-9a-z]{0,61}[0-9a-z])?)*\.?$/i,ipv4:/^(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)$/,ipv6:/^((([0-9a-f]{1,4}:){7}([0-9a-f]{1,4}|:))|(([0-9a-f]{1,4}:){6}(:[0-9a-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){5}(((:[0-9a-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){4}(((:[0-9a-f]{1,4}){1,3})|((:[0-9a-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){3}(((:[0-9a-f]{1,4}){1,4})|((:[0-9a-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){2}(((:[0-9a-f]{1,4}){1,5})|((:[0-9a-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){1}(((:[0-9a-f]{1,4}){1,6})|((:[0-9a-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9a-f]{1,4}){1,7})|((:[0-9a-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))$/i,regex:function(e){if(v.test(e))return!1;try{return new RegExp(e),!0}catch(e){return!1}},uuid:/^(?:urn:uuid:)?[0-9a-f]{8}-(?:[0-9a-f]{4}-){3}[0-9a-f]{12}$/i,"json-pointer":/^(?:\/(?:[^~/]|~0|~1)*)*$/,"json-pointer-uri-fragment":/^#(?:\/(?:[a-z0-9_\-.!$&'()*+,;:=@]|%[0-9a-f]{2}|~0|~1)*)*$/i,"relative-json-pointer":/^(?:0|[1-9][0-9]*)(?:#|(?:\/(?:[^~/]|~0|~1)*)*)$/,byte:function(e){return m.lastIndex=0,m.test(e)},int32:{type:"number",validate:function(e){return Number.isInteger(e)&&e<=y&&e>=p}},int64:{type:"number",validate:function(e){return Number.isInteger(e)}},float:{type:"number",validate:$},double:{type:"number",validate:$},password:!0,binary:!0},t.fastFormats={...t.fullFormats,date:r(/^\d\d\d\d-[0-1]\d-[0-3]\d$/,n),time:r(/^(?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)?$/i,d),"date-time":r(/^\d\d\d\d-[0-1]\d-[0-3]\d[t\s](?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)$/i,u),uri:/^(?:[a-z][a-z0-9+\-.]*:)(?:\/?\/)?[^\s]*$/i,"uri-reference":/^(?:(?:[a-z][a-z0-9+\-.]*:)?\/?\/)?(?:[^\\\s#][^\s#]*)?(?:#[^\\\s]*)?$/i,email:/^[a-z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?)*$/i},t.formatNames=Object.keys(t.fullFormats);const a=/^(\d\d\d\d)-(\d\d)-(\d\d)$/,s=[0,31,28,31,30,31,30,31,31,30,31,30,31];function o(e){const t=a.exec(e);if(!t)return!1;const r=+t[1],o=+t[2],n=+t[3];return o>=1&&o<=12&&n>=1&&n<=(2===o&&function(e){return e%4==0&&(e%100!=0||e%400==0)}(r)?29:s[o])}function n(e,t){if(e&&t)return e>t?1:e<t?-1:0}const i=/^(\d\d):(\d\d):(\d\d)(\.\d+)?(z|[+-]\d\d(?::?\d\d)?)?$/i;function c(e,t){const r=i.exec(e);if(!r)return!1;const a=+r[1],s=+r[2],o=+r[3],n=r[5];return(a<=23&&s<=59&&o<=59||23===a&&59===s&&60===o)&&(!t||""!==n)}function d(e,t){if(!e||!t)return;const r=i.exec(e),a=i.exec(t);return r&&a?(e=r[1]+r[2]+r[3]+(r[4]||""))>(t=a[1]+a[2]+a[3]+(a[4]||""))?1:e<t?-1:0:void 0}const l=/t|\s/i;function u(e,t){if(!e||!t)return;const[r,a]=e.split(l),[s,o]=t.split(l),i=n(r,s);return void 0!==i?i||d(a,o):void 0}const f=/\/|:/,h=/^(?:[a-z][a-z0-9+\-.]*:)(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)(?:\?(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i,m=/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/gm,p=-(2**31),y=2**31-1;function $(){return!0}const v=/[^\\]\\Z/},58218:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(67796),s=r(60891),o=r(17231),n=new o.Name("fullFormats"),i=new o.Name("fastFormats"),c=(e,t={keywords:!0})=>{if(Array.isArray(t))return d(e,t,a.fullFormats,n),e;const[r,o]="fast"===t.mode?[a.fastFormats,i]:[a.fullFormats,n];return d(e,t.formats||a.formatNames,r,o),t.keywords&&s.default(e),e};function d(e,t,r,a){var s,n;null!==(s=(n=e.opts.code).formats)&&void 0!==s||(n.formats=o._`require("ajv-formats/dist/formats").${a}`);for(const a of t)e.addFormat(a,r[a])}c.get=(e,t="full")=>{const r=("fast"===t?a.fastFormats:a.fullFormats)[e];if(!r)throw new Error(`Unknown format "${e}"`);return r},e.exports=t=c,Object.defineProperty(t,"__esModule",{value:!0}),t.default=c},60891:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.formatLimitDefinition=void 0;const a=r(20151),s=r(17231),o=s.operators,n={formatMaximum:{okStr:"<=",ok:o.LTE,fail:o.GT},formatMinimum:{okStr:">=",ok:o.GTE,fail:o.LT},formatExclusiveMaximum:{okStr:"<",ok:o.LT,fail:o.GTE},formatExclusiveMinimum:{okStr:">",ok:o.GT,fail:o.LTE}},i={message:({keyword:e,schemaCode:t})=>s.str`should be ${n[e].okStr} ${t}`,params:({keyword:e,schemaCode:t})=>s._`{comparison: ${n[e].okStr}, limit: ${t}}`};t.formatLimitDefinition={keyword:Object.keys(n),type:"string",schemaType:"string",$data:!0,error:i,code(e){const{gen:t,data:r,schemaCode:o,keyword:i,it:c}=e,{opts:d,self:l}=c;if(!d.validateFormats)return;const u=new a.KeywordCxt(c,l.RULES.all.format.definition,"format");function f(e){return s._`${e}.compare(${r}, ${o}) ${n[i].fail} 0`}u.$data?function(){const r=t.scopeValue("formats",{ref:l.formats,code:d.code.formats}),a=t.const("fmt",s._`${r}[${u.schemaCode}]`);e.fail$data(s.or(s._`typeof ${a} != "object"`,s._`${a} instanceof RegExp`,s._`typeof ${a}.compare != "function"`,f(a)))}():function(){const r=u.schema,a=l.formats[r];if(!a||!0===a)return;if("object"!=typeof a||a instanceof RegExp||"function"!=typeof a.compare)throw new Error(`"${i}": format "${r}" does not define "compare" function`);const o=t.scopeValue("formats",{key:r,ref:a,code:d.code.formats?s._`${d.code.formats}${s.getProperty(r)}`:void 0});e.fail$data(f(o))}()},dependencies:["format"]},t.default=e=>(e.addKeyword(t.formatLimitDefinition),e)},20151:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MissingRefError=t.ValidationError=t.CodeGen=t.Name=t.nil=t.stringify=t.str=t._=t.KeywordCxt=void 0;const a=r(98955),s=r(87213),o=r(30648),n=r(60172),i=["/properties"],c="http://json-schema.org/draft-07/schema";class d extends a.default{_addVocabularies(){super._addVocabularies(),s.default.forEach((e=>this.addVocabulary(e))),this.opts.discriminator&&this.addKeyword(o.default)}_addDefaultMetaSchema(){if(super._addDefaultMetaSchema(),!this.opts.meta)return;const e=this.opts.$data?this.$dataMetaSchema(n,i):n;this.addMetaSchema(e,c,!1),this.refs["http://json-schema.org/schema"]=c}defaultMeta(){return this.opts.defaultMeta=super.defaultMeta()||(this.getSchema(c)?c:void 0)}}e.exports=t=d,Object.defineProperty(t,"__esModule",{value:!0}),t.default=d;var l=r(40764);Object.defineProperty(t,"KeywordCxt",{enumerable:!0,get:function(){return l.KeywordCxt}});var u=r(17231);Object.defineProperty(t,"_",{enumerable:!0,get:function(){return u._}}),Object.defineProperty(t,"str",{enumerable:!0,get:function(){return u.str}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return u.stringify}}),Object.defineProperty(t,"nil",{enumerable:!0,get:function(){return u.nil}}),Object.defineProperty(t,"Name",{enumerable:!0,get:function(){return u.Name}}),Object.defineProperty(t,"CodeGen",{enumerable:!0,get:function(){return u.CodeGen}});var f=r(17766);Object.defineProperty(t,"ValidationError",{enumerable:!0,get:function(){return f.default}});var h=r(26317);Object.defineProperty(t,"MissingRefError",{enumerable:!0,get:function(){return h.default}})},28115:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.regexpCode=t.getEsmExportName=t.getProperty=t.safeStringify=t.stringify=t.strConcat=t.addCodeArg=t.str=t._=t.nil=t._Code=t.Name=t.IDENTIFIER=t._CodeOrName=void 0;class r{}t._CodeOrName=r,t.IDENTIFIER=/^[a-z$_][a-z$_0-9]*$/i;class a extends r{constructor(e){if(super(),!t.IDENTIFIER.test(e))throw new Error("CodeGen: name must be a valid identifier");this.str=e}toString(){return this.str}emptyStr(){return!1}get names(){return{[this.str]:1}}}t.Name=a;class s extends r{constructor(e){super(),this._items="string"==typeof e?[e]:e}toString(){return this.str}emptyStr(){if(this._items.length>1)return!1;const e=this._items[0];return""===e||'""'===e}get str(){var e;return null!==(e=this._str)&&void 0!==e?e:this._str=this._items.reduce(((e,t)=>`${e}${t}`),"")}get names(){var e;return null!==(e=this._names)&&void 0!==e?e:this._names=this._items.reduce(((e,t)=>(t instanceof a&&(e[t.str]=(e[t.str]||0)+1),e)),{})}}function o(e,...t){const r=[e[0]];let a=0;for(;a<t.length;)c(r,t[a]),r.push(e[++a]);return new s(r)}t._Code=s,t.nil=new s(""),t._=o;const n=new s("+");function i(e,...t){const r=[l(e[0])];let a=0;for(;a<t.length;)r.push(n),c(r,t[a]),r.push(n,l(e[++a]));return function(e){let t=1;for(;t<e.length-1;){if(e[t]===n){const r=d(e[t-1],e[t+1]);if(void 0!==r){e.splice(t-1,3,r);continue}e[t++]="+"}t++}}(r),new s(r)}function c(e,t){var r;t instanceof s?e.push(...t._items):t instanceof a?e.push(t):e.push("number"==typeof(r=t)||"boolean"==typeof r||null===r?r:l(Array.isArray(r)?r.join(","):r))}function d(e,t){if('""'===t)return e;if('""'===e)return t;if("string"==typeof e){if(t instanceof a||'"'!==e[e.length-1])return;return"string"!=typeof t?`${e.slice(0,-1)}${t}"`:'"'===t[0]?e.slice(0,-1)+t.slice(1):void 0}return"string"!=typeof t||'"'!==t[0]||e instanceof a?void 0:`"${e}${t.slice(1)}`}function l(e){return JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")}t.str=i,t.addCodeArg=c,t.strConcat=function(e,t){return t.emptyStr()?e:e.emptyStr()?t:i`${e}${t}`},t.stringify=function(e){return new s(l(e))},t.safeStringify=l,t.getProperty=function(e){return"string"==typeof e&&t.IDENTIFIER.test(e)?new s(`.${e}`):o`[${e}]`},t.getEsmExportName=function(e){if("string"==typeof e&&t.IDENTIFIER.test(e))return new s(`${e}`);throw new Error(`CodeGen: invalid export name: ${e}, use explicit $id name mapping`)},t.regexpCode=function(e){return new s(e.toString())}},17231:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.or=t.and=t.not=t.CodeGen=t.operators=t.varKinds=t.ValueScopeName=t.ValueScope=t.Scope=t.Name=t.regexpCode=t.stringify=t.getProperty=t.nil=t.strConcat=t.str=t._=void 0;const a=r(28115),s=r(99131);var o=r(28115);Object.defineProperty(t,"_",{enumerable:!0,get:function(){return o._}}),Object.defineProperty(t,"str",{enumerable:!0,get:function(){return o.str}}),Object.defineProperty(t,"strConcat",{enumerable:!0,get:function(){return o.strConcat}}),Object.defineProperty(t,"nil",{enumerable:!0,get:function(){return o.nil}}),Object.defineProperty(t,"getProperty",{enumerable:!0,get:function(){return o.getProperty}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return o.stringify}}),Object.defineProperty(t,"regexpCode",{enumerable:!0,get:function(){return o.regexpCode}}),Object.defineProperty(t,"Name",{enumerable:!0,get:function(){return o.Name}});var n=r(99131);Object.defineProperty(t,"Scope",{enumerable:!0,get:function(){return n.Scope}}),Object.defineProperty(t,"ValueScope",{enumerable:!0,get:function(){return n.ValueScope}}),Object.defineProperty(t,"ValueScopeName",{enumerable:!0,get:function(){return n.ValueScopeName}}),Object.defineProperty(t,"varKinds",{enumerable:!0,get:function(){return n.varKinds}}),t.operators={GT:new a._Code(">"),GTE:new a._Code(">="),LT:new a._Code("<"),LTE:new a._Code("<="),EQ:new a._Code("==="),NEQ:new a._Code("!=="),NOT:new a._Code("!"),OR:new a._Code("||"),AND:new a._Code("&&"),ADD:new a._Code("+")};class i{optimizeNodes(){return this}optimizeNames(e,t){return this}}class c extends i{constructor(e,t,r){super(),this.varKind=e,this.name=t,this.rhs=r}render({es5:e,_n:t}){const r=e?s.varKinds.var:this.varKind,a=void 0===this.rhs?"":` = ${this.rhs}`;return`${r} ${this.name}${a};`+t}optimizeNames(e,t){if(e[this.name.str])return this.rhs&&(this.rhs=x(this.rhs,e,t)),this}get names(){return this.rhs instanceof a._CodeOrName?this.rhs.names:{}}}class d extends i{constructor(e,t,r){super(),this.lhs=e,this.rhs=t,this.sideEffects=r}render({_n:e}){return`${this.lhs} = ${this.rhs};`+e}optimizeNames(e,t){if(!(this.lhs instanceof a.Name)||e[this.lhs.str]||this.sideEffects)return this.rhs=x(this.rhs,e,t),this}get names(){return T(this.lhs instanceof a.Name?{}:{...this.lhs.names},this.rhs)}}class l extends d{constructor(e,t,r,a){super(e,r,a),this.op=t}render({_n:e}){return`${this.lhs} ${this.op}= ${this.rhs};`+e}}class u extends i{constructor(e){super(),this.label=e,this.names={}}render({_n:e}){return`${this.label}:`+e}}class f extends i{constructor(e){super(),this.label=e,this.names={}}render({_n:e}){return`break${this.label?` ${this.label}`:""};`+e}}class h extends i{constructor(e){super(),this.error=e}render({_n:e}){return`throw ${this.error};`+e}get names(){return this.error.names}}class m extends i{constructor(e){super(),this.code=e}render({_n:e}){return`${this.code};`+e}optimizeNodes(){return`${this.code}`?this:void 0}optimizeNames(e,t){return this.code=x(this.code,e,t),this}get names(){return this.code instanceof a._CodeOrName?this.code.names:{}}}class p extends i{constructor(e=[]){super(),this.nodes=e}render(e){return this.nodes.reduce(((t,r)=>t+r.render(e)),"")}optimizeNodes(){const{nodes:e}=this;let t=e.length;for(;t--;){const r=e[t].optimizeNodes();Array.isArray(r)?e.splice(t,1,...r):r?e[t]=r:e.splice(t,1)}return e.length>0?this:void 0}optimizeNames(e,t){const{nodes:r}=this;let a=r.length;for(;a--;){const s=r[a];s.optimizeNames(e,t)||(R(e,s.names),r.splice(a,1))}return r.length>0?this:void 0}get names(){return this.nodes.reduce(((e,t)=>O(e,t.names)),{})}}class y extends p{render(e){return"{"+e._n+super.render(e)+"}"+e._n}}class $ extends p{}class v extends y{}v.kind="else";class g extends y{constructor(e,t){super(t),this.condition=e}render(e){let t=`if(${this.condition})`+super.render(e);return this.else&&(t+="else "+this.else.render(e)),t}optimizeNodes(){super.optimizeNodes();const e=this.condition;if(!0===e)return this.nodes;let t=this.else;if(t){const e=t.optimizeNodes();t=this.else=Array.isArray(e)?new v(e):e}return t?!1===e?t instanceof g?t:t.nodes:this.nodes.length?this:new g(C(e),t instanceof g?[t]:t.nodes):!1!==e&&this.nodes.length?this:void 0}optimizeNames(e,t){var r;if(this.else=null===(r=this.else)||void 0===r?void 0:r.optimizeNames(e,t),super.optimizeNames(e,t)||this.else)return this.condition=x(this.condition,e,t),this}get names(){const e=super.names;return T(e,this.condition),this.else&&O(e,this.else.names),e}}g.kind="if";class _ extends y{}_.kind="for";class w extends _{constructor(e){super(),this.iteration=e}render(e){return`for(${this.iteration})`+super.render(e)}optimizeNames(e,t){if(super.optimizeNames(e,t))return this.iteration=x(this.iteration,e,t),this}get names(){return O(super.names,this.iteration.names)}}class b extends _{constructor(e,t,r,a){super(),this.varKind=e,this.name=t,this.from=r,this.to=a}render(e){const t=e.es5?s.varKinds.var:this.varKind,{name:r,from:a,to:o}=this;return`for(${t} ${r}=${a}; ${r}<${o}; ${r}++)`+super.render(e)}get names(){const e=T(super.names,this.from);return T(e,this.to)}}class P extends _{constructor(e,t,r,a){super(),this.loop=e,this.varKind=t,this.name=r,this.iterable=a}render(e){return`for(${this.varKind} ${this.name} ${this.loop} ${this.iterable})`+super.render(e)}optimizeNames(e,t){if(super.optimizeNames(e,t))return this.iterable=x(this.iterable,e,t),this}get names(){return O(super.names,this.iterable.names)}}class E extends y{constructor(e,t,r){super(),this.name=e,this.args=t,this.async=r}render(e){return`${this.async?"async ":""}function ${this.name}(${this.args})`+super.render(e)}}E.kind="func";class S extends p{render(e){return"return "+super.render(e)}}S.kind="return";class k extends y{render(e){let t="try"+super.render(e);return this.catch&&(t+=this.catch.render(e)),this.finally&&(t+=this.finally.render(e)),t}optimizeNodes(){var e,t;return super.optimizeNodes(),null===(e=this.catch)||void 0===e||e.optimizeNodes(),null===(t=this.finally)||void 0===t||t.optimizeNodes(),this}optimizeNames(e,t){var r,a;return super.optimizeNames(e,t),null===(r=this.catch)||void 0===r||r.optimizeNames(e,t),null===(a=this.finally)||void 0===a||a.optimizeNames(e,t),this}get names(){const e=super.names;return this.catch&&O(e,this.catch.names),this.finally&&O(e,this.finally.names),e}}class j extends y{constructor(e){super(),this.error=e}render(e){return`catch(${this.error})`+super.render(e)}}j.kind="catch";class N extends y{render(e){return"finally"+super.render(e)}}function O(e,t){for(const r in t)e[r]=(e[r]||0)+(t[r]||0);return e}function T(e,t){return t instanceof a._CodeOrName?O(e,t.names):e}function x(e,t,r){return e instanceof a.Name?o(e):(s=e)instanceof a._Code&&s._items.some((e=>e instanceof a.Name&&1===t[e.str]&&void 0!==r[e.str]))?new a._Code(e._items.reduce(((e,t)=>(t instanceof a.Name&&(t=o(t)),t instanceof a._Code?e.push(...t._items):e.push(t),e)),[])):e;var s;function o(e){const a=r[e.str];return void 0===a||1!==t[e.str]?e:(delete t[e.str],a)}}function R(e,t){for(const r in t)e[r]=(e[r]||0)-(t[r]||0)}function C(e){return"boolean"==typeof e||"number"==typeof e||null===e?!e:a._`!${A(e)}`}N.kind="finally",t.CodeGen=class{constructor(e,t={}){this._values={},this._blockStarts=[],this._constants={},this.opts={...t,_n:t.lines?"\n":""},this._extScope=e,this._scope=new s.Scope({parent:e}),this._nodes=[new $]}toString(){return this._root.render(this.opts)}name(e){return this._scope.name(e)}scopeName(e){return this._extScope.name(e)}scopeValue(e,t){const r=this._extScope.value(e,t);return(this._values[r.prefix]||(this._values[r.prefix]=new Set)).add(r),r}getScopeValue(e,t){return this._extScope.getValue(e,t)}scopeRefs(e){return this._extScope.scopeRefs(e,this._values)}scopeCode(){return this._extScope.scopeCode(this._values)}_def(e,t,r,a){const s=this._scope.toName(t);return void 0!==r&&a&&(this._constants[s.str]=r),this._leafNode(new c(e,s,r)),s}const(e,t,r){return this._def(s.varKinds.const,e,t,r)}let(e,t,r){return this._def(s.varKinds.let,e,t,r)}var(e,t,r){return this._def(s.varKinds.var,e,t,r)}assign(e,t,r){return this._leafNode(new d(e,t,r))}add(e,r){return this._leafNode(new l(e,t.operators.ADD,r))}code(e){return"function"==typeof e?e():e!==a.nil&&this._leafNode(new m(e)),this}object(...e){const t=["{"];for(const[r,s]of e)t.length>1&&t.push(","),t.push(r),(r!==s||this.opts.es5)&&(t.push(":"),(0,a.addCodeArg)(t,s));return t.push("}"),new a._Code(t)}if(e,t,r){if(this._blockNode(new g(e)),t&&r)this.code(t).else().code(r).endIf();else if(t)this.code(t).endIf();else if(r)throw new Error('CodeGen: "else" body without "then" body');return this}elseIf(e){return this._elseNode(new g(e))}else(){return this._elseNode(new v)}endIf(){return this._endBlockNode(g,v)}_for(e,t){return this._blockNode(e),t&&this.code(t).endFor(),this}for(e,t){return this._for(new w(e),t)}forRange(e,t,r,a,o=(this.opts.es5?s.varKinds.var:s.varKinds.let)){const n=this._scope.toName(e);return this._for(new b(o,n,t,r),(()=>a(n)))}forOf(e,t,r,o=s.varKinds.const){const n=this._scope.toName(e);if(this.opts.es5){const e=t instanceof a.Name?t:this.var("_arr",t);return this.forRange("_i",0,a._`${e}.length`,(t=>{this.var(n,a._`${e}[${t}]`),r(n)}))}return this._for(new P("of",o,n,t),(()=>r(n)))}forIn(e,t,r,o=(this.opts.es5?s.varKinds.var:s.varKinds.const)){if(this.opts.ownProperties)return this.forOf(e,a._`Object.keys(${t})`,r);const n=this._scope.toName(e);return this._for(new P("in",o,n,t),(()=>r(n)))}endFor(){return this._endBlockNode(_)}label(e){return this._leafNode(new u(e))}break(e){return this._leafNode(new f(e))}return(e){const t=new S;if(this._blockNode(t),this.code(e),1!==t.nodes.length)throw new Error('CodeGen: "return" should have one node');return this._endBlockNode(S)}try(e,t,r){if(!t&&!r)throw new Error('CodeGen: "try" without "catch" and "finally"');const a=new k;if(this._blockNode(a),this.code(e),t){const e=this.name("e");this._currNode=a.catch=new j(e),t(e)}return r&&(this._currNode=a.finally=new N,this.code(r)),this._endBlockNode(j,N)}throw(e){return this._leafNode(new h(e))}block(e,t){return this._blockStarts.push(this._nodes.length),e&&this.code(e).endBlock(t),this}endBlock(e){const t=this._blockStarts.pop();if(void 0===t)throw new Error("CodeGen: not in self-balancing block");const r=this._nodes.length-t;if(r<0||void 0!==e&&r!==e)throw new Error(`CodeGen: wrong number of nodes: ${r} vs ${e} expected`);return this._nodes.length=t,this}func(e,t=a.nil,r,s){return this._blockNode(new E(e,t,r)),s&&this.code(s).endFunc(),this}endFunc(){return this._endBlockNode(E)}optimize(e=1){for(;e-- >0;)this._root.optimizeNodes(),this._root.optimizeNames(this._root.names,this._constants)}_leafNode(e){return this._currNode.nodes.push(e),this}_blockNode(e){this._currNode.nodes.push(e),this._nodes.push(e)}_endBlockNode(e,t){const r=this._currNode;if(r instanceof e||t&&r instanceof t)return this._nodes.pop(),this;throw new Error(`CodeGen: not in block "${t?`${e.kind}/${t.kind}`:e.kind}"`)}_elseNode(e){const t=this._currNode;if(!(t instanceof g))throw new Error('CodeGen: "else" without "if"');return this._currNode=t.else=e,this}get _root(){return this._nodes[0]}get _currNode(){const e=this._nodes;return e[e.length-1]}set _currNode(e){const t=this._nodes;t[t.length-1]=e}},t.not=C;const I=D(t.operators.AND);t.and=function(...e){return e.reduce(I)};const M=D(t.operators.OR);function D(e){return(t,r)=>t===a.nil?r:r===a.nil?t:a._`${A(t)} ${e} ${A(r)}`}function A(e){return e instanceof a.Name?e:a._`(${e})`}t.or=function(...e){return e.reduce(M)}},99131:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ValueScope=t.ValueScopeName=t.Scope=t.varKinds=t.UsedValueState=void 0;const a=r(28115);class s extends Error{constructor(e){super(`CodeGen: "code" for ${e} not defined`),this.value=e.value}}var o;!function(e){e[e.Started=0]="Started",e[e.Completed=1]="Completed"}(o=t.UsedValueState||(t.UsedValueState={})),t.varKinds={const:new a.Name("const"),let:new a.Name("let"),var:new a.Name("var")};class n{constructor({prefixes:e,parent:t}={}){this._names={},this._prefixes=e,this._parent=t}toName(e){return e instanceof a.Name?e:this.name(e)}name(e){return new a.Name(this._newName(e))}_newName(e){return`${e}${(this._names[e]||this._nameGroup(e)).index++}`}_nameGroup(e){var t,r;if((null===(r=null===(t=this._parent)||void 0===t?void 0:t._prefixes)||void 0===r?void 0:r.has(e))||this._prefixes&&!this._prefixes.has(e))throw new Error(`CodeGen: prefix "${e}" is not allowed in this scope`);return this._names[e]={prefix:e,index:0}}}t.Scope=n;class i extends a.Name{constructor(e,t){super(t),this.prefix=e}setValue(e,{property:t,itemIndex:r}){this.value=e,this.scopePath=a._`.${new a.Name(t)}[${r}]`}}t.ValueScopeName=i;const c=a._`\n`;t.ValueScope=class extends n{constructor(e){super(e),this._values={},this._scope=e.scope,this.opts={...e,_n:e.lines?c:a.nil}}get(){return this._scope}name(e){return new i(e,this._newName(e))}value(e,t){var r;if(void 0===t.ref)throw new Error("CodeGen: ref must be passed in value");const a=this.toName(e),{prefix:s}=a,o=null!==(r=t.key)&&void 0!==r?r:t.ref;let n=this._values[s];if(n){const e=n.get(o);if(e)return e}else n=this._values[s]=new Map;n.set(o,a);const i=this._scope[s]||(this._scope[s]=[]),c=i.length;return i[c]=t.ref,a.setValue(t,{property:s,itemIndex:c}),a}getValue(e,t){const r=this._values[e];if(r)return r.get(t)}scopeRefs(e,t=this._values){return this._reduceValues(t,(t=>{if(void 0===t.scopePath)throw new Error(`CodeGen: name "${t}" has no value`);return a._`${e}${t.scopePath}`}))}scopeCode(e=this._values,t,r){return this._reduceValues(e,(e=>{if(void 0===e.value)throw new Error(`CodeGen: name "${e}" has no value`);return e.value.code}),t,r)}_reduceValues(e,r,n={},i){let c=a.nil;for(const d in e){const l=e[d];if(!l)continue;const u=n[d]=n[d]||new Map;l.forEach((e=>{if(u.has(e))return;u.set(e,o.Started);let n=r(e);if(n){const r=this.opts.es5?t.varKinds.var:t.varKinds.const;c=a._`${c}${r} ${e} = ${n};${this.opts._n}`}else{if(!(n=null==i?void 0:i(e)))throw new s(e);c=a._`${c}${n}${this.opts._n}`}u.set(e,o.Completed)}))}return c}}},82263:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.extendErrors=t.resetErrorsCount=t.reportExtraError=t.reportError=t.keyword$DataError=t.keywordError=void 0;const a=r(17231),s=r(20679),o=r(3948);function n(e,t){const r=e.const("err",t);e.if(a._`${o.default.vErrors} === null`,(()=>e.assign(o.default.vErrors,a._`[${r}]`)),a._`${o.default.vErrors}.push(${r})`),e.code(a._`${o.default.errors}++`)}function i(e,t){const{gen:r,validateName:s,schemaEnv:o}=e;o.$async?r.throw(a._`new ${e.ValidationError}(${t})`):(r.assign(a._`${s}.errors`,t),r.return(!1))}t.keywordError={message:({keyword:e})=>a.str`must pass "${e}" keyword validation`},t.keyword$DataError={message:({keyword:e,schemaType:t})=>t?a.str`"${e}" keyword must be ${t} ($data)`:a.str`"${e}" keyword is invalid ($data)`},t.reportError=function(e,r=t.keywordError,s,o){const{it:c}=e,{gen:l,compositeRule:u,allErrors:f}=c,h=d(e,r,s);(null!=o?o:u||f)?n(l,h):i(c,a._`[${h}]`)},t.reportExtraError=function(e,r=t.keywordError,a){const{it:s}=e,{gen:c,compositeRule:l,allErrors:u}=s;n(c,d(e,r,a)),l||u||i(s,o.default.vErrors)},t.resetErrorsCount=function(e,t){e.assign(o.default.errors,t),e.if(a._`${o.default.vErrors} !== null`,(()=>e.if(t,(()=>e.assign(a._`${o.default.vErrors}.length`,t)),(()=>e.assign(o.default.vErrors,null)))))},t.extendErrors=function({gen:e,keyword:t,schemaValue:r,data:s,errsCount:n,it:i}){if(void 0===n)throw new Error("ajv implementation error");const c=e.name("err");e.forRange("i",n,o.default.errors,(n=>{e.const(c,a._`${o.default.vErrors}[${n}]`),e.if(a._`${c}.instancePath === undefined`,(()=>e.assign(a._`${c}.instancePath`,(0,a.strConcat)(o.default.instancePath,i.errorPath)))),e.assign(a._`${c}.schemaPath`,a.str`${i.errSchemaPath}/${t}`),i.opts.verbose&&(e.assign(a._`${c}.schema`,r),e.assign(a._`${c}.data`,s))}))};const c={keyword:new a.Name("keyword"),schemaPath:new a.Name("schemaPath"),params:new a.Name("params"),propertyName:new a.Name("propertyName"),message:new a.Name("message"),schema:new a.Name("schema"),parentSchema:new a.Name("parentSchema")};function d(e,t,r){const{createErrors:s}=e.it;return!1===s?a._`{}`:function(e,t,r={}){const{gen:s,it:n}=e,i=[l(n,r),u(e,r)];return function(e,{params:t,message:r},s){const{keyword:n,data:i,schemaValue:d,it:l}=e,{opts:u,propertyName:f,topSchemaRef:h,schemaPath:m}=l;s.push([c.keyword,n],[c.params,"function"==typeof t?t(e):t||a._`{}`]),u.messages&&s.push([c.message,"function"==typeof r?r(e):r]),u.verbose&&s.push([c.schema,d],[c.parentSchema,a._`${h}${m}`],[o.default.data,i]),f&&s.push([c.propertyName,f])}(e,t,i),s.object(...i)}(e,t,r)}function l({errorPath:e},{instancePath:t}){const r=t?a.str`${e}${(0,s.getErrorPath)(t,s.Type.Str)}`:e;return[o.default.instancePath,(0,a.strConcat)(o.default.instancePath,r)]}function u({keyword:e,it:{errSchemaPath:t}},{schemaPath:r,parentSchema:o}){let n=o?t:a.str`${t}/${e}`;return r&&(n=a.str`${n}${(0,s.getErrorPath)(r,s.Type.Str)}`),[c.schemaPath,n]}},50052:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.resolveSchema=t.getCompilingSchema=t.resolveRef=t.compileSchema=t.SchemaEnv=void 0;const a=r(17231),s=r(17766),o=r(3948),n=r(33761),i=r(20679),c=r(40764);class d{constructor(e){var t;let r;this.refs={},this.dynamicAnchors={},"object"==typeof e.schema&&(r=e.schema),this.schema=e.schema,this.schemaId=e.schemaId,this.root=e.root||this,this.baseId=null!==(t=e.baseId)&&void 0!==t?t:(0,n.normalizeId)(null==r?void 0:r[e.schemaId||"$id"]),this.schemaPath=e.schemaPath,this.localRefs=e.localRefs,this.meta=e.meta,this.$async=null==r?void 0:r.$async,this.refs={}}}function l(e){const t=f.call(this,e);if(t)return t;const r=(0,n.getFullPath)(this.opts.uriResolver,e.root.baseId),{es5:i,lines:d}=this.opts.code,{ownProperties:l}=this.opts,u=new a.CodeGen(this.scope,{es5:i,lines:d,ownProperties:l});let h;e.$async&&(h=u.scopeValue("Error",{ref:s.default,code:a._`require("ajv/dist/runtime/validation_error").default`}));const m=u.scopeName("validate");e.validateName=m;const p={gen:u,allErrors:this.opts.allErrors,data:o.default.data,parentData:o.default.parentData,parentDataProperty:o.default.parentDataProperty,dataNames:[o.default.data],dataPathArr:[a.nil],dataLevel:0,dataTypes:[],definedProperties:new Set,topSchemaRef:u.scopeValue("schema",!0===this.opts.code.source?{ref:e.schema,code:(0,a.stringify)(e.schema)}:{ref:e.schema}),validateName:m,ValidationError:h,schema:e.schema,schemaEnv:e,rootId:r,baseId:e.baseId||r,schemaPath:a.nil,errSchemaPath:e.schemaPath||(this.opts.jtd?"":"#"),errorPath:a._`""`,opts:this.opts,self:this};let y;try{this._compilations.add(e),(0,c.validateFunctionCode)(p),u.optimize(this.opts.code.optimize);const t=u.toString();y=`${u.scopeRefs(o.default.scope)}return ${t}`,this.opts.code.process&&(y=this.opts.code.process(y,e));const r=new Function(`${o.default.self}`,`${o.default.scope}`,y)(this,this.scope.get());if(this.scope.value(m,{ref:r}),r.errors=null,r.schema=e.schema,r.schemaEnv=e,e.$async&&(r.$async=!0),!0===this.opts.code.source&&(r.source={validateName:m,validateCode:t,scopeValues:u._values}),this.opts.unevaluated){const{props:e,items:t}=p;r.evaluated={props:e instanceof a.Name?void 0:e,items:t instanceof a.Name?void 0:t,dynamicProps:e instanceof a.Name,dynamicItems:t instanceof a.Name},r.source&&(r.source.evaluated=(0,a.stringify)(r.evaluated))}return e.validate=r,e}catch(t){throw delete e.validate,delete e.validateName,y&&this.logger.error("Error compiling schema, function code:",y),t}finally{this._compilations.delete(e)}}function u(e){return(0,n.inlineRef)(e.schema,this.opts.inlineRefs)?e.schema:e.validate?e:l.call(this,e)}function f(e){for(const a of this._compilations)if(r=e,(t=a).schema===r.schema&&t.root===r.root&&t.baseId===r.baseId)return a;var t,r}function h(e,t){let r;for(;"string"==typeof(r=this.refs[t]);)t=r;return r||this.schemas[t]||m.call(this,e,t)}function m(e,t){const r=this.opts.uriResolver.parse(t),a=(0,n._getFullPath)(this.opts.uriResolver,r);let s=(0,n.getFullPath)(this.opts.uriResolver,e.baseId,void 0);if(Object.keys(e.schema).length>0&&a===s)return y.call(this,r,e);const o=(0,n.normalizeId)(a),i=this.refs[o]||this.schemas[o];if("string"==typeof i){const t=m.call(this,e,i);if("object"!=typeof(null==t?void 0:t.schema))return;return y.call(this,r,t)}if("object"==typeof(null==i?void 0:i.schema)){if(i.validate||l.call(this,i),o===(0,n.normalizeId)(t)){const{schema:t}=i,{schemaId:r}=this.opts,a=t[r];return a&&(s=(0,n.resolveUrl)(this.opts.uriResolver,s,a)),new d({schema:t,schemaId:r,root:e,baseId:s})}return y.call(this,r,i)}}t.SchemaEnv=d,t.compileSchema=l,t.resolveRef=function(e,t,r){var a;r=(0,n.resolveUrl)(this.opts.uriResolver,t,r);const s=e.refs[r];if(s)return s;let o=h.call(this,e,r);if(void 0===o){const s=null===(a=e.localRefs)||void 0===a?void 0:a[r],{schemaId:n}=this.opts;s&&(o=new d({schema:s,schemaId:n,root:e,baseId:t}))}return void 0!==o?e.refs[r]=u.call(this,o):void 0},t.getCompilingSchema=f,t.resolveSchema=m;const p=new Set(["properties","patternProperties","enum","dependencies","definitions"]);function y(e,{baseId:t,schema:r,root:a}){var s;if("/"!==(null===(s=e.fragment)||void 0===s?void 0:s[0]))return;for(const a of e.fragment.slice(1).split("/")){if("boolean"==typeof r)return;const e=r[(0,i.unescapeFragment)(a)];if(void 0===e)return;const s="object"==typeof(r=e)&&r[this.opts.schemaId];!p.has(a)&&s&&(t=(0,n.resolveUrl)(this.opts.uriResolver,t,s))}let o;if("boolean"!=typeof r&&r.$ref&&!(0,i.schemaHasRulesButRef)(r,this.RULES)){const e=(0,n.resolveUrl)(this.opts.uriResolver,t,r.$ref);o=m.call(this,a,e)}const{schemaId:c}=this.opts;return o=o||new d({schema:r,schemaId:c,root:a,baseId:t}),o.schema!==o.root.schema?o:void 0}},3948:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(17231),s={data:new a.Name("data"),valCxt:new a.Name("valCxt"),instancePath:new a.Name("instancePath"),parentData:new a.Name("parentData"),parentDataProperty:new a.Name("parentDataProperty"),rootData:new a.Name("rootData"),dynamicAnchors:new a.Name("dynamicAnchors"),vErrors:new a.Name("vErrors"),errors:new a.Name("errors"),this:new a.Name("this"),self:new a.Name("self"),scope:new a.Name("scope"),json:new a.Name("json"),jsonPos:new a.Name("jsonPos"),jsonLen:new a.Name("jsonLen"),jsonPart:new a.Name("jsonPart")};t.default=s},26317:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(33761);class s extends Error{constructor(e,t,r,s){super(s||`can't resolve reference ${r} from id ${t}`),this.missingRef=(0,a.resolveUrl)(e,t,r),this.missingSchema=(0,a.normalizeId)((0,a.getFullPath)(e,this.missingRef))}}t.default=s},33761:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getSchemaRefs=t.resolveUrl=t.normalizeId=t._getFullPath=t.getFullPath=t.inlineRef=void 0;const a=r(20679),s=r(66471),o=r(25127),n=new Set(["type","format","pattern","maxLength","minLength","maxProperties","minProperties","maxItems","minItems","maximum","minimum","uniqueItems","multipleOf","required","enum","const"]);t.inlineRef=function(e,t=!0){return"boolean"==typeof e||(!0===t?!c(e):!!t&&d(e)<=t)};const i=new Set(["$ref","$recursiveRef","$recursiveAnchor","$dynamicRef","$dynamicAnchor"]);function c(e){for(const t in e){if(i.has(t))return!0;const r=e[t];if(Array.isArray(r)&&r.some(c))return!0;if("object"==typeof r&&c(r))return!0}return!1}function d(e){let t=0;for(const r in e){if("$ref"===r)return 1/0;if(t++,!n.has(r)&&("object"==typeof e[r]&&(0,a.eachItem)(e[r],(e=>t+=d(e))),t===1/0))return 1/0}return t}function l(e,t="",r){!1!==r&&(t=h(t));const a=e.parse(t);return u(e,a)}function u(e,t){return e.serialize(t).split("#")[0]+"#"}t.getFullPath=l,t._getFullPath=u;const f=/#\/?$/;function h(e){return e?e.replace(f,""):""}t.normalizeId=h,t.resolveUrl=function(e,t,r){return r=h(r),e.resolve(t,r)};const m=/^[a-z_][-a-z0-9._]*$/i;t.getSchemaRefs=function(e,t){if("boolean"==typeof e)return{};const{schemaId:r,uriResolver:a}=this.opts,n=h(e[r]||t),i={"":n},c=l(a,n,!1),d={},u=new Set;return o(e,{allKeys:!0},((e,t,a,s)=>{if(void 0===s)return;const o=c+t;let n=i[s];function l(t){const r=this.opts.uriResolver.resolve;if(t=h(n?r(n,t):t),u.has(t))throw p(t);u.add(t);let a=this.refs[t];return"string"==typeof a&&(a=this.refs[a]),"object"==typeof a?f(e,a.schema,t):t!==h(o)&&("#"===t[0]?(f(e,d[t],t),d[t]=e):this.refs[t]=o),t}function y(e){if("string"==typeof e){if(!m.test(e))throw new Error(`invalid anchor "${e}"`);l.call(this,`#${e}`)}}"string"==typeof e[r]&&(n=l.call(this,e[r])),y.call(this,e.$anchor),y.call(this,e.$dynamicAnchor),i[t]=n})),d;function f(e,t,r){if(void 0!==t&&!s(e,t))throw p(r)}function p(e){return new Error(`reference "${e}" resolves to more than one schema`)}}},24623:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getRules=t.isJSONType=void 0;const r=new Set(["string","number","integer","boolean","null","object","array"]);t.isJSONType=function(e){return"string"==typeof e&&r.has(e)},t.getRules=function(){const e={number:{type:"number",rules:[]},string:{type:"string",rules:[]},array:{type:"array",rules:[]},object:{type:"object",rules:[]}};return{types:{...e,integer:!0,boolean:!0,null:!0},rules:[{rules:[]},e.number,e.string,e.array,e.object],post:{rules:[]},all:{},keywords:{}}}},20679:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.checkStrictMode=t.getErrorPath=t.Type=t.useFunc=t.setEvaluated=t.evaluatedPropsToName=t.mergeEvaluated=t.eachItem=t.unescapeJsonPointer=t.escapeJsonPointer=t.escapeFragment=t.unescapeFragment=t.schemaRefOrVal=t.schemaHasRulesButRef=t.schemaHasRules=t.checkUnknownRules=t.alwaysValidSchema=t.toHash=void 0;const a=r(17231),s=r(28115);function o(e,t=e.schema){const{opts:r,self:a}=e;if(!r.strictSchema)return;if("boolean"==typeof t)return;const s=a.RULES.keywords;for(const r in t)s[r]||m(e,`unknown keyword: "${r}"`)}function n(e,t){if("boolean"==typeof e)return!e;for(const r in e)if(t[r])return!0;return!1}function i(e){return"number"==typeof e?`${e}`:e.replace(/~/g,"~0").replace(/\//g,"~1")}function c(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}function d({mergeNames:e,mergeToName:t,mergeValues:r,resultToName:s}){return(o,n,i,c)=>{const d=void 0===i?n:i instanceof a.Name?(n instanceof a.Name?e(o,n,i):t(o,n,i),i):n instanceof a.Name?(t(o,i,n),n):r(n,i);return c!==a.Name||d instanceof a.Name?d:s(o,d)}}function l(e,t){if(!0===t)return e.var("props",!0);const r=e.var("props",a._`{}`);return void 0!==t&&u(e,r,t),r}function u(e,t,r){Object.keys(r).forEach((r=>e.assign(a._`${t}${(0,a.getProperty)(r)}`,!0)))}t.toHash=function(e){const t={};for(const r of e)t[r]=!0;return t},t.alwaysValidSchema=function(e,t){return"boolean"==typeof t?t:0===Object.keys(t).length||(o(e,t),!n(t,e.self.RULES.all))},t.checkUnknownRules=o,t.schemaHasRules=n,t.schemaHasRulesButRef=function(e,t){if("boolean"==typeof e)return!e;for(const r in e)if("$ref"!==r&&t.all[r])return!0;return!1},t.schemaRefOrVal=function({topSchemaRef:e,schemaPath:t},r,s,o){if(!o){if("number"==typeof r||"boolean"==typeof r)return r;if("string"==typeof r)return a._`${r}`}return a._`${e}${t}${(0,a.getProperty)(s)}`},t.unescapeFragment=function(e){return c(decodeURIComponent(e))},t.escapeFragment=function(e){return encodeURIComponent(i(e))},t.escapeJsonPointer=i,t.unescapeJsonPointer=c,t.eachItem=function(e,t){if(Array.isArray(e))for(const r of e)t(r);else t(e)},t.mergeEvaluated={props:d({mergeNames:(e,t,r)=>e.if(a._`${r} !== true && ${t} !== undefined`,(()=>{e.if(a._`${t} === true`,(()=>e.assign(r,!0)),(()=>e.assign(r,a._`${r} || {}`).code(a._`Object.assign(${r}, ${t})`)))})),mergeToName:(e,t,r)=>e.if(a._`${r} !== true`,(()=>{!0===t?e.assign(r,!0):(e.assign(r,a._`${r} || {}`),u(e,r,t))})),mergeValues:(e,t)=>!0===e||{...e,...t},resultToName:l}),items:d({mergeNames:(e,t,r)=>e.if(a._`${r} !== true && ${t} !== undefined`,(()=>e.assign(r,a._`${t} === true ? true : ${r} > ${t} ? ${r} : ${t}`))),mergeToName:(e,t,r)=>e.if(a._`${r} !== true`,(()=>e.assign(r,!0===t||a._`${r} > ${t} ? ${r} : ${t}`))),mergeValues:(e,t)=>!0===e||Math.max(e,t),resultToName:(e,t)=>e.var("items",t)})},t.evaluatedPropsToName=l,t.setEvaluated=u;const f={};var h;function m(e,t,r=e.opts.strictSchema){if(r){if(t=`strict mode: ${t}`,!0===r)throw new Error(t);e.self.logger.warn(t)}}t.useFunc=function(e,t){return e.scopeValue("func",{ref:t,code:f[t.code]||(f[t.code]=new s._Code(t.code))})},function(e){e[e.Num=0]="Num",e[e.Str=1]="Str"}(h=t.Type||(t.Type={})),t.getErrorPath=function(e,t,r){if(e instanceof a.Name){const s=t===h.Num;return r?s?a._`"[" + ${e} + "]"`:a._`"['" + ${e} + "']"`:s?a._`"/" + ${e}`:a._`"/" + ${e}.replace(/~/g, "~0").replace(/\\//g, "~1")`}return r?(0,a.getProperty)(e).toString():"/"+i(e)},t.checkStrictMode=m},54782:(e,t)=>{"use strict";function r(e,t){return t.rules.some((t=>a(e,t)))}function a(e,t){var r;return void 0!==e[t.keyword]||(null===(r=t.definition.implements)||void 0===r?void 0:r.some((t=>void 0!==e[t])))}Object.defineProperty(t,"__esModule",{value:!0}),t.shouldUseRule=t.shouldUseGroup=t.schemaHasRulesForType=void 0,t.schemaHasRulesForType=function({schema:e,self:t},a){const s=t.RULES.types[a];return s&&!0!==s&&r(e,s)},t.shouldUseGroup=r,t.shouldUseRule=a},64168:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.boolOrEmptySchema=t.topBoolOrEmptySchema=void 0;const a=r(82263),s=r(17231),o=r(3948),n={message:"boolean schema is false"};function i(e,t){const{gen:r,data:s}=e,o={gen:r,keyword:"false schema",data:s,schema:!1,schemaCode:!1,schemaValue:!1,params:{},it:e};(0,a.reportError)(o,n,void 0,t)}t.topBoolOrEmptySchema=function(e){const{gen:t,schema:r,validateName:a}=e;!1===r?i(e,!1):"object"==typeof r&&!0===r.$async?t.return(o.default.data):(t.assign(s._`${a}.errors`,null),t.return(!0))},t.boolOrEmptySchema=function(e,t){const{gen:r,schema:a}=e;!1===a?(r.var(t,!1),i(e)):r.var(t,!0)}},85077:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.reportTypeError=t.checkDataTypes=t.checkDataType=t.coerceAndCheckDataType=t.getJSONTypes=t.getSchemaTypes=t.DataType=void 0;const a=r(24623),s=r(54782),o=r(82263),n=r(17231),i=r(20679);var c;function d(e){const t=Array.isArray(e)?e:e?[e]:[];if(t.every(a.isJSONType))return t;throw new Error("type must be JSONType or JSONType[]: "+t.join(","))}!function(e){e[e.Correct=0]="Correct",e[e.Wrong=1]="Wrong"}(c=t.DataType||(t.DataType={})),t.getSchemaTypes=function(e){const t=d(e.type);if(t.includes("null")){if(!1===e.nullable)throw new Error("type: null contradicts nullable: false")}else{if(!t.length&&void 0!==e.nullable)throw new Error('"nullable" cannot be used without "type"');!0===e.nullable&&t.push("null")}return t},t.getJSONTypes=d,t.coerceAndCheckDataType=function(e,t){const{gen:r,data:a,opts:o}=e,i=function(e,t){return t?e.filter((e=>l.has(e)||"array"===t&&"array"===e)):[]}(t,o.coerceTypes),d=t.length>0&&!(0===i.length&&1===t.length&&(0,s.schemaHasRulesForType)(e,t[0]));if(d){const s=f(t,a,o.strictNumbers,c.Wrong);r.if(s,(()=>{i.length?function(e,t,r){const{gen:a,data:s,opts:o}=e,i=a.let("dataType",n._`typeof ${s}`),c=a.let("coerced",n._`undefined`);"array"===o.coerceTypes&&a.if(n._`${i} == 'object' && Array.isArray(${s}) && ${s}.length == 1`,(()=>a.assign(s,n._`${s}[0]`).assign(i,n._`typeof ${s}`).if(f(t,s,o.strictNumbers),(()=>a.assign(c,s))))),a.if(n._`${c} !== undefined`);for(const e of r)(l.has(e)||"array"===e&&"array"===o.coerceTypes)&&d(e);function d(e){switch(e){case"string":return void a.elseIf(n._`${i} == "number" || ${i} == "boolean"`).assign(c,n._`"" + ${s}`).elseIf(n._`${s} === null`).assign(c,n._`""`);case"number":return void a.elseIf(n._`${i} == "boolean" || ${s} === null
              || (${i} == "string" && ${s} && ${s} == +${s})`).assign(c,n._`+${s}`);case"integer":return void a.elseIf(n._`${i} === "boolean" || ${s} === null
              || (${i} === "string" && ${s} && ${s} == +${s} && !(${s} % 1))`).assign(c,n._`+${s}`);case"boolean":return void a.elseIf(n._`${s} === "false" || ${s} === 0 || ${s} === null`).assign(c,!1).elseIf(n._`${s} === "true" || ${s} === 1`).assign(c,!0);case"null":return a.elseIf(n._`${s} === "" || ${s} === 0 || ${s} === false`),void a.assign(c,null);case"array":a.elseIf(n._`${i} === "string" || ${i} === "number"
              || ${i} === "boolean" || ${s} === null`).assign(c,n._`[${s}]`)}}a.else(),m(e),a.endIf(),a.if(n._`${c} !== undefined`,(()=>{a.assign(s,c),function({gen:e,parentData:t,parentDataProperty:r},a){e.if(n._`${t} !== undefined`,(()=>e.assign(n._`${t}[${r}]`,a)))}(e,c)}))}(e,t,i):m(e)}))}return d};const l=new Set(["string","number","integer","boolean","null"]);function u(e,t,r,a=c.Correct){const s=a===c.Correct?n.operators.EQ:n.operators.NEQ;let o;switch(e){case"null":return n._`${t} ${s} null`;case"array":o=n._`Array.isArray(${t})`;break;case"object":o=n._`${t} && typeof ${t} == "object" && !Array.isArray(${t})`;break;case"integer":o=i(n._`!(${t} % 1) && !isNaN(${t})`);break;case"number":o=i();break;default:return n._`typeof ${t} ${s} ${e}`}return a===c.Correct?o:(0,n.not)(o);function i(e=n.nil){return(0,n.and)(n._`typeof ${t} == "number"`,e,r?n._`isFinite(${t})`:n.nil)}}function f(e,t,r,a){if(1===e.length)return u(e[0],t,r,a);let s;const o=(0,i.toHash)(e);if(o.array&&o.object){const e=n._`typeof ${t} != "object"`;s=o.null?e:n._`!${t} || ${e}`,delete o.null,delete o.array,delete o.object}else s=n.nil;o.number&&delete o.integer;for(const e in o)s=(0,n.and)(s,u(e,t,r,a));return s}t.checkDataType=u,t.checkDataTypes=f;const h={message:({schema:e})=>`must be ${e}`,params:({schema:e,schemaValue:t})=>"string"==typeof e?n._`{type: ${e}}`:n._`{type: ${t}}`};function m(e){const t=function(e){const{gen:t,data:r,schema:a}=e,s=(0,i.schemaRefOrVal)(e,a,"type");return{gen:t,keyword:"type",data:r,schema:a.type,schemaCode:s,schemaValue:s,parentSchema:a,params:{},it:e}}(e);(0,o.reportError)(t,h)}t.reportTypeError=m},3862:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assignDefaults=void 0;const a=r(17231),s=r(20679);function o(e,t,r){const{gen:o,compositeRule:n,data:i,opts:c}=e;if(void 0===r)return;const d=a._`${i}${(0,a.getProperty)(t)}`;if(n)return void(0,s.checkStrictMode)(e,`default is ignored for: ${d}`);let l=a._`${d} === undefined`;"empty"===c.useDefaults&&(l=a._`${l} || ${d} === null || ${d} === ""`),o.if(l,a._`${d} = ${(0,a.stringify)(r)}`)}t.assignDefaults=function(e,t){const{properties:r,items:a}=e.schema;if("object"===t&&r)for(const t in r)o(e,t,r[t].default);else"array"===t&&Array.isArray(a)&&a.forEach(((t,r)=>o(e,r,t.default)))}},40764:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getData=t.KeywordCxt=t.validateFunctionCode=void 0;const a=r(64168),s=r(85077),o=r(54782),n=r(85077),i=r(3862),c=r(62591),d=r(87218),l=r(17231),u=r(3948),f=r(33761),h=r(20679),m=r(82263);function p({gen:e,validateName:t,schema:r,schemaEnv:a,opts:s},o){s.code.es5?e.func(t,l._`${u.default.data}, ${u.default.valCxt}`,a.$async,(()=>{e.code(l._`"use strict"; ${y(r,s)}`),function(e,t){e.if(u.default.valCxt,(()=>{e.var(u.default.instancePath,l._`${u.default.valCxt}.${u.default.instancePath}`),e.var(u.default.parentData,l._`${u.default.valCxt}.${u.default.parentData}`),e.var(u.default.parentDataProperty,l._`${u.default.valCxt}.${u.default.parentDataProperty}`),e.var(u.default.rootData,l._`${u.default.valCxt}.${u.default.rootData}`),t.dynamicRef&&e.var(u.default.dynamicAnchors,l._`${u.default.valCxt}.${u.default.dynamicAnchors}`)}),(()=>{e.var(u.default.instancePath,l._`""`),e.var(u.default.parentData,l._`undefined`),e.var(u.default.parentDataProperty,l._`undefined`),e.var(u.default.rootData,u.default.data),t.dynamicRef&&e.var(u.default.dynamicAnchors,l._`{}`)}))}(e,s),e.code(o)})):e.func(t,l._`${u.default.data}, ${function(e){return l._`{${u.default.instancePath}="", ${u.default.parentData}, ${u.default.parentDataProperty}, ${u.default.rootData}=${u.default.data}${e.dynamicRef?l._`, ${u.default.dynamicAnchors}={}`:l.nil}}={}`}(s)}`,a.$async,(()=>e.code(y(r,s)).code(o)))}function y(e,t){const r="object"==typeof e&&e[t.schemaId];return r&&(t.code.source||t.code.process)?l._`/*# sourceURL=${r} */`:l.nil}function $({schema:e,self:t}){if("boolean"==typeof e)return!e;for(const r in e)if(t.RULES.all[r])return!0;return!1}function v(e){return"boolean"!=typeof e.schema}function g(e){(0,h.checkUnknownRules)(e),function(e){const{schema:t,errSchemaPath:r,opts:a,self:s}=e;t.$ref&&a.ignoreKeywordsWithRef&&(0,h.schemaHasRulesButRef)(t,s.RULES)&&s.logger.warn(`$ref: keywords ignored in schema at path "${r}"`)}(e)}function _(e,t){if(e.opts.jtd)return b(e,[],!1,t);const r=(0,s.getSchemaTypes)(e.schema);b(e,r,!(0,s.coerceAndCheckDataType)(e,r),t)}function w({gen:e,schemaEnv:t,schema:r,errSchemaPath:a,opts:s}){const o=r.$comment;if(!0===s.$comment)e.code(l._`${u.default.self}.logger.log(${o})`);else if("function"==typeof s.$comment){const r=l.str`${a}/$comment`,s=e.scopeValue("root",{ref:t.root});e.code(l._`${u.default.self}.opts.$comment(${o}, ${r}, ${s}.schema)`)}}function b(e,t,r,a){const{gen:s,schema:i,data:c,allErrors:d,opts:f,self:m}=e,{RULES:p}=m;function y(h){(0,o.shouldUseGroup)(i,h)&&(h.type?(s.if((0,n.checkDataType)(h.type,c,f.strictNumbers)),P(e,h),1===t.length&&t[0]===h.type&&r&&(s.else(),(0,n.reportTypeError)(e)),s.endIf()):P(e,h),d||s.if(l._`${u.default.errors} === ${a||0}`))}!i.$ref||!f.ignoreKeywordsWithRef&&(0,h.schemaHasRulesButRef)(i,p)?(f.jtd||function(e,t){!e.schemaEnv.meta&&e.opts.strictTypes&&(function(e,t){t.length&&(e.dataTypes.length?(t.forEach((t=>{E(e.dataTypes,t)||S(e,`type "${t}" not allowed by context "${e.dataTypes.join(",")}"`)})),function(e,t){const r=[];for(const a of e.dataTypes)E(t,a)?r.push(a):t.includes("integer")&&"number"===a&&r.push("integer");e.dataTypes=r}(e,t)):e.dataTypes=t)}(e,t),e.opts.allowUnionTypes||function(e,t){t.length>1&&(2!==t.length||!t.includes("null"))&&S(e,"use allowUnionTypes to allow union type keyword")}(e,t),function(e,t){const r=e.self.RULES.all;for(const a in r){const s=r[a];if("object"==typeof s&&(0,o.shouldUseRule)(e.schema,s)){const{type:r}=s.definition;r.length&&!r.some((e=>{return a=e,(r=t).includes(a)||"number"===a&&r.includes("integer");var r,a}))&&S(e,`missing type "${r.join(",")}" for keyword "${a}"`)}}}(e,e.dataTypes))}(e,t),s.block((()=>{for(const e of p.rules)y(e);y(p.post)}))):s.block((()=>j(e,"$ref",p.all.$ref.definition)))}function P(e,t){const{gen:r,schema:a,opts:{useDefaults:s}}=e;s&&(0,i.assignDefaults)(e,t.type),r.block((()=>{for(const r of t.rules)(0,o.shouldUseRule)(a,r)&&j(e,r.keyword,r.definition,t.type)}))}function E(e,t){return e.includes(t)||"integer"===t&&e.includes("number")}function S(e,t){t+=` at "${e.schemaEnv.baseId+e.errSchemaPath}" (strictTypes)`,(0,h.checkStrictMode)(e,t,e.opts.strictTypes)}t.validateFunctionCode=function(e){v(e)&&(g(e),$(e))?function(e){const{schema:t,opts:r,gen:a}=e;p(e,(()=>{r.$comment&&t.$comment&&w(e),function(e){const{schema:t,opts:r}=e;void 0!==t.default&&r.useDefaults&&r.strictSchema&&(0,h.checkStrictMode)(e,"default is ignored in the schema root")}(e),a.let(u.default.vErrors,null),a.let(u.default.errors,0),r.unevaluated&&function(e){const{gen:t,validateName:r}=e;e.evaluated=t.const("evaluated",l._`${r}.evaluated`),t.if(l._`${e.evaluated}.dynamicProps`,(()=>t.assign(l._`${e.evaluated}.props`,l._`undefined`))),t.if(l._`${e.evaluated}.dynamicItems`,(()=>t.assign(l._`${e.evaluated}.items`,l._`undefined`)))}(e),_(e),function(e){const{gen:t,schemaEnv:r,validateName:a,ValidationError:s,opts:o}=e;r.$async?t.if(l._`${u.default.errors} === 0`,(()=>t.return(u.default.data)),(()=>t.throw(l._`new ${s}(${u.default.vErrors})`))):(t.assign(l._`${a}.errors`,u.default.vErrors),o.unevaluated&&function({gen:e,evaluated:t,props:r,items:a}){r instanceof l.Name&&e.assign(l._`${t}.props`,r),a instanceof l.Name&&e.assign(l._`${t}.items`,a)}(e),t.return(l._`${u.default.errors} === 0`))}(e)}))}(e):p(e,(()=>(0,a.topBoolOrEmptySchema)(e)))};class k{constructor(e,t,r){if((0,c.validateKeywordUsage)(e,t,r),this.gen=e.gen,this.allErrors=e.allErrors,this.keyword=r,this.data=e.data,this.schema=e.schema[r],this.$data=t.$data&&e.opts.$data&&this.schema&&this.schema.$data,this.schemaValue=(0,h.schemaRefOrVal)(e,this.schema,r,this.$data),this.schemaType=t.schemaType,this.parentSchema=e.schema,this.params={},this.it=e,this.def=t,this.$data)this.schemaCode=e.gen.const("vSchema",T(this.$data,e));else if(this.schemaCode=this.schemaValue,!(0,c.validSchemaType)(this.schema,t.schemaType,t.allowUndefined))throw new Error(`${r} value must be ${JSON.stringify(t.schemaType)}`);("code"in t?t.trackErrors:!1!==t.errors)&&(this.errsCount=e.gen.const("_errs",u.default.errors))}result(e,t,r){this.failResult((0,l.not)(e),t,r)}failResult(e,t,r){this.gen.if(e),r?r():this.error(),t?(this.gen.else(),t(),this.allErrors&&this.gen.endIf()):this.allErrors?this.gen.endIf():this.gen.else()}pass(e,t){this.failResult((0,l.not)(e),void 0,t)}fail(e){if(void 0===e)return this.error(),void(this.allErrors||this.gen.if(!1));this.gen.if(e),this.error(),this.allErrors?this.gen.endIf():this.gen.else()}fail$data(e){if(!this.$data)return this.fail(e);const{schemaCode:t}=this;this.fail(l._`${t} !== undefined && (${(0,l.or)(this.invalid$data(),e)})`)}error(e,t,r){if(t)return this.setParams(t),this._error(e,r),void this.setParams({});this._error(e,r)}_error(e,t){(e?m.reportExtraError:m.reportError)(this,this.def.error,t)}$dataError(){(0,m.reportError)(this,this.def.$dataError||m.keyword$DataError)}reset(){if(void 0===this.errsCount)throw new Error('add "trackErrors" to keyword definition');(0,m.resetErrorsCount)(this.gen,this.errsCount)}ok(e){this.allErrors||this.gen.if(e)}setParams(e,t){t?Object.assign(this.params,e):this.params=e}block$data(e,t,r=l.nil){this.gen.block((()=>{this.check$data(e,r),t()}))}check$data(e=l.nil,t=l.nil){if(!this.$data)return;const{gen:r,schemaCode:a,schemaType:s,def:o}=this;r.if((0,l.or)(l._`${a} === undefined`,t)),e!==l.nil&&r.assign(e,!0),(s.length||o.validateSchema)&&(r.elseIf(this.invalid$data()),this.$dataError(),e!==l.nil&&r.assign(e,!1)),r.else()}invalid$data(){const{gen:e,schemaCode:t,schemaType:r,def:a,it:s}=this;return(0,l.or)(function(){if(r.length){if(!(t instanceof l.Name))throw new Error("ajv implementation error");const e=Array.isArray(r)?r:[r];return l._`${(0,n.checkDataTypes)(e,t,s.opts.strictNumbers,n.DataType.Wrong)}`}return l.nil}(),function(){if(a.validateSchema){const r=e.scopeValue("validate$data",{ref:a.validateSchema});return l._`!${r}(${t})`}return l.nil}())}subschema(e,t){const r=(0,d.getSubschema)(this.it,e);(0,d.extendSubschemaData)(r,this.it,e),(0,d.extendSubschemaMode)(r,e);const s={...this.it,...r,items:void 0,props:void 0};return function(e,t){v(e)&&(g(e),$(e))?function(e,t){const{schema:r,gen:a,opts:s}=e;s.$comment&&r.$comment&&w(e),function(e){const t=e.schema[e.opts.schemaId];t&&(e.baseId=(0,f.resolveUrl)(e.opts.uriResolver,e.baseId,t))}(e),function(e){if(e.schema.$async&&!e.schemaEnv.$async)throw new Error("async schema in sync schema")}(e);const o=a.const("_errs",u.default.errors);_(e,o),a.var(t,l._`${o} === ${u.default.errors}`)}(e,t):(0,a.boolOrEmptySchema)(e,t)}(s,t),s}mergeEvaluated(e,t){const{it:r,gen:a}=this;r.opts.unevaluated&&(!0!==r.props&&void 0!==e.props&&(r.props=h.mergeEvaluated.props(a,e.props,r.props,t)),!0!==r.items&&void 0!==e.items&&(r.items=h.mergeEvaluated.items(a,e.items,r.items,t)))}mergeValidEvaluated(e,t){const{it:r,gen:a}=this;if(r.opts.unevaluated&&(!0!==r.props||!0!==r.items))return a.if(t,(()=>this.mergeEvaluated(e,l.Name))),!0}}function j(e,t,r,a){const s=new k(e,r,t);"code"in r?r.code(s,a):s.$data&&r.validate?(0,c.funcKeywordCode)(s,r):"macro"in r?(0,c.macroKeywordCode)(s,r):(r.compile||r.validate)&&(0,c.funcKeywordCode)(s,r)}t.KeywordCxt=k;const N=/^\/(?:[^~]|~0|~1)*$/,O=/^([0-9]+)(#|\/(?:[^~]|~0|~1)*)?$/;function T(e,{dataLevel:t,dataNames:r,dataPathArr:a}){let s,o;if(""===e)return u.default.rootData;if("/"===e[0]){if(!N.test(e))throw new Error(`Invalid JSON-pointer: ${e}`);s=e,o=u.default.rootData}else{const n=O.exec(e);if(!n)throw new Error(`Invalid JSON-pointer: ${e}`);const i=+n[1];if(s=n[2],"#"===s){if(i>=t)throw new Error(c("property/index",i));return a[t-i]}if(i>t)throw new Error(c("data",i));if(o=r[t-i],!s)return o}let n=o;const i=s.split("/");for(const e of i)e&&(o=l._`${o}${(0,l.getProperty)((0,h.unescapeJsonPointer)(e))}`,n=l._`${n} && ${o}`);return n;function c(e,r){return`Cannot access ${e} ${r} levels up, current level is ${t}`}}t.getData=T},62591:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateKeywordUsage=t.validSchemaType=t.funcKeywordCode=t.macroKeywordCode=void 0;const a=r(17231),s=r(3948),o=r(36704),n=r(82263);function i(e){const{gen:t,data:r,it:s}=e;t.if(s.parentData,(()=>t.assign(r,a._`${s.parentData}[${s.parentDataProperty}]`)))}function c(e,t,r){if(void 0===r)throw new Error(`keyword "${t}" failed to compile`);return e.scopeValue("keyword","function"==typeof r?{ref:r}:{ref:r,code:(0,a.stringify)(r)})}t.macroKeywordCode=function(e,t){const{gen:r,keyword:s,schema:o,parentSchema:n,it:i}=e,d=t.macro.call(i.self,o,n,i),l=c(r,s,d);!1!==i.opts.validateSchema&&i.self.validateSchema(d,!0);const u=r.name("valid");e.subschema({schema:d,schemaPath:a.nil,errSchemaPath:`${i.errSchemaPath}/${s}`,topSchemaRef:l,compositeRule:!0},u),e.pass(u,(()=>e.error(!0)))},t.funcKeywordCode=function(e,t){var r;const{gen:d,keyword:l,schema:u,parentSchema:f,$data:h,it:m}=e;!function({schemaEnv:e},t){if(t.async&&!e.$async)throw new Error("async keyword in sync schema")}(m,t);const p=!h&&t.compile?t.compile.call(m.self,u,f,m):t.validate,y=c(d,l,p),$=d.let("valid");function v(r=(t.async?a._`await `:a.nil)){const n=m.opts.passContext?s.default.this:s.default.self,i=!("compile"in t&&!h||!1===t.schema);d.assign($,a._`${r}${(0,o.callValidateCode)(e,y,n,i)}`,t.modifying)}function g(e){var r;d.if((0,a.not)(null!==(r=t.valid)&&void 0!==r?r:$),e)}e.block$data($,(function(){if(!1===t.errors)v(),t.modifying&&i(e),g((()=>e.error()));else{const r=t.async?function(){const e=d.let("ruleErrs",null);return d.try((()=>v(a._`await `)),(t=>d.assign($,!1).if(a._`${t} instanceof ${m.ValidationError}`,(()=>d.assign(e,a._`${t}.errors`)),(()=>d.throw(t))))),e}():function(){const e=a._`${y}.errors`;return d.assign(e,null),v(a.nil),e}();t.modifying&&i(e),g((()=>function(e,t){const{gen:r}=e;r.if(a._`Array.isArray(${t})`,(()=>{r.assign(s.default.vErrors,a._`${s.default.vErrors} === null ? ${t} : ${s.default.vErrors}.concat(${t})`).assign(s.default.errors,a._`${s.default.vErrors}.length`),(0,n.extendErrors)(e)}),(()=>e.error()))}(e,r)))}})),e.ok(null!==(r=t.valid)&&void 0!==r?r:$)},t.validSchemaType=function(e,t,r=!1){return!t.length||t.some((t=>"array"===t?Array.isArray(e):"object"===t?e&&"object"==typeof e&&!Array.isArray(e):typeof e==t||r&&void 0===e))},t.validateKeywordUsage=function({schema:e,opts:t,self:r,errSchemaPath:a},s,o){if(Array.isArray(s.keyword)?!s.keyword.includes(o):s.keyword!==o)throw new Error("ajv implementation error");const n=s.dependencies;if(null==n?void 0:n.some((t=>!Object.prototype.hasOwnProperty.call(e,t))))throw new Error(`parent schema must have dependencies of ${o}: ${n.join(",")}`);if(s.validateSchema&&!s.validateSchema(e[o])){const e=`keyword "${o}" value is invalid at path "${a}": `+r.errorsText(s.validateSchema.errors);if("log"!==t.validateSchema)throw new Error(e);r.logger.error(e)}}},87218:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.extendSubschemaMode=t.extendSubschemaData=t.getSubschema=void 0;const a=r(17231),s=r(20679);t.getSubschema=function(e,{keyword:t,schemaProp:r,schema:o,schemaPath:n,errSchemaPath:i,topSchemaRef:c}){if(void 0!==t&&void 0!==o)throw new Error('both "keyword" and "schema" passed, only one allowed');if(void 0!==t){const o=e.schema[t];return void 0===r?{schema:o,schemaPath:a._`${e.schemaPath}${(0,a.getProperty)(t)}`,errSchemaPath:`${e.errSchemaPath}/${t}`}:{schema:o[r],schemaPath:a._`${e.schemaPath}${(0,a.getProperty)(t)}${(0,a.getProperty)(r)}`,errSchemaPath:`${e.errSchemaPath}/${t}/${(0,s.escapeFragment)(r)}`}}if(void 0!==o){if(void 0===n||void 0===i||void 0===c)throw new Error('"schemaPath", "errSchemaPath" and "topSchemaRef" are required with "schema"');return{schema:o,schemaPath:n,topSchemaRef:c,errSchemaPath:i}}throw new Error('either "keyword" or "schema" must be passed')},t.extendSubschemaData=function(e,t,{dataProp:r,dataPropType:o,data:n,dataTypes:i,propertyName:c}){if(void 0!==n&&void 0!==r)throw new Error('both "data" and "dataProp" passed, only one allowed');const{gen:d}=t;if(void 0!==r){const{errorPath:n,dataPathArr:i,opts:c}=t;l(d.let("data",a._`${t.data}${(0,a.getProperty)(r)}`,!0)),e.errorPath=a.str`${n}${(0,s.getErrorPath)(r,o,c.jsPropertySyntax)}`,e.parentDataProperty=a._`${r}`,e.dataPathArr=[...i,e.parentDataProperty]}function l(r){e.data=r,e.dataLevel=t.dataLevel+1,e.dataTypes=[],t.definedProperties=new Set,e.parentData=t.data,e.dataNames=[...t.dataNames,r]}void 0!==n&&(l(n instanceof a.Name?n:d.let("data",n,!0)),void 0!==c&&(e.propertyName=c)),i&&(e.dataTypes=i)},t.extendSubschemaMode=function(e,{jtdDiscriminator:t,jtdMetadata:r,compositeRule:a,createErrors:s,allErrors:o}){void 0!==a&&(e.compositeRule=a),void 0!==s&&(e.createErrors=s),void 0!==o&&(e.allErrors=o),e.jtdDiscriminator=t,e.jtdMetadata=r}},98955:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CodeGen=t.Name=t.nil=t.stringify=t.str=t._=t.KeywordCxt=void 0;var a=r(40764);Object.defineProperty(t,"KeywordCxt",{enumerable:!0,get:function(){return a.KeywordCxt}});var s=r(17231);Object.defineProperty(t,"_",{enumerable:!0,get:function(){return s._}}),Object.defineProperty(t,"str",{enumerable:!0,get:function(){return s.str}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return s.stringify}}),Object.defineProperty(t,"nil",{enumerable:!0,get:function(){return s.nil}}),Object.defineProperty(t,"Name",{enumerable:!0,get:function(){return s.Name}}),Object.defineProperty(t,"CodeGen",{enumerable:!0,get:function(){return s.CodeGen}});const o=r(17766),n=r(26317),i=r(24623),c=r(50052),d=r(17231),l=r(33761),u=r(85077),f=r(20679),h=r(94801),m=r(42658),p=(e,t)=>new RegExp(e,t);p.code="new RegExp";const y=["removeAdditional","useDefaults","coerceTypes"],$=new Set(["validate","serialize","parse","wrapper","root","schema","keyword","pattern","formats","validate$data","func","obj","Error"]),v={errorDataPath:"",format:"`validateFormats: false` can be used instead.",nullable:'"nullable" keyword is supported by default.',jsonPointers:"Deprecated jsPropertySyntax can be used instead.",extendRefs:"Deprecated ignoreKeywordsWithRef can be used instead.",missingRefs:"Pass empty schema with $id that should be ignored to ajv.addSchema.",processCode:"Use option `code: {process: (code, schemaEnv: object) => string}`",sourceCode:"Use option `code: {source: true}`",strictDefaults:"It is default now, see option `strict`.",strictKeywords:"It is default now, see option `strict`.",uniqueItems:'"uniqueItems" keyword is always validated.',unknownFormats:"Disable strict mode or pass `true` to `ajv.addFormat` (or `formats` option).",cache:"Map is used as cache, schema object as key.",serialize:"Map is used as cache, schema object as key.",ajvErrors:"It is default now."},g={ignoreKeywordsWithRef:"",jsPropertySyntax:"",unicode:'"minLength"/"maxLength" account for unicode characters by default.'};function _(e){var t,r,a,s,o,n,i,c,d,l,u,f,h,y,$,v,g,_,w,b,P,E,S,k,j;const N=e.strict,O=null===(t=e.code)||void 0===t?void 0:t.optimize,T=!0===O||void 0===O?1:O||0,x=null!==(a=null===(r=e.code)||void 0===r?void 0:r.regExp)&&void 0!==a?a:p,R=null!==(s=e.uriResolver)&&void 0!==s?s:m.default;return{strictSchema:null===(n=null!==(o=e.strictSchema)&&void 0!==o?o:N)||void 0===n||n,strictNumbers:null===(c=null!==(i=e.strictNumbers)&&void 0!==i?i:N)||void 0===c||c,strictTypes:null!==(l=null!==(d=e.strictTypes)&&void 0!==d?d:N)&&void 0!==l?l:"log",strictTuples:null!==(f=null!==(u=e.strictTuples)&&void 0!==u?u:N)&&void 0!==f?f:"log",strictRequired:null!==(y=null!==(h=e.strictRequired)&&void 0!==h?h:N)&&void 0!==y&&y,code:e.code?{...e.code,optimize:T,regExp:x}:{optimize:T,regExp:x},loopRequired:null!==($=e.loopRequired)&&void 0!==$?$:200,loopEnum:null!==(v=e.loopEnum)&&void 0!==v?v:200,meta:null===(g=e.meta)||void 0===g||g,messages:null===(_=e.messages)||void 0===_||_,inlineRefs:null===(w=e.inlineRefs)||void 0===w||w,schemaId:null!==(b=e.schemaId)&&void 0!==b?b:"$id",addUsedSchema:null===(P=e.addUsedSchema)||void 0===P||P,validateSchema:null===(E=e.validateSchema)||void 0===E||E,validateFormats:null===(S=e.validateFormats)||void 0===S||S,unicodeRegExp:null===(k=e.unicodeRegExp)||void 0===k||k,int32range:null===(j=e.int32range)||void 0===j||j,uriResolver:R}}class w{constructor(e={}){this.schemas={},this.refs={},this.formats={},this._compilations=new Set,this._loading={},this._cache=new Map,e=this.opts={...e,..._(e)};const{es5:t,lines:r}=this.opts.code;this.scope=new d.ValueScope({scope:{},prefixes:$,es5:t,lines:r}),this.logger=function(e){if(!1===e)return N;if(void 0===e)return console;if(e.log&&e.warn&&e.error)return e;throw new Error("logger must implement log, warn and error methods")}(e.logger);const a=e.validateFormats;e.validateFormats=!1,this.RULES=(0,i.getRules)(),b.call(this,v,e,"NOT SUPPORTED"),b.call(this,g,e,"DEPRECATED","warn"),this._metaOpts=j.call(this),e.formats&&S.call(this),this._addVocabularies(),this._addDefaultMetaSchema(),e.keywords&&k.call(this,e.keywords),"object"==typeof e.meta&&this.addMetaSchema(e.meta),E.call(this),e.validateFormats=a}_addVocabularies(){this.addKeyword("$async")}_addDefaultMetaSchema(){const{$data:e,meta:t,schemaId:r}=this.opts;let a=h;"id"===r&&(a={...h},a.id=a.$id,delete a.$id),t&&e&&this.addMetaSchema(a,a[r],!1)}defaultMeta(){const{meta:e,schemaId:t}=this.opts;return this.opts.defaultMeta="object"==typeof e?e[t]||e:void 0}validate(e,t){let r;if("string"==typeof e){if(r=this.getSchema(e),!r)throw new Error(`no schema with key or ref "${e}"`)}else r=this.compile(e);const a=r(t);return"$async"in r||(this.errors=r.errors),a}compile(e,t){const r=this._addSchema(e,t);return r.validate||this._compileSchemaEnv(r)}compileAsync(e,t){if("function"!=typeof this.opts.loadSchema)throw new Error("options.loadSchema should be a function");const{loadSchema:r}=this.opts;return a.call(this,e,t);async function a(e,t){await s.call(this,e.$schema);const r=this._addSchema(e,t);return r.validate||o.call(this,r)}async function s(e){e&&!this.getSchema(e)&&await a.call(this,{$ref:e},!0)}async function o(e){try{return this._compileSchemaEnv(e)}catch(t){if(!(t instanceof n.default))throw t;return i.call(this,t),await c.call(this,t.missingSchema),o.call(this,e)}}function i({missingSchema:e,missingRef:t}){if(this.refs[e])throw new Error(`AnySchema ${e} is loaded but ${t} cannot be resolved`)}async function c(e){const r=await d.call(this,e);this.refs[e]||await s.call(this,r.$schema),this.refs[e]||this.addSchema(r,e,t)}async function d(e){const t=this._loading[e];if(t)return t;try{return await(this._loading[e]=r(e))}finally{delete this._loading[e]}}}addSchema(e,t,r,a=this.opts.validateSchema){if(Array.isArray(e)){for(const t of e)this.addSchema(t,void 0,r,a);return this}let s;if("object"==typeof e){const{schemaId:t}=this.opts;if(s=e[t],void 0!==s&&"string"!=typeof s)throw new Error(`schema ${t} must be string`)}return t=(0,l.normalizeId)(t||s),this._checkUnique(t),this.schemas[t]=this._addSchema(e,r,t,a,!0),this}addMetaSchema(e,t,r=this.opts.validateSchema){return this.addSchema(e,t,!0,r),this}validateSchema(e,t){if("boolean"==typeof e)return!0;let r;if(r=e.$schema,void 0!==r&&"string"!=typeof r)throw new Error("$schema must be a string");if(r=r||this.opts.defaultMeta||this.defaultMeta(),!r)return this.logger.warn("meta-schema not available"),this.errors=null,!0;const a=this.validate(r,e);if(!a&&t){const e="schema is invalid: "+this.errorsText();if("log"!==this.opts.validateSchema)throw new Error(e);this.logger.error(e)}return a}getSchema(e){let t;for(;"string"==typeof(t=P.call(this,e));)e=t;if(void 0===t){const{schemaId:r}=this.opts,a=new c.SchemaEnv({schema:{},schemaId:r});if(t=c.resolveSchema.call(this,a,e),!t)return;this.refs[e]=t}return t.validate||this._compileSchemaEnv(t)}removeSchema(e){if(e instanceof RegExp)return this._removeAllSchemas(this.schemas,e),this._removeAllSchemas(this.refs,e),this;switch(typeof e){case"undefined":return this._removeAllSchemas(this.schemas),this._removeAllSchemas(this.refs),this._cache.clear(),this;case"string":{const t=P.call(this,e);return"object"==typeof t&&this._cache.delete(t.schema),delete this.schemas[e],delete this.refs[e],this}case"object":{const t=e;this._cache.delete(t);let r=e[this.opts.schemaId];return r&&(r=(0,l.normalizeId)(r),delete this.schemas[r],delete this.refs[r]),this}default:throw new Error("ajv.removeSchema: invalid parameter")}}addVocabulary(e){for(const t of e)this.addKeyword(t);return this}addKeyword(e,t){let r;if("string"==typeof e)r=e,"object"==typeof t&&(this.logger.warn("these parameters are deprecated, see docs for addKeyword"),t.keyword=r);else{if("object"!=typeof e||void 0!==t)throw new Error("invalid addKeywords parameters");if(r=(t=e).keyword,Array.isArray(r)&&!r.length)throw new Error("addKeywords: keyword must be string or non-empty array")}if(T.call(this,r,t),!t)return(0,f.eachItem)(r,(e=>x.call(this,e))),this;C.call(this,t);const a={...t,type:(0,u.getJSONTypes)(t.type),schemaType:(0,u.getJSONTypes)(t.schemaType)};return(0,f.eachItem)(r,0===a.type.length?e=>x.call(this,e,a):e=>a.type.forEach((t=>x.call(this,e,a,t)))),this}getKeyword(e){const t=this.RULES.all[e];return"object"==typeof t?t.definition:!!t}removeKeyword(e){const{RULES:t}=this;delete t.keywords[e],delete t.all[e];for(const r of t.rules){const t=r.rules.findIndex((t=>t.keyword===e));t>=0&&r.rules.splice(t,1)}return this}addFormat(e,t){return"string"==typeof t&&(t=new RegExp(t)),this.formats[e]=t,this}errorsText(e=this.errors,{separator:t=", ",dataVar:r="data"}={}){return e&&0!==e.length?e.map((e=>`${r}${e.instancePath} ${e.message}`)).reduce(((e,r)=>e+t+r)):"No errors"}$dataMetaSchema(e,t){const r=this.RULES.all;e=JSON.parse(JSON.stringify(e));for(const a of t){const t=a.split("/").slice(1);let s=e;for(const e of t)s=s[e];for(const e in r){const t=r[e];if("object"!=typeof t)continue;const{$data:a}=t.definition,o=s[e];a&&o&&(s[e]=M(o))}}return e}_removeAllSchemas(e,t){for(const r in e){const a=e[r];t&&!t.test(r)||("string"==typeof a?delete e[r]:a&&!a.meta&&(this._cache.delete(a.schema),delete e[r]))}}_addSchema(e,t,r,a=this.opts.validateSchema,s=this.opts.addUsedSchema){let o;const{schemaId:n}=this.opts;if("object"==typeof e)o=e[n];else{if(this.opts.jtd)throw new Error("schema must be object");if("boolean"!=typeof e)throw new Error("schema must be object or boolean")}let i=this._cache.get(e);if(void 0!==i)return i;r=(0,l.normalizeId)(o||r);const d=l.getSchemaRefs.call(this,e,r);return i=new c.SchemaEnv({schema:e,schemaId:n,meta:t,baseId:r,localRefs:d}),this._cache.set(i.schema,i),s&&!r.startsWith("#")&&(r&&this._checkUnique(r),this.refs[r]=i),a&&this.validateSchema(e,!0),i}_checkUnique(e){if(this.schemas[e]||this.refs[e])throw new Error(`schema with key or id "${e}" already exists`)}_compileSchemaEnv(e){if(e.meta?this._compileMetaSchema(e):c.compileSchema.call(this,e),!e.validate)throw new Error("ajv implementation error");return e.validate}_compileMetaSchema(e){const t=this.opts;this.opts=this._metaOpts;try{c.compileSchema.call(this,e)}finally{this.opts=t}}}function b(e,t,r,a="error"){for(const s in e){const o=s;o in t&&this.logger[a](`${r}: option ${s}. ${e[o]}`)}}function P(e){return e=(0,l.normalizeId)(e),this.schemas[e]||this.refs[e]}function E(){const e=this.opts.schemas;if(e)if(Array.isArray(e))this.addSchema(e);else for(const t in e)this.addSchema(e[t],t)}function S(){for(const e in this.opts.formats){const t=this.opts.formats[e];t&&this.addFormat(e,t)}}function k(e){if(Array.isArray(e))this.addVocabulary(e);else{this.logger.warn("keywords option as map is deprecated, pass array");for(const t in e){const r=e[t];r.keyword||(r.keyword=t),this.addKeyword(r)}}}function j(){const e={...this.opts};for(const t of y)delete e[t];return e}t.default=w,w.ValidationError=o.default,w.MissingRefError=n.default;const N={log(){},warn(){},error(){}},O=/^[a-z_$][a-z0-9_$:-]*$/i;function T(e,t){const{RULES:r}=this;if((0,f.eachItem)(e,(e=>{if(r.keywords[e])throw new Error(`Keyword ${e} is already defined`);if(!O.test(e))throw new Error(`Keyword ${e} has invalid name`)})),t&&t.$data&&!("code"in t)&&!("validate"in t))throw new Error('$data keyword must have "code" or "validate" function')}function x(e,t,r){var a;const s=null==t?void 0:t.post;if(r&&s)throw new Error('keyword with "post" flag cannot have "type"');const{RULES:o}=this;let n=s?o.post:o.rules.find((({type:e})=>e===r));if(n||(n={type:r,rules:[]},o.rules.push(n)),o.keywords[e]=!0,!t)return;const i={keyword:e,definition:{...t,type:(0,u.getJSONTypes)(t.type),schemaType:(0,u.getJSONTypes)(t.schemaType)}};t.before?R.call(this,n,i,t.before):n.rules.push(i),o.all[e]=i,null===(a=t.implements)||void 0===a||a.forEach((e=>this.addKeyword(e)))}function R(e,t,r){const a=e.rules.findIndex((e=>e.keyword===r));a>=0?e.rules.splice(a,0,t):(e.rules.push(t),this.logger.warn(`rule ${r} is not defined`))}function C(e){let{metaSchema:t}=e;void 0!==t&&(e.$data&&this.opts.$data&&(t=M(t)),e.validateSchema=this.compile(t,!0))}const I={$ref:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#"};function M(e){return{anyOf:[e,I]}}},59586:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(66471);a.code='require("ajv/dist/runtime/equal").default',t.default=a},20680:(e,t)=>{"use strict";function r(e){const t=e.length;let r,a=0,s=0;for(;s<t;)a++,r=e.charCodeAt(s++),r>=55296&&r<=56319&&s<t&&(r=e.charCodeAt(s),56320==(64512&r)&&s++);return a}Object.defineProperty(t,"__esModule",{value:!0}),t.default=r,r.code='require("ajv/dist/runtime/ucs2length").default'},42658:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(22371);a.code='require("ajv/dist/runtime/uri").default',t.default=a},17766:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});class r extends Error{constructor(e){super("validation failed"),this.errors=e,this.ajv=this.validation=!0}}t.default=r},28145:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateAdditionalItems=void 0;const a=r(17231),s=r(20679),o={keyword:"additionalItems",type:"array",schemaType:["boolean","object"],before:"uniqueItems",error:{message:({params:{len:e}})=>a.str`must NOT have more than ${e} items`,params:({params:{len:e}})=>a._`{limit: ${e}}`},code(e){const{parentSchema:t,it:r}=e,{items:a}=t;Array.isArray(a)?n(e,a):(0,s.checkStrictMode)(r,'"additionalItems" is ignored when "items" is not an array of schemas')}};function n(e,t){const{gen:r,schema:o,data:n,keyword:i,it:c}=e;c.items=!0;const d=r.const("len",a._`${n}.length`);if(!1===o)e.setParams({len:t.length}),e.pass(a._`${d} <= ${t.length}`);else if("object"==typeof o&&!(0,s.alwaysValidSchema)(c,o)){const o=r.var("valid",a._`${d} <= ${t.length}`);r.if((0,a.not)(o),(()=>function(o){r.forRange("i",t.length,d,(t=>{e.subschema({keyword:i,dataProp:t,dataPropType:s.Type.Num},o),c.allErrors||r.if((0,a.not)(o),(()=>r.break()))}))}(o))),e.ok(o)}}t.validateAdditionalItems=n,t.default=o},2888:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(36704),s=r(17231),o=r(3948),n=r(20679),i={keyword:"additionalProperties",type:["object"],schemaType:["boolean","object"],allowUndefined:!0,trackErrors:!0,error:{message:"must NOT have additional properties",params:({params:e})=>s._`{additionalProperty: ${e.additionalProperty}}`},code(e){const{gen:t,schema:r,parentSchema:i,data:c,errsCount:d,it:l}=e;if(!d)throw new Error("ajv implementation error");const{allErrors:u,opts:f}=l;if(l.props=!0,"all"!==f.removeAdditional&&(0,n.alwaysValidSchema)(l,r))return;const h=(0,a.allSchemaProperties)(i.properties),m=(0,a.allSchemaProperties)(i.patternProperties);function p(e){t.code(s._`delete ${c}[${e}]`)}function y(a){if("all"===f.removeAdditional||f.removeAdditional&&!1===r)p(a);else{if(!1===r)return e.setParams({additionalProperty:a}),e.error(),void(u||t.break());if("object"==typeof r&&!(0,n.alwaysValidSchema)(l,r)){const r=t.name("valid");"failing"===f.removeAdditional?($(a,r,!1),t.if((0,s.not)(r),(()=>{e.reset(),p(a)}))):($(a,r),u||t.if((0,s.not)(r),(()=>t.break())))}}}function $(t,r,a){const s={keyword:"additionalProperties",dataProp:t,dataPropType:n.Type.Str};!1===a&&Object.assign(s,{compositeRule:!0,createErrors:!1,allErrors:!1}),e.subschema(s,r)}t.forIn("key",c,(r=>{h.length||m.length?t.if(function(r){let o;if(h.length>8){const e=(0,n.schemaRefOrVal)(l,i.properties,"properties");o=(0,a.isOwnProperty)(t,e,r)}else o=h.length?(0,s.or)(...h.map((e=>s._`${r} === ${e}`))):s.nil;return m.length&&(o=(0,s.or)(o,...m.map((t=>s._`${(0,a.usePattern)(e,t)}.test(${r})`)))),(0,s.not)(o)}(r),(()=>y(r))):y(r)})),e.ok(s._`${d} === ${o.default.errors}`)}};t.default=i},48402:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(20679),s={keyword:"allOf",schemaType:"array",code(e){const{gen:t,schema:r,it:s}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");const o=t.name("valid");r.forEach(((t,r)=>{if((0,a.alwaysValidSchema)(s,t))return;const n=e.subschema({keyword:"allOf",schemaProp:r},o);e.ok(o),e.mergeEvaluated(n)}))}};t.default=s},82709:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a={keyword:"anyOf",schemaType:"array",trackErrors:!0,code:r(36704).validateUnion,error:{message:"must match a schema in anyOf"}};t.default=a},54175:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(17231),s=r(20679),o={keyword:"contains",type:"array",schemaType:["object","boolean"],before:"uniqueItems",trackErrors:!0,error:{message:({params:{min:e,max:t}})=>void 0===t?a.str`must contain at least ${e} valid item(s)`:a.str`must contain at least ${e} and no more than ${t} valid item(s)`,params:({params:{min:e,max:t}})=>void 0===t?a._`{minContains: ${e}}`:a._`{minContains: ${e}, maxContains: ${t}}`},code(e){const{gen:t,schema:r,parentSchema:o,data:n,it:i}=e;let c,d;const{minContains:l,maxContains:u}=o;i.opts.next?(c=void 0===l?1:l,d=u):c=1;const f=t.const("len",a._`${n}.length`);if(e.setParams({min:c,max:d}),void 0===d&&0===c)return void(0,s.checkStrictMode)(i,'"minContains" == 0 without "maxContains": "contains" keyword ignored');if(void 0!==d&&c>d)return(0,s.checkStrictMode)(i,'"minContains" > "maxContains" is always invalid'),void e.fail();if((0,s.alwaysValidSchema)(i,r)){let t=a._`${f} >= ${c}`;return void 0!==d&&(t=a._`${t} && ${f} <= ${d}`),void e.pass(t)}i.items=!0;const h=t.name("valid");function m(){const e=t.name("_valid"),r=t.let("count",0);p(e,(()=>t.if(e,(()=>function(e){t.code(a._`${e}++`),void 0===d?t.if(a._`${e} >= ${c}`,(()=>t.assign(h,!0).break())):(t.if(a._`${e} > ${d}`,(()=>t.assign(h,!1).break())),1===c?t.assign(h,!0):t.if(a._`${e} >= ${c}`,(()=>t.assign(h,!0))))}(r)))))}function p(r,a){t.forRange("i",0,f,(t=>{e.subschema({keyword:"contains",dataProp:t,dataPropType:s.Type.Num,compositeRule:!0},r),a()}))}void 0===d&&1===c?p(h,(()=>t.if(h,(()=>t.break())))):0===c?(t.let(h,!0),void 0!==d&&t.if(a._`${n}.length > 0`,m)):(t.let(h,!1),m()),e.result(h,(()=>e.reset()))}};t.default=o},87747:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateSchemaDeps=t.validatePropertyDeps=t.error=void 0;const a=r(17231),s=r(20679),o=r(36704);t.error={message:({params:{property:e,depsCount:t,deps:r}})=>{const s=1===t?"property":"properties";return a.str`must have ${s} ${r} when property ${e} is present`},params:({params:{property:e,depsCount:t,deps:r,missingProperty:s}})=>a._`{property: ${e},
    missingProperty: ${s},
    depsCount: ${t},
    deps: ${r}}`};const n={keyword:"dependencies",type:"object",schemaType:"object",error:t.error,code(e){const[t,r]=function({schema:e}){const t={},r={};for(const a in e)"__proto__"!==a&&((Array.isArray(e[a])?t:r)[a]=e[a]);return[t,r]}(e);i(e,t),c(e,r)}};function i(e,t=e.schema){const{gen:r,data:s,it:n}=e;if(0===Object.keys(t).length)return;const i=r.let("missing");for(const c in t){const d=t[c];if(0===d.length)continue;const l=(0,o.propertyInData)(r,s,c,n.opts.ownProperties);e.setParams({property:c,depsCount:d.length,deps:d.join(", ")}),n.allErrors?r.if(l,(()=>{for(const t of d)(0,o.checkReportMissingProp)(e,t)})):(r.if(a._`${l} && (${(0,o.checkMissingProp)(e,d,i)})`),(0,o.reportMissingProp)(e,i),r.else())}}function c(e,t=e.schema){const{gen:r,data:a,keyword:n,it:i}=e,c=r.name("valid");for(const d in t)(0,s.alwaysValidSchema)(i,t[d])||(r.if((0,o.propertyInData)(r,a,d,i.opts.ownProperties),(()=>{const t=e.subschema({keyword:n,schemaProp:d},c);e.mergeValidEvaluated(t,c)}),(()=>r.var(c,!0))),e.ok(c))}t.validatePropertyDeps=i,t.validateSchemaDeps=c,t.default=n},76441:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(17231),s=r(20679),o={keyword:"if",schemaType:["object","boolean"],trackErrors:!0,error:{message:({params:e})=>a.str`must match "${e.ifClause}" schema`,params:({params:e})=>a._`{failingKeyword: ${e.ifClause}}`},code(e){const{gen:t,parentSchema:r,it:o}=e;void 0===r.then&&void 0===r.else&&(0,s.checkStrictMode)(o,'"if" without "then" and "else" is ignored');const i=n(o,"then"),c=n(o,"else");if(!i&&!c)return;const d=t.let("valid",!0),l=t.name("_valid");if(function(){const t=e.subschema({keyword:"if",compositeRule:!0,createErrors:!1,allErrors:!1},l);e.mergeEvaluated(t)}(),e.reset(),i&&c){const r=t.let("ifClause");e.setParams({ifClause:r}),t.if(l,u("then",r),u("else",r))}else i?t.if(l,u("then")):t.if((0,a.not)(l),u("else"));function u(r,s){return()=>{const o=e.subschema({keyword:r},l);t.assign(d,l),e.mergeValidEvaluated(o,d),s?t.assign(s,a._`${r}`):e.setParams({ifClause:r})}}e.pass(d,(()=>e.error(!0)))}};function n(e,t){const r=e.schema[t];return void 0!==r&&!(0,s.alwaysValidSchema)(e,r)}t.default=o},75448:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(28145),s=r(97903),o=r(96499),n=r(53493),i=r(54175),c=r(87747),d=r(28548),l=r(2888),u=r(5245),f=r(76797),h=r(19136),m=r(82709),p=r(18730),y=r(48402),$=r(76441),v=r(95671);t.default=function(e=!1){const t=[h.default,m.default,p.default,y.default,$.default,v.default,d.default,l.default,c.default,u.default,f.default];return e?t.push(s.default,n.default):t.push(a.default,o.default),t.push(i.default),t}},96499:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateTuple=void 0;const a=r(17231),s=r(20679),o=r(36704),n={keyword:"items",type:"array",schemaType:["object","array","boolean"],before:"uniqueItems",code(e){const{schema:t,it:r}=e;if(Array.isArray(t))return i(e,"additionalItems",t);r.items=!0,(0,s.alwaysValidSchema)(r,t)||e.ok((0,o.validateArray)(e))}};function i(e,t,r=e.schema){const{gen:o,parentSchema:n,data:i,keyword:c,it:d}=e;!function(e){const{opts:a,errSchemaPath:o}=d,n=r.length,i=n===e.minItems&&(n===e.maxItems||!1===e[t]);if(a.strictTuples&&!i){const e=`"${c}" is ${n}-tuple, but minItems or maxItems/${t} are not specified or different at path "${o}"`;(0,s.checkStrictMode)(d,e,a.strictTuples)}}(n),d.opts.unevaluated&&r.length&&!0!==d.items&&(d.items=s.mergeEvaluated.items(o,r.length,d.items));const l=o.name("valid"),u=o.const("len",a._`${i}.length`);r.forEach(((t,r)=>{(0,s.alwaysValidSchema)(d,t)||(o.if(a._`${u} > ${r}`,(()=>e.subschema({keyword:c,schemaProp:r,dataProp:r},l))),e.ok(l))}))}t.validateTuple=i,t.default=n},53493:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(17231),s=r(20679),o=r(36704),n=r(28145),i={keyword:"items",type:"array",schemaType:["object","boolean"],before:"uniqueItems",error:{message:({params:{len:e}})=>a.str`must NOT have more than ${e} items`,params:({params:{len:e}})=>a._`{limit: ${e}}`},code(e){const{schema:t,parentSchema:r,it:a}=e,{prefixItems:i}=r;a.items=!0,(0,s.alwaysValidSchema)(a,t)||(i?(0,n.validateAdditionalItems)(e,i):e.ok((0,o.validateArray)(e)))}};t.default=i},19136:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(20679),s={keyword:"not",schemaType:["object","boolean"],trackErrors:!0,code(e){const{gen:t,schema:r,it:s}=e;if((0,a.alwaysValidSchema)(s,r))return void e.fail();const o=t.name("valid");e.subschema({keyword:"not",compositeRule:!0,createErrors:!1,allErrors:!1},o),e.failResult(o,(()=>e.reset()),(()=>e.error()))},error:{message:"must NOT be valid"}};t.default=s},18730:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(17231),s=r(20679),o={keyword:"oneOf",schemaType:"array",trackErrors:!0,error:{message:"must match exactly one schema in oneOf",params:({params:e})=>a._`{passingSchemas: ${e.passing}}`},code(e){const{gen:t,schema:r,parentSchema:o,it:n}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");if(n.opts.discriminator&&o.discriminator)return;const i=r,c=t.let("valid",!1),d=t.let("passing",null),l=t.name("_valid");e.setParams({passing:d}),t.block((function(){i.forEach(((r,o)=>{let i;(0,s.alwaysValidSchema)(n,r)?t.var(l,!0):i=e.subschema({keyword:"oneOf",schemaProp:o,compositeRule:!0},l),o>0&&t.if(a._`${l} && ${c}`).assign(c,!1).assign(d,a._`[${d}, ${o}]`).else(),t.if(l,(()=>{t.assign(c,!0),t.assign(d,o),i&&e.mergeEvaluated(i,a.Name)}))}))})),e.result(c,(()=>e.reset()),(()=>e.error(!0)))}};t.default=o},76797:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(36704),s=r(17231),o=r(20679),n=r(20679),i={keyword:"patternProperties",type:"object",schemaType:"object",code(e){const{gen:t,schema:r,data:i,parentSchema:c,it:d}=e,{opts:l}=d,u=(0,a.allSchemaProperties)(r),f=u.filter((e=>(0,o.alwaysValidSchema)(d,r[e])));if(0===u.length||f.length===u.length&&(!d.opts.unevaluated||!0===d.props))return;const h=l.strictSchema&&!l.allowMatchingProperties&&c.properties,m=t.name("valid");!0===d.props||d.props instanceof s.Name||(d.props=(0,n.evaluatedPropsToName)(t,d.props));const{props:p}=d;function y(e){for(const t in h)new RegExp(e).test(t)&&(0,o.checkStrictMode)(d,`property ${t} matches pattern ${e} (use allowMatchingProperties)`)}function $(r){t.forIn("key",i,(o=>{t.if(s._`${(0,a.usePattern)(e,r)}.test(${o})`,(()=>{const a=f.includes(r);a||e.subschema({keyword:"patternProperties",schemaProp:r,dataProp:o,dataPropType:n.Type.Str},m),d.opts.unevaluated&&!0!==p?t.assign(s._`${p}[${o}]`,!0):a||d.allErrors||t.if((0,s.not)(m),(()=>t.break()))}))}))}!function(){for(const e of u)h&&y(e),d.allErrors?$(e):(t.var(m,!0),$(e),t.if(m))}()}};t.default=i},97903:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(96499),s={keyword:"prefixItems",type:"array",schemaType:["array"],before:"uniqueItems",code:e=>(0,a.validateTuple)(e,"items")};t.default=s},5245:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(40764),s=r(36704),o=r(20679),n=r(2888),i={keyword:"properties",type:"object",schemaType:"object",code(e){const{gen:t,schema:r,parentSchema:i,data:c,it:d}=e;"all"===d.opts.removeAdditional&&void 0===i.additionalProperties&&n.default.code(new a.KeywordCxt(d,n.default,"additionalProperties"));const l=(0,s.allSchemaProperties)(r);for(const e of l)d.definedProperties.add(e);d.opts.unevaluated&&l.length&&!0!==d.props&&(d.props=o.mergeEvaluated.props(t,(0,o.toHash)(l),d.props));const u=l.filter((e=>!(0,o.alwaysValidSchema)(d,r[e])));if(0===u.length)return;const f=t.name("valid");for(const r of u)h(r)?m(r):(t.if((0,s.propertyInData)(t,c,r,d.opts.ownProperties)),m(r),d.allErrors||t.else().var(f,!0),t.endIf()),e.it.definedProperties.add(r),e.ok(f);function h(e){return d.opts.useDefaults&&!d.compositeRule&&void 0!==r[e].default}function m(t){e.subschema({keyword:"properties",schemaProp:t,dataProp:t},f)}}};t.default=i},28548:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(17231),s=r(20679),o={keyword:"propertyNames",type:"object",schemaType:["object","boolean"],error:{message:"property name must be valid",params:({params:e})=>a._`{propertyName: ${e.propertyName}}`},code(e){const{gen:t,schema:r,data:o,it:n}=e;if((0,s.alwaysValidSchema)(n,r))return;const i=t.name("valid");t.forIn("key",o,(r=>{e.setParams({propertyName:r}),e.subschema({keyword:"propertyNames",data:r,dataTypes:["string"],propertyName:r,compositeRule:!0},i),t.if((0,a.not)(i),(()=>{e.error(!0),n.allErrors||t.break()}))})),e.ok(i)}};t.default=o},95671:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(20679),s={keyword:["then","else"],schemaType:["object","boolean"],code({keyword:e,parentSchema:t,it:r}){void 0===t.if&&(0,a.checkStrictMode)(r,`"${e}" without "if" is ignored`)}};t.default=s},36704:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateUnion=t.validateArray=t.usePattern=t.callValidateCode=t.schemaProperties=t.allSchemaProperties=t.noPropertyInData=t.propertyInData=t.isOwnProperty=t.hasPropFunc=t.reportMissingProp=t.checkMissingProp=t.checkReportMissingProp=void 0;const a=r(17231),s=r(20679),o=r(3948),n=r(20679);function i(e){return e.scopeValue("func",{ref:Object.prototype.hasOwnProperty,code:a._`Object.prototype.hasOwnProperty`})}function c(e,t,r){return a._`${i(e)}.call(${t}, ${r})`}function d(e,t,r,s){const o=a._`${t}${(0,a.getProperty)(r)} === undefined`;return s?(0,a.or)(o,(0,a.not)(c(e,t,r))):o}function l(e){return e?Object.keys(e).filter((e=>"__proto__"!==e)):[]}t.checkReportMissingProp=function(e,t){const{gen:r,data:s,it:o}=e;r.if(d(r,s,t,o.opts.ownProperties),(()=>{e.setParams({missingProperty:a._`${t}`},!0),e.error()}))},t.checkMissingProp=function({gen:e,data:t,it:{opts:r}},s,o){return(0,a.or)(...s.map((s=>(0,a.and)(d(e,t,s,r.ownProperties),a._`${o} = ${s}`))))},t.reportMissingProp=function(e,t){e.setParams({missingProperty:t},!0),e.error()},t.hasPropFunc=i,t.isOwnProperty=c,t.propertyInData=function(e,t,r,s){const o=a._`${t}${(0,a.getProperty)(r)} !== undefined`;return s?a._`${o} && ${c(e,t,r)}`:o},t.noPropertyInData=d,t.allSchemaProperties=l,t.schemaProperties=function(e,t){return l(t).filter((r=>!(0,s.alwaysValidSchema)(e,t[r])))},t.callValidateCode=function({schemaCode:e,data:t,it:{gen:r,topSchemaRef:s,schemaPath:n,errorPath:i},it:c},d,l,u){const f=u?a._`${e}, ${t}, ${s}${n}`:t,h=[[o.default.instancePath,(0,a.strConcat)(o.default.instancePath,i)],[o.default.parentData,c.parentData],[o.default.parentDataProperty,c.parentDataProperty],[o.default.rootData,o.default.rootData]];c.opts.dynamicRef&&h.push([o.default.dynamicAnchors,o.default.dynamicAnchors]);const m=a._`${f}, ${r.object(...h)}`;return l!==a.nil?a._`${d}.call(${l}, ${m})`:a._`${d}(${m})`};const u=a._`new RegExp`;t.usePattern=function({gen:e,it:{opts:t}},r){const s=t.unicodeRegExp?"u":"",{regExp:o}=t.code,i=o(r,s);return e.scopeValue("pattern",{key:i.toString(),ref:i,code:a._`${"new RegExp"===o.code?u:(0,n.useFunc)(e,o)}(${r}, ${s})`})},t.validateArray=function(e){const{gen:t,data:r,keyword:o,it:n}=e,i=t.name("valid");if(n.allErrors){const e=t.let("valid",!0);return c((()=>t.assign(e,!1))),e}return t.var(i,!0),c((()=>t.break())),i;function c(n){const c=t.const("len",a._`${r}.length`);t.forRange("i",0,c,(r=>{e.subschema({keyword:o,dataProp:r,dataPropType:s.Type.Num},i),t.if((0,a.not)(i),n)}))}},t.validateUnion=function(e){const{gen:t,schema:r,keyword:o,it:n}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");if(r.some((e=>(0,s.alwaysValidSchema)(n,e)))&&!n.opts.unevaluated)return;const i=t.let("valid",!1),c=t.name("_valid");t.block((()=>r.forEach(((r,s)=>{const n=e.subschema({keyword:o,schemaProp:s,compositeRule:!0},c);t.assign(i,a._`${i} || ${c}`),e.mergeValidEvaluated(n,c)||t.if((0,a.not)(i))})))),e.result(i,(()=>e.reset()),(()=>e.error(!0)))}},40960:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r={keyword:"id",code(){throw new Error('NOT SUPPORTED: keyword "id", use "$id" for schema ID')}};t.default=r},18243:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(40960),s=r(15534),o=["$schema","$id","$defs","$vocabulary",{keyword:"$comment"},"definitions",a.default,s.default];t.default=o},15534:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.callRef=t.getValidate=void 0;const a=r(26317),s=r(36704),o=r(17231),n=r(3948),i=r(50052),c=r(20679),d={keyword:"$ref",schemaType:"string",code(e){const{gen:t,schema:r,it:s}=e,{baseId:n,schemaEnv:c,validateName:d,opts:f,self:h}=s,{root:m}=c;if(("#"===r||"#/"===r)&&n===m.baseId)return function(){if(c===m)return u(e,d,c,c.$async);const r=t.scopeValue("root",{ref:m});return u(e,o._`${r}.validate`,m,m.$async)}();const p=i.resolveRef.call(h,m,n,r);if(void 0===p)throw new a.default(s.opts.uriResolver,n,r);return p instanceof i.SchemaEnv?function(t){const r=l(e,t);u(e,r,t,t.$async)}(p):function(a){const s=t.scopeValue("schema",!0===f.code.source?{ref:a,code:(0,o.stringify)(a)}:{ref:a}),n=t.name("valid"),i=e.subschema({schema:a,dataTypes:[],schemaPath:o.nil,topSchemaRef:s,errSchemaPath:r},n);e.mergeEvaluated(i),e.ok(n)}(p)}};function l(e,t){const{gen:r}=e;return t.validate?r.scopeValue("validate",{ref:t.validate}):o._`${r.scopeValue("wrapper",{ref:t})}.validate`}function u(e,t,r,a){const{gen:i,it:d}=e,{allErrors:l,schemaEnv:u,opts:f}=d,h=f.passContext?n.default.this:o.nil;function m(e){const t=o._`${e}.errors`;i.assign(n.default.vErrors,o._`${n.default.vErrors} === null ? ${t} : ${n.default.vErrors}.concat(${t})`),i.assign(n.default.errors,o._`${n.default.vErrors}.length`)}function p(e){var t;if(!d.opts.unevaluated)return;const a=null===(t=null==r?void 0:r.validate)||void 0===t?void 0:t.evaluated;if(!0!==d.props)if(a&&!a.dynamicProps)void 0!==a.props&&(d.props=c.mergeEvaluated.props(i,a.props,d.props));else{const t=i.var("props",o._`${e}.evaluated.props`);d.props=c.mergeEvaluated.props(i,t,d.props,o.Name)}if(!0!==d.items)if(a&&!a.dynamicItems)void 0!==a.items&&(d.items=c.mergeEvaluated.items(i,a.items,d.items));else{const t=i.var("items",o._`${e}.evaluated.items`);d.items=c.mergeEvaluated.items(i,t,d.items,o.Name)}}a?function(){if(!u.$async)throw new Error("async schema referenced by sync schema");const r=i.let("valid");i.try((()=>{i.code(o._`await ${(0,s.callValidateCode)(e,t,h)}`),p(t),l||i.assign(r,!0)}),(e=>{i.if(o._`!(${e} instanceof ${d.ValidationError})`,(()=>i.throw(e))),m(e),l||i.assign(r,!1)})),e.ok(r)}():e.result((0,s.callValidateCode)(e,t,h),(()=>p(t)),(()=>m(t)))}t.getValidate=l,t.callRef=u,t.default=d},30648:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(17231),s=r(2185),o=r(50052),n=r(20679),i={keyword:"discriminator",type:"object",schemaType:"object",error:{message:({params:{discrError:e,tagName:t}})=>e===s.DiscrError.Tag?`tag "${t}" must be string`:`value of tag "${t}" must be in oneOf`,params:({params:{discrError:e,tag:t,tagName:r}})=>a._`{error: ${e}, tag: ${r}, tagValue: ${t}}`},code(e){const{gen:t,data:r,schema:i,parentSchema:c,it:d}=e,{oneOf:l}=c;if(!d.opts.discriminator)throw new Error("discriminator: requires discriminator option");const u=i.propertyName;if("string"!=typeof u)throw new Error("discriminator: requires propertyName");if(i.mapping)throw new Error("discriminator: mapping is not supported");if(!l)throw new Error("discriminator: requires oneOf keyword");const f=t.let("valid",!1),h=t.const("tag",a._`${r}${(0,a.getProperty)(u)}`);function m(r){const s=t.name("valid"),o=e.subschema({keyword:"oneOf",schemaProp:r},s);return e.mergeEvaluated(o,a.Name),s}t.if(a._`typeof ${h} == "string"`,(()=>function(){const r=function(){var e;const t={},r=s(c);let a=!0;for(let t=0;t<l.length;t++){let c=l[t];(null==c?void 0:c.$ref)&&!(0,n.schemaHasRulesButRef)(c,d.self.RULES)&&(c=o.resolveRef.call(d.self,d.schemaEnv.root,d.baseId,null==c?void 0:c.$ref),c instanceof o.SchemaEnv&&(c=c.schema));const f=null===(e=null==c?void 0:c.properties)||void 0===e?void 0:e[u];if("object"!=typeof f)throw new Error(`discriminator: oneOf subschemas (or referenced schemas) must have "properties/${u}"`);a=a&&(r||s(c)),i(f,t)}if(!a)throw new Error(`discriminator: "${u}" must be required`);return t;function s({required:e}){return Array.isArray(e)&&e.includes(u)}function i(e,t){if(e.const)f(e.const,t);else{if(!e.enum)throw new Error(`discriminator: "properties/${u}" must have "const" or "enum"`);for(const r of e.enum)f(r,t)}}function f(e,r){if("string"!=typeof e||e in t)throw new Error(`discriminator: "${u}" values must be unique strings`);t[e]=r}}();t.if(!1);for(const e in r)t.elseIf(a._`${h} === ${e}`),t.assign(f,m(r[e]));t.else(),e.error(!1,{discrError:s.DiscrError.Mapping,tag:h,tagName:u}),t.endIf()}()),(()=>e.error(!1,{discrError:s.DiscrError.Tag,tag:h,tagName:u}))),e.ok(f)}};t.default=i},2185:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.DiscrError=void 0,(r=t.DiscrError||(t.DiscrError={})).Tag="tag",r.Mapping="mapping"},87213:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(18243),s=r(19591),o=r(75448),n=r(4505),i=r(51935),c=[a.default,s.default,(0,o.default)(),n.default,i.metadataVocabulary,i.contentVocabulary];t.default=c},78803:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(17231),s={keyword:"format",type:["number","string"],schemaType:"string",$data:!0,error:{message:({schemaCode:e})=>a.str`must match format "${e}"`,params:({schemaCode:e})=>a._`{format: ${e}}`},code(e,t){const{gen:r,data:s,$data:o,schema:n,schemaCode:i,it:c}=e,{opts:d,errSchemaPath:l,schemaEnv:u,self:f}=c;d.validateFormats&&(o?function(){const o=r.scopeValue("formats",{ref:f.formats,code:d.code.formats}),n=r.const("fDef",a._`${o}[${i}]`),c=r.let("fType"),l=r.let("format");r.if(a._`typeof ${n} == "object" && !(${n} instanceof RegExp)`,(()=>r.assign(c,a._`${n}.type || "string"`).assign(l,a._`${n}.validate`)),(()=>r.assign(c,a._`"string"`).assign(l,n))),e.fail$data((0,a.or)(!1===d.strictSchema?a.nil:a._`${i} && !${l}`,function(){const e=u.$async?a._`(${n}.async ? await ${l}(${s}) : ${l}(${s}))`:a._`${l}(${s})`,r=a._`(typeof ${l} == "function" ? ${e} : ${l}.test(${s}))`;return a._`${l} && ${l} !== true && ${c} === ${t} && !${r}`}()))}():function(){const o=f.formats[n];if(!o)return void function(){if(!1!==d.strictSchema)throw new Error(e());function e(){return`unknown format "${n}" ignored in schema at path "${l}"`}f.logger.warn(e())}();if(!0===o)return;const[i,c,h]=function(e){const t=e instanceof RegExp?(0,a.regexpCode)(e):d.code.formats?a._`${d.code.formats}${(0,a.getProperty)(n)}`:void 0,s=r.scopeValue("formats",{key:n,ref:e,code:t});return"object"!=typeof e||e instanceof RegExp?["string",e,s]:[e.type||"string",e.validate,a._`${s}.validate`]}(o);i===t&&e.pass(function(){if("object"==typeof o&&!(o instanceof RegExp)&&o.async){if(!u.$async)throw new Error("async format in sync schema");return a._`await ${h}(${s})`}return"function"==typeof c?a._`${h}(${s})`:a._`${h}.test(${s})`}())}())}};t.default=s},4505:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=[r(78803).default];t.default=a},51935:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.contentVocabulary=t.metadataVocabulary=void 0,t.metadataVocabulary=["title","description","default","deprecated","readOnly","writeOnly","examples"],t.contentVocabulary=["contentMediaType","contentEncoding","contentSchema"]},15603:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(17231),s=r(20679),o=r(59586),n={keyword:"const",$data:!0,error:{message:"must be equal to constant",params:({schemaCode:e})=>a._`{allowedValue: ${e}}`},code(e){const{gen:t,data:r,$data:n,schemaCode:i,schema:c}=e;n||c&&"object"==typeof c?e.fail$data(a._`!${(0,s.useFunc)(t,o.default)}(${r}, ${i})`):e.fail(a._`${c} !== ${r}`)}};t.default=n},4649:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(17231),s=r(20679),o=r(59586),n={keyword:"enum",schemaType:"array",$data:!0,error:{message:"must be equal to one of the allowed values",params:({schemaCode:e})=>a._`{allowedValues: ${e}}`},code(e){const{gen:t,data:r,$data:n,schema:i,schemaCode:c,it:d}=e;if(!n&&0===i.length)throw new Error("enum must have non-empty array");const l=i.length>=d.opts.loopEnum;let u;const f=()=>null!=u?u:u=(0,s.useFunc)(t,o.default);let h;if(l||n)h=t.let("valid"),e.block$data(h,(function(){t.assign(h,!1),t.forOf("v",c,(e=>t.if(a._`${f()}(${r}, ${e})`,(()=>t.assign(h,!0).break()))))}));else{if(!Array.isArray(i))throw new Error("ajv implementation error");const e=t.const("vSchema",c);h=(0,a.or)(...i.map(((t,s)=>function(e,t){const s=i[t];return"object"==typeof s&&null!==s?a._`${f()}(${r}, ${e}[${t}])`:a._`${r} === ${s}`}(e,s))))}e.pass(h)}};t.default=n},19591:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(22734),s=r(98334),o=r(65030),n=r(74287),i=r(73485),c=r(53758),d=r(39633),l=r(49150),u=r(15603),f=r(4649),h=[a.default,s.default,o.default,n.default,i.default,c.default,d.default,l.default,{keyword:"type",schemaType:["string","array"]},{keyword:"nullable",schemaType:"boolean"},u.default,f.default];t.default=h},39633:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(17231),s={keyword:["maxItems","minItems"],type:"array",schemaType:"number",$data:!0,error:{message({keyword:e,schemaCode:t}){const r="maxItems"===e?"more":"fewer";return a.str`must NOT have ${r} than ${t} items`},params:({schemaCode:e})=>a._`{limit: ${e}}`},code(e){const{keyword:t,data:r,schemaCode:s}=e,o="maxItems"===t?a.operators.GT:a.operators.LT;e.fail$data(a._`${r}.length ${o} ${s}`)}};t.default=s},65030:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(17231),s=r(20679),o=r(20680),n={keyword:["maxLength","minLength"],type:"string",schemaType:"number",$data:!0,error:{message({keyword:e,schemaCode:t}){const r="maxLength"===e?"more":"fewer";return a.str`must NOT have ${r} than ${t} characters`},params:({schemaCode:e})=>a._`{limit: ${e}}`},code(e){const{keyword:t,data:r,schemaCode:n,it:i}=e,c="maxLength"===t?a.operators.GT:a.operators.LT,d=!1===i.opts.unicode?a._`${r}.length`:a._`${(0,s.useFunc)(e.gen,o.default)}(${r})`;e.fail$data(a._`${d} ${c} ${n}`)}};t.default=n},22734:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(17231),s=a.operators,o={maximum:{okStr:"<=",ok:s.LTE,fail:s.GT},minimum:{okStr:">=",ok:s.GTE,fail:s.LT},exclusiveMaximum:{okStr:"<",ok:s.LT,fail:s.GTE},exclusiveMinimum:{okStr:">",ok:s.GT,fail:s.LTE}},n={message:({keyword:e,schemaCode:t})=>a.str`must be ${o[e].okStr} ${t}`,params:({keyword:e,schemaCode:t})=>a._`{comparison: ${o[e].okStr}, limit: ${t}}`},i={keyword:Object.keys(o),type:"number",schemaType:"number",$data:!0,error:n,code(e){const{keyword:t,data:r,schemaCode:s}=e;e.fail$data(a._`${r} ${o[t].fail} ${s} || isNaN(${r})`)}};t.default=i},73485:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(17231),s={keyword:["maxProperties","minProperties"],type:"object",schemaType:"number",$data:!0,error:{message({keyword:e,schemaCode:t}){const r="maxProperties"===e?"more":"fewer";return a.str`must NOT have ${r} than ${t} properties`},params:({schemaCode:e})=>a._`{limit: ${e}}`},code(e){const{keyword:t,data:r,schemaCode:s}=e,o="maxProperties"===t?a.operators.GT:a.operators.LT;e.fail$data(a._`Object.keys(${r}).length ${o} ${s}`)}};t.default=s},98334:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(17231),s={keyword:"multipleOf",type:"number",schemaType:"number",$data:!0,error:{message:({schemaCode:e})=>a.str`must be multiple of ${e}`,params:({schemaCode:e})=>a._`{multipleOf: ${e}}`},code(e){const{gen:t,data:r,schemaCode:s,it:o}=e,n=o.opts.multipleOfPrecision,i=t.let("res"),c=n?a._`Math.abs(Math.round(${i}) - ${i}) > 1e-${n}`:a._`${i} !== parseInt(${i})`;e.fail$data(a._`(${s} === 0 || (${i} = ${r}/${s}, ${c}))`)}};t.default=s},74287:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(36704),s=r(17231),o={keyword:"pattern",type:"string",schemaType:"string",$data:!0,error:{message:({schemaCode:e})=>s.str`must match pattern "${e}"`,params:({schemaCode:e})=>s._`{pattern: ${e}}`},code(e){const{data:t,$data:r,schema:o,schemaCode:n,it:i}=e,c=i.opts.unicodeRegExp?"u":"",d=r?s._`(new RegExp(${n}, ${c}))`:(0,a.usePattern)(e,o);e.fail$data(s._`!${d}.test(${t})`)}};t.default=o},53758:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(36704),s=r(17231),o=r(20679),n={keyword:"required",type:"object",schemaType:"array",$data:!0,error:{message:({params:{missingProperty:e}})=>s.str`must have required property '${e}'`,params:({params:{missingProperty:e}})=>s._`{missingProperty: ${e}}`},code(e){const{gen:t,schema:r,schemaCode:n,data:i,$data:c,it:d}=e,{opts:l}=d;if(!c&&0===r.length)return;const u=r.length>=l.loopRequired;if(d.allErrors?function(){if(u||c)e.block$data(s.nil,f);else for(const t of r)(0,a.checkReportMissingProp)(e,t)}():function(){const o=t.let("missing");if(u||c){const r=t.let("valid",!0);e.block$data(r,(()=>function(r,o){e.setParams({missingProperty:r}),t.forOf(r,n,(()=>{t.assign(o,(0,a.propertyInData)(t,i,r,l.ownProperties)),t.if((0,s.not)(o),(()=>{e.error(),t.break()}))}),s.nil)}(o,r))),e.ok(r)}else t.if((0,a.checkMissingProp)(e,r,o)),(0,a.reportMissingProp)(e,o),t.else()}(),l.strictRequired){const t=e.parentSchema.properties,{definedProperties:a}=e.it;for(const e of r)if(void 0===(null==t?void 0:t[e])&&!a.has(e)){const t=`required property "${e}" is not defined at "${d.schemaEnv.baseId+d.errSchemaPath}" (strictRequired)`;(0,o.checkStrictMode)(d,t,d.opts.strictRequired)}}function f(){t.forOf("prop",n,(r=>{e.setParams({missingProperty:r}),t.if((0,a.noPropertyInData)(t,i,r,l.ownProperties),(()=>e.error()))}))}}};t.default=n},49150:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const a=r(85077),s=r(17231),o=r(20679),n=r(59586),i={keyword:"uniqueItems",type:"array",schemaType:"boolean",$data:!0,error:{message:({params:{i:e,j:t}})=>s.str`must NOT have duplicate items (items ## ${t} and ${e} are identical)`,params:({params:{i:e,j:t}})=>s._`{i: ${e}, j: ${t}}`},code(e){const{gen:t,data:r,$data:i,schema:c,parentSchema:d,schemaCode:l,it:u}=e;if(!i&&!c)return;const f=t.let("valid"),h=d.items?(0,a.getSchemaTypes)(d.items):[];function m(o,n){const i=t.name("item"),c=(0,a.checkDataTypes)(h,i,u.opts.strictNumbers,a.DataType.Wrong),d=t.const("indices",s._`{}`);t.for(s._`;${o}--;`,(()=>{t.let(i,s._`${r}[${o}]`),t.if(c,s._`continue`),h.length>1&&t.if(s._`typeof ${i} == "string"`,s._`${i} += "_"`),t.if(s._`typeof ${d}[${i}] == "number"`,(()=>{t.assign(n,s._`${d}[${i}]`),e.error(),t.assign(f,!1).break()})).code(s._`${d}[${i}] = ${o}`)}))}function p(a,i){const c=(0,o.useFunc)(t,n.default),d=t.name("outer");t.label(d).for(s._`;${a}--;`,(()=>t.for(s._`${i} = ${a}; ${i}--;`,(()=>t.if(s._`${c}(${r}[${a}], ${r}[${i}])`,(()=>{e.error(),t.assign(f,!1).break(d)}))))))}e.block$data(f,(function(){const a=t.let("i",s._`${r}.length`),o=t.let("j");e.setParams({i:a,j:o}),t.assign(f,!0),t.if(s._`${a} > 1`,(()=>(h.length>0&&!h.some((e=>"object"===e||"array"===e))?m:p)(a,o)))}),s._`${l} === false`),e.ok(f)}};t.default=i},52485:(e,t,r)=>{var a=r(74554),s=r(95915),o=r(88379);function n(e){var t=-1,r=null==e?0:e.length;for(this.__data__=new a;++t<r;)this.add(e[t])}n.prototype.add=n.prototype.push=s,n.prototype.has=o,e.exports=n},90756:e=>{e.exports=function(e,t){for(var r=-1,a=null==e?0:e.length;++r<a;)if(t(e[r],r,e))return!0;return!1}},32866:(e,t,r)=>{var a=r(12772),s=r(92360);e.exports=function e(t,r,o,n,i){return t===r||(null==t||null==r||!s(t)&&!s(r)?t!=t&&r!=r:a(t,r,o,n,e,i))}},12772:(e,t,r)=>{var a=r(23694),s=r(27042),o=r(370),n=r(39584),i=r(3533),c=r(19785),d=r(43854),l=r(48519),u="[object Arguments]",f="[object Array]",h="[object Object]",m=Object.prototype.hasOwnProperty;e.exports=function(e,t,r,p,y,$){var v=c(e),g=c(t),_=v?f:i(e),w=g?f:i(t),b=(_=_==u?h:_)==h,P=(w=w==u?h:w)==h,E=_==w;if(E&&d(e)){if(!d(t))return!1;v=!0,b=!1}if(E&&!b)return $||($=new a),v||l(e)?s(e,t,r,p,y,$):o(e,t,_,r,p,y,$);if(!(1&r)){var S=b&&m.call(e,"__wrapped__"),k=P&&m.call(t,"__wrapped__");if(S||k){var j=S?e.value():e,N=k?t.value():t;return $||($=new a),y(j,N,r,p,$)}}return!!E&&($||($=new a),n(e,t,r,p,y,$))}},65581:e=>{e.exports=function(e,t){return e.has(t)}},27042:(e,t,r)=>{var a=r(52485),s=r(90756),o=r(65581);e.exports=function(e,t,r,n,i,c){var d=1&r,l=e.length,u=t.length;if(l!=u&&!(d&&u>l))return!1;var f=c.get(e),h=c.get(t);if(f&&h)return f==t&&h==e;var m=-1,p=!0,y=2&r?new a:void 0;for(c.set(e,t),c.set(t,e);++m<l;){var $=e[m],v=t[m];if(n)var g=d?n(v,$,m,t,e,c):n($,v,m,e,t,c);if(void 0!==g){if(g)continue;p=!1;break}if(y){if(!s(t,(function(e,t){if(!o(y,t)&&($===e||i($,e,r,n,c)))return y.push(t)}))){p=!1;break}}else if($!==v&&!i($,v,r,n,c)){p=!1;break}}return c.delete(e),c.delete(t),p}},370:(e,t,r)=>{var a=r(96539),s=r(59942),o=r(85638),n=r(27042),i=r(19383),c=r(43735),d=a?a.prototype:void 0,l=d?d.valueOf:void 0;e.exports=function(e,t,r,a,d,u,f){switch(r){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":return!(e.byteLength!=t.byteLength||!u(new s(e),new s(t)));case"[object Boolean]":case"[object Date]":case"[object Number]":return o(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var h=i;case"[object Set]":var m=1&a;if(h||(h=c),e.size!=t.size&&!m)return!1;var p=f.get(e);if(p)return p==t;a|=2,f.set(e,t);var y=n(h(e),h(t),a,d,u,f);return f.delete(e),y;case"[object Symbol]":if(l)return l.call(e)==l.call(t)}return!1}},39584:(e,t,r)=>{var a=r(51385),s=Object.prototype.hasOwnProperty;e.exports=function(e,t,r,o,n,i){var c=1&r,d=a(e),l=d.length;if(l!=a(t).length&&!c)return!1;for(var u=l;u--;){var f=d[u];if(!(c?f in t:s.call(t,f)))return!1}var h=i.get(e),m=i.get(t);if(h&&m)return h==t&&m==e;var p=!0;i.set(e,t),i.set(t,e);for(var y=c;++u<l;){var $=e[f=d[u]],v=t[f];if(o)var g=c?o(v,$,f,t,e,i):o($,v,f,e,t,i);if(!(void 0===g?$===v||n($,v,r,o,i):g)){p=!1;break}y||(y="constructor"==f)}if(p&&!y){var _=e.constructor,w=t.constructor;_==w||!("constructor"in e)||!("constructor"in t)||"function"==typeof _&&_ instanceof _&&"function"==typeof w&&w instanceof w||(p=!1)}return i.delete(e),i.delete(t),p}},19383:e=>{e.exports=function(e){var t=-1,r=Array(e.size);return e.forEach((function(e,a){r[++t]=[a,e]})),r}},95915:e=>{e.exports=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this}},88379:e=>{e.exports=function(e){return this.__data__.has(e)}},43735:e=>{e.exports=function(e){var t=-1,r=Array(e.size);return e.forEach((function(e){r[++t]=e})),r}},56141:(e,t,r)=>{var a=r(32866);e.exports=function(e,t){return a(e,t)}},94801:e=>{"use strict";e.exports=JSON.parse('{"$id":"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#","description":"Meta-schema for $data reference (JSON AnySchema extension proposal)","type":"object","required":["$data"],"properties":{"$data":{"type":"string","anyOf":[{"format":"relative-json-pointer"},{"format":"json-pointer"}]}},"additionalProperties":false}')},60172:e=>{"use strict";e.exports=JSON.parse('{"$schema":"http://json-schema.org/draft-07/schema#","$id":"http://json-schema.org/draft-07/schema#","title":"Core schema meta-schema","definitions":{"schemaArray":{"type":"array","minItems":1,"items":{"$ref":"#"}},"nonNegativeInteger":{"type":"integer","minimum":0},"nonNegativeIntegerDefault0":{"allOf":[{"$ref":"#/definitions/nonNegativeInteger"},{"default":0}]},"simpleTypes":{"enum":["array","boolean","integer","null","number","object","string"]},"stringArray":{"type":"array","items":{"type":"string"},"uniqueItems":true,"default":[]}},"type":["object","boolean"],"properties":{"$id":{"type":"string","format":"uri-reference"},"$schema":{"type":"string","format":"uri"},"$ref":{"type":"string","format":"uri-reference"},"$comment":{"type":"string"},"title":{"type":"string"},"description":{"type":"string"},"default":true,"readOnly":{"type":"boolean","default":false},"examples":{"type":"array","items":true},"multipleOf":{"type":"number","exclusiveMinimum":0},"maximum":{"type":"number"},"exclusiveMaximum":{"type":"number"},"minimum":{"type":"number"},"exclusiveMinimum":{"type":"number"},"maxLength":{"$ref":"#/definitions/nonNegativeInteger"},"minLength":{"$ref":"#/definitions/nonNegativeIntegerDefault0"},"pattern":{"type":"string","format":"regex"},"additionalItems":{"$ref":"#"},"items":{"anyOf":[{"$ref":"#"},{"$ref":"#/definitions/schemaArray"}],"default":true},"maxItems":{"$ref":"#/definitions/nonNegativeInteger"},"minItems":{"$ref":"#/definitions/nonNegativeIntegerDefault0"},"uniqueItems":{"type":"boolean","default":false},"contains":{"$ref":"#"},"maxProperties":{"$ref":"#/definitions/nonNegativeInteger"},"minProperties":{"$ref":"#/definitions/nonNegativeIntegerDefault0"},"required":{"$ref":"#/definitions/stringArray"},"additionalProperties":{"$ref":"#"},"definitions":{"type":"object","additionalProperties":{"$ref":"#"},"default":{}},"properties":{"type":"object","additionalProperties":{"$ref":"#"},"default":{}},"patternProperties":{"type":"object","additionalProperties":{"$ref":"#"},"propertyNames":{"format":"regex"},"default":{}},"dependencies":{"type":"object","additionalProperties":{"anyOf":[{"$ref":"#"},{"$ref":"#/definitions/stringArray"}]}},"propertyNames":{"$ref":"#"},"const":true,"enum":{"type":"array","items":true,"minItems":1,"uniqueItems":true},"type":{"anyOf":[{"$ref":"#/definitions/simpleTypes"},{"type":"array","items":{"$ref":"#/definitions/simpleTypes"},"minItems":1,"uniqueItems":true}]},"format":{"type":"string"},"contentMediaType":{"type":"string"},"contentEncoding":{"type":"string"},"if":{"$ref":"#"},"then":{"$ref":"#"},"else":{"$ref":"#"},"allOf":{"$ref":"#/definitions/schemaArray"},"anyOf":{"$ref":"#/definitions/schemaArray"},"oneOf":{"$ref":"#/definitions/schemaArray"},"not":{"$ref":"#"}},"default":true}')}}]);
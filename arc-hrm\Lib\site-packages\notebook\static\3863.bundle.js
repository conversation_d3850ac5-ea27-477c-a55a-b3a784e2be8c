"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[3863],{73863:(e,r,t)=>{t.r(r),t.d(r,{default:()=>i});var n=t(68239);const i=[{id:"@jupyterlab/ui-components-extension:labicon-manager",description:"Provides the icon manager.",provides:n.ILabIconManager,autoStart:!0,activate:e=>Object.create(null)},{id:"@jupyterlab/ui-components-extension:form-renderer-registry",description:"Provides the settings form renderer registry.",provides:n.IFormRendererRegistry,autoStart:!0,activate:e=>new n.FormRendererRegistry}]}}]);
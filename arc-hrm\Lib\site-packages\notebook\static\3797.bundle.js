"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[3797],{23797:(t,e,a)=>{a.r(e),a.d(e,{Tag:()=>s,classHighlighter:()=>E,getStyleTags:()=>p,highlightTree:()=>f,styleTags:()=>l,tagHighlighter:()=>g,tags:()=>A});var r=a(73887);let i=0;class s{constructor(t,e,a){this.set=t,this.base=e,this.modified=a,this.id=i++}static define(t){if(null==t?void 0:t.base)throw new Error("Can not derive from a modified tag");let e=new s([],null,[]);if(e.set.push(e),t)for(let a of t.set)e.set.push(a);return e}static defineModifier(){let t=new n;return e=>e.modified.indexOf(t)>-1?e:n.get(e.base||e,e.modified.concat(t).sort(((t,e)=>t.id-e.id)))}}let o=0;class n{constructor(){this.instances=[],this.id=o++}static get(t,e){if(!e.length)return t;let a=e[0].instances.find((a=>{return a.base==t&&(r=e,i=a.modified,r.length==i.length&&r.every(((t,e)=>t==i[e])));var r,i}));if(a)return a;let r=[],i=new s(r,t,e);for(let t of e)t.instances.push(i);let o=function(t){let e=[[]];for(let a=0;a<t.length;a++)for(let r=0,i=e.length;r<i;r++)e.push(e[r].concat(t[a]));return e.sort(((t,e)=>e.length-t.length))}(e);for(let e of t.set)if(!e.modified.length)for(let t of o)r.push(n.get(e,t));return i}}function l(t){let e=Object.create(null);for(let a in t){let r=t[a];Array.isArray(r)||(r=[r]);for(let t of a.split(" "))if(t){let a=[],i=2,s=t;for(let e=0;;){if("..."==s&&e>0&&e+3==t.length){i=1;break}let r=/^"(?:[^"\\]|\\.)*?"|[^\/!]+/.exec(s);if(!r)throw new RangeError("Invalid path: "+t);if(a.push("*"==r[0]?"":'"'==r[0][0]?JSON.parse(r[0]):r[0]),e+=r[0].length,e==t.length)break;let o=t[e++];if(e==t.length&&"!"==o){i=0;break}if("/"!=o)throw new RangeError("Invalid path: "+t);s=t.slice(e)}let o=a.length-1,n=a[o];if(!n)throw new RangeError("Invalid path: "+t);let l=new c(r,i,o>0?a.slice(0,o):null);e[n]=l.sort(e[n])}}return h.add(e)}const h=new r.NodeProp;class c{constructor(t,e,a,r){this.tags=t,this.mode=e,this.context=a,this.next=r}get opaque(){return 0==this.mode}get inherit(){return 1==this.mode}sort(t){return!t||t.depth<this.depth?(this.next=t,this):(t.next=this.sort(t.next),t)}get depth(){return this.context?this.context.length:0}}function g(t,e){let a=Object.create(null);for(let e of t)if(Array.isArray(e.tag))for(let t of e.tag)a[t.id]=e.class;else a[e.tag.id]=e.class;let{scope:r,all:i=null}=e||{};return{style:t=>{let e=i;for(let r of t)for(let t of r.set){let r=a[t.id];if(r){e=e?e+" "+r:r;break}}return e},scope:r}}function f(t,e,a,r=0,i=t.length){let s=new d(r,Array.isArray(e)?e:[e],a);s.highlightRange(t.cursor(),r,i,"",s.highlighters),s.flush(i)}c.empty=new c([],2,null);class d{constructor(t,e,a){this.at=t,this.highlighters=e,this.span=a,this.class=""}startSpan(t,e){e!=this.class&&(this.flush(t),t>this.at&&(this.at=t),this.class=e)}flush(t){t>this.at&&this.class&&this.span(this.at,t,this.class)}highlightRange(t,e,a,i,s){let{type:o,from:n,to:l}=t;if(n>=a||l<=e)return;o.isTop&&(s=this.highlighters.filter((t=>!t.scope||t.scope(o))));let h=i,g=p(t)||c.empty,f=function(t,e){let a=null;for(let r of t){let t=r.style(e);t&&(a=a?a+" "+t:t)}return a}(s,g.tags);if(f&&(h&&(h+=" "),h+=f,1==g.mode&&(i+=(i?" ":"")+f)),this.startSpan(Math.max(e,n),h),g.opaque)return;let d=t.tree&&t.tree.prop(r.NodeProp.mounted);if(d&&d.overlay){let r=t.node.enter(d.overlay[0].from+n,1),o=this.highlighters.filter((t=>!t.scope||t.scope(d.tree.type))),c=t.firstChild();for(let g=0,f=n;;g++){let p=g<d.overlay.length?d.overlay[g]:null,m=p?p.from+n:l,u=Math.max(e,f),k=Math.min(a,m);if(u<k&&c)for(;t.from<k&&(this.highlightRange(t,u,k,i,s),this.startSpan(Math.min(k,t.to),h),!(t.to>=m)&&t.nextSibling()););if(!p||m>a)break;f=p.to+n,f>e&&(this.highlightRange(r.cursor(),Math.max(e,p.from+n),Math.min(a,f),"",o),this.startSpan(Math.min(a,f),h))}c&&t.parent()}else if(t.firstChild()){d&&(i="");do{if(!(t.to<=e)){if(t.from>=a)break;this.highlightRange(t,e,a,i,s),this.startSpan(Math.min(a,t.to),h)}}while(t.nextSibling());t.parent()}}}function p(t){let e=t.type.prop(h);for(;e&&e.context&&!t.matchContext(e.context);)e=e.next;return e||null}const m=s.define,u=m(),k=m(),b=m(k),y=m(k),N=m(),w=m(N),v=m(N),x=m(),O=m(x),M=m(),R=m(),C=m(),S=m(C),T=m(),A={comment:u,lineComment:m(u),blockComment:m(u),docComment:m(u),name:k,variableName:m(k),typeName:b,tagName:m(b),propertyName:y,attributeName:m(y),className:m(k),labelName:m(k),namespace:m(k),macroName:m(k),literal:N,string:w,docString:m(w),character:m(w),attributeValue:m(w),number:v,integer:m(v),float:m(v),bool:m(N),regexp:m(N),escape:m(N),color:m(N),url:m(N),keyword:M,self:m(M),null:m(M),atom:m(M),unit:m(M),modifier:m(M),operatorKeyword:m(M),controlKeyword:m(M),definitionKeyword:m(M),moduleKeyword:m(M),operator:R,derefOperator:m(R),arithmeticOperator:m(R),logicOperator:m(R),bitwiseOperator:m(R),compareOperator:m(R),updateOperator:m(R),definitionOperator:m(R),typeOperator:m(R),controlOperator:m(R),punctuation:C,separator:m(C),bracket:S,angleBracket:m(S),squareBracket:m(S),paren:m(S),brace:m(S),content:x,heading:O,heading1:m(O),heading2:m(O),heading3:m(O),heading4:m(O),heading5:m(O),heading6:m(O),contentSeparator:m(x),list:m(x),quote:m(x),emphasis:m(x),strong:m(x),link:m(x),monospace:m(x),strikethrough:m(x),inserted:m(),deleted:m(),changed:m(),invalid:m(),meta:T,documentMeta:m(T),annotation:m(T),processingInstruction:m(T),definition:s.defineModifier(),constant:s.defineModifier(),function:s.defineModifier(),standard:s.defineModifier(),local:s.defineModifier(),special:s.defineModifier()},E=g([{tag:A.link,class:"tok-link"},{tag:A.heading,class:"tok-heading"},{tag:A.emphasis,class:"tok-emphasis"},{tag:A.strong,class:"tok-strong"},{tag:A.keyword,class:"tok-keyword"},{tag:A.atom,class:"tok-atom"},{tag:A.bool,class:"tok-bool"},{tag:A.url,class:"tok-url"},{tag:A.labelName,class:"tok-labelName"},{tag:A.inserted,class:"tok-inserted"},{tag:A.deleted,class:"tok-deleted"},{tag:A.literal,class:"tok-literal"},{tag:A.string,class:"tok-string"},{tag:A.number,class:"tok-number"},{tag:[A.regexp,A.escape,A.special(A.string)],class:"tok-string2"},{tag:A.variableName,class:"tok-variableName"},{tag:A.local(A.variableName),class:"tok-variableName tok-local"},{tag:A.definition(A.variableName),class:"tok-variableName tok-definition"},{tag:A.special(A.variableName),class:"tok-variableName2"},{tag:A.definition(A.propertyName),class:"tok-propertyName tok-definition"},{tag:A.typeName,class:"tok-typeName"},{tag:A.namespace,class:"tok-namespace"},{tag:A.className,class:"tok-className"},{tag:A.macroName,class:"tok-macroName"},{tag:A.propertyName,class:"tok-propertyName"},{tag:A.operator,class:"tok-operator"},{tag:A.comment,class:"tok-comment"},{tag:A.meta,class:"tok-meta"},{tag:A.invalid,class:"tok-invalid"},{tag:A.punctuation,class:"tok-punctuation"}])}}]);
/*! For license information please see 2913.bundle.js.LICENSE.txt */
"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[2913],{93036:u=>{u.exports={mode:"lazy"}},90425:(u,e,r)=>{const d=r(93036),n=r(55460),t=r(3661),a=new WeakMap;function f(u){let e=a.get(u);e||(e=function(u){const e=t.transform(u,m);return new t.TransformResult(e.getAST(),o)}(t.parse(`/${u.source}/${u.flags}`)),a.set(u,e));const r=e.getExtra();return{measurementRegExp:e.toRegExp(),groupInfos:r}}function c(u,e){const r=u.index,d=r+u[0].length,n=[],t=u.groups?Object.create(null):void 0;i(n,0,[r,d]);for(const d of e){let e;if(void 0!==u[d.newGroupNumber]){let n=r;if(d.measurementGroups)for(const e of d.measurementGroups)n+=u[e].length;e=[n,n+u[d.newGroupNumber].length]}i(n,d.oldGroupNumber,e),t&&void 0!==d.groupName&&i(t,d.groupName,e)}return i(n,"groups",t),n}function i(u,e,r){const d=Object.getOwnPropertyDescriptor(u,e);if(d?d.configurable:Object.isExtensible(u)){const n={enumerable:!d||d.enumerable,configurable:!d||d.configurable,writable:!0,value:r};Object.defineProperty(u,e,n)}}let o,s=!1,l=new Set,b=[],p=!1,h=1,g=[],v=new Map,y=new Map;const m={init(){s=!1,l.clear(),b.length=0,p=!1,h=1,g.length=0,v.clear(),y.clear(),o=[]},RegExp:u=>(t.traverse(u.node,C),l.size>0&&(t.transform(u.node,S),t.transform(u.node,x),s&&t.transform(u.node,A)),!1)},_={pre(u){b.push(p),p="Group"===u.node.type&&u.node.capturing},post(u){p&&l.add(u.node),p=b.pop()||p}},C={Alternative:_,Disjunction:_,Assertion:_,Group:_,Repetition:_,Backreference(u){s=!0}},S={Alternative(u){if(l.has(u.node)){let e=0,r=[];const d=[],n=[];for(let a=0;a<u.node.expressions.length;a++){const f=u.node.expressions[a];if(l.has(f)){if(a>e){const u={type:"Group",capturing:!0,number:-1,expression:r.length>1?{type:"Alternative",expressions:r}:1===r.length?r[0]:null};n.push(u),d.push(u),e=a,r=[]}g.push(d),t.transform(f,S),g.pop(),r.push(f)}else r.push(f)}u.update({expressions:n.concat(r)})}return!1},Group(u){u.node.capturing&&v.set(u.node,function(){const u=[];for(const e of g)for(const r of e)u.push(r);return u}())}},x={Group(u){if(!o)throw new Error("Not initialized.");if(!u.node.capturing)return;const e=u.node.number,r=h++,d=v.get(u.node);-1!==e&&(o.push({oldGroupNumber:e,newGroupNumber:r,measurementGroups:d&&d.map((u=>u.number)),groupName:u.node.name}),y.set(e,r)),u.update({number:r})}},A={Backreference(u){const e=y.get(u.node.number);e&&("number"===u.node.kind?u.update({number:e,reference:e}):u.update({number:e}))}};u.exports=function(u){return"spec-compliant"===d.mode?function(u,e){const{measurementRegExp:r,groupInfos:d}=f(u);r.lastIndex=u.lastIndex;const t=n.call(r,e);if(null===t)return null;u.lastIndex=r.lastIndex;const a=[];i(a,0,t[0]);for(const u of d)i(a,u.oldGroupNumber,t[u.newGroupNumber]);return i(a,"index",t.index),i(a,"input",t.input),i(a,"groups",t.groups),i(a,"indices",c(t,d)),a}(this,u):function(u,e){const r=u.lastIndex,d=n.call(u,e);if(null===d)return null;let t;return Object.defineProperty(d,"indices",{enumerable:!0,configurable:!0,get(){if(void 0===t){const{measurementRegExp:a,groupInfos:o}=f(u);a.lastIndex=r;const s=n.call(a,e);if(null===s)throw new TypeError;i(d,"indices",t=c(s,o))}return t},set(u){i(d,"indices",u)}}),d}(this,u)}},32913:(u,e,r)=>{const d=r(90425),n=r(55460),t=r(5565),a=r(37637),f=r(93036),c=t();function i(u,e){return c.call(u,e)}i.implementation=d,i.native=n,i.getPolyfill=t,i.shim=a,i.config=f,i||(i={}),u.exports=i},55460:u=>{const e=RegExp.prototype.exec;u.exports=e},5565:(u,e,r)=>{const d=r(55460),n=r(90425);u.exports=function(){const u=new RegExp("a");return d.call(u,"a").indices?d:n}},37637:(u,e,r)=>{const d=r(5565);u.exports=function(){const u=d();RegExp.prototype.exec!==u&&(RegExp.prototype.exec=u)}},47921:(u,e,r)=>{var d=r(24845),n=r(42113);u.exports={transform:function(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=e.length>0?e:Object.keys(d),t=void 0,a={};return r.forEach((function(e){if(!d.hasOwnProperty(e))throw new Error("Unknown compat-transform: "+e+". Available transforms are: "+Object.keys(d).join(", "));var r=d[e];t=n.transform(u,r),u=t.getAST(),"function"==typeof r.getExtra&&(a[e]=r.getExtra())})),t.setExtra(a),t}}},3561:u=>{var e=function(){function u(u,e){for(var r=0;r<e.length;r++){var d=e[r];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(u,d.key,d)}}return function(e,r,d){return r&&u(e.prototype,r),d&&u(e,d),e}}(),r=function(){function u(e,r){var d=r.flags,n=r.groups,t=r.source;!function(u,e){if(!(u instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u),this._re=e,this._groups=n,this.flags=d,this.source=t||e.source,this.dotAll=d.includes("s"),this.global=e.global,this.ignoreCase=e.ignoreCase,this.multiline=e.multiline,this.sticky=e.sticky,this.unicode=e.unicode}return e(u,[{key:"test",value:function(u){return this._re.test(u)}},{key:"compile",value:function(u){return this._re.compile(u)}},{key:"toString",value:function(){return this._toStringResult||(this._toStringResult="/"+this.source+"/"+this.flags),this._toStringResult}},{key:"exec",value:function(u){var e=this._re.exec(u);if(!this._groups||!e)return e;for(var r in e.groups={},this._groups){var d=this._groups[r];e.groups[r]=e[d]}return e}}]),u}();u.exports={RegExpTree:r}},11895:u=>{u.exports={_hasUFlag:!1,shouldRun:function(u){return!!u.flags.includes("s")&&(u.flags=u.flags.replace("s",""),this._hasUFlag=u.flags.includes("u"),!0)},Char:function(u){var e=u.node;if("meta"===e.kind&&"."===e.value){var r="\\uFFFF",d="￿";this._hasUFlag&&(r="\\u{10FFFF}",d="􏿿"),u.replace({type:"CharacterClass",expressions:[{type:"ClassRange",from:{type:"Char",value:"\\0",kind:"decimal",symbol:"\0"},to:{type:"Char",value:r,kind:"unicode",symbol:d}}]})}}}},20466:u=>{u.exports={_groupNames:{},init:function(){this._groupNames={}},getExtra:function(){return this._groupNames},Group:function(u){var e=u.node;e.name&&(this._groupNames[e.name]=e.number,delete e.name,delete e.nameRaw)},Backreference:function(u){var e=u.node;"name"===e.kind&&(e.kind="number",e.reference=e.number,delete e.referenceRaw)}}},62080:u=>{u.exports={RegExp:function(u){var e=u.node;e.flags.includes("x")&&(e.flags=e.flags.replace("x",""))}}},24845:(u,e,r)=>{u.exports={dotAll:r(11895),namedCapturingGroups:r(20466),xFlag:r(62080)}},44340:u=>{function e(u){return u?r[u.type](u):""}var r={RegExp:function(u){return"/"+e(u.body)+"/"+u.flags},Alternative:function(u){return(u.expressions||[]).map(e).join("")},Disjunction:function(u){return e(u.left)+"|"+e(u.right)},Group:function(u){var r=e(u.expression);return u.capturing?u.name?"(?<"+(u.nameRaw||u.name)+">"+r+")":"("+r+")":"(?:"+r+")"},Backreference:function(u){switch(u.kind){case"number":return"\\"+u.reference;case"name":return"\\k<"+(u.referenceRaw||u.reference)+">";default:throw new TypeError("Unknown Backreference kind: "+u.kind)}},Assertion:function(u){switch(u.kind){case"^":case"$":case"\\b":case"\\B":return u.kind;case"Lookahead":var r=e(u.assertion);return u.negative?"(?!"+r+")":"(?="+r+")";case"Lookbehind":var d=e(u.assertion);return u.negative?"(?<!"+d+")":"(?<="+d+")";default:throw new TypeError("Unknown Assertion kind: "+u.kind)}},CharacterClass:function(u){var r=u.expressions.map(e).join("");return u.negative?"[^"+r+"]":"["+r+"]"},ClassRange:function(u){return e(u.from)+"-"+e(u.to)},Repetition:function(u){return""+e(u.expression)+e(u.quantifier)},Quantifier:function(u){var e=void 0,r=u.greedy?"":"?";switch(u.kind){case"+":case"?":case"*":e=u.kind;break;case"Range":e=u.from===u.to?"{"+u.from+"}":u.to?"{"+u.from+","+u.to+"}":"{"+u.from+",}";break;default:throw new TypeError("Unknown Quantifier kind: "+u.kind)}return""+e+r},Char:function(u){var e=u.value;switch(u.kind){case"simple":return u.escaped?"\\"+e:e;case"hex":case"unicode":case"oct":case"decimal":case"control":case"meta":return e;default:throw new TypeError("Unknown Char kind: "+u.kind)}},UnicodeProperty:function(u){return"\\"+(u.negative?"P":"p")+"{"+(u.shorthand||u.binary?"":u.name+"=")+u.value+"}"}};u.exports={generate:e}},37791:u=>{var e=function(u,e){if(Array.isArray(u))return u;if(Symbol.iterator in Object(u))return function(u,e){var r=[],d=!0,n=!1,t=void 0;try{for(var a,f=u[Symbol.iterator]();!(d=(a=f.next()).done)&&(r.push(a.value),!e||r.length!==e);d=!0);}catch(u){n=!0,t=u}finally{try{!d&&f.return&&f.return()}finally{if(n)throw t}}return r}(u,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")};function r(u){if(Array.isArray(u)){for(var e=0,r=Array(u.length);e<u.length;e++)r[e]=u[e];return r}return Array.from(u)}var d=null;function n(u,e){if(!e)return!1;if(u.length!==e.length)return!1;for(var d=0;d<u.length;d++){var n=u[d],t=e[d];if(n.size!==t.size)return!1;if([].concat(r(n)).sort().join(",")!==[].concat(r(t)).sort().join(","))return!1}return!0}function t(u,e,r,d){var n=!0,t=!1,f=void 0;try{for(var c,i=d[Symbol.iterator]();!(n=(c=i.next()).done);n=!0)if(!a(u,e,r,c.value))return!1}catch(u){t=!0,f=u}finally{try{!n&&i.return&&i.return()}finally{if(t)throw f}}return!0}function a(u,e,r,n){if(!d[u]||!d[e])return!1;var t=r[u][n],a=r[e][n];return!t&&!a||d[u].has(t)&&d[e].has(a)}u.exports={minimize:function(u){var a=u.getTransitionTable(),f=Object.keys(a),c=u.getAlphabet(),i=u.getAcceptingStateNumbers();d={};var o=new Set;f.forEach((function(u){u=Number(u),i.has(u)?d[u]=i:(o.add(u),d[u]=o)}));var s=[[o,i].filter((function(u){return u.size>0}))],l=void 0,b=void 0;l=s[s.length-1],b=s[s.length-2];for(var p=function(){var u,e={},n=!0,f=!1,i=void 0;try{for(var o,p=l[Symbol.iterator]();!(n=(o=p.next()).done);n=!0){var h=o.value,g={},v=(u=h,Array.isArray(u)?u:Array.from(u)),y=v[0],m=v.slice(1);g[y]=new Set([y]);var _=!0,C=!1,S=void 0;try{u:for(var x,A=m[Symbol.iterator]();!(_=(x=A.next()).done);_=!0){var k=x.value,P=!0,E=!1,w=void 0;try{for(var T,O=Object.keys(g)[Symbol.iterator]();!(P=(T=O.next()).done);P=!0){var R=T.value;if(t(k,R,a,c)){g[R].add(k),g[k]=g[R];continue u}}}catch(u){E=!0,w=u}finally{try{!P&&O.return&&O.return()}finally{if(E)throw w}}g[k]=new Set([k])}}catch(u){C=!0,S=u}finally{try{!_&&A.return&&A.return()}finally{if(C)throw S}}Object.assign(e,g)}}catch(u){f=!0,i=u}finally{try{!n&&p.return&&p.return()}finally{if(f)throw i}}d=e;var N=new Set(Object.keys(e).map((function(u){return e[u]})));s.push([].concat(r(N))),l=s[s.length-1],b=s[s.length-2]};!n(l,b);)p();var h=new Map,g=1;l.forEach((function(u){return h.set(u,g++)}));var v={},y=new Set,m=function(u,e){var r=!0,d=!1,n=void 0;try{for(var t,a=u[Symbol.iterator]();!(r=(t=a.next()).done);r=!0){var f=t.value;i.has(f)&&y.add(e)}}catch(u){d=!0,n=u}finally{try{!r&&a.return&&a.return()}finally{if(d)throw n}}},_=!0,C=!1,S=void 0;try{for(var x,A=h.entries()[Symbol.iterator]();!(_=(x=A.next()).done);_=!0){var k=x.value,P=e(k,2),E=P[0],w=P[1];v[w]={};var T=!0,O=!1,R=void 0;try{for(var N,L=c[Symbol.iterator]();!(T=(N=L.next()).done);T=!0){var I=N.value;m(E,w);var F=void 0,D=!0,M=!1,G=void 0;try{for(var j,B=E[Symbol.iterator]();!(D=(j=B.next()).done);D=!0){var U=j.value;if(F=a[U][I])break}}catch(u){M=!0,G=u}finally{try{!D&&B.return&&B.return()}finally{if(M)throw G}}F&&(v[w][I]=h.get(d[F]))}}catch(u){O=!0,R=u}finally{try{!T&&L.return&&L.return()}finally{if(O)throw R}}}}catch(u){C=!0,S=u}finally{try{!_&&A.return&&A.return()}finally{if(C)throw S}}return u.setTransitionTable(v),u.setAcceptingStateNumbers(y),u}}},24210:(u,e,r)=>{var d=function(){function u(u,e){for(var r=0;r<e.length;r++){var d=e[r];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(u,d.key,d)}}return function(e,r,d){return r&&u(e.prototype,r),d&&u(e,d),e}}();function n(u){if(Array.isArray(u)){for(var e=0,r=Array(u.length);e<u.length;e++)r[e]=u[e];return r}return Array.from(u)}var t=r(37791),a=r(28200).EPSILON_CLOSURE,f=function(){function u(e){!function(u,e){if(!(u instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u),this._nfa=e}return d(u,[{key:"minimize",value:function(){this.getTransitionTable(),this._originalAcceptingStateNumbers=this._acceptingStateNumbers,this._originalTransitionTable=this._transitionTable,t.minimize(this)}},{key:"getAlphabet",value:function(){return this._nfa.getAlphabet()}},{key:"getAcceptingStateNumbers",value:function(){return this._acceptingStateNumbers||this.getTransitionTable(),this._acceptingStateNumbers}},{key:"getOriginaAcceptingStateNumbers",value:function(){return this._originalAcceptingStateNumbers||this.getTransitionTable(),this._originalAcceptingStateNumbers}},{key:"setTransitionTable",value:function(u){this._transitionTable=u}},{key:"setAcceptingStateNumbers",value:function(u){this._acceptingStateNumbers=u}},{key:"getTransitionTable",value:function(){var u=this;if(this._transitionTable)return this._transitionTable;var e=this._nfa.getTransitionTable(),r=Object.keys(e);this._acceptingStateNumbers=new Set;for(var d=[e[r[0]][a]],t=this.getAlphabet(),f=this._nfa.getAcceptingStateNumbers(),c={},i=function(e){var r=!0,d=!1,n=void 0;try{for(var t,a=f[Symbol.iterator]();!(r=(t=a.next()).done);r=!0){var c=t.value;if(-1!==e.indexOf(c)){u._acceptingStateNumbers.add(e.join(","));break}}}catch(u){d=!0,n=u}finally{try{!r&&a.return&&a.return()}finally{if(d)throw n}}};d.length>0;){var o=d.shift(),s=o.join(",");c[s]={};var l=!0,b=!1,p=void 0;try{for(var h,g=t[Symbol.iterator]();!(l=(h=g.next()).done);l=!0){var v=h.value,y=[];i(o);var m=!0,_=!1,C=void 0;try{for(var S,x=o[Symbol.iterator]();!(m=(S=x.next()).done);m=!0){var A=e[S.value][v];if(A){var k=!0,P=!1,E=void 0;try{for(var w,T=A[Symbol.iterator]();!(k=(w=T.next()).done);k=!0){var O=w.value;e[O]&&y.push.apply(y,n(e[O][a]))}}catch(u){P=!0,E=u}finally{try{!k&&T.return&&T.return()}finally{if(P)throw E}}}}}catch(u){_=!0,C=u}finally{try{!m&&x.return&&x.return()}finally{if(_)throw C}}var R=new Set(y),N=[].concat(n(R));if(N.length>0){var L=N.join(",");c[s][v]=L,c.hasOwnProperty(L)||d.unshift(N)}}}catch(u){b=!0,p=u}finally{try{!l&&g.return&&g.return()}finally{if(b)throw p}}}return this._transitionTable=this._remapStateNumbers(c)}},{key:"_remapStateNumbers",value:function(u){var e={};this._originalTransitionTable=u;var r={};for(var d in Object.keys(u).forEach((function(u,r){e[u]=r+1})),u){var n=u[d],t={};for(var a in n)t[a]=e[n[a]];r[e[d]]=t}this._originalAcceptingStateNumbers=this._acceptingStateNumbers,this._acceptingStateNumbers=new Set;var f=!0,c=!1,i=void 0;try{for(var o,s=this._originalAcceptingStateNumbers[Symbol.iterator]();!(f=(o=s.next()).done);f=!0){var l=o.value;this._acceptingStateNumbers.add(e[l])}}catch(u){c=!0,i=u}finally{try{!f&&s.return&&s.return()}finally{if(c)throw i}}return r}},{key:"getOriginalTransitionTable",value:function(){return this._originalTransitionTable||this.getTransitionTable(),this._originalTransitionTable}},{key:"matches",value:function(u){for(var e=1,r=0,d=this.getTransitionTable();u[r];)if(!(e=d[e][u[r++]]))return!1;return!!this.getAcceptingStateNumbers().has(e)}}]),u}();u.exports=f},76186:(u,e,r)=>{var d=r(54149),n=r(24210),t=r(22388),a=r(32651);u.exports={NFA:d,DFA:n,builders:a,toNFA:function(u){return t.build(u)},toDFA:function(u){return new n(this.toNFA(u))},test:function(u,e){return this.toDFA(u).matches(e)}}},32651:(u,e,r)=>{var d=r(54149),n=r(78679),t=r(28200).EPSILON;function a(u){var e=new n,r=new n({accepting:!0});return new d(e.addTransition(u,r),r)}function f(u,e){return u.out.accepting=!1,e.out.accepting=!0,u.out.addTransition(t,e.in),new d(u.in,e.out)}function c(u,e){var r=new n,a=new n;return r.addTransition(t,u.in),r.addTransition(t,e.in),a.accepting=!0,u.out.accepting=!1,e.out.accepting=!1,u.out.addTransition(t,a),e.out.addTransition(t,a),new d(r,a)}u.exports={alt:function(u){for(var e=arguments.length,r=Array(e>1?e-1:0),d=1;d<e;d++)r[d-1]=arguments[d];var n=!0,t=!1,a=void 0;try{for(var c,i=r[Symbol.iterator]();!(n=(c=i.next()).done);n=!0)u=f(u,c.value)}catch(u){t=!0,a=u}finally{try{!n&&i.return&&i.return()}finally{if(t)throw a}}return u},char:a,e:function(){return a(t)},or:function(u){for(var e=arguments.length,r=Array(e>1?e-1:0),d=1;d<e;d++)r[d-1]=arguments[d];var n=!0,t=!1,a=void 0;try{for(var f,i=r[Symbol.iterator]();!(n=(f=i.next()).done);n=!0)u=c(u,f.value)}catch(u){t=!0,a=u}finally{try{!n&&i.return&&i.return()}finally{if(t)throw a}}return u},rep:function(u){return u.in.addTransition(t,u.out),u.out.addTransition(t,u.in),u},repExplicit:function(u){var e=new n,r=new n({accepting:!0});return e.addTransition(t,u.in),e.addTransition(t,r),u.out.accepting=!1,u.out.addTransition(t,r),r.addTransition(t,u.in),new d(e,r)},plusRep:function(u){return u.out.addTransition(t,u.in),u},questionRep:function(u){return u.in.addTransition(t,u.out),u}}},22388:(u,e,r)=>{var d=r(46418),n=r(32651),t=n.alt,a=n.char,f=n.or,c=n.rep,i=n.plusRep,o=n.questionRep;function s(u){if(u&&!l[u.type])throw new Error(u.type+" is not supported in NFA/DFA interpreter.");return u?l[u.type](u):""}var l={RegExp:function(u){if(""!==u.flags)throw new Error("NFA/DFA: Flags are not supported yet.");return s(u.body)},Alternative:function(u){var e=(u.expressions||[]).map(s);return t.apply(void 0,function(u){if(Array.isArray(u)){for(var e=0,r=Array(u.length);e<u.length;e++)r[e]=u[e];return r}return Array.from(u)}(e))},Disjunction:function(u){return f(s(u.left),s(u.right))},Repetition:function(u){switch(u.quantifier.kind){case"*":return c(s(u.expression));case"+":return i(s(u.expression));case"?":return o(s(u.expression));default:throw new Error("Unknown repeatition: "+u.quantifier.kind+".")}},Char:function(u){if("simple"!==u.kind)throw new Error("NFA/DFA: Only simple chars are supported yet.");return a(u.value)},Group:function(u){return s(u.expression)}};u.exports={build:function(u){var e=u;return u instanceof RegExp&&(u=""+u),"string"==typeof u&&(e=d.parse(u,{captureLocations:!0})),s(e)}}},78679:(u,e,r)=>{var d=function(){function u(u,e){for(var r=0;r<e.length;r++){var d=e[r];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(u,d.key,d)}}return function(e,r,d){return r&&u(e.prototype,r),d&&u(e,d),e}}(),n=r(76146),t=r(28200).EPSILON,a=function(u){function e(){return function(u,e){if(!(u instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(u,e){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?u:e}(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(u,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);u.prototype=Object.create(e&&e.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(u,e):u.__proto__=e)}(e,u),d(e,[{key:"matches",value:function(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Set;if(e.has(this))return!1;if(e.add(this),0===u.length){if(this.accepting)return!0;var r=!0,d=!1,n=void 0;try{for(var a,f=this.getTransitionsOnSymbol(t)[Symbol.iterator]();!(r=(a=f.next()).done);r=!0)if(a.value.matches("",e))return!0}catch(u){d=!0,n=u}finally{try{!r&&f.return&&f.return()}finally{if(d)throw n}}return!1}var c=u[0],i=u.slice(1),o=this.getTransitionsOnSymbol(c),s=!0,l=!1,b=void 0;try{for(var p,h=o[Symbol.iterator]();!(s=(p=h.next()).done);s=!0)if(p.value.matches(i))return!0}catch(u){l=!0,b=u}finally{try{!s&&h.return&&h.return()}finally{if(l)throw b}}var g=!0,v=!1,y=void 0;try{for(var m,_=this.getTransitionsOnSymbol(t)[Symbol.iterator]();!(g=(m=_.next()).done);g=!0)if(m.value.matches(u,e))return!0}catch(u){v=!0,y=u}finally{try{!g&&_.return&&_.return()}finally{if(v)throw y}}return!1}},{key:"getEpsilonClosure",value:function(){var u=this;return this._epsilonClosure||function(){var e=u.getTransitionsOnSymbol(t),r=u._epsilonClosure=new Set;r.add(u);var d=!0,n=!1,a=void 0;try{for(var f,c=e[Symbol.iterator]();!(d=(f=c.next()).done);d=!0){var i=f.value;r.has(i)||(r.add(i),i.getEpsilonClosure().forEach((function(u){return r.add(u)})))}}catch(u){n=!0,a=u}finally{try{!d&&c.return&&c.return()}finally{if(n)throw a}}}(),this._epsilonClosure}}]),e}(n);u.exports=a},54149:(u,e,r)=>{var d=function(u,e){if(Array.isArray(u))return u;if(Symbol.iterator in Object(u))return function(u,e){var r=[],d=!0,n=!1,t=void 0;try{for(var a,f=u[Symbol.iterator]();!(d=(a=f.next()).done)&&(r.push(a.value),!e||r.length!==e);d=!0);}catch(u){n=!0,t=u}finally{try{!d&&f.return&&f.return()}finally{if(n)throw t}}return r}(u,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},n=function(){function u(u,e){for(var r=0;r<e.length;r++){var d=e[r];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(u,d.key,d)}}return function(e,r,d){return r&&u(e.prototype,r),d&&u(e,d),e}}(),t=r(28200),a=t.EPSILON,f=t.EPSILON_CLOSURE,c=function(){function u(e,r){!function(u,e){if(!(u instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u),this.in=e,this.out=r}return n(u,[{key:"matches",value:function(u){return this.in.matches(u)}},{key:"getAlphabet",value:function(){if(!this._alphabet){this._alphabet=new Set;var u=this.getTransitionTable();for(var e in u){var r=u[e];for(var d in r)d!==f&&this._alphabet.add(d)}}return this._alphabet}},{key:"getAcceptingStates",value:function(){return this._acceptingStates||this.getTransitionTable(),this._acceptingStates}},{key:"getAcceptingStateNumbers",value:function(){if(!this._acceptingStateNumbers){this._acceptingStateNumbers=new Set;var u=!0,e=!1,r=void 0;try{for(var d,n=this.getAcceptingStates()[Symbol.iterator]();!(u=(d=n.next()).done);u=!0){var t=d.value;this._acceptingStateNumbers.add(t.number)}}catch(u){e=!0,r=u}finally{try{!u&&n.return&&n.return()}finally{if(e)throw r}}}return this._acceptingStateNumbers}},{key:"getTransitionTable",value:function(){var u=this;if(!this._transitionTable){this._transitionTable={},this._acceptingStates=new Set;var e=new Set,r=new Set;!function n(t){if(!e.has(t)){e.add(t),t.number=e.size,u._transitionTable[t.number]={},t.accepting&&u._acceptingStates.add(t);var a=t.getTransitions(),f=!0,c=!1,i=void 0;try{for(var o,s=a[Symbol.iterator]();!(f=(o=s.next()).done);f=!0){var l=o.value,b=d(l,2),p=b[0],h=b[1],g=[];r.add(p);var v=!0,y=!1,m=void 0;try{for(var _,C=h[Symbol.iterator]();!(v=(_=C.next()).done);v=!0){var S=_.value;n(S),g.push(S.number)}}catch(u){y=!0,m=u}finally{try{!v&&C.return&&C.return()}finally{if(y)throw m}}u._transitionTable[t.number][p]=g}}catch(u){c=!0,i=u}finally{try{!f&&s.return&&s.return()}finally{if(c)throw i}}}}(this.in),e.forEach((function(e){delete u._transitionTable[e.number][a],u._transitionTable[e.number][f]=[].concat(function(u){if(Array.isArray(u)){for(var e=0,r=Array(u.length);e<u.length;e++)r[e]=u[e];return r}return Array.from(u)}(e.getEpsilonClosure())).map((function(u){return u.number}))}))}return this._transitionTable}}]),u}();u.exports=c},28200:u=>{u.exports={EPSILON:"ε",EPSILON_CLOSURE:"ε*"}},76146:u=>{var e=function(){function u(u,e){for(var r=0;r<e.length;r++){var d=e[r];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(u,d.key,d)}}return function(e,r,d){return r&&u(e.prototype,r),d&&u(e,d),e}}(),r=function(){function u(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).accepting,r=void 0!==e&&e;!function(u,e){if(!(u instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u),this._transitions=new Map,this.accepting=r}return e(u,[{key:"getTransitions",value:function(){return this._transitions}},{key:"addTransition",value:function(u,e){return this.getTransitionsOnSymbol(u).add(e),this}},{key:"getTransitionsOnSymbol",value:function(u){var e=this._transitions.get(u);return e||(e=new Set,this._transitions.set(u,e)),e}}]),u}();u.exports=r},33186:(u,e,r)=>{var d=r(64456),n=r(46418),t=r(42113),a=r(35292);u.exports={optimize:function(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.whitelist,f=void 0===r?[]:r,c=e.blacklist,i=void 0===c?[]:c,o=(f.length>0?f:Array.from(a.keys())).filter((function(u){return!i.includes(u)})),s=u;u instanceof RegExp&&(u=""+u),"string"==typeof u&&(s=n.parse(u));var l=new t.TransformResult(s),b=void 0;do{b=l.toString(),s=d(l.getAST()),o.forEach((function(u){if(!a.has(u))throw new Error("Unknown optimization-transform: "+u+". Available transforms are: "+Array.from(a.keys()).join(", "));var e=a.get(u),r=t.transform(s,e);r.toString()!==l.toString()&&(r.toString().length<=l.toString().length?l=r:s=d(l.getAST()))}))}while(l.toString()!==b);return l}}},30223:u=>{var e="A".codePointAt(0),r="Z".codePointAt(0);u.exports={_AZClassRanges:null,_hasUFlag:!1,init:function(u){this._AZClassRanges=new Set,this._hasUFlag=u.flags.includes("u")},shouldRun:function(u){return u.flags.includes("i")},Char:function(u){var d=u.node,n=u.parent;if(!isNaN(d.codePoint)&&(this._hasUFlag||!(d.codePoint>=4096))){if("ClassRange"===n.type){if(!(this._AZClassRanges.has(n)||(t=n,a=t.from,f=t.to,a.codePoint>=e&&a.codePoint<=r&&f.codePoint>=e&&f.codePoint<=r)))return;this._AZClassRanges.add(n)}var t,a,f,c=d.symbol.toLowerCase();c!==d.symbol&&(d.value=function(u,e){var r=u.codePointAt(0);if("decimal"===e.kind)return"\\"+r;if("oct"===e.kind)return"\\0"+r.toString(8);if("hex"===e.kind)return"\\x"+r.toString(16);if("unicode"===e.kind){if(e.isSurrogatePair){var d=function(u){var e=(u-65536)%1024+56320;return{lead:(Math.floor((u-65536)/1024)+55296).toString(16),trail:e.toString(16)}}(r),n=d.lead,t=d.trail;return"\\u"+"0".repeat(4-n.length)+n+"\\u"+"0".repeat(4-t.length)+t}if(e.value.includes("{"))return"\\u{"+r.toString(16)+"}";var a=r.toString(16);return"\\u"+"0".repeat(4-a.length)+a}return u}(c,d),d.symbol=c,d.codePoint=c.codePointAt(0))}}}},94251:u=>{function e(u,e){var t=r(u),a=r(e);if(t===a){if("ClassRange"===u.type&&"ClassRange"!==e.type)return-1;if("ClassRange"===e.type&&"ClassRange"!==u.type)return 1;if("ClassRange"===u.type&&"ClassRange"===e.type)return r(u.to)-r(e.to);if(d(u)&&d(e)||n(u)&&n(e))return u.value<e.value?-1:1}return t-a}function r(u){return"Char"===u.type?"-"===u.value||"control"===u.kind?1/0:"meta"===u.kind&&isNaN(u.codePoint)?-1:u.codePoint:u.from.codePoint}function d(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return"Char"===u.type&&"meta"===u.kind&&(e?u.value===e:/^\\[dws]$/i.test(u.value))}function n(u){return"Char"===u.type&&"control"===u.kind}function t(u,e,r){for(var d=0;d<e.length;d++)if(a(u,e[d],r))return!0;return!1}function a(u,e,r){return"ClassRange"===u.type?a(u.from,e,r)&&a(u.to,e,r):!("\\S"!==e||!d(u,"\\w")&&!d(u,"\\d"))||!("\\D"!==e||!d(u,"\\W")&&!d(u,"\\s"))||!("\\w"!==e||!d(u,"\\d"))||!("\\W"!==e||!d(u,"\\s"))||"Char"===u.type&&!isNaN(u.codePoint)&&("\\s"===e?f(u):"\\S"===e?!f(u):"\\d"===e?c(u):"\\D"===e?!c(u):"\\w"===e?i(u,r):"\\W"===e&&!i(u,r))}function f(u){return 9===u.codePoint||10===u.codePoint||11===u.codePoint||12===u.codePoint||13===u.codePoint||32===u.codePoint||160===u.codePoint||5760===u.codePoint||u.codePoint>=8192&&u.codePoint<=8202||8232===u.codePoint||8233===u.codePoint||8239===u.codePoint||8287===u.codePoint||12288===u.codePoint||65279===u.codePoint}function c(u){return u.codePoint>=48&&u.codePoint<=57}function i(u,e){return c(u)||u.codePoint>=65&&u.codePoint<=90||u.codePoint>=97&&u.codePoint<=122||"_"===u.value||e&&(383===u.codePoint||8490===u.codePoint)}function o(u,e){if(e&&"ClassRange"===e.type){if(l(u,e))return!0;if(p(u)&&e.to.codePoint===u.codePoint-1)return e.to=u,!0;if("ClassRange"===u.type&&u.from.codePoint<=e.to.codePoint+1&&u.to.codePoint>=e.from.codePoint-1)return u.from.codePoint<e.from.codePoint&&(e.from=u.from),u.to.codePoint>e.to.codePoint&&(e.to=u.to),!0}return!1}function s(u,e){return!(!e||"ClassRange"!==e.type||!p(u)||e.from.codePoint!==u.codePoint+1||(e.from=u,0))}function l(u,e){return("Char"!==u.type||!isNaN(u.codePoint))&&("ClassRange"===u.type?l(u.from,e)&&l(u.to,e):u.codePoint>=e.from.codePoint&&u.codePoint<=e.to.codePoint)}function b(u,e,r){if(!p(u))return 0;for(var d=0;e>0;){var n=r[e],t=r[e-1];if(!p(t)||t.codePoint!==n.codePoint-1)break;d++,e--}return d>1?(r[e]={type:"ClassRange",from:r[e],to:u},d):0}function p(u){return u&&"Char"===u.type&&!isNaN(u.codePoint)&&(i(u,!1)||"unicode"===u.kind||"hex"===u.kind||"oct"===u.kind||"decimal"===u.kind)}u.exports={_hasIUFlags:!1,init:function(u){this._hasIUFlags=u.flags.includes("i")&&u.flags.includes("u")},CharacterClass:function(u){var r=u.node.expressions,n=[];r.forEach((function(u){d(u)&&n.push(u.value)})),r.sort(e);for(var a=0;a<r.length;a++){var f=r[a];if(t(f,n,this._hasIUFlags)||o(f,r[a-1])||s(f,r[a+1]))r.splice(a,1),a--;else{var c=b(f,a,r);r.splice(a-c+1,c),a-=c}}}}},65501:u=>{u.exports={ClassRange:function(u){var e=u.node;e.from.codePoint===e.to.codePoint?u.replace(e.from):e.from.codePoint===e.to.codePoint-1&&(u.getParent().insertChildAt(e.to,u.index+1),u.replace(e.from))}}},56885:u=>{u.exports={CharacterClass:function(u){for(var e=u.node,r={},d=0;d<e.expressions.length;d++){var n=u.getChild(d),t=n.jsonEncode();r.hasOwnProperty(t)&&(n.remove(),d--),r[t]=!0}}}},77698:u=>{function e(u){if(Array.isArray(u)){for(var e=0,r=Array(u.length);e<u.length;e++)r[e]=u[e];return r}return Array.from(u)}u.exports={_hasIFlag:!1,_hasUFlag:!1,init:function(u){this._hasIFlag=u.flags.includes("i"),this._hasUFlag=u.flags.includes("u")},CharacterClass:function(u){!function(u){u.node.expressions.forEach((function(e,r){(function(u){return"ClassRange"===u.type&&"0"===u.from.value&&"9"===u.to.value})(e)&&u.getChild(r).replace({type:"Char",value:"\\d",kind:"meta"})}))}(u),function(u,e,r){var d=u.node,a=null,f=null,c=null,i=null,o=null,s=null;d.expressions.forEach((function(d,l){n(d,"\\d")?a=u.getChild(l):function(u){return"ClassRange"===u.type&&"a"===u.from.value&&"z"===u.to.value}(d)?f=u.getChild(l):function(u){return"ClassRange"===u.type&&"A"===u.from.value&&"Z"===u.to.value}(d)?c=u.getChild(l):function(u){return"Char"===u.type&&"_"===u.value&&"simple"===u.kind}(d)?i=u.getChild(l):e&&r&&t(d,383)?o=u.getChild(l):e&&r&&t(d,8490)&&(s=u.getChild(l))})),a&&(f&&c||e&&(f||c))&&i&&(!r||!e||o&&s)&&(a.replace({type:"Char",value:"\\w",kind:"meta"}),f&&f.remove(),c&&c.remove(),i.remove(),o&&o.remove(),s&&s.remove())}(u,this._hasIFlag,this._hasUFlag),function(u){var e=u.node;if(!(e.expressions.length<r.length)&&r.every((function(u){return e.expressions.some((function(e){return u(e)}))}))){var d=e.expressions.find((function(u){return n(u,"\\n")}));d.value="\\s",d.symbol=void 0,d.codePoint=NaN,e.expressions.map((function(e,d){return r.some((function(u){return u(e)}))?u.getChild(d):void 0})).filter(Boolean).forEach((function(u){return u.remove()}))}}(u)}};var r=[function(u){return d(u," ")}].concat(e(["\\f","\\n","\\r","\\t","\\v"].map((function(u){return function(e){return n(e,u)}}))),e([160,5760,8232,8233,8239,8287,12288,65279].map((function(u){return function(e){return t(e,u)}}))),[function(u){return"ClassRange"===u.type&&t(u.from,8192)&&t(u.to,8202)}]);function d(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"simple";return"Char"===u.type&&u.value===e&&u.kind===r}function n(u,e){return d(u,e,"meta")}function t(u,e){return"Char"===u.type&&"unicode"===u.kind&&u.codePoint===e}},77867:u=>{function e(u){return/[*[()+?$./{}|]/.test(u)}u.exports={CharacterClass:function(u){var r=u.node;if(1===r.expressions.length&&function(u){var e=u.parent,r=u.index;if("Alternative"!==e.type)return!0;var d=e.expressions[r-1];return null==d||("Backreference"!==d.type||"number"!==d.kind)&&("Char"!==d.type||"decimal"!==d.kind)}(u)&&function(u){return"Char"===u.type&&"\\b"!==u.value}(r.expressions[0])){var d=r.expressions[0],n=d.value,t=d.kind,a=d.escaped;if(r.negative){if(!function(u){return/^\\[dwsDWS]$/.test(u)}(n))return;n=function(u){return/[dws]/.test(u)?u.toUpperCase():u.toLowerCase()}(n)}u.replace({type:"Char",value:n,kind:t,escaped:a||e(n)})}}}},53540:u=>{var e="A".codePointAt(0),r="Z".codePointAt(0),d="a".codePointAt(0),n="z".codePointAt(0),t="0".codePointAt(0),a="9".codePointAt(0);u.exports={Char:function(u){var f,c,i,o=u.node,s=u.parent;if(!isNaN(o.codePoint)&&"simple"!==o.kind&&("ClassRange"!==s.type||(c=(f=s).from,i=f.to,c.codePoint>=t&&c.codePoint<=a&&i.codePoint>=t&&i.codePoint<=a||c.codePoint>=e&&c.codePoint<=r&&i.codePoint>=e&&i.codePoint<=r||c.codePoint>=d&&c.codePoint<=n&&i.codePoint>=d&&i.codePoint<=n))&&(l=o.codePoint)>=32&&l<=126){var l,b=String.fromCodePoint(o.codePoint),p={type:"Char",kind:"simple",value:b,symbol:b,codePoint:o.codePoint};(function(u,e){return"ClassRange"===e||"CharacterClass"===e?/[\]\\^-]/.test(u):/[*[()+?^$./\\|{}]/.test(u)})(b,s.type)&&(p.escaped=!0),u.replace(p)}}}},7421:u=>{function e(u,e,r){for(var d=u,n=(r?d>=0:d<e.expressions.length)&&e.expressions[d];n&&"Char"===n.type&&"simple"===n.kind&&!n.escaped&&/\d/.test(n.value);)r?d--:d++,n=(r?d>=0:d<e.expressions.length)&&e.expressions[d];return Math.abs(u-d)}function r(u,e){return u&&"Char"===u.type&&"simple"===u.kind&&!u.escaped&&u.value===e}u.exports={_hasXFlag:!1,init:function(u){this._hasXFlag=u.flags.includes("x")},Char:function(u){var d=u.node;d.escaped&&function(u,d){var n=u.node.value,t=u.index,a=u.parent;return"CharacterClass"!==a.type&&"ClassRange"!==a.type?!function(u,d,n,t){return"{"===u?function(u,d){if(null==u)return!1;var n=e(u+1,d),t=u+n+1,a=t<d.expressions.length&&d.expressions[t];if(n){if(r(a,"}"))return!0;if(r(a,","))return r(a=(t=t+(n=e(t+1,d))+1)<d.expressions.length&&d.expressions[t],"}")}return!1}(d,n):"}"===u?function(u,d){if(null==u)return!1;var n=e(u-1,d,!0),t=u-n-1,a=t>=0&&d.expressions[t];return!(!n||!r(a,"{"))||!!r(a,",")&&(a=(t=t-(n=e(t-1,d,!0))-1)<d.expressions.length&&d.expressions[t],n&&r(a,"{"))}(d,n):!(!t||!/[ #]/.test(u))||/[*[()+?^$./\\|]/.test(u)}(n,t,a,d):!function(u,e,r){return"^"===u?0===e&&!r.negative:"-"===u||/[\]\\]/.test(u)}(n,t,a)}(u,this._hasXFlag)&&delete d.escaped}}},87126:u=>{u.exports={shouldRun:function(u){return u.flags.includes("u")},Char:function(u){var e=u.node;"unicode"===e.kind&&e.isSurrogatePair&&!isNaN(e.codePoint)&&(e.value="\\u{"+e.codePoint.toString(16)+"}",delete e.isSurrogatePair)}}},92879:(u,e,r)=>{function d(u){if(Array.isArray(u)){for(var e=0,r=Array(u.length);e<u.length;e++)r[e]=u[e];return r}return Array.from(u)}var n=r(53636),t=r(33065).increaseQuantifierByOne;function a(u,e,r){for(var t=u.node,a=Math.ceil(r/2),f=0;f<a;){var c=r-2*f-1,i=void 0,o=void 0;if(0===f?(i=e,o=u.getChild(c)):(i=n.getForNode({type:"Alternative",expressions:[].concat(d(t.expressions.slice(r-f,r)),[e.node])}),o=n.getForNode({type:"Alternative",expressions:[].concat(d(t.expressions.slice(c,r-f)))})),i.hasEqualSource(o)){for(var s=0;s<2*f+1;s++)u.getChild(c).remove();return e.replace({type:"Repetition",expression:0===f&&"Repetition"!==i.node.type?i.node:{type:"Group",capturing:!1,expression:i.node},quantifier:{type:"Quantifier",kind:"Range",from:2,to:2,greedy:!0}}),c}f++}return r}function f(u,e,r){for(var a=u.node,f=0;f<r;){var c=u.getChild(f);if("Repetition"===c.node.type&&c.node.quantifier.greedy){var i=c.getChild(),o=void 0;if("Group"!==i.node.type||i.node.capturing||(i=i.getChild()),f+1===r?"Group"!==(o=e).node.type||o.node.capturing||(o=o.getChild()):o=n.getForNode({type:"Alternative",expressions:[].concat(d(a.expressions.slice(f+1,r+1)))}),i.hasEqualSource(o)){for(var s=f;s<r;s++)u.getChild(f+1).remove();return t(c.node.quantifier),f}}f++}return r}function c(u,e,r){var a=u.node;if("Repetition"===e.node.type&&e.node.quantifier.greedy){var f=e.getChild(),c=void 0;"Group"!==f.node.type||f.node.capturing||(f=f.getChild());var i=void 0;if("Alternative"===f.node.type?(i=f.node.expressions.length,c=n.getForNode({type:"Alternative",expressions:[].concat(d(a.expressions.slice(r-i,r)))})):(i=1,"Group"!==(c=u.getChild(r-1)).node.type||c.node.capturing||(c=c.getChild())),c.hasEqualSource(f)){for(var o=r-i;o<r;o++)u.getChild(r-i).remove();return t(e.node.quantifier),r-i}}return r}u.exports={Alternative:function(u){for(var e=u.node,r=1;r<e.expressions.length;){var d=u.getChild(r);if((r=Math.max(1,a(u,d,r)))>=e.expressions.length)break;if(d=u.getChild(r),(r=Math.max(1,f(u,d,r)))>=e.expressions.length)break;d=u.getChild(r),r=Math.max(1,c(u,d,r)),r++}}}},52340:(u,e,r)=>{var d=r(53636),n=r(33065),t=n.disjunctionToList,a=n.listToDisjunction;u.exports={Disjunction:function(u){var e=u.node,r={},n=t(e).filter((function(u){var e=u?d.getForNode(u).jsonEncode():"null";return!r.hasOwnProperty(e)&&(r[e]=u,!0)}));u.replace(a(n))}}},68794:u=>{u.exports={Disjunction:function(u){var d=u.node,n=u.parent;if(e[n.type]){var t=new Map;if(r(d,t)&&t.size){var a={type:"CharacterClass",expressions:Array.from(t.keys()).sort().map((function(u){return t.get(u)}))};e[n.type](u.getParent(),a)}}}};var e={RegExp:function(u,e){u.node.body=e},Group:function(u,e){var r=u.node;r.capturing?r.expression=e:u.replace(e)}};function r(u,e){if(!u)return!1;var d=u.type;if("Disjunction"===d){var n=u.left,t=u.right;return r(n,e)&&r(t,e)}if("Char"===d){if("meta"===u.kind&&"."===u.symbol)return!1;var a=u.value;return e.set(a,u),!0}return"CharacterClass"===d&&!u.negative&&u.expressions.every((function(u){return r(u,e)}))}},35292:(u,e,r)=>{u.exports=new Map([["charSurrogatePairToSingleUnicode",r(87126)],["charCodeToSimpleChar",r(53540)],["charCaseInsensitiveLowerCaseTransform",r(30223)],["charClassRemoveDuplicates",r(56885)],["quantifiersMerge",r(44035)],["quantifierRangeToSymbol",r(48264)],["charClassClassrangesToChars",r(65501)],["charClassToMeta",r(77698)],["charClassToSingleChar",r(77867)],["charEscapeUnescape",r(7421)],["charClassClassrangesMerge",r(94251)],["disjunctionRemoveDuplicates",r(52340)],["groupSingleCharsToCharClass",r(68794)],["removeEmptyGroup",r(74169)],["ungroup",r(47188)],["combineRepeatingPatterns",r(92879)]])},48264:u=>{u.exports={Quantifier:function(u){"Range"===u.node.kind&&(function(u){var e=u.node;0!==e.from||e.to||(e.kind="*",delete e.from)}(u),function(u){var e=u.node;1!==e.from||e.to||(e.kind="+",delete e.from)}(u),function(u){var e=u.node;1===e.from&&1===e.to&&u.parentPath.replace(u.parentPath.node.expression)}(u))}}},44035:(u,e,r)=>{var d=r(33065).increaseQuantifierByOne;function n(u){return u.greedy&&("+"===u.kind||"*"===u.kind||"Range"===u.kind&&!u.to)}function t(u){var e=void 0,r=void 0;return"*"===u.kind?e=0:"+"===u.kind?e=1:"?"===u.kind?(e=0,r=1):(e=u.from,u.to&&(r=u.to)),{from:e,to:r}}u.exports={Repetition:function(u){var e=u.node;if("Alternative"===u.parent.type&&u.index){var r=u.getPreviousSibling();if(r)if("Repetition"===r.node.type){if(!r.getChild().hasEqualSource(u.getChild()))return;var a=t(r.node.quantifier),f=a.from,c=a.to,i=t(e.quantifier),o=i.from,s=i.to;if(r.node.quantifier.greedy!==e.quantifier.greedy&&!n(r.node.quantifier)&&!n(e.quantifier))return;e.quantifier.kind="Range",e.quantifier.from=f+o,c&&s?e.quantifier.to=c+s:delete e.quantifier.to,(n(r.node.quantifier)||n(e.quantifier))&&(e.quantifier.greedy=!0),r.remove()}else{if(!r.hasEqualSource(u.getChild()))return;d(e.quantifier),r.remove()}}}}},74169:u=>{u.exports={Group:function(u){var e=u.node,r=u.parent,d=u.getChild();e.capturing||d||("Repetition"===r.type?u.getParent().replace(e):"RegExp"!==r.type&&u.remove())}}},47188:u=>{function e(u){if(Array.isArray(u)){for(var e=0,r=Array(u.length);e<u.length;e++)r[e]=u[e];return r}return Array.from(u)}u.exports={Group:function(u){var r=u.node,d=u.parent,n=u.getChild();if(!r.capturing&&n&&function(u){var e=u.parent,r=u.index;if("Alternative"!==e.type)return!0;var d=e.expressions[r-1];return null==d||("Backreference"!==d.type||"number"!==d.kind)&&("Char"!==d.type||"decimal"!==d.kind)}(u)&&!("Disjunction"===n.node.type&&"RegExp"!==d.type||"Repetition"===d.type&&"Char"!==n.node.type&&"CharacterClass"!==n.node.type))if("Alternative"===n.node.type){var t=u.getParent();"Alternative"===t.node.type&&t.replace({type:"Alternative",expressions:[].concat(e(d.expressions.slice(0,u.index)),e(n.node.expressions),e(d.expressions.slice(u.index+1)))})}else u.replace(n.node)}}},19495:(u,e,r)=>{function d(u){if(Array.isArray(u)){for(var e=0,r=Array(u.length);e<u.length;e++)r[e]=u[e];return r}return Array.from(u)}var n=void 0,t={},a=void 0,f=void 0;function c(u,e){return t.options.captureLocations?u&&e?{startOffset:u.startOffset,endOffset:e.endOffset,startLine:u.startLine,endLine:e.endLine,startColumn:u.startColumn,endColumn:e.endColumn}:u||e:null}var i=[[-1,1,function(u,e){f=c(e,e),a=u}],[0,4,function(u,e,r,d,n,t,i,o){f=c(n,o),a=L({type:"RegExp",body:e,flags:P(d)},I(n,o||i))}],[1,1,function(u,e){f=c(e,e),a=u}],[1,0,function(){f=null,a=""}],[2,1,function(u,e){f=c(e,e),a=u}],[2,2,function(u,e,r,d){f=c(r,d),a=u+e}],[3,1,function(u,e){f=c(e,e),a=u}],[4,1,function(u,e){f=c(e,e),a=u}],[4,3,function(u,e,r,d,n,t){f=c(d,t);var i=null;n&&(i=I(d||n,t||n)),a=L({type:"Disjunction",left:u,right:r},i)}],[5,1,function(u,e){f=c(e,e),a=0!==u.length?1===u.length?L(u[0],f):L({type:"Alternative",expressions:u},f):null}],[6,0,function(){f=null,a=[]}],[6,2,function(u,e,r,d){f=c(r,d),a=u.concat(e)}],[7,1,function(u,e){f=c(e,e),a=L(Object.assign({type:"Assertion"},u),f)}],[7,2,function(u,e,r,d){f=c(r,d),a=u,e&&(a=L({type:"Repetition",expression:u,quantifier:e},f))}],[8,1,function(u,e){f=c(e,e),a={kind:"^"}}],[8,1,function(u,e){f=c(e,e),a={kind:"$"}}],[8,1,function(u,e){f=c(e,e),a={kind:"\\b"}}],[8,1,function(u,e){f=c(e,e),a={kind:"\\B"}}],[8,3,function(u,e,r,d,n,t){f=c(d,t),a={kind:"Lookahead",assertion:e}}],[8,3,function(u,e,r,d,n,t){f=c(d,t),a={kind:"Lookahead",negative:!0,assertion:e}}],[8,3,function(u,e,r,d,n,t){f=c(d,t),a={kind:"Lookbehind",assertion:e}}],[8,3,function(u,e,r,d,n,t){f=c(d,t),a={kind:"Lookbehind",negative:!0,assertion:e}}],[9,1,function(u,e){f=c(e,e),a=u}],[9,1,function(u,e){f=c(e,e),a=u}],[9,1,function(u,e){f=c(e,e),a=u}],[10,1,function(u,e){f=c(e,e),a=A(u,"simple",f)}],[10,1,function(u,e){f=c(e,e),(a=A(u.slice(1),"simple",f)).escaped=!0}],[10,1,function(u,e){f=c(e,e),(a=A(u,"unicode",f)).isSurrogatePair=!0}],[10,1,function(u,e){f=c(e,e),a=A(u,"unicode",f)}],[10,1,function(u,e){f=c(e,e),a=function(u,e){var r="P"===u[1],d=u.indexOf("="),n=u.slice(3,-1!==d?d:-1),t=void 0,a=-1===d&&x.isGeneralCategoryValue(n),f=-1===d&&x.isBinaryPropertyName(n);if(a)t=n,n="General_Category";else if(f)t=n;else{if(!x.isValidName(n))throw new SyntaxError("Invalid unicode property name: "+n+".");if(t=u.slice(d+1,-1),!x.isValidValue(n,t))throw new SyntaxError("Invalid "+n+" unicode property value: "+t+".")}return L({type:"UnicodeProperty",name:n,value:t,negative:r,shorthand:a,binary:f,canonicalName:x.getCanonicalName(n)||n,canonicalValue:x.getCanonicalValue(t)||t},e)}(u,f)}],[10,1,function(u,e){f=c(e,e),a=A(u,"control",f)}],[10,1,function(u,e){f=c(e,e),a=A(u,"hex",f)}],[10,1,function(u,e){f=c(e,e),a=A(u,"oct",f)}],[10,1,function(u,e){var r,d,n;f=c(e,e),r=u,d=f,n=Number(r.slice(1)),a=n>0&&n<=y?L({type:"Backreference",kind:"number",number:n,reference:n},d):A(r,"decimal",d)}],[10,1,function(u,e){f=c(e,e),a=A(u,"meta",f)}],[10,1,function(u,e){f=c(e,e),a=A(u,"meta",f)}],[10,1,function(u,e){f=c(e,e),a=function(u,e){var r=u.slice(3,-1),d=N(r);if(m.hasOwnProperty(d))return L({type:"Backreference",kind:"name",number:m[d],reference:d,referenceRaw:r},e);var n=null,t=null,a=null,f=null;e&&(n=e.startOffset,t=e.startLine,a=e.endLine,f=e.startColumn);var c=/^[\w$<>]/,i=void 0,o=[A(u.slice(1,2),"simple",n?{startLine:t,endLine:a,startColumn:f,startOffset:n,endOffset:n+=2,endColumn:f+=2}:null)];for(o[0].escaped=!0,u=u.slice(2);u.length>0;){var s=null;(s=u.match(E))||(s=u.match(w))?(n&&(i={startLine:t,endLine:a,startColumn:f,startOffset:n,endOffset:n+=s[0].length,endColumn:f+=s[0].length}),o.push(A(s[0],"unicode",i)),u=u.slice(s[0].length)):(s=u.match(c))&&(n&&(i={startLine:t,endLine:a,startColumn:f,startOffset:n,endOffset:++n,endColumn:++f}),o.push(A(s[0],"simple",i)),u=u.slice(1))}return o}(u,e)}],[11,1,function(u,e){f=c(e,e),a=u}],[11,0],[12,1,function(u,e){f=c(e,e),a=u}],[12,2,function(u,e,r,d){f=c(r,d),u.greedy=!1,a=u}],[13,1,function(u,e){f=c(e,e),a=L({type:"Quantifier",kind:u,greedy:!0},f)}],[13,1,function(u,e){f=c(e,e),a=L({type:"Quantifier",kind:u,greedy:!0},f)}],[13,1,function(u,e){f=c(e,e),a=L({type:"Quantifier",kind:u,greedy:!0},f)}],[13,1,function(u,e){f=c(e,e);var r=C(u);a=L({type:"Quantifier",kind:"Range",from:r[0],to:r[0],greedy:!0},f)}],[13,1,function(u,e){f=c(e,e),a=L({type:"Quantifier",kind:"Range",from:C(u)[0],greedy:!0},f)}],[13,1,function(u,e){f=c(e,e);var r=C(u);a=L({type:"Quantifier",kind:"Range",from:r[0],to:r[1],greedy:!0},f)}],[14,1,function(u,e){f=c(e,e),a=u}],[14,1,function(u,e){f=c(e,e),a=u}],[15,3,function(u,e,r,d,n,i){f=c(d,i);var o=String(u),s=N(o);if(!t.options.allowGroupNameDuplicates&&m.hasOwnProperty(s))throw new SyntaxError('Duplicate of the named group "'+s+'".');m[s]=u.groupNumber,a=L({type:"Group",capturing:!0,name:s,nameRaw:o,number:u.groupNumber,expression:e},f)}],[15,3,function(u,e,r,d,n,t){f=c(d,t),a=L({type:"Group",capturing:!0,number:u.groupNumber,expression:e},f)}],[16,3,function(u,e,r,d,n,t){f=c(d,t),a=L({type:"Group",capturing:!1,expression:e},f)}],[17,3,function(u,e,r,d,n,t){f=c(d,t),a=L({type:"CharacterClass",negative:!0,expressions:e},f)}],[17,3,function(u,e,r,d,n,t){f=c(d,t),a=L({type:"CharacterClass",expressions:e},f)}],[18,0,function(){f=null,a=[]}],[18,1,function(u,e){f=c(e,e),a=u}],[19,1,function(u,e){f=c(e,e),a=[u]}],[19,2,function(u,e,r,d){f=c(r,d),a=[u].concat(e)}],[19,4,function(u,e,r,d,n,t,i,o){f=c(n,o),S(u,r),a=[L({type:"ClassRange",from:u,to:r},I(n,i))],d&&(a=a.concat(d))}],[20,1,function(u,e){f=c(e,e),a=u}],[20,2,function(u,e,r,d){f=c(r,d),a=[u].concat(e)}],[20,4,function(u,e,r,d,n,t,i,o){f=c(n,o),S(u,r),a=[L({type:"ClassRange",from:u,to:r},I(n,i))],d&&(a=a.concat(d))}],[21,1,function(u,e){f=c(e,e),a=A(u,"simple",f)}],[21,1,function(u,e){f=c(e,e),a=u}],[22,1,function(u,e){f=c(e,e),a=u}],[22,1,function(u,e){f=c(e,e),a=A(u,"meta",f)}]],o={SLASH:"23",CHAR:"24",BAR:"25",BOS:"26",EOS:"27",ESC_b:"28",ESC_B:"29",POS_LA_ASSERT:"30",R_PAREN:"31",NEG_LA_ASSERT:"32",POS_LB_ASSERT:"33",NEG_LB_ASSERT:"34",ESC_CHAR:"35",U_CODE_SURROGATE:"36",U_CODE:"37",U_PROP_VALUE_EXP:"38",CTRL_CH:"39",HEX_CODE:"40",OCT_CODE:"41",DEC_CODE:"42",META_CHAR:"43",ANY:"44",NAMED_GROUP_REF:"45",Q_MARK:"46",STAR:"47",PLUS:"48",RANGE_EXACT:"49",RANGE_OPEN:"50",RANGE_CLOSED:"51",NAMED_CAPTURE_GROUP:"52",L_PAREN:"53",NON_CAPTURE_GROUP:"54",NEG_CLASS:"55",R_BRACKET:"56",L_BRACKET:"57",DASH:"58",$:"59"},s=[{0:1,23:"s2"},{59:"acc"},{3:3,4:4,5:5,6:6,23:"r10",24:"r10",25:"r10",26:"r10",27:"r10",28:"r10",29:"r10",30:"r10",32:"r10",33:"r10",34:"r10",35:"r10",36:"r10",37:"r10",38:"r10",39:"r10",40:"r10",41:"r10",42:"r10",43:"r10",44:"r10",45:"r10",52:"r10",53:"r10",54:"r10",55:"r10",57:"r10"},{23:"s7"},{23:"r6",25:"s12"},{23:"r7",25:"r7",31:"r7"},{7:14,8:15,9:16,10:25,14:27,15:42,16:43,17:26,23:"r9",24:"s28",25:"r9",26:"s17",27:"s18",28:"s19",29:"s20",30:"s21",31:"r9",32:"s22",33:"s23",34:"s24",35:"s29",36:"s30",37:"s31",38:"s32",39:"s33",40:"s34",41:"s35",42:"s36",43:"s37",44:"s38",45:"s39",52:"s44",53:"s45",54:"s46",55:"s40",57:"s41"},{1:8,2:9,24:"s10",59:"r3"},{59:"r1"},{24:"s11",59:"r2"},{24:"r4",59:"r4"},{24:"r5",59:"r5"},{5:13,6:6,23:"r10",24:"r10",25:"r10",26:"r10",27:"r10",28:"r10",29:"r10",30:"r10",31:"r10",32:"r10",33:"r10",34:"r10",35:"r10",36:"r10",37:"r10",38:"r10",39:"r10",40:"r10",41:"r10",42:"r10",43:"r10",44:"r10",45:"r10",52:"r10",53:"r10",54:"r10",55:"r10",57:"r10"},{23:"r8",25:"r8",31:"r8"},{23:"r11",24:"r11",25:"r11",26:"r11",27:"r11",28:"r11",29:"r11",30:"r11",31:"r11",32:"r11",33:"r11",34:"r11",35:"r11",36:"r11",37:"r11",38:"r11",39:"r11",40:"r11",41:"r11",42:"r11",43:"r11",44:"r11",45:"r11",52:"r11",53:"r11",54:"r11",55:"r11",57:"r11"},{23:"r12",24:"r12",25:"r12",26:"r12",27:"r12",28:"r12",29:"r12",30:"r12",31:"r12",32:"r12",33:"r12",34:"r12",35:"r12",36:"r12",37:"r12",38:"r12",39:"r12",40:"r12",41:"r12",42:"r12",43:"r12",44:"r12",45:"r12",52:"r12",53:"r12",54:"r12",55:"r12",57:"r12"},{11:47,12:48,13:49,23:"r38",24:"r38",25:"r38",26:"r38",27:"r38",28:"r38",29:"r38",30:"r38",31:"r38",32:"r38",33:"r38",34:"r38",35:"r38",36:"r38",37:"r38",38:"r38",39:"r38",40:"r38",41:"r38",42:"r38",43:"r38",44:"r38",45:"r38",46:"s52",47:"s50",48:"s51",49:"s53",50:"s54",51:"s55",52:"r38",53:"r38",54:"r38",55:"r38",57:"r38"},{23:"r14",24:"r14",25:"r14",26:"r14",27:"r14",28:"r14",29:"r14",30:"r14",31:"r14",32:"r14",33:"r14",34:"r14",35:"r14",36:"r14",37:"r14",38:"r14",39:"r14",40:"r14",41:"r14",42:"r14",43:"r14",44:"r14",45:"r14",52:"r14",53:"r14",54:"r14",55:"r14",57:"r14"},{23:"r15",24:"r15",25:"r15",26:"r15",27:"r15",28:"r15",29:"r15",30:"r15",31:"r15",32:"r15",33:"r15",34:"r15",35:"r15",36:"r15",37:"r15",38:"r15",39:"r15",40:"r15",41:"r15",42:"r15",43:"r15",44:"r15",45:"r15",52:"r15",53:"r15",54:"r15",55:"r15",57:"r15"},{23:"r16",24:"r16",25:"r16",26:"r16",27:"r16",28:"r16",29:"r16",30:"r16",31:"r16",32:"r16",33:"r16",34:"r16",35:"r16",36:"r16",37:"r16",38:"r16",39:"r16",40:"r16",41:"r16",42:"r16",43:"r16",44:"r16",45:"r16",52:"r16",53:"r16",54:"r16",55:"r16",57:"r16"},{23:"r17",24:"r17",25:"r17",26:"r17",27:"r17",28:"r17",29:"r17",30:"r17",31:"r17",32:"r17",33:"r17",34:"r17",35:"r17",36:"r17",37:"r17",38:"r17",39:"r17",40:"r17",41:"r17",42:"r17",43:"r17",44:"r17",45:"r17",52:"r17",53:"r17",54:"r17",55:"r17",57:"r17"},{4:57,5:5,6:6,24:"r10",25:"r10",26:"r10",27:"r10",28:"r10",29:"r10",30:"r10",31:"r10",32:"r10",33:"r10",34:"r10",35:"r10",36:"r10",37:"r10",38:"r10",39:"r10",40:"r10",41:"r10",42:"r10",43:"r10",44:"r10",45:"r10",52:"r10",53:"r10",54:"r10",55:"r10",57:"r10"},{4:59,5:5,6:6,24:"r10",25:"r10",26:"r10",27:"r10",28:"r10",29:"r10",30:"r10",31:"r10",32:"r10",33:"r10",34:"r10",35:"r10",36:"r10",37:"r10",38:"r10",39:"r10",40:"r10",41:"r10",42:"r10",43:"r10",44:"r10",45:"r10",52:"r10",53:"r10",54:"r10",55:"r10",57:"r10"},{4:61,5:5,6:6,24:"r10",25:"r10",26:"r10",27:"r10",28:"r10",29:"r10",30:"r10",31:"r10",32:"r10",33:"r10",34:"r10",35:"r10",36:"r10",37:"r10",38:"r10",39:"r10",40:"r10",41:"r10",42:"r10",43:"r10",44:"r10",45:"r10",52:"r10",53:"r10",54:"r10",55:"r10",57:"r10"},{4:63,5:5,6:6,24:"r10",25:"r10",26:"r10",27:"r10",28:"r10",29:"r10",30:"r10",31:"r10",32:"r10",33:"r10",34:"r10",35:"r10",36:"r10",37:"r10",38:"r10",39:"r10",40:"r10",41:"r10",42:"r10",43:"r10",44:"r10",45:"r10",52:"r10",53:"r10",54:"r10",55:"r10",57:"r10"},{23:"r22",24:"r22",25:"r22",26:"r22",27:"r22",28:"r22",29:"r22",30:"r22",31:"r22",32:"r22",33:"r22",34:"r22",35:"r22",36:"r22",37:"r22",38:"r22",39:"r22",40:"r22",41:"r22",42:"r22",43:"r22",44:"r22",45:"r22",46:"r22",47:"r22",48:"r22",49:"r22",50:"r22",51:"r22",52:"r22",53:"r22",54:"r22",55:"r22",57:"r22"},{23:"r23",24:"r23",25:"r23",26:"r23",27:"r23",28:"r23",29:"r23",30:"r23",31:"r23",32:"r23",33:"r23",34:"r23",35:"r23",36:"r23",37:"r23",38:"r23",39:"r23",40:"r23",41:"r23",42:"r23",43:"r23",44:"r23",45:"r23",46:"r23",47:"r23",48:"r23",49:"r23",50:"r23",51:"r23",52:"r23",53:"r23",54:"r23",55:"r23",57:"r23"},{23:"r24",24:"r24",25:"r24",26:"r24",27:"r24",28:"r24",29:"r24",30:"r24",31:"r24",32:"r24",33:"r24",34:"r24",35:"r24",36:"r24",37:"r24",38:"r24",39:"r24",40:"r24",41:"r24",42:"r24",43:"r24",44:"r24",45:"r24",46:"r24",47:"r24",48:"r24",49:"r24",50:"r24",51:"r24",52:"r24",53:"r24",54:"r24",55:"r24",57:"r24"},{23:"r25",24:"r25",25:"r25",26:"r25",27:"r25",28:"r25",29:"r25",30:"r25",31:"r25",32:"r25",33:"r25",34:"r25",35:"r25",36:"r25",37:"r25",38:"r25",39:"r25",40:"r25",41:"r25",42:"r25",43:"r25",44:"r25",45:"r25",46:"r25",47:"r25",48:"r25",49:"r25",50:"r25",51:"r25",52:"r25",53:"r25",54:"r25",55:"r25",56:"r25",57:"r25",58:"r25"},{23:"r26",24:"r26",25:"r26",26:"r26",27:"r26",28:"r26",29:"r26",30:"r26",31:"r26",32:"r26",33:"r26",34:"r26",35:"r26",36:"r26",37:"r26",38:"r26",39:"r26",40:"r26",41:"r26",42:"r26",43:"r26",44:"r26",45:"r26",46:"r26",47:"r26",48:"r26",49:"r26",50:"r26",51:"r26",52:"r26",53:"r26",54:"r26",55:"r26",56:"r26",57:"r26",58:"r26"},{23:"r27",24:"r27",25:"r27",26:"r27",27:"r27",28:"r27",29:"r27",30:"r27",31:"r27",32:"r27",33:"r27",34:"r27",35:"r27",36:"r27",37:"r27",38:"r27",39:"r27",40:"r27",41:"r27",42:"r27",43:"r27",44:"r27",45:"r27",46:"r27",47:"r27",48:"r27",49:"r27",50:"r27",51:"r27",52:"r27",53:"r27",54:"r27",55:"r27",56:"r27",57:"r27",58:"r27"},{23:"r28",24:"r28",25:"r28",26:"r28",27:"r28",28:"r28",29:"r28",30:"r28",31:"r28",32:"r28",33:"r28",34:"r28",35:"r28",36:"r28",37:"r28",38:"r28",39:"r28",40:"r28",41:"r28",42:"r28",43:"r28",44:"r28",45:"r28",46:"r28",47:"r28",48:"r28",49:"r28",50:"r28",51:"r28",52:"r28",53:"r28",54:"r28",55:"r28",56:"r28",57:"r28",58:"r28"},{23:"r29",24:"r29",25:"r29",26:"r29",27:"r29",28:"r29",29:"r29",30:"r29",31:"r29",32:"r29",33:"r29",34:"r29",35:"r29",36:"r29",37:"r29",38:"r29",39:"r29",40:"r29",41:"r29",42:"r29",43:"r29",44:"r29",45:"r29",46:"r29",47:"r29",48:"r29",49:"r29",50:"r29",51:"r29",52:"r29",53:"r29",54:"r29",55:"r29",56:"r29",57:"r29",58:"r29"},{23:"r30",24:"r30",25:"r30",26:"r30",27:"r30",28:"r30",29:"r30",30:"r30",31:"r30",32:"r30",33:"r30",34:"r30",35:"r30",36:"r30",37:"r30",38:"r30",39:"r30",40:"r30",41:"r30",42:"r30",43:"r30",44:"r30",45:"r30",46:"r30",47:"r30",48:"r30",49:"r30",50:"r30",51:"r30",52:"r30",53:"r30",54:"r30",55:"r30",56:"r30",57:"r30",58:"r30"},{23:"r31",24:"r31",25:"r31",26:"r31",27:"r31",28:"r31",29:"r31",30:"r31",31:"r31",32:"r31",33:"r31",34:"r31",35:"r31",36:"r31",37:"r31",38:"r31",39:"r31",40:"r31",41:"r31",42:"r31",43:"r31",44:"r31",45:"r31",46:"r31",47:"r31",48:"r31",49:"r31",50:"r31",51:"r31",52:"r31",53:"r31",54:"r31",55:"r31",56:"r31",57:"r31",58:"r31"},{23:"r32",24:"r32",25:"r32",26:"r32",27:"r32",28:"r32",29:"r32",30:"r32",31:"r32",32:"r32",33:"r32",34:"r32",35:"r32",36:"r32",37:"r32",38:"r32",39:"r32",40:"r32",41:"r32",42:"r32",43:"r32",44:"r32",45:"r32",46:"r32",47:"r32",48:"r32",49:"r32",50:"r32",51:"r32",52:"r32",53:"r32",54:"r32",55:"r32",56:"r32",57:"r32",58:"r32"},{23:"r33",24:"r33",25:"r33",26:"r33",27:"r33",28:"r33",29:"r33",30:"r33",31:"r33",32:"r33",33:"r33",34:"r33",35:"r33",36:"r33",37:"r33",38:"r33",39:"r33",40:"r33",41:"r33",42:"r33",43:"r33",44:"r33",45:"r33",46:"r33",47:"r33",48:"r33",49:"r33",50:"r33",51:"r33",52:"r33",53:"r33",54:"r33",55:"r33",56:"r33",57:"r33",58:"r33"},{23:"r34",24:"r34",25:"r34",26:"r34",27:"r34",28:"r34",29:"r34",30:"r34",31:"r34",32:"r34",33:"r34",34:"r34",35:"r34",36:"r34",37:"r34",38:"r34",39:"r34",40:"r34",41:"r34",42:"r34",43:"r34",44:"r34",45:"r34",46:"r34",47:"r34",48:"r34",49:"r34",50:"r34",51:"r34",52:"r34",53:"r34",54:"r34",55:"r34",56:"r34",57:"r34",58:"r34"},{23:"r35",24:"r35",25:"r35",26:"r35",27:"r35",28:"r35",29:"r35",30:"r35",31:"r35",32:"r35",33:"r35",34:"r35",35:"r35",36:"r35",37:"r35",38:"r35",39:"r35",40:"r35",41:"r35",42:"r35",43:"r35",44:"r35",45:"r35",46:"r35",47:"r35",48:"r35",49:"r35",50:"r35",51:"r35",52:"r35",53:"r35",54:"r35",55:"r35",56:"r35",57:"r35",58:"r35"},{23:"r36",24:"r36",25:"r36",26:"r36",27:"r36",28:"r36",29:"r36",30:"r36",31:"r36",32:"r36",33:"r36",34:"r36",35:"r36",36:"r36",37:"r36",38:"r36",39:"r36",40:"r36",41:"r36",42:"r36",43:"r36",44:"r36",45:"r36",46:"r36",47:"r36",48:"r36",49:"r36",50:"r36",51:"r36",52:"r36",53:"r36",54:"r36",55:"r36",56:"r36",57:"r36",58:"r36"},{10:70,18:65,19:66,21:67,22:69,24:"s28",28:"s71",35:"s29",36:"s30",37:"s31",38:"s32",39:"s33",40:"s34",41:"s35",42:"s36",43:"s37",44:"s38",45:"s39",56:"r54",58:"s68"},{10:70,18:83,19:66,21:67,22:69,24:"s28",28:"s71",35:"s29",36:"s30",37:"s31",38:"s32",39:"s33",40:"s34",41:"s35",42:"s36",43:"s37",44:"s38",45:"s39",56:"r54",58:"s68"},{23:"r47",24:"r47",25:"r47",26:"r47",27:"r47",28:"r47",29:"r47",30:"r47",31:"r47",32:"r47",33:"r47",34:"r47",35:"r47",36:"r47",37:"r47",38:"r47",39:"r47",40:"r47",41:"r47",42:"r47",43:"r47",44:"r47",45:"r47",46:"r47",47:"r47",48:"r47",49:"r47",50:"r47",51:"r47",52:"r47",53:"r47",54:"r47",55:"r47",57:"r47"},{23:"r48",24:"r48",25:"r48",26:"r48",27:"r48",28:"r48",29:"r48",30:"r48",31:"r48",32:"r48",33:"r48",34:"r48",35:"r48",36:"r48",37:"r48",38:"r48",39:"r48",40:"r48",41:"r48",42:"r48",43:"r48",44:"r48",45:"r48",46:"r48",47:"r48",48:"r48",49:"r48",50:"r48",51:"r48",52:"r48",53:"r48",54:"r48",55:"r48",57:"r48"},{4:85,5:5,6:6,24:"r10",25:"r10",26:"r10",27:"r10",28:"r10",29:"r10",30:"r10",31:"r10",32:"r10",33:"r10",34:"r10",35:"r10",36:"r10",37:"r10",38:"r10",39:"r10",40:"r10",41:"r10",42:"r10",43:"r10",44:"r10",45:"r10",52:"r10",53:"r10",54:"r10",55:"r10",57:"r10"},{4:87,5:5,6:6,24:"r10",25:"r10",26:"r10",27:"r10",28:"r10",29:"r10",30:"r10",31:"r10",32:"r10",33:"r10",34:"r10",35:"r10",36:"r10",37:"r10",38:"r10",39:"r10",40:"r10",41:"r10",42:"r10",43:"r10",44:"r10",45:"r10",52:"r10",53:"r10",54:"r10",55:"r10",57:"r10"},{4:89,5:5,6:6,24:"r10",25:"r10",26:"r10",27:"r10",28:"r10",29:"r10",30:"r10",31:"r10",32:"r10",33:"r10",34:"r10",35:"r10",36:"r10",37:"r10",38:"r10",39:"r10",40:"r10",41:"r10",42:"r10",43:"r10",44:"r10",45:"r10",52:"r10",53:"r10",54:"r10",55:"r10",57:"r10"},{23:"r13",24:"r13",25:"r13",26:"r13",27:"r13",28:"r13",29:"r13",30:"r13",31:"r13",32:"r13",33:"r13",34:"r13",35:"r13",36:"r13",37:"r13",38:"r13",39:"r13",40:"r13",41:"r13",42:"r13",43:"r13",44:"r13",45:"r13",52:"r13",53:"r13",54:"r13",55:"r13",57:"r13"},{23:"r37",24:"r37",25:"r37",26:"r37",27:"r37",28:"r37",29:"r37",30:"r37",31:"r37",32:"r37",33:"r37",34:"r37",35:"r37",36:"r37",37:"r37",38:"r37",39:"r37",40:"r37",41:"r37",42:"r37",43:"r37",44:"r37",45:"r37",52:"r37",53:"r37",54:"r37",55:"r37",57:"r37"},{23:"r39",24:"r39",25:"r39",26:"r39",27:"r39",28:"r39",29:"r39",30:"r39",31:"r39",32:"r39",33:"r39",34:"r39",35:"r39",36:"r39",37:"r39",38:"r39",39:"r39",40:"r39",41:"r39",42:"r39",43:"r39",44:"r39",45:"r39",46:"s56",52:"r39",53:"r39",54:"r39",55:"r39",57:"r39"},{23:"r41",24:"r41",25:"r41",26:"r41",27:"r41",28:"r41",29:"r41",30:"r41",31:"r41",32:"r41",33:"r41",34:"r41",35:"r41",36:"r41",37:"r41",38:"r41",39:"r41",40:"r41",41:"r41",42:"r41",43:"r41",44:"r41",45:"r41",46:"r41",52:"r41",53:"r41",54:"r41",55:"r41",57:"r41"},{23:"r42",24:"r42",25:"r42",26:"r42",27:"r42",28:"r42",29:"r42",30:"r42",31:"r42",32:"r42",33:"r42",34:"r42",35:"r42",36:"r42",37:"r42",38:"r42",39:"r42",40:"r42",41:"r42",42:"r42",43:"r42",44:"r42",45:"r42",46:"r42",52:"r42",53:"r42",54:"r42",55:"r42",57:"r42"},{23:"r43",24:"r43",25:"r43",26:"r43",27:"r43",28:"r43",29:"r43",30:"r43",31:"r43",32:"r43",33:"r43",34:"r43",35:"r43",36:"r43",37:"r43",38:"r43",39:"r43",40:"r43",41:"r43",42:"r43",43:"r43",44:"r43",45:"r43",46:"r43",52:"r43",53:"r43",54:"r43",55:"r43",57:"r43"},{23:"r44",24:"r44",25:"r44",26:"r44",27:"r44",28:"r44",29:"r44",30:"r44",31:"r44",32:"r44",33:"r44",34:"r44",35:"r44",36:"r44",37:"r44",38:"r44",39:"r44",40:"r44",41:"r44",42:"r44",43:"r44",44:"r44",45:"r44",46:"r44",52:"r44",53:"r44",54:"r44",55:"r44",57:"r44"},{23:"r45",24:"r45",25:"r45",26:"r45",27:"r45",28:"r45",29:"r45",30:"r45",31:"r45",32:"r45",33:"r45",34:"r45",35:"r45",36:"r45",37:"r45",38:"r45",39:"r45",40:"r45",41:"r45",42:"r45",43:"r45",44:"r45",45:"r45",46:"r45",52:"r45",53:"r45",54:"r45",55:"r45",57:"r45"},{23:"r46",24:"r46",25:"r46",26:"r46",27:"r46",28:"r46",29:"r46",30:"r46",31:"r46",32:"r46",33:"r46",34:"r46",35:"r46",36:"r46",37:"r46",38:"r46",39:"r46",40:"r46",41:"r46",42:"r46",43:"r46",44:"r46",45:"r46",46:"r46",52:"r46",53:"r46",54:"r46",55:"r46",57:"r46"},{23:"r40",24:"r40",25:"r40",26:"r40",27:"r40",28:"r40",29:"r40",30:"r40",31:"r40",32:"r40",33:"r40",34:"r40",35:"r40",36:"r40",37:"r40",38:"r40",39:"r40",40:"r40",41:"r40",42:"r40",43:"r40",44:"r40",45:"r40",52:"r40",53:"r40",54:"r40",55:"r40",57:"r40"},{25:"s12",31:"s58"},{23:"r18",24:"r18",25:"r18",26:"r18",27:"r18",28:"r18",29:"r18",30:"r18",31:"r18",32:"r18",33:"r18",34:"r18",35:"r18",36:"r18",37:"r18",38:"r18",39:"r18",40:"r18",41:"r18",42:"r18",43:"r18",44:"r18",45:"r18",52:"r18",53:"r18",54:"r18",55:"r18",57:"r18"},{25:"s12",31:"s60"},{23:"r19",24:"r19",25:"r19",26:"r19",27:"r19",28:"r19",29:"r19",30:"r19",31:"r19",32:"r19",33:"r19",34:"r19",35:"r19",36:"r19",37:"r19",38:"r19",39:"r19",40:"r19",41:"r19",42:"r19",43:"r19",44:"r19",45:"r19",52:"r19",53:"r19",54:"r19",55:"r19",57:"r19"},{25:"s12",31:"s62"},{23:"r20",24:"r20",25:"r20",26:"r20",27:"r20",28:"r20",29:"r20",30:"r20",31:"r20",32:"r20",33:"r20",34:"r20",35:"r20",36:"r20",37:"r20",38:"r20",39:"r20",40:"r20",41:"r20",42:"r20",43:"r20",44:"r20",45:"r20",52:"r20",53:"r20",54:"r20",55:"r20",57:"r20"},{25:"s12",31:"s64"},{23:"r21",24:"r21",25:"r21",26:"r21",27:"r21",28:"r21",29:"r21",30:"r21",31:"r21",32:"r21",33:"r21",34:"r21",35:"r21",36:"r21",37:"r21",38:"r21",39:"r21",40:"r21",41:"r21",42:"r21",43:"r21",44:"r21",45:"r21",52:"r21",53:"r21",54:"r21",55:"r21",57:"r21"},{56:"s72"},{56:"r55"},{10:70,20:73,21:75,22:76,24:"s28",28:"s71",35:"s29",36:"s30",37:"s31",38:"s32",39:"s33",40:"s34",41:"s35",42:"s36",43:"s37",44:"s38",45:"s39",56:"r56",58:"s74"},{24:"r62",28:"r62",35:"r62",36:"r62",37:"r62",38:"r62",39:"r62",40:"r62",41:"r62",42:"r62",43:"r62",44:"r62",45:"r62",56:"r62",58:"r62"},{24:"r63",28:"r63",35:"r63",36:"r63",37:"r63",38:"r63",39:"r63",40:"r63",41:"r63",42:"r63",43:"r63",44:"r63",45:"r63",56:"r63",58:"r63"},{24:"r64",28:"r64",35:"r64",36:"r64",37:"r64",38:"r64",39:"r64",40:"r64",41:"r64",42:"r64",43:"r64",44:"r64",45:"r64",56:"r64",58:"r64"},{24:"r65",28:"r65",35:"r65",36:"r65",37:"r65",38:"r65",39:"r65",40:"r65",41:"r65",42:"r65",43:"r65",44:"r65",45:"r65",56:"r65",58:"r65"},{23:"r52",24:"r52",25:"r52",26:"r52",27:"r52",28:"r52",29:"r52",30:"r52",31:"r52",32:"r52",33:"r52",34:"r52",35:"r52",36:"r52",37:"r52",38:"r52",39:"r52",40:"r52",41:"r52",42:"r52",43:"r52",44:"r52",45:"r52",46:"r52",47:"r52",48:"r52",49:"r52",50:"r52",51:"r52",52:"r52",53:"r52",54:"r52",55:"r52",57:"r52"},{56:"r57"},{10:70,21:77,22:69,24:"s28",28:"s71",35:"s29",36:"s30",37:"s31",38:"s32",39:"s33",40:"s34",41:"s35",42:"s36",43:"s37",44:"s38",45:"s39",56:"r62",58:"s68"},{56:"r59"},{10:70,20:79,21:75,22:76,24:"s28",28:"s71",35:"s29",36:"s30",37:"s31",38:"s32",39:"s33",40:"s34",41:"s35",42:"s36",43:"s37",44:"s38",45:"s39",56:"r63",58:"s80"},{10:70,18:78,19:66,21:67,22:69,24:"s28",28:"s71",35:"s29",36:"s30",37:"s31",38:"s32",39:"s33",40:"s34",41:"s35",42:"s36",43:"s37",44:"s38",45:"s39",56:"r54",58:"s68"},{56:"r58"},{56:"r60"},{10:70,21:81,22:69,24:"s28",28:"s71",35:"s29",36:"s30",37:"s31",38:"s32",39:"s33",40:"s34",41:"s35",42:"s36",43:"s37",44:"s38",45:"s39",56:"r62",58:"s68"},{10:70,18:82,19:66,21:67,22:69,24:"s28",28:"s71",35:"s29",36:"s30",37:"s31",38:"s32",39:"s33",40:"s34",41:"s35",42:"s36",43:"s37",44:"s38",45:"s39",56:"r54",58:"s68"},{56:"r61"},{56:"s84"},{23:"r53",24:"r53",25:"r53",26:"r53",27:"r53",28:"r53",29:"r53",30:"r53",31:"r53",32:"r53",33:"r53",34:"r53",35:"r53",36:"r53",37:"r53",38:"r53",39:"r53",40:"r53",41:"r53",42:"r53",43:"r53",44:"r53",45:"r53",46:"r53",47:"r53",48:"r53",49:"r53",50:"r53",51:"r53",52:"r53",53:"r53",54:"r53",55:"r53",57:"r53"},{25:"s12",31:"s86"},{23:"r49",24:"r49",25:"r49",26:"r49",27:"r49",28:"r49",29:"r49",30:"r49",31:"r49",32:"r49",33:"r49",34:"r49",35:"r49",36:"r49",37:"r49",38:"r49",39:"r49",40:"r49",41:"r49",42:"r49",43:"r49",44:"r49",45:"r49",46:"r49",47:"r49",48:"r49",49:"r49",50:"r49",51:"r49",52:"r49",53:"r49",54:"r49",55:"r49",57:"r49"},{25:"s12",31:"s88"},{23:"r50",24:"r50",25:"r50",26:"r50",27:"r50",28:"r50",29:"r50",30:"r50",31:"r50",32:"r50",33:"r50",34:"r50",35:"r50",36:"r50",37:"r50",38:"r50",39:"r50",40:"r50",41:"r50",42:"r50",43:"r50",44:"r50",45:"r50",46:"r50",47:"r50",48:"r50",49:"r50",50:"r50",51:"r50",52:"r50",53:"r50",54:"r50",55:"r50",57:"r50"},{25:"s12",31:"s90"},{23:"r51",24:"r51",25:"r51",26:"r51",27:"r51",28:"r51",29:"r51",30:"r51",31:"r51",32:"r51",33:"r51",34:"r51",35:"r51",36:"r51",37:"r51",38:"r51",39:"r51",40:"r51",41:"r51",42:"r51",43:"r51",44:"r51",45:"r51",46:"r51",47:"r51",48:"r51",49:"r51",50:"r51",51:"r51",52:"r51",53:"r51",54:"r51",55:"r51",57:"r51"}],l=[],b=void 0,p=[[/^#[^\n]+/,function(){}],[/^\s+/,function(){}],[/^-/,function(){return"DASH"}],[/^\//,function(){return"CHAR"}],[/^#/,function(){return"CHAR"}],[/^\|/,function(){return"CHAR"}],[/^\./,function(){return"CHAR"}],[/^\{/,function(){return"CHAR"}],[/^\{\d+\}/,function(){return"RANGE_EXACT"}],[/^\{\d+,\}/,function(){return"RANGE_OPEN"}],[/^\{\d+,\d+\}/,function(){return"RANGE_CLOSED"}],[/^\\k<(([\u0041-\u005a\u0061-\u007a\u00aa\u00b5\u00ba\u00c0-\u00d6\u00d8-\u00f6\u00f8-\u02c1\u02c6-\u02d1\u02e0-\u02e4\u02ec\u02ee\u0370-\u0374\u0376-\u0377\u037a-\u037d\u037f\u0386\u0388-\u038a\u038c\u038e-\u03a1\u03a3-\u03f5\u03f7-\u0481\u048a-\u052f\u0531-\u0556\u0559\u0560-\u0588\u05d0-\u05ea\u05ef-\u05f2\u0620-\u064a\u066e-\u066f\u0671-\u06d3\u06d5\u06e5-\u06e6\u06ee-\u06ef\u06fa-\u06fc\u06ff\u0710\u0712-\u072f\u074d-\u07a5\u07b1\u07ca-\u07ea\u07f4-\u07f5\u07fa\u0800-\u0815\u081a\u0824\u0828\u0840-\u0858\u0860-\u086a\u08a0-\u08b4\u08b6-\u08bd\u0904-\u0939\u093d\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098c\u098f-\u0990\u0993-\u09a8\u09aa-\u09b0\u09b2\u09b6-\u09b9\u09bd\u09ce\u09dc-\u09dd\u09df-\u09e1\u09f0-\u09f1\u09fc\u0a05-\u0a0a\u0a0f-\u0a10\u0a13-\u0a28\u0a2a-\u0a30\u0a32-\u0a33\u0a35-\u0a36\u0a38-\u0a39\u0a59-\u0a5c\u0a5e\u0a72-\u0a74\u0a85-\u0a8d\u0a8f-\u0a91\u0a93-\u0aa8\u0aaa-\u0ab0\u0ab2-\u0ab3\u0ab5-\u0ab9\u0abd\u0ad0\u0ae0-\u0ae1\u0af9\u0b05-\u0b0c\u0b0f-\u0b10\u0b13-\u0b28\u0b2a-\u0b30\u0b32-\u0b33\u0b35-\u0b39\u0b3d\u0b5c-\u0b5d\u0b5f-\u0b61\u0b71\u0b83\u0b85-\u0b8a\u0b8e-\u0b90\u0b92-\u0b95\u0b99-\u0b9a\u0b9c\u0b9e-\u0b9f\u0ba3-\u0ba4\u0ba8-\u0baa\u0bae-\u0bb9\u0bd0\u0c05-\u0c0c\u0c0e-\u0c10\u0c12-\u0c28\u0c2a-\u0c39\u0c3d\u0c58-\u0c5a\u0c60-\u0c61\u0c80\u0c85-\u0c8c\u0c8e-\u0c90\u0c92-\u0ca8\u0caa-\u0cb3\u0cb5-\u0cb9\u0cbd\u0cde\u0ce0-\u0ce1\u0cf1-\u0cf2\u0d05-\u0d0c\u0d0e-\u0d10\u0d12-\u0d3a\u0d3d\u0d4e\u0d54-\u0d56\u0d5f-\u0d61\u0d7a-\u0d7f\u0d85-\u0d96\u0d9a-\u0db1\u0db3-\u0dbb\u0dbd\u0dc0-\u0dc6\u0e01-\u0e30\u0e32-\u0e33\u0e40-\u0e46\u0e81-\u0e82\u0e84\u0e86-\u0e8a\u0e8c-\u0ea3\u0ea5\u0ea7-\u0eb0\u0eb2-\u0eb3\u0ebd\u0ec0-\u0ec4\u0ec6\u0edc-\u0edf\u0f00\u0f40-\u0f47\u0f49-\u0f6c\u0f88-\u0f8c\u1000-\u102a\u103f\u1050-\u1055\u105a-\u105d\u1061\u1065-\u1066\u106e-\u1070\u1075-\u1081\u108e\u10a0-\u10c5\u10c7\u10cd\u10d0-\u10fa\u10fc-\u1248\u124a-\u124d\u1250-\u1256\u1258\u125a-\u125d\u1260-\u1288\u128a-\u128d\u1290-\u12b0\u12b2-\u12b5\u12b8-\u12be\u12c0\u12c2-\u12c5\u12c8-\u12d6\u12d8-\u1310\u1312-\u1315\u1318-\u135a\u1380-\u138f\u13a0-\u13f5\u13f8-\u13fd\u1401-\u166c\u166f-\u167f\u1681-\u169a\u16a0-\u16ea\u16ee-\u16f8\u1700-\u170c\u170e-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176c\u176e-\u1770\u1780-\u17b3\u17d7\u17dc\u1820-\u1878\u1880-\u18a8\u18aa\u18b0-\u18f5\u1900-\u191e\u1950-\u196d\u1970-\u1974\u1980-\u19ab\u19b0-\u19c9\u1a00-\u1a16\u1a20-\u1a54\u1aa7\u1b05-\u1b33\u1b45-\u1b4b\u1b83-\u1ba0\u1bae-\u1baf\u1bba-\u1be5\u1c00-\u1c23\u1c4d-\u1c4f\u1c5a-\u1c7d\u1c80-\u1c88\u1c90-\u1cba\u1cbd-\u1cbf\u1ce9-\u1cec\u1cee-\u1cf3\u1cf5-\u1cf6\u1cfa\u1d00-\u1dbf\u1e00-\u1f15\u1f18-\u1f1d\u1f20-\u1f45\u1f48-\u1f4d\u1f50-\u1f57\u1f59\u1f5b\u1f5d\u1f5f-\u1f7d\u1f80-\u1fb4\u1fb6-\u1fbc\u1fbe\u1fc2-\u1fc4\u1fc6-\u1fcc\u1fd0-\u1fd3\u1fd6-\u1fdb\u1fe0-\u1fec\u1ff2-\u1ff4\u1ff6-\u1ffc\u2071\u207f\u2090-\u209c\u2102\u2107\u210a-\u2113\u2115\u2118-\u211d\u2124\u2126\u2128\u212a-\u2139\u213c-\u213f\u2145-\u2149\u214e\u2160-\u2188\u2c00-\u2c2e\u2c30-\u2c5e\u2c60-\u2ce4\u2ceb-\u2cee\u2cf2-\u2cf3\u2d00-\u2d25\u2d27\u2d2d\u2d30-\u2d67\u2d6f\u2d80-\u2d96\u2da0-\u2da6\u2da8-\u2dae\u2db0-\u2db6\u2db8-\u2dbe\u2dc0-\u2dc6\u2dc8-\u2dce\u2dd0-\u2dd6\u2dd8-\u2dde\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303c\u3041-\u3096\u309b-\u309f\u30a1-\u30fa\u30fc-\u30ff\u3105-\u312f\u3131-\u318e\u31a0-\u31ba\u31f0-\u31ff\u3400-\u4db5\u4e00-\u9fef\ua000-\ua48c\ua4d0-\ua4fd\ua500-\ua60c\ua610-\ua61f\ua62a-\ua62b\ua640-\ua66e\ua67f-\ua69d\ua6a0-\ua6ef\ua717-\ua71f\ua722-\ua788\ua78b-\ua7bf\ua7c2-\ua7c6\ua7f7-\ua801\ua803-\ua805\ua807-\ua80a\ua80c-\ua822\ua840-\ua873\ua882-\ua8b3\ua8f2-\ua8f7\ua8fb\ua8fd-\ua8fe\ua90a-\ua925\ua930-\ua946\ua960-\ua97c\ua984-\ua9b2\ua9cf\ua9e0-\ua9e4\ua9e6-\ua9ef\ua9fa-\ua9fe\uaa00-\uaa28\uaa40-\uaa42\uaa44-\uaa4b\uaa60-\uaa76\uaa7a\uaa7e-\uaaaf\uaab1\uaab5-\uaab6\uaab9-\uaabd\uaac0\uaac2\uaadb-\uaadd\uaae0-\uaaea\uaaf2-\uaaf4\uab01-\uab06\uab09-\uab0e\uab11-\uab16\uab20-\uab26\uab28-\uab2e\uab30-\uab5a\uab5c-\uab67\uab70-\uabe2\uac00-\ud7a3\ud7b0-\ud7c6\ud7cb-\ud7fb\uf900-\ufa6d\ufa70-\ufad9\ufb00-\ufb06\ufb13-\ufb17\ufb1d\ufb1f-\ufb28\ufb2a-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufbb1\ufbd3-\ufd3d\ufd50-\ufd8f\ufd92-\ufdc7\ufdf0-\ufdfb\ufe70-\ufe74\ufe76-\ufefc\uff21-\uff3a\uff41-\uff5a\uff66-\uffbe\uffc2-\uffc7\uffca-\uffcf\uffd2-\uffd7\uffda-\uffdc]|\ud800[\udc00-\udc0b\udc0d-\udc26\udc28-\udc3a\udc3c-\udc3d\udc3f-\udc4d\udc50-\udc5d\udc80-\udcfa\udd40-\udd74\ude80-\ude9c\udea0-\uded0\udf00-\udf1f\udf2d-\udf4a\udf50-\udf75\udf80-\udf9d\udfa0-\udfc3\udfc8-\udfcf\udfd1-\udfd5]|\ud801[\udc00-\udc9d\udcb0-\udcd3\udcd8-\udcfb\udd00-\udd27\udd30-\udd63\ude00-\udf36\udf40-\udf55\udf60-\udf67]|\ud802[\udc00-\udc05\udc08\udc0a-\udc35\udc37-\udc38\udc3c\udc3f-\udc55\udc60-\udc76\udc80-\udc9e\udce0-\udcf2\udcf4-\udcf5\udd00-\udd15\udd20-\udd39\udd80-\uddb7\uddbe-\uddbf\ude00\ude10-\ude13\ude15-\ude17\ude19-\ude35\ude60-\ude7c\ude80-\ude9c\udec0-\udec7\udec9-\udee4\udf00-\udf35\udf40-\udf55\udf60-\udf72\udf80-\udf91]|\ud803[\udc00-\udc48\udc80-\udcb2\udcc0-\udcf2\udd00-\udd23\udf00-\udf1c\udf27\udf30-\udf45\udfe0-\udff6]|\ud804[\udc03-\udc37\udc83-\udcaf\udcd0-\udce8\udd03-\udd26\udd44\udd50-\udd72\udd76\udd83-\uddb2\uddc1-\uddc4\uddda\udddc\ude00-\ude11\ude13-\ude2b\ude80-\ude86\ude88\ude8a-\ude8d\ude8f-\ude9d\ude9f-\udea8\udeb0-\udede\udf05-\udf0c\udf0f-\udf10\udf13-\udf28\udf2a-\udf30\udf32-\udf33\udf35-\udf39\udf3d\udf50\udf5d-\udf61]|\ud805[\udc00-\udc34\udc47-\udc4a\udc5f\udc80-\udcaf\udcc4-\udcc5\udcc7\udd80-\uddae\uddd8-\udddb\ude00-\ude2f\ude44\ude80-\udeaa\udeb8\udf00-\udf1a]|\ud806[\udc00-\udc2b\udca0-\udcdf\udcff\udda0-\udda7\uddaa-\uddd0\udde1\udde3\ude00\ude0b-\ude32\ude3a\ude50\ude5c-\ude89\ude9d\udec0-\udef8]|\ud807[\udc00-\udc08\udc0a-\udc2e\udc40\udc72-\udc8f\udd00-\udd06\udd08-\udd09\udd0b-\udd30\udd46\udd60-\udd65\udd67-\udd68\udd6a-\udd89\udd98\udee0-\udef2]|\ud808[\udc00-\udf99]|\ud809[\udc00-\udc6e\udc80-\udd43]|\ud80c[\udc00-\udfff]|\ud80d[\udc00-\udc2e]|\ud811[\udc00-\ude46]|\ud81a[\udc00-\ude38\ude40-\ude5e\uded0-\udeed\udf00-\udf2f\udf40-\udf43\udf63-\udf77\udf7d-\udf8f]|\ud81b[\ude40-\ude7f\udf00-\udf4a\udf50\udf93-\udf9f\udfe0-\udfe1\udfe3]|\ud81c[\udc00-\udfff]|\ud81d[\udc00-\udfff]|\ud81e[\udc00-\udfff]|\ud81f[\udc00-\udfff]|\ud820[\udc00-\udfff]|\ud821[\udc00-\udff7]|\ud822[\udc00-\udef2]|\ud82c[\udc00-\udd1e\udd50-\udd52\udd64-\udd67\udd70-\udefb]|\ud82f[\udc00-\udc6a\udc70-\udc7c\udc80-\udc88\udc90-\udc99]|\ud835[\udc00-\udc54\udc56-\udc9c\udc9e-\udc9f\udca2\udca5-\udca6\udca9-\udcac\udcae-\udcb9\udcbb\udcbd-\udcc3\udcc5-\udd05\udd07-\udd0a\udd0d-\udd14\udd16-\udd1c\udd1e-\udd39\udd3b-\udd3e\udd40-\udd44\udd46\udd4a-\udd50\udd52-\udea5\udea8-\udec0\udec2-\udeda\udedc-\udefa\udefc-\udf14\udf16-\udf34\udf36-\udf4e\udf50-\udf6e\udf70-\udf88\udf8a-\udfa8\udfaa-\udfc2\udfc4-\udfcb]|\ud838[\udd00-\udd2c\udd37-\udd3d\udd4e\udec0-\udeeb]|\ud83a[\udc00-\udcc4\udd00-\udd43\udd4b]|\ud83b[\ude00-\ude03\ude05-\ude1f\ude21-\ude22\ude24\ude27\ude29-\ude32\ude34-\ude37\ude39\ude3b\ude42\ude47\ude49\ude4b\ude4d-\ude4f\ude51-\ude52\ude54\ude57\ude59\ude5b\ude5d\ude5f\ude61-\ude62\ude64\ude67-\ude6a\ude6c-\ude72\ude74-\ude77\ude79-\ude7c\ude7e\ude80-\ude89\ude8b-\ude9b\udea1-\udea3\udea5-\udea9\udeab-\udebb]|\ud840[\udc00-\udfff]|\ud841[\udc00-\udfff]|\ud842[\udc00-\udfff]|\ud843[\udc00-\udfff]|\ud844[\udc00-\udfff]|\ud845[\udc00-\udfff]|\ud846[\udc00-\udfff]|\ud847[\udc00-\udfff]|\ud848[\udc00-\udfff]|\ud849[\udc00-\udfff]|\ud84a[\udc00-\udfff]|\ud84b[\udc00-\udfff]|\ud84c[\udc00-\udfff]|\ud84d[\udc00-\udfff]|\ud84e[\udc00-\udfff]|\ud84f[\udc00-\udfff]|\ud850[\udc00-\udfff]|\ud851[\udc00-\udfff]|\ud852[\udc00-\udfff]|\ud853[\udc00-\udfff]|\ud854[\udc00-\udfff]|\ud855[\udc00-\udfff]|\ud856[\udc00-\udfff]|\ud857[\udc00-\udfff]|\ud858[\udc00-\udfff]|\ud859[\udc00-\udfff]|\ud85a[\udc00-\udfff]|\ud85b[\udc00-\udfff]|\ud85c[\udc00-\udfff]|\ud85d[\udc00-\udfff]|\ud85e[\udc00-\udfff]|\ud85f[\udc00-\udfff]|\ud860[\udc00-\udfff]|\ud861[\udc00-\udfff]|\ud862[\udc00-\udfff]|\ud863[\udc00-\udfff]|\ud864[\udc00-\udfff]|\ud865[\udc00-\udfff]|\ud866[\udc00-\udfff]|\ud867[\udc00-\udfff]|\ud868[\udc00-\udfff]|\ud869[\udc00-\uded6\udf00-\udfff]|\ud86a[\udc00-\udfff]|\ud86b[\udc00-\udfff]|\ud86c[\udc00-\udfff]|\ud86d[\udc00-\udf34\udf40-\udfff]|\ud86e[\udc00-\udc1d\udc20-\udfff]|\ud86f[\udc00-\udfff]|\ud870[\udc00-\udfff]|\ud871[\udc00-\udfff]|\ud872[\udc00-\udfff]|\ud873[\udc00-\udea1\udeb0-\udfff]|\ud874[\udc00-\udfff]|\ud875[\udc00-\udfff]|\ud876[\udc00-\udfff]|\ud877[\udc00-\udfff]|\ud878[\udc00-\udfff]|\ud879[\udc00-\udfff]|\ud87a[\udc00-\udfe0]|\ud87e[\udc00-\ude1d])|[$_]|(\\u[0-9a-fA-F]{4}|\\u\{[0-9a-fA-F]{1,}\}))(([\u0030-\u0039\u0041-\u005a\u005f\u0061-\u007a\u00aa\u00b5\u00b7\u00ba\u00c0-\u00d6\u00d8-\u00f6\u00f8-\u02c1\u02c6-\u02d1\u02e0-\u02e4\u02ec\u02ee\u0300-\u0374\u0376-\u0377\u037a-\u037d\u037f\u0386-\u038a\u038c\u038e-\u03a1\u03a3-\u03f5\u03f7-\u0481\u0483-\u0487\u048a-\u052f\u0531-\u0556\u0559\u0560-\u0588\u0591-\u05bd\u05bf\u05c1-\u05c2\u05c4-\u05c5\u05c7\u05d0-\u05ea\u05ef-\u05f2\u0610-\u061a\u0620-\u0669\u066e-\u06d3\u06d5-\u06dc\u06df-\u06e8\u06ea-\u06fc\u06ff\u0710-\u074a\u074d-\u07b1\u07c0-\u07f5\u07fa\u07fd\u0800-\u082d\u0840-\u085b\u0860-\u086a\u08a0-\u08b4\u08b6-\u08bd\u08d3-\u08e1\u08e3-\u0963\u0966-\u096f\u0971-\u0983\u0985-\u098c\u098f-\u0990\u0993-\u09a8\u09aa-\u09b0\u09b2\u09b6-\u09b9\u09bc-\u09c4\u09c7-\u09c8\u09cb-\u09ce\u09d7\u09dc-\u09dd\u09df-\u09e3\u09e6-\u09f1\u09fc\u09fe\u0a01-\u0a03\u0a05-\u0a0a\u0a0f-\u0a10\u0a13-\u0a28\u0a2a-\u0a30\u0a32-\u0a33\u0a35-\u0a36\u0a38-\u0a39\u0a3c\u0a3e-\u0a42\u0a47-\u0a48\u0a4b-\u0a4d\u0a51\u0a59-\u0a5c\u0a5e\u0a66-\u0a75\u0a81-\u0a83\u0a85-\u0a8d\u0a8f-\u0a91\u0a93-\u0aa8\u0aaa-\u0ab0\u0ab2-\u0ab3\u0ab5-\u0ab9\u0abc-\u0ac5\u0ac7-\u0ac9\u0acb-\u0acd\u0ad0\u0ae0-\u0ae3\u0ae6-\u0aef\u0af9-\u0aff\u0b01-\u0b03\u0b05-\u0b0c\u0b0f-\u0b10\u0b13-\u0b28\u0b2a-\u0b30\u0b32-\u0b33\u0b35-\u0b39\u0b3c-\u0b44\u0b47-\u0b48\u0b4b-\u0b4d\u0b56-\u0b57\u0b5c-\u0b5d\u0b5f-\u0b63\u0b66-\u0b6f\u0b71\u0b82-\u0b83\u0b85-\u0b8a\u0b8e-\u0b90\u0b92-\u0b95\u0b99-\u0b9a\u0b9c\u0b9e-\u0b9f\u0ba3-\u0ba4\u0ba8-\u0baa\u0bae-\u0bb9\u0bbe-\u0bc2\u0bc6-\u0bc8\u0bca-\u0bcd\u0bd0\u0bd7\u0be6-\u0bef\u0c00-\u0c0c\u0c0e-\u0c10\u0c12-\u0c28\u0c2a-\u0c39\u0c3d-\u0c44\u0c46-\u0c48\u0c4a-\u0c4d\u0c55-\u0c56\u0c58-\u0c5a\u0c60-\u0c63\u0c66-\u0c6f\u0c80-\u0c83\u0c85-\u0c8c\u0c8e-\u0c90\u0c92-\u0ca8\u0caa-\u0cb3\u0cb5-\u0cb9\u0cbc-\u0cc4\u0cc6-\u0cc8\u0cca-\u0ccd\u0cd5-\u0cd6\u0cde\u0ce0-\u0ce3\u0ce6-\u0cef\u0cf1-\u0cf2\u0d00-\u0d03\u0d05-\u0d0c\u0d0e-\u0d10\u0d12-\u0d44\u0d46-\u0d48\u0d4a-\u0d4e\u0d54-\u0d57\u0d5f-\u0d63\u0d66-\u0d6f\u0d7a-\u0d7f\u0d82-\u0d83\u0d85-\u0d96\u0d9a-\u0db1\u0db3-\u0dbb\u0dbd\u0dc0-\u0dc6\u0dca\u0dcf-\u0dd4\u0dd6\u0dd8-\u0ddf\u0de6-\u0def\u0df2-\u0df3\u0e01-\u0e3a\u0e40-\u0e4e\u0e50-\u0e59\u0e81-\u0e82\u0e84\u0e86-\u0e8a\u0e8c-\u0ea3\u0ea5\u0ea7-\u0ebd\u0ec0-\u0ec4\u0ec6\u0ec8-\u0ecd\u0ed0-\u0ed9\u0edc-\u0edf\u0f00\u0f18-\u0f19\u0f20-\u0f29\u0f35\u0f37\u0f39\u0f3e-\u0f47\u0f49-\u0f6c\u0f71-\u0f84\u0f86-\u0f97\u0f99-\u0fbc\u0fc6\u1000-\u1049\u1050-\u109d\u10a0-\u10c5\u10c7\u10cd\u10d0-\u10fa\u10fc-\u1248\u124a-\u124d\u1250-\u1256\u1258\u125a-\u125d\u1260-\u1288\u128a-\u128d\u1290-\u12b0\u12b2-\u12b5\u12b8-\u12be\u12c0\u12c2-\u12c5\u12c8-\u12d6\u12d8-\u1310\u1312-\u1315\u1318-\u135a\u135d-\u135f\u1369-\u1371\u1380-\u138f\u13a0-\u13f5\u13f8-\u13fd\u1401-\u166c\u166f-\u167f\u1681-\u169a\u16a0-\u16ea\u16ee-\u16f8\u1700-\u170c\u170e-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176c\u176e-\u1770\u1772-\u1773\u1780-\u17d3\u17d7\u17dc-\u17dd\u17e0-\u17e9\u180b-\u180d\u1810-\u1819\u1820-\u1878\u1880-\u18aa\u18b0-\u18f5\u1900-\u191e\u1920-\u192b\u1930-\u193b\u1946-\u196d\u1970-\u1974\u1980-\u19ab\u19b0-\u19c9\u19d0-\u19da\u1a00-\u1a1b\u1a20-\u1a5e\u1a60-\u1a7c\u1a7f-\u1a89\u1a90-\u1a99\u1aa7\u1ab0-\u1abd\u1b00-\u1b4b\u1b50-\u1b59\u1b6b-\u1b73\u1b80-\u1bf3\u1c00-\u1c37\u1c40-\u1c49\u1c4d-\u1c7d\u1c80-\u1c88\u1c90-\u1cba\u1cbd-\u1cbf\u1cd0-\u1cd2\u1cd4-\u1cfa\u1d00-\u1df9\u1dfb-\u1f15\u1f18-\u1f1d\u1f20-\u1f45\u1f48-\u1f4d\u1f50-\u1f57\u1f59\u1f5b\u1f5d\u1f5f-\u1f7d\u1f80-\u1fb4\u1fb6-\u1fbc\u1fbe\u1fc2-\u1fc4\u1fc6-\u1fcc\u1fd0-\u1fd3\u1fd6-\u1fdb\u1fe0-\u1fec\u1ff2-\u1ff4\u1ff6-\u1ffc\u203f-\u2040\u2054\u2071\u207f\u2090-\u209c\u20d0-\u20dc\u20e1\u20e5-\u20f0\u2102\u2107\u210a-\u2113\u2115\u2118-\u211d\u2124\u2126\u2128\u212a-\u2139\u213c-\u213f\u2145-\u2149\u214e\u2160-\u2188\u2c00-\u2c2e\u2c30-\u2c5e\u2c60-\u2ce4\u2ceb-\u2cf3\u2d00-\u2d25\u2d27\u2d2d\u2d30-\u2d67\u2d6f\u2d7f-\u2d96\u2da0-\u2da6\u2da8-\u2dae\u2db0-\u2db6\u2db8-\u2dbe\u2dc0-\u2dc6\u2dc8-\u2dce\u2dd0-\u2dd6\u2dd8-\u2dde\u2de0-\u2dff\u3005-\u3007\u3021-\u302f\u3031-\u3035\u3038-\u303c\u3041-\u3096\u3099-\u309f\u30a1-\u30fa\u30fc-\u30ff\u3105-\u312f\u3131-\u318e\u31a0-\u31ba\u31f0-\u31ff\u3400-\u4db5\u4e00-\u9fef\ua000-\ua48c\ua4d0-\ua4fd\ua500-\ua60c\ua610-\ua62b\ua640-\ua66f\ua674-\ua67d\ua67f-\ua6f1\ua717-\ua71f\ua722-\ua788\ua78b-\ua7bf\ua7c2-\ua7c6\ua7f7-\ua827\ua840-\ua873\ua880-\ua8c5\ua8d0-\ua8d9\ua8e0-\ua8f7\ua8fb\ua8fd-\ua92d\ua930-\ua953\ua960-\ua97c\ua980-\ua9c0\ua9cf-\ua9d9\ua9e0-\ua9fe\uaa00-\uaa36\uaa40-\uaa4d\uaa50-\uaa59\uaa60-\uaa76\uaa7a-\uaac2\uaadb-\uaadd\uaae0-\uaaef\uaaf2-\uaaf6\uab01-\uab06\uab09-\uab0e\uab11-\uab16\uab20-\uab26\uab28-\uab2e\uab30-\uab5a\uab5c-\uab67\uab70-\uabea\uabec-\uabed\uabf0-\uabf9\uac00-\ud7a3\ud7b0-\ud7c6\ud7cb-\ud7fb\uf900-\ufa6d\ufa70-\ufad9\ufb00-\ufb06\ufb13-\ufb17\ufb1d-\ufb28\ufb2a-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufbb1\ufbd3-\ufd3d\ufd50-\ufd8f\ufd92-\ufdc7\ufdf0-\ufdfb\ufe00-\ufe0f\ufe20-\ufe2f\ufe33-\ufe34\ufe4d-\ufe4f\ufe70-\ufe74\ufe76-\ufefc\uff10-\uff19\uff21-\uff3a\uff3f\uff41-\uff5a\uff66-\uffbe\uffc2-\uffc7\uffca-\uffcf\uffd2-\uffd7\uffda-\uffdc]|\ud800[\udc00-\udc0b\udc0d-\udc26\udc28-\udc3a\udc3c-\udc3d\udc3f-\udc4d\udc50-\udc5d\udc80-\udcfa\udd40-\udd74\uddfd\ude80-\ude9c\udea0-\uded0\udee0\udf00-\udf1f\udf2d-\udf4a\udf50-\udf7a\udf80-\udf9d\udfa0-\udfc3\udfc8-\udfcf\udfd1-\udfd5]|\ud801[\udc00-\udc9d\udca0-\udca9\udcb0-\udcd3\udcd8-\udcfb\udd00-\udd27\udd30-\udd63\ude00-\udf36\udf40-\udf55\udf60-\udf67]|\ud802[\udc00-\udc05\udc08\udc0a-\udc35\udc37-\udc38\udc3c\udc3f-\udc55\udc60-\udc76\udc80-\udc9e\udce0-\udcf2\udcf4-\udcf5\udd00-\udd15\udd20-\udd39\udd80-\uddb7\uddbe-\uddbf\ude00-\ude03\ude05-\ude06\ude0c-\ude13\ude15-\ude17\ude19-\ude35\ude38-\ude3a\ude3f\ude60-\ude7c\ude80-\ude9c\udec0-\udec7\udec9-\udee6\udf00-\udf35\udf40-\udf55\udf60-\udf72\udf80-\udf91]|\ud803[\udc00-\udc48\udc80-\udcb2\udcc0-\udcf2\udd00-\udd27\udd30-\udd39\udf00-\udf1c\udf27\udf30-\udf50\udfe0-\udff6]|\ud804[\udc00-\udc46\udc66-\udc6f\udc7f-\udcba\udcd0-\udce8\udcf0-\udcf9\udd00-\udd34\udd36-\udd3f\udd44-\udd46\udd50-\udd73\udd76\udd80-\uddc4\uddc9-\uddcc\uddd0-\uddda\udddc\ude00-\ude11\ude13-\ude37\ude3e\ude80-\ude86\ude88\ude8a-\ude8d\ude8f-\ude9d\ude9f-\udea8\udeb0-\udeea\udef0-\udef9\udf00-\udf03\udf05-\udf0c\udf0f-\udf10\udf13-\udf28\udf2a-\udf30\udf32-\udf33\udf35-\udf39\udf3b-\udf44\udf47-\udf48\udf4b-\udf4d\udf50\udf57\udf5d-\udf63\udf66-\udf6c\udf70-\udf74]|\ud805[\udc00-\udc4a\udc50-\udc59\udc5e-\udc5f\udc80-\udcc5\udcc7\udcd0-\udcd9\udd80-\uddb5\uddb8-\uddc0\uddd8-\udddd\ude00-\ude40\ude44\ude50-\ude59\ude80-\udeb8\udec0-\udec9\udf00-\udf1a\udf1d-\udf2b\udf30-\udf39]|\ud806[\udc00-\udc3a\udca0-\udce9\udcff\udda0-\udda7\uddaa-\uddd7\uddda-\udde1\udde3-\udde4\ude00-\ude3e\ude47\ude50-\ude99\ude9d\udec0-\udef8]|\ud807[\udc00-\udc08\udc0a-\udc36\udc38-\udc40\udc50-\udc59\udc72-\udc8f\udc92-\udca7\udca9-\udcb6\udd00-\udd06\udd08-\udd09\udd0b-\udd36\udd3a\udd3c-\udd3d\udd3f-\udd47\udd50-\udd59\udd60-\udd65\udd67-\udd68\udd6a-\udd8e\udd90-\udd91\udd93-\udd98\udda0-\udda9\udee0-\udef6]|\ud808[\udc00-\udf99]|\ud809[\udc00-\udc6e\udc80-\udd43]|\ud80c[\udc00-\udfff]|\ud80d[\udc00-\udc2e]|\ud811[\udc00-\ude46]|\ud81a[\udc00-\ude38\ude40-\ude5e\ude60-\ude69\uded0-\udeed\udef0-\udef4\udf00-\udf36\udf40-\udf43\udf50-\udf59\udf63-\udf77\udf7d-\udf8f]|\ud81b[\ude40-\ude7f\udf00-\udf4a\udf4f-\udf87\udf8f-\udf9f\udfe0-\udfe1\udfe3]|\ud81c[\udc00-\udfff]|\ud81d[\udc00-\udfff]|\ud81e[\udc00-\udfff]|\ud81f[\udc00-\udfff]|\ud820[\udc00-\udfff]|\ud821[\udc00-\udff7]|\ud822[\udc00-\udef2]|\ud82c[\udc00-\udd1e\udd50-\udd52\udd64-\udd67\udd70-\udefb]|\ud82f[\udc00-\udc6a\udc70-\udc7c\udc80-\udc88\udc90-\udc99\udc9d-\udc9e]|\ud834[\udd65-\udd69\udd6d-\udd72\udd7b-\udd82\udd85-\udd8b\uddaa-\uddad\ude42-\ude44]|\ud835[\udc00-\udc54\udc56-\udc9c\udc9e-\udc9f\udca2\udca5-\udca6\udca9-\udcac\udcae-\udcb9\udcbb\udcbd-\udcc3\udcc5-\udd05\udd07-\udd0a\udd0d-\udd14\udd16-\udd1c\udd1e-\udd39\udd3b-\udd3e\udd40-\udd44\udd46\udd4a-\udd50\udd52-\udea5\udea8-\udec0\udec2-\udeda\udedc-\udefa\udefc-\udf14\udf16-\udf34\udf36-\udf4e\udf50-\udf6e\udf70-\udf88\udf8a-\udfa8\udfaa-\udfc2\udfc4-\udfcb\udfce-\udfff]|\ud836[\ude00-\ude36\ude3b-\ude6c\ude75\ude84\ude9b-\ude9f\udea1-\udeaf]|\ud838[\udc00-\udc06\udc08-\udc18\udc1b-\udc21\udc23-\udc24\udc26-\udc2a\udd00-\udd2c\udd30-\udd3d\udd40-\udd49\udd4e\udec0-\udef9]|\ud83a[\udc00-\udcc4\udcd0-\udcd6\udd00-\udd4b\udd50-\udd59]|\ud83b[\ude00-\ude03\ude05-\ude1f\ude21-\ude22\ude24\ude27\ude29-\ude32\ude34-\ude37\ude39\ude3b\ude42\ude47\ude49\ude4b\ude4d-\ude4f\ude51-\ude52\ude54\ude57\ude59\ude5b\ude5d\ude5f\ude61-\ude62\ude64\ude67-\ude6a\ude6c-\ude72\ude74-\ude77\ude79-\ude7c\ude7e\ude80-\ude89\ude8b-\ude9b\udea1-\udea3\udea5-\udea9\udeab-\udebb]|\ud840[\udc00-\udfff]|\ud841[\udc00-\udfff]|\ud842[\udc00-\udfff]|\ud843[\udc00-\udfff]|\ud844[\udc00-\udfff]|\ud845[\udc00-\udfff]|\ud846[\udc00-\udfff]|\ud847[\udc00-\udfff]|\ud848[\udc00-\udfff]|\ud849[\udc00-\udfff]|\ud84a[\udc00-\udfff]|\ud84b[\udc00-\udfff]|\ud84c[\udc00-\udfff]|\ud84d[\udc00-\udfff]|\ud84e[\udc00-\udfff]|\ud84f[\udc00-\udfff]|\ud850[\udc00-\udfff]|\ud851[\udc00-\udfff]|\ud852[\udc00-\udfff]|\ud853[\udc00-\udfff]|\ud854[\udc00-\udfff]|\ud855[\udc00-\udfff]|\ud856[\udc00-\udfff]|\ud857[\udc00-\udfff]|\ud858[\udc00-\udfff]|\ud859[\udc00-\udfff]|\ud85a[\udc00-\udfff]|\ud85b[\udc00-\udfff]|\ud85c[\udc00-\udfff]|\ud85d[\udc00-\udfff]|\ud85e[\udc00-\udfff]|\ud85f[\udc00-\udfff]|\ud860[\udc00-\udfff]|\ud861[\udc00-\udfff]|\ud862[\udc00-\udfff]|\ud863[\udc00-\udfff]|\ud864[\udc00-\udfff]|\ud865[\udc00-\udfff]|\ud866[\udc00-\udfff]|\ud867[\udc00-\udfff]|\ud868[\udc00-\udfff]|\ud869[\udc00-\uded6\udf00-\udfff]|\ud86a[\udc00-\udfff]|\ud86b[\udc00-\udfff]|\ud86c[\udc00-\udfff]|\ud86d[\udc00-\udf34\udf40-\udfff]|\ud86e[\udc00-\udc1d\udc20-\udfff]|\ud86f[\udc00-\udfff]|\ud870[\udc00-\udfff]|\ud871[\udc00-\udfff]|\ud872[\udc00-\udfff]|\ud873[\udc00-\udea1\udeb0-\udfff]|\ud874[\udc00-\udfff]|\ud875[\udc00-\udfff]|\ud876[\udc00-\udfff]|\ud877[\udc00-\udfff]|\ud878[\udc00-\udfff]|\ud879[\udc00-\udfff]|\ud87a[\udc00-\udfe0]|\ud87e[\udc00-\ude1d]|\udb40[\udd00-\uddef])|[$_]|(\\u[0-9a-fA-F]{4}|\\u\{[0-9a-fA-F]{1,}\})|[\u200c\u200d])*>/,function(){return O(n.slice(3,-1),this.getCurrentState()),"NAMED_GROUP_REF"}],[/^\\b/,function(){return"ESC_b"}],[/^\\B/,function(){return"ESC_B"}],[/^\\c[a-zA-Z]/,function(){return"CTRL_CH"}],[/^\\0\d{1,2}/,function(){return"OCT_CODE"}],[/^\\0/,function(){return"DEC_CODE"}],[/^\\\d{1,3}/,function(){return"DEC_CODE"}],[/^\\u[dD][89abAB][0-9a-fA-F]{2}\\u[dD][c-fC-F][0-9a-fA-F]{2}/,function(){return"U_CODE_SURROGATE"}],[/^\\u\{[0-9a-fA-F]{1,}\}/,function(){return"U_CODE"}],[/^\\u[0-9a-fA-F]{4}/,function(){return"U_CODE"}],[/^\\[pP]\{\w+(?:=\w+)?\}/,function(){return"U_PROP_VALUE_EXP"}],[/^\\x[0-9a-fA-F]{2}/,function(){return"HEX_CODE"}],[/^\\[tnrdDsSwWvf]/,function(){return"META_CHAR"}],[/^\\\//,function(){return"ESC_CHAR"}],[/^\\[ #]/,function(){return"ESC_CHAR"}],[/^\\[\^\$\.\*\+\?\(\)\\\[\]\{\}\|\/]/,function(){return"ESC_CHAR"}],[/^\\[^*?+\[()\\|]/,function(){var u=this.getCurrentState();if("u_class"===u&&"\\-"===n)return"ESC_CHAR";if("u"===u||"xu"===u||"u_class"===u)throw new SyntaxError("invalid Unicode escape "+n);return"ESC_CHAR"}],[/^\(/,function(){return"CHAR"}],[/^\)/,function(){return"CHAR"}],[/^\(\?=/,function(){return"POS_LA_ASSERT"}],[/^\(\?!/,function(){return"NEG_LA_ASSERT"}],[/^\(\?<=/,function(){return"POS_LB_ASSERT"}],[/^\(\?<!/,function(){return"NEG_LB_ASSERT"}],[/^\(\?:/,function(){return"NON_CAPTURE_GROUP"}],[/^\(\?<(([\u0041-\u005a\u0061-\u007a\u00aa\u00b5\u00ba\u00c0-\u00d6\u00d8-\u00f6\u00f8-\u02c1\u02c6-\u02d1\u02e0-\u02e4\u02ec\u02ee\u0370-\u0374\u0376-\u0377\u037a-\u037d\u037f\u0386\u0388-\u038a\u038c\u038e-\u03a1\u03a3-\u03f5\u03f7-\u0481\u048a-\u052f\u0531-\u0556\u0559\u0560-\u0588\u05d0-\u05ea\u05ef-\u05f2\u0620-\u064a\u066e-\u066f\u0671-\u06d3\u06d5\u06e5-\u06e6\u06ee-\u06ef\u06fa-\u06fc\u06ff\u0710\u0712-\u072f\u074d-\u07a5\u07b1\u07ca-\u07ea\u07f4-\u07f5\u07fa\u0800-\u0815\u081a\u0824\u0828\u0840-\u0858\u0860-\u086a\u08a0-\u08b4\u08b6-\u08bd\u0904-\u0939\u093d\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098c\u098f-\u0990\u0993-\u09a8\u09aa-\u09b0\u09b2\u09b6-\u09b9\u09bd\u09ce\u09dc-\u09dd\u09df-\u09e1\u09f0-\u09f1\u09fc\u0a05-\u0a0a\u0a0f-\u0a10\u0a13-\u0a28\u0a2a-\u0a30\u0a32-\u0a33\u0a35-\u0a36\u0a38-\u0a39\u0a59-\u0a5c\u0a5e\u0a72-\u0a74\u0a85-\u0a8d\u0a8f-\u0a91\u0a93-\u0aa8\u0aaa-\u0ab0\u0ab2-\u0ab3\u0ab5-\u0ab9\u0abd\u0ad0\u0ae0-\u0ae1\u0af9\u0b05-\u0b0c\u0b0f-\u0b10\u0b13-\u0b28\u0b2a-\u0b30\u0b32-\u0b33\u0b35-\u0b39\u0b3d\u0b5c-\u0b5d\u0b5f-\u0b61\u0b71\u0b83\u0b85-\u0b8a\u0b8e-\u0b90\u0b92-\u0b95\u0b99-\u0b9a\u0b9c\u0b9e-\u0b9f\u0ba3-\u0ba4\u0ba8-\u0baa\u0bae-\u0bb9\u0bd0\u0c05-\u0c0c\u0c0e-\u0c10\u0c12-\u0c28\u0c2a-\u0c39\u0c3d\u0c58-\u0c5a\u0c60-\u0c61\u0c80\u0c85-\u0c8c\u0c8e-\u0c90\u0c92-\u0ca8\u0caa-\u0cb3\u0cb5-\u0cb9\u0cbd\u0cde\u0ce0-\u0ce1\u0cf1-\u0cf2\u0d05-\u0d0c\u0d0e-\u0d10\u0d12-\u0d3a\u0d3d\u0d4e\u0d54-\u0d56\u0d5f-\u0d61\u0d7a-\u0d7f\u0d85-\u0d96\u0d9a-\u0db1\u0db3-\u0dbb\u0dbd\u0dc0-\u0dc6\u0e01-\u0e30\u0e32-\u0e33\u0e40-\u0e46\u0e81-\u0e82\u0e84\u0e86-\u0e8a\u0e8c-\u0ea3\u0ea5\u0ea7-\u0eb0\u0eb2-\u0eb3\u0ebd\u0ec0-\u0ec4\u0ec6\u0edc-\u0edf\u0f00\u0f40-\u0f47\u0f49-\u0f6c\u0f88-\u0f8c\u1000-\u102a\u103f\u1050-\u1055\u105a-\u105d\u1061\u1065-\u1066\u106e-\u1070\u1075-\u1081\u108e\u10a0-\u10c5\u10c7\u10cd\u10d0-\u10fa\u10fc-\u1248\u124a-\u124d\u1250-\u1256\u1258\u125a-\u125d\u1260-\u1288\u128a-\u128d\u1290-\u12b0\u12b2-\u12b5\u12b8-\u12be\u12c0\u12c2-\u12c5\u12c8-\u12d6\u12d8-\u1310\u1312-\u1315\u1318-\u135a\u1380-\u138f\u13a0-\u13f5\u13f8-\u13fd\u1401-\u166c\u166f-\u167f\u1681-\u169a\u16a0-\u16ea\u16ee-\u16f8\u1700-\u170c\u170e-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176c\u176e-\u1770\u1780-\u17b3\u17d7\u17dc\u1820-\u1878\u1880-\u18a8\u18aa\u18b0-\u18f5\u1900-\u191e\u1950-\u196d\u1970-\u1974\u1980-\u19ab\u19b0-\u19c9\u1a00-\u1a16\u1a20-\u1a54\u1aa7\u1b05-\u1b33\u1b45-\u1b4b\u1b83-\u1ba0\u1bae-\u1baf\u1bba-\u1be5\u1c00-\u1c23\u1c4d-\u1c4f\u1c5a-\u1c7d\u1c80-\u1c88\u1c90-\u1cba\u1cbd-\u1cbf\u1ce9-\u1cec\u1cee-\u1cf3\u1cf5-\u1cf6\u1cfa\u1d00-\u1dbf\u1e00-\u1f15\u1f18-\u1f1d\u1f20-\u1f45\u1f48-\u1f4d\u1f50-\u1f57\u1f59\u1f5b\u1f5d\u1f5f-\u1f7d\u1f80-\u1fb4\u1fb6-\u1fbc\u1fbe\u1fc2-\u1fc4\u1fc6-\u1fcc\u1fd0-\u1fd3\u1fd6-\u1fdb\u1fe0-\u1fec\u1ff2-\u1ff4\u1ff6-\u1ffc\u2071\u207f\u2090-\u209c\u2102\u2107\u210a-\u2113\u2115\u2118-\u211d\u2124\u2126\u2128\u212a-\u2139\u213c-\u213f\u2145-\u2149\u214e\u2160-\u2188\u2c00-\u2c2e\u2c30-\u2c5e\u2c60-\u2ce4\u2ceb-\u2cee\u2cf2-\u2cf3\u2d00-\u2d25\u2d27\u2d2d\u2d30-\u2d67\u2d6f\u2d80-\u2d96\u2da0-\u2da6\u2da8-\u2dae\u2db0-\u2db6\u2db8-\u2dbe\u2dc0-\u2dc6\u2dc8-\u2dce\u2dd0-\u2dd6\u2dd8-\u2dde\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303c\u3041-\u3096\u309b-\u309f\u30a1-\u30fa\u30fc-\u30ff\u3105-\u312f\u3131-\u318e\u31a0-\u31ba\u31f0-\u31ff\u3400-\u4db5\u4e00-\u9fef\ua000-\ua48c\ua4d0-\ua4fd\ua500-\ua60c\ua610-\ua61f\ua62a-\ua62b\ua640-\ua66e\ua67f-\ua69d\ua6a0-\ua6ef\ua717-\ua71f\ua722-\ua788\ua78b-\ua7bf\ua7c2-\ua7c6\ua7f7-\ua801\ua803-\ua805\ua807-\ua80a\ua80c-\ua822\ua840-\ua873\ua882-\ua8b3\ua8f2-\ua8f7\ua8fb\ua8fd-\ua8fe\ua90a-\ua925\ua930-\ua946\ua960-\ua97c\ua984-\ua9b2\ua9cf\ua9e0-\ua9e4\ua9e6-\ua9ef\ua9fa-\ua9fe\uaa00-\uaa28\uaa40-\uaa42\uaa44-\uaa4b\uaa60-\uaa76\uaa7a\uaa7e-\uaaaf\uaab1\uaab5-\uaab6\uaab9-\uaabd\uaac0\uaac2\uaadb-\uaadd\uaae0-\uaaea\uaaf2-\uaaf4\uab01-\uab06\uab09-\uab0e\uab11-\uab16\uab20-\uab26\uab28-\uab2e\uab30-\uab5a\uab5c-\uab67\uab70-\uabe2\uac00-\ud7a3\ud7b0-\ud7c6\ud7cb-\ud7fb\uf900-\ufa6d\ufa70-\ufad9\ufb00-\ufb06\ufb13-\ufb17\ufb1d\ufb1f-\ufb28\ufb2a-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufbb1\ufbd3-\ufd3d\ufd50-\ufd8f\ufd92-\ufdc7\ufdf0-\ufdfb\ufe70-\ufe74\ufe76-\ufefc\uff21-\uff3a\uff41-\uff5a\uff66-\uffbe\uffc2-\uffc7\uffca-\uffcf\uffd2-\uffd7\uffda-\uffdc]|\ud800[\udc00-\udc0b\udc0d-\udc26\udc28-\udc3a\udc3c-\udc3d\udc3f-\udc4d\udc50-\udc5d\udc80-\udcfa\udd40-\udd74\ude80-\ude9c\udea0-\uded0\udf00-\udf1f\udf2d-\udf4a\udf50-\udf75\udf80-\udf9d\udfa0-\udfc3\udfc8-\udfcf\udfd1-\udfd5]|\ud801[\udc00-\udc9d\udcb0-\udcd3\udcd8-\udcfb\udd00-\udd27\udd30-\udd63\ude00-\udf36\udf40-\udf55\udf60-\udf67]|\ud802[\udc00-\udc05\udc08\udc0a-\udc35\udc37-\udc38\udc3c\udc3f-\udc55\udc60-\udc76\udc80-\udc9e\udce0-\udcf2\udcf4-\udcf5\udd00-\udd15\udd20-\udd39\udd80-\uddb7\uddbe-\uddbf\ude00\ude10-\ude13\ude15-\ude17\ude19-\ude35\ude60-\ude7c\ude80-\ude9c\udec0-\udec7\udec9-\udee4\udf00-\udf35\udf40-\udf55\udf60-\udf72\udf80-\udf91]|\ud803[\udc00-\udc48\udc80-\udcb2\udcc0-\udcf2\udd00-\udd23\udf00-\udf1c\udf27\udf30-\udf45\udfe0-\udff6]|\ud804[\udc03-\udc37\udc83-\udcaf\udcd0-\udce8\udd03-\udd26\udd44\udd50-\udd72\udd76\udd83-\uddb2\uddc1-\uddc4\uddda\udddc\ude00-\ude11\ude13-\ude2b\ude80-\ude86\ude88\ude8a-\ude8d\ude8f-\ude9d\ude9f-\udea8\udeb0-\udede\udf05-\udf0c\udf0f-\udf10\udf13-\udf28\udf2a-\udf30\udf32-\udf33\udf35-\udf39\udf3d\udf50\udf5d-\udf61]|\ud805[\udc00-\udc34\udc47-\udc4a\udc5f\udc80-\udcaf\udcc4-\udcc5\udcc7\udd80-\uddae\uddd8-\udddb\ude00-\ude2f\ude44\ude80-\udeaa\udeb8\udf00-\udf1a]|\ud806[\udc00-\udc2b\udca0-\udcdf\udcff\udda0-\udda7\uddaa-\uddd0\udde1\udde3\ude00\ude0b-\ude32\ude3a\ude50\ude5c-\ude89\ude9d\udec0-\udef8]|\ud807[\udc00-\udc08\udc0a-\udc2e\udc40\udc72-\udc8f\udd00-\udd06\udd08-\udd09\udd0b-\udd30\udd46\udd60-\udd65\udd67-\udd68\udd6a-\udd89\udd98\udee0-\udef2]|\ud808[\udc00-\udf99]|\ud809[\udc00-\udc6e\udc80-\udd43]|\ud80c[\udc00-\udfff]|\ud80d[\udc00-\udc2e]|\ud811[\udc00-\ude46]|\ud81a[\udc00-\ude38\ude40-\ude5e\uded0-\udeed\udf00-\udf2f\udf40-\udf43\udf63-\udf77\udf7d-\udf8f]|\ud81b[\ude40-\ude7f\udf00-\udf4a\udf50\udf93-\udf9f\udfe0-\udfe1\udfe3]|\ud81c[\udc00-\udfff]|\ud81d[\udc00-\udfff]|\ud81e[\udc00-\udfff]|\ud81f[\udc00-\udfff]|\ud820[\udc00-\udfff]|\ud821[\udc00-\udff7]|\ud822[\udc00-\udef2]|\ud82c[\udc00-\udd1e\udd50-\udd52\udd64-\udd67\udd70-\udefb]|\ud82f[\udc00-\udc6a\udc70-\udc7c\udc80-\udc88\udc90-\udc99]|\ud835[\udc00-\udc54\udc56-\udc9c\udc9e-\udc9f\udca2\udca5-\udca6\udca9-\udcac\udcae-\udcb9\udcbb\udcbd-\udcc3\udcc5-\udd05\udd07-\udd0a\udd0d-\udd14\udd16-\udd1c\udd1e-\udd39\udd3b-\udd3e\udd40-\udd44\udd46\udd4a-\udd50\udd52-\udea5\udea8-\udec0\udec2-\udeda\udedc-\udefa\udefc-\udf14\udf16-\udf34\udf36-\udf4e\udf50-\udf6e\udf70-\udf88\udf8a-\udfa8\udfaa-\udfc2\udfc4-\udfcb]|\ud838[\udd00-\udd2c\udd37-\udd3d\udd4e\udec0-\udeeb]|\ud83a[\udc00-\udcc4\udd00-\udd43\udd4b]|\ud83b[\ude00-\ude03\ude05-\ude1f\ude21-\ude22\ude24\ude27\ude29-\ude32\ude34-\ude37\ude39\ude3b\ude42\ude47\ude49\ude4b\ude4d-\ude4f\ude51-\ude52\ude54\ude57\ude59\ude5b\ude5d\ude5f\ude61-\ude62\ude64\ude67-\ude6a\ude6c-\ude72\ude74-\ude77\ude79-\ude7c\ude7e\ude80-\ude89\ude8b-\ude9b\udea1-\udea3\udea5-\udea9\udeab-\udebb]|\ud840[\udc00-\udfff]|\ud841[\udc00-\udfff]|\ud842[\udc00-\udfff]|\ud843[\udc00-\udfff]|\ud844[\udc00-\udfff]|\ud845[\udc00-\udfff]|\ud846[\udc00-\udfff]|\ud847[\udc00-\udfff]|\ud848[\udc00-\udfff]|\ud849[\udc00-\udfff]|\ud84a[\udc00-\udfff]|\ud84b[\udc00-\udfff]|\ud84c[\udc00-\udfff]|\ud84d[\udc00-\udfff]|\ud84e[\udc00-\udfff]|\ud84f[\udc00-\udfff]|\ud850[\udc00-\udfff]|\ud851[\udc00-\udfff]|\ud852[\udc00-\udfff]|\ud853[\udc00-\udfff]|\ud854[\udc00-\udfff]|\ud855[\udc00-\udfff]|\ud856[\udc00-\udfff]|\ud857[\udc00-\udfff]|\ud858[\udc00-\udfff]|\ud859[\udc00-\udfff]|\ud85a[\udc00-\udfff]|\ud85b[\udc00-\udfff]|\ud85c[\udc00-\udfff]|\ud85d[\udc00-\udfff]|\ud85e[\udc00-\udfff]|\ud85f[\udc00-\udfff]|\ud860[\udc00-\udfff]|\ud861[\udc00-\udfff]|\ud862[\udc00-\udfff]|\ud863[\udc00-\udfff]|\ud864[\udc00-\udfff]|\ud865[\udc00-\udfff]|\ud866[\udc00-\udfff]|\ud867[\udc00-\udfff]|\ud868[\udc00-\udfff]|\ud869[\udc00-\uded6\udf00-\udfff]|\ud86a[\udc00-\udfff]|\ud86b[\udc00-\udfff]|\ud86c[\udc00-\udfff]|\ud86d[\udc00-\udf34\udf40-\udfff]|\ud86e[\udc00-\udc1d\udc20-\udfff]|\ud86f[\udc00-\udfff]|\ud870[\udc00-\udfff]|\ud871[\udc00-\udfff]|\ud872[\udc00-\udfff]|\ud873[\udc00-\udea1\udeb0-\udfff]|\ud874[\udc00-\udfff]|\ud875[\udc00-\udfff]|\ud876[\udc00-\udfff]|\ud877[\udc00-\udfff]|\ud878[\udc00-\udfff]|\ud879[\udc00-\udfff]|\ud87a[\udc00-\udfe0]|\ud87e[\udc00-\ude1d])|[$_]|(\\u[0-9a-fA-F]{4}|\\u\{[0-9a-fA-F]{1,}\}))(([\u0030-\u0039\u0041-\u005a\u005f\u0061-\u007a\u00aa\u00b5\u00b7\u00ba\u00c0-\u00d6\u00d8-\u00f6\u00f8-\u02c1\u02c6-\u02d1\u02e0-\u02e4\u02ec\u02ee\u0300-\u0374\u0376-\u0377\u037a-\u037d\u037f\u0386-\u038a\u038c\u038e-\u03a1\u03a3-\u03f5\u03f7-\u0481\u0483-\u0487\u048a-\u052f\u0531-\u0556\u0559\u0560-\u0588\u0591-\u05bd\u05bf\u05c1-\u05c2\u05c4-\u05c5\u05c7\u05d0-\u05ea\u05ef-\u05f2\u0610-\u061a\u0620-\u0669\u066e-\u06d3\u06d5-\u06dc\u06df-\u06e8\u06ea-\u06fc\u06ff\u0710-\u074a\u074d-\u07b1\u07c0-\u07f5\u07fa\u07fd\u0800-\u082d\u0840-\u085b\u0860-\u086a\u08a0-\u08b4\u08b6-\u08bd\u08d3-\u08e1\u08e3-\u0963\u0966-\u096f\u0971-\u0983\u0985-\u098c\u098f-\u0990\u0993-\u09a8\u09aa-\u09b0\u09b2\u09b6-\u09b9\u09bc-\u09c4\u09c7-\u09c8\u09cb-\u09ce\u09d7\u09dc-\u09dd\u09df-\u09e3\u09e6-\u09f1\u09fc\u09fe\u0a01-\u0a03\u0a05-\u0a0a\u0a0f-\u0a10\u0a13-\u0a28\u0a2a-\u0a30\u0a32-\u0a33\u0a35-\u0a36\u0a38-\u0a39\u0a3c\u0a3e-\u0a42\u0a47-\u0a48\u0a4b-\u0a4d\u0a51\u0a59-\u0a5c\u0a5e\u0a66-\u0a75\u0a81-\u0a83\u0a85-\u0a8d\u0a8f-\u0a91\u0a93-\u0aa8\u0aaa-\u0ab0\u0ab2-\u0ab3\u0ab5-\u0ab9\u0abc-\u0ac5\u0ac7-\u0ac9\u0acb-\u0acd\u0ad0\u0ae0-\u0ae3\u0ae6-\u0aef\u0af9-\u0aff\u0b01-\u0b03\u0b05-\u0b0c\u0b0f-\u0b10\u0b13-\u0b28\u0b2a-\u0b30\u0b32-\u0b33\u0b35-\u0b39\u0b3c-\u0b44\u0b47-\u0b48\u0b4b-\u0b4d\u0b56-\u0b57\u0b5c-\u0b5d\u0b5f-\u0b63\u0b66-\u0b6f\u0b71\u0b82-\u0b83\u0b85-\u0b8a\u0b8e-\u0b90\u0b92-\u0b95\u0b99-\u0b9a\u0b9c\u0b9e-\u0b9f\u0ba3-\u0ba4\u0ba8-\u0baa\u0bae-\u0bb9\u0bbe-\u0bc2\u0bc6-\u0bc8\u0bca-\u0bcd\u0bd0\u0bd7\u0be6-\u0bef\u0c00-\u0c0c\u0c0e-\u0c10\u0c12-\u0c28\u0c2a-\u0c39\u0c3d-\u0c44\u0c46-\u0c48\u0c4a-\u0c4d\u0c55-\u0c56\u0c58-\u0c5a\u0c60-\u0c63\u0c66-\u0c6f\u0c80-\u0c83\u0c85-\u0c8c\u0c8e-\u0c90\u0c92-\u0ca8\u0caa-\u0cb3\u0cb5-\u0cb9\u0cbc-\u0cc4\u0cc6-\u0cc8\u0cca-\u0ccd\u0cd5-\u0cd6\u0cde\u0ce0-\u0ce3\u0ce6-\u0cef\u0cf1-\u0cf2\u0d00-\u0d03\u0d05-\u0d0c\u0d0e-\u0d10\u0d12-\u0d44\u0d46-\u0d48\u0d4a-\u0d4e\u0d54-\u0d57\u0d5f-\u0d63\u0d66-\u0d6f\u0d7a-\u0d7f\u0d82-\u0d83\u0d85-\u0d96\u0d9a-\u0db1\u0db3-\u0dbb\u0dbd\u0dc0-\u0dc6\u0dca\u0dcf-\u0dd4\u0dd6\u0dd8-\u0ddf\u0de6-\u0def\u0df2-\u0df3\u0e01-\u0e3a\u0e40-\u0e4e\u0e50-\u0e59\u0e81-\u0e82\u0e84\u0e86-\u0e8a\u0e8c-\u0ea3\u0ea5\u0ea7-\u0ebd\u0ec0-\u0ec4\u0ec6\u0ec8-\u0ecd\u0ed0-\u0ed9\u0edc-\u0edf\u0f00\u0f18-\u0f19\u0f20-\u0f29\u0f35\u0f37\u0f39\u0f3e-\u0f47\u0f49-\u0f6c\u0f71-\u0f84\u0f86-\u0f97\u0f99-\u0fbc\u0fc6\u1000-\u1049\u1050-\u109d\u10a0-\u10c5\u10c7\u10cd\u10d0-\u10fa\u10fc-\u1248\u124a-\u124d\u1250-\u1256\u1258\u125a-\u125d\u1260-\u1288\u128a-\u128d\u1290-\u12b0\u12b2-\u12b5\u12b8-\u12be\u12c0\u12c2-\u12c5\u12c8-\u12d6\u12d8-\u1310\u1312-\u1315\u1318-\u135a\u135d-\u135f\u1369-\u1371\u1380-\u138f\u13a0-\u13f5\u13f8-\u13fd\u1401-\u166c\u166f-\u167f\u1681-\u169a\u16a0-\u16ea\u16ee-\u16f8\u1700-\u170c\u170e-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176c\u176e-\u1770\u1772-\u1773\u1780-\u17d3\u17d7\u17dc-\u17dd\u17e0-\u17e9\u180b-\u180d\u1810-\u1819\u1820-\u1878\u1880-\u18aa\u18b0-\u18f5\u1900-\u191e\u1920-\u192b\u1930-\u193b\u1946-\u196d\u1970-\u1974\u1980-\u19ab\u19b0-\u19c9\u19d0-\u19da\u1a00-\u1a1b\u1a20-\u1a5e\u1a60-\u1a7c\u1a7f-\u1a89\u1a90-\u1a99\u1aa7\u1ab0-\u1abd\u1b00-\u1b4b\u1b50-\u1b59\u1b6b-\u1b73\u1b80-\u1bf3\u1c00-\u1c37\u1c40-\u1c49\u1c4d-\u1c7d\u1c80-\u1c88\u1c90-\u1cba\u1cbd-\u1cbf\u1cd0-\u1cd2\u1cd4-\u1cfa\u1d00-\u1df9\u1dfb-\u1f15\u1f18-\u1f1d\u1f20-\u1f45\u1f48-\u1f4d\u1f50-\u1f57\u1f59\u1f5b\u1f5d\u1f5f-\u1f7d\u1f80-\u1fb4\u1fb6-\u1fbc\u1fbe\u1fc2-\u1fc4\u1fc6-\u1fcc\u1fd0-\u1fd3\u1fd6-\u1fdb\u1fe0-\u1fec\u1ff2-\u1ff4\u1ff6-\u1ffc\u203f-\u2040\u2054\u2071\u207f\u2090-\u209c\u20d0-\u20dc\u20e1\u20e5-\u20f0\u2102\u2107\u210a-\u2113\u2115\u2118-\u211d\u2124\u2126\u2128\u212a-\u2139\u213c-\u213f\u2145-\u2149\u214e\u2160-\u2188\u2c00-\u2c2e\u2c30-\u2c5e\u2c60-\u2ce4\u2ceb-\u2cf3\u2d00-\u2d25\u2d27\u2d2d\u2d30-\u2d67\u2d6f\u2d7f-\u2d96\u2da0-\u2da6\u2da8-\u2dae\u2db0-\u2db6\u2db8-\u2dbe\u2dc0-\u2dc6\u2dc8-\u2dce\u2dd0-\u2dd6\u2dd8-\u2dde\u2de0-\u2dff\u3005-\u3007\u3021-\u302f\u3031-\u3035\u3038-\u303c\u3041-\u3096\u3099-\u309f\u30a1-\u30fa\u30fc-\u30ff\u3105-\u312f\u3131-\u318e\u31a0-\u31ba\u31f0-\u31ff\u3400-\u4db5\u4e00-\u9fef\ua000-\ua48c\ua4d0-\ua4fd\ua500-\ua60c\ua610-\ua62b\ua640-\ua66f\ua674-\ua67d\ua67f-\ua6f1\ua717-\ua71f\ua722-\ua788\ua78b-\ua7bf\ua7c2-\ua7c6\ua7f7-\ua827\ua840-\ua873\ua880-\ua8c5\ua8d0-\ua8d9\ua8e0-\ua8f7\ua8fb\ua8fd-\ua92d\ua930-\ua953\ua960-\ua97c\ua980-\ua9c0\ua9cf-\ua9d9\ua9e0-\ua9fe\uaa00-\uaa36\uaa40-\uaa4d\uaa50-\uaa59\uaa60-\uaa76\uaa7a-\uaac2\uaadb-\uaadd\uaae0-\uaaef\uaaf2-\uaaf6\uab01-\uab06\uab09-\uab0e\uab11-\uab16\uab20-\uab26\uab28-\uab2e\uab30-\uab5a\uab5c-\uab67\uab70-\uabea\uabec-\uabed\uabf0-\uabf9\uac00-\ud7a3\ud7b0-\ud7c6\ud7cb-\ud7fb\uf900-\ufa6d\ufa70-\ufad9\ufb00-\ufb06\ufb13-\ufb17\ufb1d-\ufb28\ufb2a-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufbb1\ufbd3-\ufd3d\ufd50-\ufd8f\ufd92-\ufdc7\ufdf0-\ufdfb\ufe00-\ufe0f\ufe20-\ufe2f\ufe33-\ufe34\ufe4d-\ufe4f\ufe70-\ufe74\ufe76-\ufefc\uff10-\uff19\uff21-\uff3a\uff3f\uff41-\uff5a\uff66-\uffbe\uffc2-\uffc7\uffca-\uffcf\uffd2-\uffd7\uffda-\uffdc]|\ud800[\udc00-\udc0b\udc0d-\udc26\udc28-\udc3a\udc3c-\udc3d\udc3f-\udc4d\udc50-\udc5d\udc80-\udcfa\udd40-\udd74\uddfd\ude80-\ude9c\udea0-\uded0\udee0\udf00-\udf1f\udf2d-\udf4a\udf50-\udf7a\udf80-\udf9d\udfa0-\udfc3\udfc8-\udfcf\udfd1-\udfd5]|\ud801[\udc00-\udc9d\udca0-\udca9\udcb0-\udcd3\udcd8-\udcfb\udd00-\udd27\udd30-\udd63\ude00-\udf36\udf40-\udf55\udf60-\udf67]|\ud802[\udc00-\udc05\udc08\udc0a-\udc35\udc37-\udc38\udc3c\udc3f-\udc55\udc60-\udc76\udc80-\udc9e\udce0-\udcf2\udcf4-\udcf5\udd00-\udd15\udd20-\udd39\udd80-\uddb7\uddbe-\uddbf\ude00-\ude03\ude05-\ude06\ude0c-\ude13\ude15-\ude17\ude19-\ude35\ude38-\ude3a\ude3f\ude60-\ude7c\ude80-\ude9c\udec0-\udec7\udec9-\udee6\udf00-\udf35\udf40-\udf55\udf60-\udf72\udf80-\udf91]|\ud803[\udc00-\udc48\udc80-\udcb2\udcc0-\udcf2\udd00-\udd27\udd30-\udd39\udf00-\udf1c\udf27\udf30-\udf50\udfe0-\udff6]|\ud804[\udc00-\udc46\udc66-\udc6f\udc7f-\udcba\udcd0-\udce8\udcf0-\udcf9\udd00-\udd34\udd36-\udd3f\udd44-\udd46\udd50-\udd73\udd76\udd80-\uddc4\uddc9-\uddcc\uddd0-\uddda\udddc\ude00-\ude11\ude13-\ude37\ude3e\ude80-\ude86\ude88\ude8a-\ude8d\ude8f-\ude9d\ude9f-\udea8\udeb0-\udeea\udef0-\udef9\udf00-\udf03\udf05-\udf0c\udf0f-\udf10\udf13-\udf28\udf2a-\udf30\udf32-\udf33\udf35-\udf39\udf3b-\udf44\udf47-\udf48\udf4b-\udf4d\udf50\udf57\udf5d-\udf63\udf66-\udf6c\udf70-\udf74]|\ud805[\udc00-\udc4a\udc50-\udc59\udc5e-\udc5f\udc80-\udcc5\udcc7\udcd0-\udcd9\udd80-\uddb5\uddb8-\uddc0\uddd8-\udddd\ude00-\ude40\ude44\ude50-\ude59\ude80-\udeb8\udec0-\udec9\udf00-\udf1a\udf1d-\udf2b\udf30-\udf39]|\ud806[\udc00-\udc3a\udca0-\udce9\udcff\udda0-\udda7\uddaa-\uddd7\uddda-\udde1\udde3-\udde4\ude00-\ude3e\ude47\ude50-\ude99\ude9d\udec0-\udef8]|\ud807[\udc00-\udc08\udc0a-\udc36\udc38-\udc40\udc50-\udc59\udc72-\udc8f\udc92-\udca7\udca9-\udcb6\udd00-\udd06\udd08-\udd09\udd0b-\udd36\udd3a\udd3c-\udd3d\udd3f-\udd47\udd50-\udd59\udd60-\udd65\udd67-\udd68\udd6a-\udd8e\udd90-\udd91\udd93-\udd98\udda0-\udda9\udee0-\udef6]|\ud808[\udc00-\udf99]|\ud809[\udc00-\udc6e\udc80-\udd43]|\ud80c[\udc00-\udfff]|\ud80d[\udc00-\udc2e]|\ud811[\udc00-\ude46]|\ud81a[\udc00-\ude38\ude40-\ude5e\ude60-\ude69\uded0-\udeed\udef0-\udef4\udf00-\udf36\udf40-\udf43\udf50-\udf59\udf63-\udf77\udf7d-\udf8f]|\ud81b[\ude40-\ude7f\udf00-\udf4a\udf4f-\udf87\udf8f-\udf9f\udfe0-\udfe1\udfe3]|\ud81c[\udc00-\udfff]|\ud81d[\udc00-\udfff]|\ud81e[\udc00-\udfff]|\ud81f[\udc00-\udfff]|\ud820[\udc00-\udfff]|\ud821[\udc00-\udff7]|\ud822[\udc00-\udef2]|\ud82c[\udc00-\udd1e\udd50-\udd52\udd64-\udd67\udd70-\udefb]|\ud82f[\udc00-\udc6a\udc70-\udc7c\udc80-\udc88\udc90-\udc99\udc9d-\udc9e]|\ud834[\udd65-\udd69\udd6d-\udd72\udd7b-\udd82\udd85-\udd8b\uddaa-\uddad\ude42-\ude44]|\ud835[\udc00-\udc54\udc56-\udc9c\udc9e-\udc9f\udca2\udca5-\udca6\udca9-\udcac\udcae-\udcb9\udcbb\udcbd-\udcc3\udcc5-\udd05\udd07-\udd0a\udd0d-\udd14\udd16-\udd1c\udd1e-\udd39\udd3b-\udd3e\udd40-\udd44\udd46\udd4a-\udd50\udd52-\udea5\udea8-\udec0\udec2-\udeda\udedc-\udefa\udefc-\udf14\udf16-\udf34\udf36-\udf4e\udf50-\udf6e\udf70-\udf88\udf8a-\udfa8\udfaa-\udfc2\udfc4-\udfcb\udfce-\udfff]|\ud836[\ude00-\ude36\ude3b-\ude6c\ude75\ude84\ude9b-\ude9f\udea1-\udeaf]|\ud838[\udc00-\udc06\udc08-\udc18\udc1b-\udc21\udc23-\udc24\udc26-\udc2a\udd00-\udd2c\udd30-\udd3d\udd40-\udd49\udd4e\udec0-\udef9]|\ud83a[\udc00-\udcc4\udcd0-\udcd6\udd00-\udd4b\udd50-\udd59]|\ud83b[\ude00-\ude03\ude05-\ude1f\ude21-\ude22\ude24\ude27\ude29-\ude32\ude34-\ude37\ude39\ude3b\ude42\ude47\ude49\ude4b\ude4d-\ude4f\ude51-\ude52\ude54\ude57\ude59\ude5b\ude5d\ude5f\ude61-\ude62\ude64\ude67-\ude6a\ude6c-\ude72\ude74-\ude77\ude79-\ude7c\ude7e\ude80-\ude89\ude8b-\ude9b\udea1-\udea3\udea5-\udea9\udeab-\udebb]|\ud840[\udc00-\udfff]|\ud841[\udc00-\udfff]|\ud842[\udc00-\udfff]|\ud843[\udc00-\udfff]|\ud844[\udc00-\udfff]|\ud845[\udc00-\udfff]|\ud846[\udc00-\udfff]|\ud847[\udc00-\udfff]|\ud848[\udc00-\udfff]|\ud849[\udc00-\udfff]|\ud84a[\udc00-\udfff]|\ud84b[\udc00-\udfff]|\ud84c[\udc00-\udfff]|\ud84d[\udc00-\udfff]|\ud84e[\udc00-\udfff]|\ud84f[\udc00-\udfff]|\ud850[\udc00-\udfff]|\ud851[\udc00-\udfff]|\ud852[\udc00-\udfff]|\ud853[\udc00-\udfff]|\ud854[\udc00-\udfff]|\ud855[\udc00-\udfff]|\ud856[\udc00-\udfff]|\ud857[\udc00-\udfff]|\ud858[\udc00-\udfff]|\ud859[\udc00-\udfff]|\ud85a[\udc00-\udfff]|\ud85b[\udc00-\udfff]|\ud85c[\udc00-\udfff]|\ud85d[\udc00-\udfff]|\ud85e[\udc00-\udfff]|\ud85f[\udc00-\udfff]|\ud860[\udc00-\udfff]|\ud861[\udc00-\udfff]|\ud862[\udc00-\udfff]|\ud863[\udc00-\udfff]|\ud864[\udc00-\udfff]|\ud865[\udc00-\udfff]|\ud866[\udc00-\udfff]|\ud867[\udc00-\udfff]|\ud868[\udc00-\udfff]|\ud869[\udc00-\uded6\udf00-\udfff]|\ud86a[\udc00-\udfff]|\ud86b[\udc00-\udfff]|\ud86c[\udc00-\udfff]|\ud86d[\udc00-\udf34\udf40-\udfff]|\ud86e[\udc00-\udc1d\udc20-\udfff]|\ud86f[\udc00-\udfff]|\ud870[\udc00-\udfff]|\ud871[\udc00-\udfff]|\ud872[\udc00-\udfff]|\ud873[\udc00-\udea1\udeb0-\udfff]|\ud874[\udc00-\udfff]|\ud875[\udc00-\udfff]|\ud876[\udc00-\udfff]|\ud877[\udc00-\udfff]|\ud878[\udc00-\udfff]|\ud879[\udc00-\udfff]|\ud87a[\udc00-\udfe0]|\ud87e[\udc00-\ude1d]|\udb40[\udd00-\uddef])|[$_]|(\\u[0-9a-fA-F]{4}|\\u\{[0-9a-fA-F]{1,}\})|[\u200c\u200d])*>/,function(){return O(n=n.slice(3,-1),this.getCurrentState()),"NAMED_CAPTURE_GROUP"}],[/^\(/,function(){return"L_PAREN"}],[/^\)/,function(){return"R_PAREN"}],[/^[*?+[^$]/,function(){return"CHAR"}],[/^\\\]/,function(){return"ESC_CHAR"}],[/^\]/,function(){return this.popState(),"R_BRACKET"}],[/^\^/,function(){return"BOS"}],[/^\$/,function(){return"EOS"}],[/^\*/,function(){return"STAR"}],[/^\?/,function(){return"Q_MARK"}],[/^\+/,function(){return"PLUS"}],[/^\|/,function(){return"BAR"}],[/^\./,function(){return"ANY"}],[/^\//,function(){return"SLASH"}],[/^[^*?+\[()\\|]/,function(){return"CHAR"}],[/^\[\^/,function(){var u=this.getCurrentState();return this.pushState("u"===u||"xu"===u?"u_class":"class"),"NEG_CLASS"}],[/^\[/,function(){var u=this.getCurrentState();return this.pushState("u"===u||"xu"===u?"u_class":"class"),"L_BRACKET"}]],h={INITIAL:[8,9,10,11,12,13,14,15,16,17,20,22,23,24,26,27,30,31,32,33,34,35,36,37,41,42,43,44,45,46,47,48,49,50,51],u:[8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,26,27,30,31,32,33,34,35,36,37,41,42,43,44,45,46,47,48,49,50,51],xu:[0,1,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,33,34,35,36,37,41,42,43,44,45,46,47,48,49,50,51],x:[0,1,8,9,10,11,12,13,14,15,16,17,20,22,23,24,26,27,30,31,32,33,34,35,36,37,41,42,43,44,45,46,47,48,49,50,51],u_class:[2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51],class:[2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,20,22,23,24,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51]},g={type:"$",value:""};b={initString:function(u){return this._string=u,this._cursor=0,this._states=["INITIAL"],this._tokensQueue=[],this._currentLine=1,this._currentColumn=0,this._currentLineBeginOffset=0,this._tokenStartOffset=0,this._tokenEndOffset=0,this._tokenStartLine=1,this._tokenEndLine=1,this._tokenStartColumn=0,this._tokenEndColumn=0,this},getStates:function(){return this._states},getCurrentState:function(){return this._states[this._states.length-1]},pushState:function(u){this._states.push(u)},begin:function(u){this.pushState(u)},popState:function(){return this._states.length>1?this._states.pop():this._states[0]},getNextToken:function(){if(this._tokensQueue.length>0)return this.onToken(this._toToken(this._tokensQueue.shift()));if(!this.hasMoreTokens())return this.onToken(g);for(var u=this._string.slice(this._cursor),e=h[this.getCurrentState()],r=0;r<e.length;r++){var t=e[r],a=p[t],f=this._match(u,a[0]);if(""===u&&""===f&&this._cursor++,null!==f){(n=f).length;var c=a[1].call(this);if(!c)return this.getNextToken();if(Array.isArray(c)){var i,o=c.slice(1);c=c[0],o.length>0&&(i=this._tokensQueue).unshift.apply(i,d(o))}return this.onToken(this._toToken(c,n))}}if(this.isEOF())return this._cursor++,g;this.throwUnexpectedToken(u[0],this._currentLine,this._currentColumn)},throwUnexpectedToken:function(u,e,r){var d=this._string.split("\n")[e-1],n="";throw d&&(n="\n\n"+d+"\n"+" ".repeat(r)+"^\n"),new SyntaxError(n+'Unexpected token: "'+u+'" at '+e+":"+r+".")},getCursor:function(){return this._cursor},getCurrentLine:function(){return this._currentLine},getCurrentColumn:function(){return this._currentColumn},_captureLocation:function(u){var e=/\n/g;this._tokenStartOffset=this._cursor,this._tokenStartLine=this._currentLine,this._tokenStartColumn=this._tokenStartOffset-this._currentLineBeginOffset;for(var r=void 0;null!==(r=e.exec(u));)this._currentLine++,this._currentLineBeginOffset=this._tokenStartOffset+r.index+1;this._tokenEndOffset=this._cursor+u.length,this._tokenEndLine=this._currentLine,this._tokenEndColumn=this._currentColumn=this._tokenEndOffset-this._currentLineBeginOffset},_toToken:function(u){return{type:u,value:arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",startOffset:this._tokenStartOffset,endOffset:this._tokenEndOffset,startLine:this._tokenStartLine,endLine:this._tokenEndLine,startColumn:this._tokenStartColumn,endColumn:this._tokenEndColumn}},isEOF:function(){return this._cursor===this._string.length},hasMoreTokens:function(){return this._cursor<=this._string.length},_match:function(u,e){var r=u.match(e);return r?(this._captureLocation(r[0]),this._cursor+=r[0].length,r[0]):null},onToken:function(u){return u}},t.lexer=b,t.tokenizer=b,t.options={captureLocations:!0};var v={setOptions:function(u){return t.options=u,this},getOptions:function(){return t.options},parse:function(u,e){if(!b)throw new Error("Tokenizer instance wasn't specified.");b.initString(u);var r=t.options;e&&(t.options=Object.assign({},t.options,e)),v.onParseBegin(u,b,t.options),l.length=0,l.push(0);var c=b.getNextToken(),p=null;do{c||(t.options=r,D());var h=l[l.length-1],g=o[c.type];s[h].hasOwnProperty(g)||(t.options=r,F(c));var y=s[h][g];if("s"===y[0]){var m=null;t.options.captureLocations&&(m={startOffset:c.startOffset,endOffset:c.endOffset,startLine:c.startLine,endLine:c.endLine,startColumn:c.startColumn,endColumn:c.endColumn}),p=this.onShift(c),l.push({symbol:o[p.type],semanticValue:p.value,loc:m},Number(y.slice(1))),c=b.getNextToken()}else if("r"===y[0]){var _=y.slice(1),C=i[_],S="function"==typeof C[2],x=S?[]:null,A=S&&t.options.captureLocations?[]:null;if(0!==C[1])for(var k=C[1];k-- >0;){l.pop();var P=l.pop();S&&(x.unshift(P.semanticValue),A&&A.unshift(P.loc))}var E={symbol:C[0]};if(S){n=p?p.value:null,p&&p.value.length;var w=null!==A?x.concat(A):x;C[2].apply(C,d(w)),E.semanticValue=a,A&&(E.loc=f)}var T=l[l.length-1],O=C[0];l.push(E,s[T][O])}else if("acc"===y){l.pop();var R=l.pop();return(1!==l.length||0!==l[0]||b.hasMoreTokens())&&(t.options=r,F(c)),R.hasOwnProperty("semanticValue")?(t.options=r,v.onParseEnd(R.semanticValue),R.semanticValue):(v.onParseEnd(),t.options=r,!0)}}while(b.hasMoreTokens()||l.length>1)},setTokenizer:function(u){return b=u,v},getTokenizer:function(){return b},onParseBegin:function(u,e,r){},onParseEnd:function(u){},onShift:function(u){return u}},y=0,m={},_="";function C(u){var e=u.match(/\d+/g).map(Number);if(Number.isFinite(e[1])&&e[1]<e[0])throw new SyntaxError("Numbers out of order in "+u+" quantifier");return e}function S(u,e){if("control"===u.kind||"control"===e.kind||!isNaN(u.codePoint)&&!isNaN(e.codePoint)&&u.codePoint>e.codePoint)throw new SyntaxError("Range "+u.value+"-"+e.value+" out of order in character class")}v.onParseBegin=function(u,e){_=u,y=0,m={};var r=u.lastIndexOf("/"),d=u.slice(r);d.includes("x")&&d.includes("u")?e.pushState("xu"):(d.includes("x")&&e.pushState("x"),d.includes("u")&&e.pushState("u"))},v.onShift=function(u){return"L_PAREN"!==u.type&&"NAMED_CAPTURE_GROUP"!==u.type||(u.value=new String(u.value),u.value.groupNumber=++y),u};var x=r(54009);function A(u,e,r){var d=void 0,n=void 0;switch(e){case"decimal":n=Number(u.slice(1)),d=String.fromCodePoint(n);break;case"oct":n=parseInt(u.slice(1),8),d=String.fromCodePoint(n);break;case"hex":case"unicode":if(u.lastIndexOf("\\u")>0){var t=function(u,e){if(Array.isArray(u))return u;if(Symbol.iterator in Object(u))return function(u,e){var r=[],d=!0,n=!1,t=void 0;try{for(var a,f=u[Symbol.iterator]();!(d=(a=f.next()).done)&&(r.push(a.value),!e||r.length!==e);d=!0);}catch(u){n=!0,t=u}finally{try{!d&&f.return&&f.return()}finally{if(n)throw t}}return r}(u,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")}(u.split("\\u").slice(1),2),a=t[0],f=t[1];n=1024*((a=parseInt(a,16))-55296)+((f=parseInt(f,16))-56320)+65536,d=String.fromCodePoint(n)}else{var c=u.slice(2).replace("{","");if((n=parseInt(c,16))>1114111)throw new SyntaxError("Bad character escape sequence: "+u);d=String.fromCodePoint(n)}break;case"meta":switch(u){case"\\t":n=(d="\t").codePointAt(0);break;case"\\n":n=(d="\n").codePointAt(0);break;case"\\r":n=(d="\r").codePointAt(0);break;case"\\v":n=(d="\v").codePointAt(0);break;case"\\f":n=(d="\f").codePointAt(0);break;case"\\b":n=(d="\b").codePointAt(0);case"\\0":d="\0",n=0;case".":d=".",n=NaN;break;default:n=NaN}break;case"simple":n=(d=u).codePointAt(0)}return L({type:"Char",value:u,kind:e,symbol:d,codePoint:n},r)}var k="gimsuxy";function P(u){var e=new Set,r=!0,d=!1,n=void 0;try{for(var t,a=u[Symbol.iterator]();!(r=(t=a.next()).done);r=!0){var f=t.value;if(e.has(f)||!k.includes(f))throw new SyntaxError("Invalid flags: "+u);e.add(f)}}catch(u){d=!0,n=u}finally{try{!r&&a.return&&a.return()}finally{if(d)throw n}}return u.split("").sort().join("")}var E=/^\\u[0-9a-fA-F]{4}/,w=/^\\u\{[0-9a-fA-F]{1,}\}/,T=/\\u\{[0-9a-fA-F]{1,}\}/;function O(u,e){if(T.test(u)&&"u"!==e&&"xu"!==e&&"u_class"!==e)throw new SyntaxError('invalid group Unicode name "'+u+'", use `u` flag.');return u}var R=/\\u(?:([dD][89aAbB][0-9a-fA-F]{2})\\u([dD][c-fC-F][0-9a-fA-F]{2})|([dD][89aAbB][0-9a-fA-F]{2})|([dD][c-fC-F][0-9a-fA-F]{2})|([0-9a-ce-fA-CE-F][0-9a-fA-F]{3}|[dD][0-7][0-9a-fA-F]{2})|\{(0*(?:[0-9a-fA-F]{1,5}|10[0-9a-fA-F]{4}))\})/;function N(u){return u.replace(new RegExp(R,"g"),(function(u,e,r,d,n,t,a){return e?String.fromCodePoint(parseInt(e,16),parseInt(r,16)):d?String.fromCodePoint(parseInt(d,16)):n?String.fromCodePoint(parseInt(n,16)):t?String.fromCodePoint(parseInt(t,16)):a?String.fromCodePoint(parseInt(a,16)):u}))}function L(u,e){return t.options.captureLocations&&(u.loc={source:_.slice(e.startOffset,e.endOffset),start:{line:e.startLine,column:e.startColumn,offset:e.startOffset},end:{line:e.endLine,column:e.endColumn,offset:e.endOffset}}),u}function I(u,e){return t.options.captureLocations?{startOffset:u.startOffset,endOffset:e.endOffset,startLine:u.startLine,endLine:e.endLine,startColumn:u.startColumn,endColumn:e.endColumn}:null}function F(u){"$"===u.type&&D(),b.throwUnexpectedToken(u.value,u.startLine,u.startColumn)}function D(){!function(u){throw new SyntaxError("Unexpected end of input.")}()}u.exports=v},46418:(u,e,r)=>{var d=r(19495),n=d.parse.bind(d);d.parse=function(u,e){return n(""+u,e)},d.setOptions({captureLocations:!1}),u.exports=d},54009:u=>{var e={General_Category:"gc",Script:"sc",Script_Extensions:"scx"},r=i(e),d={ASCII:"ASCII",ASCII_Hex_Digit:"AHex",Alphabetic:"Alpha",Any:"Any",Assigned:"Assigned",Bidi_Control:"Bidi_C",Bidi_Mirrored:"Bidi_M",Case_Ignorable:"CI",Cased:"Cased",Changes_When_Casefolded:"CWCF",Changes_When_Casemapped:"CWCM",Changes_When_Lowercased:"CWL",Changes_When_NFKC_Casefolded:"CWKCF",Changes_When_Titlecased:"CWT",Changes_When_Uppercased:"CWU",Dash:"Dash",Default_Ignorable_Code_Point:"DI",Deprecated:"Dep",Diacritic:"Dia",Emoji:"Emoji",Emoji_Component:"Emoji_Component",Emoji_Modifier:"Emoji_Modifier",Emoji_Modifier_Base:"Emoji_Modifier_Base",Emoji_Presentation:"Emoji_Presentation",Extended_Pictographic:"Extended_Pictographic",Extender:"Ext",Grapheme_Base:"Gr_Base",Grapheme_Extend:"Gr_Ext",Hex_Digit:"Hex",IDS_Binary_Operator:"IDSB",IDS_Trinary_Operator:"IDST",ID_Continue:"IDC",ID_Start:"IDS",Ideographic:"Ideo",Join_Control:"Join_C",Logical_Order_Exception:"LOE",Lowercase:"Lower",Math:"Math",Noncharacter_Code_Point:"NChar",Pattern_Syntax:"Pat_Syn",Pattern_White_Space:"Pat_WS",Quotation_Mark:"QMark",Radical:"Radical",Regional_Indicator:"RI",Sentence_Terminal:"STerm",Soft_Dotted:"SD",Terminal_Punctuation:"Term",Unified_Ideograph:"UIdeo",Uppercase:"Upper",Variation_Selector:"VS",White_Space:"space",XID_Continue:"XIDC",XID_Start:"XIDS"},n=i(d),t={Cased_Letter:"LC",Close_Punctuation:"Pe",Connector_Punctuation:"Pc",Control:["Cc","cntrl"],Currency_Symbol:"Sc",Dash_Punctuation:"Pd",Decimal_Number:["Nd","digit"],Enclosing_Mark:"Me",Final_Punctuation:"Pf",Format:"Cf",Initial_Punctuation:"Pi",Letter:"L",Letter_Number:"Nl",Line_Separator:"Zl",Lowercase_Letter:"Ll",Mark:["M","Combining_Mark"],Math_Symbol:"Sm",Modifier_Letter:"Lm",Modifier_Symbol:"Sk",Nonspacing_Mark:"Mn",Number:"N",Open_Punctuation:"Ps",Other:"C",Other_Letter:"Lo",Other_Number:"No",Other_Punctuation:"Po",Other_Symbol:"So",Paragraph_Separator:"Zp",Private_Use:"Co",Punctuation:["P","punct"],Separator:"Z",Space_Separator:"Zs",Spacing_Mark:"Mc",Surrogate:"Cs",Symbol:"S",Titlecase_Letter:"Lt",Unassigned:"Cn",Uppercase_Letter:"Lu"},a=i(t),f={Adlam:"Adlm",Ahom:"Ahom",Anatolian_Hieroglyphs:"Hluw",Arabic:"Arab",Armenian:"Armn",Avestan:"Avst",Balinese:"Bali",Bamum:"Bamu",Bassa_Vah:"Bass",Batak:"Batk",Bengali:"Beng",Bhaiksuki:"Bhks",Bopomofo:"Bopo",Brahmi:"Brah",Braille:"Brai",Buginese:"Bugi",Buhid:"Buhd",Canadian_Aboriginal:"Cans",Carian:"Cari",Caucasian_Albanian:"Aghb",Chakma:"Cakm",Cham:"Cham",Cherokee:"Cher",Common:"Zyyy",Coptic:["Copt","Qaac"],Cuneiform:"Xsux",Cypriot:"Cprt",Cyrillic:"Cyrl",Deseret:"Dsrt",Devanagari:"Deva",Dogra:"Dogr",Duployan:"Dupl",Egyptian_Hieroglyphs:"Egyp",Elbasan:"Elba",Ethiopic:"Ethi",Georgian:"Geor",Glagolitic:"Glag",Gothic:"Goth",Grantha:"Gran",Greek:"Grek",Gujarati:"Gujr",Gunjala_Gondi:"Gong",Gurmukhi:"Guru",Han:"Hani",Hangul:"Hang",Hanifi_Rohingya:"Rohg",Hanunoo:"Hano",Hatran:"Hatr",Hebrew:"Hebr",Hiragana:"Hira",Imperial_Aramaic:"Armi",Inherited:["Zinh","Qaai"],Inscriptional_Pahlavi:"Phli",Inscriptional_Parthian:"Prti",Javanese:"Java",Kaithi:"Kthi",Kannada:"Knda",Katakana:"Kana",Kayah_Li:"Kali",Kharoshthi:"Khar",Khmer:"Khmr",Khojki:"Khoj",Khudawadi:"Sind",Lao:"Laoo",Latin:"Latn",Lepcha:"Lepc",Limbu:"Limb",Linear_A:"Lina",Linear_B:"Linb",Lisu:"Lisu",Lycian:"Lyci",Lydian:"Lydi",Mahajani:"Mahj",Makasar:"Maka",Malayalam:"Mlym",Mandaic:"Mand",Manichaean:"Mani",Marchen:"Marc",Medefaidrin:"Medf",Masaram_Gondi:"Gonm",Meetei_Mayek:"Mtei",Mende_Kikakui:"Mend",Meroitic_Cursive:"Merc",Meroitic_Hieroglyphs:"Mero",Miao:"Plrd",Modi:"Modi",Mongolian:"Mong",Mro:"Mroo",Multani:"Mult",Myanmar:"Mymr",Nabataean:"Nbat",New_Tai_Lue:"Talu",Newa:"Newa",Nko:"Nkoo",Nushu:"Nshu",Ogham:"Ogam",Ol_Chiki:"Olck",Old_Hungarian:"Hung",Old_Italic:"Ital",Old_North_Arabian:"Narb",Old_Permic:"Perm",Old_Persian:"Xpeo",Old_Sogdian:"Sogo",Old_South_Arabian:"Sarb",Old_Turkic:"Orkh",Oriya:"Orya",Osage:"Osge",Osmanya:"Osma",Pahawh_Hmong:"Hmng",Palmyrene:"Palm",Pau_Cin_Hau:"Pauc",Phags_Pa:"Phag",Phoenician:"Phnx",Psalter_Pahlavi:"Phlp",Rejang:"Rjng",Runic:"Runr",Samaritan:"Samr",Saurashtra:"Saur",Sharada:"Shrd",Shavian:"Shaw",Siddham:"Sidd",SignWriting:"Sgnw",Sinhala:"Sinh",Sogdian:"Sogd",Sora_Sompeng:"Sora",Soyombo:"Soyo",Sundanese:"Sund",Syloti_Nagri:"Sylo",Syriac:"Syrc",Tagalog:"Tglg",Tagbanwa:"Tagb",Tai_Le:"Tale",Tai_Tham:"Lana",Tai_Viet:"Tavt",Takri:"Takr",Tamil:"Taml",Tangut:"Tang",Telugu:"Telu",Thaana:"Thaa",Thai:"Thai",Tibetan:"Tibt",Tifinagh:"Tfng",Tirhuta:"Tirh",Ugaritic:"Ugar",Vai:"Vaii",Warang_Citi:"Wara",Yi:"Yiii",Zanabazar_Square:"Zanb"},c=i(f);function i(u){var e={};for(var r in u)if(u.hasOwnProperty(r)){var d=u[r];if(Array.isArray(d))for(var n=0;n<d.length;n++)e[d[n]]=r;else e[d]=r}return e}function o(u){return t.hasOwnProperty(u)||a.hasOwnProperty(u)}function s(u){return f.hasOwnProperty(u)||c.hasOwnProperty(u)}u.exports={isAlias:function(u){return r.hasOwnProperty(u)||n.hasOwnProperty(u)},isValidName:function(u){return e.hasOwnProperty(u)||r.hasOwnProperty(u)||d.hasOwnProperty(u)||n.hasOwnProperty(u)},isValidValue:function(u,e){return function(u){return"General_Category"===u||"gc"==u}(u)?o(e):!!function(u){return"Script"===u||"Script_Extensions"===u||"sc"===u||"scx"===u}(u)&&s(e)},isGeneralCategoryValue:o,isScriptCategoryValue:s,isBinaryPropertyName:function(u){return d.hasOwnProperty(u)||n.hasOwnProperty(u)},getCanonicalName:function(u){return r.hasOwnProperty(u)?r[u]:n.hasOwnProperty(u)?n[u]:null},getCanonicalValue:function(u){return a.hasOwnProperty(u)?a[u]:c.hasOwnProperty(u)?c[u]:n.hasOwnProperty(u)?n[u]:null},NON_BINARY_PROP_NAMES_TO_ALIASES:e,NON_BINARY_ALIASES_TO_PROP_NAMES:r,BINARY_PROP_NAMES_TO_ALIASES:d,BINARY_ALIASES_TO_PROP_NAMES:n,GENERAL_CATEGORY_VALUE_TO_ALIASES:t,GENERAL_CATEGORY_VALUE_ALIASES_TO_VALUES:a,SCRIPT_VALUE_TO_ALIASES:f,SCRIPT_VALUE_ALIASES_TO_VALUE:c}},1315:(u,e,r)=>{var d=r(47921),n=r(44340),t=r(33186),a=r(46418),f=r(42113),c=r(34862),i=r(76186),o=r(3561).RegExpTree,s={parser:a,fa:i,TransformResult:f.TransformResult,parse:function(u,e){return a.parse(""+u,e)},traverse:function(u,e,r){return c.traverse(u,e,r)},transform:function(u,e){return f.transform(u,e)},generate:function(u){return n.generate(u)},toRegExp:function(u){var e=this.compatTranspile(u);return new RegExp(e.getSource(),e.getFlags())},optimize:function(u,e){var r=(arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}).blacklist;return t.optimize(u,{whitelist:e,blacklist:r})},compatTranspile:function(u,e){return d.transform(u,e)},exec:function(u,e){if("string"==typeof u){var r=this.compatTranspile(u),d=r.getExtra();u=d.namedCapturingGroups?new o(r.toRegExp(),{flags:r.getFlags(),source:r.getSource(),groups:d.namedCapturingGroups}):r.toRegExp()}return u.exec(e)}};u.exports=s},42113:(u,e,r)=>{var d=function(){function u(u,e){for(var r=0;r<e.length;r++){var d=e[r];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(u,d.key,d)}}return function(e,r,d){return r&&u(e.prototype,r),d&&u(e,d),e}}(),n=r(44340),t=r(46418),a=r(34862),f=function(){function u(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;!function(u,e){if(!(u instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u),this._ast=e,this._source=null,this._string=null,this._regexp=null,this._extra=r}return d(u,[{key:"getAST",value:function(){return this._ast}},{key:"setExtra",value:function(u){this._extra=u}},{key:"getExtra",value:function(){return this._extra}},{key:"toRegExp",value:function(){return this._regexp||(this._regexp=new RegExp(this.getSource(),this._ast.flags)),this._regexp}},{key:"getSource",value:function(){return this._source||(this._source=n.generate(this._ast.body)),this._source}},{key:"getFlags",value:function(){return this._ast.flags}},{key:"toString",value:function(){return this._string||(this._string=n.generate(this._ast)),this._string}}]),u}();u.exports={TransformResult:f,transform:function(u,e){var r=u;return u instanceof RegExp&&(u=""+u),"string"==typeof u&&(r=t.parse(u,{captureLocations:!0})),a.traverse(r,e),new f(r)}}},33065:u=>{u.exports={disjunctionToList:function u(e){if("Disjunction"!==e.type)throw new TypeError('Expected "Disjunction" node, got "'+e.type+'"');var r=[];return e.left&&"Disjunction"===e.left.type?r.push.apply(r,function(u){if(Array.isArray(u)){for(var e=0,r=Array(u.length);e<u.length;e++)r[e]=u[e];return r}return Array.from(u)}(u(e.left)).concat([e.right])):r.push(e.left,e.right),r},listToDisjunction:function(u){return u.reduce((function(u,e){return{type:"Disjunction",left:u,right:e}}))},increaseQuantifierByOne:function(u){"*"===u.kind?u.kind="+":"+"===u.kind?(u.kind="Range",u.from=2,delete u.to):"?"===u.kind?(u.kind="Range",u.from=1,u.to=2):"Range"===u.kind&&(u.from+=1,u.to&&(u.to+=1))}}},34862:(u,e,r)=>{var d=r(53636);u.exports={traverse:function(u,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{asNodes:!1};function n(u,e,r,n){var t=d.getForNode(e);return d.getForNode(u,t,r,n)}Array.isArray(e)||(e=[e]),e=e.filter((function(e){return"function"!=typeof e.shouldRun||e.shouldRun(u)})),d.initRegistry(),e.forEach((function(e){"function"==typeof e.init&&e.init(u)})),function(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.pre,n=e.post,t=e.skipProperty;!function u(e,a,f,c){if(e&&"string"==typeof e.type){var i=void 0;if(r&&(i=r(e,a,f,c)),!1!==i)for(var o in a&&a[f]&&(e=isNaN(c)?a[f]:a[f][c]),e)if(e.hasOwnProperty(o)){if(t?t(o,e):"$"===o[0])continue;var s=e[o];if(Array.isArray(s)){var l=0;for(d.traversingIndexStack.push(l);l<s.length;)u(s[l],e,o,l),l=d.updateTraversingIndex(1);d.traversingIndexStack.pop()}else u(s,e,o)}n&&n(e,a,f,c)}}(u,null)}(u,{pre:function(u,d,t,a){var f=void 0;r.asNodes||(f=n(u,d,t,a));var c=!0,i=!1,o=void 0;try{for(var s,l=e[Symbol.iterator]();!(c=(s=l.next()).done);c=!0){var b=s.value;if("function"==typeof b["*"])if(f){if(!f.isRemoved()&&!1===b["*"](f))return!1}else b["*"](u,d,t,a);var p=void 0;if("function"==typeof b[u.type]?p=b[u.type]:"object"==typeof b[u.type]&&"function"==typeof b[u.type].pre&&(p=b[u.type].pre),p)if(f){if(!f.isRemoved()&&!1===p.call(b,f))return!1}else p.call(b,u,d,t,a)}}catch(u){i=!0,o=u}finally{try{!c&&l.return&&l.return()}finally{if(i)throw o}}},post:function(u,d,t,a){if(u){var f=void 0;r.asNodes||(f=n(u,d,t,a));var c=!0,i=!1,o=void 0;try{for(var s,l=e[Symbol.iterator]();!(c=(s=l.next()).done);c=!0){var b=s.value,p=void 0;if("object"==typeof b[u.type]&&"function"==typeof b[u.type].post&&(p=b[u.type].post),p)if(f){if(!f.isRemoved()&&!1===p.call(b,f))return!1}else p.call(b,u,d,t,a)}}catch(u){i=!0,o=u}finally{try{!c&&l.return&&l.return()}finally{if(i)throw o}}}},skipProperty:function(u){return"loc"===u}})}}},53636:u=>{var e=function(){function u(u,e){for(var r=0;r<e.length;r++){var d=e[r];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(u,d.key,d)}}return function(e,r,d){return r&&u(e.prototype,r),d&&u(e,d),e}}(),r="expressions",d="expression",n=function(){function u(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;!function(u,e){if(!(u instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u),this.node=e,this.parentPath=r,this.parent=r?r.node:null,this.property=d,this.index=n}return e(u,[{key:"_enforceProp",value:function(u){if(!this.node.hasOwnProperty(u))throw new Error("Node of type "+this.node.type+" doesn't have \""+u+'" collection.')}},{key:"setChild",value:function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,a=void 0;return null!=n?(t||(t=r),this._enforceProp(t),this.node[t][n]=e,a=u.getForNode(e,this,t,n)):(t||(t=d),this._enforceProp(t),this.node[t]=e,a=u.getForNode(e,this,t,null)),a}},{key:"appendChild",value:function(u){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;e||(e=r),this._enforceProp(e);var d=this.node[e].length;return this.setChild(u,d,e)}},{key:"insertChildAt",value:function(e,d){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:r;this._enforceProp(n),this.node[n].splice(d,0,e),d<=u.getTraversingIndex()&&u.updateTraversingIndex(1),this._rebuildIndex(this.node,n)}},{key:"remove",value:function(){if(!this.isRemoved()&&(u.registry.delete(this.node),this.node=null,this.parent)){if(null!==this.index)return this.parent[this.property].splice(this.index,1),this.index<=u.getTraversingIndex()&&u.updateTraversingIndex(-1),this._rebuildIndex(this.parent,this.property),this.index=null,void(this.property=null);delete this.parent[this.property],this.property=null}}},{key:"_rebuildIndex",value:function(e,r){for(var d=u.getForNode(e),n=0;n<e[r].length;n++)u.getForNode(e[r][n],d,r,n).index=n}},{key:"isRemoved",value:function(){return null===this.node}},{key:"replace",value:function(e){return u.registry.delete(this.node),this.node=e,this.parent?(null!==this.index?this.parent[this.property][this.index]=e:this.parent[this.property]=e,u.getForNode(e,this.parentPath,this.property,this.index)):null}},{key:"update",value:function(u){Object.assign(this.node,u)}},{key:"getParent",value:function(){return this.parentPath}},{key:"getChild",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return this.node.expressions?u.getForNode(this.node.expressions[e],this,r,e):this.node.expression&&0==e?u.getForNode(this.node.expression,this,d):null}},{key:"hasEqualSource",value:function(u){return JSON.stringify(this.node,t)===JSON.stringify(u.node,t)}},{key:"jsonEncode",value:function(){var u=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=u.format,r=u.useLoc;return JSON.stringify(this.node,r?null:t,e)}},{key:"getPreviousSibling",value:function(){return this.parent&&null!=this.index?u.getForNode(this.parent[this.property][this.index-1],u.getForNode(this.parent),this.property,this.index-1):null}},{key:"getNextSibling",value:function(){return this.parent&&null!=this.index?u.getForNode(this.parent[this.property][this.index+1],u.getForNode(this.parent),this.property,this.index+1):null}}],[{key:"getForNode",value:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:-1;if(!e)return null;u.registry.has(e)||u.registry.set(e,new u(e,r,d,-1==n?null:n));var t=u.registry.get(e);return null!==r&&(t.parentPath=r,t.parent=t.parentPath.node),null!==d&&(t.property=d),n>=0&&(t.index=n),t}},{key:"initRegistry",value:function(){u.registry||(u.registry=new Map),u.registry.clear()}},{key:"updateTraversingIndex",value:function(e){return u.traversingIndexStack[u.traversingIndexStack.length-1]+=e}},{key:"getTraversingIndex",value:function(){return u.traversingIndexStack[u.traversingIndexStack.length-1]}}]),u}();function t(u,e){if("loc"!==u)return e}n.initRegistry(),n.traversingIndexStack=[],u.exports=n},64456:u=>{u.exports=function u(e){if(null===e||"object"!=typeof e)return e;var r=void 0;for(var d in r=Array.isArray(e)?[]:{},e)r[d]=u(e[d]);return r}},3661:(u,e,r)=>{u.exports=r(1315)}}]);
"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[1684,5601],{95601:(e,t,n)=>{n.r(t),n.d(t,{default:()=>l});var a=n(3053),o=n(38639),i=n(33673),r=n(42266),s=n(33625);const c={id:"@jupyter-notebook/terminal-extension:opener",description:"A plugin to open terminals in a new tab.",requires:[a.IRouter,i.ITerminalTracker],autoStart:!0,activate:(e,t,n)=>{const{commands:a}=e,o=new RegExp("/terminals/(.*)"),i="router:terminal";a.addCommand(i,{execute:e=>{const t=e.path.match(o);if(!t)return;const[,i]=t;i&&(n.widgetAdded.connect(((e,t)=>{t.content.setOption("closeOnExit",!1)})),a.execute("terminal:open",{name:i}))}}),t.register({command:i,pattern:o})}},d={id:"@jupyter-notebook/terminal-extension:redirect",description:"Open terminals in a new tab.",requires:[i.ITerminalTracker],optional:[r.INotebookPathOpener],autoStart:!0,activate:(e,t,n)=>{const a=o.PageConfig.getBaseUrl(),i=null!=n?n:r.defaultNotebookPathOpener;t.widgetAdded.connect(((t,n)=>{if((0,s.find)(e.shell.widgets("main"),(e=>e.id===n.id)))return;const r=n.content.session.name;i.open({prefix:o.URLExt.join(a,"terminals"),path:r,target:"_blank"}),n.dispose()}))}},l=[c,d]}}]);
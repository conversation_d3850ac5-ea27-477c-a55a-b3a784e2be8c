"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[5325,377],{35325:(t,e,n)=>{n.r(e),n.d(e,{HTMLViewer:()=>h,HTMLViewerFactory:()=>m,IHTMLViewerTracker:()=>r,ToolbarItems:()=>o});const r=new(n(20998).Token)("@jupyterlab/htmlviewer:IHTMLViewerTracker","A widget tracker for rendered HTML documents.\n  Use this if you want to be able to iterate over and interact with HTML documents\n  viewed by the application.");var o,a,s=n(38639),i=n(35312),l=n(71677),u=n(68239),c=n(81997),d=n(78156);class h extends i.DocumentWidget{constructor(t){super({...t,content:new u.IFrame({sandbox:["allow-same-origin"]})}),this._renderPending=!1,this._parser=new DOMParser,this._monitor=null,this._objectUrl="",this._trustedChanged=new c.Signal(this),this.translator=t.translator||l.nullTranslator,this.content.addClass("jp-HTMLViewer"),this.context.ready.then((()=>{this.update(),this._monitor=new s.ActivityMonitor({signal:this.context.model.contentChanged,timeout:1e3}),this._monitor.activityStopped.connect(this.update,this)}))}get trusted(){return-1!==this.content.sandbox.indexOf("allow-scripts")}set trusted(t){this.trusted!==t&&(this.content.sandbox=t?a.trusted:a.untrusted,this.update(),this._trustedChanged.emit(t))}get trustedChanged(){return this._trustedChanged}dispose(){if(this._objectUrl)try{URL.revokeObjectURL(this._objectUrl)}catch(t){}super.dispose()}onUpdateRequest(){this._renderPending||(this._renderPending=!0,this._renderModel().then((()=>this._renderPending=!1)))}async _renderModel(){let t=this.context.model.toString();t=await this._setupDocument(t);const e=new Blob([t],{type:"text/html"}),n=this._objectUrl;if(this._objectUrl=URL.createObjectURL(e),this.content.url=this._objectUrl,n)try{URL.revokeObjectURL(n)}catch(t){}}async _setupDocument(t){const e=this._parser.parseFromString(t,"text/html");let n=e.querySelector("base");n||(n=e.createElement("base"),e.head.insertBefore(n,e.head.firstChild));const r=this.context.path,o=await this.context.urlResolver.getDownloadUrl(r);if(n.href=o,n.target="_self",!this.trusted){const t=this.translator.load("jupyterlab").__("Action disabled as the file is not trusted.");e.body.insertAdjacentHTML("beforeend",`<style>\na[target="_blank"],\narea[target="_blank"],\nform[target="_blank"],\nbutton[formtarget="_blank"],\ninput[formtarget="_blank"][type="image"],\ninput[formtarget="_blank"][type="submit"] {\n  cursor: not-allowed !important;\n}\na[target="_blank"]:hover::after,\narea[target="_blank"]:hover::after,\nform[target="_blank"]:hover::after,\nbutton[formtarget="_blank"]:hover::after,\ninput[formtarget="_blank"][type="image"]:hover::after,\ninput[formtarget="_blank"][type="submit"]:hover::after {\n  content: "${{warning:t}.warning}";\n  box-sizing: border-box;\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  z-index: 1000;\n  border: 2px solid #e65100;\n  background-color: #ffb74d;\n  color: black;\n  font-family: system-ui, -apple-system, blinkmacsystemfont, 'Segoe UI', helvetica, arial, sans-serif;\n  text-align: center;\n}\n</style>`)}return e.documentElement.innerHTML}}class m extends i.ABCWidgetFactory{createNewWidget(t){return new h({context:t})}defaultToolbarFactory(t){return[{name:"refresh",widget:o.createRefreshButton(t,this.translator)},{name:"trust",widget:o.createTrustButton(t,this.translator)}]}}!function(t){t.createRefreshButton=function(t,e){const n=(null!=e?e:l.nullTranslator).load("jupyterlab");return new u.ToolbarButton({icon:u.refreshIcon,onClick:async()=>{t.context.model.dirty||(await t.context.revert(),t.update())},tooltip:n.__("Rerender HTML Document")})},t.createTrustButton=function(t,e){return u.ReactWidget.create(d.createElement(a.TrustButtonComponent,{htmlDocument:t,translator:e}))}}(o||(o={})),function(t){t.untrusted=[],t.trusted=["allow-scripts","allow-popups"],t.TrustButtonComponent=function(t){const e=(t.translator||l.nullTranslator).load("jupyterlab");return d.createElement(u.UseSignal,{signal:t.htmlDocument.trustedChanged,initialSender:t.htmlDocument},(()=>d.createElement(u.ToolbarButtonComponent,{className:"",onClick:()=>t.htmlDocument.trusted=!t.htmlDocument.trusted,tooltip:e.__("Whether the HTML file is trusted.\nTrusting the file allows opening pop-ups and running scripts\nwhich may result in security risks.\nOnly enable for files you trust."),label:t.htmlDocument.trusted?e.__("Distrust HTML"):e.__("Trust HTML")})))}}(a||(a={}))}}]);
/*! For license information please see 7796.bundle.js.LICENSE.txt */
(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[7796],{66471:e=>{"use strict";e.exports=function e(r,t){if(r===t)return!0;if(r&&t&&"object"==typeof r&&"object"==typeof t){if(r.constructor!==t.constructor)return!1;var o,n,i;if(Array.isArray(r)){if((o=r.length)!=t.length)return!1;for(n=o;0!=n--;)if(!e(r[n],t[n]))return!1;return!0}if(r.constructor===RegExp)return r.source===t.source&&r.flags===t.flags;if(r.valueOf!==Object.prototype.valueOf)return r.valueOf()===t.valueOf();if(r.toString!==Object.prototype.toString)return r.toString()===t.toString();if((o=(i=Object.keys(r)).length)!==Object.keys(t).length)return!1;for(n=o;0!=n--;)if(!Object.prototype.hasOwnProperty.call(t,i[n]))return!1;for(n=o;0!=n--;){var a=i[n];if(!e(r[a],t[a]))return!1}return!0}return r!=r&&t!=t}},25127:e=>{"use strict";var r=e.exports=function(e,r,o){"function"==typeof r&&(o=r,r={}),t(r,"function"==typeof(o=r.cb||o)?o:o.pre||function(){},o.post||function(){},e,"",e)};function t(e,o,n,i,a,s,u,c,p,f){if(i&&"object"==typeof i&&!Array.isArray(i)){for(var h in o(i,a,s,u,c,p,f),i){var l=i[h];if(Array.isArray(l)){if(h in r.arrayKeywords)for(var v=0;v<l.length;v++)t(e,o,n,l[v],a+"/"+h+"/"+v,s,a,h,i,v)}else if(h in r.propsKeywords){if(l&&"object"==typeof l)for(var d in l)t(e,o,n,l[d],a+"/"+h+"/"+d.replace(/~/g,"~0").replace(/\//g,"~1"),s,a,h,i,d)}else(h in r.keywords||e.allKeys&&!(h in r.skipKeywords))&&t(e,o,n,l,a+"/"+h,s,a,h,i)}n(i,a,s,u,c,p,f)}}r.keywords={additionalItems:!0,items:!0,contains:!0,additionalProperties:!0,propertyNames:!0,not:!0,if:!0,then:!0,else:!0},r.arrayKeywords={items:!0,allOf:!0,anyOf:!0,oneOf:!0},r.propsKeywords={$defs:!0,definitions:!0,properties:!0,patternProperties:!0,dependencies:!0},r.skipKeywords={default:!0,enum:!0,const:!0,required:!0,maximum:!0,minimum:!0,exclusiveMaximum:!0,exclusiveMinimum:!0,multipleOf:!0,maxLength:!0,minLength:!0,pattern:!0,format:!0,maxItems:!0,minItems:!0,uniqueItems:!0,maxProperties:!0,minProperties:!0}},22371:function(e,r){!function(e){"use strict";function r(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];if(r.length>1){r[0]=r[0].slice(0,-1);for(var o=r.length-1,n=1;n<o;++n)r[n]=r[n].slice(1,-1);return r[o]=r[o].slice(1),r.join("")}return r[0]}function t(e){return"(?:"+e+")"}function o(e){return void 0===e?"undefined":null===e?"null":Object.prototype.toString.call(e).split(" ").pop().split("]").shift().toLowerCase()}function n(e){return e.toUpperCase()}function i(e){var o="[A-Za-z]",n="[0-9]",i=r(n,"[A-Fa-f]"),a=t(t("%[EFef]"+i+"%"+i+i+"%"+i+i)+"|"+t("%[89A-Fa-f]"+i+"%"+i+i)+"|"+t("%"+i+i)),s="[\\!\\$\\&\\'\\(\\)\\*\\+\\,\\;\\=]",u=r("[\\:\\/\\?\\#\\[\\]\\@]",s),c=e?"[\\uE000-\\uF8FF]":"[]",p=r(o,n,"[\\-\\.\\_\\~]",e?"[\\xA0-\\u200D\\u2010-\\u2029\\u202F-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]":"[]"),f=t(o+r(o,n,"[\\+\\-\\.]")+"*"),h=t(t(a+"|"+r(p,s,"[\\:]"))+"*"),l=(t(t("25[0-5]")+"|"+t("2[0-4]"+n)+"|"+t("1"+n+n)+"|"+t("[1-9]"+n)+"|"+n),t(t("25[0-5]")+"|"+t("2[0-4]"+n)+"|"+t("1"+n+n)+"|"+t("0?[1-9]"+n)+"|0?0?"+n)),v=t(l+"\\."+l+"\\."+l+"\\."+l),d=t(i+"{1,4}"),m=t(t(d+"\\:"+d)+"|"+v),g=t(t(d+"\\:")+"{6}"+m),y=t("\\:\\:"+t(d+"\\:")+"{5}"+m),E=t(t(d)+"?\\:\\:"+t(d+"\\:")+"{4}"+m),C=t(t(t(d+"\\:")+"{0,1}"+d)+"?\\:\\:"+t(d+"\\:")+"{3}"+m),S=t(t(t(d+"\\:")+"{0,2}"+d)+"?\\:\\:"+t(d+"\\:")+"{2}"+m),w=t(t(t(d+"\\:")+"{0,3}"+d)+"?\\:\\:"+d+"\\:"+m),O=t(t(t(d+"\\:")+"{0,4}"+d)+"?\\:\\:"+m),A=t(t(t(d+"\\:")+"{0,5}"+d)+"?\\:\\:"+d),b=t(t(t(d+"\\:")+"{0,6}"+d)+"?\\:\\:"),D=t([g,y,E,C,S,w,O,A,b].join("|")),x=t(t(p+"|"+a)+"+"),I=(t(D+"\\%25"+x),t(D+t("\\%25|\\%(?!"+i+"{2})")+x)),N=t("[vV]"+i+"+\\."+r(p,s,"[\\:]")+"+"),T=t("\\["+t(I+"|"+D+"|"+N)+"\\]"),R=t(t(a+"|"+r(p,s))+"*"),P=t(T+"|"+v+"(?!"+R+")|"+R),_=t(n+"*"),F=t(t(h+"@")+"?"+P+t("\\:"+_)+"?"),j=t(a+"|"+r(p,s,"[\\:\\@]")),U=t(j+"*"),q=t(j+"+"),H=t(t(a+"|"+r(p,s,"[\\@]"))+"+"),L=t(t("\\/"+U)+"*"),z=t("\\/"+t(q+L)+"?"),k=t(H+L),$=t(q+L),M="(?!"+j+")",V=(t(L+"|"+z+"|"+k+"|"+$+"|"+M),t(t(j+"|"+r("[\\/\\?]",c))+"*")),K=t(t(j+"|[\\/\\?]")+"*"),Y=t(t("\\/\\/"+F+L)+"|"+z+"|"+$+"|"+M),Z=t(f+"\\:"+Y+t("\\?"+V)+"?"+t("\\#"+K)+"?"),B=t(t("\\/\\/"+F+L)+"|"+z+"|"+k+"|"+M),G=t(B+t("\\?"+V)+"?"+t("\\#"+K)+"?");return t(Z+"|"+G),t(f+"\\:"+Y+t("\\?"+V)+"?"),t(t("\\/\\/("+t("("+h+")@")+"?("+P+")"+t("\\:("+_+")")+"?)")+"?("+L+"|"+z+"|"+$+"|"+M+")"),t("\\?("+V+")"),t("\\#("+K+")"),t(t("\\/\\/("+t("("+h+")@")+"?("+P+")"+t("\\:("+_+")")+"?)")+"?("+L+"|"+z+"|"+k+"|"+M+")"),t("\\?("+V+")"),t("\\#("+K+")"),t(t("\\/\\/("+t("("+h+")@")+"?("+P+")"+t("\\:("+_+")")+"?)")+"?("+L+"|"+z+"|"+$+"|"+M+")"),t("\\?("+V+")"),t("\\#("+K+")"),t("("+h+")@"),t("\\:("+_+")"),{NOT_SCHEME:new RegExp(r("[^]",o,n,"[\\+\\-\\.]"),"g"),NOT_USERINFO:new RegExp(r("[^\\%\\:]",p,s),"g"),NOT_HOST:new RegExp(r("[^\\%\\[\\]\\:]",p,s),"g"),NOT_PATH:new RegExp(r("[^\\%\\/\\:\\@]",p,s),"g"),NOT_PATH_NOSCHEME:new RegExp(r("[^\\%\\/\\@]",p,s),"g"),NOT_QUERY:new RegExp(r("[^\\%]",p,s,"[\\:\\@\\/\\?]",c),"g"),NOT_FRAGMENT:new RegExp(r("[^\\%]",p,s,"[\\:\\@\\/\\?]"),"g"),ESCAPE:new RegExp(r("[^]",p,s),"g"),UNRESERVED:new RegExp(p,"g"),OTHER_CHARS:new RegExp(r("[^\\%]",p,u),"g"),PCT_ENCODED:new RegExp(a,"g"),IPV4ADDRESS:new RegExp("^("+v+")$"),IPV6ADDRESS:new RegExp("^\\[?("+D+")"+t(t("\\%25|\\%(?!"+i+"{2})")+"("+x+")")+"?\\]?$")}}var a=i(!1),s=i(!0),u=function(e,r){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return function(e,r){var t=[],o=!0,n=!1,i=void 0;try{for(var a,s=e[Symbol.iterator]();!(o=(a=s.next()).done)&&(t.push(a.value),!r||t.length!==r);o=!0);}catch(e){n=!0,i=e}finally{try{!o&&s.return&&s.return()}finally{if(n)throw i}}return t}(e,r);throw new TypeError("Invalid attempt to destructure non-iterable instance")},c=2147483647,p=36,f=/^xn--/,h=/[^\0-\x7E]/,l=/[\x2E\u3002\uFF0E\uFF61]/g,v={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},d=Math.floor,m=String.fromCharCode;function g(e){throw new RangeError(v[e])}function y(e,r){var t=e.split("@"),o="";return t.length>1&&(o=t[0]+"@",e=t[1]),o+function(e,r){for(var t=[],o=e.length;o--;)t[o]=r(e[o]);return t}((e=e.replace(l,".")).split("."),r).join(".")}function E(e){for(var r=[],t=0,o=e.length;t<o;){var n=e.charCodeAt(t++);if(n>=55296&&n<=56319&&t<o){var i=e.charCodeAt(t++);56320==(64512&i)?r.push(((1023&n)<<10)+(1023&i)+65536):(r.push(n),t--)}else r.push(n)}return r}var C=function(e,r){return e+22+75*(e<26)-((0!=r)<<5)},S=function(e,r,t){var o=0;for(e=t?d(e/700):e>>1,e+=d(e/r);e>455;o+=p)e=d(e/35);return d(o+36*e/(e+38))},w=function(e){var r,t=[],o=e.length,n=0,i=128,a=72,s=e.lastIndexOf("-");s<0&&(s=0);for(var u=0;u<s;++u)e.charCodeAt(u)>=128&&g("not-basic"),t.push(e.charCodeAt(u));for(var f=s>0?s+1:0;f<o;){for(var h=n,l=1,v=p;;v+=p){f>=o&&g("invalid-input");var m=(r=e.charCodeAt(f++))-48<10?r-22:r-65<26?r-65:r-97<26?r-97:p;(m>=p||m>d((c-n)/l))&&g("overflow"),n+=m*l;var y=v<=a?1:v>=a+26?26:v-a;if(m<y)break;var E=p-y;l>d(c/E)&&g("overflow"),l*=E}var C=t.length+1;a=S(n-h,C,0==h),d(n/C)>c-i&&g("overflow"),i+=d(n/C),n%=C,t.splice(n++,0,i)}return String.fromCodePoint.apply(String,t)},O=function(e){var r=[],t=(e=E(e)).length,o=128,n=0,i=72,a=!0,s=!1,u=void 0;try{for(var f,h=e[Symbol.iterator]();!(a=(f=h.next()).done);a=!0){var l=f.value;l<128&&r.push(m(l))}}catch(e){s=!0,u=e}finally{try{!a&&h.return&&h.return()}finally{if(s)throw u}}var v=r.length,y=v;for(v&&r.push("-");y<t;){var w=c,O=!0,A=!1,b=void 0;try{for(var D,x=e[Symbol.iterator]();!(O=(D=x.next()).done);O=!0){var I=D.value;I>=o&&I<w&&(w=I)}}catch(e){A=!0,b=e}finally{try{!O&&x.return&&x.return()}finally{if(A)throw b}}var N=y+1;w-o>d((c-n)/N)&&g("overflow"),n+=(w-o)*N,o=w;var T=!0,R=!1,P=void 0;try{for(var _,F=e[Symbol.iterator]();!(T=(_=F.next()).done);T=!0){var j=_.value;if(j<o&&++n>c&&g("overflow"),j==o){for(var U=n,q=p;;q+=p){var H=q<=i?1:q>=i+26?26:q-i;if(U<H)break;var L=U-H,z=p-H;r.push(m(C(H+L%z,0))),U=d(L/z)}r.push(m(C(U,0))),i=S(n,N,y==v),n=0,++y}}}catch(e){R=!0,P=e}finally{try{!T&&F.return&&F.return()}finally{if(R)throw P}}++n,++o}return r.join("")},A={version:"2.1.0",ucs2:{decode:E,encode:function(e){return String.fromCodePoint.apply(String,function(e){if(Array.isArray(e)){for(var r=0,t=Array(e.length);r<e.length;r++)t[r]=e[r];return t}return Array.from(e)}(e))}},decode:w,encode:O,toASCII:function(e){return y(e,(function(e){return h.test(e)?"xn--"+O(e):e}))},toUnicode:function(e){return y(e,(function(e){return f.test(e)?w(e.slice(4).toLowerCase()):e}))}},b={};function D(e){var r=e.charCodeAt(0);return r<16?"%0"+r.toString(16).toUpperCase():r<128?"%"+r.toString(16).toUpperCase():r<2048?"%"+(r>>6|192).toString(16).toUpperCase()+"%"+(63&r|128).toString(16).toUpperCase():"%"+(r>>12|224).toString(16).toUpperCase()+"%"+(r>>6&63|128).toString(16).toUpperCase()+"%"+(63&r|128).toString(16).toUpperCase()}function x(e){for(var r="",t=0,o=e.length;t<o;){var n=parseInt(e.substr(t+1,2),16);if(n<128)r+=String.fromCharCode(n),t+=3;else if(n>=194&&n<224){if(o-t>=6){var i=parseInt(e.substr(t+4,2),16);r+=String.fromCharCode((31&n)<<6|63&i)}else r+=e.substr(t,6);t+=6}else if(n>=224){if(o-t>=9){var a=parseInt(e.substr(t+4,2),16),s=parseInt(e.substr(t+7,2),16);r+=String.fromCharCode((15&n)<<12|(63&a)<<6|63&s)}else r+=e.substr(t,9);t+=9}else r+=e.substr(t,3),t+=3}return r}function I(e,r){function t(e){var t=x(e);return t.match(r.UNRESERVED)?t:e}return e.scheme&&(e.scheme=String(e.scheme).replace(r.PCT_ENCODED,t).toLowerCase().replace(r.NOT_SCHEME,"")),void 0!==e.userinfo&&(e.userinfo=String(e.userinfo).replace(r.PCT_ENCODED,t).replace(r.NOT_USERINFO,D).replace(r.PCT_ENCODED,n)),void 0!==e.host&&(e.host=String(e.host).replace(r.PCT_ENCODED,t).toLowerCase().replace(r.NOT_HOST,D).replace(r.PCT_ENCODED,n)),void 0!==e.path&&(e.path=String(e.path).replace(r.PCT_ENCODED,t).replace(e.scheme?r.NOT_PATH:r.NOT_PATH_NOSCHEME,D).replace(r.PCT_ENCODED,n)),void 0!==e.query&&(e.query=String(e.query).replace(r.PCT_ENCODED,t).replace(r.NOT_QUERY,D).replace(r.PCT_ENCODED,n)),void 0!==e.fragment&&(e.fragment=String(e.fragment).replace(r.PCT_ENCODED,t).replace(r.NOT_FRAGMENT,D).replace(r.PCT_ENCODED,n)),e}function N(e){return e.replace(/^0*(.*)/,"$1")||"0"}function T(e,r){var t=e.match(r.IPV4ADDRESS)||[],o=u(t,2)[1];return o?o.split(".").map(N).join("."):e}function R(e,r){var t=e.match(r.IPV6ADDRESS)||[],o=u(t,3),n=o[1],i=o[2];if(n){for(var a=n.toLowerCase().split("::").reverse(),s=u(a,2),c=s[0],p=s[1],f=p?p.split(":").map(N):[],h=c.split(":").map(N),l=r.IPV4ADDRESS.test(h[h.length-1]),v=l?7:8,d=h.length-v,m=Array(v),g=0;g<v;++g)m[g]=f[g]||h[d+g]||"";l&&(m[v-1]=T(m[v-1],r));var y=m.reduce((function(e,r,t){if(!r||"0"===r){var o=e[e.length-1];o&&o.index+o.length===t?o.length++:e.push({index:t,length:1})}return e}),[]).sort((function(e,r){return r.length-e.length}))[0],E=void 0;if(y&&y.length>1){var C=m.slice(0,y.index),S=m.slice(y.index+y.length);E=C.join(":")+"::"+S.join(":")}else E=m.join(":");return i&&(E+="%"+i),E}return e}var P=/^(?:([^:\/?#]+):)?(?:\/\/((?:([^\/?#@]*)@)?(\[[^\/?#\]]+\]|[^\/?#:]*)(?:\:(\d*))?))?([^?#]*)(?:\?([^#]*))?(?:#((?:.|\n|\r)*))?/i,_=void 0==="".match(/(){0}/)[1];function F(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t={},o=!1!==r.iri?s:a;"suffix"===r.reference&&(e=(r.scheme?r.scheme+":":"")+"//"+e);var n=e.match(P);if(n){_?(t.scheme=n[1],t.userinfo=n[3],t.host=n[4],t.port=parseInt(n[5],10),t.path=n[6]||"",t.query=n[7],t.fragment=n[8],isNaN(t.port)&&(t.port=n[5])):(t.scheme=n[1]||void 0,t.userinfo=-1!==e.indexOf("@")?n[3]:void 0,t.host=-1!==e.indexOf("//")?n[4]:void 0,t.port=parseInt(n[5],10),t.path=n[6]||"",t.query=-1!==e.indexOf("?")?n[7]:void 0,t.fragment=-1!==e.indexOf("#")?n[8]:void 0,isNaN(t.port)&&(t.port=e.match(/\/\/(?:.|\n)*\:(?:\/|\?|\#|$)/)?n[4]:void 0)),t.host&&(t.host=R(T(t.host,o),o)),void 0!==t.scheme||void 0!==t.userinfo||void 0!==t.host||void 0!==t.port||t.path||void 0!==t.query?void 0===t.scheme?t.reference="relative":void 0===t.fragment?t.reference="absolute":t.reference="uri":t.reference="same-document",r.reference&&"suffix"!==r.reference&&r.reference!==t.reference&&(t.error=t.error||"URI is not a "+r.reference+" reference.");var i=b[(r.scheme||t.scheme||"").toLowerCase()];if(r.unicodeSupport||i&&i.unicodeSupport)I(t,o);else{if(t.host&&(r.domainHost||i&&i.domainHost))try{t.host=A.toASCII(t.host.replace(o.PCT_ENCODED,x).toLowerCase())}catch(e){t.error=t.error||"Host's domain name can not be converted to ASCII via punycode: "+e}I(t,a)}i&&i.parse&&i.parse(t,r)}else t.error=t.error||"URI can not be parsed.";return t}var j=/^\.\.?\//,U=/^\/\.(\/|$)/,q=/^\/\.\.(\/|$)/,H=/^\/?(?:.|\n)*?(?=\/|$)/;function L(e){for(var r=[];e.length;)if(e.match(j))e=e.replace(j,"");else if(e.match(U))e=e.replace(U,"/");else if(e.match(q))e=e.replace(q,"/"),r.pop();else if("."===e||".."===e)e="";else{var t=e.match(H);if(!t)throw new Error("Unexpected dot segment condition");var o=t[0];e=e.slice(o.length),r.push(o)}return r.join("")}function z(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=r.iri?s:a,o=[],n=b[(r.scheme||e.scheme||"").toLowerCase()];if(n&&n.serialize&&n.serialize(e,r),e.host)if(t.IPV6ADDRESS.test(e.host));else if(r.domainHost||n&&n.domainHost)try{e.host=r.iri?A.toUnicode(e.host):A.toASCII(e.host.replace(t.PCT_ENCODED,x).toLowerCase())}catch(t){e.error=e.error||"Host's domain name can not be converted to "+(r.iri?"Unicode":"ASCII")+" via punycode: "+t}I(e,t),"suffix"!==r.reference&&e.scheme&&(o.push(e.scheme),o.push(":"));var i=function(e,r){var t=!1!==r.iri?s:a,o=[];return void 0!==e.userinfo&&(o.push(e.userinfo),o.push("@")),void 0!==e.host&&o.push(R(T(String(e.host),t),t).replace(t.IPV6ADDRESS,(function(e,r,t){return"["+r+(t?"%25"+t:"")+"]"}))),"number"!=typeof e.port&&"string"!=typeof e.port||(o.push(":"),o.push(String(e.port))),o.length?o.join(""):void 0}(e,r);if(void 0!==i&&("suffix"!==r.reference&&o.push("//"),o.push(i),e.path&&"/"!==e.path.charAt(0)&&o.push("/")),void 0!==e.path){var u=e.path;r.absolutePath||n&&n.absolutePath||(u=L(u)),void 0===i&&(u=u.replace(/^\/\//,"/%2F")),o.push(u)}return void 0!==e.query&&(o.push("?"),o.push(e.query)),void 0!==e.fragment&&(o.push("#"),o.push(e.fragment)),o.join("")}function k(e,r){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o={};return arguments[3]||(e=F(z(e,t),t),r=F(z(r,t),t)),!(t=t||{}).tolerant&&r.scheme?(o.scheme=r.scheme,o.userinfo=r.userinfo,o.host=r.host,o.port=r.port,o.path=L(r.path||""),o.query=r.query):(void 0!==r.userinfo||void 0!==r.host||void 0!==r.port?(o.userinfo=r.userinfo,o.host=r.host,o.port=r.port,o.path=L(r.path||""),o.query=r.query):(r.path?("/"===r.path.charAt(0)?o.path=L(r.path):(void 0===e.userinfo&&void 0===e.host&&void 0===e.port||e.path?e.path?o.path=e.path.slice(0,e.path.lastIndexOf("/")+1)+r.path:o.path=r.path:o.path="/"+r.path,o.path=L(o.path)),o.query=r.query):(o.path=e.path,void 0!==r.query?o.query=r.query:o.query=e.query),o.userinfo=e.userinfo,o.host=e.host,o.port=e.port),o.scheme=e.scheme),o.fragment=r.fragment,o}function $(e,r){return e&&e.toString().replace(r&&r.iri?s.PCT_ENCODED:a.PCT_ENCODED,x)}var M={scheme:"http",domainHost:!0,parse:function(e,r){return e.host||(e.error=e.error||"HTTP URIs must have a host."),e},serialize:function(e,r){var t="https"===String(e.scheme).toLowerCase();return e.port!==(t?443:80)&&""!==e.port||(e.port=void 0),e.path||(e.path="/"),e}},V={scheme:"https",domainHost:M.domainHost,parse:M.parse,serialize:M.serialize};function K(e){return"boolean"==typeof e.secure?e.secure:"wss"===String(e.scheme).toLowerCase()}var Y={scheme:"ws",domainHost:!0,parse:function(e,r){var t=e;return t.secure=K(t),t.resourceName=(t.path||"/")+(t.query?"?"+t.query:""),t.path=void 0,t.query=void 0,t},serialize:function(e,r){if(e.port!==(K(e)?443:80)&&""!==e.port||(e.port=void 0),"boolean"==typeof e.secure&&(e.scheme=e.secure?"wss":"ws",e.secure=void 0),e.resourceName){var t=e.resourceName.split("?"),o=u(t,2),n=o[0],i=o[1];e.path=n&&"/"!==n?n:void 0,e.query=i,e.resourceName=void 0}return e.fragment=void 0,e}},Z={scheme:"wss",domainHost:Y.domainHost,parse:Y.parse,serialize:Y.serialize},B={},G="[A-Za-z0-9\\-\\.\\_\\~\\xA0-\\u200D\\u2010-\\u2029\\u202F-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]",J="[0-9A-Fa-f]",Q=t(t("%[EFef]"+J+"%"+J+J+"%"+J+J)+"|"+t("%[89A-Fa-f]"+J+"%"+J+J)+"|"+t("%"+J+J)),W=r("[\\!\\$\\%\\'\\(\\)\\*\\+\\,\\-\\.0-9\\<\\>A-Z\\x5E-\\x7E]",'[\\"\\\\]'),X=new RegExp(G,"g"),ee=new RegExp(Q,"g"),re=new RegExp(r("[^]","[A-Za-z0-9\\!\\$\\%\\'\\*\\+\\-\\^\\_\\`\\{\\|\\}\\~]","[\\.]",'[\\"]',W),"g"),te=new RegExp(r("[^]",G,"[\\!\\$\\'\\(\\)\\*\\+\\,\\;\\:\\@]"),"g"),oe=te;function ne(e){var r=x(e);return r.match(X)?r:e}var ie={scheme:"mailto",parse:function(e,r){var t=e,o=t.to=t.path?t.path.split(","):[];if(t.path=void 0,t.query){for(var n=!1,i={},a=t.query.split("&"),s=0,u=a.length;s<u;++s){var c=a[s].split("=");switch(c[0]){case"to":for(var p=c[1].split(","),f=0,h=p.length;f<h;++f)o.push(p[f]);break;case"subject":t.subject=$(c[1],r);break;case"body":t.body=$(c[1],r);break;default:n=!0,i[$(c[0],r)]=$(c[1],r)}}n&&(t.headers=i)}t.query=void 0;for(var l=0,v=o.length;l<v;++l){var d=o[l].split("@");if(d[0]=$(d[0]),r.unicodeSupport)d[1]=$(d[1],r).toLowerCase();else try{d[1]=A.toASCII($(d[1],r).toLowerCase())}catch(e){t.error=t.error||"Email address's domain name can not be converted to ASCII via punycode: "+e}o[l]=d.join("@")}return t},serialize:function(e,r){var t,o=e,i=null!=(t=e.to)?t instanceof Array?t:"number"!=typeof t.length||t.split||t.setInterval||t.call?[t]:Array.prototype.slice.call(t):[];if(i){for(var a=0,s=i.length;a<s;++a){var u=String(i[a]),c=u.lastIndexOf("@"),p=u.slice(0,c).replace(ee,ne).replace(ee,n).replace(re,D),f=u.slice(c+1);try{f=r.iri?A.toUnicode(f):A.toASCII($(f,r).toLowerCase())}catch(e){o.error=o.error||"Email address's domain name can not be converted to "+(r.iri?"Unicode":"ASCII")+" via punycode: "+e}i[a]=p+"@"+f}o.path=i.join(",")}var h=e.headers=e.headers||{};e.subject&&(h.subject=e.subject),e.body&&(h.body=e.body);var l=[];for(var v in h)h[v]!==B[v]&&l.push(v.replace(ee,ne).replace(ee,n).replace(te,D)+"="+h[v].replace(ee,ne).replace(ee,n).replace(oe,D));return l.length&&(o.query=l.join("&")),o}},ae=/^([^\:]+)\:(.*)/,se={scheme:"urn",parse:function(e,r){var t=e.path&&e.path.match(ae),o=e;if(t){var n=r.scheme||o.scheme||"urn",i=t[1].toLowerCase(),a=t[2],s=n+":"+(r.nid||i),u=b[s];o.nid=i,o.nss=a,o.path=void 0,u&&(o=u.parse(o,r))}else o.error=o.error||"URN can not be parsed.";return o},serialize:function(e,r){var t=r.scheme||e.scheme||"urn",o=e.nid,n=t+":"+(r.nid||o),i=b[n];i&&(e=i.serialize(e,r));var a=e,s=e.nss;return a.path=(o||r.nid)+":"+s,a}},ue=/^[0-9A-Fa-f]{8}(?:\-[0-9A-Fa-f]{4}){3}\-[0-9A-Fa-f]{12}$/,ce={scheme:"urn:uuid",parse:function(e,r){var t=e;return t.uuid=t.nss,t.nss=void 0,r.tolerant||t.uuid&&t.uuid.match(ue)||(t.error=t.error||"UUID is not valid."),t},serialize:function(e,r){var t=e;return t.nss=(e.uuid||"").toLowerCase(),t}};b[M.scheme]=M,b[V.scheme]=V,b[Y.scheme]=Y,b[Z.scheme]=Z,b[ie.scheme]=ie,b[se.scheme]=se,b[ce.scheme]=ce,e.SCHEMES=b,e.pctEncChar=D,e.pctDecChars=x,e.parse=F,e.removeDotSegments=L,e.serialize=z,e.resolveComponents=k,e.resolve=function(e,r,t){var o=function(e,r){var t=e;if(r)for(var o in r)t[o]=r[o];return t}({scheme:"null"},t);return z(k(F(e,o),F(r,o),o,!0),o)},e.normalize=function(e,r){return"string"==typeof e?e=z(F(e,r),r):"object"===o(e)&&(e=F(z(e,r),r)),e},e.equal=function(e,r,t){return"string"==typeof e?e=z(F(e,t),t):"object"===o(e)&&(e=z(e,t)),"string"==typeof r?r=z(F(r,t),t):"object"===o(r)&&(r=z(r,t)),e===r},e.escapeComponent=function(e,r){return e&&e.toString().replace(r&&r.iri?s.ESCAPE:a.ESCAPE,D)},e.unescapeComponent=$,Object.defineProperty(e,"__esModule",{value:!0})}(r)}}]);
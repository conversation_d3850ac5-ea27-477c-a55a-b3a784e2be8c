"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[2955],{22955:(t,e,i)=>{i.r(e),i.d(e,{BidiSpan:()=>ie,BlockInfo:()=>ri,BlockType:()=>at,Decoration:()=>ct,Direction:()=>jt,EditorView:()=>Xi,GutterMarker:()=>To,MatchDecorator:()=>Es,RectangleMarker:()=>as,ViewPlugin:()=>Ht,ViewUpdate:()=>Gt,WidgetType:()=>ht,__test:()=>on,closeHoverTooltips:()=>bo,crosshairCursor:()=>Qs,drawSelection:()=>ws,dropCursor:()=>Ts,getDrawSelectionConfig:()=>vs,getPanel:()=>Mo,getTooltip:()=>mo,gutter:()=>Bo,gutterLineClass:()=>Oo,gutters:()=>Ho,hasHoverTooltips:()=>wo,highlightActiveLine:()=>Ks,highlightActiveLineGutter:()=>$o,highlightSpecialChars:()=>Ns,highlightTrailingWhitespace:()=>sn,highlightWhitespace:()=>tn,hoverTooltip:()=>go,keymap:()=>is,layer:()=>ps,lineNumberMarkers:()=>Io,lineNumbers:()=>Go,logException:()=>Et,panels:()=>xo,placeholder:()=>Gs,rectangularSelection:()=>Us,repositionTooltips:()=>yo,runScopeHandlers:()=>ns,scrollPastEnd:()=>Is,showPanel:()=>Do,showTooltip:()=>ho,tooltips:()=>eo});for(var s=i(17811),o=i(4434),n={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},r={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},l="undefined"!=typeof navigator&&/Mac/.test(navigator.platform),h="undefined"!=typeof navigator&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),a=0;a<10;a++)n[48+a]=n[96+a]=String(a);for(a=1;a<=24;a++)n[a+111]="F"+a;for(a=65;a<=90;a++)n[a]=String.fromCharCode(a+32),r[a]=String.fromCharCode(a);for(var c in n)r.hasOwnProperty(c)||(r[c]=n[c]);function d(t){let e;return e=11==t.nodeType?t.getSelection?t:t.ownerDocument:t,e.getSelection()}function u(t,e){return!!e&&(t==e||t.contains(1!=e.nodeType?e.parentNode:e))}function f(t,e){if(!e.anchorNode)return!1;try{return u(t,e.anchorNode)}catch(t){return!1}}function p(t){return 3==t.nodeType?C(t,0,t.nodeValue.length).getClientRects():1==t.nodeType?t.getClientRects():[]}function g(t,e,i,s){return!!i&&(w(t,e,i,s,-1)||w(t,e,i,s,1))}function m(t){for(var e=0;;e++)if(!(t=t.previousSibling))return e}function w(t,e,i,s,o){for(;;){if(t==i&&e==s)return!0;if(e==(o<0?0:v(t))){if("DIV"==t.nodeName)return!1;let i=t.parentNode;if(!i||1!=i.nodeType)return!1;e=m(t)+(o<0?0:1),t=i}else{if(1!=t.nodeType)return!1;if(1==(t=t.childNodes[e+(o<0?-1:0)]).nodeType&&"false"==t.contentEditable)return!1;e=o<0?v(t):0}}}function v(t){return 3==t.nodeType?t.nodeValue.length:t.childNodes.length}function b(t,e){let i=e?t.left:t.right;return{left:i,right:i,top:t.top,bottom:t.bottom}}function y(t){return{left:0,right:t.innerWidth,top:0,bottom:t.innerHeight}}class S{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}eq(t){return this.anchorNode==t.anchorNode&&this.anchorOffset==t.anchorOffset&&this.focusNode==t.focusNode&&this.focusOffset==t.focusOffset}setRange(t){let{anchorNode:e,focusNode:i}=t;this.set(e,Math.min(t.anchorOffset,e?v(e):0),i,Math.min(t.focusOffset,i?v(i):0))}set(t,e,i,s){this.anchorNode=t,this.anchorOffset=e,this.focusNode=i,this.focusOffset=s}}let x,M=null;function k(t){if(t.setActive)return t.setActive();if(M)return t.focus(M);let e=[];for(let i=t;i&&(e.push(i,i.scrollTop,i.scrollLeft),i!=i.ownerDocument);i=i.parentNode);if(t.focus(null==M?{get preventScroll(){return M={preventScroll:!0},!0}}:void 0),!M){M=!1;for(let t=0;t<e.length;){let i=e[t++],s=e[t++],o=e[t++];i.scrollTop!=s&&(i.scrollTop=s),i.scrollLeft!=o&&(i.scrollLeft=o)}}}function C(t,e,i=e){let s=x||(x=document.createRange());return s.setEnd(t,i),s.setStart(t,e),s}function A(t,e,i){let s={key:e,code:e,keyCode:i,which:i,cancelable:!0},o=new KeyboardEvent("keydown",s);o.synthetic=!0,t.dispatchEvent(o);let n=new KeyboardEvent("keyup",s);return n.synthetic=!0,t.dispatchEvent(n),o.defaultPrevented||n.defaultPrevented}function D(t){for(;t.attributes.length;)t.removeAttributeNode(t.attributes[0])}function T(t){return t.scrollTop>Math.max(1,t.scrollHeight-t.clientHeight-4)}class O{constructor(t,e,i=!0){this.node=t,this.offset=e,this.precise=i}static before(t,e){return new O(t.parentNode,m(t),e)}static after(t,e){return new O(t.parentNode,m(t)+1,e)}}const E=[];class R{constructor(){this.parent=null,this.dom=null,this.flags=2}get overrideDOMText(){return null}get posAtStart(){return this.parent?this.parent.posBefore(this):0}get posAtEnd(){return this.posAtStart+this.length}posBefore(t){let e=this.posAtStart;for(let i of this.children){if(i==t)return e;e+=i.length+i.breakAfter}throw new RangeError("Invalid child in posBefore")}posAfter(t){return this.posBefore(t)+t.length}sync(t,e){if(2&this.flags){let i,s=this.dom,o=null;for(let n of this.children){if(7&n.flags){if(!n.dom&&(i=o?o.nextSibling:s.firstChild)){let t=R.get(i);(!t||!t.parent&&t.canReuseDOM(n))&&n.reuseDOM(i)}n.sync(t,e),n.flags&=-8}if(i=o?o.nextSibling:s.firstChild,e&&!e.written&&e.node==s&&i!=n.dom&&(e.written=!0),n.dom.parentNode==s)for(;i&&i!=n.dom;)i=B(i);else s.insertBefore(n.dom,i);o=n.dom}for(i=o?o.nextSibling:s.firstChild,i&&e&&e.node==s&&(e.written=!0);i;)i=B(i)}else if(1&this.flags)for(let i of this.children)7&i.flags&&(i.sync(t,e),i.flags&=-8)}reuseDOM(t){}localPosFromDOM(t,e){let i;if(t==this.dom)i=this.dom.childNodes[e];else{let s=0==v(t)?0:0==e?-1:1;for(;;){let e=t.parentNode;if(e==this.dom)break;0==s&&e.firstChild!=e.lastChild&&(s=t==e.firstChild?-1:1),t=e}i=s<0?t:t.nextSibling}if(i==this.dom.firstChild)return 0;for(;i&&!R.get(i);)i=i.nextSibling;if(!i)return this.length;for(let t=0,e=0;;t++){let s=this.children[t];if(s.dom==i)return e;e+=s.length+s.breakAfter}}domBoundsAround(t,e,i=0){let s=-1,o=-1,n=-1,r=-1;for(let l=0,h=i,a=i;l<this.children.length;l++){let i=this.children[l],c=h+i.length;if(h<t&&c>e)return i.domBoundsAround(t,e,h);if(c>=t&&-1==s&&(s=l,o=h),h>e&&i.dom.parentNode==this.dom){n=l,r=a;break}a=c,h=c+i.breakAfter}return{from:o,to:r<0?i+this.length:r,startDOM:(s?this.children[s-1].dom.nextSibling:null)||this.dom.firstChild,endDOM:n<this.children.length&&n>=0?this.children[n].dom:null}}markDirty(t=!1){this.flags|=2,this.markParentsDirty(t)}markParentsDirty(t){for(let e=this.parent;e;e=e.parent){if(t&&(e.flags|=2),1&e.flags)return;e.flags|=1,t=!1}}setParent(t){this.parent!=t&&(this.parent=t,7&this.flags&&this.markParentsDirty(!0))}setDOM(t){this.dom!=t&&(this.dom&&(this.dom.cmView=null),this.dom=t,t.cmView=this)}get rootView(){for(let t=this;;){let e=t.parent;if(!e)return t;t=e}}replaceChildren(t,e,i=E){this.markDirty();for(let i=t;i<e;i++){let t=this.children[i];t.parent==this&&t.destroy()}this.children.splice(t,e-t,...i);for(let t=0;t<i.length;t++)i[t].setParent(this)}ignoreMutation(t){return!1}ignoreEvent(t){return!1}childCursor(t=this.length){return new L(this.children,t,this.children.length)}childPos(t,e=1){return this.childCursor().findPos(t,e)}toString(){let t=this.constructor.name.replace("View","");return t+(this.children.length?"("+this.children.join()+")":this.length?"["+("Text"==t?this.text:this.length)+"]":"")+(this.breakAfter?"#":"")}static get(t){return t.cmView}get isEditable(){return!0}get isWidget(){return!1}get isHidden(){return!1}merge(t,e,i,s,o,n){return!1}become(t){return!1}canReuseDOM(t){return t.constructor==this.constructor&&!(8&(this.flags|t.flags))}getSide(){return 0}destroy(){this.parent=null}}function B(t){let e=t.nextSibling;return t.parentNode.removeChild(t),e}R.prototype.breakAfter=0;class L{constructor(t,e,i){this.children=t,this.pos=e,this.i=i,this.off=0}findPos(t,e=1){for(;;){if(t>this.pos||t==this.pos&&(e>0||0==this.i||this.children[this.i-1].breakAfter))return this.off=t-this.pos,this;let i=this.children[--this.i];this.pos-=i.length+i.breakAfter}}}function H(t,e,i,s,o,n,r,l,h){let{children:a}=t,c=a.length?a[e]:null,d=n.length?n[n.length-1]:null,u=d?d.breakAfter:r;if(!(e==s&&c&&!r&&!u&&n.length<2&&c.merge(i,o,n.length?d:null,0==i,l,h))){if(s<a.length){let t=a[s];t&&(o<t.length||t.breakAfter&&(null==d?void 0:d.breakAfter))?(e==s&&(t=t.split(o),o=0),!u&&d&&t.merge(0,o,d,!0,0,h)?n[n.length-1]=t:((o||t.children.length&&!t.children[0].length)&&t.merge(0,o,null,!1,0,h),n.push(t))):(null==t?void 0:t.breakAfter)&&(d?d.breakAfter=1:r=1),s++}for(c&&(c.breakAfter=r,i>0&&(!r&&n.length&&c.merge(i,c.length,n[0],!1,l,0)?c.breakAfter=n.shift().breakAfter:(i<c.length||c.children.length&&0==c.children[c.children.length-1].length)&&c.merge(i,c.length,null,!1,l,0),e++));e<s&&n.length;)if(a[s-1].become(n[n.length-1]))s--,n.pop(),h=n.length?0:l;else{if(!a[e].become(n[0]))break;e++,n.shift(),l=n.length?0:h}!n.length&&e&&s<a.length&&!a[e-1].breakAfter&&a[s].merge(0,0,a[e-1],!1,l,h)&&e--,(e<s||n.length)&&t.replaceChildren(e,s,n)}}function P(t,e,i,s,o,n){let r=t.childCursor(),{i:l,off:h}=r.findPos(i,1),{i:a,off:c}=r.findPos(e,-1),d=e-i;for(let t of s)d+=t.length;t.length+=d,H(t,a,c,l,h,s,0,o,n)}let N="undefined"!=typeof navigator?navigator:{userAgent:"",vendor:"",platform:""},V="undefined"!=typeof document?document:{documentElement:{style:{}}};const F=/Edge\/(\d+)/.exec(N.userAgent),W=/MSIE \d/.test(N.userAgent),z=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(N.userAgent),I=!!(W||z||F),K=!I&&/gecko\/(\d+)/i.test(N.userAgent),q=!I&&/Chrome\/(\d+)/.exec(N.userAgent),_="webkitFontSmoothing"in V.documentElement.style,Y=!I&&/Apple Computer/.test(N.vendor),G=Y&&(/Mobile\/\w+/.test(N.userAgent)||N.maxTouchPoints>2);var j={mac:G||/Mac/.test(N.platform),windows:/Win/.test(N.platform),linux:/Linux|X11/.test(N.platform),ie:I,ie_version:W?V.documentMode||6:z?+z[1]:F?+F[1]:0,gecko:K,gecko_version:K?+(/Firefox\/(\d+)/.exec(N.userAgent)||[0,0])[1]:0,chrome:!!q,chrome_version:q?+q[1]:0,ios:G,android:/Android\b/.test(N.userAgent),webkit:_,safari:Y,webkit_version:_?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0,tabSize:null!=V.documentElement.style.tabSize?"tab-size":"-moz-tab-size"};class X extends R{constructor(t){super(),this.text=t}get length(){return this.text.length}createDOM(t){this.setDOM(t||document.createTextNode(this.text))}sync(t,e){this.dom||this.createDOM(),this.dom.nodeValue!=this.text&&(e&&e.node==this.dom&&(e.written=!0),this.dom.nodeValue=this.text)}reuseDOM(t){3==t.nodeType&&this.createDOM(t)}merge(t,e,i){return!(8&this.flags||i&&(!(i instanceof X)||this.length-(e-t)+i.length>256||8&i.flags)||(this.text=this.text.slice(0,t)+(i?i.text:"")+this.text.slice(e),this.markDirty(),0))}split(t){let e=new X(this.text.slice(t));return this.text=this.text.slice(0,t),this.markDirty(),e.flags|=8&this.flags,e}localPosFromDOM(t,e){return t==this.dom?e:e?this.text.length:0}domAtPos(t){return new O(this.dom,t)}domBoundsAround(t,e,i){return{from:i,to:i+this.length,startDOM:this.dom,endDOM:this.dom.nextSibling}}coordsAt(t,e){return function(t,e,i){let s=t.nodeValue.length;e>s&&(e=s);let o=e,n=e,r=0;0==e&&i<0||e==s&&i>=0?j.chrome||j.gecko||(e?(o--,r=1):n<s&&(n++,r=-1)):i<0?o--:n<s&&n++;let l=C(t,o,n).getClientRects();if(!l.length)return null;let h=l[(r?r<0:i>=0)?0:l.length-1];return j.safari&&!r&&0==h.width&&(h=Array.prototype.find.call(l,(t=>t.width))||h),r?b(h,r<0):h||null}(this.dom,t,e)}}class U extends R{constructor(t,e=[],i=0){super(),this.mark=t,this.children=e,this.length=i;for(let t of e)t.setParent(this)}setAttrs(t){if(D(t),this.mark.class&&(t.className=this.mark.class),this.mark.attrs)for(let e in this.mark.attrs)t.setAttribute(e,this.mark.attrs[e]);return t}canReuseDOM(t){return super.canReuseDOM(t)&&!(8&(this.flags|t.flags))}reuseDOM(t){t.nodeName==this.mark.tagName.toUpperCase()&&(this.setDOM(t),this.flags|=6)}sync(t,e){this.dom?4&this.flags&&this.setAttrs(this.dom):this.setDOM(this.setAttrs(document.createElement(this.mark.tagName))),super.sync(t,e)}merge(t,e,i,s,o,n){return!(i&&(!(i instanceof U&&i.mark.eq(this.mark))||t&&o<=0||e<this.length&&n<=0)||(P(this,t,e,i?i.children:[],o-1,n-1),this.markDirty(),0))}split(t){let e=[],i=0,s=-1,o=0;for(let n of this.children){let r=i+n.length;r>t&&e.push(i<t?n.split(t-i):n),s<0&&i>=t&&(s=o),i=r,o++}let n=this.length-t;return this.length=t,s>-1&&(this.children.length=s,this.markDirty()),new U(this.mark,e,n)}domAtPos(t){return Q(this,t)}coordsAt(t,e){return tt(this,t,e)}}class $ extends R{static create(t,e,i){return new $(t,e,i)}constructor(t,e,i){super(),this.widget=t,this.length=e,this.side=i,this.prevWidget=null}split(t){let e=$.create(this.widget,this.length-t,this.side);return this.length-=t,e}sync(t){this.dom&&this.widget.updateDOM(this.dom,t)||(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(t)),this.dom.contentEditable="false")}getSide(){return this.side}merge(t,e,i,s,o,n){return!(i&&(!(i instanceof $&&this.widget.compare(i.widget))||t>0&&o<=0||e<this.length&&n<=0)||(this.length=t+(i?i.length:0)+(this.length-e),0))}become(t){return t instanceof $&&t.side==this.side&&this.widget.constructor==t.widget.constructor&&(this.widget.compare(t.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=t.widget,this.length=t.length,!0)}ignoreMutation(){return!0}ignoreEvent(t){return this.widget.ignoreEvent(t)}get overrideDOMText(){if(0==this.length)return s.Text.empty;let t=this;for(;t.parent;)t=t.parent;let{view:e}=t,i=e&&e.state.doc,o=this.posAtStart;return i?i.slice(o,o+this.length):s.Text.empty}domAtPos(t){return(this.length?0==t:this.side>0)?O.before(this.dom):O.after(this.dom,t==this.length)}domBoundsAround(){return null}coordsAt(t,e){let i=this.widget.coordsAt(this.dom,t,e);if(i)return i;let s=this.dom.getClientRects(),o=null;if(!s.length)return null;let n=this.side?this.side<0:t>0;for(let e=n?s.length-1:0;o=s[e],!(t>0?0==e:e==s.length-1||o.top<o.bottom);e+=n?-1:1);return b(o,!n)}get isEditable(){return!1}get isWidget(){return!0}get isHidden(){return this.widget.isHidden}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}}class J extends R{constructor(t){super(),this.side=t}get length(){return 0}merge(){return!1}become(t){return t instanceof J&&t.side==this.side}split(){return new J(this.side)}sync(){if(!this.dom){let t=document.createElement("img");t.className="cm-widgetBuffer",t.setAttribute("aria-hidden","true"),this.setDOM(t)}}getSide(){return this.side}domAtPos(t){return this.side>0?O.before(this.dom):O.after(this.dom)}localPosFromDOM(){return 0}domBoundsAround(){return null}coordsAt(t){return this.dom.getBoundingClientRect()}get overrideDOMText(){return s.Text.empty}get isHidden(){return!0}}function Q(t,e){let i=t.dom,{children:s}=t,o=0;for(let t=0;o<s.length;o++){let n=s[o],r=t+n.length;if(!(r==t&&n.getSide()<=0)){if(e>t&&e<r&&n.dom.parentNode==i)return n.domAtPos(e-t);if(e<=t)break;t=r}}for(let t=o;t>0;t--){let e=s[t-1];if(e.dom.parentNode==i)return e.domAtPos(e.length)}for(let t=o;t<s.length;t++){let e=s[t];if(e.dom.parentNode==i)return e.domAtPos(0)}return new O(i,0)}function Z(t,e,i){let s,{children:o}=t;i>0&&e instanceof U&&o.length&&(s=o[o.length-1])instanceof U&&s.mark.eq(e.mark)?Z(s,e.children[0],i-1):(o.push(e),e.setParent(t)),t.length+=e.length}function tt(t,e,i){let s=null,o=-1,n=null,r=-1;!function t(e,l){for(let h=0,a=0;h<e.children.length&&a<=l;h++){let c=e.children[h],d=a+c.length;d>=l&&(c.children.length?t(c,l-a):(!n||n.isHidden&&i>0)&&(d>l||a==d&&c.getSide()>0)?(n=c,r=l-a):(a<l||a==d&&c.getSide()<0&&!c.isHidden)&&(s=c,o=l-a)),a=d}}(t,e);let l=(i<0?s:n)||s||n;return l?l.coordsAt(Math.max(0,l==s?o:r),i):function(t){let e=t.dom.lastChild;if(!e)return t.dom.getBoundingClientRect();let i=p(e);return i[i.length-1]||null}(t)}function et(t,e){for(let i in t)"class"==i&&e.class?e.class+=" "+t.class:"style"==i&&e.style?e.style+=";"+t.style:e[i]=t[i];return e}X.prototype.children=$.prototype.children=J.prototype.children=E;const it=Object.create(null);function st(t,e,i){if(t==e)return!0;t||(t=it),e||(e=it);let s=Object.keys(t),o=Object.keys(e);if(s.length-(i&&s.indexOf(i)>-1?1:0)!=o.length-(i&&o.indexOf(i)>-1?1:0))return!1;for(let n of s)if(n!=i&&(-1==o.indexOf(n)||t[n]!==e[n]))return!1;return!0}function ot(t,e,i){let s=!1;if(e)for(let o in e)i&&o in i||(s=!0,"style"==o?t.style.cssText="":t.removeAttribute(o));if(i)for(let o in i)e&&e[o]==i[o]||(s=!0,"style"==o?t.style.cssText=i[o]:t.setAttribute(o,i[o]));return s}function nt(t){let e=Object.create(null);for(let i=0;i<t.attributes.length;i++){let s=t.attributes[i];e[s.name]=s.value}return e}class rt extends R{constructor(){super(...arguments),this.children=[],this.length=0,this.prevAttrs=void 0,this.attrs=null,this.breakAfter=0}merge(t,e,i,s,o,n){if(i){if(!(i instanceof rt))return!1;this.dom||i.transferDOM(this)}return s&&this.setDeco(i?i.attrs:null),P(this,t,e,i?i.children:[],o,n),!0}split(t){let e=new rt;if(e.breakAfter=this.breakAfter,0==this.length)return e;let{i,off:s}=this.childPos(t);s&&(e.append(this.children[i].split(s),0),this.children[i].merge(s,this.children[i].length,null,!1,0,0),i++);for(let t=i;t<this.children.length;t++)e.append(this.children[t],0);for(;i>0&&0==this.children[i-1].length;)this.children[--i].destroy();return this.children.length=i,this.markDirty(),this.length=t,e}transferDOM(t){this.dom&&(this.markDirty(),t.setDOM(this.dom),t.prevAttrs=void 0===this.prevAttrs?this.attrs:this.prevAttrs,this.prevAttrs=void 0,this.dom=null)}setDeco(t){st(this.attrs,t)||(this.dom&&(this.prevAttrs=this.attrs,this.markDirty()),this.attrs=t)}append(t,e){Z(this,t,e)}addLineDeco(t){let e=t.spec.attributes,i=t.spec.class;e&&(this.attrs=et(e,this.attrs||{})),i&&(this.attrs=et({class:i},this.attrs||{}))}domAtPos(t){return Q(this,t)}reuseDOM(t){"DIV"==t.nodeName&&(this.setDOM(t),this.flags|=6)}sync(t,e){var i;this.dom?4&this.flags&&(D(this.dom),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0):(this.setDOM(document.createElement("div")),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0),void 0!==this.prevAttrs&&(ot(this.dom,this.prevAttrs,this.attrs),this.dom.classList.add("cm-line"),this.prevAttrs=void 0),super.sync(t,e);let s=this.dom.lastChild;for(;s&&R.get(s)instanceof U;)s=s.lastChild;if(!(s&&this.length&&("BR"==s.nodeName||0!=(null===(i=R.get(s))||void 0===i?void 0:i.isEditable)||j.ios&&this.children.some((t=>t instanceof X))))){let t=document.createElement("BR");t.cmIgnore=!0,this.dom.appendChild(t)}}measureTextSize(){if(0==this.children.length||this.length>20)return null;let t,e=0;for(let i of this.children){if(!(i instanceof X)||/[^ -~]/.test(i.text))return null;let s=p(i.dom);if(1!=s.length)return null;e+=s[0].width,t=s[0].height}return e?{lineHeight:this.dom.getBoundingClientRect().height,charWidth:e/this.length,textHeight:t}:null}coordsAt(t,e){let i=tt(this,t,e);if(!this.children.length&&i&&this.parent){let{heightOracle:t}=this.parent.view.viewState,e=i.bottom-i.top;if(Math.abs(e-t.lineHeight)<2&&t.textHeight<e){let s=(e-t.textHeight)/2;return{top:i.top+s,bottom:i.bottom-s,left:i.left,right:i.left}}}return i}become(t){return!1}covers(){return!0}static find(t,e){for(let i=0,s=0;i<t.children.length;i++){let o=t.children[i],n=s+o.length;if(n>=e){if(o instanceof rt)return o;if(n>e)break}s=n+o.breakAfter}return null}}class lt extends R{constructor(t,e,i){super(),this.widget=t,this.length=e,this.deco=i,this.breakAfter=0,this.prevWidget=null}merge(t,e,i,s,o,n){return!(i&&(!(i instanceof lt&&this.widget.compare(i.widget))||t>0&&o<=0||e<this.length&&n<=0)||(this.length=t+(i?i.length:0)+(this.length-e),0))}domAtPos(t){return 0==t?O.before(this.dom):O.after(this.dom,t==this.length)}split(t){let e=this.length-t;this.length=t;let i=new lt(this.widget,e,this.deco);return i.breakAfter=this.breakAfter,i}get children(){return E}sync(t){this.dom&&this.widget.updateDOM(this.dom,t)||(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(t)),this.dom.contentEditable="false")}get overrideDOMText(){return this.parent?this.parent.view.state.doc.slice(this.posAtStart,this.posAtEnd):s.Text.empty}domBoundsAround(){return null}become(t){return t instanceof lt&&t.widget.constructor==this.widget.constructor&&(t.widget.compare(this.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=t.widget,this.length=t.length,this.deco=t.deco,this.breakAfter=t.breakAfter,!0)}ignoreMutation(){return!0}ignoreEvent(t){return this.widget.ignoreEvent(t)}get isEditable(){return!1}get isWidget(){return!0}coordsAt(t,e){return this.widget.coordsAt(this.dom,t,e)}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}covers(t){let{startSide:e,endSide:i}=this.deco;return e!=i&&(t<0?e<0:i>0)}}class ht{eq(t){return!1}updateDOM(t,e){return!1}compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}get estimatedHeight(){return-1}get lineBreaks(){return 0}ignoreEvent(t){return!0}coordsAt(t,e,i){return null}get isHidden(){return!1}destroy(t){}}var at=function(t){return t[t.Text=0]="Text",t[t.WidgetBefore=1]="WidgetBefore",t[t.WidgetAfter=2]="WidgetAfter",t[t.WidgetRange=3]="WidgetRange",t}(at||(at={}));class ct extends s.RangeValue{constructor(t,e,i,s){super(),this.startSide=t,this.endSide=e,this.widget=i,this.spec=s}get heightRelevant(){return!1}static mark(t){return new dt(t)}static widget(t){let e=Math.max(-1e4,Math.min(1e4,t.side||0)),i=!!t.block;return e+=i&&!t.inlineOrder?e>0?3e8:-4e8:e>0?1e8:-1e8,new ft(t,e,e,i,t.widget||null,!1)}static replace(t){let e,i,s=!!t.block;if(t.isBlockGap)e=-5e8,i=4e8;else{let{start:o,end:n}=pt(t,s);e=(o?s?-3e8:-1:5e8)-1,i=1+(n?s?2e8:1:-6e8)}return new ft(t,e,i,s,t.widget||null,!0)}static line(t){return new ut(t)}static set(t,e=!1){return s.RangeSet.of(t,e)}hasHeight(){return!!this.widget&&this.widget.estimatedHeight>-1}}ct.none=s.RangeSet.empty;class dt extends ct{constructor(t){let{start:e,end:i}=pt(t);super(e?-1:5e8,i?1:-6e8,null,t),this.tagName=t.tagName||"span",this.class=t.class||"",this.attrs=t.attributes||null}eq(t){var e,i;return this==t||t instanceof dt&&this.tagName==t.tagName&&(this.class||(null===(e=this.attrs)||void 0===e?void 0:e.class))==(t.class||(null===(i=t.attrs)||void 0===i?void 0:i.class))&&st(this.attrs,t.attrs,"class")}range(t,e=t){if(t>=e)throw new RangeError("Mark decorations may not be empty");return super.range(t,e)}}dt.prototype.point=!1;class ut extends ct{constructor(t){super(-2e8,-2e8,null,t)}eq(t){return t instanceof ut&&this.spec.class==t.spec.class&&st(this.spec.attributes,t.spec.attributes)}range(t,e=t){if(e!=t)throw new RangeError("Line decoration ranges must be zero-length");return super.range(t,e)}}ut.prototype.mapMode=s.MapMode.TrackBefore,ut.prototype.point=!0;class ft extends ct{constructor(t,e,i,o,n,r){super(e,i,n,t),this.block=o,this.isReplace=r,this.mapMode=o?e<=0?s.MapMode.TrackBefore:s.MapMode.TrackAfter:s.MapMode.TrackDel}get type(){return this.startSide!=this.endSide?at.WidgetRange:this.startSide<=0?at.WidgetBefore:at.WidgetAfter}get heightRelevant(){return this.block||!!this.widget&&(this.widget.estimatedHeight>=5||this.widget.lineBreaks>0)}eq(t){return t instanceof ft&&((e=this.widget)==(i=t.widget)||!!(e&&i&&e.compare(i)))&&this.block==t.block&&this.startSide==t.startSide&&this.endSide==t.endSide;var e,i}range(t,e=t){if(this.isReplace&&(t>e||t==e&&this.startSide>0&&this.endSide<=0))throw new RangeError("Invalid range for replacement decoration");if(!this.isReplace&&e!=t)throw new RangeError("Widget decorations can only have zero-length ranges");return super.range(t,e)}}function pt(t,e=!1){let{inclusiveStart:i,inclusiveEnd:s}=t;return null==i&&(i=t.inclusive),null==s&&(s=t.inclusive),{start:null!=i?i:e,end:null!=s?s:e}}function gt(t,e,i,s=0){let o=i.length-1;o>=0&&i[o]+s>=t?i[o]=Math.max(i[o],e):i.push(t,e)}ft.prototype.point=!0;class mt{constructor(t,e,i,s){this.doc=t,this.pos=e,this.end=i,this.disallowBlockEffectsFor=s,this.content=[],this.curLine=null,this.breakAtStart=0,this.pendingBuffer=0,this.bufferMarks=[],this.atCursorPos=!0,this.openStart=-1,this.openEnd=-1,this.text="",this.textOff=0,this.cursor=t.iter(),this.skip=e}posCovered(){if(0==this.content.length)return!this.breakAtStart&&this.doc.lineAt(this.pos).from!=this.pos;let t=this.content[this.content.length-1];return!(t.breakAfter||t instanceof lt&&t.deco.endSide<0)}getLine(){return this.curLine||(this.content.push(this.curLine=new rt),this.atCursorPos=!0),this.curLine}flushBuffer(t=this.bufferMarks){this.pendingBuffer&&(this.curLine.append(wt(new J(-1),t),t.length),this.pendingBuffer=0)}addBlockWidget(t){this.flushBuffer(),this.curLine=null,this.content.push(t)}finish(t){this.pendingBuffer&&t<=this.bufferMarks.length?this.flushBuffer():this.pendingBuffer=0,this.posCovered()||t&&this.content.length&&this.content[this.content.length-1]instanceof lt||this.getLine()}buildText(t,e,i){for(;t>0;){if(this.textOff==this.text.length){let{value:e,lineBreak:i,done:s}=this.cursor.next(this.skip);if(this.skip=0,s)throw new Error("Ran out of text content when drawing inline views");if(i){this.posCovered()||this.getLine(),this.content.length?this.content[this.content.length-1].breakAfter=1:this.breakAtStart=1,this.flushBuffer(),this.curLine=null,this.atCursorPos=!0,t--;continue}this.text=e,this.textOff=0}let s=Math.min(this.text.length-this.textOff,t,512);this.flushBuffer(e.slice(e.length-i)),this.getLine().append(wt(new X(this.text.slice(this.textOff,this.textOff+s)),e),i),this.atCursorPos=!0,this.textOff+=s,t-=s,i=0}}span(t,e,i,s){this.buildText(e-t,i,s),this.pos=e,this.openStart<0&&(this.openStart=s)}point(t,e,i,s,o,n){if(this.disallowBlockEffectsFor[n]&&i instanceof ft){if(i.block)throw new RangeError("Block decorations may not be specified via plugins");if(e>this.doc.lineAt(this.pos).to)throw new RangeError("Decorations that replace line breaks may not be specified via plugins")}let r=e-t;if(i instanceof ft)if(i.block)i.startSide>0&&!this.posCovered()&&this.getLine(),this.addBlockWidget(new lt(i.widget||new vt("div"),r,i));else{let n=$.create(i.widget||new vt("span"),r,r?0:i.startSide),l=this.atCursorPos&&!n.isEditable&&o<=s.length&&(t<e||i.startSide>0),h=!n.isEditable&&(t<e||o>s.length||i.startSide<=0),a=this.getLine();2!=this.pendingBuffer||l||n.isEditable||(this.pendingBuffer=0),this.flushBuffer(s),l&&(a.append(wt(new J(1),s),o),o=s.length+Math.max(0,o-s.length)),a.append(wt(n,s),o),this.atCursorPos=h,this.pendingBuffer=h?t<e||o>s.length?1:2:0,this.pendingBuffer&&(this.bufferMarks=s.slice())}else this.doc.lineAt(this.pos).from==this.pos&&this.getLine().addLineDeco(i);r&&(this.textOff+r<=this.text.length?this.textOff+=r:(this.skip+=r-(this.text.length-this.textOff),this.text="",this.textOff=0),this.pos=e),this.openStart<0&&(this.openStart=o)}static build(t,e,i,o,n){let r=new mt(t,e,i,n);return r.openEnd=s.RangeSet.spans(o,e,i,r),r.openStart<0&&(r.openStart=r.openEnd),r.finish(r.openEnd),r}}function wt(t,e){for(let i of e)t=new U(i,[t],t.length);return t}class vt extends ht{constructor(t){super(),this.tag=t}eq(t){return t.tag==this.tag}toDOM(){return document.createElement(this.tag)}updateDOM(t){return t.nodeName.toLowerCase()==this.tag}get isHidden(){return!0}}const bt=s.Facet.define(),yt=s.Facet.define(),St=s.Facet.define(),xt=s.Facet.define(),Mt=s.Facet.define(),kt=s.Facet.define(),Ct=s.Facet.define(),At=s.Facet.define({combine:t=>t.some((t=>t))}),Dt=s.Facet.define({combine:t=>t.some((t=>t))});class Tt{constructor(t,e="nearest",i="nearest",s=5,o=5){this.range=t,this.y=e,this.x=i,this.yMargin=s,this.xMargin=o}map(t){return t.empty?this:new Tt(this.range.map(t),this.y,this.x,this.yMargin,this.xMargin)}}const Ot=s.StateEffect.define({map:(t,e)=>t.map(e)});function Et(t,e,i){let s=t.facet(xt);s.length?s[0](e):window.onerror?window.onerror(String(e),i,void 0,void 0,e):i?console.error(i+":",e):console.error(e)}const Rt=s.Facet.define({combine:t=>!t.length||t[0]});let Bt=0;const Lt=s.Facet.define();class Ht{constructor(t,e,i,s,o){this.id=t,this.create=e,this.domEventHandlers=i,this.domEventObservers=s,this.extension=o(this)}static define(t,e){const{eventHandlers:i,eventObservers:s,provide:o,decorations:n}=e||{};return new Ht(Bt++,t,i,s,(t=>{let e=[Lt.of(t)];return n&&e.push(Ft.of((e=>{let i=e.plugin(t);return i?n(i):ct.none}))),o&&e.push(o(t)),e}))}static fromClass(t,e){return Ht.define((e=>new t(e)),e)}}class Pt{constructor(t){this.spec=t,this.mustUpdate=null,this.value=null}update(t){if(this.value){if(this.mustUpdate){let t=this.mustUpdate;if(this.mustUpdate=null,this.value.update)try{this.value.update(t)}catch(e){if(Et(t.state,e,"CodeMirror plugin crashed"),this.value.destroy)try{this.value.destroy()}catch(t){}this.deactivate()}}}else if(this.spec)try{this.value=this.spec.create(t)}catch(e){Et(t.state,e,"CodeMirror plugin crashed"),this.deactivate()}return this}destroy(t){var e;if(null===(e=this.value)||void 0===e?void 0:e.destroy)try{this.value.destroy()}catch(e){Et(t.state,e,"CodeMirror plugin crashed")}}deactivate(){this.spec=this.value=null}}const Nt=s.Facet.define(),Vt=s.Facet.define(),Ft=s.Facet.define(),Wt=s.Facet.define(),zt=s.Facet.define();function It(t,e,i){let o=t.state.facet(zt);if(!o.length)return o;let n=o.map((e=>e instanceof Function?e(t):e)),r=[];return s.RangeSet.spans(n,e,i,{point(){},span(t,e,i,s){let o=r;for(let n=i.length-1;n>=0;n--,s--){let r,l=i[n].spec.bidiIsolate;if(null!=l)if(s>0&&o.length&&(r=o[o.length-1]).to==t&&r.direction==l)r.to=e,o=r.inner;else{let i={from:t,to:e,direction:l,inner:[]};o.push(i),o=i.inner}}}}),r}const Kt=s.Facet.define();function qt(t){let e=0,i=0,s=0,o=0;for(let n of t.state.facet(Kt)){let r=n(t);r&&(null!=r.left&&(e=Math.max(e,r.left)),null!=r.right&&(i=Math.max(i,r.right)),null!=r.top&&(s=Math.max(s,r.top)),null!=r.bottom&&(o=Math.max(o,r.bottom)))}return{left:e,right:i,top:s,bottom:o}}const _t=s.Facet.define();class Yt{constructor(t,e,i,s){this.fromA=t,this.toA=e,this.fromB=i,this.toB=s}join(t){return new Yt(Math.min(this.fromA,t.fromA),Math.max(this.toA,t.toA),Math.min(this.fromB,t.fromB),Math.max(this.toB,t.toB))}addToSet(t){let e=t.length,i=this;for(;e>0;e--){let s=t[e-1];if(!(s.fromA>i.toA)){if(s.toA<i.fromA)break;i=i.join(s),t.splice(e-1,1)}}return t.splice(e,0,i),t}static extendWithRanges(t,e){if(0==e.length)return t;let i=[];for(let s=0,o=0,n=0,r=0;;s++){let l=s==t.length?null:t[s],h=n-r,a=l?l.fromB:1e9;for(;o<e.length&&e[o]<a;){let t=e[o],s=e[o+1],n=Math.max(r,t),l=Math.min(a,s);if(n<=l&&new Yt(n+h,l+h,n,l).addToSet(i),s>a)break;o+=2}if(!l)return i;new Yt(l.fromA,l.toA,l.fromB,l.toB).addToSet(i),n=l.toA,r=l.toB}}}class Gt{constructor(t,e,i){this.view=t,this.state=e,this.transactions=i,this.flags=0,this.startState=t.state,this.changes=s.ChangeSet.empty(this.startState.doc.length);for(let t of i)this.changes=this.changes.compose(t.changes);let o=[];this.changes.iterChangedRanges(((t,e,i,s)=>o.push(new Yt(t,e,i,s)))),this.changedRanges=o}static create(t,e,i){return new Gt(t,e,i)}get viewportChanged(){return(4&this.flags)>0}get heightChanged(){return(2&this.flags)>0}get geometryChanged(){return this.docChanged||(10&this.flags)>0}get focusChanged(){return(1&this.flags)>0}get docChanged(){return!this.changes.empty}get selectionSet(){return this.transactions.some((t=>t.selection))}get empty(){return 0==this.flags&&0==this.transactions.length}}var jt=function(t){return t[t.LTR=0]="LTR",t[t.RTL=1]="RTL",t}(jt||(jt={}));const Xt=jt.LTR,Ut=jt.RTL;function $t(t){let e=[];for(let i=0;i<t.length;i++)e.push(1<<+t[i]);return e}const Jt=$t("88888888888888888888888888888888888666888888787833333333337888888000000000000000000000000008888880000000000000000000000000088888888888888888888888888888888888887866668888088888663380888308888800000000000000000000000800000000000000000000000000000008"),Qt=$t("4444448826627288999999999992222222222222222222222222222222222222222222222229999999999999999999994444444444644222822222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222999999949999999229989999223333333333"),Zt=Object.create(null),te=[];for(let t of["()","[]","{}"]){let e=t.charCodeAt(0),i=t.charCodeAt(1);Zt[e]=i,Zt[i]=-e}const ee=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac\ufb50-\ufdff]/;class ie{get dir(){return this.level%2?Ut:Xt}constructor(t,e,i){this.from=t,this.to=e,this.level=i}side(t,e){return this.dir==e==t?this.to:this.from}static find(t,e,i,s){let o=-1;for(let n=0;n<t.length;n++){let r=t[n];if(r.from<=e&&r.to>=e){if(r.level==i)return n;(o<0||(0!=s?s<0?r.from<e:r.to>e:t[o].level>r.level))&&(o=n)}}if(o<0)throw new RangeError("Index out of range");return o}}function se(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++){let s=t[i],o=e[i];if(s.from!=o.from||s.to!=o.to||s.direction!=o.direction||!se(s.inner,o.inner))return!1}return!0}const oe=[];function ne(t,e,i,s,o,n,r){let l=s%2?2:1;if(s%2==o%2)for(let h=e,a=0;h<i;){let e=!0,c=!1;if(a==n.length||h<n[a].from){let t=oe[h];t!=l&&(e=!1,c=16==t)}let d=e||1!=l?null:[],u=e?s:s+1,f=h;t:for(;;)if(a<n.length&&f==n[a].from){if(c)break t;let p=n[a];if(!e)for(let t=p.to,e=a+1;;){if(t==i)break t;if(!(e<n.length&&n[e].from==t)){if(oe[t]==l)break t;break}t=n[e++].to}a++,d?d.push(p):(p.from>h&&r.push(new ie(h,p.from,u)),re(t,p.direction==Xt!=!(u%2)?s+1:s,o,p.inner,p.from,p.to,r),h=p.to),f=p.to}else{if(f==i||(e?oe[f]!=l:oe[f]==l))break;f++}d?ne(t,h,f,s+1,o,d,r):h<f&&r.push(new ie(h,f,u)),h=f}else for(let h=i,a=n.length;h>e;){let i=!0,c=!1;if(!a||h>n[a-1].to){let t=oe[h-1];t!=l&&(i=!1,c=16==t)}let d=i||1!=l?null:[],u=i?s:s+1,f=h;t:for(;;)if(a&&f==n[a-1].to){if(c)break t;let p=n[--a];if(!i)for(let t=p.from,i=a;;){if(t==e)break t;if(!i||n[i-1].to!=t){if(oe[t-1]==l)break t;break}t=n[--i].from}d?d.push(p):(p.to<h&&r.push(new ie(p.to,h,u)),re(t,p.direction==Xt!=!(u%2)?s+1:s,o,p.inner,p.from,p.to,r),h=p.from),f=p.from}else{if(f==e||(i?oe[f-1]!=l:oe[f-1]==l))break;f--}d?ne(t,f,h,s+1,o,d,r):f<h&&r.push(new ie(f,h,u)),h=f}}function re(t,e,i,s,o,n,r){let l=e%2?2:1;!function(t,e,i,s,o){for(let r=0;r<=s.length;r++){let l=r?s[r-1].to:e,h=r<s.length?s[r].from:i,a=r?256:o;for(let e=l,i=a,s=a;e<h;e++){let o=(n=t.charCodeAt(e))<=247?Jt[n]:1424<=n&&n<=1524?2:1536<=n&&n<=1785?Qt[n-1536]:1774<=n&&n<=2220?4:8192<=n&&n<=8203?256:64336<=n&&n<=65023?4:8204==n?256:1;512==o?o=i:8==o&&4==s&&(o=16),oe[e]=4==o?2:o,7&o&&(s=o),i=o}for(let t=l,e=a,s=a;t<h;t++){let o=oe[t];if(128==o)t<h-1&&e==oe[t+1]&&24&e?o=oe[t]=e:oe[t]=256;else if(64==o){let o=t+1;for(;o<h&&64==oe[o];)o++;let n=t&&8==e||o<i&&8==oe[o]?1==s?1:8:256;for(let e=t;e<o;e++)oe[e]=n;t=o-1}else 8==o&&1==s&&(oe[t]=1);e=o,7&o&&(s=o)}}var n}(t,o,n,s,l),function(t,e,i,s,o){let n=1==o?2:1;for(let r=0,l=0,h=0;r<=s.length;r++){let a=r?s[r-1].to:e,c=r<s.length?s[r].from:i;for(let e,i,s,r=a;r<c;r++)if(i=Zt[e=t.charCodeAt(r)])if(i<0){for(let t=l-3;t>=0;t-=3)if(te[t+1]==-i){let e=te[t+2],i=2&e?o:4&e?1&e?n:o:0;i&&(oe[r]=oe[te[t]]=i),l=t;break}}else{if(189==te.length)break;te[l++]=r,te[l++]=e,te[l++]=h}else if(2==(s=oe[r])||1==s){let t=s==o;h=t?0:1;for(let e=l-3;e>=0;e-=3){let i=te[e+2];if(2&i)break;if(t)te[e+2]|=2;else{if(4&i)break;te[e+2]|=4}}}}}(t,o,n,s,l),function(t,e,i,s){for(let o=0,n=s;o<=i.length;o++){let r=o?i[o-1].to:t,l=o<i.length?i[o].from:e;for(let h=r;h<l;){let r=oe[h];if(256==r){let r=h+1;for(;;)if(r==l){if(o==i.length)break;r=i[o++].to,l=o<i.length?i[o].from:e}else{if(256!=oe[r])break;r++}let a=1==n,c=a==(1==(r<e?oe[r]:s))?a?1:2:s;for(let e=r,s=o,n=s?i[s-1].to:t;e>h;)e==n&&(e=i[--s].from,n=s?i[s-1].to:t),oe[--e]=c;h=r}else n=r,h++}}}(o,n,s,l),ne(t,o,n,e,i,s,r)}function le(t,e,i){if(!t)return[new ie(0,0,e==Ut?1:0)];if(e==Xt&&!i.length&&!ee.test(t))return he(t.length);if(i.length)for(;t.length>oe.length;)oe[oe.length]=256;let s=[],o=e==Xt?0:1;return re(t,o,o,i,0,t.length,s),s}function he(t){return[new ie(0,t,0)]}let ae="";function ce(t,e,i,o,n){var r;let l=o.head-t.from,h=-1;if(0==l){if(!n||!t.length)return null;e[0].level!=i&&(l=e[0].side(!1,i),h=0)}else if(l==t.length){if(n)return null;let t=e[e.length-1];t.level!=i&&(l=t.side(!0,i),h=e.length-1)}h<0&&(h=ie.find(e,l,null!==(r=o.bidiLevel)&&void 0!==r?r:-1,o.assoc));let a=e[h];l==a.side(n,i)&&(a=e[h+=n?1:-1],l=a.side(!n,i));let c=n==(a.dir==i),d=(0,s.findClusterBreak)(t.text,l,c);if(ae=t.text.slice(Math.min(l,d),Math.max(l,d)),d!=a.side(n,i))return s.EditorSelection.cursor(d+t.from,c?-1:1,a.level);let u=h==(n?e.length-1:0)?null:e[h+(n?1:-1)];return u||a.level==i?u&&u.level<a.level?s.EditorSelection.cursor(u.side(!n,i)+t.from,n?1:-1,u.level):s.EditorSelection.cursor(d+t.from,n?-1:1,a.level):s.EditorSelection.cursor(n?t.to:t.from,n?-1:1,i)}class de extends R{get length(){return this.view.state.doc.length}constructor(t){super(),this.view=t,this.decorations=[],this.dynamicDecorationMap=[],this.domChanged=null,this.hasComposition=null,this.markedForComposition=new Set,this.minWidth=0,this.minWidthFrom=0,this.minWidthTo=0,this.impreciseAnchor=null,this.impreciseHead=null,this.forceSelection=!1,this.lastUpdate=Date.now(),this.setDOM(t.contentDOM),this.children=[new rt],this.children[0].setParent(this),this.updateDeco(),this.updateInner([new Yt(0,0,0,t.state.doc.length)],0,null)}update(t){var e;let i=t.changedRanges;this.minWidth>0&&i.length&&(i.every((({fromA:t,toA:e})=>e<this.minWidthFrom||t>this.minWidthTo))?(this.minWidthFrom=t.changes.mapPos(this.minWidthFrom,1),this.minWidthTo=t.changes.mapPos(this.minWidthTo,1)):this.minWidth=this.minWidthFrom=this.minWidthTo=0);let o=-1;this.view.inputState.composing>=0&&((null===(e=this.domChanged)||void 0===e?void 0:e.newSel)?o=this.domChanged.newSel.head:function(t,e){let i=!1;return e&&t.iterChangedRanges(((t,s)=>{t<e.to&&s>e.from&&(i=!0)})),i}(t.changes,this.hasComposition)||t.selectionSet||(o=t.state.selection.main.head));let n=o>-1?function(t,e,i){let s=fe(t,i);if(!s)return null;let{node:o,from:n,to:r}=s,l=o.nodeValue;if(/[\n\r]/.test(l))return null;if(t.state.doc.sliceString(s.from,s.to)!=l)return null;let h=e.invertedDesc,a=new Yt(h.mapPos(n),h.mapPos(r),n,r),c=[];for(let e=o.parentNode;;e=e.parentNode){let i=R.get(e);if(i instanceof U)c.push({node:e,deco:i.mark});else{if(i instanceof rt||"DIV"==e.nodeName&&e.parentNode==t.contentDOM)return{range:a,text:o,marks:c,line:e};if(e==t.contentDOM)return null;c.push({node:e,deco:new dt({inclusive:!0,attributes:nt(e),tagName:e.tagName.toLowerCase()})})}}}(this.view,t.changes,o):null;if(this.domChanged=null,this.hasComposition){this.markedForComposition.clear();let{from:e,to:s}=this.hasComposition;i=new Yt(e,s,t.changes.mapPos(e,-1),t.changes.mapPos(s,1)).addToSet(i.slice())}this.hasComposition=n?{from:n.range.fromB,to:n.range.toB}:null,(j.ie||j.chrome)&&!n&&t&&t.state.doc.lines!=t.startState.doc.lines&&(this.forceSelection=!0);let r=function(t,e,i){let o=new ge;return s.RangeSet.compare(t,e,i,o),o.changes}(this.decorations,this.updateDeco(),t.changes);return i=Yt.extendWithRanges(i,r),!!(7&this.flags||0!=i.length)&&(this.updateInner(i,t.startState.doc.length,n),t.transactions.length&&(this.lastUpdate=Date.now()),!0)}updateInner(t,e,i){this.view.viewState.mustMeasureContent=!0,this.updateChildren(t,e,i);let{observer:s}=this.view;s.ignore((()=>{this.dom.style.height=this.view.viewState.contentHeight/this.view.scaleY+"px",this.dom.style.flexBasis=this.minWidth?this.minWidth+"px":"";let t=j.chrome||j.ios?{node:s.selectionRange.focusNode,written:!1}:void 0;this.sync(this.view,t),this.flags&=-8,t&&(t.written||s.selectionRange.focusNode!=t.node)&&(this.forceSelection=!0),this.dom.style.height=""})),this.markedForComposition.forEach((t=>t.flags&=-9));let o=[];if(this.view.viewport.from||this.view.viewport.to<this.view.state.doc.length)for(let t of this.children)t instanceof lt&&t.widget instanceof ue&&o.push(t.dom);s.updateGaps(o)}updateChildren(t,e,i){let s=i?i.range.addToSet(t.slice()):t,o=this.childCursor(e);for(let t=s.length-1;;t--){let e=t>=0?s[t]:null;if(!e)break;let n,r,l,h,{fromA:a,toA:c,fromB:d,toB:u}=e;if(i&&i.range.fromB<u&&i.range.toB>d){let t=mt.build(this.view.state.doc,d,i.range.fromB,this.decorations,this.dynamicDecorationMap),e=mt.build(this.view.state.doc,i.range.toB,u,this.decorations,this.dynamicDecorationMap);r=t.breakAtStart,l=t.openStart,h=e.openEnd;let s=this.compositionView(i);e.breakAtStart?s.breakAfter=1:e.content.length&&s.merge(s.length,s.length,e.content[0],!1,e.openStart,0)&&(s.breakAfter=e.content[0].breakAfter,e.content.shift()),t.content.length&&s.merge(0,0,t.content[t.content.length-1],!0,0,t.openEnd)&&t.content.pop(),n=t.content.concat(s).concat(e.content)}else({content:n,breakAtStart:r,openStart:l,openEnd:h}=mt.build(this.view.state.doc,d,u,this.decorations,this.dynamicDecorationMap));let{i:f,off:p}=o.findPos(c,1),{i:g,off:m}=o.findPos(a,-1);H(this,g,m,f,p,n,r,l,h)}i&&this.fixCompositionDOM(i)}compositionView(t){let e=new X(t.text.nodeValue);e.flags|=8;for(let{deco:i}of t.marks)e=new U(i,[e],e.length);let i=new rt;return i.append(e,0),i}fixCompositionDOM(t){let e=(t,e)=>{e.flags|=8|(e.children.some((t=>7&t.flags))?1:0),this.markedForComposition.add(e);let i=R.get(t);i&&i!=e&&(i.dom=null),e.setDOM(t)},i=this.childPos(t.range.fromB,1),s=this.children[i.i];e(t.line,s);for(let o=t.marks.length-1;o>=-1;o--)i=s.childPos(i.off,1),s=s.children[i.i],e(o>=0?t.marks[o].node:t.text,s)}updateSelection(t=!1,e=!1){!t&&this.view.observer.selectionRange.focusNode||this.view.observer.readSelectionRange();let i=this.view.root.activeElement,s=i==this.dom,o=!s&&f(this.dom,this.view.observer.selectionRange)&&!(i&&this.dom.contains(i));if(!(s||e||o))return;let n=this.forceSelection;this.forceSelection=!1;let r=this.view.state.selection.main,l=this.moveToLine(this.domAtPos(r.anchor)),h=r.empty?l:this.moveToLine(this.domAtPos(r.head));if(j.gecko&&r.empty&&!this.hasComposition&&1==(a=l).node.nodeType&&a.node.firstChild&&(0==a.offset||"false"==a.node.childNodes[a.offset-1].contentEditable)&&(a.offset==a.node.childNodes.length||"false"==a.node.childNodes[a.offset].contentEditable)){let t=document.createTextNode("");this.view.observer.ignore((()=>l.node.insertBefore(t,l.node.childNodes[l.offset]||null))),l=h=new O(t,0),n=!0}var a;let c=this.view.observer.selectionRange;!n&&c.focusNode&&g(l.node,l.offset,c.anchorNode,c.anchorOffset)&&g(h.node,h.offset,c.focusNode,c.focusOffset)||(this.view.observer.ignore((()=>{j.android&&j.chrome&&this.dom.contains(c.focusNode)&&function(t,e){for(let i=t;i&&i!=e;i=i.assignedSlot||i.parentNode)if(1==i.nodeType&&"false"==i.contentEditable)return!0;return!1}(c.focusNode,this.dom)&&(this.dom.blur(),this.dom.focus({preventScroll:!0}));let t=d(this.view.root);if(t)if(r.empty){if(j.gecko){let t=(e=l.node,s=l.offset,1!=e.nodeType?0:(s&&"false"==e.childNodes[s-1].contentEditable?1:0)|(s<e.childNodes.length&&"false"==e.childNodes[s].contentEditable?2:0));if(t&&3!=t){let e=pe(l.node,l.offset,1==t?1:-1);e&&(l=new O(e.node,e.offset))}}t.collapse(l.node,l.offset),null!=r.bidiLevel&&void 0!==t.caretBidiLevel&&(t.caretBidiLevel=r.bidiLevel)}else if(t.extend){t.collapse(l.node,l.offset);try{t.extend(h.node,h.offset)}catch(t){}}else{let e=document.createRange();r.anchor>r.head&&([l,h]=[h,l]),e.setEnd(h.node,h.offset),e.setStart(l.node,l.offset),t.removeAllRanges(),t.addRange(e)}var e,s;o&&this.view.root.activeElement==this.dom&&(this.dom.blur(),i&&i.focus())})),this.view.observer.setSelectionRange(l,h)),this.impreciseAnchor=l.precise?null:new O(c.anchorNode,c.anchorOffset),this.impreciseHead=h.precise?null:new O(c.focusNode,c.focusOffset)}enforceCursorAssoc(){if(this.hasComposition)return;let{view:t}=this,e=t.state.selection.main,i=d(t.root),{anchorNode:s,anchorOffset:o}=t.observer.selectionRange;if(!(i&&e.empty&&e.assoc&&i.modify))return;let n=rt.find(this,e.head);if(!n)return;let r=n.posAtStart;if(e.head==r||e.head==r+n.length)return;let l=this.coordsAt(e.head,-1),h=this.coordsAt(e.head,1);if(!l||!h||l.bottom>h.top)return;let a=this.domAtPos(e.head+e.assoc);i.collapse(a.node,a.offset),i.modify("move",e.assoc<0?"forward":"backward","lineboundary"),t.observer.readSelectionRange();let c=t.observer.selectionRange;t.docView.posFromDOM(c.anchorNode,c.anchorOffset)!=e.from&&i.collapse(s,o)}moveToLine(t){let e,i=this.dom;if(t.node!=i)return t;for(let s=t.offset;!e&&s<i.childNodes.length;s++){let t=R.get(i.childNodes[s]);t instanceof rt&&(e=t.domAtPos(0))}for(let s=t.offset-1;!e&&s>=0;s--){let t=R.get(i.childNodes[s]);t instanceof rt&&(e=t.domAtPos(t.length))}return e?new O(e.node,e.offset,!0):t}nearest(t){for(let e=t;e;){let t=R.get(e);if(t&&t.rootView==this)return t;e=e.parentNode}return null}posFromDOM(t,e){let i=this.nearest(t);if(!i)throw new RangeError("Trying to find position for a DOM position outside of the document");return i.localPosFromDOM(t,e)+i.posAtStart}domAtPos(t){let{i:e,off:i}=this.childCursor().findPos(t,-1);for(;e<this.children.length-1;){let t=this.children[e];if(i<t.length||t instanceof rt)break;e++,i=0}return this.children[e].domAtPos(i)}coordsAt(t,e){let i=null,s=0;for(let o=this.length,n=this.children.length-1;n>=0;n--){let r=this.children[n],l=o-r.breakAfter,h=l-r.length;if(l<t)break;h<=t&&(h<t||r.covers(-1))&&(l>t||r.covers(1))&&(!i||r instanceof rt&&!(i instanceof rt&&e>=0))&&(i=r,s=h),o=h}return i?i.coordsAt(t-s,e):null}coordsForChar(t){let{i:e,off:i}=this.childPos(t,1),o=this.children[e];if(!(o instanceof rt))return null;for(;o.children.length;){let{i:t,off:e}=o.childPos(i,1);for(;;t++){if(t==o.children.length)return null;if((o=o.children[t]).length)break}i=e}if(!(o instanceof X))return null;let n=(0,s.findClusterBreak)(o.text,i);if(n==i)return null;let r=C(o.dom,i,n).getClientRects();for(let t=0;t<r.length;t++){let e=r[t];if(t==r.length-1||e.top<e.bottom&&e.left<e.right)return e}return null}measureVisibleLineHeights(t){let e=[],{from:i,to:s}=t,o=this.view.contentDOM.clientWidth,n=o>Math.max(this.view.scrollDOM.clientWidth,this.minWidth)+1,r=-1,l=this.view.textDirection==jt.LTR;for(let t=0,h=0;h<this.children.length;h++){let a=this.children[h],c=t+a.length;if(c>s)break;if(t>=i){let i=a.dom.getBoundingClientRect();if(e.push(i.height),n){let e=a.dom.lastChild,s=e?p(e):[];if(s.length){let e=s[s.length-1],n=l?e.right-i.left:i.right-e.left;n>r&&(r=n,this.minWidth=o,this.minWidthFrom=t,this.minWidthTo=c)}}}t=c+a.breakAfter}return e}textDirectionAt(t){let{i:e}=this.childPos(t,1);return"rtl"==getComputedStyle(this.children[e].dom).direction?jt.RTL:jt.LTR}measureTextSize(){for(let t of this.children)if(t instanceof rt){let e=t.measureTextSize();if(e)return e}let t,e,i,s=document.createElement("div");return s.className="cm-line",s.style.width="99999px",s.style.position="absolute",s.textContent="abc def ghi jkl mno pqr stu",this.view.observer.ignore((()=>{this.dom.appendChild(s);let o=p(s.firstChild)[0];t=s.getBoundingClientRect().height,e=o?o.width/27:7,i=o?o.height:t,s.remove()})),{lineHeight:t,charWidth:e,textHeight:i}}childCursor(t=this.length){let e=this.children.length;return e&&(t-=this.children[--e].length),new L(this.children,t,e)}computeBlockGapDeco(){let t=[],e=this.view.viewState;for(let i=0,s=0;;s++){let o=s==e.viewports.length?null:e.viewports[s],n=o?o.from-1:this.length;if(n>i){let s=(e.lineBlockAt(n).bottom-e.lineBlockAt(i).top)/this.view.scaleY;t.push(ct.replace({widget:new ue(s),block:!0,inclusive:!0,isBlockGap:!0}).range(i,n))}if(!o)break;i=o.to+1}return ct.set(t)}updateDeco(){let t=this.view.state.facet(Ft).map(((t,e)=>(this.dynamicDecorationMap[e]="function"==typeof t)?t(this.view):t));for(let e=t.length;e<t.length+3;e++)this.dynamicDecorationMap[e]=!1;return this.decorations=[...t,this.computeBlockGapDeco(),this.view.viewState.lineGapDeco]}scrollIntoView(t){let e,{range:i}=t,s=this.coordsAt(i.head,i.empty?i.assoc:i.head>i.anchor?-1:1);if(!s)return;!i.empty&&(e=this.coordsAt(i.anchor,i.anchor>i.head?-1:1))&&(s={left:Math.min(s.left,e.left),top:Math.min(s.top,e.top),right:Math.max(s.right,e.right),bottom:Math.max(s.bottom,e.bottom)});let o=qt(this.view),n={left:s.left-o.left,top:s.top-o.top,right:s.right+o.right,bottom:s.bottom+o.bottom};!function(t,e,i,s,o,n,r,l){let h=t.ownerDocument,a=h.defaultView||window;for(let c=t,d=!1;c&&!d;)if(1==c.nodeType){let t,u=c==h.body,f=1,p=1;if(u)t=y(a);else{if(/^(fixed|sticky)$/.test(getComputedStyle(c).position)&&(d=!0),c.scrollHeight<=c.clientHeight&&c.scrollWidth<=c.clientWidth){c=c.assignedSlot||c.parentNode;continue}let e=c.getBoundingClientRect();f=e.width/c.offsetWidth,p=e.height/c.offsetHeight,t={left:e.left,right:e.left+c.clientWidth*f,top:e.top,bottom:e.top+c.clientHeight*p}}let g=0,m=0;if("nearest"==o)e.top<t.top?(m=-(t.top-e.top+r),i>0&&e.bottom>t.bottom+m&&(m=e.bottom-t.bottom+m+r)):e.bottom>t.bottom&&(m=e.bottom-t.bottom+r,i<0&&e.top-m<t.top&&(m=-(t.top+m-e.top+r)));else{let s=e.bottom-e.top,n=t.bottom-t.top;m=("center"==o&&s<=n?e.top+s/2-n/2:"start"==o||"center"==o&&i<0?e.top-r:e.bottom-n+r)-t.top}if("nearest"==s?e.left<t.left?(g=-(t.left-e.left+n),i>0&&e.right>t.right+g&&(g=e.right-t.right+g+n)):e.right>t.right&&(g=e.right-t.right+n,i<0&&e.left<t.left+g&&(g=-(t.left+g-e.left+n))):g=("center"==s?e.left+(e.right-e.left)/2-(t.right-t.left)/2:"start"==s==l?e.left-n:e.right-(t.right-t.left)+n)-t.left,g||m)if(u)a.scrollBy(g,m);else{let t=0,i=0;if(m){let t=c.scrollTop;c.scrollTop+=m/p,i=(c.scrollTop-t)*p}if(g){let e=c.scrollLeft;c.scrollLeft+=g/f,t=(c.scrollLeft-e)*f}e={left:e.left-t,top:e.top-i,right:e.right-t,bottom:e.bottom-i},t&&Math.abs(t-g)<1&&(s="nearest"),i&&Math.abs(i-m)<1&&(o="nearest")}if(u)break;c=c.assignedSlot||c.parentNode}else{if(11!=c.nodeType)break;c=c.host}}(this.view.scrollDOM,n,i.head<i.anchor?-1:1,t.x,t.y,t.xMargin,t.yMargin,this.view.textDirection==jt.LTR)}}class ue extends ht{constructor(t){super(),this.height=t}toDOM(){let t=document.createElement("div");return this.updateDOM(t),t}eq(t){return t.height==this.height}updateDOM(t){return t.style.height=this.height+"px",!0}get estimatedHeight(){return this.height}}function fe(t,e){let i=t.observer.selectionRange,s=i.focusNode&&pe(i.focusNode,i.focusOffset,0);if(!s)return null;let o=e-s.offset;return{from:o,to:o+s.node.nodeValue.length,node:s.node}}function pe(t,e,i){if(i<=0)for(let i=t,s=e;;){if(3==i.nodeType)return{node:i,offset:s};if(!(1==i.nodeType&&s>0))break;i=i.childNodes[s-1],s=v(i)}if(i>=0)for(let s=t,o=e;;){if(3==s.nodeType)return{node:s,offset:o};if(!(1==s.nodeType&&o<s.childNodes.length&&i>=0))break;s=s.childNodes[o],o=0}return null}let ge=class{constructor(){this.changes=[]}compareRange(t,e){gt(t,e,this.changes)}comparePoint(t,e){gt(t,e,this.changes)}};function me(t,e){return e.left>t?e.left-t:Math.max(0,t-e.right)}function we(t,e){return e.top>t?e.top-t:Math.max(0,t-e.bottom)}function ve(t,e){return t.top<e.bottom-1&&t.bottom>e.top+1}function be(t,e){return e<t.top?{top:e,left:t.left,right:t.right,bottom:t.bottom}:t}function ye(t,e){return e>t.bottom?{top:t.top,left:t.left,right:t.right,bottom:e}:t}function Se(t,e,i){let s,o,n,r,l,h,a,c,d=!1;for(let u=t.firstChild;u;u=u.nextSibling){let t=p(u);for(let f=0;f<t.length;f++){let p=t[f];o&&ve(o,p)&&(p=be(ye(p,o.bottom),o.top));let g=me(e,p),m=we(i,p);if(0==g&&0==m)return 3==u.nodeType?xe(u,e,i):Se(u,e,i);if(!s||r>m||r==m&&n>g){s=u,o=p,n=g,r=m;let l=m?i<p.top?-1:1:g?e<p.left?-1:1:0;d=!l||(l>0?f<t.length-1:f>0)}0==g?i>p.bottom&&(!a||a.bottom<p.bottom)?(l=u,a=p):i<p.top&&(!c||c.top>p.top)&&(h=u,c=p):a&&ve(a,p)?a=ye(a,p.bottom):c&&ve(c,p)&&(c=be(c,p.top))}}if(a&&a.bottom>=i?(s=l,o=a):c&&c.top<=i&&(s=h,o=c),!s)return{node:t,offset:0};let u=Math.max(o.left,Math.min(o.right,e));return 3==s.nodeType?xe(s,u,i):d&&"false"!=s.contentEditable?Se(s,u,i):{node:t,offset:Array.prototype.indexOf.call(t.childNodes,s)+(e>=(o.left+o.right)/2?1:0)}}function xe(t,e,i){let s=t.nodeValue.length,o=-1,n=1e9,r=0;for(let l=0;l<s;l++){let s=C(t,l,l+1).getClientRects();for(let h=0;h<s.length;h++){let a=s[h];if(a.top==a.bottom)continue;r||(r=e-a.left);let c=(a.top>i?a.top-i:i-a.bottom)-1;if(a.left-1<=e&&a.right+1>=e&&c<n){let i=e>=(a.left+a.right)/2,s=i;if((j.chrome||j.gecko)&&C(t,l).getBoundingClientRect().left==a.right&&(s=!i),c<=0)return{node:t,offset:l+(s?1:0)};o=l+(s?1:0),n=c}}}return{node:t,offset:o>-1?o:r>0?t.nodeValue.length:0}}function Me(t,e,i,s=-1){var o,n;let r,l=t.contentDOM.getBoundingClientRect(),h=l.top+t.viewState.paddingTop,{docHeight:a}=t.viewState,{x:c,y:d}=e,u=d-h;if(u<0)return 0;if(u>a)return t.state.doc.length;for(let e=t.viewState.heightOracle.textHeight/2,o=!1;r=t.elementAtHeight(u),r.type!=at.Text;)for(;u=s>0?r.bottom+e:r.top-e,!(u>=0&&u<=a);){if(o)return i?null:0;o=!0,s=-s}d=h+u;let f=r.from;if(f<t.viewport.from)return 0==t.viewport.from?0:i?null:ke(t,l,r,c,d);if(f>t.viewport.to)return t.viewport.to==t.state.doc.length?t.state.doc.length:i?null:ke(t,l,r,c,d);let p=t.dom.ownerDocument,g=t.root.elementFromPoint?t.root:p,m=g.elementFromPoint(c,d);m&&!t.contentDOM.contains(m)&&(m=null),m||(c=Math.max(l.left+1,Math.min(l.right-1,c)),m=g.elementFromPoint(c,d),m&&!t.contentDOM.contains(m)&&(m=null));let w,v=-1;if(m&&0!=(null===(o=t.docView.nearest(m))||void 0===o?void 0:o.isEditable))if(p.caretPositionFromPoint){let t=p.caretPositionFromPoint(c,d);t&&({offsetNode:w,offset:v}=t)}else if(p.caretRangeFromPoint){let e=p.caretRangeFromPoint(c,d);e&&(({startContainer:w,startOffset:v}=e),(!t.contentDOM.contains(w)||j.safari&&function(t,e,i){let s;if(3!=t.nodeType||e!=(s=t.nodeValue.length))return!1;for(let e=t.nextSibling;e;e=e.nextSibling)if(1!=e.nodeType||"BR"!=e.nodeName)return!1;return C(t,s-1,s).getBoundingClientRect().left>i}(w,v,c)||j.chrome&&function(t,e,i){if(0!=e)return!1;for(let e=t;;){let t=e.parentNode;if(!t||1!=t.nodeType||t.firstChild!=e)return!1;if(t.classList.contains("cm-line"))break;e=t}return i-(1==t.nodeType?t.getBoundingClientRect():C(t,0,Math.max(t.nodeValue.length,1)).getBoundingClientRect()).left>5}(w,v,c))&&(w=void 0))}if(!w||!t.docView.dom.contains(w)){let e=rt.find(t.docView,f);if(!e)return u>r.top+r.height/2?r.to:r.from;({node:w,offset:v}=Se(e.dom,c,d))}let b=t.docView.nearest(w);if(!b)return null;if(b.isWidget&&1==(null===(n=b.dom)||void 0===n?void 0:n.nodeType)){let t=b.dom.getBoundingClientRect();return e.y<t.top||e.y<=t.bottom&&e.x<=(t.left+t.right)/2?b.posAtStart:b.posAtEnd}return b.localPosFromDOM(w,v)+b.posAtStart}function ke(t,e,i,o,n){let r=Math.round((o-e.left)*t.defaultCharacterWidth);if(t.lineWrapping&&i.height>1.5*t.defaultLineHeight){let e=t.viewState.heightOracle.textHeight;r+=Math.floor((n-i.top-.5*(t.defaultLineHeight-e))/e)*t.viewState.heightOracle.lineLength}let l=t.state.sliceDoc(i.from,i.to);return i.from+(0,s.findColumn)(l,r,t.state.tabSize)}function Ce(t,e){let i=t.lineBlockAt(e);if(Array.isArray(i.type))for(let t of i.type)if(t.to>e||t.to==e&&(t.to==i.to||t.type==at.Text))return t;return i}function Ae(t,e,i,o){let n=t.state.doc.lineAt(e.head),r=t.bidiSpans(n),l=t.textDirectionAt(n.from);for(let h=e,a=null;;){let e=ce(n,r,l,h,i),c=ae;if(!e){if(n.number==(i?t.state.doc.lines:1))return h;c="\n",n=t.state.doc.line(n.number+(i?1:-1)),r=t.bidiSpans(n),e=s.EditorSelection.cursor(i?n.from:n.to)}if(a){if(!a(c))return h}else{if(!o)return e;a=o(c)}h=e}}function De(t,e,i){for(;;){let s=0;for(let o of t)o.between(e-1,e+1,((t,o,n)=>{if(e>t&&e<o){let n=s||i||(e-t<o-e?-1:1);e=n<0?t:o,s=n}}));if(!s)return e}}function Te(t,e,i){let o=De(t.state.facet(Wt).map((e=>e(t))),i.from,e.head>i.from?-1:1);return o==i.from?i:s.EditorSelection.cursor(o,o<i.from?1:-1)}class Oe{setSelectionOrigin(t){this.lastSelectionOrigin=t,this.lastSelectionTime=Date.now()}constructor(t){this.view=t,this.lastKeyCode=0,this.lastKeyTime=0,this.lastTouchTime=0,this.lastFocusTime=0,this.lastScrollTop=0,this.lastScrollLeft=0,this.pendingIOSKey=void 0,this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastEscPress=0,this.lastContextMenu=0,this.scrollHandlers=[],this.handlers=Object.create(null),this.composing=-1,this.compositionFirstChange=null,this.compositionEndedAt=0,this.compositionPendingKey=!1,this.compositionPendingChange=!1,this.mouseSelection=null,this.handleEvent=this.handleEvent.bind(this),this.notifiedFocused=t.hasFocus,j.safari&&t.contentDOM.addEventListener("input",(()=>null)),j.gecko&&function(t){ii.has(t)||(ii.add(t),t.addEventListener("copy",(()=>{})),t.addEventListener("cut",(()=>{})))}(t.contentDOM.ownerDocument)}handleEvent(t){(function(t,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let i,s=e.target;s!=t.contentDOM;s=s.parentNode)if(!s||11==s.nodeType||(i=R.get(s))&&i.ignoreEvent(e))return!1;return!0})(this.view,t)&&!this.ignoreDuringComposition(t)&&("keydown"==t.type&&this.keydown(t)||this.runHandlers(t.type,t))}runHandlers(t,e){let i=this.handlers[t];if(i){for(let t of i.observers)t(this.view,e);for(let t of i.handlers){if(e.defaultPrevented)break;if(t(this.view,e)){e.preventDefault();break}}}}ensureHandlers(t){let e=Re(t),i=this.handlers,s=this.view.contentDOM;for(let t in e)if("scroll"!=t){let o=!e[t].handlers.length,n=i[t];n&&o!=!n.handlers.length&&(s.removeEventListener(t,this.handleEvent),n=null),n||s.addEventListener(t,this.handleEvent,{passive:o})}for(let t in i)"scroll"==t||e[t]||s.removeEventListener(t,this.handleEvent);this.handlers=e}keydown(t){if(this.lastKeyCode=t.keyCode,this.lastKeyTime=Date.now(),9==t.keyCode&&Date.now()<this.lastEscPress+2e3)return!0;if(27!=t.keyCode&&He.indexOf(t.keyCode)<0&&(this.view.inputState.lastEscPress=0),j.android&&j.chrome&&!t.synthetic&&(13==t.keyCode||8==t.keyCode))return this.view.observer.delayAndroidKey(t.key,t.keyCode),!0;let e;return!j.ios||t.synthetic||t.altKey||t.metaKey||!((e=Be.find((e=>e.keyCode==t.keyCode)))&&!t.ctrlKey||Le.indexOf(t.key)>-1&&t.ctrlKey&&!t.shiftKey)?(229!=t.keyCode&&this.view.observer.forceFlush(),!1):(this.pendingIOSKey=e||t,setTimeout((()=>this.flushIOSKey()),250),!0)}flushIOSKey(){let t=this.pendingIOSKey;return!!t&&(this.pendingIOSKey=void 0,A(this.view.contentDOM,t.key,t.keyCode))}ignoreDuringComposition(t){return!!/^key/.test(t.type)&&(this.composing>0||!!(j.safari&&!j.ios&&this.compositionPendingKey&&Date.now()-this.compositionEndedAt<100)&&(this.compositionPendingKey=!1,!0))}startMouseSelection(t){this.mouseSelection&&this.mouseSelection.destroy(),this.mouseSelection=t}update(t){this.mouseSelection&&this.mouseSelection.update(t),t.transactions.length&&(this.lastKeyCode=this.lastSelectionTime=0)}destroy(){this.mouseSelection&&this.mouseSelection.destroy()}}function Ee(t,e){return(i,s)=>{try{return e.call(t,s,i)}catch(t){Et(i.state,t)}}}function Re(t){let e=Object.create(null);function i(t){return e[t]||(e[t]={observers:[],handlers:[]})}for(let e of t){let t=e.spec;if(t&&t.domEventHandlers)for(let s in t.domEventHandlers){let o=t.domEventHandlers[s];o&&i(s).handlers.push(Ee(e.value,o))}if(t&&t.domEventObservers)for(let s in t.domEventObservers){let o=t.domEventObservers[s];o&&i(s).observers.push(Ee(e.value,o))}}for(let t in Ve)i(t).handlers.push(Ve[t]);for(let t in Fe)i(t).observers.push(Fe[t]);return e}const Be=[{key:"Backspace",keyCode:8,inputType:"deleteContentBackward"},{key:"Enter",keyCode:13,inputType:"insertParagraph"},{key:"Enter",keyCode:13,inputType:"insertLineBreak"},{key:"Delete",keyCode:46,inputType:"deleteContentForward"}],Le="dthko",He=[16,17,18,20,91,92,224,225];function Pe(t){return.7*Math.max(0,t)+8}class Ne{constructor(t,e,i,o){this.view=t,this.startEvent=e,this.style=i,this.mustSelect=o,this.scrollSpeed={x:0,y:0},this.scrolling=-1,this.lastEvent=e,this.scrollParent=function(t){let e=t.ownerDocument;for(let i=t.parentNode;i&&i!=e.body;)if(1==i.nodeType){if(i.scrollHeight>i.clientHeight||i.scrollWidth>i.clientWidth)return i;i=i.assignedSlot||i.parentNode}else{if(11!=i.nodeType)break;i=i.host}return null}(t.contentDOM),this.atoms=t.state.facet(Wt).map((e=>e(t)));let n=t.contentDOM.ownerDocument;n.addEventListener("mousemove",this.move=this.move.bind(this)),n.addEventListener("mouseup",this.up=this.up.bind(this)),this.extend=e.shiftKey,this.multiple=t.state.facet(s.EditorState.allowMultipleSelections)&&function(t,e){let i=t.state.facet(bt);return i.length?i[0](e):j.mac?e.metaKey:e.ctrlKey}(t,e),this.dragging=!(!function(t,e){let{main:i}=t.state.selection;if(i.empty)return!1;let s=d(t.root);if(!s||0==s.rangeCount)return!0;let o=s.getRangeAt(0).getClientRects();for(let t=0;t<o.length;t++){let i=o[t];if(i.left<=e.clientX&&i.right>=e.clientX&&i.top<=e.clientY&&i.bottom>=e.clientY)return!0}return!1}(t,e)||1!=$e(e))&&null}start(t){!1===this.dragging&&this.select(t)}move(t){var e,i,s;if(0==t.buttons)return this.destroy();if(this.dragging||null==this.dragging&&(i=this.startEvent,s=t,Math.max(Math.abs(i.clientX-s.clientX),Math.abs(i.clientY-s.clientY))<10))return;this.select(this.lastEvent=t);let o=0,n=0,r=(null===(e=this.scrollParent)||void 0===e?void 0:e.getBoundingClientRect())||{left:0,top:0,right:this.view.win.innerWidth,bottom:this.view.win.innerHeight},l=qt(this.view);t.clientX-l.left<=r.left+6?o=-Pe(r.left-t.clientX):t.clientX+l.right>=r.right-6&&(o=Pe(t.clientX-r.right)),t.clientY-l.top<=r.top+6?n=-Pe(r.top-t.clientY):t.clientY+l.bottom>=r.bottom-6&&(n=Pe(t.clientY-r.bottom)),this.setScrollSpeed(o,n)}up(t){null==this.dragging&&this.select(this.lastEvent),this.dragging||t.preventDefault(),this.destroy()}destroy(){this.setScrollSpeed(0,0);let t=this.view.contentDOM.ownerDocument;t.removeEventListener("mousemove",this.move),t.removeEventListener("mouseup",this.up),this.view.inputState.mouseSelection=null}setScrollSpeed(t,e){this.scrollSpeed={x:t,y:e},t||e?this.scrolling<0&&(this.scrolling=setInterval((()=>this.scroll()),50)):this.scrolling>-1&&(clearInterval(this.scrolling),this.scrolling=-1)}scroll(){this.scrollParent?(this.scrollParent.scrollLeft+=this.scrollSpeed.x,this.scrollParent.scrollTop+=this.scrollSpeed.y):this.view.win.scrollBy(this.scrollSpeed.x,this.scrollSpeed.y),!1===this.dragging&&this.select(this.lastEvent)}skipAtoms(t){let e=null;for(let i=0;i<t.ranges.length;i++){let o=t.ranges[i],n=null;if(o.empty){let t=De(this.atoms,o.from,0);t!=o.from&&(n=s.EditorSelection.cursor(t,-1))}else{let t=De(this.atoms,o.from,-1),e=De(this.atoms,o.to,1);t==o.from&&e==o.to||(n=s.EditorSelection.range(o.from==o.anchor?t:e,o.from==o.head?t:e))}n&&(e||(e=t.ranges.slice()),e[i]=n)}return e?s.EditorSelection.create(e,t.mainIndex):t}select(t){let{view:e}=this,i=this.skipAtoms(this.style.get(t,this.extend,this.multiple));(this.mustSelect||!i.eq(e.state.selection)||i.main.assoc!=e.state.selection.main.assoc&&!1===this.dragging)&&this.view.dispatch({selection:i,userEvent:"select.pointer"}),this.mustSelect=!1}update(t){t.docChanged&&this.dragging&&(this.dragging=this.dragging.map(t.changes)),this.style.update(t)&&setTimeout((()=>this.select(this.lastEvent)),20)}}const Ve=Object.create(null),Fe=Object.create(null),We=j.ie&&j.ie_version<15||j.ios&&j.webkit_version<604;function ze(t,e){let i,{state:o}=t,n=1,r=o.toText(e),l=r.lines==o.selection.ranges.length;if(null!=Qe&&o.selection.ranges.every((t=>t.empty))&&Qe==r.toString()){let t=-1;i=o.changeByRange((i=>{let h=o.doc.lineAt(i.from);if(h.from==t)return{range:i};t=h.from;let a=o.toText((l?r.line(n++).text:e)+o.lineBreak);return{changes:{from:h.from,insert:a},range:s.EditorSelection.cursor(i.from+a.length)}}))}else i=l?o.changeByRange((t=>{let e=r.line(n++);return{changes:{from:t.from,to:t.to,insert:e.text},range:s.EditorSelection.cursor(t.from+e.length)}})):o.replaceSelection(r);t.dispatch(i,{userEvent:"input.paste",scrollIntoView:!0})}function Ie(t,e,i,o){if(1==o)return s.EditorSelection.cursor(e,i);if(2==o)return function(t,e,i=1){let o=t.charCategorizer(e),n=t.doc.lineAt(e),r=e-n.from;if(0==n.length)return s.EditorSelection.cursor(e);0==r?i=1:r==n.length&&(i=-1);let l=r,h=r;i<0?l=(0,s.findClusterBreak)(n.text,r,!1):h=(0,s.findClusterBreak)(n.text,r);let a=o(n.text.slice(l,h));for(;l>0;){let t=(0,s.findClusterBreak)(n.text,l,!1);if(o(n.text.slice(t,l))!=a)break;l=t}for(;h<n.length;){let t=(0,s.findClusterBreak)(n.text,h);if(o(n.text.slice(h,t))!=a)break;h=t}return s.EditorSelection.range(l+n.from,h+n.from)}(t.state,e,i);{let i=rt.find(t.docView,e),o=t.state.doc.lineAt(i?i.posAtEnd:e),n=i?i.posAtStart:o.from,r=i?i.posAtEnd:o.to;return r<t.state.doc.length&&r==o.to&&r++,s.EditorSelection.range(n,r)}}Fe.scroll=t=>{t.inputState.lastScrollTop=t.scrollDOM.scrollTop,t.inputState.lastScrollLeft=t.scrollDOM.scrollLeft},Ve.keydown=(t,e)=>(t.inputState.setSelectionOrigin("select"),27==e.keyCode&&(t.inputState.lastEscPress=Date.now()),!1),Fe.touchstart=(t,e)=>{t.inputState.lastTouchTime=Date.now(),t.inputState.setSelectionOrigin("select.pointer")},Fe.touchmove=t=>{t.inputState.setSelectionOrigin("select.pointer")},Ve.mousedown=(t,e)=>{if(t.observer.flush(),t.inputState.lastTouchTime>Date.now()-2e3)return!1;let i=null;for(let s of t.state.facet(St))if(i=s(t,e),i)break;if(i||0!=e.button||(i=function(t,e){let i=Ye(t,e),o=$e(e),n=t.state.selection;return{update(t){t.docChanged&&(i.pos=t.changes.mapPos(i.pos),n=n.map(t.changes))},get(e,r,l){let h,a=Ye(t,e),c=Ie(t,a.pos,a.bias,o);if(i.pos!=a.pos&&!r){let e=Ie(t,i.pos,i.bias,o),n=Math.min(e.from,c.from),r=Math.max(e.to,c.to);c=n<c.from?s.EditorSelection.range(n,r):s.EditorSelection.range(r,n)}return r?n.replaceRange(n.main.extend(c.from,c.to)):l&&1==o&&n.ranges.length>1&&(h=function(t,e){for(let i=0;i<t.ranges.length;i++){let{from:o,to:n}=t.ranges[i];if(o<=e&&n>=e)return s.EditorSelection.create(t.ranges.slice(0,i).concat(t.ranges.slice(i+1)),t.mainIndex==i?0:t.mainIndex-(t.mainIndex>i?1:0))}return null}(n,a.pos))?h:l?n.addRange(c):s.EditorSelection.create([c])}}}(t,e)),i){let s=!t.hasFocus;t.inputState.startMouseSelection(new Ne(t,e,i,s)),s&&t.observer.ignore((()=>k(t.contentDOM)));let o=t.inputState.mouseSelection;if(o)return o.start(e),!1===o.dragging}return!1};let Ke=(t,e)=>t>=e.top&&t<=e.bottom,qe=(t,e,i)=>Ke(e,i)&&t>=i.left&&t<=i.right;function _e(t,e,i,s){let o=rt.find(t.docView,e);if(!o)return 1;let n=e-o.posAtStart;if(0==n)return 1;if(n==o.length)return-1;let r=o.coordsAt(n,-1);if(r&&qe(i,s,r))return-1;let l=o.coordsAt(n,1);return l&&qe(i,s,l)?1:r&&Ke(s,r)?-1:1}function Ye(t,e){let i=t.posAtCoords({x:e.clientX,y:e.clientY},!1);return{pos:i,bias:_e(t,i,e.clientX,e.clientY)}}const Ge=j.ie&&j.ie_version<=11;let je=null,Xe=0,Ue=0;function $e(t){if(!Ge)return t.detail;let e=je,i=Ue;return je=t,Ue=Date.now(),Xe=!e||i>Date.now()-400&&Math.abs(e.clientX-t.clientX)<2&&Math.abs(e.clientY-t.clientY)<2?(Xe+1)%3:1}function Je(t,e,i,s){if(!i)return;let o=t.posAtCoords({x:e.clientX,y:e.clientY},!1),{mouseSelection:n}=t.inputState,r=s&&n&&n.dragging&&function(t,e){let i=t.state.facet(yt);return i.length?i[0](e):j.mac?!e.altKey:!e.ctrlKey}(t,e)?{from:n.dragging.from,to:n.dragging.to}:null,l={from:o,insert:i},h=t.state.changes(r?[r,l]:l);t.focus(),t.dispatch({changes:h,selection:{anchor:h.mapPos(o,-1),head:h.mapPos(o,1)},userEvent:r?"move.drop":"input.drop"})}Ve.dragstart=(t,e)=>{let{selection:{main:i}}=t.state,{mouseSelection:s}=t.inputState;return s&&(s.dragging=i),e.dataTransfer&&(e.dataTransfer.setData("Text",t.state.sliceDoc(i.from,i.to)),e.dataTransfer.effectAllowed="copyMove"),!1},Ve.drop=(t,e)=>{if(!e.dataTransfer)return!1;if(t.state.readOnly)return!0;let i=e.dataTransfer.files;if(i&&i.length){let s=Array(i.length),o=0,n=()=>{++o==i.length&&Je(t,e,s.filter((t=>null!=t)).join(t.state.lineBreak),!1)};for(let t=0;t<i.length;t++){let e=new FileReader;e.onerror=n,e.onload=()=>{/[\x00-\x08\x0e-\x1f]{2}/.test(e.result)||(s[t]=e.result),n()},e.readAsText(i[t])}return!0}{let i=e.dataTransfer.getData("Text");if(i)return Je(t,e,i,!0),!0}return!1},Ve.paste=(t,e)=>{if(t.state.readOnly)return!0;t.observer.flush();let i=We?null:e.clipboardData;return i?(ze(t,i.getData("text/plain")||i.getData("text/uri-text")),!0):(function(t){let e=t.dom.parentNode;if(!e)return;let i=e.appendChild(document.createElement("textarea"));i.style.cssText="position: fixed; left: -10000px; top: 10px",i.focus(),setTimeout((()=>{t.focus(),i.remove(),ze(t,i.value)}),50)}(t),!1)};let Qe=null;Ve.copy=Ve.cut=(t,e)=>{let{text:i,ranges:s,linewise:o}=function(t){let e=[],i=[],s=!1;for(let s of t.selection.ranges)s.empty||(e.push(t.sliceDoc(s.from,s.to)),i.push(s));if(!e.length){let o=-1;for(let{from:s}of t.selection.ranges){let n=t.doc.lineAt(s);n.number>o&&(e.push(n.text),i.push({from:n.from,to:Math.min(t.doc.length,n.to+1)})),o=n.number}s=!0}return{text:e.join(t.lineBreak),ranges:i,linewise:s}}(t.state);if(!i&&!o)return!1;Qe=o?i:null,"cut"!=e.type||t.state.readOnly||t.dispatch({changes:s,scrollIntoView:!0,userEvent:"delete.cut"});let n=We?null:e.clipboardData;return n?(n.clearData(),n.setData("text/plain",i),!0):(function(t,e){let i=t.dom.parentNode;if(!i)return;let s=i.appendChild(document.createElement("textarea"));s.style.cssText="position: fixed; left: -10000px; top: 10px",s.value=e,s.focus(),s.selectionEnd=e.length,s.selectionStart=0,setTimeout((()=>{s.remove(),t.focus()}),50)}(t,i),!1)};const Ze=s.Annotation.define();function ti(t,e){let i=[];for(let s of t.facet(Ct)){let o=s(t,e);o&&i.push(o)}return i?t.update({effects:i,annotations:Ze.of(!0)}):null}function ei(t){setTimeout((()=>{let e=t.hasFocus;if(e!=t.inputState.notifiedFocused){let i=ti(t.state,e);i?t.dispatch(i):t.update([])}}),10)}Fe.focus=t=>{t.inputState.lastFocusTime=Date.now(),t.scrollDOM.scrollTop||!t.inputState.lastScrollTop&&!t.inputState.lastScrollLeft||(t.scrollDOM.scrollTop=t.inputState.lastScrollTop,t.scrollDOM.scrollLeft=t.inputState.lastScrollLeft),ei(t)},Fe.blur=t=>{t.observer.clearSelectionRange(),ei(t)},Fe.compositionstart=Fe.compositionupdate=t=>{null==t.inputState.compositionFirstChange&&(t.inputState.compositionFirstChange=!0),t.inputState.composing<0&&(t.inputState.composing=0)},Fe.compositionend=t=>{t.inputState.composing=-1,t.inputState.compositionEndedAt=Date.now(),t.inputState.compositionPendingKey=!0,t.inputState.compositionPendingChange=t.observer.pendingRecords().length>0,t.inputState.compositionFirstChange=null,j.chrome&&j.android?t.observer.flushSoon():t.inputState.compositionPendingChange?Promise.resolve().then((()=>t.observer.flush())):setTimeout((()=>{t.inputState.composing<0&&t.docView.hasComposition&&t.update([])}),50)},Fe.contextmenu=t=>{t.inputState.lastContextMenu=Date.now()},Ve.beforeinput=(t,e)=>{var i;let s;if(j.chrome&&j.android&&(s=Be.find((t=>t.inputType==e.inputType)))&&(t.observer.delayAndroidKey(s.key,s.keyCode),"Backspace"==s.key||"Delete"==s.key)){let e=(null===(i=window.visualViewport)||void 0===i?void 0:i.height)||0;setTimeout((()=>{var i;((null===(i=window.visualViewport)||void 0===i?void 0:i.height)||0)>e+10&&t.hasFocus&&(t.contentDOM.blur(),t.focus())}),100)}return!1};const ii=new Set,si=["pre-wrap","normal","pre-line","break-spaces"];class oi{constructor(t){this.lineWrapping=t,this.doc=s.Text.empty,this.heightSamples={},this.lineHeight=14,this.charWidth=7,this.textHeight=14,this.lineLength=30,this.heightChanged=!1}heightForGap(t,e){let i=this.doc.lineAt(e).number-this.doc.lineAt(t).number+1;return this.lineWrapping&&(i+=Math.max(0,Math.ceil((e-t-i*this.lineLength*.5)/this.lineLength))),this.lineHeight*i}heightForLine(t){return this.lineWrapping?(1+Math.max(0,Math.ceil((t-this.lineLength)/(this.lineLength-5))))*this.lineHeight:this.lineHeight}setDoc(t){return this.doc=t,this}mustRefreshForWrapping(t){return si.indexOf(t)>-1!=this.lineWrapping}mustRefreshForHeights(t){let e=!1;for(let i=0;i<t.length;i++){let s=t[i];s<0?i++:this.heightSamples[Math.floor(10*s)]||(e=!0,this.heightSamples[Math.floor(10*s)]=!0)}return e}refresh(t,e,i,s,o,n){let r=si.indexOf(t)>-1,l=Math.round(e)!=Math.round(this.lineHeight)||this.lineWrapping!=r;if(this.lineWrapping=r,this.lineHeight=e,this.charWidth=i,this.textHeight=s,this.lineLength=o,l){this.heightSamples={};for(let t=0;t<n.length;t++){let e=n[t];e<0?t++:this.heightSamples[Math.floor(10*e)]=!0}}return l}}class ni{constructor(t,e){this.from=t,this.heights=e,this.index=0}get more(){return this.index<this.heights.length}}class ri{constructor(t,e,i,s,o){this.from=t,this.length=e,this.top=i,this.height=s,this._content=o}get type(){return"number"==typeof this._content?at.Text:Array.isArray(this._content)?this._content:this._content.type}get to(){return this.from+this.length}get bottom(){return this.top+this.height}get widget(){return this._content instanceof ft?this._content.widget:null}get widgetLineBreaks(){return"number"==typeof this._content?this._content:0}join(t){let e=(Array.isArray(this._content)?this._content:[this]).concat(Array.isArray(t._content)?t._content:[t]);return new ri(this.from,this.length+t.length,this.top,this.height+t.height,e)}}var li=function(t){return t[t.ByPos=0]="ByPos",t[t.ByHeight=1]="ByHeight",t[t.ByPosNoHeight=2]="ByPosNoHeight",t}(li||(li={}));const hi=.001;class ai{constructor(t,e,i=2){this.length=t,this.height=e,this.flags=i}get outdated(){return(2&this.flags)>0}set outdated(t){this.flags=(t?2:0)|-3&this.flags}setHeight(t,e){this.height!=e&&(Math.abs(this.height-e)>hi&&(t.heightChanged=!0),this.height=e)}replace(t,e,i){return ai.of(i)}decomposeLeft(t,e){e.push(this)}decomposeRight(t,e){e.push(this)}applyChanges(t,e,i,s){let o=this,n=i.doc;for(let r=s.length-1;r>=0;r--){let{fromA:l,toA:h,fromB:a,toB:c}=s[r],d=o.lineAt(l,li.ByPosNoHeight,i.setDoc(e),0,0),u=d.to>=h?d:o.lineAt(h,li.ByPosNoHeight,i,0,0);for(c+=u.to-h,h=u.to;r>0&&d.from<=s[r-1].toA;)l=s[r-1].fromA,a=s[r-1].fromB,r--,l<d.from&&(d=o.lineAt(l,li.ByPosNoHeight,i,0,0));a+=d.from-l,l=d.from;let f=gi.build(i.setDoc(n),t,a,c);o=o.replace(l,h,f)}return o.updateHeight(i,0)}static empty(){return new di(0,0)}static of(t){if(1==t.length)return t[0];let e=0,i=t.length,s=0,o=0;for(;;)if(e==i)if(s>2*o){let o=t[e-1];o.break?t.splice(--e,1,o.left,null,o.right):t.splice(--e,1,o.left,o.right),i+=1+o.break,s-=o.size}else{if(!(o>2*s))break;{let e=t[i];e.break?t.splice(i,1,e.left,null,e.right):t.splice(i,1,e.left,e.right),i+=2+e.break,o-=e.size}}else if(s<o){let i=t[e++];i&&(s+=i.size)}else{let e=t[--i];e&&(o+=e.size)}let n=0;return null==t[e-1]?(n=1,e--):null==t[e]&&(n=1,i++),new fi(ai.of(t.slice(0,e)),n,ai.of(t.slice(i)))}}ai.prototype.size=1;class ci extends ai{constructor(t,e,i){super(t,e),this.deco=i}blockAt(t,e,i,s){return new ri(s,this.length,i,this.height,this.deco||0)}lineAt(t,e,i,s,o){return this.blockAt(0,i,s,o)}forEachLine(t,e,i,s,o,n){t<=o+this.length&&e>=o&&n(this.blockAt(0,i,s,o))}updateHeight(t,e=0,i=!1,s){return s&&s.from<=e&&s.more&&this.setHeight(t,s.heights[s.index++]),this.outdated=!1,this}toString(){return`block(${this.length})`}}class di extends ci{constructor(t,e){super(t,e,null),this.collapsed=0,this.widgetHeight=0,this.breaks=0}blockAt(t,e,i,s){return new ri(s,this.length,i,this.height,this.breaks)}replace(t,e,i){let s=i[0];return 1==i.length&&(s instanceof di||s instanceof ui&&4&s.flags)&&Math.abs(this.length-s.length)<10?(s instanceof ui?s=new di(s.length,this.height):s.height=this.height,this.outdated||(s.outdated=!1),s):ai.of(i)}updateHeight(t,e=0,i=!1,s){return s&&s.from<=e&&s.more?this.setHeight(t,s.heights[s.index++]):(i||this.outdated)&&this.setHeight(t,Math.max(this.widgetHeight,t.heightForLine(this.length-this.collapsed))+this.breaks*t.lineHeight),this.outdated=!1,this}toString(){return`line(${this.length}${this.collapsed?-this.collapsed:""}${this.widgetHeight?":"+this.widgetHeight:""})`}}class ui extends ai{constructor(t){super(t,0)}heightMetrics(t,e){let i,s=t.doc.lineAt(e).number,o=t.doc.lineAt(e+this.length).number,n=o-s+1,r=0;if(t.lineWrapping){let e=Math.min(this.height,t.lineHeight*n);i=e/n,this.length>n+1&&(r=(this.height-e)/(this.length-n-1))}else i=this.height/n;return{firstLine:s,lastLine:o,perLine:i,perChar:r}}blockAt(t,e,i,s){let{firstLine:o,lastLine:n,perLine:r,perChar:l}=this.heightMetrics(e,s);if(e.lineWrapping){let o=s+Math.round(Math.max(0,Math.min(1,(t-i)/this.height))*this.length),n=e.doc.lineAt(o),h=r+n.length*l,a=Math.max(i,t-h/2);return new ri(n.from,n.length,a,h,0)}{let s=Math.max(0,Math.min(n-o,Math.floor((t-i)/r))),{from:l,length:h}=e.doc.line(o+s);return new ri(l,h,i+r*s,r,0)}}lineAt(t,e,i,s,o){if(e==li.ByHeight)return this.blockAt(t,i,s,o);if(e==li.ByPosNoHeight){let{from:e,to:s}=i.doc.lineAt(t);return new ri(e,s-e,0,0,0)}let{firstLine:n,perLine:r,perChar:l}=this.heightMetrics(i,o),h=i.doc.lineAt(t),a=r+h.length*l,c=h.number-n,d=s+r*c+l*(h.from-o-c);return new ri(h.from,h.length,Math.max(s,Math.min(d,s+this.height-a)),a,0)}forEachLine(t,e,i,s,o,n){t=Math.max(t,o),e=Math.min(e,o+this.length);let{firstLine:r,perLine:l,perChar:h}=this.heightMetrics(i,o);for(let a=t,c=s;a<=e;){let e=i.doc.lineAt(a);if(a==t){let i=e.number-r;c+=l*i+h*(t-o-i)}let s=l+h*e.length;n(new ri(e.from,e.length,c,s,0)),c+=s,a=e.to+1}}replace(t,e,i){let s=this.length-e;if(s>0){let t=i[i.length-1];t instanceof ui?i[i.length-1]=new ui(t.length+s):i.push(null,new ui(s-1))}if(t>0){let e=i[0];e instanceof ui?i[0]=new ui(t+e.length):i.unshift(new ui(t-1),null)}return ai.of(i)}decomposeLeft(t,e){e.push(new ui(t-1),null)}decomposeRight(t,e){e.push(null,new ui(this.length-t-1))}updateHeight(t,e=0,i=!1,s){let o=e+this.length;if(s&&s.from<=e+this.length&&s.more){let i=[],n=Math.max(e,s.from),r=-1;for(s.from>e&&i.push(new ui(s.from-e-1).updateHeight(t,e));n<=o&&s.more;){let e=t.doc.lineAt(n).length;i.length&&i.push(null);let o=s.heights[s.index++];-1==r?r=o:Math.abs(o-r)>=hi&&(r=-2);let l=new di(e,o);l.outdated=!1,i.push(l),n+=e+1}n<=o&&i.push(null,new ui(o-n).updateHeight(t,n));let l=ai.of(i);return(r<0||Math.abs(l.height-this.height)>=hi||Math.abs(r-this.heightMetrics(t,e).perLine)>=hi)&&(t.heightChanged=!0),l}return(i||this.outdated)&&(this.setHeight(t,t.heightForGap(e,e+this.length)),this.outdated=!1),this}toString(){return`gap(${this.length})`}}class fi extends ai{constructor(t,e,i){super(t.length+e+i.length,t.height+i.height,e|(t.outdated||i.outdated?2:0)),this.left=t,this.right=i,this.size=t.size+i.size}get break(){return 1&this.flags}blockAt(t,e,i,s){let o=i+this.left.height;return t<o?this.left.blockAt(t,e,i,s):this.right.blockAt(t,e,o,s+this.left.length+this.break)}lineAt(t,e,i,s,o){let n=s+this.left.height,r=o+this.left.length+this.break,l=e==li.ByHeight?t<n:t<r,h=l?this.left.lineAt(t,e,i,s,o):this.right.lineAt(t,e,i,n,r);if(this.break||(l?h.to<r:h.from>r))return h;let a=e==li.ByPosNoHeight?li.ByPosNoHeight:li.ByPos;return l?h.join(this.right.lineAt(r,a,i,n,r)):this.left.lineAt(r,a,i,s,o).join(h)}forEachLine(t,e,i,s,o,n){let r=s+this.left.height,l=o+this.left.length+this.break;if(this.break)t<l&&this.left.forEachLine(t,e,i,s,o,n),e>=l&&this.right.forEachLine(t,e,i,r,l,n);else{let h=this.lineAt(l,li.ByPos,i,s,o);t<h.from&&this.left.forEachLine(t,h.from-1,i,s,o,n),h.to>=t&&h.from<=e&&n(h),e>h.to&&this.right.forEachLine(h.to+1,e,i,r,l,n)}}replace(t,e,i){let s=this.left.length+this.break;if(e<s)return this.balanced(this.left.replace(t,e,i),this.right);if(t>this.left.length)return this.balanced(this.left,this.right.replace(t-s,e-s,i));let o=[];t>0&&this.decomposeLeft(t,o);let n=o.length;for(let t of i)o.push(t);if(t>0&&pi(o,n-1),e<this.length){let t=o.length;this.decomposeRight(e,o),pi(o,t)}return ai.of(o)}decomposeLeft(t,e){let i=this.left.length;if(t<=i)return this.left.decomposeLeft(t,e);e.push(this.left),this.break&&(i++,t>=i&&e.push(null)),t>i&&this.right.decomposeLeft(t-i,e)}decomposeRight(t,e){let i=this.left.length,s=i+this.break;if(t>=s)return this.right.decomposeRight(t-s,e);t<i&&this.left.decomposeRight(t,e),this.break&&t<s&&e.push(null),e.push(this.right)}balanced(t,e){return t.size>2*e.size||e.size>2*t.size?ai.of(this.break?[t,null,e]:[t,e]):(this.left=t,this.right=e,this.height=t.height+e.height,this.outdated=t.outdated||e.outdated,this.size=t.size+e.size,this.length=t.length+this.break+e.length,this)}updateHeight(t,e=0,i=!1,s){let{left:o,right:n}=this,r=e+o.length+this.break,l=null;return s&&s.from<=e+o.length&&s.more?l=o=o.updateHeight(t,e,i,s):o.updateHeight(t,e,i),s&&s.from<=r+n.length&&s.more?l=n=n.updateHeight(t,r,i,s):n.updateHeight(t,r,i),l?this.balanced(o,n):(this.height=this.left.height+this.right.height,this.outdated=!1,this)}toString(){return this.left+(this.break?" ":"-")+this.right}}function pi(t,e){let i,s;null==t[e]&&(i=t[e-1])instanceof ui&&(s=t[e+1])instanceof ui&&t.splice(e-1,3,new ui(i.length+1+s.length))}class gi{constructor(t,e){this.pos=t,this.oracle=e,this.nodes=[],this.lineStart=-1,this.lineEnd=-1,this.covering=null,this.writtenTo=t}get isCovered(){return this.covering&&this.nodes[this.nodes.length-1]==this.covering}span(t,e){if(this.lineStart>-1){let t=Math.min(e,this.lineEnd),i=this.nodes[this.nodes.length-1];i instanceof di?i.length+=t-this.pos:(t>this.pos||!this.isCovered)&&this.nodes.push(new di(t-this.pos,-1)),this.writtenTo=t,e>t&&(this.nodes.push(null),this.writtenTo++,this.lineStart=-1)}this.pos=e}point(t,e,i){if(t<e||i.heightRelevant){let s=i.widget?i.widget.estimatedHeight:0,o=i.widget?i.widget.lineBreaks:0;s<0&&(s=this.oracle.lineHeight);let n=e-t;i.block?this.addBlock(new ci(n,s,i)):(n||o||s>=5)&&this.addLineDeco(s,o,n)}else e>t&&this.span(t,e);this.lineEnd>-1&&this.lineEnd<this.pos&&(this.lineEnd=this.oracle.doc.lineAt(this.pos).to)}enterLine(){if(this.lineStart>-1)return;let{from:t,to:e}=this.oracle.doc.lineAt(this.pos);this.lineStart=t,this.lineEnd=e,this.writtenTo<t&&((this.writtenTo<t-1||null==this.nodes[this.nodes.length-1])&&this.nodes.push(this.blankContent(this.writtenTo,t-1)),this.nodes.push(null)),this.pos>t&&this.nodes.push(new di(this.pos-t,-1)),this.writtenTo=this.pos}blankContent(t,e){let i=new ui(e-t);return this.oracle.doc.lineAt(t).to==e&&(i.flags|=4),i}ensureLine(){this.enterLine();let t=this.nodes.length?this.nodes[this.nodes.length-1]:null;if(t instanceof di)return t;let e=new di(0,-1);return this.nodes.push(e),e}addBlock(t){this.enterLine();let e=t.deco;e&&e.startSide>0&&!this.isCovered&&this.ensureLine(),this.nodes.push(t),this.writtenTo=this.pos=this.pos+t.length,e&&e.endSide>0&&(this.covering=t)}addLineDeco(t,e,i){let s=this.ensureLine();s.length+=i,s.collapsed+=i,s.widgetHeight=Math.max(s.widgetHeight,t),s.breaks+=e,this.writtenTo=this.pos=this.pos+i}finish(t){let e=0==this.nodes.length?null:this.nodes[this.nodes.length-1];!(this.lineStart>-1)||e instanceof di||this.isCovered?(this.writtenTo<this.pos||null==e)&&this.nodes.push(this.blankContent(this.writtenTo,this.pos)):this.nodes.push(new di(0,-1));let i=t;for(let t of this.nodes)t instanceof di&&t.updateHeight(this.oracle,i),i+=t?t.length:1;return this.nodes}static build(t,e,i,o){let n=new gi(i,t);return s.RangeSet.spans(e,i,o,n,0),n.finish(i)}}class mi{constructor(){this.changes=[]}compareRange(){}comparePoint(t,e,i,s){(t<e||i&&i.heightRelevant||s&&s.heightRelevant)&&gt(t,e,this.changes,5)}}function wi(t,e){let i=t.getBoundingClientRect(),s=t.ownerDocument,o=s.defaultView||window,n=Math.max(0,i.left),r=Math.min(o.innerWidth,i.right),l=Math.max(0,i.top),h=Math.min(o.innerHeight,i.bottom);for(let e=t.parentNode;e&&e!=s.body;)if(1==e.nodeType){let i=e,s=window.getComputedStyle(i);if((i.scrollHeight>i.clientHeight||i.scrollWidth>i.clientWidth)&&"visible"!=s.overflow){let s=i.getBoundingClientRect();n=Math.max(n,s.left),r=Math.min(r,s.right),l=Math.max(l,s.top),h=e==t.parentNode?s.bottom:Math.min(h,s.bottom)}e="absolute"==s.position||"fixed"==s.position?i.offsetParent:i.parentNode}else{if(11!=e.nodeType)break;e=e.host}return{left:n-i.left,right:Math.max(n,r)-i.left,top:l-(i.top+e),bottom:Math.max(l,h)-(i.top+e)}}function vi(t,e){let i=t.getBoundingClientRect();return{left:0,right:i.right-i.left,top:e,bottom:i.bottom-(i.top+e)}}class bi{constructor(t,e,i){this.from=t,this.to=e,this.size=i}static same(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++){let s=t[i],o=e[i];if(s.from!=o.from||s.to!=o.to||s.size!=o.size)return!1}return!0}draw(t,e){return ct.replace({widget:new yi(this.size*(e?t.scaleY:t.scaleX),e)}).range(this.from,this.to)}}class yi extends ht{constructor(t,e){super(),this.size=t,this.vertical=e}eq(t){return t.size==this.size&&t.vertical==this.vertical}toDOM(){let t=document.createElement("div");return this.vertical?t.style.height=this.size+"px":(t.style.width=this.size+"px",t.style.height="2px",t.style.display="inline-block"),t}get estimatedHeight(){return this.vertical?this.size:-1}}class Si{constructor(t){this.state=t,this.pixelViewport={left:0,right:window.innerWidth,top:0,bottom:0},this.inView=!0,this.paddingTop=0,this.paddingBottom=0,this.contentDOMWidth=0,this.contentDOMHeight=0,this.editorHeight=0,this.editorWidth=0,this.scrollTop=0,this.scrolledToBottom=!0,this.scaleX=1,this.scaleY=1,this.scrollAnchorPos=0,this.scrollAnchorHeight=-1,this.scaler=Ai,this.scrollTarget=null,this.printing=!1,this.mustMeasureContent=!0,this.defaultTextDirection=jt.LTR,this.visibleRanges=[],this.mustEnforceCursorAssoc=!1;let e=t.facet(Vt).some((t=>"function"!=typeof t&&"cm-lineWrapping"==t.class));this.heightOracle=new oi(e),this.stateDeco=t.facet(Ft).filter((t=>"function"!=typeof t)),this.heightMap=ai.empty().applyChanges(this.stateDeco,s.Text.empty,this.heightOracle.setDoc(t.doc),[new Yt(0,0,0,t.doc.length)]),this.viewport=this.getViewport(0,null),this.updateViewportLines(),this.updateForViewport(),this.lineGaps=this.ensureLineGaps([]),this.lineGapDeco=ct.set(this.lineGaps.map((t=>t.draw(this,!1)))),this.computeVisibleRanges()}updateForViewport(){let t=[this.viewport],{main:e}=this.state.selection;for(let i=0;i<=1;i++){let s=i?e.head:e.anchor;if(!t.some((({from:t,to:e})=>s>=t&&s<=e))){let{from:e,to:i}=this.lineBlockAt(s);t.push(new xi(e,i))}}this.viewports=t.sort(((t,e)=>t.from-e.from)),this.scaler=this.heightMap.height<=7e6?Ai:new Di(this.heightOracle,this.heightMap,this.viewports)}updateViewportLines(){this.viewportLines=[],this.heightMap.forEachLine(this.viewport.from,this.viewport.to,this.heightOracle.setDoc(this.state.doc),0,0,(t=>{this.viewportLines.push(1==this.scaler.scale?t:Ti(t,this.scaler))}))}update(t,e=null){this.state=t.state;let i=this.stateDeco;this.stateDeco=this.state.facet(Ft).filter((t=>"function"!=typeof t));let o=t.changedRanges,n=Yt.extendWithRanges(o,function(t,e,i){let o=new mi;return s.RangeSet.compare(t,e,i,o,0),o.changes}(i,this.stateDeco,t?t.changes:s.ChangeSet.empty(this.state.doc.length))),r=this.heightMap.height,l=this.scrolledToBottom?null:this.scrollAnchorAt(this.scrollTop);this.heightMap=this.heightMap.applyChanges(this.stateDeco,t.startState.doc,this.heightOracle.setDoc(this.state.doc),n),this.heightMap.height!=r&&(t.flags|=2),l?(this.scrollAnchorPos=t.changes.mapPos(l.from,-1),this.scrollAnchorHeight=l.top):(this.scrollAnchorPos=-1,this.scrollAnchorHeight=this.heightMap.height);let h=n.length?this.mapViewport(this.viewport,t.changes):this.viewport;(e&&(e.range.head<h.from||e.range.head>h.to)||!this.viewportIsAppropriate(h))&&(h=this.getViewport(0,e));let a=!t.changes.empty||2&t.flags||h.from!=this.viewport.from||h.to!=this.viewport.to;this.viewport=h,this.updateForViewport(),a&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(this.mapLineGaps(this.lineGaps,t.changes))),t.flags|=this.computeVisibleRanges(),e&&(this.scrollTarget=e),!this.mustEnforceCursorAssoc&&t.selectionSet&&t.view.lineWrapping&&t.state.selection.main.empty&&t.state.selection.main.assoc&&!t.state.facet(Dt)&&(this.mustEnforceCursorAssoc=!0)}measure(t){let e=t.contentDOM,i=window.getComputedStyle(e),o=this.heightOracle,n=i.whiteSpace;this.defaultTextDirection="rtl"==i.direction?jt.RTL:jt.LTR;let r=this.heightOracle.mustRefreshForWrapping(n),l=e.getBoundingClientRect(),h=r||this.mustMeasureContent||this.contentDOMHeight!=l.height;this.contentDOMHeight=l.height,this.mustMeasureContent=!1;let a=0,c=0;if(l.width&&l.height){let t=l.width/e.offsetWidth,i=l.height/e.offsetHeight;(t>.995&&t<1.005||!isFinite(t)||Math.abs(l.width-e.offsetWidth)<1)&&(t=1),(i>.995&&i<1.005||!isFinite(i)||Math.abs(l.height-e.offsetHeight)<1)&&(i=1),this.scaleX==t&&this.scaleY==i||(this.scaleX=t,this.scaleY=i,a|=8,r=h=!0)}let d=(parseInt(i.paddingTop)||0)*this.scaleY,u=(parseInt(i.paddingBottom)||0)*this.scaleY;this.paddingTop==d&&this.paddingBottom==u||(this.paddingTop=d,this.paddingBottom=u,a|=10),this.editorWidth!=t.scrollDOM.clientWidth&&(o.lineWrapping&&(h=!0),this.editorWidth=t.scrollDOM.clientWidth,a|=8);let f=t.scrollDOM.scrollTop*this.scaleY;this.scrollTop!=f&&(this.scrollAnchorHeight=-1,this.scrollTop=f),this.scrolledToBottom=T(t.scrollDOM);let p=(this.printing?vi:wi)(e,this.paddingTop),g=p.top-this.pixelViewport.top,m=p.bottom-this.pixelViewport.bottom;this.pixelViewport=p;let w=this.pixelViewport.bottom>this.pixelViewport.top&&this.pixelViewport.right>this.pixelViewport.left;if(w!=this.inView&&(this.inView=w,w&&(h=!0)),!this.inView&&!this.scrollTarget)return 0;let v=l.width;if(this.contentDOMWidth==v&&this.editorHeight==t.scrollDOM.clientHeight||(this.contentDOMWidth=l.width,this.editorHeight=t.scrollDOM.clientHeight,a|=8),h){let e=t.docView.measureVisibleLineHeights(this.viewport);if(o.mustRefreshForHeights(e)&&(r=!0),r||o.lineWrapping&&Math.abs(v-this.contentDOMWidth)>o.charWidth){let{lineHeight:i,charWidth:s,textHeight:l}=t.docView.measureTextSize();r=i>0&&o.refresh(n,i,s,l,v/s,e),r&&(t.docView.minWidth=0,a|=8)}g>0&&m>0?c=Math.max(g,m):g<0&&m<0&&(c=Math.min(g,m)),o.heightChanged=!1;for(let i of this.viewports){let n=i.from==this.viewport.from?e:t.docView.measureVisibleLineHeights(i);this.heightMap=(r?ai.empty().applyChanges(this.stateDeco,s.Text.empty,this.heightOracle,[new Yt(0,0,0,t.state.doc.length)]):this.heightMap).updateHeight(o,0,r,new ni(i.from,n))}o.heightChanged&&(a|=2)}let b=!this.viewportIsAppropriate(this.viewport,c)||this.scrollTarget&&(this.scrollTarget.range.head<this.viewport.from||this.scrollTarget.range.head>this.viewport.to);return b&&(this.viewport=this.getViewport(c,this.scrollTarget)),this.updateForViewport(),(2&a||b)&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(r?[]:this.lineGaps,t)),a|=this.computeVisibleRanges(),this.mustEnforceCursorAssoc&&(this.mustEnforceCursorAssoc=!1,t.docView.enforceCursorAssoc()),a}get visibleTop(){return this.scaler.fromDOM(this.pixelViewport.top)}get visibleBottom(){return this.scaler.fromDOM(this.pixelViewport.bottom)}getViewport(t,e){let i=.5-Math.max(-.5,Math.min(.5,t/1e3/2)),s=this.heightMap,o=this.heightOracle,{visibleTop:n,visibleBottom:r}=this,l=new xi(s.lineAt(n-1e3*i,li.ByHeight,o,0,0).from,s.lineAt(r+1e3*(1-i),li.ByHeight,o,0,0).to);if(e){let{head:t}=e.range;if(t<l.from||t>l.to){let i,n=Math.min(this.editorHeight,this.pixelViewport.bottom-this.pixelViewport.top),r=s.lineAt(t,li.ByPos,o,0,0);i="center"==e.y?(r.top+r.bottom)/2-n/2:"start"==e.y||"nearest"==e.y&&t<l.from?r.top:r.bottom-n,l=new xi(s.lineAt(i-500,li.ByHeight,o,0,0).from,s.lineAt(i+n+500,li.ByHeight,o,0,0).to)}}return l}mapViewport(t,e){let i=e.mapPos(t.from,-1),s=e.mapPos(t.to,1);return new xi(this.heightMap.lineAt(i,li.ByPos,this.heightOracle,0,0).from,this.heightMap.lineAt(s,li.ByPos,this.heightOracle,0,0).to)}viewportIsAppropriate({from:t,to:e},i=0){if(!this.inView)return!0;let{top:s}=this.heightMap.lineAt(t,li.ByPos,this.heightOracle,0,0),{bottom:o}=this.heightMap.lineAt(e,li.ByPos,this.heightOracle,0,0),{visibleTop:n,visibleBottom:r}=this;return(0==t||s<=n-Math.max(10,Math.min(-i,250)))&&(e==this.state.doc.length||o>=r+Math.max(10,Math.min(i,250)))&&s>n-2e3&&o<r+2e3}mapLineGaps(t,e){if(!t.length||e.empty)return t;let i=[];for(let s of t)e.touchesRange(s.from,s.to)||i.push(new bi(e.mapPos(s.from),e.mapPos(s.to),s.size));return i}ensureLineGaps(t,e){let i=this.heightOracle.lineWrapping,o=i?1e4:2e3,n=o>>1,r=o<<1;if(this.defaultTextDirection!=jt.LTR&&!i)return[];let l=[],h=(o,r,a,c)=>{if(r-o<n)return;let d=this.state.selection.main,u=[d.from];d.empty||u.push(d.to);for(let t of u)if(t>o&&t<r)return h(o,t-10,a,c),void h(t+10,r,a,c);let f=function(t,e){for(let i of t)if(e(i))return i}(t,(t=>t.from>=a.from&&t.to<=a.to&&Math.abs(t.from-o)<n&&Math.abs(t.to-r)<n&&!u.some((e=>t.from<e&&t.to>e))));if(!f){if(r<a.to&&e&&i&&e.visibleRanges.some((t=>t.from<=r&&t.to>=r))){let t=e.moveToLineBoundary(s.EditorSelection.cursor(r),!1,!0).head;t>o&&(r=t)}f=new bi(o,r,this.gapSize(a,o,r,c))}l.push(f)};for(let t of this.viewportLines){if(t.length<r)continue;let e=Mi(t.from,t.to,this.stateDeco);if(e.total<r)continue;let s,n,l=this.scrollTarget?this.scrollTarget.range.head:null;if(i){let i,r,h=o/this.heightOracle.lineLength*this.heightOracle.lineHeight;if(null!=l){let s=Ci(e,l),o=((this.visibleBottom-this.visibleTop)/2+h)/t.height;i=s-o,r=s+o}else i=(this.visibleTop-t.top-h)/t.height,r=(this.visibleBottom-t.top+h)/t.height;s=ki(e,i),n=ki(e,r)}else{let t,i,r=e.total*this.heightOracle.charWidth,h=o*this.heightOracle.charWidth;if(null!=l){let s=Ci(e,l),o=((this.pixelViewport.right-this.pixelViewport.left)/2+h)/r;t=s-o,i=s+o}else t=(this.pixelViewport.left-h)/r,i=(this.pixelViewport.right+h)/r;s=ki(e,t),n=ki(e,i)}s>t.from&&h(t.from,s,t,e),n<t.to&&h(n,t.to,t,e)}return l}gapSize(t,e,i,s){let o=Ci(s,i)-Ci(s,e);return this.heightOracle.lineWrapping?t.height*o:s.total*this.heightOracle.charWidth*o}updateLineGaps(t){bi.same(t,this.lineGaps)||(this.lineGaps=t,this.lineGapDeco=ct.set(t.map((t=>t.draw(this,this.heightOracle.lineWrapping)))))}computeVisibleRanges(){let t=this.stateDeco;this.lineGaps.length&&(t=t.concat(this.lineGapDeco));let e=[];s.RangeSet.spans(t,this.viewport.from,this.viewport.to,{span(t,i){e.push({from:t,to:i})},point(){}},20);let i=e.length!=this.visibleRanges.length||this.visibleRanges.some(((t,i)=>t.from!=e[i].from||t.to!=e[i].to));return this.visibleRanges=e,i?4:0}lineBlockAt(t){return t>=this.viewport.from&&t<=this.viewport.to&&this.viewportLines.find((e=>e.from<=t&&e.to>=t))||Ti(this.heightMap.lineAt(t,li.ByPos,this.heightOracle,0,0),this.scaler)}lineBlockAtHeight(t){return Ti(this.heightMap.lineAt(this.scaler.fromDOM(t),li.ByHeight,this.heightOracle,0,0),this.scaler)}scrollAnchorAt(t){let e=this.lineBlockAtHeight(t+8);return e.from>=this.viewport.from||this.viewportLines[0].top-t>200?e:this.viewportLines[0]}elementAtHeight(t){return Ti(this.heightMap.blockAt(this.scaler.fromDOM(t),this.heightOracle,0,0),this.scaler)}get docHeight(){return this.scaler.toDOM(this.heightMap.height)}get contentHeight(){return this.docHeight+this.paddingTop+this.paddingBottom}}class xi{constructor(t,e){this.from=t,this.to=e}}function Mi(t,e,i){let o=[],n=t,r=0;return s.RangeSet.spans(i,t,e,{span(){},point(t,e){t>n&&(o.push({from:n,to:t}),r+=t-n),n=e}},20),n<e&&(o.push({from:n,to:e}),r+=e-n),{total:r,ranges:o}}function ki({total:t,ranges:e},i){if(i<=0)return e[0].from;if(i>=1)return e[e.length-1].to;let s=Math.floor(t*i);for(let t=0;;t++){let{from:i,to:o}=e[t],n=o-i;if(s<=n)return i+s;s-=n}}function Ci(t,e){let i=0;for(let{from:s,to:o}of t.ranges){if(e<=o){i+=e-s;break}i+=o-s}return i/t.total}const Ai={toDOM:t=>t,fromDOM:t=>t,scale:1};class Di{constructor(t,e,i){let s=0,o=0,n=0;this.viewports=i.map((({from:i,to:o})=>{let n=e.lineAt(i,li.ByPos,t,0,0).top,r=e.lineAt(o,li.ByPos,t,0,0).bottom;return s+=r-n,{from:i,to:o,top:n,bottom:r,domTop:0,domBottom:0}})),this.scale=(7e6-s)/(e.height-s);for(let t of this.viewports)t.domTop=n+(t.top-o)*this.scale,n=t.domBottom=t.domTop+(t.bottom-t.top),o=t.bottom}toDOM(t){for(let e=0,i=0,s=0;;e++){let o=e<this.viewports.length?this.viewports[e]:null;if(!o||t<o.top)return s+(t-i)*this.scale;if(t<=o.bottom)return o.domTop+(t-o.top);i=o.bottom,s=o.domBottom}}fromDOM(t){for(let e=0,i=0,s=0;;e++){let o=e<this.viewports.length?this.viewports[e]:null;if(!o||t<o.domTop)return i+(t-s)/this.scale;if(t<=o.domBottom)return o.top+(t-o.domTop);i=o.bottom,s=o.domBottom}}}function Ti(t,e){if(1==e.scale)return t;let i=e.toDOM(t.top),s=e.toDOM(t.bottom);return new ri(t.from,t.length,i,s-i,Array.isArray(t._content)?t._content.map((t=>Ti(t,e))):t._content)}const Oi=s.Facet.define({combine:t=>t.join(" ")}),Ei=s.Facet.define({combine:t=>t.indexOf(!0)>-1}),Ri=o.V.newName(),Bi=o.V.newName(),Li=o.V.newName(),Hi={"&light":"."+Bi,"&dark":"."+Li};function Pi(t,e,i){return new o.V(e,{finish:e=>/&/.test(e)?e.replace(/&\w*/,(e=>{if("&"==e)return t;if(!i||!i[e])throw new RangeError(`Unsupported selector: ${e}`);return i[e]})):t+" "+e})}const Ni=Pi("."+Ri,{"&":{position:"relative !important",boxSizing:"border-box","&.cm-focused":{outline:"1px dotted #212121"},display:"flex !important",flexDirection:"column"},".cm-scroller":{display:"flex !important",alignItems:"flex-start !important",fontFamily:"monospace",lineHeight:1.4,height:"100%",overflowX:"auto",position:"relative",zIndex:0},".cm-content":{margin:0,flexGrow:2,flexShrink:0,display:"block",whiteSpace:"pre",wordWrap:"normal",boxSizing:"border-box",minHeight:"100%",padding:"4px 0",outline:"none","&[contenteditable=true]":{WebkitUserModify:"read-write-plaintext-only"}},".cm-lineWrapping":{whiteSpace_fallback:"pre-wrap",whiteSpace:"break-spaces",wordBreak:"break-word",overflowWrap:"anywhere",flexShrink:1},"&light .cm-content":{caretColor:"black"},"&dark .cm-content":{caretColor:"white"},".cm-line":{display:"block",padding:"0 2px 0 6px"},".cm-layer":{position:"absolute",left:0,top:0,contain:"size style","& > *":{position:"absolute"}},"&light .cm-selectionBackground":{background:"#d9d9d9"},"&dark .cm-selectionBackground":{background:"#222"},"&light.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#d7d4f0"},"&dark.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#233"},".cm-cursorLayer":{pointerEvents:"none"},"&.cm-focused > .cm-scroller > .cm-cursorLayer":{animation:"steps(1) cm-blink 1.2s infinite"},"@keyframes cm-blink":{"0%":{},"50%":{opacity:0},"100%":{}},"@keyframes cm-blink2":{"0%":{},"50%":{opacity:0},"100%":{}},".cm-cursor, .cm-dropCursor":{borderLeft:"1.2px solid black",marginLeft:"-0.6px",pointerEvents:"none"},".cm-cursor":{display:"none"},"&dark .cm-cursor":{borderLeftColor:"#444"},".cm-dropCursor":{position:"absolute"},"&.cm-focused > .cm-scroller > .cm-cursorLayer .cm-cursor":{display:"block"},"&light .cm-activeLine":{backgroundColor:"#cceeff44"},"&dark .cm-activeLine":{backgroundColor:"#99eeff33"},"&light .cm-specialChar":{color:"red"},"&dark .cm-specialChar":{color:"#f78"},".cm-gutters":{flexShrink:0,display:"flex",height:"100%",boxSizing:"border-box",insetInlineStart:0,zIndex:200},"&light .cm-gutters":{backgroundColor:"#f5f5f5",color:"#6c6c6c",borderRight:"1px solid #ddd"},"&dark .cm-gutters":{backgroundColor:"#333338",color:"#ccc"},".cm-gutter":{display:"flex !important",flexDirection:"column",flexShrink:0,boxSizing:"border-box",minHeight:"100%",overflow:"hidden"},".cm-gutterElement":{boxSizing:"border-box"},".cm-lineNumbers .cm-gutterElement":{padding:"0 3px 0 5px",minWidth:"20px",textAlign:"right",whiteSpace:"nowrap"},"&light .cm-activeLineGutter":{backgroundColor:"#e2f2ff"},"&dark .cm-activeLineGutter":{backgroundColor:"#222227"},".cm-panels":{boxSizing:"border-box",position:"sticky",left:0,right:0},"&light .cm-panels":{backgroundColor:"#f5f5f5",color:"black"},"&light .cm-panels-top":{borderBottom:"1px solid #ddd"},"&light .cm-panels-bottom":{borderTop:"1px solid #ddd"},"&dark .cm-panels":{backgroundColor:"#333338",color:"white"},".cm-tab":{display:"inline-block",overflow:"hidden",verticalAlign:"bottom"},".cm-widgetBuffer":{verticalAlign:"text-top",height:"1em",width:0,display:"inline"},".cm-placeholder":{color:"#888",display:"inline-block",verticalAlign:"top"},".cm-highlightSpace:before":{content:"attr(data-display)",position:"absolute",pointerEvents:"none",color:"#888"},".cm-highlightTab":{backgroundImage:'url(\'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="20"><path stroke="%23888" stroke-width="1" fill="none" d="M1 10H196L190 5M190 15L196 10M197 4L197 16"/></svg>\')',backgroundSize:"auto 100%",backgroundPosition:"right 90%",backgroundRepeat:"no-repeat"},".cm-trailingSpace":{backgroundColor:"#ff332255"},".cm-button":{verticalAlign:"middle",color:"inherit",fontSize:"70%",padding:".2em 1em",borderRadius:"1px"},"&light .cm-button":{backgroundImage:"linear-gradient(#eff1f5, #d9d9df)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#b4b4b4, #d0d3d6)"}},"&dark .cm-button":{backgroundImage:"linear-gradient(#393939, #111)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#111, #333)"}},".cm-textfield":{verticalAlign:"middle",color:"inherit",fontSize:"70%",border:"1px solid silver",padding:".2em .5em"},"&light .cm-textfield":{backgroundColor:"white"},"&dark .cm-textfield":{border:"1px solid #555",backgroundColor:"inherit"}},Hi),Vi="￿";class Fi{constructor(t,e){this.points=t,this.text="",this.lineSeparator=e.facet(s.EditorState.lineSeparator)}append(t){this.text+=t}lineBreak(){this.text+=Vi}readRange(t,e){if(!t)return this;let i=t.parentNode;for(let s=t;;){this.findPointBefore(i,s);let t=this.text.length;this.readNode(s);let o=s.nextSibling;if(o==e)break;let n=R.get(s),r=R.get(o);(n&&r?n.breakAfter:(n?n.breakAfter:zi(s))||zi(o)&&("BR"!=s.nodeName||s.cmIgnore)&&this.text.length>t)&&this.lineBreak(),s=o}return this.findPointBefore(i,e),this}readTextNode(t){let e=t.nodeValue;for(let i of this.points)i.node==t&&(i.pos=this.text.length+Math.min(i.offset,e.length));for(let i=0,s=this.lineSeparator?null:/\r\n?|\n/g;;){let o,n=-1,r=1;if(this.lineSeparator?(n=e.indexOf(this.lineSeparator,i),r=this.lineSeparator.length):(o=s.exec(e))&&(n=o.index,r=o[0].length),this.append(e.slice(i,n<0?e.length:n)),n<0)break;if(this.lineBreak(),r>1)for(let e of this.points)e.node==t&&e.pos>this.text.length&&(e.pos-=r-1);i=n+r}}readNode(t){if(t.cmIgnore)return;let e=R.get(t),i=e&&e.overrideDOMText;if(null!=i){this.findPointInside(t,i.length);for(let t=i.iter();!t.next().done;)t.lineBreak?this.lineBreak():this.append(t.value)}else 3==t.nodeType?this.readTextNode(t):"BR"==t.nodeName?t.nextSibling&&this.lineBreak():1==t.nodeType&&this.readRange(t.firstChild,null)}findPointBefore(t,e){for(let i of this.points)i.node==t&&t.childNodes[i.offset]==e&&(i.pos=this.text.length)}findPointInside(t,e){for(let i of this.points)(3==t.nodeType?i.node==t:t.contains(i.node))&&(i.pos=this.text.length+(Wi(t,i.node,i.offset)?e:0))}}function Wi(t,e,i){for(;;){if(!e||i<v(e))return!1;if(e==t)return!0;i=m(e)+1,e=e.parentNode}}function zi(t){return 1==t.nodeType&&/^(DIV|P|LI|UL|OL|BLOCKQUOTE|DD|DT|H\d|SECTION|PRE)$/.test(t.nodeName)}class Ii{constructor(t,e){this.node=t,this.offset=e,this.pos=-1}}class Ki{constructor(t,e,i,o){this.typeOver=o,this.bounds=null,this.text="";let{impreciseHead:n,impreciseAnchor:r}=t.docView;if(t.state.readOnly&&e>-1)this.newSel=null;else if(e>-1&&(this.bounds=t.docView.domBoundsAround(e,i,0))){let e=n||r?[]:function(t){let e=[];if(t.root.activeElement!=t.contentDOM)return e;let{anchorNode:i,anchorOffset:s,focusNode:o,focusOffset:n}=t.observer.selectionRange;return i&&(e.push(new Ii(i,s)),o==i&&n==s||e.push(new Ii(o,n))),e}(t),i=new Fi(e,t.state);i.readRange(this.bounds.startDOM,this.bounds.endDOM),this.text=i.text,this.newSel=function(t,e){if(0==t.length)return null;let i=t[0].pos,o=2==t.length?t[1].pos:i;return i>-1&&o>-1?s.EditorSelection.single(i+e,o+e):null}(e,this.bounds.from)}else{let e=t.observer.selectionRange,i=n&&n.node==e.focusNode&&n.offset==e.focusOffset||!u(t.contentDOM,e.focusNode)?t.state.selection.main.head:t.docView.posFromDOM(e.focusNode,e.focusOffset),o=r&&r.node==e.anchorNode&&r.offset==e.anchorOffset||!u(t.contentDOM,e.anchorNode)?t.state.selection.main.anchor:t.docView.posFromDOM(e.anchorNode,e.anchorOffset);this.newSel=s.EditorSelection.single(o,i)}}}function qi(t,e){let i,{newSel:o}=e,n=t.state.selection.main,r=t.inputState.lastKeyTime>Date.now()-100?t.inputState.lastKeyCode:-1;if(e.bounds){let{from:o,to:l}=e.bounds,h=n.from,a=null;(8===r||j.android&&e.text.length<l-o)&&(h=n.to,a="end");let c=function(t,e,i,s){let o=Math.min(t.length,e.length),n=0;for(;n<o&&t.charCodeAt(n)==e.charCodeAt(n);)n++;if(n==o&&t.length==e.length)return null;let r=t.length,l=e.length;for(;r>0&&l>0&&t.charCodeAt(r-1)==e.charCodeAt(l-1);)r--,l--;return"end"==s&&(i-=r+Math.max(0,n-Math.min(r,l))-n),r<n&&t.length<e.length?(n-=i<=n&&i>=r?n-i:0,l=n+(l-r),r=n):l<n&&(n-=i<=n&&i>=l?n-i:0,r=n+(r-l),l=n),{from:n,toA:r,toB:l}}(t.state.doc.sliceString(o,l,Vi),e.text,h-o,a);c&&(j.chrome&&13==r&&c.toB==c.from+2&&e.text.slice(c.from,c.toB)==Vi+Vi&&c.toB--,i={from:o+c.from,to:o+c.toA,insert:s.Text.of(e.text.slice(c.from,c.toB).split(Vi))})}else o&&(!t.hasFocus&&t.state.facet(Rt)||o.main.eq(n))&&(o=null);if(!i&&!o)return!1;if(!i&&e.typeOver&&!n.empty&&o&&o.main.empty?i={from:n.from,to:n.to,insert:t.state.doc.slice(n.from,n.to)}:i&&i.from>=n.from&&i.to<=n.to&&(i.from!=n.from||i.to!=n.to)&&n.to-n.from-(i.to-i.from)<=4?i={from:n.from,to:n.to,insert:t.state.doc.slice(n.from,i.from).append(i.insert).append(t.state.doc.slice(i.to,n.to))}:(j.mac||j.android)&&i&&i.from==i.to&&i.from==n.head-1&&/^\. ?$/.test(i.insert.toString())&&"off"==t.contentDOM.getAttribute("autocorrect")?(o&&2==i.insert.length&&(o=s.EditorSelection.single(o.main.anchor-1,o.main.head-1)),i={from:n.from,to:n.to,insert:s.Text.of([" "])}):j.chrome&&i&&i.from==i.to&&i.from==n.head&&"\n "==i.insert.toString()&&t.lineWrapping&&(o&&(o=s.EditorSelection.single(o.main.anchor-1,o.main.head-1)),i={from:n.from,to:n.to,insert:s.Text.of([" "])}),i){if(j.ios&&t.inputState.flushIOSKey())return!0;if(j.android&&(i.from==n.from&&i.to==n.to&&1==i.insert.length&&2==i.insert.lines&&A(t.contentDOM,"Enter",13)||(i.from==n.from-1&&i.to==n.to&&0==i.insert.length||8==r&&i.insert.length<i.to-i.from&&i.to>n.head)&&A(t.contentDOM,"Backspace",8)||i.from==n.from&&i.to==n.to+1&&0==i.insert.length&&A(t.contentDOM,"Delete",46)))return!0;let e,l=i.insert.toString();t.inputState.composing>=0&&t.inputState.composing++;let h=()=>e||(e=function(t,e,i){let o,n=t.state,r=n.selection.main;if(e.from>=r.from&&e.to<=r.to&&e.to-e.from>=(r.to-r.from)/3&&(!i||i.main.empty&&i.main.from==e.from+e.insert.length)&&t.inputState.composing<0){let i=r.from<e.from?n.sliceDoc(r.from,e.from):"",s=r.to>e.to?n.sliceDoc(e.to,r.to):"";o=n.replaceSelection(t.state.toText(i+e.insert.sliceString(0,void 0,t.state.lineBreak)+s))}else{let l=n.changes(e),h=i&&i.main.to<=l.newLength?i.main:void 0;if(n.selection.ranges.length>1&&t.inputState.composing>=0&&e.to<=r.to&&e.to>=r.to-10){let a,c=t.state.sliceDoc(e.from,e.to),d=i&&fe(t,i.main.head);if(d){let t=e.insert.length-(e.to-e.from);a={from:d.from,to:d.to-t}}else a=t.state.doc.lineAt(r.head);let u=r.to-e.to,f=r.to-r.from;o=n.changeByRange((i=>{if(i.from==r.from&&i.to==r.to)return{changes:l,range:h||i.map(l)};let o=i.to-u,d=o-c.length;if(i.to-i.from!=f||t.state.sliceDoc(d,o)!=c||i.to>=a.from&&i.from<=a.to)return{range:i};let p=n.changes({from:d,to:o,insert:e.insert}),g=i.to-r.to;return{changes:p,range:h?s.EditorSelection.range(Math.max(0,h.anchor+g),Math.max(0,h.head+g)):i.map(p)}}))}else o={changes:l,selection:h&&n.selection.replaceRange(h)}}let l="input.type";return(t.composing||t.inputState.compositionPendingChange&&t.inputState.compositionEndedAt>Date.now()-50)&&(t.inputState.compositionPendingChange=!1,l+=".compose",t.inputState.compositionFirstChange&&(l+=".start",t.inputState.compositionFirstChange=!1)),n.update(o,{userEvent:l,scrollIntoView:!0})}(t,i,o));return t.state.facet(kt).some((e=>e(t,i.from,i.to,l,h)))||t.dispatch(h()),!0}if(o&&!o.main.eq(n)){let e=!1,i="select";return t.inputState.lastSelectionTime>Date.now()-50&&("select"==t.inputState.lastSelectionOrigin&&(e=!0),i=t.inputState.lastSelectionOrigin),t.dispatch({selection:o,scrollIntoView:e,userEvent:i}),!0}return!1}const _i={childList:!0,characterData:!0,subtree:!0,attributes:!0,characterDataOldValue:!0},Yi=j.ie&&j.ie_version<=11;class Gi{constructor(t){this.view=t,this.active=!1,this.selectionRange=new S,this.selectionChanged=!1,this.delayedFlush=-1,this.resizeTimeout=-1,this.queue=[],this.delayedAndroidKey=null,this.flushingAndroidKey=-1,this.lastChange=0,this.scrollTargets=[],this.intersection=null,this.resizeScroll=null,this.resizeContent=null,this.intersecting=!1,this.gapIntersection=null,this.gaps=[],this.parentCheck=-1,this.dom=t.contentDOM,this.observer=new MutationObserver((e=>{for(let t of e)this.queue.push(t);(j.ie&&j.ie_version<=11||j.ios&&t.composing)&&e.some((t=>"childList"==t.type&&t.removedNodes.length||"characterData"==t.type&&t.oldValue.length>t.target.nodeValue.length))?this.flushSoon():this.flush()})),Yi&&(this.onCharData=t=>{this.queue.push({target:t.target,type:"characterData",oldValue:t.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this),this.onResize=this.onResize.bind(this),this.onPrint=this.onPrint.bind(this),this.onScroll=this.onScroll.bind(this),"function"==typeof ResizeObserver&&(this.resizeScroll=new ResizeObserver((()=>{var t;(null===(t=this.view.docView)||void 0===t?void 0:t.lastUpdate)<Date.now()-75&&this.onResize()})),this.resizeScroll.observe(t.scrollDOM),this.resizeContent=new ResizeObserver((()=>this.view.requestMeasure())),this.resizeContent.observe(t.contentDOM)),this.addWindowListeners(this.win=t.win),this.start(),"function"==typeof IntersectionObserver&&(this.intersection=new IntersectionObserver((t=>{this.parentCheck<0&&(this.parentCheck=setTimeout(this.listenForScroll.bind(this),1e3)),t.length>0&&t[t.length-1].intersectionRatio>0!=this.intersecting&&(this.intersecting=!this.intersecting,this.intersecting!=this.view.inView&&this.onScrollChanged(document.createEvent("Event")))}),{threshold:[0,.001]}),this.intersection.observe(this.dom),this.gapIntersection=new IntersectionObserver((t=>{t.length>0&&t[t.length-1].intersectionRatio>0&&this.onScrollChanged(document.createEvent("Event"))}),{})),this.listenForScroll(),this.readSelectionRange()}onScrollChanged(t){this.view.inputState.runHandlers("scroll",t),this.intersecting&&this.view.measure()}onScroll(t){this.intersecting&&this.flush(!1),this.onScrollChanged(t)}onResize(){this.resizeTimeout<0&&(this.resizeTimeout=setTimeout((()=>{this.resizeTimeout=-1,this.view.requestMeasure()}),50))}onPrint(){this.view.viewState.printing=!0,this.view.measure(),setTimeout((()=>{this.view.viewState.printing=!1,this.view.requestMeasure()}),500)}updateGaps(t){if(this.gapIntersection&&(t.length!=this.gaps.length||this.gaps.some(((e,i)=>e!=t[i])))){this.gapIntersection.disconnect();for(let e of t)this.gapIntersection.observe(e);this.gaps=t}}onSelectionChange(t){let e=this.selectionChanged;if(!this.readSelectionRange()||this.delayedAndroidKey)return;let{view:i}=this,s=this.selectionRange;if(i.state.facet(Rt)?i.root.activeElement!=this.dom:!f(i.dom,s))return;let o=s.anchorNode&&i.docView.nearest(s.anchorNode);o&&o.ignoreEvent(t)?e||(this.selectionChanged=!1):(j.ie&&j.ie_version<=11||j.android&&j.chrome)&&!i.state.selection.main.empty&&s.focusNode&&g(s.focusNode,s.focusOffset,s.anchorNode,s.anchorOffset)?this.flushSoon():this.flush(!1)}readSelectionRange(){let{view:t}=this,e=j.safari&&11==t.root.nodeType&&function(t){let e=t.activeElement;for(;e&&e.shadowRoot;)e=e.shadowRoot.activeElement;return e}(this.dom.ownerDocument)==this.dom&&function(t){let e=null;function i(t){t.preventDefault(),t.stopImmediatePropagation(),e=t.getTargetRanges()[0]}if(t.contentDOM.addEventListener("beforeinput",i,!0),t.dom.ownerDocument.execCommand("indent"),t.contentDOM.removeEventListener("beforeinput",i,!0),!e)return null;let s=e.startContainer,o=e.startOffset,n=e.endContainer,r=e.endOffset,l=t.docView.domAtPos(t.state.selection.main.anchor);return g(l.node,l.offset,n,r)&&([s,o,n,r]=[n,r,s,o]),{anchorNode:s,anchorOffset:o,focusNode:n,focusOffset:r}}(this.view)||d(t.root);if(!e||this.selectionRange.eq(e))return!1;let i=f(this.dom,e);return i&&!this.selectionChanged&&t.inputState.lastFocusTime>Date.now()-200&&t.inputState.lastTouchTime<Date.now()-300&&function(t,e){let i=e.focusNode,s=e.focusOffset;if(!i||e.anchorNode!=i||e.anchorOffset!=s)return!1;for(s=Math.min(s,v(i));;)if(s){if(1!=i.nodeType)return!1;let t=i.childNodes[s-1];"false"==t.contentEditable?s--:(i=t,s=v(i))}else{if(i==t)return!0;s=m(i),i=i.parentNode}}(this.dom,e)?(this.view.inputState.lastFocusTime=0,t.docView.updateSelection(),!1):(this.selectionRange.setRange(e),i&&(this.selectionChanged=!0),!0)}setSelectionRange(t,e){this.selectionRange.set(t.node,t.offset,e.node,e.offset),this.selectionChanged=!1}clearSelectionRange(){this.selectionRange.set(null,0,null,0)}listenForScroll(){this.parentCheck=-1;let t=0,e=null;for(let i=this.dom;i;)if(1==i.nodeType)!e&&t<this.scrollTargets.length&&this.scrollTargets[t]==i?t++:e||(e=this.scrollTargets.slice(0,t)),e&&e.push(i),i=i.assignedSlot||i.parentNode;else{if(11!=i.nodeType)break;i=i.host}if(t<this.scrollTargets.length&&!e&&(e=this.scrollTargets.slice(0,t)),e){for(let t of this.scrollTargets)t.removeEventListener("scroll",this.onScroll);for(let t of this.scrollTargets=e)t.addEventListener("scroll",this.onScroll)}}ignore(t){if(!this.active)return t();try{return this.stop(),t()}finally{this.start(),this.clear()}}start(){this.active||(this.observer.observe(this.dom,_i),Yi&&this.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.active=!0)}stop(){this.active&&(this.active=!1,this.observer.disconnect(),Yi&&this.dom.removeEventListener("DOMCharacterDataModified",this.onCharData))}clear(){this.processRecords(),this.queue.length=0,this.selectionChanged=!1}delayAndroidKey(t,e){var i;if(!this.delayedAndroidKey){let t=()=>{let t=this.delayedAndroidKey;t&&(this.clearDelayedAndroidKey(),this.view.inputState.lastKeyCode=t.keyCode,this.view.inputState.lastKeyTime=Date.now(),!this.flush()&&t.force&&A(this.dom,t.key,t.keyCode))};this.flushingAndroidKey=this.view.win.requestAnimationFrame(t)}this.delayedAndroidKey&&"Enter"!=t||(this.delayedAndroidKey={key:t,keyCode:e,force:this.lastChange<Date.now()-50||!!(null===(i=this.delayedAndroidKey)||void 0===i?void 0:i.force)})}clearDelayedAndroidKey(){this.win.cancelAnimationFrame(this.flushingAndroidKey),this.delayedAndroidKey=null,this.flushingAndroidKey=-1}flushSoon(){this.delayedFlush<0&&(this.delayedFlush=this.view.win.requestAnimationFrame((()=>{this.delayedFlush=-1,this.flush()})))}forceFlush(){this.delayedFlush>=0&&(this.view.win.cancelAnimationFrame(this.delayedFlush),this.delayedFlush=-1),this.flush()}pendingRecords(){for(let t of this.observer.takeRecords())this.queue.push(t);return this.queue}processRecords(){let t=this.pendingRecords();t.length&&(this.queue=[]);let e=-1,i=-1,s=!1;for(let o of t){let t=this.readMutation(o);t&&(t.typeOver&&(s=!0),-1==e?({from:e,to:i}=t):(e=Math.min(t.from,e),i=Math.max(t.to,i)))}return{from:e,to:i,typeOver:s}}readChange(){let{from:t,to:e,typeOver:i}=this.processRecords(),s=this.selectionChanged&&f(this.dom,this.selectionRange);if(t<0&&!s)return null;t>-1&&(this.lastChange=Date.now()),this.view.inputState.lastFocusTime=0,this.selectionChanged=!1;let o=new Ki(this.view,t,e,i);return this.view.docView.domChanged={newSel:o.newSel?o.newSel.main:null},o}flush(t=!0){if(this.delayedFlush>=0||this.delayedAndroidKey)return!1;t&&this.readSelectionRange();let e=this.readChange();if(!e)return this.view.requestMeasure(),!1;let i=this.view.state,s=qi(this.view,e);return this.view.state==i&&this.view.update([]),s}readMutation(t){let e=this.view.docView.nearest(t.target);if(!e||e.ignoreMutation(t))return null;if(e.markDirty("attributes"==t.type),"attributes"==t.type&&(e.flags|=4),"childList"==t.type){let i=ji(e,t.previousSibling||t.target.previousSibling,-1),s=ji(e,t.nextSibling||t.target.nextSibling,1);return{from:i?e.posAfter(i):e.posAtStart,to:s?e.posBefore(s):e.posAtEnd,typeOver:!1}}return"characterData"==t.type?{from:e.posAtStart,to:e.posAtEnd,typeOver:t.target.nodeValue==t.oldValue}:null}setWindow(t){t!=this.win&&(this.removeWindowListeners(this.win),this.win=t,this.addWindowListeners(this.win))}addWindowListeners(t){t.addEventListener("resize",this.onResize),t.addEventListener("beforeprint",this.onPrint),t.addEventListener("scroll",this.onScroll),t.document.addEventListener("selectionchange",this.onSelectionChange)}removeWindowListeners(t){t.removeEventListener("scroll",this.onScroll),t.removeEventListener("resize",this.onResize),t.removeEventListener("beforeprint",this.onPrint),t.document.removeEventListener("selectionchange",this.onSelectionChange)}destroy(){var t,e,i,s;this.stop(),null===(t=this.intersection)||void 0===t||t.disconnect(),null===(e=this.gapIntersection)||void 0===e||e.disconnect(),null===(i=this.resizeScroll)||void 0===i||i.disconnect(),null===(s=this.resizeContent)||void 0===s||s.disconnect();for(let t of this.scrollTargets)t.removeEventListener("scroll",this.onScroll);this.removeWindowListeners(this.win),clearTimeout(this.parentCheck),clearTimeout(this.resizeTimeout),this.win.cancelAnimationFrame(this.delayedFlush),this.win.cancelAnimationFrame(this.flushingAndroidKey)}}function ji(t,e,i){for(;e;){let s=R.get(e);if(s&&s.parent==t)return s;let o=e.parentNode;e=o!=t.dom?o:i>0?e.nextSibling:e.previousSibling}return null}class Xi{get state(){return this.viewState.state}get viewport(){return this.viewState.viewport}get visibleRanges(){return this.viewState.visibleRanges}get inView(){return this.viewState.inView}get composing(){return this.inputState.composing>0}get compositionStarted(){return this.inputState.composing>=0}get root(){return this._root}get win(){return this.dom.ownerDocument.defaultView||window}constructor(t={}){this.plugins=[],this.pluginMap=new Map,this.editorAttrs={},this.contentAttrs={},this.bidiCache=[],this.destroyed=!1,this.updateState=2,this.measureScheduled=-1,this.measureRequests=[],this.contentDOM=document.createElement("div"),this.scrollDOM=document.createElement("div"),this.scrollDOM.tabIndex=-1,this.scrollDOM.className="cm-scroller",this.scrollDOM.appendChild(this.contentDOM),this.announceDOM=document.createElement("div"),this.announceDOM.style.cssText="position: fixed; top: -10000px",this.announceDOM.setAttribute("aria-live","polite"),this.dom=document.createElement("div"),this.dom.appendChild(this.announceDOM),this.dom.appendChild(this.scrollDOM);let{dispatch:e}=t;this.dispatchTransactions=t.dispatchTransactions||e&&(t=>t.forEach((t=>e(t,this))))||(t=>this.update(t)),this.dispatch=this.dispatch.bind(this),this._root=t.root||function(t){for(;t;){if(t&&(9==t.nodeType||11==t.nodeType&&t.host))return t;t=t.assignedSlot||t.parentNode}return null}(t.parent)||document,this.viewState=new Si(t.state||s.EditorState.create(t)),this.plugins=this.state.facet(Lt).map((t=>new Pt(t)));for(let t of this.plugins)t.update(this);this.observer=new Gi(this),this.inputState=new Oe(this),this.inputState.ensureHandlers(this.plugins),this.docView=new de(this),this.mountStyles(),this.updateAttrs(),this.updateState=0,this.requestMeasure(),t.parent&&t.parent.appendChild(this.dom)}dispatch(...t){let e=1==t.length&&t[0]instanceof s.Transaction?t:1==t.length&&Array.isArray(t[0])?t[0]:[this.state.update(...t)];this.dispatchTransactions(e,this)}update(t){if(0!=this.updateState)throw new Error("Calls to EditorView.update are not allowed while an update is in progress");let e,i=!1,o=!1,n=this.state;for(let e of t){if(e.startState!=n)throw new RangeError("Trying to update state with a transaction that doesn't start from the previous state.");n=e.state}if(this.destroyed)return void(this.viewState.state=n);let r=this.hasFocus,l=0,h=null;t.some((t=>t.annotation(Ze)))?(this.inputState.notifiedFocused=r,l=1):r!=this.inputState.notifiedFocused&&(this.inputState.notifiedFocused=r,h=ti(n,r),h||(l=1));let a=this.observer.delayedAndroidKey,c=null;if(a?(this.observer.clearDelayedAndroidKey(),c=this.observer.readChange(),(c&&!this.state.doc.eq(n.doc)||!this.state.selection.eq(n.selection))&&(c=null)):this.observer.clear(),n.facet(s.EditorState.phrases)!=this.state.facet(s.EditorState.phrases))return this.setState(n);e=Gt.create(this,n,t),e.flags|=l;let d=this.viewState.scrollTarget;try{this.updateState=2;for(let e of t){if(d&&(d=d.map(e.changes)),e.scrollIntoView){let{main:t}=e.state.selection;d=new Tt(t.empty?t:s.EditorSelection.cursor(t.head,t.head>t.anchor?-1:1))}for(let t of e.effects)t.is(Ot)&&(d=t.value)}this.viewState.update(e,d),this.bidiCache=Ji.update(this.bidiCache,e.changes),e.empty||(this.updatePlugins(e),this.inputState.update(e)),i=this.docView.update(e),this.state.facet(_t)!=this.styleModules&&this.mountStyles(),o=this.updateAttrs(),this.showAnnouncements(t),this.docView.updateSelection(i,t.some((t=>t.isUserEvent("select.pointer"))))}finally{this.updateState=0}if(e.startState.facet(Oi)!=e.state.facet(Oi)&&(this.viewState.mustMeasureContent=!0),(i||o||d||this.viewState.mustEnforceCursorAssoc||this.viewState.mustMeasureContent)&&this.requestMeasure(),!e.empty)for(let t of this.state.facet(Mt))t(e);(h||c)&&Promise.resolve().then((()=>{h&&this.state==h.startState&&this.dispatch(h),c&&!qi(this,c)&&a.force&&A(this.contentDOM,a.key,a.keyCode)}))}setState(t){if(0!=this.updateState)throw new Error("Calls to EditorView.setState are not allowed while an update is in progress");if(this.destroyed)return void(this.viewState.state=t);this.updateState=2;let e=this.hasFocus;try{for(let t of this.plugins)t.destroy(this);this.viewState=new Si(t),this.plugins=t.facet(Lt).map((t=>new Pt(t))),this.pluginMap.clear();for(let t of this.plugins)t.update(this);this.docView=new de(this),this.inputState.ensureHandlers(this.plugins),this.mountStyles(),this.updateAttrs(),this.bidiCache=[]}finally{this.updateState=0}e&&this.focus(),this.requestMeasure()}updatePlugins(t){let e=t.startState.facet(Lt),i=t.state.facet(Lt);if(e!=i){let s=[];for(let o of i){let i=e.indexOf(o);if(i<0)s.push(new Pt(o));else{let e=this.plugins[i];e.mustUpdate=t,s.push(e)}}for(let e of this.plugins)e.mustUpdate!=t&&e.destroy(this);this.plugins=s,this.pluginMap.clear()}else for(let e of this.plugins)e.mustUpdate=t;for(let t=0;t<this.plugins.length;t++)this.plugins[t].update(this);e!=i&&this.inputState.ensureHandlers(this.plugins)}measure(t=!0){if(this.destroyed)return;if(this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.observer.delayedAndroidKey)return this.measureScheduled=-1,void this.requestMeasure();this.measureScheduled=0,t&&this.observer.forceFlush();let e=null,i=this.scrollDOM,s=i.scrollTop*this.scaleY,{scrollAnchorPos:o,scrollAnchorHeight:n}=this.viewState;Math.abs(s-this.viewState.scrollTop)>1&&(n=-1),this.viewState.scrollAnchorHeight=-1;try{for(let t=0;;t++){if(n<0)if(T(i))o=-1,n=this.viewState.heightMap.height;else{let t=this.viewState.scrollAnchorAt(s);o=t.from,n=t.top}this.updateState=1;let r=this.viewState.measure(this);if(!r&&!this.measureRequests.length&&null==this.viewState.scrollTarget)break;if(t>5){console.warn(this.measureRequests.length?"Measure loop restarted more than 5 times":"Viewport failed to stabilize");break}let l=[];4&r||([this.measureRequests,l]=[l,this.measureRequests]);let h=l.map((t=>{try{return t.read(this)}catch(t){return Et(this.state,t),$i}})),a=Gt.create(this,this.state,[]),c=!1;a.flags|=r,e?e.flags|=r:e=a,this.updateState=2,a.empty||(this.updatePlugins(a),this.inputState.update(a),this.updateAttrs(),c=this.docView.update(a));for(let t=0;t<l.length;t++)if(h[t]!=$i)try{let e=l[t];e.write&&e.write(h[t],this)}catch(t){Et(this.state,t)}if(c&&this.docView.updateSelection(!0),!a.viewportChanged&&0==this.measureRequests.length){if(this.viewState.editorHeight){if(this.viewState.scrollTarget){this.docView.scrollIntoView(this.viewState.scrollTarget),this.viewState.scrollTarget=null;continue}{let t=(o<0?this.viewState.heightMap.height:this.viewState.lineBlockAt(o).top)-n;if(t>1||t<-1){s+=t,i.scrollTop=s/this.scaleY,n=-1;continue}}}break}}}finally{this.updateState=0,this.measureScheduled=-1}if(e&&!e.empty)for(let t of this.state.facet(Mt))t(e)}get themeClasses(){return Ri+" "+(this.state.facet(Ei)?Li:Bi)+" "+this.state.facet(Oi)}updateAttrs(){let t=Qi(this,Nt,{class:"cm-editor"+(this.hasFocus?" cm-focused ":" ")+this.themeClasses}),e={spellcheck:"false",autocorrect:"off",autocapitalize:"off",translate:"no",contenteditable:this.state.facet(Rt)?"true":"false",class:"cm-content",style:`${j.tabSize}: ${this.state.tabSize}`,role:"textbox","aria-multiline":"true"};this.state.readOnly&&(e["aria-readonly"]="true"),Qi(this,Vt,e);let i=this.observer.ignore((()=>{let i=ot(this.contentDOM,this.contentAttrs,e),s=ot(this.dom,this.editorAttrs,t);return i||s}));return this.editorAttrs=t,this.contentAttrs=e,i}showAnnouncements(t){let e=!0;for(let i of t)for(let t of i.effects)t.is(Xi.announce)&&(e&&(this.announceDOM.textContent=""),e=!1,this.announceDOM.appendChild(document.createElement("div")).textContent=t.value)}mountStyles(){this.styleModules=this.state.facet(_t);let t=this.state.facet(Xi.cspNonce);o.V.mount(this.root,this.styleModules.concat(Ni).reverse(),t?{nonce:t}:void 0)}readMeasured(){if(2==this.updateState)throw new Error("Reading the editor layout isn't allowed during an update");0==this.updateState&&this.measureScheduled>-1&&this.measure(!1)}requestMeasure(t){if(this.measureScheduled<0&&(this.measureScheduled=this.win.requestAnimationFrame((()=>this.measure()))),t){if(this.measureRequests.indexOf(t)>-1)return;if(null!=t.key)for(let e=0;e<this.measureRequests.length;e++)if(this.measureRequests[e].key===t.key)return void(this.measureRequests[e]=t);this.measureRequests.push(t)}}plugin(t){let e=this.pluginMap.get(t);return(void 0===e||e&&e.spec!=t)&&this.pluginMap.set(t,e=this.plugins.find((e=>e.spec==t))||null),e&&e.update(this).value}get documentTop(){return this.contentDOM.getBoundingClientRect().top+this.viewState.paddingTop}get documentPadding(){return{top:this.viewState.paddingTop,bottom:this.viewState.paddingBottom}}get scaleX(){return this.viewState.scaleX}get scaleY(){return this.viewState.scaleY}elementAtHeight(t){return this.readMeasured(),this.viewState.elementAtHeight(t)}lineBlockAtHeight(t){return this.readMeasured(),this.viewState.lineBlockAtHeight(t)}get viewportLineBlocks(){return this.viewState.viewportLines}lineBlockAt(t){return this.viewState.lineBlockAt(t)}get contentHeight(){return this.viewState.contentHeight}moveByChar(t,e,i){return Te(this,t,Ae(this,t,e,i))}moveByGroup(t,e){return Te(this,t,Ae(this,t,e,(e=>function(t,e,i){let o=t.state.charCategorizer(e),n=o(i);return t=>{let e=o(t);return n==s.CharCategory.Space&&(n=e),n==e}}(this,t.head,e))))}moveToLineBoundary(t,e,i=!0){return function(t,e,i,o){let n=Ce(t,e.head),r=o&&n.type==at.Text&&(t.lineWrapping||n.widgetLineBreaks)?t.coordsAtPos(e.assoc<0&&e.head>n.from?e.head-1:e.head):null;if(r){let e=t.dom.getBoundingClientRect(),o=t.textDirectionAt(n.from),l=t.posAtCoords({x:i==(o==jt.LTR)?e.right-1:e.left+1,y:(r.top+r.bottom)/2});if(null!=l)return s.EditorSelection.cursor(l,i?-1:1)}return s.EditorSelection.cursor(i?n.to:n.from,i?-1:1)}(this,t,e,i)}moveVertically(t,e,i){return Te(this,t,function(t,e,i,o){let n=e.head,r=i?1:-1;if(n==(i?t.state.doc.length:0))return s.EditorSelection.cursor(n,e.assoc);let l,h=e.goalColumn,a=t.contentDOM.getBoundingClientRect(),c=t.coordsAtPos(n,e.assoc||-1),d=t.documentTop;if(c)null==h&&(h=c.left-a.left),l=r<0?c.top:c.bottom;else{let e=t.viewState.lineBlockAt(n);null==h&&(h=Math.min(a.right-a.left,t.defaultCharacterWidth*(n-e.from))),l=(r<0?e.top:e.bottom)+d}let u=a.left+h,f=null!=o?o:t.viewState.heightOracle.textHeight>>1;for(let e=0;;e+=10){let i=l+(f+e)*r,o=Me(t,{x:u,y:i},!1,r);if(i<a.top||i>a.bottom||(r<0?o<n:o>n)){let e=t.docView.coordsForChar(o),n=!e||i<e.top?-1:1;return s.EditorSelection.cursor(o,n,void 0,h)}}}(this,t,e,i))}domAtPos(t){return this.docView.domAtPos(t)}posAtDOM(t,e=0){return this.docView.posFromDOM(t,e)}posAtCoords(t,e=!0){return this.readMeasured(),Me(this,t,e)}coordsAtPos(t,e=1){this.readMeasured();let i=this.docView.coordsAt(t,e);if(!i||i.left==i.right)return i;let s=this.state.doc.lineAt(t),o=this.bidiSpans(s);return b(i,o[ie.find(o,t-s.from,-1,e)].dir==jt.LTR==e>0)}coordsForChar(t){return this.readMeasured(),this.docView.coordsForChar(t)}get defaultCharacterWidth(){return this.viewState.heightOracle.charWidth}get defaultLineHeight(){return this.viewState.heightOracle.lineHeight}get textDirection(){return this.viewState.defaultTextDirection}textDirectionAt(t){return!this.state.facet(At)||t<this.viewport.from||t>this.viewport.to?this.textDirection:(this.readMeasured(),this.docView.textDirectionAt(t))}get lineWrapping(){return this.viewState.heightOracle.lineWrapping}bidiSpans(t){if(t.length>Ui)return he(t.length);let e,i=this.textDirectionAt(t.from);for(let s of this.bidiCache)if(s.from==t.from&&s.dir==i&&(s.fresh||se(s.isolates,e=It(this,t.from,t.to))))return s.order;e||(e=It(this,t.from,t.to));let s=le(t.text,i,e);return this.bidiCache.push(new Ji(t.from,t.to,i,e,!0,s)),s}get hasFocus(){var t;return(this.dom.ownerDocument.hasFocus()||j.safari&&(null===(t=this.inputState)||void 0===t?void 0:t.lastContextMenu)>Date.now()-3e4)&&this.root.activeElement==this.contentDOM}focus(){this.observer.ignore((()=>{k(this.contentDOM),this.docView.updateSelection()}))}setRoot(t){this._root!=t&&(this._root=t,this.observer.setWindow((9==t.nodeType?t:t.ownerDocument).defaultView||window),this.mountStyles())}destroy(){for(let t of this.plugins)t.destroy(this);this.plugins=[],this.inputState.destroy(),this.dom.remove(),this.observer.destroy(),this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.destroyed=!0}static scrollIntoView(t,e={}){return Ot.of(new Tt("number"==typeof t?s.EditorSelection.cursor(t):t,e.y,e.x,e.yMargin,e.xMargin))}static domEventHandlers(t){return Ht.define((()=>({})),{eventHandlers:t})}static domEventObservers(t){return Ht.define((()=>({})),{eventObservers:t})}static theme(t,e){let i=o.V.newName(),s=[Oi.of(i),_t.of(Pi(`.${i}`,t))];return e&&e.dark&&s.push(Ei.of(!0)),s}static baseTheme(t){return s.Prec.lowest(_t.of(Pi("."+Ri,t,Hi)))}static findFromDOM(t){var e;let i=t.querySelector(".cm-content"),s=i&&R.get(i)||R.get(t);return(null===(e=null==s?void 0:s.rootView)||void 0===e?void 0:e.view)||null}}Xi.styleModule=_t,Xi.inputHandler=kt,Xi.focusChangeEffect=Ct,Xi.perLineTextDirection=At,Xi.exceptionSink=xt,Xi.updateListener=Mt,Xi.editable=Rt,Xi.mouseSelectionStyle=St,Xi.dragMovesSelection=yt,Xi.clickAddsSelectionRange=bt,Xi.decorations=Ft,Xi.atomicRanges=Wt,Xi.bidiIsolatedRanges=zt,Xi.scrollMargins=Kt,Xi.darkTheme=Ei,Xi.cspNonce=s.Facet.define({combine:t=>t.length?t[0]:""}),Xi.contentAttributes=Vt,Xi.editorAttributes=Nt,Xi.lineWrapping=Xi.contentAttributes.of({class:"cm-lineWrapping"}),Xi.announce=s.StateEffect.define();const Ui=4096,$i={};class Ji{constructor(t,e,i,s,o,n){this.from=t,this.to=e,this.dir=i,this.isolates=s,this.fresh=o,this.order=n}static update(t,e){if(e.empty&&!t.some((t=>t.fresh)))return t;let i=[],s=t.length?t[t.length-1].dir:jt.LTR;for(let o=Math.max(0,t.length-10);o<t.length;o++){let n=t[o];n.dir!=s||e.touchesRange(n.from,n.to)||i.push(new Ji(e.mapPos(n.from,1),e.mapPos(n.to,-1),n.dir,n.isolates,!1,n.order))}return i}}function Qi(t,e,i){for(let s=t.state.facet(e),o=s.length-1;o>=0;o--){let e=s[o],n="function"==typeof e?e(t):e;n&&et(n,i)}return i}const Zi=j.mac?"mac":j.windows?"win":j.linux?"linux":"key";function ts(t,e,i){return e.altKey&&(t="Alt-"+t),e.ctrlKey&&(t="Ctrl-"+t),e.metaKey&&(t="Meta-"+t),!1!==i&&e.shiftKey&&(t="Shift-"+t),t}const es=s.Prec.default(Xi.domEventHandlers({keydown:(t,e)=>hs(os(e.state),t,e,"editor")})),is=s.Facet.define({enables:es}),ss=new WeakMap;function os(t){let e=t.facet(is),i=ss.get(e);return i||ss.set(e,i=function(t,e=Zi){let i=Object.create(null),s=Object.create(null),o=(t,e)=>{let i=s[t];if(null==i)s[t]=e;else if(i!=e)throw new Error("Key binding "+t+" is used both as a regular binding and as a multi-stroke prefix")},n=(t,s,n,r,l)=>{var h,a;let c=i[t]||(i[t]=Object.create(null)),d=s.split(/ (?!$)/).map((t=>function(t,e){const i=t.split(/-(?!$)/);let s,o,n,r,l=i[i.length-1];"Space"==l&&(l=" ");for(let t=0;t<i.length-1;++t){const l=i[t];if(/^(cmd|meta|m)$/i.test(l))r=!0;else if(/^a(lt)?$/i.test(l))s=!0;else if(/^(c|ctrl|control)$/i.test(l))o=!0;else if(/^s(hift)?$/i.test(l))n=!0;else{if(!/^mod$/i.test(l))throw new Error("Unrecognized modifier name: "+l);"mac"==e?r=!0:o=!0}}return s&&(l="Alt-"+l),o&&(l="Ctrl-"+l),r&&(l="Meta-"+l),n&&(l="Shift-"+l),l}(t,e)));for(let e=1;e<d.length;e++){let i=d.slice(0,e).join(" ");o(i,!0),c[i]||(c[i]={preventDefault:!0,stopPropagation:!1,run:[e=>{let s=rs={view:e,prefix:i,scope:t};return setTimeout((()=>{rs==s&&(rs=null)}),ls),!0}]})}let u=d.join(" ");o(u,!1);let f=c[u]||(c[u]={preventDefault:!1,stopPropagation:!1,run:(null===(a=null===(h=c._any)||void 0===h?void 0:h.run)||void 0===a?void 0:a.slice())||[]});n&&f.run.push(n),r&&(f.preventDefault=!0),l&&(f.stopPropagation=!0)};for(let s of t){let t=s.scope?s.scope.split(" "):["editor"];if(s.any)for(let e of t){let t=i[e]||(i[e]=Object.create(null));t._any||(t._any={preventDefault:!1,stopPropagation:!1,run:[]});for(let e in t)t[e].run.push(s.any)}let o=s[e]||s.key;if(o)for(let e of t)n(e,o,s.run,s.preventDefault,s.stopPropagation),s.shift&&n(e,"Shift-"+o,s.shift,s.preventDefault,s.stopPropagation)}return i}(e.reduce(((t,e)=>t.concat(e)),[]))),i}function ns(t,e,i){return hs(os(t.state),e,t,i)}let rs=null;const ls=4e3;function hs(t,e,i,o){let a=function(t){var e=!(l&&t.metaKey&&t.shiftKey&&!t.ctrlKey&&!t.altKey||h&&t.shiftKey&&t.key&&1==t.key.length||"Unidentified"==t.key)&&t.key||(t.shiftKey?r:n)[t.keyCode]||t.key||"Unidentified";return"Esc"==e&&(e="Escape"),"Del"==e&&(e="Delete"),"Left"==e&&(e="ArrowLeft"),"Up"==e&&(e="ArrowUp"),"Right"==e&&(e="ArrowRight"),"Down"==e&&(e="ArrowDown"),e}(e),c=(0,s.codePointAt)(a,0),d=(0,s.codePointSize)(c)==a.length&&" "!=a,u="",f=!1,p=!1,g=!1;rs&&rs.view==i&&rs.scope==o&&(u=rs.prefix+" ",He.indexOf(e.keyCode)<0&&(p=!0,rs=null));let m,w,v=new Set,b=t=>{if(t){for(let s of t.run)if(!v.has(s)&&(v.add(s),s(i,e)))return t.stopPropagation&&(g=!0),!0;t.preventDefault&&(t.stopPropagation&&(g=!0),p=!0)}return!1},y=t[o];return y&&(b(y[u+ts(a,e,!d)])?f=!0:d&&(e.altKey||e.metaKey||e.ctrlKey)&&!(j.windows&&e.ctrlKey&&e.altKey)&&(m=n[e.keyCode])&&m!=a?(b(y[u+ts(m,e,!0)])||e.shiftKey&&(w=r[e.keyCode])!=a&&w!=m&&b(y[u+ts(w,e,!1)]))&&(f=!0):d&&e.shiftKey&&b(y[u+ts(a,e,!0)])&&(f=!0),!f&&b(y._any)&&(f=!0)),p&&(f=!0),f&&g&&e.stopPropagation(),f}class as{constructor(t,e,i,s,o){this.className=t,this.left=e,this.top=i,this.width=s,this.height=o}draw(){let t=document.createElement("div");return t.className=this.className,this.adjust(t),t}update(t,e){return e.className==this.className&&(this.adjust(t),!0)}adjust(t){t.style.left=this.left+"px",t.style.top=this.top+"px",null!=this.width&&(t.style.width=this.width+"px"),t.style.height=this.height+"px"}eq(t){return this.left==t.left&&this.top==t.top&&this.width==t.width&&this.height==t.height&&this.className==t.className}static forRange(t,e,i){if(i.empty){let s=t.coordsAtPos(i.head,i.assoc||1);if(!s)return[];let o=cs(t);return[new as(e,s.left-o.left,s.top-o.top,null,s.bottom-s.top)]}return function(t,e,i){if(i.to<=t.viewport.from||i.from>=t.viewport.to)return[];let s=Math.max(i.from,t.viewport.from),o=Math.min(i.to,t.viewport.to),n=t.textDirection==jt.LTR,r=t.contentDOM,l=r.getBoundingClientRect(),h=cs(t),a=r.querySelector(".cm-line"),c=a&&window.getComputedStyle(a),d=l.left+(c?parseInt(c.paddingLeft)+Math.min(0,parseInt(c.textIndent)):0),u=l.right-(c?parseInt(c.paddingRight):0),f=Ce(t,s),p=Ce(t,o),g=f.type==at.Text?f:null,m=p.type==at.Text?p:null;if(g&&(t.lineWrapping||f.widgetLineBreaks)&&(g=ds(t,s,g)),m&&(t.lineWrapping||p.widgetLineBreaks)&&(m=ds(t,o,m)),g&&m&&g.from==m.from)return v(b(i.from,i.to,g));{let e=g?b(i.from,null,g):y(f,!1),s=m?b(null,i.to,m):y(p,!0),o=[];return(g||f).to<(m||p).from-(g&&m?1:0)||f.widgetLineBreaks>1&&e.bottom+t.defaultLineHeight/2<s.top?o.push(w(d,e.bottom,u,s.top)):e.bottom<s.top&&t.elementAtHeight((e.bottom+s.top)/2).type==at.Text&&(e.bottom=s.top=(e.bottom+s.top)/2),v(e).concat(o).concat(v(s))}function w(t,i,s,o){return new as(e,t-h.left,i-h.top-.01,s-t,o-i+.01)}function v({top:t,bottom:e,horizontal:i}){let s=[];for(let o=0;o<i.length;o+=2)s.push(w(i[o],t,i[o+1],e));return s}function b(e,i,s){let o=1e9,r=-1e9,l=[];function h(e,i,h,a,c){let f=t.coordsAtPos(e,e==s.to?-2:2),p=t.coordsAtPos(h,h==s.from?2:-2);f&&p&&(o=Math.min(f.top,p.top,o),r=Math.max(f.bottom,p.bottom,r),c==jt.LTR?l.push(n&&i?d:f.left,n&&a?u:p.right):l.push(!n&&a?d:p.left,!n&&i?u:f.right))}let a=null!=e?e:s.from,c=null!=i?i:s.to;for(let s of t.visibleRanges)if(s.to>a&&s.from<c)for(let o=Math.max(s.from,a),n=Math.min(s.to,c);;){let s=t.state.doc.lineAt(o);for(let r of t.bidiSpans(s)){let t=r.from+s.from,l=r.to+s.from;if(t>=n)break;l>o&&h(Math.max(t,o),null==e&&t<=a,Math.min(l,n),null==i&&l>=c,r.dir)}if(o=s.to+1,o>=n)break}return 0==l.length&&h(a,null==e,c,null==i,t.textDirection),{top:o,bottom:r,horizontal:l}}function y(t,e){let i=l.top+(e?t.top:t.bottom);return{top:i,bottom:i,horizontal:[]}}}(t,e,i)}}function cs(t){let e=t.scrollDOM.getBoundingClientRect();return{left:(t.textDirection==jt.LTR?e.left:e.right-t.scrollDOM.clientWidth*t.scaleX)-t.scrollDOM.scrollLeft*t.scaleX,top:e.top-t.scrollDOM.scrollTop*t.scaleY}}function ds(t,e,i){let o=s.EditorSelection.cursor(e);return{from:Math.max(i.from,t.moveToLineBoundary(o,!1,!0).from),to:Math.min(i.to,t.moveToLineBoundary(o,!0,!0).from),type:at.Text}}class us{constructor(t,e){this.view=t,this.layer=e,this.drawn=[],this.scaleX=1,this.scaleY=1,this.measureReq={read:this.measure.bind(this),write:this.draw.bind(this)},this.dom=t.scrollDOM.appendChild(document.createElement("div")),this.dom.classList.add("cm-layer"),e.above&&this.dom.classList.add("cm-layer-above"),e.class&&this.dom.classList.add(e.class),this.scale(),this.dom.setAttribute("aria-hidden","true"),this.setOrder(t.state),t.requestMeasure(this.measureReq),e.mount&&e.mount(this.dom,t)}update(t){t.startState.facet(fs)!=t.state.facet(fs)&&this.setOrder(t.state),(this.layer.update(t,this.dom)||t.geometryChanged)&&(this.scale(),t.view.requestMeasure(this.measureReq))}setOrder(t){let e=0,i=t.facet(fs);for(;e<i.length&&i[e]!=this.layer;)e++;this.dom.style.zIndex=String((this.layer.above?150:-1)-e)}measure(){return this.layer.markers(this.view)}scale(){let{scaleX:t,scaleY:e}=this.view;t==this.scaleX&&e==this.scaleY||(this.scaleX=t,this.scaleY=e,this.dom.style.transform=`scale(${1/t}, ${1/e})`)}draw(t){if(t.length!=this.drawn.length||t.some(((t,e)=>{return i=t,s=this.drawn[e],!(i.constructor==s.constructor&&i.eq(s));var i,s}))){let e=this.dom.firstChild,i=0;for(let s of t)s.update&&e&&s.constructor&&this.drawn[i].constructor&&s.update(e,this.drawn[i])?(e=e.nextSibling,i++):this.dom.insertBefore(s.draw(),e);for(;e;){let t=e.nextSibling;e.remove(),e=t}this.drawn=t}}destroy(){this.layer.destroy&&this.layer.destroy(this.dom,this.view),this.dom.remove()}}const fs=s.Facet.define();function ps(t){return[Ht.define((e=>new us(e,t))),fs.of(t)]}const gs=!j.ios,ms=s.Facet.define({combine:t=>(0,s.combineConfig)(t,{cursorBlinkRate:1200,drawRangeCursor:!0},{cursorBlinkRate:(t,e)=>Math.min(t,e),drawRangeCursor:(t,e)=>t||e})});function ws(t={}){return[ms.of(t),ys,xs,ks,Dt.of(!0)]}function vs(t){return t.facet(ms)}function bs(t){return t.startState.facet(ms)!=t.state.facet(ms)}const ys=ps({above:!0,markers(t){let{state:e}=t,i=e.facet(ms),o=[];for(let n of e.selection.ranges){let r=n==e.selection.main;if(n.empty?!r||gs:i.drawRangeCursor){let e=r?"cm-cursor cm-cursor-primary":"cm-cursor cm-cursor-secondary",i=n.empty?n:s.EditorSelection.cursor(n.head,n.head>n.anchor?-1:1);for(let s of as.forRange(t,e,i))o.push(s)}}return o},update(t,e){t.transactions.some((t=>t.selection))&&(e.style.animationName="cm-blink"==e.style.animationName?"cm-blink2":"cm-blink");let i=bs(t);return i&&Ss(t.state,e),t.docChanged||t.selectionSet||i},mount(t,e){Ss(e.state,t)},class:"cm-cursorLayer"});function Ss(t,e){e.style.animationDuration=t.facet(ms).cursorBlinkRate+"ms"}const xs=ps({above:!1,markers:t=>t.state.selection.ranges.map((e=>e.empty?[]:as.forRange(t,"cm-selectionBackground",e))).reduce(((t,e)=>t.concat(e))),update:(t,e)=>t.docChanged||t.selectionSet||t.viewportChanged||bs(t),class:"cm-selectionLayer"}),Ms={".cm-line":{"& ::selection":{backgroundColor:"transparent !important"},"&::selection":{backgroundColor:"transparent !important"}}};gs&&(Ms[".cm-line"].caretColor="transparent !important");const ks=s.Prec.highest(Xi.theme(Ms)),Cs=s.StateEffect.define({map:(t,e)=>null==t?null:e.mapPos(t)}),As=s.StateField.define({create:()=>null,update:(t,e)=>(null!=t&&(t=e.changes.mapPos(t)),e.effects.reduce(((t,e)=>e.is(Cs)?e.value:t),t))}),Ds=Ht.fromClass(class{constructor(t){this.view=t,this.cursor=null,this.measureReq={read:this.readPos.bind(this),write:this.drawCursor.bind(this)}}update(t){var e;let i=t.state.field(As);null==i?null!=this.cursor&&(null===(e=this.cursor)||void 0===e||e.remove(),this.cursor=null):(this.cursor||(this.cursor=this.view.scrollDOM.appendChild(document.createElement("div")),this.cursor.className="cm-dropCursor"),(t.startState.field(As)!=i||t.docChanged||t.geometryChanged)&&this.view.requestMeasure(this.measureReq))}readPos(){let{view:t}=this,e=t.state.field(As),i=null!=e&&t.coordsAtPos(e);if(!i)return null;let s=t.scrollDOM.getBoundingClientRect();return{left:i.left-s.left+t.scrollDOM.scrollLeft*t.scaleX,top:i.top-s.top+t.scrollDOM.scrollTop*t.scaleY,height:i.bottom-i.top}}drawCursor(t){if(this.cursor){let{scaleX:e,scaleY:i}=this.view;t?(this.cursor.style.left=t.left/e+"px",this.cursor.style.top=t.top/i+"px",this.cursor.style.height=t.height/i+"px"):this.cursor.style.left="-100000px"}}destroy(){this.cursor&&this.cursor.remove()}setDropPos(t){this.view.state.field(As)!=t&&this.view.dispatch({effects:Cs.of(t)})}},{eventObservers:{dragover(t){this.setDropPos(this.view.posAtCoords({x:t.clientX,y:t.clientY}))},dragleave(t){t.target!=this.view.contentDOM&&this.view.contentDOM.contains(t.relatedTarget)||this.setDropPos(null)},dragend(){this.setDropPos(null)},drop(){this.setDropPos(null)}}});function Ts(){return[As,Ds]}function Os(t,e,i,s,o){e.lastIndex=0;for(let n,r=t.iterRange(i,s),l=i;!r.next().done;l+=r.value.length)if(!r.lineBreak)for(;n=e.exec(r.value);)o(l+n.index,n)}class Es{constructor(t){const{regexp:e,decoration:i,decorate:s,boundary:o,maxLength:n=1e3}=t;if(!e.global)throw new RangeError("The regular expression given to MatchDecorator should have its 'g' flag set");if(this.regexp=e,s)this.addMatch=(t,e,i,o)=>s(o,i,i+t[0].length,t,e);else if("function"==typeof i)this.addMatch=(t,e,s,o)=>{let n=i(t,e,s);n&&o(s,s+t[0].length,n)};else{if(!i)throw new RangeError("Either 'decorate' or 'decoration' should be provided to MatchDecorator");this.addMatch=(t,e,s,o)=>o(s,s+t[0].length,i)}this.boundary=o,this.maxLength=n}createDeco(t){let e=new s.RangeSetBuilder,i=e.add.bind(e);for(let{from:e,to:s}of function(t,e){let i=t.visibleRanges;if(1==i.length&&i[0].from==t.viewport.from&&i[0].to==t.viewport.to)return i;let s=[];for(let{from:o,to:n}of i)o=Math.max(t.state.doc.lineAt(o).from,o-e),n=Math.min(t.state.doc.lineAt(n).to,n+e),s.length&&s[s.length-1].to>=o?s[s.length-1].to=n:s.push({from:o,to:n});return s}(t,this.maxLength))Os(t.state.doc,this.regexp,e,s,((e,s)=>this.addMatch(s,t,e,i)));return e.finish()}updateDeco(t,e){let i=1e9,s=-1;return t.docChanged&&t.changes.iterChanges(((e,o,n,r)=>{r>t.view.viewport.from&&n<t.view.viewport.to&&(i=Math.min(n,i),s=Math.max(r,s))})),t.viewportChanged||s-i>1e3?this.createDeco(t.view):s>-1?this.updateRange(t.view,e.map(t.changes),i,s):e}updateRange(t,e,i,s){for(let o of t.visibleRanges){let n=Math.max(o.from,i),r=Math.min(o.to,s);if(r>n){let i=t.state.doc.lineAt(n),s=i.to<r?t.state.doc.lineAt(r):i,l=Math.max(o.from,i.from),h=Math.min(o.to,s.to);if(this.boundary){for(;n>i.from;n--)if(this.boundary.test(i.text[n-1-i.from])){l=n;break}for(;r<s.to;r++)if(this.boundary.test(s.text[r-s.from])){h=r;break}}let a,c=[],d=(t,e,i)=>c.push(i.range(t,e));if(i==s)for(this.regexp.lastIndex=l-i.from;(a=this.regexp.exec(i.text))&&a.index<h-i.from;)this.addMatch(a,t,a.index+i.from,d);else Os(t.state.doc,this.regexp,l,h,((e,i)=>this.addMatch(i,t,e,d)));e=e.update({filterFrom:l,filterTo:h,filter:(t,e)=>t<l||e>h,add:c})}}return e}}const Rs=null!=/x/.unicode?"gu":"g",Bs=new RegExp("[\0-\b\n--­؜​‎‏\u2028\u2029‭‮⁦⁧⁩\ufeff￹-￼]",Rs),Ls={0:"null",7:"bell",8:"backspace",10:"newline",11:"vertical tab",13:"carriage return",27:"escape",8203:"zero width space",8204:"zero width non-joiner",8205:"zero width joiner",8206:"left-to-right mark",8207:"right-to-left mark",8232:"line separator",8237:"left-to-right override",8238:"right-to-left override",8294:"left-to-right isolate",8295:"right-to-left isolate",8297:"pop directional isolate",8233:"paragraph separator",65279:"zero width no-break space",65532:"object replacement"};let Hs=null;const Ps=s.Facet.define({combine(t){let e=(0,s.combineConfig)(t,{render:null,specialChars:Bs,addSpecialChars:null});return(e.replaceTabs=!function(){var t;if(null==Hs&&"undefined"!=typeof document&&document.body){let e=document.body.style;Hs=null!=(null!==(t=e.tabSize)&&void 0!==t?t:e.MozTabSize)}return Hs||!1}())&&(e.specialChars=new RegExp("\t|"+e.specialChars.source,Rs)),e.addSpecialChars&&(e.specialChars=new RegExp(e.specialChars.source+"|"+e.addSpecialChars.source,Rs)),e}});function Ns(t={}){return[Ps.of(t),Vs||(Vs=Ht.fromClass(class{constructor(t){this.view=t,this.decorations=ct.none,this.decorationCache=Object.create(null),this.decorator=this.makeDecorator(t.state.facet(Ps)),this.decorations=this.decorator.createDeco(t)}makeDecorator(t){return new Es({regexp:t.specialChars,decoration:(e,i,o)=>{let{doc:n}=i.state,r=(0,s.codePointAt)(e[0],0);if(9==r){let t=n.lineAt(o),e=i.state.tabSize,r=(0,s.countColumn)(t.text,e,o-t.from);return ct.replace({widget:new Ws((e-r%e)*this.view.defaultCharacterWidth/this.view.scaleX)})}return this.decorationCache[r]||(this.decorationCache[r]=ct.replace({widget:new Fs(t,r)}))},boundary:t.replaceTabs?void 0:/[^]/})}update(t){let e=t.state.facet(Ps);t.startState.facet(Ps)!=e?(this.decorator=this.makeDecorator(e),this.decorations=this.decorator.createDeco(t.view)):this.decorations=this.decorator.updateDeco(t,this.decorations)}},{decorations:t=>t.decorations}))]}let Vs=null;class Fs extends ht{constructor(t,e){super(),this.options=t,this.code=e}eq(t){return t.code==this.code}toDOM(t){let e=function(t){return t>=32?"•":10==t?"␤":String.fromCharCode(9216+t)}(this.code),i=t.state.phrase("Control character")+" "+(Ls[this.code]||"0x"+this.code.toString(16)),s=this.options.render&&this.options.render(this.code,i,e);if(s)return s;let o=document.createElement("span");return o.textContent=e,o.title=i,o.setAttribute("aria-label",i),o.className="cm-specialChar",o}ignoreEvent(){return!1}}class Ws extends ht{constructor(t){super(),this.width=t}eq(t){return t.width==this.width}toDOM(){let t=document.createElement("span");return t.textContent="\t",t.className="cm-tab",t.style.width=this.width+"px",t}ignoreEvent(){return!1}}const zs=Ht.fromClass(class{constructor(){this.height=1e3,this.attrs={style:"padding-bottom: 1000px"}}update(t){let{view:e}=t,i=e.viewState.editorHeight*e.scaleY-e.defaultLineHeight-e.documentPadding.top-.5;i>=0&&i!=this.height&&(this.height=i,this.attrs={style:`padding-bottom: ${i}px`})}});function Is(){return[zs,Vt.of((t=>{var e;return(null===(e=t.plugin(zs))||void 0===e?void 0:e.attrs)||null}))]}function Ks(){return _s}const qs=ct.line({class:"cm-activeLine"}),_s=Ht.fromClass(class{constructor(t){this.decorations=this.getDeco(t)}update(t){(t.docChanged||t.selectionSet)&&(this.decorations=this.getDeco(t.view))}getDeco(t){let e=-1,i=[];for(let s of t.state.selection.ranges){let o=t.lineBlockAt(s.head);o.from>e&&(i.push(qs.range(o.from)),e=o.from)}return ct.set(i)}},{decorations:t=>t.decorations});class Ys extends ht{constructor(t){super(),this.content=t}toDOM(){let t=document.createElement("span");return t.className="cm-placeholder",t.style.pointerEvents="none",t.appendChild("string"==typeof this.content?document.createTextNode(this.content):this.content),"string"==typeof this.content?t.setAttribute("aria-label","placeholder "+this.content):t.setAttribute("aria-hidden","true"),t}coordsAt(t){let e=t.firstChild?p(t.firstChild):[];if(!e.length)return null;let i=window.getComputedStyle(t.parentNode),s=b(e[0],"rtl"!=i.direction),o=parseInt(i.lineHeight);return s.bottom-s.top>1.5*o?{left:s.left,right:s.right,top:s.top,bottom:s.top+o}:s}ignoreEvent(){return!1}}function Gs(t){return Ht.fromClass(class{constructor(e){this.view=e,this.placeholder=t?ct.set([ct.widget({widget:new Ys(t),side:1}).range(0)]):ct.none}get decorations(){return this.view.state.doc.length?ct.none:this.placeholder}},{decorations:t=>t.decorations})}const js=2e3;function Xs(t,e){let i=t.posAtCoords({x:e.clientX,y:e.clientY},!1),o=t.state.doc.lineAt(i),n=i-o.from,r=n>js?-1:n==o.length?function(t,e){let i=t.coordsAtPos(t.viewport.from);return i?Math.round(Math.abs((i.left-e)/t.defaultCharacterWidth)):-1}(t,e.clientX):(0,s.countColumn)(o.text,t.state.tabSize,i-o.from);return{line:o.number,col:r,off:n}}function Us(t){let e=(null==t?void 0:t.eventFilter)||(t=>t.altKey&&0==t.button);return Xi.mouseSelectionStyle.of(((t,i)=>e(i)?function(t,e){let i=Xs(t,e),o=t.state.selection;return i?{update(t){if(t.docChanged){let e=t.changes.mapPos(t.startState.doc.line(i.line).from),s=t.state.doc.lineAt(e);i={line:s.number,col:i.col,off:Math.min(i.off,s.length)},o=o.map(t.changes)}},get(e,n,r){let l=Xs(t,e);if(!l)return o;let h=function(t,e,i){let o=Math.min(e.line,i.line),n=Math.max(e.line,i.line),r=[];if(e.off>js||i.off>js||e.col<0||i.col<0){let l=Math.min(e.off,i.off),h=Math.max(e.off,i.off);for(let e=o;e<=n;e++){let i=t.doc.line(e);i.length<=h&&r.push(s.EditorSelection.range(i.from+l,i.to+h))}}else{let l=Math.min(e.col,i.col),h=Math.max(e.col,i.col);for(let e=o;e<=n;e++){let i=t.doc.line(e),o=(0,s.findColumn)(i.text,l,t.tabSize,!0);if(o<0)r.push(s.EditorSelection.cursor(i.to));else{let e=(0,s.findColumn)(i.text,h,t.tabSize);r.push(s.EditorSelection.range(i.from+o,i.from+e))}}}return r}(t.state,i,l);return h.length?r?s.EditorSelection.create(h.concat(o.ranges)):s.EditorSelection.create(h):o}}:null}(t,i):null))}const $s={Alt:[18,t=>!!t.altKey],Control:[17,t=>!!t.ctrlKey],Shift:[16,t=>!!t.shiftKey],Meta:[91,t=>!!t.metaKey]},Js={style:"cursor: crosshair"};function Qs(t={}){let[e,i]=$s[t.key||"Alt"],s=Ht.fromClass(class{constructor(t){this.view=t,this.isDown=!1}set(t){this.isDown!=t&&(this.isDown=t,this.view.update([]))}},{eventObservers:{keydown(t){this.set(t.keyCode==e||i(t))},keyup(t){t.keyCode!=e&&i(t)||this.set(!1)},mousemove(t){this.set(i(t))}}});return[s,Xi.contentAttributes.of((t=>{var e;return(null===(e=t.plugin(s))||void 0===e?void 0:e.isDown)?Js:null}))]}const Zs="-10000px";class to{constructor(t,e,i){this.facet=e,this.createTooltipView=i,this.input=t.state.facet(e),this.tooltips=this.input.filter((t=>t)),this.tooltipViews=this.tooltips.map(i)}update(t,e){var i;let s=t.state.facet(this.facet),o=s.filter((t=>t));if(s===this.input){for(let e of this.tooltipViews)e.update&&e.update(t);return!1}let n=[],r=e?[]:null;for(let i=0;i<o.length;i++){let s=o[i],l=-1;if(s){for(let t=0;t<this.tooltips.length;t++){let e=this.tooltips[t];e&&e.create==s.create&&(l=t)}if(l<0)n[i]=this.createTooltipView(s),r&&(r[i]=!!s.above);else{let s=n[i]=this.tooltipViews[l];r&&(r[i]=e[l]),s.update&&s.update(t)}}}for(let t of this.tooltipViews)n.indexOf(t)<0&&(t.dom.remove(),null===(i=t.destroy)||void 0===i||i.call(t));return e&&(r.forEach(((t,i)=>e[i]=t)),e.length=r.length),this.input=s,this.tooltips=o,this.tooltipViews=n,!0}}function eo(t={}){return so.of(t)}function io(t){let{win:e}=t;return{top:0,left:0,bottom:e.innerHeight,right:e.innerWidth}}const so=s.Facet.define({combine:t=>{var e,i,s;return{position:j.ios?"absolute":(null===(e=t.find((t=>t.position)))||void 0===e?void 0:e.position)||"fixed",parent:(null===(i=t.find((t=>t.parent)))||void 0===i?void 0:i.parent)||null,tooltipSpace:(null===(s=t.find((t=>t.tooltipSpace)))||void 0===s?void 0:s.tooltipSpace)||io}}}),oo=new WeakMap,no=Ht.fromClass(class{constructor(t){this.view=t,this.above=[],this.inView=!0,this.madeAbsolute=!1,this.lastTransaction=0,this.measureTimeout=-1;let e=t.state.facet(so);this.position=e.position,this.parent=e.parent,this.classes=t.themeClasses,this.createContainer(),this.measureReq={read:this.readMeasure.bind(this),write:this.writeMeasure.bind(this),key:this},this.manager=new to(t,ho,(t=>this.createTooltip(t))),this.intersectionObserver="function"==typeof IntersectionObserver?new IntersectionObserver((t=>{Date.now()>this.lastTransaction-50&&t.length>0&&t[t.length-1].intersectionRatio<1&&this.measureSoon()}),{threshold:[1]}):null,this.observeIntersection(),t.win.addEventListener("resize",this.measureSoon=this.measureSoon.bind(this)),this.maybeMeasure()}createContainer(){this.parent?(this.container=document.createElement("div"),this.container.style.position="relative",this.container.className=this.view.themeClasses,this.parent.appendChild(this.container)):this.container=this.view.dom}observeIntersection(){if(this.intersectionObserver){this.intersectionObserver.disconnect();for(let t of this.manager.tooltipViews)this.intersectionObserver.observe(t.dom)}}measureSoon(){this.measureTimeout<0&&(this.measureTimeout=setTimeout((()=>{this.measureTimeout=-1,this.maybeMeasure()}),50))}update(t){t.transactions.length&&(this.lastTransaction=Date.now());let e=this.manager.update(t,this.above);e&&this.observeIntersection();let i=e||t.geometryChanged,s=t.state.facet(so);if(s.position!=this.position&&!this.madeAbsolute){this.position=s.position;for(let t of this.manager.tooltipViews)t.dom.style.position=this.position;i=!0}if(s.parent!=this.parent){this.parent&&this.container.remove(),this.parent=s.parent,this.createContainer();for(let t of this.manager.tooltipViews)this.container.appendChild(t.dom);i=!0}else this.parent&&this.view.themeClasses!=this.classes&&(this.classes=this.container.className=this.view.themeClasses);i&&this.maybeMeasure()}createTooltip(t){let e=t.create(this.view);if(e.dom.classList.add("cm-tooltip"),t.arrow&&!e.dom.querySelector(".cm-tooltip > .cm-tooltip-arrow")){let t=document.createElement("div");t.className="cm-tooltip-arrow",e.dom.appendChild(t)}return e.dom.style.position=this.position,e.dom.style.top=Zs,this.container.appendChild(e.dom),e.mount&&e.mount(this.view),e}destroy(){var t,e;this.view.win.removeEventListener("resize",this.measureSoon);for(let e of this.manager.tooltipViews)e.dom.remove(),null===(t=e.destroy)||void 0===t||t.call(e);this.parent&&this.container.remove(),null===(e=this.intersectionObserver)||void 0===e||e.disconnect(),clearTimeout(this.measureTimeout)}readMeasure(){let t=this.view.dom.getBoundingClientRect(),e=1,i=1,s=!1;if("fixed"==this.position&&this.manager.tooltipViews.length){let{offsetParent:t}=this.manager.tooltipViews[0].dom;s=!(!t||t==this.container.ownerDocument.body)}if(s||"absolute"==this.position)if(this.parent){let t=this.parent.getBoundingClientRect();t.width&&t.height&&(e=t.width/this.parent.offsetWidth,i=t.height/this.parent.offsetHeight)}else({scaleX:e,scaleY:i}=this.view.viewState);return{editor:t,parent:this.parent?this.container.getBoundingClientRect():t,pos:this.manager.tooltips.map(((t,e)=>{let i=this.manager.tooltipViews[e];return i.getCoords?i.getCoords(t.pos):this.view.coordsAtPos(t.pos)})),size:this.manager.tooltipViews.map((({dom:t})=>t.getBoundingClientRect())),space:this.view.state.facet(so).tooltipSpace(this.view),scaleX:e,scaleY:i,makeAbsolute:s}}writeMeasure(t){var e;if(t.makeAbsolute){this.madeAbsolute=!0,this.position="absolute";for(let t of this.manager.tooltipViews)t.dom.style.position="absolute"}let{editor:i,space:s,scaleX:o,scaleY:n}=t,r=[];for(let l=0;l<this.manager.tooltips.length;l++){let h=this.manager.tooltips[l],a=this.manager.tooltipViews[l],{dom:c}=a,d=t.pos[l],u=t.size[l];if(!d||d.bottom<=Math.max(i.top,s.top)||d.top>=Math.min(i.bottom,s.bottom)||d.right<Math.max(i.left,s.left)-.1||d.left>Math.min(i.right,s.right)+.1){c.style.top=Zs;continue}let f=h.arrow?a.dom.querySelector(".cm-tooltip-arrow"):null,p=f?7:0,g=u.right-u.left,m=null!==(e=oo.get(a))&&void 0!==e?e:u.bottom-u.top,w=a.offset||lo,v=this.view.textDirection==jt.LTR,b=u.width>s.right-s.left?v?s.left:s.right-u.width:v?Math.min(d.left-(f?14:0)+w.x,s.right-g):Math.max(s.left,d.left-g+(f?14:0)-w.x),y=this.above[l];!h.strictSide&&(y?d.top-(u.bottom-u.top)-w.y<s.top:d.bottom+(u.bottom-u.top)+w.y>s.bottom)&&y==s.bottom-d.bottom>d.top-s.top&&(y=this.above[l]=!y);let S=(y?d.top-s.top:s.bottom-d.bottom)-p;if(S<m&&!1!==a.resize){if(S<this.view.defaultLineHeight){c.style.top=Zs;continue}oo.set(a,m),c.style.height=(m=S)/n+"px"}else c.style.height&&(c.style.height="");let x=y?d.top-m-p-w.y:d.bottom+p+w.y,M=b+g;if(!0!==a.overlap)for(let t of r)t.left<M&&t.right>b&&t.top<x+m&&t.bottom>x&&(x=y?t.top-m-2-p:t.bottom+p+2);if("absolute"==this.position?(c.style.top=(x-t.parent.top)/n+"px",c.style.left=(b-t.parent.left)/o+"px"):(c.style.top=x/n+"px",c.style.left=b/o+"px"),f){let t=d.left+(v?w.x:-w.x)-(b+14-7);f.style.left=t/o+"px"}!0!==a.overlap&&r.push({left:b,top:x,right:M,bottom:x+m}),c.classList.toggle("cm-tooltip-above",y),c.classList.toggle("cm-tooltip-below",!y),a.positioned&&a.positioned(t.space)}}maybeMeasure(){if(this.manager.tooltips.length&&(this.view.inView&&this.view.requestMeasure(this.measureReq),this.inView!=this.view.inView&&(this.inView=this.view.inView,!this.inView)))for(let t of this.manager.tooltipViews)t.dom.style.top=Zs}},{eventObservers:{scroll(){this.maybeMeasure()}}}),ro=Xi.baseTheme({".cm-tooltip":{zIndex:100,boxSizing:"border-box"},"&light .cm-tooltip":{border:"1px solid #bbb",backgroundColor:"#f5f5f5"},"&light .cm-tooltip-section:not(:first-child)":{borderTop:"1px solid #bbb"},"&dark .cm-tooltip":{backgroundColor:"#333338",color:"white"},".cm-tooltip-arrow":{height:"7px",width:"14px",position:"absolute",zIndex:-1,overflow:"hidden","&:before, &:after":{content:"''",position:"absolute",width:0,height:0,borderLeft:"7px solid transparent",borderRight:"7px solid transparent"},".cm-tooltip-above &":{bottom:"-7px","&:before":{borderTop:"7px solid #bbb"},"&:after":{borderTop:"7px solid #f5f5f5",bottom:"1px"}},".cm-tooltip-below &":{top:"-7px","&:before":{borderBottom:"7px solid #bbb"},"&:after":{borderBottom:"7px solid #f5f5f5",top:"1px"}}},"&dark .cm-tooltip .cm-tooltip-arrow":{"&:before":{borderTopColor:"#333338",borderBottomColor:"#333338"},"&:after":{borderTopColor:"transparent",borderBottomColor:"transparent"}}}),lo={x:0,y:0},ho=s.Facet.define({enables:[no,ro]}),ao=s.Facet.define();class co{static create(t){return new co(t)}constructor(t){this.view=t,this.mounted=!1,this.dom=document.createElement("div"),this.dom.classList.add("cm-tooltip-hover"),this.manager=new to(t,ao,(t=>this.createHostedView(t)))}createHostedView(t){let e=t.create(this.view);return e.dom.classList.add("cm-tooltip-section"),this.dom.appendChild(e.dom),this.mounted&&e.mount&&e.mount(this.view),e}mount(t){for(let e of this.manager.tooltipViews)e.mount&&e.mount(t);this.mounted=!0}positioned(t){for(let e of this.manager.tooltipViews)e.positioned&&e.positioned(t)}update(t){this.manager.update(t)}destroy(){var t;for(let e of this.manager.tooltipViews)null===(t=e.destroy)||void 0===t||t.call(e)}}const uo=ho.compute([ao],(t=>{let e=t.facet(ao).filter((t=>t));return 0===e.length?null:{pos:Math.min(...e.map((t=>t.pos))),end:Math.max(...e.filter((t=>null!=t.end)).map((t=>t.end))),create:co.create,above:e[0].above,arrow:e.some((t=>t.arrow))}}));class fo{constructor(t,e,i,s,o){this.view=t,this.source=e,this.field=i,this.setHover=s,this.hoverTime=o,this.hoverTimeout=-1,this.restartTimeout=-1,this.pending=null,this.lastMove={x:0,y:0,target:t.dom,time:0},this.checkHover=this.checkHover.bind(this),t.dom.addEventListener("mouseleave",this.mouseleave=this.mouseleave.bind(this)),t.dom.addEventListener("mousemove",this.mousemove=this.mousemove.bind(this))}update(){this.pending&&(this.pending=null,clearTimeout(this.restartTimeout),this.restartTimeout=setTimeout((()=>this.startHover()),20))}get active(){return this.view.state.field(this.field)}checkHover(){if(this.hoverTimeout=-1,this.active)return;let t=Date.now()-this.lastMove.time;t<this.hoverTime?this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime-t):this.startHover()}startHover(){clearTimeout(this.restartTimeout);let{view:t,lastMove:e}=this,i=t.docView.nearest(e.target);if(!i)return;let s,o=1;if(i instanceof $)s=i.posAtStart;else{if(s=t.posAtCoords(e),null==s)return;let i=t.coordsAtPos(s);if(!i||e.y<i.top||e.y>i.bottom||e.x<i.left-t.defaultCharacterWidth||e.x>i.right+t.defaultCharacterWidth)return;let n=t.bidiSpans(t.state.doc.lineAt(s)).find((t=>t.from<=s&&t.to>=s)),r=n&&n.dir==jt.RTL?-1:1;o=e.x<i.left?-r:r}let n=this.source(t,s,o);if(null==n?void 0:n.then){let e=this.pending={pos:s};n.then((i=>{this.pending==e&&(this.pending=null,i&&t.dispatch({effects:this.setHover.of(i)}))}),(e=>Et(t.state,e,"hover tooltip")))}else n&&t.dispatch({effects:this.setHover.of(n)})}mousemove(t){var e;this.lastMove={x:t.clientX,y:t.clientY,target:t.target,time:Date.now()},this.hoverTimeout<0&&(this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime));let i=this.active;if(i&&!po(this.lastMove.target)||this.pending){let{pos:s}=i||this.pending,o=null!==(e=null==i?void 0:i.end)&&void 0!==e?e:s;(s==o?this.view.posAtCoords(this.lastMove)==s:function(t,e,i,s,o,n){let r=t.scrollDOM.getBoundingClientRect(),l=t.documentTop+t.documentPadding.top+t.contentHeight;if(r.left>s||r.right<s||r.top>o||Math.min(r.bottom,l)<o)return!1;let h=t.posAtCoords({x:s,y:o},!1);return h>=e&&h<=i}(this.view,s,o,t.clientX,t.clientY))||(this.view.dispatch({effects:this.setHover.of(null)}),this.pending=null)}}mouseleave(t){clearTimeout(this.hoverTimeout),this.hoverTimeout=-1,this.active&&!po(t.relatedTarget)&&this.view.dispatch({effects:this.setHover.of(null)})}destroy(){clearTimeout(this.hoverTimeout),this.view.dom.removeEventListener("mouseleave",this.mouseleave),this.view.dom.removeEventListener("mousemove",this.mousemove)}}function po(t){for(let e=t;e;e=e.parentNode)if(1==e.nodeType&&e.classList.contains("cm-tooltip"))return!0;return!1}function go(t,e={}){let i=s.StateEffect.define(),o=s.StateField.define({create:()=>null,update(t,o){if(t&&(e.hideOnChange&&(o.docChanged||o.selection)||e.hideOn&&e.hideOn(o,t)))return null;if(t&&o.docChanged){let e=o.changes.mapPos(t.pos,-1,s.MapMode.TrackDel);if(null==e)return null;let i=Object.assign(Object.create(null),t);i.pos=e,null!=t.end&&(i.end=o.changes.mapPos(t.end)),t=i}for(let e of o.effects)e.is(i)&&(t=e.value),e.is(vo)&&(t=null);return t},provide:t=>ao.from(t)});return[o,Ht.define((s=>new fo(s,t,o,i,e.hoverTime||300))),uo]}function mo(t,e){let i=t.plugin(no);if(!i)return null;let s=i.manager.tooltips.indexOf(e);return s<0?null:i.manager.tooltipViews[s]}function wo(t){return t.facet(ao).some((t=>t))}const vo=s.StateEffect.define(),bo=vo.of(null);function yo(t){let e=t.plugin(no);e&&e.maybeMeasure()}const So=s.Facet.define({combine(t){let e,i;for(let s of t)e=e||s.topContainer,i=i||s.bottomContainer;return{topContainer:e,bottomContainer:i}}});function xo(t){return t?[So.of(t)]:[]}function Mo(t,e){let i=t.plugin(ko),s=i?i.specs.indexOf(e):-1;return s>-1?i.panels[s]:null}const ko=Ht.fromClass(class{constructor(t){this.input=t.state.facet(Do),this.specs=this.input.filter((t=>t)),this.panels=this.specs.map((e=>e(t)));let e=t.state.facet(So);this.top=new Co(t,!0,e.topContainer),this.bottom=new Co(t,!1,e.bottomContainer),this.top.sync(this.panels.filter((t=>t.top))),this.bottom.sync(this.panels.filter((t=>!t.top)));for(let t of this.panels)t.dom.classList.add("cm-panel"),t.mount&&t.mount()}update(t){let e=t.state.facet(So);this.top.container!=e.topContainer&&(this.top.sync([]),this.top=new Co(t.view,!0,e.topContainer)),this.bottom.container!=e.bottomContainer&&(this.bottom.sync([]),this.bottom=new Co(t.view,!1,e.bottomContainer)),this.top.syncClasses(),this.bottom.syncClasses();let i=t.state.facet(Do);if(i!=this.input){let e=i.filter((t=>t)),s=[],o=[],n=[],r=[];for(let i of e){let e,l=this.specs.indexOf(i);l<0?(e=i(t.view),r.push(e)):(e=this.panels[l],e.update&&e.update(t)),s.push(e),(e.top?o:n).push(e)}this.specs=e,this.panels=s,this.top.sync(o),this.bottom.sync(n);for(let t of r)t.dom.classList.add("cm-panel"),t.mount&&t.mount()}else for(let e of this.panels)e.update&&e.update(t)}destroy(){this.top.sync([]),this.bottom.sync([])}},{provide:t=>Xi.scrollMargins.of((e=>{let i=e.plugin(t);return i&&{top:i.top.scrollMargin(),bottom:i.bottom.scrollMargin()}}))});class Co{constructor(t,e,i){this.view=t,this.top=e,this.container=i,this.dom=void 0,this.classes="",this.panels=[],this.syncClasses()}sync(t){for(let e of this.panels)e.destroy&&t.indexOf(e)<0&&e.destroy();this.panels=t,this.syncDOM()}syncDOM(){if(0==this.panels.length)return void(this.dom&&(this.dom.remove(),this.dom=void 0));if(!this.dom){this.dom=document.createElement("div"),this.dom.className=this.top?"cm-panels cm-panels-top":"cm-panels cm-panels-bottom",this.dom.style[this.top?"top":"bottom"]="0";let t=this.container||this.view.dom;t.insertBefore(this.dom,this.top?t.firstChild:null)}let t=this.dom.firstChild;for(let e of this.panels)if(e.dom.parentNode==this.dom){for(;t!=e.dom;)t=Ao(t);t=t.nextSibling}else this.dom.insertBefore(e.dom,t);for(;t;)t=Ao(t)}scrollMargin(){return!this.dom||this.container?0:Math.max(0,this.top?this.dom.getBoundingClientRect().bottom-Math.max(0,this.view.scrollDOM.getBoundingClientRect().top):Math.min(innerHeight,this.view.scrollDOM.getBoundingClientRect().bottom)-this.dom.getBoundingClientRect().top)}syncClasses(){if(this.container&&this.classes!=this.view.themeClasses){for(let t of this.classes.split(" "))t&&this.container.classList.remove(t);for(let t of(this.classes=this.view.themeClasses).split(" "))t&&this.container.classList.add(t)}}}function Ao(t){let e=t.nextSibling;return t.remove(),e}const Do=s.Facet.define({enables:ko});class To extends s.RangeValue{compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}eq(t){return!1}destroy(t){}}To.prototype.elementClass="",To.prototype.toDOM=void 0,To.prototype.mapMode=s.MapMode.TrackBefore,To.prototype.startSide=To.prototype.endSide=-1,To.prototype.point=!0;const Oo=s.Facet.define(),Eo={class:"",renderEmptyElements:!1,elementStyle:"",markers:()=>s.RangeSet.empty,lineMarker:()=>null,widgetMarker:()=>null,lineMarkerChange:null,initialSpacer:null,updateSpacer:null,domEventHandlers:{}},Ro=s.Facet.define();function Bo(t){return[Ho(),Ro.of(Object.assign(Object.assign({},Eo),t))]}const Lo=s.Facet.define({combine:t=>t.some((t=>t))});function Ho(t){let e=[Po];return t&&!1===t.fixed&&e.push(Lo.of(!0)),e}const Po=Ht.fromClass(class{constructor(t){this.view=t,this.prevViewport=t.viewport,this.dom=document.createElement("div"),this.dom.className="cm-gutters",this.dom.setAttribute("aria-hidden","true"),this.dom.style.minHeight=this.view.contentHeight/this.view.scaleY+"px",this.gutters=t.state.facet(Ro).map((e=>new Wo(t,e)));for(let t of this.gutters)this.dom.appendChild(t.dom);this.fixed=!t.state.facet(Lo),this.fixed&&(this.dom.style.position="sticky"),this.syncGutters(!1),t.scrollDOM.insertBefore(this.dom,t.contentDOM)}update(t){if(this.updateGutters(t)){let e=this.prevViewport,i=t.view.viewport,s=Math.min(e.to,i.to)-Math.max(e.from,i.from);this.syncGutters(s<.8*(i.to-i.from))}t.geometryChanged&&(this.dom.style.minHeight=this.view.contentHeight+"px"),this.view.state.facet(Lo)!=!this.fixed&&(this.fixed=!this.fixed,this.dom.style.position=this.fixed?"sticky":""),this.prevViewport=t.view.viewport}syncGutters(t){let e=this.dom.nextSibling;t&&this.dom.remove();let i=s.RangeSet.iter(this.view.state.facet(Oo),this.view.viewport.from),o=[],n=this.gutters.map((t=>new Fo(t,this.view.viewport,-this.view.documentPadding.top)));for(let t of this.view.viewportLineBlocks)if(o.length&&(o=[]),Array.isArray(t.type)){let e=!0;for(let s of t.type)if(s.type==at.Text&&e){Vo(i,o,s.from);for(let t of n)t.line(this.view,s,o);e=!1}else if(s.widget)for(let t of n)t.widget(this.view,s)}else if(t.type==at.Text){Vo(i,o,t.from);for(let e of n)e.line(this.view,t,o)}else if(t.widget)for(let e of n)e.widget(this.view,t);for(let t of n)t.finish();t&&this.view.scrollDOM.insertBefore(this.dom,e)}updateGutters(t){let e=t.startState.facet(Ro),i=t.state.facet(Ro),o=t.docChanged||t.heightChanged||t.viewportChanged||!s.RangeSet.eq(t.startState.facet(Oo),t.state.facet(Oo),t.view.viewport.from,t.view.viewport.to);if(e==i)for(let e of this.gutters)e.update(t)&&(o=!0);else{o=!0;let s=[];for(let o of i){let i=e.indexOf(o);i<0?s.push(new Wo(this.view,o)):(this.gutters[i].update(t),s.push(this.gutters[i]))}for(let t of this.gutters)t.dom.remove(),s.indexOf(t)<0&&t.destroy();for(let t of s)this.dom.appendChild(t.dom);this.gutters=s}return o}destroy(){for(let t of this.gutters)t.destroy();this.dom.remove()}},{provide:t=>Xi.scrollMargins.of((e=>{let i=e.plugin(t);return i&&0!=i.gutters.length&&i.fixed?e.textDirection==jt.LTR?{left:i.dom.offsetWidth*e.scaleX}:{right:i.dom.offsetWidth*e.scaleX}:null}))});function No(t){return Array.isArray(t)?t:[t]}function Vo(t,e,i){for(;t.value&&t.from<=i;)t.from==i&&e.push(t.value),t.next()}class Fo{constructor(t,e,i){this.gutter=t,this.height=i,this.i=0,this.cursor=s.RangeSet.iter(t.markers,e.from)}addElement(t,e,i){let{gutter:s}=this,o=(e.top-this.height)/t.scaleY,n=e.height/t.scaleY;if(this.i==s.elements.length){let e=new zo(t,n,o,i);s.elements.push(e),s.dom.appendChild(e.dom)}else s.elements[this.i].update(t,n,o,i);this.height=e.bottom,this.i++}line(t,e,i){let s=[];Vo(this.cursor,s,e.from),i.length&&(s=s.concat(i));let o=this.gutter.config.lineMarker(t,e,s);o&&s.unshift(o);let n=this.gutter;(0!=s.length||n.config.renderEmptyElements)&&this.addElement(t,e,s)}widget(t,e){let i=this.gutter.config.widgetMarker(t,e.widget,e);i&&this.addElement(t,e,[i])}finish(){let t=this.gutter;for(;t.elements.length>this.i;){let e=t.elements.pop();t.dom.removeChild(e.dom),e.destroy()}}}class Wo{constructor(t,e){this.view=t,this.config=e,this.elements=[],this.spacer=null,this.dom=document.createElement("div"),this.dom.className="cm-gutter"+(this.config.class?" "+this.config.class:"");for(let i in e.domEventHandlers)this.dom.addEventListener(i,(s=>{let o,n=s.target;if(n!=this.dom&&this.dom.contains(n)){for(;n.parentNode!=this.dom;)n=n.parentNode;let t=n.getBoundingClientRect();o=(t.top+t.bottom)/2}else o=s.clientY;let r=t.lineBlockAtHeight(o-t.documentTop);e.domEventHandlers[i](t,r,s)&&s.preventDefault()}));this.markers=No(e.markers(t)),e.initialSpacer&&(this.spacer=new zo(t,0,0,[e.initialSpacer(t)]),this.dom.appendChild(this.spacer.dom),this.spacer.dom.style.cssText+="visibility: hidden; pointer-events: none")}update(t){let e=this.markers;if(this.markers=No(this.config.markers(t.view)),this.spacer&&this.config.updateSpacer){let e=this.config.updateSpacer(this.spacer.markers[0],t);e!=this.spacer.markers[0]&&this.spacer.update(t.view,0,0,[e])}let i=t.view.viewport;return!s.RangeSet.eq(this.markers,e,i.from,i.to)||!!this.config.lineMarkerChange&&this.config.lineMarkerChange(t)}destroy(){for(let t of this.elements)t.destroy()}}class zo{constructor(t,e,i,s){this.height=-1,this.above=0,this.markers=[],this.dom=document.createElement("div"),this.dom.className="cm-gutterElement",this.update(t,e,i,s)}update(t,e,i,s){this.height!=e&&(this.height=e,this.dom.style.height=e+"px"),this.above!=i&&(this.dom.style.marginTop=(this.above=i)?i+"px":""),function(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++)if(!t[i].compare(e[i]))return!1;return!0}(this.markers,s)||this.setMarkers(t,s)}setMarkers(t,e){let i="cm-gutterElement",s=this.dom.firstChild;for(let o=0,n=0;;){let r=n,l=o<e.length?e[o++]:null,h=!1;if(l){let t=l.elementClass;t&&(i+=" "+t);for(let t=n;t<this.markers.length;t++)if(this.markers[t].compare(l)){r=t,h=!0;break}}else r=this.markers.length;for(;n<r;){let t=this.markers[n++];if(t.toDOM){t.destroy(s);let e=s.nextSibling;s.remove(),s=e}}if(!l)break;l.toDOM&&(h?s=s.nextSibling:this.dom.insertBefore(l.toDOM(t),s)),h&&n++}this.dom.className=i,this.markers=e}destroy(){this.setMarkers(null,[])}}const Io=s.Facet.define(),Ko=s.Facet.define({combine:t=>(0,s.combineConfig)(t,{formatNumber:String,domEventHandlers:{}},{domEventHandlers(t,e){let i=Object.assign({},t);for(let t in e){let s=i[t],o=e[t];i[t]=s?(t,e,i)=>s(t,e,i)||o(t,e,i):o}return i}})});class qo extends To{constructor(t){super(),this.number=t}eq(t){return this.number==t.number}toDOM(){return document.createTextNode(this.number)}}function _o(t,e){return t.state.facet(Ko).formatNumber(e,t.state)}const Yo=Ro.compute([Ko],(t=>({class:"cm-lineNumbers",renderEmptyElements:!1,markers:t=>t.state.facet(Io),lineMarker:(t,e,i)=>i.some((t=>t.toDOM))?null:new qo(_o(t,t.state.doc.lineAt(e.from).number)),widgetMarker:()=>null,lineMarkerChange:t=>t.startState.facet(Ko)!=t.state.facet(Ko),initialSpacer:t=>new qo(_o(t,jo(t.state.doc.lines))),updateSpacer(t,e){let i=_o(e.view,jo(e.view.state.doc.lines));return i==t.number?t:new qo(i)},domEventHandlers:t.facet(Ko).domEventHandlers})));function Go(t={}){return[Ko.of(t),Ho(),Yo]}function jo(t){let e=9;for(;e<t;)e=10*e+9;return e}const Xo=new class extends To{constructor(){super(...arguments),this.elementClass="cm-activeLineGutter"}},Uo=Oo.compute(["selection"],(t=>{let e=[],i=-1;for(let s of t.selection.ranges){let o=t.doc.lineAt(s.head).from;o>i&&(i=o,e.push(Xo.range(o)))}return s.RangeSet.of(e)}));function $o(){return Uo}const Jo=new Map;function Qo(t){return Ht.define((e=>({decorations:t.createDeco(e),update(e){this.decorations=t.updateDeco(e,this.decorations)}})),{decorations:t=>t.decorations})}const Zo=Qo(new Es({regexp:/\t| +/g,decoration:t=>function(t){let e=Jo.get(t);return e||Jo.set(t,e=ct.mark({attributes:"\t"===t?{class:"cm-highlightTab"}:{class:"cm-highlightSpace","data-display":t.replace(/ /g,"·")}})),e}(t[0]),boundary:/\S/}));function tn(){return Zo}const en=Qo(new Es({regexp:/\s+$/g,decoration:ct.mark({class:"cm-trailingSpace"}),boundary:/\S/}));function sn(){return en}const on={HeightMap:ai,HeightOracle:oi,MeasuredHeights:ni,QueryType:li,ChangedRange:Yt,computeOrder:le,moveVisually:ce}},4434:(t,e,i)=>{i.d(e,{V:()=>r});const s="undefined"==typeof Symbol?"__ͼ":Symbol.for("ͼ"),o="undefined"==typeof Symbol?"__styleSet"+Math.floor(1e8*Math.random()):Symbol("styleSet"),n="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:{};class r{constructor(t,e){this.rules=[];let{finish:i}=e||{};function s(t){return/^@/.test(t)?[t]:t.split(/,\s*/)}function o(t,e,n,r){let l=[],h=/^@(\w+)\b/.exec(t[0]),a=h&&"keyframes"==h[1];if(h&&null==e)return n.push(t[0]+";");for(let i in e){let r=e[i];if(/&/.test(i))o(i.split(/,\s*/).map((e=>t.map((t=>e.replace(/&/,t))))).reduce(((t,e)=>t.concat(e))),r,n);else if(r&&"object"==typeof r){if(!h)throw new RangeError("The value of a property ("+i+") should be a primitive value.");o(s(i),r,l,a)}else null!=r&&l.push(i.replace(/_.*/,"").replace(/[A-Z]/g,(t=>"-"+t.toLowerCase()))+": "+r+";")}(l.length||a)&&n.push((!i||h||r?t:t.map(i)).join(", ")+" {"+l.join(" ")+"}")}for(let e in t)o(s(e),t[e],this.rules)}getRules(){return this.rules.join("\n")}static newName(){let t=n[s]||1;return n[s]=t+1,"ͼ"+t.toString(36)}static mount(t,e,i){let s=t[o],n=i&&i.nonce;s?n&&s.setNonce(n):s=new h(t,n),s.mount(Array.isArray(e)?e:[e])}}let l=new Map;class h{constructor(t,e){let i=t.ownerDocument||t,s=i.defaultView;if(!t.head&&t.adoptedStyleSheets&&s.CSSStyleSheet){let e=l.get(i);if(e)return t.adoptedStyleSheets=[e.sheet,...t.adoptedStyleSheets],t[o]=e;this.sheet=new s.CSSStyleSheet,t.adoptedStyleSheets=[this.sheet,...t.adoptedStyleSheets],l.set(i,this)}else{this.styleTag=i.createElement("style"),e&&this.styleTag.setAttribute("nonce",e);let s=t.head||t;s.insertBefore(this.styleTag,s.firstChild)}this.modules=[],t[o]=this}mount(t){let e=this.sheet,i=0,s=0;for(let o=0;o<t.length;o++){let n=t[o],r=this.modules.indexOf(n);if(r<s&&r>-1&&(this.modules.splice(r,1),s--,r=-1),-1==r){if(this.modules.splice(s++,0,n),e)for(let t=0;t<n.rules.length;t++)e.insertRule(n.rules[t],i++)}else{for(;s<r;)i+=this.modules[s++].rules.length;i+=n.rules.length,s++}}if(!e){let t="";for(let e=0;e<this.modules.length;e++)t+=this.modules[e].getRules()+"\n";this.styleTag.textContent=t}}setNonce(t){this.styleTag&&this.styleTag.getAttribute("nonce")!=t&&this.styleTag.setAttribute("nonce",t)}}}}]);
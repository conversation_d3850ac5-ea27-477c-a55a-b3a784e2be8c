"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[8032],{18032:(t,e,s)=>{s.r(e),s.d(e,{DSVModel:()=>a});var i=s(20998),r=s(95585),o=s(48234);const n={quotes:o.G,noquotes:o.z};class a extends r.DataModel{constructor(t){super(),this._rowCount=0,this._header=[],this._columnOffsets=new Uint32Array(0),this._columnOffsetsStartingRow=0,this._maxCacheGet=1e3,this._rowOffsets=new Uint32Array(0),this._delayedParse=null,this._startedParsing=!1,this._doneParsing=!1,this._isDisposed=!1,this._ready=new i.PromiseDelegate;let{data:e,delimiter:s=",",rowDelimiter:r,quote:o='"',quoteParser:n,header:a=!0,initialRows:h=500}=t;if(this._rawData=e,this._delimiter=s,this._quote=o,this._quoteEscaped=new RegExp(o+o,"g"),this._initialRows=h,void 0===r){const t=e.slice(0,5e3).indexOf("\r");r=-1===t?"\n":"\n"===e[t+1]?"\r\n":"\r"}if(this._rowDelimiter=r,void 0===n&&(n=e.indexOf(o)>=0),this._parser=n?"quotes":"noquotes",this.parseAsync(),!0===a&&this._columnCount>0){const t=[];for(let e=0;e<this._columnCount;e++)t.push(this._getField(0,e));this._header=t}}get isDisposed(){return this._isDisposed}get ready(){return this._ready.promise}get rawData(){return this._rawData}set rawData(t){this._rawData=t}get initialRows(){return this._initialRows}set initialRows(t){this._initialRows=t}get header(){return this._header}set header(t){this._header=t}get delimiter(){return this._delimiter}get rowDelimiter(){return this._rowDelimiter}get doneParsing(){return this._doneParsing}rowCount(t){return"body"===t?0===this._header.length?this._rowCount:this._rowCount-1:1}columnCount(t){return"body"===t?this._columnCount:1}data(t,e,s){let i;switch(t){case"body":i=0===this._header.length?this._getField(e,s):this._getField(e+1,s);break;case"column-header":i=0===this._header.length?(s+1).toString():this._header[s];break;case"row-header":i=(e+1).toString();break;case"corner-header":i="";break;default:throw"unreachable"}return i}dispose(){this._isDisposed||(this._isDisposed=!0,this._columnCount=void 0,this._rowCount=void 0,this._rowOffsets=null,this._columnOffsets=null,this._rawData=null,!1===this._doneParsing&&(this.ready.catch((()=>{})),this._ready.reject(void 0)),null!==this._delayedParse&&window.clearTimeout(this._delayedParse))}getOffsetIndex(t,e){const s=this._columnCount;let i=(t-this._columnOffsetsStartingRow)*s;if((i<0||i>this._columnOffsets.length)&&(this._columnOffsets.fill(4294967295),this._columnOffsetsStartingRow=t,i=0),4294967295===this._columnOffsets[i]){let e=1;for(;e<=this._maxCacheGet&&16777215===this._columnOffsets[i+e*s];)e++;const{offsets:r}=n[this._parser]({data:this._rawData,delimiter:this._delimiter,rowDelimiter:this._rowDelimiter,quote:this._quote,columnOffsets:!0,maxRows:e,ncols:s,startIndex:this._rowOffsets[t]});for(let t=0;t<r.length;t++)this._columnOffsets[i+t]=r[t]}return this._columnOffsets[i+e]}parseAsync(){let t=this._initialRows,e=Math.pow(2,32)-1;const s=t=>{try{this._computeRowOffsets(t)}catch(e){if("quotes"!==this._parser)throw e;console.warn(e),this._parser="noquotes",this._resetParser(),this._computeRowOffsets(t)}return this._doneParsing};if(this._resetParser(),s(t))return;const i=()=>{const r=s(t+e);t+=e,e<1e6&&(e*=2),this._delayedParse=r?null:window.setTimeout(i,30)};this._delayedParse=window.setTimeout(i,30)}_computeRowOffsets(t=4294967295){var e;if(this._rowCount>=t||!0===this._doneParsing)return;void 0===this._columnCount&&(this._columnCount=n[this._parser]({data:this._rawData,delimiter:this._delimiter,rowDelimiter:this._rowDelimiter,quote:this._quote,columnOffsets:!0,maxRows:1}).ncols);const s=this._rowCount>0?1:0,{nrows:i,offsets:r}=n[this._parser]({data:this._rawData,startIndex:null!==(e=this._rowOffsets[this._rowCount-s])&&void 0!==e?e:0,delimiter:this._delimiter,rowDelimiter:this._rowDelimiter,quote:this._quote,columnOffsets:!1,maxRows:t-this._rowCount+s});if(this._startedParsing&&i<=s)return this._doneParsing=!0,void this._ready.resolve(void 0);this._startedParsing=!0;const o=this._rowCount,a=Math.min(i,s);if(this._rowCount=o+i-a,this._rowCount<t&&(this._doneParsing=!0,this._ready.resolve(void 0)),this._rowCount>o){const t=this._rowOffsets;this._rowOffsets=new Uint32Array(this._rowCount),this._rowOffsets.set(t),this._rowOffsets.set(r,o-a)}const h=Math.floor(33554432/this._columnCount);if(o<=h)if(this._rowCount<=h){const t=this._columnOffsets;this._columnOffsets=new Uint32Array(this._rowCount*this._columnCount),this._columnOffsets.set(t),this._columnOffsets.fill(4294967295,t.length)}else{const t=this._columnOffsets;this._columnOffsets=new Uint32Array(Math.min(this._maxCacheGet,h)*this._columnCount),this._columnOffsets.set(t.subarray(0,this._columnOffsets.length)),this._columnOffsets.fill(4294967295,t.length),this._columnOffsetsStartingRow=0}let l=o;this._header.length>0&&(l-=1),this.emitChanged({type:"rows-inserted",region:"body",index:l,span:this._rowCount-o})}_getField(t,e){let s,i;const r=this.getOffsetIndex(t,e);let o=0,n=0;return e===this._columnCount-1?t<this._rowCount-1?(i=this.getOffsetIndex(t+1,0),o+=this._rowDelimiter.length):(i=this._rawData.length,this._rawData[i-1]===this._rowDelimiter[this._rowDelimiter.length-1]&&(o+=this._rowDelimiter.length)):(i=this.getOffsetIndex(t,e+1),r<i&&this._rawData[i-1]===this._delimiter&&(o+=1)),this._rawData[r]===this._quote&&(n+=1,o+=1),s=this._rawData.slice(r+n,i-o),1===n&&-1!==s.indexOf(this._quote)&&(s=s.replace(this._quoteEscaped,this._quote)),s}_resetParser(){this._columnCount=void 0,this._rowOffsets=new Uint32Array(0),this._rowCount=0,this._startedParsing=!1,this._columnOffsets=new Uint32Array(0),!1===this._doneParsing&&(this.ready.catch((()=>{})),this._ready.reject(void 0)),this._doneParsing=!1,this._ready=new i.PromiseDelegate,null!==this._delayedParse&&(window.clearTimeout(this._delayedParse),this._delayedParse=null),this.emitChanged({type:"model-reset"})}}},48234:(t,e,s)=>{var i,r;function o(t){const{data:e,columnOffsets:s,delimiter:o=",",startIndex:n=0,maxRows:a=4294967295,rowDelimiter:h="\r\n",quote:l='"'}=t;let u=t.ncols,_=0;const d=[],f=o.charCodeAt(0),c=l.charCodeAt(0),w=10,m=e.length,{QUOTED_FIELD:g,QUOTED_FIELD_QUOTE:O,UNQUOTED_FIELD:C,NEW_FIELD:D,NEW_ROW:p}=i,{CR:E,LF:b,CRLF:x}=r,[y,R]="\r\n"===h?[x,2]:"\r"===h?[E,1]:[b,1];let P,$=p,U=n,k=0;for(;U<m;){switch($===p&&(d.push(U),k=1),P=e.charCodeAt(U),$){case p:case D:switch(P){case c:$=g;break;case f:$=D;break;case 13:if(y===E)$=p;else{if(y!==x||e.charCodeAt(U+1)!==w)throw`string index ${U} (in row ${_}, column ${k}): carriage return found, but not as part of a row delimiter C ${e.charCodeAt(U+1)}`;U++,$=p}break;case w:if(y!==b)throw`string index ${U} (in row ${_}, column ${k}): line feed found, but row delimiter starts with a carriage return`;$=p;break;default:$=C}break;case g:if(U=e.indexOf(l,U),U<0)throw`string index ${U} (in row ${_}, column ${k}): mismatched quote`;$=O;break;case O:switch(P){case c:$=g;break;case f:$=D;break;case 13:if(y===E)$=p;else{if(y!==x||e.charCodeAt(U+1)!==w)throw`string index ${U} (in row ${_}, column ${k}): carriage return found, but not as part of a row delimiter C ${e.charCodeAt(U+1)}`;U++,$=p}break;case w:if(y!==b)throw`string index ${U} (in row ${_}, column ${k}): line feed found, but row delimiter starts with a carriage return`;$=p;break;default:throw`string index ${U} (in row ${_}, column ${k}): quote in escaped field not followed by quote, delimiter, or row delimiter`}break;case C:for(;U<m&&(P=e.charCodeAt(U),P!==f&&P!==w&&13!==P);)U++;switch(P){case f:$=D;break;case 13:if(y===E)$=p;else{if(y!==x||e.charCodeAt(U+1)!==w)throw`string index ${U} (in row ${_}, column ${k}): carriage return found, but not as part of a row delimiter C ${e.charCodeAt(U+1)}`;U++,$=p}break;case w:if(y!==b)throw`string index ${U} (in row ${_}, column ${k}): line feed found, but row delimiter starts with a carriage return`;$=p;break;default:continue}break;default:throw`string index ${U} (in row ${_}, column ${k}): state not recognized`}switch(U++,$){case p:if(_++,void 0===u){if(1!==_)throw new Error("Error parsing default number of columns");u=k}if(!0===s)if(k<u)for(;k<u;k++)d.push(U-R);else k>u&&(d.length=d.length-(k-u));if(_===a)return{nrows:_,ncols:s?u:0,offsets:d};break;case D:!0===s&&d.push(U),k++}}if($!==p&&(_++,!0===s))if(void 0===u&&(u=k),k<u)for(;k<u;k++)d.push(U-(R-1));else k>u&&(d.length=d.length-(k-u));return{nrows:_,ncols:s&&null!=u?u:0,offsets:d}}function n(t){const{data:e,columnOffsets:s,delimiter:i=",",rowDelimiter:r="\r\n",startIndex:o=0,maxRows:n=4294967295}=t;let a=t.ncols;const h=[];let l=0;const u=r.length;let _=o;const d=e.length;let f,c,w,m,g;for(f=o;-1!==f&&l<n&&_<d;){if(h.push(_),l++,f=e.indexOf(r,_),g=-1===f?d:f,!0===s)if(c=1,w=e.slice(_,g),m=w.indexOf(i),void 0===a){for(;-1!==m;)h.push(_+m+1),c++,m=w.indexOf(i,m+1);a=c}else{for(;-1!==m&&c<a;)h.push(_+m+1),c++,m=w.indexOf(i,m+1);for(;c<a;)h.push(g),c++}_=g+u}return{nrows:l,ncols:s&&null!=a?a:0,offsets:h}}s.d(e,{G:()=>o,z:()=>n}),function(t){t[t.QUOTED_FIELD=0]="QUOTED_FIELD",t[t.QUOTED_FIELD_QUOTE=1]="QUOTED_FIELD_QUOTE",t[t.UNQUOTED_FIELD=2]="UNQUOTED_FIELD",t[t.NEW_FIELD=3]="NEW_FIELD",t[t.NEW_ROW=4]="NEW_ROW"}(i||(i={})),function(t){t[t.CR=0]="CR",t[t.CRLF=1]="CRLF",t[t.LF=2]="LF"}(r||(r={}))}}]);
"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[6962],{56962:(e,t,r)=>{r.r(t),r.d(t,{default:()=>T});var a=r(3053),n=r(12982),o=r(95101),s=r(36768),i=r(71677),c=r(68239);const l="@jupyterlab/htmlviewer-extension:plugin",d="HTML Viewer";var u;!function(e){e.trustHTML="htmlviewer:trust-html"}(u||(u={}));const m={activate:function(e,t,r,a,s,i){let T;const h=t.load("jupyterlab");i&&(i.addFactory(d,"refresh",(e=>o.ToolbarItems.createRefreshButton(e,t))),i.addFactory(d,"trust",(e=>o.ToolbarItems.createTrustButton(e,t))),s&&(T=(0,n.createToolbarFactory)(i,s,d,m.id,t)));const y={name:"html",contentType:"file",fileFormat:"text",displayName:h.__("HTML File"),extensions:[".html"],mimeTypes:["text/html"],icon:c.html5Icon};e.docRegistry.addFileType(y);const p=new o.HTMLViewerFactory({name:d,label:h.__("HTML Viewer"),fileTypes:["html"],defaultFor:["html"],readOnly:!0,toolbarFactory:T,translator:t}),g=new n.WidgetTracker({namespace:"htmlviewer"});a&&a.restore(g,{command:"docmanager:open",args:e=>({path:e.context.path,factory:"HTML Viewer"}),name:e=>e.context.path});let f=!1;if(s){const t=s.load(l),r=e=>{f=e.get("trustByDefault").composite};Promise.all([t,e.restored]).then((([e])=>{r(e),e.changed.connect((e=>{r(e)}))})).catch((e=>{console.error(e.message)}))}return e.docRegistry.addWidgetFactory(p),p.widgetCreated.connect(((t,r)=>{var a,n;g.add(r),r.context.pathChanged.connect((()=>{g.save(r)})),r.trustedChanged.connect((()=>{e.commands.notifyCommandChanged(u.trustHTML)})),r.trusted=f,r.title.icon=y.icon,r.title.iconClass=null!==(a=y.iconClass)&&void 0!==a?a:"",r.title.iconLabel=null!==(n=y.iconLabel)&&void 0!==n?n:""})),e.commands.addCommand(u.trustHTML,{label:h.__("Trust HTML File"),caption:h.__("Whether the HTML file is trusted.\n    Trusting the file allows scripts to run in it,\n    which may result in security risks.\n    Only enable for files you trust."),isEnabled:()=>!!g.currentWidget,isToggled:()=>{const e=g.currentWidget;return!!e&&-1!==e.content.sandbox.indexOf("allow-scripts")},execute:()=>{const e=g.currentWidget;e&&(e.trusted=!e.trusted)}}),r&&r.addItem({command:u.trustHTML,category:h.__("File Operations")}),g},id:l,description:"Adds HTML file viewer and provides its tracker.",provides:o.IHTMLViewerTracker,requires:[i.ITranslator],optional:[n.ICommandPalette,a.ILayoutRestorer,s.ISettingRegistry,n.IToolbarWidgetRegistry],autoStart:!0},T=m}}]);
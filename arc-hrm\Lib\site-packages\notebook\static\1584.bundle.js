"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[1584],{31584:(t,e,n)=>{n.r(e),n.d(e,{DocInput:()=>b,HighlightStyle:()=>Dt,IndentContext:()=>R,LRLanguage:()=>p,Language:()=>f,LanguageDescription:()=>O,LanguageSupport:()=>N,ParseContext:()=>S,StreamLanguage:()=>ee,StringStream:()=>Qt,TreeIndentContext:()=>_,bracketMatching:()=>Ht,bracketMatchingHandle:()=>Gt,codeFolding:()=>bt,continuedIndent:()=>J,defaultHighlightStyle:()=>Ft,defineLanguageFacet:()=>u,delimitedIndent:()=>z,ensureSyntaxTree:()=>m,flatIndent:()=>G,foldAll:()=>dt,foldCode:()=>ut,foldEffect:()=>nt,foldGutter:()=>At,foldInside:()=>Q,foldKeymap:()=>kt,foldNodeProp:()=>Z,foldService:()=>K,foldState:()=>it,foldable:()=>tt,foldedRanges:()=>ot,forceParsing:()=>w,getIndentUnit:()=>B,getIndentation:()=>L,highlightingFor:()=>Bt,indentNodeProp:()=>V,indentOnInput:()=>Y,indentRange:()=>F,indentService:()=>I,indentString:()=>M,indentUnit:()=>E,language:()=>D,languageDataProp:()=>h,matchBrackets:()=>Yt,sublanguageProp:()=>c,syntaxHighlighting:()=>Et,syntaxParserRunning:()=>v,syntaxTree:()=>g,syntaxTreeAvailable:()=>k,toggleFold:()=>mt,unfoldAll:()=>pt,unfoldCode:()=>ct,unfoldEffect:()=>rt});var r,s=n(73887),i=n(17811),o=n(30890),a=n(66361),l=n(4434);const h=new s.NodeProp;function u(t){return i.Facet.define({combine:t?e=>e.concat(t):void 0})}const c=new s.NodeProp;class f{constructor(t,e,n=[],r=""){this.data=t,this.name=r,i.EditorState.prototype.hasOwnProperty("tree")||Object.defineProperty(i.EditorState.prototype,"tree",{get(){return g(this)}}),this.parser=e,this.extension=[D.of(this),i.EditorState.languageData.of(((t,e,n)=>{let r=d(t,e,n),s=r.type.prop(h);if(!s)return[];let i=t.facet(s),o=r.type.prop(c);if(o){let s=r.resolve(e-r.from,n);for(let e of o)if(e.test(s,t)){let n=t.facet(e.facet);return"replace"==e.type?n:n.concat(i)}}return i}))].concat(n)}isActiveAt(t,e,n=-1){return d(t,e,n).type.prop(h)==this.data}findRegions(t){let e=t.facet(D);if((null==e?void 0:e.data)==this.data)return[{from:0,to:t.doc.length}];if(!e||!e.allowsNesting)return[];let n=[],r=(t,e)=>{if(t.prop(h)==this.data)return void n.push({from:e,to:e+t.length});let i=t.prop(s.NodeProp.mounted);if(i){if(i.tree.prop(h)==this.data){if(i.overlay)for(let t of i.overlay)n.push({from:t.from+e,to:t.to+e});else n.push({from:e,to:e+t.length});return}if(i.overlay){let t=n.length;if(r(i.tree,i.overlay[0].from+e),n.length>t)return}}for(let n=0;n<t.children.length;n++){let i=t.children[n];i instanceof s.Tree&&r(i,t.positions[n]+e)}};return r(g(t),0),n}get allowsNesting(){return!0}}function d(t,e,n){let r=t.facet(D),i=g(t).topNode;if(!r||r.allowsNesting)for(let t=i;t;t=t.enter(e,n,s.IterMode.ExcludeBuffers))t.type.isTop&&(i=t);return i}f.setState=i.StateEffect.define();class p extends f{constructor(t,e,n){super(t,e,[],n),this.parser=e}static define(t){let e=u(t.languageData);return new p(e,t.parser.configure({props:[h.add((t=>t.isTop?e:void 0))]}),t.name)}configure(t,e){return new p(this.data,this.parser.configure(t),e||this.name)}get allowsNesting(){return this.parser.hasWrappers()}}function g(t){let e=t.field(f.state,!1);return e?e.tree:s.Tree.empty}function m(t,e,n=50){var r;let s=null===(r=t.field(f.state,!1))||void 0===r?void 0:r.context;if(!s)return null;let i=s.viewport;s.updateViewport({from:0,to:e});let o=s.isDone(e)||s.work(n,e)?s.tree:null;return s.updateViewport(i),o}function k(t,e=t.doc.length){var n;return(null===(n=t.field(f.state,!1))||void 0===n?void 0:n.context.isDone(e))||!1}function w(t,e=t.viewport.to,n=100){let r=m(t.state,e,n);return r!=g(t.state)&&t.dispatch({}),!!r}function v(t){var e;return(null===(e=t.plugin(C))||void 0===e?void 0:e.isWorking())||!1}class b{constructor(t){this.doc=t,this.cursorPos=0,this.string="",this.cursor=t.iter()}get length(){return this.doc.length}syncTo(t){return this.string=this.cursor.next(t-this.cursorPos).value,this.cursorPos=t+this.string.length,this.cursorPos-this.string.length}chunk(t){return this.syncTo(t),this.string}get lineChunks(){return!0}read(t,e){let n=this.cursorPos-this.string.length;return t<n||e>=this.cursorPos?this.doc.sliceString(t,e):this.string.slice(t-n,e-n)}}let y=null;class S{constructor(t,e,n=[],r,s,i,o,a){this.parser=t,this.state=e,this.fragments=n,this.tree=r,this.treeLen=s,this.viewport=i,this.skipped=o,this.scheduleOn=a,this.parse=null,this.tempSkipped=[]}static create(t,e,n){return new S(t,e,[],s.Tree.empty,0,n,[],null)}startParse(){return this.parser.startParse(new b(this.state.doc),this.fragments)}work(t,e){return null!=e&&e>=this.state.doc.length&&(e=void 0),this.tree!=s.Tree.empty&&this.isDone(null!=e?e:this.state.doc.length)?(this.takeTree(),!0):this.withContext((()=>{var n;if("number"==typeof t){let e=Date.now()+t;t=()=>Date.now()>e}for(this.parse||(this.parse=this.startParse()),null!=e&&(null==this.parse.stoppedAt||this.parse.stoppedAt>e)&&e<this.state.doc.length&&this.parse.stopAt(e);;){let r=this.parse.advance();if(r){if(this.fragments=this.withoutTempSkipped(s.TreeFragment.addTree(r,this.fragments,null!=this.parse.stoppedAt)),this.treeLen=null!==(n=this.parse.stoppedAt)&&void 0!==n?n:this.state.doc.length,this.tree=r,this.parse=null,!(this.treeLen<(null!=e?e:this.state.doc.length)))return!0;this.parse=this.startParse()}if(t())return!1}}))}takeTree(){let t,e;this.parse&&(t=this.parse.parsedPos)>=this.treeLen&&((null==this.parse.stoppedAt||this.parse.stoppedAt>t)&&this.parse.stopAt(t),this.withContext((()=>{for(;!(e=this.parse.advance()););})),this.treeLen=t,this.tree=e,this.fragments=this.withoutTempSkipped(s.TreeFragment.addTree(this.tree,this.fragments,!0)),this.parse=null)}withContext(t){let e=y;y=this;try{return t()}finally{y=e}}withoutTempSkipped(t){for(let e;e=this.tempSkipped.pop();)t=x(t,e.from,e.to);return t}changes(t,e){let{fragments:n,tree:r,treeLen:i,viewport:o,skipped:a}=this;if(this.takeTree(),!t.empty){let e=[];if(t.iterChangedRanges(((t,n,r,s)=>e.push({fromA:t,toA:n,fromB:r,toB:s}))),n=s.TreeFragment.applyChanges(n,e),r=s.Tree.empty,i=0,o={from:t.mapPos(o.from,-1),to:t.mapPos(o.to,1)},this.skipped.length){a=[];for(let e of this.skipped){let n=t.mapPos(e.from,1),r=t.mapPos(e.to,-1);n<r&&a.push({from:n,to:r})}}}return new S(this.parser,e,n,r,i,o,a,this.scheduleOn)}updateViewport(t){if(this.viewport.from==t.from&&this.viewport.to==t.to)return!1;this.viewport=t;let e=this.skipped.length;for(let e=0;e<this.skipped.length;e++){let{from:n,to:r}=this.skipped[e];n<t.to&&r>t.from&&(this.fragments=x(this.fragments,n,r),this.skipped.splice(e--,1))}return!(this.skipped.length>=e||(this.reset(),0))}reset(){this.parse&&(this.takeTree(),this.parse=null)}skipUntilInView(t,e){this.skipped.push({from:t,to:e})}static getSkippingParser(t){return new class extends s.Parser{createParse(e,n,r){let i=r[0].from,o=r[r.length-1].to;return{parsedPos:i,advance(){let e=y;if(e){for(let t of r)e.tempSkipped.push(t);t&&(e.scheduleOn=e.scheduleOn?Promise.all([e.scheduleOn,t]):t)}return this.parsedPos=o,new s.Tree(s.NodeType.none,[],[],o-i)},stoppedAt:null,stopAt(){}}}}}isDone(t){t=Math.min(t,this.state.doc.length);let e=this.fragments;return this.treeLen>=t&&e.length&&0==e[0].from&&e[0].to>=t}static get(){return y}}function x(t,e,n){return s.TreeFragment.applyChanges(t,[{fromA:e,toA:n,fromB:e,toB:n}])}class P{constructor(t){this.context=t,this.tree=t.tree}apply(t){if(!t.docChanged&&this.tree==this.context.tree)return this;let e=this.context.changes(t.changes,t.state),n=this.context.treeLen==t.startState.doc.length?void 0:Math.max(t.changes.mapPos(this.context.treeLen),e.viewport.to);return e.work(20,n)||e.takeTree(),new P(e)}static init(t){let e=Math.min(3e3,t.doc.length),n=S.create(t.facet(D).parser,t,{from:0,to:e});return n.work(20,e)||n.takeTree(),new P(n)}}f.state=i.StateField.define({create:P.init,update(t,e){for(let t of e.effects)if(t.is(f.setState))return t.value;return e.startState.facet(D)!=e.state.facet(D)?P.init(e.state):t.apply(e)}});let T=t=>{let e=setTimeout((()=>t()),500);return()=>clearTimeout(e)};"undefined"!=typeof requestIdleCallback&&(T=t=>{let e=-1,n=setTimeout((()=>{e=requestIdleCallback(t,{timeout:400})}),100);return()=>e<0?clearTimeout(n):cancelIdleCallback(e)});const A="undefined"!=typeof navigator&&(null===(r=navigator.scheduling)||void 0===r?void 0:r.isInputPending)?()=>navigator.scheduling.isInputPending():null,C=o.ViewPlugin.fromClass(class{constructor(t){this.view=t,this.working=null,this.workScheduled=0,this.chunkEnd=-1,this.chunkBudget=-1,this.work=this.work.bind(this),this.scheduleWork()}update(t){let e=this.view.state.field(f.state).context;(e.updateViewport(t.view.viewport)||this.view.viewport.to>e.treeLen)&&this.scheduleWork(),(t.docChanged||t.selectionSet)&&(this.view.hasFocus&&(this.chunkBudget+=50),this.scheduleWork()),this.checkAsyncSchedule(e)}scheduleWork(){if(this.working)return;let{state:t}=this.view,e=t.field(f.state);e.tree==e.context.tree&&e.context.isDone(t.doc.length)||(this.working=T(this.work))}work(t){this.working=null;let e=Date.now();if(this.chunkEnd<e&&(this.chunkEnd<0||this.view.hasFocus)&&(this.chunkEnd=e+3e4,this.chunkBudget=3e3),this.chunkBudget<=0)return;let{state:n,viewport:{to:r}}=this.view,s=n.field(f.state);if(s.tree==s.context.tree&&s.context.isDone(r+1e5))return;let i=Date.now()+Math.min(this.chunkBudget,100,t&&!A?Math.max(25,t.timeRemaining()-5):1e9),o=s.context.treeLen<r&&n.doc.length>r+1e3,a=s.context.work((()=>A&&A()||Date.now()>i),r+(o?0:1e5));this.chunkBudget-=Date.now()-e,(a||this.chunkBudget<=0)&&(s.context.takeTree(),this.view.dispatch({effects:f.setState.of(new P(s.context))})),this.chunkBudget>0&&(!a||o)&&this.scheduleWork(),this.checkAsyncSchedule(s.context)}checkAsyncSchedule(t){t.scheduleOn&&(this.workScheduled++,t.scheduleOn.then((()=>this.scheduleWork())).catch((t=>(0,o.logException)(this.view.state,t))).then((()=>this.workScheduled--)),t.scheduleOn=null)}destroy(){this.working&&this.working()}isWorking(){return!!(this.working||this.workScheduled>0)}},{eventHandlers:{focus(){this.scheduleWork()}}}),D=i.Facet.define({combine:t=>t.length?t[0]:null,enables:t=>[f.state,C,o.EditorView.contentAttributes.compute([t],(e=>{let n=e.facet(t);return n&&n.name?{"data-language":n.name}:{}}))]});class N{constructor(t,e=[]){this.language=t,this.support=e,this.extension=[t,e]}}class O{constructor(t,e,n,r,s,i=void 0){this.name=t,this.alias=e,this.extensions=n,this.filename=r,this.loadFunc=s,this.support=i,this.loading=null}load(){return this.loading||(this.loading=this.loadFunc().then((t=>this.support=t),(t=>{throw this.loading=null,t})))}static of(t){let{load:e,support:n}=t;if(!e){if(!n)throw new RangeError("Must pass either 'load' or 'support' to LanguageDescription.of");e=()=>Promise.resolve(n)}return new O(t.name,(t.alias||[]).concat(t.name).map((t=>t.toLowerCase())),t.extensions||[],t.filename,e,n)}static matchFilename(t,e){for(let n of t)if(n.filename&&n.filename.test(e))return n;let n=/\.([^.]+)$/.exec(e);if(n)for(let e of t)if(e.extensions.indexOf(n[1])>-1)return e;return null}static matchLanguageName(t,e,n=!0){e=e.toLowerCase();for(let n of t)if(n.alias.some((t=>t==e)))return n;if(n)for(let n of t)for(let t of n.alias){let r=e.indexOf(t);if(r>-1&&(t.length>2||!/\w/.test(e[r-1])&&!/\w/.test(e[r+t.length])))return n}return null}}const I=i.Facet.define(),E=i.Facet.define({combine:t=>{if(!t.length)return"  ";let e=t[0];if(!e||/\S/.test(e)||Array.from(e).some((t=>t!=e[0])))throw new Error("Invalid indent unit: "+JSON.stringify(t[0]));return e}});function B(t){let e=t.facet(E);return 9==e.charCodeAt(0)?t.tabSize*e.length:e.length}function M(t,e){let n="",r=t.tabSize,s=t.facet(E)[0];if("\t"==s){for(;e>=r;)n+="\t",e-=r;s=" "}for(let t=0;t<e;t++)n+=s;return n}function L(t,e){t instanceof i.EditorState&&(t=new R(t));for(let n of t.state.facet(I)){let r=n(t,e);if(void 0!==r)return r}let n=g(t.state);return n.length>=e?function(t,e,n){let r=e.resolveStack(n),s=r.node.enterUnfinishedNodesBefore(n);if(s!=r.node){let t=[];for(let e=s;e!=r.node;e=e.parent)t.push(e);for(let e=t.length-1;e>=0;e--)r={node:t[e],next:r}}return U(r,t,n)}(t,n,e):null}function F(t,e,n){let r=Object.create(null),s=new R(t,{overrideIndentation:t=>{var e;return null!==(e=r[t])&&void 0!==e?e:-1}}),i=[];for(let o=e;o<=n;){let e=t.doc.lineAt(o);o=e.to+1;let n=L(s,e.from);if(null==n)continue;/\S/.test(e.text)||(n=0);let a=/^\s*/.exec(e.text)[0],l=M(t,n);a!=l&&(r[e.from]=n,i.push({from:e.from,to:e.from+a.length,insert:l}))}return t.changes(i)}class R{constructor(t,e={}){this.state=t,this.options=e,this.unit=B(t)}lineAt(t,e=1){let n=this.state.doc.lineAt(t),{simulateBreak:r,simulateDoubleBreak:s}=this.options;return null!=r&&r>=n.from&&r<=n.to?s&&r==t?{text:"",from:t}:(e<0?r<t:r<=t)?{text:n.text.slice(r-n.from),from:r}:{text:n.text.slice(0,r-n.from),from:n.from}:n}textAfterPos(t,e=1){if(this.options.simulateDoubleBreak&&t==this.options.simulateBreak)return"";let{text:n,from:r}=this.lineAt(t,e);return n.slice(t-r,Math.min(n.length,t+100-r))}column(t,e=1){let{text:n,from:r}=this.lineAt(t,e),s=this.countColumn(n,t-r),i=this.options.overrideIndentation?this.options.overrideIndentation(r):-1;return i>-1&&(s+=i-this.countColumn(n,n.search(/\S|$/))),s}countColumn(t,e=t.length){return(0,i.countColumn)(t,this.state.tabSize,e)}lineIndent(t,e=1){let{text:n,from:r}=this.lineAt(t,e),s=this.options.overrideIndentation;if(s){let t=s(r);if(t>-1)return t}return this.countColumn(n,n.search(/\S|$/))}get simulatedBreak(){return this.options.simulateBreak||null}}const V=new s.NodeProp;function U(t,e,n){for(let r=t;r;r=r.next){let t=j(r.node);if(t)return t(_.create(e,n,r))}return 0}function j(t){let e=t.type.prop(V);if(e)return e;let n,r=t.firstChild;if(r&&(n=r.type.prop(s.NodeProp.closedBy))){let e=t.lastChild,r=e&&n.indexOf(e.name)>-1;return t=>H(t,!0,1,void 0,r&&!function(t){return t.pos==t.options.simulateBreak&&t.options.simulateDoubleBreak}(t)?e.from:void 0)}return null==t.parent?W:null}function W(){return 0}class _ extends R{constructor(t,e,n){super(t.state,t.options),this.base=t,this.pos=e,this.context=n}get node(){return this.context.node}static create(t,e,n){return new _(t,e,n)}get textAfter(){return this.textAfterPos(this.pos)}get baseIndent(){return this.baseIndentFor(this.node)}baseIndentFor(t){let e=this.state.doc.lineAt(t.from);for(;;){let n=t.resolve(e.from);for(;n.parent&&n.parent.from==n.from;)n=n.parent;if($(n,t))break;e=this.state.doc.lineAt(n.from)}return this.lineIndent(e.from)}continue(){return U(this.context.next,this.base,this.pos)}}function $(t,e){for(let n=e;n;n=n.parent)if(t==n)return!0;return!1}function z({closing:t,align:e=!0,units:n=1}){return r=>H(r,e,n,t)}function H(t,e,n,r,s){let i=t.textAfter,o=i.match(/^\s*/)[0].length,a=r&&i.slice(o,o+r.length)==r||s==t.pos+o,l=e?function(t){let e=t.node,n=e.childAfter(e.from),r=e.lastChild;if(!n)return null;let s=t.options.simulateBreak,i=t.state.doc.lineAt(n.from),o=null==s||s<=i.from?i.to:Math.min(i.to,s);for(let t=n.to;;){let s=e.childAfter(t);if(!s||s==r)return null;if(!s.type.isSkipped)return s.from<o?n:null;t=s.to}}(t):null;return l?a?t.column(l.from):t.column(l.to):t.baseIndent+(a?0:t.unit*n)}const G=t=>t.baseIndent;function J({except:t,units:e=1}={}){return n=>{let r=t&&t.test(n.textAfter);return n.baseIndent+(r?0:e*n.unit)}}const q=200;function Y(){return i.EditorState.transactionFilter.of((t=>{if(!t.docChanged||!t.isUserEvent("input.type")&&!t.isUserEvent("input.complete"))return t;let e=t.startState.languageDataAt("indentOnInput",t.startState.selection.main.head);if(!e.length)return t;let n=t.newDoc,{head:r}=t.newSelection.main,s=n.lineAt(r);if(r>s.from+q)return t;let i=n.sliceString(s.from,r);if(!e.some((t=>t.test(i))))return t;let{state:o}=t,a=-1,l=[];for(let{head:t}of o.selection.ranges){let e=o.doc.lineAt(t);if(e.from==a)continue;a=e.from;let n=L(o,e.from);if(null==n)continue;let r=/^\s*/.exec(e.text)[0],s=M(o,n);r!=s&&l.push({from:e.from,to:e.from+r.length,insert:s})}return l.length?[t,{changes:l,sequential:!0}]:t}))}const K=i.Facet.define(),Z=new s.NodeProp;function Q(t){let e=t.firstChild,n=t.lastChild;return e&&e.to<n.from?{from:e.to,to:n.type.isError?t.to:n.from}:null}function X(t){let e=t.lastChild;return e&&e.to==t.to&&e.type.isError}function tt(t,e,n){for(let r of t.facet(K)){let s=r(t,e,n);if(s)return s}return function(t,e,n){let r=g(t);if(r.length<n)return null;let s=null;for(let i=r.resolveStack(n,1);i;i=i.next){let o=i.node;if(o.to<=n||o.from>n)continue;if(s&&o.from<e)break;let a=o.type.prop(Z);if(a&&(o.to<r.length-50||r.length==t.doc.length||!X(o))){let r=a(o,t);r&&r.from<=n&&r.from>=e&&r.to>n&&(s=r)}}return s}(t,e,n)}function et(t,e){let n=e.mapPos(t.from,1),r=e.mapPos(t.to,-1);return n>=r?void 0:{from:n,to:r}}const nt=i.StateEffect.define({map:et}),rt=i.StateEffect.define({map:et});function st(t){let e=[];for(let{head:n}of t.state.selection.ranges)e.some((t=>t.from<=n&&t.to>=n))||e.push(t.lineBlockAt(n));return e}const it=i.StateField.define({create:()=>o.Decoration.none,update(t,e){t=t.map(e.changes);for(let n of e.effects)if(n.is(nt)&&!lt(t,n.value.from,n.value.to)){let{preparePlaceholder:r}=e.state.facet(vt),s=r?o.Decoration.replace({widget:new xt(r(e.state,n.value))}):St;t=t.update({add:[s.range(n.value.from,n.value.to)]})}else n.is(rt)&&(t=t.update({filter:(t,e)=>n.value.from!=t||n.value.to!=e,filterFrom:n.value.from,filterTo:n.value.to}));if(e.selection){let n=!1,{head:r}=e.selection.main;t.between(r,r,((t,e)=>{t<r&&e>r&&(n=!0)})),n&&(t=t.update({filterFrom:r,filterTo:r,filter:(t,e)=>e<=r||t>=r}))}return t},provide:t=>o.EditorView.decorations.from(t),toJSON(t,e){let n=[];return t.between(0,e.doc.length,((t,e)=>{n.push(t,e)})),n},fromJSON(t){if(!Array.isArray(t)||t.length%2)throw new RangeError("Invalid JSON for fold state");let e=[];for(let n=0;n<t.length;){let r=t[n++],s=t[n++];if("number"!=typeof r||"number"!=typeof s)throw new RangeError("Invalid JSON for fold state");e.push(St.range(r,s))}return o.Decoration.set(e,!0)}});function ot(t){return t.field(it,!1)||i.RangeSet.empty}function at(t,e,n){var r;let s=null;return null===(r=t.field(it,!1))||void 0===r||r.between(e,n,((t,e)=>{(!s||s.from>t)&&(s={from:t,to:e})})),s}function lt(t,e,n){let r=!1;return t.between(e,e,((t,s)=>{t==e&&s==n&&(r=!0)})),r}function ht(t,e){return t.field(it,!1)?e:e.concat(i.StateEffect.appendConfig.of(bt()))}const ut=t=>{for(let e of st(t)){let n=tt(t.state,e.from,e.to);if(n)return t.dispatch({effects:ht(t.state,[nt.of(n),ft(t,n)])}),!0}return!1},ct=t=>{if(!t.state.field(it,!1))return!1;let e=[];for(let n of st(t)){let r=at(t.state,n.from,n.to);r&&e.push(rt.of(r),ft(t,r,!1))}return e.length&&t.dispatch({effects:e}),e.length>0};function ft(t,e,n=!0){let r=t.state.doc.lineAt(e.from).number,s=t.state.doc.lineAt(e.to).number;return o.EditorView.announce.of(`${t.state.phrase(n?"Folded lines":"Unfolded lines")} ${r} ${t.state.phrase("to")} ${s}.`)}const dt=t=>{let{state:e}=t,n=[];for(let r=0;r<e.doc.length;){let s=t.lineBlockAt(r),i=tt(e,s.from,s.to);i&&n.push(nt.of(i)),r=(i?t.lineBlockAt(i.to):s).to+1}return n.length&&t.dispatch({effects:ht(t.state,n)}),!!n.length},pt=t=>{let e=t.state.field(it,!1);if(!e||!e.size)return!1;let n=[];return e.between(0,t.state.doc.length,((t,e)=>{n.push(rt.of({from:t,to:e}))})),t.dispatch({effects:n}),!0};function gt(t,e){for(let n=e;;){let r=tt(t.state,n.from,n.to);if(r&&r.to>e.from)return r;if(!n.from)return null;n=t.lineBlockAt(n.from-1)}}const mt=t=>{let e=[];for(let n of st(t)){let r=at(t.state,n.from,n.to);if(r)e.push(rt.of(r),ft(t,r,!1));else{let r=gt(t,n);r&&e.push(nt.of(r),ft(t,r))}}return e.length>0&&t.dispatch({effects:ht(t.state,e)}),!!e.length},kt=[{key:"Ctrl-Shift-[",mac:"Cmd-Alt-[",run:ut},{key:"Ctrl-Shift-]",mac:"Cmd-Alt-]",run:ct},{key:"Ctrl-Alt-[",run:dt},{key:"Ctrl-Alt-]",run:pt}],wt={placeholderDOM:null,preparePlaceholder:null,placeholderText:"…"},vt=i.Facet.define({combine:t=>(0,i.combineConfig)(t,wt)});function bt(t){let e=[it,Ct];return t&&e.push(vt.of(t)),e}function yt(t,e){let{state:n}=t,r=n.facet(vt),s=e=>{let n=t.lineBlockAt(t.posAtDOM(e.target)),r=at(t.state,n.from,n.to);r&&t.dispatch({effects:rt.of(r)}),e.preventDefault()};if(r.placeholderDOM)return r.placeholderDOM(t,s,e);let i=document.createElement("span");return i.textContent=r.placeholderText,i.setAttribute("aria-label",n.phrase("folded code")),i.title=n.phrase("unfold"),i.className="cm-foldPlaceholder",i.onclick=s,i}const St=o.Decoration.replace({widget:new class extends o.WidgetType{toDOM(t){return yt(t,null)}}});class xt extends o.WidgetType{constructor(t){super(),this.value=t}eq(t){return this.value==t.value}toDOM(t){return yt(t,this.value)}}const Pt={openText:"⌄",closedText:"›",markerDOM:null,domEventHandlers:{},foldingChanged:()=>!1};class Tt extends o.GutterMarker{constructor(t,e){super(),this.config=t,this.open=e}eq(t){return this.config==t.config&&this.open==t.open}toDOM(t){if(this.config.markerDOM)return this.config.markerDOM(this.open);let e=document.createElement("span");return e.textContent=this.open?this.config.openText:this.config.closedText,e.title=t.state.phrase(this.open?"Fold line":"Unfold line"),e}}function At(t={}){let e=Object.assign(Object.assign({},Pt),t),n=new Tt(e,!0),r=new Tt(e,!1),s=o.ViewPlugin.fromClass(class{constructor(t){this.from=t.viewport.from,this.markers=this.buildMarkers(t)}update(t){(t.docChanged||t.viewportChanged||t.startState.facet(D)!=t.state.facet(D)||t.startState.field(it,!1)!=t.state.field(it,!1)||g(t.startState)!=g(t.state)||e.foldingChanged(t))&&(this.markers=this.buildMarkers(t.view))}buildMarkers(t){let e=new i.RangeSetBuilder;for(let s of t.viewportLineBlocks){let i=at(t.state,s.from,s.to)?r:tt(t.state,s.from,s.to)?n:null;i&&e.add(s.from,s.from,i)}return e.finish()}}),{domEventHandlers:a}=e;return[s,(0,o.gutter)({class:"cm-foldGutter",markers(t){var e;return(null===(e=t.plugin(s))||void 0===e?void 0:e.markers)||i.RangeSet.empty},initialSpacer:()=>new Tt(e,!1),domEventHandlers:Object.assign(Object.assign({},a),{click:(t,e,n)=>{if(a.click&&a.click(t,e,n))return!0;let r=at(t.state,e.from,e.to);if(r)return t.dispatch({effects:rt.of(r)}),!0;let s=tt(t.state,e.from,e.to);return!!s&&(t.dispatch({effects:nt.of(s)}),!0)}})}),bt()]}const Ct=o.EditorView.baseTheme({".cm-foldPlaceholder":{backgroundColor:"#eee",border:"1px solid #ddd",color:"#888",borderRadius:".2em",margin:"0 1px",padding:"0 1px",cursor:"pointer"},".cm-foldGutter span":{padding:"0 1px",cursor:"pointer"}});class Dt{constructor(t,e){let n;function r(t){let e=l.V.newName();return(n||(n=Object.create(null)))["."+e]=t,e}this.specs=t;const s="string"==typeof e.all?e.all:e.all?r(e.all):void 0,i=e.scope;this.scope=i instanceof f?t=>t.prop(h)==i.data:i?t=>t==i:void 0,this.style=(0,a.tagHighlighter)(t.map((t=>({tag:t.tag,class:t.class||r(Object.assign({},t,{tag:null}))}))),{all:s}).style,this.module=n?new l.V(n):null,this.themeType=e.themeType}static define(t,e){return new Dt(t,e||{})}}const Nt=i.Facet.define(),Ot=i.Facet.define({combine:t=>t.length?[t[0]]:null});function It(t){let e=t.facet(Nt);return e.length?e:t.facet(Ot)}function Et(t,e){let n,r=[Lt];return t instanceof Dt&&(t.module&&r.push(o.EditorView.styleModule.of(t.module)),n=t.themeType),(null==e?void 0:e.fallback)?r.push(Ot.of(t)):n?r.push(Nt.computeN([o.EditorView.darkTheme],(e=>e.facet(o.EditorView.darkTheme)==("dark"==n)?[t]:[]))):r.push(Nt.of(t)),r}function Bt(t,e,n){let r=It(t),s=null;if(r)for(let t of r)if(!t.scope||n&&t.scope(n)){let n=t.style(e);n&&(s=s?s+" "+n:n)}return s}class Mt{constructor(t){this.markCache=Object.create(null),this.tree=g(t.state),this.decorations=this.buildDeco(t,It(t.state))}update(t){let e=g(t.state),n=It(t.state),r=n!=It(t.startState);e.length<t.view.viewport.to&&!r&&e.type==this.tree.type?this.decorations=this.decorations.map(t.changes):(e!=this.tree||t.viewportChanged||r)&&(this.tree=e,this.decorations=this.buildDeco(t.view,n))}buildDeco(t,e){if(!e||!this.tree.length)return o.Decoration.none;let n=new i.RangeSetBuilder;for(let{from:r,to:s}of t.visibleRanges)(0,a.highlightTree)(this.tree,e,((t,e,r)=>{n.add(t,e,this.markCache[r]||(this.markCache[r]=o.Decoration.mark({class:r})))}),r,s);return n.finish()}}const Lt=i.Prec.high(o.ViewPlugin.fromClass(Mt,{decorations:t=>t.decorations})),Ft=Dt.define([{tag:a.tags.meta,color:"#404740"},{tag:a.tags.link,textDecoration:"underline"},{tag:a.tags.heading,textDecoration:"underline",fontWeight:"bold"},{tag:a.tags.emphasis,fontStyle:"italic"},{tag:a.tags.strong,fontWeight:"bold"},{tag:a.tags.strikethrough,textDecoration:"line-through"},{tag:a.tags.keyword,color:"#708"},{tag:[a.tags.atom,a.tags.bool,a.tags.url,a.tags.contentSeparator,a.tags.labelName],color:"#219"},{tag:[a.tags.literal,a.tags.inserted],color:"#164"},{tag:[a.tags.string,a.tags.deleted],color:"#a11"},{tag:[a.tags.regexp,a.tags.escape,a.tags.special(a.tags.string)],color:"#e40"},{tag:a.tags.definition(a.tags.variableName),color:"#00f"},{tag:a.tags.local(a.tags.variableName),color:"#30a"},{tag:[a.tags.typeName,a.tags.namespace],color:"#085"},{tag:a.tags.className,color:"#167"},{tag:[a.tags.special(a.tags.variableName),a.tags.macroName],color:"#256"},{tag:a.tags.definition(a.tags.propertyName),color:"#00c"},{tag:a.tags.comment,color:"#940"},{tag:a.tags.invalid,color:"#f00"}]),Rt=o.EditorView.baseTheme({"&.cm-focused .cm-matchingBracket":{backgroundColor:"#328c8252"},"&.cm-focused .cm-nonmatchingBracket":{backgroundColor:"#bb555544"}}),Vt=1e4,Ut="()[]{}",jt=i.Facet.define({combine:t=>(0,i.combineConfig)(t,{afterCursor:!0,brackets:Ut,maxScanDistance:Vt,renderMatch:$t})}),Wt=o.Decoration.mark({class:"cm-matchingBracket"}),_t=o.Decoration.mark({class:"cm-nonmatchingBracket"});function $t(t){let e=[],n=t.matched?Wt:_t;return e.push(n.range(t.start.from,t.start.to)),t.end&&e.push(n.range(t.end.from,t.end.to)),e}const zt=[i.StateField.define({create:()=>o.Decoration.none,update(t,e){if(!e.docChanged&&!e.selection)return t;let n=[],r=e.state.facet(jt);for(let t of e.state.selection.ranges){if(!t.empty)continue;let s=Yt(e.state,t.head,-1,r)||t.head>0&&Yt(e.state,t.head-1,1,r)||r.afterCursor&&(Yt(e.state,t.head,1,r)||t.head<e.state.doc.length&&Yt(e.state,t.head+1,-1,r));s&&(n=n.concat(r.renderMatch(s,e.state)))}return o.Decoration.set(n,!0)},provide:t=>o.EditorView.decorations.from(t)}),Rt];function Ht(t={}){return[jt.of(t),zt]}const Gt=new s.NodeProp;function Jt(t,e,n){let r=t.prop(e<0?s.NodeProp.openedBy:s.NodeProp.closedBy);if(r)return r;if(1==t.name.length){let r=n.indexOf(t.name);if(r>-1&&r%2==(e<0?1:0))return[n[r+e]]}return null}function qt(t){let e=t.type.prop(Gt);return e?e(t.node):t}function Yt(t,e,n,r={}){let s=r.maxScanDistance||Vt,i=r.brackets||Ut,o=g(t),a=o.resolveInner(e,n);for(let t=a;t;t=t.parent){let r=Jt(t.type,n,i);if(r&&t.from<t.to){let s=qt(t);if(s&&(n>0?e>=s.from&&e<s.to:e>s.from&&e<=s.to))return Kt(0,0,n,t,s,r,i)}}return function(t,e,n,r,s,i,o){let a=n<0?t.sliceDoc(e-1,e):t.sliceDoc(e,e+1),l=o.indexOf(a);if(l<0||l%2==0!=n>0)return null;let h={from:n<0?e-1:e,to:n>0?e+1:e},u=t.doc.iterRange(e,n>0?t.doc.length:0),c=0;for(let t=0;!u.next().done&&t<=i;){let i=u.value;n<0&&(t+=i.length);let a=e+t*n;for(let t=n>0?0:i.length-1,e=n>0?i.length:-1;t!=e;t+=n){let e=o.indexOf(i[t]);if(!(e<0||r.resolveInner(a+t,1).type!=s))if(e%2==0==n>0)c++;else{if(1==c)return{start:h,end:{from:a+t,to:a+t+1},matched:e>>1==l>>1};c--}}n>0&&(t+=i.length)}return u.done?{start:h,matched:!1}:null}(t,e,n,o,a.type,s,i)}function Kt(t,e,n,r,s,i,o){let a=r.parent,l={from:s.from,to:s.to},h=0,u=null==a?void 0:a.cursor();if(u&&(n<0?u.childBefore(r.from):u.childAfter(r.to)))do{if(n<0?u.to<=r.from:u.from>=r.to){if(0==h&&i.indexOf(u.type.name)>-1&&u.from<u.to){let t=qt(u);return{start:l,end:t?{from:t.from,to:t.to}:void 0,matched:!0}}if(Jt(u.type,n,o))h++;else if(Jt(u.type,-n,o)){if(0==h){let t=qt(u);return{start:l,end:t&&t.from<t.to?{from:t.from,to:t.to}:void 0,matched:!1}}h--}}}while(n<0?u.prevSibling():u.nextSibling());return{start:l,matched:!1}}function Zt(t,e,n,r=0,s=0){null==e&&-1==(e=t.search(/[^\s\u00a0]/))&&(e=t.length);let i=s;for(let s=r;s<e;s++)9==t.charCodeAt(s)?i+=n-i%n:i++;return i}class Qt{constructor(t,e,n,r){this.string=t,this.tabSize=e,this.indentUnit=n,this.overrideIndent=r,this.pos=0,this.start=0,this.lastColumnPos=0,this.lastColumnValue=0}eol(){return this.pos>=this.string.length}sol(){return 0==this.pos}peek(){return this.string.charAt(this.pos)||void 0}next(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)}eat(t){let e,n=this.string.charAt(this.pos);if(e="string"==typeof t?n==t:n&&(t instanceof RegExp?t.test(n):t(n)),e)return++this.pos,n}eatWhile(t){let e=this.pos;for(;this.eat(t););return this.pos>e}eatSpace(){let t=this.pos;for(;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>t}skipToEnd(){this.pos=this.string.length}skipTo(t){let e=this.string.indexOf(t,this.pos);if(e>-1)return this.pos=e,!0}backUp(t){this.pos-=t}column(){return this.lastColumnPos<this.start&&(this.lastColumnValue=Zt(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue}indentation(){var t;return null!==(t=this.overrideIndent)&&void 0!==t?t:Zt(this.string,null,this.tabSize)}match(t,e,n){if("string"==typeof t){let r=t=>n?t.toLowerCase():t;return r(this.string.substr(this.pos,t.length))==r(t)?(!1!==e&&(this.pos+=t.length),!0):null}{let n=this.string.slice(this.pos).match(t);return n&&n.index>0?null:(n&&!1!==e&&(this.pos+=n[0].length),n)}}current(){return this.string.slice(this.start,this.pos)}}function Xt(t){if("object"!=typeof t)return t;let e={};for(let n in t){let r=t[n];e[n]=r instanceof Array?r.slice():r}return e}const te=new WeakMap;class ee extends f{constructor(t){let e,n=u(t.languageData),r={name:(i=t).name||"",token:i.token,blankLine:i.blankLine||(()=>{}),startState:i.startState||(()=>!0),copyState:i.copyState||Xt,indent:i.indent||(()=>null),languageData:i.languageData||{},tokenTable:i.tokenTable||oe};var i;super(n,new class extends s.Parser{createParse(t,n,r){return new se(e,t,n,r)}},[I.of(((t,e)=>this.getIndent(t,e)))],t.name),this.topNode=function(t){let e=s.NodeType.define({id:ae.length,name:"Document",props:[h.add((()=>t))],top:!0});return ae.push(e),e}(n),e=this,this.streamParser=r,this.stateAfter=new s.NodeProp({perNode:!0}),this.tokenTable=t.tokenTable?new ce(r.tokenTable):fe}static define(t){return new ee(t)}getIndent(t,e){let n,r=g(t.state),s=r.resolve(e);for(;s&&s.type!=this.topNode;)s=s.parent;if(!s)return null;let{overrideIndentation:i}=t.options;i&&(n=te.get(t.state),null!=n&&n<e-1e4&&(n=void 0));let o,a,l=ne(this,r,0,s.from,null!=n?n:e);if(l?(a=l.state,o=l.pos+1):(a=this.streamParser.startState(t.unit),o=0),e-o>1e4)return null;for(;o<e;){let n=t.state.doc.lineAt(o),r=Math.min(e,n.to);if(n.length){let e=i?i(n.from):-1,s=new Qt(n.text,t.state.tabSize,t.unit,e<0?void 0:e);for(;s.pos<r-n.from;)ie(this.streamParser.token,s,a)}else this.streamParser.blankLine(a,t.unit);if(r==e)break;o=n.to+1}let h=t.lineAt(e);return i&&null==n&&te.set(t.state,h.from),this.streamParser.indent(a,/^\s*(.*)/.exec(h.text)[1],t)}get allowsNesting(){return!1}}function ne(t,e,n,r,i){let o=n>=r&&n+e.length<=i&&e.prop(t.stateAfter);if(o)return{state:t.streamParser.copyState(o),pos:n+e.length};for(let o=e.children.length-1;o>=0;o--){let a=e.children[o],l=n+e.positions[o],h=a instanceof s.Tree&&l<i&&ne(t,a,l,r,i);if(h)return h}return null}function re(t,e,n,r,i){if(i&&n<=0&&r>=e.length)return e;i||e.type!=t.topNode||(i=!0);for(let o=e.children.length-1;o>=0;o--){let a,l=e.positions[o],h=e.children[o];if(l<r&&h instanceof s.Tree){if(!(a=re(t,h,n-l,r-l,i)))break;return i?new s.Tree(e.type,e.children.slice(0,o).concat(a),e.positions.slice(0,o+1),l+a.length):a}}return null}class se{constructor(t,e,n,r){this.lang=t,this.input=e,this.fragments=n,this.ranges=r,this.stoppedAt=null,this.chunks=[],this.chunkPos=[],this.chunk=[],this.chunkReused=void 0,this.rangeIndex=0,this.to=r[r.length-1].to;let i=S.get(),o=r[0].from,{state:a,tree:l}=function(t,e,n,r){for(let r of e){let e,s=r.from+(r.openStart?25:0),i=r.to-(r.openEnd?25:0),o=s<=n&&i>n&&ne(t,r.tree,0-r.offset,n,i);if(o&&(e=re(t,r.tree,n+r.offset,o.pos+r.offset,!1)))return{state:o.state,tree:e}}return{state:t.streamParser.startState(r?B(r):4),tree:s.Tree.empty}}(t,n,o,null==i?void 0:i.state);this.state=a,this.parsedPos=this.chunkStart=o+l.length;for(let t=0;t<l.children.length;t++)this.chunks.push(l.children[t]),this.chunkPos.push(l.positions[t]);i&&this.parsedPos<i.viewport.from-1e5&&(this.state=this.lang.streamParser.startState(B(i.state)),i.skipUntilInView(this.parsedPos,i.viewport.from),this.parsedPos=i.viewport.from),this.moveRangeIndex()}advance(){let t=S.get(),e=null==this.stoppedAt?this.to:Math.min(this.to,this.stoppedAt),n=Math.min(e,this.chunkStart+2048);for(t&&(n=Math.min(n,t.viewport.to));this.parsedPos<n;)this.parseLine(t);return this.chunkStart<this.parsedPos&&this.finishChunk(),this.parsedPos>=e?this.finish():t&&this.parsedPos>=t.viewport.to?(t.skipUntilInView(this.parsedPos,e),this.finish()):null}stopAt(t){this.stoppedAt=t}lineAfter(t){let e=this.input.chunk(t);if(this.input.lineChunks)"\n"==e&&(e="");else{let t=e.indexOf("\n");t>-1&&(e=e.slice(0,t))}return t+e.length<=this.to?e:e.slice(0,this.to-t)}nextLine(){let t=this.parsedPos,e=this.lineAfter(t),n=t+e.length;for(let t=this.rangeIndex;;){let r=this.ranges[t].to;if(r>=n)break;if(e=e.slice(0,r-(n-e.length)),t++,t==this.ranges.length)break;let s=this.ranges[t].from,i=this.lineAfter(s);e+=i,n=s+i.length}return{line:e,end:n}}skipGapsTo(t,e,n){for(;;){let r=this.ranges[this.rangeIndex].to,s=t+e;if(n>0?r>s:r>=s)break;e+=this.ranges[++this.rangeIndex].from-r}return e}moveRangeIndex(){for(;this.ranges[this.rangeIndex].to<this.parsedPos;)this.rangeIndex++}emitToken(t,e,n,r,s){if(this.ranges.length>1){e+=s=this.skipGapsTo(e,s,1);let t=this.chunk.length;n+=s=this.skipGapsTo(n,s,-1),r+=this.chunk.length-t}return this.chunk.push(t,e,n,r),s}parseLine(t){let{line:e,end:n}=this.nextLine(),r=0,{streamParser:s}=this.lang,i=new Qt(e,t?t.state.tabSize:4,t?B(t.state):2);if(i.eol())s.blankLine(this.state,i.indentUnit);else for(;!i.eol();){let t=ie(s.token,i,this.state);if(t&&(r=this.emitToken(this.lang.tokenTable.resolve(t),this.parsedPos+i.start,this.parsedPos+i.pos,4,r)),i.start>1e4)break}this.parsedPos=n,this.moveRangeIndex(),this.parsedPos<this.to&&this.parsedPos++}finishChunk(){let t=s.Tree.build({buffer:this.chunk,start:this.chunkStart,length:this.parsedPos-this.chunkStart,nodeSet:le,topID:0,maxBufferLength:2048,reused:this.chunkReused});t=new s.Tree(t.type,t.children,t.positions,t.length,[[this.lang.stateAfter,this.lang.streamParser.copyState(this.state)]]),this.chunks.push(t),this.chunkPos.push(this.chunkStart-this.ranges[0].from),this.chunk=[],this.chunkReused=void 0,this.chunkStart=this.parsedPos}finish(){return new s.Tree(this.lang.topNode,this.chunks,this.chunkPos,this.parsedPos-this.ranges[0].from).balance()}}function ie(t,e,n){e.start=e.pos;for(let r=0;r<10;r++){let r=t(e,n);if(e.pos>e.start)return r}throw new Error("Stream parser failed to advance stream.")}const oe=Object.create(null),ae=[s.NodeType.none],le=new s.NodeSet(ae),he=[],ue=Object.create(null);for(let[t,e]of[["variable","variableName"],["variable-2","variableName.special"],["string-2","string.special"],["def","variableName.definition"],["tag","tagName"],["attribute","attributeName"],["type","typeName"],["builtin","variableName.standard"],["qualifier","modifier"],["error","invalid"],["header","heading"],["property","propertyName"]])ue[t]=pe(oe,e);class ce{constructor(t){this.extra=t,this.table=Object.assign(Object.create(null),ue)}resolve(t){return t?this.table[t]||(this.table[t]=pe(this.extra,t)):0}}const fe=new ce(oe);function de(t,e){he.indexOf(t)>-1||(he.push(t),console.warn(e))}function pe(t,e){let n=null;for(let r of e.split(".")){let e=t[r]||a.tags[r];e?"function"==typeof e?n?n=e(n):de(r,`Modifier ${r} used at start of tag`):n?de(r,`Tag ${r} used as modifier`):n=e:de(r,`Unknown highlighting tag ${r}`)}if(!n)return 0;let r=e.replace(/ /g,"_"),i=s.NodeType.define({id:ae.length,name:r,props:[(0,a.styleTags)({[r]:n})]});return ae.push(i),i.id}},4434:(t,e,n)=>{n.d(e,{V:()=>o});const r="undefined"==typeof Symbol?"__ͼ":Symbol.for("ͼ"),s="undefined"==typeof Symbol?"__styleSet"+Math.floor(1e8*Math.random()):Symbol("styleSet"),i="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:{};class o{constructor(t,e){this.rules=[];let{finish:n}=e||{};function r(t){return/^@/.test(t)?[t]:t.split(/,\s*/)}function s(t,e,i,o){let a=[],l=/^@(\w+)\b/.exec(t[0]),h=l&&"keyframes"==l[1];if(l&&null==e)return i.push(t[0]+";");for(let n in e){let o=e[n];if(/&/.test(n))s(n.split(/,\s*/).map((e=>t.map((t=>e.replace(/&/,t))))).reduce(((t,e)=>t.concat(e))),o,i);else if(o&&"object"==typeof o){if(!l)throw new RangeError("The value of a property ("+n+") should be a primitive value.");s(r(n),o,a,h)}else null!=o&&a.push(n.replace(/_.*/,"").replace(/[A-Z]/g,(t=>"-"+t.toLowerCase()))+": "+o+";")}(a.length||h)&&i.push((!n||l||o?t:t.map(n)).join(", ")+" {"+a.join(" ")+"}")}for(let e in t)s(r(e),t[e],this.rules)}getRules(){return this.rules.join("\n")}static newName(){let t=i[r]||1;return i[r]=t+1,"ͼ"+t.toString(36)}static mount(t,e,n){let r=t[s],i=n&&n.nonce;r?i&&r.setNonce(i):r=new l(t,i),r.mount(Array.isArray(e)?e:[e])}}let a=new Map;class l{constructor(t,e){let n=t.ownerDocument||t,r=n.defaultView;if(!t.head&&t.adoptedStyleSheets&&r.CSSStyleSheet){let e=a.get(n);if(e)return t.adoptedStyleSheets=[e.sheet,...t.adoptedStyleSheets],t[s]=e;this.sheet=new r.CSSStyleSheet,t.adoptedStyleSheets=[this.sheet,...t.adoptedStyleSheets],a.set(n,this)}else{this.styleTag=n.createElement("style"),e&&this.styleTag.setAttribute("nonce",e);let r=t.head||t;r.insertBefore(this.styleTag,r.firstChild)}this.modules=[],t[s]=this}mount(t){let e=this.sheet,n=0,r=0;for(let s=0;s<t.length;s++){let i=t[s],o=this.modules.indexOf(i);if(o<r&&o>-1&&(this.modules.splice(o,1),r--,o=-1),-1==o){if(this.modules.splice(r++,0,i),e)for(let t=0;t<i.rules.length;t++)e.insertRule(i.rules[t],n++)}else{for(;r<o;)n+=this.modules[r++].rules.length;n+=i.rules.length,r++}}if(!e){let t="";for(let e=0;e<this.modules.length;e++)t+=this.modules[e].getRules()+"\n";this.styleTag.textContent=t}}setNonce(t){this.styleTag&&this.styleTag.getAttribute("nonce")!=t&&this.styleTag.setAttribute("nonce",t)}}}}]);
"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[9222],{19222:(e,t,r)=>{function n(){return i.keyboardLayout}function o(e){i.keyboardLayout=e}r.r(t),r.d(t,{EN_US:()=>a,KeycodeLayout:()=>s,getKeyboardLayout:()=>n,setKeyboardLayout:()=>o});class s{constructor(e,t,r=[]){this.name=e,this._codes=t,this._keys=s.extractKeys(t),this._modifierKeys=s.convertToKeySet(r)}keys(){return Object.keys(this._keys)}isValidKey(e){return e in this._keys}isModifierKey(e){return e in this._modifierKeys}keyForKeydownEvent(e){return this._codes[e.keyCode]||""}}!function(e){e.extractKeys=function(e){let t=Object.create(null);for(let r in e)t[e[r]]=!0;return t},e.convertToKeySet=function(e){let t=Object(null);for(let r=0,n=e.length;r<n;++r)t[e[r]]=!0;return t}}(s||(s={}));const a=new s("en-us",{8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",27:"Escape",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",48:"0",49:"1",50:"2",51:"3",52:"4",53:"5",54:"6",55:"7",56:"8",57:"9",59:";",61:"=",65:"A",66:"B",67:"C",68:"D",69:"E",70:"F",71:"G",72:"H",73:"I",74:"J",75:"K",76:"L",77:"M",78:"N",79:"O",80:"P",81:"Q",82:"R",83:"S",84:"T",85:"U",86:"V",87:"W",88:"X",89:"Y",90:"Z",91:"Meta",93:"ContextMenu",96:"0",97:"1",98:"2",99:"3",100:"4",101:"5",102:"6",103:"7",104:"8",105:"9",106:"*",107:"+",109:"-",110:".",111:"/",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",224:"Meta"},["Shift","Ctrl","Alt","Meta"]);var i;!function(e){e.keyboardLayout=a}(i||(i={}))}}]);
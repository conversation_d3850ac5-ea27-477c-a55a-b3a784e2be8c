"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[6256],{86256:(e,n,o)=>{o.r(n),o.d(n,{default:()=>S});var t=o(3053),r=o(12982),l=o(31536),s=o(76867),a=o(46122),i=o(97713),d=o(389),c=o(97104),u=o(70856),p=o(36768),m=o(71677),C=o(68239),v=o(33625),g=o(20998),h=o(2549),b=o(14421);const f={id:"@jupyterlab/console-extension:foreign",description:"Add foreign handler of IOPub messages to the console.",requires:[a.IConsoleTracker,p.<PERSON>ng<PERSON>eg<PERSON>ry,m.ITranslator],optional:[r.ICommandPalette],activate:function(e,n,o,t,r){const l=t.load("jupyterlab"),{shell:s}=e;n.widgetAdded.connect(((e,n)=>{const t=n.console,r=new a.ForeignHandler({sessionContext:t.sessionContext,parent:t});k.foreignHandlerProperty.set(t,r),o.get("@jupyterlab/console-extension:tracker","showAllKernelActivity").then((({composite:e})=>{const n=e;r.enabled=n})),t.disposed.connect((()=>{r.dispose()}))}));const{commands:i}=e,d=l.__("Console"),c="console:toggle-show-all-kernel-activity";i.addCommand(c,{label:e=>l.__("Show All Kernel Activity"),execute:e=>{const o=function(e){const o=n.currentWidget;return!1!==e.activate&&o&&s.activateById(o.id),o}(e);if(!o)return;const t=k.foreignHandlerProperty.get(o.console);t&&(t.enabled=!t.enabled)},isToggled:()=>{var e;return null!==n.currentWidget&&!!(null===(e=k.foreignHandlerProperty.get(n.currentWidget.console))||void 0===e?void 0:e.enabled)},isEnabled:()=>null!==n.currentWidget&&n.currentWidget===s.currentWidget}),r&&r.addItem({command:c,category:d,args:{isPalette:!0}})},autoStart:!0},y=f;var k,_;!function(e){e.foreignHandlerProperty=new b.AttachedProperty({name:"foreignHandler",create:()=>{}})}(k||(k={})),function(e){e.autoClosingBrackets="console:toggle-autoclosing-brackets",e.create="console:create",e.clear="console:clear",e.runUnforced="console:run-unforced",e.runForced="console:run-forced",e.linebreak="console:linebreak",e.interrupt="console:interrupt-kernel",e.restart="console:restart-kernel",e.closeAndShutdown="console:close-and-shutdown",e.open="console:open",e.inject="console:inject",e.changeKernel="console:change-kernel",e.getKernel="console:get-kernel",e.enterToExecute="console:enter-to-execute",e.shiftEnterToExecute="console:shift-enter-to-execute",e.interactionMode="console:interaction-mode",e.redo="console:redo",e.replaceSelection="console:replace-selection",e.shutdown="console:shutdown",e.undo="console:undo",e.invokeCompleter="completer:invoke-console",e.selectCompleter="completer:select-console"}(_||(_={}));const x={id:"@jupyterlab/console-extension:tracker",description:"Provides the console widget tracker.",provides:a.IConsoleTracker,requires:[a.ConsolePanel.IContentFactory,l.IEditorServices,u.IRenderMimeRegistry,p.ISettingRegistry],optional:[t.ILayoutRestorer,i.IDefaultFileBrowser,c.IMainMenu,r.ICommandPalette,d.ILauncher,t.ILabStatus,r.ISessionContextDialogs,C.IFormRendererRegistry,m.ITranslator],activate:async function(e,n,o,t,l,s,i,d,c,u,p,b,f,y){const k=null!=y?y:m.nullTranslator,x=k.load("jupyterlab"),w=e.serviceManager,{commands:I,shell:E}=e,S=x.__("Console"),P=null!=b?b:new r.SessionContextDialogs({translator:k}),M=new r.WidgetTracker({namespace:"console"});async function j(e){var r,s;await w.ready;const i=new a.ConsolePanel({manager:w,contentFactory:n,mimeTypeService:o.mimeTypeService,rendermime:t,sessionDialogs:P,translator:k,setBusy:null!==(r=p&&(()=>p.setBusy()))&&void 0!==r?r:void 0,...e}),d=(await l.get("@jupyterlab/console-extension:tracker","interactionMode")).composite;return i.console.node.dataset.jpInteractionMode=d,await M.add(i),i.sessionContext.propertyChanged.connect((()=>{M.save(i)})),E.add(i,"main",{ref:e.ref,mode:e.insertMode,activate:!1!==e.activate,type:null!==(s=e.type)&&void 0!==s?s:"Console"}),i}s&&s.restore(M,{command:_.create,args:e=>{const{path:n,name:o,kernelPreference:t}=e.console.sessionContext;return{path:n,name:o,kernelPreference:{...t}}},name:e=>{var n;return null!==(n=e.console.sessionContext.path)&&void 0!==n?n:g.UUID.uuid4()},when:w.ready}),u&&w.ready.then((()=>{let e=null;const n=()=>{e&&(e.dispose(),e=null);const n=w.kernelspecs.specs;if(n){e=new h.DisposableSet;for(const o in n.kernelspecs){const t=o===n.default?0:1/0,r=n.kernelspecs[o],l=r.resources["logo-svg"]||r.resources["logo-64x64"];e.add(u.add({command:_.create,args:{isLauncher:!0,kernelPreference:{name:o}},category:x.__("Console"),rank:t,kernelIconUrl:l,metadata:{kernel:g.JSONExt.deepCopy(r.metadata||{})}}))}}};n(),w.kernelspecs.specsChanged.connect(n)}));const T="@jupyterlab/console-extension:tracker";let A,K={};async function R(e){A=(await l.get(T,"interactionMode")).composite,K=(await l.get(T,"promptCellConfig")).composite;const n=e=>{var n,o;e.console.node.dataset.jpInteractionMode=A,e.console.editorConfig=K,null===(o=null===(n=e.console.promptCell)||void 0===n?void 0:n.editor)||void 0===o||o.setOptions(K)};e?n(e):M.forEach(n)}if(l.pluginChanged.connect(((e,n)=>{n===T&&R()})),await R(),f){const e=f.getRenderer("@jupyterlab/codemirror-extension:plugin.defaultConfig");e&&f.addRenderer("@jupyterlab/console-extension:tracker.promptCellConfig",e)}function B(){return null!==M.currentWidget&&M.currentWidget===E.currentWidget}M.widgetAdded.connect(((e,n)=>{R(n)})),I.addCommand(_.autoClosingBrackets,{execute:async e=>{var n;K.autoClosingBrackets=!!(null!==(n=e.force)&&void 0!==n?n:!K.autoClosingBrackets),await l.set(T,"promptCellConfig",K)},label:x.__("Auto Close Brackets for Code Console Prompt"),isToggled:()=>K.autoClosingBrackets});let U=_.open;function W(e){const n=M.currentWidget;return!1!==e.activate&&n&&E.activateById(n.id),null!=n?n:null}I.addCommand(U,{label:x.__("Open a console for the provided `path`."),execute:e=>{const n=e.path,o=M.find((e=>{var o;return(null===(o=e.console.sessionContext.session)||void 0===o?void 0:o.path)===n}));return o?(!1!==e.activate&&E.activateById(o.id),o):w.ready.then((()=>(0,v.find)(w.sessions.running(),(e=>e.path===n))?j(e):Promise.reject(`No running kernel session for path: ${n}`)))}}),U=_.create,I.addCommand(U,{label:e=>{var n,o,t,r;if(e.isPalette)return x.__("New Console");if(e.isLauncher&&e.kernelPreference){const l=e.kernelPreference;return null!==(r=null===(t=null===(o=null===(n=w.kernelspecs)||void 0===n?void 0:n.specs)||void 0===o?void 0:o.kernelspecs[l.name||""])||void 0===t?void 0:t.display_name)&&void 0!==r?r:""}return x.__("Console")},icon:e=>e.isPalette?void 0:C.consoleIcon,execute:e=>{var n;return j({basePath:null!==(n=e.basePath||e.cwd||(null==i?void 0:i.model.path))&&void 0!==n?n:"",...e})}}),I.addCommand(_.undo,{execute:e=>{var n;const o=W(e);if(!o)return;const t=null===(n=o.console.promptCell)||void 0===n?void 0:n.editor;t&&t.undo()},isEnabled:e=>{var n,o,t;if(!B())return!1;const r=null===(t=null===(o=null===(n=W(e))||void 0===n?void 0:n.console)||void 0===o?void 0:o.promptCell)||void 0===t?void 0:t.editor;return!!r&&r.model.sharedModel.canUndo()},icon:C.undoIcon.bindprops({stylesheet:"menuItem"}),label:x.__("Undo")}),I.addCommand(_.redo,{execute:e=>{var n;const o=W(e);if(!o)return;const t=null===(n=o.console.promptCell)||void 0===n?void 0:n.editor;t&&t.redo()},isEnabled:e=>{var n,o,t;if(!B())return!1;const r=null===(t=null===(o=null===(n=W(e))||void 0===n?void 0:n.console)||void 0===o?void 0:o.promptCell)||void 0===t?void 0:t.editor;return!!r&&r.model.sharedModel.canRedo()},icon:C.redoIcon.bindprops({stylesheet:"menuItem"}),label:x.__("Redo")}),I.addCommand(_.clear,{label:x.__("Clear Console Cells"),execute:e=>{const n=W(e);n&&n.console.clear()},isEnabled:B}),I.addCommand(_.runUnforced,{label:x.__("Run Cell (unforced)"),execute:e=>{const n=W(e);if(n)return n.console.execute()},isEnabled:B}),I.addCommand(_.runForced,{label:x.__("Run Cell (forced)"),execute:e=>{const n=W(e);if(n)return n.console.execute(!0)},isEnabled:B}),I.addCommand(_.linebreak,{label:x.__("Insert Line Break"),execute:e=>{const n=W(e);n&&n.console.insertLinebreak()},isEnabled:B}),I.addCommand(_.replaceSelection,{label:x.__("Replace Selection in Console"),execute:e=>{const n=W(e);if(!n)return;const o=e.text||"";n.console.replaceSelection(o)},isEnabled:B}),I.addCommand(_.interrupt,{label:x.__("Interrupt Kernel"),execute:e=>{var n;const o=W(e);if(!o)return;const t=null===(n=o.console.sessionContext.session)||void 0===n?void 0:n.kernel;return t?t.interrupt():void 0},isEnabled:B}),I.addCommand(_.restart,{label:x.__("Restart Kernel…"),execute:e=>{const n=W(e);if(n)return P.restart(n.console.sessionContext)},isEnabled:B}),I.addCommand(_.shutdown,{label:x.__("Shut Down"),execute:e=>{const n=W(e);if(n)return n.console.sessionContext.shutdown()}}),I.addCommand(_.closeAndShutdown,{label:x.__("Close and Shut Down…"),execute:e=>{const n=W(e);if(n)return(0,r.showDialog)({title:x.__("Shut down the console?"),body:x.__('Are you sure you want to close "%1"?',n.title.label),buttons:[r.Dialog.cancelButton({ariaLabel:x.__("Cancel console Shut Down")}),r.Dialog.warnButton({ariaLabel:x.__("Confirm console Shut Down")})]}).then((e=>!!e.button.accept&&I.execute(_.shutdown,{activate:!1}).then((()=>(n.dispose(),!0)))))},isEnabled:B}),I.addCommand(_.inject,{label:x.__("Inject some code in a console."),execute:e=>{const n=e.path;M.find((o=>{var t;return(null===(t=o.console.sessionContext.session)||void 0===t?void 0:t.path)===n&&(!1!==e.activate&&E.activateById(o.id),o.console.inject(e.code,e.metadata),!0)}))},isEnabled:B}),I.addCommand(_.changeKernel,{label:x.__("Change Kernel…"),execute:e=>{const n=W(e);if(n)return P.selectKernel(n.console.sessionContext)},isEnabled:B}),I.addCommand(_.getKernel,{label:x.__("Get Kernel"),execute:e=>{var n;const o=W({activate:!1,...e});if(o)return null===(n=o.sessionContext.session)||void 0===n?void 0:n.kernel},isEnabled:B}),c&&[_.create,_.linebreak,_.clear,_.runUnforced,_.runForced,_.restart,_.interrupt,_.changeKernel,_.closeAndShutdown].forEach((e=>{c.addItem({command:e,category:S,args:{isPalette:!0}})})),d&&(d.fileMenu.closeAndCleaners.add({id:_.closeAndShutdown,isEnabled:B}),d.kernelMenu.kernelUsers.changeKernel.add({id:_.changeKernel,isEnabled:B}),d.kernelMenu.kernelUsers.clearWidget.add({id:_.clear,isEnabled:B}),d.kernelMenu.kernelUsers.interruptKernel.add({id:_.interrupt,isEnabled:B}),d.kernelMenu.kernelUsers.restartKernel.add({id:_.restart,isEnabled:B}),d.kernelMenu.kernelUsers.shutdownKernel.add({id:_.shutdown,isEnabled:B}),d.runMenu.codeRunners.run.add({id:_.runForced,isEnabled:B}),d.editMenu.clearers.clearCurrent.add({id:_.clear,isEnabled:B}),d.editMenu.undoers.redo.add({id:_.redo,isEnabled:B}),d.editMenu.undoers.undo.add({id:_.undo,isEnabled:B}),d.helpMenu.getKernel.add({id:_.getKernel,isEnabled:B}));const D={notebook:x.__("Execute with Shift+Enter"),terminal:x.__("Execute with Enter")};return I.addCommand(_.interactionMode,{label:e=>{var n;return null!==(n=D[e.interactionMode])&&void 0!==n?n:"Set the console interaction mode."},execute:async e=>{try{await l.set(T,"interactionMode",e.interactionMode)}catch(e){console.error(`Failed to set ${T}:keyMap - ${e.message}`)}},isToggled:e=>e.interactionMode===A}),M},autoStart:!0},w={id:"@jupyterlab/console-extension:factory",description:"Provides the console widget content factory.",provides:a.ConsolePanel.IContentFactory,requires:[l.IEditorServices],autoStart:!0,activate:(e,n)=>{const o=n.factoryService.newInlineEditor;return new a.ConsolePanel.ContentFactory({editorFactory:o})}},I={id:"@jupyterlab/console-extension:kernel-status",description:"Adds the console to the kernel status indicator model.",autoStart:!0,requires:[a.IConsoleTracker,r.IKernelStatusModel],activate:(e,n,o)=>{o.addSessionProvider((e=>e&&n.has(e)?e.sessionContext:null))}},E={id:"@jupyterlab/console-extension:cursor-position",description:"Adds the console to the code editor cursor position model.",autoStart:!0,requires:[a.IConsoleTracker,l.IPositionModel],activate:(e,n,o)=>{let t=null;o.addEditorProvider((async e=>{let r=null;if(e!==t){if(null==t||t.console.promptCellCreated.disconnect(o.update),t=null,e&&n.has(e)){e.console.promptCellCreated.connect(o.update);const n=e.console.promptCell;r=null,n&&(await n.ready,r=n.editor),t=e}}else if(e){const n=e.console.promptCell;r=null,n&&(await n.ready,r=n.editor)}return r}))}},S=[w,x,y,I,E,{id:"@jupyterlab/console-extension:completer",description:"Adds completion to the console.",autoStart:!0,requires:[a.IConsoleTracker],optional:[s.ICompletionProviderManager,m.ITranslator,r.ISanitizer],activate:function(e,n,o,t,l){if(!o)return;const s=(null!=t?t:m.nullTranslator).load("jupyterlab"),a=null!=l?l:new r.Sanitizer;e.commands.addCommand(_.invokeCompleter,{label:s.__("Display the completion helper."),execute:()=>{const e=n.currentWidget&&n.currentWidget.id;if(e)return o.invoke(e)}}),e.commands.addCommand(_.selectCompleter,{label:s.__("Select the completion suggestion."),execute:()=>{const e=n.currentWidget&&n.currentWidget.id;if(e)return o.select(e)}}),e.commands.addKeyBinding({command:_.selectCompleter,keys:["Enter"],selector:".jp-ConsolePanel .jp-mod-completer-active"});const i=async(e,n)=>{var t,r;const l={editor:null!==(r=null===(t=n.console.promptCell)||void 0===t?void 0:t.editor)&&void 0!==r?r:null,session:n.console.sessionContext.session,widget:n};await o.updateCompleter(l),n.console.promptCellCreated.connect(((e,t)=>{const r={editor:t.editor,session:e.sessionContext.session,widget:n,sanitzer:a};o.updateCompleter(r).catch(console.error)})),n.console.sessionContext.sessionChanged.connect((()=>{var e,t;const r={editor:null!==(t=null===(e=n.console.promptCell)||void 0===e?void 0:e.editor)&&void 0!==t?t:null,session:n.console.sessionContext.session,widget:n,sanitizer:a};o.updateCompleter(r).catch(console.error)}))};n.widgetAdded.connect(i),o.activeProvidersChanged.connect((()=>{n.forEach((e=>{i(0,e).catch((e=>console.error(e)))}))}))}}]}}]);
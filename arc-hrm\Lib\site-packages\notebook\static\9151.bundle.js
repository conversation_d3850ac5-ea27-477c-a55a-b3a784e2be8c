(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[9151],{59151:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ExtensionsPanel:()=>R,ListModel:()=>p});var n,s=r(12982),a=r(38639),i=r(16954),o=r(71677),l=r(68239),h=r(97934),c=r(38873),u=r(78156);class p extends l.VDomModel{constructor(e,t){super(),this.actionError=null,this.installedError=null,this.searchError=null,this.promptReload=!1,this._isDisclaimed=!1,this._isEnabled=!1,this._isLoadingInstalledExtensions=!1,this._isSearching=!1,this._query="",this._page=1,this._pagination=30,this._lastPage=1,this._pendingActions=[];const r=JSON.parse(a.PageConfig.getOption("extensionManager")||"{}");this.name=r.name,this.canInstall=r.can_install,this.installPath=r.install_path,this.translator=t||o.nullTranslator,this._installed=[],this._lastSearchResult=[],this.serviceManager=e,this._debouncedSearch=new h.Debouncer(this.search.bind(this),1e3)}get installed(){return this._installed}get isDisclaimed(){return this._isDisclaimed}set isDisclaimed(e){e!==this._isDisclaimed&&(this._isDisclaimed=e,this.stateChanged.emit(),this._debouncedSearch.invoke())}get isEnabled(){return this._isEnabled}set isEnabled(e){e!==this._isEnabled&&(this._isEnabled=e,this.stateChanged.emit())}get isLoadingInstalledExtensions(){return this._isLoadingInstalledExtensions}get isSearching(){return this._isSearching}get searchResult(){return this._lastSearchResult}get query(){return this._query}set query(e){this._query!==e&&(this._query=e,this._page=1,this._debouncedSearch.invoke())}get page(){return this._page}set page(e){this._page!==e&&(this._page=e,this._debouncedSearch.invoke())}get pagination(){return this._pagination}set pagination(e){this._pagination!==e&&(this._pagination=e,this._debouncedSearch.invoke())}get lastPage(){return this._lastPage}dispose(){this.isDisposed||(this._debouncedSearch.dispose(),super.dispose())}hasPendingActions(){return this._pendingActions.length>0}async install(e,t={}){await this.performAction("install",e,t).then((t=>("ok"!==t.status&&function(e,t,r){const n=(r=r||o.nullTranslator).load("jupyterlab"),a=[];a.push(u.createElement("p",null,n.__(`An error occurred installing "${e}".`))),t&&a.push(u.createElement("p",null,u.createElement("span",{className:"jp-extensionmanager-dialog-subheader"},n.__("Error message:"))),u.createElement("pre",null,t.trim()));const i=u.createElement("div",{className:"jp-extensionmanager-dialog"},a);(0,s.showDialog)({title:n.__("Extension Installation Error"),body:i,buttons:[s.Dialog.warnButton({label:n.__("Ok")})]})}(e.name,t.message,this.translator),this.update(!0))))}async uninstall(e){if(!e.installed)throw new Error(`Not installed, cannot uninstall: ${e.name}`);return await this.performAction("uninstall",e),this.update(!0)}async enable(e){if(e.enabled)throw new Error(`Already enabled: ${e.name}`);await this.performAction("enable",e),await this.refreshInstalled(!0)}async disable(e){if(!e.enabled)throw new Error(`Already disabled: ${e.name}`);await this.performAction("disable",e),await this.refreshInstalled(!0)}async refreshInstalled(e=!1){this.installedError=null,this._isLoadingInstalledExtensions=!0,this.stateChanged.emit();try{const[t]=await n.requestAPI({refresh:e?1:0});this._installed=t.sort(n.comparator)}catch(e){this.installedError=e.toString()}finally{this._isLoadingInstalledExtensions=!1,this.stateChanged.emit()}}async search(e=!1){var t,r;if(!this.isDisclaimed)return Promise.reject("Installation warning is not disclaimed.");this.searchError=null,this._isSearching=!0,this.stateChanged.emit();try{const[s,i]=await n.requestAPI({query:null!==(t=this.query)&&void 0!==t?t:"",page:this.page,per_page:this.pagination,refresh:e?1:0}),o=i.last;if(o){const e=a.URLExt.queryStringToObject(null!==(r=a.URLExt.parse(o).search)&&void 0!==r?r:"").page;e&&(this._lastPage=parseInt(e,10))}const l=this._installed.map((e=>e.name));this._lastSearchResult=s.filter((e=>!l.includes(e.name))).sort(n.comparator)}catch(e){this.searchError=e.toString()}finally{this._isSearching=!1,this.stateChanged.emit()}}async update(e=!1){this.isDisclaimed&&(await this.refreshInstalled(e),await this.search())}performAction(e,t,r={}){const a={cmd:e,extension_name:t.name};r.useVersion&&(a.extension_version=r.useVersion);const i=n.requestAPI({},{method:"POST",body:JSON.stringify(a)});return i.then((([e])=>{const t=this.translator.load("jupyterlab");if(e.needs_restart.includes("server"))(0,s.showDialog)({title:t.__("Information"),body:t.__("You will need to restart JupyterLab to apply the changes."),buttons:[s.Dialog.okButton({label:t.__("Ok")})]});else{const r=[];e.needs_restart.includes("frontend")&&r.push(window.isElectron?t.__("reload JupyterLab"):t.__("refresh the web page")),e.needs_restart.includes("kernel")&&r.push(t.__("install the extension in all kernels and restart them")),(0,s.showDialog)({title:t.__("Information"),body:t.__("You will need to %1 to apply the changes.",r.join(t.__(" and "))),buttons:[s.Dialog.okButton({label:t.__("Ok")})]})}this.actionError=null}),(e=>{this.actionError=e.toString()})),this.addPendingAction(i),i.then((([e])=>e))}addPendingAction(e){this._pendingActions.push(e);const t=()=>{const t=this._pendingActions.indexOf(e);this._pendingActions.splice(t,1),this.stateChanged.emit(void 0)};e.then(t,t),this.stateChanged.emit(void 0)}}!function(e){e.entryHasUpdate=function(e){return!(!e.installed||!e.latest_version)&&c.lt(e.installed_version,e.latest_version)}}(p||(p={})),function(e){e.comparator=function(e,t){return e.name===t.name?0:e.name>t.name?1:-1};const t=/<([^>]+)>; rel="([^"]+)",?/g;e.requestAPI=async function(e={},r={}){var n;const s=i.ServerConnection.makeSettings(),o=a.URLExt.join(s.baseUrl,"lab/api/extensions");let l;try{l=await i.ServerConnection.makeRequest(o+a.URLExt.objectToQueryString(e),r,s)}catch(e){throw new i.ServerConnection.NetworkError(e)}let h=await l.text();if(h.length>0)try{h=JSON.parse(h)}catch(e){console.log("Not a JSON response body.",l)}if(!l.ok)throw new i.ServerConnection.ResponseError(l,h.message||h);const c=null!==(n=l.headers.get("Link"))&&void 0!==n?n:"",u={};let p=null;for(;null!==(p=t.exec(c));)u[p[2]]=p[1];return[h,u]}}(n||(n={}));var d=r(96389),m=r.n(d);const f=32,g=Math.floor(devicePixelRatio*f);function E(e){const{canFetch:t,entry:r,supportInstallation:n,trans:s}=e,a=[];r.status&&-1!==["ok","warning","error"].indexOf(r.status)&&a.push(`jp-extensionmanager-entry-${r.status}`);const i=t?function(e){return e.homepage_url&&e.homepage_url.startsWith("https://github.com/")?e.homepage_url.split("/")[3]:e.repository_url&&e.repository_url.startsWith("https://github.com/")?e.repository_url.split("/")[3]:null}(r):null;return r.allowed||a.push("jp-extensionmanager-entry-should-be-uninstalled"),u.createElement("li",{className:`jp-extensionmanager-entry ${a.join(" ")}`,style:{display:"flex"}},u.createElement("div",{style:{marginRight:"8px"}},i?u.createElement("img",{src:`https://github.com/${i}.png?size=${g}`,style:{width:"32px",height:"32px"}}):u.createElement("div",{style:{width:`${f}px`,height:`${f}px`}})),u.createElement("div",{className:"jp-extensionmanager-entry-description"},u.createElement("div",{className:"jp-extensionmanager-entry-title"},u.createElement("div",{className:"jp-extensionmanager-entry-name"},r.homepage_url?u.createElement("a",{href:r.homepage_url,target:"_blank",rel:"noopener noreferrer",title:s.__("%1 extension home page",r.name)},r.name):u.createElement("div",null,r.name)),u.createElement("div",{className:"jp-extensionmanager-entry-version"},u.createElement("div",{title:s.__("Version: %1",r.installed_version)},r.installed_version)),r.installed&&!r.allowed&&u.createElement(l.ToolbarButtonComponent,{icon:l.infoIcon,iconLabel:s.__("%1 extension is not allowed anymore. Please uninstall it immediately or contact your administrator.",r.name),onClick:()=>window.open("https://jupyterlab.readthedocs.io/en/stable/user/extensions.html")}),r.approved&&u.createElement(l.jupyterIcon.react,{className:"jp-extensionmanager-is-approved",top:"1px",height:"auto",width:"1em",title:s.__("This extension is approved by your security team.")})),u.createElement("div",{className:"jp-extensionmanager-entry-content"},u.createElement("div",{className:"jp-extensionmanager-entry-description"},r.description),e.performAction&&u.createElement("div",{className:"jp-extensionmanager-entry-buttons"},r.installed?u.createElement(u.Fragment,null,n&&u.createElement(u.Fragment,null,p.entryHasUpdate(r)&&u.createElement(l.Button,{onClick:()=>e.performAction("install",r,{useVersion:r.latest_version}),title:s.__('Update "%1" to "%2"',r.name,r.latest_version),minimal:!0,small:!0},s.__("Update to %1",r.latest_version)),u.createElement(l.Button,{onClick:()=>e.performAction("uninstall",r),title:s.__('Uninstall "%1"',r.name),minimal:!0,small:!0},s.__("Uninstall"))),r.enabled?u.createElement(l.Button,{onClick:()=>e.performAction("disable",r),title:s.__('Disable "%1"',r.name),minimal:!0,small:!0},s.__("Disable")):u.createElement(l.Button,{onClick:()=>e.performAction("enable",r),title:s.__('Enable "%1"',r.name),minimal:!0,small:!0},s.__("Enable"))):n&&u.createElement(l.Button,{onClick:()=>e.performAction("install",r),title:s.__('Install "%1"',r.name),minimal:!0,small:!0},s.__("Install"))))))}function v(e){var t;const{canFetch:r,performAction:n,supportInstallation:s,trans:a}=e;return u.createElement("div",{className:"jp-extensionmanager-listview-wrapper"},e.entries.length>0?u.createElement("ul",{className:"jp-extensionmanager-listview"},e.entries.map((e=>u.createElement(E,{key:e.name,canFetch:r,entry:e,performAction:n,supportInstallation:s,trans:a})))):u.createElement("div",{key:"message",className:"jp-extensionmanager-listview-message"},a.__("No entries")),e.numPages>1&&u.createElement("div",{className:"jp-extensionmanager-pagination"},u.createElement(m(),{previousLabel:"<",nextLabel:">",breakLabel:"...",breakClassName:"break",initialPage:(null!==(t=e.initialPage)&&void 0!==t?t:1)-1,pageCount:e.numPages,marginPagesDisplayed:2,pageRangeDisplayed:3,onPageChange:t=>e.onPage(t.selected+1),activeClassName:"active"})))}function y(e){return u.createElement("div",{className:"jp-extensionmanager-error"},e.children)}class b extends l.ReactWidget{constructor(e,t,r){super(),this.model=e,this.trans=t,this.searchInputRef=r,e.stateChanged.connect(this.update,this),this.addClass("jp-extensionmanager-header")}render(){return u.createElement(u.Fragment,null,u.createElement("div",{className:"jp-extensionmanager-title"},u.createElement("span",null,this.trans.__("%1 Manager",this.model.name)),this.model.installPath&&u.createElement(l.infoIcon.react,{className:"jp-extensionmanager-path",tag:"span",title:this.trans.__("Extension installation path: %1",this.model.installPath)})),u.createElement(l.FilterBox,{placeholder:this.trans.__("Search extensions"),disabled:!this.model.isDisclaimed,updateFilter:(e,t)=>{this.model.query=null!=t?t:""},useFuzzyFilter:!1,inputRef:this.searchInputRef}),u.createElement("div",{className:"jp-extensionmanager-pending "+(this.model.hasPendingActions()?"jp-mod-hasPending":"")}),this.model.actionError&&u.createElement(y,null,u.createElement("p",null,this.trans.__("Error when performing an action.")),u.createElement("p",null,this.trans.__("Reason given:")),u.createElement("pre",null,this.model.actionError)))}}class w extends l.ReactWidget{constructor(e,t){super(),this.model=e,this.trans=t,this.addClass("jp-extensionmanager-disclaimer"),e.stateChanged.connect(this.update,this)}render(){return u.createElement(u.Fragment,null,u.createElement("p",null,this.trans.__("The JupyterLab development team is excited to have a robust\nthird-party extension community. However, we do not review\nthird-party extensions, and some extensions may introduce security\nrisks or contain malicious code that runs on your machine. Moreover in order\nto work, this panel needs to fetch data from web services. Do you agree to\nactivate this feature?"),u.createElement("br",null),u.createElement("a",{href:"https://jupyterlab.readthedocs.io/en/stable/privacy_policies.html",target:"_blank",rel:"noreferrer"},this.trans.__("Please read the privacy policy."))),this.model.isDisclaimed?u.createElement(l.Button,{className:"jp-extensionmanager-disclaimer-disable",onClick:e=>{this.model.isDisclaimed=!1},title:this.trans.__("This will withdraw your consent.")},this.trans.__("No")):u.createElement("div",null,u.createElement(l.Button,{className:"jp-extensionmanager-disclaimer-enable",onClick:()=>{this.model.isDisclaimed=!0}},this.trans.__("Yes")),u.createElement(l.Button,{className:"jp-extensionmanager-disclaimer-disable",onClick:()=>{this.model.isEnabled=!1},title:this.trans.__("This will disable the extension manager panel; including the listing of installed extension.")},this.trans.__("No, disable"))))}}class x extends l.ReactWidget{constructor(e,t){super(),this.model=e,this.trans=t,e.stateChanged.connect(this.update,this)}render(){return u.createElement(u.Fragment,null,null!==this.model.installedError?u.createElement(y,null,"Error querying installed extensions"+(this.model.installedError?`: ${this.model.installedError}`:".")):this.model.isLoadingInstalledExtensions?u.createElement("div",{className:"jp-extensionmanager-loader"},this.trans.__("Updating extensions list…")):u.createElement(v,{canFetch:this.model.isDisclaimed,entries:this.model.installed.filter((e=>new RegExp(this.model.query.toLowerCase()).test(e.name))),numPages:1,trans:this.trans,onPage:e=>{},performAction:this.model.isDisclaimed?this.onAction.bind(this):null,supportInstallation:this.model.canInstall&&this.model.isDisclaimed}))}onAction(e,t,r={}){switch(e){case"install":return this.model.install(t,r);case"uninstall":return this.model.uninstall(t);case"enable":return this.model.enable(t);case"disable":return this.model.disable(t);default:throw new Error(`Invalid action: ${e}`)}}}class _ extends l.ReactWidget{constructor(e,t){super(),this.model=e,this.trans=t,e.stateChanged.connect(this.update,this)}onPage(e){this.model.page=e}onAction(e,t,r={}){switch(e){case"install":return this.model.install(t,r);case"uninstall":return this.model.uninstall(t);case"enable":return this.model.enable(t);case"disable":return this.model.disable(t);default:throw new Error(`Invalid action: ${e}`)}}render(){return u.createElement(u.Fragment,null,null!==this.model.searchError?u.createElement(y,null,"Error searching for extensions"+(this.model.searchError?`: ${this.model.searchError}`:".")):this.model.isSearching?u.createElement("div",{className:"jp-extensionmanager-loader"},this.trans.__("Updating extensions list…")):u.createElement(v,{canFetch:this.model.isDisclaimed,entries:this.model.searchResult,initialPage:this.model.page,numPages:this.model.lastPage,onPage:e=>{this.onPage(e)},performAction:this.model.isDisclaimed?this.onAction.bind(this):null,supportInstallation:this.model.canInstall&&this.model.isDisclaimed,trans:this.trans}))}update(){this.title.label=this.model.query?this.trans.__("Search Results"):this.trans.__("Discover"),super.update()}}class R extends l.SidePanel{constructor(e){const{model:t,translator:r}=e;super({translator:r}),this._wasInitialized=!1,this._wasDisclaimed=!0,this.model=t,this._searchInputRef=u.createRef(),this.addClass("jp-extensionmanager-view"),this.trans=r.load("jupyterlab"),this.header.addWidget(new b(t,this.trans,this._searchInputRef));const n=new w(t,this.trans);n.title.label=this.trans.__("Warning"),this.addWidget(n);const s=new l.PanelWithToolbar;if(s.addClass("jp-extensionmanager-installedlist"),s.title.label=this.trans.__("Installed"),s.toolbar.addItem("refresh",new l.ToolbarButton({icon:l.refreshIcon,onClick:()=>{t.refreshInstalled(!0).catch((e=>{console.error(`Failed to refresh the installed extensions list:\n${e}`)}))},tooltip:this.trans.__("Refresh extensions list")})),s.addWidget(new x(t,this.trans)),this.addWidget(s),this.model.canInstall){const e=new _(t,this.trans);e.addClass("jp-extensionmanager-searchresults"),this.addWidget(e)}this._wasDisclaimed=this.model.isDisclaimed,this.model.isDisclaimed?(this.content.collapse(0),this.content.layout.setRelativeSizes([0,1,1])):(this.content.expand(0),this.content.collapse(1),this.content.collapse(2)),this.model.stateChanged.connect(this._onStateChanged,this)}dispose(){this.isDisposed||(this.model.stateChanged.disconnect(this._onStateChanged,this),super.dispose())}handleEvent(e){switch(e.type){case"focus":case"blur":this._toggleFocused()}}onBeforeAttach(e){this.node.addEventListener("focus",this,!0),this.node.addEventListener("blur",this,!0),super.onBeforeAttach(e)}onBeforeShow(e){this._wasInitialized||(this._wasInitialized=!0,this.model.refreshInstalled().catch((e=>{console.log(`Failed to refresh installed extension list:\n${e}`)})))}onAfterDetach(e){super.onAfterDetach(e),this.node.removeEventListener("focus",this,!0),this.node.removeEventListener("blur",this,!0)}onActivateRequest(e){if(this.isAttached){const e=this._searchInputRef.current;e&&(e.focus(),e.select())}super.onActivateRequest(e)}_onStateChanged(){!this._wasDisclaimed&&this.model.isDisclaimed&&(this.content.collapse(0),this.content.expand(1),this.content.expand(2)),this._wasDisclaimed=this.model.isDisclaimed}_toggleFocused(){const e=document.activeElement===this._searchInputRef.current;this.toggleClass("lm-mod-focused",e)}}},27061:e=>{var t,r,n=e.exports={};function s(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function i(e){if(t===setTimeout)return setTimeout(e,0);if((t===s||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(r){try{return t.call(null,e,0)}catch(r){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:s}catch(e){t=s}try{r="function"==typeof clearTimeout?clearTimeout:a}catch(e){r=a}}();var o,l=[],h=!1,c=-1;function u(){h&&o&&(h=!1,o.length?l=o.concat(l):c=-1,l.length&&p())}function p(){if(!h){var e=i(u);h=!0;for(var t=l.length;t;){for(o=l,l=[];++c<t;)o&&o[c].run();c=-1,t=l.length}o=null,h=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===a||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{return r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function d(e,t){this.fun=e,this.array=t}function m(){}n.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];l.push(new d(e,t)),1!==l.length||h||i(p)},d.prototype.run=function(){this.fun.apply(null,this.array)},n.title="browser",n.browser=!0,n.env={},n.argv=[],n.version="",n.versions={},n.on=m,n.addListener=m,n.once=m,n.off=m,n.removeListener=m,n.removeAllListeners=m,n.emit=m,n.prependListener=m,n.prependOnceListener=m,n.listeners=function(e){return[]},n.binding=function(e){throw new Error("process.binding is not supported")},n.cwd=function(){return"/"},n.chdir=function(e){throw new Error("process.chdir is not supported")},n.umask=function(){return 0}},96389:(e,t,r)=>{var n;r.g,e.exports=(n=r(78156),function(e){var t={};function r(n){if(t[n])return t[n].exports;var s=t[n]={i:n,l:!1,exports:{}};return e[n].call(s.exports,s,s.exports,r),s.l=!0,s.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var s in e)r.d(n,s,function(t){return e[t]}.bind(null,s));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=4)}([function(e,t,r){e.exports=r(2)()},function(e,t){e.exports=n},function(e,t,r){"use strict";var n=r(3);function s(){}function a(){}a.resetWarningCache=s,e.exports=function(){function e(e,t,r,s,a,i){if(i!==n){var o=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw o.name="Invariant Violation",o}}function t(){return e}e.isRequired=e;var r={array:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:a,resetWarningCache:s};return r.PropTypes=r,r}},function(e,t,r){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},function(e,r,n){"use strict";n.r(r);var s=n(1),a=n.n(s),i=n(0),o=n.n(i);function l(){return(l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var h=function(e){var t=e.pageClassName,r=e.pageLinkClassName,n=e.page,s=e.selected,i=e.activeClassName,o=e.activeLinkClassName,h=e.getEventListener,c=e.pageSelectedHandler,u=e.href,p=e.extraAriaContext,d=e.ariaLabel||"Page "+n+(p?" "+p:""),m=null;return s&&(m="page",d=e.ariaLabel||"Page "+n+" is your current page",t=void 0!==t?t+" "+i:i,void 0!==r?void 0!==o&&(r=r+" "+o):r=o),a.a.createElement("li",{className:t},a.a.createElement("a",l({role:"button",className:r,href:u,tabIndex:"0","aria-label":d,"aria-current":m,onKeyPress:c},h(c)),n))};h.propTypes={pageSelectedHandler:o.a.func.isRequired,selected:o.a.bool.isRequired,pageClassName:o.a.string,pageLinkClassName:o.a.string,activeClassName:o.a.string,activeLinkClassName:o.a.string,extraAriaContext:o.a.string,href:o.a.string,ariaLabel:o.a.string,page:o.a.number.isRequired,getEventListener:o.a.func.isRequired};var c=h;function u(){return(u=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}!function(){var e="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0;if(e){var n=void 0!==r?r:t;if(n)if("function"!=typeof n){for(var s in n)if(Object.prototype.hasOwnProperty.call(n,s)){var a=void 0;try{a=n[s]}catch(e){continue}e.register(a,s,"/home/<USER>/workspace/react-paginate/react_components/PageView.js")}}else e.register(n,"module.exports","/home/<USER>/workspace/react-paginate/react_components/PageView.js")}}();var p=function(e){var t=e.breakLabel,r=e.breakClassName,n=e.breakLinkClassName,s=e.breakHandler,i=e.getEventListener,o=r||"break";return a.a.createElement("li",{className:o},a.a.createElement("a",u({className:n,role:"button",tabIndex:"0",onKeyPress:s},i(s)),t))};p.propTypes={breakLabel:o.a.oneOfType([o.a.string,o.a.node]),breakClassName:o.a.string,breakLinkClassName:o.a.string,breakHandler:o.a.func.isRequired,getEventListener:o.a.func.isRequired};var d=p;function m(e){return(m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function f(){return(f=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function g(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function E(e,t){return(E=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function v(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=b(e);if(t){var s=b(this).constructor;r=Reflect.construct(n,arguments,s)}else r=n.apply(this,arguments);return function(e,t){return!t||"object"!==m(t)&&"function"!=typeof t?y(e):t}(this,r)}}function y(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function b(e){return(b=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function w(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}!function(){var e="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0;if(e){var n=void 0!==r?r:t;if(n)if("function"!=typeof n){for(var s in n)if(Object.prototype.hasOwnProperty.call(n,s)){var a=void 0;try{a=n[s]}catch(e){continue}e.register(a,s,"/home/<USER>/workspace/react-paginate/react_components/BreakView.js")}}else e.register(n,"module.exports","/home/<USER>/workspace/react-paginate/react_components/BreakView.js")}}();var x=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&E(e,t)}(s,e);var t,r,n=v(s);function s(e){var t,r;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),w(y(t=n.call(this,e)),"handlePreviousPage",(function(e){var r=t.state.selected;e.preventDefault?e.preventDefault():e.returnValue=!1,r>0&&t.handlePageSelected(r-1,e)})),w(y(t),"handleNextPage",(function(e){var r=t.state.selected,n=t.props.pageCount;e.preventDefault?e.preventDefault():e.returnValue=!1,r<n-1&&t.handlePageSelected(r+1,e)})),w(y(t),"handlePageSelected",(function(e,r){r.preventDefault?r.preventDefault():r.returnValue=!1,t.state.selected!==e&&(t.setState({selected:e}),t.callCallback(e))})),w(y(t),"getEventListener",(function(e){return w({},t.props.eventListener,e)})),w(y(t),"handleBreakClick",(function(e,r){r.preventDefault?r.preventDefault():r.returnValue=!1;var n=t.state.selected;t.handlePageSelected(n<e?t.getForwardJump():t.getBackwardJump(),r)})),w(y(t),"callCallback",(function(e){void 0!==t.props.onPageChange&&"function"==typeof t.props.onPageChange&&t.props.onPageChange({selected:e})})),w(y(t),"pagination",(function(){var e=[],r=t.props,n=r.pageRangeDisplayed,s=r.pageCount,i=r.marginPagesDisplayed,o=r.breakLabel,l=r.breakClassName,h=r.breakLinkClassName,c=t.state.selected;if(s<=n)for(var u=0;u<s;u++)e.push(t.getPageElement(u));else{var p,m,f,g=n/2,E=n-g;c>s-n/2?g=n-(E=s-c):c<n/2&&(E=n-(g=c));var v=function(e){return t.getPageElement(e)};for(p=0;p<s;p++)(m=p+1)<=i||m>s-i||p>=c-g&&p<=c+E?e.push(v(p)):o&&e[e.length-1]!==f&&(f=a.a.createElement(d,{key:p,breakLabel:o,breakClassName:l,breakLinkClassName:h,breakHandler:t.handleBreakClick.bind(null,p),getEventListener:t.getEventListener}),e.push(f))}return e})),r=e.initialPage?e.initialPage:e.forcePage?e.forcePage:0,t.state={selected:r},t}return t=s,(r=[{key:"componentDidMount",value:function(){var e=this.props,t=e.initialPage,r=e.disableInitialCallback,n=e.extraAriaContext;void 0===t||r||this.callCallback(t),n&&console.warn("DEPRECATED (react-paginate): The extraAriaContext prop is deprecated. You should now use the ariaLabelBuilder instead.")}},{key:"componentDidUpdate",value:function(e){void 0!==this.props.forcePage&&this.props.forcePage!==e.forcePage&&this.setState({selected:this.props.forcePage})}},{key:"getForwardJump",value:function(){var e=this.state.selected,t=this.props,r=t.pageCount,n=e+t.pageRangeDisplayed;return n>=r?r-1:n}},{key:"getBackwardJump",value:function(){var e=this.state.selected-this.props.pageRangeDisplayed;return e<0?0:e}},{key:"hrefBuilder",value:function(e){var t=this.props,r=t.hrefBuilder,n=t.pageCount;if(r&&e!==this.state.selected&&e>=0&&e<n)return r(e+1)}},{key:"ariaLabelBuilder",value:function(e){var t=e===this.state.selected;if(this.props.ariaLabelBuilder&&e>=0&&e<this.props.pageCount){var r=this.props.ariaLabelBuilder(e+1,t);return this.props.extraAriaContext&&!t&&(r=r+" "+this.props.extraAriaContext),r}}},{key:"getPageElement",value:function(e){var t=this.state.selected,r=this.props,n=r.pageClassName,s=r.pageLinkClassName,i=r.activeClassName,o=r.activeLinkClassName,l=r.extraAriaContext;return a.a.createElement(c,{key:e,pageSelectedHandler:this.handlePageSelected.bind(null,e),selected:t===e,pageClassName:n,pageLinkClassName:s,activeClassName:i,activeLinkClassName:o,extraAriaContext:l,href:this.hrefBuilder(e),ariaLabel:this.ariaLabelBuilder(e),page:e+1,getEventListener:this.getEventListener})}},{key:"render",value:function(){var e=this.props,t=e.disabledClassName,r=e.pageCount,n=e.containerClassName,s=e.previousLabel,i=e.previousClassName,o=e.previousLinkClassName,l=e.previousAriaLabel,h=e.nextLabel,c=e.nextClassName,u=e.nextLinkClassName,p=e.nextAriaLabel,d=this.state.selected,m=i+(0===d?" ".concat(t):""),g=c+(d===r-1?" ".concat(t):""),E=0===d?"true":"false",v=d===r-1?"true":"false";return a.a.createElement("ul",{className:n},a.a.createElement("li",{className:m},a.a.createElement("a",f({className:o,href:this.hrefBuilder(d-1),tabIndex:"0",role:"button",onKeyPress:this.handlePreviousPage,"aria-disabled":E,"aria-label":l},this.getEventListener(this.handlePreviousPage)),s)),this.pagination(),a.a.createElement("li",{className:g},a.a.createElement("a",f({className:u,href:this.hrefBuilder(d+1),tabIndex:"0",role:"button",onKeyPress:this.handleNextPage,"aria-disabled":v,"aria-label":p},this.getEventListener(this.handleNextPage)),h)))}}])&&g(t.prototype,r),s}(s.Component);w(x,"propTypes",{pageCount:o.a.number.isRequired,pageRangeDisplayed:o.a.number.isRequired,marginPagesDisplayed:o.a.number.isRequired,previousLabel:o.a.node,previousAriaLabel:o.a.string,nextLabel:o.a.node,nextAriaLabel:o.a.string,breakLabel:o.a.oneOfType([o.a.string,o.a.node]),hrefBuilder:o.a.func,onPageChange:o.a.func,initialPage:o.a.number,forcePage:o.a.number,disableInitialCallback:o.a.bool,containerClassName:o.a.string,pageClassName:o.a.string,pageLinkClassName:o.a.string,activeClassName:o.a.string,activeLinkClassName:o.a.string,previousClassName:o.a.string,nextClassName:o.a.string,previousLinkClassName:o.a.string,nextLinkClassName:o.a.string,disabledClassName:o.a.string,breakClassName:o.a.string,breakLinkClassName:o.a.string,extraAriaContext:o.a.string,ariaLabelBuilder:o.a.func,eventListener:o.a.string}),w(x,"defaultProps",{pageCount:10,pageRangeDisplayed:2,marginPagesDisplayed:3,activeClassName:"selected",previousLabel:"Previous",previousClassName:"previous",previousAriaLabel:"Previous page",nextLabel:"Next",nextClassName:"next",nextAriaLabel:"Next page",breakLabel:"...",disabledClassName:"disabled",disableInitialCallback:!1,eventListener:"onClick"}),function(){var e="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0;if(e){var n=void 0!==r?r:t;if(n)if("function"!=typeof n){for(var s in n)if(Object.prototype.hasOwnProperty.call(n,s)){var a=void 0;try{a=n[s]}catch(e){continue}e.register(a,s,"/home/<USER>/workspace/react-paginate/react_components/PaginationBoxView.js")}}else e.register(n,"module.exports","/home/<USER>/workspace/react-paginate/react_components/PaginationBoxView.js")}}(),r.default=x,function(){var e="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0;if(e){var n=void 0!==r?r:t;if(n)if("function"!=typeof n){for(var s in n)if(Object.prototype.hasOwnProperty.call(n,s)){var a=void 0;try{a=n[s]}catch(e){continue}e.register(a,s,"/home/<USER>/workspace/react-paginate/react_components/index.js")}}else e.register(n,"module.exports","/home/<USER>/workspace/react-paginate/react_components/index.js")}}()}]))},43134:(e,t,r)=>{const n=Symbol("SemVer ANY");class s{static get ANY(){return n}constructor(e,t){if(t=a(t),e instanceof s){if(e.loose===!!t.loose)return e;e=e.value}e=e.trim().split(/\s+/).join(" "),h("comparator",e,t),this.options=t,this.loose=!!t.loose,this.parse(e),this.semver===n?this.value="":this.value=this.operator+this.semver.version,h("comp",this)}parse(e){const t=this.options.loose?i[o.COMPARATORLOOSE]:i[o.COMPARATOR],r=e.match(t);if(!r)throw new TypeError(`Invalid comparator: ${e}`);this.operator=void 0!==r[1]?r[1]:"","="===this.operator&&(this.operator=""),r[2]?this.semver=new c(r[2],this.options.loose):this.semver=n}toString(){return this.value}test(e){if(h("Comparator.test",e,this.options.loose),this.semver===n||e===n)return!0;if("string"==typeof e)try{e=new c(e,this.options)}catch(e){return!1}return l(e,this.operator,this.semver,this.options)}intersects(e,t){if(!(e instanceof s))throw new TypeError("a Comparator is required");return""===this.operator?""===this.value||new u(e.value,t).test(this.value):""===e.operator?""===e.value||new u(this.value,t).test(e.semver):!((t=a(t)).includePrerelease&&("<0.0.0-0"===this.value||"<0.0.0-0"===e.value)||!t.includePrerelease&&(this.value.startsWith("<0.0.0")||e.value.startsWith("<0.0.0"))||(!this.operator.startsWith(">")||!e.operator.startsWith(">"))&&(!this.operator.startsWith("<")||!e.operator.startsWith("<"))&&(this.semver.version!==e.semver.version||!this.operator.includes("=")||!e.operator.includes("="))&&!(l(this.semver,"<",e.semver,t)&&this.operator.startsWith(">")&&e.operator.startsWith("<"))&&!(l(this.semver,">",e.semver,t)&&this.operator.startsWith("<")&&e.operator.startsWith(">")))}}e.exports=s;const a=r(48716),{safeRe:i,t:o}=r(19022),l=r(35452),h=r(46830),c=r(89510),u=r(87374)},87374:(e,t,r)=>{class n{constructor(e,t){if(t=a(t),e instanceof n)return e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease?e:new n(e.raw,t);if(e instanceof i)return this.raw=e.value,this.set=[[e]],this.format(),this;if(this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease,this.raw=e.trim().split(/\s+/).join(" "),this.set=this.raw.split("||").map((e=>this.parseRange(e.trim()))).filter((e=>e.length)),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){const e=this.set[0];if(this.set=this.set.filter((e=>!g(e[0]))),0===this.set.length)this.set=[e];else if(this.set.length>1)for(const e of this.set)if(1===e.length&&E(e[0])){this.set=[e];break}}this.format()}format(){return this.range=this.set.map((e=>e.join(" ").trim())).join("||").trim(),this.range}toString(){return this.range}parseRange(e){const t=((this.options.includePrerelease&&m)|(this.options.loose&&f))+":"+e,r=s.get(t);if(r)return r;const n=this.options.loose,a=n?h[c.HYPHENRANGELOOSE]:h[c.HYPHENRANGE];e=e.replace(a,A(this.options.includePrerelease)),o("hyphen replace",e),e=e.replace(h[c.COMPARATORTRIM],u),o("comparator trim",e),e=e.replace(h[c.TILDETRIM],p),o("tilde trim",e),e=e.replace(h[c.CARETTRIM],d),o("caret trim",e);let l=e.split(" ").map((e=>y(e,this.options))).join(" ").split(/\s+/).map((e=>$(e,this.options)));n&&(l=l.filter((e=>(o("loose invalid filter",e,this.options),!!e.match(h[c.COMPARATORLOOSE]))))),o("range list",l);const E=new Map,v=l.map((e=>new i(e,this.options)));for(const e of v){if(g(e))return[e];E.set(e.value,e)}E.size>1&&E.has("")&&E.delete("");const b=[...E.values()];return s.set(t,b),b}intersects(e,t){if(!(e instanceof n))throw new TypeError("a Range is required");return this.set.some((r=>v(r,t)&&e.set.some((e=>v(e,t)&&r.every((r=>e.every((e=>r.intersects(e,t)))))))))}test(e){if(!e)return!1;if("string"==typeof e)try{e=new l(e,this.options)}catch(e){return!1}for(let t=0;t<this.set.length;t++)if(O(this.set[t],e,this.options))return!0;return!1}}e.exports=n;const s=new(r(42873))({max:1e3}),a=r(48716),i=r(43134),o=r(46830),l=r(89510),{safeRe:h,t:c,comparatorTrimReplace:u,tildeTrimReplace:p,caretTrimReplace:d}=r(19022),{FLAG_INCLUDE_PRERELEASE:m,FLAG_LOOSE:f}=r(50039),g=e=>"<0.0.0-0"===e.value,E=e=>""===e.value,v=(e,t)=>{let r=!0;const n=e.slice();let s=n.pop();for(;r&&n.length;)r=n.every((e=>s.intersects(e,t))),s=n.pop();return r},y=(e,t)=>(o("comp",e,t),e=_(e,t),o("caret",e),e=w(e,t),o("tildes",e),e=N(e,t),o("xrange",e),e=L(e,t),o("stars",e),e),b=e=>!e||"x"===e.toLowerCase()||"*"===e,w=(e,t)=>e.trim().split(/\s+/).map((e=>x(e,t))).join(" "),x=(e,t)=>{const r=t.loose?h[c.TILDELOOSE]:h[c.TILDE];return e.replace(r,((t,r,n,s,a)=>{let i;return o("tilde",e,t,r,n,s,a),b(r)?i="":b(n)?i=`>=${r}.0.0 <${+r+1}.0.0-0`:b(s)?i=`>=${r}.${n}.0 <${r}.${+n+1}.0-0`:a?(o("replaceTilde pr",a),i=`>=${r}.${n}.${s}-${a} <${r}.${+n+1}.0-0`):i=`>=${r}.${n}.${s} <${r}.${+n+1}.0-0`,o("tilde return",i),i}))},_=(e,t)=>e.trim().split(/\s+/).map((e=>R(e,t))).join(" "),R=(e,t)=>{o("caret",e,t);const r=t.loose?h[c.CARETLOOSE]:h[c.CARET],n=t.includePrerelease?"-0":"";return e.replace(r,((t,r,s,a,i)=>{let l;return o("caret",e,t,r,s,a,i),b(r)?l="":b(s)?l=`>=${r}.0.0${n} <${+r+1}.0.0-0`:b(a)?l="0"===r?`>=${r}.${s}.0${n} <${r}.${+s+1}.0-0`:`>=${r}.${s}.0${n} <${+r+1}.0.0-0`:i?(o("replaceCaret pr",i),l="0"===r?"0"===s?`>=${r}.${s}.${a}-${i} <${r}.${s}.${+a+1}-0`:`>=${r}.${s}.${a}-${i} <${r}.${+s+1}.0-0`:`>=${r}.${s}.${a}-${i} <${+r+1}.0.0-0`):(o("no pr"),l="0"===r?"0"===s?`>=${r}.${s}.${a}${n} <${r}.${s}.${+a+1}-0`:`>=${r}.${s}.${a}${n} <${r}.${+s+1}.0-0`:`>=${r}.${s}.${a} <${+r+1}.0.0-0`),o("caret return",l),l}))},N=(e,t)=>(o("replaceXRanges",e,t),e.split(/\s+/).map((e=>I(e,t))).join(" ")),I=(e,t)=>{e=e.trim();const r=t.loose?h[c.XRANGELOOSE]:h[c.XRANGE];return e.replace(r,((r,n,s,a,i,l)=>{o("xRange",e,r,n,s,a,i,l);const h=b(s),c=h||b(a),u=c||b(i),p=u;return"="===n&&p&&(n=""),l=t.includePrerelease?"-0":"",h?r=">"===n||"<"===n?"<0.0.0-0":"*":n&&p?(c&&(a=0),i=0,">"===n?(n=">=",c?(s=+s+1,a=0,i=0):(a=+a+1,i=0)):"<="===n&&(n="<",c?s=+s+1:a=+a+1),"<"===n&&(l="-0"),r=`${n+s}.${a}.${i}${l}`):c?r=`>=${s}.0.0${l} <${+s+1}.0.0-0`:u&&(r=`>=${s}.${a}.0${l} <${s}.${+a+1}.0-0`),o("xRange return",r),r}))},L=(e,t)=>(o("replaceStars",e,t),e.trim().replace(h[c.STAR],"")),$=(e,t)=>(o("replaceGTE0",e,t),e.trim().replace(h[t.includePrerelease?c.GTE0PRE:c.GTE0],"")),A=e=>(t,r,n,s,a,i,o,l,h,c,u,p,d)=>`${r=b(n)?"":b(s)?`>=${n}.0.0${e?"-0":""}`:b(a)?`>=${n}.${s}.0${e?"-0":""}`:i?`>=${r}`:`>=${r}${e?"-0":""}`} ${l=b(h)?"":b(c)?`<${+h+1}.0.0-0`:b(u)?`<${h}.${+c+1}.0-0`:p?`<=${h}.${c}.${u}-${p}`:e?`<${h}.${c}.${+u+1}-0`:`<=${l}`}`.trim(),O=(e,t,r)=>{for(let r=0;r<e.length;r++)if(!e[r].test(t))return!1;if(t.prerelease.length&&!r.includePrerelease){for(let r=0;r<e.length;r++)if(o(e[r].semver),e[r].semver!==i.ANY&&e[r].semver.prerelease.length>0){const n=e[r].semver;if(n.major===t.major&&n.minor===t.minor&&n.patch===t.patch)return!0}return!1}return!0}},89510:(e,t,r)=>{const n=r(46830),{MAX_LENGTH:s,MAX_SAFE_INTEGER:a}=r(50039),{safeRe:i,t:o}=r(19022),l=r(48716),{compareIdentifiers:h}=r(8822);class c{constructor(e,t){if(t=l(t),e instanceof c){if(e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease)return e;e=e.version}else if("string"!=typeof e)throw new TypeError(`Invalid version. Must be a string. Got type "${typeof e}".`);if(e.length>s)throw new TypeError(`version is longer than ${s} characters`);n("SemVer",e,t),this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease;const r=e.trim().match(t.loose?i[o.LOOSE]:i[o.FULL]);if(!r)throw new TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+r[1],this.minor=+r[2],this.patch=+r[3],this.major>a||this.major<0)throw new TypeError("Invalid major version");if(this.minor>a||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>a||this.patch<0)throw new TypeError("Invalid patch version");r[4]?this.prerelease=r[4].split(".").map((e=>{if(/^[0-9]+$/.test(e)){const t=+e;if(t>=0&&t<a)return t}return e})):this.prerelease=[],this.build=r[5]?r[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if(n("SemVer.compare",this.version,this.options,e),!(e instanceof c)){if("string"==typeof e&&e===this.version)return 0;e=new c(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof c||(e=new c(e,this.options)),h(this.major,e.major)||h(this.minor,e.minor)||h(this.patch,e.patch)}comparePre(e){if(e instanceof c||(e=new c(e,this.options)),this.prerelease.length&&!e.prerelease.length)return-1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let t=0;do{const r=this.prerelease[t],s=e.prerelease[t];if(n("prerelease compare",t,r,s),void 0===r&&void 0===s)return 0;if(void 0===s)return 1;if(void 0===r)return-1;if(r!==s)return h(r,s)}while(++t)}compareBuild(e){e instanceof c||(e=new c(e,this.options));let t=0;do{const r=this.build[t],s=e.build[t];if(n("prerelease compare",t,r,s),void 0===r&&void 0===s)return 0;if(void 0===s)return 1;if(void 0===r)return-1;if(r!==s)return h(r,s)}while(++t)}inc(e,t,r){switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",t,r);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",t,r);break;case"prepatch":this.prerelease.length=0,this.inc("patch",t,r),this.inc("pre",t,r);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",t,r),this.inc("pre",t,r);break;case"major":0===this.minor&&0===this.patch&&0!==this.prerelease.length||this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":0===this.patch&&0!==this.prerelease.length||this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":{const e=Number(r)?1:0;if(!t&&!1===r)throw new Error("invalid increment argument: identifier is empty");if(0===this.prerelease.length)this.prerelease=[e];else{let n=this.prerelease.length;for(;--n>=0;)"number"==typeof this.prerelease[n]&&(this.prerelease[n]++,n=-2);if(-1===n){if(t===this.prerelease.join(".")&&!1===r)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(e)}}if(t){let n=[t,e];!1===r&&(n=[t]),0===h(this.prerelease[0],t)?isNaN(this.prerelease[1])&&(this.prerelease=n):this.prerelease=n}break}default:throw new Error(`invalid increment argument: ${e}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}}e.exports=c},76457:(e,t,r)=>{const n=r(95692);e.exports=(e,t)=>{const r=n(e.trim().replace(/^[=v]+/,""),t);return r?r.version:null}},35452:(e,t,r)=>{const n=r(98565),s=r(23328),a=r(82260),i=r(86579),o=r(20290),l=r(5891);e.exports=(e,t,r,h)=>{switch(t){case"===":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e===r;case"!==":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e!==r;case"":case"=":case"==":return n(e,r,h);case"!=":return s(e,r,h);case">":return a(e,r,h);case">=":return i(e,r,h);case"<":return o(e,r,h);case"<=":return l(e,r,h);default:throw new TypeError(`Invalid operator: ${t}`)}}},99469:(e,t,r)=>{const n=r(89510),s=r(95692),{safeRe:a,t:i}=r(19022);e.exports=(e,t)=>{if(e instanceof n)return e;if("number"==typeof e&&(e=String(e)),"string"!=typeof e)return null;let r=null;if((t=t||{}).rtl){let t;for(;(t=a[i.COERCERTL].exec(e))&&(!r||r.index+r[0].length!==e.length);)r&&t.index+t[0].length===r.index+r[0].length||(r=t),a[i.COERCERTL].lastIndex=t.index+t[1].length+t[2].length;a[i.COERCERTL].lastIndex=-1}else r=e.match(a[i.COERCE]);return null===r?null:s(`${r[2]}.${r[3]||"0"}.${r[4]||"0"}`,t)}},51868:(e,t,r)=>{const n=r(89510);e.exports=(e,t,r)=>{const s=new n(e,r),a=new n(t,r);return s.compare(a)||s.compareBuild(a)}},5919:(e,t,r)=>{const n=r(43992);e.exports=(e,t)=>n(e,t,!0)},43992:(e,t,r)=>{const n=r(89510);e.exports=(e,t,r)=>new n(e,r).compare(new n(t,r))},41007:(e,t,r)=>{const n=r(95692);e.exports=(e,t)=>{const r=n(e,null,!0),s=n(t,null,!0),a=r.compare(s);if(0===a)return null;const i=a>0,o=i?r:s,l=i?s:r,h=!!o.prerelease.length;if(l.prerelease.length&&!h)return l.patch||l.minor?o.patch?"patch":o.minor?"minor":"major":"major";const c=h?"pre":"";return r.major!==s.major?c+"major":r.minor!==s.minor?c+"minor":r.patch!==s.patch?c+"patch":"prerelease"}},98565:(e,t,r)=>{const n=r(43992);e.exports=(e,t,r)=>0===n(e,t,r)},82260:(e,t,r)=>{const n=r(43992);e.exports=(e,t,r)=>n(e,t,r)>0},86579:(e,t,r)=>{const n=r(43992);e.exports=(e,t,r)=>n(e,t,r)>=0},30515:(e,t,r)=>{const n=r(89510);e.exports=(e,t,r,s,a)=>{"string"==typeof r&&(a=s,s=r,r=void 0);try{return new n(e instanceof n?e.version:e,r).inc(t,s,a).version}catch(e){return null}}},20290:(e,t,r)=>{const n=r(43992);e.exports=(e,t,r)=>n(e,t,r)<0},5891:(e,t,r)=>{const n=r(43992);e.exports=(e,t,r)=>n(e,t,r)<=0},62611:(e,t,r)=>{const n=r(89510);e.exports=(e,t)=>new n(e,t).major},76319:(e,t,r)=>{const n=r(89510);e.exports=(e,t)=>new n(e,t).minor},23328:(e,t,r)=>{const n=r(43992);e.exports=(e,t,r)=>0!==n(e,t,r)},95692:(e,t,r)=>{const n=r(89510);e.exports=(e,t,r=!1)=>{if(e instanceof n)return e;try{return new n(e,t)}catch(e){if(!r)return null;throw e}}},7368:(e,t,r)=>{const n=r(89510);e.exports=(e,t)=>new n(e,t).patch},97794:(e,t,r)=>{const n=r(95692);e.exports=(e,t)=>{const r=n(e,t);return r&&r.prerelease.length?r.prerelease:null}},19114:(e,t,r)=>{const n=r(43992);e.exports=(e,t,r)=>n(t,e,r)},3843:(e,t,r)=>{const n=r(51868);e.exports=(e,t)=>e.sort(((e,r)=>n(r,e,t)))},99845:(e,t,r)=>{const n=r(87374);e.exports=(e,t,r)=>{try{t=new n(t,r)}catch(e){return!1}return t.test(e)}},58753:(e,t,r)=>{const n=r(51868);e.exports=(e,t)=>e.sort(((e,r)=>n(e,r,t)))},30398:(e,t,r)=>{const n=r(95692);e.exports=(e,t)=>{const r=n(e,t);return r?r.version:null}},38873:(e,t,r)=>{const n=r(19022),s=r(50039),a=r(89510),i=r(8822),o=r(95692),l=r(30398),h=r(76457),c=r(30515),u=r(41007),p=r(62611),d=r(76319),m=r(7368),f=r(97794),g=r(43992),E=r(19114),v=r(5919),y=r(51868),b=r(58753),w=r(3843),x=r(82260),_=r(20290),R=r(98565),N=r(23328),I=r(86579),L=r(5891),$=r(35452),A=r(99469),O=r(43134),P=r(87374),C=r(99845),S=r(98384),T=r(86369),k=r(2663),D=r(20075),j=r(19178),F=r(19434),G=r(48237),M=r(89860),U=r(88258),B=r(53607),X=r(42199);e.exports={parse:o,valid:l,clean:h,inc:c,diff:u,major:p,minor:d,patch:m,prerelease:f,compare:g,rcompare:E,compareLoose:v,compareBuild:y,sort:b,rsort:w,gt:x,lt:_,eq:R,neq:N,gte:I,lte:L,cmp:$,coerce:A,Comparator:O,Range:P,satisfies:C,toComparators:S,maxSatisfying:T,minSatisfying:k,minVersion:D,validRange:j,outside:F,gtr:G,ltr:M,intersects:U,simplifyRange:B,subset:X,SemVer:a,re:n.re,src:n.src,tokens:n.t,SEMVER_SPEC_VERSION:s.SEMVER_SPEC_VERSION,RELEASE_TYPES:s.RELEASE_TYPES,compareIdentifiers:i.compareIdentifiers,rcompareIdentifiers:i.rcompareIdentifiers}},50039:e=>{const t=Number.MAX_SAFE_INTEGER||9007199254740991;e.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:16,MAX_SAFE_BUILD_LENGTH:250,MAX_SAFE_INTEGER:t,RELEASE_TYPES:["major","premajor","minor","preminor","patch","prepatch","prerelease"],SEMVER_SPEC_VERSION:"2.0.0",FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}},46830:(e,t,r)=>{var n=r(27061);const s="object"==typeof n&&n.env&&n.env.NODE_DEBUG&&/\bsemver\b/i.test(n.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};e.exports=s},8822:e=>{const t=/^[0-9]+$/,r=(e,r)=>{const n=t.test(e),s=t.test(r);return n&&s&&(e=+e,r=+r),e===r?0:n&&!s?-1:s&&!n?1:e<r?-1:1};e.exports={compareIdentifiers:r,rcompareIdentifiers:(e,t)=>r(t,e)}},48716:e=>{const t=Object.freeze({loose:!0}),r=Object.freeze({});e.exports=e=>e?"object"!=typeof e?t:e:r},19022:(e,t,r)=>{const{MAX_SAFE_COMPONENT_LENGTH:n,MAX_SAFE_BUILD_LENGTH:s,MAX_LENGTH:a}=r(50039),i=r(46830),o=(t=e.exports={}).re=[],l=t.safeRe=[],h=t.src=[],c=t.t={};let u=0;const p="[a-zA-Z0-9-]",d=[["\\s",1],["\\d",a],[p,s]],m=(e,t,r)=>{const n=(e=>{for(const[t,r]of d)e=e.split(`${t}*`).join(`${t}{0,${r}}`).split(`${t}+`).join(`${t}{1,${r}}`);return e})(t),s=u++;i(e,s,t),c[e]=s,h[s]=t,o[s]=new RegExp(t,r?"g":void 0),l[s]=new RegExp(n,r?"g":void 0)};m("NUMERICIDENTIFIER","0|[1-9]\\d*"),m("NUMERICIDENTIFIERLOOSE","\\d+"),m("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${p}*`),m("MAINVERSION",`(${h[c.NUMERICIDENTIFIER]})\\.(${h[c.NUMERICIDENTIFIER]})\\.(${h[c.NUMERICIDENTIFIER]})`),m("MAINVERSIONLOOSE",`(${h[c.NUMERICIDENTIFIERLOOSE]})\\.(${h[c.NUMERICIDENTIFIERLOOSE]})\\.(${h[c.NUMERICIDENTIFIERLOOSE]})`),m("PRERELEASEIDENTIFIER",`(?:${h[c.NUMERICIDENTIFIER]}|${h[c.NONNUMERICIDENTIFIER]})`),m("PRERELEASEIDENTIFIERLOOSE",`(?:${h[c.NUMERICIDENTIFIERLOOSE]}|${h[c.NONNUMERICIDENTIFIER]})`),m("PRERELEASE",`(?:-(${h[c.PRERELEASEIDENTIFIER]}(?:\\.${h[c.PRERELEASEIDENTIFIER]})*))`),m("PRERELEASELOOSE",`(?:-?(${h[c.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${h[c.PRERELEASEIDENTIFIERLOOSE]})*))`),m("BUILDIDENTIFIER",`${p}+`),m("BUILD",`(?:\\+(${h[c.BUILDIDENTIFIER]}(?:\\.${h[c.BUILDIDENTIFIER]})*))`),m("FULLPLAIN",`v?${h[c.MAINVERSION]}${h[c.PRERELEASE]}?${h[c.BUILD]}?`),m("FULL",`^${h[c.FULLPLAIN]}$`),m("LOOSEPLAIN",`[v=\\s]*${h[c.MAINVERSIONLOOSE]}${h[c.PRERELEASELOOSE]}?${h[c.BUILD]}?`),m("LOOSE",`^${h[c.LOOSEPLAIN]}$`),m("GTLT","((?:<|>)?=?)"),m("XRANGEIDENTIFIERLOOSE",`${h[c.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),m("XRANGEIDENTIFIER",`${h[c.NUMERICIDENTIFIER]}|x|X|\\*`),m("XRANGEPLAIN",`[v=\\s]*(${h[c.XRANGEIDENTIFIER]})(?:\\.(${h[c.XRANGEIDENTIFIER]})(?:\\.(${h[c.XRANGEIDENTIFIER]})(?:${h[c.PRERELEASE]})?${h[c.BUILD]}?)?)?`),m("XRANGEPLAINLOOSE",`[v=\\s]*(${h[c.XRANGEIDENTIFIERLOOSE]})(?:\\.(${h[c.XRANGEIDENTIFIERLOOSE]})(?:\\.(${h[c.XRANGEIDENTIFIERLOOSE]})(?:${h[c.PRERELEASELOOSE]})?${h[c.BUILD]}?)?)?`),m("XRANGE",`^${h[c.GTLT]}\\s*${h[c.XRANGEPLAIN]}$`),m("XRANGELOOSE",`^${h[c.GTLT]}\\s*${h[c.XRANGEPLAINLOOSE]}$`),m("COERCE",`(^|[^\\d])(\\d{1,${n}})(?:\\.(\\d{1,${n}}))?(?:\\.(\\d{1,${n}}))?(?:$|[^\\d])`),m("COERCERTL",h[c.COERCE],!0),m("LONETILDE","(?:~>?)"),m("TILDETRIM",`(\\s*)${h[c.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",m("TILDE",`^${h[c.LONETILDE]}${h[c.XRANGEPLAIN]}$`),m("TILDELOOSE",`^${h[c.LONETILDE]}${h[c.XRANGEPLAINLOOSE]}$`),m("LONECARET","(?:\\^)"),m("CARETTRIM",`(\\s*)${h[c.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",m("CARET",`^${h[c.LONECARET]}${h[c.XRANGEPLAIN]}$`),m("CARETLOOSE",`^${h[c.LONECARET]}${h[c.XRANGEPLAINLOOSE]}$`),m("COMPARATORLOOSE",`^${h[c.GTLT]}\\s*(${h[c.LOOSEPLAIN]})$|^$`),m("COMPARATOR",`^${h[c.GTLT]}\\s*(${h[c.FULLPLAIN]})$|^$`),m("COMPARATORTRIM",`(\\s*)${h[c.GTLT]}\\s*(${h[c.LOOSEPLAIN]}|${h[c.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",m("HYPHENRANGE",`^\\s*(${h[c.XRANGEPLAIN]})\\s+-\\s+(${h[c.XRANGEPLAIN]})\\s*$`),m("HYPHENRANGELOOSE",`^\\s*(${h[c.XRANGEPLAINLOOSE]})\\s+-\\s+(${h[c.XRANGEPLAINLOOSE]})\\s*$`),m("STAR","(<|>)?=?\\s*\\*"),m("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),m("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")},42873:(e,t,r)=>{"use strict";const n=r(7372),s=Symbol("max"),a=Symbol("length"),i=Symbol("lengthCalculator"),o=Symbol("allowStale"),l=Symbol("maxAge"),h=Symbol("dispose"),c=Symbol("noDisposeOnSet"),u=Symbol("lruList"),p=Symbol("cache"),d=Symbol("updateAgeOnGet"),m=()=>1,f=(e,t,r)=>{const n=e[p].get(t);if(n){const t=n.value;if(g(e,t)){if(v(e,n),!e[o])return}else r&&(e[d]&&(n.value.now=Date.now()),e[u].unshiftNode(n));return t.value}},g=(e,t)=>{if(!t||!t.maxAge&&!e[l])return!1;const r=Date.now()-t.now;return t.maxAge?r>t.maxAge:e[l]&&r>e[l]},E=e=>{if(e[a]>e[s])for(let t=e[u].tail;e[a]>e[s]&&null!==t;){const r=t.prev;v(e,t),t=r}},v=(e,t)=>{if(t){const r=t.value;e[h]&&e[h](r.key,r.value),e[a]-=r.length,e[p].delete(r.key),e[u].removeNode(t)}};class y{constructor(e,t,r,n,s){this.key=e,this.value=t,this.length=r,this.now=n,this.maxAge=s||0}}const b=(e,t,r,n)=>{let s=r.value;g(e,s)&&(v(e,r),e[o]||(s=void 0)),s&&t.call(n,s.value,s.key,e)};e.exports=class{constructor(e){if("number"==typeof e&&(e={max:e}),e||(e={}),e.max&&("number"!=typeof e.max||e.max<0))throw new TypeError("max must be a non-negative number");this[s]=e.max||1/0;const t=e.length||m;if(this[i]="function"!=typeof t?m:t,this[o]=e.stale||!1,e.maxAge&&"number"!=typeof e.maxAge)throw new TypeError("maxAge must be a number");this[l]=e.maxAge||0,this[h]=e.dispose,this[c]=e.noDisposeOnSet||!1,this[d]=e.updateAgeOnGet||!1,this.reset()}set max(e){if("number"!=typeof e||e<0)throw new TypeError("max must be a non-negative number");this[s]=e||1/0,E(this)}get max(){return this[s]}set allowStale(e){this[o]=!!e}get allowStale(){return this[o]}set maxAge(e){if("number"!=typeof e)throw new TypeError("maxAge must be a non-negative number");this[l]=e,E(this)}get maxAge(){return this[l]}set lengthCalculator(e){"function"!=typeof e&&(e=m),e!==this[i]&&(this[i]=e,this[a]=0,this[u].forEach((e=>{e.length=this[i](e.value,e.key),this[a]+=e.length}))),E(this)}get lengthCalculator(){return this[i]}get length(){return this[a]}get itemCount(){return this[u].length}rforEach(e,t){t=t||this;for(let r=this[u].tail;null!==r;){const n=r.prev;b(this,e,r,t),r=n}}forEach(e,t){t=t||this;for(let r=this[u].head;null!==r;){const n=r.next;b(this,e,r,t),r=n}}keys(){return this[u].toArray().map((e=>e.key))}values(){return this[u].toArray().map((e=>e.value))}reset(){this[h]&&this[u]&&this[u].length&&this[u].forEach((e=>this[h](e.key,e.value))),this[p]=new Map,this[u]=new n,this[a]=0}dump(){return this[u].map((e=>!g(this,e)&&{k:e.key,v:e.value,e:e.now+(e.maxAge||0)})).toArray().filter((e=>e))}dumpLru(){return this[u]}set(e,t,r){if((r=r||this[l])&&"number"!=typeof r)throw new TypeError("maxAge must be a number");const n=r?Date.now():0,o=this[i](t,e);if(this[p].has(e)){if(o>this[s])return v(this,this[p].get(e)),!1;const i=this[p].get(e).value;return this[h]&&(this[c]||this[h](e,i.value)),i.now=n,i.maxAge=r,i.value=t,this[a]+=o-i.length,i.length=o,this.get(e),E(this),!0}const d=new y(e,t,o,n,r);return d.length>this[s]?(this[h]&&this[h](e,t),!1):(this[a]+=d.length,this[u].unshift(d),this[p].set(e,this[u].head),E(this),!0)}has(e){if(!this[p].has(e))return!1;const t=this[p].get(e).value;return!g(this,t)}get(e){return f(this,e,!0)}peek(e){return f(this,e,!1)}pop(){const e=this[u].tail;return e?(v(this,e),e.value):null}del(e){v(this,this[p].get(e))}load(e){this.reset();const t=Date.now();for(let r=e.length-1;r>=0;r--){const n=e[r],s=n.e||0;if(0===s)this.set(n.k,n.v);else{const e=s-t;e>0&&this.set(n.k,n.v,e)}}}prune(){this[p].forEach(((e,t)=>f(this,t,!1)))}}},48237:(e,t,r)=>{const n=r(19434);e.exports=(e,t,r)=>n(e,t,">",r)},88258:(e,t,r)=>{const n=r(87374);e.exports=(e,t,r)=>(e=new n(e,r),t=new n(t,r),e.intersects(t,r))},89860:(e,t,r)=>{const n=r(19434);e.exports=(e,t,r)=>n(e,t,"<",r)},86369:(e,t,r)=>{const n=r(89510),s=r(87374);e.exports=(e,t,r)=>{let a=null,i=null,o=null;try{o=new s(t,r)}catch(e){return null}return e.forEach((e=>{o.test(e)&&(a&&-1!==i.compare(e)||(a=e,i=new n(a,r)))})),a}},2663:(e,t,r)=>{const n=r(89510),s=r(87374);e.exports=(e,t,r)=>{let a=null,i=null,o=null;try{o=new s(t,r)}catch(e){return null}return e.forEach((e=>{o.test(e)&&(a&&1!==i.compare(e)||(a=e,i=new n(a,r)))})),a}},20075:(e,t,r)=>{const n=r(89510),s=r(87374),a=r(82260);e.exports=(e,t)=>{e=new s(e,t);let r=new n("0.0.0");if(e.test(r))return r;if(r=new n("0.0.0-0"),e.test(r))return r;r=null;for(let t=0;t<e.set.length;++t){const s=e.set[t];let i=null;s.forEach((e=>{const t=new n(e.semver.version);switch(e.operator){case">":0===t.prerelease.length?t.patch++:t.prerelease.push(0),t.raw=t.format();case"":case">=":i&&!a(t,i)||(i=t);break;case"<":case"<=":break;default:throw new Error(`Unexpected operation: ${e.operator}`)}})),!i||r&&!a(r,i)||(r=i)}return r&&e.test(r)?r:null}},19434:(e,t,r)=>{const n=r(89510),s=r(43134),{ANY:a}=s,i=r(87374),o=r(99845),l=r(82260),h=r(20290),c=r(5891),u=r(86579);e.exports=(e,t,r,p)=>{let d,m,f,g,E;switch(e=new n(e,p),t=new i(t,p),r){case">":d=l,m=c,f=h,g=">",E=">=";break;case"<":d=h,m=u,f=l,g="<",E="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(o(e,t,p))return!1;for(let r=0;r<t.set.length;++r){const n=t.set[r];let i=null,o=null;if(n.forEach((e=>{e.semver===a&&(e=new s(">=0.0.0")),i=i||e,o=o||e,d(e.semver,i.semver,p)?i=e:f(e.semver,o.semver,p)&&(o=e)})),i.operator===g||i.operator===E)return!1;if((!o.operator||o.operator===g)&&m(e,o.semver))return!1;if(o.operator===E&&f(e,o.semver))return!1}return!0}},53607:(e,t,r)=>{const n=r(99845),s=r(43992);e.exports=(e,t,r)=>{const a=[];let i=null,o=null;const l=e.sort(((e,t)=>s(e,t,r)));for(const e of l)n(e,t,r)?(o=e,i||(i=e)):(o&&a.push([i,o]),o=null,i=null);i&&a.push([i,null]);const h=[];for(const[e,t]of a)e===t?h.push(e):t||e!==l[0]?t?e===l[0]?h.push(`<=${t}`):h.push(`${e} - ${t}`):h.push(`>=${e}`):h.push("*");const c=h.join(" || "),u="string"==typeof t.raw?t.raw:String(t);return c.length<u.length?c:t}},42199:(e,t,r)=>{const n=r(87374),s=r(43134),{ANY:a}=s,i=r(99845),o=r(43992),l=[new s(">=0.0.0-0")],h=[new s(">=0.0.0")],c=(e,t,r)=>{if(e===t)return!0;if(1===e.length&&e[0].semver===a){if(1===t.length&&t[0].semver===a)return!0;e=r.includePrerelease?l:h}if(1===t.length&&t[0].semver===a){if(r.includePrerelease)return!0;t=h}const n=new Set;let s,c,d,m,f,g,E;for(const t of e)">"===t.operator||">="===t.operator?s=u(s,t,r):"<"===t.operator||"<="===t.operator?c=p(c,t,r):n.add(t.semver);if(n.size>1)return null;if(s&&c){if(d=o(s.semver,c.semver,r),d>0)return null;if(0===d&&(">="!==s.operator||"<="!==c.operator))return null}for(const e of n){if(s&&!i(e,String(s),r))return null;if(c&&!i(e,String(c),r))return null;for(const n of t)if(!i(e,String(n),r))return!1;return!0}let v=!(!c||r.includePrerelease||!c.semver.prerelease.length)&&c.semver,y=!(!s||r.includePrerelease||!s.semver.prerelease.length)&&s.semver;v&&1===v.prerelease.length&&"<"===c.operator&&0===v.prerelease[0]&&(v=!1);for(const e of t){if(E=E||">"===e.operator||">="===e.operator,g=g||"<"===e.operator||"<="===e.operator,s)if(y&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===y.major&&e.semver.minor===y.minor&&e.semver.patch===y.patch&&(y=!1),">"===e.operator||">="===e.operator){if(m=u(s,e,r),m===e&&m!==s)return!1}else if(">="===s.operator&&!i(s.semver,String(e),r))return!1;if(c)if(v&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===v.major&&e.semver.minor===v.minor&&e.semver.patch===v.patch&&(v=!1),"<"===e.operator||"<="===e.operator){if(f=p(c,e,r),f===e&&f!==c)return!1}else if("<="===c.operator&&!i(c.semver,String(e),r))return!1;if(!e.operator&&(c||s)&&0!==d)return!1}return!(s&&g&&!c&&0!==d||c&&E&&!s&&0!==d||y||v)},u=(e,t,r)=>{if(!e)return t;const n=o(e.semver,t.semver,r);return n>0?e:n<0||">"===t.operator&&">="===e.operator?t:e},p=(e,t,r)=>{if(!e)return t;const n=o(e.semver,t.semver,r);return n<0?e:n>0||"<"===t.operator&&"<="===e.operator?t:e};e.exports=(e,t,r={})=>{if(e===t)return!0;e=new n(e,r),t=new n(t,r);let s=!1;e:for(const n of e.set){for(const e of t.set){const t=c(n,e,r);if(s=s||null!==t,t)continue e}if(s)return!1}return!0}},98384:(e,t,r)=>{const n=r(87374);e.exports=(e,t)=>new n(e,t).set.map((e=>e.map((e=>e.value)).join(" ").trim().split(" ")))},19178:(e,t,r)=>{const n=r(87374);e.exports=(e,t)=>{try{return new n(e,t).range||"*"}catch(e){return null}}},99976:e=>{"use strict";e.exports=function(e){e.prototype[Symbol.iterator]=function*(){for(let e=this.head;e;e=e.next)yield e.value}}},7372:(e,t,r)=>{"use strict";function n(e){var t=this;if(t instanceof n||(t=new n),t.tail=null,t.head=null,t.length=0,e&&"function"==typeof e.forEach)e.forEach((function(e){t.push(e)}));else if(arguments.length>0)for(var r=0,s=arguments.length;r<s;r++)t.push(arguments[r]);return t}function s(e,t,r){var n=t===e.head?new o(r,null,t,e):new o(r,t,t.next,e);return null===n.next&&(e.tail=n),null===n.prev&&(e.head=n),e.length++,n}function a(e,t){e.tail=new o(t,e.tail,null,e),e.head||(e.head=e.tail),e.length++}function i(e,t){e.head=new o(t,null,e.head,e),e.tail||(e.tail=e.head),e.length++}function o(e,t,r,n){if(!(this instanceof o))return new o(e,t,r,n);this.list=n,this.value=e,t?(t.next=this,this.prev=t):this.prev=null,r?(r.prev=this,this.next=r):this.next=null}e.exports=n,n.Node=o,n.create=n,n.prototype.removeNode=function(e){if(e.list!==this)throw new Error("removing node which does not belong to this list");var t=e.next,r=e.prev;return t&&(t.prev=r),r&&(r.next=t),e===this.head&&(this.head=t),e===this.tail&&(this.tail=r),e.list.length--,e.next=null,e.prev=null,e.list=null,t},n.prototype.unshiftNode=function(e){if(e!==this.head){e.list&&e.list.removeNode(e);var t=this.head;e.list=this,e.next=t,t&&(t.prev=e),this.head=e,this.tail||(this.tail=e),this.length++}},n.prototype.pushNode=function(e){if(e!==this.tail){e.list&&e.list.removeNode(e);var t=this.tail;e.list=this,e.prev=t,t&&(t.next=e),this.tail=e,this.head||(this.head=e),this.length++}},n.prototype.push=function(){for(var e=0,t=arguments.length;e<t;e++)a(this,arguments[e]);return this.length},n.prototype.unshift=function(){for(var e=0,t=arguments.length;e<t;e++)i(this,arguments[e]);return this.length},n.prototype.pop=function(){if(this.tail){var e=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,e}},n.prototype.shift=function(){if(this.head){var e=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,e}},n.prototype.forEach=function(e,t){t=t||this;for(var r=this.head,n=0;null!==r;n++)e.call(t,r.value,n,this),r=r.next},n.prototype.forEachReverse=function(e,t){t=t||this;for(var r=this.tail,n=this.length-1;null!==r;n--)e.call(t,r.value,n,this),r=r.prev},n.prototype.get=function(e){for(var t=0,r=this.head;null!==r&&t<e;t++)r=r.next;if(t===e&&null!==r)return r.value},n.prototype.getReverse=function(e){for(var t=0,r=this.tail;null!==r&&t<e;t++)r=r.prev;if(t===e&&null!==r)return r.value},n.prototype.map=function(e,t){t=t||this;for(var r=new n,s=this.head;null!==s;)r.push(e.call(t,s.value,this)),s=s.next;return r},n.prototype.mapReverse=function(e,t){t=t||this;for(var r=new n,s=this.tail;null!==s;)r.push(e.call(t,s.value,this)),s=s.prev;return r},n.prototype.reduce=function(e,t){var r,n=this.head;if(arguments.length>1)r=t;else{if(!this.head)throw new TypeError("Reduce of empty list with no initial value");n=this.head.next,r=this.head.value}for(var s=0;null!==n;s++)r=e(r,n.value,s),n=n.next;return r},n.prototype.reduceReverse=function(e,t){var r,n=this.tail;if(arguments.length>1)r=t;else{if(!this.tail)throw new TypeError("Reduce of empty list with no initial value");n=this.tail.prev,r=this.tail.value}for(var s=this.length-1;null!==n;s--)r=e(r,n.value,s),n=n.prev;return r},n.prototype.toArray=function(){for(var e=new Array(this.length),t=0,r=this.head;null!==r;t++)e[t]=r.value,r=r.next;return e},n.prototype.toArrayReverse=function(){for(var e=new Array(this.length),t=0,r=this.tail;null!==r;t++)e[t]=r.value,r=r.prev;return e},n.prototype.slice=function(e,t){(t=t||this.length)<0&&(t+=this.length),(e=e||0)<0&&(e+=this.length);var r=new n;if(t<e||t<0)return r;e<0&&(e=0),t>this.length&&(t=this.length);for(var s=0,a=this.head;null!==a&&s<e;s++)a=a.next;for(;null!==a&&s<t;s++,a=a.next)r.push(a.value);return r},n.prototype.sliceReverse=function(e,t){(t=t||this.length)<0&&(t+=this.length),(e=e||0)<0&&(e+=this.length);var r=new n;if(t<e||t<0)return r;e<0&&(e=0),t>this.length&&(t=this.length);for(var s=this.length,a=this.tail;null!==a&&s>t;s--)a=a.prev;for(;null!==a&&s>e;s--,a=a.prev)r.push(a.value);return r},n.prototype.splice=function(e,t,...r){e>this.length&&(e=this.length-1),e<0&&(e=this.length+e);for(var n=0,a=this.head;null!==a&&n<e;n++)a=a.next;var i=[];for(n=0;a&&n<t;n++)i.push(a.value),a=this.removeNode(a);for(null===a&&(a=this.tail),a!==this.head&&a!==this.tail&&(a=a.prev),n=0;n<r.length;n++)a=s(this,a,r[n]);return i},n.prototype.reverse=function(){for(var e=this.head,t=this.tail,r=e;null!==r;r=r.prev){var n=r.prev;r.prev=r.next,r.next=n}return this.head=t,this.tail=e,this};try{r(99976)(n)}catch(e){}}}]);
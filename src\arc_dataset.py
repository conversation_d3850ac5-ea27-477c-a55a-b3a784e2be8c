import torch
from torch.utils.data import Dataset
import json
import os
from src.tokenizer import GrammarTokenizer # Correction de l'import

class ArcDataset(Dataset):
    def __init__(self, data_folder: str, tokenizer: GrammarTokenizer, max_prog_len: int = 200):
        self.data = []
        self.tokenizer = tokenizer # Utilisation du tokenizer fourni
        self.max_prog_len = max_prog_len
        
        # Parcourir les fichiers dans le dossier
        for filename in os.listdir(data_folder):
            if filename.endswith('.json'):
                puzzle_id = filename.split('.')[0]
                json_path = os.path.join(data_folder, filename)
                program_path = os.path.join(data_folder, f"{puzzle_id}_TEST0_VALID.agi.txt")
                
                if not os.path.exists(program_path):
                    continue
                
                # Charger le puzzle
                with open(json_path, 'r') as f:
                    puzzle_data = json.load(f)
                
                # Charger le programme
                with open(program_path, 'r') as f:
                    program = f.read().strip()
                
                # Prendre le premier exemple d'entraînement
                input_grid = puzzle_data['train'][0]['input']
                
                self.data.append({
                    'input_grid': input_grid,
                    'program': program
                })
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        input_grid = item['input_grid']
        program = item['program']
        
        # Convertir la grille en tensor
        grid_tensor = torch.tensor(input_grid, dtype=torch.long)
        
        # Tokeniser le programme
        prog_tokens = self.tokenizer.tokenize(program)
        prog_tokens = [self.tokenizer.vocab['<sos>']] + prog_tokens + [self.tokenizer.vocab['<eos>']]
        if len(prog_tokens) > self.max_prog_len:
            prog_tokens = prog_tokens[:self.max_prog_len]
        else:
            prog_tokens = prog_tokens + [self.tokenizer.vocab['<pad>']] * (self.max_prog_len - len(prog_tokens))
        
        return grid_tensor, torch.tensor(prog_tokens, dtype=torch.long)
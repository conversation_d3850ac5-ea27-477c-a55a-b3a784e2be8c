"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[2615],{92615:(e,t,a)=>{a.r(t),a.d(t,{DETAILS_CLASS:()=>u,IMermaidManager:()=>h,IMermaidMarkdown:()=>M,MERMAID_CLASS:()=>l,MERMAID_CODE_CLASS:()=>c,MERMAID_DARK_THEME:()=>m,MERMAID_DEFAULT_THEME:()=>d,MERMAID_FILE_EXTENSIONS:()=>s,MERMAID_MIME_TYPE:()=>i,MermaidManager:()=>p,MermaidMarkdown:()=>_,RenderedMermaid:()=>w,SUMMARY_CLASS:()=>g,WARNING_CLASS:()=>o,rendererFactory:()=>T});var r=a(20998),n=a(38639);const i="text/vnd.mermaid",s=[".mmd",".mermaid"],d="default",m="dark",l="jp-RenderedMermaid",c="mermaid",o="jp-mod-warning",u="jp-RenderedMermaid-Details",g="jp-RenderedMermaid-Summary",h=new r.Token("@jupyterlab/mermaid:IMermaidManager","a manager for rendering mermaid text-based diagrams"),M=new r.Token("@jupyterlab/mermaid:IMermaidMarkdown","a manager for rendering mermaid text-based diagrams in markdown fenced code blocks");class p{constructor(e={}){this._diagrams=new n.LruCache({maxSize:e.maxCacheSize||null}),e.themes&&(E.initThemes(e.themes||null),e.themes.themeChanged.connect(this.initialize,this))}static cleanMermaidSvg(e){return e.replace(E.RE_VOID_ELEMENT,E.replaceVoidElement)}initialize(){this._diagrams.clear(),E.initMermaid()}async getMermaid(){return await E.ensureMermaid()}getMermaidVersion(){return E.version()}getCachedFigure(e){return this._diagrams.get(e)}async renderSvg(e){const t=await this.getMermaid(),a=`jp-mermaid-${E.nextMermaidId()}`,r=document.createElement("div");document.body.appendChild(r);try{let{svg:n}=await t.render(a,e,r);n=p.cleanMermaidSvg(n);const i=(new DOMParser).parseFromString(n,"image/svg+xml"),s={text:e,svg:n},d=i.querySelector("svg"),{maxWidth:m}=(null==d?void 0:d.style)||{};s.width=m?parseFloat(m):null;const l=i.querySelector("title"),c=i.querySelector("desc");return l&&(s.accessibleTitle=l.textContent),c&&(s.accessibleDescription=c.textContent),s}finally{r.remove()}}async renderFigure(e){let t=this._diagrams.get(e);if(null!=t)return t;let a=l,r=null;t=document.createElement("div"),t.className=a;try{const t=await this.renderSvg(e);r=this.makeMermaidFigure(t)}catch(a){t.classList.add(o),r=await this.makeMermaidError(e)}let n=this.getMermaidVersion();return n&&(r.dataset.jpMermaidVersion=n),t.appendChild(r),this._diagrams.set(e,t),t}makeMermaidCode(e){const t=document.createElement("pre"),a=document.createElement("code");return a.innerText=e,t.appendChild(a),a.className=c,a.textContent=e,t}async makeMermaidError(e){const t=await this.getMermaid();let a="";try{await t.parse(e)}catch(e){a=`${e}`}const r=document.createElement("details");r.className=u;const n=document.createElement("summary");n.className=g,n.appendChild(this.makeMermaidCode(e)),r.appendChild(n);const i=document.createElement("pre");return i.innerText=a,r.appendChild(i),r}makeMermaidFigure(e){const t=document.createElement("figure"),a=document.createElement("img");if(t.appendChild(a),a.setAttribute("src",`data:image/svg+xml,${encodeURIComponent(e.svg)}`),e.width&&(a.width=e.width),e.accessibleTitle&&a.setAttribute("alt",e.accessibleTitle),t.appendChild(this.makeMermaidCode(e.text)),e.accessibleDescription){const a=document.createElement("figcaption");a.className="sr-only",a.textContent=e.accessibleDescription,t.appendChild(a)}return t}}var E;!function(e){let t=null,n=null,i=null,s=0,l=null;function c(){if(!n)return!1;let e=d;if(t){const a=t.theme;e=a&&t.isLight(a)?d:m}const a=window.getComputedStyle(document.body).getPropertyValue("--jp-ui-font-family");return n.mermaidAPI.globalReset(),n.mermaidAPI.initialize({theme:e,fontFamily:a,securityLevel:"strict",maxTextSize:1e5,maxEdges:1e5,startOnLoad:!1}),!0}e.initThemes=function(e){t=e},e.version=function(){return l},e.initMermaid=c,e.getMermaid=function(){return n},e.nextMermaidId=function(){return s++},e.ensureMermaid=async function(){return null!=n?n:i?i.promise:(i=new r.PromiseDelegate,l=(await a.e(1169).then(a.t.bind(a,21169,19))).version,n=(await Promise.all([a.e(4499),a.e(4630)]).then(a.bind(a,84630))).default,c(),i.resolve(n),n)},e.RE_VOID_ELEMENT=/<\s*(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)\s*([^>]*?)\s*>/gi,e.replaceVoidElement=function(e,t,a){return(a=a.trim()).endsWith("/")||(a=`${a} /`),`<${t} ${a}>`}}(E||(E={}));class _{constructor(e){this.languages=["mermaid"],this.rank=100,this._mermaid=e.mermaid}async walk(e){await this._mermaid.renderFigure(e)}render(e){let t=this._mermaid.getCachedFigure(e);return t?t.outerHTML:null}}var y=a(31516);const C="image/svg+xml";class w extends y.Widget{constructor(e){super(),this._lastRendered=null,this._mimeType=e.mimeType,this.addClass(l)}static set manager(e){w._manager?console.warn("Mermaid manager may only be set once, and is already set."):(w._manager=e,w._managerReady.resolve(e))}async renderModel(e){const t=await w._managerReady.promise,a=e.data[this._mimeType];if(null==a||a===this._lastRendered)return;this._lastRendered=a;const r=await t.renderFigure(a);if(r.classList.contains(o)?this.node.classList.add(o):this.node.classList.remove(o),!r.firstChild)return;this.node.innerHTML!==r.innerHTML&&(this.node.innerHTML=r.innerHTML);const n=t.getMermaidVersion(),s={...e.metadata[i]||{},version:n},d={...e.metadata,[i]:s},m=r.querySelector("img");if(m){const t=decodeURIComponent(m.src.split(",")[1]);t!==e.data[C]&&e.setData({data:{...e.data,[C]:t},metadata:d})}else{const t={...e.data};delete t[C],e.setData({data:t,metadata:d})}}}w._manager=null,w._managerReady=new r.PromiseDelegate;const T={safe:!0,mimeTypes:[i],createRenderer:e=>new w(e)}}}]);
"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[4837,3146],{73146:(e,t,o)=>{o.r(t),o.d(t,{INotebookTree:()=>n,NotebookTreeWidget:()=>s});var r=o(68239),a=o(31516);class s extends a.TabPanel{constructor(){super({tabPlacement:"top",tabsMovable:!0,renderer:r.TabBarSvg.defaultRenderer}),this.addClass("jp-TreePanel")}}const n=new(o(20998).Token)("@jupyter-notebook/tree:INotebookTree")}}]);
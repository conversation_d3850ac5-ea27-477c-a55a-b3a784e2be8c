"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[2241],{32241:(t,e,a)=>{a.d(e,{diagram:()=>ot});var r=a(24028),s=a(23617),i=a(26476),n=a(7608),o=(a(27693),a(31699),function(){var t=function(t,e,a,r){for(a=a||{},r=t.length;r--;a[t[r]]=e);return a},e=[1,2],a=[1,3],r=[1,4],s=[2,4],i=[1,9],n=[1,11],o=[1,13],c=[1,14],l=[1,16],d=[1,17],h=[1,18],p=[1,24],g=[1,25],u=[1,26],x=[1,27],y=[1,28],m=[1,29],b=[1,30],f=[1,31],T=[1,32],E=[1,33],w=[1,34],P=[1,35],_=[1,36],v=[1,37],L=[1,38],k=[1,39],I=[1,41],N=[1,42],M=[1,43],A=[1,44],O=[1,45],S=[1,46],D=[1,4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,48,49,50,52,53,54,59,60,61,62,70],R=[4,5,16,50,52,53],C=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],Y=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,49,50,52,53,54,59,60,61,62,70],$=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,48,50,52,53,54,59,60,61,62,70],B=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,50,52,53,54,59,60,61,62,70],V=[68,69,70],F=[1,120],q={trace:function(){},yy:{},symbols_:{error:2,start:3,SPACE:4,NEWLINE:5,SD:6,document:7,line:8,statement:9,box_section:10,box_line:11,participant_statement:12,create:13,box:14,restOfLine:15,end:16,signal:17,autonumber:18,NUM:19,off:20,activate:21,actor:22,deactivate:23,note_statement:24,links_statement:25,link_statement:26,properties_statement:27,details_statement:28,title:29,legacy_title:30,acc_title:31,acc_title_value:32,acc_descr:33,acc_descr_value:34,acc_descr_multiline_value:35,loop:36,rect:37,opt:38,alt:39,else_sections:40,par:41,par_sections:42,par_over:43,critical:44,option_sections:45,break:46,option:47,and:48,else:49,participant:50,AS:51,participant_actor:52,destroy:53,note:54,placement:55,text2:56,over:57,actor_pair:58,links:59,link:60,properties:61,details:62,spaceList:63,",":64,left_of:65,right_of:66,signaltype:67,"+":68,"-":69,ACTOR:70,SOLID_OPEN_ARROW:71,DOTTED_OPEN_ARROW:72,SOLID_ARROW:73,DOTTED_ARROW:74,SOLID_CROSS:75,DOTTED_CROSS:76,SOLID_POINT:77,DOTTED_POINT:78,TXT:79,$accept:0,$end:1},terminals_:{2:"error",4:"SPACE",5:"NEWLINE",6:"SD",13:"create",14:"box",15:"restOfLine",16:"end",18:"autonumber",19:"NUM",20:"off",21:"activate",23:"deactivate",29:"title",30:"legacy_title",31:"acc_title",32:"acc_title_value",33:"acc_descr",34:"acc_descr_value",35:"acc_descr_multiline_value",36:"loop",37:"rect",38:"opt",39:"alt",41:"par",43:"par_over",44:"critical",46:"break",47:"option",48:"and",49:"else",50:"participant",51:"AS",52:"participant_actor",53:"destroy",54:"note",57:"over",59:"links",60:"link",61:"properties",62:"details",64:",",65:"left_of",66:"right_of",68:"+",69:"-",70:"ACTOR",71:"SOLID_OPEN_ARROW",72:"DOTTED_OPEN_ARROW",73:"SOLID_ARROW",74:"DOTTED_ARROW",75:"SOLID_CROSS",76:"DOTTED_CROSS",77:"SOLID_POINT",78:"DOTTED_POINT",79:"TXT"},productions_:[0,[3,2],[3,2],[3,2],[7,0],[7,2],[8,2],[8,1],[8,1],[10,0],[10,2],[11,2],[11,1],[11,1],[9,1],[9,2],[9,4],[9,2],[9,4],[9,3],[9,3],[9,2],[9,3],[9,3],[9,2],[9,2],[9,2],[9,2],[9,2],[9,1],[9,1],[9,2],[9,2],[9,1],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[45,1],[45,4],[42,1],[42,4],[40,1],[40,4],[12,5],[12,3],[12,5],[12,3],[12,3],[24,4],[24,4],[25,3],[26,3],[27,3],[28,3],[63,2],[63,1],[58,3],[58,1],[55,1],[55,1],[17,5],[17,5],[17,4],[22,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[56,1]],performAction:function(t,e,a,r,s,i,n){var o=i.length-1;switch(s){case 3:return r.apply(i[o]),i[o];case 4:case 9:case 8:case 13:this.$=[];break;case 5:case 10:i[o-1].push(i[o]),this.$=i[o-1];break;case 6:case 7:case 11:case 12:case 62:this.$=i[o];break;case 15:i[o].type="createParticipant",this.$=i[o];break;case 16:i[o-1].unshift({type:"boxStart",boxData:r.parseBoxData(i[o-2])}),i[o-1].push({type:"boxEnd",boxText:i[o-2]}),this.$=i[o-1];break;case 18:this.$={type:"sequenceIndex",sequenceIndex:Number(i[o-2]),sequenceIndexStep:Number(i[o-1]),sequenceVisible:!0,signalType:r.LINETYPE.AUTONUMBER};break;case 19:this.$={type:"sequenceIndex",sequenceIndex:Number(i[o-1]),sequenceIndexStep:1,sequenceVisible:!0,signalType:r.LINETYPE.AUTONUMBER};break;case 20:this.$={type:"sequenceIndex",sequenceVisible:!1,signalType:r.LINETYPE.AUTONUMBER};break;case 21:this.$={type:"sequenceIndex",sequenceVisible:!0,signalType:r.LINETYPE.AUTONUMBER};break;case 22:this.$={type:"activeStart",signalType:r.LINETYPE.ACTIVE_START,actor:i[o-1]};break;case 23:this.$={type:"activeEnd",signalType:r.LINETYPE.ACTIVE_END,actor:i[o-1]};break;case 29:r.setDiagramTitle(i[o].substring(6)),this.$=i[o].substring(6);break;case 30:r.setDiagramTitle(i[o].substring(7)),this.$=i[o].substring(7);break;case 31:this.$=i[o].trim(),r.setAccTitle(this.$);break;case 32:case 33:this.$=i[o].trim(),r.setAccDescription(this.$);break;case 34:i[o-1].unshift({type:"loopStart",loopText:r.parseMessage(i[o-2]),signalType:r.LINETYPE.LOOP_START}),i[o-1].push({type:"loopEnd",loopText:i[o-2],signalType:r.LINETYPE.LOOP_END}),this.$=i[o-1];break;case 35:i[o-1].unshift({type:"rectStart",color:r.parseMessage(i[o-2]),signalType:r.LINETYPE.RECT_START}),i[o-1].push({type:"rectEnd",color:r.parseMessage(i[o-2]),signalType:r.LINETYPE.RECT_END}),this.$=i[o-1];break;case 36:i[o-1].unshift({type:"optStart",optText:r.parseMessage(i[o-2]),signalType:r.LINETYPE.OPT_START}),i[o-1].push({type:"optEnd",optText:r.parseMessage(i[o-2]),signalType:r.LINETYPE.OPT_END}),this.$=i[o-1];break;case 37:i[o-1].unshift({type:"altStart",altText:r.parseMessage(i[o-2]),signalType:r.LINETYPE.ALT_START}),i[o-1].push({type:"altEnd",signalType:r.LINETYPE.ALT_END}),this.$=i[o-1];break;case 38:i[o-1].unshift({type:"parStart",parText:r.parseMessage(i[o-2]),signalType:r.LINETYPE.PAR_START}),i[o-1].push({type:"parEnd",signalType:r.LINETYPE.PAR_END}),this.$=i[o-1];break;case 39:i[o-1].unshift({type:"parStart",parText:r.parseMessage(i[o-2]),signalType:r.LINETYPE.PAR_OVER_START}),i[o-1].push({type:"parEnd",signalType:r.LINETYPE.PAR_END}),this.$=i[o-1];break;case 40:i[o-1].unshift({type:"criticalStart",criticalText:r.parseMessage(i[o-2]),signalType:r.LINETYPE.CRITICAL_START}),i[o-1].push({type:"criticalEnd",signalType:r.LINETYPE.CRITICAL_END}),this.$=i[o-1];break;case 41:i[o-1].unshift({type:"breakStart",breakText:r.parseMessage(i[o-2]),signalType:r.LINETYPE.BREAK_START}),i[o-1].push({type:"breakEnd",optText:r.parseMessage(i[o-2]),signalType:r.LINETYPE.BREAK_END}),this.$=i[o-1];break;case 43:this.$=i[o-3].concat([{type:"option",optionText:r.parseMessage(i[o-1]),signalType:r.LINETYPE.CRITICAL_OPTION},i[o]]);break;case 45:this.$=i[o-3].concat([{type:"and",parText:r.parseMessage(i[o-1]),signalType:r.LINETYPE.PAR_AND},i[o]]);break;case 47:this.$=i[o-3].concat([{type:"else",altText:r.parseMessage(i[o-1]),signalType:r.LINETYPE.ALT_ELSE},i[o]]);break;case 48:i[o-3].draw="participant",i[o-3].type="addParticipant",i[o-3].description=r.parseMessage(i[o-1]),this.$=i[o-3];break;case 49:i[o-1].draw="participant",i[o-1].type="addParticipant",this.$=i[o-1];break;case 50:i[o-3].draw="actor",i[o-3].type="addParticipant",i[o-3].description=r.parseMessage(i[o-1]),this.$=i[o-3];break;case 51:i[o-1].draw="actor",i[o-1].type="addParticipant",this.$=i[o-1];break;case 52:i[o-1].type="destroyParticipant",this.$=i[o-1];break;case 53:this.$=[i[o-1],{type:"addNote",placement:i[o-2],actor:i[o-1].actor,text:i[o]}];break;case 54:i[o-2]=[].concat(i[o-1],i[o-1]).slice(0,2),i[o-2][0]=i[o-2][0].actor,i[o-2][1]=i[o-2][1].actor,this.$=[i[o-1],{type:"addNote",placement:r.PLACEMENT.OVER,actor:i[o-2].slice(0,2),text:i[o]}];break;case 55:this.$=[i[o-1],{type:"addLinks",actor:i[o-1].actor,text:i[o]}];break;case 56:this.$=[i[o-1],{type:"addALink",actor:i[o-1].actor,text:i[o]}];break;case 57:this.$=[i[o-1],{type:"addProperties",actor:i[o-1].actor,text:i[o]}];break;case 58:this.$=[i[o-1],{type:"addDetails",actor:i[o-1].actor,text:i[o]}];break;case 61:this.$=[i[o-2],i[o]];break;case 63:this.$=r.PLACEMENT.LEFTOF;break;case 64:this.$=r.PLACEMENT.RIGHTOF;break;case 65:this.$=[i[o-4],i[o-1],{type:"addMessage",from:i[o-4].actor,to:i[o-1].actor,signalType:i[o-3],msg:i[o],activate:!0},{type:"activeStart",signalType:r.LINETYPE.ACTIVE_START,actor:i[o-1]}];break;case 66:this.$=[i[o-4],i[o-1],{type:"addMessage",from:i[o-4].actor,to:i[o-1].actor,signalType:i[o-3],msg:i[o]},{type:"activeEnd",signalType:r.LINETYPE.ACTIVE_END,actor:i[o-4]}];break;case 67:this.$=[i[o-3],i[o-1],{type:"addMessage",from:i[o-3].actor,to:i[o-1].actor,signalType:i[o-2],msg:i[o]}];break;case 68:this.$={type:"addParticipant",actor:i[o]};break;case 69:this.$=r.LINETYPE.SOLID_OPEN;break;case 70:this.$=r.LINETYPE.DOTTED_OPEN;break;case 71:this.$=r.LINETYPE.SOLID;break;case 72:this.$=r.LINETYPE.DOTTED;break;case 73:this.$=r.LINETYPE.SOLID_CROSS;break;case 74:this.$=r.LINETYPE.DOTTED_CROSS;break;case 75:this.$=r.LINETYPE.SOLID_POINT;break;case 76:this.$=r.LINETYPE.DOTTED_POINT;break;case 77:this.$=r.parseMessage(i[o].trim().substring(1))}},table:[{3:1,4:e,5:a,6:r},{1:[3]},{3:5,4:e,5:a,6:r},{3:6,4:e,5:a,6:r},t([1,4,5,13,14,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],s,{7:7}),{1:[2,1]},{1:[2,2]},{1:[2,3],4:i,5:n,8:8,9:10,12:12,13:o,14:c,17:15,18:l,21:d,22:40,23:h,24:19,25:20,26:21,27:22,28:23,29:p,30:g,31:u,33:x,35:y,36:m,37:b,38:f,39:T,41:E,43:w,44:P,46:_,50:v,52:L,53:k,54:I,59:N,60:M,61:A,62:O,70:S},t(D,[2,5]),{9:47,12:12,13:o,14:c,17:15,18:l,21:d,22:40,23:h,24:19,25:20,26:21,27:22,28:23,29:p,30:g,31:u,33:x,35:y,36:m,37:b,38:f,39:T,41:E,43:w,44:P,46:_,50:v,52:L,53:k,54:I,59:N,60:M,61:A,62:O,70:S},t(D,[2,7]),t(D,[2,8]),t(D,[2,14]),{12:48,50:v,52:L,53:k},{15:[1,49]},{5:[1,50]},{5:[1,53],19:[1,51],20:[1,52]},{22:54,70:S},{22:55,70:S},{5:[1,56]},{5:[1,57]},{5:[1,58]},{5:[1,59]},{5:[1,60]},t(D,[2,29]),t(D,[2,30]),{32:[1,61]},{34:[1,62]},t(D,[2,33]),{15:[1,63]},{15:[1,64]},{15:[1,65]},{15:[1,66]},{15:[1,67]},{15:[1,68]},{15:[1,69]},{15:[1,70]},{22:71,70:S},{22:72,70:S},{22:73,70:S},{67:74,71:[1,75],72:[1,76],73:[1,77],74:[1,78],75:[1,79],76:[1,80],77:[1,81],78:[1,82]},{55:83,57:[1,84],65:[1,85],66:[1,86]},{22:87,70:S},{22:88,70:S},{22:89,70:S},{22:90,70:S},t([5,51,64,71,72,73,74,75,76,77,78,79],[2,68]),t(D,[2,6]),t(D,[2,15]),t(R,[2,9],{10:91}),t(D,[2,17]),{5:[1,93],19:[1,92]},{5:[1,94]},t(D,[2,21]),{5:[1,95]},{5:[1,96]},t(D,[2,24]),t(D,[2,25]),t(D,[2,26]),t(D,[2,27]),t(D,[2,28]),t(D,[2,31]),t(D,[2,32]),t(C,s,{7:97}),t(C,s,{7:98}),t(C,s,{7:99}),t(Y,s,{40:100,7:101}),t($,s,{42:102,7:103}),t($,s,{7:103,42:104}),t(B,s,{45:105,7:106}),t(C,s,{7:107}),{5:[1,109],51:[1,108]},{5:[1,111],51:[1,110]},{5:[1,112]},{22:115,68:[1,113],69:[1,114],70:S},t(V,[2,69]),t(V,[2,70]),t(V,[2,71]),t(V,[2,72]),t(V,[2,73]),t(V,[2,74]),t(V,[2,75]),t(V,[2,76]),{22:116,70:S},{22:118,58:117,70:S},{70:[2,63]},{70:[2,64]},{56:119,79:F},{56:121,79:F},{56:122,79:F},{56:123,79:F},{4:[1,126],5:[1,128],11:125,12:127,16:[1,124],50:v,52:L,53:k},{5:[1,129]},t(D,[2,19]),t(D,[2,20]),t(D,[2,22]),t(D,[2,23]),{4:i,5:n,8:8,9:10,12:12,13:o,14:c,16:[1,130],17:15,18:l,21:d,22:40,23:h,24:19,25:20,26:21,27:22,28:23,29:p,30:g,31:u,33:x,35:y,36:m,37:b,38:f,39:T,41:E,43:w,44:P,46:_,50:v,52:L,53:k,54:I,59:N,60:M,61:A,62:O,70:S},{4:i,5:n,8:8,9:10,12:12,13:o,14:c,16:[1,131],17:15,18:l,21:d,22:40,23:h,24:19,25:20,26:21,27:22,28:23,29:p,30:g,31:u,33:x,35:y,36:m,37:b,38:f,39:T,41:E,43:w,44:P,46:_,50:v,52:L,53:k,54:I,59:N,60:M,61:A,62:O,70:S},{4:i,5:n,8:8,9:10,12:12,13:o,14:c,16:[1,132],17:15,18:l,21:d,22:40,23:h,24:19,25:20,26:21,27:22,28:23,29:p,30:g,31:u,33:x,35:y,36:m,37:b,38:f,39:T,41:E,43:w,44:P,46:_,50:v,52:L,53:k,54:I,59:N,60:M,61:A,62:O,70:S},{16:[1,133]},{4:i,5:n,8:8,9:10,12:12,13:o,14:c,16:[2,46],17:15,18:l,21:d,22:40,23:h,24:19,25:20,26:21,27:22,28:23,29:p,30:g,31:u,33:x,35:y,36:m,37:b,38:f,39:T,41:E,43:w,44:P,46:_,49:[1,134],50:v,52:L,53:k,54:I,59:N,60:M,61:A,62:O,70:S},{16:[1,135]},{4:i,5:n,8:8,9:10,12:12,13:o,14:c,16:[2,44],17:15,18:l,21:d,22:40,23:h,24:19,25:20,26:21,27:22,28:23,29:p,30:g,31:u,33:x,35:y,36:m,37:b,38:f,39:T,41:E,43:w,44:P,46:_,48:[1,136],50:v,52:L,53:k,54:I,59:N,60:M,61:A,62:O,70:S},{16:[1,137]},{16:[1,138]},{4:i,5:n,8:8,9:10,12:12,13:o,14:c,16:[2,42],17:15,18:l,21:d,22:40,23:h,24:19,25:20,26:21,27:22,28:23,29:p,30:g,31:u,33:x,35:y,36:m,37:b,38:f,39:T,41:E,43:w,44:P,46:_,47:[1,139],50:v,52:L,53:k,54:I,59:N,60:M,61:A,62:O,70:S},{4:i,5:n,8:8,9:10,12:12,13:o,14:c,16:[1,140],17:15,18:l,21:d,22:40,23:h,24:19,25:20,26:21,27:22,28:23,29:p,30:g,31:u,33:x,35:y,36:m,37:b,38:f,39:T,41:E,43:w,44:P,46:_,50:v,52:L,53:k,54:I,59:N,60:M,61:A,62:O,70:S},{15:[1,141]},t(D,[2,49]),{15:[1,142]},t(D,[2,51]),t(D,[2,52]),{22:143,70:S},{22:144,70:S},{56:145,79:F},{56:146,79:F},{56:147,79:F},{64:[1,148],79:[2,62]},{5:[2,55]},{5:[2,77]},{5:[2,56]},{5:[2,57]},{5:[2,58]},t(D,[2,16]),t(R,[2,10]),{12:149,50:v,52:L,53:k},t(R,[2,12]),t(R,[2,13]),t(D,[2,18]),t(D,[2,34]),t(D,[2,35]),t(D,[2,36]),t(D,[2,37]),{15:[1,150]},t(D,[2,38]),{15:[1,151]},t(D,[2,39]),t(D,[2,40]),{15:[1,152]},t(D,[2,41]),{5:[1,153]},{5:[1,154]},{56:155,79:F},{56:156,79:F},{5:[2,67]},{5:[2,53]},{5:[2,54]},{22:157,70:S},t(R,[2,11]),t(Y,s,{7:101,40:158}),t($,s,{7:103,42:159}),t(B,s,{7:106,45:160}),t(D,[2,48]),t(D,[2,50]),{5:[2,65]},{5:[2,66]},{79:[2,61]},{16:[2,47]},{16:[2,45]},{16:[2,43]}],defaultActions:{5:[2,1],6:[2,2],85:[2,63],86:[2,64],119:[2,55],120:[2,77],121:[2,56],122:[2,57],123:[2,58],145:[2,67],146:[2,53],147:[2,54],155:[2,65],156:[2,66],157:[2,61],158:[2,47],159:[2,45],160:[2,43]},parseError:function(t,e){if(!e.recoverable){var a=new Error(t);throw a.hash=e,a}this.trace(t)},parse:function(t){var e=[0],a=[],r=[null],s=[],i=this.table,n="",o=0,c=0,l=s.slice.call(arguments,1),d=Object.create(this.lexer),h={yy:{}};for(var p in this.yy)Object.prototype.hasOwnProperty.call(this.yy,p)&&(h.yy[p]=this.yy[p]);d.setInput(t,h.yy),h.yy.lexer=d,h.yy.parser=this,void 0===d.yylloc&&(d.yylloc={});var g=d.yylloc;s.push(g);var u=d.options&&d.options.ranges;"function"==typeof h.yy.parseError?this.parseError=h.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;for(var x,y,m,b,f,T,E,w,P,_={};;){if(y=e[e.length-1],this.defaultActions[y]?m=this.defaultActions[y]:(null==x&&(P=void 0,"number"!=typeof(P=a.pop()||d.lex()||1)&&(P instanceof Array&&(P=(a=P).pop()),P=this.symbols_[P]||P),x=P),m=i[y]&&i[y][x]),void 0===m||!m.length||!m[0]){var v;for(f in w=[],i[y])this.terminals_[f]&&f>2&&w.push("'"+this.terminals_[f]+"'");v=d.showPosition?"Parse error on line "+(o+1)+":\n"+d.showPosition()+"\nExpecting "+w.join(", ")+", got '"+(this.terminals_[x]||x)+"'":"Parse error on line "+(o+1)+": Unexpected "+(1==x?"end of input":"'"+(this.terminals_[x]||x)+"'"),this.parseError(v,{text:d.match,token:this.terminals_[x]||x,line:d.yylineno,loc:g,expected:w})}if(m[0]instanceof Array&&m.length>1)throw new Error("Parse Error: multiple actions possible at state: "+y+", token: "+x);switch(m[0]){case 1:e.push(x),r.push(d.yytext),s.push(d.yylloc),e.push(m[1]),x=null,c=d.yyleng,n=d.yytext,o=d.yylineno,g=d.yylloc;break;case 2:if(T=this.productions_[m[1]][1],_.$=r[r.length-T],_._$={first_line:s[s.length-(T||1)].first_line,last_line:s[s.length-1].last_line,first_column:s[s.length-(T||1)].first_column,last_column:s[s.length-1].last_column},u&&(_._$.range=[s[s.length-(T||1)].range[0],s[s.length-1].range[1]]),void 0!==(b=this.performAction.apply(_,[n,c,o,h.yy,m[1],r,s].concat(l))))return b;T&&(e=e.slice(0,-1*T*2),r=r.slice(0,-1*T),s=s.slice(0,-1*T)),e.push(this.productions_[m[1]][0]),r.push(_.$),s.push(_._$),E=i[e[e.length-2]][e[e.length-1]],e.push(E);break;case 3:return!0}}return!0}},W={EOF:1,parseError:function(t,e){if(!this.yy.parser)throw new Error(t);this.yy.parser.parseError(t,e)},setInput:function(t,e){return this.yy=e||this.yy||{},this._input=t,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},input:function(){var t=this._input[0];return this.yytext+=t,this.yyleng++,this.offset++,this.match+=t,this.matched+=t,t.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),t},unput:function(t){var e=t.length,a=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-e),this.offset-=e;var r=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),a.length-1&&(this.yylineno-=a.length-1);var s=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:a?(a.length===r.length?this.yylloc.first_column:0)+r[r.length-a.length].length-a[0].length:this.yylloc.first_column-e},this.options.ranges&&(this.yylloc.range=[s[0],s[0]+this.yyleng-e]),this.yyleng=this.yytext.length,this},more:function(){return this._more=!0,this},reject:function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},less:function(t){this.unput(this.match.slice(t))},pastInput:function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var t=this.match;return t.length<20&&(t+=this._input.substr(0,20-t.length)),(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var t=this.pastInput(),e=new Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"},test_match:function(t,e){var a,r,s;if(this.options.backtrack_lexer&&(s={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(s.yylloc.range=this.yylloc.range.slice(0))),(r=t[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=r.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:r?r[r.length-1].length-r[r.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length},this.yytext+=t[0],this.match+=t[0],this.matches=t,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(t[0].length),this.matched+=t[0],a=this.performAction.call(this,this.yy,this,e,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),a)return a;if(this._backtrack){for(var i in s)this[i]=s[i];return!1}return!1},next:function(){if(this.done)return this.EOF;var t,e,a,r;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var s=this._currentRules(),i=0;i<s.length;i++)if((a=this._input.match(this.rules[s[i]]))&&(!e||a[0].length>e[0].length)){if(e=a,r=i,this.options.backtrack_lexer){if(!1!==(t=this.test_match(a,s[i])))return t;if(this._backtrack){e=!1;continue}return!1}if(!this.options.flex)break}return e?!1!==(t=this.test_match(e,s[r]))&&t:""===this._input?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},lex:function(){return this.next()||this.lex()},begin:function(t){this.conditionStack.push(t)},popState:function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]},_currentRules:function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},topState:function(t){return(t=this.conditionStack.length-1-Math.abs(t||0))>=0?this.conditionStack[t]:"INITIAL"},pushState:function(t){this.begin(t)},stateStackSize:function(){return this.conditionStack.length},options:{"case-insensitive":!0},performAction:function(t,e,a,r){switch(a){case 0:case 51:case 64:return 5;case 1:case 2:case 3:case 4:case 5:break;case 6:return 19;case 7:return this.begin("LINE"),14;case 8:return this.begin("ID"),50;case 9:return this.begin("ID"),52;case 10:return 13;case 11:return this.begin("ID"),53;case 12:return e.yytext=e.yytext.trim(),this.begin("ALIAS"),70;case 13:return this.popState(),this.popState(),this.begin("LINE"),51;case 14:return this.popState(),this.popState(),5;case 15:return this.begin("LINE"),36;case 16:return this.begin("LINE"),37;case 17:return this.begin("LINE"),38;case 18:return this.begin("LINE"),39;case 19:return this.begin("LINE"),49;case 20:return this.begin("LINE"),41;case 21:return this.begin("LINE"),43;case 22:return this.begin("LINE"),48;case 23:return this.begin("LINE"),44;case 24:return this.begin("LINE"),47;case 25:return this.begin("LINE"),46;case 26:return this.popState(),15;case 27:return 16;case 28:return 65;case 29:return 66;case 30:return 59;case 31:return 60;case 32:return 61;case 33:return 62;case 34:return 57;case 35:return 54;case 36:return this.begin("ID"),21;case 37:return this.begin("ID"),23;case 38:return 29;case 39:return 30;case 40:return this.begin("acc_title"),31;case 41:return this.popState(),"acc_title_value";case 42:return this.begin("acc_descr"),33;case 43:return this.popState(),"acc_descr_value";case 44:this.begin("acc_descr_multiline");break;case 45:this.popState();break;case 46:return"acc_descr_multiline_value";case 47:return 6;case 48:return 18;case 49:return 20;case 50:return 64;case 52:return e.yytext=e.yytext.trim(),70;case 53:return 73;case 54:return 74;case 55:return 71;case 56:return 72;case 57:return 75;case 58:return 76;case 59:return 77;case 60:return 78;case 61:return 79;case 62:return 68;case 63:return 69;case 65:return"INVALID"}},rules:[/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:((?!\n)\s)+)/i,/^(?:#[^\n]*)/i,/^(?:%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[0-9]+(?=[ \n]+))/i,/^(?:box\b)/i,/^(?:participant\b)/i,/^(?:actor\b)/i,/^(?:create\b)/i,/^(?:destroy\b)/i,/^(?:[^\->:\n,;]+?([\-]*[^\->:\n,;]+?)*?(?=((?!\n)\s)+as(?!\n)\s|[#\n;]|$))/i,/^(?:as\b)/i,/^(?:(?:))/i,/^(?:loop\b)/i,/^(?:rect\b)/i,/^(?:opt\b)/i,/^(?:alt\b)/i,/^(?:else\b)/i,/^(?:par\b)/i,/^(?:par_over\b)/i,/^(?:and\b)/i,/^(?:critical\b)/i,/^(?:option\b)/i,/^(?:break\b)/i,/^(?:(?:[:]?(?:no)?wrap)?[^#\n;]*)/i,/^(?:end\b)/i,/^(?:left of\b)/i,/^(?:right of\b)/i,/^(?:links\b)/i,/^(?:link\b)/i,/^(?:properties\b)/i,/^(?:details\b)/i,/^(?:over\b)/i,/^(?:note\b)/i,/^(?:activate\b)/i,/^(?:deactivate\b)/i,/^(?:title\s[^#\n;]+)/i,/^(?:title:\s[^#\n;]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:sequenceDiagram\b)/i,/^(?:autonumber\b)/i,/^(?:off\b)/i,/^(?:,)/i,/^(?:;)/i,/^(?:[^\+\->:\n,;]+((?!(-x|--x|-\)|--\)))[\-]*[^\+\->:\n,;]+)*)/i,/^(?:->>)/i,/^(?:-->>)/i,/^(?:->)/i,/^(?:-->)/i,/^(?:-[x])/i,/^(?:--[x])/i,/^(?:-[\)])/i,/^(?:--[\)])/i,/^(?::(?:(?:no)?wrap)?[^#\n;]+)/i,/^(?:\+)/i,/^(?:-)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[45,46],inclusive:!1},acc_descr:{rules:[43],inclusive:!1},acc_title:{rules:[41],inclusive:!1},ID:{rules:[2,3,12],inclusive:!1},ALIAS:{rules:[2,3,13,14],inclusive:!1},LINE:{rules:[2,3,26],inclusive:!1},INITIAL:{rules:[0,1,3,4,5,6,7,8,9,10,11,15,16,17,18,19,20,21,22,23,24,25,27,28,29,30,31,32,33,34,35,36,37,38,39,40,42,44,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65],inclusive:!0}}};function z(){this.yy={}}return q.lexer=W,z.prototype=q,q.Parser=z,new z}());o.parser=o;const c=o,l=new class{constructor(t){this.init=t,this.records=this.init()}reset(){this.records=this.init()}}((()=>({prevActor:void 0,actors:{},createdActors:{},destroyedActors:{},boxes:[],messages:[],notes:[],sequenceNumbersEnabled:!1,wrapEnabled:void 0,currentBox:void 0,lastCreated:void 0,lastDestroyed:void 0}))),d=function(t,e,a,r){let s=l.records.currentBox;const i=l.records.actors[t];if(i){if(l.records.currentBox&&i.box&&l.records.currentBox!==i.box)throw new Error("A same participant should only be defined in one Box: "+i.name+" can't be in '"+i.box.name+"' and in '"+l.records.currentBox.name+"' at the same time.");if(s=i.box?i.box:l.records.currentBox,i.box=s,i&&e===i.name&&null==a)return}null!=a&&null!=a.text||(a={text:e,wrap:null,type:r}),null!=r&&null!=a.text||(a={text:e,wrap:null,type:r}),l.records.actors[t]={box:s,name:e,description:a.text,wrap:void 0===a.wrap&&g()||!!a.wrap,prevActor:l.records.prevActor,links:{},properties:{},actorCnt:null,rectData:null,type:r||"participant"},l.records.prevActor&&l.records.actors[l.records.prevActor]&&(l.records.actors[l.records.prevActor].nextActor=t),l.records.currentBox&&l.records.currentBox.actorKeys.push(t),l.records.prevActor=t},h=function(t,e,a={text:void 0,wrap:void 0},r,s=!1){if(r===u.ACTIVE_END&&(t=>{let e,a=0;for(e=0;e<l.records.messages.length;e++)l.records.messages[e].type===u.ACTIVE_START&&l.records.messages[e].from.actor===t&&a++,l.records.messages[e].type===u.ACTIVE_END&&l.records.messages[e].from.actor===t&&a--;return a})(t.actor)<1){let e=new Error("Trying to inactivate an inactive participant ("+t.actor+")");throw e.hash={text:"->>-",token:"->>-",line:"1",loc:{first_line:1,last_line:1,first_column:1,last_column:1},expected:["'ACTIVE_PARTICIPANT'"]},e}return l.records.messages.push({from:t,to:e,message:a.text,wrap:void 0===a.wrap&&g()||!!a.wrap,type:r,activate:s}),!0},p=function(t){return l.records.actors[t]},g=()=>void 0!==l.records.wrapEnabled?l.records.wrapEnabled:(0,r.c)().sequence.wrap,u={SOLID:0,DOTTED:1,NOTE:2,SOLID_CROSS:3,DOTTED_CROSS:4,SOLID_OPEN:5,DOTTED_OPEN:6,LOOP_START:10,LOOP_END:11,ALT_START:12,ALT_ELSE:13,ALT_END:14,OPT_START:15,OPT_END:16,ACTIVE_START:17,ACTIVE_END:18,PAR_START:19,PAR_AND:20,PAR_END:21,RECT_START:22,RECT_END:23,SOLID_POINT:24,DOTTED_POINT:25,AUTONUMBER:26,CRITICAL_START:27,CRITICAL_OPTION:28,CRITICAL_END:29,BREAK_START:30,BREAK_END:31,PAR_OVER_START:32},x=function(t,e,a){const r={actor:t,placement:e,message:a.text,wrap:void 0===a.wrap&&g()||!!a.wrap},s=[].concat(t,t);l.records.notes.push(r),l.records.messages.push({from:s[0],to:s[1],message:a.text,wrap:void 0===a.wrap&&g()||!!a.wrap,type:u.NOTE,placement:e})},y=function(t,e){const a=p(t);try{let t=(0,r.d)(e.text,(0,r.c)());t=t.replace(/&amp;/g,"&"),t=t.replace(/&equals;/g,"="),m(a,JSON.parse(t))}catch(t){r.l.error("error while parsing actor link text",t)}};function m(t,e){if(null==t.links)t.links=e;else for(let a in e)t.links[a]=e[a]}const b=function(t,e){const a=p(t);try{let t=(0,r.d)(e.text,(0,r.c)());f(a,JSON.parse(t))}catch(t){r.l.error("error while parsing actor properties text",t)}};function f(t,e){if(null==t.properties)t.properties=e;else for(let a in e)t.properties[a]=e[a]}const T=function(t,e){const a=p(t),s=document.getElementById(e.text);try{const t=s.innerHTML,e=JSON.parse(t);e.properties&&f(a,e.properties),e.links&&m(a,e.links)}catch(t){r.l.error("error while parsing actor details text",t)}},E=function(t){if(Array.isArray(t))t.forEach((function(t){E(t)}));else switch(t.type){case"sequenceIndex":l.records.messages.push({from:void 0,to:void 0,message:{start:t.sequenceIndex,step:t.sequenceIndexStep,visible:t.sequenceVisible},wrap:!1,type:t.signalType});break;case"addParticipant":d(t.actor,t.actor,t.description,t.draw);break;case"createParticipant":if(l.records.actors[t.actor])throw new Error("It is not possible to have actors with the same id, even if one is destroyed before the next is created. Use 'AS' aliases to simulate the behavior");l.records.lastCreated=t.actor,d(t.actor,t.actor,t.description,t.draw),l.records.createdActors[t.actor]=l.records.messages.length;break;case"destroyParticipant":l.records.lastDestroyed=t.actor,l.records.destroyedActors[t.actor]=l.records.messages.length;break;case"activeStart":case"activeEnd":h(t.actor,void 0,void 0,t.signalType);break;case"addNote":x(t.actor,t.placement,t.text);break;case"addLinks":y(t.actor,t.text);break;case"addALink":!function(t,e){const a=p(t);try{const t={};let o=(0,r.d)(e.text,(0,r.c)());var s=o.indexOf("@");o=o.replace(/&amp;/g,"&"),o=o.replace(/&equals;/g,"=");var i=o.slice(0,s-1).trim(),n=o.slice(s+1).trim();t[i]=n,m(a,t)}catch(t){r.l.error("error while parsing actor link text",t)}}(t.actor,t.text);break;case"addProperties":b(t.actor,t.text);break;case"addDetails":T(t.actor,t.text);break;case"addMessage":if(l.records.lastCreated){if(t.to!==l.records.lastCreated)throw new Error("The created participant "+l.records.lastCreated+" does not have an associated creating message after its declaration. Please check the sequence diagram.");l.records.lastCreated=void 0}else if(l.records.lastDestroyed){if(t.to!==l.records.lastDestroyed&&t.from!==l.records.lastDestroyed)throw new Error("The destroyed participant "+l.records.lastDestroyed+" does not have an associated destroying message after its declaration. Please check the sequence diagram.");l.records.lastDestroyed=void 0}h(t.from,t.to,t.msg,t.signalType,t.activate);break;case"boxStart":e=t.boxData,l.records.boxes.push({name:e.text,wrap:void 0===e.wrap&&g()||!!e.wrap,fill:e.color,actorKeys:[]}),l.records.currentBox=l.records.boxes.slice(-1)[0];break;case"boxEnd":l.records.currentBox=void 0;break;case"loopStart":h(void 0,void 0,t.loopText,t.signalType);break;case"loopEnd":case"rectEnd":case"optEnd":case"altEnd":case"parEnd":case"criticalEnd":case"breakEnd":h(void 0,void 0,void 0,t.signalType);break;case"rectStart":h(void 0,void 0,t.color,t.signalType);break;case"optStart":h(void 0,void 0,t.optText,t.signalType);break;case"altStart":case"else":h(void 0,void 0,t.altText,t.signalType);break;case"setAccTitle":(0,r.s)(t.text);break;case"parStart":case"and":h(void 0,void 0,t.parText,t.signalType);break;case"criticalStart":h(void 0,void 0,t.criticalText,t.signalType);break;case"option":h(void 0,void 0,t.optionText,t.signalType);break;case"breakStart":h(void 0,void 0,t.breakText,t.signalType)}var e},w={addActor:d,addMessage:function(t,e,a,r){l.records.messages.push({from:t,to:e,message:a.text,wrap:void 0===a.wrap&&g()||!!a.wrap,answer:r})},addSignal:h,addLinks:y,addDetails:T,addProperties:b,autoWrap:g,setWrap:function(t){l.records.wrapEnabled=t},enableSequenceNumbers:function(){l.records.sequenceNumbersEnabled=!0},disableSequenceNumbers:function(){l.records.sequenceNumbersEnabled=!1},showSequenceNumbers:()=>l.records.sequenceNumbersEnabled,getMessages:function(){return l.records.messages},getActors:function(){return l.records.actors},getCreatedActors:function(){return l.records.createdActors},getDestroyedActors:function(){return l.records.destroyedActors},getActor:p,getActorKeys:function(){return Object.keys(l.records.actors)},getActorProperty:function(t,e){if(void 0!==t&&void 0!==t.properties)return t.properties[e]},getAccTitle:r.g,getBoxes:function(){return l.records.boxes},getDiagramTitle:r.r,setDiagramTitle:r.q,getConfig:()=>(0,r.c)().sequence,clear:function(){l.reset(),(0,r.t)()},parseMessage:function(t){const e=t.trim(),a={text:e.replace(/^:?(?:no)?wrap:/,"").trim(),wrap:null!==e.match(/^:?wrap:/)||null===e.match(/^:?nowrap:/)&&void 0};return r.l.debug("parseMessage:",a),a},parseBoxData:function(t){const e=t.match(/^((?:rgba?|hsla?)\s*\(.*\)|\w*)(.*)$/);let a=null!=e&&e[1]?e[1].trim():"transparent",s=null!=e&&e[2]?e[2].trim():void 0;if(window&&window.CSS)window.CSS.supports("color",a)||(a="transparent",s=t.trim());else{const e=(new Option).style;e.color=a,e.color!==a&&(a="transparent",s=t.trim())}return{color:a,text:void 0!==s?(0,r.d)(s.replace(/^:?(?:no)?wrap:/,""),(0,r.c)()):void 0,wrap:void 0!==s?null!==s.match(/^:?wrap:/)||null===s.match(/^:?nowrap:/)&&void 0:void 0}},LINETYPE:u,ARROWTYPE:{FILLED:0,OPEN:1},PLACEMENT:{LEFTOF:0,RIGHTOF:1,OVER:2},addNote:x,setAccTitle:r.s,apply:E,setAccDescription:r.b,getAccDescription:r.a,hasAtLeastOneBox:function(){return l.records.boxes.length>0},hasAtLeastOneBoxWithTitle:function(){return l.records.boxes.some((t=>t.name))}},P=function(t,e){return(0,i.d)(t,e)},_=(t,e)=>{(0,r.F)((()=>{const a=document.querySelectorAll(t);0!==a.length&&(a[0].addEventListener("mouseover",(function(){v("actor"+e+"_popup")})),a[0].addEventListener("mouseout",(function(){L("actor"+e+"_popup")})))}))},v=function(t){var e=document.getElementById(t);null!=e&&(e.style.display="block")},L=function(t){var e=document.getElementById(t);null!=e&&(e.style.display="none")},k=function(t,e){let a=0,s=0;const i=e.text.split(r.e.lineBreakRegex),[n,o]=(0,r.C)(e.fontSize);let c=[],l=0,d=()=>e.y;if(void 0!==e.valign&&void 0!==e.textMargin&&e.textMargin>0)switch(e.valign){case"top":case"start":d=()=>Math.round(e.y+e.textMargin);break;case"middle":case"center":d=()=>Math.round(e.y+(a+s+e.textMargin)/2);break;case"bottom":case"end":d=()=>Math.round(e.y+(a+s+2*e.textMargin)-e.textMargin)}if(void 0!==e.anchor&&void 0!==e.textMargin&&void 0!==e.width)switch(e.anchor){case"left":case"start":e.x=Math.round(e.x+e.textMargin),e.anchor="start",e.dominantBaseline="middle",e.alignmentBaseline="middle";break;case"middle":case"center":e.x=Math.round(e.x+e.width/2),e.anchor="middle",e.dominantBaseline="middle",e.alignmentBaseline="middle";break;case"right":case"end":e.x=Math.round(e.x+e.width-e.textMargin),e.anchor="end",e.dominantBaseline="middle",e.alignmentBaseline="middle"}for(let[h,p]of i.entries()){void 0!==e.textMargin&&0===e.textMargin&&void 0!==n&&(l=h*n);const i=t.append("text");i.attr("x",e.x),i.attr("y",d()),void 0!==e.anchor&&i.attr("text-anchor",e.anchor).attr("dominant-baseline",e.dominantBaseline).attr("alignment-baseline",e.alignmentBaseline),void 0!==e.fontFamily&&i.style("font-family",e.fontFamily),void 0!==o&&i.style("font-size",o),void 0!==e.fontWeight&&i.style("font-weight",e.fontWeight),void 0!==e.fill&&i.attr("fill",e.fill),void 0!==e.class&&i.attr("class",e.class),void 0!==e.dy?i.attr("dy",e.dy):0!==l&&i.attr("dy",l);const g=p||r.Z;if(e.tspan){const t=i.append("tspan");t.attr("x",e.x),void 0!==e.fill&&t.attr("fill",e.fill),t.text(g)}else i.text(g);void 0!==e.valign&&void 0!==e.textMargin&&e.textMargin>0&&(s+=(i._groups||i)[0][0].getBBox().height,a=s),c.push(i)}return c},I=function(t,e){const a=t.append("polygon");var r,s,i,n;return a.attr("points",(r=e.x)+","+(s=e.y)+" "+(r+(i=e.width))+","+s+" "+(r+i)+","+(s+(n=e.height)-7)+" "+(r+i-8.4)+","+(s+n)+" "+r+","+(s+n)),a.attr("class","labelBox"),e.y=e.y+e.height/2,k(t,e),a};let N=-1;const M=(t,e,a,r)=>{t.select&&a.forEach((a=>{const s=e[a],i=t.select("#actor"+s.actorCnt);!r.mirrorActors&&s.stopy?i.attr("y2",s.stopy+s.height/2):r.mirrorActors&&i.attr("y2",s.stopy)}))},A=function(t,e){(0,i.a)(t,e)},O=function(){function t(t,e,a,r,i,n,o){s(e.append("text").attr("x",a+i/2).attr("y",r+n/2+5).style("text-anchor","middle").text(t),o)}function e(t,e,a,i,n,o,c,l){const{actorFontSize:d,actorFontFamily:h,actorFontWeight:p}=l,[g,u]=(0,r.C)(d),x=t.split(r.e.lineBreakRegex);for(let t=0;t<x.length;t++){const r=t*g-g*(x.length-1)/2,l=e.append("text").attr("x",a+n/2).attr("y",i).style("text-anchor","middle").style("font-size",u).style("font-weight",p).style("font-family",h);l.append("tspan").attr("x",a+n/2).attr("dy",r).text(x[t]),l.attr("y",i+o/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),s(l,c)}}function a(t,a,r,i,n,o,c,l){const d=a.append("switch"),h=d.append("foreignObject").attr("x",r).attr("y",i).attr("width",n).attr("height",o).append("xhtml:div").style("display","table").style("height","100%").style("width","100%");h.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(t),e(t,d,r,i,n,o,c,l),s(h,c)}function s(t,e){for(const a in e)e.hasOwnProperty(a)&&t.attr(a,e[a])}return function(r){return"fo"===r.textPlacement?a:"old"===r.textPlacement?t:e}}(),S=function(){function t(t,e,a,r,i,n,o){s(e.append("text").attr("x",a).attr("y",r).style("text-anchor","start").text(t),o)}function e(t,e,a,i,n,o,c,l){const{actorFontSize:d,actorFontFamily:h,actorFontWeight:p}=l,g=t.split(r.e.lineBreakRegex);for(let t=0;t<g.length;t++){const r=t*d-d*(g.length-1)/2,n=e.append("text").attr("x",a).attr("y",i).style("text-anchor","start").style("font-size",d).style("font-weight",p).style("font-family",h);n.append("tspan").attr("x",a).attr("dy",r).text(g[t]),n.attr("y",i+o/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),s(n,c)}}function a(t,a,r,i,n,o,c,l){const d=a.append("switch"),h=d.append("foreignObject").attr("x",r).attr("y",i).attr("width",n).attr("height",o).append("xhtml:div").style("display","table").style("height","100%").style("width","100%");h.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(t),e(t,d,r,i,0,o,c,l),s(h,c)}function s(t,e){for(const a in e)e.hasOwnProperty(a)&&t.attr(a,e[a])}return function(r){return"fo"===r.textPlacement?a:"old"===r.textPlacement?t:e}}(),D=P,R=function(t,e,a,r){switch(e.type){case"actor":return function(t,e,a,r){const s=r?e.stopy:e.starty,n=e.x+e.width/2,o=s+80;t.lower(),r||(N++,t.append("line").attr("id","actor"+N).attr("x1",n).attr("y1",o).attr("x2",n).attr("y2",2e3).attr("class","actor-line").attr("class","200").attr("stroke-width","0.5px").attr("stroke","#999"),e.actorCnt=N);const c=t.append("g");c.attr("class","actor-man");const l=(0,i.g)();l.x=e.x,l.y=s,l.fill="#eaeaea",l.width=e.width,l.height=e.height,l.class="actor",l.rx=3,l.ry=3,c.append("line").attr("id","actor-man-torso"+N).attr("x1",n).attr("y1",s+25).attr("x2",n).attr("y2",s+45),c.append("line").attr("id","actor-man-arms"+N).attr("x1",n-18).attr("y1",s+33).attr("x2",n+18).attr("y2",s+33),c.append("line").attr("x1",n-18).attr("y1",s+60).attr("x2",n).attr("y2",s+45),c.append("line").attr("x1",n).attr("y1",s+45).attr("x2",n+18-2).attr("y2",s+60);const d=c.append("circle");d.attr("cx",e.x+e.width/2),d.attr("cy",s+10),d.attr("r",15),d.attr("width",e.width),d.attr("height",e.height);const h=c.node().getBBox();return e.height=h.height,O(a)(e.description,c,l.x,l.y+35,l.width,l.height,{class:"actor"},a),e.height}(t,e,a,r);case"participant":return function(t,e,a,r){const s=r?e.stopy:e.starty,n=e.x+e.width/2,o=s+5,c=t.append("g").lower();var l=c;r||(N++,l.append("line").attr("id","actor"+N).attr("x1",n).attr("y1",o).attr("x2",n).attr("y2",2e3).attr("class","actor-line").attr("class","200").attr("stroke-width","0.5px").attr("stroke","#999"),l=c.append("g"),e.actorCnt=N,null!=e.links&&(l.attr("id","root-"+N),_("#root-"+N,N)));const d=(0,i.g)();var h="actor";null!=e.properties&&e.properties.class?h=e.properties.class:d.fill="#eaeaea",d.x=e.x,d.y=s,d.width=e.width,d.height=e.height,d.class=h,d.rx=3,d.ry=3;const p=P(l,d);if(e.rectData=d,null!=e.properties&&e.properties.icon){const t=e.properties.icon.trim();"@"===t.charAt(0)?(0,i.b)(l,d.x+d.width-20,d.y+10,t.substr(1)):(0,i.c)(l,d.x+d.width-20,d.y+10,t)}O(a)(e.description,l,d.x,d.y,d.width,d.height,{class:"actor"},a);let g=e.height;if(p.node){const t=p.node().getBBox();e.height=t.height,g=t.height}return g}(t,e,a,r)}},C=function(t,e,a){const r=t.append("g");A(r,e),e.name&&O(a)(e.name,r,e.x,e.y+(e.textMaxHeight||0)/2,e.width,0,{class:"text"},a),r.lower()},Y=function(t,e,a,r,s){if(void 0===e.links||null===e.links||0===Object.keys(e.links).length)return{height:0,width:0};const i=e.links,o=e.actorCnt,c=e.rectData;var l="none";s&&(l="block !important");const d=t.append("g");d.attr("id","actor"+o+"_popup"),d.attr("class","actorPopupMenu"),d.attr("display",l),_("#actor"+o+"_popup",o);var h="";void 0!==c.class&&(h=" "+c.class);let p=c.width>a?c.width:a;const g=d.append("rect");if(g.attr("class","actorPopupMenuPanel"+h),g.attr("x",c.x),g.attr("y",c.height),g.attr("fill",c.fill),g.attr("stroke",c.stroke),g.attr("width",p),g.attr("height",c.height),g.attr("rx",c.rx),g.attr("ry",c.ry),null!=i){var u=20;for(let t in i){var x=d.append("a"),y=(0,n.Nm)(i[t]);x.attr("xlink:href",y),x.attr("target","_blank"),S(r)(t,x,c.x+10,c.height+u,p,20,{class:"actor"},r),u+=30}}return g.attr("height",u),{height:c.height+u,width:p}},$=function(t){return t.append("g")},B=function(t,e,a,r,s){const n=(0,i.g)(),o=e.anchored;n.x=e.startx,n.y=e.starty,n.class="activation"+s%3,n.width=e.stopx-e.startx,n.height=a-e.starty,P(o,n)},V=function(t,e,a,r){const{boxMargin:s,boxTextMargin:n,labelBoxHeight:o,labelBoxWidth:c,messageFontFamily:l,messageFontSize:d,messageFontWeight:h}=r,p=t.append("g"),g=function(t,e,a,r){return p.append("line").attr("x1",t).attr("y1",e).attr("x2",a).attr("y2",r).attr("class","loopLine")};g(e.startx,e.starty,e.stopx,e.starty),g(e.stopx,e.starty,e.stopx,e.stopy),g(e.startx,e.stopy,e.stopx,e.stopy),g(e.startx,e.starty,e.startx,e.stopy),void 0!==e.sections&&e.sections.forEach((function(t){g(e.startx,t.y,e.stopx,t.y).style("stroke-dasharray","3, 3")}));let u=(0,i.e)();u.text=a,u.x=e.startx,u.y=e.starty,u.fontFamily=l,u.fontSize=d,u.fontWeight=h,u.anchor="middle",u.valign="middle",u.tspan=!1,u.width=c||50,u.height=o||20,u.textMargin=n,u.class="labelText",I(p,u),u={x:0,y:0,fill:void 0,anchor:void 0,style:"#666",width:void 0,height:void 0,textMargin:0,rx:0,ry:0,tspan:!0,valign:void 0},u.text=e.title,u.x=e.startx+c/2+(e.stopx-e.startx)/2,u.y=e.starty+s+n,u.anchor="middle",u.valign="middle",u.textMargin=n,u.class="loopText",u.fontFamily=l,u.fontSize=d,u.fontWeight=h,u.wrap=!0;let x=k(p,u);return void 0!==e.sectionTitles&&e.sectionTitles.forEach((function(t,a){if(t.message){u.text=t.message,u.x=e.startx+(e.stopx-e.startx)/2,u.y=e.sections[a].y+s+n,u.class="loopText",u.anchor="middle",u.valign="middle",u.tspan=!1,u.fontFamily=l,u.fontSize=d,u.fontWeight=h,u.wrap=e.wrap,x=k(p,u);let r=Math.round(x.map((t=>(t._groups||t)[0][0].getBBox().height)).reduce(((t,e)=>t+e)));e.sections[a].height+=r-(s+n)}})),e.height=Math.round(e.stopy-e.starty),p},F=A,q=function(t){t.append("defs").append("marker").attr("id","arrowhead").attr("refX",7.9).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z")},W=function(t){t.append("defs").append("marker").attr("id","filled-head").attr("refX",15.5).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},z=function(t){t.append("defs").append("marker").attr("id","sequencenumber").attr("refX",15).attr("refY",15).attr("markerWidth",60).attr("markerHeight",40).attr("orient","auto").append("circle").attr("cx",15).attr("cy",15).attr("r",6)},U=function(t){t.append("defs").append("marker").attr("id","crosshead").attr("markerWidth",15).attr("markerHeight",8).attr("orient","auto").attr("refX",4).attr("refY",4.5).append("path").attr("fill","none").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1pt").attr("d","M 1,2 L 6,7 M 6,2 L 1,7")},H=function(t){t.append("defs").append("symbol").attr("id","database").attr("fill-rule","evenodd").attr("clip-rule","evenodd").append("path").attr("transform","scale(.5)").attr("d","M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z")},j=function(t){t.append("defs").append("symbol").attr("id","computer").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z")},K=function(t){t.append("defs").append("symbol").attr("id","clock").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z")};n.Nm;let X={};const G={data:{startx:void 0,stopx:void 0,starty:void 0,stopy:void 0},verticalPos:0,sequenceItems:[],activations:[],models:{getHeight:function(){return Math.max.apply(null,0===this.actors.length?[0]:this.actors.map((t=>t.height||0)))+(0===this.loops.length?0:this.loops.map((t=>t.height||0)).reduce(((t,e)=>t+e)))+(0===this.messages.length?0:this.messages.map((t=>t.height||0)).reduce(((t,e)=>t+e)))+(0===this.notes.length?0:this.notes.map((t=>t.height||0)).reduce(((t,e)=>t+e)))},clear:function(){this.actors=[],this.boxes=[],this.loops=[],this.messages=[],this.notes=[]},addBox:function(t){this.boxes.push(t)},addActor:function(t){this.actors.push(t)},addLoop:function(t){this.loops.push(t)},addMessage:function(t){this.messages.push(t)},addNote:function(t){this.notes.push(t)},lastActor:function(){return this.actors[this.actors.length-1]},lastLoop:function(){return this.loops[this.loops.length-1]},lastMessage:function(){return this.messages[this.messages.length-1]},lastNote:function(){return this.notes[this.notes.length-1]},actors:[],boxes:[],loops:[],messages:[],notes:[]},init:function(){this.sequenceItems=[],this.activations=[],this.models.clear(),this.data={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0},this.verticalPos=0,at((0,r.c)())},updateVal:function(t,e,a,r){void 0===t[e]?t[e]=a:t[e]=r(a,t[e])},updateBounds:function(t,e,a,r){const s=this;let i=0;function n(n){return function(o){i++;const c=s.sequenceItems.length-i+1;s.updateVal(o,"starty",e-c*X.boxMargin,Math.min),s.updateVal(o,"stopy",r+c*X.boxMargin,Math.max),s.updateVal(G.data,"startx",t-c*X.boxMargin,Math.min),s.updateVal(G.data,"stopx",a+c*X.boxMargin,Math.max),"activation"!==n&&(s.updateVal(o,"startx",t-c*X.boxMargin,Math.min),s.updateVal(o,"stopx",a+c*X.boxMargin,Math.max),s.updateVal(G.data,"starty",e-c*X.boxMargin,Math.min),s.updateVal(G.data,"stopy",r+c*X.boxMargin,Math.max))}}this.sequenceItems.forEach(n()),this.activations.forEach(n("activation"))},insert:function(t,e,a,s){const i=r.e.getMin(t,a),n=r.e.getMax(t,a),o=r.e.getMin(e,s),c=r.e.getMax(e,s);this.updateVal(G.data,"startx",i,Math.min),this.updateVal(G.data,"starty",o,Math.min),this.updateVal(G.data,"stopx",n,Math.max),this.updateVal(G.data,"stopy",c,Math.max),this.updateBounds(i,o,n,c)},newActivation:function(t,e,a){const r=a[t.from.actor],s=rt(t.from.actor).length||0,i=r.x+r.width/2+(s-1)*X.activationWidth/2;this.activations.push({startx:i,starty:this.verticalPos+2,stopx:i+X.activationWidth,stopy:void 0,actor:t.from.actor,anchored:$(e)})},endActivation:function(t){const e=this.activations.map((function(t){return t.actor})).lastIndexOf(t.from.actor);return this.activations.splice(e,1)[0]},createLoop:function(t={message:void 0,wrap:!1,width:void 0},e){return{startx:void 0,starty:this.verticalPos,stopx:void 0,stopy:void 0,title:t.message,wrap:t.wrap,width:t.width,height:0,fill:e}},newLoop:function(t={message:void 0,wrap:!1,width:void 0},e){this.sequenceItems.push(this.createLoop(t,e))},endLoop:function(){return this.sequenceItems.pop()},isLoopOverlap:function(){return!!this.sequenceItems.length&&this.sequenceItems[this.sequenceItems.length-1].overlap},addSectionToLoop:function(t){const e=this.sequenceItems.pop();e.sections=e.sections||[],e.sectionTitles=e.sectionTitles||[],e.sections.push({y:G.getVerticalPos(),height:0}),e.sectionTitles.push(t),this.sequenceItems.push(e)},saveVerticalPos:function(){this.isLoopOverlap()&&(this.savedVerticalPos=this.verticalPos)},resetVerticalPos:function(){this.isLoopOverlap()&&(this.verticalPos=this.savedVerticalPos)},bumpVerticalPos:function(t){this.verticalPos=this.verticalPos+t,this.data.stopy=r.e.getMax(this.data.stopy,this.verticalPos)},getVerticalPos:function(){return this.verticalPos},getBounds:function(){return{bounds:this.data,models:this.models}}},J=t=>({fontFamily:t.messageFontFamily,fontSize:t.messageFontSize,fontWeight:t.messageFontWeight}),Z=t=>({fontFamily:t.noteFontFamily,fontSize:t.noteFontSize,fontWeight:t.noteFontWeight}),Q=t=>({fontFamily:t.actorFontFamily,fontSize:t.actorFontSize,fontWeight:t.actorFontWeight}),tt=function(t,e,a,s){if(s){let s=0;G.bumpVerticalPos(2*X.boxMargin);for(const i of a){const a=e[i];a.stopy||(a.stopy=G.getVerticalPos());const n=R(t,a,X,!0);s=r.e.getMax(s,n)}G.bumpVerticalPos(s+X.boxMargin)}else for(const r of a){const a=e[r];R(t,a,X,!1)}},et=function(t,e,a,r){let s=0,i=0;for(const n of a){const a=e[n],o=nt(a),c=Y(t,a,o,X,X.forceMenus,r);c.height>s&&(s=c.height),c.width+a.x>i&&(i=c.width+a.x)}return{maxHeight:s,maxWidth:i}},at=function(t){(0,r.f)(X,t),t.fontFamily&&(X.actorFontFamily=X.noteFontFamily=X.messageFontFamily=t.fontFamily),t.fontSize&&(X.actorFontSize=X.noteFontSize=X.messageFontSize=t.fontSize),t.fontWeight&&(X.actorFontWeight=X.noteFontWeight=X.messageFontWeight=t.fontWeight)},rt=function(t){return G.activations.filter((function(e){return e.actor===t}))},st=function(t,e){const a=e[t],s=rt(t);return[s.reduce((function(t,e){return r.e.getMin(t,e.startx)}),a.x+a.width/2-1),s.reduce((function(t,e){return r.e.getMax(t,e.stopx)}),a.x+a.width/2+1)]};function it(t,e,a,s,i){G.bumpVerticalPos(a);let n=s;if(e.id&&e.message&&t[e.id]){const a=t[e.id].width,i=J(X);e.message=r.u.wrapLabel(`[${e.message}]`,a-2*X.wrapPadding,i),e.width=a,e.wrap=!0;const o=r.u.calculateTextDimensions(e.message,i),c=r.e.getMax(o.height,X.labelBoxHeight);n=s+c,r.l.debug(`${c} - ${e.message}`)}i(e),G.bumpVerticalPos(n)}const nt=function(t){let e=0;const a=Q(X);for(const s in t.links){const t=r.u.calculateTextDimensions(s,a).width+2*X.wrapPadding+2*X.boxMargin;e<t&&(e=t)}return e},ot={parser:c,db:w,renderer:{bounds:G,drawActors:tt,drawActorsPopup:et,setConf:at,draw:function(t,e,a,n){const{securityLevel:o,sequence:c}=(0,r.c)();let l;X=c,"sandbox"===o&&(l=(0,s.Ys)("#i"+e));const d="sandbox"===o?(0,s.Ys)(l.nodes()[0].contentDocument.body):(0,s.Ys)("body"),h="sandbox"===o?l.nodes()[0].contentDocument:document;G.init(),r.l.debug(n.db);const p="sandbox"===o?d.select(`[id="${e}"]`):(0,s.Ys)(`[id="${e}"]`),g=n.db.getActors(),u=n.db.getCreatedActors(),x=n.db.getDestroyedActors(),y=n.db.getBoxes();let m=n.db.getActorKeys();const b=n.db.getMessages(),f=n.db.getDiagramTitle(),T=n.db.hasAtLeastOneBox(),E=n.db.hasAtLeastOneBoxWithTitle(),w=function(t,e,a){const s={};return e.forEach((function(e){if(t[e.to]&&t[e.from]){const i=t[e.to];if(e.placement===a.db.PLACEMENT.LEFTOF&&!i.prevActor)return;if(e.placement===a.db.PLACEMENT.RIGHTOF&&!i.nextActor)return;const n=void 0!==e.placement,o=!n,c=n?Z(X):J(X),l=e.wrap?r.u.wrapLabel(e.message,X.width-2*X.wrapPadding,c):e.message,d=r.u.calculateTextDimensions(l,c).width+2*X.wrapPadding;o&&e.from===i.nextActor?s[e.to]=r.e.getMax(s[e.to]||0,d):o&&e.from===i.prevActor?s[e.from]=r.e.getMax(s[e.from]||0,d):o&&e.from===e.to?(s[e.from]=r.e.getMax(s[e.from]||0,d/2),s[e.to]=r.e.getMax(s[e.to]||0,d/2)):e.placement===a.db.PLACEMENT.RIGHTOF?s[e.from]=r.e.getMax(s[e.from]||0,d):e.placement===a.db.PLACEMENT.LEFTOF?s[i.prevActor]=r.e.getMax(s[i.prevActor]||0,d):e.placement===a.db.PLACEMENT.OVER&&(i.prevActor&&(s[i.prevActor]=r.e.getMax(s[i.prevActor]||0,d/2)),i.nextActor&&(s[e.from]=r.e.getMax(s[e.from]||0,d/2)))}})),r.l.debug("maxMessageWidthPerActor:",s),s}(g,b,n);if(X.height=function(t,e,a){let s=0;Object.keys(t).forEach((e=>{const a=t[e];a.wrap&&(a.description=r.u.wrapLabel(a.description,X.width-2*X.wrapPadding,Q(X)));const i=r.u.calculateTextDimensions(a.description,Q(X));a.width=a.wrap?X.width:r.e.getMax(X.width,i.width+2*X.wrapPadding),a.height=a.wrap?r.e.getMax(i.height,X.height):X.height,s=r.e.getMax(s,a.height)}));for(const a in e){const s=t[a];if(!s)continue;const i=t[s.nextActor];if(!i){const t=e[a]+X.actorMargin-s.width/2;s.margin=r.e.getMax(t,X.actorMargin);continue}const n=e[a]+X.actorMargin-s.width/2-i.width/2;s.margin=r.e.getMax(n,X.actorMargin)}let i=0;return a.forEach((e=>{const a=J(X);let s=e.actorKeys.reduce(((e,a)=>e+(t[a].width+(t[a].margin||0))),0);s-=2*X.boxTextMargin,e.wrap&&(e.name=r.u.wrapLabel(e.name,s-2*X.wrapPadding,a));const n=r.u.calculateTextDimensions(e.name,a);i=r.e.getMax(n.height,i);const o=r.e.getMax(s,n.width+2*X.wrapPadding);if(e.margin=X.boxTextMargin,s<o){const t=(o-s)/2;e.margin+=t}})),a.forEach((t=>t.textMaxHeight=i)),r.e.getMax(s,X.height)}(g,w,y),j(p),H(p),K(p),T&&(G.bumpVerticalPos(X.boxMargin),E&&G.bumpVerticalPos(y[0].textMaxHeight)),!0===X.hideUnusedParticipants){const t=new Set;b.forEach((e=>{t.add(e.from),t.add(e.to)})),m=m.filter((e=>t.has(e)))}!function(t,e,a,s,i,n,o){let c,l=0,d=0,h=0;for(const t of s){const s=e[t],i=s.box;c&&c!=i&&(G.models.addBox(c),d+=X.boxMargin+c.margin),i&&i!=c&&(i.x=l+d,i.y=0,d+=i.margin),s.width=s.width||X.width,s.height=r.e.getMax(s.height||X.height,X.height),s.margin=s.margin||X.actorMargin,h=r.e.getMax(h,s.height),a[s.name]&&(d+=s.width/2),s.x=l+d,s.starty=G.getVerticalPos(),G.insert(s.x,0,s.x+s.width,s.height),l+=s.width+d,s.box&&(s.box.width=l+i.margin-s.box.x),d=s.margin,c=s.box,G.models.addActor(s)}c&&G.models.addBox(c),G.bumpVerticalPos(h)}(0,g,u,m);const P=function(t,e,a,s){const i={},n=[];let o,c,l;return t.forEach((function(t){switch(t.id=r.u.random({length:10}),t.type){case s.db.LINETYPE.LOOP_START:case s.db.LINETYPE.ALT_START:case s.db.LINETYPE.OPT_START:case s.db.LINETYPE.PAR_START:case s.db.LINETYPE.PAR_OVER_START:case s.db.LINETYPE.CRITICAL_START:case s.db.LINETYPE.BREAK_START:n.push({id:t.id,msg:t.message,from:Number.MAX_SAFE_INTEGER,to:Number.MIN_SAFE_INTEGER,width:0});break;case s.db.LINETYPE.ALT_ELSE:case s.db.LINETYPE.PAR_AND:case s.db.LINETYPE.CRITICAL_OPTION:t.message&&(o=n.pop(),i[o.id]=o,i[t.id]=o,n.push(o));break;case s.db.LINETYPE.LOOP_END:case s.db.LINETYPE.ALT_END:case s.db.LINETYPE.OPT_END:case s.db.LINETYPE.PAR_END:case s.db.LINETYPE.CRITICAL_END:case s.db.LINETYPE.BREAK_END:o=n.pop(),i[o.id]=o;break;case s.db.LINETYPE.ACTIVE_START:{const a=e[t.from?t.from.actor:t.to.actor],r=rt(t.from?t.from.actor:t.to.actor).length,s=a.x+a.width/2+(r-1)*X.activationWidth/2,i={startx:s,stopx:s+X.activationWidth,actor:t.from.actor,enabled:!0};G.activations.push(i)}break;case s.db.LINETYPE.ACTIVE_END:{const e=G.activations.map((t=>t.actor)).lastIndexOf(t.from.actor);delete G.activations.splice(e,1)[0]}}void 0!==t.placement?(c=function(t,e,a){const s=e[t.from].x,i=e[t.to].x,n=t.wrap&&t.message;let o=r.u.calculateTextDimensions(n?r.u.wrapLabel(t.message,X.width,Z(X)):t.message,Z(X));const c={width:n?X.width:r.e.getMax(X.width,o.width+2*X.noteMargin),height:0,startx:e[t.from].x,stopx:0,starty:0,stopy:0,message:t.message};return t.placement===a.db.PLACEMENT.RIGHTOF?(c.width=n?r.e.getMax(X.width,o.width):r.e.getMax(e[t.from].width/2+e[t.to].width/2,o.width+2*X.noteMargin),c.startx=s+(e[t.from].width+X.actorMargin)/2):t.placement===a.db.PLACEMENT.LEFTOF?(c.width=n?r.e.getMax(X.width,o.width+2*X.noteMargin):r.e.getMax(e[t.from].width/2+e[t.to].width/2,o.width+2*X.noteMargin),c.startx=s-c.width+(e[t.from].width-X.actorMargin)/2):t.to===t.from?(o=r.u.calculateTextDimensions(n?r.u.wrapLabel(t.message,r.e.getMax(X.width,e[t.from].width),Z(X)):t.message,Z(X)),c.width=n?r.e.getMax(X.width,e[t.from].width):r.e.getMax(e[t.from].width,X.width,o.width+2*X.noteMargin),c.startx=s+(e[t.from].width-c.width)/2):(c.width=Math.abs(s+e[t.from].width/2-(i+e[t.to].width/2))+X.actorMargin,c.startx=s<i?s+e[t.from].width/2-X.actorMargin/2:i+e[t.to].width/2-X.actorMargin/2),n&&(c.message=r.u.wrapLabel(t.message,c.width-2*X.wrapPadding,Z(X))),r.l.debug(`NM:[${c.startx},${c.stopx},${c.starty},${c.stopy}:${c.width},${c.height}=${t.message}]`),c}(t,e,s),t.noteModel=c,n.forEach((t=>{o=t,o.from=r.e.getMin(o.from,c.startx),o.to=r.e.getMax(o.to,c.startx+c.width),o.width=r.e.getMax(o.width,Math.abs(o.from-o.to))-X.labelBoxWidth}))):(l=function(t,e,a){if(![a.db.LINETYPE.SOLID_OPEN,a.db.LINETYPE.DOTTED_OPEN,a.db.LINETYPE.SOLID,a.db.LINETYPE.DOTTED,a.db.LINETYPE.SOLID_CROSS,a.db.LINETYPE.DOTTED_CROSS,a.db.LINETYPE.SOLID_POINT,a.db.LINETYPE.DOTTED_POINT].includes(t.type))return{};const[s,i]=st(t.from,e),[n,o]=st(t.to,e),c=s<=n,l=c?i:s;let d=c?n:o;const h=Math.abs(n-o)>2,p=t=>c?-t:t;t.from===t.to?d=l:(t.activate&&!h&&(d+=p(X.activationWidth/2-1)),[a.db.LINETYPE.SOLID_OPEN,a.db.LINETYPE.DOTTED_OPEN].includes(t.type)||(d+=p(3)));const g=[s,i,n,o],u=Math.abs(l-d);t.wrap&&t.message&&(t.message=r.u.wrapLabel(t.message,r.e.getMax(u+2*X.wrapPadding,X.width),J(X)));const x=r.u.calculateTextDimensions(t.message,J(X));return{width:r.e.getMax(t.wrap?0:x.width+2*X.wrapPadding,u+2*X.wrapPadding,X.width),height:0,startx:l,stopx:d,starty:0,stopy:0,message:t.message,type:t.type,wrap:t.wrap,fromBounds:Math.min.apply(null,g),toBounds:Math.max.apply(null,g)}}(t,e,s),t.msgModel=l,l.startx&&l.stopx&&n.length>0&&n.forEach((a=>{if(o=a,l.startx===l.stopx){const a=e[t.from],s=e[t.to];o.from=r.e.getMin(a.x-l.width/2,a.x-a.width/2,o.from),o.to=r.e.getMax(s.x+l.width/2,s.x+a.width/2,o.to),o.width=r.e.getMax(o.width,Math.abs(o.to-o.from))-X.labelBoxWidth}else o.from=r.e.getMin(l.startx,o.from),o.to=r.e.getMax(l.stopx,o.to),o.width=r.e.getMax(o.width,l.width)-X.labelBoxWidth})))})),G.activations=[],r.l.debug("Loop type widths:",i),i}(b,g,0,n);q(p),U(p),W(p),z(p);let _=1,v=1;const L=[],I=[];b.forEach((function(t,e){let a,s,o;switch(t.type){case n.db.LINETYPE.NOTE:G.resetVerticalPos(),s=t.noteModel,function(t,e){G.bumpVerticalPos(X.boxMargin),e.height=X.boxMargin,e.starty=G.getVerticalPos();const a=(0,i.g)();a.x=e.startx,a.y=e.starty,a.width=e.width||X.width,a.class="note";const r=t.append("g"),s=D(r,a),n=(0,i.e)();n.x=e.startx,n.y=e.starty,n.width=a.width,n.dy="1em",n.text=e.message,n.class="noteText",n.fontFamily=X.noteFontFamily,n.fontSize=X.noteFontSize,n.fontWeight=X.noteFontWeight,n.anchor=X.noteAlign,n.textMargin=X.noteMargin,n.valign="center";const o=k(r,n),c=Math.round(o.map((t=>(t._groups||t)[0][0].getBBox().height)).reduce(((t,e)=>t+e)));s.attr("height",c+2*X.noteMargin),e.height+=c+2*X.noteMargin,G.bumpVerticalPos(c+2*X.noteMargin),e.stopy=e.starty+c+2*X.noteMargin,e.stopx=e.startx+a.width,G.insert(e.startx,e.starty,e.stopx,e.stopy),G.models.addNote(e)}(p,s);break;case n.db.LINETYPE.ACTIVE_START:G.newActivation(t,p,g);break;case n.db.LINETYPE.ACTIVE_END:!function(t,e){const a=G.endActivation(t);a.starty+18>e&&(a.starty=e-6,e+=12),B(p,a,e,X,rt(t.from.actor).length),G.insert(a.startx,e-10,a.stopx,e)}(t,G.getVerticalPos());break;case n.db.LINETYPE.LOOP_START:it(P,t,X.boxMargin,X.boxMargin+X.boxTextMargin,(t=>G.newLoop(t)));break;case n.db.LINETYPE.LOOP_END:a=G.endLoop(),V(p,a,"loop",X),G.bumpVerticalPos(a.stopy-G.getVerticalPos()),G.models.addLoop(a);break;case n.db.LINETYPE.RECT_START:it(P,t,X.boxMargin,X.boxMargin,(t=>G.newLoop(void 0,t.message)));break;case n.db.LINETYPE.RECT_END:a=G.endLoop(),I.push(a),G.models.addLoop(a),G.bumpVerticalPos(a.stopy-G.getVerticalPos());break;case n.db.LINETYPE.OPT_START:it(P,t,X.boxMargin,X.boxMargin+X.boxTextMargin,(t=>G.newLoop(t)));break;case n.db.LINETYPE.OPT_END:a=G.endLoop(),V(p,a,"opt",X),G.bumpVerticalPos(a.stopy-G.getVerticalPos()),G.models.addLoop(a);break;case n.db.LINETYPE.ALT_START:it(P,t,X.boxMargin,X.boxMargin+X.boxTextMargin,(t=>G.newLoop(t)));break;case n.db.LINETYPE.ALT_ELSE:it(P,t,X.boxMargin+X.boxTextMargin,X.boxMargin,(t=>G.addSectionToLoop(t)));break;case n.db.LINETYPE.ALT_END:a=G.endLoop(),V(p,a,"alt",X),G.bumpVerticalPos(a.stopy-G.getVerticalPos()),G.models.addLoop(a);break;case n.db.LINETYPE.PAR_START:case n.db.LINETYPE.PAR_OVER_START:it(P,t,X.boxMargin,X.boxMargin+X.boxTextMargin,(t=>G.newLoop(t))),G.saveVerticalPos();break;case n.db.LINETYPE.PAR_AND:it(P,t,X.boxMargin+X.boxTextMargin,X.boxMargin,(t=>G.addSectionToLoop(t)));break;case n.db.LINETYPE.PAR_END:a=G.endLoop(),V(p,a,"par",X),G.bumpVerticalPos(a.stopy-G.getVerticalPos()),G.models.addLoop(a);break;case n.db.LINETYPE.AUTONUMBER:_=t.message.start||_,v=t.message.step||v,t.message.visible?n.db.enableSequenceNumbers():n.db.disableSequenceNumbers();break;case n.db.LINETYPE.CRITICAL_START:it(P,t,X.boxMargin,X.boxMargin+X.boxTextMargin,(t=>G.newLoop(t)));break;case n.db.LINETYPE.CRITICAL_OPTION:it(P,t,X.boxMargin+X.boxTextMargin,X.boxMargin,(t=>G.addSectionToLoop(t)));break;case n.db.LINETYPE.CRITICAL_END:a=G.endLoop(),V(p,a,"critical",X),G.bumpVerticalPos(a.stopy-G.getVerticalPos()),G.models.addLoop(a);break;case n.db.LINETYPE.BREAK_START:it(P,t,X.boxMargin,X.boxMargin+X.boxTextMargin,(t=>G.newLoop(t)));break;case n.db.LINETYPE.BREAK_END:a=G.endLoop(),V(p,a,"break",X),G.bumpVerticalPos(a.stopy-G.getVerticalPos()),G.models.addLoop(a);break;default:try{o=t.msgModel,o.starty=G.getVerticalPos(),o.sequenceIndex=_,o.sequenceVisible=n.db.showSequenceNumbers();const a=function(t,e){G.bumpVerticalPos(10);const{startx:a,stopx:s,message:i}=e,n=r.e.splitBreaks(i).length,o=r.u.calculateTextDimensions(i,J(X)),c=o.height/n;let l;e.height+=c,G.bumpVerticalPos(c);let d=o.height-10;const h=o.width;if(a===s){l=G.getVerticalPos()+d,X.rightAngles||(d+=X.boxMargin,l=G.getVerticalPos()+d),d+=30;const t=r.e.getMax(h/2,X.width/2);G.insert(a-t,G.getVerticalPos()-10+d,s+t,G.getVerticalPos()+30+d)}else d+=X.boxMargin,l=G.getVerticalPos()+d,G.insert(a,l-10,s,l);return G.bumpVerticalPos(d),e.height+=d,e.stopy=e.starty+e.height,G.insert(e.fromBounds,e.starty,e.toBounds,e.stopy),l}(0,o);!function(t,e,a,r,s,i,n){function o(a,r){a.x<s[t.from].x?(G.insert(e.stopx-r,e.starty,e.startx,e.stopy+a.height/2+X.noteMargin),e.stopx=e.stopx+r):(G.insert(e.startx,e.starty,e.stopx+r,e.stopy+a.height/2+X.noteMargin),e.stopx=e.stopx-r)}if(i[t.to]==r){const e=s[t.to];o(e,"actor"==e.type?21:e.width/2+3),e.starty=a-e.height/2,G.bumpVerticalPos(e.height/2)}else if(n[t.from]==r){const r=s[t.from];X.mirrorActors&&function(a,r){a.x<s[t.to].x?(G.insert(e.startx-r,e.starty,e.stopx,e.stopy+a.height/2+X.noteMargin),e.startx=e.startx+r):(G.insert(e.stopx,e.starty,e.startx+r,e.stopy+a.height/2+X.noteMargin),e.startx=e.startx-r)}(r,"actor"==r.type?18:r.width/2),r.stopy=a-r.height/2,G.bumpVerticalPos(r.height/2)}else if(n[t.to]==r){const e=s[t.to];X.mirrorActors&&o(e,"actor"==e.type?21:e.width/2+3),e.stopy=a-e.height/2,G.bumpVerticalPos(e.height/2)}}(t,o,a,e,g,u,x),L.push({messageModel:o,lineStartY:a}),G.models.addMessage(o)}catch(t){r.l.error("error while drawing message",t)}}[n.db.LINETYPE.SOLID_OPEN,n.db.LINETYPE.DOTTED_OPEN,n.db.LINETYPE.SOLID,n.db.LINETYPE.DOTTED,n.db.LINETYPE.SOLID_CROSS,n.db.LINETYPE.DOTTED_CROSS,n.db.LINETYPE.SOLID_POINT,n.db.LINETYPE.DOTTED_POINT].includes(t.type)&&(_+=v)})),r.l.debug("createdActors",u),r.l.debug("destroyedActors",x),tt(p,g,m,!1),L.forEach((t=>function(t,e,a,s){const{startx:n,stopx:o,starty:c,message:l,type:d,sequenceIndex:h,sequenceVisible:p}=e,g=r.u.calculateTextDimensions(l,J(X)),u=(0,i.e)();u.x=n,u.y=c+10,u.width=o-n,u.class="messageText",u.dy="1em",u.text=l,u.fontFamily=X.messageFontFamily,u.fontSize=X.messageFontSize,u.fontWeight=X.messageFontWeight,u.anchor=X.messageAlign,u.valign="center",u.textMargin=X.wrapPadding,u.tspan=!1,k(t,u);const x=g.width;let y;n===o?y=X.rightAngles?t.append("path").attr("d",`M  ${n},${a} H ${n+r.e.getMax(X.width/2,x/2)} V ${a+25} H ${n}`):t.append("path").attr("d","M "+n+","+a+" C "+(n+60)+","+(a-10)+" "+(n+60)+","+(a+30)+" "+n+","+(a+20)):(y=t.append("line"),y.attr("x1",n),y.attr("y1",a),y.attr("x2",o),y.attr("y2",a)),d===s.db.LINETYPE.DOTTED||d===s.db.LINETYPE.DOTTED_CROSS||d===s.db.LINETYPE.DOTTED_POINT||d===s.db.LINETYPE.DOTTED_OPEN?(y.style("stroke-dasharray","3, 3"),y.attr("class","messageLine1")):y.attr("class","messageLine0");let m="";X.arrowMarkerAbsolute&&(m=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,m=m.replace(/\(/g,"\\("),m=m.replace(/\)/g,"\\)")),y.attr("stroke-width",2),y.attr("stroke","none"),y.style("fill","none"),d!==s.db.LINETYPE.SOLID&&d!==s.db.LINETYPE.DOTTED||y.attr("marker-end","url("+m+"#arrowhead)"),d!==s.db.LINETYPE.SOLID_POINT&&d!==s.db.LINETYPE.DOTTED_POINT||y.attr("marker-end","url("+m+"#filled-head)"),d!==s.db.LINETYPE.SOLID_CROSS&&d!==s.db.LINETYPE.DOTTED_CROSS||y.attr("marker-end","url("+m+"#crosshead)"),(p||X.showSequenceNumbers)&&(y.attr("marker-start","url("+m+"#sequencenumber)"),t.append("text").attr("x",n).attr("y",a+4).attr("font-family","sans-serif").attr("font-size","12px").attr("text-anchor","middle").attr("class","sequenceNumber").text(h))}(p,t.messageModel,t.lineStartY,n))),X.mirrorActors&&tt(p,g,m,!0),I.forEach((t=>F(p,t))),M(p,g,m,X),G.models.boxes.forEach((function(t){t.height=G.getVerticalPos()-t.y,G.insert(t.x,t.y,t.x+t.width,t.height),t.startx=t.x,t.starty=t.y,t.stopx=t.startx+t.width,t.stopy=t.starty+t.height,t.stroke="rgb(0,0,0, 0.5)",C(p,t,X)})),T&&G.bumpVerticalPos(X.boxMargin);const N=et(p,g,m,h),{bounds:A}=G.getBounds();let O=A.stopy-A.starty;O<N.maxHeight&&(O=N.maxHeight);let S=O+2*X.diagramMarginY;X.mirrorActors&&(S=S-X.boxMargin+X.bottomMarginAdj);let R=A.stopx-A.startx;R<N.maxWidth&&(R=N.maxWidth);const Y=R+2*X.diagramMarginX;f&&p.append("text").text(f).attr("x",(A.stopx-A.startx)/2-2*X.diagramMarginX).attr("y",-25),(0,r.i)(p,S,Y,X.useMaxWidth);const $=f?40:0;p.attr("viewBox",A.startx-X.diagramMarginX+" -"+(X.diagramMarginY+$)+" "+Y+" "+(S+$)),r.l.debug("models:",G.models)}},styles:t=>`.actor {\n    stroke: ${t.actorBorder};\n    fill: ${t.actorBkg};\n  }\n\n  text.actor > tspan {\n    fill: ${t.actorTextColor};\n    stroke: none;\n  }\n\n  .actor-line {\n    stroke: ${t.actorLineColor};\n  }\n\n  .messageLine0 {\n    stroke-width: 1.5;\n    stroke-dasharray: none;\n    stroke: ${t.signalColor};\n  }\n\n  .messageLine1 {\n    stroke-width: 1.5;\n    stroke-dasharray: 2, 2;\n    stroke: ${t.signalColor};\n  }\n\n  #arrowhead path {\n    fill: ${t.signalColor};\n    stroke: ${t.signalColor};\n  }\n\n  .sequenceNumber {\n    fill: ${t.sequenceNumberColor};\n  }\n\n  #sequencenumber {\n    fill: ${t.signalColor};\n  }\n\n  #crosshead path {\n    fill: ${t.signalColor};\n    stroke: ${t.signalColor};\n  }\n\n  .messageText {\n    fill: ${t.signalTextColor};\n    stroke: none;\n  }\n\n  .labelBox {\n    stroke: ${t.labelBoxBorderColor};\n    fill: ${t.labelBoxBkgColor};\n  }\n\n  .labelText, .labelText > tspan {\n    fill: ${t.labelTextColor};\n    stroke: none;\n  }\n\n  .loopText, .loopText > tspan {\n    fill: ${t.loopTextColor};\n    stroke: none;\n  }\n\n  .loopLine {\n    stroke-width: 2px;\n    stroke-dasharray: 2, 2;\n    stroke: ${t.labelBoxBorderColor};\n    fill: ${t.labelBoxBorderColor};\n  }\n\n  .note {\n    //stroke: #decc93;\n    stroke: ${t.noteBorderColor};\n    fill: ${t.noteBkgColor};\n  }\n\n  .noteText, .noteText > tspan {\n    fill: ${t.noteTextColor};\n    stroke: none;\n  }\n\n  .activation0 {\n    fill: ${t.activationBkgColor};\n    stroke: ${t.activationBorderColor};\n  }\n\n  .activation1 {\n    fill: ${t.activationBkgColor};\n    stroke: ${t.activationBorderColor};\n  }\n\n  .activation2 {\n    fill: ${t.activationBkgColor};\n    stroke: ${t.activationBorderColor};\n  }\n\n  .actorPopupMenu {\n    position: absolute;\n  }\n\n  .actorPopupMenuPanel {\n    position: absolute;\n    fill: ${t.actorBkg};\n    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);\n    filter: drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));\n}\n  .actor-man line {\n    stroke: ${t.actorBorder};\n    fill: ${t.actorBkg};\n  }\n  .actor-man circle, line {\n    stroke: ${t.actorBorder};\n    fill: ${t.actorBkg};\n    stroke-width: 2px;\n  }\n`,init:({wrap:t})=>{w.setWrap(t)}}},26476:(t,e,a)=>{a.d(e,{a:()=>n,b:()=>l,c:()=>c,d:()=>i,e:()=>h,f:()=>o,g:()=>d});var r=a(7608),s=a(24028);const i=(t,e)=>{const a=t.append("rect");if(a.attr("x",e.x),a.attr("y",e.y),a.attr("fill",e.fill),a.attr("stroke",e.stroke),a.attr("width",e.width),a.attr("height",e.height),void 0!==e.rx&&a.attr("rx",e.rx),void 0!==e.ry&&a.attr("ry",e.ry),void 0!==e.attrs)for(const t in e.attrs)a.attr(t,e.attrs[t]);return void 0!==e.class&&a.attr("class",e.class),a},n=(t,e)=>{const a={x:e.startx,y:e.starty,width:e.stopx-e.startx,height:e.stopy-e.starty,fill:e.fill,stroke:e.stroke,class:"rect"};i(t,a).lower()},o=(t,e)=>{const a=e.text.replace(s.H," "),r=t.append("text");r.attr("x",e.x),r.attr("y",e.y),r.attr("class","legend"),r.style("text-anchor",e.anchor),void 0!==e.class&&r.attr("class",e.class);const i=r.append("tspan");return i.attr("x",e.x+2*e.textMargin),i.text(a),r},c=(t,e,a,s)=>{const i=t.append("image");i.attr("x",e),i.attr("y",a);const n=(0,r.Nm)(s);i.attr("xlink:href",n)},l=(t,e,a,s)=>{const i=t.append("use");i.attr("x",e),i.attr("y",a);const n=(0,r.Nm)(s);i.attr("xlink:href",`#${n}`)},d=()=>({x:0,y:0,width:100,height:100,fill:"#EDF2AE",stroke:"#666",anchor:"start",rx:0,ry:0}),h=()=>({x:0,y:0,width:100,height:100,"text-anchor":"start",style:"#666",textMargin:0,rx:0,ry:0,tspan:!0})}}]);
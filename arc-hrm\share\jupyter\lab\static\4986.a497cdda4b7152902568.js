"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[4986],{54986:(t,e,i)=>{i.r(e);i.d(e,{BidiSpan:()=>we,BlockInfo:()=>Fi,BlockType:()=>Mt,Decoration:()=>kt,Direction:()=>le,EditorView:()=>Os,GutterMarker:()=>Mn,MatchDecorator:()=>wo,RectangleMarker:()=>$s,ViewPlugin:()=>Qt,ViewUpdate:()=>re,WidgetType:()=>xt,__test:()=>tr,closeHoverTooltips:()=>pn,crosshairCursor:()=>$o,drawSelection:()=>so,dropCursor:()=>po,getPanel:()=>vn,getTooltip:()=>fn,gutter:()=>Dn,gutterLineClass:()=>kn,gutters:()=>On,hasHoverTooltips:()=>dn,highlightActiveLine:()=>Lo,highlightActiveLineGutter:()=>$n,highlightSpecialChars:()=>ko,highlightTrailingWhitespace:()=>Zn,highlightWhitespace:()=>Qn,hoverTooltip:()=>cn,keymap:()=>Ws,layer:()=>to,lineNumberMarkers:()=>Nn,lineNumbers:()=>Kn,logException:()=>_t,panels:()=>wn,placeholder:()=>No,rectangularSelection:()=>qo,repositionTooltips:()=>gn,runScopeHandlers:()=>Is,scrollPastEnd:()=>Bo,showPanel:()=>xn,showTooltip:()=>sn,tooltips:()=>Xo});var s=i(37496);var o=i(67737);var n={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"};var r={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'};var l=typeof navigator!="undefined"&&/Chrome\/(\d+)/.exec(navigator.userAgent);var h=typeof navigator!="undefined"&&/Gecko\/\d+/.test(navigator.userAgent);var a=typeof navigator!="undefined"&&/Mac/.test(navigator.platform);var c=typeof navigator!="undefined"&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent);var f=a||l&&+l[1]<57;for(var d=0;d<10;d++)n[48+d]=n[96+d]=String(d);for(var d=1;d<=24;d++)n[d+111]="F"+d;for(var d=65;d<=90;d++){n[d]=String.fromCharCode(d+32);r[d]=String.fromCharCode(d)}for(var u in n)if(!r.hasOwnProperty(u))r[u]=n[u];function p(t){var e=f&&(t.ctrlKey||t.altKey||t.metaKey)||c&&t.shiftKey&&t.key&&t.key.length==1||t.key=="Unidentified";var i=!e&&t.key||(t.shiftKey?r:n)[t.keyCode]||t.key||"Unidentified";if(i=="Esc")i="Escape";if(i=="Del")i="Delete";if(i=="Left")i="ArrowLeft";if(i=="Up")i="ArrowUp";if(i=="Right")i="ArrowRight";if(i=="Down")i="ArrowDown";return i}function g(t){let e;if(t.nodeType==11){e=t.getSelection?t:t.ownerDocument}else{e=t}return e.getSelection()}function m(t,e){return e?t==e||t.contains(e.nodeType!=1?e.parentNode:e):false}function w(t){let e=t.activeElement;while(e&&e.shadowRoot)e=e.shadowRoot.activeElement;return e}function v(t,e){if(!e.anchorNode)return false;try{return m(t,e.anchorNode)}catch(i){return false}}function b(t){if(t.nodeType==3)return L(t,0,t.nodeValue.length).getClientRects();else if(t.nodeType==1)return t.getClientRects();else return[]}function y(t,e,i,s){return i?x(t,e,i,s,-1)||x(t,e,i,s,1):false}function S(t){for(var e=0;;e++){t=t.previousSibling;if(!t)return e}}function x(t,e,i,s,o){for(;;){if(t==i&&e==s)return true;if(e==(o<0?0:M(t))){if(t.nodeName=="DIV")return false;let i=t.parentNode;if(!i||i.nodeType!=1)return false;e=S(t)+(o<0?0:1);t=i}else if(t.nodeType==1){t=t.childNodes[e+(o<0?-1:0)];if(t.nodeType==1&&t.contentEditable=="false")return false;e=o<0?M(t):0}else{return false}}}function M(t){return t.nodeType==3?t.nodeValue.length:t.childNodes.length}const k={left:0,right:0,top:0,bottom:0};function C(t,e){let i=e?t.left:t.right;return{left:i,right:i,top:t.top,bottom:t.bottom}}function A(t){return{left:0,right:t.innerWidth,top:0,bottom:t.innerHeight}}function D(t,e,i,s,o,n,r,l){let h=t.ownerDocument,a=h.defaultView||window;for(let c=t;c;){if(c.nodeType==1){let t,f=c==h.body;if(f){t=A(a)}else{if(c.scrollHeight<=c.clientHeight&&c.scrollWidth<=c.clientWidth){c=c.assignedSlot||c.parentNode;continue}let e=c.getBoundingClientRect();t={left:e.left,right:e.left+c.clientWidth,top:e.top,bottom:e.top+c.clientHeight}}let d=0,u=0;if(o=="nearest"){if(e.top<t.top){u=-(t.top-e.top+r);if(i>0&&e.bottom>t.bottom+u)u=e.bottom-t.bottom+u+r}else if(e.bottom>t.bottom){u=e.bottom-t.bottom+r;if(i<0&&e.top-u<t.top)u=-(t.top+u-e.top+r)}}else{let s=e.bottom-e.top,n=t.bottom-t.top;let l=o=="center"&&s<=n?e.top+s/2-n/2:o=="start"||o=="center"&&i<0?e.top-r:e.bottom-n+r;u=l-t.top}if(s=="nearest"){if(e.left<t.left){d=-(t.left-e.left+n);if(i>0&&e.right>t.right+d)d=e.right-t.right+d+n}else if(e.right>t.right){d=e.right-t.right+n;if(i<0&&e.left<t.left+d)d=-(t.left+d-e.left+n)}}else{let i=s=="center"?e.left+(e.right-e.left)/2-(t.right-t.left)/2:s=="start"==l?e.left-n:e.right-(t.right-t.left)+n;d=i-t.left}if(d||u){if(f){a.scrollBy(d,u)}else{let t=0,i=0;if(u){let t=c.scrollTop;c.scrollTop+=u;i=c.scrollTop-t}if(d){let e=c.scrollLeft;c.scrollLeft+=d;t=c.scrollLeft-e}e={left:e.left-t,top:e.top-i,right:e.right-t,bottom:e.bottom-i};if(t&&Math.abs(t-d)<1)s="nearest";if(i&&Math.abs(i-u)<1)o="nearest"}}if(f)break;c=c.assignedSlot||c.parentNode}else if(c.nodeType==11){c=c.host}else{break}}}function T(t){let e=t.ownerDocument;for(let i=t.parentNode;i;){if(i==e.body){break}else if(i.nodeType==1){if(i.scrollHeight>i.clientHeight||i.scrollWidth>i.clientWidth)return i;i=i.assignedSlot||i.parentNode}else if(i.nodeType==11){i=i.host}else{break}}return null}class O{constructor(){this.anchorNode=null;this.anchorOffset=0;this.focusNode=null;this.focusOffset=0}eq(t){return this.anchorNode==t.anchorNode&&this.anchorOffset==t.anchorOffset&&this.focusNode==t.focusNode&&this.focusOffset==t.focusOffset}setRange(t){this.set(t.anchorNode,t.anchorOffset,t.focusNode,t.focusOffset)}set(t,e,i,s){this.anchorNode=t;this.anchorOffset=e;this.focusNode=i;this.focusOffset=s}}let E=null;function R(t){if(t.setActive)return t.setActive();if(E)return t.focus(E);let e=[];for(let i=t;i;i=i.parentNode){e.push(i,i.scrollTop,i.scrollLeft);if(i==i.ownerDocument)break}t.focus(E==null?{get preventScroll(){E={preventScroll:true};return true}}:undefined);if(!E){E=false;for(let t=0;t<e.length;){let i=e[t++],s=e[t++],o=e[t++];if(i.scrollTop!=s)i.scrollTop=s;if(i.scrollLeft!=o)i.scrollLeft=o}}}let B;function L(t,e,i=e){let s=B||(B=document.createRange());s.setEnd(t,i);s.setStart(t,e);return s}function H(t,e,i){let s={key:e,code:e,keyCode:i,which:i,cancelable:true};let o=new KeyboardEvent("keydown",s);o.synthetic=true;t.dispatchEvent(o);let n=new KeyboardEvent("keyup",s);n.synthetic=true;t.dispatchEvent(n);return o.defaultPrevented||n.defaultPrevented}function P(t){while(t){if(t&&(t.nodeType==9||t.nodeType==11&&t.host))return t;t=t.assignedSlot||t.parentNode}return null}function V(t){while(t.attributes.length)t.removeAttributeNode(t.attributes[0])}function N(t,e){let i=e.focusNode,s=e.focusOffset;if(!i||e.anchorNode!=i||e.anchorOffset!=s)return false;for(;;){if(s){if(i.nodeType!=1)return false;let t=i.childNodes[s-1];if(t.contentEditable=="false")s--;else{i=t;s=M(i)}}else if(i==t){return true}else{s=S(i);i=i.parentNode}}}class W{constructor(t,e,i=true){this.node=t;this.offset=e;this.precise=i}static before(t,e){return new W(t.parentNode,S(t),e)}static after(t,e){return new W(t.parentNode,S(t)+1,e)}}const F=[];class z{constructor(){this.parent=null;this.dom=null;this.dirty=2}get overrideDOMText(){return null}get posAtStart(){return this.parent?this.parent.posBefore(this):0}get posAtEnd(){return this.posAtStart+this.length}posBefore(t){let e=this.posAtStart;for(let i of this.children){if(i==t)return e;e+=i.length+i.breakAfter}throw new RangeError("Invalid child in posBefore")}posAfter(t){return this.posBefore(t)+t.length}coordsAt(t,e){return null}sync(t,e){if(this.dirty&2){let i=this.dom;let s=null,o;for(let n of this.children){if(n.dirty){if(!n.dom&&(o=s?s.nextSibling:i.firstChild)){let t=z.get(o);if(!t||!t.parent&&t.canReuseDOM(n))n.reuseDOM(o)}n.sync(t,e);n.dirty=0}o=s?s.nextSibling:i.firstChild;if(e&&!e.written&&e.node==i&&o!=n.dom)e.written=true;if(n.dom.parentNode==i){while(o&&o!=n.dom)o=I(o)}else{i.insertBefore(n.dom,o)}s=n.dom}o=s?s.nextSibling:i.firstChild;if(o&&e&&e.node==i)e.written=true;while(o)o=I(o)}else if(this.dirty&1){for(let i of this.children)if(i.dirty){i.sync(t,e);i.dirty=0}}}reuseDOM(t){}localPosFromDOM(t,e){let i;if(t==this.dom){i=this.dom.childNodes[e]}else{let s=M(t)==0?0:e==0?-1:1;for(;;){let e=t.parentNode;if(e==this.dom)break;if(s==0&&e.firstChild!=e.lastChild){if(t==e.firstChild)s=-1;else s=1}t=e}if(s<0)i=t;else i=t.nextSibling}if(i==this.dom.firstChild)return 0;while(i&&!z.get(i))i=i.nextSibling;if(!i)return this.length;for(let s=0,o=0;;s++){let t=this.children[s];if(t.dom==i)return o;o+=t.length+t.breakAfter}}domBoundsAround(t,e,i=0){let s=-1,o=-1,n=-1,r=-1;for(let l=0,h=i,a=i;l<this.children.length;l++){let i=this.children[l],c=h+i.length;if(h<t&&c>e)return i.domBoundsAround(t,e,h);if(c>=t&&s==-1){s=l;o=h}if(h>e&&i.dom.parentNode==this.dom){n=l;r=a;break}a=c;h=c+i.breakAfter}return{from:o,to:r<0?i+this.length:r,startDOM:(s?this.children[s-1].dom.nextSibling:null)||this.dom.firstChild,endDOM:n<this.children.length&&n>=0?this.children[n].dom:null}}markDirty(t=false){this.dirty|=2;this.markParentsDirty(t)}markParentsDirty(t){for(let e=this.parent;e;e=e.parent){if(t)e.dirty|=2;if(e.dirty&1)return;e.dirty|=1;t=false}}setParent(t){if(this.parent!=t){this.parent=t;if(this.dirty)this.markParentsDirty(true)}}setDOM(t){if(this.dom)this.dom.cmView=null;this.dom=t;t.cmView=this}get rootView(){for(let t=this;;){let e=t.parent;if(!e)return t;t=e}}replaceChildren(t,e,i=F){this.markDirty();for(let s=t;s<e;s++){let t=this.children[s];if(t.parent==this)t.destroy()}this.children.splice(t,e-t,...i);for(let s=0;s<i.length;s++)i[s].setParent(this)}ignoreMutation(t){return false}ignoreEvent(t){return false}childCursor(t=this.length){return new K(this.children,t,this.children.length)}childPos(t,e=1){return this.childCursor().findPos(t,e)}toString(){let t=this.constructor.name.replace("View","");return t+(this.children.length?"("+this.children.join()+")":this.length?"["+(t=="Text"?this.text:this.length)+"]":"")+(this.breakAfter?"#":"")}static get(t){return t.cmView}get isEditable(){return true}get isWidget(){return false}get isHidden(){return false}merge(t,e,i,s,o,n){return false}become(t){return false}canReuseDOM(t){return t.constructor==this.constructor}getSide(){return 0}destroy(){this.parent=null}}z.prototype.breakAfter=0;function I(t){let e=t.nextSibling;t.parentNode.removeChild(t);return e}class K{constructor(t,e,i){this.children=t;this.pos=e;this.i=i;this.off=0}findPos(t,e=1){for(;;){if(t>this.pos||t==this.pos&&(e>0||this.i==0||this.children[this.i-1].breakAfter)){this.off=t-this.pos;return this}let i=this.children[--this.i];this.pos-=i.length+i.breakAfter}}}function q(t,e,i,s,o,n,r,l,h){let{children:a}=t;let c=a.length?a[e]:null;let f=n.length?n[n.length-1]:null;let d=f?f.breakAfter:r;if(e==s&&c&&!r&&!d&&n.length<2&&c.merge(i,o,n.length?f:null,i==0,l,h))return;if(s<a.length){let t=a[s];if(t&&o<t.length){if(e==s){t=t.split(o);o=0}if(!d&&f&&t.merge(0,o,f,true,0,h)){n[n.length-1]=t}else{if(o)t.merge(0,o,null,false,0,h);n.push(t)}}else if(t===null||t===void 0?void 0:t.breakAfter){if(f)f.breakAfter=1;else r=1}s++}if(c){c.breakAfter=r;if(i>0){if(!r&&n.length&&c.merge(i,c.length,n[0],false,l,0)){c.breakAfter=n.shift().breakAfter}else if(i<c.length||c.children.length&&c.children[c.children.length-1].length==0){c.merge(i,c.length,null,false,l,0)}e++}}while(e<s&&n.length){if(a[s-1].become(n[n.length-1])){s--;n.pop();h=n.length?0:l}else if(a[e].become(n[0])){e++;n.shift();l=n.length?0:h}else{break}}if(!n.length&&e&&s<a.length&&!a[e-1].breakAfter&&a[s].merge(0,0,a[e-1],false,l,h))e--;if(e<s||n.length)t.replaceChildren(e,s,n)}function G(t,e,i,s,o,n){let r=t.childCursor();let{i:l,off:h}=r.findPos(i,1);let{i:a,off:c}=r.findPos(e,-1);let f=e-i;for(let d of s)f+=d.length;t.length+=f;q(t,a,c,l,h,s,0,o,n)}let j=typeof navigator!="undefined"?navigator:{userAgent:"",vendor:"",platform:""};let $=typeof document!="undefined"?document:{documentElement:{style:{}}};const _=/Edge\/(\d+)/.exec(j.userAgent);const U=/MSIE \d/.test(j.userAgent);const X=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(j.userAgent);const Y=!!(U||X||_);const Q=!Y&&/gecko\/(\d+)/i.test(j.userAgent);const J=!Y&&/Chrome\/(\d+)/.exec(j.userAgent);const Z="webkitFontSmoothing"in $.documentElement.style;const tt=!Y&&/Apple Computer/.test(j.vendor);const et=tt&&(/Mobile\/\w+/.test(j.userAgent)||j.maxTouchPoints>2);var it={mac:et||/Mac/.test(j.platform),windows:/Win/.test(j.platform),linux:/Linux|X11/.test(j.platform),ie:Y,ie_version:U?$.documentMode||6:X?+X[1]:_?+_[1]:0,gecko:Q,gecko_version:Q?+(/Firefox\/(\d+)/.exec(j.userAgent)||[0,0])[1]:0,chrome:!!J,chrome_version:J?+J[1]:0,ios:et,android:/Android\b/.test(j.userAgent),webkit:Z,safari:tt,webkit_version:Z?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0,tabSize:$.documentElement.style.tabSize!=null?"tab-size":"-moz-tab-size"};const st=256;class ot extends z{constructor(t){super();this.text=t}get length(){return this.text.length}createDOM(t){this.setDOM(t||document.createTextNode(this.text))}sync(t,e){if(!this.dom)this.createDOM();if(this.dom.nodeValue!=this.text){if(e&&e.node==this.dom)e.written=true;this.dom.nodeValue=this.text}}reuseDOM(t){if(t.nodeType==3)this.createDOM(t)}merge(t,e,i){if(i&&(!(i instanceof ot)||this.length-(e-t)+i.length>st))return false;this.text=this.text.slice(0,t)+(i?i.text:"")+this.text.slice(e);this.markDirty();return true}split(t){let e=new ot(this.text.slice(t));this.text=this.text.slice(0,t);this.markDirty();return e}localPosFromDOM(t,e){return t==this.dom?e:e?this.text.length:0}domAtPos(t){return new W(this.dom,t)}domBoundsAround(t,e,i){return{from:i,to:i+this.length,startDOM:this.dom,endDOM:this.dom.nextSibling}}coordsAt(t,e){return rt(this.dom,t,e)}}class nt extends z{constructor(t,e=[],i=0){super();this.mark=t;this.children=e;this.length=i;for(let s of e)s.setParent(this)}setAttrs(t){V(t);if(this.mark.class)t.className=this.mark.class;if(this.mark.attrs)for(let e in this.mark.attrs)t.setAttribute(e,this.mark.attrs[e]);return t}reuseDOM(t){if(t.nodeName==this.mark.tagName.toUpperCase()){this.setDOM(t);this.dirty|=4|2}}sync(t,e){if(!this.dom)this.setDOM(this.setAttrs(document.createElement(this.mark.tagName)));else if(this.dirty&4)this.setAttrs(this.dom);super.sync(t,e)}merge(t,e,i,s,o,n){if(i&&(!(i instanceof nt&&i.mark.eq(this.mark))||t&&o<=0||e<this.length&&n<=0))return false;G(this,t,e,i?i.children:[],o-1,n-1);this.markDirty();return true}split(t){let e=[],i=0,s=-1,o=0;for(let r of this.children){let n=i+r.length;if(n>t)e.push(i<t?r.split(t-i):r);if(s<0&&i>=t)s=o;i=n;o++}let n=this.length-t;this.length=t;if(s>-1){this.children.length=s;this.markDirty()}return new nt(this.mark,e,n)}domAtPos(t){return gt(this,t)}coordsAt(t,e){return wt(this,t,e)}}function rt(t,e,i){let s=t.nodeValue.length;if(e>s)e=s;let o=e,n=e,r=0;if(e==0&&i<0||e==s&&i>=0){if(!(it.chrome||it.gecko)){if(e){o--;r=1}else if(n<s){n++;r=-1}}}else{if(i<0)o--;else if(n<s)n++}let l=L(t,o,n).getClientRects();if(!l.length)return k;let h=l[(r?r<0:i>=0)?0:l.length-1];if(it.safari&&!r&&h.width==0)h=Array.prototype.find.call(l,(t=>t.width))||h;return r?C(h,r<0):h||null}class lt extends z{constructor(t,e,i){super();this.widget=t;this.length=e;this.side=i;this.prevWidget=null}static create(t,e,i){return new(t.customView||lt)(t,e,i)}split(t){let e=lt.create(this.widget,this.length-t,this.side);this.length-=t;return e}sync(t){if(!this.dom||!this.widget.updateDOM(this.dom,t)){if(this.dom&&this.prevWidget)this.prevWidget.destroy(this.dom);this.prevWidget=null;this.setDOM(this.widget.toDOM(t));this.dom.contentEditable="false"}}getSide(){return this.side}merge(t,e,i,s,o,n){if(i&&(!(i instanceof lt)||!this.widget.compare(i.widget)||t>0&&o<=0||e<this.length&&n<=0))return false;this.length=t+(i?i.length:0)+(this.length-e);return true}become(t){if(t instanceof lt&&t.side==this.side&&this.widget.constructor==t.widget.constructor){if(!this.widget.compare(t.widget))this.markDirty(true);if(this.dom&&!this.prevWidget)this.prevWidget=this.widget;this.widget=t.widget;this.length=t.length;return true}return false}ignoreMutation(){return true}ignoreEvent(t){return this.widget.ignoreEvent(t)}get overrideDOMText(){if(this.length==0)return s.Text.empty;let t=this;while(t.parent)t=t.parent;let{view:e}=t,i=e&&e.state.doc,o=this.posAtStart;return i?i.slice(o,o+this.length):s.Text.empty}domAtPos(t){return(this.length?t==0:this.side>0)?W.before(this.dom):W.after(this.dom,t==this.length)}domBoundsAround(){return null}coordsAt(t,e){let i=this.dom.getClientRects(),s=null;if(!i.length)return k;for(let o=t>0?i.length-1:0;;o+=t>0?-1:1){s=i[o];if(t>0?o==0:o==i.length-1||s.top<s.bottom)break}return this.length?s:C(s,this.side>0)}get isEditable(){return false}get isWidget(){return true}get isHidden(){return this.widget.isHidden}destroy(){super.destroy();if(this.dom)this.widget.destroy(this.dom)}}class ht extends lt{domAtPos(t){let{topView:e,text:i}=this.widget;if(!e)return new W(i,Math.min(t,i.nodeValue.length));return at(t,0,e,i,this.length-e.length,((t,e)=>t.domAtPos(e)),((t,e)=>new W(t,Math.min(e,t.nodeValue.length))))}sync(){this.setDOM(this.widget.toDOM())}localPosFromDOM(t,e){let{topView:i,text:s}=this.widget;if(!i)return Math.min(e,this.length);return ft(t,e,i,s,this.length-i.length)}ignoreMutation(){return false}get overrideDOMText(){return null}coordsAt(t,e){let{topView:i,text:s}=this.widget;if(!i)return rt(s,t,e);return at(t,e,i,s,this.length-i.length,((t,e,i)=>t.coordsAt(e,i)),((t,e,i)=>rt(t,e,i)))}destroy(){var t;super.destroy();(t=this.widget.topView)===null||t===void 0?void 0:t.destroy()}get isEditable(){return true}canReuseDOM(){return true}}function at(t,e,i,s,o,n,r){if(i instanceof nt){for(let l=i.dom.firstChild;l;l=l.nextSibling){let i=z.get(l);if(!i){let i=ct(t,e,l,r);if(typeof i!="number")return i;t=i}else{let h=m(l,s);let a=i.length+(h?o:0);if(t<a||t==a&&i.getSide()<=0)return h?at(t,e,i,s,o,n,r):n(i,t,e);t-=a}}return n(i,i.length,-1)}else if(i.dom==s){return r(s,t,e)}else{return n(i,t,e)}}function ct(t,e,i,s){if(i.nodeType==3){let o=i.nodeValue.length;if(t<=o)return s(i,t,e);t-=o}else if(i.nodeType==1&&i.contentEditable!="false"){for(let o=i.firstChild;o;o=o.nextSibling){let i=ct(t,e,o,s);if(typeof i!="number")return i;t=i}}return t}function ft(t,e,i,s,o){if(i instanceof nt){let n=0;for(let r=i.dom.firstChild;r;r=r.nextSibling){let i=z.get(r);if(i){let l=m(r,s);if(m(r,t))return n+(l?ft(t,e,i,s,o):i.localPosFromDOM(t,e));n+=i.length+(l?o:0)}else{let i=dt(t,e,r);if(i.result!=null)return n+i.result;n+=i.size}}}else if(i.dom==s){return Math.min(e,s.nodeValue.length)}return i.localPosFromDOM(t,e)}function dt(t,e,i){if(i.nodeType==3){return t==i?{result:e}:{size:i.nodeValue.length}}else if(i.nodeType==1&&i.contentEditable!="false"){let s=0;for(let o=i.firstChild,n=0;;o=o.nextSibling,n++){if(t==i&&n==e)return{result:s};if(!o)return{size:s};let r=dt(t,e,o);if(r.result!=null)return{result:e+r.result};s+=r.size}}else{return i.contains(t)?{result:0}:{size:0}}}class ut extends z{constructor(t){super();this.side=t}get length(){return 0}merge(){return false}become(t){return t instanceof ut&&t.side==this.side}split(){return new ut(this.side)}sync(){if(!this.dom){let t=document.createElement("img");t.className="cm-widgetBuffer";t.setAttribute("aria-hidden","true");this.setDOM(t)}}getSide(){return this.side}domAtPos(t){return this.side>0?W.before(this.dom):W.after(this.dom)}localPosFromDOM(){return 0}domBoundsAround(){return null}coordsAt(t){let e=this.dom.getBoundingClientRect();let i=pt(this,this.side>0?-1:1);return i&&i.top<e.bottom&&i.bottom>e.top?{left:e.left,right:e.right,top:i.top,bottom:i.bottom}:e}get overrideDOMText(){return s.Text.empty}get isHidden(){return true}}ot.prototype.children=lt.prototype.children=ut.prototype.children=F;function pt(t,e){let i=t.parent,s=i?i.children.indexOf(t):-1;while(i&&s>=0){if(e<0?s>0:s<i.children.length){let t=i.children[s+e];if(t instanceof ot){let i=t.coordsAt(e<0?t.length:0,e);if(i)return i}s+=e}else if(i instanceof nt&&i.parent){s=i.parent.children.indexOf(i)+(e<0?0:1);i=i.parent}else{let t=i.dom.lastChild;if(t&&t.nodeName=="BR")return t.getClientRects()[0];break}}return undefined}function gt(t,e){let i=t.dom,{children:s}=t,o=0;for(let n=0;o<s.length;o++){let t=s[o],r=n+t.length;if(r==n&&t.getSide()<=0)continue;if(e>n&&e<r&&t.dom.parentNode==i)return t.domAtPos(e-n);if(e<=n)break;n=r}for(let n=o;n>0;n--){let t=s[n-1];if(t.dom.parentNode==i)return t.domAtPos(t.length)}for(let n=o;n<s.length;n++){let t=s[n];if(t.dom.parentNode==i)return t.domAtPos(0)}return new W(i,0)}function mt(t,e,i){let s,{children:o}=t;if(i>0&&e instanceof nt&&o.length&&(s=o[o.length-1])instanceof nt&&s.mark.eq(e.mark)){mt(s,e.children[0],i-1)}else{o.push(e);e.setParent(t)}t.length+=e.length}function wt(t,e,i){let s=null,o=-1,n=null,r=-1;function l(t,e){for(let h=0,a=0;h<t.children.length&&a<=e;h++){let c=t.children[h],f=a+c.length;if(f>=e){if(c.children.length){l(c,e-a)}else if((!n||n instanceof ut&&i>0)&&(f>e||a==f&&c.getSide()>0)){n=c;r=e-a}else if(a<e||a==f&&c.getSide()<0){s=c;o=e-a}}a=f}}l(t,e);let h=(i<0?s:n)||s||n;if(h)return h.coordsAt(Math.max(0,h==s?o:r),i);return vt(t)}function vt(t){let e=t.dom.lastChild;if(!e)return t.dom.getBoundingClientRect();let i=b(e);return i[i.length-1]||null}function bt(t,e){for(let i in t){if(i=="class"&&e.class)e.class+=" "+t.class;else if(i=="style"&&e.style)e.style+=";"+t.style;else e[i]=t[i]}return e}function yt(t,e){if(t==e)return true;if(!t||!e)return false;let i=Object.keys(t),s=Object.keys(e);if(i.length!=s.length)return false;for(let o of i){if(s.indexOf(o)==-1||t[o]!==e[o])return false}return true}function St(t,e,i){let s=null;if(e)for(let o in e)if(!(i&&o in i))t.removeAttribute(s=o);if(i)for(let o in i)if(!(e&&e[o]==i[o]))t.setAttribute(s=o,i[o]);return!!s}class xt{eq(t){return false}updateDOM(t,e){return false}compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}get estimatedHeight(){return-1}ignoreEvent(t){return true}get customView(){return null}get isHidden(){return false}destroy(t){}}var Mt=function(t){t[t["Text"]=0]="Text";t[t["WidgetBefore"]=1]="WidgetBefore";t[t["WidgetAfter"]=2]="WidgetAfter";t[t["WidgetRange"]=3]="WidgetRange";return t}(Mt||(Mt={}));class kt extends s.RangeValue{constructor(t,e,i,s){super();this.startSide=t;this.endSide=e;this.widget=i;this.spec=s}get heightRelevant(){return false}static mark(t){return new Ct(t)}static widget(t){let e=t.side||0,i=!!t.block;e+=i?e>0?3e8:-4e8:e>0?1e8:-1e8;return new Dt(t,e,e,i,t.widget||null,false)}static replace(t){let e=!!t.block,i,s;if(t.isBlockGap){i=-5e8;s=4e8}else{let{start:o,end:n}=Tt(t,e);i=(o?e?-3e8:-1:5e8)-1;s=(n?e?2e8:1:-6e8)+1}return new Dt(t,i,s,e,t.widget||null,true)}static line(t){return new At(t)}static set(t,e=false){return s.RangeSet.of(t,e)}hasHeight(){return this.widget?this.widget.estimatedHeight>-1:false}}kt.none=s.RangeSet.empty;class Ct extends kt{constructor(t){let{start:e,end:i}=Tt(t);super(e?-1:5e8,i?1:-6e8,null,t);this.tagName=t.tagName||"span";this.class=t.class||"";this.attrs=t.attributes||null}eq(t){return this==t||t instanceof Ct&&this.tagName==t.tagName&&this.class==t.class&&yt(this.attrs,t.attrs)}range(t,e=t){if(t>=e)throw new RangeError("Mark decorations may not be empty");return super.range(t,e)}}Ct.prototype.point=false;class At extends kt{constructor(t){super(-2e8,-2e8,null,t)}eq(t){return t instanceof At&&this.spec.class==t.spec.class&&yt(this.spec.attributes,t.spec.attributes)}range(t,e=t){if(e!=t)throw new RangeError("Line decoration ranges must be zero-length");return super.range(t,e)}}At.prototype.mapMode=s.MapMode.TrackBefore;At.prototype.point=true;class Dt extends kt{constructor(t,e,i,o,n,r){super(e,i,n,t);this.block=o;this.isReplace=r;this.mapMode=!o?s.MapMode.TrackDel:e<=0?s.MapMode.TrackBefore:s.MapMode.TrackAfter}get type(){return this.startSide<this.endSide?Mt.WidgetRange:this.startSide<=0?Mt.WidgetBefore:Mt.WidgetAfter}get heightRelevant(){return this.block||!!this.widget&&this.widget.estimatedHeight>=5}eq(t){return t instanceof Dt&&Ot(this.widget,t.widget)&&this.block==t.block&&this.startSide==t.startSide&&this.endSide==t.endSide}range(t,e=t){if(this.isReplace&&(t>e||t==e&&this.startSide>0&&this.endSide<=0))throw new RangeError("Invalid range for replacement decoration");if(!this.isReplace&&e!=t)throw new RangeError("Widget decorations can only have zero-length ranges");return super.range(t,e)}}Dt.prototype.point=true;function Tt(t,e=false){let{inclusiveStart:i,inclusiveEnd:s}=t;if(i==null)i=t.inclusive;if(s==null)s=t.inclusive;return{start:i!==null&&i!==void 0?i:e,end:s!==null&&s!==void 0?s:e}}function Ot(t,e){return t==e||!!(t&&e&&t.compare(e))}function Et(t,e,i,s=0){let o=i.length-1;if(o>=0&&i[o]+s>=t)i[o]=Math.max(i[o],e);else i.push(t,e)}class Rt extends z{constructor(){super(...arguments);this.children=[];this.length=0;this.prevAttrs=undefined;this.attrs=null;this.breakAfter=0}merge(t,e,i,s,o,n){if(i){if(!(i instanceof Rt))return false;if(!this.dom)i.transferDOM(this)}if(s)this.setDeco(i?i.attrs:null);G(this,t,e,i?i.children:[],o,n);return true}split(t){let e=new Rt;e.breakAfter=this.breakAfter;if(this.length==0)return e;let{i,off:s}=this.childPos(t);if(s){e.append(this.children[i].split(s),0);this.children[i].merge(s,this.children[i].length,null,false,0,0);i++}for(let o=i;o<this.children.length;o++)e.append(this.children[o],0);while(i>0&&this.children[i-1].length==0)this.children[--i].destroy();this.children.length=i;this.markDirty();this.length=t;return e}transferDOM(t){if(!this.dom)return;this.markDirty();t.setDOM(this.dom);t.prevAttrs=this.prevAttrs===undefined?this.attrs:this.prevAttrs;this.prevAttrs=undefined;this.dom=null}setDeco(t){if(!yt(this.attrs,t)){if(this.dom){this.prevAttrs=this.attrs;this.markDirty()}this.attrs=t}}append(t,e){mt(this,t,e)}addLineDeco(t){let e=t.spec.attributes,i=t.spec.class;if(e)this.attrs=bt(e,this.attrs||{});if(i)this.attrs=bt({class:i},this.attrs||{})}domAtPos(t){return gt(this,t)}reuseDOM(t){if(t.nodeName=="DIV"){this.setDOM(t);this.dirty|=4|2}}sync(t,e){var i;if(!this.dom){this.setDOM(document.createElement("div"));this.dom.className="cm-line";this.prevAttrs=this.attrs?null:undefined}else if(this.dirty&4){V(this.dom);this.dom.className="cm-line";this.prevAttrs=this.attrs?null:undefined}if(this.prevAttrs!==undefined){St(this.dom,this.prevAttrs,this.attrs);this.dom.classList.add("cm-line");this.prevAttrs=undefined}super.sync(t,e);let s=this.dom.lastChild;while(s&&z.get(s)instanceof nt)s=s.lastChild;if(!s||!this.length||s.nodeName!="BR"&&((i=z.get(s))===null||i===void 0?void 0:i.isEditable)==false&&(!it.ios||!this.children.some((t=>t instanceof ot)))){let t=document.createElement("BR");t.cmIgnore=true;this.dom.appendChild(t)}}measureTextSize(){if(this.children.length==0||this.length>20)return null;let t=0,e;for(let i of this.children){if(!(i instanceof ot)||/[^ -~]/.test(i.text))return null;let s=b(i.dom);if(s.length!=1)return null;t+=s[0].width;e=s[0].height}return!t?null:{lineHeight:this.dom.getBoundingClientRect().height,charWidth:t/this.length,textHeight:e}}coordsAt(t,e){let i=wt(this,t,e);if(!this.children.length&&i&&this.parent){let{heightOracle:t}=this.parent.view.viewState,e=i.bottom-i.top;if(Math.abs(e-t.lineHeight)<2&&t.textHeight<e){let s=(e-t.textHeight)/2;return{top:i.top+s,bottom:i.bottom-s,left:i.left,right:i.left}}}return i}become(t){return false}get type(){return Mt.Text}static find(t,e){for(let i=0,s=0;i<t.children.length;i++){let o=t.children[i],n=s+o.length;if(n>=e){if(o instanceof Rt)return o;if(n>e)break}s=n+o.breakAfter}return null}}class Bt extends z{constructor(t,e,i){super();this.widget=t;this.length=e;this.type=i;this.breakAfter=0;this.prevWidget=null}merge(t,e,i,s,o,n){if(i&&(!(i instanceof Bt)||!this.widget.compare(i.widget)||t>0&&o<=0||e<this.length&&n<=0))return false;this.length=t+(i?i.length:0)+(this.length-e);return true}domAtPos(t){return t==0?W.before(this.dom):W.after(this.dom,t==this.length)}split(t){let e=this.length-t;this.length=t;let i=new Bt(this.widget,e,this.type);i.breakAfter=this.breakAfter;return i}get children(){return F}sync(t){if(!this.dom||!this.widget.updateDOM(this.dom,t)){if(this.dom&&this.prevWidget)this.prevWidget.destroy(this.dom);this.prevWidget=null;this.setDOM(this.widget.toDOM(t));this.dom.contentEditable="false"}}get overrideDOMText(){return this.parent?this.parent.view.state.doc.slice(this.posAtStart,this.posAtEnd):s.Text.empty}domBoundsAround(){return null}become(t){if(t instanceof Bt&&t.type==this.type&&t.widget.constructor==this.widget.constructor){if(!t.widget.compare(this.widget))this.markDirty(true);if(this.dom&&!this.prevWidget)this.prevWidget=this.widget;this.widget=t.widget;this.length=t.length;this.breakAfter=t.breakAfter;return true}return false}ignoreMutation(){return true}ignoreEvent(t){return this.widget.ignoreEvent(t)}get isEditable(){return false}get isWidget(){return true}destroy(){super.destroy();if(this.dom)this.widget.destroy(this.dom)}}class Lt{constructor(t,e,i,s){this.doc=t;this.pos=e;this.end=i;this.disallowBlockEffectsFor=s;this.content=[];this.curLine=null;this.breakAtStart=0;this.pendingBuffer=0;this.bufferMarks=[];this.atCursorPos=true;this.openStart=-1;this.openEnd=-1;this.text="";this.textOff=0;this.cursor=t.iter();this.skip=e}posCovered(){if(this.content.length==0)return!this.breakAtStart&&this.doc.lineAt(this.pos).from!=this.pos;let t=this.content[this.content.length-1];return!t.breakAfter&&!(t instanceof Bt&&t.type==Mt.WidgetBefore)}getLine(){if(!this.curLine){this.content.push(this.curLine=new Rt);this.atCursorPos=true}return this.curLine}flushBuffer(t=this.bufferMarks){if(this.pendingBuffer){this.curLine.append(Ht(new ut(-1),t),t.length);this.pendingBuffer=0}}addBlockWidget(t){this.flushBuffer();this.curLine=null;this.content.push(t)}finish(t){if(this.pendingBuffer&&t<=this.bufferMarks.length)this.flushBuffer();else this.pendingBuffer=0;if(!this.posCovered())this.getLine()}buildText(t,e,i){while(t>0){if(this.textOff==this.text.length){let{value:e,lineBreak:i,done:s}=this.cursor.next(this.skip);this.skip=0;if(s)throw new Error("Ran out of text content when drawing inline views");if(i){if(!this.posCovered())this.getLine();if(this.content.length)this.content[this.content.length-1].breakAfter=1;else this.breakAtStart=1;this.flushBuffer();this.curLine=null;this.atCursorPos=true;t--;continue}else{this.text=e;this.textOff=0}}let s=Math.min(this.text.length-this.textOff,t,512);this.flushBuffer(e.slice(e.length-i));this.getLine().append(Ht(new ot(this.text.slice(this.textOff,this.textOff+s)),e),i);this.atCursorPos=true;this.textOff+=s;t-=s;i=0}}span(t,e,i,s){this.buildText(e-t,i,s);this.pos=e;if(this.openStart<0)this.openStart=s}point(t,e,i,s,o,n){if(this.disallowBlockEffectsFor[n]&&i instanceof Dt){if(i.block)throw new RangeError("Block decorations may not be specified via plugins");if(e>this.doc.lineAt(this.pos).to)throw new RangeError("Decorations that replace line breaks may not be specified via plugins")}let r=e-t;if(i instanceof Dt){if(i.block){let{type:t}=i;if(t==Mt.WidgetAfter&&!this.posCovered())this.getLine();this.addBlockWidget(new Bt(i.widget||new Pt("div"),r,t))}else{let n=lt.create(i.widget||new Pt("span"),r,r?0:i.startSide);let l=this.atCursorPos&&!n.isEditable&&o<=s.length&&(t<e||i.startSide>0);let h=!n.isEditable&&(t<e||o>s.length||i.startSide<=0);let a=this.getLine();if(this.pendingBuffer==2&&!l&&!n.isEditable)this.pendingBuffer=0;this.flushBuffer(s);if(l){a.append(Ht(new ut(1),s),o);o=s.length+Math.max(0,o-s.length)}a.append(Ht(n,s),o);this.atCursorPos=h;this.pendingBuffer=!h?0:t<e||o>s.length?1:2;if(this.pendingBuffer)this.bufferMarks=s.slice()}}else if(this.doc.lineAt(this.pos).from==this.pos){this.getLine().addLineDeco(i)}if(r){if(this.textOff+r<=this.text.length){this.textOff+=r}else{this.skip+=r-(this.text.length-this.textOff);this.text="";this.textOff=0}this.pos=e}if(this.openStart<0)this.openStart=o}static build(t,e,i,o,n){let r=new Lt(t,e,i,n);r.openEnd=s.RangeSet.spans(o,e,i,r);if(r.openStart<0)r.openStart=r.openEnd;r.finish(r.openEnd);return r}}function Ht(t,e){for(let i of e)t=new nt(i,[t],t.length);return t}class Pt extends xt{constructor(t){super();this.tag=t}eq(t){return t.tag==this.tag}toDOM(){return document.createElement(this.tag)}updateDOM(t){return t.nodeName.toLowerCase()==this.tag}get isHidden(){return true}}const Vt=s.Facet.define();const Nt=s.Facet.define();const Wt=s.Facet.define();const Ft=s.Facet.define();const zt=s.Facet.define();const It=s.Facet.define();const Kt=s.Facet.define();const qt=s.Facet.define({combine:t=>t.some((t=>t))});const Gt=s.Facet.define({combine:t=>t.some((t=>t))});class jt{constructor(t,e="nearest",i="nearest",s=5,o=5){this.range=t;this.y=e;this.x=i;this.yMargin=s;this.xMargin=o}map(t){return t.empty?this:new jt(this.range.map(t),this.y,this.x,this.yMargin,this.xMargin)}}const $t=s.StateEffect.define({map:(t,e)=>t.map(e)});function _t(t,e,i){let s=t.facet(Ft);if(s.length)s[0](e);else if(window.onerror)window.onerror(String(e),i,undefined,undefined,e);else if(i)console.error(i+":",e);else console.error(e)}const Ut=s.Facet.define({combine:t=>t.length?t[0]:true});let Xt=0;const Yt=s.Facet.define();class Qt{constructor(t,e,i,s){this.id=t;this.create=e;this.domEventHandlers=i;this.extension=s(this)}static define(t,e){const{eventHandlers:i,provide:s,decorations:o}=e||{};return new Qt(Xt++,t,i,(t=>{let e=[Yt.of(t)];if(o)e.push(ee.of((e=>{let i=e.plugin(t);return i?o(i):kt.none})));if(s)e.push(s(t));return e}))}static fromClass(t,e){return Qt.define((e=>new t(e)),e)}}class Jt{constructor(t){this.spec=t;this.mustUpdate=null;this.value=null}update(t){if(!this.value){if(this.spec){try{this.value=this.spec.create(t)}catch(e){_t(t.state,e,"CodeMirror plugin crashed");this.deactivate()}}}else if(this.mustUpdate){let t=this.mustUpdate;this.mustUpdate=null;if(this.value.update){try{this.value.update(t)}catch(e){_t(t.state,e,"CodeMirror plugin crashed");if(this.value.destroy)try{this.value.destroy()}catch(i){}this.deactivate()}}}return this}destroy(t){var e;if((e=this.value)===null||e===void 0?void 0:e.destroy){try{this.value.destroy()}catch(i){_t(t.state,i,"CodeMirror plugin crashed")}}}deactivate(){this.spec=this.value=null}}const Zt=s.Facet.define();const te=s.Facet.define();const ee=s.Facet.define();const ie=s.Facet.define();const se=s.Facet.define();const oe=s.Facet.define();class ne{constructor(t,e,i,s){this.fromA=t;this.toA=e;this.fromB=i;this.toB=s}join(t){return new ne(Math.min(this.fromA,t.fromA),Math.max(this.toA,t.toA),Math.min(this.fromB,t.fromB),Math.max(this.toB,t.toB))}addToSet(t){let e=t.length,i=this;for(;e>0;e--){let s=t[e-1];if(s.fromA>i.toA)continue;if(s.toA<i.fromA)break;i=i.join(s);t.splice(e-1,1)}t.splice(e,0,i);return t}static extendWithRanges(t,e){if(e.length==0)return t;let i=[];for(let s=0,o=0,n=0,r=0;;s++){let l=s==t.length?null:t[s],h=n-r;let a=l?l.fromB:1e9;while(o<e.length&&e[o]<a){let t=e[o],s=e[o+1];let n=Math.max(r,t),l=Math.min(a,s);if(n<=l)new ne(n+h,l+h,n,l).addToSet(i);if(s>a)break;else o+=2}if(!l)return i;new ne(l.fromA,l.toA,l.fromB,l.toB).addToSet(i);n=l.toA;r=l.toB}}}class re{constructor(t,e,i){this.view=t;this.state=e;this.transactions=i;this.flags=0;this.startState=t.state;this.changes=s.ChangeSet.empty(this.startState.doc.length);for(let s of i)this.changes=this.changes.compose(s.changes);let o=[];this.changes.iterChangedRanges(((t,e,i,s)=>o.push(new ne(t,e,i,s))));this.changedRanges=o}static create(t,e,i){return new re(t,e,i)}get viewportChanged(){return(this.flags&4)>0}get heightChanged(){return(this.flags&2)>0}get geometryChanged(){return this.docChanged||(this.flags&(8|2))>0}get focusChanged(){return(this.flags&1)>0}get docChanged(){return!this.changes.empty}get selectionSet(){return this.transactions.some((t=>t.selection))}get empty(){return this.flags==0&&this.transactions.length==0}}var le=function(t){t[t["LTR"]=0]="LTR";t[t["RTL"]=1]="RTL";return t}(le||(le={}));const he=le.LTR,ae=le.RTL;function ce(t){let e=[];for(let i=0;i<t.length;i++)e.push(1<<+t[i]);return e}const fe=ce("88888888888888888888888888888888888666888888787833333333337888888000000000000000000000000008888880000000000000000000000000088888888888888888888888888888888888887866668888088888663380888308888800000000000000000000000800000000000000000000000000000008");const de=ce("4444448826627288999999999992222222222222222222222222222222222222222222222229999999999999999999994444444444644222822222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222999999949999999229989999223333333333");const ue=Object.create(null),pe=[];for(let er of["()","[]","{}"]){let t=er.charCodeAt(0),e=er.charCodeAt(1);ue[t]=e;ue[e]=-t}function ge(t){return t<=247?fe[t]:1424<=t&&t<=1524?2:1536<=t&&t<=1785?de[t-1536]:1774<=t&&t<=2220?4:8192<=t&&t<=8203?256:64336<=t&&t<=65023?4:t==8204?256:1}const me=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac\ufb50-\ufdff]/;class we{constructor(t,e,i){this.from=t;this.to=e;this.level=i}get dir(){return this.level%2?ae:he}side(t,e){return this.dir==e==t?this.to:this.from}static find(t,e,i,s){let o=-1;for(let n=0;n<t.length;n++){let r=t[n];if(r.from<=e&&r.to>=e){if(r.level==i)return n;if(o<0||(s!=0?s<0?r.from<e:r.to>e:t[o].level>r.level))o=n}}if(o<0)throw new RangeError("Index out of range");return o}}const ve=[];function be(t,e){let i=t.length,s=e==he?1:2,o=e==he?2:1;if(!t||s==1&&!me.test(t))return ye(i);for(let r=0,l=s,h=s;r<i;r++){let e=ge(t.charCodeAt(r));if(e==512)e=l;else if(e==8&&h==4)e=16;ve[r]=e==4?2:e;if(e&7)h=e;l=e}for(let r=0,l=s,h=s;r<i;r++){let t=ve[r];if(t==128){if(r<i-1&&l==ve[r+1]&&l&24)t=ve[r]=l;else ve[r]=256}else if(t==64){let t=r+1;while(t<i&&ve[t]==64)t++;let e=r&&l==8||t<i&&ve[t]==8?h==1?1:8:256;for(let i=r;i<t;i++)ve[i]=e;r=t-1}else if(t==8&&h==1){ve[r]=1}l=t;if(t&7)h=t}for(let r=0,l=0,h=0,a,c,f;r<i;r++){if(c=ue[a=t.charCodeAt(r)]){if(c<0){for(let t=l-3;t>=0;t-=3){if(pe[t+1]==-c){let e=pe[t+2];let i=e&2?s:!(e&4)?0:e&1?o:s;if(i)ve[r]=ve[pe[t]]=i;l=t;break}}}else if(pe.length==189){break}else{pe[l++]=r;pe[l++]=a;pe[l++]=h}}else if((f=ve[r])==2||f==1){let t=f==s;h=t?0:1;for(let e=l-3;e>=0;e-=3){let i=pe[e+2];if(i&2)break;if(t){pe[e+2]|=2}else{if(i&4)break;pe[e+2]|=4}}}}for(let r=0;r<i;r++){if(ve[r]==256){let t=r+1;while(t<i&&ve[t]==256)t++;let e=(r?ve[r-1]:s)==1;let o=(t<i?ve[t]:s)==1;let n=e==o?e?1:2:s;for(let i=r;i<t;i++)ve[i]=n;r=t-1}}let n=[];if(s==1){for(let t=0;t<i;){let e=t,s=ve[t++]!=1;while(t<i&&s==(ve[t]!=1))t++;if(s){for(let i=t;i>e;){let t=i,s=ve[--i]!=2;while(i>e&&s==(ve[i-1]!=2))i--;n.push(new we(i,t,s?2:1))}}else{n.push(new we(e,t,0))}}}else{for(let t=0;t<i;){let e=t,s=ve[t++]==2;while(t<i&&s==(ve[t]==2))t++;n.push(new we(e,t,s?1:2))}}return n}function ye(t){return[new we(0,t,0)]}let Se="";function xe(t,e,i,o,n){var r;let l=o.head-t.from,h=-1;if(l==0){if(!n||!t.length)return null;if(e[0].level!=i){l=e[0].side(false,i);h=0}}else if(l==t.length){if(n)return null;let t=e[e.length-1];if(t.level!=i){l=t.side(true,i);h=e.length-1}}if(h<0)h=we.find(e,l,(r=o.bidiLevel)!==null&&r!==void 0?r:-1,o.assoc);let a=e[h];if(l==a.side(n,i)){a=e[h+=n?1:-1];l=a.side(!n,i)}let c=n==(a.dir==i);let f=(0,s.findClusterBreak)(t.text,l,c);Se=t.text.slice(Math.min(l,f),Math.max(l,f));if(f!=a.side(n,i))return s.EditorSelection.cursor(f+t.from,c?-1:1,a.level);let d=h==(n?e.length-1:0)?null:e[h+(n?1:-1)];if(!d&&a.level!=i)return s.EditorSelection.cursor(n?t.to:t.from,n?-1:1,i);if(d&&d.level<a.level)return s.EditorSelection.cursor(d.side(!n,i)+t.from,n?1:-1,d.level);return s.EditorSelection.cursor(f+t.from,n?-1:1,a.level)}const Me="￿";class ke{constructor(t,e){this.points=t;this.text="";this.lineSeparator=e.facet(s.EditorState.lineSeparator)}append(t){this.text+=t}lineBreak(){this.text+=Me}readRange(t,e){if(!t)return this;let i=t.parentNode;for(let s=t;;){this.findPointBefore(i,s);this.readNode(s);let t=s.nextSibling;if(t==e)break;let o=z.get(s),n=z.get(t);if(o&&n?o.breakAfter:(o?o.breakAfter:Ce(s))||Ce(t)&&(s.nodeName!="BR"||s.cmIgnore))this.lineBreak();s=t}this.findPointBefore(i,e);return this}readTextNode(t){let e=t.nodeValue;for(let i of this.points)if(i.node==t)i.pos=this.text.length+Math.min(i.offset,e.length);for(let i=0,s=this.lineSeparator?null:/\r\n?|\n/g;;){let o=-1,n=1,r;if(this.lineSeparator){o=e.indexOf(this.lineSeparator,i);n=this.lineSeparator.length}else if(r=s.exec(e)){o=r.index;n=r[0].length}this.append(e.slice(i,o<0?e.length:o));if(o<0)break;this.lineBreak();if(n>1)for(let e of this.points)if(e.node==t&&e.pos>this.text.length)e.pos-=n-1;i=o+n}}readNode(t){if(t.cmIgnore)return;let e=z.get(t);let i=e&&e.overrideDOMText;if(i!=null){this.findPointInside(t,i.length);for(let t=i.iter();!t.next().done;){if(t.lineBreak)this.lineBreak();else this.append(t.value)}}else if(t.nodeType==3){this.readTextNode(t)}else if(t.nodeName=="BR"){if(t.nextSibling)this.lineBreak()}else if(t.nodeType==1){this.readRange(t.firstChild,null)}}findPointBefore(t,e){for(let i of this.points)if(i.node==t&&t.childNodes[i.offset]==e)i.pos=this.text.length}findPointInside(t,e){for(let i of this.points)if(t.nodeType==3?i.node==t:t.contains(i.node))i.pos=this.text.length+Math.min(e,i.offset)}}function Ce(t){return t.nodeType==1&&/^(DIV|P|LI|UL|OL|BLOCKQUOTE|DD|DT|H\d|SECTION|PRE)$/.test(t.nodeName)}class Ae{constructor(t,e){this.node=t;this.offset=e;this.pos=-1}}class De extends z{constructor(t){super();this.view=t;this.compositionDeco=kt.none;this.decorations=[];this.dynamicDecorationMap=[];this.minWidth=0;this.minWidthFrom=0;this.minWidthTo=0;this.impreciseAnchor=null;this.impreciseHead=null;this.forceSelection=false;this.lastUpdate=Date.now();this.setDOM(t.contentDOM);this.children=[new Rt];this.children[0].setParent(this);this.updateDeco();this.updateInner([new ne(0,0,0,t.state.doc.length)],0)}get length(){return this.view.state.doc.length}update(t){let e=t.changedRanges;if(this.minWidth>0&&e.length){if(!e.every((({fromA:t,toA:e})=>e<this.minWidthFrom||t>this.minWidthTo))){this.minWidth=this.minWidthFrom=this.minWidthTo=0}else{this.minWidthFrom=t.changes.mapPos(this.minWidthFrom,1);this.minWidthTo=t.changes.mapPos(this.minWidthTo,1)}}if(this.view.inputState.composing<0)this.compositionDeco=kt.none;else if(t.transactions.length||this.dirty)this.compositionDeco=Re(this.view,t.changes);if((it.ie||it.chrome)&&!this.compositionDeco.size&&t&&t.state.doc.lines!=t.startState.doc.lines)this.forceSelection=true;let i=this.decorations,s=this.updateDeco();let o=Ve(i,s,t.changes);e=ne.extendWithRanges(e,o);if(this.dirty==0&&e.length==0){return false}else{this.updateInner(e,t.startState.doc.length);if(t.transactions.length)this.lastUpdate=Date.now();return true}}updateInner(t,e){this.view.viewState.mustMeasureContent=true;this.updateChildren(t,e);let{observer:i}=this.view;i.ignore((()=>{this.dom.style.height=this.view.viewState.contentHeight+"px";this.dom.style.flexBasis=this.minWidth?this.minWidth+"px":"";let t=it.chrome||it.ios?{node:i.selectionRange.focusNode,written:false}:undefined;this.sync(this.view,t);this.dirty=0;if(t&&(t.written||i.selectionRange.focusNode!=t.node))this.forceSelection=true;this.dom.style.height=""}));let s=[];if(this.view.viewport.from||this.view.viewport.to<this.view.state.doc.length)for(let o of this.children)if(o instanceof Bt&&o.widget instanceof Oe)s.push(o.dom);i.updateGaps(s)}updateChildren(t,e){let i=this.childCursor(e);for(let s=t.length-1;;s--){let e=s>=0?t[s]:null;if(!e)break;let{fromA:o,toA:n,fromB:r,toB:l}=e;let{content:h,breakAtStart:a,openStart:c,openEnd:f}=Lt.build(this.view.state.doc,r,l,this.decorations,this.dynamicDecorationMap);let{i:d,off:u}=i.findPos(n,1);let{i:p,off:g}=i.findPos(o,-1);q(this,p,g,d,u,h,a,c,f)}}updateSelection(t=false,e=false){if(t||!this.view.observer.selectionRange.focusNode)this.view.observer.readSelectionRange();let i=this.view.root.activeElement,s=i==this.dom;let o=!s&&v(this.dom,this.view.observer.selectionRange)&&!(i&&this.dom.contains(i));if(!(s||e||o))return;let n=this.forceSelection;this.forceSelection=false;let r=this.view.state.selection.main;let l=this.domAtPos(r.anchor);let h=r.empty?l:this.domAtPos(r.head);if(it.gecko&&r.empty&&!this.compositionDeco.size&&Te(l)){let t=document.createTextNode("");this.view.observer.ignore((()=>l.node.insertBefore(t,l.node.childNodes[l.offset]||null)));l=h=new W(t,0);n=true}let a=this.view.observer.selectionRange;if(n||!a.focusNode||!y(l.node,l.offset,a.anchorNode,a.anchorOffset)||!y(h.node,h.offset,a.focusNode,a.focusOffset)){this.view.observer.ignore((()=>{if(it.android&&it.chrome&&this.dom.contains(a.focusNode)&&Ne(a.focusNode,this.dom)){this.dom.blur();this.dom.focus({preventScroll:true})}let t=g(this.view.root);if(!t);else if(r.empty){if(it.gecko){let t=He(l.node,l.offset);if(t&&t!=(1|2)){let e=Le(l.node,l.offset,t==1?1:-1);if(e)l=new W(e,t==1?0:e.nodeValue.length)}}t.collapse(l.node,l.offset);if(r.bidiLevel!=null&&a.cursorBidiLevel!=null)a.cursorBidiLevel=r.bidiLevel}else if(t.extend){t.collapse(l.node,l.offset);try{t.extend(h.node,h.offset)}catch(e){}}else{let e=document.createRange();if(r.anchor>r.head)[l,h]=[h,l];e.setEnd(h.node,h.offset);e.setStart(l.node,l.offset);t.removeAllRanges();t.addRange(e)}if(o&&this.view.root.activeElement==this.dom){this.dom.blur();if(i)i.focus()}}));this.view.observer.setSelectionRange(l,h)}this.impreciseAnchor=l.precise?null:new W(a.anchorNode,a.anchorOffset);this.impreciseHead=h.precise?null:new W(a.focusNode,a.focusOffset)}enforceCursorAssoc(){if(this.compositionDeco.size)return;let{view:t}=this,e=t.state.selection.main;let i=g(t.root);let{anchorNode:s,anchorOffset:o}=t.observer.selectionRange;if(!i||!e.empty||!e.assoc||!i.modify)return;let n=Rt.find(this,e.head);if(!n)return;let r=n.posAtStart;if(e.head==r||e.head==r+n.length)return;let l=this.coordsAt(e.head,-1),h=this.coordsAt(e.head,1);if(!l||!h||l.bottom>h.top)return;let a=this.domAtPos(e.head+e.assoc);i.collapse(a.node,a.offset);i.modify("move",e.assoc<0?"forward":"backward","lineboundary");t.observer.readSelectionRange();let c=t.observer.selectionRange;if(t.docView.posFromDOM(c.anchorNode,c.anchorOffset)!=e.from)i.collapse(s,o)}nearest(t){for(let e=t;e;){let t=z.get(e);if(t&&t.rootView==this)return t;e=e.parentNode}return null}posFromDOM(t,e){let i=this.nearest(t);if(!i)throw new RangeError("Trying to find position for a DOM position outside of the document");return i.localPosFromDOM(t,e)+i.posAtStart}domAtPos(t){let{i:e,off:i}=this.childCursor().findPos(t,-1);for(;e<this.children.length-1;){let t=this.children[e];if(i<t.length||t instanceof Rt)break;e++;i=0}return this.children[e].domAtPos(i)}coordsAt(t,e){for(let i=this.length,s=this.children.length-1;;s--){let o=this.children[s],n=i-o.breakAfter-o.length;if(t>n||t==n&&o.type!=Mt.WidgetBefore&&o.type!=Mt.WidgetAfter&&(!s||e==2||this.children[s-1].breakAfter||this.children[s-1].type==Mt.WidgetBefore&&e>-2))return o.coordsAt(t-n,e);i=n}}measureVisibleLineHeights(t){let e=[],{from:i,to:s}=t;let o=this.view.contentDOM.clientWidth;let n=o>Math.max(this.view.scrollDOM.clientWidth,this.minWidth)+1;let r=-1,l=this.view.textDirection==le.LTR;for(let h=0,a=0;a<this.children.length;a++){let t=this.children[a],c=h+t.length;if(c>s)break;if(h>=i){let i=t.dom.getBoundingClientRect();e.push(i.height);if(n){let e=t.dom.lastChild;let s=e?b(e):[];if(s.length){let t=s[s.length-1];let e=l?t.right-i.left:i.right-t.left;if(e>r){r=e;this.minWidth=o;this.minWidthFrom=h;this.minWidthTo=c}}}}h=c+t.breakAfter}return e}textDirectionAt(t){let{i:e}=this.childPos(t,1);return getComputedStyle(this.children[e].dom).direction=="rtl"?le.RTL:le.LTR}measureTextSize(){for(let o of this.children){if(o instanceof Rt){let t=o.measureTextSize();if(t)return t}}let t=document.createElement("div"),e,i,s;t.className="cm-line";t.style.width="99999px";t.textContent="abc def ghi jkl mno pqr stu";this.view.observer.ignore((()=>{this.dom.appendChild(t);let o=b(t.firstChild)[0];e=t.getBoundingClientRect().height;i=o?o.width/27:7;s=o?o.height:e;t.remove()}));return{lineHeight:e,charWidth:i,textHeight:s}}childCursor(t=this.length){let e=this.children.length;if(e)t-=this.children[--e].length;return new K(this.children,t,e)}computeBlockGapDeco(){let t=[],e=this.view.viewState;for(let i=0,s=0;;s++){let o=s==e.viewports.length?null:e.viewports[s];let n=o?o.from-1:this.length;if(n>i){let s=e.lineBlockAt(n).bottom-e.lineBlockAt(i).top;t.push(kt.replace({widget:new Oe(s),block:true,inclusive:true,isBlockGap:true}).range(i,n))}if(!o)break;i=o.to+1}return kt.set(t)}updateDeco(){let t=this.view.state.facet(ee).map(((t,e)=>{let i=this.dynamicDecorationMap[e]=typeof t=="function";return i?t(this.view):t}));for(let e=t.length;e<t.length+3;e++)this.dynamicDecorationMap[e]=false;return this.decorations=[...t,this.compositionDeco,this.computeBlockGapDeco(),this.view.viewState.lineGapDeco]}scrollIntoView(t){let{range:e}=t;let i=this.coordsAt(e.head,e.empty?e.assoc:e.head>e.anchor?-1:1),s;if(!i)return;if(!e.empty&&(s=this.coordsAt(e.anchor,e.anchor>e.head?-1:1)))i={left:Math.min(i.left,s.left),top:Math.min(i.top,s.top),right:Math.max(i.right,s.right),bottom:Math.max(i.bottom,s.bottom)};let o=0,n=0,r=0,l=0;for(let a of this.view.state.facet(se).map((t=>t(this.view))))if(a){let{left:t,right:e,top:i,bottom:s}=a;if(t!=null)o=Math.max(o,t);if(e!=null)n=Math.max(n,e);if(i!=null)r=Math.max(r,i);if(s!=null)l=Math.max(l,s)}let h={left:i.left-o,top:i.top-r,right:i.right+n,bottom:i.bottom+l};D(this.view.scrollDOM,h,e.head<e.anchor?-1:1,t.x,t.y,t.xMargin,t.yMargin,this.view.textDirection==le.LTR)}}function Te(t){return t.node.nodeType==1&&t.node.firstChild&&(t.offset==0||t.node.childNodes[t.offset-1].contentEditable=="false")&&(t.offset==t.node.childNodes.length||t.node.childNodes[t.offset].contentEditable=="false")}class Oe extends xt{constructor(t){super();this.height=t}toDOM(){let t=document.createElement("div");this.updateDOM(t);return t}eq(t){return t.height==this.height}updateDOM(t){t.style.height=this.height+"px";return true}get estimatedHeight(){return this.height}}function Ee(t){let e=t.observer.selectionRange;let i=e.focusNode&&Le(e.focusNode,e.focusOffset,0);if(!i)return null;let s=t.docView.nearest(i);if(!s)return null;if(s instanceof Rt){let t=i;while(t.parentNode!=s.dom)t=t.parentNode;let e=t.previousSibling;while(e&&!z.get(e))e=e.previousSibling;let o=e?z.get(e).posAtEnd:s.posAtStart;return{from:o,to:o,node:t,text:i}}else{for(;;){let{parent:t}=s;if(!t)return null;if(t instanceof Rt)break;s=t}let t=s.posAtStart;return{from:t,to:t+s.length,node:s.dom,text:i}}}function Re(t,e){let i=Ee(t);if(!i)return kt.none;let{from:s,to:o,node:n,text:r}=i;let l=e.mapPos(s,1),h=Math.max(l,e.mapPos(o,-1));let{state:a}=t,c=n.nodeType==3?n.nodeValue:new ke([],a).readRange(n.firstChild,null).text;if(h-l<c.length){if(a.doc.sliceString(l,Math.min(a.doc.length,l+c.length),Me)==c)h=l+c.length;else if(a.doc.sliceString(Math.max(0,h-c.length),h,Me)==c)l=h-c.length;else return kt.none}else if(a.doc.sliceString(l,h,Me)!=c){return kt.none}let f=z.get(n);if(f instanceof ht)f=f.widget.topView;else if(f)f.parent=null;return kt.set(kt.replace({widget:new Be(n,r,f),inclusive:true}).range(l,h))}class Be extends xt{constructor(t,e,i){super();this.top=t;this.text=e;this.topView=i}eq(t){return this.top==t.top&&this.text==t.text}toDOM(){return this.top}ignoreEvent(){return false}get customView(){return ht}}function Le(t,e,i){if(i<=0)for(let s=t,o=e;;){if(s.nodeType==3)return s;if(s.nodeType==1&&o>0){s=s.childNodes[o-1];o=M(s)}else{break}}if(i>=0)for(let s=t,o=e;;){if(s.nodeType==3)return s;if(s.nodeType==1&&o<s.childNodes.length&&i>=0){s=s.childNodes[o];o=0}else{break}}return null}function He(t,e){if(t.nodeType!=1)return 0;return(e&&t.childNodes[e-1].contentEditable=="false"?1:0)|(e<t.childNodes.length&&t.childNodes[e].contentEditable=="false"?2:0)}class Pe{constructor(){this.changes=[]}compareRange(t,e){Et(t,e,this.changes)}comparePoint(t,e){Et(t,e,this.changes)}}function Ve(t,e,i){let o=new Pe;s.RangeSet.compare(t,e,i,o);return o.changes}function Ne(t,e){for(let i=t;i&&i!=e;i=i.assignedSlot||i.parentNode){if(i.nodeType==1&&i.contentEditable=="false"){return true}}return false}function We(t,e,i=1){let o=t.charCategorizer(e);let n=t.doc.lineAt(e),r=e-n.from;if(n.length==0)return s.EditorSelection.cursor(e);if(r==0)i=1;else if(r==n.length)i=-1;let l=r,h=r;if(i<0)l=(0,s.findClusterBreak)(n.text,r,false);else h=(0,s.findClusterBreak)(n.text,r);let a=o(n.text.slice(l,h));while(l>0){let t=(0,s.findClusterBreak)(n.text,l,false);if(o(n.text.slice(t,l))!=a)break;l=t}while(h<n.length){let t=(0,s.findClusterBreak)(n.text,h);if(o(n.text.slice(h,t))!=a)break;h=t}return s.EditorSelection.range(l+n.from,h+n.from)}function Fe(t,e){return e.left>t?e.left-t:Math.max(0,t-e.right)}function ze(t,e){return e.top>t?e.top-t:Math.max(0,t-e.bottom)}function Ie(t,e){return t.top<e.bottom-1&&t.bottom>e.top+1}function Ke(t,e){return e<t.top?{top:e,left:t.left,right:t.right,bottom:t.bottom}:t}function qe(t,e){return e>t.bottom?{top:t.top,left:t.left,right:t.right,bottom:e}:t}function Ge(t,e,i){let s,o,n,r,l=false;let h,a,c,f;for(let p=t.firstChild;p;p=p.nextSibling){let t=b(p);for(let d=0;d<t.length;d++){let u=t[d];if(o&&Ie(o,u))u=Ke(qe(u,o.bottom),o.top);let g=Fe(e,u),m=ze(i,u);if(g==0&&m==0)return p.nodeType==3?je(p,e,i):Ge(p,e,i);if(!s||r>m||r==m&&n>g){s=p;o=u;n=g;r=m;let h=m?i<u.top?-1:1:g?e<u.left?-1:1:0;l=!h||(h>0?d<t.length-1:d>0)}if(g==0){if(i>u.bottom&&(!c||c.bottom<u.bottom)){h=p;c=u}else if(i<u.top&&(!f||f.top>u.top)){a=p;f=u}}else if(c&&Ie(c,u)){c=qe(c,u.bottom)}else if(f&&Ie(f,u)){f=Ke(f,u.top)}}}if(c&&c.bottom>=i){s=h;o=c}else if(f&&f.top<=i){s=a;o=f}if(!s)return{node:t,offset:0};let d=Math.max(o.left,Math.min(o.right,e));if(s.nodeType==3)return je(s,d,i);if(l&&s.contentEditable!="false")return Ge(s,d,i);let u=Array.prototype.indexOf.call(t.childNodes,s)+(e>=(o.left+o.right)/2?1:0);return{node:t,offset:u}}function je(t,e,i){let s=t.nodeValue.length;let o=-1,n=1e9,r=0;for(let l=0;l<s;l++){let s=L(t,l,l+1).getClientRects();for(let h=0;h<s.length;h++){let a=s[h];if(a.top==a.bottom)continue;if(!r)r=e-a.left;let c=(a.top>i?a.top-i:i-a.bottom)-1;if(a.left-1<=e&&a.right+1>=e&&c<n){let i=e>=(a.left+a.right)/2,s=i;if(it.chrome||it.gecko){let e=L(t,l).getBoundingClientRect();if(e.left==a.right)s=!i}if(c<=0)return{node:t,offset:l+(s?1:0)};o=l+(s?1:0);n=c}}}return{node:t,offset:o>-1?o:r>0?t.nodeValue.length:0}}function $e(t,e,i,s=-1){var o,n;let r=t.contentDOM.getBoundingClientRect(),l=r.top+t.viewState.paddingTop;let h,{docHeight:a}=t.viewState;let{x:c,y:f}=e,d=f-l;if(d<0)return 0;if(d>a)return t.state.doc.length;for(let y=t.defaultLineHeight/2,S=false;;){h=t.elementAtHeight(d);if(h.type==Mt.Text)break;for(;;){d=s>0?h.bottom+y:h.top-y;if(d>=0&&d<=a)break;if(S)return i?null:0;S=true;s=-s}}f=l+d;let u=h.from;if(u<t.viewport.from)return t.viewport.from==0?0:i?null:_e(t,r,h,c,f);if(u>t.viewport.to)return t.viewport.to==t.state.doc.length?t.state.doc.length:i?null:_e(t,r,h,c,f);let p=t.dom.ownerDocument;let g=t.root.elementFromPoint?t.root:p;let m=g.elementFromPoint(c,f);if(m&&!t.contentDOM.contains(m))m=null;if(!m){c=Math.max(r.left+1,Math.min(r.right-1,c));m=g.elementFromPoint(c,f);if(m&&!t.contentDOM.contains(m))m=null}let w,v=-1;if(m&&((o=t.docView.nearest(m))===null||o===void 0?void 0:o.isEditable)!=false){if(p.caretPositionFromPoint){let t=p.caretPositionFromPoint(c,f);if(t)({offsetNode:w,offset:v}=t)}else if(p.caretRangeFromPoint){let e=p.caretRangeFromPoint(c,f);if(e){({startContainer:w,startOffset:v}=e);if(!t.contentDOM.contains(w)||it.safari&&Ue(w,v,c)||it.chrome&&Xe(w,v,c))w=undefined}}}if(!w||!t.docView.dom.contains(w)){let e=Rt.find(t.docView,u);if(!e)return d>h.top+h.height/2?h.to:h.from;({node:w,offset:v}=Ge(e.dom,c,f))}let b=t.docView.nearest(w);if(!b)return null;if(b.isWidget&&((n=b.dom)===null||n===void 0?void 0:n.nodeType)==1){let t=b.dom.getBoundingClientRect();return e.y<t.top||e.y<=t.bottom&&e.x<=(t.left+t.right)/2?b.posAtStart:b.posAtEnd}else{return b.localPosFromDOM(w,v)+b.posAtStart}}function _e(t,e,i,o,n){let r=Math.round((o-e.left)*t.defaultCharacterWidth);if(t.lineWrapping&&i.height>t.defaultLineHeight*1.5){let e=Math.floor((n-i.top)/t.defaultLineHeight);r+=e*t.viewState.heightOracle.lineLength}let l=t.state.sliceDoc(i.from,i.to);return i.from+(0,s.findColumn)(l,r,t.state.tabSize)}function Ue(t,e,i){let s;if(t.nodeType!=3||e!=(s=t.nodeValue.length))return false;for(let o=t.nextSibling;o;o=o.nextSibling)if(o.nodeType!=1||o.nodeName!="BR")return false;return L(t,s-1,s).getBoundingClientRect().left>i}function Xe(t,e,i){if(e!=0)return false;for(let o=t;;){let t=o.parentNode;if(!t||t.nodeType!=1||t.firstChild!=o)return false;if(t.classList.contains("cm-line"))break;o=t}let s=t.nodeType==1?t.getBoundingClientRect():L(t,0,Math.max(t.nodeValue.length,1)).getBoundingClientRect();return i-s.left>5}function Ye(t,e,i,o){let n=t.state.doc.lineAt(e.head);let r=!o||!t.lineWrapping?null:t.coordsAtPos(e.assoc<0&&e.head>n.from?e.head-1:e.head);if(r){let e=t.dom.getBoundingClientRect();let o=t.textDirectionAt(n.from);let l=t.posAtCoords({x:i==(o==le.LTR)?e.right-1:e.left+1,y:(r.top+r.bottom)/2});if(l!=null)return s.EditorSelection.cursor(l,i?-1:1)}let l=Rt.find(t.docView,e.head);let h=l?i?l.posAtEnd:l.posAtStart:i?n.to:n.from;return s.EditorSelection.cursor(h,i?-1:1)}function Qe(t,e,i,o){let n=t.state.doc.lineAt(e.head),r=t.bidiSpans(n);let l=t.textDirectionAt(n.from);for(let h=e,a=null;;){let e=xe(n,r,l,h,i),c=Se;if(!e){if(n.number==(i?t.state.doc.lines:1))return h;c="\n";n=t.state.doc.line(n.number+(i?1:-1));r=t.bidiSpans(n);e=s.EditorSelection.cursor(i?n.from:n.to)}if(!a){if(!o)return e;a=o(c)}else if(!a(c)){return h}h=e}}function Je(t,e,i){let o=t.state.charCategorizer(e);let n=o(i);return t=>{let e=o(t);if(n==s.CharCategory.Space)n=e;return n==e}}function Ze(t,e,i,o){let n=e.head,r=i?1:-1;if(n==(i?t.state.doc.length:0))return s.EditorSelection.cursor(n,e.assoc);let l=e.goalColumn,h;let a=t.contentDOM.getBoundingClientRect();let c=t.coordsAtPos(n),f=t.documentTop;if(c){if(l==null)l=c.left-a.left;h=r<0?c.top:c.bottom}else{let e=t.viewState.lineBlockAt(n);if(l==null)l=Math.min(a.right-a.left,t.defaultCharacterWidth*(n-e.from));h=(r<0?e.top:e.bottom)+f}let d=a.left+l;let u=o!==null&&o!==void 0?o:t.defaultLineHeight>>1;for(let p=0;;p+=10){let i=h+(u+p)*r;let o=$e(t,{x:d,y:i},false,r);if(i<a.top||i>a.bottom||(r<0?o<n:o>n))return s.EditorSelection.cursor(o,e.assoc,undefined,l)}}function ti(t,e,i){let o=t.state.facet(ie).map((e=>e(t)));for(;;){let t=false;for(let n of o){n.between(i.from-1,i.from+1,((o,n,r)=>{if(i.from>o&&i.from<n){i=e.head>i.from?s.EditorSelection.cursor(o,1):s.EditorSelection.cursor(n,-1);t=true}}))}if(!t)return i}}class ei{constructor(t){this.lastKeyCode=0;this.lastKeyTime=0;this.lastTouchTime=0;this.lastFocusTime=0;this.lastScrollTop=0;this.lastScrollLeft=0;this.chromeScrollHack=-1;this.pendingIOSKey=undefined;this.lastSelectionOrigin=null;this.lastSelectionTime=0;this.lastEscPress=0;this.lastContextMenu=0;this.scrollHandlers=[];this.registeredEvents=[];this.customHandlers=[];this.composing=-1;this.compositionFirstChange=null;this.compositionEndedAt=0;this.compositionPendingKey=false;this.compositionPendingChange=false;this.mouseSelection=null;let e=(e,i)=>{if(this.ignoreDuringComposition(i))return;if(i.type=="keydown"&&this.keydown(t,i))return;if(this.mustFlushObserver(i))t.observer.forceFlush();if(this.runCustomHandlers(i.type,t,i))i.preventDefault();else e(t,i)};for(let i in di){let s=di[i];t.contentDOM.addEventListener(i,(i=>{if(fi(t,i))e(s,i)}),ui[i]);this.registeredEvents.push(i)}t.scrollDOM.addEventListener("mousedown",(i=>{if(i.target==t.scrollDOM&&i.clientY>t.contentDOM.getBoundingClientRect().bottom){e(di.mousedown,i);if(!i.defaultPrevented&&i.button==2){let e=t.contentDOM.style.minHeight;t.contentDOM.style.minHeight="100%";setTimeout((()=>t.contentDOM.style.minHeight=e),200)}}}));if(it.chrome&&it.chrome_version==102){t.scrollDOM.addEventListener("wheel",(()=>{if(this.chromeScrollHack<0)t.contentDOM.style.pointerEvents="none";else window.clearTimeout(this.chromeScrollHack);this.chromeScrollHack=setTimeout((()=>{this.chromeScrollHack=-1;t.contentDOM.style.pointerEvents=""}),100)}),{passive:true})}this.notifiedFocused=t.hasFocus;if(it.safari)t.contentDOM.addEventListener("input",(()=>null))}setSelectionOrigin(t){this.lastSelectionOrigin=t;this.lastSelectionTime=Date.now()}ensureHandlers(t,e){var i;let s;this.customHandlers=[];for(let o of e)if(s=(i=o.update(t).spec)===null||i===void 0?void 0:i.domEventHandlers){this.customHandlers.push({plugin:o.value,handlers:s});for(let e in s)if(this.registeredEvents.indexOf(e)<0&&e!="scroll"){this.registeredEvents.push(e);t.contentDOM.addEventListener(e,(i=>{if(!fi(t,i))return;if(this.runCustomHandlers(e,t,i))i.preventDefault()}))}}}runCustomHandlers(t,e,i){for(let o of this.customHandlers){let n=o.handlers[t];if(n){try{if(n.call(o.plugin,i,e)||i.defaultPrevented)return true}catch(s){_t(e.state,s)}}}return false}runScrollHandlers(t,e){this.lastScrollTop=t.scrollDOM.scrollTop;this.lastScrollLeft=t.scrollDOM.scrollLeft;for(let s of this.customHandlers){let o=s.handlers.scroll;if(o){try{o.call(s.plugin,e,t)}catch(i){_t(t.state,i)}}}}keydown(t,e){this.lastKeyCode=e.keyCode;this.lastKeyTime=Date.now();if(e.keyCode==9&&Date.now()<this.lastEscPress+2e3)return true;if(e.keyCode!=27&&oi.indexOf(e.keyCode)<0)t.inputState.lastEscPress=0;if(it.android&&it.chrome&&!e.synthetic&&(e.keyCode==13||e.keyCode==8)){t.observer.delayAndroidKey(e.key,e.keyCode);return true}let i;if(it.ios&&!e.synthetic&&!e.altKey&&!e.metaKey&&((i=ii.find((t=>t.keyCode==e.keyCode)))&&!e.ctrlKey||si.indexOf(e.key)>-1&&e.ctrlKey&&!e.shiftKey)){this.pendingIOSKey=i||e;setTimeout((()=>this.flushIOSKey(t)),250);return true}return false}flushIOSKey(t){let e=this.pendingIOSKey;if(!e)return false;this.pendingIOSKey=undefined;return H(t.contentDOM,e.key,e.keyCode)}ignoreDuringComposition(t){if(!/^key/.test(t.type))return false;if(this.composing>0)return true;if(it.safari&&!it.ios&&this.compositionPendingKey&&Date.now()-this.compositionEndedAt<100){this.compositionPendingKey=false;return true}return false}mustFlushObserver(t){return t.type=="keydown"&&t.keyCode!=229}startMouseSelection(t){if(this.mouseSelection)this.mouseSelection.destroy();this.mouseSelection=t}update(t){if(this.mouseSelection)this.mouseSelection.update(t);if(t.transactions.length)this.lastKeyCode=this.lastSelectionTime=0}destroy(){if(this.mouseSelection)this.mouseSelection.destroy()}}const ii=[{key:"Backspace",keyCode:8,inputType:"deleteContentBackward"},{key:"Enter",keyCode:13,inputType:"insertParagraph"},{key:"Delete",keyCode:46,inputType:"deleteContentForward"}];const si="dthko";const oi=[16,17,18,20,91,92,224,225];const ni=6;function ri(t){return Math.max(0,t)*.7+8}class li{constructor(t,e,i,o){this.view=t;this.style=i;this.mustSelect=o;this.scrollSpeed={x:0,y:0};this.scrolling=-1;this.lastEvent=e;this.scrollParent=T(t.contentDOM);let n=t.contentDOM.ownerDocument;n.addEventListener("mousemove",this.move=this.move.bind(this));n.addEventListener("mouseup",this.up=this.up.bind(this));this.extend=e.shiftKey;this.multiple=t.state.facet(s.EditorState.allowMultipleSelections)&&hi(t,e);this.dragMove=ai(t,e);this.dragging=ci(t,e)&&Ai(e)==1?null:false}start(t){if(this.dragging===false){t.preventDefault();this.select(t)}}move(t){var e;if(t.buttons==0)return this.destroy();if(this.dragging!==false)return;this.select(this.lastEvent=t);let i=0,s=0;let o=((e=this.scrollParent)===null||e===void 0?void 0:e.getBoundingClientRect())||{left:0,top:0,right:this.view.win.innerWidth,bottom:this.view.win.innerHeight};if(t.clientX<=o.left+ni)i=-ri(o.left-t.clientX);else if(t.clientX>=o.right-ni)i=ri(t.clientX-o.right);if(t.clientY<=o.top+ni)s=-ri(o.top-t.clientY);else if(t.clientY>=o.bottom-ni)s=ri(t.clientY-o.bottom);this.setScrollSpeed(i,s)}up(t){if(this.dragging==null)this.select(this.lastEvent);if(!this.dragging)t.preventDefault();this.destroy()}destroy(){this.setScrollSpeed(0,0);let t=this.view.contentDOM.ownerDocument;t.removeEventListener("mousemove",this.move);t.removeEventListener("mouseup",this.up);this.view.inputState.mouseSelection=null}setScrollSpeed(t,e){this.scrollSpeed={x:t,y:e};if(t||e){if(this.scrolling<0)this.scrolling=setInterval((()=>this.scroll()),50)}else if(this.scrolling>-1){clearInterval(this.scrolling);this.scrolling=-1}}scroll(){if(this.scrollParent){this.scrollParent.scrollLeft+=this.scrollSpeed.x;this.scrollParent.scrollTop+=this.scrollSpeed.y}else{this.view.win.scrollBy(this.scrollSpeed.x,this.scrollSpeed.y)}if(this.dragging===false)this.select(this.lastEvent)}select(t){let e=this.style.get(t,this.extend,this.multiple);if(this.mustSelect||!e.eq(this.view.state.selection)||e.main.assoc!=this.view.state.selection.main.assoc)this.view.dispatch({selection:e,userEvent:"select.pointer"});this.mustSelect=false}update(t){if(t.docChanged&&this.dragging)this.dragging=this.dragging.map(t.changes);if(this.style.update(t))setTimeout((()=>this.select(this.lastEvent)),20)}}function hi(t,e){let i=t.state.facet(Vt);return i.length?i[0](e):it.mac?e.metaKey:e.ctrlKey}function ai(t,e){let i=t.state.facet(Nt);return i.length?i[0](e):it.mac?!e.altKey:!e.ctrlKey}function ci(t,e){let{main:i}=t.state.selection;if(i.empty)return false;let s=g(t.root);if(!s||s.rangeCount==0)return true;let o=s.getRangeAt(0).getClientRects();for(let n=0;n<o.length;n++){let t=o[n];if(t.left<=e.clientX&&t.right>=e.clientX&&t.top<=e.clientY&&t.bottom>=e.clientY)return true}return false}function fi(t,e){if(!e.bubbles)return true;if(e.defaultPrevented)return false;for(let i=e.target,s;i!=t.contentDOM;i=i.parentNode)if(!i||i.nodeType==11||(s=z.get(i))&&s.ignoreEvent(e))return false;return true}const di=Object.create(null);const ui=Object.create(null);const pi=it.ie&&it.ie_version<15||it.ios&&it.webkit_version<604;function gi(t){let e=t.dom.parentNode;if(!e)return;let i=e.appendChild(document.createElement("textarea"));i.style.cssText="position: fixed; left: -10000px; top: 10px";i.focus();setTimeout((()=>{t.focus();i.remove();mi(t,i.value)}),50)}function mi(t,e){let{state:i}=t,o,n=1,r=i.toText(e);let l=r.lines==i.selection.ranges.length;let h=Bi!=null&&i.selection.ranges.every((t=>t.empty))&&Bi==r.toString();if(h){let t=-1;o=i.changeByRange((o=>{let h=i.doc.lineAt(o.from);if(h.from==t)return{range:o};t=h.from;let a=i.toText((l?r.line(n++).text:e)+i.lineBreak);return{changes:{from:h.from,insert:a},range:s.EditorSelection.cursor(o.from+a.length)}}))}else if(l){o=i.changeByRange((t=>{let e=r.line(n++);return{changes:{from:t.from,to:t.to,insert:e.text},range:s.EditorSelection.cursor(t.from+e.length)}}))}else{o=i.replaceSelection(r)}t.dispatch(o,{userEvent:"input.paste",scrollIntoView:true})}di.keydown=(t,e)=>{t.inputState.setSelectionOrigin("select");if(e.keyCode==27)t.inputState.lastEscPress=Date.now()};di.touchstart=(t,e)=>{t.inputState.lastTouchTime=Date.now();t.inputState.setSelectionOrigin("select.pointer")};di.touchmove=t=>{t.inputState.setSelectionOrigin("select.pointer")};ui.touchstart=ui.touchmove={passive:true};di.mousedown=(t,e)=>{t.observer.flush();if(t.inputState.lastTouchTime>Date.now()-2e3)return;let i=null;for(let s of t.state.facet(Wt)){i=s(t,e);if(i)break}if(!i&&e.button==0)i=Di(t,e);if(i){let s=t.root.activeElement!=t.contentDOM;t.inputState.startMouseSelection(new li(t,e,i,s));if(s)t.observer.ignore((()=>R(t.contentDOM)));if(t.inputState.mouseSelection)t.inputState.mouseSelection.start(e)}};function wi(t,e,i,o){if(o==1){return s.EditorSelection.cursor(e,i)}else if(o==2){return We(t.state,e,i)}else{let i=Rt.find(t.docView,e),o=t.state.doc.lineAt(i?i.posAtEnd:e);let n=i?i.posAtStart:o.from,r=i?i.posAtEnd:o.to;if(r<t.state.doc.length&&r==o.to)r++;return s.EditorSelection.range(n,r)}}let vi=(t,e)=>t>=e.top&&t<=e.bottom;let bi=(t,e,i)=>vi(e,i)&&t>=i.left&&t<=i.right;function yi(t,e,i,s){let o=Rt.find(t.docView,e);if(!o)return 1;let n=e-o.posAtStart;if(n==0)return 1;if(n==o.length)return-1;let r=o.coordsAt(n,-1);if(r&&bi(i,s,r))return-1;let l=o.coordsAt(n,1);if(l&&bi(i,s,l))return 1;return r&&vi(s,r)?-1:1}function Si(t,e){let i=t.posAtCoords({x:e.clientX,y:e.clientY},false);return{pos:i,bias:yi(t,i,e.clientX,e.clientY)}}const xi=it.ie&&it.ie_version<=11;let Mi=null,ki=0,Ci=0;function Ai(t){if(!xi)return t.detail;let e=Mi,i=Ci;Mi=t;Ci=Date.now();return ki=!e||i>Date.now()-400&&Math.abs(e.clientX-t.clientX)<2&&Math.abs(e.clientY-t.clientY)<2?(ki+1)%3:1}function Di(t,e){let i=Si(t,e),o=Ai(e);let n=t.state.selection;return{update(t){if(t.docChanged){i.pos=t.changes.mapPos(i.pos);n=n.map(t.changes)}},get(e,r,l){let h=Si(t,e),a;let c=wi(t,h.pos,h.bias,o);if(i.pos!=h.pos&&!r){let e=wi(t,i.pos,i.bias,o);let n=Math.min(e.from,c.from),r=Math.max(e.to,c.to);c=n<c.from?s.EditorSelection.range(n,r):s.EditorSelection.range(r,n)}if(r)return n.replaceRange(n.main.extend(c.from,c.to));else if(l&&o==1&&n.ranges.length>1&&(a=Ti(n,h.pos)))return a;else if(l)return n.addRange(c);else return s.EditorSelection.create([c])}}}function Ti(t,e){for(let i=0;i<t.ranges.length;i++){let{from:o,to:n}=t.ranges[i];if(o<=e&&n>=e)return s.EditorSelection.create(t.ranges.slice(0,i).concat(t.ranges.slice(i+1)),t.mainIndex==i?0:t.mainIndex-(t.mainIndex>i?1:0))}return null}di.dragstart=(t,e)=>{let{selection:{main:i}}=t.state;let{mouseSelection:s}=t.inputState;if(s)s.dragging=i;if(e.dataTransfer){e.dataTransfer.setData("Text",t.state.sliceDoc(i.from,i.to));e.dataTransfer.effectAllowed="copyMove"}};function Oi(t,e,i,s){if(!i)return;let o=t.posAtCoords({x:e.clientX,y:e.clientY},false);e.preventDefault();let{mouseSelection:n}=t.inputState;let r=s&&n&&n.dragging&&n.dragMove?{from:n.dragging.from,to:n.dragging.to}:null;let l={from:o,insert:i};let h=t.state.changes(r?[r,l]:l);t.focus();t.dispatch({changes:h,selection:{anchor:h.mapPos(o,-1),head:h.mapPos(o,1)},userEvent:r?"move.drop":"input.drop"})}di.drop=(t,e)=>{if(!e.dataTransfer)return;if(t.state.readOnly)return e.preventDefault();let i=e.dataTransfer.files;if(i&&i.length){e.preventDefault();let s=Array(i.length),o=0;let n=()=>{if(++o==i.length)Oi(t,e,s.filter((t=>t!=null)).join(t.state.lineBreak),false)};for(let t=0;t<i.length;t++){let e=new FileReader;e.onerror=n;e.onload=()=>{if(!/[\x00-\x08\x0e-\x1f]{2}/.test(e.result))s[t]=e.result;n()};e.readAsText(i[t])}}else{Oi(t,e,e.dataTransfer.getData("Text"),true)}};di.paste=(t,e)=>{if(t.state.readOnly)return e.preventDefault();t.observer.flush();let i=pi?null:e.clipboardData;if(i){mi(t,i.getData("text/plain")||i.getData("text/uri-text"));e.preventDefault()}else{gi(t)}};function Ei(t,e){let i=t.dom.parentNode;if(!i)return;let s=i.appendChild(document.createElement("textarea"));s.style.cssText="position: fixed; left: -10000px; top: 10px";s.value=e;s.focus();s.selectionEnd=e.length;s.selectionStart=0;setTimeout((()=>{s.remove();t.focus()}),50)}function Ri(t){let e=[],i=[],s=false;for(let o of t.selection.ranges)if(!o.empty){e.push(t.sliceDoc(o.from,o.to));i.push(o)}if(!e.length){let o=-1;for(let{from:s}of t.selection.ranges){let n=t.doc.lineAt(s);if(n.number>o){e.push(n.text);i.push({from:n.from,to:Math.min(t.doc.length,n.to+1)})}o=n.number}s=true}return{text:e.join(t.lineBreak),ranges:i,linewise:s}}let Bi=null;di.copy=di.cut=(t,e)=>{let{text:i,ranges:s,linewise:o}=Ri(t.state);if(!i&&!o)return;Bi=o?i:null;let n=pi?null:e.clipboardData;if(n){e.preventDefault();n.clearData();n.setData("text/plain",i)}else{Ei(t,i)}if(e.type=="cut"&&!t.state.readOnly)t.dispatch({changes:s,scrollIntoView:true,userEvent:"delete.cut"})};const Li=s.Annotation.define();function Hi(t,e){let i=[];for(let s of t.facet(Kt)){let o=s(t,e);if(o)i.push(o)}return i?t.update({effects:i,annotations:Li.of(true)}):null}function Pi(t){setTimeout((()=>{let e=t.hasFocus;if(e!=t.inputState.notifiedFocused){let i=Hi(t.state,e);if(i)t.dispatch(i);else t.update([])}}),10)}di.focus=t=>{t.inputState.lastFocusTime=Date.now();if(!t.scrollDOM.scrollTop&&(t.inputState.lastScrollTop||t.inputState.lastScrollLeft)){t.scrollDOM.scrollTop=t.inputState.lastScrollTop;t.scrollDOM.scrollLeft=t.inputState.lastScrollLeft}Pi(t)};di.blur=t=>{t.observer.clearSelectionRange();Pi(t)};di.compositionstart=di.compositionupdate=t=>{if(t.inputState.compositionFirstChange==null)t.inputState.compositionFirstChange=true;if(t.inputState.composing<0){t.inputState.composing=0}};di.compositionend=t=>{t.inputState.composing=-1;t.inputState.compositionEndedAt=Date.now();t.inputState.compositionPendingKey=true;t.inputState.compositionPendingChange=t.observer.pendingRecords().length>0;t.inputState.compositionFirstChange=null;if(it.chrome&&it.android){t.observer.flushSoon()}else if(t.inputState.compositionPendingChange){Promise.resolve().then((()=>t.observer.flush()))}else{setTimeout((()=>{if(t.inputState.composing<0&&t.docView.compositionDeco.size)t.update([])}),50)}};di.contextmenu=t=>{t.inputState.lastContextMenu=Date.now()};di.beforeinput=(t,e)=>{var i;let s;if(it.chrome&&it.android&&(s=ii.find((t=>t.inputType==e.inputType)))){t.observer.delayAndroidKey(s.key,s.keyCode);if(s.key=="Backspace"||s.key=="Delete"){let e=((i=window.visualViewport)===null||i===void 0?void 0:i.height)||0;setTimeout((()=>{var i;if((((i=window.visualViewport)===null||i===void 0?void 0:i.height)||0)>e+10&&t.hasFocus){t.contentDOM.blur();t.focus()}}),100)}}};const Vi=["pre-wrap","normal","pre-line","break-spaces"];class Ni{constructor(t){this.lineWrapping=t;this.doc=s.Text.empty;this.heightSamples={};this.lineHeight=14;this.charWidth=7;this.textHeight=14;this.lineLength=30;this.heightChanged=false}heightForGap(t,e){let i=this.doc.lineAt(e).number-this.doc.lineAt(t).number+1;if(this.lineWrapping)i+=Math.max(0,Math.ceil((e-t-i*this.lineLength*.5)/this.lineLength));return this.lineHeight*i}heightForLine(t){if(!this.lineWrapping)return this.lineHeight;let e=1+Math.max(0,Math.ceil((t-this.lineLength)/(this.lineLength-5)));return e*this.lineHeight}setDoc(t){this.doc=t;return this}mustRefreshForWrapping(t){return Vi.indexOf(t)>-1!=this.lineWrapping}mustRefreshForHeights(t){let e=false;for(let i=0;i<t.length;i++){let s=t[i];if(s<0){i++}else if(!this.heightSamples[Math.floor(s*10)]){e=true;this.heightSamples[Math.floor(s*10)]=true}}return e}refresh(t,e,i,s,o,n){let r=Vi.indexOf(t)>-1;let l=Math.round(e)!=Math.round(this.lineHeight)||this.lineWrapping!=r;this.lineWrapping=r;this.lineHeight=e;this.charWidth=i;this.textHeight=s;this.lineLength=o;if(l){this.heightSamples={};for(let t=0;t<n.length;t++){let e=n[t];if(e<0)t++;else this.heightSamples[Math.floor(e*10)]=true}}return l}}class Wi{constructor(t,e){this.from=t;this.heights=e;this.index=0}get more(){return this.index<this.heights.length}}class Fi{constructor(t,e,i,s,o){this.from=t;this.length=e;this.top=i;this.height=s;this.type=o}get to(){return this.from+this.length}get bottom(){return this.top+this.height}join(t){let e=(Array.isArray(this.type)?this.type:[this]).concat(Array.isArray(t.type)?t.type:[t]);return new Fi(this.from,this.length+t.length,this.top,this.height+t.height,e)}}var zi=function(t){t[t["ByPos"]=0]="ByPos";t[t["ByHeight"]=1]="ByHeight";t[t["ByPosNoHeight"]=2]="ByPosNoHeight";return t}(zi||(zi={}));const Ii=.001;class Ki{constructor(t,e,i=2){this.length=t;this.height=e;this.flags=i}get outdated(){return(this.flags&2)>0}set outdated(t){this.flags=(t?2:0)|this.flags&~2}setHeight(t,e){if(this.height!=e){if(Math.abs(this.height-e)>Ii)t.heightChanged=true;this.height=e}}replace(t,e,i){return Ki.of(i)}decomposeLeft(t,e){e.push(this)}decomposeRight(t,e){e.push(this)}applyChanges(t,e,i,s){let o=this,n=i.doc;for(let r=s.length-1;r>=0;r--){let{fromA:l,toA:h,fromB:a,toB:c}=s[r];let f=o.lineAt(l,zi.ByPosNoHeight,i.setDoc(e),0,0);let d=f.to>=h?f:o.lineAt(h,zi.ByPosNoHeight,i,0,0);c+=d.to-h;h=d.to;while(r>0&&f.from<=s[r-1].toA){l=s[r-1].fromA;a=s[r-1].fromB;r--;if(l<f.from)f=o.lineAt(l,zi.ByPosNoHeight,i,0,0)}a+=f.from-l;l=f.from;let u=Xi.build(i.setDoc(n),t,a,c);o=o.replace(l,h,u)}return o.updateHeight(i,0)}static empty(){return new Gi(0,0)}static of(t){if(t.length==1)return t[0];let e=0,i=t.length,s=0,o=0;for(;;){if(e==i){if(s>o*2){let o=t[e-1];if(o.break)t.splice(--e,1,o.left,null,o.right);else t.splice(--e,1,o.left,o.right);i+=1+o.break;s-=o.size}else if(o>s*2){let e=t[i];if(e.break)t.splice(i,1,e.left,null,e.right);else t.splice(i,1,e.left,e.right);i+=2+e.break;o-=e.size}else{break}}else if(s<o){let i=t[e++];if(i)s+=i.size}else{let e=t[--i];if(e)o+=e.size}}let n=0;if(t[e-1]==null){n=1;e--}else if(t[e]==null){n=1;i++}return new $i(Ki.of(t.slice(0,e)),n,Ki.of(t.slice(i)))}}Ki.prototype.size=1;class qi extends Ki{constructor(t,e,i){super(t,e);this.type=i}blockAt(t,e,i,s){return new Fi(s,this.length,i,this.height,this.type)}lineAt(t,e,i,s,o){return this.blockAt(0,i,s,o)}forEachLine(t,e,i,s,o,n){if(t<=o+this.length&&e>=o)n(this.blockAt(0,i,s,o))}updateHeight(t,e=0,i=false,s){if(s&&s.from<=e&&s.more)this.setHeight(t,s.heights[s.index++]);this.outdated=false;return this}toString(){return`block(${this.length})`}}class Gi extends qi{constructor(t,e){super(t,e,Mt.Text);this.collapsed=0;this.widgetHeight=0}replace(t,e,i){let s=i[0];if(i.length==1&&(s instanceof Gi||s instanceof ji&&s.flags&4)&&Math.abs(this.length-s.length)<10){if(s instanceof ji)s=new Gi(s.length,this.height);else s.height=this.height;if(!this.outdated)s.outdated=false;return s}else{return Ki.of(i)}}updateHeight(t,e=0,i=false,s){if(s&&s.from<=e&&s.more)this.setHeight(t,s.heights[s.index++]);else if(i||this.outdated)this.setHeight(t,Math.max(this.widgetHeight,t.heightForLine(this.length-this.collapsed)));this.outdated=false;return this}toString(){return`line(${this.length}${this.collapsed?-this.collapsed:""}${this.widgetHeight?":"+this.widgetHeight:""})`}}class ji extends Ki{constructor(t){super(t,0)}heightMetrics(t,e){let i=t.doc.lineAt(e).number,s=t.doc.lineAt(e+this.length).number;let o=s-i+1;let n,r=0;if(t.lineWrapping){let e=Math.min(this.height,t.lineHeight*o);n=e/o;if(this.length>o+1)r=(this.height-e)/(this.length-o-1)}else{n=this.height/o}return{firstLine:i,lastLine:s,perLine:n,perChar:r}}blockAt(t,e,i,s){let{firstLine:o,lastLine:n,perLine:r,perChar:l}=this.heightMetrics(e,s);if(e.lineWrapping){let o=s+Math.round(Math.max(0,Math.min(1,(t-i)/this.height))*this.length);let n=e.doc.lineAt(o),h=r+n.length*l;let a=Math.max(i,t-h/2);return new Fi(n.from,n.length,a,h,Mt.Text)}else{let s=Math.max(0,Math.min(n-o,Math.floor((t-i)/r)));let{from:l,length:h}=e.doc.line(o+s);return new Fi(l,h,i+r*s,r,Mt.Text)}}lineAt(t,e,i,s,o){if(e==zi.ByHeight)return this.blockAt(t,i,s,o);if(e==zi.ByPosNoHeight){let{from:e,to:s}=i.doc.lineAt(t);return new Fi(e,s-e,0,0,Mt.Text)}let{firstLine:n,perLine:r,perChar:l}=this.heightMetrics(i,o);let h=i.doc.lineAt(t),a=r+h.length*l;let c=h.number-n;let f=s+r*c+l*(h.from-o-c);return new Fi(h.from,h.length,Math.max(s,Math.min(f,s+this.height-a)),a,Mt.Text)}forEachLine(t,e,i,s,o,n){t=Math.max(t,o);e=Math.min(e,o+this.length);let{firstLine:r,perLine:l,perChar:h}=this.heightMetrics(i,o);for(let a=t,c=s;a<=e;){let e=i.doc.lineAt(a);if(a==t){let i=e.number-r;c+=l*i+h*(t-o-i)}let s=l+h*e.length;n(new Fi(e.from,e.length,c,s,Mt.Text));c+=s;a=e.to+1}}replace(t,e,i){let s=this.length-e;if(s>0){let t=i[i.length-1];if(t instanceof ji)i[i.length-1]=new ji(t.length+s);else i.push(null,new ji(s-1))}if(t>0){let e=i[0];if(e instanceof ji)i[0]=new ji(t+e.length);else i.unshift(new ji(t-1),null)}return Ki.of(i)}decomposeLeft(t,e){e.push(new ji(t-1),null)}decomposeRight(t,e){e.push(null,new ji(this.length-t-1))}updateHeight(t,e=0,i=false,s){let o=e+this.length;if(s&&s.from<=e+this.length&&s.more){let i=[],n=Math.max(e,s.from),r=-1;if(s.from>e)i.push(new ji(s.from-e-1).updateHeight(t,e));while(n<=o&&s.more){let e=t.doc.lineAt(n).length;if(i.length)i.push(null);let o=s.heights[s.index++];if(r==-1)r=o;else if(Math.abs(o-r)>=Ii)r=-2;let l=new Gi(e,o);l.outdated=false;i.push(l);n+=e+1}if(n<=o)i.push(null,new ji(o-n).updateHeight(t,n));let l=Ki.of(i);if(r<0||Math.abs(l.height-this.height)>=Ii||Math.abs(r-this.heightMetrics(t,e).perLine)>=Ii)t.heightChanged=true;return l}else if(i||this.outdated){this.setHeight(t,t.heightForGap(e,e+this.length));this.outdated=false}return this}toString(){return`gap(${this.length})`}}class $i extends Ki{constructor(t,e,i){super(t.length+e+i.length,t.height+i.height,e|(t.outdated||i.outdated?2:0));this.left=t;this.right=i;this.size=t.size+i.size}get break(){return this.flags&1}blockAt(t,e,i,s){let o=i+this.left.height;return t<o?this.left.blockAt(t,e,i,s):this.right.blockAt(t,e,o,s+this.left.length+this.break)}lineAt(t,e,i,s,o){let n=s+this.left.height,r=o+this.left.length+this.break;let l=e==zi.ByHeight?t<n:t<r;let h=l?this.left.lineAt(t,e,i,s,o):this.right.lineAt(t,e,i,n,r);if(this.break||(l?h.to<r:h.from>r))return h;let a=e==zi.ByPosNoHeight?zi.ByPosNoHeight:zi.ByPos;if(l)return h.join(this.right.lineAt(r,a,i,n,r));else return this.left.lineAt(r,a,i,s,o).join(h)}forEachLine(t,e,i,s,o,n){let r=s+this.left.height,l=o+this.left.length+this.break;if(this.break){if(t<l)this.left.forEachLine(t,e,i,s,o,n);if(e>=l)this.right.forEachLine(t,e,i,r,l,n)}else{let h=this.lineAt(l,zi.ByPos,i,s,o);if(t<h.from)this.left.forEachLine(t,h.from-1,i,s,o,n);if(h.to>=t&&h.from<=e)n(h);if(e>h.to)this.right.forEachLine(h.to+1,e,i,r,l,n)}}replace(t,e,i){let s=this.left.length+this.break;if(e<s)return this.balanced(this.left.replace(t,e,i),this.right);if(t>this.left.length)return this.balanced(this.left,this.right.replace(t-s,e-s,i));let o=[];if(t>0)this.decomposeLeft(t,o);let n=o.length;for(let r of i)o.push(r);if(t>0)_i(o,n-1);if(e<this.length){let t=o.length;this.decomposeRight(e,o);_i(o,t)}return Ki.of(o)}decomposeLeft(t,e){let i=this.left.length;if(t<=i)return this.left.decomposeLeft(t,e);e.push(this.left);if(this.break){i++;if(t>=i)e.push(null)}if(t>i)this.right.decomposeLeft(t-i,e)}decomposeRight(t,e){let i=this.left.length,s=i+this.break;if(t>=s)return this.right.decomposeRight(t-s,e);if(t<i)this.left.decomposeRight(t,e);if(this.break&&t<s)e.push(null);e.push(this.right)}balanced(t,e){if(t.size>2*e.size||e.size>2*t.size)return Ki.of(this.break?[t,null,e]:[t,e]);this.left=t;this.right=e;this.height=t.height+e.height;this.outdated=t.outdated||e.outdated;this.size=t.size+e.size;this.length=t.length+this.break+e.length;return this}updateHeight(t,e=0,i=false,s){let{left:o,right:n}=this,r=e+o.length+this.break,l=null;if(s&&s.from<=e+o.length&&s.more)l=o=o.updateHeight(t,e,i,s);else o.updateHeight(t,e,i);if(s&&s.from<=r+n.length&&s.more)l=n=n.updateHeight(t,r,i,s);else n.updateHeight(t,r,i);if(l)return this.balanced(o,n);this.height=this.left.height+this.right.height;this.outdated=false;return this}toString(){return this.left+(this.break?" ":"-")+this.right}}function _i(t,e){let i,s;if(t[e]==null&&(i=t[e-1])instanceof ji&&(s=t[e+1])instanceof ji)t.splice(e-1,3,new ji(i.length+1+s.length))}const Ui=5;class Xi{constructor(t,e){this.pos=t;this.oracle=e;this.nodes=[];this.lineStart=-1;this.lineEnd=-1;this.covering=null;this.writtenTo=t}get isCovered(){return this.covering&&this.nodes[this.nodes.length-1]==this.covering}span(t,e){if(this.lineStart>-1){let t=Math.min(e,this.lineEnd),i=this.nodes[this.nodes.length-1];if(i instanceof Gi)i.length+=t-this.pos;else if(t>this.pos||!this.isCovered)this.nodes.push(new Gi(t-this.pos,-1));this.writtenTo=t;if(e>t){this.nodes.push(null);this.writtenTo++;this.lineStart=-1}}this.pos=e}point(t,e,i){if(t<e||i.heightRelevant){let s=i.widget?i.widget.estimatedHeight:0;if(s<0)s=this.oracle.lineHeight;let o=e-t;if(i.block){this.addBlock(new qi(o,s,i.type))}else if(o||s>=Ui){this.addLineDeco(s,o)}}else if(e>t){this.span(t,e)}if(this.lineEnd>-1&&this.lineEnd<this.pos)this.lineEnd=this.oracle.doc.lineAt(this.pos).to}enterLine(){if(this.lineStart>-1)return;let{from:t,to:e}=this.oracle.doc.lineAt(this.pos);this.lineStart=t;this.lineEnd=e;if(this.writtenTo<t){if(this.writtenTo<t-1||this.nodes[this.nodes.length-1]==null)this.nodes.push(this.blankContent(this.writtenTo,t-1));this.nodes.push(null)}if(this.pos>t)this.nodes.push(new Gi(this.pos-t,-1));this.writtenTo=this.pos}blankContent(t,e){let i=new ji(e-t);if(this.oracle.doc.lineAt(t).to==e)i.flags|=4;return i}ensureLine(){this.enterLine();let t=this.nodes.length?this.nodes[this.nodes.length-1]:null;if(t instanceof Gi)return t;let e=new Gi(0,-1);this.nodes.push(e);return e}addBlock(t){this.enterLine();if(t.type==Mt.WidgetAfter&&!this.isCovered)this.ensureLine();this.nodes.push(t);this.writtenTo=this.pos=this.pos+t.length;if(t.type!=Mt.WidgetBefore)this.covering=t}addLineDeco(t,e){let i=this.ensureLine();i.length+=e;i.collapsed+=e;i.widgetHeight=Math.max(i.widgetHeight,t);this.writtenTo=this.pos=this.pos+e}finish(t){let e=this.nodes.length==0?null:this.nodes[this.nodes.length-1];if(this.lineStart>-1&&!(e instanceof Gi)&&!this.isCovered)this.nodes.push(new Gi(0,-1));else if(this.writtenTo<this.pos||e==null)this.nodes.push(this.blankContent(this.writtenTo,this.pos));let i=t;for(let s of this.nodes){if(s instanceof Gi)s.updateHeight(this.oracle,i);i+=s?s.length:1}return this.nodes}static build(t,e,i,o){let n=new Xi(i,t);s.RangeSet.spans(e,i,o,n,0);return n.finish(i)}}function Yi(t,e,i){let o=new Qi;s.RangeSet.compare(t,e,i,o,0);return o.changes}class Qi{constructor(){this.changes=[]}compareRange(){}comparePoint(t,e,i,s){if(t<e||i&&i.heightRelevant||s&&s.heightRelevant)Et(t,e,this.changes,5)}}function Ji(t,e){let i=t.getBoundingClientRect();let s=t.ownerDocument,o=s.defaultView||window;let n=Math.max(0,i.left),r=Math.min(o.innerWidth,i.right);let l=Math.max(0,i.top),h=Math.min(o.innerHeight,i.bottom);for(let a=t.parentNode;a&&a!=s.body;){if(a.nodeType==1){let e=a;let i=window.getComputedStyle(e);if((e.scrollHeight>e.clientHeight||e.scrollWidth>e.clientWidth)&&i.overflow!="visible"){let i=e.getBoundingClientRect();n=Math.max(n,i.left);r=Math.min(r,i.right);l=Math.max(l,i.top);h=a==t.parentNode?i.bottom:Math.min(h,i.bottom)}a=i.position=="absolute"||i.position=="fixed"?e.offsetParent:e.parentNode}else if(a.nodeType==11){a=a.host}else{break}}return{left:n-i.left,right:Math.max(n,r)-i.left,top:l-(i.top+e),bottom:Math.max(l,h)-(i.top+e)}}function Zi(t,e){let i=t.getBoundingClientRect();return{left:0,right:i.right-i.left,top:e,bottom:i.bottom-(i.top+e)}}class ts{constructor(t,e,i){this.from=t;this.to=e;this.size=i}static same(t,e){if(t.length!=e.length)return false;for(let i=0;i<t.length;i++){let s=t[i],o=e[i];if(s.from!=o.from||s.to!=o.to||s.size!=o.size)return false}return true}draw(t){return kt.replace({widget:new es(this.size,t)}).range(this.from,this.to)}}class es extends xt{constructor(t,e){super();this.size=t;this.vertical=e}eq(t){return t.size==this.size&&t.vertical==this.vertical}toDOM(){let t=document.createElement("div");if(this.vertical){t.style.height=this.size+"px"}else{t.style.width=this.size+"px";t.style.height="2px";t.style.display="inline-block"}return t}get estimatedHeight(){return this.vertical?this.size:-1}}class is{constructor(t){this.state=t;this.pixelViewport={left:0,right:window.innerWidth,top:0,bottom:0};this.inView=true;this.paddingTop=0;this.paddingBottom=0;this.contentDOMWidth=0;this.contentDOMHeight=0;this.editorHeight=0;this.editorWidth=0;this.scaler=hs;this.scrollTarget=null;this.printing=false;this.mustMeasureContent=true;this.defaultTextDirection=le.LTR;this.visibleRanges=[];this.mustEnforceCursorAssoc=false;let e=t.facet(te).some((t=>typeof t!="function"&&t.class=="cm-lineWrapping"));this.heightOracle=new Ni(e);this.stateDeco=t.facet(ee).filter((t=>typeof t!="function"));this.heightMap=Ki.empty().applyChanges(this.stateDeco,s.Text.empty,this.heightOracle.setDoc(t.doc),[new ne(0,0,0,t.doc.length)]);this.viewport=this.getViewport(0,null);this.updateViewportLines();this.updateForViewport();this.lineGaps=this.ensureLineGaps([]);this.lineGapDeco=kt.set(this.lineGaps.map((t=>t.draw(false))));this.computeVisibleRanges()}updateForViewport(){let t=[this.viewport],{main:e}=this.state.selection;for(let i=0;i<=1;i++){let s=i?e.head:e.anchor;if(!t.some((({from:t,to:e})=>s>=t&&s<=e))){let{from:e,to:i}=this.lineBlockAt(s);t.push(new ss(e,i))}}this.viewports=t.sort(((t,e)=>t.from-e.from));this.scaler=this.heightMap.height<=7e6?hs:new as(this.heightOracle,this.heightMap,this.viewports)}updateViewportLines(){this.viewportLines=[];this.heightMap.forEachLine(this.viewport.from,this.viewport.to,this.heightOracle.setDoc(this.state.doc),0,0,(t=>{this.viewportLines.push(this.scaler.scale==1?t:cs(t,this.scaler))}))}update(t,e=null){this.state=t.state;let i=this.stateDeco;this.stateDeco=this.state.facet(ee).filter((t=>typeof t!="function"));let o=t.changedRanges;let n=ne.extendWithRanges(o,Yi(i,this.stateDeco,t?t.changes:s.ChangeSet.empty(this.state.doc.length)));let r=this.heightMap.height;this.heightMap=this.heightMap.applyChanges(this.stateDeco,t.startState.doc,this.heightOracle.setDoc(this.state.doc),n);if(this.heightMap.height!=r)t.flags|=2;let l=n.length?this.mapViewport(this.viewport,t.changes):this.viewport;if(e&&(e.range.head<l.from||e.range.head>l.to)||!this.viewportIsAppropriate(l))l=this.getViewport(0,e);let h=!t.changes.empty||t.flags&2||l.from!=this.viewport.from||l.to!=this.viewport.to;this.viewport=l;this.updateForViewport();if(h)this.updateViewportLines();if(this.lineGaps.length||this.viewport.to-this.viewport.from>2e3<<1)this.updateLineGaps(this.ensureLineGaps(this.mapLineGaps(this.lineGaps,t.changes)));t.flags|=this.computeVisibleRanges();if(e)this.scrollTarget=e;if(!this.mustEnforceCursorAssoc&&t.selectionSet&&t.view.lineWrapping&&t.state.selection.main.empty&&t.state.selection.main.assoc&&!t.state.facet(Gt))this.mustEnforceCursorAssoc=true}measure(t){let e=t.contentDOM,i=window.getComputedStyle(e);let o=this.heightOracle;let n=i.whiteSpace;this.defaultTextDirection=i.direction=="rtl"?le.RTL:le.LTR;let r=this.heightOracle.mustRefreshForWrapping(n);let l=e.getBoundingClientRect();let h=r||this.mustMeasureContent||this.contentDOMHeight!=l.height;this.contentDOMHeight=l.height;this.mustMeasureContent=false;let a=0,c=0;let f=parseInt(i.paddingTop)||0,d=parseInt(i.paddingBottom)||0;if(this.paddingTop!=f||this.paddingBottom!=d){this.paddingTop=f;this.paddingBottom=d;a|=8|2}if(this.editorWidth!=t.scrollDOM.clientWidth){if(o.lineWrapping)h=true;this.editorWidth=t.scrollDOM.clientWidth;a|=8}let u=(this.printing?Zi:Ji)(e,this.paddingTop);let p=u.top-this.pixelViewport.top,g=u.bottom-this.pixelViewport.bottom;this.pixelViewport=u;let m=this.pixelViewport.bottom>this.pixelViewport.top&&this.pixelViewport.right>this.pixelViewport.left;if(m!=this.inView){this.inView=m;if(m)h=true}if(!this.inView&&!this.scrollTarget)return 0;let w=l.width;if(this.contentDOMWidth!=w||this.editorHeight!=t.scrollDOM.clientHeight){this.contentDOMWidth=l.width;this.editorHeight=t.scrollDOM.clientHeight;a|=8}if(h){let e=t.docView.measureVisibleLineHeights(this.viewport);if(o.mustRefreshForHeights(e))r=true;if(r||o.lineWrapping&&Math.abs(w-this.contentDOMWidth)>o.charWidth){let{lineHeight:i,charWidth:s,textHeight:l}=t.docView.measureTextSize();r=i>0&&o.refresh(n,i,s,l,w/s,e);if(r){t.docView.minWidth=0;a|=8}}if(p>0&&g>0)c=Math.max(p,g);else if(p<0&&g<0)c=Math.min(p,g);o.heightChanged=false;for(let i of this.viewports){let n=i.from==this.viewport.from?e:t.docView.measureVisibleLineHeights(i);this.heightMap=(r?Ki.empty().applyChanges(this.stateDeco,s.Text.empty,this.heightOracle,[new ne(0,0,0,t.state.doc.length)]):this.heightMap).updateHeight(o,0,r,new Wi(i.from,n))}if(o.heightChanged)a|=2}let v=!this.viewportIsAppropriate(this.viewport,c)||this.scrollTarget&&(this.scrollTarget.range.head<this.viewport.from||this.scrollTarget.range.head>this.viewport.to);if(v)this.viewport=this.getViewport(c,this.scrollTarget);this.updateForViewport();if(a&2||v)this.updateViewportLines();if(this.lineGaps.length||this.viewport.to-this.viewport.from>2e3<<1)this.updateLineGaps(this.ensureLineGaps(r?[]:this.lineGaps,t));a|=this.computeVisibleRanges();if(this.mustEnforceCursorAssoc){this.mustEnforceCursorAssoc=false;t.docView.enforceCursorAssoc()}return a}get visibleTop(){return this.scaler.fromDOM(this.pixelViewport.top)}get visibleBottom(){return this.scaler.fromDOM(this.pixelViewport.bottom)}getViewport(t,e){let i=.5-Math.max(-.5,Math.min(.5,t/1e3/2));let s=this.heightMap,o=this.heightOracle;let{visibleTop:n,visibleBottom:r}=this;let l=new ss(s.lineAt(n-i*1e3,zi.ByHeight,o,0,0).from,s.lineAt(r+(1-i)*1e3,zi.ByHeight,o,0,0).to);if(e){let{head:t}=e.range;if(t<l.from||t>l.to){let i=Math.min(this.editorHeight,this.pixelViewport.bottom-this.pixelViewport.top);let n=s.lineAt(t,zi.ByPos,o,0,0),r;if(e.y=="center")r=(n.top+n.bottom)/2-i/2;else if(e.y=="start"||e.y=="nearest"&&t<l.from)r=n.top;else r=n.bottom-i;l=new ss(s.lineAt(r-1e3/2,zi.ByHeight,o,0,0).from,s.lineAt(r+i+1e3/2,zi.ByHeight,o,0,0).to)}}return l}mapViewport(t,e){let i=e.mapPos(t.from,-1),s=e.mapPos(t.to,1);return new ss(this.heightMap.lineAt(i,zi.ByPos,this.heightOracle,0,0).from,this.heightMap.lineAt(s,zi.ByPos,this.heightOracle,0,0).to)}viewportIsAppropriate({from:t,to:e},i=0){if(!this.inView)return true;let{top:s}=this.heightMap.lineAt(t,zi.ByPos,this.heightOracle,0,0);let{bottom:o}=this.heightMap.lineAt(e,zi.ByPos,this.heightOracle,0,0);let{visibleTop:n,visibleBottom:r}=this;return(t==0||s<=n-Math.max(10,Math.min(-i,250)))&&(e==this.state.doc.length||o>=r+Math.max(10,Math.min(i,250)))&&(s>n-2*1e3&&o<r+2*1e3)}mapLineGaps(t,e){if(!t.length||e.empty)return t;let i=[];for(let s of t)if(!e.touchesRange(s.from,s.to))i.push(new ts(e.mapPos(s.from),e.mapPos(s.to),s.size));return i}ensureLineGaps(t,e){let i=this.heightOracle.lineWrapping;let o=i?1e4:2e3,n=o>>1,r=o<<1;if(this.defaultTextDirection!=le.LTR&&!i)return[];let l=[];let h=(o,r,a,c)=>{if(r-o<n)return;let f=this.state.selection.main,d=[f.from];if(!f.empty)d.push(f.to);for(let t of d){if(t>o&&t<r){h(o,t-10,a,c);h(t+10,r,a,c);return}}let u=ls(t,(t=>t.from>=a.from&&t.to<=a.to&&Math.abs(t.from-o)<n&&Math.abs(t.to-r)<n&&!d.some((e=>t.from<e&&t.to>e))));if(!u){if(r<a.to&&e&&i&&e.visibleRanges.some((t=>t.from<=r&&t.to>=r))){let t=e.moveToLineBoundary(s.EditorSelection.cursor(r),false,true).head;if(t>o)r=t}u=new ts(o,r,this.gapSize(a,o,r,c))}l.push(u)};for(let s of this.viewportLines){if(s.length<r)continue;let t=os(s.from,s.to,this.stateDeco);if(t.total<r)continue;let e=this.scrollTarget?this.scrollTarget.range.head:null;let n,l;if(i){let i=o/this.heightOracle.lineLength*this.heightOracle.lineHeight;let r,h;if(e!=null){let o=rs(t,e);let n=((this.visibleBottom-this.visibleTop)/2+i)/s.height;r=o-n;h=o+n}else{r=(this.visibleTop-s.top-i)/s.height;h=(this.visibleBottom-s.top+i)/s.height}n=ns(t,r);l=ns(t,h)}else{let i=t.total*this.heightOracle.charWidth;let s=o*this.heightOracle.charWidth;let r,h;if(e!=null){let o=rs(t,e);let n=((this.pixelViewport.right-this.pixelViewport.left)/2+s)/i;r=o-n;h=o+n}else{r=(this.pixelViewport.left-s)/i;h=(this.pixelViewport.right+s)/i}n=ns(t,r);l=ns(t,h)}if(n>s.from)h(s.from,n,s,t);if(l<s.to)h(l,s.to,s,t)}return l}gapSize(t,e,i,s){let o=rs(s,i)-rs(s,e);if(this.heightOracle.lineWrapping){return t.height*o}else{return s.total*this.heightOracle.charWidth*o}}updateLineGaps(t){if(!ts.same(t,this.lineGaps)){this.lineGaps=t;this.lineGapDeco=kt.set(t.map((t=>t.draw(this.heightOracle.lineWrapping))))}}computeVisibleRanges(){let t=this.stateDeco;if(this.lineGaps.length)t=t.concat(this.lineGapDeco);let e=[];s.RangeSet.spans(t,this.viewport.from,this.viewport.to,{span(t,i){e.push({from:t,to:i})},point(){}},20);let i=e.length!=this.visibleRanges.length||this.visibleRanges.some(((t,i)=>t.from!=e[i].from||t.to!=e[i].to));this.visibleRanges=e;return i?4:0}lineBlockAt(t){return t>=this.viewport.from&&t<=this.viewport.to&&this.viewportLines.find((e=>e.from<=t&&e.to>=t))||cs(this.heightMap.lineAt(t,zi.ByPos,this.heightOracle,0,0),this.scaler)}lineBlockAtHeight(t){return cs(this.heightMap.lineAt(this.scaler.fromDOM(t),zi.ByHeight,this.heightOracle,0,0),this.scaler)}elementAtHeight(t){return cs(this.heightMap.blockAt(this.scaler.fromDOM(t),this.heightOracle,0,0),this.scaler)}get docHeight(){return this.scaler.toDOM(this.heightMap.height)}get contentHeight(){return this.docHeight+this.paddingTop+this.paddingBottom}}class ss{constructor(t,e){this.from=t;this.to=e}}function os(t,e,i){let o=[],n=t,r=0;s.RangeSet.spans(i,t,e,{span(){},point(t,e){if(t>n){o.push({from:n,to:t});r+=t-n}n=e}},20);if(n<e){o.push({from:n,to:e});r+=e-n}return{total:r,ranges:o}}function ns({total:t,ranges:e},i){if(i<=0)return e[0].from;if(i>=1)return e[e.length-1].to;let s=Math.floor(t*i);for(let o=0;;o++){let{from:t,to:i}=e[o],n=i-t;if(s<=n)return t+s;s-=n}}function rs(t,e){let i=0;for(let{from:s,to:o}of t.ranges){if(e<=o){i+=e-s;break}i+=o-s}return i/t.total}function ls(t,e){for(let i of t)if(e(i))return i;return undefined}const hs={toDOM(t){return t},fromDOM(t){return t},scale:1};class as{constructor(t,e,i){let s=0,o=0,n=0;this.viewports=i.map((({from:i,to:o})=>{let n=e.lineAt(i,zi.ByPos,t,0,0).top;let r=e.lineAt(o,zi.ByPos,t,0,0).bottom;s+=r-n;return{from:i,to:o,top:n,bottom:r,domTop:0,domBottom:0}}));this.scale=(7e6-s)/(e.height-s);for(let r of this.viewports){r.domTop=n+(r.top-o)*this.scale;n=r.domBottom=r.domTop+(r.bottom-r.top);o=r.bottom}}toDOM(t){for(let e=0,i=0,s=0;;e++){let o=e<this.viewports.length?this.viewports[e]:null;if(!o||t<o.top)return s+(t-i)*this.scale;if(t<=o.bottom)return o.domTop+(t-o.top);i=o.bottom;s=o.domBottom}}fromDOM(t){for(let e=0,i=0,s=0;;e++){let o=e<this.viewports.length?this.viewports[e]:null;if(!o||t<o.domTop)return i+(t-s)/this.scale;if(t<=o.domBottom)return o.top+(t-o.domTop);i=o.bottom;s=o.domBottom}}}function cs(t,e){if(e.scale==1)return t;let i=e.toDOM(t.top),s=e.toDOM(t.bottom);return new Fi(t.from,t.length,i,s-i,Array.isArray(t.type)?t.type.map((t=>cs(t,e))):t.type)}const fs=s.Facet.define({combine:t=>t.join(" ")});const ds=s.Facet.define({combine:t=>t.indexOf(true)>-1});const us=o.StyleModule.newName(),ps=o.StyleModule.newName(),gs=o.StyleModule.newName();const ms={"&light":"."+ps,"&dark":"."+gs};function ws(t,e,i){return new o.StyleModule(e,{finish(e){return/&/.test(e)?e.replace(/&\w*/,(e=>{if(e=="&")return t;if(!i||!i[e])throw new RangeError(`Unsupported selector: ${e}`);return i[e]})):t+" "+e}})}const vs=ws("."+us,{"&":{position:"relative !important",boxSizing:"border-box","&.cm-focused":{outline:"1px dotted #212121"},display:"flex !important",flexDirection:"column"},".cm-scroller":{display:"flex !important",alignItems:"flex-start !important",fontFamily:"monospace",lineHeight:1.4,height:"100%",overflowX:"auto",position:"relative",zIndex:0},".cm-content":{margin:0,flexGrow:2,flexShrink:0,display:"block",whiteSpace:"pre",wordWrap:"normal",boxSizing:"border-box",padding:"4px 0",outline:"none","&[contenteditable=true]":{WebkitUserModify:"read-write-plaintext-only"}},".cm-lineWrapping":{whiteSpace_fallback:"pre-wrap",whiteSpace:"break-spaces",wordBreak:"break-word",overflowWrap:"anywhere",flexShrink:1},"&light .cm-content":{caretColor:"black"},"&dark .cm-content":{caretColor:"white"},".cm-line":{display:"block",padding:"0 2px 0 6px"},".cm-layer":{position:"absolute",left:0,top:0,contain:"size style","& > *":{position:"absolute"}},"&light .cm-selectionBackground":{background:"#d9d9d9"},"&dark .cm-selectionBackground":{background:"#222"},"&light.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#d7d4f0"},"&dark.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#233"},".cm-cursorLayer":{pointerEvents:"none"},"&.cm-focused > .cm-scroller > .cm-cursorLayer":{animation:"steps(1) cm-blink 1.2s infinite"},"@keyframes cm-blink":{"0%":{},"50%":{opacity:0},"100%":{}},"@keyframes cm-blink2":{"0%":{},"50%":{opacity:0},"100%":{}},".cm-cursor, .cm-dropCursor":{borderLeft:"1.2px solid black",marginLeft:"-0.6px",pointerEvents:"none"},".cm-cursor":{display:"none"},"&dark .cm-cursor":{borderLeftColor:"#444"},".cm-dropCursor":{position:"absolute"},"&.cm-focused > .cm-scroller > .cm-cursorLayer .cm-cursor":{display:"block"},"&light .cm-activeLine":{backgroundColor:"#cceeff44"},"&dark .cm-activeLine":{backgroundColor:"#99eeff33"},"&light .cm-specialChar":{color:"red"},"&dark .cm-specialChar":{color:"#f78"},".cm-gutters":{flexShrink:0,display:"flex",height:"100%",boxSizing:"border-box",left:0,zIndex:200},"&light .cm-gutters":{backgroundColor:"#f5f5f5",color:"#6c6c6c",borderRight:"1px solid #ddd"},"&dark .cm-gutters":{backgroundColor:"#333338",color:"#ccc"},".cm-gutter":{display:"flex !important",flexDirection:"column",flexShrink:0,boxSizing:"border-box",minHeight:"100%",overflow:"hidden"},".cm-gutterElement":{boxSizing:"border-box"},".cm-lineNumbers .cm-gutterElement":{padding:"0 3px 0 5px",minWidth:"20px",textAlign:"right",whiteSpace:"nowrap"},"&light .cm-activeLineGutter":{backgroundColor:"#e2f2ff"},"&dark .cm-activeLineGutter":{backgroundColor:"#222227"},".cm-panels":{boxSizing:"border-box",position:"sticky",left:0,right:0},"&light .cm-panels":{backgroundColor:"#f5f5f5",color:"black"},"&light .cm-panels-top":{borderBottom:"1px solid #ddd"},"&light .cm-panels-bottom":{borderTop:"1px solid #ddd"},"&dark .cm-panels":{backgroundColor:"#333338",color:"white"},".cm-tab":{display:"inline-block",overflow:"hidden",verticalAlign:"bottom"},".cm-widgetBuffer":{verticalAlign:"text-top",height:"1em",width:0,display:"inline"},".cm-placeholder":{color:"#888",display:"inline-block",verticalAlign:"top"},".cm-highlightSpace:before":{content:"attr(data-display)",position:"absolute",pointerEvents:"none",color:"#888"},".cm-highlightTab":{backgroundImage:`url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="20"><path stroke="%23888" stroke-width="1" fill="none" d="M1 10H196L190 5M190 15L196 10M197 4L197 16"/></svg>')`,backgroundSize:"auto 100%",backgroundPosition:"right 90%",backgroundRepeat:"no-repeat"},".cm-trailingSpace":{backgroundColor:"#ff332255"},".cm-button":{verticalAlign:"middle",color:"inherit",fontSize:"70%",padding:".2em 1em",borderRadius:"1px"},"&light .cm-button":{backgroundImage:"linear-gradient(#eff1f5, #d9d9df)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#b4b4b4, #d0d3d6)"}},"&dark .cm-button":{backgroundImage:"linear-gradient(#393939, #111)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#111, #333)"}},".cm-textfield":{verticalAlign:"middle",color:"inherit",fontSize:"70%",border:"1px solid silver",padding:".2em .5em"},"&light .cm-textfield":{backgroundColor:"white"},"&dark .cm-textfield":{border:"1px solid #555",backgroundColor:"inherit"}},ms);class bs{constructor(t,e,i,o){this.typeOver=o;this.bounds=null;this.text="";let{impreciseHead:n,impreciseAnchor:r}=t.docView;if(t.state.readOnly&&e>-1){this.newSel=null}else if(e>-1&&(this.bounds=t.docView.domBoundsAround(e,i,0))){let e=n||r?[]:xs(t);let i=new ke(e,t.state);i.readRange(this.bounds.startDOM,this.bounds.endDOM);this.text=i.text;this.newSel=Ms(e,this.bounds.from)}else{let e=t.observer.selectionRange;let i=n&&n.node==e.focusNode&&n.offset==e.focusOffset||!m(t.contentDOM,e.focusNode)?t.state.selection.main.head:t.docView.posFromDOM(e.focusNode,e.focusOffset);let o=r&&r.node==e.anchorNode&&r.offset==e.anchorOffset||!m(t.contentDOM,e.anchorNode)?t.state.selection.main.anchor:t.docView.posFromDOM(e.anchorNode,e.anchorOffset);this.newSel=s.EditorSelection.single(o,i)}}}function ys(t,e){let i;let{newSel:o}=e,n=t.state.selection.main;if(e.bounds){let{from:o,to:r}=e.bounds;let l=n.from,h=null;if(t.inputState.lastKeyCode===8&&t.inputState.lastKeyTime>Date.now()-100||it.android&&e.text.length<r-o){l=n.to;h="end"}let a=Ss(t.state.doc.sliceString(o,r,Me),e.text,l-o,h);if(a){if(it.chrome&&t.inputState.lastKeyCode==13&&a.toB==a.from+2&&e.text.slice(a.from,a.toB)==Me+Me)a.toB--;i={from:o+a.from,to:o+a.toA,insert:s.Text.of(e.text.slice(a.from,a.toB).split(Me))}}}else if(o&&(!t.hasFocus&&t.state.facet(Ut)||o.main.eq(n))){o=null}if(!i&&!o)return false;if(!i&&e.typeOver&&!n.empty&&o&&o.main.empty){i={from:n.from,to:n.to,insert:t.state.doc.slice(n.from,n.to)}}else if(i&&i.from>=n.from&&i.to<=n.to&&(i.from!=n.from||i.to!=n.to)&&n.to-n.from-(i.to-i.from)<=4){i={from:n.from,to:n.to,insert:t.state.doc.slice(n.from,i.from).append(i.insert).append(t.state.doc.slice(i.to,n.to))}}else if((it.mac||it.android)&&i&&i.from==i.to&&i.from==n.head-1&&/^\. ?$/.test(i.insert.toString())&&t.contentDOM.getAttribute("autocorrect")=="off"){if(o&&i.insert.length==2)o=s.EditorSelection.single(o.main.anchor-1,o.main.head-1);i={from:n.from,to:n.to,insert:s.Text.of([" "])}}else if(it.chrome&&i&&i.from==i.to&&i.from==n.head&&i.insert.toString()=="\n "&&t.lineWrapping){if(o)o=s.EditorSelection.single(o.main.anchor-1,o.main.head-1);i={from:n.from,to:n.to,insert:s.Text.of([" "])}}if(i){let e=t.state;if(it.ios&&t.inputState.flushIOSKey(t))return true;if(it.android&&(i.from==n.from&&i.to==n.to&&i.insert.length==1&&i.insert.lines==2&&H(t.contentDOM,"Enter",13)||i.from==n.from-1&&i.to==n.to&&i.insert.length==0&&H(t.contentDOM,"Backspace",8)||i.from==n.from&&i.to==n.to+1&&i.insert.length==0&&H(t.contentDOM,"Delete",46)))return true;let r=i.insert.toString();if(t.state.facet(It).some((e=>e(t,i.from,i.to,r))))return true;if(t.inputState.composing>=0)t.inputState.composing++;let l;if(i.from>=n.from&&i.to<=n.to&&i.to-i.from>=(n.to-n.from)/3&&(!o||o.main.empty&&o.main.from==i.from+i.insert.length)&&t.inputState.composing<0){let s=n.from<i.from?e.sliceDoc(n.from,i.from):"";let o=n.to>i.to?e.sliceDoc(i.to,n.to):"";l=e.replaceSelection(t.state.toText(s+i.insert.sliceString(0,undefined,t.state.lineBreak)+o))}else{let r=e.changes(i);let h=o&&o.main.to<=r.newLength?o.main:undefined;if(e.selection.ranges.length>1&&t.inputState.composing>=0&&i.to<=n.to&&i.to>=n.to-10){let o=t.state.sliceDoc(i.from,i.to);let a=Ee(t)||t.state.doc.lineAt(n.head);let c=n.to-i.to,f=n.to-n.from;l=e.changeByRange((l=>{if(l.from==n.from&&l.to==n.to)return{changes:r,range:h||l.map(r)};let d=l.to-c,u=d-o.length;if(l.to-l.from!=f||t.state.sliceDoc(u,d)!=o||a&&l.to>=a.from&&l.from<=a.to)return{range:l};let p=e.changes({from:u,to:d,insert:i.insert}),g=l.to-n.to;return{changes:p,range:!h?l.map(p):s.EditorSelection.range(Math.max(0,h.anchor+g),Math.max(0,h.head+g))}}))}else{l={changes:r,selection:h&&e.selection.replaceRange(h)}}}let h="input.type";if(t.composing||t.inputState.compositionPendingChange&&t.inputState.compositionEndedAt>Date.now()-50){t.inputState.compositionPendingChange=false;h+=".compose";if(t.inputState.compositionFirstChange){h+=".start";t.inputState.compositionFirstChange=false}}t.dispatch(l,{scrollIntoView:true,userEvent:h});return true}else if(o&&!o.main.eq(n)){let e=false,i="select";if(t.inputState.lastSelectionTime>Date.now()-50){if(t.inputState.lastSelectionOrigin=="select")e=true;i=t.inputState.lastSelectionOrigin}t.dispatch({selection:o,scrollIntoView:e,userEvent:i});return true}else{return false}}function Ss(t,e,i,s){let o=Math.min(t.length,e.length);let n=0;while(n<o&&t.charCodeAt(n)==e.charCodeAt(n))n++;if(n==o&&t.length==e.length)return null;let r=t.length,l=e.length;while(r>0&&l>0&&t.charCodeAt(r-1)==e.charCodeAt(l-1)){r--;l--}if(s=="end"){let t=Math.max(0,n-Math.min(r,l));i-=r+t-n}if(r<n&&t.length<e.length){let t=i<=n&&i>=r?n-i:0;n-=t;l=n+(l-r);r=n}else if(l<n){let t=i<=n&&i>=l?n-i:0;n-=t;r=n+(r-l);l=n}return{from:n,toA:r,toB:l}}function xs(t){let e=[];if(t.root.activeElement!=t.contentDOM)return e;let{anchorNode:i,anchorOffset:s,focusNode:o,focusOffset:n}=t.observer.selectionRange;if(i){e.push(new Ae(i,s));if(o!=i||n!=s)e.push(new Ae(o,n))}return e}function Ms(t,e){if(t.length==0)return null;let i=t[0].pos,o=t.length==2?t[1].pos:i;return i>-1&&o>-1?s.EditorSelection.single(i+e,o+e):null}const ks={childList:true,characterData:true,subtree:true,attributes:true,characterDataOldValue:true};const Cs=it.ie&&it.ie_version<=11;class As{constructor(t){this.view=t;this.active=false;this.selectionRange=new O;this.selectionChanged=false;this.delayedFlush=-1;this.resizeTimeout=-1;this.queue=[];this.delayedAndroidKey=null;this.flushingAndroidKey=-1;this.lastChange=0;this.scrollTargets=[];this.intersection=null;this.resizeScroll=null;this.resizeContent=null;this.intersecting=false;this.gapIntersection=null;this.gaps=[];this.parentCheck=-1;this.dom=t.contentDOM;this.observer=new MutationObserver((e=>{for(let t of e)this.queue.push(t);if((it.ie&&it.ie_version<=11||it.ios&&t.composing)&&e.some((t=>t.type=="childList"&&t.removedNodes.length||t.type=="characterData"&&t.oldValue.length>t.target.nodeValue.length)))this.flushSoon();else this.flush()}));if(Cs)this.onCharData=t=>{this.queue.push({target:t.target,type:"characterData",oldValue:t.prevValue});this.flushSoon()};this.onSelectionChange=this.onSelectionChange.bind(this);this.onResize=this.onResize.bind(this);this.onPrint=this.onPrint.bind(this);this.onScroll=this.onScroll.bind(this);if(typeof ResizeObserver=="function"){this.resizeScroll=new ResizeObserver((()=>{var t;if(((t=this.view.docView)===null||t===void 0?void 0:t.lastUpdate)<Date.now()-75)this.onResize()}));this.resizeScroll.observe(t.scrollDOM);this.resizeContent=new ResizeObserver((()=>this.view.requestMeasure()));this.resizeContent.observe(t.contentDOM)}this.addWindowListeners(this.win=t.win);this.start();if(typeof IntersectionObserver=="function"){this.intersection=new IntersectionObserver((t=>{if(this.parentCheck<0)this.parentCheck=setTimeout(this.listenForScroll.bind(this),1e3);if(t.length>0&&t[t.length-1].intersectionRatio>0!=this.intersecting){this.intersecting=!this.intersecting;if(this.intersecting!=this.view.inView)this.onScrollChanged(document.createEvent("Event"))}}),{});this.intersection.observe(this.dom);this.gapIntersection=new IntersectionObserver((t=>{if(t.length>0&&t[t.length-1].intersectionRatio>0)this.onScrollChanged(document.createEvent("Event"))}),{})}this.listenForScroll();this.readSelectionRange()}onScrollChanged(t){this.view.inputState.runScrollHandlers(this.view,t);if(this.intersecting)this.view.measure()}onScroll(t){if(this.intersecting)this.flush(false);this.onScrollChanged(t)}onResize(){if(this.resizeTimeout<0)this.resizeTimeout=setTimeout((()=>{this.resizeTimeout=-1;this.view.requestMeasure()}),50)}onPrint(){this.view.viewState.printing=true;this.view.measure();setTimeout((()=>{this.view.viewState.printing=false;this.view.requestMeasure()}),500)}updateGaps(t){if(this.gapIntersection&&(t.length!=this.gaps.length||this.gaps.some(((e,i)=>e!=t[i])))){this.gapIntersection.disconnect();for(let e of t)this.gapIntersection.observe(e);this.gaps=t}}onSelectionChange(t){let e=this.selectionChanged;if(!this.readSelectionRange()||this.delayedAndroidKey)return;let{view:i}=this,s=this.selectionRange;if(i.state.facet(Ut)?i.root.activeElement!=this.dom:!v(i.dom,s))return;let o=s.anchorNode&&i.docView.nearest(s.anchorNode);if(o&&o.ignoreEvent(t)){if(!e)this.selectionChanged=false;return}if((it.ie&&it.ie_version<=11||it.android&&it.chrome)&&!i.state.selection.main.empty&&s.focusNode&&y(s.focusNode,s.focusOffset,s.anchorNode,s.anchorOffset))this.flushSoon();else this.flush(false)}readSelectionRange(){let{view:t}=this;let e=it.safari&&t.root.nodeType==11&&w(this.dom.ownerDocument)==this.dom&&Ts(this.view)||g(t.root);if(!e||this.selectionRange.eq(e))return false;let i=v(this.dom,e);if(i&&!this.selectionChanged&&t.inputState.lastFocusTime>Date.now()-200&&t.inputState.lastTouchTime<Date.now()-300&&N(this.dom,e)){this.view.inputState.lastFocusTime=0;t.docView.updateSelection();return false}this.selectionRange.setRange(e);if(i)this.selectionChanged=true;return true}setSelectionRange(t,e){this.selectionRange.set(t.node,t.offset,e.node,e.offset);this.selectionChanged=false}clearSelectionRange(){this.selectionRange.set(null,0,null,0)}listenForScroll(){this.parentCheck=-1;let t=0,e=null;for(let i=this.dom;i;){if(i.nodeType==1){if(!e&&t<this.scrollTargets.length&&this.scrollTargets[t]==i)t++;else if(!e)e=this.scrollTargets.slice(0,t);if(e)e.push(i);i=i.assignedSlot||i.parentNode}else if(i.nodeType==11){i=i.host}else{break}}if(t<this.scrollTargets.length&&!e)e=this.scrollTargets.slice(0,t);if(e){for(let t of this.scrollTargets)t.removeEventListener("scroll",this.onScroll);for(let t of this.scrollTargets=e)t.addEventListener("scroll",this.onScroll)}}ignore(t){if(!this.active)return t();try{this.stop();return t()}finally{this.start();this.clear()}}start(){if(this.active)return;this.observer.observe(this.dom,ks);if(Cs)this.dom.addEventListener("DOMCharacterDataModified",this.onCharData);this.active=true}stop(){if(!this.active)return;this.active=false;this.observer.disconnect();if(Cs)this.dom.removeEventListener("DOMCharacterDataModified",this.onCharData)}clear(){this.processRecords();this.queue.length=0;this.selectionChanged=false}delayAndroidKey(t,e){var i;if(!this.delayedAndroidKey){let t=()=>{let t=this.delayedAndroidKey;if(t){this.clearDelayedAndroidKey();if(!this.flush()&&t.force)H(this.dom,t.key,t.keyCode)}};this.flushingAndroidKey=this.view.win.requestAnimationFrame(t)}if(!this.delayedAndroidKey||t=="Enter")this.delayedAndroidKey={key:t,keyCode:e,force:this.lastChange<Date.now()-50||!!((i=this.delayedAndroidKey)===null||i===void 0?void 0:i.force)}}clearDelayedAndroidKey(){this.win.cancelAnimationFrame(this.flushingAndroidKey);this.delayedAndroidKey=null;this.flushingAndroidKey=-1}flushSoon(){if(this.delayedFlush<0)this.delayedFlush=this.view.win.requestAnimationFrame((()=>{this.delayedFlush=-1;this.flush()}))}forceFlush(){if(this.delayedFlush>=0){this.view.win.cancelAnimationFrame(this.delayedFlush);this.delayedFlush=-1}this.flush()}pendingRecords(){for(let t of this.observer.takeRecords())this.queue.push(t);return this.queue}processRecords(){let t=this.pendingRecords();if(t.length)this.queue=[];let e=-1,i=-1,s=false;for(let o of t){let t=this.readMutation(o);if(!t)continue;if(t.typeOver)s=true;if(e==-1){({from:e,to:i}=t)}else{e=Math.min(t.from,e);i=Math.max(t.to,i)}}return{from:e,to:i,typeOver:s}}readChange(){let{from:t,to:e,typeOver:i}=this.processRecords();let s=this.selectionChanged&&v(this.dom,this.selectionRange);if(t<0&&!s)return null;if(t>-1)this.lastChange=Date.now();this.view.inputState.lastFocusTime=0;this.selectionChanged=false;return new bs(this.view,t,e,i)}flush(t=true){if(this.delayedFlush>=0||this.delayedAndroidKey)return false;if(t)this.readSelectionRange();let e=this.readChange();if(!e)return false;let i=this.view.state;let s=ys(this.view,e);if(this.view.state==i)this.view.update([]);return s}readMutation(t){let e=this.view.docView.nearest(t.target);if(!e||e.ignoreMutation(t))return null;e.markDirty(t.type=="attributes");if(t.type=="attributes")e.dirty|=4;if(t.type=="childList"){let i=Ds(e,t.previousSibling||t.target.previousSibling,-1);let s=Ds(e,t.nextSibling||t.target.nextSibling,1);return{from:i?e.posAfter(i):e.posAtStart,to:s?e.posBefore(s):e.posAtEnd,typeOver:false}}else if(t.type=="characterData"){return{from:e.posAtStart,to:e.posAtEnd,typeOver:t.target.nodeValue==t.oldValue}}else{return null}}setWindow(t){if(t!=this.win){this.removeWindowListeners(this.win);this.win=t;this.addWindowListeners(this.win)}}addWindowListeners(t){t.addEventListener("resize",this.onResize);t.addEventListener("beforeprint",this.onPrint);t.addEventListener("scroll",this.onScroll);t.document.addEventListener("selectionchange",this.onSelectionChange)}removeWindowListeners(t){t.removeEventListener("scroll",this.onScroll);t.removeEventListener("resize",this.onResize);t.removeEventListener("beforeprint",this.onPrint);t.document.removeEventListener("selectionchange",this.onSelectionChange)}destroy(){var t,e,i,s;this.stop();(t=this.intersection)===null||t===void 0?void 0:t.disconnect();(e=this.gapIntersection)===null||e===void 0?void 0:e.disconnect();(i=this.resizeScroll)===null||i===void 0?void 0:i.disconnect();(s=this.resizeContent)===null||s===void 0?void 0:s.disconnect();for(let o of this.scrollTargets)o.removeEventListener("scroll",this.onScroll);this.removeWindowListeners(this.win);clearTimeout(this.parentCheck);clearTimeout(this.resizeTimeout);this.win.cancelAnimationFrame(this.delayedFlush);this.win.cancelAnimationFrame(this.flushingAndroidKey)}}function Ds(t,e,i){while(e){let s=z.get(e);if(s&&s.parent==t)return s;let o=e.parentNode;e=o!=t.dom?o:i>0?e.nextSibling:e.previousSibling}return null}function Ts(t){let e=null;function i(t){t.preventDefault();t.stopImmediatePropagation();e=t.getTargetRanges()[0]}t.contentDOM.addEventListener("beforeinput",i,true);t.dom.ownerDocument.execCommand("indent");t.contentDOM.removeEventListener("beforeinput",i,true);if(!e)return null;let s=e.startContainer,o=e.startOffset;let n=e.endContainer,r=e.endOffset;let l=t.docView.domAtPos(t.state.selection.main.anchor);if(y(l.node,l.offset,n,r))[s,o,n,r]=[n,r,s,o];return{anchorNode:s,anchorOffset:o,focusNode:n,focusOffset:r}}class Os{constructor(t={}){this.plugins=[];this.pluginMap=new Map;this.editorAttrs={};this.contentAttrs={};this.bidiCache=[];this.destroyed=false;this.updateState=2;this.measureScheduled=-1;this.measureRequests=[];this.contentDOM=document.createElement("div");this.scrollDOM=document.createElement("div");this.scrollDOM.tabIndex=-1;this.scrollDOM.className="cm-scroller";this.scrollDOM.appendChild(this.contentDOM);this.announceDOM=document.createElement("div");this.announceDOM.style.cssText="position: fixed; top: -10000px";this.announceDOM.setAttribute("aria-live","polite");this.dom=document.createElement("div");this.dom.appendChild(this.announceDOM);this.dom.appendChild(this.scrollDOM);this._dispatch=t.dispatch||(t=>this.update([t]));this.dispatch=this.dispatch.bind(this);this._root=t.root||P(t.parent)||document;this.viewState=new is(t.state||s.EditorState.create(t));this.plugins=this.state.facet(Yt).map((t=>new Jt(t)));for(let e of this.plugins)e.update(this);this.observer=new As(this);this.inputState=new ei(this);this.inputState.ensureHandlers(this,this.plugins);this.docView=new De(this);this.mountStyles();this.updateAttrs();this.updateState=0;this.requestMeasure();if(t.parent)t.parent.appendChild(this.dom)}get state(){return this.viewState.state}get viewport(){return this.viewState.viewport}get visibleRanges(){return this.viewState.visibleRanges}get inView(){return this.viewState.inView}get composing(){return this.inputState.composing>0}get compositionStarted(){return this.inputState.composing>=0}get root(){return this._root}get win(){return this.dom.ownerDocument.defaultView||window}dispatch(...t){this._dispatch(t.length==1&&t[0]instanceof s.Transaction?t[0]:this.state.update(...t))}update(t){if(this.updateState!=0)throw new Error("Calls to EditorView.update are not allowed while an update is in progress");let e=false,i=false,o;let n=this.state;for(let s of t){if(s.startState!=n)throw new RangeError("Trying to update state with a transaction that doesn't start from the previous state.");n=s.state}if(this.destroyed){this.viewState.state=n;return}let r=this.hasFocus,l=0,h=null;if(t.some((t=>t.annotation(Li)))){this.inputState.notifiedFocused=r;l=1}else if(r!=this.inputState.notifiedFocused){this.inputState.notifiedFocused=r;h=Hi(n,r);if(!h)l=1}let a=this.observer.delayedAndroidKey,c=null;if(a){this.observer.clearDelayedAndroidKey();c=this.observer.readChange();if(c&&!this.state.doc.eq(n.doc)||!this.state.selection.eq(n.selection))c=null}else{this.observer.clear()}if(n.facet(s.EditorState.phrases)!=this.state.facet(s.EditorState.phrases))return this.setState(n);o=re.create(this,n,t);o.flags|=l;let f=this.viewState.scrollTarget;try{this.updateState=2;for(let e of t){if(f)f=f.map(e.changes);if(e.scrollIntoView){let{main:t}=e.state.selection;f=new jt(t.empty?t:s.EditorSelection.cursor(t.head,t.head>t.anchor?-1:1))}for(let t of e.effects)if(t.is($t))f=t.value}this.viewState.update(o,f);this.bidiCache=Bs.update(this.bidiCache,o.changes);if(!o.empty){this.updatePlugins(o);this.inputState.update(o)}e=this.docView.update(o);if(this.state.facet(oe)!=this.styleModules)this.mountStyles();i=this.updateAttrs();this.showAnnouncements(t);this.docView.updateSelection(e,t.some((t=>t.isUserEvent("select.pointer"))))}finally{this.updateState=0}if(o.startState.facet(fs)!=o.state.facet(fs))this.viewState.mustMeasureContent=true;if(e||i||f||this.viewState.mustEnforceCursorAssoc||this.viewState.mustMeasureContent)this.requestMeasure();if(!o.empty)for(let s of this.state.facet(zt))s(o);if(h||c)Promise.resolve().then((()=>{if(h&&this.state==h.startState)this.dispatch(h);if(c){if(!ys(this,c)&&a.force)H(this.contentDOM,a.key,a.keyCode)}}))}setState(t){if(this.updateState!=0)throw new Error("Calls to EditorView.setState are not allowed while an update is in progress");if(this.destroyed){this.viewState.state=t;return}this.updateState=2;let e=this.hasFocus;try{for(let t of this.plugins)t.destroy(this);this.viewState=new is(t);this.plugins=t.facet(Yt).map((t=>new Jt(t)));this.pluginMap.clear();for(let t of this.plugins)t.update(this);this.docView=new De(this);this.inputState.ensureHandlers(this,this.plugins);this.mountStyles();this.updateAttrs();this.bidiCache=[]}finally{this.updateState=0}if(e)this.focus();this.requestMeasure()}updatePlugins(t){let e=t.startState.facet(Yt),i=t.state.facet(Yt);if(e!=i){let s=[];for(let o of i){let i=e.indexOf(o);if(i<0){s.push(new Jt(o))}else{let e=this.plugins[i];e.mustUpdate=t;s.push(e)}}for(let e of this.plugins)if(e.mustUpdate!=t)e.destroy(this);this.plugins=s;this.pluginMap.clear();this.inputState.ensureHandlers(this,this.plugins)}else{for(let e of this.plugins)e.mustUpdate=t}for(let s=0;s<this.plugins.length;s++)this.plugins[s].update(this)}measure(t=true){if(this.destroyed)return;if(this.measureScheduled>-1)this.win.cancelAnimationFrame(this.measureScheduled);this.measureScheduled=0;if(t)this.observer.forceFlush();let e=null;let{scrollHeight:i,scrollTop:s,clientHeight:o}=this.scrollDOM;let n=s>i-o-4?i:s;try{for(let t=0;;t++){this.updateState=1;let i=this.viewport;let s=this.viewState.lineBlockAtHeight(n);let o=this.viewState.measure(this);if(!o&&!this.measureRequests.length&&this.viewState.scrollTarget==null)break;if(t>5){console.warn(this.measureRequests.length?"Measure loop restarted more than 5 times":"Viewport failed to stabilize");break}let l=[];if(!(o&4))[this.measureRequests,l]=[l,this.measureRequests];let h=l.map((t=>{try{return t.read(this)}catch(e){_t(this.state,e);return Rs}}));let a=re.create(this,this.state,[]),c=false,f=false;a.flags|=o;if(!e)e=a;else e.flags|=o;this.updateState=2;if(!a.empty){this.updatePlugins(a);this.inputState.update(a);this.updateAttrs();c=this.docView.update(a)}for(let t=0;t<l.length;t++)if(h[t]!=Rs){try{let e=l[t];if(e.write)e.write(h[t],this)}catch(r){_t(this.state,r)}}if(this.viewState.editorHeight){if(this.viewState.scrollTarget){this.docView.scrollIntoView(this.viewState.scrollTarget);this.viewState.scrollTarget=null;f=true}else{let t=this.viewState.lineBlockAt(s.from).top-s.top;if(t>1||t<-1){this.scrollDOM.scrollTop+=t;f=true}}}if(c)this.docView.updateSelection(true);if(this.viewport.from==i.from&&this.viewport.to==i.to&&!f&&this.measureRequests.length==0)break}}finally{this.updateState=0;this.measureScheduled=-1}if(e&&!e.empty)for(let l of this.state.facet(zt))l(e)}get themeClasses(){return us+" "+(this.state.facet(ds)?gs:ps)+" "+this.state.facet(fs)}updateAttrs(){let t=Ls(this,Zt,{class:"cm-editor"+(this.hasFocus?" cm-focused ":" ")+this.themeClasses});let e={spellcheck:"false",autocorrect:"off",autocapitalize:"off",translate:"no",contenteditable:!this.state.facet(Ut)?"false":"true",class:"cm-content",style:`${it.tabSize}: ${this.state.tabSize}`,role:"textbox","aria-multiline":"true"};if(this.state.readOnly)e["aria-readonly"]="true";Ls(this,te,e);let i=this.observer.ignore((()=>{let i=St(this.contentDOM,this.contentAttrs,e);let s=St(this.dom,this.editorAttrs,t);return i||s}));this.editorAttrs=t;this.contentAttrs=e;return i}showAnnouncements(t){let e=true;for(let i of t)for(let t of i.effects)if(t.is(Os.announce)){if(e)this.announceDOM.textContent="";e=false;let i=this.announceDOM.appendChild(document.createElement("div"));i.textContent=t.value}}mountStyles(){this.styleModules=this.state.facet(oe);o.StyleModule.mount(this.root,this.styleModules.concat(vs).reverse())}readMeasured(){if(this.updateState==2)throw new Error("Reading the editor layout isn't allowed during an update");if(this.updateState==0&&this.measureScheduled>-1)this.measure(false)}requestMeasure(t){if(this.measureScheduled<0)this.measureScheduled=this.win.requestAnimationFrame((()=>this.measure()));if(t){if(this.measureRequests.indexOf(t)>-1)return;if(t.key!=null)for(let e=0;e<this.measureRequests.length;e++){if(this.measureRequests[e].key===t.key){this.measureRequests[e]=t;return}}this.measureRequests.push(t)}}plugin(t){let e=this.pluginMap.get(t);if(e===undefined||e&&e.spec!=t)this.pluginMap.set(t,e=this.plugins.find((e=>e.spec==t))||null);return e&&e.update(this).value}get documentTop(){return this.contentDOM.getBoundingClientRect().top+this.viewState.paddingTop}get documentPadding(){return{top:this.viewState.paddingTop,bottom:this.viewState.paddingBottom}}elementAtHeight(t){this.readMeasured();return this.viewState.elementAtHeight(t)}lineBlockAtHeight(t){this.readMeasured();return this.viewState.lineBlockAtHeight(t)}get viewportLineBlocks(){return this.viewState.viewportLines}lineBlockAt(t){return this.viewState.lineBlockAt(t)}get contentHeight(){return this.viewState.contentHeight}moveByChar(t,e,i){return ti(this,t,Qe(this,t,e,i))}moveByGroup(t,e){return ti(this,t,Qe(this,t,e,(e=>Je(this,t.head,e))))}moveToLineBoundary(t,e,i=true){return Ye(this,t,e,i)}moveVertically(t,e,i){return ti(this,t,Ze(this,t,e,i))}domAtPos(t){return this.docView.domAtPos(t)}posAtDOM(t,e=0){return this.docView.posFromDOM(t,e)}posAtCoords(t,e=true){this.readMeasured();return $e(this,t,e)}coordsAtPos(t,e=1){this.readMeasured();let i=this.docView.coordsAt(t,e);if(!i||i.left==i.right)return i;let s=this.state.doc.lineAt(t),o=this.bidiSpans(s);let n=o[we.find(o,t-s.from,-1,e)];return C(i,n.dir==le.LTR==e>0)}get defaultCharacterWidth(){return this.viewState.heightOracle.charWidth}get defaultLineHeight(){return this.viewState.heightOracle.lineHeight}get textDirection(){return this.viewState.defaultTextDirection}textDirectionAt(t){let e=this.state.facet(qt);if(!e||t<this.viewport.from||t>this.viewport.to)return this.textDirection;this.readMeasured();return this.docView.textDirectionAt(t)}get lineWrapping(){return this.viewState.heightOracle.lineWrapping}bidiSpans(t){if(t.length>Es)return ye(t.length);let e=this.textDirectionAt(t.from);for(let s of this.bidiCache)if(s.from==t.from&&s.dir==e)return s.order;let i=be(t.text,e);this.bidiCache.push(new Bs(t.from,t.to,e,i));return i}get hasFocus(){var t;return(this.dom.ownerDocument.hasFocus()||it.safari&&((t=this.inputState)===null||t===void 0?void 0:t.lastContextMenu)>Date.now()-3e4)&&this.root.activeElement==this.contentDOM}focus(){this.observer.ignore((()=>{R(this.contentDOM);this.docView.updateSelection()}))}setRoot(t){if(this._root!=t){this._root=t;this.observer.setWindow((t.nodeType==9?t:t.ownerDocument).defaultView||window);this.mountStyles()}}destroy(){for(let t of this.plugins)t.destroy(this);this.plugins=[];this.inputState.destroy();this.dom.remove();this.observer.destroy();if(this.measureScheduled>-1)this.win.cancelAnimationFrame(this.measureScheduled);this.destroyed=true}static scrollIntoView(t,e={}){return $t.of(new jt(typeof t=="number"?s.EditorSelection.cursor(t):t,e.y,e.x,e.yMargin,e.xMargin))}static domEventHandlers(t){return Qt.define((()=>({})),{eventHandlers:t})}static theme(t,e){let i=o.StyleModule.newName();let s=[fs.of(i),oe.of(ws(`.${i}`,t))];if(e&&e.dark)s.push(ds.of(true));return s}static baseTheme(t){return s.Prec.lowest(oe.of(ws("."+us,t,ms)))}static findFromDOM(t){var e;let i=t.querySelector(".cm-content");let s=i&&z.get(i)||z.get(t);return((e=s===null||s===void 0?void 0:s.rootView)===null||e===void 0?void 0:e.view)||null}}Os.styleModule=oe;Os.inputHandler=It;Os.focusChangeEffect=Kt;Os.perLineTextDirection=qt;Os.exceptionSink=Ft;Os.updateListener=zt;Os.editable=Ut;Os.mouseSelectionStyle=Wt;Os.dragMovesSelection=Nt;Os.clickAddsSelectionRange=Vt;Os.decorations=ee;Os.atomicRanges=ie;Os.scrollMargins=se;Os.darkTheme=ds;Os.contentAttributes=te;Os.editorAttributes=Zt;Os.lineWrapping=Os.contentAttributes.of({class:"cm-lineWrapping"});Os.announce=s.StateEffect.define();const Es=4096;const Rs={};class Bs{constructor(t,e,i,s){this.from=t;this.to=e;this.dir=i;this.order=s}static update(t,e){if(e.empty)return t;let i=[],s=t.length?t[t.length-1].dir:le.LTR;for(let o=Math.max(0,t.length-10);o<t.length;o++){let n=t[o];if(n.dir==s&&!e.touchesRange(n.from,n.to))i.push(new Bs(e.mapPos(n.from,1),e.mapPos(n.to,-1),n.dir,n.order))}return i}}function Ls(t,e,i){for(let s=t.state.facet(e),o=s.length-1;o>=0;o--){let e=s[o],n=typeof e=="function"?e(t):e;if(n)bt(n,i)}return i}const Hs=it.mac?"mac":it.windows?"win":it.linux?"linux":"key";function Ps(t,e){const i=t.split(/-(?!$)/);let s=i[i.length-1];if(s=="Space")s=" ";let o,n,r,l;for(let h=0;h<i.length-1;++h){const t=i[h];if(/^(cmd|meta|m)$/i.test(t))l=true;else if(/^a(lt)?$/i.test(t))o=true;else if(/^(c|ctrl|control)$/i.test(t))n=true;else if(/^s(hift)?$/i.test(t))r=true;else if(/^mod$/i.test(t)){if(e=="mac")l=true;else n=true}else throw new Error("Unrecognized modifier name: "+t)}if(o)s="Alt-"+s;if(n)s="Ctrl-"+s;if(l)s="Meta-"+s;if(r)s="Shift-"+s;return s}function Vs(t,e,i){if(e.altKey)t="Alt-"+t;if(e.ctrlKey)t="Ctrl-"+t;if(e.metaKey)t="Meta-"+t;if(i!==false&&e.shiftKey)t="Shift-"+t;return t}const Ns=s.Prec["default"](Os.domEventHandlers({keydown(t,e){return js(zs(e.state),t,e,"editor")}}));const Ws=s.Facet.define({enables:Ns});const Fs=new WeakMap;function zs(t){let e=t.facet(Ws);let i=Fs.get(e);if(!i)Fs.set(e,i=Gs(e.reduce(((t,e)=>t.concat(e)),[])));return i}function Is(t,e,i){return js(zs(t.state),e,t,i)}let Ks=null;const qs=4e3;function Gs(t,e=Hs){let i=Object.create(null);let s=Object.create(null);let o=(t,e)=>{let i=s[t];if(i==null)s[t]=e;else if(i!=e)throw new Error("Key binding "+t+" is used both as a regular binding and as a multi-stroke prefix")};let n=(t,s,n,r)=>{var l,h;let a=i[t]||(i[t]=Object.create(null));let c=s.split(/ (?!$)/).map((t=>Ps(t,e)));for(let e=1;e<c.length;e++){let i=c.slice(0,e).join(" ");o(i,true);if(!a[i])a[i]={preventDefault:true,run:[e=>{let s=Ks={view:e,prefix:i,scope:t};setTimeout((()=>{if(Ks==s)Ks=null}),qs);return true}]}}let f=c.join(" ");o(f,false);let d=a[f]||(a[f]={preventDefault:false,run:((h=(l=a._any)===null||l===void 0?void 0:l.run)===null||h===void 0?void 0:h.slice())||[]});if(n)d.run.push(n);if(r)d.preventDefault=true};for(let r of t){let t=r.scope?r.scope.split(" "):["editor"];if(r.any)for(let e of t){let t=i[e]||(i[e]=Object.create(null));if(!t._any)t._any={preventDefault:false,run:[]};for(let e in t)t[e].run.push(r.any)}let s=r[e]||r.key;if(!s)continue;for(let e of t){n(e,s,r.run,r.preventDefault);if(r.shift)n(e,"Shift-"+s,r.shift,r.preventDefault)}}return i}function js(t,e,i,o){let l=p(e);let h=(0,s.codePointAt)(l,0),a=(0,s.codePointSize)(h)==l.length&&l!=" ";let c="",f=false;if(Ks&&Ks.view==i&&Ks.scope==o){c=Ks.prefix+" ";if(f=oi.indexOf(e.keyCode)<0)Ks=null}let d=new Set;let u=t=>{if(t){for(let s of t.run)if(!d.has(s)){d.add(s);if(s(i,e))return true}if(t.preventDefault)f=true}return false};let g=t[o],m,w;if(g){if(u(g[c+Vs(l,e,!a)]))return true;if(a&&(e.altKey||e.metaKey||e.ctrlKey)&&!(it.windows&&e.ctrlKey&&e.altKey)&&(m=n[e.keyCode])&&m!=l){if(u(g[c+Vs(m,e,true)]))return true;else if(e.shiftKey&&(w=r[e.keyCode])!=l&&w!=m&&u(g[c+Vs(w,e,false)]))return true}else if(a&&e.shiftKey){if(u(g[c+Vs(l,e,true)]))return true}if(u(g._any))return true}return f}class $s{constructor(t,e,i,s,o){this.className=t;this.left=e;this.top=i;this.width=s;this.height=o}draw(){let t=document.createElement("div");t.className=this.className;this.adjust(t);return t}update(t,e){if(e.className!=this.className)return false;this.adjust(t);return true}adjust(t){t.style.left=this.left+"px";t.style.top=this.top+"px";if(this.width!=null)t.style.width=this.width+"px";t.style.height=this.height+"px"}eq(t){return this.left==t.left&&this.top==t.top&&this.width==t.width&&this.height==t.height&&this.className==t.className}static forRange(t,e,i){if(i.empty){let s=t.coordsAtPos(i.head,i.assoc||1);if(!s)return[];let o=_s(t);return[new $s(e,s.left-o.left,s.top-o.top,null,s.bottom-s.top)]}else{return Ys(t,e,i)}}}function _s(t){let e=t.scrollDOM.getBoundingClientRect();let i=t.textDirection==le.LTR?e.left:e.right-t.scrollDOM.clientWidth;return{left:i-t.scrollDOM.scrollLeft,top:e.top-t.scrollDOM.scrollTop}}function Us(t,e,i){let o=s.EditorSelection.cursor(e);return{from:Math.max(i.from,t.moveToLineBoundary(o,false,true).from),to:Math.min(i.to,t.moveToLineBoundary(o,true,true).from),type:Mt.Text}}function Xs(t,e){let i=t.lineBlockAt(e);if(Array.isArray(i.type))for(let s of i.type){if(s.to>e||s.to==e&&(s.to==i.to||s.type==Mt.Text))return s}return i}function Ys(t,e,i){if(i.to<=t.viewport.from||i.from>=t.viewport.to)return[];let s=Math.max(i.from,t.viewport.from),o=Math.min(i.to,t.viewport.to);let n=t.textDirection==le.LTR;let r=t.contentDOM,l=r.getBoundingClientRect(),h=_s(t);let a=r.querySelector(".cm-line"),c=a&&window.getComputedStyle(a);let f=l.left+(c?parseInt(c.paddingLeft)+Math.min(0,parseInt(c.textIndent)):0);let d=l.right-(c?parseInt(c.paddingRight):0);let u=Xs(t,s),p=Xs(t,o);let g=u.type==Mt.Text?u:null;let m=p.type==Mt.Text?p:null;if(t.lineWrapping){if(g)g=Us(t,s,g);if(m)m=Us(t,o,m)}if(g&&m&&g.from==m.from){return v(b(i.from,i.to,g))}else{let e=g?b(i.from,null,g):y(u,false);let s=m?b(null,i.to,m):y(p,true);let o=[];if((g||u).to<(m||p).from-1)o.push(w(f,e.bottom,d,s.top));else if(e.bottom<s.top&&t.elementAtHeight((e.bottom+s.top)/2).type==Mt.Text)e.bottom=s.top=(e.bottom+s.top)/2;return v(e).concat(o).concat(v(s))}function w(t,i,s,o){return new $s(e,t-h.left,i-h.top-.01,s-t,o-i+.01)}function v({top:t,bottom:e,horizontal:i}){let s=[];for(let o=0;o<i.length;o+=2)s.push(w(i[o],t,i[o+1],e));return s}function b(e,i,s){let o=1e9,r=-1e9,l=[];function h(e,i,h,a,c){let u=t.coordsAtPos(e,e==s.to?-2:2);let p=t.coordsAtPos(h,h==s.from?2:-2);o=Math.min(u.top,p.top,o);r=Math.max(u.bottom,p.bottom,r);if(c==le.LTR)l.push(n&&i?f:u.left,n&&a?d:p.right);else l.push(!n&&a?f:p.left,!n&&i?d:u.right)}let a=e!==null&&e!==void 0?e:s.from,c=i!==null&&i!==void 0?i:s.to;for(let n of t.visibleRanges)if(n.to>a&&n.from<c){for(let s=Math.max(n.from,a),o=Math.min(n.to,c);;){let n=t.state.doc.lineAt(s);for(let r of t.bidiSpans(n)){let t=r.from+n.from,l=r.to+n.from;if(t>=o)break;if(l>s)h(Math.max(t,s),e==null&&t<=a,Math.min(l,o),i==null&&l>=c,r.dir)}s=n.to+1;if(s>=o)break}}if(l.length==0)h(a,e==null,c,i==null,t.textDirection);return{top:o,bottom:r,horizontal:l}}function y(t,e){let i=l.top+(e?t.top:t.bottom);return{top:i,bottom:i,horizontal:[]}}}function Qs(t,e){return t.constructor==e.constructor&&t.eq(e)}class Js{constructor(t,e){this.view=t;this.layer=e;this.drawn=[];this.measureReq={read:this.measure.bind(this),write:this.draw.bind(this)};this.dom=t.scrollDOM.appendChild(document.createElement("div"));this.dom.classList.add("cm-layer");if(e.above)this.dom.classList.add("cm-layer-above");if(e.class)this.dom.classList.add(e.class);this.dom.setAttribute("aria-hidden","true");this.setOrder(t.state);t.requestMeasure(this.measureReq);if(e.mount)e.mount(this.dom,t)}update(t){if(t.startState.facet(Zs)!=t.state.facet(Zs))this.setOrder(t.state);if(this.layer.update(t,this.dom)||t.geometryChanged)t.view.requestMeasure(this.measureReq)}setOrder(t){let e=0,i=t.facet(Zs);while(e<i.length&&i[e]!=this.layer)e++;this.dom.style.zIndex=String((this.layer.above?150:-1)-e)}measure(){return this.layer.markers(this.view)}draw(t){if(t.length!=this.drawn.length||t.some(((t,e)=>!Qs(t,this.drawn[e])))){let e=this.dom.firstChild,i=0;for(let s of t){if(s.update&&e&&s.constructor&&this.drawn[i].constructor&&s.update(e,this.drawn[i])){e=e.nextSibling;i++}else{this.dom.insertBefore(s.draw(),e)}}while(e){let t=e.nextSibling;e.remove();e=t}this.drawn=t}}destroy(){if(this.layer.destroy)this.layer.destroy(this.dom,this.view);this.dom.remove()}}const Zs=s.Facet.define();function to(t){return[Qt.define((e=>new Js(e,t))),Zs.of(t)]}const eo=!it.ios;const io=s.Facet.define({combine(t){return(0,s.combineConfig)(t,{cursorBlinkRate:1200,drawRangeCursor:true},{cursorBlinkRate:(t,e)=>Math.min(t,e),drawRangeCursor:(t,e)=>t||e})}});function so(t={}){return[io.of(t),no,lo,ao,Gt.of(true)]}function oo(t){return t.startState.facet(io)!=t.state.facet(io)}const no=to({above:true,markers(t){let{state:e}=t,i=e.facet(io);let o=[];for(let n of e.selection.ranges){let r=n==e.selection.main;if(n.empty?!r||eo:i.drawRangeCursor){let e=r?"cm-cursor cm-cursor-primary":"cm-cursor cm-cursor-secondary";let i=n.empty?n:s.EditorSelection.cursor(n.head,n.head>n.anchor?-1:1);for(let s of $s.forRange(t,e,i))o.push(s)}}return o},update(t,e){if(t.transactions.some((t=>t.selection)))e.style.animationName=e.style.animationName=="cm-blink"?"cm-blink2":"cm-blink";let i=oo(t);if(i)ro(t.state,e);return t.docChanged||t.selectionSet||i},mount(t,e){ro(e.state,t)},class:"cm-cursorLayer"});function ro(t,e){e.style.animationDuration=t.facet(io).cursorBlinkRate+"ms"}const lo=to({above:false,markers(t){return t.state.selection.ranges.map((e=>e.empty?[]:$s.forRange(t,"cm-selectionBackground",e))).reduce(((t,e)=>t.concat(e)))},update(t,e){return t.docChanged||t.selectionSet||t.viewportChanged||oo(t)},class:"cm-selectionLayer"});const ho={".cm-line":{"& ::selection":{backgroundColor:"transparent !important"},"&::selection":{backgroundColor:"transparent !important"}}};if(eo)ho[".cm-line"].caretColor="transparent !important";const ao=s.Prec.highest(Os.theme(ho));const co=s.StateEffect.define({map(t,e){return t==null?null:e.mapPos(t)}});const fo=s.StateField.define({create(){return null},update(t,e){if(t!=null)t=e.changes.mapPos(t);return e.effects.reduce(((t,e)=>e.is(co)?e.value:t),t)}});const uo=Qt.fromClass(class{constructor(t){this.view=t;this.cursor=null;this.measureReq={read:this.readPos.bind(this),write:this.drawCursor.bind(this)}}update(t){var e;let i=t.state.field(fo);if(i==null){if(this.cursor!=null){(e=this.cursor)===null||e===void 0?void 0:e.remove();this.cursor=null}}else{if(!this.cursor){this.cursor=this.view.scrollDOM.appendChild(document.createElement("div"));this.cursor.className="cm-dropCursor"}if(t.startState.field(fo)!=i||t.docChanged||t.geometryChanged)this.view.requestMeasure(this.measureReq)}}readPos(){let t=this.view.state.field(fo);let e=t!=null&&this.view.coordsAtPos(t);if(!e)return null;let i=this.view.scrollDOM.getBoundingClientRect();return{left:e.left-i.left+this.view.scrollDOM.scrollLeft,top:e.top-i.top+this.view.scrollDOM.scrollTop,height:e.bottom-e.top}}drawCursor(t){if(this.cursor){if(t){this.cursor.style.left=t.left+"px";this.cursor.style.top=t.top+"px";this.cursor.style.height=t.height+"px"}else{this.cursor.style.left="-100000px"}}}destroy(){if(this.cursor)this.cursor.remove()}setDropPos(t){if(this.view.state.field(fo)!=t)this.view.dispatch({effects:co.of(t)})}},{eventHandlers:{dragover(t){this.setDropPos(this.view.posAtCoords({x:t.clientX,y:t.clientY}))},dragleave(t){if(t.target==this.view.contentDOM||!this.view.contentDOM.contains(t.relatedTarget))this.setDropPos(null)},dragend(){this.setDropPos(null)},drop(){this.setDropPos(null)}}});function po(){return[fo,uo]}function go(t,e,i,s,o){e.lastIndex=0;for(let n=t.iterRange(i,s),r=i,l;!n.next().done;r+=n.value.length){if(!n.lineBreak)while(l=e.exec(n.value))o(r+l.index,l)}}function mo(t,e){let i=t.visibleRanges;if(i.length==1&&i[0].from==t.viewport.from&&i[0].to==t.viewport.to)return i;let s=[];for(let{from:o,to:n}of i){o=Math.max(t.state.doc.lineAt(o).from,o-e);n=Math.min(t.state.doc.lineAt(n).to,n+e);if(s.length&&s[s.length-1].to>=o)s[s.length-1].to=n;else s.push({from:o,to:n})}return s}class wo{constructor(t){const{regexp:e,decoration:i,decorate:s,boundary:o,maxLength:n=1e3}=t;if(!e.global)throw new RangeError("The regular expression given to MatchDecorator should have its 'g' flag set");this.regexp=e;if(s){this.addMatch=(t,e,i,o)=>s(o,i,i+t[0].length,t,e)}else if(typeof i=="function"){this.addMatch=(t,e,s,o)=>{let n=i(t,e,s);if(n)o(s,s+t[0].length,n)}}else if(i){this.addMatch=(t,e,s,o)=>o(s,s+t[0].length,i)}else{throw new RangeError("Either 'decorate' or 'decoration' should be provided to MatchDecorator")}this.boundary=o;this.maxLength=n}createDeco(t){let e=new s.RangeSetBuilder,i=e.add.bind(e);for(let{from:s,to:o}of mo(t,this.maxLength))go(t.state.doc,this.regexp,s,o,((e,s)=>this.addMatch(s,t,e,i)));return e.finish()}updateDeco(t,e){let i=1e9,s=-1;if(t.docChanged)t.changes.iterChanges(((e,o,n,r)=>{if(r>t.view.viewport.from&&n<t.view.viewport.to){i=Math.min(n,i);s=Math.max(r,s)}}));if(t.viewportChanged||s-i>1e3)return this.createDeco(t.view);if(s>-1)return this.updateRange(t.view,e.map(t.changes),i,s);return e}updateRange(t,e,i,s){for(let o of t.visibleRanges){let n=Math.max(o.from,i),r=Math.min(o.to,s);if(r>n){let i=t.state.doc.lineAt(n),s=i.to<r?t.state.doc.lineAt(r):i;let l=Math.max(o.from,i.from),h=Math.min(o.to,s.to);if(this.boundary){for(;n>i.from;n--)if(this.boundary.test(i.text[n-1-i.from])){l=n;break}for(;r<s.to;r++)if(this.boundary.test(s.text[r-s.from])){h=r;break}}let a=[],c;let f=(t,e,i)=>a.push(i.range(t,e));if(i==s){this.regexp.lastIndex=l-i.from;while((c=this.regexp.exec(i.text))&&c.index<h-i.from)this.addMatch(c,t,c.index+i.from,f)}else{go(t.state.doc,this.regexp,l,h,((e,i)=>this.addMatch(i,t,e,f)))}e=e.update({filterFrom:l,filterTo:h,filter:(t,e)=>t<l||e>h,add:a})}}return e}}const vo=/x/.unicode!=null?"gu":"g";const bo=new RegExp("[\0-\b\n--­؜​‎‏\u2028\u2029‭‮⁦⁧⁩\ufeff￹-￼]",vo);const yo={0:"null",7:"bell",8:"backspace",10:"newline",11:"vertical tab",13:"carriage return",27:"escape",8203:"zero width space",8204:"zero width non-joiner",8205:"zero width joiner",8206:"left-to-right mark",8207:"right-to-left mark",8232:"line separator",8237:"left-to-right override",8238:"right-to-left override",8294:"left-to-right isolate",8295:"right-to-left isolate",8297:"pop directional isolate",8233:"paragraph separator",65279:"zero width no-break space",65532:"object replacement"};let So=null;function xo(){var t;if(So==null&&typeof document!="undefined"&&document.body){let e=document.body.style;So=((t=e.tabSize)!==null&&t!==void 0?t:e.MozTabSize)!=null}return So||false}const Mo=s.Facet.define({combine(t){let e=(0,s.combineConfig)(t,{render:null,specialChars:bo,addSpecialChars:null});if(e.replaceTabs=!xo())e.specialChars=new RegExp("\t|"+e.specialChars.source,vo);if(e.addSpecialChars)e.specialChars=new RegExp(e.specialChars.source+"|"+e.addSpecialChars.source,vo);return e}});function ko(t={}){return[Mo.of(t),Ao()]}let Co=null;function Ao(){return Co||(Co=Qt.fromClass(class{constructor(t){this.view=t;this.decorations=kt.none;this.decorationCache=Object.create(null);this.decorator=this.makeDecorator(t.state.facet(Mo));this.decorations=this.decorator.createDeco(t)}makeDecorator(t){return new wo({regexp:t.specialChars,decoration:(e,i,o)=>{let{doc:n}=i.state;let r=(0,s.codePointAt)(e[0],0);if(r==9){let t=n.lineAt(o);let e=i.state.tabSize,r=(0,s.countColumn)(t.text,e,o-t.from);return kt.replace({widget:new Eo((e-r%e)*this.view.defaultCharacterWidth)})}return this.decorationCache[r]||(this.decorationCache[r]=kt.replace({widget:new Oo(t,r)}))},boundary:t.replaceTabs?undefined:/[^]/})}update(t){let e=t.state.facet(Mo);if(t.startState.facet(Mo)!=e){this.decorator=this.makeDecorator(e);this.decorations=this.decorator.createDeco(t.view)}else{this.decorations=this.decorator.updateDeco(t,this.decorations)}}},{decorations:t=>t.decorations}))}const Do="•";function To(t){if(t>=32)return Do;if(t==10)return"␤";return String.fromCharCode(9216+t)}class Oo extends xt{constructor(t,e){super();this.options=t;this.code=e}eq(t){return t.code==this.code}toDOM(t){let e=To(this.code);let i=t.state.phrase("Control character")+" "+(yo[this.code]||"0x"+this.code.toString(16));let s=this.options.render&&this.options.render(this.code,i,e);if(s)return s;let o=document.createElement("span");o.textContent=e;o.title=i;o.setAttribute("aria-label",i);o.className="cm-specialChar";return o}ignoreEvent(){return false}}class Eo extends xt{constructor(t){super();this.width=t}eq(t){return t.width==this.width}toDOM(){let t=document.createElement("span");t.textContent="\t";t.className="cm-tab";t.style.width=this.width+"px";return t}ignoreEvent(){return false}}const Ro=Qt.fromClass(class{constructor(){this.height=1e3;this.attrs={style:"padding-bottom: 1000px"}}update(t){let{view:e}=t;let i=e.viewState.editorHeight-e.defaultLineHeight-e.documentPadding.top-.5;if(i!=this.height){this.height=i;this.attrs={style:`padding-bottom: ${i}px`}}}});function Bo(){return[Ro,te.of((t=>{var e;return((e=t.plugin(Ro))===null||e===void 0?void 0:e.attrs)||null}))]}function Lo(){return Po}const Ho=kt.line({class:"cm-activeLine"});const Po=Qt.fromClass(class{constructor(t){this.decorations=this.getDeco(t)}update(t){if(t.docChanged||t.selectionSet)this.decorations=this.getDeco(t.view)}getDeco(t){let e=-1,i=[];for(let s of t.state.selection.ranges){let o=t.lineBlockAt(s.head);if(o.from>e){i.push(Ho.range(o.from));e=o.from}}return kt.set(i)}},{decorations:t=>t.decorations});class Vo extends xt{constructor(t){super();this.content=t}toDOM(){let t=document.createElement("span");t.className="cm-placeholder";t.style.pointerEvents="none";t.appendChild(typeof this.content=="string"?document.createTextNode(this.content):this.content);if(typeof this.content=="string")t.setAttribute("aria-label","placeholder "+this.content);else t.setAttribute("aria-hidden","true");return t}ignoreEvent(){return false}}function No(t){return Qt.fromClass(class{constructor(e){this.view=e;this.placeholder=kt.set([kt.widget({widget:new Vo(t),side:1}).range(0)])}get decorations(){return this.view.state.doc.length?kt.none:this.placeholder}},{decorations:t=>t.decorations})}const Wo=2e3;function Fo(t,e,i){let o=Math.min(e.line,i.line),n=Math.max(e.line,i.line);let r=[];if(e.off>Wo||i.off>Wo||e.col<0||i.col<0){let l=Math.min(e.off,i.off),h=Math.max(e.off,i.off);for(let e=o;e<=n;e++){let i=t.doc.line(e);if(i.length<=h)r.push(s.EditorSelection.range(i.from+l,i.to+h))}}else{let l=Math.min(e.col,i.col),h=Math.max(e.col,i.col);for(let e=o;e<=n;e++){let i=t.doc.line(e);let o=(0,s.findColumn)(i.text,l,t.tabSize,true);if(o<0){r.push(s.EditorSelection.cursor(i.to))}else{let e=(0,s.findColumn)(i.text,h,t.tabSize);r.push(s.EditorSelection.range(i.from+o,i.from+e))}}}return r}function zo(t,e){let i=t.coordsAtPos(t.viewport.from);return i?Math.round(Math.abs((i.left-e)/t.defaultCharacterWidth)):-1}function Io(t,e){let i=t.posAtCoords({x:e.clientX,y:e.clientY},false);let o=t.state.doc.lineAt(i),n=i-o.from;let r=n>Wo?-1:n==o.length?zo(t,e.clientX):(0,s.countColumn)(o.text,t.state.tabSize,i-o.from);return{line:o.number,col:r,off:n}}function Ko(t,e){let i=Io(t,e),o=t.state.selection;if(!i)return null;return{update(t){if(t.docChanged){let e=t.changes.mapPos(t.startState.doc.line(i.line).from);let s=t.state.doc.lineAt(e);i={line:s.number,col:i.col,off:Math.min(i.off,s.length)};o=o.map(t.changes)}},get(e,n,r){let l=Io(t,e);if(!l)return o;let h=Fo(t.state,i,l);if(!h.length)return o;if(r)return s.EditorSelection.create(h.concat(o.ranges));else return s.EditorSelection.create(h)}}}function qo(t){let e=(t===null||t===void 0?void 0:t.eventFilter)||(t=>t.altKey&&t.button==0);return Os.mouseSelectionStyle.of(((t,i)=>e(i)?Ko(t,i):null))}const Go={Alt:[18,t=>t.altKey],Control:[17,t=>t.ctrlKey],Shift:[16,t=>t.shiftKey],Meta:[91,t=>t.metaKey]};const jo={style:"cursor: crosshair"};function $o(t={}){let[e,i]=Go[t.key||"Alt"];let s=Qt.fromClass(class{constructor(t){this.view=t;this.isDown=false}set(t){if(this.isDown!=t){this.isDown=t;this.view.update([])}}},{eventHandlers:{keydown(t){this.set(t.keyCode==e||i(t))},keyup(t){if(t.keyCode==e||!i(t))this.set(false)},mousemove(t){this.set(i(t))}}});return[s,Os.contentAttributes.of((t=>{var e;return((e=t.plugin(s))===null||e===void 0?void 0:e.isDown)?jo:null}))]}const _o="-10000px";class Uo{constructor(t,e,i){this.facet=e;this.createTooltipView=i;this.input=t.state.facet(e);this.tooltips=this.input.filter((t=>t));this.tooltipViews=this.tooltips.map(i)}update(t){var e;let i=t.state.facet(this.facet);let s=i.filter((t=>t));if(i===this.input){for(let e of this.tooltipViews)if(e.update)e.update(t);return false}let o=[];for(let n=0;n<s.length;n++){let e=s[n],i=-1;if(!e)continue;for(let t=0;t<this.tooltips.length;t++){let s=this.tooltips[t];if(s&&s.create==e.create)i=t}if(i<0){o[n]=this.createTooltipView(e)}else{let e=o[n]=this.tooltipViews[i];if(e.update)e.update(t)}}for(let n of this.tooltipViews)if(o.indexOf(n)<0){n.dom.remove();(e=n.destroy)===null||e===void 0?void 0:e.call(n)}this.input=i;this.tooltips=s;this.tooltipViews=o;return true}}function Xo(t={}){return Qo.of(t)}function Yo(t){let{win:e}=t;return{top:0,left:0,bottom:e.innerHeight,right:e.innerWidth}}const Qo=s.Facet.define({combine:t=>{var e,i,s;return{position:it.ios?"absolute":((e=t.find((t=>t.position)))===null||e===void 0?void 0:e.position)||"fixed",parent:((i=t.find((t=>t.parent)))===null||i===void 0?void 0:i.parent)||null,tooltipSpace:((s=t.find((t=>t.tooltipSpace)))===null||s===void 0?void 0:s.tooltipSpace)||Yo}}});const Jo=new WeakMap;const Zo=Qt.fromClass(class{constructor(t){this.view=t;this.inView=true;this.lastTransaction=0;this.measureTimeout=-1;let e=t.state.facet(Qo);this.position=e.position;this.parent=e.parent;this.classes=t.themeClasses;this.createContainer();this.measureReq={read:this.readMeasure.bind(this),write:this.writeMeasure.bind(this),key:this};this.manager=new Uo(t,sn,(t=>this.createTooltip(t)));this.intersectionObserver=typeof IntersectionObserver=="function"?new IntersectionObserver((t=>{if(Date.now()>this.lastTransaction-50&&t.length>0&&t[t.length-1].intersectionRatio<1)this.measureSoon()}),{threshold:[1]}):null;this.observeIntersection();t.win.addEventListener("resize",this.measureSoon=this.measureSoon.bind(this));this.maybeMeasure()}createContainer(){if(this.parent){this.container=document.createElement("div");this.container.style.position="relative";this.container.className=this.view.themeClasses;this.parent.appendChild(this.container)}else{this.container=this.view.dom}}observeIntersection(){if(this.intersectionObserver){this.intersectionObserver.disconnect();for(let t of this.manager.tooltipViews)this.intersectionObserver.observe(t.dom)}}measureSoon(){if(this.measureTimeout<0)this.measureTimeout=setTimeout((()=>{this.measureTimeout=-1;this.maybeMeasure()}),50)}update(t){if(t.transactions.length)this.lastTransaction=Date.now();let e=this.manager.update(t);if(e)this.observeIntersection();let i=e||t.geometryChanged;let s=t.state.facet(Qo);if(s.position!=this.position){this.position=s.position;for(let t of this.manager.tooltipViews)t.dom.style.position=this.position;i=true}if(s.parent!=this.parent){if(this.parent)this.container.remove();this.parent=s.parent;this.createContainer();for(let t of this.manager.tooltipViews)this.container.appendChild(t.dom);i=true}else if(this.parent&&this.view.themeClasses!=this.classes){this.classes=this.container.className=this.view.themeClasses}if(i)this.maybeMeasure()}createTooltip(t){let e=t.create(this.view);e.dom.classList.add("cm-tooltip");if(t.arrow&&!e.dom.querySelector(".cm-tooltip > .cm-tooltip-arrow")){let t=document.createElement("div");t.className="cm-tooltip-arrow";e.dom.appendChild(t)}e.dom.style.position=this.position;e.dom.style.top=_o;this.container.appendChild(e.dom);if(e.mount)e.mount(this.view);return e}destroy(){var t,e;this.view.win.removeEventListener("resize",this.measureSoon);for(let i of this.manager.tooltipViews){i.dom.remove();(t=i.destroy)===null||t===void 0?void 0:t.call(i)}(e=this.intersectionObserver)===null||e===void 0?void 0:e.disconnect();clearTimeout(this.measureTimeout)}readMeasure(){let t=this.view.dom.getBoundingClientRect();return{editor:t,parent:this.parent?this.container.getBoundingClientRect():t,pos:this.manager.tooltips.map(((t,e)=>{let i=this.manager.tooltipViews[e];return i.getCoords?i.getCoords(t.pos):this.view.coordsAtPos(t.pos)})),size:this.manager.tooltipViews.map((({dom:t})=>t.getBoundingClientRect())),space:this.view.state.facet(Qo).tooltipSpace(this.view)}}writeMeasure(t){var e;let{editor:i,space:s}=t;let o=[];for(let n=0;n<this.manager.tooltips.length;n++){let r=this.manager.tooltips[n],l=this.manager.tooltipViews[n],{dom:h}=l;let a=t.pos[n],c=t.size[n];if(!a||a.bottom<=Math.max(i.top,s.top)||a.top>=Math.min(i.bottom,s.bottom)||a.right<Math.max(i.left,s.left)-.1||a.left>Math.min(i.right,s.right)+.1){h.style.top=_o;continue}let f=r.arrow?l.dom.querySelector(".cm-tooltip-arrow"):null;let d=f?7:0;let u=c.right-c.left,p=(e=Jo.get(l))!==null&&e!==void 0?e:c.bottom-c.top;let g=l.offset||en,m=this.view.textDirection==le.LTR;let w=c.width>s.right-s.left?m?s.left:s.right-c.width:m?Math.min(a.left-(f?14:0)+g.x,s.right-u):Math.max(s.left,a.left-u+(f?14:0)-g.x);let v=!!r.above;if(!r.strictSide&&(v?a.top-(c.bottom-c.top)-g.y<s.top:a.bottom+(c.bottom-c.top)+g.y>s.bottom)&&v==s.bottom-a.bottom>a.top-s.top)v=!v;let b=(v?a.top-s.top:s.bottom-a.bottom)-d;if(b<p&&l.resize!==false){if(b<this.view.defaultLineHeight){h.style.top=_o;continue}Jo.set(l,p);h.style.height=(p=b)+"px"}else if(h.style.height){h.style.height=""}let y=v?a.top-p-d-g.y:a.bottom+d+g.y;let S=w+u;if(l.overlap!==true)for(let t of o)if(t.left<S&&t.right>w&&t.top<y+p&&t.bottom>y)y=v?t.top-p-2-d:t.bottom+d+2;if(this.position=="absolute"){h.style.top=y-t.parent.top+"px";h.style.left=w-t.parent.left+"px"}else{h.style.top=y+"px";h.style.left=w+"px"}if(f)f.style.left=`${a.left+(m?g.x:-g.x)-(w+14-7)}px`;if(l.overlap!==true)o.push({left:w,top:y,right:S,bottom:y+p});h.classList.toggle("cm-tooltip-above",v);h.classList.toggle("cm-tooltip-below",!v);if(l.positioned)l.positioned(t.space)}}maybeMeasure(){if(this.manager.tooltips.length){if(this.view.inView)this.view.requestMeasure(this.measureReq);if(this.inView!=this.view.inView){this.inView=this.view.inView;if(!this.inView)for(let t of this.manager.tooltipViews)t.dom.style.top=_o}}}},{eventHandlers:{scroll(){this.maybeMeasure()}}});const tn=Os.baseTheme({".cm-tooltip":{zIndex:100,boxSizing:"border-box"},"&light .cm-tooltip":{border:"1px solid #bbb",backgroundColor:"#f5f5f5"},"&light .cm-tooltip-section:not(:first-child)":{borderTop:"1px solid #bbb"},"&dark .cm-tooltip":{backgroundColor:"#333338",color:"white"},".cm-tooltip-arrow":{height:`${7}px`,width:`${7*2}px`,position:"absolute",zIndex:-1,overflow:"hidden","&:before, &:after":{content:"''",position:"absolute",width:0,height:0,borderLeft:`${7}px solid transparent`,borderRight:`${7}px solid transparent`},".cm-tooltip-above &":{bottom:`-${7}px`,"&:before":{borderTop:`${7}px solid #bbb`},"&:after":{borderTop:`${7}px solid #f5f5f5`,bottom:"1px"}},".cm-tooltip-below &":{top:`-${7}px`,"&:before":{borderBottom:`${7}px solid #bbb`},"&:after":{borderBottom:`${7}px solid #f5f5f5`,top:"1px"}}},"&dark .cm-tooltip .cm-tooltip-arrow":{"&:before":{borderTopColor:"#333338",borderBottomColor:"#333338"},"&:after":{borderTopColor:"transparent",borderBottomColor:"transparent"}}});const en={x:0,y:0};const sn=s.Facet.define({enables:[Zo,tn]});const on=s.Facet.define();class nn{constructor(t){this.view=t;this.mounted=false;this.dom=document.createElement("div");this.dom.classList.add("cm-tooltip-hover");this.manager=new Uo(t,on,(t=>this.createHostedView(t)))}static create(t){return new nn(t)}createHostedView(t){let e=t.create(this.view);e.dom.classList.add("cm-tooltip-section");this.dom.appendChild(e.dom);if(this.mounted&&e.mount)e.mount(this.view);return e}mount(t){for(let e of this.manager.tooltipViews){if(e.mount)e.mount(t)}this.mounted=true}positioned(t){for(let e of this.manager.tooltipViews){if(e.positioned)e.positioned(t)}}update(t){this.manager.update(t)}destroy(){var t;for(let e of this.manager.tooltipViews)(t=e.destroy)===null||t===void 0?void 0:t.call(e)}}const rn=sn.compute([on],(t=>{let e=t.facet(on).filter((t=>t));if(e.length===0)return null;return{pos:Math.min(...e.map((t=>t.pos))),end:Math.max(...e.filter((t=>t.end!=null)).map((t=>t.end))),create:nn.create,above:e[0].above,arrow:e.some((t=>t.arrow))}}));class ln{constructor(t,e,i,s,o){this.view=t;this.source=e;this.field=i;this.setHover=s;this.hoverTime=o;this.hoverTimeout=-1;this.restartTimeout=-1;this.pending=null;this.lastMove={x:0,y:0,target:t.dom,time:0};this.checkHover=this.checkHover.bind(this);t.dom.addEventListener("mouseleave",this.mouseleave=this.mouseleave.bind(this));t.dom.addEventListener("mousemove",this.mousemove=this.mousemove.bind(this))}update(){if(this.pending){this.pending=null;clearTimeout(this.restartTimeout);this.restartTimeout=setTimeout((()=>this.startHover()),20)}}get active(){return this.view.state.field(this.field)}checkHover(){this.hoverTimeout=-1;if(this.active)return;let t=Date.now()-this.lastMove.time;if(t<this.hoverTime)this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime-t);else this.startHover()}startHover(){clearTimeout(this.restartTimeout);let{lastMove:t}=this;let e=this.view.contentDOM.contains(t.target)?this.view.posAtCoords(t):null;if(e==null)return;let i=this.view.coordsAtPos(e);if(i==null||t.y<i.top||t.y>i.bottom||t.x<i.left-this.view.defaultCharacterWidth||t.x>i.right+this.view.defaultCharacterWidth)return;let s=this.view.bidiSpans(this.view.state.doc.lineAt(e)).find((t=>t.from<=e&&t.to>=e));let o=s&&s.dir==le.RTL?-1:1;let n=this.source(this.view,e,t.x<i.left?-o:o);if(n===null||n===void 0?void 0:n.then){let t=this.pending={pos:e};n.then((e=>{if(this.pending==t){this.pending=null;if(e)this.view.dispatch({effects:this.setHover.of(e)})}}),(t=>_t(this.view.state,t,"hover tooltip")))}else if(n){this.view.dispatch({effects:this.setHover.of(n)})}}mousemove(t){var e;this.lastMove={x:t.clientX,y:t.clientY,target:t.target,time:Date.now()};if(this.hoverTimeout<0)this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime);let i=this.active;if(i&&!hn(this.lastMove.target)||this.pending){let{pos:s}=i||this.pending,o=(e=i===null||i===void 0?void 0:i.end)!==null&&e!==void 0?e:s;if(s==o?this.view.posAtCoords(this.lastMove)!=s:!an(this.view,s,o,t.clientX,t.clientY,6)){this.view.dispatch({effects:this.setHover.of(null)});this.pending=null}}}mouseleave(t){clearTimeout(this.hoverTimeout);this.hoverTimeout=-1;if(this.active&&!hn(t.relatedTarget))this.view.dispatch({effects:this.setHover.of(null)})}destroy(){clearTimeout(this.hoverTimeout);this.view.dom.removeEventListener("mouseleave",this.mouseleave);this.view.dom.removeEventListener("mousemove",this.mousemove)}}function hn(t){for(let e=t;e;e=e.parentNode)if(e.nodeType==1&&e.classList.contains("cm-tooltip"))return true;return false}function an(t,e,i,s,o,n){let r=document.createRange();let l=t.domAtPos(e),h=t.domAtPos(i);r.setEnd(h.node,h.offset);r.setStart(l.node,l.offset);let a=r.getClientRects();r.detach();for(let c=0;c<a.length;c++){let t=a[c];let e=Math.max(t.top-o,o-t.bottom,t.left-s,s-t.right);if(e<=n)return true}return false}function cn(t,e={}){let i=s.StateEffect.define();let o=s.StateField.define({create(){return null},update(t,o){if(t&&(e.hideOnChange&&(o.docChanged||o.selection)||e.hideOn&&e.hideOn(o,t)))return null;if(t&&o.docChanged){let e=o.changes.mapPos(t.pos,-1,s.MapMode.TrackDel);if(e==null)return null;let i=Object.assign(Object.create(null),t);i.pos=e;if(t.end!=null)i.end=o.changes.mapPos(t.end);t=i}for(let e of o.effects){if(e.is(i))t=e.value;if(e.is(un))t=null}return t},provide:t=>on.from(t)});return[o,Qt.define((s=>new ln(s,t,o,i,e.hoverTime||300))),rn]}function fn(t,e){let i=t.plugin(Zo);if(!i)return null;let s=i.manager.tooltips.indexOf(e);return s<0?null:i.manager.tooltipViews[s]}function dn(t){return t.facet(on).some((t=>t))}const un=s.StateEffect.define();const pn=un.of(null);function gn(t){var e;(e=t.plugin(Zo))===null||e===void 0?void 0:e.maybeMeasure()}const mn=s.Facet.define({combine(t){let e,i;for(let s of t){e=e||s.topContainer;i=i||s.bottomContainer}return{topContainer:e,bottomContainer:i}}});function wn(t){return t?[mn.of(t)]:[]}function vn(t,e){let i=t.plugin(bn);let s=i?i.specs.indexOf(e):-1;return s>-1?i.panels[s]:null}const bn=Qt.fromClass(class{constructor(t){this.input=t.state.facet(xn);this.specs=this.input.filter((t=>t));this.panels=this.specs.map((e=>e(t)));let e=t.state.facet(mn);this.top=new yn(t,true,e.topContainer);this.bottom=new yn(t,false,e.bottomContainer);this.top.sync(this.panels.filter((t=>t.top)));this.bottom.sync(this.panels.filter((t=>!t.top)));for(let i of this.panels){i.dom.classList.add("cm-panel");if(i.mount)i.mount()}}update(t){let e=t.state.facet(mn);if(this.top.container!=e.topContainer){this.top.sync([]);this.top=new yn(t.view,true,e.topContainer)}if(this.bottom.container!=e.bottomContainer){this.bottom.sync([]);this.bottom=new yn(t.view,false,e.bottomContainer)}this.top.syncClasses();this.bottom.syncClasses();let i=t.state.facet(xn);if(i!=this.input){let e=i.filter((t=>t));let s=[],o=[],n=[],r=[];for(let i of e){let e=this.specs.indexOf(i),l;if(e<0){l=i(t.view);r.push(l)}else{l=this.panels[e];if(l.update)l.update(t)}s.push(l);(l.top?o:n).push(l)}this.specs=e;this.panels=s;this.top.sync(o);this.bottom.sync(n);for(let t of r){t.dom.classList.add("cm-panel");if(t.mount)t.mount()}}else{for(let e of this.panels)if(e.update)e.update(t)}}destroy(){this.top.sync([]);this.bottom.sync([])}},{provide:t=>Os.scrollMargins.of((e=>{let i=e.plugin(t);return i&&{top:i.top.scrollMargin(),bottom:i.bottom.scrollMargin()}}))});class yn{constructor(t,e,i){this.view=t;this.top=e;this.container=i;this.dom=undefined;this.classes="";this.panels=[];this.syncClasses()}sync(t){for(let e of this.panels)if(e.destroy&&t.indexOf(e)<0)e.destroy();this.panels=t;this.syncDOM()}syncDOM(){if(this.panels.length==0){if(this.dom){this.dom.remove();this.dom=undefined}return}if(!this.dom){this.dom=document.createElement("div");this.dom.className=this.top?"cm-panels cm-panels-top":"cm-panels cm-panels-bottom";this.dom.style[this.top?"top":"bottom"]="0";let t=this.container||this.view.dom;t.insertBefore(this.dom,this.top?t.firstChild:null)}let t=this.dom.firstChild;for(let e of this.panels){if(e.dom.parentNode==this.dom){while(t!=e.dom)t=Sn(t);t=t.nextSibling}else{this.dom.insertBefore(e.dom,t)}}while(t)t=Sn(t)}scrollMargin(){return!this.dom||this.container?0:Math.max(0,this.top?this.dom.getBoundingClientRect().bottom-Math.max(0,this.view.scrollDOM.getBoundingClientRect().top):Math.min(innerHeight,this.view.scrollDOM.getBoundingClientRect().bottom)-this.dom.getBoundingClientRect().top)}syncClasses(){if(!this.container||this.classes==this.view.themeClasses)return;for(let t of this.classes.split(" "))if(t)this.container.classList.remove(t);for(let t of(this.classes=this.view.themeClasses).split(" "))if(t)this.container.classList.add(t)}}function Sn(t){let e=t.nextSibling;t.remove();return e}const xn=s.Facet.define({enables:bn});class Mn extends s.RangeValue{compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}eq(t){return false}destroy(t){}}Mn.prototype.elementClass="";Mn.prototype.toDOM=undefined;Mn.prototype.mapMode=s.MapMode.TrackBefore;Mn.prototype.startSide=Mn.prototype.endSide=-1;Mn.prototype.point=true;const kn=s.Facet.define();const Cn={class:"",renderEmptyElements:false,elementStyle:"",markers:()=>s.RangeSet.empty,lineMarker:()=>null,lineMarkerChange:null,initialSpacer:null,updateSpacer:null,domEventHandlers:{}};const An=s.Facet.define();function Dn(t){return[On(),An.of(Object.assign(Object.assign({},Cn),t))]}const Tn=s.Facet.define({combine:t=>t.some((t=>t))});function On(t){let e=[En];if(t&&t.fixed===false)e.push(Tn.of(true));return e}const En=Qt.fromClass(class{constructor(t){this.view=t;this.prevViewport=t.viewport;this.dom=document.createElement("div");this.dom.className="cm-gutters";this.dom.setAttribute("aria-hidden","true");this.dom.style.minHeight=this.view.contentHeight+"px";this.gutters=t.state.facet(An).map((e=>new Hn(t,e)));for(let e of this.gutters)this.dom.appendChild(e.dom);this.fixed=!t.state.facet(Tn);if(this.fixed){this.dom.style.position="sticky"}this.syncGutters(false);t.scrollDOM.insertBefore(this.dom,t.contentDOM)}update(t){if(this.updateGutters(t)){let e=this.prevViewport,i=t.view.viewport;let s=Math.min(e.to,i.to)-Math.max(e.from,i.from);this.syncGutters(s<(i.to-i.from)*.8)}if(t.geometryChanged)this.dom.style.minHeight=this.view.contentHeight+"px";if(this.view.state.facet(Tn)!=!this.fixed){this.fixed=!this.fixed;this.dom.style.position=this.fixed?"sticky":""}this.prevViewport=t.view.viewport}syncGutters(t){let e=this.dom.nextSibling;if(t)this.dom.remove();let i=s.RangeSet.iter(this.view.state.facet(kn),this.view.viewport.from);let o=[];let n=this.gutters.map((t=>new Ln(t,this.view.viewport,-this.view.documentPadding.top)));for(let s of this.view.viewportLineBlocks){let t;if(Array.isArray(s.type)){for(let e of s.type)if(e.type==Mt.Text){t=e;break}}else{t=s.type==Mt.Text?s:undefined}if(!t)continue;if(o.length)o=[];Bn(i,o,s.from);for(let e of n)e.line(this.view,t,o)}for(let s of n)s.finish();if(t)this.view.scrollDOM.insertBefore(this.dom,e)}updateGutters(t){let e=t.startState.facet(An),i=t.state.facet(An);let o=t.docChanged||t.heightChanged||t.viewportChanged||!s.RangeSet.eq(t.startState.facet(kn),t.state.facet(kn),t.view.viewport.from,t.view.viewport.to);if(e==i){for(let e of this.gutters)if(e.update(t))o=true}else{o=true;let s=[];for(let o of i){let i=e.indexOf(o);if(i<0){s.push(new Hn(this.view,o))}else{this.gutters[i].update(t);s.push(this.gutters[i])}}for(let t of this.gutters){t.dom.remove();if(s.indexOf(t)<0)t.destroy()}for(let t of s)this.dom.appendChild(t.dom);this.gutters=s}return o}destroy(){for(let t of this.gutters)t.destroy();this.dom.remove()}},{provide:t=>Os.scrollMargins.of((e=>{let i=e.plugin(t);if(!i||i.gutters.length==0||!i.fixed)return null;return e.textDirection==le.LTR?{left:i.dom.offsetWidth}:{right:i.dom.offsetWidth}}))});function Rn(t){return Array.isArray(t)?t:[t]}function Bn(t,e,i){while(t.value&&t.from<=i){if(t.from==i)e.push(t.value);t.next()}}class Ln{constructor(t,e,i){this.gutter=t;this.height=i;this.i=0;this.cursor=s.RangeSet.iter(t.markers,e.from)}line(t,e,i){let s=[];Bn(this.cursor,s,e.from);if(i.length)s=s.concat(i);let o=this.gutter.config.lineMarker(t,e,s);if(o)s.unshift(o);let n=this.gutter;if(s.length==0&&!n.config.renderEmptyElements)return;let r=e.top-this.height;if(this.i==n.elements.length){let i=new Pn(t,e.height,r,s);n.elements.push(i);n.dom.appendChild(i.dom)}else{n.elements[this.i].update(t,e.height,r,s)}this.height=e.bottom;this.i++}finish(){let t=this.gutter;while(t.elements.length>this.i){let e=t.elements.pop();t.dom.removeChild(e.dom);e.destroy()}}}class Hn{constructor(t,e){this.view=t;this.config=e;this.elements=[];this.spacer=null;this.dom=document.createElement("div");this.dom.className="cm-gutter"+(this.config.class?" "+this.config.class:"");for(let i in e.domEventHandlers){this.dom.addEventListener(i,(s=>{let o=s.target,n;if(o!=this.dom&&this.dom.contains(o)){while(o.parentNode!=this.dom)o=o.parentNode;let t=o.getBoundingClientRect();n=(t.top+t.bottom)/2}else{n=s.clientY}let r=t.lineBlockAtHeight(n-t.documentTop);if(e.domEventHandlers[i](t,r,s))s.preventDefault()}))}this.markers=Rn(e.markers(t));if(e.initialSpacer){this.spacer=new Pn(t,0,0,[e.initialSpacer(t)]);this.dom.appendChild(this.spacer.dom);this.spacer.dom.style.cssText+="visibility: hidden; pointer-events: none"}}update(t){let e=this.markers;this.markers=Rn(this.config.markers(t.view));if(this.spacer&&this.config.updateSpacer){let e=this.config.updateSpacer(this.spacer.markers[0],t);if(e!=this.spacer.markers[0])this.spacer.update(t.view,0,0,[e])}let i=t.view.viewport;return!s.RangeSet.eq(this.markers,e,i.from,i.to)||(this.config.lineMarkerChange?this.config.lineMarkerChange(t):false)}destroy(){for(let t of this.elements)t.destroy()}}class Pn{constructor(t,e,i,s){this.height=-1;this.above=0;this.markers=[];this.dom=document.createElement("div");this.dom.className="cm-gutterElement";this.update(t,e,i,s)}update(t,e,i,s){if(this.height!=e)this.dom.style.height=(this.height=e)+"px";if(this.above!=i)this.dom.style.marginTop=(this.above=i)?i+"px":"";if(!Vn(this.markers,s))this.setMarkers(t,s)}setMarkers(t,e){let i="cm-gutterElement",s=this.dom.firstChild;for(let o=0,n=0;;){let r=n,l=o<e.length?e[o++]:null,h=false;if(l){let t=l.elementClass;if(t)i+=" "+t;for(let e=n;e<this.markers.length;e++)if(this.markers[e].compare(l)){r=e;h=true;break}}else{r=this.markers.length}while(n<r){let t=this.markers[n++];if(t.toDOM){t.destroy(s);let e=s.nextSibling;s.remove();s=e}}if(!l)break;if(l.toDOM){if(h)s=s.nextSibling;else this.dom.insertBefore(l.toDOM(t),s)}if(h)n++}this.dom.className=i;this.markers=e}destroy(){this.setMarkers(null,[])}}function Vn(t,e){if(t.length!=e.length)return false;for(let i=0;i<t.length;i++)if(!t[i].compare(e[i]))return false;return true}const Nn=s.Facet.define();const Wn=s.Facet.define({combine(t){return(0,s.combineConfig)(t,{formatNumber:String,domEventHandlers:{}},{domEventHandlers(t,e){let i=Object.assign({},t);for(let s in e){let t=i[s],o=e[s];i[s]=t?(e,i,s)=>t(e,i,s)||o(e,i,s):o}return i}})}});class Fn extends Mn{constructor(t){super();this.number=t}eq(t){return this.number==t.number}toDOM(){return document.createTextNode(this.number)}}function zn(t,e){return t.state.facet(Wn).formatNumber(e,t.state)}const In=An.compute([Wn],(t=>({class:"cm-lineNumbers",renderEmptyElements:false,markers(t){return t.state.facet(Nn)},lineMarker(t,e,i){if(i.some((t=>t.toDOM)))return null;return new Fn(zn(t,t.state.doc.lineAt(e.from).number))},lineMarkerChange:t=>t.startState.facet(Wn)!=t.state.facet(Wn),initialSpacer(t){return new Fn(zn(t,qn(t.state.doc.lines)))},updateSpacer(t,e){let i=zn(e.view,qn(e.view.state.doc.lines));return i==t.number?t:new Fn(i)},domEventHandlers:t.facet(Wn).domEventHandlers})));function Kn(t={}){return[Wn.of(t),On(),In]}function qn(t){let e=9;while(e<t)e=e*10+9;return e}const Gn=new class extends Mn{constructor(){super(...arguments);this.elementClass="cm-activeLineGutter"}};const jn=kn.compute(["selection"],(t=>{let e=[],i=-1;for(let s of t.selection.ranges){let o=t.doc.lineAt(s.head).from;if(o>i){i=o;e.push(Gn.range(o))}}return s.RangeSet.of(e)}));function $n(){return jn}const _n=new Map;function Un(t){let e=_n.get(t);if(!e)_n.set(t,e=kt.mark({attributes:t==="\t"?{class:"cm-highlightTab"}:{class:"cm-highlightSpace","data-display":t.replace(/ /g,"·")}}));return e}function Xn(t){return Qt.define((e=>({decorations:t.createDeco(e),update(e){this.decorations=t.updateDeco(e,this.decorations)}})),{decorations:t=>t.decorations})}const Yn=Xn(new wo({regexp:/\t| +/g,decoration:t=>Un(t[0]),boundary:/\S/}));function Qn(){return Yn}const Jn=Xn(new wo({regexp:/\s+$/g,decoration:kt.mark({class:"cm-trailingSpace"}),boundary:/\S/}));function Zn(){return Jn}const tr={HeightMap:Ki,HeightOracle:Ni,MeasuredHeights:Wi,QueryType:zi,ChangedRange:ne,computeOrder:be,moveVisually:xe}}}]);
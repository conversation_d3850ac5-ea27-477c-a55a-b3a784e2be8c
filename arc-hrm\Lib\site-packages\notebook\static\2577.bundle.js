"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[2577],{92577:(e,t,n)=>{n.r(t),n.d(t,{CellList:()=>m,CellTypeSwitcher:()=>f,CommandEditStatus:()=>E,ExecutionIndicator:()=>k,ExecutionIndicatorComponent:()=>b,INotebookTools:()=>we,INotebookTracker:()=>ye,INotebookWidgetFactory:()=>fe,KernelError:()=>g,Notebook:()=>ce,NotebookActions:()=>C,NotebookAdapter:()=>N,NotebookModel:()=>M,NotebookModelFactory:()=>I,NotebookPanel:()=>ue,NotebookSearchProvider:()=>ve,NotebookToCFactory:()=>pe,NotebookToCModel:()=>me,NotebookTools:()=>B,NotebookTracker:()=>be,NotebookTrustStatus:()=>Se,NotebookViewModel:()=>$,NotebookWidgetFactory:()=>Me,NotebookWindowedLayout:()=>J,RunningStatus:()=>ge,StaticNotebook:()=>re,ToolbarItems:()=>p,getIdForHeading:()=>_e});var o=n(12982),i=n(24912),s=n(38639),l=n(71677),a=n(33625),d=n(20998),r=n(81997),c=n(78156),h=n.n(c);const u="application/vnd.jupyter.cells";class g extends Error{constructor(e){const t=e,n=t.ename,o=t.evalue;super(`KernelReplyNotOK: ${n} ${o}`),this.errorName=n,this.errorValue=o,this.traceback=t.traceback,Object.setPrototypeOf(this,g.prototype)}}class C{static get executed(){return v.executed}static get executionScheduled(){return v.executionScheduled}static get selectionExecuted(){return v.selectionExecuted}static get outputCleared(){return v.outputCleared}constructor(){}}var v;!function(e){function t(e,t){if(!e.model||!e.activeCell)return;const n=v.getState(e),o=e.widgets.findIndex((t=>e.isSelectedOrActive(t)));let i=e.widgets.slice(o+1).findIndex((t=>!e.isSelectedOrActive(t)));i>=0?i+=o+1:i=e.model.cells.length,t>0?e.moveCell(o,i,i-o):e.moveCell(o,o+t,i-o),v.handleState(e,n,!0)}function n(e,t="below",n,o=!1){if(!e.model||!e.activeCell)return;const i=v.getState(e),s=e.model;e.mode="command";let l=0;const a=e.activeCellIndex;s.sharedModel.transact((()=>{switch(t){case"below":l=e.activeCellIndex+1;break;case"belowSelected":e.widgets.forEach(((t,n)=>{e.isSelectedOrActive(t)&&(l=n+1)}));break;case"above":l=e.activeCellIndex;break;case"replace":{const t=[];e.widgets.forEach(((n,o)=>{const i=!1!==n.model.sharedModel.getMetadata("deletable");e.isSelectedOrActive(n)&&i&&t.push(o)})),t.length>0&&t.reverse().forEach((e=>{s.sharedModel.deleteCell(e)})),l=t[0];break}}s.sharedModel.insertCells(l,n.map((t=>(t.id="code"===t.cell_type&&"cut"===e.lastClipboardInteraction&&"string"==typeof t.id?t.id:void 0,t))))})),e.activeCellIndex=a+n.length,e.deselectAll(),o&&(e.lastClipboardInteraction="paste"),v.handleState(e,i,!0)}function r(t,n,o){const s=(0,a.findIndex)(o.widgets,((e,n)=>t.model.id===e.model.id));if(-1===s)return-1;if(!o.widgets.length)return s+1;let l=e.getHeadingInfo(t);if(t.isHidden||!(t instanceof i.MarkdownCell)||!l.isHeading)return s+1;let d,r=!1,c=0;for(d=s+1;d<o.widgets.length;d++){let t=o.widgets[d],i=e.getHeadingInfo(t);if(i.isHeading&&i.headingLevel<=l.headingLevel){d-=1;break}r&&i.isHeading&&i.headingLevel<=c&&(r=!1),n||r?t.setHidden(!0):(i.collapsed&&i.isHeading&&(r=!0,c=i.headingLevel),t.setHidden(!1))}return d===o.widgets.length?t.numberChildNodes=d-s-1:t.numberChildNodes=d-s,e.setCellCollapse(t,n),d+1}function h(e){if(!(e instanceof i.MarkdownCell))return{isHeading:!1,headingLevel:7};let t=e.headingInfo.level;return{isHeading:t>0,headingLevel:t,collapsed:e.headingCollapsed}}async function g(e,t={waitUntilReady:!0,preventScroll:!1}){const{activeCell:n}=e,{waitUntilReady:o,preventScroll:i}=t;n&&(o&&await n.ready,e.isDisposed||n.isDisposed||n.node.focus({preventScroll:i}))}e.splitCell=function(e){if(!e.model||!e.activeCell)return;const t=v.getState(e);e.mode="edit",e.deselectAll();const n=e.model,o=e.activeCellIndex,i=e.widgets[o],s=i.editor;if(!s)return;const l=s.getSelections(),a=i.model.sharedModel.getSource(),d=[0];let r=-1,c=-1;for(let e=0;e<l.length;e++)r=s.getOffsetAt(l[e].start),c=s.getOffsetAt(l[e].end),r<c?(d.push(r),d.push(c)):c<r?(d.push(c),d.push(r)):d.push(r);d.push(a.length);const h=d.length-1,u=d.slice(0,-1).map(((e,t)=>{const{cell_type:n,metadata:o,outputs:s}=i.model.sharedModel.toJSON();return{cell_type:n,metadata:o,source:a.slice(e,d[t+1]).replace(/^\n+/,"").replace(/\n+$/,""),outputs:t===h-1&&"code"===n?s:void 0}}));n.sharedModel.transact((()=>{n.sharedModel.deleteCell(o),n.sharedModel.insertCells(o,u)}));const g=r!==c?2:1;e.activeCellIndex=o+u.length-g,e.scrollToItem(e.activeCellIndex).then((()=>{var t;null===(t=e.activeCell)||void 0===t||t.editor.focus()})).catch((e=>{})),v.handleState(e,t)},e.mergeCells=function(e,t=!1){if(!e.model||!e.activeCell)return;const n=v.getState(e),o=[],s=[],l=e.model,a=l.cells,d=e.activeCell,r=e.activeCellIndex,c={};if(e.widgets.forEach(((t,n)=>{if(e.isSelectedOrActive(t)){o.push(t.model.sharedModel.getSource()),n!==r&&s.push(n);const e=t.model;if((0,i.isRawCellModel)(e)||(0,i.isMarkdownCellModel)(e))for(const t of e.attachments.keys)c[t]=e.attachments.get(t).toJSON()}})),1===o.length)if(!0===t){if(0===r)return;const e=a.get(r-1);o.unshift(e.sharedModel.getSource()),s.push(r-1)}else if(!1===t){if(r===a.length-1)return;const e=a.get(r+1);o.push(e.sharedModel.getSource()),s.push(r+1)}e.deselectAll();const h=d.model.sharedModel,{cell_type:u,metadata:g}=h.toJSON();"code"===h.cell_type&&(g.trusted=!0);const C={cell_type:u,metadata:g,source:o.join("\n\n"),attachments:"markdown"===h.cell_type||"raw"===h.cell_type?c:void 0};l.sharedModel.transact((()=>{l.sharedModel.deleteCell(r),l.sharedModel.insertCell(r,C),s.sort(((e,t)=>t-e)).forEach((e=>{l.sharedModel.deleteCell(e)}))})),d instanceof i.MarkdownCell&&(e.activeCell.rendered=!1),v.handleState(e,n)},e.deleteCells=function(e){if(!e.model||!e.activeCell)return;const t=v.getState(e);v.deleteCells(e),v.handleState(e,t,!0)},e.insertAbove=function(e){if(!e.model)return;const t=v.getState(e),n=e.model,o=e.activeCell?e.activeCellIndex:0;n.sharedModel.insertCell(o,{cell_type:e.notebookConfig.defaultCell,metadata:"code"===e.notebookConfig.defaultCell?{trusted:!0}:{}}),e.activeCellIndex=o,e.deselectAll(),v.handleState(e,t,!0)},e.insertBelow=function(e){if(!e.model)return;const t=v.getState(e),n=e.model,o=e.activeCell?e.activeCellIndex+1:0;n.sharedModel.insertCell(o,{cell_type:e.notebookConfig.defaultCell,metadata:"code"===e.notebookConfig.defaultCell?{trusted:!0}:{}}),e.activeCellIndex=o,e.deselectAll(),v.handleState(e,t,!0)},e.moveDown=function(e){t(e,1)},e.moveUp=function(e){t(e,-1)},e.changeCellType=function(e,t){if(!e.model||!e.activeCell)return;const n=v.getState(e);v.changeCellType(e,t),v.handleState(e,n)},e.run=function(e,t,n,o){if(!e.model||!e.activeCell)return Promise.resolve(!1);const i=v.getState(e),s=v.runSelected(e,t,n,o);return v.handleRunState(e,i),s},e.runCells=function(e,t,n,o,i){if(!e.model)return Promise.resolve(!1);const s=v.getState(e),l=v.runCells(e,t,n,o,i);return v.handleRunState(e,s),l},e.runAndAdvance=async function(e,t,n,o){var i;if(!e.model||!e.activeCell)return Promise.resolve(!1);const l=v.getState(e),a=v.runSelected(e,t,n,o),d=e.model;return e.activeCellIndex===e.widgets.length-1?(d.sharedModel.insertCell(e.widgets.length,{cell_type:e.notebookConfig.defaultCell,metadata:"code"===e.notebookConfig.defaultCell?{trusted:!0}:{}}),e.activeCellIndex++,!1===(null===(i=e.activeCell)||void 0===i?void 0:i.inViewport)&&await(0,s.signalToPromise)(e.activeCell.inViewportChanged,200).catch((()=>{})),e.mode="edit"):e.activeCellIndex++,v.handleRunState(e,l,"center"),a},e.runAndInsert=async function(e,t,n,o){var i;if(!e.model||!e.activeCell)return Promise.resolve(!1);const l=v.getState(e),a=v.runSelected(e,t,n,o);return e.model.sharedModel.insertCell(e.activeCellIndex+1,{cell_type:e.notebookConfig.defaultCell,metadata:"code"===e.notebookConfig.defaultCell?{trusted:!0}:{}}),e.activeCellIndex++,!1===(null===(i=e.activeCell)||void 0===i?void 0:i.inViewport)&&await(0,s.signalToPromise)(e.activeCell.inViewportChanged,200).catch((()=>{})),e.mode="edit",v.handleRunState(e,l,"center"),a},e.runAll=function(e,t,n,o){if(!e.model||!e.activeCell)return Promise.resolve(!1);const i=v.getState(e),s=e.widgets.length,l=v.runCells(e,e.widgets,t,n,o);return e.activeCellIndex=s,e.deselectAll(),v.handleRunState(e,i),l},e.renderAllMarkdown=function(e){if(!e.model||!e.activeCell)return Promise.resolve(!1);const t=e.activeCellIndex,n=v.getState(e);if(e.widgets.forEach(((t,n)=>{"markdown"===t.model.type&&(e.select(t),e.activeCellIndex=n)})),"markdown"!==e.activeCell.model.type)return Promise.resolve(!0);const o=v.runSelected(e);return e.activeCellIndex=t,v.handleRunState(e,n),o},e.runAllAbove=function(e,t,n,o){const{activeCell:i,activeCellIndex:s,model:l}=e;if(!l||!i||s<1)return Promise.resolve(!1);const a=v.getState(e),d=v.runCells(e,e.widgets.slice(0,e.activeCellIndex),t,n,o);return e.deselectAll(),v.handleRunState(e,a),d},e.runAllBelow=function(e,t,n,o){if(!e.model||!e.activeCell)return Promise.resolve(!1);const i=v.getState(e),s=e.widgets.length,l=v.runCells(e,e.widgets.slice(e.activeCellIndex),t,n,o);return e.activeCellIndex=s,e.deselectAll(),v.handleRunState(e,i),l},e.replaceSelection=function(e,t){var n,o,i;e.model&&(null===(n=e.activeCell)||void 0===n?void 0:n.editor)&&(null===(i=(o=e.activeCell.editor).replaceSelection)||void 0===i||i.call(o,t))},e.selectAbove=function(e){if(!e.model||!e.activeCell)return;const t=e.layout.footer;if(t&&document.activeElement===t.node)return t.node.blur(),void(e.mode="command");if(0===e.activeCellIndex)return;let n=e.activeCellIndex-1;for(;n>=0;){const t=e.widgets[n];if(!t.inputHidden&&!t.isHidden)break;n-=1}const o=v.getState(e);e.activeCellIndex=n,e.deselectAll(),v.handleState(e,o,!0)},e.selectBelow=function(e){if(!e.model||!e.activeCell)return;let t=e.widgets.length-1;for(;e.widgets[t].isHidden||e.widgets[t].inputHidden;)t-=1;if(e.activeCellIndex===t){const t=e.layout.footer;return void(null==t||t.node.focus())}let n=e.activeCellIndex+1;for(;n<t;){let t=e.widgets[n];if(!t.inputHidden&&!t.isHidden)break;n+=1}const o=v.getState(e);e.activeCellIndex=n,e.deselectAll(),v.handleState(e,o,!0)},e.insertSameLevelHeadingAbove=async function(e){if(!e.model||!e.activeCell)return;let t=v.Headings.determineHeadingLevel(e.activeCell,e);-1==t?await v.Headings.insertHeadingAboveCellIndex(0,1,e):await v.Headings.insertHeadingAboveCellIndex(e.activeCellIndex,t,e)},e.insertSameLevelHeadingBelow=async function(e){if(!e.model||!e.activeCell)return;let t=v.Headings.determineHeadingLevel(e.activeCell,e);t=t>-1?t:1;let n=v.Headings.findLowerEqualLevelHeadingBelow(e.activeCell,e,!0);await v.Headings.insertHeadingAboveCellIndex(-1==n?e.model.cells.length:n,t,e)},e.selectHeadingAboveOrCollapseHeading=function(e){if(!e.model||!e.activeCell)return;const t=v.getState(e);let n=h(e.activeCell);if(n.isHeading&&!n.collapsed)r(e.activeCell,!0,e);else{let t=v.Headings.findLowerEqualLevelParentHeadingAbove(e.activeCell,e,!0);t>-1&&(e.activeCellIndex=t)}e.deselectAll(),v.handleState(e,t,!0)},e.selectHeadingBelowOrExpandHeading=function(e){if(!e.model||!e.activeCell)return;const t=v.getState(e);let n=h(e.activeCell);if(n.isHeading&&n.collapsed)r(e.activeCell,!1,e);else{let t=v.Headings.findHeadingBelow(e.activeCell,e,!0);t>-1&&(e.activeCellIndex=t)}e.deselectAll(),v.handleState(e,t,!0)},e.extendSelectionAbove=function(e,t=!1){if(!e.model||!e.activeCell)return;if(0===e.activeCellIndex)return;const n=v.getState(e);e.mode="command",t?e.extendContiguousSelectionTo(0):e.extendContiguousSelectionTo(e.activeCellIndex-1),v.handleState(e,n,!0)},e.extendSelectionBelow=function(e,t=!1){if(!e.model||!e.activeCell)return;if(e.activeCellIndex===e.widgets.length-1)return;const n=v.getState(e);e.mode="command",t?e.extendContiguousSelectionTo(e.widgets.length-1):e.extendContiguousSelectionTo(e.activeCellIndex+1),v.handleState(e,n,!0)},e.selectAll=function(e){e.model&&e.activeCell&&e.widgets.forEach((t=>{e.select(t)}))},e.deselectAll=function(e){e.model&&e.activeCell&&e.deselectAll()},e.copy=function(e){v.copyOrCut(e,!1)},e.cut=function(e){v.copyOrCut(e,!0)},e.paste=function(e,t="below"){const i=o.Clipboard.getInstance();i.hasData(u)&&(n(e,t,i.getData(u),!0),g(e))},e.duplicate=function(e,t="below"){const o=v.selectedCells(e);o&&0!==o.length&&n(e,t,o,!1)},e.undo=function(e){if(!e.model)return;const t=v.getState(e);e.mode="command",e.model.sharedModel.undo(),e.deselectAll(),v.handleState(e,t)},e.redo=function(e){if(!e.model||!e.activeCell)return;const t=v.getState(e);e.mode="command",e.model.sharedModel.redo(),e.deselectAll(),v.handleState(e,t)},e.toggleAllLineNumbers=function(e){if(!e.model||!e.activeCell)return;const t=v.getState(e),n=e.editorConfig,o=!(n.code.lineNumbers&&n.markdown.lineNumbers&&n.raw.lineNumbers),i={code:{...n.code,lineNumbers:o},markdown:{...n.markdown,lineNumbers:o},raw:{...n.raw,lineNumbers:o}};e.editorConfig=i,v.handleState(e,t)},e.clearOutputs=function(e){if(!e.model||!e.activeCell)return;const t=v.getState(e);let n=-1;for(const t of e.model.cells){const o=e.widgets[++n];e.isSelectedOrActive(o)&&"code"===t.type&&(t.sharedModel.transact((()=>{t.clearExecution(),o.outputHidden=!1}),!1),v.outputCleared.emit({notebook:e,cell:o}))}v.handleState(e,t,!0)},e.clearAllOutputs=function(e){if(!e.model||!e.activeCell)return;const t=v.getState(e);let n=-1;for(const t of e.model.cells){const o=e.widgets[++n];"code"===t.type&&(t.sharedModel.transact((()=>{t.clearExecution(),o.outputHidden=!1}),!1),v.outputCleared.emit({notebook:e,cell:o}))}v.handleState(e,t,!0)},e.hideCode=function(e){if(!e.model||!e.activeCell)return;const t=v.getState(e);e.widgets.forEach((t=>{e.isSelectedOrActive(t)&&"code"===t.model.type&&(t.inputHidden=!0)})),v.handleState(e,t)},e.showCode=function(e){if(!e.model||!e.activeCell)return;const t=v.getState(e);e.widgets.forEach((t=>{e.isSelectedOrActive(t)&&"code"===t.model.type&&(t.inputHidden=!1)})),v.handleState(e,t)},e.hideAllCode=function(e){if(!e.model||!e.activeCell)return;const t=v.getState(e);e.widgets.forEach((e=>{"code"===e.model.type&&(e.inputHidden=!0)})),v.handleState(e,t)},e.showAllCode=function(e){if(!e.model||!e.activeCell)return;const t=v.getState(e);e.widgets.forEach((e=>{"code"===e.model.type&&(e.inputHidden=!1)})),v.handleState(e,t)},e.hideOutput=function(e){if(!e.model||!e.activeCell)return;const t=v.getState(e);e.widgets.forEach((t=>{e.isSelectedOrActive(t)&&"code"===t.model.type&&(t.outputHidden=!0)})),v.handleState(e,t,!0)},e.showOutput=function(e){if(!e.model||!e.activeCell)return;const t=v.getState(e);e.widgets.forEach((t=>{e.isSelectedOrActive(t)&&"code"===t.model.type&&(t.outputHidden=!1)})),v.handleState(e,t)},e.hideAllOutputs=function(e){if(!e.model||!e.activeCell)return;const t=v.getState(e);e.widgets.forEach((e=>{"code"===e.model.type&&(e.outputHidden=!0)})),v.handleState(e,t,!0)},e.renderSideBySide=function(e){e.renderingLayout="side-by-side"},e.renderDefault=function(e){e.renderingLayout="default"},e.showAllOutputs=function(e){if(!e.model||!e.activeCell)return;const t=v.getState(e);e.widgets.forEach((e=>{"code"===e.model.type&&(e.outputHidden=!1)})),v.handleState(e,t)},e.enableOutputScrolling=function(e){if(!e.model||!e.activeCell)return;const t=v.getState(e);e.widgets.forEach((t=>{e.isSelectedOrActive(t)&&"code"===t.model.type&&(t.outputsScrolled=!0)})),v.handleState(e,t,!0)},e.disableOutputScrolling=function(e){if(!e.model||!e.activeCell)return;const t=v.getState(e);e.widgets.forEach((t=>{e.isSelectedOrActive(t)&&"code"===t.model.type&&(t.outputsScrolled=!1)})),v.handleState(e,t)},e.selectLastRunCell=function(e){let t=null,n=null;e.widgets.forEach(((e,o)=>{if("code"===e.model.type){const i=e.model.getMetadata("execution");if(i&&d.JSONExt.isObject(i)&&void 0!==i["iopub.status.busy"]){const e=i["iopub.status.busy"].toString();if(e){const i=new Date(e);(!t||i>=t)&&(t=i,n=o)}}}})),null!==n&&(e.activeCellIndex=n)},e.setMarkdownHeader=function(e,t){if(!e.model||!e.activeCell)return;const n=v.getState(e),o=e.model.cells;t=Math.min(Math.max(t,1),6),e.widgets.forEach(((n,i)=>{e.isSelectedOrActive(n)&&v.setMarkdownHeader(o.get(i),t)})),v.changeCellType(e,"markdown"),v.handleState(e,n)},e.collapseAllHeadings=function(t){const n=v.getState(t);for(const n of t.widgets)e.getHeadingInfo(n).isHeading&&(e.setHeadingCollapse(n,!0,t),e.setCellCollapse(n,!0));t.activeCellIndex=0,v.handleState(t,n,!0)},e.expandAllHeadings=function(t){for(const n of t.widgets)e.getHeadingInfo(n).isHeading&&(e.setHeadingCollapse(n,!1,t),e.setCellCollapse(n,!1))},e.expandParent=function e(t,n){let o=function(e,t){const n=(0,a.findIndex)(t.widgets,((t,n)=>e.model.id===t.model.id));if(-1===n)return;if(n>=t.widgets.length)return;let o=h(t.widgets[n]);for(let e=n-1;e>=0;e--)if(e<t.widgets.length){let n=h(t.widgets[e]);if(n.isHeading&&n.headingLevel<o.headingLevel)return t.widgets[e]}}(t,n);o&&(h(o).collapsed||o.isHidden)&&(o.isHidden&&e(o,n),h(o).collapsed&&r(o,!1,n))},e.findNextParentHeading=function(e,t){let n=(0,a.findIndex)(t.widgets,((t,n)=>e.model.id===t.model.id));if(-1===n)return-1;let o=h(e);for(n+=1;n<t.widgets.length;n++){let e=h(t.widgets[n]);if(e.isHeading&&e.headingLevel<=o.headingLevel)return n}return t.widgets.length},e.setHeadingCollapse=r,e.toggleCurrentHeadingCollapse=function(t){if(!t.activeCell||void 0===t.activeCellIndex)return;let n=e.getHeadingInfo(t.activeCell);n.isHeading&&e.setHeadingCollapse(t.activeCell,!n.collapsed,t),t.scrollToItem(t.activeCellIndex).catch((e=>{}))},e.setCellCollapse=function(e,t){e instanceof i.MarkdownCell?e.headingCollapsed=t:e.setHidden(t)},e.getHeadingInfo=h,e.trust=function(e,t){const n=(t=t||l.nullTranslator).load("jupyterlab");if(!e.model)return Promise.resolve();const i=(0,a.every)(e.model.cells,(e=>e.trusted)),s=c.createElement("p",null,n.__("A trusted Jupyter notebook may execute hidden malicious code when you open it."),c.createElement("br",null),n.__('Selecting "Trust" will re-render this notebook in a trusted state.'),c.createElement("br",null),n.__("For more information, see")," ",c.createElement("a",{href:"https://jupyter-server.readthedocs.io/en/stable/operators/security.html",target:"_blank",rel:"noopener noreferrer"},n.__("the Jupyter security documentation")),".");return i?(0,o.showDialog)({body:n.__("Notebook is already trusted"),buttons:[o.Dialog.okButton()]}).then((()=>{})):(0,o.showDialog)({body:s,title:n.__("Trust this notebook?"),buttons:[o.Dialog.cancelButton(),o.Dialog.warnButton({label:n.__("Trust"),ariaLabel:n.__("Confirm Trusting this notebook")})]}).then((t=>{if(t.button.accept&&e.model)for(const t of e.model.cells)t.trusted=!0}))},e.focusActiveCell=g,e.accessPreviousHistory=async function(e){if(!e.notebookConfig.accessKernelHistory)return;const t=e.activeCell;if(t&&e.kernelHistory){const n=await e.kernelHistory.back(t);e.kernelHistory.updateEditor(t,n)}},e.accessNextHistory=async function(e){if(!e.notebookConfig.accessKernelHistory)return;const t=e.activeCell;if(t&&e.kernelHistory){const n=await e.kernelHistory.forward(t);e.kernelHistory.updateEditor(t,n)}}}(C||(C={})),function(e){function t(e){var t,n;return{wasFocused:e.node.contains(document.activeElement),activeCellId:null!==(n=null===(t=e.activeCell)||void 0===t?void 0:t.model.id)&&void 0!==n?n:null}}async function n(e,t,n=!1){const{activeCell:o,activeCellIndex:i}=e;n&&o&&await e.scrollToItem(i,"auto",0).catch((e=>{})),(t.wasFocused||"edit"===e.mode)&&e.activate()}function d(t,n,s,d,r){const c=n[-1];t.mode="command";let h=!1;return Promise.all(n.map((n=>{if("code"===n.model.type&&t.notebookConfig.enableKernelInitNotification&&s&&"initializing"===s.kernelDisplayStatus&&!h){h=!0;const e=(r=r||l.nullTranslator).load("jupyterlab");return o.Notification.emit(e.__(`Kernel '${s.kernelDisplayName}' for '${s.path}' is still initializing. You can run code cells when the kernel has initialized.`),"warning",{autoClose:!1}),Promise.resolve(!1)}return"code"===n.model.type&&t.notebookConfig.enableKernelInitNotification&&h?Promise.resolve(!1):async function(t,n,s,d,r){var c,h,u;const C=(r=r||l.nullTranslator).load("jupyterlab");switch(n.model.type){case"markdown":n.rendered=!0,n.inputHidden=!1,e.executed.emit({notebook:t,cell:n,success:!0});break;case"code":if(s){if(s.isTerminating){await(0,o.showDialog)({title:C.__("Kernel Terminating"),body:C.__("The kernel for %1 appears to be terminating. You can not run any cell for now.",null===(c=s.session)||void 0===c?void 0:c.path),buttons:[o.Dialog.okButton()]});break}if(s.pendingInput)return await(0,o.showDialog)({title:C.__("Cell not executed due to pending input"),body:C.__("The cell has not been executed to avoid kernel deadlock as there is another pending input! Submit your pending input and try again."),buttons:[o.Dialog.okButton()]}),!1;if(s.hasNoKernel&&await s.startKernel()&&d&&await d.selectKernel(s),s.hasNoKernel)return n.model.sharedModel.transact((()=>{n.model.clearExecution()})),!0;const l=null!==(u=null===(h=t.model)||void 0===h?void 0:h.deletedCells)&&void 0!==u?u:[];e.executionScheduled.emit({notebook:t,cell:n});let r=!1;try{const e=await i.CodeCell.execute(n,s,{deletedCells:l,recordTiming:t.notebookConfig.recordTiming});l.splice(0,l.length),r=(()=>{if(n.isDisposed)return!1;if(!e)return!0;if("ok"===e.content.status){const o=e.content;return o.payload&&o.payload.length&&function(e,t,n){var o;const i=null===(o=e.payload)||void 0===o?void 0:o.filter((e=>"set_next_input"===e.source))[0];if(!i)return;const s=i.text;if(i.replace)return void n.model.sharedModel.setSource(s);const l=t.model.sharedModel,d=t.model.cells,r=(0,a.findIndex)(d,(e=>e===n.model));-1===r?l.insertCell(l.cells.length,{cell_type:"code",source:s,metadata:{trusted:!1}}):l.insertCell(r+1,{cell_type:"code",source:s,metadata:{trusted:!1}})}(o,t,n),!0}throw new g(e.content)})()}catch(o){if(!n.isDisposed&&!o.message.startsWith("Canceled"))throw e.executed.emit({notebook:t,cell:n,success:!1,error:o}),o;r=!1}return r&&e.executed.emit({notebook:t,cell:n,success:!0}),r}n.model.sharedModel.transact((()=>{n.model.clearExecution()}),!1)}return Promise.resolve(!0)}(t,n,s,d,r)}))).then((n=>!t.isDisposed&&(e.selectionExecuted.emit({notebook:t,lastCell:c}),t.update(),n.every((e=>e))))).catch((o=>{if(!o.message.startsWith("KernelReplyNotOK"))throw o;return n.map((e=>{"code"===e.model.type&&null==e.model.executionCount&&e.setPrompt("")})),e.selectionExecuted.emit({notebook:t,lastCell:c}),t.update(),!1}))}function c(e){const t=e.model.sharedModel,n=[];e.mode="command",e.widgets.forEach(((t,o)=>{var i;const s=!1!==t.model.getMetadata("deletable");e.isSelectedOrActive(t)&&s&&(n.push(o),null===(i=e.model)||void 0===i||i.deletedCells.push(t.model.id))})),n.length>0&&(t.transact((()=>{n.reverse().forEach((e=>{t.deleteCell(e)})),t.cells.length==n.length&&t.insertCell(0,{cell_type:e.notebookConfig.defaultCell,metadata:"code"===e.notebookConfig.defaultCell?{trusted:!0}:{}})})),e.activeCellIndex=n[0]-n.length+1),e.deselectAll()}let h;e.executed=new r.Signal({}),e.executionScheduled=new r.Signal({}),e.selectionExecuted=new r.Signal({}),e.outputCleared=new r.Signal({}),e.getState=t,e.handleState=n,e.handleRunState=async function(e,t,n){const{activeCell:o,activeCellIndex:i}=e;o&&await e.scrollToItem(i,"smart",0,n).catch((e=>{})),(t.wasFocused||"edit"===e.mode)&&e.activate()},e.runCells=d,e.runSelected=function(e,t,n,o){e.mode="command";let i=e.activeCellIndex;const s=e.widgets.filter(((t,n)=>{const o=e.isSelectedOrActive(t);return o&&(i=n),o}));return e.activeCellIndex=i,e.deselectAll(),d(e,s,t,n,o)},e.selectedCells=function(e){return e.widgets.filter((t=>e.isSelectedOrActive(t))).map((e=>e.model.toJSON())).map((e=>(void 0!==e.metadata.deletable&&delete e.metadata.deletable,e)))},e.copyOrCut=function(i,s){if(!i.model||!i.activeCell)return;const l=t(i),a=o.Clipboard.getInstance();i.mode="command",a.clear();const d=e.selectedCells(i);a.setData(u,d),s?c(i):i.deselectAll(),i.lastClipboardInteraction=s?"cut":"copy",n(i,l)},e.changeCellType=function(e,t){const n=e.model.sharedModel;e.widgets.forEach(((o,i)=>{if(e.isSelectedOrActive(o)){if(o.model.type!==t){const e=o.model.toJSON();n.transact((()=>{n.deleteCell(i),e.metadata.trusted="code"===t||void 0;const o=n.insertCell(i,{cell_type:t,source:e.source,metadata:e.metadata});e.attachments&&["markdown","raw"].includes(t)&&(o.attachments=e.attachments)}))}"markdown"===t&&((o=e.widgets[i]).rendered=!1)}})),e.deselectAll()},e.deleteCells=c,e.setMarkdownHeader=function(e,t){let n=e.sharedModel.getSource();const o=Array(t+1).join("#")+" ",i=/^(#+\s*)|^(\s*)/.exec(n);i&&(n=n.slice(i[0].length)),e.sharedModel.setSource(o+n)},function(t){function n(e,t,n=!1,o=!1){let i=t.widgets.indexOf(e)-(n?1:0);for(;i>=0;){if(C.getHeadingInfo(t.widgets[i]).isHeading)return o?i:t.widgets[i];i--}return o?-1:null}t.findParentHeading=n,t.findLowerEqualLevelParentHeadingAbove=function(t,n,o=!1){let i=e.Headings.determineHeadingLevel(t,n);-1==i&&(i=1);let s=n.widgets.indexOf(t)-1;for(;s>=0;){let e=n.widgets[s],t=C.getHeadingInfo(e);if(t.isHeading&&t.headingLevel<=i)return o?s:e;s--}return o?-1:null},t.findLowerEqualLevelHeadingBelow=function(t,n,o=!1){let i=e.Headings.determineHeadingLevel(t,n);-1==i&&(i=1);let s=n.widgets.indexOf(t)+1;for(;s<n.widgets.length;){let e=n.widgets[s],t=C.getHeadingInfo(e);if(t.isHeading&&t.headingLevel<=i)return o?s:e;s++}return o?-1:null},t.findHeadingBelow=function(e,t,n=!1){let o=t.widgets.indexOf(e)+1;for(;o<t.widgets.length;){let e=t.widgets[o];if(C.getHeadingInfo(e).isHeading)return n?o:e;o++}return n?-1:null},t.determineHeadingLevel=function(e,t){let o=C.getHeadingInfo(e);if(o.isHeading)return o.headingLevel;{let o=n(e,t,!0);return null==o?-1:C.getHeadingInfo(o).headingLevel}},t.insertHeadingAboveCellIndex=async function(t,n,o){var i;n=Math.min(Math.max(n,1),6);const l=e.getState(o);o.model.sharedModel.insertCell(t,{cell_type:"markdown",source:"#".repeat(n)+" "}),o.activeCellIndex=t,!1===(null===(i=o.activeCell)||void 0===i?void 0:i.inViewport)&&await(0,s.signalToPromise)(o.activeCell.inViewportChanged,200).catch((()=>{})),o.deselectAll(),e.handleState(o,l,!0),o.mode="edit",o.widgets[t].setHidden(!1)}}(h=e.Headings||(e.Headings={}))}(v||(v={}));class m{constructor(e){this.model=e,this._cellMap=new WeakMap,this._changed=new r.Signal(this),this._isDisposed=!1,this._insertCells(0,this.model.cells),this.model.changed.connect(this._onSharedModelChanged,this)}get changed(){return this._changed}get isDisposed(){return this._isDisposed}get length(){return this.model.cells.length}*[Symbol.iterator](){for(const e of this.model.cells)yield this._cellMap.get(e)}dispose(){var e;if(!this._isDisposed){this._isDisposed=!0;for(const t of this.model.cells)null===(e=this._cellMap.get(t))||void 0===e||e.dispose();r.Signal.clearData(this)}}get(e){return this._cellMap.get(this.model.cells[e])}_insertCells(e,t){return t.forEach((e=>{let t;switch(e.cell_type){case"code":t=new i.CodeCellModel({sharedModel:e});break;case"markdown":t=new i.MarkdownCellModel({sharedModel:e});break;default:t=new i.RawCellModel({sharedModel:e})}this._cellMap.set(e,t),e.disposed.connect((()=>{t.dispose(),this._cellMap.delete(e)}))})),this.length}_onSharedModelChanged(e,t){var n;let o=0;const i=new Array;null===(n=t.cellsChange)||void 0===n||n.forEach((e=>{null!=e.insert?(this._insertCells(o,e.insert),i.push({type:"add",newIndex:o,newValues:e.insert.map((e=>this._cellMap.get(e))),oldIndex:-2,oldValues:[]}),o+=e.insert.length):null!=e.delete?i.push({type:"remove",newIndex:-1,newValues:[],oldIndex:o,oldValues:new Array(e.delete).fill(void 0)}):null!=e.retain&&(o+=e.retain)})),i.forEach((e=>this._changed.emit(e)))}}var p,_=n(68239);!function(e){function t(e,t){const n=(t||l.nullTranslator).load("jupyterlab");function i(){if(e.context.model.readOnly)return(0,o.showDialog)({title:n.__("Cannot Save"),body:n.__("Document is read-only"),buttons:[o.Dialog.okButton()]});e.context.save().then((()=>{if(!e.isDisposed)return e.context.createCheckpoint()}))}return(0,_.addToolbarButtonClass)(_.ReactWidget.create(c.createElement(_.UseSignal,{signal:e.context.fileChanged},(()=>c.createElement(_.ToolbarButtonComponent,{icon:_.saveIcon,onClick:i,tooltip:n.__("Save the notebook contents and create checkpoint"),enabled:!!(e&&e.context&&e.context.contentsModel&&e.context.contentsModel.writable)})))))}function n(e,t){const n=(t||l.nullTranslator).load("jupyterlab");return new _.ToolbarButton({icon:_.addIcon,onClick:()=>{C.insertBelow(e.content)},tooltip:n.__("Insert a cell below")})}function i(e,t){const n=(t||l.nullTranslator).load("jupyterlab");return new _.ToolbarButton({icon:_.cutIcon,onClick:()=>{C.cut(e.content)},tooltip:n.__("Cut the selected cells")})}function s(e,t){const n=(t||l.nullTranslator).load("jupyterlab");return new _.ToolbarButton({icon:_.copyIcon,onClick:()=>{C.copy(e.content)},tooltip:n.__("Copy the selected cells")})}function a(e,t){const n=(t||l.nullTranslator).load("jupyterlab");return new _.ToolbarButton({icon:_.pasteIcon,onClick:()=>{C.paste(e.content)},tooltip:n.__("Paste cells from the clipboard")})}function d(e,t,n){const o=(null!=n?n:l.nullTranslator).load("jupyterlab");return new _.ToolbarButton({icon:_.runIcon,onClick:()=>{C.runAndAdvance(e.content,e.sessionContext,t,n)},tooltip:o.__("Run the selected cells and advance")})}function r(e,t,n){const i=(null!=n?n:l.nullTranslator).load("jupyterlab");return new _.ToolbarButton({icon:_.fastForwardIcon,onClick:()=>{const i=null!=t?t:new o.SessionContextDialogs({translator:n});i.restart(e.sessionContext).then((t=>(t&&C.runAll(e.content,e.sessionContext,i,n),t)))},tooltip:i.__("Restart the kernel, then re-run the whole notebook")})}function h(e,t){return new f(e.content,t)}e.createSaveButton=t,e.createInsertButton=n,e.createCutButton=i,e.createCopyButton=s,e.createPasteButton=a,e.createRunButton=d,e.createRestartRunAllButton=r,e.createCellTypeItem=h,e.getDefaultItems=function(e,l,c){return[{name:"save",widget:t(e,c)},{name:"insert",widget:n(e,c)},{name:"cut",widget:i(e,c)},{name:"copy",widget:s(e,c)},{name:"paste",widget:a(e,c)},{name:"run",widget:d(e,l,c)},{name:"interrupt",widget:o.Toolbar.createInterruptButton(e.sessionContext,c)},{name:"restart",widget:o.Toolbar.createRestartButton(e.sessionContext,l,c)},{name:"restart-and-run",widget:r(e,l,c)},{name:"cellType",widget:h(e,c)},{name:"spacer",widget:_.Toolbar.createSpacerItem()},{name:"kernelName",widget:o.Toolbar.createKernelNameItem(e.sessionContext,l,c)}]}}(p||(p={}));class f extends _.ReactWidget{constructor(e,t){super(),this.handleChange=e=>{"-"!==e.target.value&&(C.changeCellType(this._notebook,e.target.value),this._notebook.activate())},this.handleKeyDown=e=>{13===e.keyCode&&this._notebook.activate()},this._trans=(t||l.nullTranslator).load("jupyterlab"),this.addClass("jp-Notebook-toolbarCellType"),this._notebook=e,e.model&&this.update(),e.activeCellChanged.connect(this.update,this),e.selectionChanged.connect(this.update,this)}render(){let e="-";this._notebook.activeCell&&(e=this._notebook.activeCell.model.type);for(const t of this._notebook.widgets)if(this._notebook.isSelectedOrActive(t)&&t.model.type!==e){e="-";break}return c.createElement(_.HTMLSelect,{className:"jp-Notebook-toolbarCellTypeDropdown",onChange:this.handleChange,onKeyDown:this.handleKeyDown,value:e,"aria-label":this._trans.__("Cell type"),title:this._trans.__("Select the cell type")},c.createElement("option",{value:"-"},"-"),c.createElement("option",{value:"code"},this._trans.__("Code")),c.createElement("option",{value:"markdown"},this._trans.__("Markdown")),c.createElement("option",{value:"raw"},this._trans.__("Raw")))}}var w=n(34276),y=n(16954);function b(e){const t=e.translator||l.nullTranslator,n=(0,o.translateKernelStatuses)(t),i=t.load("jupyterlab"),s=e.state,a=e.displayOption.showOnToolBar,d=e.displayOption.showProgress,r=a?"down":"up",c=h().createElement("div",null);if(!s)return c;const u=s.kernelStatus,g={alignSelf:"normal",height:"24px"},C=s.totalTime,v=s.scheduledCellNumber||0,m=v-(s.scheduledCell.size||0);let p=100*m/v,f=d?"":"hidden";!d&&p<100&&(p=0);const y=e=>h().createElement(w.ProgressCircle,{progress:e,width:16,height:24,label:i.__("Kernel status")}),b=e=>i.__("Kernel status: %1",e),k=(e,t,o)=>h().createElement("div",{className:"jp-Notebook-ExecutionIndicator",title:d?"":b(n[e]),"data-status":e},t,h().createElement("div",{className:`jp-Notebook-ExecutionIndicator-tooltip ${r} ${f}`},h().createElement("span",null," ",b(n[e])," "),o));if("connecting"===s.kernelStatus||"disconnected"===s.kernelStatus||"unknown"===s.kernelStatus)return k(u,h().createElement(_.offlineBoltIcon.react,{...g}),[]);if("starting"===s.kernelStatus||"terminating"===s.kernelStatus||"restarting"===s.kernelStatus||"initializing"===s.kernelStatus)return k(u,h().createElement(_.circleIcon.react,{...g}),[]);if("busy"===s.executionStatus)return k("busy",y(p),[h().createElement("span",{key:0},i.__(`Executed ${m}/${v} cells`)),h().createElement("span",{key:1},i._n("Elapsed time: %1 second","Elapsed time: %1 seconds",C))]);{const e="busy"===s.kernelStatus?0:100,t="busy"===s.kernelStatus||0===C?[]:[h().createElement("span",{key:0},i._n("Executed %1 cell","Executed %1 cells",v)),h().createElement("span",{key:1},i._n("Elapsed time: %1 second","Elapsed time: %1 seconds",C))];return k(s.kernelStatus,y(e),t)}}class k extends _.VDomRenderer{constructor(e,t=!0){super(new k.Model),this.translator=e||l.nullTranslator,this.addClass("jp-mod-highlighted")}render(){if(null!==this.model&&this.model.renderFlag){const e=this.model.currentNotebook;return e?h().createElement(b,{displayOption:this.model.displayOption,state:this.model.executionState(e),translator:this.translator}):h().createElement(b,{displayOption:this.model.displayOption,state:void 0,translator:this.translator})}return h().createElement("div",null)}}!function(e){class t extends _.VDomModel{constructor(){super(),this._notebookExecutionProgress=new WeakMap,this._displayOption={showOnToolBar:!0,showProgress:!0},this._renderFlag=!0}attachNotebook(e){var t,n,o,i;if(e&&e.content&&e.context){const s=e.content,l=e.context;if(this._currentNotebook=s,!this._notebookExecutionProgress.has(s)){this._notebookExecutionProgress.set(s,{executionStatus:"idle",kernelStatus:"idle",totalTime:0,interval:0,timeout:0,scheduledCell:new Set,scheduledCellNumber:0,needReset:!0});const e=this._notebookExecutionProgress.get(s),a=t=>{e&&(e.kernelStatus=t.kernelDisplayStatus),this.stateChanged.emit(void 0)};l.statusChanged.connect(a,this);const d=t=>{e&&(e.kernelStatus=t.kernelDisplayStatus),this.stateChanged.emit(void 0)};l.connectionStatusChanged.connect(d,this),l.disposed.connect((e=>{e.connectionStatusChanged.disconnect(d,this),e.statusChanged.disconnect(a,this)}));const r=(e,t)=>{const n=t.msg,o=n.header.msg_id;if("execute_request"===n.header.msg_type)this._cellScheduledCallback(s,o);else if(y.KernelMessage.isStatusMsg(n)&&"idle"===n.content.execution_state){const e=n.parent_header.msg_id;this._cellExecutedCallback(s,e)}else y.KernelMessage.isStatusMsg(n)&&"restarting"===n.content.execution_state?this._restartHandler(s):"execute_input"===n.header.msg_type&&this._startTimer(s)};null===(n=null===(t=l.session)||void 0===t?void 0:t.kernel)||void 0===n||n.anyMessage.connect(r),null===(i=null===(o=l.session)||void 0===o?void 0:o.kernel)||void 0===i||i.disposed.connect((e=>e.anyMessage.disconnect(r)));const c=(t,n)=>{e&&(this._resetTime(e),this.stateChanged.emit(void 0),n.newValue&&n.newValue.anyMessage.connect(r))};l.kernelChanged.connect(c),l.disposed.connect((e=>e.kernelChanged.disconnect(c)))}}}get currentNotebook(){return this._currentNotebook}get displayOption(){return this._displayOption}set displayOption(e){this._displayOption=e}executionState(e){return this._notebookExecutionProgress.get(e)}_scheduleSwitchToIdle(e){window.setTimeout((()=>{e.executionStatus="idle",clearInterval(e.interval),this.stateChanged.emit(void 0)}),150),e.timeout=window.setTimeout((()=>{e.needReset=!0}),1e3)}_cellExecutedCallback(e,t){const n=this._notebookExecutionProgress.get(e);n&&n.scheduledCell.has(t)&&(n.scheduledCell.delete(t),0===n.scheduledCell.size&&this._scheduleSwitchToIdle(n))}_restartHandler(e){const t=this._notebookExecutionProgress.get(e);t&&(t.scheduledCell.clear(),this._scheduleSwitchToIdle(t))}_startTimer(e){const t=this._notebookExecutionProgress.get(e);t&&(t.scheduledCell.size>0?"busy"!==t.executionStatus&&(t.executionStatus="busy",clearTimeout(t.timeout),this.stateChanged.emit(void 0),t.interval=window.setInterval((()=>{this._tick(t)}),1e3)):this._resetTime(t))}_cellScheduledCallback(e,t){const n=this._notebookExecutionProgress.get(e);n&&!n.scheduledCell.has(t)&&(n.needReset&&this._resetTime(n),n.scheduledCell.add(t),n.scheduledCellNumber+=1)}_tick(e){e.totalTime+=1,this.stateChanged.emit(void 0)}_resetTime(e){e.totalTime=0,e.scheduledCellNumber=0,e.executionStatus="idle",e.scheduledCell=new Set,clearTimeout(e.timeout),clearInterval(e.interval),e.needReset=!1}get renderFlag(){return this._renderFlag}updateRenderOption(e){this.displayOption.showOnToolBar&&(e.showOnToolBar?this._renderFlag=!0:this._renderFlag=!1),this.displayOption.showProgress=e.showProgress,this.stateChanged.emit(void 0)}}function n(e){let t=!0,n=!0;const o=e.get("kernelStatus").composite;return o&&(t=!o.showOnStatusBar,n=o.showProgress),{showOnToolBar:t,showProgress:n}}e.Model=t,e.createExecutionIndicatorItem=function(t,o,i){const s=new e(o);return s.model.displayOption={showOnToolBar:!0,showProgress:!0},s.model.attachNotebook({content:t.content,context:t.sessionContext}),i&&i.then((e=>{const t=e=>{s.model.updateRenderOption(n(e))};e.changed.connect(t),t(e),s.disposed.connect((()=>{e.changed.disconnect(t)}))})).catch((e=>{console.error(e.message)})),s},e.getSettingValue=n}(k||(k={}));var S=n(85923),x=n(23781);class M{constructor(e={}){var t,n;this.standaloneModel=!1,this._dirty=!1,this._readOnly=!1,this._contentChanged=new r.Signal(this),this._stateChanged=new r.Signal(this),this._isDisposed=!1,this._metadataChanged=new r.Signal(this),this.standaloneModel=void 0===e.sharedModel,e.sharedModel?this.sharedModel=e.sharedModel:this.sharedModel=x.YNotebook.create({disableDocumentWideUndoRedo:null===(t=e.disableDocumentWideUndoRedo)||void 0===t||t,data:{nbformat:S.MAJOR_VERSION,nbformat_minor:S.MINOR_VERSION,metadata:{kernelspec:{name:"",display_name:""},language_info:{name:null!==(n=e.languagePreference)&&void 0!==n?n:""}}}}),this._cells=new m(this.sharedModel),this._trans=(e.translator||l.nullTranslator).load("jupyterlab"),this._deletedCells=[],this._collaborationEnabled=!!(null==e?void 0:e.collaborationEnabled),this._cells.changed.connect(this._onCellsChanged,this),this.sharedModel.changed.connect(this._onStateChanged,this),this.sharedModel.metadataChanged.connect(this._onMetadataChanged,this)}get contentChanged(){return this._contentChanged}get metadataChanged(){return this._metadataChanged}get stateChanged(){return this._stateChanged}get cells(){return this._cells}get dirty(){return this._dirty}set dirty(e){const t=this._dirty;e!==t&&(this._dirty=e,this.triggerStateChange({name:"dirty",oldValue:t,newValue:e}))}get readOnly(){return this._readOnly}set readOnly(e){if(e===this._readOnly)return;const t=this._readOnly;this._readOnly=e,this.triggerStateChange({name:"readOnly",oldValue:t,newValue:e})}get metadata(){return this.sharedModel.metadata}get nbformat(){return this.sharedModel.nbformat}get nbformatMinor(){return this.sharedModel.nbformat_minor}get defaultKernelName(){var e;const t=this.getMetadata("kernelspec");return null!==(e=null==t?void 0:t.name)&&void 0!==e?e:""}get deletedCells(){return this._deletedCells}get defaultKernelLanguage(){var e;const t=this.getMetadata("language_info");return null!==(e=null==t?void 0:t.name)&&void 0!==e?e:""}get collaborative(){return this._collaborationEnabled}dispose(){if(this.isDisposed)return;this._isDisposed=!0;const e=this.cells;this._cells=null,e.dispose(),this.standaloneModel&&this.sharedModel.dispose(),r.Signal.clearData(this)}deleteMetadata(e){return this.sharedModel.deleteMetadata(e)}getMetadata(e){return this.sharedModel.getMetadata(e)}setMetadata(e,t){void 0===t?this.sharedModel.deleteMetadata(e):this.sharedModel.setMetadata(e,t)}toString(){return JSON.stringify(this.toJSON())}fromString(e){this.fromJSON(JSON.parse(e))}toJSON(){return this._ensureMetadata(),this.sharedModel.toJSON()}fromJSON(e){var t,n;const i=d.JSONExt.deepCopy(e),s=e.metadata.orig_nbformat;if(i.nbformat=Math.max(e.nbformat,S.MAJOR_VERSION),(i.nbformat!==e.nbformat||i.nbformat_minor<S.MINOR_VERSION)&&(i.nbformat_minor=S.MINOR_VERSION),void 0!==s&&i.nbformat!==s){let e;e=i.nbformat>s?this._trans.__("This notebook has been converted from an older notebook format (v%1)\nto the current notebook format (v%2).\nThe next time you save this notebook, the current notebook format (v%2) will be used.\n'Older versions of Jupyter may not be able to read the new format.' To preserve the original format version,\nclose the notebook without saving it.",s,i.nbformat):this._trans.__("This notebook has been converted from an newer notebook format (v%1)\nto the current notebook format (v%2).\nThe next time you save this notebook, the current notebook format (v%2) will be used.\nSome features of the original notebook may not be available.' To preserve the original format version,\nclose the notebook without saving it.",s,i.nbformat),(0,o.showDialog)({title:this._trans.__("Notebook converted"),body:e,buttons:[o.Dialog.okButton({label:this._trans.__("Ok")})]})}0===(null!==(n=null===(t=i.cells)||void 0===t?void 0:t.length)&&void 0!==n?n:0)&&(i.cells=[{cell_type:"code",source:"",metadata:{trusted:!0}}]),this.sharedModel.fromJSON(i),this._ensureMetadata(),this.dirty=!0}_onCellsChanged(e,t){switch(t.type){case"add":case"set":t.newValues.forEach((e=>{e.contentChanged.connect(this.triggerContentChange,this)}))}this.triggerContentChange()}_onMetadataChanged(e,t){this._metadataChanged.emit(t),this.triggerContentChange()}_onStateChanged(e,t){t.stateChange&&t.stateChange.forEach((e=>{"dirty"===e.name?this.dirty=e.newValue:e.oldValue!==e.newValue&&this.triggerStateChange({newValue:void 0,oldValue:void 0,...e})}))}_ensureMetadata(e=""){this.getMetadata("language_info")||this.sharedModel.setMetadata("language_info",{name:e}),this.getMetadata("kernelspec")||this.sharedModel.setMetadata("kernelspec",{name:"",display_name:""})}triggerStateChange(e){this._stateChanged.emit(e)}triggerContentChange(){this._contentChanged.emit(void 0),this.dirty=!0}get isDisposed(){return this._isDisposed}}class I{constructor(e={}){var t,n;this._disposed=!1,this._disableDocumentWideUndoRedo=null===(t=e.disableDocumentWideUndoRedo)||void 0===t||t,this._collaborative=null===(n=e.collaborative)||void 0===n||n}get disableDocumentWideUndoRedo(){return this._disableDocumentWideUndoRedo}set disableDocumentWideUndoRedo(e){this._disableDocumentWideUndoRedo=e}get name(){return"notebook"}get contentType(){return"notebook"}get fileFormat(){return"json"}get collaborative(){return this._collaborative}get isDisposed(){return this._disposed}dispose(){this._disposed=!0}createNew(e={}){return new M({languagePreference:e.languagePreference,sharedModel:e.sharedModel,collaborationEnabled:e.collaborationEnabled&&this.collaborative,disableDocumentWideUndoRedo:this._disableDocumentWideUndoRedo})}preferredLanguage(e){return""}}function A(e){const t=(e.translator||l.nullTranslator).load("jupyterlab");return c.createElement(w.TextItem,{source:t.__("Mode: %1",e.modeNames[e.notebookMode])})}class E extends _.VDomRenderer{constructor(e){super(new E.Model),this.translator=e||l.nullTranslator,this._trans=this.translator.load("jupyterlab"),this._modeNames={command:this._trans.__("Command"),edit:this._trans.__("Edit")}}render(){return this.model?(this.node.title=this._trans.__("Notebook is in %1 mode",this._modeNames[this.model.notebookMode]),c.createElement(A,{notebookMode:this.model.notebookMode,translator:this.translator,modeNames:this._modeNames})):null}}!function(e){class t extends _.VDomModel{constructor(){super(...arguments),this._onChanged=e=>{const t=this._notebookMode;this._notebook?this._notebookMode=e.mode:this._notebookMode="command",this._triggerChange(t,this._notebookMode)},this._notebookMode="command",this._notebook=null}get notebookMode(){return this._notebookMode}set notebook(e){const t=this._notebook;null!==t&&(t.stateChanged.disconnect(this._onChanged,this),t.activeCellChanged.disconnect(this._onChanged,this),t.modelContentChanged.disconnect(this._onChanged,this));const n=this._notebookMode;this._notebook=e,null===this._notebook?this._notebookMode="command":(this._notebookMode=this._notebook.mode,this._notebook.stateChanged.connect(this._onChanged,this),this._notebook.activeCellChanged.connect(this._onChanged,this),this._notebook.modelContentChanged.connect(this._onChanged,this)),this._triggerChange(n,this._notebookMode)}_triggerChange(e,t){e!==t&&this.stateChanged.emit(void 0)}}e.Model=t}(E||(E={}));var T=n(31536),P=n(12729);class N extends P.WidgetLSPAdapter{constructor(e,t){super(e,t),this.editorWidget=e,this.options=t,this._type="code",this._readyDelegate=new d.PromiseDelegate,this._editorToCell=new Map,this.editor=e.content,this._cellToEditor=new WeakMap,Promise.all([this.widget.context.sessionContext.ready,this.connectionManager.ready]).then((async()=>{await this.initOnceReady(),this._readyDelegate.resolve()})).catch(console.error)}get documentPath(){return this.widget.context.path}get mimeType(){var e;let t,n=this.language_info();return t=n&&n.mimetype?n.mimetype:this.widget.content.codeMimetype,Array.isArray(t)?null!==(e=t[0])&&void 0!==e?e:T.IEditorMimeTypeService.defaultMimeType:t}get languageFileExtension(){let e=this.language_info();if(e&&e.file_extension)return e.file_extension.replace(".","")}get wrapperElement(){return this.widget.node}get editors(){if(this.isDisposed)return[];let e=this.widget.content;return this._editorToCell.clear(),e.isDisposed?[]:e.widgets.map((e=>({ceEditor:this._getCellEditor(e),type:e.model.type,value:e.model.sharedModel.getSource()})))}get activeEditor(){return this.editor.activeCell?this._getCellEditor(this.editor.activeCell):void 0}get ready(){return this._readyDelegate.promise}getEditorIndexAt(e){let t=this._getCellAt(e);return this.widget.content.widgets.findIndex((e=>t===e))}getEditorIndex(e){let t=this._editorToCell.get(e);return this.editor.widgets.findIndex((e=>t===e))}getEditorWrapper(e){return this._editorToCell.get(e).node}async onKernelChanged(e,t){if(t.newValue)try{const e=this._languageInfo;await(0,P.untilReady)(this.isReady,-1),await this._updateLanguageInfo();const t=this._languageInfo;(null==e?void 0:e.name)!=t.name||(null==e?void 0:e.mimetype)!=(null==t?void 0:t.mimetype)||(null==e?void 0:e.file_extension)!=(null==t?void 0:t.file_extension)?(console.log(`Changed to ${this._languageInfo.name} kernel, reconnecting`),this.reloadConnection()):console.log("Keeping old LSP connection as the new kernel uses the same langauge")}catch(e){console.warn(e),this.reloadConnection()}}dispose(){this.isDisposed||(this.widget.context.sessionContext.kernelChanged.disconnect(this.onKernelChanged,this),this.widget.content.activeCellChanged.disconnect(this._activeCellChanged,this),super.dispose(),this._editorToCell.clear(),r.Signal.clearData(this))}isReady(){var e;return!this.widget.isDisposed&&this.widget.context.isReady&&this.widget.content.isVisible&&this.widget.content.widgets.length>0&&null!=(null===(e=this.widget.context.sessionContext.session)||void 0===e?void 0:e.kernel)}async handleCellChange(e,t){let n=[],o=[];const i=this._type;if("set"===t.type){let e=[],s=[];if(t.newValues.length===t.oldValues.length){for(let n=0;n<t.newValues.length;n++)t.oldValues[n].type===i&&t.newValues[n].type!==i?e.push(t.newValues[n]):t.oldValues[n].type!==i&&t.newValues[n].type===i&&s.push(t.newValues[n]);n=s,o=e}}else"add"==t.type&&(n=t.newValues.filter((e=>e.type===i)));(o.length||n.length||"set"===t.type||"move"===t.type||"remove"===t.type)&&await this.updateDocuments();for(let e of n){let t=this.widget.content.widgets.find((t=>t.model.id===e.id));t?this._getCellEditor(t):console.warn(`Widget for added cell with ID: ${e.id} not found!`)}}createVirtualDocument(){return new P.VirtualDocument({language:this.language,foreignCodeExtractors:this.options.foreignCodeExtractorsManager,path:this.documentPath,fileExtension:this.languageFileExtension,standalone:!1,hasLspSupportedFile:!1})}language_info(){return this._languageInfo}async initOnceReady(){await(0,P.untilReady)(this.isReady.bind(this),-1),await this._updateLanguageInfo(),this.initVirtual(),this.connectDocument(this.virtualDocument,!1).catch(console.warn),this.widget.context.sessionContext.kernelChanged.connect(this.onKernelChanged,this),this.widget.content.activeCellChanged.connect(this._activeCellChanged,this),this._connectModelSignals(this.widget),this.editor.modelChanged.connect((e=>{console.warn("Model changed, connecting cell change handler; this is not something we were expecting"),this._connectModelSignals(e)}))}_connectModelSignals(e){null===e.model?console.warn(`Model is missing for notebook ${e}, cannot connect cell changed signal!`):e.model.cells.changed.connect(this.handleCellChange,this)}async _updateLanguageInfo(){var e,t,n,o;const i=null===(o=await(null===(n=null===(t=null===(e=this.widget.context.sessionContext)||void 0===e?void 0:e.session)||void 0===t?void 0:t.kernel)||void 0===n?void 0:n.info))||void 0===o?void 0:o.language_info;if(!i)throw new Error("Language info update failed (no session, kernel, or info available)");this._languageInfo=i}_activeCellChanged(e,t){t&&t.model.type===this._type&&this._activeEditorChanged.emit({editor:this._getCellEditor(t)})}_getCellAt(e){let t=this.virtualDocument.getEditorAtVirtualLine(e);return this._editorToCell.get(t)}_getCellEditor(e){if(!this._cellToEditor.has(e)){const t=Object.freeze({getEditor:()=>e.editor,ready:async()=>(await e.ready,e.editor),reveal:async()=>(await this.editor.scrollToCell(e),e.editor)});this._cellToEditor.set(e,t),this._editorToCell.set(t,e),e.disposed.connect((()=>{this._cellToEditor.delete(e),this._editorToCell.delete(t),this._editorRemoved.emit({editor:t})})),this._editorAdded.emit({editor:t})}return this._cellToEditor.get(e)}}var H,D=n(11351),L=n(49503),O=n(31516);class R extends O.Widget{constructor(){super(),this._items=[],this.layout=new O.PanelLayout,this.addClass("jp-RankedPanel")}addWidget(e,t){const n={widget:e,rank:t},o=a.ArrayExt.upperBound(this._items,n,H.itemCmp);a.ArrayExt.insert(this._items,o,n),this.layout.insertWidget(o,e)}onChildRemoved(e){const t=a.ArrayExt.findFirstIndex(this._items,(t=>t.widget===e.child));-1!==t&&a.ArrayExt.removeAt(this._items,t)}}class B extends O.Widget{constructor(e){super(),this.addClass("jp-NotebookTools"),this.translator=e.translator||l.nullTranslator,this._tools=[],this.layout=new O.PanelLayout,this._tracker=e.tracker,this._tracker.currentChanged.connect(this._onActiveNotebookPanelChanged,this),this._tracker.activeCellChanged.connect(this._onActiveCellChanged,this),this._tracker.selectionChanged.connect(this._onSelectionChanged,this),this._onActiveNotebookPanelChanged(),this._onActiveCellChanged(),this._onSelectionChanged()}get activeCell(){return this._tracker.activeCell}get selectedCells(){const e=this._tracker.currentWidget;if(!e)return[];const t=e.content;return t.widgets.filter((e=>t.isSelectedOrActive(e)))}get activeNotebookPanel(){return this._tracker.currentWidget}addItem(e){var t;const n=e.tool,o=null!==(t=e.rank)&&void 0!==t?t:100;let i;const s=this._tools.find((t=>t.section===e.section));if(!s)throw new Error(`The section ${e.section} does not exist`);i=s.panel,n.addClass("jp-NotebookTools-tool"),i.addWidget(n,o),n.notebookTools=this,L.MessageLoop.sendMessage(n,B.ActiveNotebookPanelMessage),L.MessageLoop.sendMessage(n,B.ActiveCellMessage)}addSection(e){var t;const n=e.sectionName,o=e.label||e.sectionName,i=e.tool;let s=null!==(t=e.rank)&&void 0!==t?t:null;const l=new R;if(l.title.label=o,i&&l.addWidget(i,0),this._tools.push({section:n,panel:l,rank:s}),null!=s)this.layout.insertWidget(s,new _.Collapser({widget:l}));else{let e=null;const t=this.layout;for(let n=0;n<t.widgets.length;n++){let o=t.widgets[n];if(o instanceof _.Collapser&&"advancedToolsSection"===o.widget.id){e=n;break}}null!==e?this.layout.insertWidget(e,new _.Collapser({widget:l})):this.layout.addWidget(new _.Collapser({widget:l}))}}_onActiveNotebookPanelChanged(){this._prevActiveNotebookModel&&!this._prevActiveNotebookModel.isDisposed&&this._prevActiveNotebookModel.metadataChanged.disconnect(this._onActiveNotebookPanelMetadataChanged,this);const e=this.activeNotebookPanel&&this.activeNotebookPanel.content?this.activeNotebookPanel.content.model:null;this._prevActiveNotebookModel=e,e&&e.metadataChanged.connect(this._onActiveNotebookPanelMetadataChanged,this);for(const e of this._toolChildren())L.MessageLoop.sendMessage(e,B.ActiveNotebookPanelMessage)}_onActiveCellChanged(){this._prevActiveCell&&!this._prevActiveCell.isDisposed&&this._prevActiveCell.metadataChanged.disconnect(this._onActiveCellMetadataChanged,this);const e=this.activeCell?this.activeCell.model:null;this._prevActiveCell=e,e&&e.metadataChanged.connect(this._onActiveCellMetadataChanged,this);for(const e of this._toolChildren())L.MessageLoop.sendMessage(e,B.ActiveCellMessage)}_onSelectionChanged(){for(const e of this._toolChildren())L.MessageLoop.sendMessage(e,B.SelectionMessage)}_onActiveNotebookPanelMetadataChanged(e,t){const n=new D.ObservableJSON.ChangeMessage("activenotebookpanel-metadata-changed",{oldValue:void 0,newValue:void 0,...t});for(const e of this._toolChildren())L.MessageLoop.sendMessage(e,n)}_onActiveCellMetadataChanged(e,t){const n=new D.ObservableJSON.ChangeMessage("activecell-metadata-changed",{newValue:void 0,oldValue:void 0,...t});for(const e of this._toolChildren())L.MessageLoop.sendMessage(e,n)}*_toolChildren(){for(let e of this._tools)yield*e.panel.children()}}!function(e){e.ActiveNotebookPanelMessage=new L.ConflatableMessage("activenotebookpanel-changed"),e.ActiveCellMessage=new L.ConflatableMessage("activecell-changed"),e.SelectionMessage=new L.ConflatableMessage("selection-changed");class t extends O.Widget{dispose(){super.dispose(),this.notebookTools&&(this.notebookTools=null)}processMessage(e){switch(super.processMessage(e),e.type){case"activenotebookpanel-changed":this.onActiveNotebookPanelChanged(e);break;case"activecell-changed":this.onActiveCellChanged(e);break;case"selection-changed":this.onSelectionChanged(e);break;case"activecell-metadata-changed":this.onActiveCellMetadataChanged(e);break;case"activenotebookpanel-metadata-changed":this.onActiveNotebookPanelMetadataChanged(e)}}onActiveNotebookPanelChanged(e){}onActiveCellChanged(e){}onSelectionChanged(e){}onActiveCellMetadataChanged(e){}onActiveNotebookPanelMetadataChanged(e){}}e.Tool=t,e.MetadataEditorTool=class extends t{constructor(e){super();const{editorFactory:t}=e;this.addClass("jp-MetadataEditorTool");const n=this.layout=new O.PanelLayout;this._editorFactory=t,this._editorLabel=e.label||"Edit Metadata",this.createEditor();const o=new O.Widget({node:document.createElement("label")});o.node.textContent=e.label||"Edit Metadata",n.addWidget(o),n.addWidget(this.editor)}get editor(){return this._editor}onActiveNotebookPanelChanged(e){this.editor.dispose(),this.notebookTools.activeNotebookPanel&&this.createEditor()}createEditor(){this._editor=new T.JSONEditor({editorFactory:this._editorFactory}),this.editor.title.label=this._editorLabel,this.layout.addWidget(this.editor)}}}(B||(B={})),function(e){e.itemCmp=function(e,t){return e.rank-t.rank}}(H||(H={}));var F=n(35312),V=n(87749),W=n(18395),j=n(99615),U=n(14421),q=n(24475);const K="jp-mod-dropTarget",z="jp-mod-dropSource";class $ extends _.WindowedListModel{constructor(e,t){super(t),this.cells=e,this.estimateWidgetSize=e=>{const t=this.cells[e].model.sharedModel.getSource().split("\n").length;return $.DEFAULT_EDITOR_LINE_HEIGHT*t+$.DEFAULT_CELL_MARGIN},this.widgetRenderer=e=>this.cells[e],this.scrollDownThreshold=$.DEFAULT_CELL_MARGIN/2+$.DEFAULT_EDITOR_LINE_HEIGHT,this.scrollUpThreshold=$.DEFAULT_CELL_MARGIN/2,this._estimatedWidgetSize=$.DEFAULT_CELL_SIZE}}$.DEFAULT_CELL_SIZE=39,$.DEFAULT_EDITOR_LINE_HEIGHT=17,$.DEFAULT_CELL_MARGIN=22;class J extends _.WindowedLayout{constructor(){super(...arguments),this._header=null,this._footer=null,this._willBeRemoved=null,this._topHiddenCodeCells=-1}get header(){return this._header}set header(e){var t;this._header&&this._header.isAttached&&O.Widget.detach(this._header),this._header=e,this._header&&(null===(t=this.parent)||void 0===t?void 0:t.isAttached)&&O.Widget.attach(this._header,this.parent.node)}get footer(){return this._footer}set footer(e){var t;this._footer&&this._footer.isAttached&&O.Widget.detach(this._footer),this._footer=e,this._footer&&(null===(t=this.parent)||void 0===t?void 0:t.isAttached)&&O.Widget.attach(this._footer,this.parent.outerNode)}get activeCell(){return this._activeCell}set activeCell(e){this._activeCell=e}dispose(){var e,t;this.isDisposed||(null===(e=this._header)||void 0===e||e.dispose(),null===(t=this._footer)||void 0===t||t.dispose(),super.dispose())}removeWidget(e){const t=this.widgets.indexOf(e);t>=0?this.removeWidgetAt(t):e===this._willBeRemoved&&this.parent&&this.detachWidget(t,e)}attachWidget(e,t){const n=t.isPlaceholder(),o=this._isSoftHidden(t);if(this.parent.isAttached&&!o&&L.MessageLoop.sendMessage(t,O.Widget.Msg.BeforeAttach),o&&this._toggleSoftVisibility(t,!0),!n&&t instanceof i.CodeCell&&t.node.parentElement)t.node.style.display="",this._topHiddenCodeCells=-1;else if(!o){const e=this._findNearestChildBinarySearch(this.parent.viewportNode.childElementCount-1,0,parseInt(t.dataset.windowedListIndex,10)+1);let n=this.parent.viewportNode.children[e];this.parent.viewportNode.insertBefore(t.node,n),this.parent.isAttached&&L.MessageLoop.sendMessage(t,O.Widget.Msg.AfterAttach)}t.inViewport=!0}detachWidget(e,t){t.inViewport=!1,t!==this.activeCell?(t instanceof i.CodeCell&&!t.node.classList.contains(z)&&t!==this._willBeRemoved?(t.node.style.display="none",this._topHiddenCodeCells=-1):(this.parent.isAttached&&L.MessageLoop.sendMessage(t,O.Widget.Msg.BeforeDetach),this.parent.viewportNode.removeChild(t.node),t.node.classList.remove(K)),this.parent.isAttached&&L.MessageLoop.sendMessage(t,O.Widget.Msg.AfterDetach)):this._toggleSoftVisibility(t,!1)}moveWidget(e,t,n){if(this._topHiddenCodeCells<0){this._topHiddenCodeCells=0;for(let e=0;e<this.parent.viewportNode.children.length&&"none"==this.parent.viewportNode.children[e].style.display;e++)this._topHiddenCodeCells++}const o=this.parent.viewportNode.children[t+this._topHiddenCodeCells];e<t?o.insertAdjacentElement("afterend",n.node):o.insertAdjacentElement("beforebegin",n.node)}onAfterAttach(e){super.onAfterAttach(e),this._header&&!this._header.isAttached&&O.Widget.attach(this._header,this.parent.node,this.parent.node.firstElementChild),this._footer&&!this._footer.isAttached&&O.Widget.attach(this._footer,this.parent.outerNode)}onBeforeDetach(e){var t,n;(null===(t=this._header)||void 0===t?void 0:t.isAttached)&&O.Widget.detach(this._header),(null===(n=this._footer)||void 0===n?void 0:n.isAttached)&&O.Widget.detach(this._footer),super.onBeforeDetach(e)}onChildRemoved(e){this._willBeRemoved=e.child,super.onChildRemoved(e),this._willBeRemoved=null}_toggleSoftVisibility(e,t){t?(e.node.style.opacity="",e.node.style.height="",e.node.style.padding=""):(e.node.style.opacity="0",e.node.style.height="0",e.node.style.padding="0")}_isSoftHidden(e){return"0"===e.node.style.opacity}_findNearestChildBinarySearch(e,t,n){for(;t<=e;){const o=t+Math.floor((e-t)/2),i=parseInt(this.parent.viewportNode.children[o].dataset.windowedListIndex,10);if(i===n)return o;i<n?t=o+1:i>n&&(e=o-1)}return t>0?t:0}}class Y extends O.Widget{constructor(e){super({node:document.createElement("button")}),this.notebook=e;const t=e.translator.load("jupyterlab");this.addClass("jp-Notebook-footer"),this.node.setAttribute("tabindex","-1"),this.node.innerText=t.__("Click to add a cell.")}handleEvent(e){switch(e.type){case"click":this.onClick();break;case"keydown":if("ArrowUp"===e.key){this.onArrowUp();break}}}onClick(){this.notebook.widgets.length>0&&(this.notebook.activeCellIndex=this.notebook.widgets.length-1),C.insertBelow(this.notebook),C.focusActiveCell(this.notebook)}onArrowUp(){}onAfterAttach(e){super.onAfterAttach(e),this.node.addEventListener("click",this),this.node.addEventListener("keydown",this)}onBeforeDetach(e){this.node.removeEventListener("click",this),this.node.removeEventListener("keydown",this),super.onBeforeDetach(e)}}const G="jp-Notebook-cell",Q="jp-mod-editMode",X="jp-mod-commandMode",Z="jp-mod-active",ee="jp-mod-selected",te="jp-mod-multiSelected",ne="jp-dragImage",oe="jp-dragImage-singlePrompt",ie="jp-dragImage-content",se="jp-dragImage-prompt",le="jp-dragImage-multipleBack",ae="application/vnd.jupyter.cells",de="jp-mod-sideBySide";void 0===window.requestIdleCallback&&(window.requestIdleCallback=function(e){let t=Date.now();return setTimeout((function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})}),1)},window.cancelIdleCallback=function(e){clearTimeout(e)});class re extends _.WindowedList{constructor(e){var t,n,o,i,s,a;const d=new Array,c="full"===(null!==(n=null===(t=e.notebookConfig)||void 0===t?void 0:t.windowingMode)&&void 0!==n?n:re.defaultNotebookConfig.windowingMode);super({model:new $(d,{overscanCount:null!==(i=null===(o=e.notebookConfig)||void 0===o?void 0:o.overscanCount)&&void 0!==i?i:re.defaultNotebookConfig.overscanCount,windowingActive:c}),layout:new J,renderer:null!==(s=e.renderer)&&void 0!==s?s:_.WindowedList.defaultRenderer,scrollbar:!1}),this._cellCollapsed=new r.Signal(this),this._cellInViewportChanged=new r.Signal(this),this._renderingLayoutChanged=new r.Signal(this),this.addClass("jp-Notebook"),this.cellsArray=d,this._idleCallBack=null,this._editorConfig=re.defaultEditorConfig,this._notebookConfig=re.defaultNotebookConfig,this._mimetype=T.IEditorMimeTypeService.defaultMimeType,this._notebookModel=null,this._modelChanged=new r.Signal(this),this._modelContentChanged=new r.Signal(this),this.node.dataset.jpKernelUser="true",this.node.dataset.jpUndoer="true",this.node.dataset.jpCodeRunner="true",this.rendermime=e.rendermime,this.translator=e.translator||l.nullTranslator,this.contentFactory=e.contentFactory,this.editorConfig=e.editorConfig||re.defaultEditorConfig,this.notebookConfig=e.notebookConfig||re.defaultNotebookConfig,this._updateNotebookConfig(),this._mimetypeService=e.mimeTypeService,this.renderingLayout=null===(a=e.notebookConfig)||void 0===a?void 0:a.renderingLayout,this.kernelHistory=e.kernelHistory}get cellCollapsed(){return this._cellCollapsed}get cellInViewportChanged(){return this._cellInViewportChanged}get modelChanged(){return this._modelChanged}get modelContentChanged(){return this._modelContentChanged}get renderingLayoutChanged(){return this._renderingLayoutChanged}get model(){return this._notebookModel}set model(e){var t;if(e=e||null,this._notebookModel===e)return;const n=this._notebookModel;this._notebookModel=e,this._onModelChanged(n,e),this.onModelChanged(n,e),this._modelChanged.emit(void 0),this.viewModel.itemsList=null!==(t=null==e?void 0:e.cells)&&void 0!==t?t:null}get codeMimetype(){return this._mimetype}get widgets(){return this.cellsArray}get editorConfig(){return this._editorConfig}set editorConfig(e){this._editorConfig=e,this._updateEditorConfig()}get notebookConfig(){return this._notebookConfig}set notebookConfig(e){this._notebookConfig=e,this._updateNotebookConfig()}get renderingLayout(){return this._renderingLayout}set renderingLayout(e){var t;this._renderingLayout=e,"side-by-side"===this._renderingLayout?this.node.classList.add(de):this.node.classList.remove(de),this._renderingLayoutChanged.emit(null!==(t=this._renderingLayout)&&void 0!==t?t:"default")}dispose(){var e;this.isDisposed||(this._notebookModel=null,null===(e=this.layout.header)||void 0===e||e.dispose(),super.dispose())}moveCell(e,t,n=1){if(!this.model)return;const o=Math.min(this.model.cells.length-1,Math.max(0,t));if(o===e)return;const i=new Array(n);for(let t=0;t<n;t++){i[t]={};const n=this.widgets[e+t];if("markdown"===n.model.type)for(const e of["rendered","headingCollapsed"])i[t][e]=n[e]}this.model.sharedModel.moveCells(e,o,n);for(let e=0;e<n;e++){const n=this.widgets[t+e],o=i[e];for(const e in o)n[e]=o[e]}}renderCellOutputs(e){const t=this.viewModel.widgetRenderer(e);t instanceof i.CodeCell&&t.isPlaceholder()&&(t.dataset.windowedListIndex=`${e}`,this.layout.insertWidget(e,t),"full"===this.notebookConfig.windowingMode&&requestAnimationFrame((()=>{this.layout.removeWidget(t)})))}addHeader(){const e=this.translator.load("jupyterlab"),t=new O.Widget;t.node.textContent=e.__("The notebook is empty. Click the + button on the toolbar to add a new cell."),this.layout.header=t}removeHeader(){var e;null===(e=this.layout.header)||void 0===e||e.dispose(),this.layout.header=null}onModelChanged(e,t){}onModelContentChanged(e,t){this._modelContentChanged.emit(void 0)}onMetadataChanged(e,t){"language_info"===t.key&&this._updateMimetype()}onCellInserted(e,t){}onCellRemoved(e,t){}onUpdateRequest(e){"defer"===this.notebookConfig.windowingMode?this._runOnIdleTime():super.onUpdateRequest(e)}_onModelChanged(e,t){var n;if(e)for(e.contentChanged.disconnect(this.onModelContentChanged,this),e.metadataChanged.disconnect(this.onMetadataChanged,this),e.cells.changed.disconnect(this._onCellsChanged,this);this.cellsArray.length;)this._removeCell(0);if(!t)return void(this._mimetype=T.IEditorMimeTypeService.defaultMimeType);this._updateMimetype();const o=t.cells;null!==(n=t.collaborative)&&void 0!==n&&n||o.length||t.sharedModel.insertCell(0,{cell_type:this.notebookConfig.defaultCell,metadata:"code"===this.notebookConfig.defaultCell?{trusted:!0}:{}});let i=-1;for(const e of o)this._insertCell(++i,e);t.cells.changed.connect(this._onCellsChanged,this),t.metadataChanged.connect(this.onMetadataChanged,this),t.contentChanged.connect(this.onModelContentChanged,this)}_onCellsChanged(e,t){switch(this.removeHeader(),t.type){case"add":{let e=0;e=t.newIndex;for(const n of t.newValues)this._insertCell(e++,n);this._updateDataWindowedListIndex(t.newIndex,this.model.cells.length,t.newValues.length);break}case"remove":for(let e=t.oldValues.length;e>0;e--)this._removeCell(t.oldIndex);if(this._updateDataWindowedListIndex(t.oldIndex,this.model.cells.length+t.oldValues.length,-1*t.oldValues.length),!e.length){const e=this.model;requestAnimationFrame((()=>{!e||e.isDisposed||e.sharedModel.cells.length||e.sharedModel.insertCell(0,{cell_type:this.notebookConfig.defaultCell,metadata:"code"===this.notebookConfig.defaultCell?{trusted:!0}:{}})}))}break;default:return}this.model.sharedModel.cells.length||this.addHeader(),this.update()}_insertCell(e,t){let n;switch(t.type){case"code":n=this._createCodeCell(t),n.model.mimeType=this._mimetype;break;case"markdown":n=this._createMarkdownCell(t),""===t.sharedModel.getSource()&&(n.rendered=!1);break;default:n=this._createRawCell(t)}n.inViewportChanged.connect(this._onCellInViewportChanged,this),n.addClass(G),a.ArrayExt.insert(this.cellsArray,e,n),this.onCellInserted(e,n),this._scheduleCellRenderOnIdle()}_createCodeCell(e){const t=this.rendermime,n={contentFactory:this.contentFactory,editorConfig:this.editorConfig.code,inputHistoryScope:this.notebookConfig.inputHistoryScope,maxNumberOutputs:this.notebookConfig.maxNumberOutputs,model:e,placeholder:"none"!==this._notebookConfig.windowingMode,rendermime:t,translator:this.translator},o=this.contentFactory.createCodeCell(n);return o.syncCollapse=!0,o.syncEditable=!0,o.syncScrolled=!0,o.outputArea.inputRequested.connect(((e,t)=>{this._onInputRequested(o).catch((e=>{console.error("Failed to scroll to cell requesting input.",e)})),t.disposed.connect((()=>{o.node.focus()}))})),o}_createMarkdownCell(e){const t=this.rendermime,n={contentFactory:this.contentFactory,editorConfig:this.editorConfig.markdown,model:e,placeholder:"none"!==this._notebookConfig.windowingMode,rendermime:t,showEditorForReadOnlyMarkdown:this._notebookConfig.showEditorForReadOnlyMarkdown},o=this.contentFactory.createMarkdownCell(n);return o.syncCollapse=!0,o.syncEditable=!0,o.headingCollapsedChanged.connect(this._onCellCollapsed,this),o}_createRawCell(e){const t=this.contentFactory,n={editorConfig:this.editorConfig.raw,model:e,contentFactory:t,placeholder:"none"!==this._notebookConfig.windowingMode},o=this.contentFactory.createRawCell(n);return o.syncCollapse=!0,o.syncEditable=!0,o}_removeCell(e){const t=this.cellsArray[e];t.parent=null,a.ArrayExt.removeAt(this.cellsArray,e),this.onCellRemoved(e,t),t.dispose()}_updateMimetype(){var e;const t=null===(e=this._notebookModel)||void 0===e?void 0:e.getMetadata("language_info");if(t){this._mimetype=this._mimetypeService.getMimeTypeByLanguage(t);for(const e of this.widgets)"code"===e.model.type&&(e.model.mimeType=this._mimetype)}}_onCellCollapsed(e,t){C.setHeadingCollapse(e,t,this),this._cellCollapsed.emit(e)}_onCellInViewportChanged(e){this._cellInViewportChanged.emit(e)}async _onInputRequested(e){if(!e.inViewport){const t=this.widgets.findIndex((t=>t===e));if(t>=0){await this.scrollToItem(t);const n=e.node.querySelector(".jp-Stdin");n&&(W.ElementExt.scrollIntoViewIfNeeded(this.node,n),n.focus())}}}_scheduleCellRenderOnIdle(){"none"===this.notebookConfig.windowingMode||this.isDisposed||this._idleCallBack||(this._idleCallBack=requestIdleCallback((e=>{this._idleCallBack=null,this._runOnIdleTime(e.didTimeout?50:e.timeRemaining())}),{timeout:3e3}))}_updateDataWindowedListIndex(e,t,n){for(let o=0;o<this.viewportNode.childElementCount;o++){const i=this.viewportNode.children[o],s=parseInt(i.dataset.windowedListIndex,10);s>=e&&s<t&&(i.dataset.windowedListIndex=`${s+n}`)}}_updateEditorConfig(){for(let e=0;e<this.widgets.length;e++){const t=this.widgets[e];let n={};switch(t.model.type){case"code":n=this._editorConfig.code;break;case"markdown":n=this._editorConfig.markdown;break;default:n=this._editorConfig.raw}t.updateEditorConfig({...n})}}async _runOnIdleTime(e=50){const t=Date.now();let n=0;for(;Date.now()-t<e&&n<this.cellsArray.length;){const e=this.cellsArray[n];if(e.isPlaceholder())switch(this.notebookConfig.windowingMode){case"defer":await this._updateForDeferMode(e,n);break;case"full":this._renderCSSAndJSOutputs(e,n)}n++}n<this.cellsArray.length?"defer"===this.notebookConfig.windowingMode&&this.viewportNode.clientHeight<this.node.clientHeight?await this._runOnIdleTime():this._scheduleCellRenderOnIdle():this._idleCallBack&&(window.cancelIdleCallback(this._idleCallBack),this._idleCallBack=null)}async _updateForDeferMode(e,t){e.dataset.windowedListIndex=`${t}`,this.layout.insertWidget(t,e),await e.ready}_renderCSSAndJSOutputs(e,t){var n,o,s;if(e instanceof i.CodeCell)for(let i=0;i<(null!==(o=null===(n=e.model.outputs)||void 0===n?void 0:n.length)&&void 0!==o?o:0);i++)if((null!==(s=e.model.outputs.get(i).data["text/html"])&&void 0!==s?s:"").match(/(<style[^>]*>[^<]*<\/style[^>]*>|<script[^>]*>.*?<\/script[^>]*>)/gims)){this.renderCellOutputs(t);break}}_updateNotebookConfig(){this.toggleClass("jp-mod-scrollPastEnd",this._notebookConfig.scrollPastEnd),this.toggleClass("jp-mod-showHiddenCellsButton",this._notebookConfig.showHiddenCellsButton);const e=this._notebookConfig.showEditorForReadOnlyMarkdown;if(void 0!==e)for(const t of this.cellsArray)"markdown"===t.model.type&&(t.showEditorForReadOnly=e);this.viewModel.windowingActive="full"===this._notebookConfig.windowingMode}}!function(e){e.defaultEditorConfig={code:{lineNumbers:!1,lineWrap:!1,matchBrackets:!0,tabFocusable:!1},markdown:{lineNumbers:!1,lineWrap:!0,matchBrackets:!1,tabFocusable:!1},raw:{lineNumbers:!1,lineWrap:!0,matchBrackets:!1,tabFocusable:!1}},e.defaultNotebookConfig={enableKernelInitNotification:!1,showHiddenCellsButton:!0,scrollPastEnd:!0,defaultCell:"code",recordTiming:!1,inputHistoryScope:"global",maxNumberOutputs:50,showEditorForReadOnlyMarkdown:!0,disableDocumentWideUndoRedo:!0,renderingLayout:"default",sideBySideLeftMarginOverride:"10px",sideBySideRightMarginOverride:"10px",sideBySideOutputRatio:1,overscanCount:1,windowingMode:"full",accessKernelHistory:!1};class t extends i.Cell.ContentFactory{createCodeCell(e){return new i.CodeCell(e).initializeState()}createMarkdownCell(e){return new i.MarkdownCell(e).initializeState()}createRawCell(e){return new i.RawCell(e).initializeState()}}e.ContentFactory=t}(re||(re={}));class ce extends re{constructor(e){super({renderer:{createOuter:()=>document.createElement("div"),createViewport(){const e=document.createElement("div");return e.setAttribute("role","feed"),e.setAttribute("aria-label","Cells"),e},createScrollbar:()=>document.createElement("ol"),createScrollbarItem(e,t,n){const o=document.createElement("li");return o.appendChild(document.createTextNode(`${t+1}`)),e.activeCellIndex===t&&o.classList.add("jp-mod-active"),e.selectedCells.some((e=>n===e.model))&&o.classList.add("jp-mod-selected"),o}},...e}),this._activeCellIndex=-1,this._activeCell=null,this._mode="command",this._drag=null,this._dragData=null,this._mouseMode=null,this._activeCellChanged=new r.Signal(this),this._stateChanged=new r.Signal(this),this._selectionChanged=new r.Signal(this),this._checkCacheOnNextResize=!1,this._lastClipboardInteraction=null,this._selectedCells=[],this.outerNode.setAttribute("data-lm-dragscroll","true"),this.activeCellChanged.connect(this._updateSelectedCells,this),this.jumped.connect(((e,t)=>this.activeCellIndex=t)),this.selectionChanged.connect(this._updateSelectedCells,this),this.addFooter()}get selectedCells(){return this._selectedCells}addFooter(){const e=new Y(this);this.layout.footer=e}_onCellsChanged(e,t){var n,o;const i=null===(n=this.activeCell)||void 0===n?void 0:n.model.id;if(super._onCellsChanged(e,t),i){const e=null===(o=this.model)||void 0===o?void 0:o.sharedModel.cells.findIndex((e=>e.getId()===i));null!=e&&(this.activeCellIndex=e)}}get activeCellChanged(){return this._activeCellChanged}get stateChanged(){return this._stateChanged}get selectionChanged(){return this._selectionChanged}get mode(){return this._mode}set mode(e){this.setMode(e)}setMode(e,t={}){var n;const o=null===(n=t.focus)||void 0===n||n,s=this.activeCell;if(s||(e="command"),e===this._mode)return void(o&&this._ensureFocus());this.update();const l=this._mode;if(this._mode=e,"edit"===e){for(const e of this.widgets)this.deselect(e);s instanceof i.MarkdownCell&&(s.rendered=!1),s.inputHidden=!1}else o&&C.focusActiveCell(this,{waitUntilReady:!1,preventScroll:!0});this._stateChanged.emit({name:"mode",oldValue:l,newValue:e}),o&&this._ensureFocus()}get activeCellIndex(){return this.model&&this.widgets.length?this._activeCellIndex:-1}set activeCellIndex(e){var t;const n=this._activeCellIndex;this.model&&this.widgets.length?(e=Math.max(e,0),e=Math.min(e,this.widgets.length-1)):e=-1,this._activeCellIndex=e;const o=null!==(t=this.widgets[e])&&void 0!==t?t:null;this.layout.activeCell=o;const s=o!==this._activeCell;s&&(this.update(),this._activeCell=o),(s||e!=n)&&this._activeCellChanged.emit(o),"edit"===this.mode&&o instanceof i.MarkdownCell&&(o.rendered=!1),this._ensureFocus(),e!==n&&(this._trimSelections(),this._stateChanged.emit({name:"activeCellIndex",oldValue:n,newValue:e}))}get activeCell(){return this._activeCell}get lastClipboardInteraction(){return this._lastClipboardInteraction}set lastClipboardInteraction(e){this._lastClipboardInteraction=e}dispose(){this.isDisposed||(this._activeCell=null,super.dispose())}moveCell(e,t,n=1){const o=e<=this.activeCellIndex&&this.activeCellIndex<e+n?this.activeCellIndex+t-e-(e>t?0:n-1):-1,i=this.widgets.slice(e,e+n).map((e=>this.isSelected(e)));super.moveCell(e,t,n),o>=0&&(this.activeCellIndex=o),e>t?i.forEach(((e,n)=>{e&&this.select(this.widgets[t+n])})):i.forEach(((e,o)=>{e&&this.select(this.widgets[t-n+1+o])}))}select(e){he.selectedProperty.get(e)||(he.selectedProperty.set(e,!0),this._selectionChanged.emit(void 0),this.update())}deselect(e){he.selectedProperty.get(e)&&(he.selectedProperty.set(e,!1),this._selectionChanged.emit(void 0),this.update())}isSelected(e){return he.selectedProperty.get(e)}isSelectedOrActive(e){return e===this._activeCell||he.selectedProperty.get(e)}deselectAll(){let e=!1;for(const t of this.widgets)he.selectedProperty.get(t)&&(e=!0),he.selectedProperty.set(t,!1);e&&this._selectionChanged.emit(void 0),this.activeCellIndex=this.activeCellIndex,this.update()}extendContiguousSelectionTo(e){let t,{head:n,anchor:o}=this.getContiguousSelection();if(null===o||null===n){if(e===this.activeCellIndex)return;n=this.activeCellIndex,o=this.activeCellIndex}if(this.activeCellIndex=e,(e=this.activeCellIndex)===o)return void this.deselectAll();let i=!1;if(n<e)for(n<o&&(he.selectedProperty.set(this.widgets[n],!1),i=!0),t=n+1;t<e;t++)t!==o&&(he.selectedProperty.set(this.widgets[t],!he.selectedProperty.get(this.widgets[t])),i=!0);else if(e<n)for(o<n&&(he.selectedProperty.set(this.widgets[n],!1),i=!0),t=e+1;t<n;t++)t!==o&&(he.selectedProperty.set(this.widgets[t],!he.selectedProperty.get(this.widgets[t])),i=!0);he.selectedProperty.get(this.widgets[o])||(i=!0),he.selectedProperty.set(this.widgets[o],!0),he.selectedProperty.get(this.widgets[e])||(i=!0),he.selectedProperty.set(this.widgets[e],!0),i&&this._selectionChanged.emit(void 0)}getContiguousSelection(){const e=this.widgets,t=a.ArrayExt.findFirstIndex(e,(e=>this.isSelected(e)));if(-1===t)return{head:null,anchor:null};const n=a.ArrayExt.findLastIndex(e,(e=>this.isSelected(e)),-1,t);for(let o=t;o<=n;o++)if(!this.isSelected(e[o]))throw new Error("Selection not contiguous");const o=this.activeCellIndex;if(t!==o&&n!==o)throw new Error("Active cell not at endpoint of selection");return t===o?{head:t,anchor:n}:{head:n,anchor:t}}async scrollToCell(e,t="auto"){try{await this.scrollToItem(this.widgets.findIndex((t=>t===e)),t)}catch(e){}this.deselectAll(),this.select(e),e.activate()}_parseFragment(e){const t=e.slice(1);if(!t)return;const n=t.split("=");return 1===n.length?{kind:"heading",value:t}:{kind:n[0],value:n.slice(1).join("=")}}async setFragment(e){const t=this._parseFragment(e);if(!t)return;let n;switch(t.kind){case"heading":n=await this._findHeading(t.value);break;case"cell-id":n=this._findCellById(t.value);break;default:console.warn(`Unknown target type for URI fragment ${e}, interpreting as a heading`),n=await this._findHeading(t.kind+"="+t.value)}if(null==n)return;let{cell:o,element:i}=n;o.inViewport||await this.scrollToCell(o,"center"),null==i&&(i=o.node);const s=this.node.getBoundingClientRect(),l=i.getBoundingClientRect();(l.top>s.bottom||l.bottom<s.top)&&i.scrollIntoView({block:"center"})}handleEvent(e){if(this.model)switch(e.type){case"contextmenu":e.eventPhase===Event.CAPTURING_PHASE&&this._evtContextMenuCapture(e);break;case"mousedown":e.eventPhase===Event.CAPTURING_PHASE?this._evtMouseDownCapture(e):e.defaultPrevented||this._evtMouseDown(e);break;case"mouseup":e.currentTarget===document&&this._evtDocumentMouseup(e);break;case"mousemove":e.currentTarget===document&&this._evtDocumentMousemove(e);break;case"keydown":this._ensureFocus(!0);break;case"dblclick":this._evtDblClick(e);break;case"focusin":this._evtFocusIn(e);break;case"focusout":this._evtFocusOut(e);break;case"lm-dragenter":this._evtDragEnter(e);break;case"lm-dragleave":this._evtDragLeave(e);break;case"lm-dragover":this._evtDragOver(e);break;case"lm-drop":this._evtDrop(e);break;default:super.handleEvent(e)}}onAfterAttach(e){super.onAfterAttach(e);const t=this.node;t.addEventListener("contextmenu",this,!0),t.addEventListener("mousedown",this,!0),t.addEventListener("mousedown",this),t.addEventListener("keydown",this),t.addEventListener("dblclick",this),t.addEventListener("focusin",this),t.addEventListener("focusout",this),t.addEventListener("lm-dragenter",this,!0),t.addEventListener("lm-dragleave",this,!0),t.addEventListener("lm-dragover",this,!0),t.addEventListener("lm-drop",this,!0)}onBeforeDetach(e){const t=this.node;t.removeEventListener("contextmenu",this,!0),t.removeEventListener("mousedown",this,!0),t.removeEventListener("mousedown",this),t.removeEventListener("keydown",this),t.removeEventListener("dblclick",this),t.removeEventListener("focusin",this),t.removeEventListener("focusout",this),t.removeEventListener("lm-dragenter",this,!0),t.removeEventListener("lm-dragleave",this,!0),t.removeEventListener("lm-dragover",this,!0),t.removeEventListener("lm-drop",this,!0),document.removeEventListener("mousemove",this,!0),document.removeEventListener("mouseup",this,!0),super.onBeforeAttach(e)}onAfterShow(e){super.onAfterShow(e),this._checkCacheOnNextResize=!0}onResize(e){var t;if(!this._checkCacheOnNextResize)return super.onResize(e);super.onResize(e),this._checkCacheOnNextResize=!1;const n=this._cellLayoutStateCache,o=parseInt(this.node.style.width,10);if(!n||o!==n.width){this._cellLayoutStateCache={width:o};for(const e of this.widgets)e instanceof i.Cell&&e.inViewport&&(null===(t=e.editorWidget)||void 0===t||t.update())}}onBeforeHide(e){super.onBeforeHide(e);const t=parseInt(this.node.style.width,10);this._cellLayoutStateCache={width:t}}onActivateRequest(e){super.onActivateRequest(e),this._ensureFocus(!0)}onUpdateRequest(e){super.onUpdateRequest(e);const t=this.activeCell;"edit"===this.mode?(this.addClass(Q),this.removeClass(X)):(this.addClass(X),this.removeClass(Q));let n=0;for(const e of this.widgets)e.node.tabIndex=-1,e.removeClass(Z),e.removeClass(te),this.isSelectedOrActive(e)?(e.addClass(ee),n++):e.removeClass(ee);t&&(t.addClass(Z),t.addClass(ee),t.node.tabIndex=0,n>1&&t.addClass(te))}onCellInserted(e,t){t.ready.then((()=>{t.isDisposed||t.editor.edgeRequested.connect(this._onEdgeRequest,this)})),this.activeCellIndex=e<=this.activeCellIndex?this.activeCellIndex+1:this.activeCellIndex}onCellRemoved(e,t){this.activeCellIndex=e<=this.activeCellIndex?this.activeCellIndex-1:this.activeCellIndex,this.isSelected(t)&&this._selectionChanged.emit(void 0)}onModelChanged(e,t){super.onModelChanged(e,t),this.activeCellIndex=0}_onEdgeRequest(e,t){const n=this.activeCellIndex;if("top"===t){if(this.activeCellIndex--,this.activeCellIndex<n){const e=this.activeCell.editor;if(e){const t=e.lineCount-1;e.setCursorPosition({line:t,column:0})}}}else if("bottom"===t&&(this.activeCellIndex++,this.activeCellIndex>n)){const e=this.activeCell.editor;e&&e.setCursorPosition({line:0,column:0})}this.mode="edit"}_ensureFocus(e=!1){var t,n;const o=this.layout.footer;if(o&&document.activeElement===o.node)return;const i=this.activeCell;"edit"===this.mode&&i&&(!0===(null===(t=i.editor)||void 0===t?void 0:t.hasFocus())&&i.inViewport||(i.inViewport?null===(n=i.editor)||void 0===n||n.focus():this.scrollToItem(this.activeCellIndex).then((()=>{i.ready.then((()=>{var e;null===(e=i.editor)||void 0===e||e.focus()}))})).catch((e=>{})))),e&&i&&!i.node.contains(document.activeElement)&&C.focusActiveCell(this,{preventScroll:!0})}_findCell(e){let t=e;for(;t&&t!==this.node;){if(t.classList.contains(G)){const e=a.ArrayExt.findFirstIndex(this.widgets,(e=>e.node===t));if(-1!==e)return e;break}t=t.parentElement}return-1}_findEventTargetAndCell(e){let t=e.target,n=this._findCell(t);return-1===n&&(t=document.elementFromPoint(e.clientX,e.clientY),n=this._findCell(t)),[t,n]}async _findHeading(e){for(let t=0;t<this.widgets.length;t++){const n=this.widgets[t];if("raw"!==n.model.type&&("markdown"!==n.model.type||n.rendered))for(const t of n.headings){let o="";switch(t.type){case i.Cell.HeadingType.HTML:o=t.id;break;case i.Cell.HeadingType.Markdown:{const e=t;o=await V.TableOfContentsUtils.Markdown.getHeadingId(this.rendermime.markdownParser,e.raw,e.level,this.rendermime.sanitizer)}}if(o===e)return{cell:n,element:this.node.querySelector(`h${t.level}[id="${CSS.escape(o)}"]`)}}}return null}_findCellById(e){for(let t=0;t<this.widgets.length;t++){const n=this.widgets[t];if(n.model.id===e)return{cell:n}}return null}_evtContextMenuCapture(e){var t;if(e.shiftKey)return;const[n,o]=this._findEventTargetAndCell(e),i=this.widgets[o];i&&(null===(t=i.editorWidget)||void 0===t?void 0:t.node.contains(n))&&e.preventDefault()}_evtMouseDownCapture(e){var t;const{button:n,shiftKey:o}=e,[i,s]=this._findEventTargetAndCell(e),l=this.widgets[s];2===n&&!o&&l&&(null===(t=l.editorWidget)||void 0===t?void 0:t.node.contains(i))&&(this.mode="command",e.preventDefault())}_evtMouseDown(e){var t,n,o;const{button:i,shiftKey:s}=e;if(0!==i&&2!==i)return;if(s&&2===i)return;const[l,a]=this._findEventTargetAndCell(e),d=this.widgets[a];let r;if(r=d?(null===(t=d.editorWidget)||void 0===t?void 0:t.node.contains(l))?"input":(null===(n=d.promptNode)||void 0===n?void 0:n.contains(l))?"prompt":"cell":"notebook","input"!==r&&(this.mode="command"),"notebook"===r)this.deselectAll();else if("prompt"===r||"cell"===r){const t=""!==(null!==(o=window.getSelection())&&void 0!==o?o:"").toString();if(0!==i||!s||t||["INPUT","OPTION"].includes(l.tagName))0!==i||s?2===i&&(this.isSelectedOrActive(d)||(this.deselectAll(),this.activeCellIndex=a),e.preventDefault()):("prompt"===r&&(this._dragData={pressX:e.clientX,pressY:e.clientY,index:a},this._mouseMode="couldDrag",document.addEventListener("mouseup",this,!0),document.addEventListener("mousemove",this,!0),e.preventDefault()),this.isSelectedOrActive(d)||(this.deselectAll(),this.activeCellIndex=a));else{e.preventDefault();try{this.extendContiguousSelectionTo(a)}catch(e){return console.error(e),void this.deselectAll()}this._mouseMode="select",document.addEventListener("mouseup",this,!0),document.addEventListener("mousemove",this,!0)}}else"input"===r&&(2!==i||this.isSelectedOrActive(d)||(this.deselectAll(),this.activeCellIndex=a));this._ensureFocus(!0)}_evtDocumentMouseup(e){if(e.preventDefault(),e.stopPropagation(),document.removeEventListener("mousemove",this,!0),document.removeEventListener("mouseup",this,!0),"couldDrag"===this._mouseMode){const[,t]=this._findEventTargetAndCell(e);this.deselectAll(),this.activeCellIndex=t,this.activeCell.node.contains(document.activeElement)||C.focusActiveCell(this)}this._mouseMode=null}_evtDocumentMousemove(e){switch(e.preventDefault(),e.stopPropagation(),this._mouseMode){case"select":{const t=e.target,n=this._findCell(t);-1!==n&&this.extendContiguousSelectionTo(n);break}case"couldDrag":{const t=this._dragData,n=Math.abs(e.clientX-t.pressX),o=Math.abs(e.clientY-t.pressY);(n>=5||o>=5)&&(this._mouseMode=null,this._startDrag(t.index,e.clientX,e.clientY));break}}}_evtDragEnter(e){if(!e.mimeData.hasData(ae))return;e.preventDefault(),e.stopPropagation();const t=e.target,n=this._findCell(t);-1!==n&&this.cellsArray[n].node.classList.add(K)}_evtDragLeave(e){if(!e.mimeData.hasData(ae))return;e.preventDefault(),e.stopPropagation();const t=this.node.getElementsByClassName(K);t.length&&t[0].classList.remove(K)}_evtDragOver(e){if(!e.mimeData.hasData(ae))return;e.preventDefault(),e.stopPropagation(),e.dropAction=e.proposedAction;const t=this.node.getElementsByClassName(K);t.length&&t[0].classList.remove(K);const n=e.target,o=this._findCell(n);-1!==o&&this.cellsArray[o].node.classList.add(K)}_evtDrop(e){if(!e.mimeData.hasData(ae))return;if(e.preventDefault(),e.stopPropagation(),"none"===e.proposedAction)return void(e.dropAction="none");let t=e.target;for(;t&&t.parentElement;){if(t.classList.contains(K)){t.classList.remove(K);break}t=t.parentElement}const n=this.model,o=e.source;if(o===this){e.dropAction="move";const n=e.mimeData.getData("internal:cells"),s=n[n.length-1];if(s instanceof i.MarkdownCell&&s.headingCollapsed){const e=C.findNextParentHeading(s,o);if(e>0){const t=(0,a.findIndex)(o.widgets,(e=>s.model.id===e.model.id));n.push(...o.widgets.slice(t+1,e))}}let l=a.ArrayExt.firstIndexOf(this.widgets,n[0]),d=this._findCell(t);if(-1!==d&&d>l?d-=1:-1===d&&(d=this.widgets.length-1),d>=l&&d<l+n.length)return;this.moveCell(l,d,n.length)}else{e.dropAction="copy";let o=this._findCell(t);-1===o&&(o=this.widgets.length);const i=o,s=e.mimeData.getData(ae);n.sharedModel.insertCells(o,s),this.deselectAll(),this.activeCellIndex=i,this.extendContiguousSelectionTo(o-1)}C.focusActiveCell(this)}_startDrag(e,t,n){var o;const i=this.model.cells,s=[],l=[];let a=-1;for(const e of this.widgets){const t=i.get(++a);this.isSelectedOrActive(e)&&(e.addClass(z),s.push(t.toJSON()),l.push(e))}const r=this.activeCell;let c,h=null;if("code"===(null==r?void 0:r.model.type)){const e=r.model.executionCount;c=" ",e&&(c=e.toString())}else c="";h=he.createDragImage(s.length,c,null!==(o=null==r?void 0:r.model.sharedModel.getSource().split("\n")[0].slice(0,26))&&void 0!==o?o:""),this._drag=new j.Drag({mimeData:new d.MimeData,dragImage:h,supportedActions:"copy-move",proposedAction:"copy",source:this}),this._drag.mimeData.setData(ae,s),this._drag.mimeData.setData("internal:cells",l);const u=l.map((e=>e.model.sharedModel.getSource())).join("\n");this._drag.mimeData.setData("text/plain",u),document.removeEventListener("mousemove",this,!0),document.removeEventListener("mouseup",this,!0),this._mouseMode=null,this._drag.start(t,n).then((e=>{if(!this.isDisposed){this._drag=null;for(const e of l)e.removeClass(z)}}))}_updateReadWrite(){const e=o.DOMUtils.hasActiveEditableElement(this.node);this.node.classList.toggle("jp-mod-readWrite",e)}_evtFocusIn(e){var t,n;this._updateReadWrite();const o=e.target,i=this._findCell(o);if(-1!==i){const e=this.widgets[i];e.editorWidget&&!e.editorWidget.node.contains(o)&&this.setMode("command",{focus:!1}),this.activeCellIndex=i;const n=null===(t=e.editorWidget)||void 0===t?void 0:t.node;(null==n?void 0:n.contains(o))&&this.setMode("edit",{focus:!1})}else{this.setMode("command",{focus:!1}),e.preventDefault();const t=e.relatedTarget;this._activeCell&&!this._activeCell.node.contains(t)?this._activeCell.ready.then((()=>{var e;null===(e=this._activeCell)||void 0===e||e.node.focus({preventScroll:!0})})).catch((()=>{var e;null===(e=this.layout.footer)||void 0===e||e.node.focus({preventScroll:!0})})):null===(n=this.layout.footer)||void 0===n||n.node.focus({preventScroll:!0})}}_evtFocusOut(e){var t;this._updateReadWrite();const n=e.relatedTarget;if(!n)return;const o=this._findCell(n);-1!==o&&(null===(t=this.widgets[o].editorWidget)||void 0===t?void 0:t.node.contains(n))||"command"!==this.mode&&this.setMode("command",{focus:!1})}_evtDblClick(e){const t=this.model;if(!t)return;this.deselectAll();const[n,o]=this._findEventTargetAndCell(e);e.target.classList.contains("jp-collapseHeadingButton")||-1===o||(this.activeCellIndex=o,"markdown"===t.cells.get(o).type?this.widgets[o].rendered=!1:"img"===n.localName&&n.classList.toggle("jp-mod-unconfined"))}_trimSelections(){for(let e=0;e<this.widgets.length;e++)if(e!==this._activeCellIndex){const t=this.widgets[e];!t.model.isDisposed&&t.editor&&t.model.selections.delete(t.editor.uuid)}}_updateSelectedCells(){this._selectedCells=this.widgets.filter((e=>this.isSelectedOrActive(e))),this.kernelHistory&&this.kernelHistory.reset()}}var he;!function(e){class t extends re.ContentFactory{}e.ContentFactory=t}(ce||(ce={})),function(e){e.selectedProperty=new U.AttachedProperty({name:"selected",create:()=>!1});class t extends O.PanelLayout{onUpdateRequest(e){}}e.NotebookPanelLayout=t,e.createDragImage=function(e,t,n){return e>1?""!==t?q.VirtualDOM.realize(q.h.div(q.h.div({className:ne},q.h.span({className:se},"["+t+"]:"),q.h.span({className:ie},n)),q.h.div({className:le},""))):q.VirtualDOM.realize(q.h.div(q.h.div({className:ne},q.h.span({className:se}),q.h.span({className:ie},n)),q.h.div({className:le},""))):""!==t?q.VirtualDOM.realize(q.h.div(q.h.div({className:`${ne} ${oe}`},q.h.span({className:se},"["+t+"]:"),q.h.span({className:ie},n)))):q.VirtualDOM.realize(q.h.div(q.h.div({className:`${ne} ${oe}`},q.h.span({className:se}),q.h.span({className:ie},n))))}}(he||(he={}));class ue extends F.DocumentWidget{constructor(e){super(e),this._autorestarting=!1,this.addClass("jp-NotebookPanel"),this.toolbar.addClass("jp-NotebookPanel-toolbar"),this.content.addClass("jp-NotebookPanel-notebook"),this.content.model=this.context.model,this.context.sessionContext.kernelChanged.connect(this._onKernelChanged,this),this.context.sessionContext.statusChanged.connect(this._onSessionStatusChanged,this),this.context.saveState.connect(this._onSave,this),this.revealed.then((()=>{if(!this.isDisposed&&1===this.content.widgets.length){const e=this.content.widgets[0].model;"code"===e.type&&""===e.sharedModel.getSource()&&(this.content.mode="edit")}}))}_onSave(e,t){if("started"===t&&this.model)for(const e of this.model.cells)if((0,i.isMarkdownCellModel)(e))for(const t of e.attachments.keys)e.sharedModel.getSource().includes(t)||e.attachments.remove(t)}get sessionContext(){return this.context.sessionContext}get model(){return this.content.model}setConfig(e){this.content.editorConfig=e.editorConfig,this.content.notebookConfig=e.notebookConfig;const t=this.context.sessionContext.kernelPreference;this.context.sessionContext.kernelPreference={...t,shutdownOnDispose:e.kernelShutdown,autoStartDefault:e.autoStartDefault}}setFragment(e){this.context.ready.then((()=>{this.content.setFragment(e)}))}dispose(){this.content.dispose(),super.dispose()}[o.Printing.symbol](){return async()=>{this.context.model.dirty&&!this.context.model.readOnly&&await this.context.save(),await o.Printing.printURL(s.PageConfig.getNBConvertURL({format:"html",download:!1,path:this.context.path}))}}onBeforeHide(e){super.onBeforeHide(e),this.content.isParentHidden=!0}onBeforeShow(e){this.content.isParentHidden=!1,super.onBeforeShow(e)}_onKernelChanged(e,t){if(!this.model||!t.newValue)return;const{newValue:n}=t;n.info.then((e=>{var t;this.model&&(null===(t=this.context.sessionContext.session)||void 0===t?void 0:t.kernel)===n&&this._updateLanguage(e.language_info)})),this._updateSpec(n)}_onSessionStatusChanged(e,t){var n;"autorestarting"!==t||this._autorestarting?"restarting"===t||(this._autorestarting=!1):((0,o.showDialog)({title:this._trans.__("Kernel Restarting"),body:this._trans.__("The kernel for %1 appears to have died. It will restart automatically.",null===(n=this.sessionContext.session)||void 0===n?void 0:n.path),buttons:[o.Dialog.okButton({label:this._trans.__("Ok")})]}),this._autorestarting=!0)}_updateLanguage(e){this.model.setMetadata("language_info",e)}async _updateSpec(e){const t=await e.spec;this.isDisposed||this.model.setMetadata("kernelspec",{name:e.name,display_name:null==t?void 0:t.display_name,language:null==t?void 0:t.language})}}!function(e){class t extends ce.ContentFactory{createNotebook(e){return new ce(e)}}e.ContentFactory=t,e.IContentFactory=new d.Token("@jupyterlab/notebook:IContentFactory","A factory object that creates new notebooks.\n    Use this if you want to create and host notebooks in your own UI elements.")}(ue||(ue={}));var ge,Ce=n(3486);class ve extends Ce.SearchProvider{constructor(e,t=l.nullTranslator){super(e),this.translator=t,this._textSelection=null,this._currentProviderIndex=null,this._delayedActiveCellChangeHandler=null,this._onSelection=!1,this._selectedCells=1,this._selectedLines=0,this._query=null,this._searchProviders=[],this._editorSelectionsObservable=null,this._selectionSearchMode="cells",this._selectionLock=!1,this._searchActive=!1,this._handleHighlightsAfterActiveCellChange=this._handleHighlightsAfterActiveCellChange.bind(this),this.widget.model.cells.changed.connect(this._onCellsChanged,this),this.widget.content.activeCellChanged.connect(this._onActiveCellChanged,this),this.widget.content.selectionChanged.connect(this._onCellSelectionChanged,this),this.widget.content.stateChanged.connect(this._onNotebookStateChanged,this),this._observeActiveCell(),this._filtersChanged.connect(this._setEnginesSelectionSearchMode,this)}_onNotebookStateChanged(e,t){"mode"===t.name&&window.setTimeout((()=>{var e;"command"===t.newValue&&(null===(e=document.activeElement)||void 0===e?void 0:e.closest(".jp-DocumentSearch-overlay"))||(this._updateSelectionMode(),this._filtersChanged.emit())}),0)}static isApplicable(e){return e instanceof ue}static createNew(e,t){return new ve(e,t)}get currentMatchIndex(){let e=0,t=!1;for(let n=0;n<this._searchProviders.length;n++){const o=this._searchProviders[n];if(this._currentProviderIndex==n){const n=o.currentMatchIndex;if(null===n)return null;e+=n,t=!0;break}e+=o.matchesCount}return t?e:null}get matchesCount(){return this._searchProviders.reduce(((e,t)=>e+t.matchesCount),0)}get isReadOnly(){var e,t,n;return null!==(n=null===(t=null===(e=this.widget)||void 0===e?void 0:e.content.model)||void 0===t?void 0:t.readOnly)&&void 0!==n&&n}get replaceOptionsSupport(){return{preserveCase:!0}}getSelectionState(){const e="cells"===this._selectionSearchMode,t=e?this._selectedCells:this._selectedLines;return t>1?"multiple":1!==t||e?"none":"single"}dispose(){var e;if(this.isDisposed)return;this.widget.content.activeCellChanged.disconnect(this._onActiveCellChanged,this),null===(e=this.widget.model)||void 0===e||e.cells.changed.disconnect(this._onCellsChanged,this),this.widget.content.stateChanged.disconnect(this._onNotebookStateChanged,this),this.widget.content.selectionChanged.disconnect(this._onCellSelectionChanged,this),this._stopObservingLastCell(),super.dispose();const t=this.widget.content.activeCellIndex;this.endQuery().then((()=>{this.widget.isDisposed||(this.widget.content.activeCellIndex=t)})).catch((e=>{console.error(`Fail to end search query in notebook:\n${e}`)}))}getFilters(){const e=this.translator.load("jupyterlab");return{output:{title:e.__("Search Cell Outputs"),description:e.__("Search in the cell outputs."),default:!1,supportReplace:!1},selection:{title:"cells"===this._selectionSearchMode?e._n("Search in %1 Selected Cell","Search in %1 Selected Cells",this._selectedCells):e._n("Search in %1 Selected Line","Search in %1 Selected Lines",this._selectedLines),description:e.__("Search only in the selected cells or text (depending on edit/command mode)."),default:!1,supportReplace:!0}}}_updateSelectionMode(){this._selectionLock||(this._selectionSearchMode=1===this._selectedCells&&"edit"===this.widget.content.mode&&0!==this._selectedLines?"text":"cells")}getInitialQuery(){var e;return(null===(e=window.getSelection())||void 0===e?void 0:e.toString())||""}async clearHighlight(){this._selectionLock=!0,null!==this._currentProviderIndex&&this._currentProviderIndex<this._searchProviders.length&&(await this._searchProviders[this._currentProviderIndex].clearHighlight(),this._currentProviderIndex=null),this._selectionLock=!1}async highlightNext(e=!0,t){const n=await this._stepNext(!1,e,t);return null!=n?n:void 0}async highlightPrevious(e=!0,t){const n=await this._stepNext(!0,e,t);return null!=n?n:void 0}async startQuery(e,t){if(!this.widget)return;await this.endQuery(),this._searchActive=!0;let n=this.widget.content.widgets;this._query=e,this._filters={output:!1,selection:!1,...null!=t?t:{}},this._onSelection=this._filters.selection;const o=this.widget.content.activeCellIndex;return this._searchProviders=await Promise.all(n.map((async(t,n)=>{const s=(0,i.createCellSearchProvider)(t);return await s.setIsActive(!this._filters.selection||this.widget.content.isSelectedOrActive(t)),this._onSelection&&"text"===this._selectionSearchMode&&n===o&&this._textSelection&&await s.setSearchSelection(this._textSelection),await s.startQuery(e,this._filters),s}))),this._currentProviderIndex=o,await this.highlightNext(!0,{from:"selection-start",scroll:!1,select:!1}),Promise.resolve()}async endQuery(){await Promise.all(this._searchProviders.map((e=>e.endQuery().then((()=>{e.dispose()}))))),this._searchActive=!1,this._searchProviders.length=0,this._currentProviderIndex=null}async replaceCurrentMatch(e,t=!0,n){let o=!1;const i=async(e=!1)=>{var n;const o=null===(n=this.widget)||void 0===n?void 0:n.content.activeCell;"markdown"===(null==o?void 0:o.model.type)&&o.rendered&&(o.rendered=!1,e&&await this.highlightNext(t))};if(null!==this._currentProviderIndex){await i();const s=this._searchProviders[this._currentProviderIndex];o=await s.replaceCurrentMatch(e,!1,n),null===s.currentMatchIndex&&await this.highlightNext(t)}return await i(!0),o}async replaceAllMatches(e,t){return(await Promise.all(this._searchProviders.map((n=>n.replaceAllMatches(e,t))))).includes(!0)}async validateFilter(e,t){if("output"!==e)return t;if(t&&this.widget.content.widgets.some((e=>e instanceof i.CodeCell&&e.isPlaceholder()))){const e=this.translator.load("jupyterlab");if(!(await(0,o.showDialog)({title:e.__("Confirmation"),body:e.__("Searching outputs is expensive and requires to first rendered all outputs. Are you sure you want to search in the cell outputs?"),buttons:[o.Dialog.cancelButton({label:e.__("Cancel")}),o.Dialog.okButton({label:e.__("Ok")})]})).button.accept)return!1;this.widget.content.widgets.forEach(((e,t)=>{e instanceof i.CodeCell&&e.isPlaceholder()&&this.widget.content.renderCellOutputs(t)}))}return t}_addCellProvider(e){var t,n;const o=this.widget.content.widgets[e],s=(0,i.createCellSearchProvider)(o);a.ArrayExt.insert(this._searchProviders,e,s),s.setIsActive(!(null!==(n=null===(t=this._filters)||void 0===t?void 0:t.selection)&&void 0!==n&&n)||this.widget.content.isSelectedOrActive(o)).then((()=>{this._searchActive&&s.startQuery(this._query,this._filters)}))}_removeCellProvider(e){const t=a.ArrayExt.removeAt(this._searchProviders,e);null==t||t.dispose()}async _onCellsChanged(e,t){switch(t.type){case"add":t.newValues.forEach(((e,n)=>{this._addCellProvider(t.newIndex+n)}));break;case"move":a.ArrayExt.move(this._searchProviders,t.oldIndex,t.newIndex);break;case"remove":for(let e=0;e<t.oldValues.length;e++)this._removeCellProvider(t.oldIndex);break;case"set":t.newValues.forEach(((e,n)=>{this._addCellProvider(t.newIndex+n),this._removeCellProvider(t.newIndex+n+1)}))}this._stateChanged.emit()}async _stepNext(e=!1,t=!1,n){const o=async e=>{var t;if(null!==(t=null==n?void 0:n.scroll)&&void 0!==t&&!t)return;if(this._selectionLock=!0,this.widget.content.activeCellIndex!==this._currentProviderIndex&&(this.widget.content.activeCellIndex=this._currentProviderIndex),-1===this.widget.content.activeCellIndex)return console.warn("No active cell (no cells or no model), aborting search"),void(this._selectionLock=!1);const o=this.widget.content.activeCell;if(!o.inViewport)try{await this.widget.content.scrollToItem(this._currentProviderIndex)}catch(e){}if(o.inputHidden&&(o.inputHidden=!1),!o.inViewport)return void(this._selectionLock=!1);await o.ready;const i=o.editor;i.revealPosition(i.getPositionAt(e.position)),this._selectionLock=!1};null===this._currentProviderIndex&&(this._currentProviderIndex=this.widget.content.activeCellIndex),e&&"command"===this.widget.content.mode&&(this._searchProviders[this._currentProviderIndex].getCurrentMatch()||(this._currentProviderIndex-=1),t&&(this._currentProviderIndex=(this._currentProviderIndex+this._searchProviders.length)%this._searchProviders.length));const i=this._currentProviderIndex;do{const i=this._searchProviders[this._currentProviderIndex],s=e?await i.highlightPrevious(!1,n):await i.highlightNext(!1,n);if(s)return await o(s),s;this._currentProviderIndex=this._currentProviderIndex+(e?-1:1),t&&(this._currentProviderIndex=(this._currentProviderIndex+this._searchProviders.length)%this._searchProviders.length)}while(t?this._currentProviderIndex!==i:0<=this._currentProviderIndex&&this._currentProviderIndex<this._searchProviders.length);if(t){const t=this._searchProviders[i],s=e?await t.highlightPrevious(!1,n):await t.highlightNext(!1,n);if(s)return await o(s),s}return this._currentProviderIndex=null,null}async _onActiveCellChanged(){null!==this._delayedActiveCellChangeHandler&&(clearTimeout(this._delayedActiveCellChangeHandler),this._delayedActiveCellChangeHandler=null),this.widget.content.activeCellIndex!==this._currentProviderIndex&&(this._delayedActiveCellChangeHandler=window.setTimeout((()=>{this.delayedActiveCellChangeHandlerReady=this._handleHighlightsAfterActiveCellChange()}),0)),this._observeActiveCell()}async _handleHighlightsAfterActiveCellChange(){if(this._onSelection){const e=null!==this._currentProviderIndex&&this._currentProviderIndex<this.widget.content.widgets.length?this.widget.content.widgets[this._currentProviderIndex]:null;e&&this.widget.content.isSelectedOrActive(e)||(await this._updateCellSelection(),await this.clearHighlight(),this._currentProviderIndex=this.widget.content.activeCellIndex)}await this._ensureCurrentMatch()}async _ensureCurrentMatch(){if(null!==this._currentProviderIndex){const e=this._searchProviders[this._currentProviderIndex];if(!e)return;!e.getCurrentMatch()&&this.matchesCount&&await this.highlightNext(!0,{from:"start",scroll:!1,select:!1})}}_observeActiveCell(){var e;const t=null===(e=this.widget.content.activeCell)||void 0===e?void 0:e.editor;t&&(this._stopObservingLastCell(),t.model.selections.changed.connect(this._setSelectedLines,this),this._editorSelectionsObservable=t.model.selections)}_stopObservingLastCell(){this._editorSelectionsObservable&&this._editorSelectionsObservable.changed.disconnect(this._setSelectedLines,this)}_setSelectedLines(){var e;const t=null===(e=this.widget.content.activeCell)||void 0===e?void 0:e.editor;if(!t)return;const n=t.getSelection(),{start:o,end:i}=n,s=i.line===o.line&&i.column===o.column?0:i.line-o.line+1;this._textSelection=n,s!==this._selectedLines&&(this._selectedLines=s,this._updateSelectionMode()),this._filtersChanged.emit()}async _setEnginesSelectionSearchMode(){let e;e=!!this._onSelection&&"text"===this._selectionSearchMode,this._selectionLock||await Promise.all(this._searchProviders.map(((t,n)=>{const o=this.widget.content.activeCellIndex===n;return t.setProtectSelection(o&&this._onSelection),t.setSearchSelection(o&&e?this._textSelection:null)})))}async _onCellSelectionChanged(){if(null!==this._delayedActiveCellChangeHandler&&(clearTimeout(this._delayedActiveCellChangeHandler),this._delayedActiveCellChangeHandler=null),await this._updateCellSelection(),null===this._currentProviderIndex){const e=this.widget.content.widgets.findIndex((e=>this.widget.content.isSelectedOrActive(e)));this._currentProviderIndex=e}await this._ensureCurrentMatch()}async _updateCellSelection(){const e=this.widget.content.widgets;let t=0;await Promise.all(e.map((async(e,n)=>{const o=this._searchProviders[n],i=this.widget.content.isSelectedOrActive(e);i&&(t+=1),o&&this._onSelection&&await o.setIsActive(i)}))),t!==this._selectedCells&&(this._selectedCells=t,this._updateSelectionMode()),this._filtersChanged.emit()}}!function(e){e[e.Idle=-1]="Idle",e[e.Error=-.5]="Error",e[e.Scheduled=0]="Scheduled",e[e.Running=1]="Running"}(ge||(ge={}));class me extends V.TableOfContentsModel{constructor(e,t,n,o){super(e,o),this.parser=t,this.sanitizer=n,this.configMetadataMap={numberHeaders:["toc-autonumbering","toc/number_sections"],numberingH1:["!toc/skip_h1_title"],baseNumbering:["toc/base_numbering"]},this._runningCells=new Array,this._errorCells=new Array,this._cellToHeadingIndex=new WeakMap,e.context.ready.then((()=>{this.setConfiguration({})})),this.widget.context.model.metadataChanged.connect(this.onMetadataChanged,this),this.widget.content.activeCellChanged.connect(this.onActiveCellChanged,this),C.executionScheduled.connect(this.onExecutionScheduled,this),C.executed.connect(this.onExecuted,this),C.outputCleared.connect(this.onOutputCleared,this),this.headingsChanged.connect(this.onHeadingsChanged,this)}get documentType(){return"notebook"}get isAlwaysActive(){return!0}get supportedOptions(){return["baseNumbering","maximalDepth","numberingH1","numberHeaders","includeOutput","syncCollapseState"]}getCellHeadings(e){const t=new Array;let n=this._cellToHeadingIndex.get(e);if(void 0!==n){const e=this.headings[n];for(t.push(e);this.headings[n-1]&&this.headings[n-1].cellRef===e.cellRef;)n--,t.unshift(this.headings[n])}return t}dispose(){var e,t,n;this.isDisposed||(this.headingsChanged.disconnect(this.onHeadingsChanged,this),null===(t=null===(e=this.widget.context)||void 0===e?void 0:e.model)||void 0===t||t.metadataChanged.disconnect(this.onMetadataChanged,this),null===(n=this.widget.content)||void 0===n||n.activeCellChanged.disconnect(this.onActiveCellChanged,this),C.executionScheduled.disconnect(this.onExecutionScheduled,this),C.executed.disconnect(this.onExecuted,this),C.outputCleared.disconnect(this.onOutputCleared,this),this._runningCells.length=0,this._errorCells.length=0,super.dispose())}setConfiguration(e){const t=this.loadConfigurationFromMetadata();super.setConfiguration({...this.configuration,...t,...e})}toggleCollapse(e){super.toggleCollapse(e),this.updateRunningStatus(this.headings)}getHeadings(){const e=this.widget.content.widgets,t=[],n=new Array;for(let o=0;o<e.length;o++){const i=e[o];switch(i.model.type){case"code":!this.configuration.syncCollapseState&&this.configuration.includeOutput&&t.push(...V.TableOfContentsUtils.filterHeadings(i.headings,this.configuration,n).map((e=>({...e,cellRef:i,collapsed:!1,isRunning:ge.Idle}))));break;case"markdown":{const e=V.TableOfContentsUtils.filterHeadings(i.headings,this.configuration,n).map(((e,t)=>({...e,cellRef:i,collapsed:!1,isRunning:ge.Idle})));if(this.configuration.syncCollapseState&&i.headingCollapsed){const t=Math.min(...e.map((e=>e.level)));e.find((e=>e.level===t)).collapsed=i.headingCollapsed}t.push(...e);break}}t.length>0&&this._cellToHeadingIndex.set(i,t.length-1)}return this.updateRunningStatus(t),Promise.resolve(t)}loadConfigurationFromMetadata(){const e=this.widget.content.model,t={};if(e)for(const n in this.configMetadataMap){const o=this.configMetadataMap[n];for(const i of o){let o=i;const s="!"===o[0];s&&(o=o.slice(1));const l=o.split("/");let a=e.getMetadata(l[0]);for(let e=1;e<l.length;e++)a=(null!=a?a:{})[l[e]];void 0!==a&&("boolean"==typeof a&&s&&(a=!a),t[n]=a)}}return t}onActiveCellChanged(e,t){const n=this.getCellHeadings(t)[0];this.setActiveHeading(null!=n?n:null,!1)}onHeadingsChanged(){this.widget.content.activeCell&&this.onActiveCellChanged(this.widget.content,this.widget.content.activeCell)}onExecuted(e,t){this._runningCells.forEach(((e,n)=>{var o;if(e===t.cell){this._runningCells.splice(n,1);const i=this._cellToHeadingIndex.get(e);if(void 0!==i){const n=this.headings[i];if(t.success||void 0===(null===(o=t.error)||void 0===o?void 0:o.errorName))return void(n.isRunning=ge.Idle);n.isRunning=ge.Error,this._errorCells.includes(e)||this._errorCells.push(e)}}})),this.updateRunningStatus(this.headings),this.stateChanged.emit()}onExecutionScheduled(e,t){this._runningCells.includes(t.cell)||this._runningCells.push(t.cell),this._errorCells.forEach(((e,n)=>{e===t.cell&&this._errorCells.splice(n,1)})),this.updateRunningStatus(this.headings),this.stateChanged.emit()}onOutputCleared(e,t){this._errorCells.forEach(((e,n)=>{if(e===t.cell){this._errorCells.splice(n,1);const t=this._cellToHeadingIndex.get(e);void 0!==t&&(this.headings[t].isRunning=ge.Idle)}})),this.updateRunningStatus(this.headings),this.stateChanged.emit()}onMetadataChanged(){this.setConfiguration({})}updateRunningStatus(e){this._runningCells.forEach(((e,t)=>{const n=this._cellToHeadingIndex.get(e);if(void 0!==n){const e=this.headings[n];e.isRunning!==ge.Running&&(e.isRunning=t>0?ge.Scheduled:ge.Running)}})),this._errorCells.forEach(((e,t)=>{const n=this._cellToHeadingIndex.get(e);if(void 0!==n){const e=this.headings[n];e.isRunning===ge.Idle&&(e.isRunning=ge.Error)}}));let t=0;for(;t<e.length;){const o=e[t];if(t++,o.collapsed){const t=Math.max(o.isRunning,n(e,o.level));o.dataset={...o.dataset,"data-running":t.toString()}}else o.dataset={...o.dataset,"data-running":o.isRunning.toString()}}function n(e,o){let i=ge.Idle;for(;t<e.length;){const s=e[t];if(s.dataset={...s.dataset,"data-running":s.isRunning.toString()},!(s.level>o))break;t++,i=Math.max(s.isRunning,i),s.collapsed&&(i=Math.max(i,n(e,s.level)),s.dataset={...s.dataset,"data-running":i.toString()})}return i}}}class pe extends V.TableOfContentsFactory{constructor(e,t,n){super(e),this.parser=t,this.sanitizer=n,this._scrollToTop=!0}get scrollToTop(){return this._scrollToTop}set scrollToTop(e){this._scrollToTop=e}_createNew(e,t){const n=new me(e,this.parser,this.sanitizer,t);let o=new WeakMap;const s=(t,n)=>{if(n){const t=async t=>{if(!t.inViewport)return;const i=o.get(n);if(i)if(this.scrollToTop)i.scrollIntoView({block:"start"});else{const t=e.content.node.getBoundingClientRect(),n=i.getBoundingClientRect();(n.top>t.bottom||n.bottom<t.top)&&i.scrollIntoView({block:"center"})}else console.debug("scrolling to heading: using fallback strategy"),await e.content.scrollToItem(e.content.activeCellIndex,this.scrollToTop?"start":void 0,0)},i=n.cellRef,s=e.content.widgets.indexOf(i);"markdown"==i.model.type&&"command"!=e.content.mode&&(e.content.mode="command"),e.content.activeCellIndex=s,i.inViewport?t(i).catch((e=>{console.error(`Fail to scroll to cell to display the required heading (${e}).`)})):e.content.scrollToItem(s,this.scrollToTop?"start":void 0).then((()=>t(i))).catch((e=>{console.error(`Fail to scroll to cell to display the required heading (${e}).`)}))}},l=e=>{n.getCellHeadings(e).forEach((async e=>{var t,n;const i=await _e(e,this.parser,this.sanitizer),s=i?`h${e.level}[id="${CSS.escape(i)}"]`:`h${e.level}`;void 0!==e.outputIndex?o.set(e,V.TableOfContentsUtils.addPrefix(e.cellRef.outputArea.widgets[e.outputIndex].node,s,null!==(t=e.prefix)&&void 0!==t?t:"")):o.set(e,V.TableOfContentsUtils.addPrefix(e.cellRef.node,s,null!==(n=e.prefix)&&void 0!==n?n:""))}))},a=t=>{this.parser&&(V.TableOfContentsUtils.clearNumbering(e.content.node),o=new WeakMap,e.content.widgets.forEach((e=>{l(e)})))},d=(t,o)=>{var s,l,a,d;if(n.configuration.syncCollapseState)if(null!==o){const e=o.cellRef;e.headingCollapsed!==(null!==(s=o.collapsed)&&void 0!==s&&s)&&(e.headingCollapsed=null!==(l=o.collapsed)&&void 0!==l&&l)}else{const t=null!==(d=null===(a=n.headings[0])||void 0===a?void 0:a.collapsed)&&void 0!==d&&d;e.content.widgets.forEach((e=>{e instanceof i.MarkdownCell&&e.headingInfo.level>=0&&(e.headingCollapsed=t)}))}},r=(e,t)=>{if(n.configuration.syncCollapseState){const e=n.getCellHeadings(t)[0];e&&n.toggleCollapse({heading:e,collapsed:t.headingCollapsed})}},c=(e,t)=>{t.inViewport?l(t):V.TableOfContentsUtils.clearNumbering(t.node)};return e.context.ready.then((()=>{a(),n.activeHeadingChanged.connect(s),n.headingsChanged.connect(a),n.collapseChanged.connect(d),e.content.cellCollapsed.connect(r),e.content.cellInViewportChanged.connect(c),e.disposed.connect((()=>{n.activeHeadingChanged.disconnect(s),n.headingsChanged.disconnect(a),n.collapseChanged.disconnect(d),e.content.cellCollapsed.disconnect(r),e.content.cellInViewportChanged.disconnect(c)}))})),n}}async function _e(e,t,n){let o=null;return e.type===i.Cell.HeadingType.Markdown?o=await V.TableOfContentsUtils.Markdown.getHeadingId(t,e.raw,e.level,n):e.type===i.Cell.HeadingType.HTML&&(o=e.id),o}const fe=new d.Token("@jupyterlab/notebook:INotebookWidgetFactory","A service to create the notebook viewer."),we=new d.Token("@jupyterlab/notebook:INotebookTools",'A service for the "Notebook Tools" panel in the\n  right sidebar. Use this to add your own functionality to the panel.'),ye=new d.Token("@jupyterlab/notebook:INotebookTracker","A widget tracker for notebooks.\n  Use this if you want to be able to iterate over and interact with notebooks\n  created by the application.");class be extends o.WidgetTracker{constructor(){super(...arguments),this._activeCell=null,this._activeCellChanged=new r.Signal(this),this._selectionChanged=new r.Signal(this)}get activeCell(){const e=this.currentWidget;return e&&e.content.activeCell||null}get activeCellChanged(){return this._activeCellChanged}get selectionChanged(){return this._selectionChanged}add(e){const t=super.add(e);return e.content.activeCellChanged.connect(this._onActiveCellChanged,this),e.content.selectionChanged.connect(this._onSelectionChanged,this),t}dispose(){this._activeCell=null,super.dispose()}onCurrentChanged(e){const t=this.activeCell;t&&t===this._activeCell||(this._activeCell=t,e&&this._activeCellChanged.emit(e.content.activeCell||null))}_onActiveCellChanged(e,t){this.currentWidget&&this.currentWidget.content===e&&(this._activeCell=t||null,this._activeCellChanged.emit(this._activeCell))}_onSelectionChanged(e){this.currentWidget&&this.currentWidget.content===e&&this._selectionChanged.emit(void 0)}}function ke(e){return e.allCellsTrusted?h().createElement(_.trustedIcon.react,{top:"2px",stylesheet:"statusBar"}):h().createElement(_.notTrustedIcon.react,{top:"2px",stylesheet:"statusBar"})}class Se extends _.VDomRenderer{constructor(e){super(new Se.Model),this.translator=e||l.nullTranslator,this.node.classList.add("jp-StatusItem-trust")}render(){if(!this.model)return null;const e=function(e,t){const n=(t=t||l.nullTranslator).load("jupyterlab");return e.trustedCells===e.totalCells?n.__("Notebook trusted: %1 of %2 code cells trusted.",e.trustedCells,e.totalCells):e.activeCellTrusted?n.__("Active cell trusted: %1 of %2 code cells trusted.",e.trustedCells,e.totalCells):n.__("Notebook not trusted: %1 of %2 code cells trusted.",e.trustedCells,e.totalCells)}(this.model,this.translator);return e!==this.node.title&&(this.node.title=e),h().createElement(ke,{allCellsTrusted:this.model.trustedCells===this.model.totalCells,activeCellTrusted:this.model.activeCellTrusted,totalCells:this.model.totalCells,trustedCells:this.model.trustedCells})}}!function(e){class t extends _.VDomModel{constructor(){super(...arguments),this._trustedCells=0,this._totalCells=0,this._activeCellTrusted=!1,this._notebook=null}get trustedCells(){return this._trustedCells}get totalCells(){return this._totalCells}get activeCellTrusted(){return this._activeCellTrusted}get notebook(){return this._notebook}set notebook(e){const t=this._notebook;null!==t&&(t.activeCellChanged.disconnect(this._onActiveCellChanged,this),t.modelContentChanged.disconnect(this._onModelChanged,this));const n=this._getAllState();if(this._notebook=e,null===this._notebook)this._trustedCells=0,this._totalCells=0,this._activeCellTrusted=!1;else{this._notebook.activeCellChanged.connect(this._onActiveCellChanged,this),this._notebook.modelContentChanged.connect(this._onModelChanged,this),this._notebook.activeCell?this._activeCellTrusted=this._notebook.activeCell.model.trusted:this._activeCellTrusted=!1;const{total:e,trusted:t}=this._deriveCellTrustState(this._notebook.model);this._totalCells=e,this._trustedCells=t}this._triggerChange(n,this._getAllState())}_onModelChanged(e){const t=this._getAllState(),{total:n,trusted:o}=this._deriveCellTrustState(e.model);this._totalCells=n,this._trustedCells=o,this._triggerChange(t,this._getAllState())}_onActiveCellChanged(e,t){const n=this._getAllState();this._activeCellTrusted=!!t&&t.model.trusted,this._triggerChange(n,this._getAllState())}_deriveCellTrustState(e){if(null===e)return{total:0,trusted:0};let t=0,n=0;for(const o of e.cells)"code"===o.type&&(t++,o.trusted&&n++);return{total:t,trusted:n}}_getAllState(){return[this._trustedCells,this._totalCells,this.activeCellTrusted]}_triggerChange(e,t){e[0]===t[0]&&e[1]===t[1]&&e[2]===t[2]||this.stateChanged.emit(void 0)}}e.Model=t}(Se||(Se={}));class xe{constructor(e){this._requestBatchSize=10,this._cursor=0,this._hasSession=!1,this._history=[],this._placeholder="",this._kernelSession="",this._setByHistory=!1,this._isDisposed=!1,this._editor=null,this._filtered=[],this._kernel=null,this._sessionContext=e.sessionContext,this._trans=(e.translator||l.nullTranslator).load("jupyterlab"),this._handleKernel().then((()=>{this._sessionContext.kernelChanged.connect(this._handleKernel,this)})),this._toRequest=this._requestBatchSize}get editor(){return this._editor}set editor(e){if(this._editor===e)return;const t=this._editor;t&&t.model.sharedModel.changed.disconnect(this.onTextChange,this),this._editor=e,e&&e.model.sharedModel.changed.connect(this.onTextChange,this)}get placeholder(){return this._placeholder}get kernelSession(){return this._kernelSession}get isDisposed(){return this._isDisposed}dispose(){this._isDisposed=!0,this._history.length=0,r.Signal.clearData(this)}async checkSession(e){var t;this._hasSession||(await this._retrieveHistory(),this._hasSession=!0,this.editor=e.editor,this._placeholder=(null===(t=this._editor)||void 0===t?void 0:t.model.sharedModel.getSource())||"",this.setFilter(this._placeholder),this._cursor=this._filtered.length-1)}async back(e){return await this.checkSession(e),--this._cursor,this._cursor<0&&await this.fetchBatch(),this._cursor=Math.max(0,this._cursor),this._filtered[this._cursor]}async forward(e){return await this.checkSession(e),++this._cursor,this._cursor=Math.min(this._filtered.length-1,this._cursor),this._filtered[this._cursor]}updateEditor(e,t){var n,o;if(e){const i=null===(n=e.editor)||void 0===n?void 0:n.model,s=null==i?void 0:i.sharedModel.getSource();if(this.isDisposed||!t)return;if(s===t)return;this._setByHistory=!0,null==i||i.sharedModel.setSource(t);let l=0;l=t.indexOf("\n"),l<0&&(l=t.length),null===(o=e.editor)||void 0===o||o.setCursorPosition({line:0,column:l})}}reset(){this._hasSession=!1,this._placeholder="",this._toRequest=this._requestBatchSize}async fetchBatch(){this._toRequest+=this._requestBatchSize;let e=this._filtered.slice().reverse(),t=this._history.slice();await this._retrieveHistory().then((()=>{this.setFilter(this._placeholder);let t=0,n=this._filtered.slice().reverse();for(let o=0;o<e.length;o++){let i=e[o];for(let e=o+t;e<n.length&&i!==n[e];e++)t+=1}this._cursor=this._filtered.length-(e.length+1)-t})),this._cursor<0&&this._history.length>t.length&&await this.fetchBatch()}onHistory(e,t){this._history.length=0;let n=["","",""],o=["","",""],i="";if("ok"===e.content.status){for(let t=0;t<e.content.history.length;t++)o=e.content.history[t],o!==n&&(i=e.content.history[t][0],this._history.push(n=o));this.kernelSession||o[2]==(null==t?void 0:t.model.sharedModel.getSource())&&(this._kernelSession=i)}}onTextChange(){this._setByHistory?this._setByHistory=!1:this.reset()}async _handleKernel(){var e;this._kernel=null===(e=this._sessionContext.session)||void 0===e?void 0:e.kernel,this._kernel?await this._retrieveHistory().catch():this._history.length=0}async _retrieveHistory(e){var t,n;return await(null===(t=this._kernel)||void 0===t?void 0:t.requestHistory((n=this._toRequest,{output:!1,raw:!0,hist_access_type:"tail",n})).then((t=>{this.onHistory(t,e)})).catch((()=>{console.warn(this._trans.__("History was unable to be retrieved"))})))}setFilter(e=""){this._filtered.length=0;let t="",n="";for(let o=0;o<this._history.length;o++)n=this._history[o][2],n!==t&&e!==n&&this._filtered.push(t=n);this._filtered.push(e)}}class Me extends F.ABCWidgetFactory{constructor(e){super(e),this.rendermime=e.rendermime,this.contentFactory=e.contentFactory,this.mimeTypeService=e.mimeTypeService,this._editorConfig=e.editorConfig||re.defaultEditorConfig,this._notebookConfig=e.notebookConfig||re.defaultNotebookConfig}get editorConfig(){return this._editorConfig}set editorConfig(e){this._editorConfig=e}get notebookConfig(){return this._notebookConfig}set notebookConfig(e){this._notebookConfig=e}createNewWidget(e,t){const n=e.translator,o=new xe({sessionContext:e.sessionContext,translator:n}),i={rendermime:t?t.content.rendermime:this.rendermime.clone({resolver:e.urlResolver}),contentFactory:this.contentFactory,mimeTypeService:this.mimeTypeService,editorConfig:t?t.content.editorConfig:this._editorConfig,notebookConfig:t?t.content.notebookConfig:this._notebookConfig,translator:n,kernelHistory:o},s=this.contentFactory.createNotebook(i);return new ue({context:e,content:s})}}}}]);
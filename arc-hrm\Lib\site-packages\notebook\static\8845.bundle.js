/*! For license information please see 8845.bundle.js.LICENSE.txt */
"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[8845],{78845:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.AllPackages=void 0,r(32742),r(70049),r(64296),r(53974),r(39613),r(51629),r(43995),r(21901),r(61767),r(99341),r(61961),r(80449),r(83370),r(65744),r(53948),r(45463),r(17915),r(74927),r(48689),r(966),r(73414),r(33289),r(37515),r(92633),r(31664),r(69553),r(22294),r(77906),r(67689),r(54687),r(63892),r(36229),r(95729),"undefined"!=typeof MathJax&&MathJax.loader&&MathJax.loader.preLoad("[tex]/action","[tex]/ams","[tex]/amscd","[tex]/bbox","[tex]/boldsymbol","[tex]/braket","[tex]/bussproofs","[tex]/cancel","[tex]/cases","[tex]/centernot","[tex]/color","[tex]/colorv2","[tex]/colortbl","[tex]/empheq","[tex]/enclose","[tex]/extpfeil","[tex]/gensymb","[tex]/html","[tex]/mathtools","[tex]/mhchem","[tex]/newcommand","[tex]/noerrors","[tex]/noundefined","[tex]/physics","[tex]/upgreek","[tex]/unicode","[tex]/verb","[tex]/configmacros","[tex]/tagformat","[tex]/textcomp","[tex]/textmacros","[tex]/setoptions"),e.AllPackages=["base","action","ams","amscd","bbox","boldsymbol","braket","bussproofs","cancel","cases","centernot","color","colortbl","empheq","enclose","extpfeil","gensymb","html","mathtools","mhchem","newcommand","noerrors","noundefined","upgreek","unicode","verb","configmacros","tagformat","textcomp","textmacros"]},70049:function(t,e,r){var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.ActionConfiguration=e.ActionMethods=void 0;var o=r(63401),a=n(r(94032)),i=r(65695),s=n(r(76914));e.ActionMethods={},e.ActionMethods.Macro=s.default.Macro,e.ActionMethods.Toggle=function(t,e){for(var r,n=[];"\\endtoggle"!==(r=t.GetArgument(e));)n.push(new a.default(r,t.stack.env,t.configuration).mml());t.Push(t.create("node","maction",n,{actiontype:"toggle"}))},e.ActionMethods.Mathtip=function(t,e){var r=t.ParseArg(e),n=t.ParseArg(e);t.Push(t.create("node","maction",[r,n],{actiontype:"tooltip"}))},new i.CommandMap("action-macros",{toggle:"Toggle",mathtip:"Mathtip",texttip:["Macro","\\mathtip{#1}{\\text{#2}}",2]},e.ActionMethods),e.ActionConfiguration=o.Configuration.create("action",{handler:{macro:["action-macros"]}})},64296:function(t,e,r){var n,o,a=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.AmsConfiguration=e.AmsTags=void 0;var i=r(63401),s=r(75377),l=r(75723),c=r(14880);r(27798);var u=r(65695),d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e}(l.AbstractTags);e.AmsTags=d,e.AmsConfiguration=i.Configuration.create("ams",{handler:{character:["AMSmath-operatorLetter"],delimiter:["AMSsymbols-delimiter","AMSmath-delimiter"],macro:["AMSsymbols-mathchar0mi","AMSsymbols-mathchar0mo","AMSsymbols-delimiter","AMSsymbols-macros","AMSmath-mathchar0mo","AMSmath-macros","AMSmath-delimiter"],environment:["AMSmath-environment"]},items:(o={},o[s.MultlineItem.prototype.kind]=s.MultlineItem,o[s.FlalignItem.prototype.kind]=s.FlalignItem,o),tags:{ams:d},init:function(t){new u.CommandMap(c.NEW_OPS,{},{}),t.append(i.Configuration.local({handler:{macro:[c.NEW_OPS]},priority:-1}))},config:function(t,e){e.parseOptions.options.multlineWidth&&(e.parseOptions.options.ams.multlineWidth=e.parseOptions.options.multlineWidth),delete e.parseOptions.options.multlineWidth},options:{multlineWidth:"",ams:{multlineWidth:"100%",multlineIndent:"1em"}}})},75377:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),a=this&&this.__assign||function(){return a=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},a.apply(this,arguments)},i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.FlalignItem=e.MultlineItem=void 0;var s=r(31201),l=i(r(55038)),c=i(r(53972)),u=i(r(54420)),d=r(28027),p=function(t){function e(e){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];var o=t.call(this,e)||this;return o.factory.configuration.tags.start("multline",!0,r[0]),o}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"multline"},enumerable:!1,configurable:!0}),e.prototype.EndEntry=function(){this.table.length&&l.default.fixInitialMO(this.factory.configuration,this.nodes);var t=this.getProperty("shove"),e=this.create("node","mtd",this.nodes,t?{columnalign:t}:{});this.setProperty("shove",null),this.row.push(e),this.Clear()},e.prototype.EndRow=function(){if(1!==this.row.length)throw new u.default("MultlineRowsOneCol","The rows within the %1 environment must have exactly one column","multline");var t=this.create("node","mtr",this.row);this.table.push(t),this.row=[]},e.prototype.EndTable=function(){if(t.prototype.EndTable.call(this),this.table.length){var e=this.table.length-1,r=-1;c.default.getAttribute(c.default.getChildren(this.table[0])[0],"columnalign")||c.default.setAttribute(c.default.getChildren(this.table[0])[0],"columnalign",d.TexConstant.Align.LEFT),c.default.getAttribute(c.default.getChildren(this.table[e])[0],"columnalign")||c.default.setAttribute(c.default.getChildren(this.table[e])[0],"columnalign",d.TexConstant.Align.RIGHT);var n=this.factory.configuration.tags.getTag();if(n){r=this.arraydef.side===d.TexConstant.Align.LEFT?0:this.table.length-1;var o=this.table[r],a=this.create("node","mlabeledtr",[n].concat(c.default.getChildren(o)));c.default.copyAttributes(o,a),this.table[r]=a}}this.factory.configuration.tags.end()},e}(s.ArrayItem);e.MultlineItem=p;var f=function(t){function e(e,r,n,o,a){var i=t.call(this,e)||this;return i.name=r,i.numbered=n,i.padded=o,i.center=a,i.factory.configuration.tags.start(r,n,n),i}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"flalign"},enumerable:!1,configurable:!0}),e.prototype.EndEntry=function(){t.prototype.EndEntry.call(this);var e=this.getProperty("xalignat");if(e&&this.row.length>e)throw new u.default("XalignOverflow","Extra %1 in row of %2","&",this.name)},e.prototype.EndRow=function(){for(var e,r=this.row,n=this.getProperty("xalignat");r.length<n;)r.push(this.create("node","mtd"));for(this.row=[],this.padded&&this.row.push(this.create("node","mtd"));e=r.shift();)this.row.push(e),(e=r.shift())&&this.row.push(e),(r.length||this.padded)&&this.row.push(this.create("node","mtd"));this.row.length>this.maxrow&&(this.maxrow=this.row.length),t.prototype.EndRow.call(this);var o=this.table[this.table.length-1];if(this.getProperty("zeroWidthLabel")&&o.isKind("mlabeledtr")){var i=c.default.getChildren(o)[0],s=this.factory.configuration.options.tagSide,l=a({width:0},"right"===s?{lspace:"-1width"}:{}),u=this.create("node","mpadded",c.default.getChildren(i),l);i.setChildren([u])}},e.prototype.EndTable=function(){t.prototype.EndTable.call(this),this.center&&this.maxrow<=2&&(delete this.arraydef.width,delete this.global.indentalign)},e}(s.EqnArrayItem);e.FlalignItem=f},27798:function(t,e,r){var n=this&&this.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(e,r);o&&!("get"in o?!e.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,n,o)}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),o=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),a=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)"default"!==r&&Object.prototype.hasOwnProperty.call(t,r)&&n(e,t,r);return o(e,t),e},i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var s=r(14880),l=a(r(65695)),c=r(28027),u=i(r(2362)),d=i(r(55038)),p=r(83045),f=r(56780);new l.CharacterMap("AMSmath-mathchar0mo",u.default.mathchar0mo,{iiiint:["⨌",{texClass:p.TEXCLASS.OP}]}),new l.RegExpMap("AMSmath-operatorLetter",s.AmsMethods.operatorLetter,/[-*]/i),new l.CommandMap("AMSmath-macros",{mathring:["Accent","02DA"],nobreakspace:"Tilde",negmedspace:["Spacer",f.MATHSPACE.negativemediummathspace],negthickspace:["Spacer",f.MATHSPACE.negativethickmathspace],idotsint:["MultiIntegral","\\int\\cdots\\int"],dddot:["Accent","20DB"],ddddot:["Accent","20DC"],sideset:"SideSet",boxed:["Macro","\\fbox{$\\displaystyle{#1}$}",1],tag:"HandleTag",notag:"HandleNoTag",eqref:["HandleRef",!0],substack:["Macro","\\begin{subarray}{c}#1\\end{subarray}",1],injlim:["NamedOp","inj&thinsp;lim"],projlim:["NamedOp","proj&thinsp;lim"],varliminf:["Macro","\\mathop{\\underline{\\mmlToken{mi}{lim}}}"],varlimsup:["Macro","\\mathop{\\overline{\\mmlToken{mi}{lim}}}"],varinjlim:["Macro","\\mathop{\\underrightarrow{\\mmlToken{mi}{lim}}}"],varprojlim:["Macro","\\mathop{\\underleftarrow{\\mmlToken{mi}{lim}}}"],DeclareMathOperator:"HandleDeclareOp",operatorname:"HandleOperatorName",genfrac:"Genfrac",frac:["Genfrac","","","",""],tfrac:["Genfrac","","","","1"],dfrac:["Genfrac","","","","0"],binom:["Genfrac","(",")","0",""],tbinom:["Genfrac","(",")","0","1"],dbinom:["Genfrac","(",")","0","0"],cfrac:"CFrac",shoveleft:["HandleShove",c.TexConstant.Align.LEFT],shoveright:["HandleShove",c.TexConstant.Align.RIGHT],xrightarrow:["xArrow",8594,5,10],xleftarrow:["xArrow",8592,10,5]},s.AmsMethods),new l.EnvironmentMap("AMSmath-environment",u.default.environment,{"equation*":["Equation",null,!1],"eqnarray*":["EqnArray",null,!1,!0,"rcl",d.default.cols(0,f.MATHSPACE.thickmathspace),".5em"],align:["EqnArray",null,!0,!0,"rl",d.default.cols(0,2)],"align*":["EqnArray",null,!1,!0,"rl",d.default.cols(0,2)],multline:["Multline",null,!0],"multline*":["Multline",null,!1],split:["EqnArray",null,!1,!1,"rl",d.default.cols(0)],gather:["EqnArray",null,!0,!0,"c"],"gather*":["EqnArray",null,!1,!0,"c"],alignat:["AlignAt",null,!0,!0],"alignat*":["AlignAt",null,!1,!0],alignedat:["AlignAt",null,!1,!1],aligned:["AmsEqnArray",null,null,null,"rl",d.default.cols(0,2),".5em","D"],gathered:["AmsEqnArray",null,null,null,"c",null,".5em","D"],xalignat:["XalignAt",null,!0,!0],"xalignat*":["XalignAt",null,!1,!0],xxalignat:["XalignAt",null,!1,!1],flalign:["FlalignArray",null,!0,!1,!0,"rlc","auto auto fit"],"flalign*":["FlalignArray",null,!1,!1,!0,"rlc","auto auto fit"],subarray:["Array",null,null,null,null,d.default.cols(0),"0.1em","S",1],smallmatrix:["Array",null,null,null,"c",d.default.cols(1/3),".2em","S",1],matrix:["Array",null,null,null,"c"],pmatrix:["Array",null,"(",")","c"],bmatrix:["Array",null,"[","]","c"],Bmatrix:["Array",null,"\\{","\\}","c"],vmatrix:["Array",null,"\\vert","\\vert","c"],Vmatrix:["Array",null,"\\Vert","\\Vert","c"],cases:["Array",null,"\\{",".","ll",null,".2em","T"]},s.AmsMethods),new l.DelimiterMap("AMSmath-delimiter",u.default.delimiter,{"\\lvert":["|",{texClass:p.TEXCLASS.OPEN}],"\\rvert":["|",{texClass:p.TEXCLASS.CLOSE}],"\\lVert":["‖",{texClass:p.TEXCLASS.OPEN}],"\\rVert":["‖",{texClass:p.TEXCLASS.CLOSE}]}),new l.CharacterMap("AMSsymbols-mathchar0mi",u.default.mathchar0mi,{digamma:"ϝ",varkappa:"ϰ",varGamma:["Γ",{mathvariant:c.TexConstant.Variant.ITALIC}],varDelta:["Δ",{mathvariant:c.TexConstant.Variant.ITALIC}],varTheta:["Θ",{mathvariant:c.TexConstant.Variant.ITALIC}],varLambda:["Λ",{mathvariant:c.TexConstant.Variant.ITALIC}],varXi:["Ξ",{mathvariant:c.TexConstant.Variant.ITALIC}],varPi:["Π",{mathvariant:c.TexConstant.Variant.ITALIC}],varSigma:["Σ",{mathvariant:c.TexConstant.Variant.ITALIC}],varUpsilon:["Υ",{mathvariant:c.TexConstant.Variant.ITALIC}],varPhi:["Φ",{mathvariant:c.TexConstant.Variant.ITALIC}],varPsi:["Ψ",{mathvariant:c.TexConstant.Variant.ITALIC}],varOmega:["Ω",{mathvariant:c.TexConstant.Variant.ITALIC}],beth:"ℶ",gimel:"ℷ",daleth:"ℸ",backprime:["‵",{variantForm:!0}],hslash:"ℏ",varnothing:["∅",{variantForm:!0}],blacktriangle:"▴",triangledown:["▽",{variantForm:!0}],blacktriangledown:"▾",square:"◻",Box:"◻",blacksquare:"◼",lozenge:"◊",Diamond:"◊",blacklozenge:"⧫",circledS:["Ⓢ",{mathvariant:c.TexConstant.Variant.NORMAL}],bigstar:"★",sphericalangle:"∢",measuredangle:"∡",nexists:"∄",complement:"∁",mho:"℧",eth:["ð",{mathvariant:c.TexConstant.Variant.NORMAL}],Finv:"Ⅎ",diagup:"╱",Game:"⅁",diagdown:"╲",Bbbk:["k",{mathvariant:c.TexConstant.Variant.DOUBLESTRUCK}],yen:"¥",circledR:"®",checkmark:"✓",maltese:"✠"}),new l.CharacterMap("AMSsymbols-mathchar0mo",u.default.mathchar0mo,{dotplus:"∔",ltimes:"⋉",smallsetminus:["∖",{variantForm:!0}],rtimes:"⋊",Cap:"⋒",doublecap:"⋒",leftthreetimes:"⋋",Cup:"⋓",doublecup:"⋓",rightthreetimes:"⋌",barwedge:"⊼",curlywedge:"⋏",veebar:"⊻",curlyvee:"⋎",doublebarwedge:"⩞",boxminus:"⊟",circleddash:"⊝",boxtimes:"⊠",circledast:"⊛",boxdot:"⊡",circledcirc:"⊚",boxplus:"⊞",centerdot:["⋅",{variantForm:!0}],divideontimes:"⋇",intercal:"⊺",leqq:"≦",geqq:"≧",leqslant:"⩽",geqslant:"⩾",eqslantless:"⪕",eqslantgtr:"⪖",lesssim:"≲",gtrsim:"≳",lessapprox:"⪅",gtrapprox:"⪆",approxeq:"≊",lessdot:"⋖",gtrdot:"⋗",lll:"⋘",llless:"⋘",ggg:"⋙",gggtr:"⋙",lessgtr:"≶",gtrless:"≷",lesseqgtr:"⋚",gtreqless:"⋛",lesseqqgtr:"⪋",gtreqqless:"⪌",doteqdot:"≑",Doteq:"≑",eqcirc:"≖",risingdotseq:"≓",circeq:"≗",fallingdotseq:"≒",triangleq:"≜",backsim:"∽",thicksim:["∼",{variantForm:!0}],backsimeq:"⋍",thickapprox:["≈",{variantForm:!0}],subseteqq:"⫅",supseteqq:"⫆",Subset:"⋐",Supset:"⋑",sqsubset:"⊏",sqsupset:"⊐",preccurlyeq:"≼",succcurlyeq:"≽",curlyeqprec:"⋞",curlyeqsucc:"⋟",precsim:"≾",succsim:"≿",precapprox:"⪷",succapprox:"⪸",vartriangleleft:"⊲",lhd:"⊲",vartriangleright:"⊳",rhd:"⊳",trianglelefteq:"⊴",unlhd:"⊴",trianglerighteq:"⊵",unrhd:"⊵",vDash:["⊨",{variantForm:!0}],Vdash:"⊩",Vvdash:"⊪",smallsmile:["⌣",{variantForm:!0}],shortmid:["∣",{variantForm:!0}],smallfrown:["⌢",{variantForm:!0}],shortparallel:["∥",{variantForm:!0}],bumpeq:"≏",between:"≬",Bumpeq:"≎",pitchfork:"⋔",varpropto:["∝",{variantForm:!0}],backepsilon:"∍",blacktriangleleft:"◂",blacktriangleright:"▸",therefore:"∴",because:"∵",eqsim:"≂",vartriangle:["△",{variantForm:!0}],Join:"⋈",nless:"≮",ngtr:"≯",nleq:"≰",ngeq:"≱",nleqslant:["⪇",{variantForm:!0}],ngeqslant:["⪈",{variantForm:!0}],nleqq:["≰",{variantForm:!0}],ngeqq:["≱",{variantForm:!0}],lneq:"⪇",gneq:"⪈",lneqq:"≨",gneqq:"≩",lvertneqq:["≨",{variantForm:!0}],gvertneqq:["≩",{variantForm:!0}],lnsim:"⋦",gnsim:"⋧",lnapprox:"⪉",gnapprox:"⪊",nprec:"⊀",nsucc:"⊁",npreceq:["⋠",{variantForm:!0}],nsucceq:["⋡",{variantForm:!0}],precneqq:"⪵",succneqq:"⪶",precnsim:"⋨",succnsim:"⋩",precnapprox:"⪹",succnapprox:"⪺",nsim:"≁",ncong:"≇",nshortmid:["∤",{variantForm:!0}],nshortparallel:["∦",{variantForm:!0}],nmid:"∤",nparallel:"∦",nvdash:"⊬",nvDash:"⊭",nVdash:"⊮",nVDash:"⊯",ntriangleleft:"⋪",ntriangleright:"⋫",ntrianglelefteq:"⋬",ntrianglerighteq:"⋭",nsubseteq:"⊈",nsupseteq:"⊉",nsubseteqq:["⊈",{variantForm:!0}],nsupseteqq:["⊉",{variantForm:!0}],subsetneq:"⊊",supsetneq:"⊋",varsubsetneq:["⊊",{variantForm:!0}],varsupsetneq:["⊋",{variantForm:!0}],subsetneqq:"⫋",supsetneqq:"⫌",varsubsetneqq:["⫋",{variantForm:!0}],varsupsetneqq:["⫌",{variantForm:!0}],leftleftarrows:"⇇",rightrightarrows:"⇉",leftrightarrows:"⇆",rightleftarrows:"⇄",Lleftarrow:"⇚",Rrightarrow:"⇛",twoheadleftarrow:"↞",twoheadrightarrow:"↠",leftarrowtail:"↢",rightarrowtail:"↣",looparrowleft:"↫",looparrowright:"↬",leftrightharpoons:"⇋",rightleftharpoons:["⇌",{variantForm:!0}],curvearrowleft:"↶",curvearrowright:"↷",circlearrowleft:"↺",circlearrowright:"↻",Lsh:"↰",Rsh:"↱",upuparrows:"⇈",downdownarrows:"⇊",upharpoonleft:"↿",upharpoonright:"↾",downharpoonleft:"⇃",restriction:"↾",multimap:"⊸",downharpoonright:"⇂",leftrightsquigarrow:"↭",rightsquigarrow:"⇝",leadsto:"⇝",dashrightarrow:"⇢",dashleftarrow:"⇠",nleftarrow:"↚",nrightarrow:"↛",nLeftarrow:"⇍",nRightarrow:"⇏",nleftrightarrow:"↮",nLeftrightarrow:"⇎"}),new l.DelimiterMap("AMSsymbols-delimiter",u.default.delimiter,{"\\ulcorner":"⌜","\\urcorner":"⌝","\\llcorner":"⌞","\\lrcorner":"⌟"}),new l.CommandMap("AMSsymbols-macros",{implies:["Macro","\\;\\Longrightarrow\\;"],impliedby:["Macro","\\;\\Longleftarrow\\;"]},s.AmsMethods)},14880:function(t,e,r){var n=this&&this.__assign||function(){return n=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},n.apply(this,arguments)},o=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,a=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=a.next()).done;)i.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return i},a=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.NEW_OPS=e.AmsMethods=void 0;var i=a(r(55038)),s=a(r(2362)),l=a(r(53972)),c=r(28027),u=a(r(94032)),d=a(r(54420)),p=r(75735),f=a(r(76914)),m=r(83045);function h(t){if(!t||t.isInferred&&0===t.childNodes.length)return[null,null];if(t.isKind("msubsup")&&g(t))return[t,null];var e=l.default.getChildAt(t,0);return t.isInferred&&e&&g(e)?(t.childNodes.splice(0,1),[e,t]):[null,t]}function g(t){var e=t.childNodes[0];return e&&e.isKind("mi")&&""===e.getText()}e.AmsMethods={},e.AmsMethods.AmsEqnArray=function(t,e,r,n,o,a,s){var l=t.GetBrackets("\\begin{"+e.getName()+"}"),c=f.default.EqnArray(t,e,r,n,o,a,s);return i.default.setArrayAlign(c,l)},e.AmsMethods.AlignAt=function(t,r,n,o){var a,s,l=r.getName(),c="",u=[];if(o||(s=t.GetBrackets("\\begin{"+l+"}")),(a=t.GetArgument("\\begin{"+l+"}")).match(/[^0-9]/))throw new d.default("PositiveIntegerArg","Argument to %1 must me a positive integer","\\begin{"+l+"}");for(var p=parseInt(a,10);p>0;)c+="rl",u.push("0em 0em"),p--;var f=u.join(" ");if(o)return e.AmsMethods.EqnArray(t,r,n,o,c,f);var m=e.AmsMethods.EqnArray(t,r,n,o,c,f);return i.default.setArrayAlign(m,s)},e.AmsMethods.Multline=function(t,e,r){t.Push(e),i.default.checkEqnEnv(t);var n=t.itemFactory.create("multline",r,t.stack);return n.arraydef={displaystyle:!0,rowspacing:".5em",columnspacing:"100%",width:t.options.ams.multlineWidth,side:t.options.tagSide,minlabelspacing:t.options.tagIndent,framespacing:t.options.ams.multlineIndent+" 0",frame:"","data-width-includes-label":!0},n},e.AmsMethods.XalignAt=function(t,r,n,o){var a=t.GetArgument("\\begin{"+r.getName()+"}");if(a.match(/[^0-9]/))throw new d.default("PositiveIntegerArg","Argument to %1 must me a positive integer","\\begin{"+r.getName()+"}");var i=o?"crl":"rlc",s=o?"fit auto auto":"auto auto fit",l=e.AmsMethods.FlalignArray(t,r,n,o,!1,i,s,!0);return l.setProperty("xalignat",2*parseInt(a)),l},e.AmsMethods.FlalignArray=function(t,e,r,n,o,a,s,l){void 0===l&&(l=!1),t.Push(e),i.default.checkEqnEnv(t),a=a.split("").join(" ").replace(/r/g,"right").replace(/l/g,"left").replace(/c/g,"center");var c=t.itemFactory.create("flalign",e.getName(),r,n,o,t.stack);return c.arraydef={width:"100%",displaystyle:!0,columnalign:a,columnspacing:"0em",columnwidth:s,rowspacing:"3pt",side:t.options.tagSide,minlabelspacing:l?"0":t.options.tagIndent,"data-width-includes-label":!0},c.setProperty("zeroWidthLabel",l),c},e.NEW_OPS="ams-declare-ops",e.AmsMethods.HandleDeclareOp=function(t,r){var n=t.GetStar()?"*":"",o=i.default.trimSpaces(t.GetArgument(r));"\\"===o.charAt(0)&&(o=o.substr(1));var a=t.GetArgument(r);t.configuration.handlers.retrieve(e.NEW_OPS).add(o,new p.Macro(o,e.AmsMethods.Macro,["\\operatorname".concat(n,"{").concat(a,"}")]))},e.AmsMethods.HandleOperatorName=function(t,e){var r=t.GetStar(),o=i.default.trimSpaces(t.GetArgument(e)),a=new u.default(o,n(n({},t.stack.env),{font:c.TexConstant.Variant.NORMAL,multiLetterIdentifiers:/^[-*a-z]+/i,operatorLetters:!0}),t.configuration).mml();if(a.isKind("mi")||(a=t.create("node","TeXAtom",[a])),l.default.setProperties(a,{movesupsub:r,movablelimits:!0,texClass:m.TEXCLASS.OP}),!r){var s=t.GetNext(),d=t.i;"\\"===s&&++t.i&&"limits"!==t.GetCS()&&(t.i=d)}t.Push(a)},e.AmsMethods.SideSet=function(t,e){var r=o(h(t.ParseArg(e)),2),n=r[0],a=r[1],s=o(h(t.ParseArg(e)),2),c=s[0],u=s[1],d=t.ParseArg(e),p=d;n&&(a?n.replaceChild(t.create("node","mphantom",[t.create("node","mpadded",[i.default.copyNode(d,t)],{width:0})]),l.default.getChildAt(n,0)):(p=t.create("node","mmultiscripts",[d]),c&&l.default.appendChildren(p,[l.default.getChildAt(c,1)||t.create("node","none"),l.default.getChildAt(c,2)||t.create("node","none")]),l.default.setProperty(p,"scriptalign","left"),l.default.appendChildren(p,[t.create("node","mprescripts"),l.default.getChildAt(n,1)||t.create("node","none"),l.default.getChildAt(n,2)||t.create("node","none")]))),c&&p===d&&(c.replaceChild(d,l.default.getChildAt(c,0)),p=c);var f=t.create("node","TeXAtom",[],{texClass:m.TEXCLASS.OP,movesupsub:!0,movablelimits:!0});a&&(n&&f.appendChild(n),f.appendChild(a)),f.appendChild(p),u&&f.appendChild(u),t.Push(f)},e.AmsMethods.operatorLetter=function(t,e){return!!t.stack.env.operatorLetters&&s.default.variable(t,e)},e.AmsMethods.MultiIntegral=function(t,e,r){var n=t.GetNext();if("\\"===n){var o=t.i;n=t.GetArgument(e),t.i=o,"\\limits"===n&&(r="\\idotsint"===e?"\\!\\!\\mathop{\\,\\,"+r+"}":"\\!\\!\\!\\mathop{\\,\\,\\,"+r+"}")}t.string=r+" "+t.string.slice(t.i),t.i=0},e.AmsMethods.xArrow=function(t,e,r,n,o){var a={width:"+"+i.default.Em((n+o)/18),lspace:i.default.Em(n/18)},s=t.GetBrackets(e),c=t.ParseArg(e),d=t.create("node","mspace",[],{depth:".25em"}),p=t.create("token","mo",{stretchy:!0,texClass:m.TEXCLASS.REL},String.fromCodePoint(r));p=t.create("node","mstyle",[p],{scriptlevel:0});var f=t.create("node","munderover",[p]),h=t.create("node","mpadded",[c,d],a);if(l.default.setAttribute(h,"voffset","-.2em"),l.default.setAttribute(h,"height","-.2em"),l.default.setChild(f,f.over,h),s){var g=new u.default(s,t.stack.env,t.configuration).mml(),v=t.create("node","mspace",[],{height:".75em"});h=t.create("node","mpadded",[g,v],a),l.default.setAttribute(h,"voffset",".15em"),l.default.setAttribute(h,"depth","-.15em"),l.default.setChild(f,f.under,h)}l.default.setProperty(f,"subsupOK",!0),t.Push(f)},e.AmsMethods.HandleShove=function(t,e,r){var n=t.stack.Top();if("multline"!==n.kind)throw new d.default("CommandOnlyAllowedInEnv","%1 only allowed in %2 environment",t.currentCS,"multline");if(n.Size())throw new d.default("CommandAtTheBeginingOfLine","%1 must come at the beginning of the line",t.currentCS);n.setProperty("shove",r)},e.AmsMethods.CFrac=function(t,e){var r=i.default.trimSpaces(t.GetBrackets(e,"")),n=t.GetArgument(e),o=t.GetArgument(e),a={l:c.TexConstant.Align.LEFT,r:c.TexConstant.Align.RIGHT,"":""},s=new u.default("\\strut\\textstyle{"+n+"}",t.stack.env,t.configuration).mml(),p=new u.default("\\strut\\textstyle{"+o+"}",t.stack.env,t.configuration).mml(),f=t.create("node","mfrac",[s,p]);if(null==(r=a[r]))throw new d.default("IllegalAlign","Illegal alignment specified in %1",t.currentCS);r&&l.default.setProperties(f,{numalign:r,denomalign:r}),t.Push(f)},e.AmsMethods.Genfrac=function(t,e,r,n,o,a){null==r&&(r=t.GetDelimiterArg(e)),null==n&&(n=t.GetDelimiterArg(e)),null==o&&(o=t.GetArgument(e)),null==a&&(a=i.default.trimSpaces(t.GetArgument(e)));var s=t.ParseArg(e),c=t.ParseArg(e),u=t.create("node","mfrac",[s,c]);if(""!==o&&l.default.setAttribute(u,"linethickness",o),(r||n)&&(l.default.setProperty(u,"withDelims",!0),u=i.default.fixedFence(t.configuration,r,u,n)),""!==a){var p=parseInt(a,10),f=["D","T","S","SS"][p];if(null==f)throw new d.default("BadMathStyleFor","Bad math style for %1",t.currentCS);u=t.create("node","mstyle",[u]),"D"===f?l.default.setProperties(u,{displaystyle:!0,scriptlevel:0}):l.default.setProperties(u,{displaystyle:!1,scriptlevel:p-1})}t.Push(u)},e.AmsMethods.HandleTag=function(t,e){if(!t.tags.currentTag.taggable&&t.tags.env)throw new d.default("CommandNotAllowedInEnv","%1 not allowed in %2 environment",t.currentCS,t.tags.env);if(t.tags.currentTag.tag)throw new d.default("MultipleCommand","Multiple %1",t.currentCS);var r=t.GetStar(),n=i.default.trimSpaces(t.GetArgument(e));t.tags.tag(n,r)},e.AmsMethods.HandleNoTag=f.default.HandleNoTag,e.AmsMethods.HandleRef=f.default.HandleRef,e.AmsMethods.Macro=f.default.Macro,e.AmsMethods.Accent=f.default.Accent,e.AmsMethods.Tilde=f.default.Tilde,e.AmsMethods.Array=f.default.Array,e.AmsMethods.Spacer=f.default.Spacer,e.AmsMethods.NamedOp=f.default.NamedOp,e.AmsMethods.EqnArray=f.default.EqnArray,e.AmsMethods.Equation=f.default.Equation},53974:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.AmsCdConfiguration=void 0;var n=r(63401);r(52410),e.AmsCdConfiguration=n.Configuration.create("amscd",{handler:{character:["amscd_special"],macro:["amscd_macros"],environment:["amscd_environment"]},options:{amscd:{colspace:"5pt",rowspace:"5pt",harrowsize:"2.75em",varrowsize:"1.75em",hideHorizontalLabels:!1}}})},52410:function(t,e,r){var n=this&&this.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(e,r);o&&!("get"in o?!e.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,n,o)}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),o=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),a=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)"default"!==r&&Object.prototype.hasOwnProperty.call(t,r)&&n(e,t,r);return o(e,t),e},i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var s=a(r(65695)),l=i(r(2362)),c=i(r(61759));new s.EnvironmentMap("amscd_environment",l.default.environment,{CD:"CD"},c.default),new s.CommandMap("amscd_macros",{minCDarrowwidth:"minCDarrowwidth",minCDarrowheight:"minCDarrowheight"},c.default),new s.MacroMap("amscd_special",{"@":"arrow"},c.default)},61759:function(t,e,r){var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var o=n(r(94032)),a=r(32742),i=r(83045),s=n(r(53972)),l={CD:function(t,e){t.Push(e);var r=t.itemFactory.create("array"),n=t.configuration.options.amscd;return r.setProperties({minw:t.stack.env.CD_minw||n.harrowsize,minh:t.stack.env.CD_minh||n.varrowsize}),r.arraydef={columnalign:"center",columnspacing:n.colspace,rowspacing:n.rowspace,displaystyle:!0},r},arrow:function(t,e){var r=t.string.charAt(t.i);if(!r.match(/[><VA.|=]/))return(0,a.Other)(t,e);t.i++;var n=t.stack.Top();n.isKind("array")&&!n.Size()||(l.cell(t,e),n=t.stack.Top());for(var c,u=n,d=u.table.length%2==1,p=(u.row.length+(d?0:1))%2;p;)l.cell(t,e),p--;var f={minsize:u.getProperty("minw"),stretchy:!0},m={minsize:u.getProperty("minh"),stretchy:!0,symmetric:!0,lspace:0,rspace:0};if("."===r);else if("|"===r)c=t.create("token","mo",m,"∥");else if("="===r)c=t.create("token","mo",f,"=");else{var h={">":"→","<":"←",V:"↓",A:"↑"}[r],g=t.GetUpTo(e+r,r),v=t.GetUpTo(e+r,r);if(">"===r||"<"===r){if(c=t.create("token","mo",f,h),g||(g="\\kern "+u.getProperty("minw")),g||v){var y={width:"+.67em",lspace:".33em"};if(c=t.create("node","munderover",[c]),g){var b=new o.default(g,t.stack.env,t.configuration).mml(),x=t.create("node","mpadded",[b],y);s.default.setAttribute(x,"voffset",".1em"),s.default.setChild(c,c.over,x)}if(v){var _=new o.default(v,t.stack.env,t.configuration).mml();s.default.setChild(c,c.under,t.create("node","mpadded",[_],y))}t.configuration.options.amscd.hideHorizontalLabels&&(c=t.create("node","mpadded",c,{depth:0,height:".67em"}))}}else{var w=t.create("token","mo",m,h);c=w,(g||v)&&(c=t.create("node","mrow"),g&&s.default.appendChildren(c,[new o.default("\\scriptstyle\\llap{"+g+"}",t.stack.env,t.configuration).mml()]),w.texClass=i.TEXCLASS.ORD,s.default.appendChildren(c,[w]),v&&s.default.appendChildren(c,[new o.default("\\scriptstyle\\rlap{"+v+"}",t.stack.env,t.configuration).mml()]))}}c&&t.Push(c),l.cell(t,e)},cell:function(t,e){var r=t.stack.Top();(r.table||[]).length%2==0&&0===(r.row||[]).length&&t.Push(t.create("node","mpadded",[],{height:"8.5pt",depth:"2pt"})),t.Push(t.itemFactory.create("cell").setProperties({isEntry:!0,name:e}))},minCDarrowwidth:function(t,e){t.stack.env.CD_minw=t.GetDimen(e)},minCDarrowheight:function(t,e){t.stack.env.CD_minh=t.GetDimen(e)}};e.default=l},39613:function(t,e,r){var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.BboxConfiguration=e.BboxMethods=void 0;var o=r(63401),a=r(65695),i=n(r(54420));e.BboxMethods={},e.BboxMethods.BBox=function(t,e){for(var r,n,o,a=t.GetBrackets(e,""),c=t.ParseArg(e),u=a.split(/,/),d=0,p=u.length;d<p;d++){var f=u[d].trim(),m=f.match(/^(\.\d+|\d+(\.\d*)?)(pt|em|ex|mu|px|in|cm|mm)$/);if(m){if(r)throw new i.default("MultipleBBoxProperty","%1 specified twice in %2","Padding",e);var h=l(m[1]+m[3]);h&&(r={height:"+"+h,depth:"+"+h,lspace:h,width:"+"+2*parseInt(m[1],10)+m[3]})}else if(f.match(/^([a-z0-9]+|\#[0-9a-f]{6}|\#[0-9a-f]{3})$/i)){if(n)throw new i.default("MultipleBBoxProperty","%1 specified twice in %2","Background",e);n=f}else if(f.match(/^[-a-z]+:/i)){if(o)throw new i.default("MultipleBBoxProperty","%1 specified twice in %2","Style",e);o=s(f)}else if(""!==f)throw new i.default("InvalidBBoxProperty",'"%1" doesn\'t look like a color, a padding dimension, or a style',f)}r&&(c=t.create("node","mpadded",[c],r)),(n||o)&&(r={},n&&Object.assign(r,{mathbackground:n}),o&&Object.assign(r,{style:o}),c=t.create("node","mstyle",[c],r)),t.Push(c)};var s=function(t){return t},l=function(t){return t};new a.CommandMap("bbox",{bbox:"BBox"},e.BboxMethods),e.BboxConfiguration=o.Configuration.create("bbox",{handler:{macro:["bbox"]}})},51629:function(t,e,r){var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},o=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.BoldsymbolConfiguration=e.rewriteBoldTokens=e.createBoldToken=e.BoldsymbolMethods=void 0;var a=r(63401),i=o(r(53972)),s=r(28027),l=r(65695),c=r(12443),u={};function d(t,e,r,n){var o=c.NodeFactory.createToken(t,e,r,n);return"mtext"!==e&&t.configuration.parser.stack.env.boldsymbol&&(i.default.setProperty(o,"fixBold",!0),t.configuration.addNode("fixBold",o)),o}function p(t){var e,r;try{for(var o=n(t.data.getList("fixBold")),a=o.next();!a.done;a=o.next()){var l=a.value;if(i.default.getProperty(l,"fixBold")){var c=i.default.getAttribute(l,"mathvariant");null==c?i.default.setAttribute(l,"mathvariant",s.TexConstant.Variant.BOLD):i.default.setAttribute(l,"mathvariant",u[c]||c),i.default.removeProperties(l,"fixBold")}}}catch(t){e={error:t}}finally{try{a&&!a.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}}u[s.TexConstant.Variant.NORMAL]=s.TexConstant.Variant.BOLD,u[s.TexConstant.Variant.ITALIC]=s.TexConstant.Variant.BOLDITALIC,u[s.TexConstant.Variant.FRAKTUR]=s.TexConstant.Variant.BOLDFRAKTUR,u[s.TexConstant.Variant.SCRIPT]=s.TexConstant.Variant.BOLDSCRIPT,u[s.TexConstant.Variant.SANSSERIF]=s.TexConstant.Variant.BOLDSANSSERIF,u["-tex-calligraphic"]="-tex-bold-calligraphic",u["-tex-oldstyle"]="-tex-bold-oldstyle",u["-tex-mathit"]=s.TexConstant.Variant.BOLDITALIC,e.BoldsymbolMethods={},e.BoldsymbolMethods.Boldsymbol=function(t,e){var r=t.stack.env.boldsymbol;t.stack.env.boldsymbol=!0;var n=t.ParseArg(e);t.stack.env.boldsymbol=r,t.Push(n)},new l.CommandMap("boldsymbol",{boldsymbol:"Boldsymbol"},e.BoldsymbolMethods),e.createBoldToken=d,e.rewriteBoldTokens=p,e.BoldsymbolConfiguration=a.Configuration.create("boldsymbol",{handler:{macro:["boldsymbol"]},nodes:{token:d},postprocessors:[p]})},43995:(t,e,r)=>{var n;Object.defineProperty(e,"__esModule",{value:!0}),e.BraketConfiguration=void 0;var o=r(63401),a=r(89238);r(77675),e.BraketConfiguration=o.Configuration.create("braket",{handler:{character:["Braket-characters"],macro:["Braket-macros"]},items:(n={},n[a.BraketItem.prototype.kind]=a.BraketItem,n)})},89238:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),a=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.BraketItem=void 0;var i=r(26539),s=r(83045),l=a(r(55038)),c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"braket"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){return e.isKind("close")?[[this.factory.create("mml",this.toMml())],!0]:e.isKind("mml")?(this.Push(e.toMml()),this.getProperty("single")?[[this.toMml()],!0]:i.BaseItem.fail):t.prototype.checkItem.call(this,e)},e.prototype.toMml=function(){var e=t.prototype.toMml.call(this),r=this.getProperty("open"),n=this.getProperty("close");if(this.getProperty("stretchy"))return l.default.fenced(this.factory.configuration,r,e,n);var o={fence:!0,stretchy:!1,symmetric:!0,texClass:s.TEXCLASS.OPEN},a=this.create("token","mo",o,r);o.texClass=s.TEXCLASS.CLOSE;var i=this.create("token","mo",o,n);return this.create("node","mrow",[a,e,i],{open:r,close:n,texClass:s.TEXCLASS.INNER})},e}(i.BaseItem);e.BraketItem=c},77675:function(t,e,r){var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(65695),a=n(r(55833));new o.CommandMap("Braket-macros",{bra:["Macro","{\\langle {#1} \\vert}",1],ket:["Macro","{\\vert {#1} \\rangle}",1],braket:["Braket","⟨","⟩",!1,1/0],set:["Braket","{","}",!1,1],Bra:["Macro","{\\left\\langle {#1} \\right\\vert}",1],Ket:["Macro","{\\left\\vert {#1} \\right\\rangle}",1],Braket:["Braket","⟨","⟩",!0,1/0],Set:["Braket","{","}",!0,1],ketbra:["Macro","{\\vert {#1} \\rangle\\langle {#2} \\vert}",2],Ketbra:["Macro","{\\left\\vert {#1} \\right\\rangle\\left\\langle {#2} \\right\\vert}",2],"|":"Bar"},a.default),new o.MacroMap("Braket-characters",{"|":"Bar"},a.default)},55833:function(t,e,r){var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var o=n(r(76914)),a=r(83045),i=n(r(54420)),s={};s.Macro=o.default.Macro,s.Braket=function(t,e,r,n,o,a){var s=t.GetNext();if(""===s)throw new i.default("MissingArgFor","Missing argument for %1",t.currentCS);var l=!0;"{"===s&&(t.i++,l=!1),t.Push(t.itemFactory.create("braket").setProperties({barmax:a,barcount:0,open:r,close:n,stretchy:o,single:l}))},s.Bar=function(t,e){var r="|"===e?"|":"∥",n=t.stack.Top();if("braket"!==n.kind||n.getProperty("barcount")>=n.getProperty("barmax")){var o=t.create("token","mo",{texClass:a.TEXCLASS.ORD,stretchy:!1},r);t.Push(o)}else if("|"===r&&"|"===t.GetNext()&&(t.i++,r="∥"),n.getProperty("stretchy")){var i=t.create("node","TeXAtom",[],{texClass:a.TEXCLASS.CLOSE});t.Push(i),n.setProperty("barcount",n.getProperty("barcount")+1),i=t.create("token","mo",{stretchy:!0,braketbar:!0},r),t.Push(i),i=t.create("node","TeXAtom",[],{texClass:a.TEXCLASS.OPEN}),t.Push(i)}else{var s=t.create("token","mo",{stretchy:!1,braketbar:!0},r);t.Push(s)}},e.default=s},21901:(t,e,r)=>{var n;Object.defineProperty(e,"__esModule",{value:!0}),e.BussproofsConfiguration=void 0;var o=r(63401),a=r(14687),i=r(32943);r(74836),e.BussproofsConfiguration=o.Configuration.create("bussproofs",{handler:{macro:["Bussproofs-macros"],environment:["Bussproofs-environments"]},items:(n={},n[a.ProofTreeItem.prototype.kind]=a.ProofTreeItem,n),preprocessors:[[i.saveDocument,1]],postprocessors:[[i.clearDocument,3],[i.makeBsprAttributes,2],[i.balanceRules,1]]})},14687:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),a=this&&this.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(e,r);o&&!("get"in o?!e.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,n,o)}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),i=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),s=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)"default"!==r&&Object.prototype.hasOwnProperty.call(t,r)&&a(e,t,r);return i(e,t),e},l=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.ProofTreeItem=void 0;var c=l(r(54420)),u=r(26539),d=l(r(35758)),p=s(r(32943)),f=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.leftLabel=null,e.rigthLabel=null,e.innerStack=new d.default(e.factory,{},!0),e}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"proofTree"},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(t){if(t.isKind("end")&&"prooftree"===t.getName()){var e=this.toMml();return p.setProperty(e,"proof",!0),[[this.factory.create("mml",e),t],!0]}if(t.isKind("stop"))throw new c.default("EnvMissingEnd","Missing \\end{%1}",this.getName());return this.innerStack.Push(t),u.BaseItem.fail},e.prototype.toMml=function(){var e=t.prototype.toMml.call(this),r=this.innerStack.Top();if(r.isKind("start")&&!r.Size())return e;this.innerStack.Push(this.factory.create("stop"));var n=this.innerStack.Top().toMml();return this.create("node","mrow",[n,e],{})},e}(u.BaseItem);e.ProofTreeItem=f},74836:function(t,e,r){var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var o=n(r(43118)),a=n(r(2362)),i=r(65695);new i.CommandMap("Bussproofs-macros",{AxiomC:"Axiom",UnaryInfC:["Inference",1],BinaryInfC:["Inference",2],TrinaryInfC:["Inference",3],QuaternaryInfC:["Inference",4],QuinaryInfC:["Inference",5],RightLabel:["Label","right"],LeftLabel:["Label","left"],AXC:"Axiom",UIC:["Inference",1],BIC:["Inference",2],TIC:["Inference",3],RL:["Label","right"],LL:["Label","left"],noLine:["SetLine","none",!1],singleLine:["SetLine","solid",!1],solidLine:["SetLine","solid",!1],dashedLine:["SetLine","dashed",!1],alwaysNoLine:["SetLine","none",!0],alwaysSingleLine:["SetLine","solid",!0],alwaysSolidLine:["SetLine","solid",!0],alwaysDashedLine:["SetLine","dashed",!0],rootAtTop:["RootAtTop",!0],alwaysRootAtTop:["RootAtTop",!0],rootAtBottom:["RootAtTop",!1],alwaysRootAtBottom:["RootAtTop",!1],fCenter:"FCenter",Axiom:"AxiomF",UnaryInf:["InferenceF",1],BinaryInf:["InferenceF",2],TrinaryInf:["InferenceF",3],QuaternaryInf:["InferenceF",4],QuinaryInf:["InferenceF",5]},o.default),new i.EnvironmentMap("Bussproofs-environments",a.default.environment,{prooftree:["Prooftree",null,!1]},o.default)},43118:function(t,e,r){var n=this&&this.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(e,r);o&&!("get"in o?!e.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,n,o)}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),o=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),a=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)"default"!==r&&Object.prototype.hasOwnProperty.call(t,r)&&n(e,t,r);return o(e,t),e},i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,a=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=a.next()).done;)i.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return i},s=this&&this.__spreadArray||function(t,e,r){if(r||2===arguments.length)for(var n,o=0,a=e.length;o<a;o++)!n&&o in e||(n||(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))},l=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var c=l(r(54420)),u=l(r(94032)),d=l(r(55038)),p=a(r(32943)),f={Prooftree:function(t,e){return t.Push(e),t.itemFactory.create("proofTree").setProperties({name:e.getName(),line:"solid",currentLine:"solid",rootAtTop:!1})},Axiom:function(t,e){var r=t.stack.Top();if("proofTree"!==r.kind)throw new c.default("IllegalProofCommand","Proof commands only allowed in prooftree environment.");var n=m(t,t.GetArgument(e));p.setProperty(n,"axiom",!0),r.Push(n)}},m=function(t,e){var r=d.default.internalMath(t,d.default.trimSpaces(e),0);if(!r[0].childNodes[0].childNodes.length)return t.create("node","mrow",[]);var n=t.create("node","mspace",[],{width:".5ex"}),o=t.create("node","mspace",[],{width:".5ex"});return t.create("node","mrow",s(s([n],i(r),!1),[o],!1))};function h(t,e,r,n,o,a,i){var s,l,c,u,d=t.create("node","mtr",[t.create("node","mtd",[e],{})],{}),f=t.create("node","mtr",[t.create("node","mtd",r,{})],{}),m=t.create("node","mtable",i?[f,d]:[d,f],{align:"top 2",rowlines:a,framespacing:"0 0"});if(p.setProperty(m,"inferenceRule",i?"up":"down"),n&&(s=t.create("node","mpadded",[n],{height:"+.5em",width:"+.5em",voffset:"-.15em"}),p.setProperty(s,"prooflabel","left")),o&&(l=t.create("node","mpadded",[o],{height:"+.5em",width:"+.5em",voffset:"-.15em"}),p.setProperty(l,"prooflabel","right")),n&&o)c=[s,m,l],u="both";else if(n)c=[s,m],u="left";else{if(!o)return m;c=[m,l],u="right"}return m=t.create("node","mrow",c),p.setProperty(m,"labelledRule",u),m}function g(t,e){if("$"!==t.GetNext())throw new c.default("IllegalUseOfCommand","Use of %1 does not match it's definition.",e);t.i++;var r=t.GetUpTo(e,"$");if(-1===r.indexOf("\\fCenter"))throw new c.default("IllegalUseOfCommand","Missing \\fCenter in %1.",e);var n=i(r.split("\\fCenter"),2),o=n[0],a=n[1],s=new u.default(o,t.stack.env,t.configuration).mml(),l=new u.default(a,t.stack.env,t.configuration).mml(),d=new u.default("\\fCenter",t.stack.env,t.configuration).mml(),f=t.create("node","mtd",[s],{}),m=t.create("node","mtd",[d],{}),h=t.create("node","mtd",[l],{}),g=t.create("node","mtr",[f,m,h],{}),v=t.create("node","mtable",[g],{columnspacing:".5ex",columnalign:"center 2"});return p.setProperty(v,"sequent",!0),t.configuration.addNode("sequent",g),v}f.Inference=function(t,e,r){var n=t.stack.Top();if("proofTree"!==n.kind)throw new c.default("IllegalProofCommand","Proof commands only allowed in prooftree environment.");if(n.Size()<r)throw new c.default("BadProofTree","Proof tree badly specified.");var o=n.getProperty("rootAtTop"),a=1!==r||n.Peek()[0].childNodes.length?r:0,i=[];do{i.length&&i.unshift(t.create("node","mtd",[],{})),i.unshift(t.create("node","mtd",[n.Pop()],{rowalign:o?"top":"bottom"})),r--}while(r>0);var s=t.create("node","mtr",i,{}),l=t.create("node","mtable",[s],{framespacing:"0 0"}),u=m(t,t.GetArgument(e)),d=n.getProperty("currentLine");d!==n.getProperty("line")&&n.setProperty("currentLine",n.getProperty("line"));var f=h(t,l,[u],n.getProperty("left"),n.getProperty("right"),d,o);n.setProperty("left",null),n.setProperty("right",null),p.setProperty(f,"inference",a),t.configuration.addNode("inference",f),n.Push(f)},f.Label=function(t,e,r){var n=t.stack.Top();if("proofTree"!==n.kind)throw new c.default("IllegalProofCommand","Proof commands only allowed in prooftree environment.");var o=d.default.internalMath(t,t.GetArgument(e),0),a=o.length>1?t.create("node","mrow",o,{}):o[0];n.setProperty(r,a)},f.SetLine=function(t,e,r,n){var o=t.stack.Top();if("proofTree"!==o.kind)throw new c.default("IllegalProofCommand","Proof commands only allowed in prooftree environment.");o.setProperty("currentLine",r),n&&o.setProperty("line",r)},f.RootAtTop=function(t,e,r){var n=t.stack.Top();if("proofTree"!==n.kind)throw new c.default("IllegalProofCommand","Proof commands only allowed in prooftree environment.");n.setProperty("rootAtTop",r)},f.AxiomF=function(t,e){var r=t.stack.Top();if("proofTree"!==r.kind)throw new c.default("IllegalProofCommand","Proof commands only allowed in prooftree environment.");var n=g(t,e);p.setProperty(n,"axiom",!0),r.Push(n)},f.FCenter=function(t,e){},f.InferenceF=function(t,e,r){var n=t.stack.Top();if("proofTree"!==n.kind)throw new c.default("IllegalProofCommand","Proof commands only allowed in prooftree environment.");if(n.Size()<r)throw new c.default("BadProofTree","Proof tree badly specified.");var o=n.getProperty("rootAtTop"),a=1!==r||n.Peek()[0].childNodes.length?r:0,i=[];do{i.length&&i.unshift(t.create("node","mtd",[],{})),i.unshift(t.create("node","mtd",[n.Pop()],{rowalign:o?"top":"bottom"})),r--}while(r>0);var s=t.create("node","mtr",i,{}),l=t.create("node","mtable",[s],{framespacing:"0 0"}),u=g(t,e),d=n.getProperty("currentLine");d!==n.getProperty("line")&&n.setProperty("currentLine",n.getProperty("line"));var f=h(t,l,[u],n.getProperty("left"),n.getProperty("right"),d,o);n.setProperty("left",null),n.setProperty("right",null),p.setProperty(f,"inference",a),t.configuration.addNode("inference",f),n.Push(f)},e.default=f},32943:function(t,e,r){var n,o=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,a=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=a.next()).done;)i.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return i},a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.clearDocument=e.saveDocument=e.makeBsprAttributes=e.removeProperty=e.getProperty=e.setProperty=e.balanceRules=void 0;var s=i(r(53972)),l=i(r(55038)),c=null,u=null,d=function(t){return u.root=t,c.outputJax.getBBox(u,c).w},p=function(t){for(var e=0;t&&!s.default.isType(t,"mtable");){if(s.default.isType(t,"text"))return null;s.default.isType(t,"mrow")?(t=t.childNodes[0],e=0):(t=t.parent.childNodes[e],e++)}return t},f=function(t,e){return t.childNodes["up"===e?1:0].childNodes[0].childNodes[0].childNodes[0].childNodes[0]},m=function(t,e){return t.childNodes[e].childNodes[0].childNodes[0]},h=function(t){return m(t,0)},g=function(t){return m(t,t.childNodes.length-1)},v=function(t,e){return t.childNodes["up"===e?0:1].childNodes[0].childNodes[0].childNodes[0]},y=function(t){for(;t&&!s.default.isType(t,"mtd");)t=t.parent;return t},b=function(t){return t.parent.childNodes[t.parent.childNodes.indexOf(t)+1]},x=function(t){for(;t&&null==(0,e.getProperty)(t,"inference");)t=t.parent;return t},_=function(t,e,r){void 0===r&&(r=!1);var n=0;if(t===e)return n;if(t!==e.parent){var o=t.childNodes,a=r?o.length-1:0;s.default.isType(o[a],"mspace")&&(n+=d(o[a])),t=e.parent}if(t===e)return n;var i=t.childNodes,l=r?i.length-1:0;return i[l]!==e&&(n+=d(i[l])),n},w=function(t,r){void 0===r&&(r=!1);var n=p(t),o=v(n,(0,e.getProperty)(n,"inferenceRule"));return _(t,n,r)+(d(n)-d(o))/2},M=function(t,r,n,o){if(void 0===o&&(o=!1),(0,e.getProperty)(r,"inferenceRule")||(0,e.getProperty)(r,"labelledRule")){var a=t.nodeFactory.create("node","mrow");r.parent.replaceChild(a,r),a.setChildren([r]),A(r,a),r=a}var i=o?r.childNodes.length-1:0,c=r.childNodes[i];s.default.isType(c,"mspace")?s.default.setAttribute(c,"width",l.default.Em(l.default.dimen2em(s.default.getAttribute(c,"width"))+n)):(c=t.nodeFactory.create("node","mspace",[],{width:l.default.Em(n)}),o?r.appendChild(c):(c.parent=r,r.childNodes.unshift(c)))},A=function(t,r){["inference","proof","maxAdjust","labelledRule"].forEach((function(n){var o=(0,e.getProperty)(t,n);null!=o&&((0,e.setProperty)(r,n,o),(0,e.removeProperty)(t,n))}))},C=function(t,r,n,o,a){var i=t.nodeFactory.create("node","mspace",[],{width:l.default.Em(a)});if("left"===o){var s=r.childNodes[n].childNodes[0];i.parent=s,s.childNodes.unshift(i)}else r.childNodes[n].appendChild(i);(0,e.setProperty)(r.parent,"sequentAdjust_"+o,a)},P=function(t,r){for(var n=r.pop();r.length;){var a=r.pop(),i=o(S(n,a),2),s=i[0],l=i[1];(0,e.getProperty)(n.parent,"axiom")&&(C(t,s<0?n:a,0,"left",Math.abs(s)),C(t,l<0?n:a,2,"right",Math.abs(l))),n=a}},S=function(t,e){var r=d(t.childNodes[2]),n=d(e.childNodes[2]);return[d(t.childNodes[0])-d(e.childNodes[0]),r-n]};e.balanceRules=function(t){var r,n;u=new t.document.options.MathItem("",null,t.math.display);var o=t.data;!function(t){var r=t.nodeLists.sequent;if(r)for(var n=r.length-1,o=void 0;o=r[n];n--)if((0,e.getProperty)(o,"sequentProcessed"))(0,e.removeProperty)(o,"sequentProcessed");else{var a=[],i=x(o);if(1===(0,e.getProperty)(i,"inference")){for(a.push(o);1===(0,e.getProperty)(i,"inference");){i=p(i);var s=h(f(i,(0,e.getProperty)(i,"inferenceRule"))),l=(0,e.getProperty)(s,"inferenceRule")?v(s,(0,e.getProperty)(s,"inferenceRule")):s;(0,e.getProperty)(l,"sequent")&&(o=l.childNodes[0],a.push(o),(0,e.setProperty)(o,"sequentProcessed",!0)),i=s}P(t,a)}}}(o);var i=o.nodeLists.inference||[];try{for(var s=a(i),l=s.next();!l.done;l=s.next()){var c=l.value,d=(0,e.getProperty)(c,"proof"),m=p(c),A=f(m,(0,e.getProperty)(m,"inferenceRule")),C=h(A);if((0,e.getProperty)(C,"inference")){var S=w(C);if(S){M(o,C,-S);var k=_(c,m,!1);M(o,c,S-k)}}var O=g(A);if(null!=(0,e.getProperty)(O,"inference")){var q=w(O,!0);M(o,O,-q,!0);var T=_(c,m,!0),E=(0,e.getProperty)(c,"maxAdjust");null!=E&&(q=Math.max(q,E));var I=void 0;if(!d&&(I=y(c))){var D=b(I);if(D){var N=o.nodeFactory.create("node","mspace",[],{width:q-T+"em"});D.appendChild(N),c.removeProperty("maxAdjust")}else{var G=x(I);G&&(q=(0,e.getProperty)(G,"maxAdjust")?Math.max((0,e.getProperty)(G,"maxAdjust"),q):q,(0,e.setProperty)(G,"maxAdjust",q))}}else M(o,(0,e.getProperty)(c,"proof")?c:c.parent,q-T,!0)}}}catch(t){r={error:t}}finally{try{l&&!l.done&&(n=s.return)&&n.call(s)}finally{if(r)throw r.error}}};var k="bspr_",O=((n={})[k+"maxAdjust"]=!0,n);e.setProperty=function(t,e,r){s.default.setProperty(t,k+e,r)},e.getProperty=function(t,e){return s.default.getProperty(t,k+e)},e.removeProperty=function(t,e){t.removeProperty(k+e)},e.makeBsprAttributes=function(t){t.data.root.walkTree((function(t,e){var r=[];t.getPropertyNames().forEach((function(e){!O[e]&&e.match(RegExp("^"+k))&&r.push(e+":"+t.getProperty(e))})),r.length&&s.default.setAttribute(t,"semantics",r.join(";"))}))},e.saveDocument=function(t){if(!("getBBox"in(c=t.document).outputJax))throw Error("The bussproofs extension requires an output jax with a getBBox() method")},e.clearDocument=function(t){c=null}},61767:function(t,e,r){var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.CancelConfiguration=e.CancelMethods=void 0;var o=r(63401),a=r(28027),i=r(65695),s=n(r(55038)),l=r(17915);e.CancelMethods={},e.CancelMethods.Cancel=function(t,e,r){var n=t.GetBrackets(e,""),o=t.ParseArg(e),a=s.default.keyvalOptions(n,l.ENCLOSE_OPTIONS);a.notation=r,t.Push(t.create("node","menclose",[o],a))},e.CancelMethods.CancelTo=function(t,e){var r=t.GetBrackets(e,""),n=t.ParseArg(e),o=t.ParseArg(e),i=s.default.keyvalOptions(r,l.ENCLOSE_OPTIONS);i.notation=[a.TexConstant.Notation.UPDIAGONALSTRIKE,a.TexConstant.Notation.UPDIAGONALARROW,a.TexConstant.Notation.NORTHEASTARROW].join(" "),n=t.create("node","mpadded",[n],{depth:"-.1em",height:"+.1em",voffset:".1em"}),t.Push(t.create("node","msup",[t.create("node","menclose",[o],i),n]))},new i.CommandMap("cancel",{cancel:["Cancel",a.TexConstant.Notation.UPDIAGONALSTRIKE],bcancel:["Cancel",a.TexConstant.Notation.DOWNDIAGONALSTRIKE],xcancel:["Cancel",a.TexConstant.Notation.UPDIAGONALSTRIKE+" "+a.TexConstant.Notation.DOWNDIAGONALSTRIKE],cancelto:"CancelTo"},e.CancelMethods),e.CancelConfiguration=o.Configuration.create("cancel",{handler:{macro:["cancel"]}})},99341:function(t,e,r){var n,o,a=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.CasesConfiguration=e.CasesMethods=e.CasesTags=e.CasesBeginItem=void 0;var s=r(63401),l=r(65695),c=i(r(55038)),u=i(r(76914)),d=i(r(54420)),p=r(31201),f=r(64296),m=r(14034),h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"cases-begin"},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){return e.isKind("end")&&e.getName()===this.getName()&&this.getProperty("end")?(this.setProperty("end",!1),[[],!0]):t.prototype.checkItem.call(this,e)},e}(p.BeginItem);e.CasesBeginItem=h;var g=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.subcounter=0,e}return a(e,t),e.prototype.start=function(e,r,n){this.subcounter=0,t.prototype.start.call(this,e,r,n)},e.prototype.autoTag=function(){null==this.currentTag.tag&&("subnumcases"===this.currentTag.env?(0===this.subcounter&&this.counter++,this.subcounter++,this.tag(this.formatNumber(this.counter,this.subcounter),!1)):(0!==this.subcounter&&"numcases-left"===this.currentTag.env||this.counter++,this.tag(this.formatNumber(this.counter),!1)))},e.prototype.formatNumber=function(t,e){return void 0===e&&(e=null),t.toString()+(null===e?"":String.fromCharCode(96+e))},e}(f.AmsTags);e.CasesTags=g,e.CasesMethods={NumCases:function(t,e){if(t.stack.env.closing===e.getName()){delete t.stack.env.closing,t.Push(t.itemFactory.create("end").setProperty("name",e.getName()));var r=t.stack.Top(),n=r.Last,o=c.default.copyNode(n,t),a=r.getProperty("left");return m.EmpheqUtil.left(n,o,a+"\\empheqlbrace\\,",t,"numcases-left"),t.Push(t.itemFactory.create("end").setProperty("name",e.getName())),null}a=t.GetArgument("\\begin{"+e.getName()+"}"),e.setProperty("left",a);var i=u.default.EqnArray(t,e,!0,!0,"ll");return i.arraydef.displaystyle=!1,i.arraydef.rowspacing=".2em",i.setProperty("numCases",!0),t.Push(e),i},Entry:function(t,e){if(!t.stack.Top().getProperty("numCases"))return u.default.Entry(t,e);t.Push(t.itemFactory.create("cell").setProperties({isEntry:!0,name:e}));for(var r=t.string,n=0,o=t.i,a=r.length;o<a;){var i=r.charAt(o);if("{"===i)n++,o++;else if("}"===i){if(0===n)break;n--,o++}else{if("&"===i&&0===n)throw new d.default("ExtraCasesAlignTab","Extra alignment tab in text for numcase environment");if("\\"===i&&0===n){var s=(r.slice(o+1).match(/^[a-z]+|./i)||[])[0];if("\\"===s||"cr"===s||"end"===s||"label"===s)break;o+=s.length}else o++}}var l=r.substr(t.i,o-t.i).replace(/^\s*/,"");t.PushAll(c.default.internalMath(t,l,0)),t.i=o}},new l.EnvironmentMap("cases-env",m.EmpheqUtil.environment,{numcases:["NumCases","cases"],subnumcases:["NumCases","cases"]},e.CasesMethods),new l.MacroMap("cases-macros",{"&":"Entry"},e.CasesMethods),e.CasesConfiguration=s.Configuration.create("cases",{handler:{environment:["cases-env"],character:["cases-macros"]},items:(o={},o[h.prototype.kind]=h,o),tags:{cases:g}})},61961:function(t,e,r){var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},o=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.CenternotConfiguration=e.filterCenterOver=void 0;var a=r(63401),i=o(r(94032)),s=o(r(53972)),l=r(65695),c=o(r(76914));function u(t){var e,r,o=t.data;try{for(var a=n(o.getList("centerOver")),i=a.next();!i.done;i=a.next()){var l=i.value,c=s.default.getTexClass(l.childNodes[0].childNodes[0]);null!==c&&s.default.setProperties(l.parent.parent.parent.parent.parent.parent,{texClass:c})}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}}new l.CommandMap("centernot",{centerOver:"CenterOver",centernot:["Macro","\\centerOver{#1}{{⧸}}",1]},{CenterOver:function(t,e){var r="{"+t.GetArgument(e)+"}",n=t.ParseArg(e),o=new i.default(r,t.stack.env,t.configuration).mml(),a=t.create("node","TeXAtom",[new i.default(r,t.stack.env,t.configuration).mml(),t.create("node","mpadded",[t.create("node","mpadded",[n],{width:0,lspace:"-.5width"}),t.create("node","mphantom",[o])],{width:0,lspace:"-.5width"})]);t.configuration.addNode("centerOver",o),t.Push(a)},Macro:c.default.Macro}),e.filterCenterOver=u,e.CenternotConfiguration=a.Configuration.create("centernot",{handler:{macro:["centernot"]},postprocessors:[u]})},80449:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.ColorConfiguration=void 0;var n=r(65695),o=r(63401),a=r(31134),i=r(83923);new n.CommandMap("color",{color:"Color",textcolor:"TextColor",definecolor:"DefineColor",colorbox:"ColorBox",fcolorbox:"FColorBox"},a.ColorMethods),e.ColorConfiguration=o.Configuration.create("color",{handler:{macro:["color"]},options:{color:{padding:"5px",borderWidth:"2px"}},config:function(t,e){e.parseOptions.packageData.set("color",{model:new i.ColorModel})}})},63421:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.COLORS=void 0,e.COLORS=new Map([["Apricot","#FBB982"],["Aquamarine","#00B5BE"],["Bittersweet","#C04F17"],["Black","#221E1F"],["Blue","#2D2F92"],["BlueGreen","#00B3B8"],["BlueViolet","#473992"],["BrickRed","#B6321C"],["Brown","#792500"],["BurntOrange","#F7921D"],["CadetBlue","#74729A"],["CarnationPink","#F282B4"],["Cerulean","#00A2E3"],["CornflowerBlue","#41B0E4"],["Cyan","#00AEEF"],["Dandelion","#FDBC42"],["DarkOrchid","#A4538A"],["Emerald","#00A99D"],["ForestGreen","#009B55"],["Fuchsia","#8C368C"],["Goldenrod","#FFDF42"],["Gray","#949698"],["Green","#00A64F"],["GreenYellow","#DFE674"],["JungleGreen","#00A99A"],["Lavender","#F49EC4"],["LimeGreen","#8DC73E"],["Magenta","#EC008C"],["Mahogany","#A9341F"],["Maroon","#AF3235"],["Melon","#F89E7B"],["MidnightBlue","#006795"],["Mulberry","#A93C93"],["NavyBlue","#006EB8"],["OliveGreen","#3C8031"],["Orange","#F58137"],["OrangeRed","#ED135A"],["Orchid","#AF72B0"],["Peach","#F7965A"],["Periwinkle","#7977B8"],["PineGreen","#008B72"],["Plum","#92268F"],["ProcessBlue","#00B0F0"],["Purple","#99479B"],["RawSienna","#974006"],["Red","#ED1B23"],["RedOrange","#F26035"],["RedViolet","#A1246B"],["Rhodamine","#EF559F"],["RoyalBlue","#0071BC"],["RoyalPurple","#613F99"],["RubineRed","#ED017D"],["Salmon","#F69289"],["SeaGreen","#3FBC9D"],["Sepia","#671800"],["SkyBlue","#46C5DD"],["SpringGreen","#C6DC67"],["Tan","#DA9D76"],["TealBlue","#00AEB3"],["Thistle","#D883B7"],["Turquoise","#00B4CE"],["Violet","#58429B"],["VioletRed","#EF58A0"],["White","#FFFFFF"],["WildStrawberry","#EE2967"],["Yellow","#FFF200"],["YellowGreen","#98CC70"],["YellowOrange","#FAA21A"]])},31134:function(t,e,r){var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.ColorMethods=void 0;var o=n(r(53972)),a=n(r(55038));function i(t){var e="+".concat(t),r=t.replace(/^.*?([a-z]*)$/,"$1"),n=2*parseFloat(e);return{width:"+".concat(n).concat(r),height:e,depth:e,lspace:t}}e.ColorMethods={},e.ColorMethods.Color=function(t,e){var r=t.GetBrackets(e,""),n=t.GetArgument(e),o=t.configuration.packageData.get("color").model.getColor(r,n),a=t.itemFactory.create("style").setProperties({styles:{mathcolor:o}});t.stack.env.color=o,t.Push(a)},e.ColorMethods.TextColor=function(t,e){var r=t.GetBrackets(e,""),n=t.GetArgument(e),o=t.configuration.packageData.get("color").model.getColor(r,n),a=t.stack.env.color;t.stack.env.color=o;var i=t.ParseArg(e);a?t.stack.env.color=a:delete t.stack.env.color;var s=t.create("node","mstyle",[i],{mathcolor:o});t.Push(s)},e.ColorMethods.DefineColor=function(t,e){var r=t.GetArgument(e),n=t.GetArgument(e),o=t.GetArgument(e);t.configuration.packageData.get("color").model.defineColor(n,r,o)},e.ColorMethods.ColorBox=function(t,e){var r=t.GetArgument(e),n=a.default.internalMath(t,t.GetArgument(e)),s=t.configuration.packageData.get("color").model,l=t.create("node","mpadded",n,{mathbackground:s.getColor("named",r)});o.default.setProperties(l,i(t.options.color.padding)),t.Push(l)},e.ColorMethods.FColorBox=function(t,e){var r=t.GetArgument(e),n=t.GetArgument(e),s=a.default.internalMath(t,t.GetArgument(e)),l=t.options.color,c=t.configuration.packageData.get("color").model,u=t.create("node","mpadded",s,{mathbackground:c.getColor("named",n),style:"border: ".concat(l.borderWidth," solid ").concat(c.getColor("named",r))});o.default.setProperties(u,i(l.padding)),t.Push(u)}},83923:function(t,e,r){var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},o=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.ColorModel=void 0;var a=o(r(54420)),i=r(63421),s=new Map,l=function(){function t(){this.userColors=new Map}return t.prototype.normalizeColor=function(t,e){if(!t||"named"===t)return e;if(s.has(t))return s.get(t)(e);throw new a.default("UndefinedColorModel","Color model '%1' not defined",t)},t.prototype.getColor=function(t,e){return t&&"named"!==t?this.normalizeColor(t,e):this.getColorByName(e)},t.prototype.getColorByName=function(t){return this.userColors.has(t)?this.userColors.get(t):i.COLORS.has(t)?i.COLORS.get(t):t},t.prototype.defineColor=function(t,e,r){var n=this.normalizeColor(t,r);this.userColors.set(e,n)},t}();e.ColorModel=l,s.set("rgb",(function(t){var e,r,o=t.trim().split(/\s*,\s*/),i="#";if(3!==o.length)throw new a.default("ModelArg1","Color values for the %1 model require 3 numbers","rgb");try{for(var s=n(o),l=s.next();!l.done;l=s.next()){var c=l.value;if(!c.match(/^(\d+(\.\d*)?|\.\d+)$/))throw new a.default("InvalidDecimalNumber","Invalid decimal number");var u=parseFloat(c);if(u<0||u>1)throw new a.default("ModelArg2","Color values for the %1 model must be between %2 and %3","rgb","0","1");var d=Math.floor(255*u).toString(16);d.length<2&&(d="0"+d),i+=d}}catch(t){e={error:t}}finally{try{l&&!l.done&&(r=s.return)&&r.call(s)}finally{if(e)throw e.error}}return i})),s.set("RGB",(function(t){var e,r,o=t.trim().split(/\s*,\s*/),i="#";if(3!==o.length)throw new a.default("ModelArg1","Color values for the %1 model require 3 numbers","RGB");try{for(var s=n(o),l=s.next();!l.done;l=s.next()){var c=l.value;if(!c.match(/^\d+$/))throw new a.default("InvalidNumber","Invalid number");var u=parseInt(c);if(u>255)throw new a.default("ModelArg2","Color values for the %1 model must be between %2 and %3","RGB","0","255");var d=u.toString(16);d.length<2&&(d="0"+d),i+=d}}catch(t){e={error:t}}finally{try{l&&!l.done&&(r=s.return)&&r.call(s)}finally{if(e)throw e.error}}return i})),s.set("gray",(function(t){if(!t.match(/^\s*(\d+(\.\d*)?|\.\d+)\s*$/))throw new a.default("InvalidDecimalNumber","Invalid decimal number");var e=parseFloat(t);if(e<0||e>1)throw new a.default("ModelArg2","Color values for the %1 model must be between %2 and %3","gray","0","1");var r=Math.floor(255*e).toString(16);return r.length<2&&(r="0"+r),"#".concat(r).concat(r).concat(r)}))},65744:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),a=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.ColortblConfiguration=e.ColorArrayItem=void 0;var i=r(31201),s=r(63401),l=r(65695),c=a(r(54420)),u=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.color={cell:"",row:"",col:[]},e.hasColor=!1,e}return o(e,t),e.prototype.EndEntry=function(){t.prototype.EndEntry.call(this);var e=this.row[this.row.length-1],r=this.color.cell||this.color.row||this.color.col[this.row.length-1];r&&(e.attributes.set("mathbackground",r),this.color.cell="",this.hasColor=!0)},e.prototype.EndRow=function(){t.prototype.EndRow.call(this),this.color.row=""},e.prototype.createMml=function(){var e=t.prototype.createMml.call(this),r=e.isKind("mrow")?e.childNodes[1]:e;return r.isKind("menclose")&&(r=r.childNodes[0].childNodes[0]),this.hasColor&&"none"===r.attributes.get("frame")&&r.attributes.set("frame",""),e},e}(i.ArrayItem);e.ColorArrayItem=u,new l.CommandMap("colortbl",{cellcolor:["TableColor","cell"],rowcolor:["TableColor","row"],columncolor:["TableColor","col"]},{TableColor:function(t,e,r){var n=t.configuration.packageData.get("color").model,o=t.GetBrackets(e,""),a=n.getColor(o,t.GetArgument(e)),i=t.stack.Top();if(!(i instanceof u))throw new c.default("UnsupportedTableColor","Unsupported use of %1",t.currentCS);if("col"===r){if(i.table.length)throw new c.default("ColumnColorNotTop","%1 must be in the top row",e);i.color.col[i.row.length]=a,t.GetBrackets(e,"")&&t.GetBrackets(e,"")}else if(i.color[r]=a,"row"===r&&(i.Size()||i.row.length))throw new c.default("RowColorNotFirst","%1 must be at the beginning of a row",e)}}),e.ColortblConfiguration=s.Configuration.create("colortbl",{handler:{macro:["colortbl"]},items:{array:u},priority:10,config:[function(t,e){e.parseOptions.packageData.has("color")||s.ConfigurationHandler.get("color").config(t,e)},10]})},83370:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.ColorConfiguration=e.ColorV2Methods=void 0;var n=r(65695),o=r(63401);e.ColorV2Methods={Color:function(t,e){var r=t.GetArgument(e),n=t.stack.env.color;t.stack.env.color=r;var o=t.ParseArg(e);n?t.stack.env.color=n:delete t.stack.env.color;var a=t.create("node","mstyle",[o],{mathcolor:r});t.Push(a)}},new n.CommandMap("colorv2",{color:"Color"},e.ColorV2Methods),e.ColorConfiguration=o.Configuration.create("colorv2",{handler:{macro:["colorv2"]}})},53948:function(t,e,r){var n,o=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},a=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.ConfigMacrosConfiguration=void 0;var i=r(63401),s=r(4498),l=r(65695),c=a(r(2362)),u=r(75735),d=a(r(39736)),p=r(36688),f="configmacros-map",m="configmacros-env-map";e.ConfigMacrosConfiguration=i.Configuration.create("configmacros",{init:function(t){new l.CommandMap(f,{},{}),new l.EnvironmentMap(m,c.default.environment,{},{}),t.append(i.Configuration.local({handler:{macro:[f],environment:[m]},priority:3}))},config:function(t,e){!function(t){var e,r,n=t.parseOptions.handlers.retrieve(f),a=t.parseOptions.options.macros;try{for(var i=o(Object.keys(a)),s=i.next();!s.done;s=i.next()){var l=s.value,c="string"==typeof a[l]?[a[l]]:a[l],p=Array.isArray(c[2])?new u.Macro(l,d.default.MacroWithTemplate,c.slice(0,2).concat(c[2])):new u.Macro(l,d.default.Macro,c);n.add(l,p)}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}}(e),function(t){var e,r,n=t.parseOptions.handlers.retrieve(m),a=t.parseOptions.options.environments;try{for(var i=o(Object.keys(a)),s=i.next();!s.done;s=i.next()){var l=s.value;n.add(l,new u.Macro(l,d.default.BeginEnv,[!0].concat(a[l])))}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}}(e)},items:(n={},n[p.BeginEnvItem.prototype.kind]=p.BeginEnvItem,n),options:{macros:(0,s.expandable)({}),environments:(0,s.expandable)({})}})},45463:function(t,e,r){var n,o,a=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,a=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=a.next()).done;)i.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return i},s=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.EmpheqConfiguration=e.EmpheqMethods=e.EmpheqBeginItem=void 0;var l=r(63401),c=r(65695),u=s(r(55038)),d=s(r(54420)),p=r(31201),f=r(14034),m=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"empheq-begin"},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){return e.isKind("end")&&e.getName()===this.getName()&&this.setProperty("end",!1),t.prototype.checkItem.call(this,e)},e}(p.BeginItem);e.EmpheqBeginItem=m,e.EmpheqMethods={Empheq:function(t,e){if(t.stack.env.closing===e.getName()){delete t.stack.env.closing,t.Push(t.itemFactory.create("end").setProperty("name",t.stack.global.empheq)),t.stack.global.empheq="";var r=t.stack.Top();f.EmpheqUtil.adjustTable(r,t),t.Push(t.itemFactory.create("end").setProperty("name","empheq"))}else{u.default.checkEqnEnv(t),delete t.stack.global.eqnenv;var n=t.GetBrackets("\\begin{"+e.getName()+"}")||"",o=i((t.GetArgument("\\begin{"+e.getName()+"}")||"").split(/=/),2),a=o[0],s=o[1];if(!f.EmpheqUtil.checkEnv(a))throw new d.default("UnknownEnv",'Unknown environment "%1"',a);n&&e.setProperties(f.EmpheqUtil.splitOptions(n,{left:1,right:1})),t.stack.global.empheq=a,t.string="\\begin{"+a+"}"+(s?"{"+s+"}":"")+t.string.slice(t.i),t.i=0,t.Push(e)}},EmpheqMO:function(t,e,r){t.Push(t.create("token","mo",{},r))},EmpheqDelim:function(t,e){var r=t.GetDelimiter(e);t.Push(t.create("token","mo",{stretchy:!0,symmetric:!0},r))}},new c.EnvironmentMap("empheq-env",f.EmpheqUtil.environment,{empheq:["Empheq","empheq"]},e.EmpheqMethods),new c.CommandMap("empheq-macros",{empheqlbrace:["EmpheqMO","{"],empheqrbrace:["EmpheqMO","}"],empheqlbrack:["EmpheqMO","["],empheqrbrack:["EmpheqMO","]"],empheqlangle:["EmpheqMO","⟨"],empheqrangle:["EmpheqMO","⟩"],empheqlparen:["EmpheqMO","("],empheqrparen:["EmpheqMO",")"],empheqlvert:["EmpheqMO","|"],empheqrvert:["EmpheqMO","|"],empheqlVert:["EmpheqMO","‖"],empheqrVert:["EmpheqMO","‖"],empheqlfloor:["EmpheqMO","⌊"],empheqrfloor:["EmpheqMO","⌋"],empheqlceil:["EmpheqMO","⌈"],empheqrceil:["EmpheqMO","⌉"],empheqbiglbrace:["EmpheqMO","{"],empheqbigrbrace:["EmpheqMO","}"],empheqbiglbrack:["EmpheqMO","["],empheqbigrbrack:["EmpheqMO","]"],empheqbiglangle:["EmpheqMO","⟨"],empheqbigrangle:["EmpheqMO","⟩"],empheqbiglparen:["EmpheqMO","("],empheqbigrparen:["EmpheqMO",")"],empheqbiglvert:["EmpheqMO","|"],empheqbigrvert:["EmpheqMO","|"],empheqbiglVert:["EmpheqMO","‖"],empheqbigrVert:["EmpheqMO","‖"],empheqbiglfloor:["EmpheqMO","⌊"],empheqbigrfloor:["EmpheqMO","⌋"],empheqbiglceil:["EmpheqMO","⌈"],empheqbigrceil:["EmpheqMO","⌉"],empheql:"EmpheqDelim",empheqr:"EmpheqDelim",empheqbigl:"EmpheqDelim",empheqbigr:"EmpheqDelim"},e.EmpheqMethods),e.EmpheqConfiguration=l.Configuration.create("empheq",{handler:{macro:["empheq-macros"],environment:["empheq-env"]},items:(o={},o[m.prototype.kind]=m,o)})},14034:function(t,e,r){var n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,a=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=a.next()).done;)i.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return i},o=this&&this.__spreadArray||function(t,e,r){if(r||2===arguments.length)for(var n,o=0,a=e.length;o<a;o++)!n&&o in e||(n||(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))},a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.EmpheqUtil=void 0;var s=i(r(55038)),l=i(r(94032));e.EmpheqUtil={environment:function(t,e,r,a){var i=a[0],s=t.itemFactory.create(i+"-begin").setProperties({name:e,end:i});t.Push(r.apply(void 0,o([t,s],n(a.slice(1)),!1)))},splitOptions:function(t,e){return void 0===e&&(e=null),s.default.keyvalOptions(t,e,!0)},columnCount:function(t){var e,r,n=0;try{for(var o=a(t.childNodes),i=o.next();!i.done;i=o.next()){var s=i.value,l=s.childNodes.length-(s.isKind("mlabeledtr")?1:0);l>n&&(n=l)}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return n},cellBlock:function(t,e,r,n){var o,i,s=r.create("node","mpadded",[],{height:0,depth:0,voffset:"-1height"}),c=new l.default(t,r.stack.env,r.configuration),u=c.mml();n&&c.configuration.tags.label&&(c.configuration.tags.currentTag.env=n,c.configuration.tags.getTag(!0));try{for(var d=a(u.isInferred?u.childNodes:[u]),p=d.next();!p.done;p=d.next()){var f=p.value;s.appendChild(f)}}catch(t){o={error:t}}finally{try{p&&!p.done&&(i=d.return)&&i.call(d)}finally{if(o)throw o.error}}return s.appendChild(r.create("node","mphantom",[r.create("node","mpadded",[e],{width:0})])),s},topRowTable:function(t,e){var r=s.default.copyNode(t,e);return r.setChildren(r.childNodes.slice(0,1)),r.attributes.set("align","baseline 1"),t.factory.create("mphantom",{},[e.create("node","mpadded",[r],{width:0})])},rowspanCell:function(t,e,r,n,o){t.appendChild(n.create("node","mpadded",[this.cellBlock(e,s.default.copyNode(r,n),n,o),this.topRowTable(r,n)],{height:0,depth:0,voffset:"height"}))},left:function(t,e,r,n,o){var i,s,l;void 0===o&&(o=""),t.attributes.set("columnalign","right "+(t.attributes.get("columnalign")||"")),t.attributes.set("columnspacing","0em "+(t.attributes.get("columnspacing")||""));try{for(var c=a(t.childNodes.slice(0).reverse()),u=c.next();!u.done;u=c.next()){var d=u.value;l=n.create("node","mtd"),d.childNodes.unshift(l),l.parent=d,d.isKind("mlabeledtr")&&(d.childNodes[0]=d.childNodes[1],d.childNodes[1]=l)}}catch(t){i={error:t}}finally{try{u&&!u.done&&(s=c.return)&&s.call(c)}finally{if(i)throw i.error}}this.rowspanCell(l,r,e,n,o)},right:function(t,r,n,o,a){void 0===a&&(a=""),0===t.childNodes.length&&t.appendChild(o.create("node","mtr"));for(var i=e.EmpheqUtil.columnCount(t),s=t.childNodes[0];s.childNodes.length<i;)s.appendChild(o.create("node","mtd"));var l=s.appendChild(o.create("node","mtd"));e.EmpheqUtil.rowspanCell(l,n,r,o,a),t.attributes.set("columnalign",(t.attributes.get("columnalign")||"").split(/ /).slice(0,i).join(" ")+" left"),t.attributes.set("columnspacing",(t.attributes.get("columnspacing")||"").split(/ /).slice(0,i-1).join(" ")+" 0em")},adjustTable:function(t,e){var r=t.getProperty("left"),n=t.getProperty("right");if(r||n){var o=t.Last,a=s.default.copyNode(o,e);r&&this.left(o,a,r,e),n&&this.right(o,a,n,e)}},allowEnv:{equation:!0,align:!0,gather:!0,flalign:!0,alignat:!0,multline:!0},checkEnv:function(t){return this.allowEnv.hasOwnProperty(t.replace(/\*$/,""))||!1}}},17915:function(t,e,r){var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.EncloseConfiguration=e.EncloseMethods=e.ENCLOSE_OPTIONS=void 0;var o=r(63401),a=r(65695),i=n(r(55038));e.ENCLOSE_OPTIONS={"data-arrowhead":1,color:1,mathcolor:1,background:1,mathbackground:1,"data-padding":1,"data-thickness":1},e.EncloseMethods={},e.EncloseMethods.Enclose=function(t,r){var n=t.GetArgument(r).replace(/,/g," "),o=t.GetBrackets(r,""),a=t.ParseArg(r),s=i.default.keyvalOptions(o,e.ENCLOSE_OPTIONS);s.notation=n,t.Push(t.create("node","menclose",[a],s))},new a.CommandMap("enclose",{enclose:"Enclose"},e.EncloseMethods),e.EncloseConfiguration=o.Configuration.create("enclose",{handler:{macro:["enclose"]}})},74927:function(t,e,r){var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.ExtpfeilConfiguration=e.ExtpfeilMethods=void 0;var o=r(63401),a=r(65695),i=r(14880),s=n(r(87357)),l=r(37515),c=n(r(54420));e.ExtpfeilMethods={},e.ExtpfeilMethods.xArrow=i.AmsMethods.xArrow,e.ExtpfeilMethods.NewExtArrow=function(t,r){var n=t.GetArgument(r),o=t.GetArgument(r),a=t.GetArgument(r);if(!n.match(/^\\([a-z]+|.)$/i))throw new c.default("NewextarrowArg1","First argument to %1 must be a control sequence name",r);if(!o.match(/^(\d+),(\d+)$/))throw new c.default("NewextarrowArg2","Second argument to %1 must be two integers separated by a comma",r);if(!a.match(/^(\d+|0x[0-9A-F]+)$/i))throw new c.default("NewextarrowArg3","Third argument to %1 must be a unicode character number",r);n=n.substr(1);var i=o.split(",");s.default.addMacro(t,n,e.ExtpfeilMethods.xArrow,[parseInt(a),parseInt(i[0]),parseInt(i[1])])},new a.CommandMap("extpfeil",{xtwoheadrightarrow:["xArrow",8608,12,16],xtwoheadleftarrow:["xArrow",8606,17,13],xmapsto:["xArrow",8614,6,7],xlongequal:["xArrow",61,7,7],xtofrom:["xArrow",8644,12,12],Newextarrow:"NewExtArrow"},e.ExtpfeilMethods),e.ExtpfeilConfiguration=o.Configuration.create("extpfeil",{handler:{macro:["extpfeil"]},init:function(t){l.NewcommandConfiguration.init(t)}})},48689:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.GensymbConfiguration=void 0;var n=r(63401),o=r(28027);new(r(65695).CharacterMap)("gensymb-symbols",(function(t,e){var r=e.attributes||{};r.mathvariant=o.TexConstant.Variant.NORMAL,r.class="MathML-Unit";var n=t.create("token","mi",r,e.char);t.Push(n)}),{ohm:"Ω",degree:"°",celsius:"℃",perthousand:"‰",micro:"µ"}),e.GensymbConfiguration=n.Configuration.create("gensymb",{handler:{macro:["gensymb-symbols"]}})},966:function(t,e,r){var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.HtmlConfiguration=void 0;var o=r(63401),a=r(65695),i=n(r(34034));new a.CommandMap("html_macros",{href:"Href",class:"Class",style:"Style",cssId:"Id"},i.default),e.HtmlConfiguration=o.Configuration.create("html",{handler:{macro:["html_macros"]}})},34034:function(t,e,r){var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var o=n(r(53972)),a={Href:function(t,e){var r=t.GetArgument(e),n=i(t,e);o.default.setAttribute(n,"href",r),t.Push(n)},Class:function(t,e){var r=t.GetArgument(e),n=i(t,e),a=o.default.getAttribute(n,"class");a&&(r=a+" "+r),o.default.setAttribute(n,"class",r),t.Push(n)},Style:function(t,e){var r=t.GetArgument(e),n=i(t,e),a=o.default.getAttribute(n,"style");a&&(";"!==r.charAt(r.length-1)&&(r+=";"),r=a+" "+r),o.default.setAttribute(n,"style",r),t.Push(n)},Id:function(t,e){var r=t.GetArgument(e),n=i(t,e);o.default.setAttribute(n,"id",r),t.Push(n)}},i=function(t,e){var r=t.ParseArg(e);if(!o.default.isInferred(r))return r;var n=o.default.getChildren(r);if(1===n.length)return n[0];var a=t.create("node","mrow");return o.default.copyChildren(r,a),o.default.copyAttributes(r,a),a};e.default=a},73414:function(t,e,r){var n,o=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},a=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.MathtoolsConfiguration=e.fixPrescripts=e.PAIREDDELIMS=void 0;var i=r(63401),s=r(65695),l=a(r(53972)),c=r(4498);r(655);var u=r(53946),d=r(43928),p=r(17519);function f(t){var e,r,n,a,i,s,c=t.data;try{for(var u=o(c.getList("mmultiscripts")),d=u.next();!d.done;d=u.next()){var p=d.value;if(p.getProperty("fixPrescript")){var f=l.default.getChildren(p),m=0;try{for(var h=(n=void 0,o([1,2])),g=h.next();!g.done;g=h.next())f[b=g.value]||(l.default.setChild(p,b,c.nodeFactory.create("node","none")),m++)}catch(t){n={error:t}}finally{try{g&&!g.done&&(a=h.return)&&a.call(h)}finally{if(n)throw n.error}}try{for(var v=(i=void 0,o([4,5])),y=v.next();!y.done;y=v.next()){var b=y.value;l.default.isType(f[b],"mrow")&&0===l.default.getChildren(f[b]).length&&l.default.setChild(p,b,c.nodeFactory.create("node","none"))}}catch(t){i={error:t}}finally{try{y&&!y.done&&(s=v.return)&&s.call(v)}finally{if(i)throw i.error}}2===m&&f.splice(1,2)}}}catch(t){e={error:t}}finally{try{d&&!d.done&&(r=u.return)&&r.call(u)}finally{if(e)throw e.error}}}e.PAIREDDELIMS="mathtools-paired-delims",e.fixPrescripts=f,e.MathtoolsConfiguration=i.Configuration.create("mathtools",{handler:{macro:["mathtools-macros","mathtools-delimiters"],environment:["mathtools-environments"],delimiter:["mathtools-delimiters"],character:["mathtools-characters"]},items:(n={},n[p.MultlinedItem.prototype.kind]=p.MultlinedItem,n),init:function(t){new s.CommandMap(e.PAIREDDELIMS,{},{}),t.append(i.Configuration.local({handler:{macro:[e.PAIREDDELIMS]},priority:-5}))},config:function(t,e){var r,n,a=e.parseOptions,i=a.options.mathtools.pairedDelimiters;try{for(var s=o(Object.keys(i)),l=s.next();!l.done;l=s.next()){var c=l.value;u.MathtoolsUtil.addPairedDelims(a,c,i[c])}}catch(t){r={error:t}}finally{try{l&&!l.done&&(n=s.return)&&n.call(s)}finally{if(r)throw r.error}}(0,d.MathtoolsTagFormat)(t,e)},postprocessors:[[f,-6]],options:{mathtools:{multlinegap:"1em","multlined-pos":"c","firstline-afterskip":"","lastline-preskip":"","smallmatrix-align":"c",shortvdotsadjustabove:".2em",shortvdotsadjustbelow:".2em",centercolon:!1,"centercolon-offset":".04em","thincolon-dx":"-.04em","thincolon-dw":"-.08em","use-unicode":!1,"prescript-sub-format":"","prescript-sup-format":"","prescript-arg-format":"","allow-mathtoolsset":!0,pairedDelimiters:(0,c.expandable)({}),tagforms:(0,c.expandable)({})}}})},17519:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),a=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.MultlinedItem=void 0;var i=r(75377),s=a(r(53972)),l=r(28027),c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"multlined"},enumerable:!1,configurable:!0}),e.prototype.EndTable=function(){if((this.Size()||this.row.length)&&(this.EndEntry(),this.EndRow()),this.table.length>1){var e=this.factory.configuration.options.mathtools,r=e.multlinegap,n=e["firstline-afterskip"]||r,o=e["lastline-preskip"]||r,a=s.default.getChildren(this.table[0])[0];s.default.getAttribute(a,"columnalign")!==l.TexConstant.Align.RIGHT&&a.appendChild(this.create("node","mspace",[],{width:n}));var i=s.default.getChildren(this.table[this.table.length-1])[0];if(s.default.getAttribute(i,"columnalign")!==l.TexConstant.Align.LEFT){var c=s.default.getChildren(i)[0];c.childNodes.unshift(null);var u=this.create("node","mspace",[],{width:o});s.default.setChild(c,0,u)}}t.prototype.EndTable.call(this)},e}(i.MultlineItem);e.MultlinedItem=c},655:function(t,e,r){var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var o=n(r(2362)),a=r(65695),i=r(28027),s=r(81274);new a.CommandMap("mathtools-macros",{shoveleft:["HandleShove",i.TexConstant.Align.LEFT],shoveright:["HandleShove",i.TexConstant.Align.RIGHT],xleftrightarrow:["xArrow",8596,10,10],xLeftarrow:["xArrow",8656,12,7],xRightarrow:["xArrow",8658,7,12],xLeftrightarrow:["xArrow",8660,12,12],xhookleftarrow:["xArrow",8617,10,5],xhookrightarrow:["xArrow",8618,5,10],xmapsto:["xArrow",8614,10,10],xrightharpoondown:["xArrow",8641,5,10],xleftharpoondown:["xArrow",8637,10,5],xrightleftharpoons:["xArrow",8652,10,10],xrightharpoonup:["xArrow",8640,5,10],xleftharpoonup:["xArrow",8636,10,5],xleftrightharpoons:["xArrow",8651,10,10],mathllap:["MathLap","l",!1],mathrlap:["MathLap","r",!1],mathclap:["MathLap","c",!1],clap:["MtLap","c"],textllap:["MtLap","l"],textrlap:["MtLap","r"],textclap:["MtLap","c"],cramped:"Cramped",crampedllap:["MathLap","l",!0],crampedrlap:["MathLap","r",!0],crampedclap:["MathLap","c",!0],crampedsubstack:["Macro","\\begin{crampedsubarray}{c}#1\\end{crampedsubarray}",1],mathmbox:"MathMBox",mathmakebox:"MathMakeBox",overbracket:"UnderOverBracket",underbracket:"UnderOverBracket",refeq:"HandleRef",MoveEqLeft:["Macro","\\hspace{#1em}&\\hspace{-#1em}",1,"2"],Aboxed:"Aboxed",ArrowBetweenLines:"ArrowBetweenLines",vdotswithin:"VDotsWithin",shortvdotswithin:"ShortVDotsWithin",MTFlushSpaceAbove:"FlushSpaceAbove",MTFlushSpaceBelow:"FlushSpaceBelow",DeclarePairedDelimiter:"DeclarePairedDelimiter",DeclarePairedDelimiterX:"DeclarePairedDelimiterX",DeclarePairedDelimiterXPP:"DeclarePairedDelimiterXPP",DeclarePairedDelimiters:"DeclarePairedDelimiter",DeclarePairedDelimitersX:"DeclarePairedDelimiterX",DeclarePairedDelimitersXPP:"DeclarePairedDelimiterXPP",centercolon:["CenterColon",!0,!0],ordinarycolon:["CenterColon",!1],MTThinColon:["CenterColon",!0,!0,!0],coloneqq:["Relation",":=","≔"],Coloneqq:["Relation","::=","⩴"],coloneq:["Relation",":-"],Coloneq:["Relation","::-"],eqqcolon:["Relation","=:","≕"],Eqqcolon:["Relation","=::"],eqcolon:["Relation","-:","∹"],Eqcolon:["Relation","-::"],colonapprox:["Relation",":\\approx"],Colonapprox:["Relation","::\\approx"],colonsim:["Relation",":\\sim"],Colonsim:["Relation","::\\sim"],dblcolon:["Relation","::","∷"],nuparrow:["NArrow","↑",".06em"],ndownarrow:["NArrow","↓",".25em"],bigtimes:["Macro","\\mathop{\\Large\\kern-.1em\\boldsymbol{\\times}\\kern-.1em}"],splitfrac:["SplitFrac",!1],splitdfrac:["SplitFrac",!0],xmathstrut:"XMathStrut",prescript:"Prescript",newtagform:["NewTagForm",!1],renewtagform:["NewTagForm",!0],usetagform:"UseTagForm",adjustlimits:["MacroWithTemplate","\\mathop{{#1}\\vphantom{{#3}}}_{{#2}\\vphantom{{#4}}}\\mathop{{#3}\\vphantom{{#1}}}_{{#4}\\vphantom{{#2}}}",4,,"_",,"_"],mathtoolsset:"SetOptions"},s.MathtoolsMethods),new a.EnvironmentMap("mathtools-environments",o.default.environment,{dcases:["Array",null,"\\{","","ll",null,".2em","D"],rcases:["Array",null,"","\\}","ll",null,".2em"],drcases:["Array",null,"","\\}","ll",null,".2em","D"],"dcases*":["Cases",null,"{","","D"],"rcases*":["Cases",null,"","}"],"drcases*":["Cases",null,"","}","D"],"cases*":["Cases",null,"{",""],"matrix*":["MtMatrix",null,null,null],"pmatrix*":["MtMatrix",null,"(",")"],"bmatrix*":["MtMatrix",null,"[","]"],"Bmatrix*":["MtMatrix",null,"\\{","\\}"],"vmatrix*":["MtMatrix",null,"\\vert","\\vert"],"Vmatrix*":["MtMatrix",null,"\\Vert","\\Vert"],"smallmatrix*":["MtSmallMatrix",null,null,null],psmallmatrix:["MtSmallMatrix",null,"(",")","c"],"psmallmatrix*":["MtSmallMatrix",null,"(",")"],bsmallmatrix:["MtSmallMatrix",null,"[","]","c"],"bsmallmatrix*":["MtSmallMatrix",null,"[","]"],Bsmallmatrix:["MtSmallMatrix",null,"\\{","\\}","c"],"Bsmallmatrix*":["MtSmallMatrix",null,"\\{","\\}"],vsmallmatrix:["MtSmallMatrix",null,"\\vert","\\vert","c"],"vsmallmatrix*":["MtSmallMatrix",null,"\\vert","\\vert"],Vsmallmatrix:["MtSmallMatrix",null,"\\Vert","\\Vert","c"],"Vsmallmatrix*":["MtSmallMatrix",null,"\\Vert","\\Vert"],crampedsubarray:["Array",null,null,null,null,"0em","0.1em","S'",1],multlined:"MtMultlined",spreadlines:["SpreadLines",!0],lgathered:["AmsEqnArray",null,null,null,"l",null,".5em","D"],rgathered:["AmsEqnArray",null,null,null,"r",null,".5em","D"]},s.MathtoolsMethods),new a.DelimiterMap("mathtools-delimiters",o.default.delimiter,{"\\lparen":"(","\\rparen":")"}),new a.CommandMap("mathtools-characters",{":":["CenterColon",!0]},s.MathtoolsMethods)},81274:function(t,e,r){var n=this&&this.__assign||function(){return n=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},n.apply(this,arguments)},o=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,a=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=a.next()).done;)i.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return i},a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.MathtoolsMethods=void 0;var s=i(r(55038)),l=r(14880),c=i(r(76914)),u=i(r(94032)),d=i(r(54420)),p=i(r(53972)),f=r(83045),m=r(56780),h=r(4498),g=i(r(87357)),v=i(r(39736)),y=r(53946);e.MathtoolsMethods={MtMatrix:function(t,r,n,o){var a=t.GetBrackets("\\begin{".concat(r.getName(),"}"),"c");return e.MathtoolsMethods.Array(t,r,n,o,a)},MtSmallMatrix:function(t,r,n,o,a){return a||(a=t.GetBrackets("\\begin{".concat(r.getName(),"}"),t.options.mathtools["smallmatrix-align"])),e.MathtoolsMethods.Array(t,r,n,o,a,s.default.Em(1/3),".2em","S",1)},MtMultlined:function(t,e){var r,n="\\begin{".concat(e.getName(),"}"),a=t.GetBrackets(n,t.options.mathtools["multlined-pos"]||"c"),i=a?t.GetBrackets(n,""):"";a&&!a.match(/^[cbt]$/)&&(i=(r=o([a,i],2))[0],a=r[1]),t.Push(e);var l=t.itemFactory.create("multlined",t,e);return l.arraydef={displaystyle:!0,rowspacing:".5em",width:i||"auto",columnwidth:"100%"},s.default.setArrayAlign(l,a||"c")},HandleShove:function(t,e,r){var n=t.stack.Top();if("multline"!==n.kind&&"multlined"!==n.kind)throw new d.default("CommandInMultlined","%1 can only appear within the multline or multlined environments",e);if(n.Size())throw new d.default("CommandAtTheBeginingOfLine","%1 must come at the beginning of the line",e);n.setProperty("shove",r);var o=t.GetBrackets(e),a=t.ParseArg(e);if(o){var i=t.create("node","mrow",[]),s=t.create("node","mspace",[],{width:o});"left"===r?(i.appendChild(s),i.appendChild(a)):(i.appendChild(a),i.appendChild(s)),a=i}t.Push(a)},SpreadLines:function(t,e){var r,n;if(t.stack.env.closing===e.getName()){delete t.stack.env.closing;var o=t.stack.Pop(),i=o.toMml(),s=o.getProperty("spread");if(i.isInferred)try{for(var l=a(p.default.getChildren(i)),c=l.next();!c.done;c=l.next()){var u=c.value;y.MathtoolsUtil.spreadLines(u,s)}}catch(t){r={error:t}}finally{try{c&&!c.done&&(n=l.return)&&n.call(l)}finally{if(r)throw r.error}}else y.MathtoolsUtil.spreadLines(i,s);t.Push(i)}else s=t.GetDimen("\\begin{".concat(e.getName(),"}")),e.setProperty("spread",s),t.Push(e)},Cases:function(t,e,r,n,o){var a=t.itemFactory.create("array").setProperty("casesEnv",e.getName());return a.arraydef={rowspacing:".2em",columnspacing:"1em",columnalign:"left"},"D"===o&&(a.arraydef.displaystyle=!0),a.setProperties({open:r,close:n}),t.Push(e),a},MathLap:function(t,e,r,o){var a=t.GetBrackets(e,"").trim(),i=t.create("node","mstyle",[t.create("node","mpadded",[t.ParseArg(e)],n({width:0},"r"===r?{}:{lspace:"l"===r?"-1width":"-.5width"}))],{"data-cramped":o});y.MathtoolsUtil.setDisplayLevel(i,a),t.Push(t.create("node","TeXAtom",[i]))},Cramped:function(t,e){var r=t.GetBrackets(e,"").trim(),n=t.ParseArg(e),o=t.create("node","mstyle",[n],{"data-cramped":!0});y.MathtoolsUtil.setDisplayLevel(o,r),t.Push(o)},MtLap:function(t,e,r){var n=s.default.internalMath(t,t.GetArgument(e),0),o=t.create("node","mpadded",n,{width:0});"r"!==r&&p.default.setAttribute(o,"lspace","l"===r?"-1width":"-.5width"),t.Push(o)},MathMakeBox:function(t,e){var r=t.GetBrackets(e),n=t.GetBrackets(e,"c"),o=t.create("node","mpadded",[t.ParseArg(e)]);r&&p.default.setAttribute(o,"width",r);var a=(0,h.lookup)(n,{c:"center",r:"right"},"");a&&p.default.setAttribute(o,"data-align",a),t.Push(o)},MathMBox:function(t,e){t.Push(t.create("node","mrow",[t.ParseArg(e)]))},UnderOverBracket:function(t,e){var r=(0,m.length2em)(t.GetBrackets(e,".1em"),.1),n=t.GetBrackets(e,".2em"),a=t.GetArgument(e),i=o("o"===e.charAt(1)?["over","accent","bottom"]:["under","accentunder","top"],3),l=i[0],c=i[1],d=i[2],f=(0,m.em)(r),h=new u.default(a,t.stack.env,t.configuration).mml(),g=new u.default(a,t.stack.env,t.configuration).mml(),v=t.create("node","mpadded",[t.create("node","mphantom",[g])],{style:"border: ".concat(f," solid; border-").concat(d,": none"),height:n,depth:0}),y=s.default.underOver(t,h,v,l,!0),b=p.default.getChildAt(p.default.getChildAt(y,0),0);p.default.setAttribute(b,c,!0),t.Push(y)},Aboxed:function(t,e){var r=y.MathtoolsUtil.checkAlignment(t,e);r.row.length%2==1&&r.row.push(t.create("node","mtd",[]));var n=t.GetArgument(e),o=t.string.substr(t.i);t.string=n+"&&\\endAboxed",t.i=0;var a=t.GetUpTo(e,"&"),i=t.GetUpTo(e,"&");t.GetUpTo(e,"\\endAboxed");var l=s.default.substituteArgs(t,[a,i],"\\rlap{\\boxed{#1{}#2}}\\kern.267em\\phantom{#1}&\\phantom{{}#2}\\kern.267em");t.string=l+o,t.i=0},ArrowBetweenLines:function(t,e){var r=y.MathtoolsUtil.checkAlignment(t,e);if(r.Size()||r.row.length)throw new d.default("BetweenLines","%1 must be on a row by itself",e);var n=t.GetStar(),o=t.GetBrackets(e,"\\Updownarrow");n&&(r.EndEntry(),r.EndEntry());var a=n?"\\quad"+o:o+"\\quad",i=new u.default(a,t.stack.env,t.configuration).mml();t.Push(i),r.EndEntry(),r.EndRow()},VDotsWithin:function(t,e){var r=t.stack.Top(),o=r.getProperty("flushspaceabove")===r.table.length,a="\\mmlToken{mi}{}"+t.GetArgument(e)+"\\mmlToken{mi}{}",i=new u.default(a,t.stack.env,t.configuration).mml(),s=t.create("node","mpadded",[t.create("node","mpadded",[t.create("node","mo",[t.create("text","⋮")])],n({width:0,lspace:"-.5width"},o?{height:"-.6em",voffset:"-.18em"}:{})),t.create("node","mphantom",[i])],{lspace:".5width"});t.Push(s)},ShortVDotsWithin:function(t,r){var n=t.stack.Top(),o=t.GetStar();e.MathtoolsMethods.FlushSpaceAbove(t,"\\MTFlushSpaceAbove"),!o&&n.EndEntry(),e.MathtoolsMethods.VDotsWithin(t,"\\vdotswithin"),o&&n.EndEntry(),e.MathtoolsMethods.FlushSpaceBelow(t,"\\MTFlushSpaceBelow")},FlushSpaceAbove:function(t,e){var r=y.MathtoolsUtil.checkAlignment(t,e);r.setProperty("flushspaceabove",r.table.length),r.addRowSpacing("-"+t.options.mathtools.shortvdotsadjustabove)},FlushSpaceBelow:function(t,e){var r=y.MathtoolsUtil.checkAlignment(t,e);r.Size()&&r.EndEntry(),r.EndRow(),r.addRowSpacing("-"+t.options.mathtools.shortvdotsadjustbelow)},PairedDelimiters:function(t,e,r,n,a,i,l,c){void 0===a&&(a="#1"),void 0===i&&(i=1),void 0===l&&(l=""),void 0===c&&(c="");var u=t.GetStar(),d=u?"":t.GetBrackets(e),p=o(u?["\\left","\\right"]:d?[d+"l",d+"r"]:["",""],2),f=p[0],m=p[1],h=u?"\\middle":d||"";if(i){for(var g=[],v=g.length;v<i;v++)g.push(t.GetArgument(e));l=s.default.substituteArgs(t,g,l),a=s.default.substituteArgs(t,g,a),c=s.default.substituteArgs(t,g,c)}a=a.replace(/\\delimsize/g,h),t.string=[l,f,r,a,m,n,c,t.string.substr(t.i)].reduce((function(e,r){return s.default.addArgs(t,e,r)}),""),t.i=0,s.default.checkMaxMacros(t)},DeclarePairedDelimiter:function(t,e){var r=g.default.GetCsNameArgument(t,e),n=t.GetArgument(e),o=t.GetArgument(e);y.MathtoolsUtil.addPairedDelims(t.configuration,r,[n,o])},DeclarePairedDelimiterX:function(t,e){var r=g.default.GetCsNameArgument(t,e),n=g.default.GetArgCount(t,e),o=t.GetArgument(e),a=t.GetArgument(e),i=t.GetArgument(e);y.MathtoolsUtil.addPairedDelims(t.configuration,r,[o,a,i,n])},DeclarePairedDelimiterXPP:function(t,e){var r=g.default.GetCsNameArgument(t,e),n=g.default.GetArgCount(t,e),o=t.GetArgument(e),a=t.GetArgument(e),i=t.GetArgument(e),s=t.GetArgument(e),l=t.GetArgument(e);y.MathtoolsUtil.addPairedDelims(t.configuration,r,[a,i,l,n,o,s])},CenterColon:function(t,e,r,o,a){void 0===o&&(o=!1),void 0===a&&(a=!1);var i=t.options.mathtools,s=t.create("token","mo",{},":");if(r&&(i.centercolon||o)){var l=i["centercolon-offset"];s=t.create("node","mpadded",[s],n({voffset:l,height:"+".concat(l),depth:"-".concat(l)},a?{width:i["thincolon-dw"],lspace:i["thincolon-dx"]}:{}))}t.Push(s)},Relation:function(t,e,r,n){t.options.mathtools["use-unicode"]&&n?t.Push(t.create("token","mo",{texClass:f.TEXCLASS.REL},n)):(r="\\mathrel{"+r.replace(/:/g,"\\MTThinColon").replace(/-/g,"\\mathrel{-}")+"}",t.string=s.default.addArgs(t,r,t.string.substr(t.i)),t.i=0)},NArrow:function(t,e,r,n){t.Push(t.create("node","TeXAtom",[t.create("token","mtext",{},r),t.create("node","mpadded",[t.create("node","mpadded",[t.create("node","menclose",[t.create("node","mspace",[],{height:".2em",depth:0,width:".4em"})],{notation:"updiagonalstrike","data-thickness":".05em","data-padding":0})],{width:0,lspace:"-.5width",voffset:n}),t.create("node","mphantom",[t.create("token","mtext",{},r)])],{width:0,lspace:"-.5width"})],{texClass:f.TEXCLASS.REL}))},SplitFrac:function(t,e,r){var n=t.ParseArg(e),o=t.ParseArg(e);t.Push(t.create("node","mstyle",[t.create("node","mfrac",[t.create("node","mstyle",[n,t.create("token","mi"),t.create("token","mspace",{width:"1em"})],{scriptlevel:0}),t.create("node","mstyle",[t.create("token","mspace",{width:"1em"}),t.create("token","mi"),o],{scriptlevel:0})],{linethickness:0,numalign:"left",denomalign:"right"})],{displaystyle:r,scriptlevel:0}))},XMathStrut:function(t,e){var r=t.GetBrackets(e),n=t.GetArgument(e);n=y.MathtoolsUtil.plusOrMinus(e,n),r=y.MathtoolsUtil.plusOrMinus(e,r||n),t.Push(t.create("node","TeXAtom",[t.create("node","mpadded",[t.create("node","mphantom",[t.create("token","mo",{stretchy:!1},"(")])],{width:0,height:n+"height",depth:r+"depth"})],{texClass:f.TEXCLASS.ORD}))},Prescript:function(t,e){var r=y.MathtoolsUtil.getScript(t,e,"sup"),n=y.MathtoolsUtil.getScript(t,e,"sub"),o=y.MathtoolsUtil.getScript(t,e,"arg");if(p.default.isType(r,"none")&&p.default.isType(n,"none"))t.Push(o);else{var a=t.create("node","mmultiscripts",[o]);p.default.getChildren(a).push(null,null),p.default.appendChildren(a,[t.create("node","mprescripts"),n,r]),a.setProperty("fixPrescript",!0),t.Push(a)}},NewTagForm:function(t,e,r){void 0===r&&(r=!1);var n=t.tags;if(!("mtFormats"in n))throw new d.default("TagsNotMT","%1 can only be used with ams or mathtools tags",e);var o=t.GetArgument(e).trim();if(!o)throw new d.default("InvalidTagFormID","Tag form name can't be empty");var a=t.GetBrackets(e,""),i=t.GetArgument(e),s=t.GetArgument(e);if(!r&&n.mtFormats.has(o))throw new d.default("DuplicateTagForm","Duplicate tag form: %1",o);n.mtFormats.set(o,[i,s,a])},UseTagForm:function(t,e){var r=t.tags;if(!("mtFormats"in r))throw new d.default("TagsNotMT","%1 can only be used with ams or mathtools tags",e);var n=t.GetArgument(e).trim();if(n){if(!r.mtFormats.has(n))throw new d.default("UndefinedTagForm","Undefined tag form: %1",n);r.mtCurrent=r.mtFormats.get(n)}else r.mtCurrent=null},SetOptions:function(t,e){var r,n,o=t.options.mathtools;if(!o["allow-mathtoolsset"])throw new d.default("ForbiddenMathtoolsSet","%1 is disabled",e);var i={};Object.keys(o).forEach((function(t){"pariedDelimiters"!==t&&"tagforms"!==t&&"allow-mathtoolsset"!==t&&(i[t]=1)}));var l=t.GetArgument(e),c=s.default.keyvalOptions(l,i,!0);try{for(var u=a(Object.keys(c)),p=u.next();!p.done;p=u.next()){var f=p.value;o[f]=c[f]}}catch(t){r={error:t}}finally{try{p&&!p.done&&(n=u.return)&&n.call(u)}finally{if(r)throw r.error}}},Array:c.default.Array,Macro:c.default.Macro,xArrow:l.AmsMethods.xArrow,HandleRef:l.AmsMethods.HandleRef,AmsEqnArray:l.AmsMethods.AmsEqnArray,MacroWithTemplate:v.default.MacroWithTemplate}},43928:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,a=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=a.next()).done;)i.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return i},s=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.MathtoolsTagFormat=void 0;var l=s(r(54420)),c=r(75723),u=0;e.MathtoolsTagFormat=function(t,e){var r=e.parseOptions.options.tags;"base"!==r&&t.tags.hasOwnProperty(r)&&c.TagsFactory.add(r,t.tags[r]);var n=function(t){function r(){var r,n,o=t.call(this)||this;o.mtFormats=new Map,o.mtCurrent=null;var i=e.parseOptions.options.mathtools.tagforms;try{for(var s=a(Object.keys(i)),c=s.next();!c.done;c=s.next()){var u=c.value;if(!Array.isArray(i[u])||3!==i[u].length)throw new l.default("InvalidTagFormDef",'The tag form definition for "%1" should be an array fo three strings',u);o.mtFormats.set(u,i[u])}}catch(t){r={error:t}}finally{try{c&&!c.done&&(n=s.return)&&n.call(s)}finally{if(r)throw r.error}}return o}return o(r,t),r.prototype.formatTag=function(e){if(this.mtCurrent){var r=i(this.mtCurrent,3),n=r[0],o=r[1],a=r[2];return a?"".concat(n).concat(a,"{").concat(e,"}").concat(o):"".concat(n).concat(e).concat(o)}return t.prototype.formatTag.call(this,e)},r}(c.TagsFactory.create(e.parseOptions.options.tags).constructor),s="MathtoolsTags-"+ ++u;c.TagsFactory.add(s,n),e.parseOptions.options.tags=s}},53946:function(t,e,r){var n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,a=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=a.next()).done;)i.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return i},o=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.MathtoolsUtil=void 0;var a=r(31201),i=o(r(55038)),s=o(r(94032)),l=o(r(54420)),c=r(75735),u=r(4498),d=r(81274),p=r(73414);e.MathtoolsUtil={setDisplayLevel:function(t,e){if(e){var r=n((0,u.lookup)(e,{"\\displaystyle":[!0,0],"\\textstyle":[!1,0],"\\scriptstyle":[!1,1],"\\scriptscriptstyle":[!1,2]},[null,null]),2),o=r[0],a=r[1];null!==o&&(t.attributes.set("displaystyle",o),t.attributes.set("scriptlevel",a))}},checkAlignment:function(t,e){var r=t.stack.Top();if(r.kind!==a.EqnArrayItem.prototype.kind)throw new l.default("NotInAlignment","%1 can only be used in aligment environments",e);return r},addPairedDelims:function(t,e,r){t.handlers.retrieve(p.PAIREDDELIMS).add(e,new c.Macro(e,d.MathtoolsMethods.PairedDelimiters,r))},spreadLines:function(t,e){if(t.isKind("mtable")){var r=t.attributes.get("rowspacing");if(r){var n=i.default.dimen2em(e);r=r.split(/ /).map((function(t){return i.default.Em(Math.max(0,i.default.dimen2em(t)+n))})).join(" ")}else r=e;t.attributes.set("rowspacing",r)}},plusOrMinus:function(t,e){if(!(e=e.trim()).match(/^[-+]?(?:\d+(?:\.\d*)?|\.\d+)$/))throw new l.default("NotANumber","Argument to %1 is not a number",t);return e.match(/^[-+]/)?e:"+"+e},getScript:function(t,e,r){var n=i.default.trimSpaces(t.GetArgument(e));if(""===n)return t.create("node","none");var o=t.options.mathtools["prescript-".concat(r,"-format")];return o&&(n="".concat(o,"{").concat(n,"}")),new s.default(n,t.stack.env,t.configuration).mml()}}},33289:function(t,e,r){var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.MhchemConfiguration=void 0;var o=r(63401),a=r(65695),i=n(r(54420)),s=n(r(76914)),l=r(14880),c=r(84752),u={};u.Macro=s.default.Macro,u.xArrow=l.AmsMethods.xArrow,u.Machine=function(t,e,r){var n,o=t.GetArgument(e);try{n=c.mhchemParser.toTex(o,r)}catch(t){throw new i.default(t[0],t[1])}t.string=n+t.string.substr(t.i),t.i=0},new a.CommandMap("mhchem",{ce:["Machine","ce"],pu:["Machine","pu"],longrightleftharpoons:["Macro","\\stackrel{\\textstyle{-}\\!\\!{\\rightharpoonup}}{\\smash{{\\leftharpoondown}\\!\\!{-}}}"],longRightleftharpoons:["Macro","\\stackrel{\\textstyle{-}\\!\\!{\\rightharpoonup}}{\\smash{\\leftharpoondown}}"],longLeftrightharpoons:["Macro","\\stackrel{\\textstyle\\vphantom{{-}}{\\rightharpoonup}}{\\smash{{\\leftharpoondown}\\!\\!{-}}}"],longleftrightarrows:["Macro","\\stackrel{\\longrightarrow}{\\smash{\\longleftarrow}\\Rule{0px}{.25em}{0px}}"],tripledash:["Macro","\\vphantom{-}\\raise2mu{\\kern2mu\\tiny\\text{-}\\kern1mu\\text{-}\\kern1mu\\text{-}\\kern2mu}"],xleftrightarrow:["xArrow",8596,6,6],xrightleftharpoons:["xArrow",8652,5,7],xRightleftharpoons:["xArrow",8652,5,7],xLeftrightharpoons:["xArrow",8652,5,7]},u),e.MhchemConfiguration=o.Configuration.create("mhchem",{handler:{macro:["mhchem"]}})},37515:function(t,e,r){var n,o=this&&this.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(e,r);o&&!("get"in o?!e.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,n,o)}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),a=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),i=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)"default"!==r&&Object.prototype.hasOwnProperty.call(t,r)&&o(e,t,r);return a(e,t),e},s=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.NewcommandConfiguration=void 0;var l=r(63401),c=r(36688),u=s(r(87357));r(73949);var d=s(r(2362)),p=i(r(65695));e.NewcommandConfiguration=l.Configuration.create("newcommand",{handler:{macro:["Newcommand-macros"]},items:(n={},n[c.BeginEnvItem.prototype.kind]=c.BeginEnvItem,n),options:{maxMacros:1e3},init:function(t){new p.DelimiterMap(u.default.NEW_DELIMITER,d.default.delimiter,{}),new p.CommandMap(u.default.NEW_COMMAND,{},{}),new p.EnvironmentMap(u.default.NEW_ENVIRONMENT,d.default.environment,{},{}),t.append(l.Configuration.local({handler:{character:[],delimiter:[u.default.NEW_DELIMITER],macro:[u.default.NEW_DELIMITER,u.default.NEW_COMMAND],environment:[u.default.NEW_ENVIRONMENT]},priority:-1}))}})},36688:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),a=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.BeginEnvItem=void 0;var i=a(r(54420)),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"beginEnv"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(e.isKind("end")){if(e.getName()!==this.getName())throw new i.default("EnvBadEnd","\\begin{%1} ended with \\end{%2}",this.getName(),e.getName());return[[this.factory.create("mml",this.toMml())],!0]}if(e.isKind("stop"))throw new i.default("EnvMissingEnd","Missing \\end{%1}",this.getName());return t.prototype.checkItem.call(this,e)},e}(r(26539).BaseItem);e.BeginEnvItem=s},73949:function(t,e,r){var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var o=n(r(39736));new(r(65695).CommandMap)("Newcommand-macros",{newcommand:"NewCommand",renewcommand:"NewCommand",newenvironment:"NewEnvironment",renewenvironment:"NewEnvironment",def:"MacroDef",let:"Let"},o.default)},39736:function(t,e,r){var n=this&&this.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(e,r);o&&!("get"in o?!e.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,n,o)}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),o=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),a=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)"default"!==r&&Object.prototype.hasOwnProperty.call(t,r)&&n(e,t,r);return o(e,t),e},i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var s=i(r(54420)),l=a(r(65695)),c=i(r(76914)),u=i(r(55038)),d=i(r(87357)),p={NewCommand:function(t,e){var r=d.default.GetCsNameArgument(t,e),n=d.default.GetArgCount(t,e),o=t.GetBrackets(e),a=t.GetArgument(e);d.default.addMacro(t,r,p.Macro,[a,n,o])},NewEnvironment:function(t,e){var r=u.default.trimSpaces(t.GetArgument(e)),n=d.default.GetArgCount(t,e),o=t.GetBrackets(e),a=t.GetArgument(e),i=t.GetArgument(e);d.default.addEnvironment(t,r,p.BeginEnv,[!0,a,i,n,o])},MacroDef:function(t,e){var r=d.default.GetCSname(t,e),n=d.default.GetTemplate(t,e,"\\"+r),o=t.GetArgument(e);n instanceof Array?d.default.addMacro(t,r,p.MacroWithTemplate,[o].concat(n)):d.default.addMacro(t,r,p.Macro,[o,n])},Let:function(t,e){var r=d.default.GetCSname(t,e),n=t.GetNext();"="===n&&(t.i++,n=t.GetNext());var o=t.configuration.handlers;if("\\"!==n){t.i++;var a=o.get("delimiter").lookup(n);a?d.default.addDelimiter(t,"\\"+r,a.char,a.attributes):d.default.addMacro(t,r,p.Macro,[n])}else{e=d.default.GetCSname(t,e);var i=o.get("delimiter").lookup("\\"+e);if(i)return void d.default.addDelimiter(t,"\\"+r,i.char,i.attributes);var s=o.get("macro").applicable(e);if(!s)return;if(s instanceof l.MacroMap){var c=s.lookup(e);return void d.default.addMacro(t,r,c.func,c.args,c.symbol)}i=s.lookup(e);var u=d.default.disassembleSymbol(r,i);d.default.addMacro(t,r,(function(t,e){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];var o=d.default.assembleSymbol(r);return s.parser(t,o)}),u)}},MacroWithTemplate:function(t,e,r,n){for(var o=[],a=4;a<arguments.length;a++)o[a-4]=arguments[a];var i=parseInt(n,10);if(i){var l=[];if(t.GetNext(),o[0]&&!d.default.MatchParam(t,o[0]))throw new s.default("MismatchUseDef","Use of %1 doesn't match its definition",e);for(var c=0;c<i;c++)l.push(d.default.GetParameter(t,e,o[c+1]));r=u.default.substituteArgs(t,l,r)}t.string=u.default.addArgs(t,r,t.string.slice(t.i)),t.i=0,u.default.checkMaxMacros(t)},BeginEnv:function(t,e,r,n,o,a){if(e.getProperty("end")&&t.stack.env.closing===e.getName()){delete t.stack.env.closing;var i=t.string.slice(t.i);return t.string=n,t.i=0,t.Parse(),t.string=i,t.i=0,t.itemFactory.create("end").setProperty("name",e.getName())}if(o){var s=[];if(null!=a){var l=t.GetBrackets("\\begin{"+e.getName()+"}");s.push(null==l?a:l)}for(var c=s.length;c<o;c++)s.push(t.GetArgument("\\begin{"+e.getName()+"}"));r=u.default.substituteArgs(t,s,r),n=u.default.substituteArgs(t,[],n)}return t.string=u.default.addArgs(t,r,t.string.slice(t.i)),t.i=0,t.itemFactory.create("beginEnv").setProperty("name",e.getName())}};p.Macro=c.default.Macro,e.default=p},87357:function(t,e,r){var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var o,a=n(r(55038)),i=n(r(54420)),s=r(75735);!function(t){function e(t,e){return t.string.substr(t.i,e.length)!==e||e.match(/\\[a-z]+$/i)&&t.string.charAt(t.i+e.length).match(/[a-z]/i)?0:(t.i+=e.length,1)}t.disassembleSymbol=function(t,e){var r=[t,e.char];if(e.attributes)for(var n in e.attributes)r.push(n),r.push(e.attributes[n]);return r},t.assembleSymbol=function(t){for(var e=t[0],r=t[1],n={},o=2;o<t.length;o+=2)n[t[o]]=t[o+1];return new s.Symbol(e,r,n)},t.GetCSname=function(t,e){if("\\"!==t.GetNext())throw new i.default("MissingCS","%1 must be followed by a control sequence",e);return a.default.trimSpaces(t.GetArgument(e)).substr(1)},t.GetCsNameArgument=function(t,e){var r=a.default.trimSpaces(t.GetArgument(e));if("\\"===r.charAt(0)&&(r=r.substr(1)),!r.match(/^(.|[a-z]+)$/i))throw new i.default("IllegalControlSequenceName","Illegal control sequence name for %1",e);return r},t.GetArgCount=function(t,e){var r=t.GetBrackets(e);if(r&&!(r=a.default.trimSpaces(r)).match(/^[0-9]+$/))throw new i.default("IllegalParamNumber","Illegal number of parameters specified in %1",e);return r},t.GetTemplate=function(t,e,r){for(var n=t.GetNext(),o=[],a=0,s=t.i;t.i<t.string.length;){if("#"===(n=t.GetNext())){if(s!==t.i&&(o[a]=t.string.substr(s,t.i-s)),!(n=t.string.charAt(++t.i)).match(/^[1-9]$/))throw new i.default("CantUseHash2","Illegal use of # in template for %1",r);if(parseInt(n)!==++a)throw new i.default("SequentialParam","Parameters for %1 must be numbered sequentially",r);s=t.i+1}else if("{"===n)return s!==t.i&&(o[a]=t.string.substr(s,t.i-s)),o.length>0?[a.toString()].concat(o):a;t.i++}throw new i.default("MissingReplacementString","Missing replacement string for definition of %1",e)},t.GetParameter=function(t,r,n){if(null==n)return t.GetArgument(r);for(var o=t.i,a=0,s=0;t.i<t.string.length;){var l=t.string.charAt(t.i);if("{"===l)t.i===o&&(s=1),t.GetArgument(r),a=t.i-o;else{if(e(t,n))return s&&(o++,a-=2),t.string.substr(o,a);if("\\"===l){t.i++,a++,s=0;var c=t.string.substr(t.i).match(/[a-z]+|./i);c&&(t.i+=c[0].length,a=t.i-o)}else t.i++,a++,s=0}}throw new i.default("RunawayArgument","Runaway argument for %1?",r)},t.MatchParam=e,t.addDelimiter=function(e,r,n,o){e.configuration.handlers.retrieve(t.NEW_DELIMITER).add(r,new s.Symbol(r,n,o))},t.addMacro=function(e,r,n,o,a){void 0===a&&(a=""),e.configuration.handlers.retrieve(t.NEW_COMMAND).add(r,new s.Macro(a||r,n,o))},t.addEnvironment=function(e,r,n,o){e.configuration.handlers.retrieve(t.NEW_ENVIRONMENT).add(r,new s.Macro(r,n,o))},t.NEW_DELIMITER="new-Delimiter",t.NEW_COMMAND="new-Command",t.NEW_ENVIRONMENT="new-Environment"}(o||(o={})),e.default=o},92633:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.NoErrorsConfiguration=void 0;var n=r(63401);e.NoErrorsConfiguration=n.Configuration.create("noerrors",{nodes:{error:function(t,e,r,n){var o=t.create("token","mtext",{},n.replace(/\n/g," "));return t.create("node","merror",[o],{"data-mjx-error":e,title:e})}}})},31664:function(t,e,r){var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.NoUndefinedConfiguration=void 0;var o=r(63401);e.NoUndefinedConfiguration=o.Configuration.create("noundefined",{fallback:{macro:function(t,e){var r,o,a=t.create("text","\\"+e),i=t.options.noundefined||{},s={};try{for(var l=n(["color","background","size"]),c=l.next();!c.done;c=l.next()){var u=c.value;i[u]&&(s["math"+u]=i[u])}}catch(t){r={error:t}}finally{try{c&&!c.done&&(o=l.return)&&o.call(l)}finally{if(r)throw r.error}}t.Push(t.create("node","mtext",[],s,a))}},options:{noundefined:{color:"red",background:"",size:""}},priority:3})},69553:(t,e,r)=>{var n;Object.defineProperty(e,"__esModule",{value:!0}),e.PhysicsConfiguration=void 0;var o=r(63401),a=r(95389);r(38125),e.PhysicsConfiguration=o.Configuration.create("physics",{handler:{macro:["Physics-automatic-bracing-macros","Physics-vector-macros","Physics-vector-mo","Physics-vector-mi","Physics-derivative-macros","Physics-expressions-macros","Physics-quick-quad-macros","Physics-bra-ket-macros","Physics-matrix-macros"],character:["Physics-characters"],environment:["Physics-aux-envs"]},items:(n={},n[a.AutoOpen.prototype.kind]=a.AutoOpen,n),options:{physics:{italicdiff:!1,arrowdel:!1}}})},95389:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),a=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.AutoOpen=void 0;var i=r(26539),s=a(r(55038)),l=a(r(53972)),c=a(r(94032)),u=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.openCount=0,e}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"auto open"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.toMml=function(){var e=this.factory.configuration.parser,r=this.getProperty("right");if(this.getProperty("smash")){var n=t.prototype.toMml.call(this),o=e.create("node","mpadded",[n],{height:0,depth:0});this.Clear(),this.Push(e.create("node","TeXAtom",[o]))}r&&this.Push(new c.default(r,e.stack.env,e.configuration).mml());var a=s.default.fenced(this.factory.configuration,this.getProperty("open"),t.prototype.toMml.call(this),this.getProperty("close"),this.getProperty("big"));return l.default.removeProperties(a,"open","close","texClass"),a},e.prototype.checkItem=function(e){if(e.isKind("mml")&&1===e.Size()){var r=e.toMml();r.isKind("mo")&&r.getText()===this.getProperty("open")&&this.openCount++}var n=e.getProperty("autoclose");return n&&n===this.getProperty("close")&&!this.openCount--?this.getProperty("ignore")?(this.Clear(),[[],!0]):[[this.toMml()],!0]:t.prototype.checkItem.call(this,e)},e.errors=Object.assign(Object.create(i.BaseItem.errors),{stop:["ExtraOrMissingDelims","Extra open or missing close delimiter"]}),e}(i.BaseItem);e.AutoOpen=u},38125:function(t,e,r){var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(65695),a=n(r(92909)),i=r(28027),s=n(r(2362)),l=r(83045);new o.CommandMap("Physics-automatic-bracing-macros",{quantity:"Quantity",qty:"Quantity",pqty:["Quantity","(",")",!0],bqty:["Quantity","[","]",!0],vqty:["Quantity","|","|",!0],Bqty:["Quantity","\\{","\\}",!0],absolutevalue:["Quantity","|","|",!0],abs:["Quantity","|","|",!0],norm:["Quantity","\\|","\\|",!0],evaluated:"Eval",eval:"Eval",order:["Quantity","(",")",!0,"O",i.TexConstant.Variant.CALLIGRAPHIC],commutator:"Commutator",comm:"Commutator",anticommutator:["Commutator","\\{","\\}"],acomm:["Commutator","\\{","\\}"],poissonbracket:["Commutator","\\{","\\}"],pb:["Commutator","\\{","\\}"]},a.default),new o.CharacterMap("Physics-vector-mo",s.default.mathchar0mo,{dotproduct:["⋅",{mathvariant:i.TexConstant.Variant.BOLD}],vdot:["⋅",{mathvariant:i.TexConstant.Variant.BOLD}],crossproduct:"×",cross:"×",cp:"×",gradientnabla:["∇",{mathvariant:i.TexConstant.Variant.BOLD}]}),new o.CharacterMap("Physics-vector-mi",s.default.mathchar0mi,{real:["ℜ",{mathvariant:i.TexConstant.Variant.NORMAL}],imaginary:["ℑ",{mathvariant:i.TexConstant.Variant.NORMAL}]}),new o.CommandMap("Physics-vector-macros",{vnabla:"Vnabla",vectorbold:"VectorBold",vb:"VectorBold",vectorarrow:["StarMacro",1,"\\vec{\\vb","{#1}}"],va:["StarMacro",1,"\\vec{\\vb","{#1}}"],vectorunit:["StarMacro",1,"\\hat{\\vb","{#1}}"],vu:["StarMacro",1,"\\hat{\\vb","{#1}}"],gradient:["OperatorApplication","\\vnabla","(","["],grad:["OperatorApplication","\\vnabla","(","["],divergence:["VectorOperator","\\vnabla\\vdot","(","["],div:["VectorOperator","\\vnabla\\vdot","(","["],curl:["VectorOperator","\\vnabla\\crossproduct","(","["],laplacian:["OperatorApplication","\\nabla^2","(","["]},a.default),new o.CommandMap("Physics-expressions-macros",{sin:"Expression",sinh:"Expression",arcsin:"Expression",asin:"Expression",cos:"Expression",cosh:"Expression",arccos:"Expression",acos:"Expression",tan:"Expression",tanh:"Expression",arctan:"Expression",atan:"Expression",csc:"Expression",csch:"Expression",arccsc:"Expression",acsc:"Expression",sec:"Expression",sech:"Expression",arcsec:"Expression",asec:"Expression",cot:"Expression",coth:"Expression",arccot:"Expression",acot:"Expression",exp:["Expression",!1],log:"Expression",ln:"Expression",det:["Expression",!1],Pr:["Expression",!1],tr:["Expression",!1],trace:["Expression",!1,"tr"],Tr:["Expression",!1],Trace:["Expression",!1,"Tr"],rank:"NamedFn",erf:["Expression",!1],Residue:["Macro","\\mathrm{Res}"],Res:["OperatorApplication","\\Residue","(","[","{"],principalvalue:["OperatorApplication","{\\cal P}"],pv:["OperatorApplication","{\\cal P}"],PV:["OperatorApplication","{\\rm P.V.}"],Re:["OperatorApplication","\\mathrm{Re}","{"],Im:["OperatorApplication","\\mathrm{Im}","{"],sine:["NamedFn","sin"],hypsine:["NamedFn","sinh"],arcsine:["NamedFn","arcsin"],asine:["NamedFn","asin"],cosine:["NamedFn","cos"],hypcosine:["NamedFn","cosh"],arccosine:["NamedFn","arccos"],acosine:["NamedFn","acos"],tangent:["NamedFn","tan"],hyptangent:["NamedFn","tanh"],arctangent:["NamedFn","arctan"],atangent:["NamedFn","atan"],cosecant:["NamedFn","csc"],hypcosecant:["NamedFn","csch"],arccosecant:["NamedFn","arccsc"],acosecant:["NamedFn","acsc"],secant:["NamedFn","sec"],hypsecant:["NamedFn","sech"],arcsecant:["NamedFn","arcsec"],asecant:["NamedFn","asec"],cotangent:["NamedFn","cot"],hypcotangent:["NamedFn","coth"],arccotangent:["NamedFn","arccot"],acotangent:["NamedFn","acot"],exponential:["NamedFn","exp"],logarithm:["NamedFn","log"],naturallogarithm:["NamedFn","ln"],determinant:["NamedFn","det"],Probability:["NamedFn","Pr"]},a.default),new o.CommandMap("Physics-quick-quad-macros",{qqtext:"Qqtext",qq:"Qqtext",qcomma:["Macro","\\qqtext*{,}"],qc:["Macro","\\qqtext*{,}"],qcc:["Qqtext","c.c."],qif:["Qqtext","if"],qthen:["Qqtext","then"],qelse:["Qqtext","else"],qotherwise:["Qqtext","otherwise"],qunless:["Qqtext","unless"],qgiven:["Qqtext","given"],qusing:["Qqtext","using"],qassume:["Qqtext","assume"],qsince:["Qqtext","since"],qlet:["Qqtext","let"],qfor:["Qqtext","for"],qall:["Qqtext","all"],qeven:["Qqtext","even"],qodd:["Qqtext","odd"],qinteger:["Qqtext","integer"],qand:["Qqtext","and"],qor:["Qqtext","or"],qas:["Qqtext","as"],qin:["Qqtext","in"]},a.default),new o.CommandMap("Physics-derivative-macros",{diffd:"DiffD",flatfrac:["Macro","\\left.#1\\middle/#2\\right.",2],differential:["Differential","\\diffd"],dd:["Differential","\\diffd"],variation:["Differential","\\delta"],var:["Differential","\\delta"],derivative:["Derivative",2,"\\diffd"],dv:["Derivative",2,"\\diffd"],partialderivative:["Derivative",3,"\\partial"],pderivative:["Derivative",3,"\\partial"],pdv:["Derivative",3,"\\partial"],functionalderivative:["Derivative",2,"\\delta"],fderivative:["Derivative",2,"\\delta"],fdv:["Derivative",2,"\\delta"]},a.default),new o.CommandMap("Physics-bra-ket-macros",{bra:"Bra",ket:"Ket",innerproduct:"BraKet",ip:"BraKet",braket:"BraKet",outerproduct:"KetBra",dyad:"KetBra",ketbra:"KetBra",op:"KetBra",expectationvalue:"Expectation",expval:"Expectation",ev:"Expectation",matrixelement:"MatrixElement",matrixel:"MatrixElement",mel:"MatrixElement"},a.default),new o.CommandMap("Physics-matrix-macros",{matrixquantity:"MatrixQuantity",mqty:"MatrixQuantity",pmqty:["Macro","\\mqty(#1)",1],Pmqty:["Macro","\\mqty*(#1)",1],bmqty:["Macro","\\mqty[#1]",1],vmqty:["Macro","\\mqty|#1|",1],smallmatrixquantity:["MatrixQuantity",!0],smqty:["MatrixQuantity",!0],spmqty:["Macro","\\smqty(#1)",1],sPmqty:["Macro","\\smqty*(#1)",1],sbmqty:["Macro","\\smqty[#1]",1],svmqty:["Macro","\\smqty|#1|",1],matrixdeterminant:["Macro","\\vmqty{#1}",1],mdet:["Macro","\\vmqty{#1}",1],smdet:["Macro","\\svmqty{#1}",1],identitymatrix:"IdentityMatrix",imat:"IdentityMatrix",xmatrix:"XMatrix",xmat:"XMatrix",zeromatrix:["Macro","\\xmat{0}{#1}{#2}",2],zmat:["Macro","\\xmat{0}{#1}{#2}",2],paulimatrix:"PauliMatrix",pmat:"PauliMatrix",diagonalmatrix:"DiagonalMatrix",dmat:"DiagonalMatrix",antidiagonalmatrix:["DiagonalMatrix",!0],admat:["DiagonalMatrix",!0]},a.default),new o.EnvironmentMap("Physics-aux-envs",s.default.environment,{smallmatrix:["Array",null,null,null,"c","0.333em",".2em","S",1]},a.default),new o.MacroMap("Physics-characters",{"|":["AutoClose",l.TEXCLASS.ORD],")":"AutoClose","]":"AutoClose"},a.default)},92909:function(t,e,r){var n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,a=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=a.next()).done;)i.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return i},o=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var a=o(r(76914)),i=o(r(94032)),s=o(r(54420)),l=r(83045),c=o(r(55038)),u=o(r(53972)),d=r(12443),p={},f={"(":")","[":"]","{":"}","|":"|"},m=/^(b|B)i(g{1,2})$/;p.Quantity=function(t,e,r,n,o,a,d){void 0===r&&(r="("),void 0===n&&(n=")"),void 0===o&&(o=!1),void 0===a&&(a=""),void 0===d&&(d="");var p=!!o&&t.GetStar(),h=t.GetNext(),g=t.i,v=null;if("\\"===h){if(t.i++,!(v=t.GetCS()).match(m)){var y=t.create("node","mrow");return t.Push(c.default.fenced(t.configuration,r,y,n)),void(t.i=g)}h=t.GetNext()}var b=f[h];if(o&&"{"!==h)throw new s.default("MissingArgFor","Missing argument for %1",t.currentCS);if(!b)return y=t.create("node","mrow"),t.Push(c.default.fenced(t.configuration,r,y,n)),void(t.i=g);if(a){var x=t.create("token","mi",{texClass:l.TEXCLASS.OP},a);d&&u.default.setAttribute(x,"mathvariant",d),t.Push(t.itemFactory.create("fn",x))}if("{"===h){var _=t.GetArgument(e);return h=o?r:"\\{",b=o?n:"\\}",_=p?h+" "+_+" "+b:v?"\\"+v+"l"+h+" "+_+" \\"+v+"r"+b:"\\left"+h+" "+_+" \\right"+b,void t.Push(new i.default(_,t.stack.env,t.configuration).mml())}o&&(h=r,b=n),t.i++,t.Push(t.itemFactory.create("auto open").setProperties({open:h,close:b,big:v}))},p.Eval=function(t,e){var r=t.GetStar(),n=t.GetNext();if("{"!==n){if("("===n||"["===n)return t.i++,void t.Push(t.itemFactory.create("auto open").setProperties({open:n,close:"|",smash:r,right:"\\vphantom{\\int}"}));throw new s.default("MissingArgFor","Missing argument for %1",t.currentCS)}var o=t.GetArgument(e),a="\\left. "+(r?"\\smash{"+o+"}":o)+" \\vphantom{\\int}\\right|";t.string=t.string.slice(0,t.i)+a+t.string.slice(t.i)},p.Commutator=function(t,e,r,n){void 0===r&&(r="["),void 0===n&&(n="]");var o=t.GetStar(),a=t.GetNext(),l=null;if("\\"===a){if(t.i++,!(l=t.GetCS()).match(m))throw new s.default("MissingArgFor","Missing argument for %1",t.currentCS);a=t.GetNext()}if("{"!==a)throw new s.default("MissingArgFor","Missing argument for %1",t.currentCS);var c=t.GetArgument(e)+","+t.GetArgument(e);c=o?r+" "+c+" "+n:l?"\\"+l+"l"+r+" "+c+" \\"+l+"r"+n:"\\left"+r+" "+c+" \\right"+n,t.Push(new i.default(c,t.stack.env,t.configuration).mml())};var h=[65,90],g=[97,122],v=[913,937],y=[945,969],b=[48,57];function x(t,e){return t>=e[0]&&t<=e[1]}function _(t,e,r,n){var o=t.configuration.parser,a=d.NodeFactory.createToken(t,e,r,n),i=n.codePointAt(0);return 1===n.length&&!o.stack.env.font&&o.stack.env.vectorFont&&(x(i,h)||x(i,g)||x(i,v)||x(i,b)||x(i,y)&&o.stack.env.vectorStar||u.default.getAttribute(a,"accent"))&&u.default.setAttribute(a,"mathvariant",o.stack.env.vectorFont),a}p.VectorBold=function(t,e){var r=t.GetStar(),n=t.GetArgument(e),o=t.configuration.nodeFactory.get("token"),a=t.stack.env.font;delete t.stack.env.font,t.configuration.nodeFactory.set("token",_),t.stack.env.vectorFont=r?"bold-italic":"bold",t.stack.env.vectorStar=r;var s=new i.default(n,t.stack.env,t.configuration).mml();a&&(t.stack.env.font=a),delete t.stack.env.vectorFont,delete t.stack.env.vectorStar,t.configuration.nodeFactory.set("token",o),t.Push(s)},p.StarMacro=function(t,e,r){for(var n=[],o=3;o<arguments.length;o++)n[o-3]=arguments[o];var a=t.GetStar(),i=[];if(r)for(var s=i.length;s<r;s++)i.push(t.GetArgument(e));var l=n.join(a?"*":"");l=c.default.substituteArgs(t,i,l),t.string=c.default.addArgs(t,l,t.string.slice(t.i)),t.i=0,c.default.checkMaxMacros(t)};var w=function(t,e,r,n,o){var a=new i.default(n,t.stack.env,t.configuration).mml();t.Push(t.itemFactory.create(e,a));var s=t.GetNext(),l=f[s];if(l){var c=-1!==o.indexOf(s);if("{"===s){var u=(c?"\\left\\{":"")+" "+t.GetArgument(r)+" "+(c?"\\right\\}":"");return t.string=u+t.string.slice(t.i),void(t.i=0)}c&&(t.i++,t.Push(t.itemFactory.create("auto open").setProperties({open:s,close:l})))}};function M(t,e,r){var o=n(t,3),a=o[0],i=o[1],s=o[2];return e&&r?"\\left\\langle{".concat(a,"}\\middle\\vert{").concat(i,"}\\middle\\vert{").concat(s,"}\\right\\rangle"):e?"\\langle{".concat(a,"}\\vert{").concat(i,"}\\vert{").concat(s,"}\\rangle"):"\\left\\langle{".concat(a,"}\\right\\vert{").concat(i,"}\\left\\vert{").concat(s,"}\\right\\rangle")}p.OperatorApplication=function(t,e,r){for(var n=[],o=3;o<arguments.length;o++)n[o-3]=arguments[o];w(t,"fn",e,r,n)},p.VectorOperator=function(t,e,r){for(var n=[],o=3;o<arguments.length;o++)n[o-3]=arguments[o];w(t,"mml",e,r,n)},p.Expression=function(t,e,r,n){void 0===r&&(r=!0),void 0===n&&(n=""),n=n||e.slice(1);var o=r?t.GetBrackets(e):null,a=t.create("token","mi",{texClass:l.TEXCLASS.OP},n);if(o){var s=new i.default(o,t.stack.env,t.configuration).mml();a=t.create("node","msup",[a,s])}t.Push(t.itemFactory.create("fn",a)),"("===t.GetNext()&&(t.i++,t.Push(t.itemFactory.create("auto open").setProperties({open:"(",close:")"})))},p.Qqtext=function(t,e,r){var n=(t.GetStar()?"":"\\quad")+"\\text{"+(r||t.GetArgument(e))+"}\\quad ";t.string=t.string.slice(0,t.i)+n+t.string.slice(t.i)},p.Differential=function(t,e,r){var n=t.GetBrackets(e),o=null!=n?"^{"+n+"}":" ",a="("===t.GetNext(),s="{"===t.GetNext(),c=r+o;if(a||s)s?(c+=t.GetArgument(e),u=new i.default(c,t.stack.env,t.configuration).mml(),t.Push(t.create("node","TeXAtom",[u],{texClass:l.TEXCLASS.OP}))):(t.Push(new i.default(c,t.stack.env,t.configuration).mml()),t.i++,t.Push(t.itemFactory.create("auto open").setProperties({open:"(",close:")"})));else{c+=t.GetArgument(e,!0)||"";var u=new i.default(c,t.stack.env,t.configuration).mml();t.Push(u)}},p.Derivative=function(t,e,r,n){var o=t.GetStar(),a=t.GetBrackets(e),s=1,l=[];for(l.push(t.GetArgument(e));"{"===t.GetNext()&&s<r;)l.push(t.GetArgument(e)),s++;var c=!1,u=" ",d=" ";r>2&&l.length>2?(u="^{"+(l.length-1)+"}",c=!0):null!=a&&(r>2&&l.length>1&&(c=!0),d=u="^{"+a+"}");for(var p=o?"\\flatfrac":"\\frac",f=l.length>1?l[0]:"",m=l.length>1?l[1]:l[0],h="",g=2,v=void 0;v=l[g];g++)h+=n+" "+v;var y=p+"{"+n+u+f+"}{"+n+" "+m+d+" "+h+"}";t.Push(new i.default(y,t.stack.env,t.configuration).mml()),"("===t.GetNext()&&(t.i++,t.Push(t.itemFactory.create("auto open").setProperties({open:"(",close:")",ignore:c})))},p.Bra=function(t,e){var r=t.GetStar(),n=t.GetArgument(e),o="",a=!1,s=!1;if("\\"===t.GetNext()){var l=t.i;t.i++;var c=t.GetCS(),u=t.lookup("macro",c);u&&"ket"===u.symbol?(a=!0,l=t.i,s=t.GetStar(),"{"===t.GetNext()?o=t.GetArgument(c,!0):(t.i=l,s=!1)):t.i=l}var d;d=a?r||s?"\\langle{".concat(n,"}\\vert{").concat(o,"}\\rangle"):"\\left\\langle{".concat(n,"}\\middle\\vert{").concat(o,"}\\right\\rangle"):r||s?"\\langle{".concat(n,"}\\vert"):"\\left\\langle{".concat(n,"}\\right\\vert{").concat(o,"}"),t.Push(new i.default(d,t.stack.env,t.configuration).mml())},p.Ket=function(t,e){var r=t.GetStar(),n=t.GetArgument(e),o=r?"\\vert{".concat(n,"}\\rangle"):"\\left\\vert{".concat(n,"}\\right\\rangle");t.Push(new i.default(o,t.stack.env,t.configuration).mml())},p.BraKet=function(t,e){var r=t.GetStar(),n=t.GetArgument(e),o=null;"{"===t.GetNext()&&(o=t.GetArgument(e,!0));var a;a=null==o?r?"\\langle{".concat(n,"}\\vert{").concat(n,"}\\rangle"):"\\left\\langle{".concat(n,"}\\middle\\vert{").concat(n,"}\\right\\rangle"):r?"\\langle{".concat(n,"}\\vert{").concat(o,"}\\rangle"):"\\left\\langle{".concat(n,"}\\middle\\vert{").concat(o,"}\\right\\rangle"),t.Push(new i.default(a,t.stack.env,t.configuration).mml())},p.KetBra=function(t,e){var r=t.GetStar(),n=t.GetArgument(e),o=null;"{"===t.GetNext()&&(o=t.GetArgument(e,!0));var a;a=null==o?r?"\\vert{".concat(n,"}\\rangle\\!\\langle{").concat(n,"}\\vert"):"\\left\\vert{".concat(n,"}\\middle\\rangle\\!\\middle\\langle{").concat(n,"}\\right\\vert"):r?"\\vert{".concat(n,"}\\rangle\\!\\langle{").concat(o,"}\\vert"):"\\left\\vert{".concat(n,"}\\middle\\rangle\\!\\middle\\langle{").concat(o,"}\\right\\vert"),t.Push(new i.default(a,t.stack.env,t.configuration).mml())},p.Expectation=function(t,e){var r=t.GetStar(),n=r&&t.GetStar(),o=t.GetArgument(e),a=null;"{"===t.GetNext()&&(a=t.GetArgument(e,!0));var s=o&&a?M([a,o,a],r,n):r?"\\langle {".concat(o,"} \\rangle"):"\\left\\langle {".concat(o,"} \\right\\rangle");t.Push(new i.default(s,t.stack.env,t.configuration).mml())},p.MatrixElement=function(t,e){var r=t.GetStar(),n=r&&t.GetStar(),o=M([t.GetArgument(e),t.GetArgument(e),t.GetArgument(e)],r,n);t.Push(new i.default(o,t.stack.env,t.configuration).mml())},p.MatrixQuantity=function(t,e,r){var n=t.GetStar(),o=r?"smallmatrix":"array",a="",s="",l="";switch(t.GetNext()){case"{":a=t.GetArgument(e);break;case"(":t.i++,s=n?"\\lgroup":"(",l=n?"\\rgroup":")",a=t.GetUpTo(e,")");break;case"[":t.i++,s="[",l="]",a=t.GetUpTo(e,"]");break;case"|":t.i++,s="|",l="|",a=t.GetUpTo(e,"|");break;default:s="(",l=")"}var c=(s?"\\left":"")+s+"\\begin{"+o+"}{} "+a+"\\end{"+o+"}"+(s?"\\right":"")+l;t.Push(new i.default(c,t.stack.env,t.configuration).mml())},p.IdentityMatrix=function(t,e){var r=t.GetArgument(e),n=parseInt(r,10);if(isNaN(n))throw new s.default("InvalidNumber","Invalid number");if(n<=1)return t.string="1"+t.string.slice(t.i),void(t.i=0);for(var o=Array(n).fill("0"),a=[],i=0;i<n;i++){var l=o.slice();l[i]="1",a.push(l.join(" & "))}t.string=a.join("\\\\ ")+t.string.slice(t.i),t.i=0},p.XMatrix=function(t,e){var r=t.GetStar(),n=t.GetArgument(e),o=t.GetArgument(e),a=t.GetArgument(e),i=parseInt(o,10),l=parseInt(a,10);if(isNaN(i)||isNaN(l)||l.toString()!==a||i.toString()!==o)throw new s.default("InvalidNumber","Invalid number");if(i=i<1?1:i,l=l<1?1:l,!r){var c=Array(l).fill(n).join(" & "),u=Array(i).fill(c).join("\\\\ ");return t.string=u+t.string.slice(t.i),void(t.i=0)}var d="";if(1===i&&1===l)d=n;else if(1===i){c=[];for(var p=1;p<=l;p++)c.push("".concat(n,"_{").concat(p,"}"));d=c.join(" & ")}else if(1===l){for(c=[],p=1;p<=i;p++)c.push("".concat(n,"_{").concat(p,"}"));d=c.join("\\\\ ")}else{var f=[];for(p=1;p<=i;p++){c=[];for(var m=1;m<=l;m++)c.push("".concat(n,"_{{").concat(p,"}{").concat(m,"}}"));f.push(c.join(" & "))}d=f.join("\\\\ ")}t.string=d+t.string.slice(t.i),t.i=0},p.PauliMatrix=function(t,e){var r=t.GetArgument(e),n=r.slice(1);switch(r[0]){case"0":n+=" 1 & 0\\\\ 0 & 1";break;case"1":case"x":n+=" 0 & 1\\\\ 1 & 0";break;case"2":case"y":n+=" 0 & -i\\\\ i & 0";break;case"3":case"z":n+=" 1 & 0\\\\ 0 & -1"}t.string=n+t.string.slice(t.i),t.i=0},p.DiagonalMatrix=function(t,e,r){if("{"===t.GetNext()){var n=t.i;t.GetArgument(e);var o=t.i;t.i=n+1;for(var a=[],i="",s=t.i;s<o;){try{i=t.GetUpTo(e,",")}catch(e){t.i=o,a.push(t.string.slice(s,o-1));break}if(t.i>=o){a.push(t.string.slice(s,o));break}s=t.i,a.push(i)}t.string=function(t,e){for(var r=t.length,n=[],o=0;o<r;o++)n.push(Array(e?r-o:o+1).join("&")+"\\mqty{"+t[o]+"}");return n.join("\\\\ ")}(a,r)+t.string.slice(o),t.i=0}},p.AutoClose=function(t,e,r){var n=t.create("token","mo",{stretchy:!1},e),o=t.itemFactory.create("mml",n).setProperties({autoclose:e});t.Push(o)},p.Vnabla=function(t,e){var r=t.options.physics.arrowdel?"\\vec{\\gradientnabla}":"{\\gradientnabla}";return t.Push(new i.default(r,t.stack.env,t.configuration).mml())},p.DiffD=function(t,e){var r=t.options.physics.italicdiff?"d":"{\\rm d}";return t.Push(new i.default(r,t.stack.env,t.configuration).mml())},p.Macro=a.default.Macro,p.NamedFn=a.default.NamedFn,p.Array=a.default.Array,e.default=p},22294:function(t,e,r){var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},o=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.SetOptionsConfiguration=e.SetOptionsUtil=void 0;var a=r(63401),i=r(65695),s=o(r(54420)),l=o(r(55038)),c=r(75735),u=o(r(76914)),d=r(4498);e.SetOptionsUtil={filterPackage:function(t,e){if("tex"!==e&&!a.ConfigurationHandler.get(e))throw new s.default("NotAPackage","Not a defined package: %1",e);var r=t.options.setoptions,n=r.allowOptions[e];if(void 0===n&&!r.allowPackageDefault||!1===n)throw new s.default("PackageNotSettable",'Options can\'t be set for package "%1"',e);return!0},filterOption:function(t,e,r){var n,o=t.options.setoptions,a=o.allowOptions[e]||{},i=a.hasOwnProperty(r)&&!(0,d.isObject)(a[r])?a[r]:null;if(!1===i||null===i&&!o.allowOptionsDefault)throw new s.default("OptionNotSettable",'Option "%1" is not allowed to be set',r);if(!(null===(n="tex"===e?t.options:t.options[e])||void 0===n?void 0:n.hasOwnProperty(r)))throw"tex"===e?new s.default("InvalidTexOption",'Invalid TeX option "%1"',r):new s.default("InvalidOptionKey",'Invalid option "%1" for package "%2"',r,e);return!0},filterValue:function(t,e,r,n){return n}};var p=new i.CommandMap("setoptions",{setOptions:"SetOptions"},{SetOptions:function(t,e){var r,o,a=t.GetBrackets(e)||"tex",i=l.default.keyvalOptions(t.GetArgument(e)),s=t.options.setoptions;if(s.filterPackage(t,a))try{for(var c=n(Object.keys(i)),u=c.next();!u.done;u=c.next()){var d=u.value;s.filterOption(t,a,d)&&(("tex"===a?t.options:t.options[a])[d]=s.filterValue(t,a,d,i[d]))}}catch(t){r={error:t}}finally{try{u&&!u.done&&(o=c.return)&&o.call(c)}finally{if(r)throw r.error}}}});e.SetOptionsConfiguration=a.Configuration.create("setoptions",{handler:{macro:["setoptions"]},config:function(t,e){var r=e.parseOptions.handlers.get("macro").lookup("require");r&&(p.add("Require",new c.Macro("Require",r._func)),p.add("require",new c.Macro("require",u.default.Macro,["\\Require{#2}\\setOptions[#2]{#1}",2,""])))},priority:3,options:{setoptions:{filterPackage:e.SetOptionsUtil.filterPackage,filterOption:e.SetOptionsUtil.filterOption,filterValue:e.SetOptionsUtil.filterValue,allowPackageDefault:!0,allowOptionsDefault:!0,allowOptions:(0,d.expandable)({tex:{FindTeX:!1,formatError:!1,package:!1,baseURL:!1,tags:!1,maxBuffer:!1,maxMaxros:!1,macros:!1,environments:!1},setoptions:!1,autoload:!1,require:!1,configmacros:!1,tagformat:!1})}}})},77906:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.TagFormatConfiguration=e.tagformatConfig=void 0;var a=r(63401),i=r(75723),s=0;function l(t,e){var r=e.parseOptions.options.tags;"base"!==r&&t.tags.hasOwnProperty(r)&&i.TagsFactory.add(r,t.tags[r]);var n=function(t){function r(){return null!==t&&t.apply(this,arguments)||this}return o(r,t),r.prototype.formatNumber=function(t){return e.parseOptions.options.tagformat.number(t)},r.prototype.formatTag=function(t){return e.parseOptions.options.tagformat.tag(t)},r.prototype.formatId=function(t){return e.parseOptions.options.tagformat.id(t)},r.prototype.formatUrl=function(t,r){return e.parseOptions.options.tagformat.url(t,r)},r}(i.TagsFactory.create(e.parseOptions.options.tags).constructor),a="configTags-"+ ++s;i.TagsFactory.add(a,n),e.parseOptions.options.tags=a}e.tagformatConfig=l,e.TagFormatConfiguration=a.Configuration.create("tagformat",{config:[l,10],options:{tagformat:{number:function(t){return t.toString()},tag:function(t){return"("+t+")"},id:function(t){return"mjx-eqn:"+t.replace(/\s/g,"_")},url:function(t,e){return e+"#"+encodeURIComponent(t)}}}})},67689:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.TextcompConfiguration=void 0;var n=r(63401);r(44438),e.TextcompConfiguration=n.Configuration.create("textcomp",{handler:{macro:["textcomp-macros"]}})},44438:function(t,e,r){var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(65695),a=r(28027),i=r(87753),s=n(r(55038)),l=r(97112);new o.CommandMap("textcomp-macros",{textasciicircum:["Insert","^"],textasciitilde:["Insert","~"],textasteriskcentered:["Insert","*"],textbackslash:["Insert","\\"],textbar:["Insert","|"],textbraceleft:["Insert","{"],textbraceright:["Insert","}"],textbullet:["Insert","•"],textdagger:["Insert","†"],textdaggerdbl:["Insert","‡"],textellipsis:["Insert","…"],textemdash:["Insert","—"],textendash:["Insert","–"],textexclamdown:["Insert","¡"],textgreater:["Insert",">"],textless:["Insert","<"],textordfeminine:["Insert","ª"],textordmasculine:["Insert","º"],textparagraph:["Insert","¶"],textperiodcentered:["Insert","·"],textquestiondown:["Insert","¿"],textquotedblleft:["Insert","“"],textquotedblright:["Insert","”"],textquoteleft:["Insert","‘"],textquoteright:["Insert","’"],textsection:["Insert","§"],textunderscore:["Insert","_"],textvisiblespace:["Insert","␣"],textacutedbl:["Insert","˝"],textasciiacute:["Insert","´"],textasciibreve:["Insert","˘"],textasciicaron:["Insert","ˇ"],textasciidieresis:["Insert","¨"],textasciimacron:["Insert","¯"],textgravedbl:["Insert","˵"],texttildelow:["Insert","˷"],textbaht:["Insert","฿"],textcent:["Insert","¢"],textcolonmonetary:["Insert","₡"],textcurrency:["Insert","¤"],textdollar:["Insert","$"],textdong:["Insert","₫"],texteuro:["Insert","€"],textflorin:["Insert","ƒ"],textguarani:["Insert","₲"],textlira:["Insert","₤"],textnaira:["Insert","₦"],textpeso:["Insert","₱"],textsterling:["Insert","£"],textwon:["Insert","₩"],textyen:["Insert","¥"],textcircledP:["Insert","℗"],textcompwordmark:["Insert","‌"],textcopyleft:["Insert","🄯"],textcopyright:["Insert","©"],textregistered:["Insert","®"],textservicemark:["Insert","℠"],texttrademark:["Insert","™"],textbardbl:["Insert","‖"],textbigcircle:["Insert","◯"],textblank:["Insert","␢"],textbrokenbar:["Insert","¦"],textdiscount:["Insert","⁒"],textestimated:["Insert","℮"],textinterrobang:["Insert","‽"],textinterrobangdown:["Insert","⸘"],textmusicalnote:["Insert","♪"],textnumero:["Insert","№"],textopenbullet:["Insert","◦"],textpertenthousand:["Insert","‱"],textperthousand:["Insert","‰"],textrecipe:["Insert","℞"],textreferencemark:["Insert","※"],textlangle:["Insert","〈"],textrangle:["Insert","〉"],textlbrackdbl:["Insert","⟦"],textrbrackdbl:["Insert","⟧"],textlquill:["Insert","⁅"],textrquill:["Insert","⁆"],textcelsius:["Insert","℃"],textdegree:["Insert","°"],textdiv:["Insert","÷"],textdownarrow:["Insert","↓"],textfractionsolidus:["Insert","⁄"],textleftarrow:["Insert","←"],textlnot:["Insert","¬"],textmho:["Insert","℧"],textminus:["Insert","−"],textmu:["Insert","µ"],textohm:["Insert","Ω"],textonehalf:["Insert","½"],textonequarter:["Insert","¼"],textonesuperior:["Insert","¹"],textpm:["Insert","±"],textrightarrow:["Insert","→"],textsurd:["Insert","√"],textthreequarters:["Insert","¾"],textthreesuperior:["Insert","³"],texttimes:["Insert","×"],texttwosuperior:["Insert","²"],textuparrow:["Insert","↑"],textborn:["Insert","*"],textdied:["Insert","†"],textdivorced:["Insert","⚮"],textmarried:["Insert","⚭"],textcentoldstyle:["Insert","¢",a.TexConstant.Variant.OLDSTYLE],textdollaroldstyle:["Insert","$",a.TexConstant.Variant.OLDSTYLE],textzerooldstyle:["Insert","0",a.TexConstant.Variant.OLDSTYLE],textoneoldstyle:["Insert","1",a.TexConstant.Variant.OLDSTYLE],texttwooldstyle:["Insert","2",a.TexConstant.Variant.OLDSTYLE],textthreeoldstyle:["Insert","3",a.TexConstant.Variant.OLDSTYLE],textfouroldstyle:["Insert","4",a.TexConstant.Variant.OLDSTYLE],textfiveoldstyle:["Insert","5",a.TexConstant.Variant.OLDSTYLE],textsixoldstyle:["Insert","6",a.TexConstant.Variant.OLDSTYLE],textsevenoldstyle:["Insert","7",a.TexConstant.Variant.OLDSTYLE],texteightoldstyle:["Insert","8",a.TexConstant.Variant.OLDSTYLE],textnineoldstyle:["Insert","9",a.TexConstant.Variant.OLDSTYLE]},{Insert:function(t,e,r,n){if(t instanceof l.TextParser){if(!n)return void i.TextMacrosMethods.Insert(t,e,r);t.saveText()}t.Push(s.default.internalText(t,r,n?{mathvariant:n}:{}))}})},54687:function(t,e,r){var n,o=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.TextMacrosConfiguration=e.TextBaseConfiguration=void 0;var a=r(63401),i=o(r(55661)),s=r(75723),l=r(31201),c=r(97112),u=r(87753);function d(t,e,r,n){var o=t.configuration.packageData.get("textmacros");return t instanceof c.TextParser||(o.texParser=t),[new c.TextParser(e,n?{mathvariant:n}:{},o.parseOptions,r).mml()]}r(13922),e.TextBaseConfiguration=a.Configuration.create("text-base",{parser:"text",handler:{character:["command","text-special"],macro:["text-macros"]},fallback:{character:function(t,e){t.text+=e},macro:function(t,e){var r=t.texParser,n=r.lookup("macro",e);n&&n._func!==u.TextMacrosMethods.Macro&&t.Error("MathMacro","%1 is only supported in math mode","\\"+e),r.parse("macro",[t,e])}},items:(n={},n[l.StartItem.prototype.kind]=l.StartItem,n[l.StopItem.prototype.kind]=l.StopItem,n[l.MmlItem.prototype.kind]=l.MmlItem,n[l.StyleItem.prototype.kind]=l.StyleItem,n)}),e.TextMacrosConfiguration=a.Configuration.create("textmacros",{config:function(t,e){var r=new a.ParserConfiguration(e.parseOptions.options.textmacros.packages,["tex","text"]);r.init();var n=new i.default(r,[]);n.options=e.parseOptions.options,r.config(e),s.TagsFactory.addTags(r.tags),n.tags=s.TagsFactory.getDefault(),n.tags.configuration=n,n.packageData=e.parseOptions.packageData,n.packageData.set("textmacros",{parseOptions:n,jax:e,texParser:null}),n.options.internalMath=d},preprocessors:[function(t){var e=t.data.packageData.get("textmacros");e.parseOptions.nodeFactory.setMmlFactory(e.jax.mmlFactory)}],options:{textmacros:{packages:["text-base"]}}})},13922:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0});var n=r(65695),o=r(28027),a=r(87753),i=r(56780);new n.MacroMap("text-special",{$:"Math","%":"Comment","^":"MathModeOnly",_:"MathModeOnly","&":"Misplaced","#":"Misplaced","~":"Tilde"," ":"Space","\t":"Space","\r":"Space","\n":"Space"," ":"Tilde","{":"OpenBrace","}":"CloseBrace","`":"OpenQuote","'":"CloseQuote"},a.TextMacrosMethods),new n.CommandMap("text-macros",{"(":"Math",$:"SelfQuote",_:"SelfQuote","%":"SelfQuote","{":"SelfQuote","}":"SelfQuote"," ":"SelfQuote","&":"SelfQuote","#":"SelfQuote","\\":"SelfQuote","'":["Accent","´"],"’":["Accent","´"],"`":["Accent","`"],"‘":["Accent","`"],"^":["Accent","^"],'"':["Accent","¨"],"~":["Accent","~"],"=":["Accent","¯"],".":["Accent","˙"],u:["Accent","˘"],v:["Accent","ˇ"],emph:"Emph",rm:["SetFont",o.TexConstant.Variant.NORMAL],mit:["SetFont",o.TexConstant.Variant.ITALIC],oldstyle:["SetFont",o.TexConstant.Variant.OLDSTYLE],cal:["SetFont",o.TexConstant.Variant.CALLIGRAPHIC],it:["SetFont","-tex-mathit"],bf:["SetFont",o.TexConstant.Variant.BOLD],bbFont:["SetFont",o.TexConstant.Variant.DOUBLESTRUCK],scr:["SetFont",o.TexConstant.Variant.SCRIPT],frak:["SetFont",o.TexConstant.Variant.FRAKTUR],sf:["SetFont",o.TexConstant.Variant.SANSSERIF],tt:["SetFont",o.TexConstant.Variant.MONOSPACE],tiny:["SetSize",.5],Tiny:["SetSize",.6],scriptsize:["SetSize",.7],small:["SetSize",.85],normalsize:["SetSize",1],large:["SetSize",1.2],Large:["SetSize",1.44],LARGE:["SetSize",1.73],huge:["SetSize",2.07],Huge:["SetSize",2.49],Bbb:["Macro","{\\bbFont #1}",1],textnormal:["Macro","{\\rm #1}",1],textup:["Macro","{\\rm #1}",1],textrm:["Macro","{\\rm #1}",1],textit:["Macro","{\\it #1}",1],textbf:["Macro","{\\bf #1}",1],textsf:["Macro","{\\sf #1}",1],texttt:["Macro","{\\tt #1}",1],dagger:["Insert","†"],ddagger:["Insert","‡"],S:["Insert","§"],",":["Spacer",i.MATHSPACE.thinmathspace],":":["Spacer",i.MATHSPACE.mediummathspace],">":["Spacer",i.MATHSPACE.mediummathspace],";":["Spacer",i.MATHSPACE.thickmathspace],"!":["Spacer",i.MATHSPACE.negativethinmathspace],enspace:["Spacer",.5],quad:["Spacer",1],qquad:["Spacer",2],thinspace:["Spacer",i.MATHSPACE.thinmathspace],negthinspace:["Spacer",i.MATHSPACE.negativethinmathspace],hskip:"Hskip",hspace:"Hskip",kern:"Hskip",mskip:"Hskip",mspace:"Hskip",mkern:"Hskip",rule:"rule",Rule:["Rule"],Space:["Rule","blank"],color:"CheckAutoload",textcolor:"CheckAutoload",colorbox:"CheckAutoload",fcolorbox:"CheckAutoload",href:"CheckAutoload",style:"CheckAutoload",class:"CheckAutoload",cssId:"CheckAutoload",unicode:"CheckAutoload",ref:["HandleRef",!1],eqref:["HandleRef",!0]},a.TextMacrosMethods)},87753:function(t,e,r){var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.TextMacrosMethods=void 0;var o=n(r(94032)),a=r(10956),i=n(r(76914));e.TextMacrosMethods={Comment:function(t,e){for(;t.i<t.string.length&&"\n"!==t.string.charAt(t.i);)t.i++;t.i++},Math:function(t,e){t.saveText();for(var r,n,a=t.i,i=0;n=t.GetNext();)switch(r=t.i++,n){case"\\":")"===t.GetCS()&&(n="\\(");case"$":if(0===i&&e===n){var s=t.texParser.configuration,l=new o.default(t.string.substr(a,r-a),t.stack.env,s).mml();return void t.PushMath(l)}break;case"{":i++;break;case"}":0===i&&t.Error("ExtraCloseMissingOpen","Extra close brace or missing open brace"),i--}t.Error("MathNotTerminated","Math-mode is not properly terminated")},MathModeOnly:function(t,e){t.Error("MathModeOnly","'%1' allowed only in math mode",e)},Misplaced:function(t,e){t.Error("Misplaced","'%1' can not be used here",e)},OpenBrace:function(t,e){var r=t.stack.env;t.envStack.push(r),t.stack.env=Object.assign({},r)},CloseBrace:function(t,e){t.envStack.length?(t.saveText(),t.stack.env=t.envStack.pop()):t.Error("ExtraCloseMissingOpen","Extra close brace or missing open brace")},OpenQuote:function(t,e){t.string.charAt(t.i)===e?(t.text+="“",t.i++):t.text+="‘"},CloseQuote:function(t,e){t.string.charAt(t.i)===e?(t.text+="”",t.i++):t.text+="’"},Tilde:function(t,e){t.text+=" "},Space:function(t,e){for(t.text+=" ";t.GetNext().match(/\s/);)t.i++},SelfQuote:function(t,e){t.text+=e.substr(1)},Insert:function(t,e,r){t.text+=r},Accent:function(t,e,r){var n=t.ParseArg(e),o=t.create("token","mo",{},r);t.addAttributes(o),t.Push(t.create("node","mover",[n,o]))},Emph:function(t,e){var r="-tex-mathit"===t.stack.env.mathvariant?"normal":"-tex-mathit";t.Push(t.ParseTextArg(e,{mathvariant:r}))},SetFont:function(t,e,r){t.saveText(),t.stack.env.mathvariant=r},SetSize:function(t,e,r){t.saveText(),t.stack.env.mathsize=r},CheckAutoload:function(t,e){var r=t.configuration.packageData.get("autoload"),n=t.texParser;e=e.slice(1);var o=n.lookup("macro",e);if(!o||r&&o._func===r.Autoload){if(n.parse("macro",[n,e]),!o)return;(0,a.retryAfter)(Promise.resolve())}n.parse("macro",[t,e])},Macro:i.default.Macro,Spacer:i.default.Spacer,Hskip:i.default.Hskip,rule:i.default.rule,Rule:i.default.Rule,HandleRef:i.default.HandleRef}},97112:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,a=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(n=a.next()).done;)i.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return i},s=this&&this.__spreadArray||function(t,e,r){if(r||2===arguments.length)for(var n,o=0,a=e.length;o<a;o++)!n&&o in e||(n||(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))},l=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.TextParser=void 0;var c=l(r(94032)),u=l(r(54420)),d=l(r(55038)),p=r(83045),f=l(r(53972)),m=r(31201),h=function(t){function e(e,r,n,o){var a=t.call(this,e,r,n)||this;return a.level=o,a}return o(e,t),Object.defineProperty(e.prototype,"texParser",{get:function(){return this.configuration.packageData.get("textmacros").texParser},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"tags",{get:function(){return this.texParser.tags},enumerable:!1,configurable:!0}),e.prototype.mml=function(){return null!=this.level?this.create("node","mstyle",this.nodes,{displaystyle:!1,scriptlevel:this.level}):1===this.nodes.length?this.nodes[0]:this.create("node","mrow",this.nodes)},e.prototype.Parse=function(){this.text="",this.nodes=[],this.envStack=[],t.prototype.Parse.call(this)},e.prototype.saveText=function(){if(this.text){var t=this.stack.env.mathvariant,e=d.default.internalText(this,this.text,t?{mathvariant:t}:{});this.text="",this.Push(e)}},e.prototype.Push=function(e){if(this.text&&this.saveText(),e instanceof m.StopItem)return t.prototype.Push.call(this,e);e instanceof m.StyleItem?this.stack.env.mathcolor=this.stack.env.color:e instanceof p.AbstractMmlNode&&(this.addAttributes(e),this.nodes.push(e))},e.prototype.PushMath=function(t){var e,r,n=this.stack.env;t.isKind("TeXAtom")||(t=this.create("node","TeXAtom",[t]));try{for(var o=a(["mathsize","mathcolor"]),i=o.next();!i.done;i=o.next()){var s=i.value;n[s]&&!t.attributes.getExplicit(s)&&(t.isToken||t.isKind("mstyle")||(t=this.create("node","mstyle",[t])),f.default.setAttribute(t,s,n[s]))}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}t.isInferred&&(t=this.create("node","mrow",t.childNodes)),this.nodes.push(t)},e.prototype.addAttributes=function(t){var e,r,n=this.stack.env;if(t.isToken)try{for(var o=a(["mathsize","mathcolor","mathvariant"]),i=o.next();!i.done;i=o.next()){var s=i.value;n[s]&&!t.attributes.getExplicit(s)&&f.default.setAttribute(t,s,n[s])}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}},e.prototype.ParseTextArg=function(t,r){return new e(this.GetArgument(t),r=Object.assign(Object.assign({},this.stack.env),r),this.configuration).mml()},e.prototype.ParseArg=function(t){return new e(this.GetArgument(t),this.stack.env,this.configuration).mml()},e.prototype.Error=function(t,e){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];throw new(u.default.bind.apply(u.default,s([void 0,t,e],i(r),!1)))},e}(c.default);e.TextParser=h},36229:function(t,e,r){var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.UnicodeConfiguration=e.UnicodeMethods=void 0;var o=r(63401),a=n(r(54420)),i=r(65695),s=n(r(55038)),l=n(r(53972)),c=r(61051);e.UnicodeMethods={};var u={};e.UnicodeMethods.Unicode=function(t,e){var r=t.GetBrackets(e),n=null,o=null;r&&(r.replace(/ /g,"").match(/^(\d+(\.\d*)?|\.\d+),(\d+(\.\d*)?|\.\d+)$/)?(n=r.replace(/ /g,"").split(/,/),o=t.GetBrackets(e)):o=r);var i=s.default.trimSpaces(t.GetArgument(e)).replace(/^0x/,"x");if(!i.match(/^(x[0-9A-Fa-f]+|[0-9]+)$/))throw new a.default("BadUnicode","Argument to \\unicode must be a number");var d=parseInt(i.match(/^x/)?"0"+i:i);u[d]?o||(o=u[d][2]):u[d]=[800,200,o,d],n&&(u[d][0]=Math.floor(1e3*parseFloat(n[0])),u[d][1]=Math.floor(1e3*parseFloat(n[1])));var p=t.stack.env.font,f={};o?(u[d][2]=f.fontfamily=o.replace(/'/g,"'"),p&&(p.match(/bold/)&&(f.fontweight="bold"),p.match(/italic|-mathit/)&&(f.fontstyle="italic"))):p&&(f.mathvariant=p);var m=t.create("token","mtext",f,(0,c.numeric)(i));l.default.setProperty(m,"unicode",!0),t.Push(m)},new i.CommandMap("unicode",{unicode:"Unicode"},e.UnicodeMethods),e.UnicodeConfiguration=o.Configuration.create("unicode",{handler:{macro:["unicode"]}})},63892:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.UpgreekConfiguration=void 0;var n=r(63401),o=r(65695),a=r(28027);new o.CharacterMap("upgreek",(function(t,e){var r=e.attributes||{};r.mathvariant=a.TexConstant.Variant.NORMAL;var n=t.create("token","mi",r,e.char);t.Push(n)}),{upalpha:"α",upbeta:"β",upgamma:"γ",updelta:"δ",upepsilon:"ϵ",upzeta:"ζ",upeta:"η",uptheta:"θ",upiota:"ι",upkappa:"κ",uplambda:"λ",upmu:"μ",upnu:"ν",upxi:"ξ",upomicron:"ο",uppi:"π",uprho:"ρ",upsigma:"σ",uptau:"τ",upupsilon:"υ",upphi:"ϕ",upchi:"χ",uppsi:"ψ",upomega:"ω",upvarepsilon:"ε",upvartheta:"ϑ",upvarpi:"ϖ",upvarrho:"ϱ",upvarsigma:"ς",upvarphi:"φ",Upgamma:"Γ",Updelta:"Δ",Uptheta:"Θ",Uplambda:"Λ",Upxi:"Ξ",Uppi:"Π",Upsigma:"Σ",Upupsilon:"Υ",Upphi:"Φ",Uppsi:"Ψ",Upomega:"Ω"}),e.UpgreekConfiguration=n.Configuration.create("upgreek",{handler:{macro:["upgreek"]}})},95729:function(t,e,r){var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.VerbConfiguration=e.VerbMethods=void 0;var o=r(63401),a=r(28027),i=r(65695),s=n(r(54420));e.VerbMethods={},e.VerbMethods.Verb=function(t,e){var r=t.GetNext(),n=++t.i;if(""===r)throw new s.default("MissingArgFor","Missing argument for %1",e);for(;t.i<t.string.length&&t.string.charAt(t.i)!==r;)t.i++;if(t.i===t.string.length)throw new s.default("NoClosingDelim","Can't find closing delimiter for %1",t.currentCS);var o=t.string.slice(n,t.i).replace(/ /g," ");t.i++,t.Push(t.create("token","mtext",{mathvariant:a.TexConstant.Variant.MONOSPACE},o))},new i.CommandMap("verb",{verb:"Verb"},e.VerbMethods),e.VerbConfiguration=o.Configuration.create("verb",{handler:{macro:["verb"]}})},84752:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.mhchemParser=void 0;var r=function(){function t(){}return t.toTex=function(t,e){return a.go(o.go(t,e),"tex"!==e)},t}();function n(t){var e,r,n={};for(e in t)for(r in t[e]){var o=r.split("|");t[e][r].stateArray=o;for(var a=0;a<o.length;a++)n[o[a]]=[]}for(e in t)for(r in t[e])for(o=t[e][r].stateArray||[],a=0;a<o.length;a++){var i=t[e][r];i.action_=[].concat(i.action_);for(var s=0;s<i.action_.length;s++)"string"==typeof i.action_[s]&&(i.action_[s]={type_:i.action_[s]});for(var l=e.split("|"),c=0;c<l.length;c++)if("*"===o[a]){var u=void 0;for(u in n)n[u].push({pattern:l[c],task:i})}else n[o[a]].push({pattern:l[c],task:i})}return n}e.mhchemParser=r;var o={go:function(t,e){if(!t)return[];void 0===e&&(e="ce");var r,n="0",a={parenthesisLevel:0};t=(t=(t=t.replace(/\n/g," ")).replace(/[\u2212\u2013\u2014\u2010]/g,"-")).replace(/[\u2026]/g,"...");for(var i=10,s=[];;){r!==t?(i=10,r=t):i--;var l=o.stateMachines[e],c=l.transitions[n]||l.transitions["*"];t:for(var u=0;u<c.length;u++){var d=o.patterns.match_(c[u].pattern,t);if(d){for(var p=c[u].task,f=0;f<p.action_.length;f++){var m=void 0;if(l.actions[p.action_[f].type_])m=l.actions[p.action_[f].type_](a,d.match_,p.action_[f].option);else{if(!o.actions[p.action_[f].type_])throw["MhchemBugA","mhchem bug A. Please report. ("+p.action_[f].type_+")"];m=o.actions[p.action_[f].type_](a,d.match_,p.action_[f].option)}o.concatArray(s,m)}if(n=p.nextState||n,!(t.length>0))return s;if(p.revisit||(t=d.remainder),!p.toContinue)break t}}if(i<=0)throw["MhchemBugU","mhchem bug U. Please report."]}},concatArray:function(t,e){if(e)if(Array.isArray(e))for(var r=0;r<e.length;r++)t.push(e[r]);else t.push(e)},patterns:{patterns:{empty:/^$/,else:/^./,else2:/^./,space:/^\s/,"space A":/^\s(?=[A-Z\\$])/,space$:/^\s$/,"a-z":/^[a-z]/,x:/^x/,x$:/^x$/,i$:/^i$/,letters:/^(?:[a-zA-Z\u03B1-\u03C9\u0391-\u03A9?@]|(?:\\(?:alpha|beta|gamma|delta|epsilon|zeta|eta|theta|iota|kappa|lambda|mu|nu|xi|omicron|pi|rho|sigma|tau|upsilon|phi|chi|psi|omega|Gamma|Delta|Theta|Lambda|Xi|Pi|Sigma|Upsilon|Phi|Psi|Omega)(?:\s+|\{\}|(?![a-zA-Z]))))+/,"\\greek":/^\\(?:alpha|beta|gamma|delta|epsilon|zeta|eta|theta|iota|kappa|lambda|mu|nu|xi|omicron|pi|rho|sigma|tau|upsilon|phi|chi|psi|omega|Gamma|Delta|Theta|Lambda|Xi|Pi|Sigma|Upsilon|Phi|Psi|Omega)(?:\s+|\{\}|(?![a-zA-Z]))/,"one lowercase latin letter $":/^(?:([a-z])(?:$|[^a-zA-Z]))$/,"$one lowercase latin letter$ $":/^\$(?:([a-z])(?:$|[^a-zA-Z]))\$$/,"one lowercase greek letter $":/^(?:\$?[\u03B1-\u03C9]\$?|\$?\\(?:alpha|beta|gamma|delta|epsilon|zeta|eta|theta|iota|kappa|lambda|mu|nu|xi|omicron|pi|rho|sigma|tau|upsilon|phi|chi|psi|omega)\s*\$?)(?:\s+|\{\}|(?![a-zA-Z]))$/,digits:/^[0-9]+/,"-9.,9":/^[+\-]?(?:[0-9]+(?:[,.][0-9]+)?|[0-9]*(?:\.[0-9]+))/,"-9.,9 no missing 0":/^[+\-]?[0-9]+(?:[.,][0-9]+)?/,"(-)(9.,9)(e)(99)":function(t){var e=t.match(/^(\+\-|\+\/\-|\+|\-|\\pm\s?)?([0-9]+(?:[,.][0-9]+)?|[0-9]*(?:\.[0-9]+))?(\((?:[0-9]+(?:[,.][0-9]+)?|[0-9]*(?:\.[0-9]+))\))?(?:(?:([eE])|\s*(\*|x|\\times|\u00D7)\s*10\^)([+\-]?[0-9]+|\{[+\-]?[0-9]+\}))?/);return e&&e[0]?{match_:e.slice(1),remainder:t.substr(e[0].length)}:null},"(-)(9)^(-9)":/^(\+\-|\+\/\-|\+|\-|\\pm\s?)?([0-9]+(?:[,.][0-9]+)?|[0-9]*(?:\.[0-9]+)?)\^([+\-]?[0-9]+|\{[+\-]?[0-9]+\})/,"state of aggregation $":function(t){var e=o.patterns.findObserveGroups(t,"",/^\([a-z]{1,3}(?=[\),])/,")","");if(e&&e.remainder.match(/^($|[\s,;\)\]\}])/))return e;var r=t.match(/^(?:\((?:\\ca\s?)?\$[amothc]\$\))/);return r?{match_:r[0],remainder:t.substr(r[0].length)}:null},"_{(state of aggregation)}$":/^_\{(\([a-z]{1,3}\))\}/,"{[(":/^(?:\\\{|\[|\()/,")]}":/^(?:\)|\]|\\\})/,", ":/^[,;]\s*/,",":/^[,;]/,".":/^[.]/,". __* ":/^([.\u22C5\u00B7\u2022]|[*])\s*/,"...":/^\.\.\.(?=$|[^.])/,"^{(...)}":function(t){return o.patterns.findObserveGroups(t,"^{","","","}")},"^($...$)":function(t){return o.patterns.findObserveGroups(t,"^","$","$","")},"^a":/^\^([0-9]+|[^\\_])/,"^\\x{}{}":function(t){return o.patterns.findObserveGroups(t,"^",/^\\[a-zA-Z]+\{/,"}","","","{","}","",!0)},"^\\x{}":function(t){return o.patterns.findObserveGroups(t,"^",/^\\[a-zA-Z]+\{/,"}","")},"^\\x":/^\^(\\[a-zA-Z]+)\s*/,"^(-1)":/^\^(-?\d+)/,"'":/^'/,"_{(...)}":function(t){return o.patterns.findObserveGroups(t,"_{","","","}")},"_($...$)":function(t){return o.patterns.findObserveGroups(t,"_","$","$","")},_9:/^_([+\-]?[0-9]+|[^\\])/,"_\\x{}{}":function(t){return o.patterns.findObserveGroups(t,"_",/^\\[a-zA-Z]+\{/,"}","","","{","}","",!0)},"_\\x{}":function(t){return o.patterns.findObserveGroups(t,"_",/^\\[a-zA-Z]+\{/,"}","")},"_\\x":/^_(\\[a-zA-Z]+)\s*/,"^_":/^(?:\^(?=_)|\_(?=\^)|[\^_]$)/,"{}^":/^\{\}(?=\^)/,"{}":/^\{\}/,"{...}":function(t){return o.patterns.findObserveGroups(t,"","{","}","")},"{(...)}":function(t){return o.patterns.findObserveGroups(t,"{","","","}")},"$...$":function(t){return o.patterns.findObserveGroups(t,"","$","$","")},"${(...)}$__$(...)$":function(t){return o.patterns.findObserveGroups(t,"${","","","}$")||o.patterns.findObserveGroups(t,"$","","","$")},"=<>":/^[=<>]/,"#":/^[#\u2261]/,"+":/^\+/,"-$":/^-(?=[\s_},;\]/]|$|\([a-z]+\))/,"-9":/^-(?=[0-9])/,"- orbital overlap":/^-(?=(?:[spd]|sp)(?:$|[\s,;\)\]\}]))/,"-":/^-/,"pm-operator":/^(?:\\pm|\$\\pm\$|\+-|\+\/-)/,operator:/^(?:\+|(?:[\-=<>]|<<|>>|\\approx|\$\\approx\$)(?=\s|$|-?[0-9]))/,arrowUpDown:/^(?:v|\(v\)|\^|\(\^\))(?=$|[\s,;\)\]\}])/,"\\bond{(...)}":function(t){return o.patterns.findObserveGroups(t,"\\bond{","","","}")},"->":/^(?:<->|<-->|->|<-|<=>>|<<=>|<=>|[\u2192\u27F6\u21CC])/,CMT:/^[CMT](?=\[)/,"[(...)]":function(t){return o.patterns.findObserveGroups(t,"[","","","]")},"1st-level escape":/^(&|\\\\|\\hline)\s*/,"\\,":/^(?:\\[,\ ;:])/,"\\x{}{}":function(t){return o.patterns.findObserveGroups(t,"",/^\\[a-zA-Z]+\{/,"}","","","{","}","",!0)},"\\x{}":function(t){return o.patterns.findObserveGroups(t,"",/^\\[a-zA-Z]+\{/,"}","")},"\\ca":/^\\ca(?:\s+|(?![a-zA-Z]))/,"\\x":/^(?:\\[a-zA-Z]+\s*|\\[_&{}%])/,orbital:/^(?:[0-9]{1,2}[spdfgh]|[0-9]{0,2}sp)(?=$|[^a-zA-Z])/,others:/^[\/~|]/,"\\frac{(...)}":function(t){return o.patterns.findObserveGroups(t,"\\frac{","","","}","{","","","}")},"\\overset{(...)}":function(t){return o.patterns.findObserveGroups(t,"\\overset{","","","}","{","","","}")},"\\underset{(...)}":function(t){return o.patterns.findObserveGroups(t,"\\underset{","","","}","{","","","}")},"\\underbrace{(...)}":function(t){return o.patterns.findObserveGroups(t,"\\underbrace{","","","}_","{","","","}")},"\\color{(...)}":function(t){return o.patterns.findObserveGroups(t,"\\color{","","","}")},"\\color{(...)}{(...)}":function(t){return o.patterns.findObserveGroups(t,"\\color{","","","}","{","","","}")||o.patterns.findObserveGroups(t,"\\color","\\","",/^(?=\{)/,"{","","","}")},"\\ce{(...)}":function(t){return o.patterns.findObserveGroups(t,"\\ce{","","","}")},"\\pu{(...)}":function(t){return o.patterns.findObserveGroups(t,"\\pu{","","","}")},oxidation$:/^(?:[+-][IVX]+|(?:\\pm|\$\\pm\$|\+-|\+\/-)\s*0)$/,"d-oxidation$":/^(?:[+-]?[IVX]+|(?:\\pm|\$\\pm\$|\+-|\+\/-)\s*0)$/,"1/2$":/^[+\-]?(?:[0-9]+|\$[a-z]\$|[a-z])\/[0-9]+(?:\$[a-z]\$|[a-z])?$/,amount:function(t){var e;if(e=t.match(/^(?:(?:(?:\([+\-]?[0-9]+\/[0-9]+\)|[+\-]?(?:[0-9]+|\$[a-z]\$|[a-z])\/[0-9]+|[+\-]?[0-9]+[.,][0-9]+|[+\-]?\.[0-9]+|[+\-]?[0-9]+)(?:[a-z](?=\s*[A-Z]))?)|[+\-]?[a-z](?=\s*[A-Z])|\+(?!\s))/))return{match_:e[0],remainder:t.substr(e[0].length)};var r=o.patterns.findObserveGroups(t,"","$","$","");return r&&(e=r.match_.match(/^\$(?:\(?[+\-]?(?:[0-9]*[a-z]?[+\-])?[0-9]*[a-z](?:[+\-][0-9]*[a-z]?)?\)?|\+|-)\$$/))?{match_:e[0],remainder:t.substr(e[0].length)}:null},amount2:function(t){return this.amount(t)},"(KV letters),":/^(?:[A-Z][a-z]{0,2}|i)(?=,)/,formula$:function(t){if(t.match(/^\([a-z]+\)$/))return null;var e=t.match(/^(?:[a-z]|(?:[0-9\ \+\-\,\.\(\)]+[a-z])+[0-9\ \+\-\,\.\(\)]*|(?:[a-z][0-9\ \+\-\,\.\(\)]+)+[a-z]?)$/);return e?{match_:e[0],remainder:t.substr(e[0].length)}:null},uprightEntities:/^(?:pH|pOH|pC|pK|iPr|iBu)(?=$|[^a-zA-Z])/,"/":/^\s*(\/)\s*/,"//":/^\s*(\/\/)\s*/,"*":/^\s*[*.]\s*/},findObserveGroups:function(t,e,r,n,o,a,i,s,l,c){var u=function(t,e){if("string"==typeof e)return 0!==t.indexOf(e)?null:e;var r=t.match(e);return r?r[0]:null},d=u(t,e);if(null===d)return null;if(t=t.substr(d.length),null===(d=u(t,r)))return null;var p=function(t,e,r){for(var n=0;e<t.length;){var o=t.charAt(e),a=u(t.substr(e),r);if(null!==a&&0===n)return{endMatchBegin:e,endMatchEnd:e+a.length};if("{"===o)n++;else if("}"===o){if(0===n)throw["ExtraCloseMissingOpen","Extra close brace or missing open brace"];n--}e++}return null}(t,d.length,n||o);if(null===p)return null;var f=t.substring(0,n?p.endMatchEnd:p.endMatchBegin);if(a||i){var m=this.findObserveGroups(t.substr(p.endMatchEnd),a,i,s,l);if(null===m)return null;var h=[f,m.match_];return{match_:c?h.join(""):h,remainder:m.remainder}}return{match_:f,remainder:t.substr(p.endMatchEnd)}},match_:function(t,e){var r=o.patterns.patterns[t];if(void 0===r)throw["MhchemBugP","mhchem bug P. Please report. ("+t+")"];if("function"==typeof r)return o.patterns.patterns[t](e);var n=e.match(r);return n?n.length>2?{match_:n.slice(1),remainder:e.substr(n[0].length)}:{match_:n[1]||n[0],remainder:e.substr(n[0].length)}:null}},actions:{"a=":function(t,e){t.a=(t.a||"")+e},"b=":function(t,e){t.b=(t.b||"")+e},"p=":function(t,e){t.p=(t.p||"")+e},"o=":function(t,e){t.o=(t.o||"")+e},"o=+p1":function(t,e,r){t.o=(t.o||"")+r},"q=":function(t,e){t.q=(t.q||"")+e},"d=":function(t,e){t.d=(t.d||"")+e},"rm=":function(t,e){t.rm=(t.rm||"")+e},"text=":function(t,e){t.text_=(t.text_||"")+e},insert:function(t,e,r){return{type_:r}},"insert+p1":function(t,e,r){return{type_:r,p1:e}},"insert+p1+p2":function(t,e,r){return{type_:r,p1:e[0],p2:e[1]}},copy:function(t,e){return e},write:function(t,e,r){return r},rm:function(t,e){return{type_:"rm",p1:e}},text:function(t,e){return o.go(e,"text")},"tex-math":function(t,e){return o.go(e,"tex-math")},"tex-math tight":function(t,e){return o.go(e,"tex-math tight")},bond:function(t,e,r){return{type_:"bond",kind_:r||e}},"color0-output":function(t,e){return{type_:"color0",color:e}},ce:function(t,e){return o.go(e,"ce")},pu:function(t,e){return o.go(e,"pu")},"1/2":function(t,e){var r=[];e.match(/^[+\-]/)&&(r.push(e.substr(0,1)),e=e.substr(1));var n=e.match(/^([0-9]+|\$[a-z]\$|[a-z])\/([0-9]+)(\$[a-z]\$|[a-z])?$/);return n[1]=n[1].replace(/\$/g,""),r.push({type_:"frac",p1:n[1],p2:n[2]}),n[3]&&(n[3]=n[3].replace(/\$/g,""),r.push({type_:"tex-math",p1:n[3]})),r},"9,9":function(t,e){return o.go(e,"9,9")}},stateMachines:{tex:{transitions:n({empty:{0:{action_:"copy"}},"\\ce{(...)}":{0:{action_:[{type_:"write",option:"{"},"ce",{type_:"write",option:"}"}]}},"\\pu{(...)}":{0:{action_:[{type_:"write",option:"{"},"pu",{type_:"write",option:"}"}]}},else:{0:{action_:"copy"}}}),actions:{}},ce:{transitions:n({empty:{"*":{action_:"output"}},else:{"0|1|2":{action_:"beginsWithBond=false",revisit:!0,toContinue:!0}},oxidation$:{0:{action_:"oxidation-output"}},CMT:{r:{action_:"rdt=",nextState:"rt"},rd:{action_:"rqt=",nextState:"rdt"}},arrowUpDown:{"0|1|2|as":{action_:["sb=false","output","operator"],nextState:"1"}},uprightEntities:{"0|1|2":{action_:["o=","output"],nextState:"1"}},orbital:{"0|1|2|3":{action_:"o=",nextState:"o"}},"->":{"0|1|2|3":{action_:"r=",nextState:"r"},"a|as":{action_:["output","r="],nextState:"r"},"*":{action_:["output","r="],nextState:"r"}},"+":{o:{action_:"d= kv",nextState:"d"},"d|D":{action_:"d=",nextState:"d"},q:{action_:"d=",nextState:"qd"},"qd|qD":{action_:"d=",nextState:"qd"},dq:{action_:["output","d="],nextState:"d"},3:{action_:["sb=false","output","operator"],nextState:"0"}},amount:{"0|2":{action_:"a=",nextState:"a"}},"pm-operator":{"0|1|2|a|as":{action_:["sb=false","output",{type_:"operator",option:"\\pm"}],nextState:"0"}},operator:{"0|1|2|a|as":{action_:["sb=false","output","operator"],nextState:"0"}},"-$":{"o|q":{action_:["charge or bond","output"],nextState:"qd"},d:{action_:"d=",nextState:"d"},D:{action_:["output",{type_:"bond",option:"-"}],nextState:"3"},q:{action_:"d=",nextState:"qd"},qd:{action_:"d=",nextState:"qd"},"qD|dq":{action_:["output",{type_:"bond",option:"-"}],nextState:"3"}},"-9":{"3|o":{action_:["output",{type_:"insert",option:"hyphen"}],nextState:"3"}},"- orbital overlap":{o:{action_:["output",{type_:"insert",option:"hyphen"}],nextState:"2"},d:{action_:["output",{type_:"insert",option:"hyphen"}],nextState:"2"}},"-":{"0|1|2":{action_:[{type_:"output",option:1},"beginsWithBond=true",{type_:"bond",option:"-"}],nextState:"3"},3:{action_:{type_:"bond",option:"-"}},a:{action_:["output",{type_:"insert",option:"hyphen"}],nextState:"2"},as:{action_:[{type_:"output",option:2},{type_:"bond",option:"-"}],nextState:"3"},b:{action_:"b="},o:{action_:{type_:"- after o/d",option:!1},nextState:"2"},q:{action_:{type_:"- after o/d",option:!1},nextState:"2"},"d|qd|dq":{action_:{type_:"- after o/d",option:!0},nextState:"2"},"D|qD|p":{action_:["output",{type_:"bond",option:"-"}],nextState:"3"}},amount2:{"1|3":{action_:"a=",nextState:"a"}},letters:{"0|1|2|3|a|as|b|p|bp|o":{action_:"o=",nextState:"o"},"q|dq":{action_:["output","o="],nextState:"o"},"d|D|qd|qD":{action_:"o after d",nextState:"o"}},digits:{o:{action_:"q=",nextState:"q"},"d|D":{action_:"q=",nextState:"dq"},q:{action_:["output","o="],nextState:"o"},a:{action_:"o=",nextState:"o"}},"space A":{"b|p|bp":{action_:[]}},space:{a:{action_:[],nextState:"as"},0:{action_:"sb=false"},"1|2":{action_:"sb=true"},"r|rt|rd|rdt|rdq":{action_:"output",nextState:"0"},"*":{action_:["output","sb=true"],nextState:"1"}},"1st-level escape":{"1|2":{action_:["output",{type_:"insert+p1",option:"1st-level escape"}]},"*":{action_:["output",{type_:"insert+p1",option:"1st-level escape"}],nextState:"0"}},"[(...)]":{"r|rt":{action_:"rd=",nextState:"rd"},"rd|rdt":{action_:"rq=",nextState:"rdq"}},"...":{"o|d|D|dq|qd|qD":{action_:["output",{type_:"bond",option:"..."}],nextState:"3"},"*":{action_:[{type_:"output",option:1},{type_:"insert",option:"ellipsis"}],nextState:"1"}},". __* ":{"*":{action_:["output",{type_:"insert",option:"addition compound"}],nextState:"1"}},"state of aggregation $":{"*":{action_:["output","state of aggregation"],nextState:"1"}},"{[(":{"a|as|o":{action_:["o=","output","parenthesisLevel++"],nextState:"2"},"0|1|2|3":{action_:["o=","output","parenthesisLevel++"],nextState:"2"},"*":{action_:["output","o=","output","parenthesisLevel++"],nextState:"2"}},")]}":{"0|1|2|3|b|p|bp|o":{action_:["o=","parenthesisLevel--"],nextState:"o"},"a|as|d|D|q|qd|qD|dq":{action_:["output","o=","parenthesisLevel--"],nextState:"o"}},", ":{"*":{action_:["output","comma"],nextState:"0"}},"^_":{"*":{action_:[]}},"^{(...)}|^($...$)":{"0|1|2|as":{action_:"b=",nextState:"b"},p:{action_:"b=",nextState:"bp"},"3|o":{action_:"d= kv",nextState:"D"},q:{action_:"d=",nextState:"qD"},"d|D|qd|qD|dq":{action_:["output","d="],nextState:"D"}},"^a|^\\x{}{}|^\\x{}|^\\x|'":{"0|1|2|as":{action_:"b=",nextState:"b"},p:{action_:"b=",nextState:"bp"},"3|o":{action_:"d= kv",nextState:"d"},q:{action_:"d=",nextState:"qd"},"d|qd|D|qD":{action_:"d="},dq:{action_:["output","d="],nextState:"d"}},"_{(state of aggregation)}$":{"d|D|q|qd|qD|dq":{action_:["output","q="],nextState:"q"}},"_{(...)}|_($...$)|_9|_\\x{}{}|_\\x{}|_\\x":{"0|1|2|as":{action_:"p=",nextState:"p"},b:{action_:"p=",nextState:"bp"},"3|o":{action_:"q=",nextState:"q"},"d|D":{action_:"q=",nextState:"dq"},"q|qd|qD|dq":{action_:["output","q="],nextState:"q"}},"=<>":{"0|1|2|3|a|as|o|q|d|D|qd|qD|dq":{action_:[{type_:"output",option:2},"bond"],nextState:"3"}},"#":{"0|1|2|3|a|as|o":{action_:[{type_:"output",option:2},{type_:"bond",option:"#"}],nextState:"3"}},"{}^":{"*":{action_:[{type_:"output",option:1},{type_:"insert",option:"tinySkip"}],nextState:"1"}},"{}":{"*":{action_:{type_:"output",option:1},nextState:"1"}},"{...}":{"0|1|2|3|a|as|b|p|bp":{action_:"o=",nextState:"o"},"o|d|D|q|qd|qD|dq":{action_:["output","o="],nextState:"o"}},"$...$":{a:{action_:"a="},"0|1|2|3|as|b|p|bp|o":{action_:"o=",nextState:"o"},"as|o":{action_:"o="},"q|d|D|qd|qD|dq":{action_:["output","o="],nextState:"o"}},"\\bond{(...)}":{"*":{action_:[{type_:"output",option:2},"bond"],nextState:"3"}},"\\frac{(...)}":{"*":{action_:[{type_:"output",option:1},"frac-output"],nextState:"3"}},"\\overset{(...)}":{"*":{action_:[{type_:"output",option:2},"overset-output"],nextState:"3"}},"\\underset{(...)}":{"*":{action_:[{type_:"output",option:2},"underset-output"],nextState:"3"}},"\\underbrace{(...)}":{"*":{action_:[{type_:"output",option:2},"underbrace-output"],nextState:"3"}},"\\color{(...)}{(...)}":{"*":{action_:[{type_:"output",option:2},"color-output"],nextState:"3"}},"\\color{(...)}":{"*":{action_:[{type_:"output",option:2},"color0-output"]}},"\\ce{(...)}":{"*":{action_:[{type_:"output",option:2},"ce"],nextState:"3"}},"\\,":{"*":{action_:[{type_:"output",option:1},"copy"],nextState:"1"}},"\\pu{(...)}":{"*":{action_:["output",{type_:"write",option:"{"},"pu",{type_:"write",option:"}"}],nextState:"3"}},"\\x{}{}|\\x{}|\\x":{"0|1|2|3|a|as|b|p|bp|o|c0":{action_:["o=","output"],nextState:"3"},"*":{action_:["output","o=","output"],nextState:"3"}},others:{"*":{action_:[{type_:"output",option:1},"copy"],nextState:"3"}},else2:{a:{action_:"a to o",nextState:"o",revisit:!0},as:{action_:["output","sb=true"],nextState:"1",revisit:!0},"r|rt|rd|rdt|rdq":{action_:["output"],nextState:"0",revisit:!0},"*":{action_:["output","copy"],nextState:"3"}}}),actions:{"o after d":function(t,e){var r;if((t.d||"").match(/^[1-9][0-9]*$/)){var n=t.d;t.d=void 0,(r=this.output(t)).push({type_:"tinySkip"}),t.b=n}else r=this.output(t);return o.actions["o="](t,e),r},"d= kv":function(t,e){t.d=e,t.dType="kv"},"charge or bond":function(t,e){if(t.beginsWithBond){var r=[];return o.concatArray(r,this.output(t)),o.concatArray(r,o.actions.bond(t,e,"-")),r}t.d=e},"- after o/d":function(t,e,r){var n=o.patterns.match_("orbital",t.o||""),a=o.patterns.match_("one lowercase greek letter $",t.o||""),i=o.patterns.match_("one lowercase latin letter $",t.o||""),s=o.patterns.match_("$one lowercase latin letter$ $",t.o||""),l="-"===e&&(n&&""===n.remainder||a||i||s);!l||t.a||t.b||t.p||t.d||t.q||n||!i||(t.o="$"+t.o+"$");var c=[];return l?(o.concatArray(c,this.output(t)),c.push({type_:"hyphen"})):(n=o.patterns.match_("digits",t.d||""),r&&n&&""===n.remainder?(o.concatArray(c,o.actions["d="](t,e)),o.concatArray(c,this.output(t))):(o.concatArray(c,this.output(t)),o.concatArray(c,o.actions.bond(t,e,"-")))),c},"a to o":function(t){t.o=t.a,t.a=void 0},"sb=true":function(t){t.sb=!0},"sb=false":function(t){t.sb=!1},"beginsWithBond=true":function(t){t.beginsWithBond=!0},"beginsWithBond=false":function(t){t.beginsWithBond=!1},"parenthesisLevel++":function(t){t.parenthesisLevel++},"parenthesisLevel--":function(t){t.parenthesisLevel--},"state of aggregation":function(t,e){return{type_:"state of aggregation",p1:o.go(e,"o")}},comma:function(t,e){var r=e.replace(/\s*$/,"");return r!==e&&0===t.parenthesisLevel?{type_:"comma enumeration L",p1:r}:{type_:"comma enumeration M",p1:r}},output:function(t,e,r){var n;if(t.r){var a;a="M"===t.rdt?o.go(t.rd,"tex-math"):"T"===t.rdt?[{type_:"text",p1:t.rd||""}]:o.go(t.rd,"ce");var i;i="M"===t.rqt?o.go(t.rq,"tex-math"):"T"===t.rqt?[{type_:"text",p1:t.rq||""}]:o.go(t.rq,"ce"),n={type_:"arrow",r:t.r,rd:a,rq:i}}else n=[],(t.a||t.b||t.p||t.o||t.q||t.d||r)&&(t.sb&&n.push({type_:"entitySkip"}),t.o||t.q||t.d||t.b||t.p||2===r?t.o||t.q||t.d||!t.b&&!t.p?t.o&&"kv"===t.dType&&o.patterns.match_("d-oxidation$",t.d||"")?t.dType="oxidation":t.o&&"kv"===t.dType&&!t.q&&(t.dType=void 0):(t.o=t.a,t.d=t.b,t.q=t.p,t.a=t.b=t.p=void 0):(t.o=t.a,t.a=void 0),n.push({type_:"chemfive",a:o.go(t.a,"a"),b:o.go(t.b,"bd"),p:o.go(t.p,"pq"),o:o.go(t.o,"o"),q:o.go(t.q,"pq"),d:o.go(t.d,"oxidation"===t.dType?"oxidation":"bd"),dType:t.dType}));for(var s in t)"parenthesisLevel"!==s&&"beginsWithBond"!==s&&delete t[s];return n},"oxidation-output":function(t,e){var r=["{"];return o.concatArray(r,o.go(e,"oxidation")),r.push("}"),r},"frac-output":function(t,e){return{type_:"frac-ce",p1:o.go(e[0],"ce"),p2:o.go(e[1],"ce")}},"overset-output":function(t,e){return{type_:"overset",p1:o.go(e[0],"ce"),p2:o.go(e[1],"ce")}},"underset-output":function(t,e){return{type_:"underset",p1:o.go(e[0],"ce"),p2:o.go(e[1],"ce")}},"underbrace-output":function(t,e){return{type_:"underbrace",p1:o.go(e[0],"ce"),p2:o.go(e[1],"ce")}},"color-output":function(t,e){return{type_:"color",color1:e[0],color2:o.go(e[1],"ce")}},"r=":function(t,e){t.r=e},"rdt=":function(t,e){t.rdt=e},"rd=":function(t,e){t.rd=e},"rqt=":function(t,e){t.rqt=e},"rq=":function(t,e){t.rq=e},operator:function(t,e,r){return{type_:"operator",kind_:r||e}}}},a:{transitions:n({empty:{"*":{action_:[]}},"1/2$":{0:{action_:"1/2"}},else:{0:{action_:[],nextState:"1",revisit:!0}},"${(...)}$__$(...)$":{"*":{action_:"tex-math tight",nextState:"1"}},",":{"*":{action_:{type_:"insert",option:"commaDecimal"}}},else2:{"*":{action_:"copy"}}}),actions:{}},o:{transitions:n({empty:{"*":{action_:[]}},"1/2$":{0:{action_:"1/2"}},else:{0:{action_:[],nextState:"1",revisit:!0}},letters:{"*":{action_:"rm"}},"\\ca":{"*":{action_:{type_:"insert",option:"circa"}}},"\\pu{(...)}":{"*":{action_:[{type_:"write",option:"{"},"pu",{type_:"write",option:"}"}]}},"\\x{}{}|\\x{}|\\x":{"*":{action_:"copy"}},"${(...)}$__$(...)$":{"*":{action_:"tex-math"}},"{(...)}":{"*":{action_:[{type_:"write",option:"{"},"text",{type_:"write",option:"}"}]}},else2:{"*":{action_:"copy"}}}),actions:{}},text:{transitions:n({empty:{"*":{action_:"output"}},"{...}":{"*":{action_:"text="}},"${(...)}$__$(...)$":{"*":{action_:"tex-math"}},"\\greek":{"*":{action_:["output","rm"]}},"\\pu{(...)}":{"*":{action_:["output",{type_:"write",option:"{"},"pu",{type_:"write",option:"}"}]}},"\\,|\\x{}{}|\\x{}|\\x":{"*":{action_:["output","copy"]}},else:{"*":{action_:"text="}}}),actions:{output:function(t){if(t.text_){var e={type_:"text",p1:t.text_};for(var r in t)delete t[r];return e}}}},pq:{transitions:n({empty:{"*":{action_:[]}},"state of aggregation $":{"*":{action_:"state of aggregation"}},i$:{0:{action_:[],nextState:"!f",revisit:!0}},"(KV letters),":{0:{action_:"rm",nextState:"0"}},formula$:{0:{action_:[],nextState:"f",revisit:!0}},"1/2$":{0:{action_:"1/2"}},else:{0:{action_:[],nextState:"!f",revisit:!0}},"${(...)}$__$(...)$":{"*":{action_:"tex-math"}},"{(...)}":{"*":{action_:"text"}},"a-z":{f:{action_:"tex-math"}},letters:{"*":{action_:"rm"}},"-9.,9":{"*":{action_:"9,9"}},",":{"*":{action_:{type_:"insert+p1",option:"comma enumeration S"}}},"\\color{(...)}{(...)}":{"*":{action_:"color-output"}},"\\color{(...)}":{"*":{action_:"color0-output"}},"\\ce{(...)}":{"*":{action_:"ce"}},"\\pu{(...)}":{"*":{action_:[{type_:"write",option:"{"},"pu",{type_:"write",option:"}"}]}},"\\,|\\x{}{}|\\x{}|\\x":{"*":{action_:"copy"}},else2:{"*":{action_:"copy"}}}),actions:{"state of aggregation":function(t,e){return{type_:"state of aggregation subscript",p1:o.go(e,"o")}},"color-output":function(t,e){return{type_:"color",color1:e[0],color2:o.go(e[1],"pq")}}}},bd:{transitions:n({empty:{"*":{action_:[]}},x$:{0:{action_:[],nextState:"!f",revisit:!0}},formula$:{0:{action_:[],nextState:"f",revisit:!0}},else:{0:{action_:[],nextState:"!f",revisit:!0}},"-9.,9 no missing 0":{"*":{action_:"9,9"}},".":{"*":{action_:{type_:"insert",option:"electron dot"}}},"a-z":{f:{action_:"tex-math"}},x:{"*":{action_:{type_:"insert",option:"KV x"}}},letters:{"*":{action_:"rm"}},"'":{"*":{action_:{type_:"insert",option:"prime"}}},"${(...)}$__$(...)$":{"*":{action_:"tex-math"}},"{(...)}":{"*":{action_:"text"}},"\\color{(...)}{(...)}":{"*":{action_:"color-output"}},"\\color{(...)}":{"*":{action_:"color0-output"}},"\\ce{(...)}":{"*":{action_:"ce"}},"\\pu{(...)}":{"*":{action_:[{type_:"write",option:"{"},"pu",{type_:"write",option:"}"}]}},"\\,|\\x{}{}|\\x{}|\\x":{"*":{action_:"copy"}},else2:{"*":{action_:"copy"}}}),actions:{"color-output":function(t,e){return{type_:"color",color1:e[0],color2:o.go(e[1],"bd")}}}},oxidation:{transitions:n({empty:{"*":{action_:"roman-numeral"}},"pm-operator":{"*":{action_:{type_:"o=+p1",option:"\\pm"}}},else:{"*":{action_:"o="}}}),actions:{"roman-numeral":function(t){return{type_:"roman numeral",p1:t.o||""}}}},"tex-math":{transitions:n({empty:{"*":{action_:"output"}},"\\ce{(...)}":{"*":{action_:["output","ce"]}},"\\pu{(...)}":{"*":{action_:["output",{type_:"write",option:"{"},"pu",{type_:"write",option:"}"}]}},"{...}|\\,|\\x{}{}|\\x{}|\\x":{"*":{action_:"o="}},else:{"*":{action_:"o="}}}),actions:{output:function(t){if(t.o){var e={type_:"tex-math",p1:t.o};for(var r in t)delete t[r];return e}}}},"tex-math tight":{transitions:n({empty:{"*":{action_:"output"}},"\\ce{(...)}":{"*":{action_:["output","ce"]}},"\\pu{(...)}":{"*":{action_:["output",{type_:"write",option:"{"},"pu",{type_:"write",option:"}"}]}},"{...}|\\,|\\x{}{}|\\x{}|\\x":{"*":{action_:"o="}},"-|+":{"*":{action_:"tight operator"}},else:{"*":{action_:"o="}}}),actions:{"tight operator":function(t,e){t.o=(t.o||"")+"{"+e+"}"},output:function(t){if(t.o){var e={type_:"tex-math",p1:t.o};for(var r in t)delete t[r];return e}}}},"9,9":{transitions:n({empty:{"*":{action_:[]}},",":{"*":{action_:"comma"}},else:{"*":{action_:"copy"}}}),actions:{comma:function(){return{type_:"commaDecimal"}}}},pu:{transitions:n({empty:{"*":{action_:"output"}},space$:{"*":{action_:["output","space"]}},"{[(|)]}":{"0|a":{action_:"copy"}},"(-)(9)^(-9)":{0:{action_:"number^",nextState:"a"}},"(-)(9.,9)(e)(99)":{0:{action_:"enumber",nextState:"a"}},space:{"0|a":{action_:[]}},"pm-operator":{"0|a":{action_:{type_:"operator",option:"\\pm"},nextState:"0"}},operator:{"0|a":{action_:"copy",nextState:"0"}},"//":{d:{action_:"o=",nextState:"/"}},"/":{d:{action_:"o=",nextState:"/"}},"{...}|else":{"0|d":{action_:"d=",nextState:"d"},a:{action_:["space","d="],nextState:"d"},"/|q":{action_:"q=",nextState:"q"}}}),actions:{enumber:function(t,e){var r=[];return"+-"===e[0]||"+/-"===e[0]?r.push("\\pm "):e[0]&&r.push(e[0]),e[1]&&(o.concatArray(r,o.go(e[1],"pu-9,9")),e[2]&&(e[2].match(/[,.]/)?o.concatArray(r,o.go(e[2],"pu-9,9")):r.push(e[2])),(e[3]||e[4])&&("e"===e[3]||"*"===e[4]?r.push({type_:"cdot"}):r.push({type_:"times"}))),e[5]&&r.push("10^{"+e[5]+"}"),r},"number^":function(t,e){var r=[];return"+-"===e[0]||"+/-"===e[0]?r.push("\\pm "):e[0]&&r.push(e[0]),o.concatArray(r,o.go(e[1],"pu-9,9")),r.push("^{"+e[2]+"}"),r},operator:function(t,e,r){return{type_:"operator",kind_:r||e}},space:function(){return{type_:"pu-space-1"}},output:function(t){var e,r=o.patterns.match_("{(...)}",t.d||"");r&&""===r.remainder&&(t.d=r.match_);var n=o.patterns.match_("{(...)}",t.q||"");if(n&&""===n.remainder&&(t.q=n.match_),t.d&&(t.d=t.d.replace(/\u00B0C|\^oC|\^{o}C/g,"{}^{\\circ}C"),t.d=t.d.replace(/\u00B0F|\^oF|\^{o}F/g,"{}^{\\circ}F")),t.q){t.q=t.q.replace(/\u00B0C|\^oC|\^{o}C/g,"{}^{\\circ}C"),t.q=t.q.replace(/\u00B0F|\^oF|\^{o}F/g,"{}^{\\circ}F");var a={d:o.go(t.d,"pu"),q:o.go(t.q,"pu")};"//"===t.o?e={type_:"pu-frac",p1:a.d,p2:a.q}:(e=a.d,a.d.length>1||a.q.length>1?e.push({type_:" / "}):e.push({type_:"/"}),o.concatArray(e,a.q))}else e=o.go(t.d,"pu-2");for(var i in t)delete t[i];return e}}},"pu-2":{transitions:n({empty:{"*":{action_:"output"}},"*":{"*":{action_:["output","cdot"],nextState:"0"}},"\\x":{"*":{action_:"rm="}},space:{"*":{action_:["output","space"],nextState:"0"}},"^{(...)}|^(-1)":{1:{action_:"^(-1)"}},"-9.,9":{0:{action_:"rm=",nextState:"0"},1:{action_:"^(-1)",nextState:"0"}},"{...}|else":{"*":{action_:"rm=",nextState:"1"}}}),actions:{cdot:function(){return{type_:"tight cdot"}},"^(-1)":function(t,e){t.rm+="^{"+e+"}"},space:function(){return{type_:"pu-space-2"}},output:function(t){var e=[];if(t.rm){var r=o.patterns.match_("{(...)}",t.rm||"");e=r&&""===r.remainder?o.go(r.match_,"pu"):{type_:"rm",p1:t.rm}}for(var n in t)delete t[n];return e}}},"pu-9,9":{transitions:n({empty:{0:{action_:"output-0"},o:{action_:"output-o"}},",":{0:{action_:["output-0","comma"],nextState:"o"}},".":{0:{action_:["output-0","copy"],nextState:"o"}},else:{"*":{action_:"text="}}}),actions:{comma:function(){return{type_:"commaDecimal"}},"output-0":function(t){var e=[];if(t.text_=t.text_||"",t.text_.length>4){var r=t.text_.length%3;0===r&&(r=3);for(var n=t.text_.length-3;n>0;n-=3)e.push(t.text_.substr(n,3)),e.push({type_:"1000 separator"});e.push(t.text_.substr(0,r)),e.reverse()}else e.push(t.text_);for(var o in t)delete t[o];return e},"output-o":function(t){var e=[];if(t.text_=t.text_||"",t.text_.length>4){var r=t.text_.length-3,n=void 0;for(n=0;n<r;n+=3)e.push(t.text_.substr(n,3)),e.push({type_:"1000 separator"});e.push(t.text_.substr(n))}else e.push(t.text_);for(var o in t)delete t[o];return e}}}}},a={go:function(t,e){if(!t)return"";for(var r="",n=!1,o=0;o<t.length;o++){var i=t[o];"string"==typeof i?r+=i:(r+=a._go2(i),"1st-level escape"===i.type_&&(n=!0))}return e&&!n&&r&&(r="{"+r+"}"),r},_goInner:function(t){return a.go(t,!1)},_go2:function(t){var e;switch(t.type_){case"chemfive":e="";var r={a:a._goInner(t.a),b:a._goInner(t.b),p:a._goInner(t.p),o:a._goInner(t.o),q:a._goInner(t.q),d:a._goInner(t.d)};r.a&&(r.a.match(/^[+\-]/)&&(r.a="{"+r.a+"}"),e+=r.a+"\\,"),(r.b||r.p)&&(e+="{\\vphantom{A}}",e+="^{\\hphantom{"+(r.b||"")+"}}_{\\hphantom{"+(r.p||"")+"}}",e+="\\mkern-1.5mu",e+="{\\vphantom{A}}",e+="^{\\smash[t]{\\vphantom{2}}\\llap{"+(r.b||"")+"}}",e+="_{\\vphantom{2}\\llap{\\smash[t]{"+(r.p||"")+"}}}"),r.o&&(r.o.match(/^[+\-]/)&&(r.o="{"+r.o+"}"),e+=r.o),"kv"===t.dType?((r.d||r.q)&&(e+="{\\vphantom{A}}"),r.d&&(e+="^{"+r.d+"}"),r.q&&(e+="_{\\smash[t]{"+r.q+"}}")):"oxidation"===t.dType?(r.d&&(e+="{\\vphantom{A}}",e+="^{"+r.d+"}"),r.q&&(e+="{\\vphantom{A}}",e+="_{\\smash[t]{"+r.q+"}}")):(r.q&&(e+="{\\vphantom{A}}",e+="_{\\smash[t]{"+r.q+"}}"),r.d&&(e+="{\\vphantom{A}}",e+="^{"+r.d+"}"));break;case"rm":case"roman numeral":e="\\mathrm{"+t.p1+"}";break;case"text":t.p1.match(/[\^_]/)?(t.p1=t.p1.replace(" ","~").replace("-","\\text{-}"),e="\\mathrm{"+t.p1+"}"):e="\\text{"+t.p1+"}";break;case"state of aggregation":e="\\mskip2mu "+a._goInner(t.p1);break;case"state of aggregation subscript":e="\\mskip1mu "+a._goInner(t.p1);break;case"bond":if(!(e=a._getBond(t.kind_)))throw["MhchemErrorBond","mhchem Error. Unknown bond type ("+t.kind_+")"];break;case"frac":var n="\\frac{"+t.p1+"}{"+t.p2+"}";e="\\mathchoice{\\textstyle"+n+"}{"+n+"}{"+n+"}{"+n+"}";break;case"pu-frac":var o="\\frac{"+a._goInner(t.p1)+"}{"+a._goInner(t.p2)+"}";e="\\mathchoice{\\textstyle"+o+"}{"+o+"}{"+o+"}{"+o+"}";break;case"tex-math":case"1st-level escape":e=t.p1+" ";break;case"frac-ce":e="\\frac{"+a._goInner(t.p1)+"}{"+a._goInner(t.p2)+"}";break;case"overset":e="\\overset{"+a._goInner(t.p1)+"}{"+a._goInner(t.p2)+"}";break;case"underset":e="\\underset{"+a._goInner(t.p1)+"}{"+a._goInner(t.p2)+"}";break;case"underbrace":e="\\underbrace{"+a._goInner(t.p1)+"}_{"+a._goInner(t.p2)+"}";break;case"color":e="{\\color{"+t.color1+"}{"+a._goInner(t.color2)+"}}";break;case"color0":e="\\color{"+t.color+"}";break;case"arrow":var i={rd:a._goInner(t.rd),rq:a._goInner(t.rq)},s=a._getArrow(t.r);i.rd||i.rq?"<=>"===t.r||"<=>>"===t.r||"<<=>"===t.r||"<--\x3e"===t.r?(s="\\long"+s,i.rd&&(s="\\overset{"+i.rd+"}{"+s+"}"),i.rq&&(s="<--\x3e"===t.r?"\\underset{\\lower2mu{"+i.rq+"}}{"+s+"}":"\\underset{\\lower6mu{"+i.rq+"}}{"+s+"}"),s=" {}\\mathrel{"+s+"}{} "):(i.rq&&(s+="[{"+i.rq+"}]"),s=" {}\\mathrel{\\x"+(s+="{"+i.rd+"}")+"}{} "):s=" {}\\mathrel{\\long"+s+"}{} ",e=s;break;case"operator":e=a._getOperator(t.kind_);break;case"space":e=" ";break;case"tinySkip":e="\\mkern2mu";break;case"entitySkip":case"pu-space-1":e="~";break;case"pu-space-2":e="\\mkern3mu ";break;case"1000 separator":e="\\mkern2mu ";break;case"commaDecimal":e="{,}";break;case"comma enumeration L":e="{"+t.p1+"}\\mkern6mu ";break;case"comma enumeration M":e="{"+t.p1+"}\\mkern3mu ";break;case"comma enumeration S":e="{"+t.p1+"}\\mkern1mu ";break;case"hyphen":e="\\text{-}";break;case"addition compound":e="\\,{\\cdot}\\,";break;case"electron dot":e="\\mkern1mu \\bullet\\mkern1mu ";break;case"KV x":e="{\\times}";break;case"prime":e="\\prime ";break;case"cdot":e="\\cdot ";break;case"tight cdot":e="\\mkern1mu{\\cdot}\\mkern1mu ";break;case"times":e="\\times ";break;case"circa":e="{\\sim}";break;case"^":e="uparrow";break;case"v":e="downarrow";break;case"ellipsis":e="\\ldots ";break;case"/":e="/";break;case" / ":e="\\,/\\,";break;default:throw["MhchemBugT","mhchem bug T. Please report."]}return e},_getArrow:function(t){switch(t){case"->":case"→":case"⟶":return"rightarrow";case"<-":return"leftarrow";case"<->":return"leftrightarrow";case"<--\x3e":return"leftrightarrows";case"<=>":case"⇌":return"rightleftharpoons";case"<=>>":return"Rightleftharpoons";case"<<=>":return"Leftrightharpoons";default:throw["MhchemBugT","mhchem bug T. Please report."]}},_getBond:function(t){switch(t){case"-":case"1":return"{-}";case"=":case"2":return"{=}";case"#":case"3":return"{\\equiv}";case"~":return"{\\tripledash}";case"~-":return"{\\rlap{\\lower.1em{-}}\\raise.1em{\\tripledash}}";case"~=":case"~--":return"{\\rlap{\\lower.2em{-}}\\rlap{\\raise.2em{\\tripledash}}-}";case"-~-":return"{\\rlap{\\lower.2em{-}}\\rlap{\\raise.2em{-}}\\tripledash}";case"...":return"{{\\cdot}{\\cdot}{\\cdot}}";case"....":return"{{\\cdot}{\\cdot}{\\cdot}{\\cdot}}";case"->":return"{\\rightarrow}";case"<-":return"{\\leftarrow}";case"<":return"{<}";case">":return"{>}";default:throw["MhchemBugT","mhchem bug T. Please report."]}},_getOperator:function(t){switch(t){case"+":return" {}+{} ";case"-":return" {}-{} ";case"=":return" {}={} ";case"<":return" {}<{} ";case">":return" {}>{} ";case"<<":return" {}\\ll{} ";case">>":return" {}\\gg{} ";case"\\pm":return" {}\\pm{} ";case"\\approx":case"$\\approx$":return" {}\\approx{} ";case"v":case"(v)":return" \\downarrow{} ";case"^":case"(^)":return" \\uparrow{} ";default:throw["MhchemBugT","mhchem bug T. Please report."]}}}}}]);
"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[4212],{24212:(e,t,n)=>{n.r(t),n.d(t,{default:()=>v});var i=n(3053),c=n(12982),s=n(3486),a=n(36768),d=n(71677),o=n(31516);const r="jp-mod-searchable",l="jp-mod-search-active";var h;!function(e){e.search="documentsearch:start",e.searchAndReplace="documentsearch:startWithReplace",e.findNext="documentsearch:highlightNext",e.findPrevious="documentsearch:highlightPrevious",e.end="documentsearch:end",e.toggleSearchInSelection="documentsearch:toggleSearchInSelection"}(h||(h={}));const u={id:"@jupyterlab/documentsearch-extension:labShellWidgetListener",description:"Active search on valid document",requires:[i.ILabShell,s.ISearchProviderRegistry],autoStart:!0,activate:(e,t,n)=>{const i=e=>{e&&(n.hasProvider(e)?e.addClass(r):e.removeClass(r))};n.changed.connect((()=>i(t.activeWidget))),t.activeChanged.connect(((e,t)=>{const n=t.oldValue;n&&n.removeClass(r),i(t.newValue)}))}};class g{constructor(e){this._commandRegistry=e,this._cache=this._buildCache(),this._commandRegistry.keyBindingChanged.connect(this._rebuildCache,this)}get next(){return this._cache.next}get previous(){return this._cache.previous}get toggleSearchInSelection(){return this._cache.toggleSearchInSelection}_rebuildCache(){this._cache=this._buildCache()}_buildCache(){return{next:this._commandRegistry.keyBindings.find((e=>e.command===h.findNext)),previous:this._commandRegistry.keyBindings.find((e=>e.command===h.findPrevious)),toggleSearchInSelection:this._commandRegistry.keyBindings.find((e=>e.command===h.toggleSearchInSelection))}}dispose(){this._commandRegistry.keyBindingChanged.disconnect(this._rebuildCache,this)}}const m={id:"@jupyterlab/documentsearch-extension:plugin",description:"Provides the document search registry.",provides:s.ISearchProviderRegistry,requires:[d.ITranslator],optional:[c.ICommandPalette,a.ISettingRegistry],autoStart:!0,activate:(e,t,n,i)=>{const a=t.load("jupyterlab");let d=500,r="never";const u=new s.SearchProviderRegistry(t),v=new Map;if(i){const t=i.load(m.id),n=e=>{d=e.get("searchDebounceTime").composite,r=e.get("autoSearchInSelection").composite};Promise.all([t,e.restored]).then((([e])=>{n(e),e.changed.connect((e=>{n(e)}))})).catch((e=>{console.error(e.message)}))}const p=()=>{const t=e.shell.currentWidget;return!!t&&u.hasProvider(t)},S=n=>{if(!n)return;const i=n.id;let a=v.get(i);if(!a){const c=u.getProvider(n);if(!c)return;const o=new s.SearchDocumentModel(c,d),r=new g(e.commands),m=new s.SearchDocumentView(o,t,r);v.set(i,m),[h.findNext,h.findPrevious,h.end,h.toggleSearchInSelection].forEach((t=>{e.commands.notifyCommandChanged(t)})),m.closed.connect((()=>{n.isDisposed||(n.activate(),n.removeClass(l))})),m.disposed.connect((()=>{n.isDisposed||(n.activate(),n.removeClass(l)),v.delete(i),[h.findNext,h.findPrevious,h.end,h.toggleSearchInSelection].forEach((t=>{e.commands.notifyCommandChanged(t)}))})),n.disposed.connect((()=>{m.dispose(),o.dispose(),c.dispose(),r.dispose()})),a=m}return a.isAttached||(o.Widget.attach(a,n.node),n.addClass(l),n instanceof c.MainAreaWidget&&(a.node.style.top=`${n.toolbar.node.getBoundingClientRect().height+n.contentHeader.node.getBoundingClientRect().height}px`),a.model.searchExpression&&a.model.refresh()),a};return e.commands.addCommand(h.search,{label:a.__("Find…"),isEnabled:p,execute:async t=>{const n=S(e.shell.currentWidget);if(n){const e=t.searchText;e?n.setSearchText(e):n.setSearchText(n.model.suggestedInitialQuery);const i=n.model.selectionState;let c=!1;switch(r){case"multiple-selected":c="multiple"===i;break;case"any-selected":c="multiple"===i||"single"===i}c&&await n.model.setFilter("selection",!0),n.focusSearchInput()}}}),e.commands.addCommand(h.searchAndReplace,{label:a.__("Find and Replace…"),isEnabled:p,execute:t=>{const n=S(e.shell.currentWidget);if(n){const e=t.searchText;e?n.setSearchText(e):n.setSearchText(n.model.suggestedInitialQuery);const i=t.replaceText;i&&n.setReplaceText(i),n.showReplace(),n.focusSearchInput()}}}),e.commands.addCommand(h.findNext,{label:a.__("Find Next"),isEnabled:()=>!!e.shell.currentWidget&&v.has(e.shell.currentWidget.id),execute:async()=>{var t;const n=e.shell.currentWidget;n&&await(null===(t=v.get(n.id))||void 0===t?void 0:t.model.highlightNext())}}),e.commands.addCommand(h.findPrevious,{label:a.__("Find Previous"),isEnabled:()=>!!e.shell.currentWidget&&v.has(e.shell.currentWidget.id),execute:async()=>{var t;const n=e.shell.currentWidget;n&&await(null===(t=v.get(n.id))||void 0===t?void 0:t.model.highlightPrevious())}}),e.commands.addCommand(h.end,{label:a.__("End Search"),isEnabled:()=>!!e.shell.currentWidget&&v.has(e.shell.currentWidget.id),execute:async()=>{var t;const n=e.shell.currentWidget;n&&(null===(t=v.get(n.id))||void 0===t||t.close())}}),e.commands.addCommand(h.toggleSearchInSelection,{label:a.__("Search in Selection"),isEnabled:()=>!!e.shell.currentWidget&&v.has(e.shell.currentWidget.id)&&"selection"in v.get(e.shell.currentWidget.id).model.filtersDefinition,execute:async()=>{var t;const n=e.shell.currentWidget;if(!n)return;const i=null===(t=v.get(n.id))||void 0===t?void 0:t.model;if(!i)return;const c=i.filters.selection;return i.setFilter("selection",!c)}}),n&&[h.search,h.findNext,h.findPrevious,h.end,h.toggleSearchInSelection].forEach((e=>{n.addItem({command:e,category:a.__("Main Area")})})),u}},v=[m,u]}}]);
"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[7639,3680],{53680:(e,t,s)=>{s.r(t),s.d(t,{GroupItem:()=>r,IStatusBar:()=>f,Popup:()=>h,ProgressBar:()=>l,ProgressCircle:()=>m,StatusBar:()=>v,TextItem:()=>u,showPopup:()=>d});var i=s(78156),a=s.n(i);function r(e){const{spacing:t,children:s,className:a,...r}=e,n=i.Children.count(s);return i.createElement("div",{className:`jp-StatusBar-GroupItem ${a||""}`,...r},i.Children.map(s,((e,s)=>0===s?i.createElement("div",{style:{marginRight:`${t}px`}},e):s===n-1?i.createElement("div",{style:{marginLeft:`${t}px`}},e):i.createElement("div",{style:{margin:`0px ${t}px`}},e))))}var n=s(68239),o=s(31516);function d(e){const t=new h(e);return e.startHidden||t.launch(),t}class h extends o.Widget{constructor(e){super(),this._body=e.body,this._body.addClass("jp-StatusBar-HoverItem"),this._anchor=e.anchor,this._align=e.align,e.hasDynamicSize&&(this._observer=new ResizeObserver((()=>{this.update()}))),(this.layout=new o.PanelLayout).addWidget(e.body),this._body.node.addEventListener("resize",(()=>{this.update()}))}launch(){this._setGeometry(),o.Widget.attach(this,document.body),this.update(),this._anchor.addClass("jp-mod-clicked"),this._anchor.removeClass("jp-mod-highlight")}onUpdateRequest(e){this._setGeometry(),super.onUpdateRequest(e)}onAfterAttach(e){var t;document.addEventListener("click",this,!1),this.node.addEventListener("keydown",this,!1),window.addEventListener("resize",this,!1),null===(t=this._observer)||void 0===t||t.observe(this._body.node)}onBeforeDetach(e){var t;null===(t=this._observer)||void 0===t||t.disconnect(),document.removeEventListener("click",this,!1),this.node.removeEventListener("keydown",this,!1),window.removeEventListener("resize",this,!1)}onResize(){this.update()}dispose(){var e;null===(e=this._observer)||void 0===e||e.disconnect(),super.dispose(),this._anchor.removeClass("jp-mod-clicked"),this._anchor.addClass("jp-mod-highlight")}handleEvent(e){switch(e.type){case"keydown":this._evtKeydown(e);break;case"click":this._evtClick(e);break;case"resize":this.onResize()}}_evtClick(e){!e.target||this._body.node.contains(e.target)||this._anchor.node.contains(e.target)||this.dispose()}_evtKeydown(e){27===e.keyCode&&(e.stopPropagation(),e.preventDefault(),this.dispose())}_setGeometry(){let e=0;const t=this._anchor.node.getBoundingClientRect(),s=this._body.node.getBoundingClientRect();"right"===this._align&&(e=-(s.width-t.width));const i=window.getComputedStyle(this._body.node);n.HoverBox.setGeometry({anchor:t,host:document.body,maxHeight:500,minHeight:20,node:this._body.node,offset:{horizontal:e},privilege:"forceAbove",style:i})}}function l(e){const{width:t,percentage:s,...a}=e;return i.createElement("div",{className:"jp-Statusbar-ProgressBar-progress-bar",role:"progressbar","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":s},i.createElement(c,{percentage:s,...a,contentWidth:t}))}function c(e){return i.createElement("div",{style:{width:`${e.percentage}%`}},i.createElement("p",null,e.content))}function u(e){const{title:t,source:s,className:a,...r}=e;return i.createElement("span",{className:`jp-StatusBar-TextItem ${a}`,title:t,...r},s)}function m(e){return a().createElement("div",{className:"jp-Statusbar-ProgressCircle",role:"progressbar","aria-label":e.label||"Unlabelled progress circle","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":e.progress},a().createElement("svg",{viewBox:"0 0 250 250"},a().createElement("circle",{cx:"125",cy:"125",r:"104",stroke:"var(--jp-inverse-layout-color3)",strokeWidth:"20",fill:"none"}),a().createElement("path",{className:"jp-Statusbar-ProgressCirclePath",transform:"translate(125,125) scale(.9)",d:(e=>{const t=Math.max(3.6*e,.1),s=t*Math.PI/180,i=104*Math.sin(s),a=-104*Math.cos(s);return"M 0 0 v -104 A 104 104 1 "+(t<180?1:0)+" 0 "+i.toFixed(4)+" "+a.toFixed(4)+" z"})(e.progress),fill:"var(--jp-inverse-layout-color3)"})))}var p,g=s(33625),_=s(2549);class v extends o.Widget{constructor(){super(),this._isWindowNarrow=()=>window.innerWidth<=630,this._leftRankItems=[],this._rightRankItems=[],this._statusItems={},this._disposables=new _.DisposableSet,this.addClass("jp-StatusBar-Widget");const e=this.layout=new o.PanelLayout,t=this._leftSide=new o.Panel,s=this._middlePanel=new o.Panel,i=this._rightSide=new o.Panel;t.addClass("jp-StatusBar-Left"),s.addClass("jp-StatusBar-Middle"),i.addClass("jp-StatusBar-Right"),e.addWidget(t),e.addWidget(s),e.addWidget(i)}registerStatusItem(e,t){if(e in this._statusItems)throw new Error(`Status item ${e} already registered.`);const s={...p.statusItemDefaults,...t},{align:i,item:a,rank:r,priority:n}=s,o=()=>{this._refreshItem(e)};s.activeStateChanged&&s.activeStateChanged.connect(o);const d={id:e,rank:r,priority:n};if(s.item.addClass("jp-StatusBar-Item"),this._statusItems[e]=s,"left"===i){const e=this._findInsertIndex(this._leftRankItems,d);-1===e?(this._leftSide.addWidget(a),this._leftRankItems.push(d)):(g.ArrayExt.insert(this._leftRankItems,e,d),this._leftSide.insertWidget(e,a))}else if("right"===i){const e=this._findInsertIndex(this._rightRankItems,d);-1===e?(this._rightSide.addWidget(a),this._rightRankItems.push(d)):(g.ArrayExt.insert(this._rightRankItems,e,d),this._rightSide.insertWidget(e,a))}else this._middlePanel.addWidget(a);this._refreshItem(e);const h=new _.DisposableDelegate((()=>{delete this._statusItems[e],s.activeStateChanged&&s.activeStateChanged.disconnect(o),a.parent=null,a.dispose()}));return this._disposables.add(h),h}dispose(){this._leftRankItems.length=0,this._rightRankItems.length=0,this._disposables.dispose(),super.dispose()}onUpdateRequest(e){this._refreshAll(),super.onUpdateRequest(e)}_findInsertIndex(e,t){return g.ArrayExt.findFirstIndex(e,(e=>e.rank>t.rank))}_refreshItem(e){const t=this._statusItems[e];!t.isActive()||0===t.priority&&this._isWindowNarrow()?t.item.hide():(t.item.show(),t.item.update())}_refreshAll(){Object.keys(this._statusItems).forEach((e=>{this._refreshItem(e)}))}}!function(e){e.statusItemDefaults={align:"left",rank:0,priority:0,isActive:()=>!0,activeStateChanged:void 0}}(p||(p={}));const f=new(s(20998).Token)("@jupyterlab/statusbar:IStatusBar","A service for the status bar on the application. Use this if you want to add new status bar items.")}}]);
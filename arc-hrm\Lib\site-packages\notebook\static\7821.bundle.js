"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[7821],{77821:(t,e,s)=>{s.r(e),s.d(e,{ConflatableMessage:()=>h,Message:()=>o,MessageLoop:()=>n});var i,n,l=s(33625);class r{constructor(){this._first=null,this._last=null,this._size=0}get isEmpty(){return 0===this._size}get size(){return this._size}get length(){return this._size}get first(){return this._first?this._first.value:void 0}get last(){return this._last?this._last.value:void 0}get firstNode(){return this._first}get lastNode(){return this._last}*[Symbol.iterator](){let t=this._first;for(;t;)yield t.value,t=t.next}*retro(){let t=this._last;for(;t;)yield t.value,t=t.prev}*nodes(){let t=this._first;for(;t;)yield t,t=t.next}*retroNodes(){let t=this._last;for(;t;)yield t,t=t.prev}assign(t){this.clear();for(const e of t)this.addLast(e)}push(t){this.addLast(t)}pop(){return this.removeLast()}shift(t){this.addFirst(t)}unshift(){return this.removeFirst()}addFirst(t){let e=new i.LinkedListNode(this,t);return this._first?(e.next=this._first,this._first.prev=e,this._first=e):(this._first=e,this._last=e),this._size++,e}addLast(t){let e=new i.LinkedListNode(this,t);return this._last?(e.prev=this._last,this._last.next=e,this._last=e):(this._first=e,this._last=e),this._size++,e}insertBefore(t,e){if(!e||e===this._first)return this.addFirst(t);if(!(e instanceof i.LinkedListNode)||e.list!==this)throw new Error("Reference node is not owned by the list.");let s=new i.LinkedListNode(this,t),n=e,l=n.prev;return s.next=n,s.prev=l,n.prev=s,l.next=s,this._size++,s}insertAfter(t,e){if(!e||e===this._last)return this.addLast(t);if(!(e instanceof i.LinkedListNode)||e.list!==this)throw new Error("Reference node is not owned by the list.");let s=new i.LinkedListNode(this,t),n=e,l=n.next;return s.next=l,s.prev=n,n.next=s,l.prev=s,this._size++,s}removeFirst(){let t=this._first;if(t)return t===this._last?(this._first=null,this._last=null):(this._first=t.next,this._first.prev=null),t.list=null,t.next=null,t.prev=null,this._size--,t.value}removeLast(){let t=this._last;if(t)return t===this._first?(this._first=null,this._last=null):(this._last=t.prev,this._last.next=null),t.list=null,t.next=null,t.prev=null,this._size--,t.value}removeNode(t){if(!(t instanceof i.LinkedListNode)||t.list!==this)throw new Error("Node is not owned by the list.");let e=t;e===this._first&&e===this._last?(this._first=null,this._last=null):e===this._first?(this._first=e.next,this._first.prev=null):e===this._last?(this._last=e.prev,this._last.next=null):(e.next.prev=e.prev,e.prev.next=e.next),e.list=null,e.next=null,e.prev=null,this._size--}clear(){let t=this._first;for(;t;){let e=t.next;t.list=null,t.prev=null,t.next=null,t=e}this._first=null,this._last=null,this._size=0}}!function(t){t.from=function(e){let s=new t;return s.assign(e),s}}(r||(r={})),function(t){t.LinkedListNode=class{constructor(t,e){this.list=null,this.next=null,this.prev=null,this.list=t,this.value=e}}}(i||(i={}));class o{constructor(t){this.type=t}get isConflatable(){return!1}conflate(t){return!1}}class h extends o{get isConflatable(){return!0}conflate(t){return!0}}!function(t){let e=null;const s=(i=Promise.resolve(),t=>{let e=!1;return i.then((()=>!e&&t())),()=>{e=!0}});var i;function n(t,e){let s=h.get(t);if(!s||0===s.length)return void _(t,e);let i=(0,l.every)((0,l.retro)(s),(s=>!s||function(t,e,s){let i=!0;try{i="function"==typeof t?t(e,s):t.messageHook(e,s)}catch(t){u(t)}return i}(s,t,e)));i&&_(t,e)}t.sendMessage=n,t.postMessage=function(t,e){e.isConflatable&&(0,l.some)(o,(s=>s.handler===t&&!!s.msg&&s.msg.type===e.type&&!!s.msg.isConflatable&&s.msg.conflate(e)))||d(t,e)},t.installMessageHook=function(t,e){let s=h.get(t);s&&-1!==s.indexOf(e)||(s?s.push(e):h.set(t,[e]))},t.removeMessageHook=function(t,e){let s=h.get(t);if(!s)return;let i=s.indexOf(e);-1!==i&&(s[i]=null,v(s))},t.clearData=function(t){let e=h.get(t);e&&e.length>0&&(l.ArrayExt.fill(e,null),v(e));for(const e of o)e.handler===t&&(e.handler=null,e.msg=null)},t.flush=function(){f||null===e||(e(),e=null,f=!0,c(),f=!1)},t.getExceptionHandler=function(){return u},t.setExceptionHandler=function(t){let e=u;return u=t,e};const o=new r,h=new WeakMap,a=new Set;let u=t=>{console.error(t)},f=!1;function _(t,e){try{t.processMessage(e)}catch(t){u(t)}}function d(t,i){o.addLast({handler:t,msg:i}),null===e&&(e=s(c))}function c(){if(e=null,o.isEmpty)return;let t={handler:null,msg:null};for(o.addLast(t);;){let e=o.removeFirst();if(e===t)return;e.handler&&e.msg&&n(e.handler,e.msg)}}function v(t){0===a.size&&s(p),a.add(t)}function p(){a.forEach(g),a.clear()}function g(t){l.ArrayExt.removeAllWhere(t,x)}function x(t){return null===t}}(n||(n={}))}}]);
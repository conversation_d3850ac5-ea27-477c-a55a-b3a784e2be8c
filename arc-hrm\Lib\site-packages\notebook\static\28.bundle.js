"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[28],{21605:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.newState=e.STATE=e.AbstractMathItem=e.protoItem=void 0,e.protoItem=function(t,e,r,n,o,i,s){return void 0===s&&(s=null),{open:t,math:e,close:r,n,start:{n:o},end:{n:i},display:s}};var r=function(){function t(t,r,n,o,i){void 0===n&&(n=!0),void 0===o&&(o={i:0,n:0,delim:""}),void 0===i&&(i={i:0,n:0,delim:""}),this.root=null,this.typesetRoot=null,this.metrics={},this.inputData={},this.outputData={},this._state=e.STATE.UNPROCESSED,this.math=t,this.inputJax=r,this.display=n,this.start=o,this.end=i,this.root=null,this.typesetRoot=null,this.metrics={},this.inputData={},this.outputData={}}return Object.defineProperty(t.prototype,"isEscaped",{get:function(){return null===this.display},enumerable:!1,configurable:!0}),t.prototype.render=function(t){t.renderActions.renderMath(this,t)},t.prototype.rerender=function(t,r){void 0===r&&(r=e.STATE.RERENDER),this.state()>=r&&this.state(r-1),t.renderActions.renderMath(this,t,r)},t.prototype.convert=function(t,r){void 0===r&&(r=e.STATE.LAST),t.renderActions.renderConvert(this,t,r)},t.prototype.compile=function(t){this.state()<e.STATE.COMPILED&&(this.root=this.inputJax.compile(this,t),this.state(e.STATE.COMPILED))},t.prototype.typeset=function(t){this.state()<e.STATE.TYPESET&&(this.typesetRoot=t.outputJax[this.isEscaped?"escaped":"typeset"](this,t),this.state(e.STATE.TYPESET))},t.prototype.updateDocument=function(t){},t.prototype.removeFromDocument=function(t){void 0===t&&(t=!1)},t.prototype.setMetrics=function(t,e,r,n,o){this.metrics={em:t,ex:e,containerWidth:r,lineWidth:n,scale:o}},t.prototype.state=function(t,r){return void 0===t&&(t=null),void 0===r&&(r=!1),null!=t&&(t<e.STATE.INSERTED&&this._state>=e.STATE.INSERTED&&this.removeFromDocument(r),t<e.STATE.TYPESET&&this._state>=e.STATE.TYPESET&&(this.outputData={}),t<e.STATE.COMPILED&&this._state>=e.STATE.COMPILED&&(this.inputData={}),this._state=t),this._state},t.prototype.reset=function(t){void 0===t&&(t=!1),this.state(e.STATE.UNPROCESSED,t)},t}();e.AbstractMathItem=r,e.STATE={UNPROCESSED:0,FINDMATH:10,COMPILED:20,CONVERT:100,METRICS:110,RERENDER:125,TYPESET:150,INSERTED:200,LAST:1e4},e.newState=function(t,r){if(t in e.STATE)throw Error("State "+t+" already exists");e.STATE[t]=r}},47578:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.TeXAtom=void 0;var s=r(83045),l=r(19625),a=function(t){function e(e,r,n){var o=t.call(this,e,r,n)||this;return o.texclass=s.TEXCLASS.ORD,o.setProperty("texClass",o.texClass),o}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"TeXAtom"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"arity",{get:function(){return-1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"notParent",{get:function(){return this.childNodes[0]&&1===this.childNodes[0].childNodes.length},enumerable:!1,configurable:!0}),e.prototype.setTeXclass=function(t){return this.childNodes[0].setTeXclass(null),this.adjustTeXclass(t)},e.prototype.adjustTeXclass=function(t){return t},e.defaults=i({},s.AbstractMmlBaseNode.defaults),e}(s.AbstractMmlBaseNode);e.TeXAtom=a,a.prototype.adjustTeXclass=l.MmlMo.prototype.adjustTeXclass},63142:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMaction=void 0;var s=r(83045),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"maction"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"arity",{get:function(){return 1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"selected",{get:function(){var t=this.attributes.get("selection"),e=Math.max(1,Math.min(this.childNodes.length,t))-1;return this.childNodes[e]||this.factory.create("mrow")},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isEmbellished",{get:function(){return this.selected.isEmbellished},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isSpacelike",{get:function(){return this.selected.isSpacelike},enumerable:!1,configurable:!0}),e.prototype.core=function(){return this.selected.core()},e.prototype.coreMO=function(){return this.selected.coreMO()},e.prototype.verifyAttributes=function(e){t.prototype.verifyAttributes.call(this,e),"toggle"!==this.attributes.get("actiontype")&&void 0!==this.attributes.getExplicit("selection")&&delete this.attributes.getAllAttributes().selection},e.prototype.setTeXclass=function(t){"tooltip"===this.attributes.get("actiontype")&&this.childNodes[1]&&this.childNodes[1].setTeXclass(null);var e=this.selected;return t=e.setTeXclass(t),this.updateTeXclass(e),t},e.prototype.nextToggleSelection=function(){var t=Math.max(1,this.attributes.get("selection")+1);t>this.childNodes.length&&(t=1),this.attributes.set("selection",t)},e.defaults=i(i({},s.AbstractMmlNode.defaults),{actiontype:"toggle",selection:1}),e}(s.AbstractMmlNode);e.MmlMaction=l},1334:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMath=void 0;var s=r(83045),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"math"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"linebreakContainer",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.setChildInheritedAttributes=function(e,r,n,o){"display"===this.attributes.get("mode")&&this.attributes.setInherited("display","block"),e=this.addInheritedAttributes(e,this.attributes.getAllAttributes()),r=!!this.attributes.get("displaystyle")||!this.attributes.get("displaystyle")&&"block"===this.attributes.get("display"),this.attributes.setInherited("displaystyle",r),n=this.attributes.get("scriptlevel")||this.constructor.defaults.scriptlevel,t.prototype.setChildInheritedAttributes.call(this,e,r,n,o)},e.defaults=i(i({},s.AbstractMmlLayoutNode.defaults),{mathvariant:"normal",mathsize:"normal",mathcolor:"",mathbackground:"transparent",dir:"ltr",scriptlevel:0,displaystyle:!1,display:"inline",maxwidth:"",overflow:"linebreak",altimg:"","altimg-width":"","altimg-height":"","altimg-valign":"",alttext:"",cdgroup:"",scriptsizemultiplier:1/Math.sqrt(2),scriptminsize:"8px",infixlinebreakstyle:"before",lineleading:"1ex",linebreakmultchar:"⁢",indentshift:"auto",indentalign:"auto",indenttarget:"",indentalignfirst:"indentalign",indentshiftfirst:"indentshift",indentalignlast:"indentalign",indentshiftlast:"indentshift"}),e}(s.AbstractMmlLayoutNode);e.MmlMath=l},99031:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMenclose=void 0;var s=r(83045),l=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.texclass=s.TEXCLASS.ORD,e}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"menclose"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"arity",{get:function(){return-1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"linebreakContininer",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.setTeXclass=function(t){return t=this.childNodes[0].setTeXclass(t),this.updateTeXclass(this.childNodes[0]),t},e.defaults=i(i({},s.AbstractMmlNode.defaults),{notation:"longdiv"}),e}(s.AbstractMmlNode);e.MmlMenclose=l},47149:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)},s=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMfenced=void 0;var l=r(83045),a=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.texclass=l.TEXCLASS.INNER,e.separators=[],e.open=null,e.close=null,e}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"mfenced"},enumerable:!1,configurable:!0}),e.prototype.setTeXclass=function(t){this.getPrevClass(t),this.open&&(t=this.open.setTeXclass(t)),this.childNodes[0]&&(t=this.childNodes[0].setTeXclass(t));for(var e=1,r=this.childNodes.length;e<r;e++)this.separators[e-1]&&(t=this.separators[e-1].setTeXclass(t)),this.childNodes[e]&&(t=this.childNodes[e].setTeXclass(t));return this.close&&(t=this.close.setTeXclass(t)),this.updateTeXclass(this.open),t},e.prototype.setChildInheritedAttributes=function(e,r,n,o){var i,l;this.addFakeNodes();try{for(var a=s([this.open,this.close].concat(this.separators)),u=a.next();!u.done;u=a.next()){var c=u.value;c&&c.setInheritedAttributes(e,r,n,o)}}catch(t){i={error:t}}finally{try{u&&!u.done&&(l=a.return)&&l.call(a)}finally{if(i)throw i.error}}t.prototype.setChildInheritedAttributes.call(this,e,r,n,o)},e.prototype.addFakeNodes=function(){var t,e,r=this.attributes.getList("open","close","separators"),n=r.open,o=r.close,i=r.separators;if(n=n.replace(/[ \t\n\r]/g,""),o=o.replace(/[ \t\n\r]/g,""),i=i.replace(/[ \t\n\r]/g,""),n&&(this.open=this.fakeNode(n,{fence:!0,form:"prefix"},l.TEXCLASS.OPEN)),i){for(;i.length<this.childNodes.length-1;)i+=i.charAt(i.length-1);var a=0;try{for(var u=s(this.childNodes.slice(1)),c=u.next();!c.done;c=u.next())c.value&&this.separators.push(this.fakeNode(i.charAt(a++)))}catch(e){t={error:e}}finally{try{c&&!c.done&&(e=u.return)&&e.call(u)}finally{if(t)throw t.error}}}o&&(this.close=this.fakeNode(o,{fence:!0,form:"postfix"},l.TEXCLASS.CLOSE))},e.prototype.fakeNode=function(t,e,r){void 0===e&&(e={}),void 0===r&&(r=null);var n=this.factory.create("text").setText(t),o=this.factory.create("mo",e,[n]);return o.texClass=r,o.parent=this,o},e.defaults=i(i({},l.AbstractMmlNode.defaults),{open:"(",close:")",separators:","}),e}(l.AbstractMmlNode);e.MmlMfenced=a},76198:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)},s=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMfrac=void 0;var l=r(83045),a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"mfrac"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"arity",{get:function(){return 2},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"linebreakContainer",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.setTeXclass=function(t){var e,r;this.getPrevClass(t);try{for(var n=s(this.childNodes),o=n.next();!o.done;o=n.next())o.value.setTeXclass(null)}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}return this},e.prototype.setChildInheritedAttributes=function(t,e,r,n){(!e||r>0)&&r++,this.childNodes[0].setInheritedAttributes(t,!1,r,n),this.childNodes[1].setInheritedAttributes(t,!1,r,!0)},e.defaults=i(i({},l.AbstractMmlBaseNode.defaults),{linethickness:"medium",numalign:"center",denomalign:"center",bevelled:!1}),e}(l.AbstractMmlBaseNode);e.MmlMfrac=a},49194:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMglyph=void 0;var s=r(83045),l=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.texclass=s.TEXCLASS.ORD,e}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"mglyph"},enumerable:!1,configurable:!0}),e.prototype.verifyAttributes=function(e){var r=this.attributes.getList("src","fontfamily","index"),n=r.src,o=r.fontfamily,i=r.index;""!==n||""!==o&&""!==i?t.prototype.verifyAttributes.call(this,e):this.mError("mglyph must have either src or fontfamily and index attributes",e,!0)},e.defaults=i(i({},s.AbstractMmlTokenNode.defaults),{alt:"",src:"",index:"",width:"auto",height:"auto",valign:"0em"}),e}(s.AbstractMmlTokenNode);e.MmlMglyph=l},91324:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMi=void 0;var s=r(83045),l=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.texclass=s.TEXCLASS.ORD,e}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"mi"},enumerable:!1,configurable:!0}),e.prototype.setInheritedAttributes=function(r,n,o,i){void 0===r&&(r={}),void 0===n&&(n=!1),void 0===o&&(o=0),void 0===i&&(i=!1),t.prototype.setInheritedAttributes.call(this,r,n,o,i),this.getText().match(e.singleCharacter)&&!r.mathvariant&&this.attributes.setInherited("mathvariant","italic")},e.prototype.setTeXclass=function(t){this.getPrevClass(t);var r=this.getText();return r.length>1&&r.match(e.operatorName)&&"normal"===this.attributes.get("mathvariant")&&void 0===this.getProperty("autoOP")&&void 0===this.getProperty("texClass")&&(this.texClass=s.TEXCLASS.OP,this.setProperty("autoOP",!0)),this},e.defaults=i({},s.AbstractMmlTokenNode.defaults),e.operatorName=/^[a-z][a-z0-9]*$/i,e.singleCharacter=/^[\uD800-\uDBFF]?.[\u0300-\u036F\u1AB0-\u1ABE\u1DC0-\u1DFF\u20D0-\u20EF]*$/,e}(s.AbstractMmlTokenNode);e.MmlMi=l},80489:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.MmlNone=e.MmlMprescripts=e.MmlMmultiscripts=void 0;var s=r(83045),l=r(41376),a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"mmultiscripts"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"arity",{get:function(){return 1},enumerable:!1,configurable:!0}),e.prototype.setChildInheritedAttributes=function(t,e,r,n){this.childNodes[0].setInheritedAttributes(t,e,r,n);for(var o=!1,i=1,s=0;i<this.childNodes.length;i++){var l=this.childNodes[i];if(l.isKind("mprescripts")){if(!o&&(o=!0,i%2==0)){var a=this.factory.create("mrow");this.childNodes.splice(i,0,a),a.parent=this,i++}}else{var u=n||s%2==0;l.setInheritedAttributes(t,!1,r+1,u),s++}}this.childNodes.length%2==(o?1:0)&&(this.appendChild(this.factory.create("mrow")),this.childNodes[this.childNodes.length-1].setInheritedAttributes(t,!1,r+1,n))},e.prototype.verifyChildren=function(e){for(var r=!1,n=e.fixMmultiscripts,o=0;o<this.childNodes.length;o++){var i=this.childNodes[o];i.isKind("mprescripts")&&(r?i.mError(i.kind+" can only appear once in "+this.kind,e,!0):(r=!0,o%2!=0||n||this.mError("There must be an equal number of prescripts of each type",e)))}this.childNodes.length%2!=(r?1:0)||n||this.mError("There must be an equal number of scripts of each type",e),t.prototype.verifyChildren.call(this,e)},e.defaults=i({},l.MmlMsubsup.defaults),e}(l.MmlMsubsup);e.MmlMmultiscripts=a;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"mprescripts"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"arity",{get:function(){return 0},enumerable:!1,configurable:!0}),e.prototype.verifyTree=function(e){t.prototype.verifyTree.call(this,e),this.parent&&!this.parent.isKind("mmultiscripts")&&this.mError(this.kind+" must be a child of mmultiscripts",e,!0)},e.defaults=i({},s.AbstractMmlNode.defaults),e}(s.AbstractMmlNode);e.MmlMprescripts=u;var c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"none"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"arity",{get:function(){return 0},enumerable:!1,configurable:!0}),e.prototype.verifyTree=function(e){t.prototype.verifyTree.call(this,e),this.parent&&!this.parent.isKind("mmultiscripts")&&this.mError(this.kind+" must be a child of mmultiscripts",e,!0)},e.defaults=i({},s.AbstractMmlNode.defaults),e}(s.AbstractMmlNode);e.MmlNone=c},14734:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMn=void 0;var s=r(83045),l=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.texclass=s.TEXCLASS.ORD,e}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"mn"},enumerable:!1,configurable:!0}),e.defaults=i({},s.AbstractMmlTokenNode.defaults),e}(s.AbstractMmlTokenNode);e.MmlMn=l},70596:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMpadded=void 0;var s=r(83045),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"mpadded"},enumerable:!1,configurable:!0}),e.defaults=i(i({},s.AbstractMmlLayoutNode.defaults),{width:"",height:"",depth:"",lspace:0,voffset:0}),e}(s.AbstractMmlLayoutNode);e.MmlMpadded=l},79020:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMroot=void 0;var s=r(83045),l=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.texclass=s.TEXCLASS.ORD,e}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"mroot"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"arity",{get:function(){return 2},enumerable:!1,configurable:!0}),e.prototype.setTeXclass=function(t){return this.getPrevClass(t),this.childNodes[0].setTeXclass(null),this.childNodes[1].setTeXclass(null),this},e.prototype.setChildInheritedAttributes=function(t,e,r,n){this.childNodes[0].setInheritedAttributes(t,e,r,!0),this.childNodes[1].setInheritedAttributes(t,!1,r+2,n)},e.defaults=i({},s.AbstractMmlNode.defaults),e}(s.AbstractMmlNode);e.MmlMroot=l},70938:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)},s=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.MmlInferredMrow=e.MmlMrow=void 0;var l=r(83045),a=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e._core=null,e}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"mrow"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isSpacelike",{get:function(){var t,e;try{for(var r=s(this.childNodes),n=r.next();!n.done;n=r.next())if(!n.value.isSpacelike)return!1}catch(e){t={error:e}}finally{try{n&&!n.done&&(e=r.return)&&e.call(r)}finally{if(t)throw t.error}}return!0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isEmbellished",{get:function(){var t,e,r=!1,n=0;try{for(var o=s(this.childNodes),i=o.next();!i.done;i=o.next()){var l=i.value;if(l)if(l.isEmbellished){if(r)return!1;r=!0,this._core=n}else if(!l.isSpacelike)return!1;n++}}catch(e){t={error:e}}finally{try{i&&!i.done&&(e=o.return)&&e.call(o)}finally{if(t)throw t.error}}return r},enumerable:!1,configurable:!0}),e.prototype.core=function(){return this.isEmbellished&&null!=this._core?this.childNodes[this._core]:this},e.prototype.coreMO=function(){return this.isEmbellished&&null!=this._core?this.childNodes[this._core].coreMO():this},e.prototype.nonSpaceLength=function(){var t,e,r=0;try{for(var n=s(this.childNodes),o=n.next();!o.done;o=n.next()){var i=o.value;i&&!i.isSpacelike&&r++}}catch(e){t={error:e}}finally{try{o&&!o.done&&(e=n.return)&&e.call(n)}finally{if(t)throw t.error}}return r},e.prototype.firstNonSpace=function(){var t,e;try{for(var r=s(this.childNodes),n=r.next();!n.done;n=r.next()){var o=n.value;if(o&&!o.isSpacelike)return o}}catch(e){t={error:e}}finally{try{n&&!n.done&&(e=r.return)&&e.call(r)}finally{if(t)throw t.error}}return null},e.prototype.lastNonSpace=function(){for(var t=this.childNodes.length;--t>=0;){var e=this.childNodes[t];if(e&&!e.isSpacelike)return e}return null},e.prototype.setTeXclass=function(t){var e,r,n,o;if(null!=this.getProperty("open")||null!=this.getProperty("close")){this.getPrevClass(t),t=null;try{for(var i=s(this.childNodes),a=i.next();!a.done;a=i.next())t=a.value.setTeXclass(t)}catch(t){e={error:t}}finally{try{a&&!a.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}null==this.texClass&&(this.texClass=l.TEXCLASS.INNER)}else{try{for(var u=s(this.childNodes),c=u.next();!c.done;c=u.next())t=c.value.setTeXclass(t)}catch(t){n={error:t}}finally{try{c&&!c.done&&(o=u.return)&&o.call(u)}finally{if(n)throw n.error}}this.childNodes[0]&&this.updateTeXclass(this.childNodes[0])}return t},e.defaults=i({},l.AbstractMmlNode.defaults),e}(l.AbstractMmlNode);e.MmlMrow=a;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"inferredMrow"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isInferred",{get:function(){return!0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"notParent",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.toString=function(){return"["+this.childNodes.join(",")+"]"},e.defaults=a.defaults,e}(a);e.MmlInferredMrow=u},75375:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMs=void 0;var s=r(83045),l=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.texclass=s.TEXCLASS.ORD,e}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"ms"},enumerable:!1,configurable:!0}),e.defaults=i(i({},s.AbstractMmlTokenNode.defaults),{lquote:'"',rquote:'"'}),e}(s.AbstractMmlTokenNode);e.MmlMs=l},58321:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMspace=void 0;var s=r(83045),l=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.texclass=s.TEXCLASS.NONE,e}return o(e,t),e.prototype.setTeXclass=function(t){return t},Object.defineProperty(e.prototype,"kind",{get:function(){return"mspace"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"arity",{get:function(){return 0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isSpacelike",{get:function(){return!0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"hasNewline",{get:function(){var t=this.attributes;return null==t.getExplicit("width")&&null==t.getExplicit("height")&&null==t.getExplicit("depth")&&"newline"===t.get("linebreak")},enumerable:!1,configurable:!0}),e.defaults=i(i({},s.AbstractMmlTokenNode.defaults),{width:"0em",height:"0ex",depth:"0ex",linebreak:"auto"}),e}(s.AbstractMmlTokenNode);e.MmlMspace=l},42061:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMsqrt=void 0;var s=r(83045),l=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.texclass=s.TEXCLASS.ORD,e}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"msqrt"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"arity",{get:function(){return-1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"linebreakContainer",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.setTeXclass=function(t){return this.getPrevClass(t),this.childNodes[0].setTeXclass(null),this},e.prototype.setChildInheritedAttributes=function(t,e,r,n){this.childNodes[0].setInheritedAttributes(t,e,r,!0)},e.defaults=i({},s.AbstractMmlNode.defaults),e}(s.AbstractMmlNode);e.MmlMsqrt=l},41376:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMsup=e.MmlMsub=e.MmlMsubsup=void 0;var s=r(83045),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"msubsup"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"arity",{get:function(){return 3},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"base",{get:function(){return 0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"sub",{get:function(){return 1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"sup",{get:function(){return 2},enumerable:!1,configurable:!0}),e.prototype.setChildInheritedAttributes=function(t,e,r,n){var o=this.childNodes;o[0].setInheritedAttributes(t,e,r,n),o[1].setInheritedAttributes(t,!1,r+1,n||1===this.sub),o[2]&&o[2].setInheritedAttributes(t,!1,r+1,n||2===this.sub)},e.defaults=i(i({},s.AbstractMmlBaseNode.defaults),{subscriptshift:"",superscriptshift:""}),e}(s.AbstractMmlBaseNode);e.MmlMsubsup=l;var a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"msub"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"arity",{get:function(){return 2},enumerable:!1,configurable:!0}),e.defaults=i({},l.defaults),e}(l);e.MmlMsub=a;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"msup"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"arity",{get:function(){return 2},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"sup",{get:function(){return 1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"sub",{get:function(){return 2},enumerable:!1,configurable:!0}),e.defaults=i({},l.defaults),e}(l);e.MmlMsup=u},60324:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)},s=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMtable=void 0;var l=r(83045),a=r(55089),u=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.properties={useHeight:!0},e.texclass=l.TEXCLASS.ORD,e}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"mtable"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"linebreakContainer",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.setInheritedAttributes=function(e,r,n,o){var i,a;try{for(var u=s(l.indentAttributes),c=u.next();!c.done;c=u.next()){var p=c.value;e[p]&&this.attributes.setInherited(p,e[p][1]),void 0!==this.attributes.getExplicit(p)&&delete this.attributes.getAllAttributes()[p]}}catch(t){i={error:t}}finally{try{c&&!c.done&&(a=u.return)&&a.call(u)}finally{if(i)throw i.error}}t.prototype.setInheritedAttributes.call(this,e,r,n,o)},e.prototype.setChildInheritedAttributes=function(t,e,r,n){var o,i,l,u;try{for(var c=s(this.childNodes),p=c.next();!p.done;p=c.next())(b=p.value).isKind("mtr")||this.replaceChild(this.factory.create("mtr"),b).appendChild(b)}catch(t){o={error:t}}finally{try{p&&!p.done&&(i=c.return)&&i.call(c)}finally{if(o)throw o.error}}r=this.getProperty("scriptlevel")||r,e=!(!this.attributes.getExplicit("displaystyle")&&!this.attributes.getDefault("displaystyle")),t=this.addInheritedAttributes(t,{columnalign:this.attributes.get("columnalign"),rowalign:"center"});var f=this.attributes.getExplicit("data-cramped"),h=(0,a.split)(this.attributes.get("rowalign"));try{for(var d=s(this.childNodes),y=d.next();!y.done;y=d.next()){var b=y.value;t.rowalign[1]=h.shift()||t.rowalign[1],b.setInheritedAttributes(t,e,r,!!f)}}catch(t){l={error:t}}finally{try{y&&!y.done&&(u=d.return)&&u.call(d)}finally{if(l)throw l.error}}},e.prototype.verifyChildren=function(e){for(var r=null,n=this.factory,o=0;o<this.childNodes.length;o++){var i=this.childNodes[o];if(i.isKind("mtr"))r=null;else{var s=i.isKind("mtd");if(r?(this.removeChild(i),o--):r=this.replaceChild(n.create("mtr"),i),r.appendChild(s?i:n.create("mtd",{},[i])),!e.fixMtables){i.parent.removeChild(i),i.parent=this,s&&r.appendChild(n.create("mtd"));var l=i.mError("Children of "+this.kind+" must be mtr or mlabeledtr",e,s);r.childNodes[r.childNodes.length-1].appendChild(l)}}}t.prototype.verifyChildren.call(this,e)},e.prototype.setTeXclass=function(t){var e,r;this.getPrevClass(t);try{for(var n=s(this.childNodes),o=n.next();!o.done;o=n.next())o.value.setTeXclass(null)}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}return this},e.defaults=i(i({},l.AbstractMmlNode.defaults),{align:"axis",rowalign:"baseline",columnalign:"center",groupalign:"{left}",alignmentscope:!0,columnwidth:"auto",width:"auto",rowspacing:"1ex",columnspacing:".8em",rowlines:"none",columnlines:"none",frame:"none",framespacing:"0.4em 0.5ex",equalrows:!1,equalcolumns:!1,displaystyle:!1,side:"right",minlabelspacing:"0.8em"}),e}(l.AbstractMmlNode);e.MmlMtable=u},24955:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMtd=void 0;var s=r(83045),l=r(95518),a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"mtd"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"arity",{get:function(){return-1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"linebreakContainer",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.verifyChildren=function(e){!this.parent||this.parent.isKind("mtr")?t.prototype.verifyChildren.call(this,e):this.mError(this.kind+" can only be a child of an mtr or mlabeledtr",e,!0)},e.prototype.setTeXclass=function(t){return this.getPrevClass(t),this.childNodes[0].setTeXclass(null),this},e.defaults=i(i({},s.AbstractMmlBaseNode.defaults),{rowspan:1,columnspan:1,rowalign:l.INHERIT,columnalign:l.INHERIT,groupalign:l.INHERIT}),e}(s.AbstractMmlBaseNode);e.MmlMtd=a},64957:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMtext=void 0;var s=r(83045),l=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.texclass=s.TEXCLASS.ORD,e}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"mtext"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isSpacelike",{get:function(){return!0},enumerable:!1,configurable:!0}),e.defaults=i({},s.AbstractMmlTokenNode.defaults),e}(s.AbstractMmlTokenNode);e.MmlMtext=l},84760:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)},s=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMlabeledtr=e.MmlMtr=void 0;var l=r(83045),a=r(95518),u=r(55089),c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"mtr"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"linebreakContainer",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.setChildInheritedAttributes=function(t,e,r,n){var o,i,l,a;try{for(var c=s(this.childNodes),p=c.next();!p.done;p=c.next())(y=p.value).isKind("mtd")||this.replaceChild(this.factory.create("mtd"),y).appendChild(y)}catch(t){o={error:t}}finally{try{p&&!p.done&&(i=c.return)&&i.call(c)}finally{if(o)throw o.error}}var f=(0,u.split)(this.attributes.get("columnalign"));1===this.arity&&f.unshift(this.parent.attributes.get("side")),t=this.addInheritedAttributes(t,{rowalign:this.attributes.get("rowalign"),columnalign:"center"});try{for(var h=s(this.childNodes),d=h.next();!d.done;d=h.next()){var y=d.value;t.columnalign[1]=f.shift()||t.columnalign[1],y.setInheritedAttributes(t,e,r,n)}}catch(t){l={error:t}}finally{try{d&&!d.done&&(a=h.return)&&a.call(h)}finally{if(l)throw l.error}}},e.prototype.verifyChildren=function(e){var r,n;if(!this.parent||this.parent.isKind("mtable")){try{for(var o=s(this.childNodes),i=o.next();!i.done;i=o.next()){var l=i.value;l.isKind("mtd")||(this.replaceChild(this.factory.create("mtd"),l).appendChild(l),e.fixMtables||l.mError("Children of "+this.kind+" must be mtd",e))}}catch(t){r={error:t}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}t.prototype.verifyChildren.call(this,e)}else this.mError(this.kind+" can only be a child of an mtable",e,!0)},e.prototype.setTeXclass=function(t){var e,r;this.getPrevClass(t);try{for(var n=s(this.childNodes),o=n.next();!o.done;o=n.next())o.value.setTeXclass(null)}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}return this},e.defaults=i(i({},l.AbstractMmlNode.defaults),{rowalign:a.INHERIT,columnalign:a.INHERIT,groupalign:a.INHERIT}),e}(l.AbstractMmlNode);e.MmlMtr=c;var p=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"mlabeledtr"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"arity",{get:function(){return 1},enumerable:!1,configurable:!0}),e}(c);e.MmlMlabeledtr=p},75579:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMover=e.MmlMunder=e.MmlMunderover=void 0;var s=r(83045),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"munderover"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"arity",{get:function(){return 3},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"base",{get:function(){return 0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"under",{get:function(){return 1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"over",{get:function(){return 2},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"linebreakContainer",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.setChildInheritedAttributes=function(t,e,r,n){var o=this.childNodes;o[0].setInheritedAttributes(t,e,r,n||!!o[this.over]);var i=!(e||!o[0].coreMO().attributes.get("movablelimits")),s=this.constructor.ACCENTS;o[1].setInheritedAttributes(t,!1,this.getScriptlevel(s[1],i,r),n||1===this.under),this.setInheritedAccent(1,s[1],e,r,n,i),o[2]&&(o[2].setInheritedAttributes(t,!1,this.getScriptlevel(s[2],i,r),n||2===this.under),this.setInheritedAccent(2,s[2],e,r,n,i))},e.prototype.getScriptlevel=function(t,e,r){return!e&&this.attributes.get(t)||r++,r},e.prototype.setInheritedAccent=function(t,e,r,n,o,i){var s=this.childNodes[t];if(null==this.attributes.getExplicit(e)&&s.isEmbellished){var l=s.coreMO().attributes.get("accent");this.attributes.setInherited(e,l),l!==this.attributes.getDefault(e)&&s.setInheritedAttributes({},r,this.getScriptlevel(e,i,n),o)}},e.defaults=i(i({},s.AbstractMmlBaseNode.defaults),{accent:!1,accentunder:!1,align:"center"}),e.ACCENTS=["","accentunder","accent"],e}(s.AbstractMmlBaseNode);e.MmlMunderover=l;var a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"munder"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"arity",{get:function(){return 2},enumerable:!1,configurable:!0}),e.defaults=i({},l.defaults),e}(l);e.MmlMunder=a;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"mover"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"arity",{get:function(){return 2},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"over",{get:function(){return 1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"under",{get:function(){return 2},enumerable:!1,configurable:!0}),e.defaults=i({},l.defaults),e.ACCENTS=["","accent","accentunder"],e}(l);e.MmlMover=u},10246:function(t,e,r){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.MmlAnnotation=e.MmlAnnotationXML=e.MmlSemantics=void 0;var s=r(83045),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"semantics"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"arity",{get:function(){return 1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"notParent",{get:function(){return!0},enumerable:!1,configurable:!0}),e.defaults=i(i({},s.AbstractMmlBaseNode.defaults),{definitionUrl:null,encoding:null}),e}(s.AbstractMmlBaseNode);e.MmlSemantics=l;var a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"annotation-xml"},enumerable:!1,configurable:!0}),e.prototype.setChildInheritedAttributes=function(){},e.defaults=i(i({},s.AbstractMmlNode.defaults),{definitionUrl:null,encoding:null,cd:"mathmlkeys",name:"",src:null}),e}(s.AbstractMmlNode);e.MmlAnnotationXML=a;var u=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.properties={isChars:!0},e}return o(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"annotation"},enumerable:!1,configurable:!0}),e.defaults=i({},a.defaults),e}(a);e.MmlAnnotation=u},72901:function(t,e){var r=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)s.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return s},o=this&&this.__spreadArray||function(t,e,r){if(r||2===arguments.length)for(var n,o=0,i=e.length;o<i;o++)!n&&o in e||(n||(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))};Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractFactory=void 0;var i=function(){function t(t){var e,n;void 0===t&&(t=null),this.defaultKind="unknown",this.nodeMap=new Map,this.node={},null===t&&(t=this.constructor.defaultNodes);try{for(var o=r(Object.keys(t)),i=o.next();!i.done;i=o.next()){var s=i.value;this.setNodeClass(s,t[s])}}catch(t){e={error:t}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(e)throw e.error}}}return t.prototype.create=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return(this.node[t]||this.node[this.defaultKind]).apply(void 0,o([],n(e),!1))},t.prototype.setNodeClass=function(t,e){this.nodeMap.set(t,e);var r=this,i=this.nodeMap.get(t);this.node[t]=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return new(i.bind.apply(i,o([void 0,r],n(t),!1)))}},t.prototype.getNodeClass=function(t){return this.nodeMap.get(t)},t.prototype.deleteNodeClass=function(t){this.nodeMap.delete(t),delete this.node[t]},t.prototype.nodeIsKind=function(t,e){return t instanceof this.getNodeClass(e)},t.prototype.getKinds=function(){return Array.from(this.nodeMap.keys())},t.defaultNodes={},t}();e.AbstractFactory=i}}]);
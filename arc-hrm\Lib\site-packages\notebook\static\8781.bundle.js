(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[8781],{67417:()=>{},60880:(e,n,t)=>{"use strict";t.r(n);var o=t(38639);async function a(e,n){try{const t=(await window._JUPYTERLAB[e].get(n))();return t.__scope__=e,t}catch(t){throw console.warn(`Failed to create module: package: ${e}; module: ${n}`),t}}t(72761),t(67417),window.addEventListener("load",(async function(){const e=[t(7831),t(86554),t(13703),t(93103)],n=await Promise.all(e);let r=[t(51999),t(73566),t(58691),t(40573),t(60595),t(2886),t(46782),t(99372).default.filter((({id:e})=>["@jupyterlab/application-extension:commands","@jupyterlab/application-extension:context-menu","@jupyterlab/application-extension:faviconbusy","@jupyterlab/application-extension:router","@jupyterlab/application-extension:top-bar","@jupyterlab/application-extension:top-spacer"].includes(e))),t(48399).default.filter((({id:e})=>["@jupyterlab/apputils-extension:palette","@jupyterlab/apputils-extension:notification","@jupyterlab/apputils-extension:sanitizer","@jupyterlab/apputils-extension:sessionDialogs","@jupyterlab/apputils-extension:settings","@jupyterlab/apputils-extension:state","@jupyterlab/apputils-extension:themes","@jupyterlab/apputils-extension:themes-palette-menu","@jupyterlab/apputils-extension:toolbar-registry","@jupyterlab/apputils-extension:utilityCommands"].includes(e))),t(11040),t(49080).default.filter((({id:e})=>["@jupyterlab/completer-extension:base-service","@jupyterlab/completer-extension:inline-completer","@jupyterlab/completer-extension:inline-completer-factory","@jupyterlab/completer-extension:inline-history","@jupyterlab/completer-extension:manager"].includes(e))),t(26182).default.filter((({id:e})=>["@jupyterlab/console-extension:completer","@jupyterlab/console-extension:factory","@jupyterlab/console-extension:foreign","@jupyterlab/console-extension:tracker"].includes(e))),t(80828),t(96313).default.filter((({id:e})=>["@jupyterlab/docmanager-extension:plugin","@jupyterlab/docmanager-extension:download","@jupyterlab/docmanager-extension:contexts","@jupyterlab/docmanager-extension:manager"].includes(e))),t(97399).default.filter((({id:e})=>["@jupyterlab/documentsearch-extension:plugin"].includes(e))),t(43738).default.filter((({id:e})=>["@jupyterlab/filebrowser-extension:factory","@jupyterlab/filebrowser-extension:default-file-browser"].includes(e))),t(96513).default.filter((({id:e})=>["@jupyterlab/fileeditor-extension:plugin"].includes(e))),t(47080).default.filter((({id:e})=>["@jupyterlab/help-extension:resources"].includes(e))),t(5009),t(85986),t(26718),t(94327),t(77434),t(25056),t(62575),t(44301).default.filter((({id:e})=>["@jupyterlab/notebook-extension:code-console","@jupyterlab/notebook-extension:export","@jupyterlab/notebook-extension:factory","@jupyterlab/notebook-extension:tracker","@jupyterlab/notebook-extension:widget-factory"].includes(e))),t(5964),t(4624),t(43727),t(57788),t(22909),t(18992),t(14340),t(88677)];switch(`/${o.PageConfig.getOption("notebookPage")}`){case"/tree":r=r.concat([t(94953),t(43738).default.filter((({id:e})=>["@jupyterlab/filebrowser-extension:browser","@jupyterlab/filebrowser-extension:download","@jupyterlab/filebrowser-extension:file-upload-status","@jupyterlab/filebrowser-extension:open-with","@jupyterlab/filebrowser-extension:search","@jupyterlab/filebrowser-extension:share-file"].includes(e))),t(36597),t(69205),t(13587)]);break;case"/notebooks":r=r.concat([t(7141),t(41642),t(84327).default.filter((({id:e})=>["@jupyterlab/debugger-extension:config","@jupyterlab/debugger-extension:main","@jupyterlab/debugger-extension:notebooks","@jupyterlab/debugger-extension:service","@jupyterlab/debugger-extension:sidebar","@jupyterlab/debugger-extension:sources"].includes(e))),t(63564),t(44301).default.filter((({id:e})=>["@jupyterlab/notebook-extension:active-cell-tool","@jupyterlab/notebook-extension:completer","@jupyterlab/notebook-extension:metadata-editor","@jupyterlab/notebook-extension:search","@jupyterlab/notebook-extension:toc","@jupyterlab/notebook-extension:tools","@jupyterlab/notebook-extension:update-raw-mimetype"].includes(e))),t(91958).default.filter((({id:e})=>["@jupyterlab/toc-extension:registry","@jupyterlab/toc-extension:tracker"].includes(e))),t(42091).default.filter((({id:e})=>["@jupyterlab/tooltip-extension:manager","@jupyterlab/tooltip-extension:notebooks"].includes(e)))]);break;case"/consoles":r=r.concat([t(42091).default.filter((({id:e})=>["@jupyterlab/tooltip-extension:manager","@jupyterlab/tooltip-extension:consoles"].includes(e)))]);break;case"/edit":r=r.concat([t(96513).default.filter((({id:e})=>["@jupyterlab/fileeditor-extension:completer","@jupyterlab/fileeditor-extension:search"].includes(e))),t(83192)])}const i=[],l=[];function*s(e){let n;n=Object.prototype.hasOwnProperty.call(e,"__esModule")?e.default:e;let t=Array.isArray(n)?n:[n];for(let n of t){const t=o.PageConfig.Extension.isDisabled(n.id);l.push({id:n.id,description:n.description,requires:n.requires??[],optional:n.optional??[],provides:n.provides??null,autoStart:n.autoStart,enabled:!t,extension:e.__scope__}),t?i.push(n.id):yield n}}const p=JSON.parse(o.PageConfig.getOption("federated_extensions")),d=[],b=[],u=[],c=[];(await Promise.allSettled(p.map((async e=>(await async function(e,n){await function(e){return new Promise(((n,t)=>{const o=document.createElement("script");o.onerror=t,o.onload=n,o.async=!0,document.head.appendChild(o),o.src=e}))}(e),await t.I("default");const o=window._JUPYTERLAB[n];await o.init(t.S.default)}(`${o.URLExt.join(o.PageConfig.getOption("fullLabextensionsUrl"),e.name,e.load)}`,e.name),e))))).forEach((e=>{if("rejected"===e.status)return void console.error(e.reason);const n=e.value;n.extension&&b.push(a(n.name,n.extension)),n.mimeExtension&&u.push(a(n.name,n.mimeExtension)),n.style&&!o.PageConfig.Extension.isDisabled(n.name)&&c.push(a(n.name,n.style))})),(await Promise.all(r)).forEach((e=>{for(let n of s(e))d.push(n)})),(await Promise.allSettled(b)).forEach((e=>{if("fulfilled"===e.status)for(let n of s(e.value))d.push(n);else console.error(e.reason)})),(await Promise.allSettled(u)).forEach((e=>{if("fulfilled"===e.status)for(let t of s(e.value))n.push(t);else console.error(e.reason)})),(await Promise.allSettled(c)).filter((({status:e})=>"rejected"===e)).forEach((({reason:e})=>{console.error(e)})),o.PageConfig.setOption("allPlugins",'{"/":{"@jupyter-notebook/application-extension":true,"@jupyter-notebook/console-extension":true,"@jupyter-notebook/docmanager-extension":true,"@jupyter-notebook/documentsearch-extension":true,"@jupyter-notebook/help-extension":true,"@jupyter-notebook/notebook-extension":true,"@jupyter-notebook/terminal-extension":true,"@jupyterlab/application-extension":["@jupyterlab/application-extension:commands","@jupyterlab/application-extension:context-menu","@jupyterlab/application-extension:faviconbusy","@jupyterlab/application-extension:router","@jupyterlab/application-extension:top-bar","@jupyterlab/application-extension:top-spacer"],"@jupyterlab/apputils-extension":["@jupyterlab/apputils-extension:palette","@jupyterlab/apputils-extension:notification","@jupyterlab/apputils-extension:sanitizer","@jupyterlab/apputils-extension:sessionDialogs","@jupyterlab/apputils-extension:settings","@jupyterlab/apputils-extension:state","@jupyterlab/apputils-extension:themes","@jupyterlab/apputils-extension:themes-palette-menu","@jupyterlab/apputils-extension:toolbar-registry","@jupyterlab/apputils-extension:utilityCommands"],"@jupyterlab/codemirror-extension":true,"@jupyterlab/completer-extension":["@jupyterlab/completer-extension:base-service","@jupyterlab/completer-extension:inline-completer","@jupyterlab/completer-extension:inline-completer-factory","@jupyterlab/completer-extension:inline-history","@jupyterlab/completer-extension:manager"],"@jupyterlab/console-extension":["@jupyterlab/console-extension:completer","@jupyterlab/console-extension:factory","@jupyterlab/console-extension:foreign","@jupyterlab/console-extension:tracker"],"@jupyterlab/csvviewer-extension":true,"@jupyterlab/docmanager-extension":["@jupyterlab/docmanager-extension:plugin","@jupyterlab/docmanager-extension:download","@jupyterlab/docmanager-extension:contexts","@jupyterlab/docmanager-extension:manager"],"@jupyterlab/documentsearch-extension":["@jupyterlab/documentsearch-extension:plugin"],"@jupyterlab/filebrowser-extension":["@jupyterlab/filebrowser-extension:factory","@jupyterlab/filebrowser-extension:default-file-browser"],"@jupyterlab/fileeditor-extension":["@jupyterlab/fileeditor-extension:plugin"],"@jupyterlab/help-extension":["@jupyterlab/help-extension:resources"],"@jupyterlab/htmlviewer-extension":true,"@jupyterlab/imageviewer-extension":true,"@jupyterlab/lsp-extension":true,"@jupyterlab/mainmenu-extension":true,"@jupyterlab/markedparser-extension":true,"@jupyterlab/mathjax-extension":true,"@jupyterlab/mermaid-extension":true,"@jupyterlab/notebook-extension":["@jupyterlab/notebook-extension:code-console","@jupyterlab/notebook-extension:export","@jupyterlab/notebook-extension:factory","@jupyterlab/notebook-extension:tracker","@jupyterlab/notebook-extension:widget-factory"],"@jupyterlab/pluginmanager-extension":true,"@jupyterlab/shortcuts-extension":true,"@jupyterlab/terminal-extension":true,"@jupyterlab/theme-light-extension":true,"@jupyterlab/theme-dark-extension":true,"@jupyterlab/translation-extension":true,"@jupyterlab/ui-components-extension":true,"@jupyterlab/hub-extension":true},"/tree":{"@jupyterlab/extensionmanager-extension":true,"@jupyterlab/filebrowser-extension":["@jupyterlab/filebrowser-extension:browser","@jupyterlab/filebrowser-extension:download","@jupyterlab/filebrowser-extension:file-upload-status","@jupyterlab/filebrowser-extension:open-with","@jupyterlab/filebrowser-extension:search","@jupyterlab/filebrowser-extension:share-file"],"@jupyter-notebook/tree-extension":true,"@jupyterlab/running-extension":true,"@jupyterlab/settingeditor-extension":true},"/notebooks":{"@jupyterlab/celltags-extension":true,"@jupyterlab/cell-toolbar-extension":true,"@jupyterlab/debugger-extension":["@jupyterlab/debugger-extension:config","@jupyterlab/debugger-extension:main","@jupyterlab/debugger-extension:notebooks","@jupyterlab/debugger-extension:service","@jupyterlab/debugger-extension:sidebar","@jupyterlab/debugger-extension:sources"],"@jupyterlab/metadataform-extension":true,"@jupyterlab/notebook-extension":["@jupyterlab/notebook-extension:active-cell-tool","@jupyterlab/notebook-extension:completer","@jupyterlab/notebook-extension:metadata-editor","@jupyterlab/notebook-extension:search","@jupyterlab/notebook-extension:toc","@jupyterlab/notebook-extension:tools","@jupyterlab/notebook-extension:update-raw-mimetype"],"@jupyterlab/toc-extension":["@jupyterlab/toc-extension:registry","@jupyterlab/toc-extension:tracker"],"@jupyterlab/tooltip-extension":["@jupyterlab/tooltip-extension:manager","@jupyterlab/tooltip-extension:notebooks"]},"/consoles":{"@jupyterlab/tooltip-extension":["@jupyterlab/tooltip-extension:manager","@jupyterlab/tooltip-extension:consoles"]},"/edit":{"@jupyterlab/fileeditor-extension":["@jupyterlab/fileeditor-extension:completer","@jupyterlab/fileeditor-extension:search"],"@jupyterlab/markdownviewer-extension":true}}');const j=new(0,t(42266).NotebookApp)({mimeExtensions:n,availablePlugins:l});j.registerPluginModules(d),"true"===(o.PageConfig.getOption("exposeAppInBrowser")||"").toLowerCase()&&(window.jupyterapp=j),await j.start()}))},72761:(e,n,t)=>{"use strict";t.r(n),t(71818),t(70022),t(26238);var o=t(94830),a=t.n(o),r=t(80592),i=t.n(r),l=t(99763),s=t.n(l),p=t(28915),d=t.n(p),b=t(80366),u=t.n(b),c=t(17352),j=t.n(c),m=t(26633),y={};y.styleTagTransform=j(),y.setAttributes=d(),y.insert=s().bind(null,"head"),y.domAPI=i(),y.insertStyleElement=u(),a()(m.Z,y),m.Z&&m.Z.locals&&m.Z.locals;var x=t(93862),g={};g.styleTagTransform=j(),g.setAttributes=d(),g.insert=s().bind(null,"head"),g.domAPI=i(),g.insertStyleElement=u(),a()(x.Z,g),x.Z&&x.Z.locals&&x.Z.locals,t(20959);var h=t(58027),f={};f.styleTagTransform=j(),f.setAttributes=d(),f.insert=s().bind(null,"head"),f.domAPI=i(),f.insertStyleElement=u(),a()(h.Z,f),h.Z&&h.Z.locals&&h.Z.locals;var A=t(99776),k={};k.styleTagTransform=j(),k.setAttributes=d(),k.insert=s().bind(null,"head"),k.domAPI=i(),k.insertStyleElement=u(),a()(A.Z,k),A.Z&&A.Z.locals&&A.Z.locals;var w=t(64399),v={};v.styleTagTransform=j(),v.setAttributes=d(),v.insert=s().bind(null,"head"),v.domAPI=i(),v.insertStyleElement=u(),a()(w.Z,v),w.Z&&w.Z.locals&&w.Z.locals;var T=t(11112),P={};P.styleTagTransform=j(),P.setAttributes=d(),P.insert=s().bind(null,"head"),P.domAPI=i(),P.insertStyleElement=u(),a()(T.Z,P),T.Z&&T.Z.locals&&T.Z.locals;var Z=t(49482),B={};B.styleTagTransform=j(),B.setAttributes=d(),B.insert=s().bind(null,"head"),B.domAPI=i(),B.insertStyleElement=u(),a()(Z.Z,B),Z.Z&&Z.Z.locals&&Z.Z.locals;var S=t(71564),C={};C.styleTagTransform=j(),C.setAttributes=d(),C.insert=s().bind(null,"head"),C.domAPI=i(),C.insertStyleElement=u(),a()(S.Z,C),S.Z&&S.Z.locals&&S.Z.locals;var N=t(8173),E={};E.styleTagTransform=j(),E.setAttributes=d(),E.insert=s().bind(null,"head"),E.domAPI=i(),E.insertStyleElement=u(),a()(N.Z,E),N.Z&&N.Z.locals&&N.Z.locals,t(40360);var D=t(30546),z={};z.styleTagTransform=j(),z.setAttributes=d(),z.insert=s().bind(null,"head"),z.domAPI=i(),z.insertStyleElement=u(),a()(D.Z,z),D.Z&&D.Z.locals&&D.Z.locals;var F=t(94623),L={};L.styleTagTransform=j(),L.setAttributes=d(),L.insert=s().bind(null,"head"),L.domAPI=i(),L.insertStyleElement=u(),a()(F.Z,L),F.Z&&F.Z.locals&&F.Z.locals;var M=t(99402),O={};O.styleTagTransform=j(),O.setAttributes=d(),O.insert=s().bind(null,"head"),O.domAPI=i(),O.insertStyleElement=u(),a()(M.Z,O),M.Z&&M.Z.locals&&M.Z.locals,t(32723),t(82649),t(15491),t(92645),t(18934),t(24017),t(72867),t(93751),t(75617),t(91532),t(20603),t(17066),t(39380),t(9755),t(82164),t(94938),t(53555),t(86258),t(11575),t(10887),t(26449),t(3727),t(97635),t(16856),t(46165),t(8604),t(26053),t(84221),t(53927),t(67074),t(47275),t(58068),t(41346),t(15209),t(65315),t(87635),t(37609),t(49733),t(40745)},58027:(e,n,t)=>{"use strict";t.d(n,{Z:()=>l});var o=t(66846),a=t.n(o),r=t(11368),i=t.n(r)()(a());i.push([e.id,"/*-----------------------------------------------------------------------------\n| Copyright (c) Jupyter Development Team.\n|\n| Distributed under the terms of the Modified BSD License.\n|----------------------------------------------------------------------------*/\n\n.jp-NotebookSpacer {\n  flex-grow: 1;\n  flex-shrink: 1;\n}\n\n.jp-MainAreaWidget {\n  height: 100%;\n}\n\n.jp-Toolbar > .jp-Toolbar-item {\n  height: unset;\n}\n\n#jp-UserMenu {\n  flex: 0 0 auto;\n  display: flex;\n  text-align: center;\n  margin-top: 8px;\n}\n\n.jp-MimeDocument .jp-RenderedJSON {\n  background: var(--jp-layout-color0);\n}\n\n/* Hide the stub toolbar that appears above terminals and documents */\n\n.jp-MainAreaWidget > .jp-Toolbar-micro {\n  display: none;\n}\n\n#jp-NotebookLogo {\n  /* bring logo to the front so it is selectable by tab*/\n  z-index: 10;\n}\n\n/* Hide the notification status item */\n.jp-Notification-Status {\n  display: none;\n}\n",""]);const l=i},26633:(e,n,t)=>{"use strict";t.d(n,{Z:()=>l});var o=t(66846),a=t.n(o),r=t(11368),i=t.n(r)()(a());i.push([e.id,"/*-----------------------------------------------------------------------------\n| Copyright (c) Jupyter Development Team.\n| Distributed under the terms of the Modified BSD License.\n|----------------------------------------------------------------------------*/\n\n:root {\n  --jp-private-topbar-height: 28px;\n  /* Override the layout-2 color for the dark theme */\n  --md-grey-800: #323232;\n  --jp-notebook-max-width: 1200px;\n}\n\nbody {\n  margin: 0;\n  padding: 0;\n  background: var(--jp-layout-color2);\n}\n\n#main {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n}\n\n#top-panel-wrapper {\n  min-height: calc(1.5 * var(--jp-private-topbar-height));\n  border-bottom: var(--jp-border-width) solid var(--jp-border-color0);\n  background: var(--jp-layout-color1);\n}\n\n#top-panel {\n  display: flex;\n  min-height: calc(1.5 * var(--jp-private-topbar-height));\n  padding-left: 5px;\n  padding-right: 5px;\n  margin-left: auto;\n  margin-right: auto;\n  max-width: 1200px;\n}\n\n#menu-panel-wrapper {\n  min-height: var(--jp-private-topbar-height);\n  background: var(--jp-layout-color1);\n  border-bottom: var(--jp-border-width) solid var(--jp-border-color0);\n  box-shadow: var(--jp-elevation-z1);\n}\n\n#menu-panel {\n  display: flex;\n  min-height: var(--jp-private-topbar-height);\n  background: var(--jp-layout-color1);\n  padding-left: 5px;\n  padding-right: 5px;\n  margin-left: auto;\n  margin-right: auto;\n  max-width: var(--jp-notebook-max-width);\n}\n\n#main-panel {\n  margin-left: auto;\n  margin-right: auto;\n  max-width: var(--jp-notebook-max-width);\n}\n\n#spacer-widget-top {\n  min-height: 16px;\n}\n\n/* Only edit pages should have a bottom space */\n\nbody[data-notebook='edit'] #spacer-widget-bottom {\n  min-height: 16px;\n}\n\n/* Special case notebooks as document oriented pages */\n\n[data-notebook]:not(body[data-notebook='notebooks']) #main-panel {\n  box-shadow: var(--jp-elevation-z4);\n}\n\n.jp-TreePanel > .lm-TabPanel-stackedPanel {\n  box-shadow: var(--jp-elevation-z4);\n}\n\nbody[data-notebook='notebooks'] #main-panel {\n  margin-left: unset;\n  margin-right: unset;\n  max-width: unset;\n}\n\nbody[data-notebook='notebooks'] #spacer-widget-top {\n  min-height: unset;\n}\n\n#main-panel > .jp-TreePanel {\n  padding: 0px 5px;\n}\n\n@media only screen and (max-width: 760px) {\n  #main-panel > .jp-TreePanel {\n    margin: 0px -5px;\n  }\n}\n",""]);const l=i},93862:(e,n,t)=>{"use strict";t.d(n,{Z:()=>l});var o=t(66846),a=t.n(o),r=t(11368),i=t.n(r)()(a());i.push([e.id,"/*-----------------------------------------------------------------------------\n| Copyright (c) Jupyter Development Team.\n| Distributed under the terms of the Modified BSD License.\n|\n| Adapted from JupyterLab's packages/application/style/sidepanel.css.\n|----------------------------------------------------------------------------*/\n\n/*-----------------------------------------------------------------------------\n| Variables\n|----------------------------------------------------------------------------*/\n\n:root {\n  --jp-private-sidebar-tab-width: 32px;\n}\n\n/*-----------------------------------------------------------------------------\n| SideBar\n|----------------------------------------------------------------------------*/\n\n/* Stack panels */\n\n#jp-right-stack,\n#jp-left-stack {\n  display: flex;\n  flex-direction: column;\n  min-width: var(--jp-sidebar-min-width);\n}\n\n#jp-left-stack .jp-SidePanel-collapse,\n#jp-right-stack .jp-SidePanel-collapse {\n  display: flex;\n  flex: 0 0 auto;\n  min-height: 0;\n  padding: 0;\n}\n\n#jp-left-stack .jp-SidePanel-collapse {\n  justify-content: right;\n}\n\n#jp-right-stack .jp-SidePanel-collapse {\n  justify-content: left;\n}\n\n#jp-left-stack .lm-StackedPanel,\n#jp-right-stack .lm-StackedPanel {\n  flex: 1 1 auto;\n}\n",""]);const l=i},99776:(e,n,t)=>{"use strict";t.d(n,{Z:()=>l});var o=t(66846),a=t.n(o),r=t(11368),i=t.n(r)()(a());i.push([e.id,"",""]);const l=i},64399:(e,n,t)=>{"use strict";t.d(n,{Z:()=>l});var o=t(66846),a=t.n(o),r=t(11368),i=t.n(r)()(a());i.push([e.id,".jp-Document {\n  height: 100%;\n}\n",""]);const l=i},11112:(e,n,t)=>{"use strict";t.d(n,{Z:()=>l});var o=t(66846),a=t.n(o),r=t(11368),i=t.n(r)()(a());i.push([e.id,"",""]);const l=i},49482:(e,n,t)=>{"use strict";t.d(n,{Z:()=>l});var o=t(66846),a=t.n(o),r=t(11368),i=t.n(r)()(a());i.push([e.id,".jp-AboutNotebook .jp-Dialog-header {\n  justify-content: center;\n  padding: 0;\n}\n\n.jp-AboutNotebook-header {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  padding: var(--jp-flat-button-padding);\n}\n\n.jp-AboutNotebook-header-text {\n  margin-left: 16px;\n}\n\n.jp-AboutNotebook-version {\n  color: var(--jp-ui-font-color1);\n  font-size: var(--jp-ui-font-size1);\n  padding-bottom: 30px;\n  font-weight: 400;\n  letter-spacing: 0.4px;\n  line-height: 1.12;\n  min-width: 360px;\n  text-align: center;\n}\n\n.jp-AboutNotebook-body {\n  display: flex;\n  font-size: var(--jp-ui-font-size2);\n  padding: var(--jp-flat-button-padding);\n  color: var(--jp-ui-font-color1);\n  text-align: center;\n  flex-direction: column;\n  min-width: 360px;\n  overflow: hidden;\n}\n\n.jp-AboutNotebook-about-body pre {\n  white-space: pre-wrap;\n}\n\n.jp-AboutNotebook-about-externalLinks {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  align-items: flex-start;\n  color: var(--jp-warn-color0);\n}\n\n.jp-AboutNotebook-about-copyright {\n  padding-top: 25px;\n}\n",""]);const l=i},71564:(e,n,t)=>{"use strict";t.d(n,{Z:()=>p});var o=t(66846),a=t.n(o),r=t(11368),i=t.n(r),l=t(99686),s=i()(a());s.i(l.Z),s.push([e.id,"/*-----------------------------------------------------------------------------\n| Copyright (c) Jupyter Development Team.\n|\n| Distributed under the terms of the Modified BSD License.\n|----------------------------------------------------------------------------*/\n\n/**\n  Document oriented look for the notebook.\n  This includes changes to the look and feel of the JupyterLab Notebook\n  component like:\n  - scrollbar to the right of the page\n  - drop shadow on the notebook\n  - smaller empty space at the bottom of the notebook\n  - compact view on mobile\n*/\n\n/* Keep the notebook centered on the page */\n\nbody[data-notebook='notebooks'] .jp-NotebookPanel-toolbar {\n  padding-left: calc(calc(100% - var(--jp-notebook-max-width)) * 0.5);\n  padding-right: calc(calc(100% - var(--jp-notebook-max-width)) * 0.5);\n}\n\nbody[data-notebook='notebooks'] .jp-WindowedPanel-outer {\n  padding-top: unset;\n  padding-left: calc(calc(100% - var(--jp-notebook-max-width)) * 0.5);\n  padding-right: calc(\n    calc(\n        100% - var(--jp-notebook-max-width) - var(--jp-notebook-padding-offset)\n      ) * 0.5\n  );\n  background: var(--jp-layout-color2);\n}\n\nbody[data-notebook='notebooks'] .jp-WindowedPanel-inner {\n  margin-top: var(--jp-notebook-toolbar-margin-bottom);\n}\n\nbody[data-notebook='notebooks'] .jp-Notebook-cell {\n  background: var(--jp-layout-color0);\n  padding-left: calc(2 * var(--jp-cell-padding));\n  padding-right: calc(2 * var(--jp-cell-padding));\n}\n\n/* Empty space at the bottom of the notebook (similar to classic) */\nbody[data-notebook='notebooks'] .jp-Notebook.jp-mod-scrollPastEnd::after {\n  min-height: 100px;\n}\n\n/* Fix background colors */\n\nbody[data-notebook='notebooks'] .jp-WindowedPanel-outer > * {\n  background: var(--jp-layout-color0);\n}\n\nbody[data-notebook='notebooks']\n  .jp-Notebook.jp-mod-commandMode\n  .jp-Cell.jp-mod-active.jp-mod-selected:not(.jp-mod-multiSelected) {\n  background: var(--jp-layout-color0) !important;\n}\n\n/**\n  Extra padding to the first and and last cell of the notebook.\n  TODO: revisit when https://github.com/jupyterlab/jupyterlab/issues/13151 is fixed\n*/\n.jp-Notebook-cell[data-windowed-list-index='0'] {\n  padding-top: calc(2 * var(--jp-notebook-padding));\n}\n\nbody[data-notebook='notebooks'] .jp-WindowedPanel-viewport > *:last-child {\n  padding-bottom: calc(2 * var(--jp-notebook-padding));\n}\n\nbody[data-notebook='notebooks']\n  .jp-Notebook\n  .jp-Notebook-cell:not(:first-child)::before {\n  content: ' ';\n  height: 100%;\n  position: absolute;\n  top: 0;\n  width: 11px;\n}\n\n/* Cell toolbar adjustments */\n\nbody[data-notebook='notebooks'] .jp-cell-toolbar {\n  background: unset;\n  box-shadow: unset;\n}\n\nbody[data-notebook='notebooks']\n  .jp-RawCell[data-windowed-list-index='0']\n  .jp-cell-toolbar,\nbody[data-notebook='notebooks']\n  .jp-MarkdownCell[data-windowed-list-index='0']\n  .jp-cell-toolbar {\n  top: calc(2 * var(--jp-notebook-padding));\n}\n\n/** first code cell on mobile\n    (keep the selector above the media query)\n*/\nbody[data-notebook='notebooks']\n  .jp-CodeCell[data-windowed-list-index='0']\n  .jp-cell-toolbar {\n  top: unset;\n}\n\n@media only screen and (max-width: 760px) {\n  /* first code cell on mobile */\n  body[data-notebook='notebooks']\n    .jp-CodeCell[data-windowed-list-index='0']\n    .jp-cell-toolbar {\n    top: var(--jp-notebook-padding);\n  }\n\n  body[data-notebook='notebooks'] .jp-MarkdownCell .jp-cell-toolbar,\n  body[data-notebook='notebooks'] .jp-RawCell .jp-cell-toolbar {\n    top: calc(0.5 * var(--jp-notebook-padding));\n  }\n}\n\n/* Tweak the notebook footer (to add a new cell) */\nbody[data-notebook='notebooks'] .jp-Notebook-footer {\n  width: 100%;\n  margin-left: unset;\n  background: unset;\n}\n\n/* Mobile View */\n\nbody[data-format='mobile'] .jp-NotebookCheckpoint {\n  display: none;\n}\n\nbody[data-format='mobile'] .jp-WindowedPanel-outer > *:first-child {\n  margin-top: 0;\n}\n\nbody[data-format='mobile'] .jp-ToolbarButton .jp-DebuggerBugButton {\n  display: none;\n}\n\n/* Virtual Notebook fixes */\n\nbody[data-notebook='notebooks'] .jp-WindowedPanel-viewport {\n  background: var(--jp-layout-color0);\n  padding: unset;\n}\n\n/* Notebook box shadow */\n\nbody[data-notebook='notebooks']\n  .jp-WindowedPanel-outer\n  > *:first-child:not(:last-child) {\n  box-shadow: var(--jp-elevation-z4);\n}\n\nbody[data-notebook='notebooks']\n  .jp-Notebook\n  > *:first-child:last-child::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  box-shadow: 0px 0px 12px 1px var(--jp-shadow-umbra-color);\n}\n\nbody[data-notebook='notebooks']\n  .jp-Notebook\n  .jp-Notebook-cell:not(:first-child)::after,\nbody[data-notebook='notebooks']\n  .jp-Notebook\n  .jp-Notebook-cell:not(:first-child)::before {\n  content: ' ';\n  height: 100%;\n  position: absolute;\n  top: 0;\n  width: 11px;\n}\n\n/* Additional customizations of the components on the notebook page */\n\n.jp-NotebookKernelLogo {\n  flex: 0 0 auto;\n  display: flex;\n  align-items: center;\n  text-align: center;\n  margin-right: 8px;\n}\n\n.jp-NotebookKernelLogo img {\n  max-width: 28px;\n  max-height: 28px;\n  display: flex;\n}\n\n.jp-NotebookKernelStatus {\n  margin: 0;\n  font-weight: normal;\n  font-size: var(--jp-ui-font-size1);\n  color: var(--jp-ui-font-color0);\n  font-family: var(--jp-ui-font-family);\n  line-height: var(--jp-private-title-panel-height);\n  padding-left: var(--jp-kernel-status-padding);\n  padding-right: var(--jp-kernel-status-padding);\n}\n\n.jp-NotebookKernelStatus-error {\n  background-color: var(--jp-error-color0);\n}\n\n.jp-NotebookKernelStatus-warn {\n  background-color: var(--jp-warn-color0);\n}\n\n.jp-NotebookKernelStatus-info {\n  background-color: var(--jp-info-color0);\n}\n\n.jp-NotebookKernelStatus-fade {\n  animation: 0.5s fade-out forwards;\n}\n\n.jp-NotebookTrustedStatus {\n  background: var(--jp-layout-color1);\n  color: var(--jp-ui-font-color1);\n  margin-top: 4px;\n  margin-bottom: 4px;\n  border: solid 1px var(--jp-border-color2);\n  cursor: help;\n}\n\n.jp-NotebookTrustedStatus-not-trusted {\n  cursor: pointer;\n}\n\n@keyframes fade-out {\n  0% {\n    opacity: 1;\n  }\n  100% {\n    opacity: 0;\n  }\n}\n\n#jp-title h1 {\n  cursor: pointer;\n  font-size: 18px;\n  margin: 0;\n  font-weight: normal;\n  color: var(--jp-ui-font-color0);\n  font-family: var(--jp-ui-font-family);\n  line-height: calc(1.5 * var(--jp-private-title-panel-height));\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n}\n\n#jp-title h1:hover {\n  background: var(--jp-layout-color2);\n}\n\n.jp-NotebookCheckpoint {\n  font-size: 14px;\n  margin-left: 5px;\n  margin-right: 5px;\n  font-weight: normal;\n  color: var(--jp-ui-font-color0);\n  font-family: var(--jp-ui-font-family);\n  line-height: calc(1.5 * var(--jp-private-title-panel-height));\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n}\n\n.jp-skiplink {\n  position: absolute;\n  top: -100em;\n}\n\n.jp-skiplink:focus-within {\n  position: absolute;\n  z-index: 10000;\n  top: 0;\n  left: 46%;\n  margin: 0 auto;\n  padding: 1em;\n  width: 15%;\n  box-shadow: var(--jp-elevation-z4);\n  border-radius: 4px;\n  background: var(--jp-layout-color0);\n  text-align: center;\n}\n\n.jp-skiplink:focus-within a {\n  text-decoration: underline;\n  color: var(--jp-content-link-color);\n}\n",""]);const p=s},99686:(e,n,t)=>{"use strict";t.d(n,{Z:()=>l});var o=t(66846),a=t.n(o),r=t(11368),i=t.n(r)()(a());i.push([e.id,":root {\n  --jp-notebook-toolbar-margin-bottom: 20px;\n  --jp-notebook-padding-offset: 20px;\n\n  --jp-kernel-status-padding: 5px;\n}\n",""]);const l=i},8173:(e,n,t)=>{"use strict";t.d(n,{Z:()=>l});var o=t(66846),a=t.n(o),r=t(11368),i=t.n(r)()(a());i.push([e.id,"",""]);const l=i},94623:(e,n,t)=>{"use strict";t.d(n,{Z:()=>l});var o=t(66846),a=t.n(o),r=t(11368),i=t.n(r)()(a());i.push([e.id,"/*-----------------------------------------------------------------------------\n| Copyright (c) Jupyter Development Team.\n|\n| Distributed under the terms of the Modified BSD License.\n|----------------------------------------------------------------------------*/\n\n.jp-FileBrowser-toolbar .jp-Toolbar-item.jp-DropdownMenu,\n.jp-FileBrowser-toolbar .jp-Toolbar-item.jp-ToolbarButton,\n.jp-FileBrowser-toolbar .jp-Toolbar-item.jp-CommandToolbarButton {\n  border: solid 1px var(--jp-border-color2);\n  margin: 1px;\n  padding: 0px;\n}\n\n.jp-FileBrowser-toolbar > .jp-Toolbar-item.jp-ToolbarButton:hover,\n.jp-FileBrowser-toolbar > .jp-Toolbar-item.jp-CommandToolbarButton:hover,\n.jp-FileBrowser-toolbar > .jp-Toolbar-item.jp-DropdownMenu:hover {\n  background: var(--neutral-fill-stealth-hover);\n}\n\n.jp-FileBrowser-toolbar .lm-MenuBar-item {\n  height: var(--jp-private-toolbar-height);\n  display: inline-flex;\n  align-items: center;\n}\n\n.jp-FileBrowser-toolbar .jp-ToolbarButtonComponent {\n  height: var(--jp-flat-button-height);\n}\n\n.jp-FileBrowser-toolbar jp-button.jp-ToolbarButtonComponent:hover {\n  background: inherit;\n}\n\n.jp-FileBrowser-filterBox {\n  padding: 0;\n  flex: 0 0 auto;\n}\n\n.jp-FileBrowser-filterBox input {\n  line-height: 24px;\n}\n\n.jp-DirListing-content .jp-DirListing-checkboxWrapper {\n  visibility: visible;\n}\n\n/* Action buttons */\n\n.jp-FileBrowser-toolbar > .jp-FileAction > .jp-ToolbarButtonComponent > svg {\n  display: none;\n}\n\n.jp-FileBrowser-toolbar > #fileAction-delete {\n  background-color: var(--jp-error-color1);\n}\n\n.jp-FileBrowser-toolbar\n  .jp-ToolbarButtonComponent[data-command='filebrowser:delete']\n  .jp-ToolbarButtonComponent-label {\n  color: var(--jp-ui-inverse-font-color1);\n}\n\n.jp-FileBrowser-toolbar .jp-FileAction {\n  border: solid 1px var(--jp-border-color2);\n  margin: 1px;\n  min-height: var(--jp-private-toolbar-height);\n}\n",""]);const l=i},30546:(e,n,t)=>{"use strict";t.d(n,{Z:()=>l});var o=t(66846),a=t.n(o),r=t(11368),i=t.n(r)()(a());i.push([e.id,".jp-FileBrowser {\n  height: 100%;\n}\n\n.lm-TabPanel {\n  height: 100%;\n}\n\n.jp-TreePanel .lm-TabPanel-tabBar {\n  overflow: visible;\n  min-height: 32px;\n  border-bottom: unset;\n  height: var(--jp-private-toolbar-height);\n}\n\n.jp-TreePanel .lm-TabBar-content {\n  height: 100%;\n}\n\n.jp-TreePanel .lm-TabBar-tab {\n  flex: 0 1 auto;\n  color: var(--jp-ui-font-color0);\n  font-size: var(--jp-ui-font-size1);\n  height: 100%;\n}\n\n.jp-TreePanel .lm-TabBar-tabLabel {\n  padding-left: 5px;\n  padding-right: 5px;\n}\n\n.jp-FileBrowser-toolbar.jp-Toolbar .jp-ToolbarButtonComponent {\n  width: unset;\n}\n\n.jp-FileBrowser-toolbar > .jp-Toolbar-item {\n  flex-direction: column;\n  justify-content: center;\n}\n\n.jp-DropdownMenu .lm-MenuBar-itemIcon svg {\n  vertical-align: sub;\n}\n\njp-button[data-command='filebrowser:refresh'] .jp-ToolbarButtonComponent-label {\n  display: none;\n}\n\n.jp-TreePanel .lm-TabBar-tabIcon svg {\n  vertical-align: sub;\n}\n",""]);const l=i},99402:(e,n,t)=>{"use strict";t.d(n,{Z:()=>l});var o=t(66846),a=t.n(o),r=t(11368),i=t.n(r)()(a());i.push([e.id,"",""]);const l=i},7413:e=>{"use strict";e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAYAAAB4ka1VAAAAsElEQVQIHQGlAFr/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA7+r3zKmT0/+pk9P/7+r3zAAAAAAAAAAABAAAAAAAAAAA6OPzM+/q9wAAAAAA6OPzMwAAAAAAAAAAAgAAAAAAAAAAGR8NiRQaCgAZIA0AGR8NiQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQyoYJ/SY80UAAAAASUVORK5CYII="}}]);
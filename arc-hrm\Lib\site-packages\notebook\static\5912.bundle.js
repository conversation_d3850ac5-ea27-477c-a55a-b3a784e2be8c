"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[5912],{15912:(e,t,n)=>{n.r(t),n.d(t,{default:()=>p});var a,r=n(3053),i=n(12982),o=n(389),s=n(97104),c=n(13589),l=n(16954),m=n(36768),d=n(33673),h=n(71677),u=n(68239),_=n(31516);!function(e){e.copy="terminal:copy",e.createNew="terminal:create-new",e.open="terminal:open",e.refresh="terminal:refresh",e.increaseFont="terminal:increase-font",e.decreaseFont="terminal:decrease-font",e.paste="terminal:paste",e.setTheme="terminal:set-theme",e.shutdown="terminal:shut-down"}(a||(a={}));const g={activate:function(e,t,n,r,o,s,c,m,h){const p=n.load("jupyterlab"),{serviceManager:y,commands:f}=e,T=p.__("Terminal"),b=new i.WidgetTracker({namespace:"terminal"});if(!y.terminals.isAvailable())return console.warn("Disabling terminals plugin because they are not available on the server"),b;s&&s.restore(b,{command:a.createNew,args:e=>({name:e.content.session.name}),name:e=>e.content.session.name});const v={};function I(e){Object.keys(e.composite).forEach((t=>{v[t]=e.composite[t]}))}function C(){b.forEach((e=>function(e){const t=e.content;t&&Object.keys(v).forEach((e=>{t.setOption(e,v[e])}))}(e)))}if(t.load(g.id).then((e=>{I(e),C(),e.changed.connect((()=>{I(e),C()}))})).catch(w.showErrorMessage),null==m||m.themeChanged.connect(((e,t)=>{b.forEach((e=>{const t=e.content;"inherit"===t.getOption("theme")&&t.setOption("theme","inherit")}))})),function(e,t,n,r,o){const s=r.load("jupyterlab"),{commands:c,serviceManager:m}=e,h=()=>null!==t.currentWidget&&t.currentWidget===e.shell.currentWidget;c.addCommand(a.createNew,{label:e=>e.isPalette?s.__("New Terminal"):s.__("Terminal"),caption:s.__("Start a new terminal session"),icon:e=>e.isPalette?void 0:u.terminalIcon,execute:async n=>{const a=n.name,s=n.cwd,c=s?m.contents.localPath(s):void 0;let h;h=a?(await l.TerminalAPI.listRunning()).map((e=>e.name)).includes(a)?m.terminals.connectTo({model:{name:a}}):await m.terminals.startNew({name:a,cwd:c}):await m.terminals.startNew({cwd:c});const _=new d.Terminal(h,o,r);_.title.icon=u.terminalIcon,_.title.label="...";const g=new i.MainAreaWidget({content:_,reveal:_.ready});return e.shell.add(g,"main",{type:"Terminal"}),t.add(g),e.shell.activateById(g.id),g}}),c.addCommand(a.open,{label:s.__("Open a terminal by its `name`."),execute:n=>{const r=n.name,i=t.find((e=>e.content.session.name===r||!1));if(!i)return c.execute(a.createNew,{name:r});e.shell.activateById(i.id)}}),c.addCommand(a.refresh,{label:s.__("Refresh Terminal"),caption:s.__("Refresh the current terminal session"),execute:async()=>{const n=t.currentWidget;if(n){e.shell.activateById(n.id);try{await n.content.refresh(),n&&n.content.activate()}catch(e){w.showErrorMessage(e)}}},icon:e=>e.isPalette?void 0:u.refreshIcon.bindprops({stylesheet:"menuItem"}),isEnabled:h}),c.addCommand(a.copy,{execute:()=>{var e;const n=null===(e=t.currentWidget)||void 0===e?void 0:e.content;if(!n)return;const a=n.getSelection();a&&i.Clipboard.copyToSystem(a)},isEnabled:()=>{var e;if(!h())return!1;const n=null===(e=t.currentWidget)||void 0===e?void 0:e.content;return!!n&&n.hasSelection()},icon:u.copyIcon.bindprops({stylesheet:"menuItem"}),label:s.__("Copy")}),c.addCommand(a.paste,{execute:async()=>{var e;const n=null===(e=t.currentWidget)||void 0===e?void 0:e.content;if(!n)return;const a=window.navigator.clipboard,r=await a.readText();r&&n.paste(r)},isEnabled:()=>{var e;return Boolean(h()&&(null===(e=t.currentWidget)||void 0===e?void 0:e.content))},icon:u.pasteIcon.bindprops({stylesheet:"menuItem"}),label:s.__("Paste")}),c.addCommand(a.shutdown,{label:s.__("Shutdown Terminal"),execute:()=>{const e=t.currentWidget;if(e)return e.content.session.shutdown()},isEnabled:h}),c.addCommand(a.increaseFont,{label:s.__("Increase Terminal Font Size"),execute:async()=>{const{fontSize:e}=o;if(e&&e<72)try{await n.set(g.id,"fontSize",e+1)}catch(e){w.showErrorMessage(e)}}}),c.addCommand(a.decreaseFont,{label:s.__("Decrease Terminal Font Size"),execute:async()=>{const{fontSize:e}=o;if(e&&e>9)try{await n.set(g.id,"fontSize",e-1)}catch(e){w.showErrorMessage(e)}}});const _={inherit:s.__("Inherit"),light:s.__("Light"),dark:s.__("Dark")};c.addCommand(a.setTheme,{label:e=>{if(void 0===e.theme)return s.__("Set terminal theme to the provided `theme`.");const t=e.theme,n=t in _?_[t]:s.__(t[0].toUpperCase()+t.slice(1));return e.isPalette?s.__("Use Terminal Theme: %1",n):n},caption:s.__("Set the terminal theme"),isToggled:e=>{const{theme:t}=o;return e.theme===t},execute:async e=>{const t=e.theme;try{await n.set(g.id,"theme",t),c.notifyCommandChanged(a.setTheme)}catch(e){console.log(e),w.showErrorMessage(e)}}})}(e,b,t,n,v),c){const e=new _.Menu({commands:f});e.title.label=p._p("menu","Terminal Theme"),e.addItem({command:a.setTheme,args:{theme:"inherit",displayName:p.__("Inherit"),isPalette:!1}}),e.addItem({command:a.setTheme,args:{theme:"light",displayName:p.__("Light"),isPalette:!1}}),e.addItem({command:a.setTheme,args:{theme:"dark",displayName:p.__("Dark"),isPalette:!1}}),c.settingsMenu.addGroup([{command:a.increaseFont},{command:a.decreaseFont},{type:"submenu",submenu:e}],40),c.fileMenu.newMenu.addItem({command:a.createNew,rank:20}),c.fileMenu.closeAndCleaners.add({id:a.shutdown,isEnabled:e=>null!==b.currentWidget&&b.has(e)})}return r&&([a.createNew,a.refresh,a.increaseFont,a.decreaseFont].forEach((e=>{r.addItem({command:e,category:T,args:{isPalette:!0}})})),r.addItem({command:a.setTheme,category:T,args:{theme:"inherit",displayName:p.__("Inherit"),isPalette:!0}}),r.addItem({command:a.setTheme,category:T,args:{theme:"light",displayName:p.__("Light"),isPalette:!0}}),r.addItem({command:a.setTheme,category:T,args:{theme:"dark",displayName:p.__("Dark"),isPalette:!0}})),o&&o.add({command:a.createNew,category:p.__("Other"),rank:0}),h&&function(e,t,n){const a=n.load("jupyterlab"),r=t.serviceManager.terminals;class i{constructor(e){this._model=e}open(){t.commands.execute("terminal:open",{name:this._model.name})}icon(){return u.terminalIcon}label(){return`terminals/${this._model.name}`}shutdown(){return r.shutdown(this._model.name)}}e.add({name:a.__("Terminals"),running:()=>Array.from(r.running()).map((e=>new i(e))),shutdownAll:()=>r.shutdownAll(),refreshRunning:()=>r.refreshRunning(),runningChanged:r.runningChanged,shutdownLabel:a.__("Shut Down"),shutdownAllLabel:a.__("Shut Down All"),shutdownAllConfirmationText:a.__("Are you sure you want to permanently shut down all running terminals?")})}(h,e,n),b},id:"@jupyterlab/terminal-extension:plugin",description:"Adds terminal and provides its tracker.",provides:d.ITerminalTracker,requires:[m.ISettingRegistry,h.ITranslator],optional:[i.ICommandPalette,o.ILauncher,r.ILayoutRestorer,s.IMainMenu,i.IThemeManager,c.IRunningSessionManagers],autoStart:!0},p=g;var w;!function(e){e.showErrorMessage=function(e){console.error(`Failed to configure ${g.id}: ${e.message}`)}}(w||(w={}))}}]);
/*! For license information please see 1664.bundle.js.LICENSE.txt */
(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[1664],{11664:(e,t,n)=>{"use strict";n.r(t),n.d(t,{AddButton:()=>oi,Button:()=>h,Collapser:()=>bt,CommandPaletteSvg:()=>pp,CommandToolbarButton:()=>Pu,CommandToolbarButtonComponent:()=>Ru,ContextMenuSvg:()=>mp,DEFAULT_STYLE_CLASS:()=>pi,DEFAULT_UI_OPTIONS:()=>si,DockPanelSvg:()=>wp,DropButton:()=>ii,FilenameSearcher:()=>Yu,FilterBox:()=>Ku,FormComponent:()=>ui,FormRendererRegistry:()=>kp,HTMLSelect:()=>mi,HTML_SELECT_CLASS:()=>gi,HoverBox:()=>Cp,IFormRendererRegistry:()=>Sp,IFrame:()=>fi,ILabIconManager:()=>_p,IRankedMenu:()=>bi,InputGroup:()=>wi,LabIcon:()=>b,MenuSvg:()=>fp,MoveButton:()=>ri,PanelWithToolbar:()=>qu,RankedMenu:()=>Ci,ReactWidget:()=>Mu,ReactiveToolbar:()=>$u,SidePanel:()=>tp,Spinner:()=>np,Styling:()=>Qu,Switch:()=>sp,TABLE_CLASS:()=>rp,TabBarSvg:()=>vp,TabPanelSvg:()=>bp,Table:()=>ip,Toolbar:()=>Ou,ToolbarButton:()=>Nu,ToolbarButtonComponent:()=>Au,UseSignal:()=>Tu,VDomModel:()=>Lu,VDomRenderer:()=>Iu,WindowedLayout:()=>hp,WindowedList:()=>cp,WindowedListModel:()=>lp,addAboveIcon:()=>C,addBelowIcon:()=>S,addCommandToolbarButtonClass:()=>zu,addIcon:()=>_,addToolbarButtonClass:()=>Bu,badIcon:()=>y,bellIcon:()=>k,blankIcon:()=>x,bugDotIcon:()=>F,bugIcon:()=>j,buildIcon:()=>M,caretDownEmptyIcon:()=>I,caretDownEmptyThinIcon:()=>T,caretDownIcon:()=>L,caretLeftIcon:()=>V,caretRightIcon:()=>D,caretUpEmptyThinIcon:()=>E,caretUpIcon:()=>O,caseSensitiveIcon:()=>$,checkIcon:()=>A,circleEmptyIcon:()=>B,circleIcon:()=>N,classes:()=>a,classesDedupe:()=>l,clearIcon:()=>R,closeIcon:()=>z,codeCheckIcon:()=>P,codeIcon:()=>H,collapseAllIcon:()=>U,collapseIcon:()=>W,consoleIcon:()=>q,copyIcon:()=>G,copyrightIcon:()=>Z,cutIcon:()=>K,deleteIcon:()=>Y,downloadIcon:()=>J,duplicateIcon:()=>X,editIcon:()=>Q,ellipsesIcon:()=>ee,errorIcon:()=>te,expandAllIcon:()=>ne,expandIcon:()=>se,extensionIcon:()=>re,fastForwardIcon:()=>ie,fileIcon:()=>oe,fileUploadIcon:()=>ae,filterDotIcon:()=>le,filterIcon:()=>ce,filterListIcon:()=>he,folderFavoriteIcon:()=>de,folderIcon:()=>ue,fuzzySearch:()=>Gu,getReactAttrs:()=>c,historyIcon:()=>pe,homeIcon:()=>ge,html5Icon:()=>me,imageIcon:()=>fe,infoIcon:()=>ve,inspectorIcon:()=>we,jsonIcon:()=>be,juliaIcon:()=>ye,jupyterFaviconIcon:()=>xe,jupyterIcon:()=>Ce,jupyterlabWordmarkIcon:()=>Se,kernelIcon:()=>_e,keyboardIcon:()=>ke,launchIcon:()=>Fe,launcherIcon:()=>je,lineFormIcon:()=>Me,linkIcon:()=>Ie,listIcon:()=>Te,lockIcon:()=>Le,markdownIcon:()=>Ve,mermaidIcon:()=>De,moveDownIcon:()=>Ee,moveUpIcon:()=>Oe,newFolderIcon:()=>$e,notTrustedIcon:()=>Ae,notebookIcon:()=>Be,numberingIcon:()=>Ne,offlineBoltIcon:()=>Re,paletteIcon:()=>ze,pasteIcon:()=>Pe,pdfIcon:()=>He,pythonIcon:()=>Ue,rKernelIcon:()=>We,reactIcon:()=>qe,redoIcon:()=>Ge,refreshIcon:()=>Ze,regexIcon:()=>Ke,runIcon:()=>Ye,runningIcon:()=>Je,saveIcon:()=>Xe,searchIcon:()=>Qe,settingsIcon:()=>et,shareIcon:()=>tt,spreadsheetIcon:()=>nt,stopIcon:()=>st,tabIcon:()=>rt,tableRowsIcon:()=>it,tagIcon:()=>ot,terminalIcon:()=>at,textEditorIcon:()=>lt,tocIcon:()=>ct,treeViewIcon:()=>ht,trustedIcon:()=>dt,undoIcon:()=>ut,updateFilterFunction:()=>Zu,userIcon:()=>pt,usersIcon:()=>gt,vegaIcon:()=>mt,wordIcon:()=>ft,yamlIcon:()=>vt});var s=n(78156),r=n.n(s),i=n(38639);function o(e){return e.map((e=>e&&"object"==typeof e?Object.keys(e).map((t=>!!e[t]&&t)):"string"==typeof e?e.split(/\s+/):[])).reduce(((e,t)=>e.concat(t)),[]).filter((e=>!!e))}function a(...e){return o(e).join(" ")}function l(...e){return[...new Set(o(e))].join(" ")}function c(e,{ignore:t=[]}={}){return e.getAttributeNames().reduce(((n,s)=>("style"===s||t.includes(s)||(s.startsWith("data")?n[s]=e.getAttribute(s):n[i.Text.camelCase(s)]=e.getAttribute(s)),n)),{})}function h(e){const{minimal:t,small:n,children:s,...i}=e;return r().createElement("button",{...i,className:a(e.className,t?"jp-mod-minimal":"",n?"jp-mod-small":"","jp-Button")},s)}var d=n(81997),u=n(31516),p=n(20998),g=n(37634);const m='<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 18 18">\n    <g class="jp-icon3" fill="#616161">\n        <path d="M9 13.5c-2.49 0-4.5-2.01-4.5-4.5S6.51 4.5 9 4.5c1.24 0 2.36.52 3.17 1.33L10 8h5V3l-1.76 1.76C12.15 3.68 10.66 3 9 3 5.69 3 3.01 5.69 3.01 9S5.69 15 9 15c2.97 0 5.43-2.16 5.9-5h-1.52c-.46 2-2.24 3.5-4.38 3.5z"/>\n    </g>\n</svg>\n';var f,v,w=n(73062);!function(e){const t={breadCrumb:{container:{$nest:{"&:first-child svg":{bottom:"1px",marginLeft:"0px",position:"relative"},"&:hover":{backgroundColor:"var(--jp-layout-color2)"},".jp-mod-dropTarget&":{backgroundColor:"var(--jp-brand-color2)",opacity:.7}}},element:{borderRadius:"var(--jp-border-radius)",cursor:"pointer",margin:"0px 2px",padding:"0px 2px",height:"16px",width:"16px",verticalAlign:"middle"}},commandPaletteHeader:{container:{height:"14px",margin:"0 14px 0 auto"},element:{height:"14px",width:"14px"},options:{elementPosition:"center"}},commandPaletteItem:{element:{height:"16px",width:"16px"},options:{elementPosition:"center"}},launcherCard:{container:{height:"52px",width:"52px"},element:{height:"52px",width:"52px"},options:{elementPosition:"center"}},launcherSection:{container:{boxSizing:"border-box",marginRight:"12px",height:"32px",width:"32px"},element:{height:"32px",width:"32px"},options:{elementPosition:"center"}},listing:{container:{flex:"0 0 20px",marginRight:"4px",position:"relative"},element:{height:"16px",width:"16px"},options:{elementPosition:"center"}},listingHeaderItem:{container:{display:"inline",height:"16px",width:"16px"},element:{height:"auto",margin:"-2px 0 0 0",width:"20px"},options:{elementPosition:"center"}},mainAreaTab:{container:{$nest:{".lm-DockPanel-tabBar &":{marginRight:"4px"}}},element:{$nest:{".lm-DockPanel-tabBar &":{height:"14px",width:"14px"}}},options:{elementPosition:"center"}},menuItem:{container:{display:"inline-block",verticalAlign:"middle"},element:{height:"16px",width:"16px"},options:{elementPosition:"center"}},runningItem:{container:{margin:"0px 4px 0px 4px"},element:{height:"16px",width:"16px"},options:{elementPosition:"center"}},select:{container:{pointerEvents:"none"},element:{position:"absolute",height:"auto",width:"16px"}},settingsEditor:{container:{display:"flex",flex:"0 0 20px",margin:"0 3px 0 0",position:"relative",height:"20px",width:"20px"},element:{height:"16px",width:"16px"},options:{elementPosition:"center"}},sideBar:{element:{height:"auto",width:"20px"},options:{elementPosition:"center"}},splash:{container:{animation:"0.3s fade-in linear forwards",height:"100%",width:"100%",zIndex:1},element:{width:"100px"},options:{elementPosition:"center"}},statusBar:{element:{left:"0px",top:"0px",height:"18px",width:"20px",position:"relative"}},toolbarButton:{container:{display:"inline-block",verticalAlign:"middle"},element:{height:"16px",width:"16px"},options:{elementPosition:"center"}}};function n(e){return{container:{alignItems:"center",display:"flex"},element:{display:"block",...e}}}const s={center:n({margin:"0 auto",width:"100%"}),top:n({margin:"0 0 auto 0"}),right:n({margin:"0 0 0 auto"}),bottom:n({margin:"auto 0 0 0"}),left:n({margin:"0 auto 0 0"}),"top right":n({margin:"0 0 auto auto"}),"bottom right":n({margin:"auto 0 0 auto"}),"bottom left":n({margin:"auto auto 0 0"}),"top left":n({margin:"0 auto 0 auto"})};function r(e){return{element:{height:e,width:e}}}const i={small:r("14px"),normal:r("16px"),large:r("20px"),xlarge:r("24px")};const o=new Map;e.styleClass=function(e){if(!e||0===Object.keys(e).length)return"";let{elementPosition:n,elementSize:r,stylesheet:a,...l}=e;const c={...n&&{elementPosition:n},...r&&{elementSize:r}},h="string"==typeof a&&0===Object.keys(l).length,d=h?[a,n,r].join(","):"";if(h&&o.has(d))return o.get(d);const u=function(e){return e?(Array.isArray(e)||(e=[e]),e.map((e=>"string"==typeof e?t[e]:e))):[]}(a);u.push({element:l,options:c});const p=function(e){var t;return(0,w.oB)({...e.container,$nest:{...null===(t=e.container)||void 0===t?void 0:t.$nest,svg:e.element}})}(function(e){const t=Object.assign({},...e.map((e=>e.options)));return t.elementPosition&&e.unshift(s[t.elementPosition]),t.elementSize&&e.unshift(i[t.elementSize]),function(e){return{container:Object.assign({},...e.map((e=>e.container))),element:Object.assign({},...e.map((e=>e.element)))}}(e)}(u));return h&&o.set(d,p),p}}(f||(f={}));class b{static remove(e){for(;e.firstChild;)e.firstChild.remove();return e.className="",e}static resolve({icon:e}){if(e instanceof b)return e;if("string"==typeof e){return b._instances.get(e)||(b._debug&&console.warn(`Lookup failed for icon, creating loading icon. icon: ${e}`),new b({name:e,svgstr:m,_loading:!0}))}return new b(e)}static resolveElement({icon:e,iconClass:t,fallback:n,...s}){return v.isResolvable(e)?b.resolve({icon:e}).element(s):!t&&n?n.element(s):(s.className=a(t,s.className),v.blankElement(s))}static resolveReact({icon:e,iconClass:t,fallback:n,...s}){if(!v.isResolvable(e))return!t&&n?r().createElement(n.react,{...s}):(s.className=a(t,s.className),r().createElement(v.blankReact,{...s}));const i=b.resolve({icon:e});return r().createElement(i.react,{...s})}static resolveSvg({name:e,svgstr:t}){const n=(new DOMParser).parseFromString(v.svgstrShim(t),"image/svg+xml"),s=n.querySelector("parsererror");if(s){const n=`SVG HTML was malformed for LabIcon instance.\nname: ${e}, svgstr: ${t}`;return b._debug?(console.error(n),s):(console.warn(n),null)}return n.documentElement}static toggleDebug(e){b._debug=null!=e?e:!b._debug}constructor({name:e,svgstr:t,render:n,unrender:s,_loading:r=!1}){if(this._props={},this._svgReplaced=new d.Signal(this),this._svgElement=void 0,this._svgInnerHTML=void 0,this._svgReactAttrs=void 0,!e||!t)return console.error(`When defining a new LabIcon, name and svgstr must both be non-empty strings. name: ${e}, svgstr: ${t}`),y;if(this._loading=r,b._instances.has(e)){const n=b._instances.get(e);return this._loading?(n.svgstr=t,this._loading=!1,n):(b._debug&&console.warn(`Redefining previously loaded icon svgstr. name: ${e}, svgstrOld: ${n.svgstr}, svgstr: ${t}`),n.svgstr=t,n)}this.name=e,this.react=this._initReact(e),this.svgstr=t,this._initRender({render:n,unrender:s}),b._instances.set(this.name,this)}bindprops(e){const t=Object.create(this);return t._props=e,t.react=t._initReact(t.name+"_bind"),t}element(e={}){var t;let{className:n,container:s,label:r,title:i,tag:o="div",...a}={...this._props,...e};const l=null==s?void 0:s.firstChild;if((null===(t=null==l?void 0:l.dataset)||void 0===t?void 0:t.iconId)===this._uuid)return l;if(!this.svgElement)return document.createElement("div");if(s)for(;s.firstChild;)s.firstChild.remove();else o&&(s=document.createElement(o));const c=this.svgElement.cloneNode(!0);return s?(null!=r&&(s.textContent=r),v.initContainer({container:s,className:n,styleProps:a,title:i}),s.appendChild(c),s):(r&&console.warn(),c)}render(e,t){var n;let s=null===(n=null==t?void 0:t.children)||void 0===n?void 0:n[0];"string"!=typeof s&&(s=void 0),this.element({container:e,label:s,...null==t?void 0:t.props})}get svgElement(){return void 0===this._svgElement&&(this._svgElement=this._initSvg({uuid:this._uuid})),this._svgElement}get svgInnerHTML(){return void 0===this._svgInnerHTML&&(null===this.svgElement?this._svgInnerHTML=null:this._svgInnerHTML=this.svgElement.innerHTML),this._svgInnerHTML}get svgReactAttrs(){return void 0===this._svgReactAttrs&&(null===this.svgElement?this._svgReactAttrs=null:this._svgReactAttrs=c(this.svgElement,{ignore:["data-icon-id"]})),this._svgReactAttrs}get svgstr(){return this._svgstr}set svgstr(e){this._svgstr=e;const t=p.UUID.uuid4(),n=this._uuid;this._uuid=t,this._svgElement=void 0,this._svgInnerHTML=void 0,this._svgReactAttrs=void 0,document.querySelectorAll(`[data-icon-id="${n}"]`).forEach((e=>{this.svgElement&&e.replaceWith(this.svgElement.cloneNode(!0))})),this._svgReplaced.emit()}_initReact(e){const t=r().forwardRef(((e={},t)=>{const{className:n,container:s,label:i,title:o,tag:l="div",...c}={...this._props,...e},[,h]=r().useState(this._uuid);r().useEffect((()=>{const e=()=>{h(this._uuid)};return this._svgReplaced.connect(e),()=>{this._svgReplaced.disconnect(e)}}));const d=null!=l?l:r().Fragment;if(!this.svgInnerHTML||!this.svgReactAttrs)return r().createElement(r().Fragment,null);const u=r().createElement("svg",{...this.svgReactAttrs,dangerouslySetInnerHTML:{__html:this.svgInnerHTML},ref:t});if(s)return v.initContainer({container:s,className:n,styleProps:c,title:o}),r().createElement(r().Fragment,null,u,i);{let e={};return d!==r().Fragment&&(e={className:n||c?a(n,f.styleClass(c)):void 0,title:o}),r().createElement(d,{...e},u,i)}}));return t.displayName=`LabIcon_${e}`,t}_initRender({render:e,unrender:t}){e?(this.render=e,t&&(this.unrender=t)):t&&console.warn("In _initRender, ignoring unrender arg since render is undefined")}_initSvg({title:e,uuid:t}={}){const n=b.resolveSvg(this);return n?("parsererror"!==n.tagName&&(n.dataset.icon=this.name,t&&(n.dataset.iconId=t),e&&v.setTitleSvg(n,e)),n):n}}b._debug=!1,b._instances=new Map,function(e){function t({container:e,className:t,styleProps:n,title:s}){null!=s&&(e.title=s);const r=f.styleClass(n);if(null!=t){const n=a(t,r);return e.className=n,n}return r?(e.classList.add(r),r):""}e.blankElement=function({className:t="",container:n,label:s,title:r,tag:i="div",...o}){if((null==n?void 0:n.className)===t)return n;if(n)for(;n.firstChild;)n.firstChild.remove();else n=document.createElement(null!=i?i:"div");return null!=s&&(n.textContent=s),e.initContainer({container:n,className:t,styleProps:o,title:r}),n},e.blankReact=r().forwardRef((({className:e="",container:n,label:s,title:i,tag:o="div",...l},c)=>{const h=null!=o?o:"div";return n?(t({container:n,className:e,styleProps:l,title:i}),r().createElement(r().Fragment,null)):r().createElement(h,{className:a(e,f.styleClass(l))},c&&x.react({ref:c}),s)})),e.blankReact.displayName="BlankReact",e.initContainer=t,e.isResolvable=function(e){return!(!e||!("string"==typeof e||e.name&&e.svgstr))},e.setTitleSvg=function(e,t){const n=e.getElementsByTagName("title");if(n.length)n[0].textContent=t;else{const n=document.createElement("title");n.textContent=t,e.appendChild(n)}},e.svgstrShim=function(e,t=!0){const[,n,s]=decodeURIComponent(e).replace(/>\s*\n\s*</g,"><").replace(/\s*\n\s*/g," ").match(t?/^(?:data:.*?(;base64)?,)?(.*)/:/(?:(base64).*)?(<svg.*)/);return n?atob(s):s};class n{constructor(e,t){this._icon=e,this._rendererOptions=t}render(e,t){}}e.Renderer=n,e.ElementRenderer=class extends n{render(e,t){var n,s;let r=null===(n=null==t?void 0:t.children)||void 0===n?void 0:n[0];"string"!=typeof r&&(r=void 0),this._icon.element({container:e,label:r,...null===(s=this._rendererOptions)||void 0===s?void 0:s.props,...null==t?void 0:t.props})}},e.ReactRenderer=class extends n{constructor(){super(...arguments),this._rootDOM=null}render(e,t){var n,s;let i=null===(n=null==t?void 0:t.children)||void 0===n?void 0:n[0];"string"!=typeof i&&(i=void 0);const o=this._icon;null!==this._rootDOM&&this._rootDOM.unmount(),this._rootDOM=(0,g.s)(e),this._rootDOM.render(r().createElement(o.react,{container:e,label:i,...null===(s=this._rendererOptions)||void 0===s?void 0:s.props,...null==t?void 0:t.props}))}unrender(e){null!==this._rootDOM&&(this._rootDOM.unmount(),this._rootDOM=null)}}}(v||(v={}));const y=new b({name:"ui-components:bad",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">\n    <path\n        class="jp-icon0"\n        fill="#000"\n        d="M24 20.188l-8.315-8.209 8.2-8.282-3.697-3.697-8.212 8.318-8.31-8.203-3.666 3.666 8.321 8.24-8.206 8.313 3.666 3.666 8.237-8.318 8.285 8.203z"\n    />\n</svg>\n'}),x=new b({name:"ui-components:blank",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">\n    <path\n        fill="#000"\n        fill-opacity="0.0"\n        d="M24 20.188l-8.315-8.209 8.2-8.282-3.697-3.697-8.212 8.318-8.31-8.203-3.666 3.666 8.321 8.24-8.206 8.313 3.666 3.666 8.237-8.318 8.285 8.203z"\n    />\n</svg>\n'}),C=new b({name:"ui-components:add-above",svgstr:'<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">\n<g clip-path="url(#clip0_137_19492)">\n<path class="jp-icon3" d="M4.75 4.93066H6.625V6.80566C6.625 7.01191 6.79375 7.18066 7 7.18066C7.20625 7.18066 7.375 7.01191 7.375 6.80566V4.93066H9.25C9.45625 4.93066 9.625 4.76191 9.625 4.55566C9.625 4.34941 9.45625 4.18066 9.25 4.18066H7.375V2.30566C7.375 2.09941 7.20625 1.93066 7 1.93066C6.79375 1.93066 6.625 2.09941 6.625 2.30566V4.18066H4.75C4.54375 4.18066 4.375 4.34941 4.375 4.55566C4.375 4.76191 4.54375 4.93066 4.75 4.93066Z" fill="#616161" stroke="#616161" stroke-width="0.7"/>\n</g>\n<path class="jp-icon3" fill-rule="evenodd" clip-rule="evenodd" d="M11.5 9.5V11.5L2.5 11.5V9.5L11.5 9.5ZM12 8C12.5523 8 13 8.44772 13 9V12C13 12.5523 12.5523 13 12 13L2 13C1.44772 13 1 12.5523 1 12V9C1 8.44772 1.44771 8 2 8L12 8Z" fill="#616161"/>\n<defs>\n<clipPath id="clip0_137_19492">\n<rect class="jp-icon3" width="6" height="6" fill="white" transform="matrix(-1 0 0 1 10 1.55566)"/>\n</clipPath>\n</defs>\n</svg>\n'}),S=new b({name:"ui-components:add-below",svgstr:'<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">\n<g clip-path="url(#clip0_137_19498)">\n<path class="jp-icon3" d="M9.25 10.0693L7.375 10.0693L7.375 8.19434C7.375 7.98809 7.20625 7.81934 7 7.81934C6.79375 7.81934 6.625 7.98809 6.625 8.19434L6.625 10.0693L4.75 10.0693C4.54375 10.0693 4.375 10.2381 4.375 10.4443C4.375 10.6506 4.54375 10.8193 4.75 10.8193L6.625 10.8193L6.625 12.6943C6.625 12.9006 6.79375 13.0693 7 13.0693C7.20625 13.0693 7.375 12.9006 7.375 12.6943L7.375 10.8193L9.25 10.8193C9.45625 10.8193 9.625 10.6506 9.625 10.4443C9.625 10.2381 9.45625 10.0693 9.25 10.0693Z" fill="#616161" stroke="#616161" stroke-width="0.7"/>\n</g>\n<path class="jp-icon3" fill-rule="evenodd" clip-rule="evenodd" d="M2.5 5.5L2.5 3.5L11.5 3.5L11.5 5.5L2.5 5.5ZM2 7C1.44772 7 1 6.55228 1 6L1 3C1 2.44772 1.44772 2 2 2L12 2C12.5523 2 13 2.44772 13 3L13 6C13 6.55229 12.5523 7 12 7L2 7Z" fill="#616161"/>\n<defs>\n<clipPath id="clip0_137_19498">\n<rect class="jp-icon3" width="6" height="6" fill="white" transform="matrix(1 1.74846e-07 1.74846e-07 -1 4 13.4443)"/>\n</clipPath>\n</defs>\n</svg>\n'}),_=new b({name:"ui-components:add",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24">\n  <g class="jp-icon3" fill="#616161">\n    <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>\n  </g>\n</svg>\n'}),k=new b({name:"ui-components:bell",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 16 16" version="1.1">\n   <path class="jp-icon2 jp-icon-selectable" fill="#333333"\n      d="m8 0.29c-1.4 0-2.7 0.73-3.6 1.8-1.2 1.5-1.4 3.4-1.5 5.2-0.18 2.2-0.44 4-2.3 5.3l0.28 1.3h5c0.026 0.66 0.32 1.1 0.71 1.5 0.84 0.61 2 0.61 2.8 0 0.52-0.4 0.6-1 0.71-1.5h5l0.28-1.3c-1.9-0.97-2.2-3.3-2.3-5.3-0.13-1.8-0.26-3.7-1.5-5.2-0.85-1-2.2-1.8-3.6-1.8zm0 1.4c0.88 0 1.9 0.55 2.5 1.3 0.88 1.1 1.1 2.7 1.2 4.4 0.13 1.7 0.23 3.6 1.3 5.2h-10c1.1-1.6 1.2-3.4 1.3-5.2 0.13-1.7 0.3-3.3 1.2-4.4 0.59-0.72 1.6-1.3 2.5-1.3zm-0.74 12h1.5c-0.0015 0.28 0.015 0.79-0.74 0.79-0.73 0.0016-0.72-0.53-0.74-0.79z" />\n</svg>\n'}),F=new b({name:"ui-components:bug-dot",svgstr:'<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">\n    <g class="jp-icon3 jp-icon-selectable" fill="#616161">\n        <path fill-rule="evenodd" clip-rule="evenodd" d="M17.19 8H20V10H17.91C17.96 10.33 18 10.66 18 11V12H20V14H18.5H18V14.0275C15.75 14.2762 14 16.1837 14 18.5C14 19.208 14.1635 19.8779 14.4549 20.4739C13.7063 20.8117 12.8757 21 12 21C9.78 21 7.85 19.79 6.81 18H4V16H6.09C6.04 15.67 6 15.34 6 15V14H4V12H6V11C6 10.66 6.04 10.33 6.09 10H4V8H6.81C7.26 7.22 7.88 6.55 8.62 6.04L7 4.41L8.41 3L10.59 5.17C11.04 5.06 11.51 5 12 5C12.49 5 12.96 5.06 13.42 5.17L15.59 3L17 4.41L15.37 6.04C16.12 6.55 16.74 7.22 17.19 8ZM10 16H14V14H10V16ZM10 12H14V10H10V12Z" fill="#616161"/>\n        <path d="M22 18.5C22 20.433 20.433 22 18.5 22C16.567 22 15 20.433 15 18.5C15 16.567 16.567 15 18.5 15C20.433 15 22 16.567 22 18.5Z" fill="#616161"/>\n    </g>\n</svg>\n'}),j=new b({name:"ui-components:bug",svgstr:'<svg viewBox="0 0 24 24" width="16" xmlns="http://www.w3.org/2000/svg">\n  <g class="jp-icon3 jp-icon-selectable" fill="#616161">\n    <path d="M20 8h-2.81c-.45-.78-1.07-1.45-1.82-1.96L17 4.41 15.59 3l-2.17 2.17C12.96 5.06 12.49 5 12 5c-.49 0-.96.06-1.41.17L8.41 3 7 4.41l1.62 1.63C7.88 6.55 7.26 7.22 6.81 8H4v2h2.09c-.05.33-.09.66-.09 1v1H4v2h2v1c0 .34.04.67.09 1H4v2h2.81c1.04 1.79 2.97 3 5.19 3s4.15-1.21 5.19-3H20v-2h-2.09c.05-.33.09-.66.09-1v-1h2v-2h-2v-1c0-.34-.04-.67-.09-1H20V8zm-6 8h-4v-2h4v2zm0-4h-4v-2h4v2z"/>\n  </g>\n</svg>\n'}),M=new b({name:"ui-components:build",svgstr:'<svg width="16" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">\n  <g class="jp-icon3" fill="#616161">\n    <path d="M14.9 17.45C16.25 17.45 17.35 16.35 17.35 15C17.35 13.65 16.25 12.55 14.9 12.55C13.54 12.55 12.45 13.65 12.45 15C12.45 16.35 13.54 17.45 14.9 17.45ZM20.1 15.68L21.58 16.84C21.71 16.95 21.75 17.13 21.66 17.29L20.26 19.71C20.17 19.86 20 19.92 19.83 19.86L18.09 19.16C17.73 19.44 17.33 19.67 16.91 19.85L16.64 21.7C16.62 21.87 16.47 22 16.3 22H13.5C13.32 22 13.18 21.87 13.15 21.7L12.89 19.85C12.46 19.67 12.07 19.44 11.71 19.16L9.96002 19.86C9.81002 19.92 9.62002 19.86 9.54002 19.71L8.14002 17.29C8.05002 17.13 8.09002 16.95 8.22002 16.84L9.70002 15.68L9.65001 15L9.70002 14.31L8.22002 13.16C8.09002 13.05 8.05002 12.86 8.14002 12.71L9.54002 10.29C9.62002 10.13 9.81002 10.07 9.96002 10.13L11.71 10.84C12.07 10.56 12.46 10.32 12.89 10.15L13.15 8.28998C13.18 8.12998 13.32 7.99998 13.5 7.99998H16.3C16.47 7.99998 16.62 8.12998 16.64 8.28998L16.91 10.15C17.33 10.32 17.73 10.56 18.09 10.84L19.83 10.13C20 10.07 20.17 10.13 20.26 10.29L21.66 12.71C21.75 12.86 21.71 13.05 21.58 13.16L20.1 14.31L20.15 15L20.1 15.68Z"/>\n    <path d="M7.32966 7.44454C8.0831 7.00954 8.33932 6.05332 7.90432 5.29988C7.46932 4.54643 6.5081 4.28156 5.75466 4.71656C5.39176 4.92608 5.12695 5.27118 5.01849 5.67594C4.91004 6.08071 4.96682 6.51198 5.17634 6.87488C5.61134 7.62832 6.57622 7.87954 7.32966 7.44454ZM9.65718 4.79593L10.8672 4.95179C10.9628 4.97741 11.0402 5.07133 11.0382 5.18793L11.0388 6.98893C11.0455 7.10054 10.9616 7.19518 10.855 7.21054L9.66001 7.38083L9.23915 8.13188L9.66961 9.25745C9.70729 9.36271 9.66934 9.47699 9.57408 9.53199L8.01523 10.432C7.91131 10.492 7.79337 10.4677 7.72105 10.3824L6.98748 9.43188L6.10931 9.43083L5.34704 10.3905C5.28909 10.4702 5.17383 10.4905 5.07187 10.4339L3.51245 9.53293C3.41049 9.47633 3.37647 9.35741 3.41075 9.25679L3.86347 8.14093L3.61749 7.77488L3.42347 7.37883L2.23075 7.21297C2.12647 7.19235 2.04049 7.10342 2.04245 6.98682L2.04187 5.18582C2.04383 5.06922 2.11909 4.97958 2.21704 4.96922L3.42065 4.79393L3.86749 4.02788L3.41105 2.91731C3.37337 2.81204 3.41131 2.69776 3.51523 2.63776L5.07408 1.73776C5.16934 1.68276 5.28729 1.70704 5.35961 1.79231L6.11915 2.72788L6.98001 2.73893L7.72496 1.78922C7.79156 1.70458 7.91548 1.67922 8.00879 1.74082L9.56821 2.64182C9.67017 2.69842 9.71285 2.81234 9.68723 2.90797L9.21718 4.03383L9.46316 4.39988L9.65718 4.79593Z"/>\n  </g>\n</svg>\n'}),I=new b({name:"ui-components:caret-down-empty",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 18 18">\n  <g class="jp-icon3" fill="#616161" shape-rendering="geometricPrecision">\n    <path d="M5.2,5.9L9,9.7l3.8-3.8l1.2,1.2l-4.9,5l-4.9-5L5.2,5.9z"/>\n  </g>\n</svg>\n'}),T=new b({name:"ui-components:caret-down-empty-thin",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 20 20">\n\t<g class="jp-icon3" fill="#616161" shape-rendering="geometricPrecision">\n\t\t<polygon class="st1" points="9.9,13.6 3.6,7.4 4.4,6.6 9.9,12.2 15.4,6.7 16.1,7.4 "/>\n\t</g>\n</svg>\n'}),L=new b({name:"ui-components:caret-down",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 18 18">\n  <g class="jp-icon3" fill="#616161" shape-rendering="geometricPrecision">\n    <path d="M5.2,7.5L9,11.2l3.8-3.8H5.2z"/>\n  </g>\n</svg>\n'}),V=new b({name:"ui-components:caret-left",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 18 18">\n\t<g class="jp-icon3" fill="#616161" shape-rendering="geometricPrecision">\n\t\t<path d="M10.8,12.8L7.1,9l3.8-3.8l0,7.6H10.8z"/>\n  </g>\n</svg>\n'}),D=new b({name:"ui-components:caret-right",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 18 18">\n  <g class="jp-icon3" fill="#616161" shape-rendering="geometricPrecision">\n    <path d="M7.2,5.2L10.9,9l-3.8,3.8V5.2H7.2z"/>\n  </g>\n</svg>\n'}),E=new b({name:"ui-components:caret-up-empty-thin",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 20 20">\n\t<g class="jp-icon3" fill="#616161" shape-rendering="geometricPrecision">\n\t\t<polygon class="st1" points="15.4,13.3 9.9,7.7 4.4,13.2 3.6,12.5 9.9,6.3 16.1,12.6 "/>\n\t</g>\n</svg>\n'}),O=new b({name:"ui-components:caret-up",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 18 18">\n\t<g class="jp-icon3" fill="#616161" shape-rendering="geometricPrecision">\n\t\t<path d="M5.2,10.5L9,6.8l3.8,3.8H5.2z"/>\n  </g>\n</svg>\n'}),$=new b({name:"ui-components:case-sensitive",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 20 20">\n  <g class="jp-icon2" fill="#414141">\n    <rect x="2" y="2" width="16" height="16"/>\n  </g>\n  <g class="jp-icon-accent2" fill="#FFF">\n    <path d="M7.6,8h0.9l3.5,8h-1.1L10,14H6l-0.9,2H4L7.6,8z M8,9.1L6.4,13h3.2L8,9.1z"/>\n    <path d="M16.6,9.8c-0.2,0.1-0.4,0.1-0.7,0.1c-0.2,0-0.4-0.1-0.6-0.2c-0.1-0.1-0.2-0.4-0.2-0.7 c-0.3,0.3-0.6,0.5-0.9,0.7c-0.3,0.1-0.7,0.2-1.1,0.2c-0.3,0-0.5,0-0.7-0.1c-0.2-0.1-0.4-0.2-0.6-0.3c-0.2-0.1-0.3-0.3-0.4-0.5 c-0.1-0.2-0.1-0.4-0.1-0.7c0-0.3,0.1-0.6,0.2-0.8c0.1-0.2,0.3-0.4,0.4-0.5C12,7,12.2,6.9,12.5,6.8c0.2-0.1,0.5-0.1,0.7-0.2 c0.3-0.1,0.5-0.1,0.7-0.1c0.2,0,0.4-0.1,0.6-0.1c0.2,0,0.3-0.1,0.4-0.2c0.1-0.1,0.2-0.2,0.2-0.4c0-1-1.1-1-1.3-1 c-0.4,0-1.4,0-1.4,1.2h-0.9c0-0.4,0.1-0.7,0.2-1c0.1-0.2,0.3-0.4,0.5-0.6c0.2-0.2,0.5-0.3,0.8-0.3C13.3,4,13.6,4,13.9,4 c0.3,0,0.5,0,0.8,0.1c0.3,0,0.5,0.1,0.7,0.2c0.2,0.1,0.4,0.3,0.5,0.5C16,5,16,5.2,16,5.6v2.9c0,0.2,0,0.4,0,0.5 c0,0.1,0.1,0.2,0.3,0.2c0.1,0,0.2,0,0.3,0V9.8z M15.2,6.9c-1.2,0.6-3.1,0.2-3.1,1.4c0,1.4,3.1,1,3.1-0.5V6.9z"/>\n  </g>\n</svg>\n'}),A=new b({name:"ui-components:check",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24">\n  <g class="jp-icon3 jp-icon-selectable" fill="#616161">\n    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>\n  </g>\n</svg>\n'}),B=new b({name:"ui-components:circle-empty",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24">\n  <g class="jp-icon3" fill="#616161">\n    <path d="M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>\n  </g>\n</svg>\n'}),N=new b({name:"ui-components:circle",svgstr:'<svg viewBox="0 0 18 18" width="16" xmlns="http://www.w3.org/2000/svg">\n  <g class="jp-icon3" fill="#616161">\n    <circle cx="9" cy="9" r="8"/>\n  </g>\n</svg>\n'}),R=new b({name:"ui-components:clear",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24">\n  <mask id="donutHole">\n    <rect width="24" height="24" fill="white" />\n    <circle cx="12" cy="12" r="8" fill="black"/>\n  </mask>\n\n  <g class="jp-icon3" fill="#616161">\n    <rect height="18" width="2" x="11" y="3" transform="rotate(315, 12, 12)"/>\n    <circle cx="12" cy="12" r="10" mask="url(#donutHole)"/>\n  </g>\n</svg>\n'}),z=new b({name:"ui-components:close",svgstr:'<svg viewBox="0 0 24 24" width="16" xmlns="http://www.w3.org/2000/svg">\n  <g class="jp-icon-none jp-icon-selectable-inverse jp-icon3-hover" fill="none">\n    <circle cx="12" cy="12" r="11"/>\n  </g>\n\n  <g class="jp-icon3 jp-icon-selectable jp-icon-accent2-hover" fill="#616161">\n    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>\n  </g>\n\n  <g class="jp-icon-none jp-icon-busy" fill="none">\n    <circle cx="12" cy="12" r="7"/>\n  </g>\n</svg>\n'}),P=new b({name:"ui-components:code-check",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="24" viewBox="0 0 24 24">\n  <g class="jp-icon3 jp-icon-selectable" fill="#616161" shape-rendering="geometricPrecision">\n    <path d="M6.59,3.41L2,8L6.59,12.6L8,11.18L4.82,8L8,4.82L6.59,3.41M12.41,3.41L11,4.82L14.18,8L11,11.18L12.41,12.6L17,8L12.41,3.41M21.59,11.59L13.5,19.68L9.83,16L8.42,17.41L13.5,22.5L23,13L21.59,11.59Z" />\n  </g>\n</svg>\n'}),H=new b({name:"ui-components:code",svgstr:'<svg width="22" height="22" viewBox="0 0 28 28" xmlns="http://www.w3.org/2000/svg">\n\t<g class="jp-icon3" fill="#616161">\n\t\t<path d="M11.4 18.6L6.8 14L11.4 9.4L10 8L4 14L10 20L11.4 18.6ZM16.6 18.6L21.2 14L16.6 9.4L18 8L24 14L18 20L16.6 18.6V18.6Z"/>\n\t</g>\n</svg>\n'}),U=new b({name:"ui-components:collapse-all",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24">\n    <g class="jp-icon3" fill="#616161">\n        <path\n            d="M8 2c1 0 11 0 12 0s2 1 2 2c0 1 0 11 0 12s0 2-2 2C20 14 20 4 20 4S10 4 6 4c0-2 1-2 2-2z" />\n        <path\n            d="M18 8c0-1-1-2-2-2S5 6 4 6s-2 1-2 2c0 1 0 11 0 12s1 2 2 2c1 0 11 0 12 0s2-1 2-2c0-1 0-11 0-12zm-2 0v12H4V8z" />\n        <path d="M6 13v2h8v-2z" />\n    </g>\n</svg>\n'}),W=new b({name:"ui-components:collapse",svgstr:'<svg width="16" viewBox="0 0 8.5 10.5" xmlns="http://www.w3.org/2000/svg">\n  <g class="jp-icon-output" fill="#BDBDBD">\n    <path d="M.019 0h8.458v1.064H.019zM0 9.52h8.491v1.059H0zM4.776 2.912H3.72V1.323h1.056z" />\n    <path d="M4.244 5.243l-1.06-1.167-1.06-1.167h4.24l-1.06 1.167zM4.772 9.257H3.716V7.665h1.056z" />\n    <path d="M4.242 5.332L5.302 6.5l1.06 1.167h-4.24l1.06-1.167z" />\n  </g>\n</svg>\n'}),q=new b({name:"ui-components:console",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 200 200">\n  <g class="jp-console-icon-background-color jp-icon-selectable" fill="#0288D1">\n    <path d="M20 19.8h160v159.9H20z"/>\n  </g>\n  <g class="jp-console-icon-color jp-icon-selectable-inverse" fill="#fff">\n    <path d="M105 127.3h40v12.8h-40zM51.1 77L74 99.9l-23.3 23.3 10.5 10.5 23.3-23.3L95 99.9 84.5 89.4 61.6 66.5z"/>\n  </g>\n</svg>\n'}),G=new b({name:"ui-components:copy",svgstr:'<svg viewBox="0 0 18 18" width="16" xmlns="http://www.w3.org/2000/svg">\n  <g class="jp-icon3" fill="#616161">\n    <path d="M11.9,1H3.2C2.4,1,1.7,1.7,1.7,2.5v10.2h1.5V2.5h8.7V1z M14.1,3.9h-8c-0.8,0-1.5,0.7-1.5,1.5v10.2c0,0.8,0.7,1.5,1.5,1.5h8 c0.8,0,1.5-0.7,1.5-1.5V5.4C15.5,4.6,14.9,3.9,14.1,3.9z M14.1,15.5h-8V5.4h8V15.5z"/>\n  </g>\n</svg>\n'}),Z=new b({name:"ui-components:copyright",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" height="24" viewBox="0 0 24 24" width="24">\n  <g class="jp-icon3" fill="#616161">\n    <path d="M11.88,9.14c1.28,0.06,1.61,1.15,1.63,1.66h1.79c-0.08-1.98-1.49-3.19-3.45-3.19C9.64,7.61,8,9,8,12.14 c0,1.94,0.93,4.24,3.84,4.24c2.22,0,3.41-1.65,3.44-2.95h-1.79c-0.03,0.59-0.45,1.38-1.63,1.44C10.55,14.83,10,13.81,10,12.14 C10,9.25,11.28,9.16,11.88,9.14z M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z M12,20c-4.41,0-8-3.59-8-8 s3.59-8,8-8s8,3.59,8,8S16.41,20,12,20z"/>\n  </g>\n</svg>\n'}),K=new b({name:"ui-components:cut",svgstr:'<svg viewBox="0 0 24 24" width="16" xmlns="http://www.w3.org/2000/svg">\n  <g class="jp-icon3" fill="#616161">\n    <path d="M9.64 7.64c.23-.5.36-1.05.36-1.64 0-2.21-1.79-4-4-4S2 3.79 2 6s1.79 4 4 4c.59 0 1.14-.13 1.64-.36L10 12l-2.36 2.36C7.14 14.13 6.59 14 6 14c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4c0-.59-.13-1.14-.36-1.64L12 14l7 7h3v-1L9.64 7.64zM6 8c-1.1 0-2-.89-2-2s.9-2 2-2 2 .89 2 2-.9 2-2 2zm0 12c-1.1 0-2-.89-2-2s.9-2 2-2 2 .89 2 2-.9 2-2 2zm6-7.5c-.28 0-.5-.22-.5-.5s.22-.5.5-.5.5.22.5.5-.22.5-.5.5zM19 3l-6 6 2 2 7-7V3z"/>\n  </g>\n</svg>\n'}),Y=new b({name:"ui-components:delete",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16px" height="16px">\n    <path d="M0 0h24v24H0z" fill="none" />\n    <path class="jp-icon3" fill="#626262" d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z" />\n</svg>\n'}),J=new b({name:"ui-components:download",svgstr:'<svg viewBox="0 0 24 24" width="16" xmlns="http://www.w3.org/2000/svg">\n  <g class="jp-icon3" fill="#616161">\n    <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>\n  </g>\n</svg>\n'}),X=new b({name:"ui-components:duplicate",svgstr:'<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">\n<path class="jp-icon3" fill-rule="evenodd" clip-rule="evenodd" d="M2.79998 0.875H8.89582C9.20061 0.875 9.44998 1.13914 9.44998 1.46198C9.44998 1.78482 9.20061 2.04896 8.89582 2.04896H3.35415C3.04936 2.04896 2.79998 2.3131 2.79998 2.63594V9.67969C2.79998 10.0025 2.55061 10.2667 2.24582 10.2667C1.94103 10.2667 1.69165 10.0025 1.69165 9.67969V2.04896C1.69165 1.40328 2.1904 0.875 2.79998 0.875ZM5.36665 11.9V4.55H11.0833V11.9H5.36665ZM4.14165 4.14167C4.14165 3.69063 4.50728 3.325 4.95832 3.325H11.4917C11.9427 3.325 12.3083 3.69063 12.3083 4.14167V12.3083C12.3083 12.7594 11.9427 13.125 11.4917 13.125H4.95832C4.50728 13.125 4.14165 12.7594 4.14165 12.3083V4.14167Z" fill="#616161"/>\n<path class="jp-icon3" d="M9.43574 8.26507H8.36431V9.3365C8.36431 9.45435 8.26788 9.55078 8.15002 9.55078C8.03217 9.55078 7.93574 9.45435 7.93574 9.3365V8.26507H6.86431C6.74645 8.26507 6.65002 8.16864 6.65002 8.05078C6.65002 7.93292 6.74645 7.8365 6.86431 7.8365H7.93574V6.76507C7.93574 6.64721 8.03217 6.55078 8.15002 6.55078C8.26788 6.55078 8.36431 6.64721 8.36431 6.76507V7.8365H9.43574C9.5536 7.8365 9.65002 7.93292 9.65002 8.05078C9.65002 8.16864 9.5536 8.26507 9.43574 8.26507Z" fill="#616161" stroke="#616161" stroke-width="0.5"/>\n</svg>\n'}),Q=new b({name:"ui-components:edit",svgstr:'<svg viewBox="0 0 24 24" width="16" xmlns="http://www.w3.org/2000/svg">\n  <g class="jp-icon3" fill="#616161">\n    <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>\n  </g>\n</svg>\n'}),ee=new b({name:"ui-components:ellipses",svgstr:'<svg viewBox="0 0 24 24" width="16" xmlns="http://www.w3.org/2000/svg">\n  <g class="jp-icon3" fill="#616161">\n    <circle cx="5" cy="12" r="2"/>\n    <circle cx="12" cy="12" r="2"/>\n    <circle cx="19" cy="12" r="2"/>\n  </g>\n</svg>\n'}),te=new b({name:"ui-components:error",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24">\n<g class="jp-icon3" fill="#616161"><circle cx="12" cy="19" r="2"/><path d="M10 3h4v12h-4z"/></g>\n<path fill="none" d="M0 0h24v24H0z"/>\n</svg>\n'}),ne=new b({name:"ui-components:expand-all",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24">\n    <g class="jp-icon3" fill="#616161">\n        <path\n            d="M8 2c1 0 11 0 12 0s2 1 2 2c0 1 0 11 0 12s0 2-2 2C20 14 20 4 20 4S10 4 6 4c0-2 1-2 2-2z" />\n        <path\n            d="M18 8c0-1-1-2-2-2S5 6 4 6s-2 1-2 2c0 1 0 11 0 12s1 2 2 2c1 0 11 0 12 0s2-1 2-2c0-1 0-11 0-12zm-2 0v12H4V8z" />\n        <path d="M11 10H9v3H6v2h3v3h2v-3h3v-2h-3z" />\n    </g>\n</svg>\n'}),se=new b({name:"ui-components:expand",svgstr:'<svg width="16" viewBox="0 0 8.5 10.5" xmlns="http://www.w3.org/2000/svg">\n  <g class="jp-icon-output" fill="#BDBDBD">\n    <path d="M.019 0h8.458v1.064H.019zM0 9.521h8.491v1.059H0zM3.712 3.699h1.056v1.589H3.712z" />\n    <path d="M4.244 1.368l1.06 1.167 1.06 1.167h-4.24l1.06-1.167zM3.712 5.288h1.056V6.88H3.712z" />\n    <path d="M4.242 9.213l-1.06-1.167-1.06-1.167h4.24l-1.06 1.167z" />\n  </g>\n</svg>\n'}),re=new b({name:"ui-components:extension",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24">\n  <g class="jp-icon3" fill="#616161">\n    <path d="M20.5 11H19V7c0-1.1-.9-2-2-2h-4V3.5C13 2.12 11.88 1 10.5 1S8 2.12 8 3.5V5H4c-1.1 0-1.99.9-1.99 2v3.8H3.5c1.49 0 2.7 1.21 2.7 2.7s-1.21 2.7-2.7 2.7H2V20c0 1.1.9 2 2 2h3.8v-1.5c0-1.49 1.21-2.7 2.7-2.7 1.49 0 2.7 1.21 2.7 2.7V22H17c1.1 0 2-.9 2-2v-4h1.5c1.38 0 2.5-1.12 2.5-2.5S21.88 11 20.5 11z"/>\n  </g>\n</svg>\n'}),ie=new b({name:"ui-components:fast-forward",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">\n    <g class="jp-icon3" fill="#616161">\n        <path d="M4 18l8.5-6L4 6v12zm9-12v12l8.5-6L13 6z"/>\n    </g>\n</svg>\n'}),oe=new b({name:"ui-components:file",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 22 22">\n  <path class="jp-icon3 jp-icon-selectable" fill="#616161" d="M19.3 8.2l-5.5-5.5c-.3-.3-.7-.5-1.2-.5H3.9c-.8.1-1.6.9-1.6 1.8v14.1c0 .9.7 1.6 1.6 1.6h14.2c.9 0 1.6-.7 1.6-1.6V9.4c.1-.5-.1-.9-.4-1.2zm-5.8-3.3l3.4 3.6h-3.4V4.9zm3.9 12.7H4.7c-.1 0-.2 0-.2-.2V4.7c0-.2.1-.3.2-.3h7.2v4.4s0 .8.3 1.1c.3.3 1.1.3 1.1.3h4.3v7.2s-.1.2-.2.2z"/>\n</svg>\n'}),ae=new b({name:"ui-components:file-upload",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24">\n  <g class="jp-icon3" fill="#616161">\n    <path d="M9 16h6v-6h4l-7-7-7 7h4zm-4 2h14v2H5z"/>\n  </g>\n</svg>\n'}),le=new b({name:"ui-components:filter-dot",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24">\n  <g class="jp-icon3" fill="#FFF">\n    <path d="M14,12V19.88C14.04,20.18 13.94,20.5 13.71,20.71C13.32,21.1 12.69,21.1 12.3,20.71L10.29,18.7C10.06,18.47 9.96,18.16 10,17.87V12H9.97L4.21,4.62C3.87,4.19 3.95,3.56 4.38,3.22C4.57,3.08 4.78,3 5,3V3H19V3C19.22,3 19.43,3.08 19.62,3.22C20.05,3.56 20.13,4.19 19.79,4.62L14.03,12H14Z" />\n  </g>\n  <g class="jp-icon-dot" fill="#FFF">\n    <circle cx="18" cy="17" r="3"></circle>\n  </g>\n</svg>\n'}),ce=new b({name:"ui-components:filter",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24">\n  <g class="jp-icon3" fill="#FFF">\n    <path d="M14,12V19.88C14.04,20.18 13.94,20.5 13.71,20.71C13.32,21.1 12.69,21.1 12.3,20.71L10.29,18.7C10.06,18.47 9.96,18.16 10,17.87V12H9.97L4.21,4.62C3.87,4.19 3.95,3.56 4.38,3.22C4.57,3.08 4.78,3 5,3V3H19V3C19.22,3 19.43,3.08 19.62,3.22C20.05,3.56 20.13,4.19 19.79,4.62L14.03,12H14Z" />\n  </g>\n</svg>\n'}),he=new b({name:"ui-components:filter-list",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24">\n  <g class="jp-icon3" fill="#616161">\n    <path d="M10 18h4v-2h-4v2zM3 6v2h18V6H3zm3 7h12v-2H6v2z"/>\n  </g>\n</svg>\n'}),de=new b({name:"ui-components:folder-favorite",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px" fill="#000000">\n  <path d="M0 0h24v24H0V0z" fill="none"/><path class="jp-icon3 jp-icon-selectable" fill="#616161" d="M20 6h-8l-2-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm-2.06 11L15 15.28 12.06 17l.78-3.33-2.59-2.24 3.41-.29L15 8l1.34 3.14 3.41.29-2.59 2.24.78 3.33z"/>\n</svg>\n'}),ue=new b({name:"ui-components:folder",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24">\n  <path class="jp-icon3 jp-icon-selectable" fill="#616161" d="M10 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2h-8l-2-2z"/>\n</svg>\n'}),pe=new b({name:"ui-components:history",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24">\n<g class="jp-icon3" fill="#616161"><path d="M13.5,8H12V13L16.28,15.54L17,14.33L13.5,12.25V8M13,3A9,9 0 0,0 4,12H1L4.96,16.03L9,12H6A7,7 0 0,1 13,5A7,7 0 0,1 20,12A7,7 0 0,1 13,19C11.07,19 9.32,18.21 8.06,16.94L6.64,18.36C8.27,20 10.5,21 13,21A9,9 0 0,0 22,12A9,9 0 0,0 13,3" /></g>\n</svg>\n'}),ge=new b({name:"ui-components:home",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px" fill="#000000">\n  <path d="M0 0h24v24H0z" fill="none"/><path class="jp-icon3 jp-icon-selectable" fill="#616161" d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>\n</svg>\n'}),me=new b({name:"ui-components:html5",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 512 512">\n  <path class="jp-icon0 jp-icon-selectable" fill="#000" d="M108.4 0h23v22.8h21.2V0h23v69h-23V46h-21v23h-23.2M206 23h-20.3V0h63.7v23H229v46h-23m53.5-69h24.1l14.8 24.3L313.2 0h24.1v69h-23V34.8l-16.1 24.8-16.1-24.8V69h-22.6m89.2-69h23v46.2h32.6V69h-55.6"/>\n  <path class="jp-icon-selectable" fill="#e44d26" d="M107.6 471l-33-370.4h362.8l-33 370.2L255.7 512"/>\n  <path class="jp-icon-selectable" fill="#f16529" d="M256 480.5V131h148.3L376 447"/>\n  <path class="jp-icon-selectable-inverse" fill="#ebebeb" d="M142 176.3h114v45.4h-64.2l4.2 46.5h60v45.3H154.4m2 22.8H202l3.2 36.3 50.8 13.6v47.4l-93.2-26"/>\n  <path class="jp-icon-selectable-inverse" fill="#fff" d="M369.6 176.3H255.8v45.4h109.6m-4.1 46.5H255.8v45.4h56l-5.3 59-50.7 13.6v47.2l93-25.8"/>\n</svg>\n'}),fe=new b({name:"ui-components:image",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 22 22">\n  <path class="jp-icon-brand4 jp-icon-selectable-inverse" fill="#FFF" d="M2.2 2.2h17.5v17.5H2.2z"/>\n  <path class="jp-icon-brand0 jp-icon-selectable" fill="#3F51B5" d="M2.2 2.2v17.5h17.5l.1-17.5H2.2zm12.1 2.2c1.2 0 2.2 1 2.2 2.2s-1 2.2-2.2 2.2-2.2-1-2.2-2.2 1-2.2 2.2-2.2zM4.4 17.6l3.3-8.8 3.3 6.6 2.2-3.2 4.4 5.4H4.4z"/>\n</svg>\n'}),ve=new b({name:"ui-components:info",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 50.978 50.978">\n\t<g class="jp-icon3" fill="#616161">\n\t\t<path d="M43.52,7.458C38.711,2.648,32.307,0,25.489,0C18.67,0,12.266,2.648,7.458,7.458\n\t\t\tc-9.943,9.941-9.943,26.119,0,36.062c4.809,4.809,11.212,7.456,18.031,7.458c0,0,0.001,0,0.002,0\n\t\t\tc6.816,0,13.221-2.648,18.029-7.458c4.809-4.809,7.457-11.212,7.457-18.03C50.977,18.67,48.328,12.266,43.52,7.458z\n\t\t\t M42.106,42.105c-4.432,4.431-10.332,6.872-16.615,6.872h-0.002c-6.285-0.001-12.187-2.441-16.617-6.872\n\t\t\tc-9.162-9.163-9.162-24.071,0-33.233C13.303,4.44,19.204,2,25.489,2c6.284,0,12.186,2.44,16.617,6.872\n\t\t\tc4.431,4.431,6.871,10.332,6.871,16.617C48.977,31.772,46.536,37.675,42.106,42.105z"/>\n\t\t<path d="M23.578,32.218c-0.023-1.734,0.143-3.059,0.496-3.972c0.353-0.913,1.11-1.997,2.272-3.253\n\t\t\tc0.468-0.536,0.923-1.062,1.367-1.575c0.626-0.753,1.104-1.478,1.436-2.175c0.331-0.707,0.495-1.541,0.495-2.5\n\t\t\tc0-1.096-0.26-2.088-0.779-2.979c-0.565-0.879-1.501-1.336-2.806-1.369c-1.802,0.057-2.985,0.667-3.55,1.832\n\t\t\tc-0.301,0.535-0.503,1.141-0.607,1.814c-0.139,0.707-0.207,1.432-0.207,2.174h-2.937c-0.091-2.208,0.407-4.114,1.493-5.719\n\t\t\tc1.062-1.64,2.855-2.481,5.378-2.527c2.16,0.023,3.874,0.608,5.141,1.758c1.278,1.16,1.929,2.764,1.95,4.811\n\t\t\tc0,1.142-0.137,2.111-0.41,2.911c-0.309,0.845-0.731,1.593-1.268,2.243c-0.492,0.65-1.068,1.318-1.73,2.002\n\t\t\tc-0.65,0.697-1.313,1.479-1.987,2.346c-0.239,0.377-0.429,0.777-0.565,1.199c-0.16,0.959-0.217,1.951-0.171,2.979\n\t\t\tC26.589,32.218,23.578,32.218,23.578,32.218z M23.578,38.22v-3.484h3.076v3.484H23.578z"/>\n\t</g>\n</svg>\n'}),we=new b({name:"ui-components:inspector",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24">\n  <path class="jp-inspector-icon-color jp-icon-selectable" fill="#616161" d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm-5 14H4v-4h11v4zm0-5H4V9h11v4zm5 5h-4V9h4v9z"/>\n</svg>\n'}),be=new b({name:"ui-components:json",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 22 22">\n  <g class="jp-json-icon-color jp-icon-selectable" fill="#F9A825">\n    <path d="M20.2 11.8c-1.6 0-1.7.5-1.7 1 0 .4.1.9.1 1.3.1.5.1.9.1 1.3 0 1.7-1.4 2.3-3.5 2.3h-.9v-1.9h.5c1.1 0 1.4 0 1.4-.8 0-.3 0-.6-.1-1 0-.4-.1-.8-.1-1.2 0-1.3 0-1.8 1.3-2-1.3-.2-1.3-.7-1.3-2 0-.4.1-.8.1-1.2.1-.4.1-.7.1-1 0-.8-.4-.7-1.4-.8h-.5V4.1h.9c2.2 0 3.5.7 3.5 2.3 0 .4-.1.9-.1 1.3-.1.5-.1.9-.1 1.3 0 .5.2 1 1.7 1v1.8zM1.8 10.1c1.6 0 1.7-.5 1.7-1 0-.4-.1-.9-.1-1.3-.1-.5-.1-.9-.1-1.3 0-1.6 1.4-2.3 3.5-2.3h.9v1.9h-.5c-1 0-1.4 0-1.4.8 0 .3 0 .6.1 1 0 .2.1.6.1 1 0 1.3 0 1.8-1.3 2C6 11.2 6 11.7 6 13c0 .4-.1.8-.1 1.2-.1.3-.1.7-.1 1 0 .8.3.8 1.4.8h.5v1.9h-.9c-2.1 0-3.5-.6-3.5-2.3 0-.4.1-.9.1-1.3.1-.5.1-.9.1-1.3 0-.5-.2-1-1.7-1v-1.9z"/>\n    <circle cx="11" cy="13.8" r="2.1"/>\n    <circle cx="11" cy="8.2" r="2.1"/>\n  </g>\n</svg>\n'}),ye=new b({name:"ui-components:julia",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 325 300">\n  <g class="jp-brand0 jp-icon-selectable" fill="#cb3c33">\n    <path d="M 150.898438 225 C 150.898438 266.421875 117.320312 300 75.898438 300 C 34.476562 300 0.898438 266.421875 0.898438 225 C 0.898438 183.578125 34.476562 150 75.898438 150 C 117.320312 150 150.898438 183.578125 150.898438 225"/>\n  </g>\n  <g class="jp-brand0 jp-icon-selectable" fill="#389826">\n    <path d="M 237.5 75 C 237.5 116.421875 203.921875 150 162.5 150 C 121.078125 150 87.5 116.421875 87.5 75 C 87.5 33.578125 121.078125 0 162.5 0 C 203.921875 0 237.5 33.578125 237.5 75"/>\n  </g>\n  <g class="jp-brand0 jp-icon-selectable" fill="#9558b2">\n    <path d="M 324.101562 225 C 324.101562 266.421875 290.523438 300 249.101562 300 C 207.679688 300 174.101562 266.421875 174.101562 225 C 174.101562 183.578125 207.679688 150 249.101562 150 C 290.523438 150 324.101562 183.578125 324.101562 225"/>\n  </g>\n</svg>\n'}),xe=new b({name:"ui-components:jupyter-favicon",svgstr:'<svg width="152" height="165" viewBox="0 0 152 165" version="1.1" xmlns="http://www.w3.org/2000/svg">\n   <g class="jp-jupyter-icon-color" fill="#F37726">\n    <path transform="translate(0.078947, 110.582927)" d="M75.9422842,29.5804561 C43.3023947,29.5804561 14.7967832,17.6534634 0,0 C5.51083211,15.8406829 15.7815389,29.5667732 29.3904947,39.2784171 C42.9997,48.9898537 59.2737,54.2067805 75.9605789,54.2067805 C92.6474579,54.2067805 108.921458,48.9898537 122.530663,39.2784171 C136.139453,29.5667732 146.410284,15.8406829 151.921158,0 C137.087868,17.6534634 108.582589,29.5804561 75.9422842,29.5804561 L75.9422842,29.5804561 Z" />\n    <path transform="translate(0.037368, 0.704878)" d="M75.9784579,24.6264073 C108.618763,24.6264073 137.124458,36.5534415 151.921158,54.2067805 C146.410284,38.366222 136.139453,24.6401317 122.530663,14.9284878 C108.921458,5.2168439 92.6474579,0 75.9605789,0 C59.2737,0 42.9997,5.2168439 29.3904947,14.9284878 C15.7815389,24.6401317 5.51083211,38.366222 0,54.2067805 C14.8330816,36.5899293 43.3385684,24.6264073 75.9784579,24.6264073 L75.9784579,24.6264073 Z" />\n  </g>\n</svg>\n'}),Ce=new b({name:"ui-components:jupyter",svgstr:'<svg width="39" height="51" viewBox="0 0 39 51" xmlns="http://www.w3.org/2000/svg">\n  <g transform="translate(-1638 -2281)">\n     <g class="jp-jupyter-icon-color" fill="#F37726">\n      <path transform="translate(1639.74 2311.98)" d="M 18.2646 7.13411C 10.4145 7.13411 3.55872 4.2576 0 0C 1.32539 3.8204 3.79556 7.13081 7.0686 9.47303C 10.3417 11.8152 14.2557 13.0734 18.269 13.0734C 22.2823 13.0734 26.1963 11.8152 29.4694 9.47303C 32.7424 7.13081 35.2126 3.8204 36.538 0C 32.9705 4.2576 26.1148 7.13411 18.2646 7.13411Z"/>\n      <path transform="translate(1639.73 2285.48)" d="M 18.2733 5.93931C 26.1235 5.93931 32.9793 8.81583 36.538 13.0734C 35.2126 9.25303 32.7424 5.94262 29.4694 3.6004C 26.1963 1.25818 22.2823 0 18.269 0C 14.2557 0 10.3417 1.25818 7.0686 3.6004C 3.79556 5.94262 1.32539 9.25303 0 13.0734C 3.56745 8.82463 10.4232 5.93931 18.2733 5.93931Z"/>\n    </g>\n    <g class="jp-icon3" fill="#616161">\n      <path transform="translate(1669.3 2281.31)" d="M 5.89353 2.844C 5.91889 3.43165 5.77085 4.01367 5.46815 4.51645C 5.16545 5.01922 4.72168 5.42015 4.19299 5.66851C 3.6643 5.91688 3.07444 6.00151 2.49805 5.91171C 1.92166 5.8219 1.38463 5.5617 0.954898 5.16401C 0.52517 4.76633 0.222056 4.24903 0.0839037 3.67757C -0.0542483 3.10611 -0.02123 2.50617 0.178781 1.95364C 0.378793 1.4011 0.736809 0.920817 1.20754 0.573538C 1.67826 0.226259 2.24055 0.0275919 2.82326 0.00267229C 3.60389 -0.0307115 4.36573 0.249789 4.94142 0.782551C 5.51711 1.31531 5.85956 2.05676 5.89353 2.844Z"/>\n      <path transform="translate(1639.8 2323.81)" d="M 7.42789 3.58338C 7.46008 4.3243 7.27355 5.05819 6.89193 5.69213C 6.51031 6.32607 5.95075 6.83156 5.28411 7.1446C 4.61747 7.45763 3.87371 7.56414 3.14702 7.45063C 2.42032 7.33712 1.74336 7.0087 1.20184 6.50695C 0.660328 6.0052 0.27861 5.35268 0.105017 4.63202C -0.0685757 3.91135 -0.0262361 3.15494 0.226675 2.45856C 0.479587 1.76217 0.931697 1.15713 1.52576 0.720033C 2.11983 0.282935 2.82914 0.0334395 3.56389 0.00313344C 4.54667 -0.0374033 5.50529 0.316706 6.22961 0.987835C 6.95393 1.65896 7.38484 2.59235 7.42789 3.58338L 7.42789 3.58338Z"/>\n      <path transform="translate(1638.36 2286.06)" d="M 2.27471 4.39629C 1.84363 4.41508 1.41671 4.30445 1.04799 4.07843C 0.679268 3.8524 0.385328 3.52114 0.203371 3.12656C 0.0214136 2.73198 -0.0403798 2.29183 0.0258116 1.86181C 0.0920031 1.4318 0.283204 1.03126 0.575213 0.710883C 0.867222 0.39051 1.24691 0.164708 1.66622 0.0620592C 2.08553 -0.0405897 2.52561 -0.0154714 2.93076 0.134235C 3.33591 0.283941 3.68792 0.551505 3.94222 0.90306C 4.19652 1.25462 4.34169 1.67436 4.35935 2.10916C 4.38299 2.69107 4.17678 3.25869 3.78597 3.68746C 3.39516 4.11624 2.85166 4.37116 2.27471 4.39629L 2.27471 4.39629Z"/>\n    </g>\n  </g>>\n</svg>\n'}),Se=new b({name:"ui-components:jupyterlab-wordmark",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="200" viewBox="0 0 1860.8 475">\n  <g class="jp-icon2" fill="#4E4E4E" transform="translate(480.136401, 64.271493)">\n    <g transform="translate(0.000000, 58.875566)">\n      <g transform="translate(0.087603, 0.140294)">\n        <path d="M-426.9,169.8c0,48.7-3.7,64.7-13.6,76.4c-10.8,10-25,15.5-39.7,15.5l3.7,29 c22.8,0.3,44.8-7.9,61.9-23.1c17.8-18.5,24-44.1,24-83.3V0H-427v170.1L-426.9,169.8L-426.9,169.8z"/>\n      </g>\n    </g>\n    <g transform="translate(155.045296, 56.837104)">\n      <g transform="translate(1.562453, 1.799842)">\n        <path d="M-312,148c0,21,0,39.5,1.7,55.4h-31.8l-2.1-33.3h-0.8c-6.7,11.6-16.4,21.3-28,27.9 c-11.6,6.6-24.8,10-38.2,9.8c-31.4,0-69-17.7-69-89V0h36.4v112.7c0,38.7,11.6,64.7,44.6,64.7c10.3-0.2,20.4-3.5,28.9-9.4 c8.5-5.9,15.1-14.3,18.9-23.9c2.2-6.1,3.3-12.5,3.3-18.9V0.2h36.4V148H-312L-312,148z"/>\n      </g>\n    </g>\n    <g transform="translate(390.013322, 53.479638)">\n      <g transform="translate(1.706458, 0.231425)">\n        <path d="M-478.6,71.4c0-26-0.8-47-1.7-66.7h32.7l1.7,34.8h0.8c7.1-12.5,17.5-22.8,30.1-29.7 c12.5-7,26.7-10.3,41-9.8c48.3,0,84.7,41.7,84.7,103.3c0,73.1-43.7,109.2-91,109.2c-12.1,0.5-24.2-2.2-35-7.8 c-10.8-5.6-19.9-13.9-26.6-24.2h-0.8V291h-36v-220L-478.6,71.4L-478.6,71.4z M-442.6,125.6c0.1,5.1,0.6,10.1,1.7,15.1 c3,12.3,9.9,23.3,19.8,31.1c9.9,7.8,22.1,12.1,34.7,12.1c38.5,0,60.7-31.9,60.7-78.5c0-40.7-21.1-75.6-59.5-75.6 c-12.9,0.4-25.3,5.1-35.3,13.4c-9.9,8.3-16.9,19.7-19.6,32.4c-1.5,4.9-2.3,10-2.5,15.1V125.6L-442.6,125.6L-442.6,125.6z"/>\n      </g>\n    </g>\n    <g transform="translate(606.740726, 56.837104)">\n      <g transform="translate(0.751226, 1.989299)">\n        <path d="M-440.8,0l43.7,120.1c4.5,13.4,9.5,29.4,12.8,41.7h0.8c3.7-12.2,7.9-27.7,12.8-42.4 l39.7-119.2h38.5L-346.9,145c-26,69.7-43.7,105.4-68.6,127.2c-12.5,11.7-27.9,20-44.6,23.9l-9.1-31.1 c11.7-3.9,22.5-10.1,31.8-18.1c13.2-11.1,23.7-25.2,30.6-41.2c1.5-2.8,2.5-5.7,2.9-8.8c-0.3-3.3-1.2-6.6-2.5-9.7L-480.2,0.1 h39.7L-440.8,0L-440.8,0z"/>\n      </g>\n    </g>\n    <g transform="translate(822.748104, 0.000000)">\n      <g transform="translate(1.464050, 0.378914)">\n        <path d="M-413.7,0v58.3h52v28.2h-52V196c0,25,7,39.5,27.3,39.5c7.1,0.1,14.2-0.7,21.1-2.5 l1.7,27.7c-10.3,3.7-21.3,5.4-32.2,5c-7.3,0.4-14.6-0.7-21.3-3.4c-6.8-2.7-12.9-6.8-17.9-12.1c-10.3-10.9-14.1-29-14.1-52.9 V86.5h-31V58.3h31V9.6L-413.7,0L-413.7,0z"/>\n      </g>\n    </g>\n    <g transform="translate(974.433286, 53.479638)">\n      <g transform="translate(0.990034, 0.610339)">\n        <path d="M-445.8,113c0.8,50,32.2,70.6,68.6,70.6c19,0.6,37.9-3,55.3-10.5l6.2,26.4 c-20.9,8.9-43.5,13.1-66.2,12.6c-61.5,0-98.3-41.2-98.3-102.5C-480.2,48.2-444.7,0-386.5,0c65.2,0,82.7,58.3,82.7,95.7 c-0.1,5.8-0.5,11.5-1.2,17.2h-140.6H-445.8L-445.8,113z M-339.2,86.6c0.4-23.5-9.5-60.1-50.4-60.1 c-36.8,0-52.8,34.4-55.7,60.1H-339.2L-339.2,86.6L-339.2,86.6z"/>\n      </g>\n    </g>\n    <g transform="translate(1201.961058, 53.479638)">\n      <g transform="translate(1.179640, 0.705068)">\n        <path d="M-478.6,68c0-23.9-0.4-44.5-1.7-63.4h31.8l1.2,39.9h1.7c9.1-27.3,31-44.5,55.3-44.5 c3.5-0.1,7,0.4,10.3,1.2v34.8c-4.1-0.9-8.2-1.3-12.4-1.2c-25.6,0-43.7,19.7-48.7,47.4c-1,5.7-1.6,11.5-1.7,17.2v108.3h-36V68 L-478.6,68z"/>\n      </g>\n    </g>\n  </g>\n\n  <g class="jp-icon-warn0" fill="#F37726">\n    <path d="M1352.3,326.2h37V28h-37V326.2z M1604.8,326.2c-2.5-13.9-3.4-31.1-3.4-48.7v-76 c0-40.7-15.1-83.1-77.3-83.1c-25.6,0-50,7.1-66.8,18.1l8.4,24.4c14.3-9.2,34-15.1,53-15.1c41.6,0,46.2,30.2,46.2,47v4.2 c-78.6-0.4-122.3,26.5-122.3,75.6c0,29.4,21,58.4,62.2,58.4c29,0,50.9-14.3,62.2-30.2h1.3l2.9,25.6H1604.8z M1565.7,257.7 c0,3.8-0.8,8-2.1,11.8c-5.9,17.2-22.7,34-49.2,34c-18.9,0-34.9-11.3-34.9-35.3c0-39.5,45.8-46.6,86.2-45.8V257.7z M1698.5,326.2 l1.7-33.6h1.3c15.1,26.9,38.7,38.2,68.1,38.2c45.4,0,91.2-36.1,91.2-108.8c0.4-61.7-35.3-103.7-85.7-103.7 c-32.8,0-56.3,14.7-69.3,37.4h-0.8V28h-36.6v245.7c0,18.1-0.8,38.6-1.7,52.5H1698.5z M1704.8,208.2c0-5.9,1.3-10.9,2.1-15.1 c7.6-28.1,31.1-45.4,56.3-45.4c39.5,0,60.5,34.9,60.5,75.6c0,46.6-23.1,78.1-61.8,78.1c-26.9,0-48.3-17.6-55.5-43.3 c-0.8-4.2-1.7-8.8-1.7-13.4V208.2z"/>\n  </g>\n</svg>\n'}),_e=new b({name:"ui-components:kernel",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24">\n    <path class="jp-icon2" fill="#616161" d="M15 9H9v6h6V9zm-2 4h-2v-2h2v2zm8-2V9h-2V7c0-1.1-.9-2-2-2h-2V3h-2v2h-2V3H9v2H7c-1.1 0-2 .9-2 2v2H3v2h2v2H3v2h2v2c0 1.1.9 2 2 2h2v2h2v-2h2v2h2v-2h2c1.1 0 2-.9 2-2v-2h2v-2h-2v-2h2zm-4 6H7V7h10v10z"/>\n</svg>\n'}),ke=new b({name:"ui-components:keyboard",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24">\n  <path class="jp-icon3 jp-icon-selectable" fill="#616161" d="M20 5H4c-1.1 0-1.99.9-1.99 2L2 17c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm-9 3h2v2h-2V8zm0 3h2v2h-2v-2zM8 8h2v2H8V8zm0 3h2v2H8v-2zm-1 2H5v-2h2v2zm0-3H5V8h2v2zm9 7H8v-2h8v2zm0-4h-2v-2h2v2zm0-3h-2V8h2v2zm3 3h-2v-2h2v2zm0-3h-2V8h2v2z"/>\n</svg>\n'}),Fe=new b({name:"ui-components:launch",svgstr:'<svg viewBox="0 0 32 32" width="32" xmlns="http://www.w3.org/2000/svg">\n  <g class="jp-icon3 jp-icon-selectable" fill="#616161">\n    <path d="M26,28H6a2.0027,2.0027,0,0,1-2-2V6A2.0027,2.0027,0,0,1,6,4H16V6H6V26H26V16h2V26A2.0027,2.0027,0,0,1,26,28Z"/>\n    <polygon points="20 2 20 4 26.586 4 18 12.586 19.414 14 28 5.414 28 12 30 12 30 2 20 2"/>\n  </g>\n</svg>\n'}),je=new b({name:"ui-components:launcher",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24">\n  <path class="jp-icon3 jp-icon-selectable" fill="#616161" d="M19 19H5V5h7V3H5a2 2 0 00-2 2v14a2 2 0 002 2h14c1.1 0 2-.9 2-2v-7h-2v7zM14 3v2h3.59l-9.83 9.83 1.41 1.41L19 6.41V10h2V3h-7z"/>\n</svg>\n'}),Me=new b({name:"ui-components:line-form",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24">\n    <path fill="white" d="M5.88 4.12L13.76 12l-7.88 7.88L8 22l10-10L8 2z"/>\n</svg>\n'}),Ie=new b({name:"ui-components:link",svgstr:'<svg viewBox="0 0 24 24" width="16" xmlns="http://www.w3.org/2000/svg">\n  <g class="jp-icon3" fill="#616161">\n    <path d="M3.9 12c0-1.71 1.39-3.1 3.1-3.1h4V7H7c-2.76 0-5 2.24-5 5s2.24 5 5 5h4v-1.9H7c-1.71 0-3.1-1.39-3.1-3.1zM8 13h8v-2H8v2zm9-6h-4v1.9h4c1.71 0 3.1 1.39 3.1 3.1s-1.39 3.1-3.1 3.1h-4V17h4c2.76 0 5-2.24 5-5s-2.24-5-5-5z"/>\n  </g>\n</svg>\n'}),Te=new b({name:"ui-components:list",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24">\n    <path class="jp-icon2 jp-icon-selectable" fill="#616161" d="M19 5v14H5V5h14m1.1-2H3.9c-.5 0-.9.4-.9.9v16.2c0 .4.4.9.9.9h16.2c.4 0 .9-.5.9-.9V3.9c0-.5-.5-.9-.9-.9zM11 7h6v2h-6V7zm0 4h6v2h-6v-2zm0 4h6v2h-6zM7 7h2v2H7zm0 4h2v2H7zm0 4h2v2H7z"/>\n</svg>\n'}),Le=new b({name:"ui-components:lock",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 23">\n  <path class="jp-icon4" fill="#333333" d="M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z" />\n</svg>\n'}),Ve=new b({name:"ui-components:markdown",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 22 22">\n  <path class="jp-icon-contrast0 jp-icon-selectable" fill="#7B1FA2" d="M5 14.9h12l-6.1 6zm9.4-6.8c0-1.3-.1-2.9-.1-4.5-.4 1.4-.9 2.9-1.3 4.3l-1.3 4.3h-2L8.5 7.9c-.4-1.3-.7-2.9-1-4.3-.1 1.6-.1 3.2-.2 4.6L7 12.4H4.8l.7-11h3.3L10 5c.4 1.2.7 2.7 1 3.9.3-1.2.7-2.6 1-3.9l1.2-3.7h3.3l.6 11h-2.4l-.3-4.2z"/>\n</svg>\n'}),De=new b({name:"ui-components:mermaid",svgstr:'<svg width="16" version="1.1" viewBox="0 0 491 675" xmlns="http://www.w3.org/2000/svg">\n <g class="jp-icon-contrast2 jp-icon-selectable" fill="#ff3670">\n  <path d="m85 92c-46 0-85 37-85 85v321c0 46 37 85 85 85h321c46 0 85-37 85-85v-321c0-46-37-85-85-85zm-2 111c72-3.1 139 41 162 109 25-67 91-112 162-109 2.4 57-25 111-72 144-24 16-39 44-39 74v51h-104v-51c0.08-29-15-57-39-74-47-32-75-86-72-144z"/>\n </g>\n</svg>\n'}),Ee=new b({name:"ui-components:move-down",svgstr:'<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">\n<path class="jp-icon3" d="M12.471 7.52899C12.7632 7.23684 12.7632 6.76316 12.471 6.47101V6.47101C12.179 6.17905 11.7057 6.17884 11.4135 6.47054L7.75 10.1275V1.75C7.75 1.33579 7.41421 1 7 1V1C6.58579 1 6.25 1.33579 6.25 1.75V10.1275L2.59726 6.46822C2.30338 6.17381 1.82641 6.17359 1.53226 6.46774V6.46774C1.2383 6.7617 1.2383 7.2383 1.53226 7.53226L6.29289 12.2929C6.68342 12.6834 7.31658 12.6834 7.70711 12.2929L12.471 7.52899Z" fill="#616161"/>\n</svg>\n'}),Oe=new b({name:"ui-components:move-up",svgstr:'<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">\n<path class="jp-icon3" d="M1.52899 6.47101C1.23684 6.76316 1.23684 7.23684 1.52899 7.52899V7.52899C1.82095 7.82095 2.29426 7.82116 2.58649 7.52946L6.25 3.8725V12.25C6.25 12.6642 6.58579 13 7 13V13C7.41421 13 7.75 12.6642 7.75 12.25V3.8725L11.4027 7.53178C11.6966 7.82619 12.1736 7.82641 12.4677 7.53226V7.53226C12.7617 7.2383 12.7617 6.7617 12.4677 6.46774L7.70711 1.70711C7.31658 1.31658 6.68342 1.31658 6.29289 1.70711L1.52899 6.47101Z" fill="#616161"/>\n</svg>\n'}),$e=new b({name:"ui-components:new-folder",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24">\n  <g class="jp-icon3" fill="#616161">\n    <path d="M20 6h-8l-2-2H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2zm-1 8h-3v3h-2v-3h-3v-2h3V9h2v3h3v2z"/>\n  </g>\n</svg>\n'}),Ae=new b({name:"ui-components:not-trusted",svgstr:'<svg fill="none" xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 25 25">\n    <path class="jp-icon2" stroke="#333333" stroke-width="2" transform="translate(3 3)" d="M1.86094 11.4409C0.826448 8.77027 0.863779 6.05764 1.24907 4.19932C2.48206 3.93347 4.08068 3.40347 5.60102 2.8449C7.23549 2.2444 8.85666 1.5815 9.9876 1.09539C11.0597 1.58341 12.6094 2.2444 14.218 2.84339C15.7503 3.41394 17.3995 3.95258 18.7539 4.21385C19.1364 6.07177 19.1709 8.77722 18.139 11.4409C17.0303 14.3032 14.6668 17.1844 9.99999 18.9354C5.33319 17.1844 2.96968 14.3032 1.86094 11.4409Z"/>\n    <path class="jp-icon2" stroke="#333333" stroke-width="2" transform="translate(9.31592 9.32031)" d="M7.36842 0L0 7.36479"/>\n    <path class="jp-icon2" stroke="#333333" stroke-width="2" transform="translate(9.31592 16.6836) scale(1 -1)" d="M7.36842 0L0 7.36479"/>\n</svg>\n'}),Be=new b({name:"ui-components:notebook",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 22 22">\n  <g class="jp-notebook-icon-color jp-icon-selectable" fill="#EF6C00">\n    <path d="M18.7 3.3v15.4H3.3V3.3h15.4m1.5-1.5H1.8v18.3h18.3l.1-18.3z"/>\n    <path d="M16.5 16.5l-5.4-4.3-5.6 4.3v-11h11z"/>\n  </g>\n</svg>\n'}),Ne=new b({name:"ui-components:numbering",svgstr:'<svg width="22" height="22" viewBox="0 0 28 28" xmlns="http://www.w3.org/2000/svg">\n\t<g class="jp-icon3" fill="#616161">\n\t\t<path d="M4 19H6V19.5H5V20.5H6V21H4V22H7V18H4V19ZM5 10H6V6H4V7H5V10ZM4 13H5.8L4 15.1V16H7V15H5.2L7 12.9V12H4V13ZM9 7V9H23V7H9ZM9 21H23V19H9V21ZM9 15H23V13H9V15Z"/>\n\t</g>\n</svg>\n'}),Re=new b({name:"ui-components:offline-bolt",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16">\n  <g class="jp-icon3" fill="#616161">\n    <path d="M12 2.02c-5.51 0-9.98 4.47-9.98 9.98s4.47 9.98 9.98 9.98 9.98-4.47 9.98-9.98S17.51 2.02 12 2.02zM11.48 20v-6.26H8L13 4v6.26h3.35L11.48 20z"/>\n  </g>\n</svg>\n'}),ze=new b({name:"ui-components:palette",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24">\n  <g class="jp-icon3" fill="#616161">\n    <path d="M18 13V20H4V6H9.02C9.07 5.29 9.24 4.62 9.5 4H4C2.9 4 2 4.9 2 6V20C2 21.1 2.9 22 4 22H18C19.1 22 20 21.1 20 20V15L18 13ZM19.3 8.89C19.74 8.19 20 7.38 20 6.5C20 4.01 17.99 2 15.5 2C13.01 2 11 4.01 11 6.5C11 8.99 13.01 11 15.49 11C16.37 11 17.19 10.74 17.88 10.3L21 13.42L22.42 12L19.3 8.89ZM15.5 9C14.12 9 13 7.88 13 6.5C13 5.12 14.12 4 15.5 4C16.88 4 18 5.12 18 6.5C18 7.88 16.88 9 15.5 9Z"/>\n    <path fill-rule="evenodd" clip-rule="evenodd" d="M4 6H9.01894C9.00639 6.16502 9 6.33176 9 6.5C9 8.81577 10.211 10.8487 12.0343 12H9V14H16V12.9811C16.5703 12.9377 17.12 12.8207 17.6396 12.6396L18 13V20H4V6ZM8 8H6V10H8V8ZM6 12H8V14H6V12ZM8 16H6V18H8V16ZM9 16H16V18H9V16Z"/>\n  </g>\n</svg>\n'}),Pe=new b({name:"ui-components:paste",svgstr:'<svg height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">\n    <g class="jp-icon3" fill="#616161">\n        <path d="M19 2h-4.18C14.4.84 13.3 0 12 0c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm7 18H5V4h2v3h10V4h2v16z"/>\n    </g>\n</svg>\n'}),He=new b({name:"ui-components:pdf",svgstr:'<svg\n   xmlns="http://www.w3.org/2000/svg" viewBox="0 0 22 22" width="16">\n    <path transform="rotate(45)" class="jp-icon-selectable" fill="#FF2A2A"\n       d="m 22.344369,-3.0163642 h 5.638604 v 1.5792433 h -3.549227 v 1.50869299 h 3.337576 V 1.6508154 h -3.337576 v 3.4352613 h -2.089377 z m -7.136444,1.5792433 v 4.9439543 h 0.74892 q 1.280761,0 1.953703,-0.6349535 0.678369,-0.6349535 0.678369,-1.8451641 0,-1.20478355 -0.672942,-1.83431011 -0.672942,-0.62952659 -1.95913,-0.62952659 z m -2.089377,-1.5792433 h 2.203343 q 1.845164,0 2.746039,0.2659207 0.906301,0.2604937 1.552108,0.8900203 0.56983,0.5481223 0.846605,1.26448006 0.276774,0.71635781 0.276774,1.62265894 0,0.9171551 -0.276774,1.6389399 -0.276775,0.7163578 -0.846605,1.26448 -0.651234,0.6295266 -1.562962,0.8954473 -0.911728,0.2604937 -2.735185,0.2604937 h -2.203343 z m -8.1458565,0 h 3.467823 q 1.5466816,0 2.3715785,0.689223 0.830324,0.6837961 0.830324,1.95370314 0,1.27533397 -0.830324,1.96455706 Q 9.9871961,2.274915 8.4405145,2.274915 H 7.0620684 V 5.0860767 H 4.9726915 Z m 2.0893769,1.5141199 v 2.26303943 h 1.155941 q 0.6078188,0 0.9388629,-0.29305547 0.3310441,-0.29848241 0.3310441,-0.84117772 0,-0.54269531 -0.3310441,-0.83575074 -0.3310441,-0.2930555 -0.9388629,-0.2930555 z"\n/>\n</svg>\n'}),Ue=new b({name:"ui-components:python",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="-10 -10 131.16136169433594 132.38899993896484">\n  <path class="jp-icon-selectable" fill="#306998" d="M 54.918785,9.1927421e-4 C 50.335132,0.02221727 45.957846,0.41313697 42.106285,1.0946693 30.760069,3.0991731 28.700036,7.2947714 28.700035,15.032169 v 10.21875 h 26.8125 v 3.40625 h -26.8125 -10.0625 c -7.792459,0 -14.6157588,4.683717 -16.7499998,13.59375 -2.46181998,10.212966 -2.57101508,16.586023 0,27.25 1.9059283,7.937852 6.4575432,13.593748 14.2499998,13.59375 h 9.21875 v -12.25 c 0,-8.849902 7.657144,-16.656248 16.75,-16.65625 h 26.78125 c 7.454951,0 13.406253,-6.138164 13.40625,-13.625 v -25.53125 c 0,-7.2663386 -6.12998,-12.7247771 -13.40625,-13.9374997 C 64.281548,0.32794397 59.502438,-0.02037903 54.918785,9.1927421e-4 Z m -14.5,8.21875012579 c 2.769547,0 5.03125,2.2986456 5.03125,5.1249996 -2e-6,2.816336 -2.261703,5.09375 -5.03125,5.09375 -2.779476,-1e-6 -5.03125,-2.277415 -5.03125,-5.09375 -10e-7,-2.826353 2.251774,-5.1249996 5.03125,-5.1249996 z"/>\n  <path class="jp-icon-selectable" fill="#ffd43b" d="m 85.637535,28.657169 v 11.90625 c 0,9.230755 -7.825895,16.999999 -16.75,17 h -26.78125 c -7.335833,0 -13.406249,6.278483 -13.40625,13.625 v 25.531247 c 0,7.266344 6.318588,11.540324 13.40625,13.625004 8.487331,2.49561 16.626237,2.94663 26.78125,0 6.750155,-1.95439 13.406253,-5.88761 13.40625,-13.625004 V 86.500919 h -26.78125 v -3.40625 h 26.78125 13.406254 c 7.792461,0 10.696251,-5.435408 13.406241,-13.59375 2.79933,-8.398886 2.68022,-16.475776 0,-27.25 -1.92578,-7.757441 -5.60387,-13.59375 -13.406241,-13.59375 z m -15.0625,64.65625 c 2.779478,3e-6 5.03125,2.277417 5.03125,5.093747 -2e-6,2.826354 -2.251775,5.125004 -5.03125,5.125004 -2.76955,0 -5.03125,-2.29865 -5.03125,-5.125004 2e-6,-2.81633 2.261697,-5.093747 5.03125,-5.093747 z"/>\n</svg>\n'}),We=new b({name:"ui-components:r-kernel",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 22 22">\n  <path class="jp-icon-contrast3 jp-icon-selectable" fill="#2196F3" d="M4.4 2.5c1.2-.1 2.9-.3 4.9-.3 2.5 0 4.1.4 5.2 1.3 1 .7 1.5 1.9 1.5 3.5 0 2-1.4 3.5-2.9 4.1 1.2.4 1.7 1.6 2.2 3 .6 1.9 1 3.9 1.3 4.6h-3.8c-.3-.4-.8-1.7-1.2-3.7s-1.2-2.6-2.6-2.6h-.9v6.4H4.4V2.5zm3.7 6.9h1.4c1.9 0 2.9-.9 2.9-2.3s-1-2.3-2.8-2.3c-.7 0-1.3 0-1.6.2v4.5h.1v-.1z"/>\n</svg>\n'}),qe=new b({name:"ui-components:react",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="150 150 541.9 295.3">\n  <g class="jp-icon-brand2 jp-icon-selectable" fill="#61DAFB">\n    <path d="M666.3 296.5c0-32.5-40.7-63.3-103.1-82.4 14.4-63.6 8-114.2-20.2-130.4-6.5-3.8-14.1-5.6-22.4-5.6v22.3c4.6 0 8.3.9 11.4 2.6 13.6 7.8 19.5 37.5 14.9 75.7-1.1 9.4-2.9 19.3-5.1 29.4-19.6-4.8-41-8.5-63.5-10.9-13.5-18.5-27.5-35.3-41.6-50 32.6-30.3 63.2-46.9 84-46.9V78c-27.5 0-63.5 19.6-99.9 53.6-36.4-33.8-72.4-53.2-99.9-53.2v22.3c20.7 0 51.4 16.5 84 46.6-14 14.7-28 31.4-41.3 49.9-22.6 2.4-44 6.1-63.6 11-2.3-10-4-19.7-5.2-29-4.7-38.2 1.1-67.9 14.6-75.8 3-1.8 6.9-2.6 11.5-2.6V78.5c-8.4 0-16 1.8-22.6 5.6-28.1 16.2-34.4 66.7-19.9 130.1-62.2 19.2-102.7 49.9-102.7 82.3 0 32.5 40.7 63.3 103.1 82.4-14.4 63.6-8 114.2 20.2 130.4 6.5 3.8 14.1 5.6 22.5 5.6 27.5 0 63.5-19.6 99.9-53.6 36.4 33.8 72.4 53.2 99.9 53.2 8.4 0 16-1.8 22.6-5.6 28.1-16.2 34.4-66.7 19.9-130.1 62-19.1 102.5-49.9 102.5-82.3zm-130.2-66.7c-3.7 12.9-8.3 26.2-13.5 39.5-4.1-8-8.4-16-13.1-24-4.6-8-9.5-15.8-14.4-23.4 14.2 2.1 27.9 4.7 41 7.9zm-45.8 106.5c-7.8 13.5-15.8 26.3-24.1 38.2-14.9 1.3-30 2-45.2 2-15.1 0-30.2-.7-45-1.9-8.3-11.9-16.4-24.6-24.2-38-7.6-13.1-14.5-26.4-20.8-39.8 6.2-13.4 13.2-26.8 20.7-39.9 7.8-13.5 15.8-26.3 24.1-38.2 14.9-1.3 30-2 45.2-2 15.1 0 30.2.7 45 1.9 8.3 11.9 16.4 24.6 24.2 38 7.6 13.1 14.5 26.4 20.8 39.8-6.3 13.4-13.2 26.8-20.7 39.9zm32.3-13c5.4 13.4 10 26.8 13.8 39.8-13.1 3.2-26.9 5.9-41.2 8 4.9-7.7 9.8-15.6 14.4-23.7 4.6-8 8.9-16.1 13-24.1zM421.2 430c-9.3-9.6-18.6-20.3-27.8-32 9 .4 18.2.7 27.5.7 9.4 0 18.7-.2 27.8-.7-9 11.7-18.3 22.4-27.5 32zm-74.4-58.9c-14.2-2.1-27.9-4.7-41-7.9 3.7-12.9 8.3-26.2 13.5-39.5 4.1 8 8.4 16 13.1 24 4.7 8 9.5 15.8 14.4 23.4zM420.7 163c9.3 9.6 18.6 20.3 27.8 32-9-.4-18.2-.7-27.5-.7-9.4 0-18.7.2-27.8.7 9-11.7 18.3-22.4 27.5-32zm-74 58.9c-4.9 7.7-9.8 15.6-14.4 23.7-4.6 8-8.9 16-13 24-5.4-13.4-10-26.8-13.8-39.8 13.1-3.1 26.9-5.8 41.2-7.9zm-90.5 125.2c-35.4-15.1-58.3-34.9-58.3-50.6 0-15.7 22.9-35.6 58.3-50.6 8.6-3.7 18-7 27.7-10.1 5.7 19.6 13.2 40 22.5 60.9-9.2 20.8-16.6 41.1-22.2 60.6-9.9-3.1-19.3-6.5-28-10.2zM310 490c-13.6-7.8-19.5-37.5-14.9-75.7 1.1-9.4 2.9-19.3 5.1-29.4 19.6 4.8 41 8.5 63.5 10.9 13.5 18.5 27.5 35.3 41.6 50-32.6 30.3-63.2 46.9-84 46.9-4.5-.1-8.3-1-11.3-2.7zm237.2-76.2c4.7 38.2-1.1 67.9-14.6 75.8-3 1.8-6.9 2.6-11.5 2.6-20.7 0-51.4-16.5-84-46.6 14-14.7 28-31.4 41.3-49.9 22.6-2.4 44-6.1 63.6-11 2.3 10.1 4.1 19.8 5.2 29.1zm38.5-66.7c-8.6 3.7-18 7-27.7 10.1-5.7-19.6-13.2-40-22.5-60.9 9.2-20.8 16.6-41.1 22.2-60.6 9.9 3.1 19.3 6.5 28.1 10.2 35.4 15.1 58.3 34.9 58.3 50.6-.1 15.7-23 35.6-58.4 50.6zM320.8 78.4z"/>\n    <circle cx="420.9" cy="296.5" r="45.7"/>\n  </g>\n</svg>\n'}),Ge=new b({name:"ui-components:redo",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="16">\n  <g class="jp-icon3" fill="#616161">\n      <path d="M0 0h24v24H0z" fill="none"/><path d="M18.4 10.6C16.55 8.99 14.15 8 11.5 8c-4.65 0-8.58 3.03-9.96 7.22L3.9 16c1.05-3.19 4.05-5.5 7.6-5.5 1.95 0 3.73.72 5.12 1.88L13 16h9V7l-3.6 3.6z"/>\n  </g>\n</svg>\n'}),Ze=new b({name:"ui-components:refresh",svgstr:m}),Ke=new b({name:"ui-components:regex",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 20 20">\n  <g class="jp-icon2" fill="#414141">\n    <rect x="2" y="2" width="16" height="16"/>\n  </g>\n\n  <g class="jp-icon-accent2" fill="#FFF">\n    <circle class="st2" cx="5.5" cy="14.5" r="1.5"/>\n    <rect x="12" y="4" class="st2" width="1" height="8"/>\n    <rect x="8.5" y="7.5" transform="matrix(0.866 -0.5 0.5 0.866 -2.3255 7.3219)" class="st2" width="8" height="1"/>\n    <rect x="12" y="4" transform="matrix(0.5 -0.866 0.866 0.5 -0.6779 14.8252)" class="st2" width="1" height="8"/>\n  </g>\n</svg>\n'}),Ye=new b({name:"ui-components:run",svgstr:'<svg height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">\n    <g class="jp-icon3" fill="#616161">\n        <path d="M8 5v14l11-7z"/>\n    </g>\n</svg>\n'}),Je=new b({name:"ui-components:running",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 512 512">\n  <g class="jp-icon3" fill="#616161">\n    <path d="M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8zm96 328c0 8.8-7.2 16-16 16H176c-8.8 0-16-7.2-16-16V176c0-8.8 7.2-16 16-16h160c8.8 0 16 7.2 16 16v160z"/>\n  </g>\n</svg>\n'}),Xe=new b({name:"ui-components:save",svgstr:'<svg height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">\n    <g class="jp-icon3" fill="#616161">\n        <path d="M17 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V7l-4-4zm-5 16c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3zm3-10H5V5h10v4z"/>\n    </g>\n</svg>\n'}),Qe=new b({name:"ui-components:search",svgstr:'<svg viewBox="0 0 18 18" width="16" xmlns="http://www.w3.org/2000/svg">\n  <g class="jp-icon3" fill="#616161">\n    <path d="M12.1,10.9h-0.7l-0.2-0.2c0.8-0.9,1.3-2.2,1.3-3.5c0-3-2.4-5.4-5.4-5.4S1.8,4.2,1.8,7.1s2.4,5.4,5.4,5.4 c1.3,0,2.5-0.5,3.5-1.3l0.2,0.2v0.7l4.1,4.1l1.2-1.2L12.1,10.9z M7.1,10.9c-2.1,0-3.7-1.7-3.7-3.7s1.7-3.7,3.7-3.7s3.7,1.7,3.7,3.7 S9.2,10.9,7.1,10.9z"/>\n  </g>\n</svg>\n'}),et=new b({name:"ui-components:settings",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24">\n  <path class="jp-icon3 jp-icon-selectable" fill="#616161" d="M19.43 12.98c.04-.32.07-.64.07-.98s-.03-.66-.07-.98l2.11-1.65c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.3-.61-.22l-2.49 1c-.52-.4-1.08-.73-1.69-.98l-.38-2.65A.488.488 0 0014 2h-4c-.25 0-.46.18-.49.42l-.38 2.65c-.61.25-1.17.59-1.69.98l-2.49-1c-.23-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64l2.11 1.65c-.04.32-.07.65-.07.98s.03.66.07.98l-2.11 1.65c-.19.15-.24.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1c.52.4 1.08.73 1.69.98l.38 2.65c.03.24.24.42.49.42h4c.25 0 .46-.18.49-.42l.38-2.65c.61-.25 1.17-.59 1.69-.98l2.49 1c.23.09.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.65zM12 15.5c-1.93 0-3.5-1.57-3.5-3.5s1.57-3.5 3.5-3.5 3.5 1.57 3.5 3.5-1.57 3.5-3.5 3.5z"/>\n</svg>\n'}),tt=new b({name:"ui-components:share",svgstr:'<svg width="16" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">\n  <g class="jp-icon3" fill="#616161">\n    <path d="M 18 2 C 16.35499 2 15 3.3549904 15 5 C 15 5.1909529 15.021791 5.3771224 15.056641 5.5585938 L 7.921875 9.7207031 C 7.3985399 9.2778539 6.7320771 9 6 9 C 4.3549904 9 3 10.35499 3 12 C 3 13.64501 4.3549904 15 6 15 C 6.7320771 15 7.3985399 14.722146 7.921875 14.279297 L 15.056641 18.439453 C 15.021555 18.621514 15 18.808386 15 19 C 15 20.64501 16.35499 22 18 22 C 19.64501 22 21 20.64501 21 19 C 21 17.35499 19.64501 16 18 16 C 17.26748 16 16.601593 16.279328 16.078125 16.722656 L 8.9433594 12.558594 C 8.9782095 12.377122 9 12.190953 9 12 C 9 11.809047 8.9782095 11.622878 8.9433594 11.441406 L 16.078125 7.2792969 C 16.60146 7.7221461 17.267923 8 18 8 C 19.64501 8 21 6.6450096 21 5 C 21 3.3549904 19.64501 2 18 2 z M 18 4 C 18.564129 4 19 4.4358706 19 5 C 19 5.5641294 18.564129 6 18 6 C 17.435871 6 17 5.5641294 17 5 C 17 4.4358706 17.435871 4 18 4 z M 6 11 C 6.5641294 11 7 11.435871 7 12 C 7 12.564129 6.5641294 13 6 13 C 5.4358706 13 5 12.564129 5 12 C 5 11.435871 5.4358706 11 6 11 z M 18 18 C 18.564129 18 19 18.435871 19 19 C 19 19.564129 18.564129 20 18 20 C 17.435871 20 17 19.564129 17 19 C 17 18.435871 17.435871 18 18 18 z"/>\n  </g>\n</svg>\n'}),nt=new b({name:"ui-components:spreadsheet",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 22 22">\n  <path class="jp-icon-contrast1 jp-icon-selectable" fill="#4CAF50" d="M2.2 2.2v17.6h17.6V2.2H2.2zm15.4 7.7h-5.5V4.4h5.5v5.5zM9.9 4.4v5.5H4.4V4.4h5.5zm-5.5 7.7h5.5v5.5H4.4v-5.5zm7.7 5.5v-5.5h5.5v5.5h-5.5z"/>\n</svg>\n'}),st=new b({name:"ui-components:stop",svgstr:'<svg height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">\n    <g class="jp-icon3" fill="#616161">\n        <path d="M0 0h24v24H0z" fill="none"/>\n        <path d="M6 6h12v12H6z"/>\n    </g>\n</svg>\n'}),rt=new b({name:"ui-components:tab",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24">\n  <g class="jp-icon3" fill="#616161">\n    <path d="M21 3H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H3V5h10v4h8v10z"/>\n  </g>\n</svg>\n'}),it=new b({name:"ui-components:table-rows",svgstr:'<svg height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">\n    <g class="jp-icon3" fill="#616161">\n        <path d="M0 0h24v24H0z" fill="none"/>\n        <path d="M21,8H3V4h18V8z M21,10H3v4h18V10z M21,16H3v4h18V16z"/>\n    </g>\n</svg>\n'}),ot=new b({name:"ui-components:tag",svgstr:'<svg width="28" height="28" viewBox="0 0 43 28" xmlns="http://www.w3.org/2000/svg">\n\t<g class="jp-icon3" fill="#616161">\n\t\t<path d="M28.8332 12.334L32.9998 16.5007L37.1665 12.334H28.8332Z"/>\n\t\t<path d="M16.2095 21.6104C15.6873 22.1299 14.8443 22.1299 14.3248 21.6104L6.9829 14.7245C6.5724 14.3394 6.08313 13.6098 6.04786 13.0482C5.95347 11.5288 6.02002 8.61944 6.06621 7.07695C6.08281 6.51477 6.55548 6.04347 7.11804 6.03055C9.08863 5.98473 13.2638 5.93579 13.6518 6.32425L21.7369 13.639C22.256 14.1585 21.7851 15.4724 21.262 15.9946L16.2095 21.6104ZM9.77585 8.265C9.33551 7.82566 8.62351 7.82566 8.1828 8.265C7.74346 8.70571 7.74346 9.41733 8.1828 9.85667C8.62382 10.2964 9.33582 10.2964 9.77585 9.85667C10.2156 9.41733 10.2156 8.70533 9.77585 8.265Z"/>\n\t</g>\n</svg>\n'}),at=new b({name:"ui-components:terminal",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24" >\n    <rect class="jp-terminal-icon-background-color jp-icon-selectable" width="20" height="20" transform="translate(2 2)" fill="#333333"/>\n    <path class="jp-terminal-icon-color jp-icon-selectable-inverse" d="M5.05664 8.76172C5.05664 8.59766 5.03125 8.45312 4.98047 8.32812C4.93359 8.19922 4.85547 8.08203 4.74609 7.97656C4.64062 7.87109 4.5 7.77539 4.32422 7.68945C4.15234 7.59961 3.94336 7.51172 3.69727 7.42578C3.30273 7.28516 2.94336 7.13672 2.61914 6.98047C2.29492 6.82422 2.01758 6.64258 1.78711 6.43555C1.56055 6.22852 1.38477 5.98828 1.25977 5.71484C1.13477 5.4375 1.07227 5.10938 1.07227 4.73047C1.07227 4.39844 1.12891 4.0957 1.24219 3.82227C1.35547 3.54492 1.51562 3.30469 1.72266 3.10156C1.92969 2.89844 2.17969 2.73437 2.47266 2.60938C2.76562 2.48438 3.0918 2.4043 3.45117 2.36914V1.10938H4.38867V2.38086C4.74023 2.42773 5.05664 2.52344 5.33789 2.66797C5.61914 2.8125 5.85742 3.00195 6.05273 3.23633C6.25195 3.4668 6.4043 3.74023 6.50977 4.05664C6.61914 4.36914 6.67383 4.7207 6.67383 5.11133H5.04492C5.04492 4.63867 4.9375 4.28125 4.72266 4.03906C4.50781 3.79297 4.2168 3.66992 3.84961 3.66992C3.65039 3.66992 3.47656 3.69727 3.32812 3.75195C3.18359 3.80273 3.06445 3.87695 2.9707 3.97461C2.87695 4.06836 2.80664 4.17969 2.75977 4.30859C2.7168 4.4375 2.69531 4.57812 2.69531 4.73047C2.69531 4.88281 2.7168 5.01953 2.75977 5.14062C2.80664 5.25781 2.88281 5.36719 2.98828 5.46875C3.09766 5.57031 3.24023 5.66797 3.41602 5.76172C3.5918 5.85156 3.81055 5.94336 4.07227 6.03711C4.4668 6.18555 4.82422 6.33984 5.14453 6.5C5.46484 6.65625 5.73828 6.83984 5.96484 7.05078C6.19531 7.25781 6.37109 7.5 6.49219 7.77734C6.61719 8.05078 6.67969 8.375 6.67969 8.75C6.67969 9.09375 6.62305 9.4043 6.50977 9.68164C6.39648 9.95508 6.23438 10.1914 6.02344 10.3906C5.8125 10.5898 5.55859 10.75 5.26172 10.8711C4.96484 10.9883 4.63281 11.0645 4.26562 11.0996V12.248H3.33398V11.0996C3.00195 11.0684 2.67969 10.9961 2.36719 10.8828C2.05469 10.7656 1.77734 10.5977 1.53516 10.3789C1.29688 10.1602 1.10547 9.88477 0.960938 9.55273C0.816406 9.2168 0.744141 8.81445 0.744141 8.3457H2.37891C2.37891 8.62695 2.41992 8.86328 2.50195 9.05469C2.58398 9.24219 2.68945 9.39258 2.81836 9.50586C2.95117 9.61523 3.10156 9.69336 3.26953 9.74023C3.4375 9.78711 3.60938 9.81055 3.78516 9.81055C4.20312 9.81055 4.51953 9.71289 4.73438 9.51758C4.94922 9.32227 5.05664 9.07031 5.05664 8.76172ZM13.418 12.2715H8.07422V11H13.418V12.2715Z" transform="translate(3.95264 6)" fill="white"/>\n</svg>\n'}),lt=new b({name:"ui-components:text-editor",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24">\n  <path class="jp-text-editor-icon-color jp-icon-selectable" fill="#616161" d="M15 15H3v2h12v-2zm0-8H3v2h12V7zM3 13h18v-2H3v2zm0 8h18v-2H3v2zM3 3v2h18V3H3z"/>\n</svg>\n'}),ct=new b({name:"ui-components:toc",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">\n  <g class="jp-icon3 jp-icon-selectable" fill="#616161">\n    <path d="M7,5H21V7H7V5M7,13V11H21V13H7M4,4.5A1.5,1.5 0 0,1 5.5,6A1.5,1.5 0 0,1 4,7.5A1.5,1.5 0 0,1 2.5,6A1.5,1.5 0 0,1 4,4.5M4,10.5A1.5,1.5 0 0,1 5.5,12A1.5,1.5 0 0,1 4,13.5A1.5,1.5 0 0,1 2.5,12A1.5,1.5 0 0,1 4,10.5M7,19V17H21V19H7M4,16.5A1.5,1.5 0 0,1 5.5,18A1.5,1.5 0 0,1 4,19.5A1.5,1.5 0 0,1 2.5,18A1.5,1.5 0 0,1 4,16.5Z" />\n  </g>\n</svg>\n'}),ht=new b({name:"ui-components:tree-view",svgstr:'<svg height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">\n    <g class="jp-icon3" fill="#616161">\n        <path d="M0 0h24v24H0z" fill="none"/>\n        <path d="M22 11V3h-7v3H9V3H2v8h7V8h2v10h4v3h7v-8h-7v3h-2V8h2v3z"/>\n    </g>\n</svg>\n'}),dt=new b({name:"ui-components:trusted",svgstr:'<svg fill="none" xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 25">\n    <path class="jp-icon2" stroke="#333333" stroke-width="2" transform="translate(2 3)" d="M1.86094 11.4409C0.826448 8.77027 0.863779 6.05764 1.24907 4.19932C2.48206 3.93347 4.08068 3.40347 5.60102 2.8449C7.23549 2.2444 8.85666 1.5815 9.9876 1.09539C11.0597 1.58341 12.6094 2.2444 14.218 2.84339C15.7503 3.41394 17.3995 3.95258 18.7539 4.21385C19.1364 6.07177 19.1709 8.77722 18.139 11.4409C17.0303 14.3032 14.6668 17.1844 9.99999 18.9354C5.3332 17.1844 2.96968 14.3032 1.86094 11.4409Z"/>\n    <path class="jp-icon2" fill="#333333" stroke="#333333" transform="translate(8 9.86719)" d="M2.86015 4.86535L0.726549 2.99959L0 3.63045L2.86015 6.13157L8 0.630872L7.27857 0L2.86015 4.86535Z"/>\n</svg>\n'}),ut=new b({name:"ui-components:undo",svgstr:'<svg viewBox="0 0 24 24" width="16" xmlns="http://www.w3.org/2000/svg">\n  <g class="jp-icon3" fill="#616161">\n    <path d="M12.5 8c-2.65 0-5.05.99-6.9 2.6L2 7v9h9l-3.62-3.62c1.39-1.16 3.16-1.88 5.12-1.88 3.54 0 6.55 2.31 7.6 5.5l2.37-.78C21.08 11.03 17.15 8 12.5 8z"/>\n  </g>\n</svg>\n'}),pt=new b({name:"ui-components:user",svgstr:'<svg width="16" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">\n  <g class="jp-icon3" fill="#616161">\n    <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>\n  </g>\n</svg>\n'}),gt=new b({name:"ui-components:users",svgstr:'<svg width="24" height="24" version="1.1" viewBox="0 0 36 24" xmlns="http://www.w3.org/2000/svg">\n <g class="jp-icon3" transform="matrix(1.7327 0 0 1.7327 -3.6282 .099577)" fill="#616161">\n  <path transform="matrix(1.5,0,0,1.5,0,-6)" d="m12.186 7.5098c-1.0535 0-1.9757 0.5665-2.4785 1.4102 0.75061 0.31277 1.3974 0.82648 1.873 1.4727h3.4863c0-1.592-1.2889-2.8828-2.8809-2.8828z"/>\n  <path d="m20.465 2.3895a2.1885 2.1885 0 0 1-2.1884 2.1885 2.1885 2.1885 0 0 1-2.1885-2.1885 2.1885 2.1885 0 0 1 2.1885-2.1885 2.1885 2.1885 0 0 1 2.1884 2.1885z"/>\n  <path transform="matrix(1.5,0,0,1.5,0,-6)" d="m3.5898 8.4219c-1.1126 0-2.0137 0.90111-2.0137 2.0137h2.8145c0.26797-0.37309 0.5907-0.70435 0.95898-0.97852-0.34433-0.61688-1.0031-1.0352-1.7598-1.0352z"/>\n  <path d="m6.9154 4.623a1.5294 1.5294 0 0 1-1.5294 1.5294 1.5294 1.5294 0 0 1-1.5294-1.5294 1.5294 1.5294 0 0 1 1.5294-1.5294 1.5294 1.5294 0 0 1 1.5294 1.5294z"/>\n  <path d="m6.135 13.535c0-3.2392 2.6259-5.865 5.865-5.865 3.2392 0 5.865 2.6259 5.865 5.865z"/>\n  <circle cx="12" cy="3.7685" r="2.9685"/>\n </g>\n</svg>\n'}),mt=new b({name:"ui-components:vega",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 22 22">\n  <g class="jp-icon1 jp-icon-selectable" fill="#212121">\n    <path d="M10.6 5.4l2.2-3.2H2.2v7.3l4-6.6z"/>\n    <path d="M15.8 2.2l-4.4 6.6L7 6.3l-4.8 8v5.5h17.6V2.2h-4zm-7 15.4H5.5v-4.4h3.3v4.4zm4.4 0H9.8V9.8h3.4v7.8zm4.4 0h-3.4V6.5h3.4v11.1z"/>\n  </g>\n</svg>\n'}),ft=new b({name:"ui-components:word",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 20 20">\n <g class="jp-icon2" fill="#414141">\n  <rect x="2" y="2" width="16" height="16"/>\n </g>\n <g class="jp-icon-accent2" transform="translate(.43 .0401)" fill="#fff">\n  <path d="m4.14 8.76q0.0682-1.89 2.42-1.89 1.16 0 1.68 0.42 0.567 0.41 0.567 1.16v3.47q0 0.462 0.514 0.462 0.103 0 0.2-0.0231v0.714q-0.399 0.103-0.651 0.103-0.452 0-0.693-0.22-0.231-0.2-0.284-0.662-0.956 0.872-2 0.872-0.903 0-1.47-0.472-0.525-0.472-0.525-1.26 0-0.262 0.0452-0.472 0.0567-0.22 0.116-0.378 0.0682-0.168 0.231-0.304 0.158-0.147 0.262-0.242 0.116-0.0914 0.368-0.168 0.262-0.0914 0.399-0.126 0.136-0.0452 0.472-0.103 0.336-0.0578 0.504-0.0798 0.158-0.0231 0.567-0.0798 0.556-0.0682 0.777-0.221 0.22-0.152 0.22-0.441v-0.252q0-0.43-0.357-0.662-0.336-0.231-0.976-0.231-0.662 0-0.998 0.262-0.336 0.252-0.399 0.798zm1.89 3.68q0.788 0 1.26-0.41 0.504-0.42 0.504-0.903v-1.05q-0.284 0.136-0.861 0.231-0.567 0.0914-0.987 0.158-0.42 0.0682-0.766 0.326-0.336 0.252-0.336 0.704t0.304 0.704 0.861 0.252z" stroke-width="1.05"/>\n  <path d="m10 4.56h0.945v3.15q0.651-0.976 1.89-0.976 1.16 0 1.89 0.84 0.682 0.84 0.682 2.31 0 1.47-0.704 2.42-0.704 0.882-1.89 0.882-1.26 0-1.89-1.02v0.766h-0.85zm2.62 3.04q-0.746 0-1.16 0.64-0.452 0.63-0.452 1.68 0 1.05 0.452 1.68t1.16 0.63q0.777 0 1.26-0.63 0.494-0.64 0.494-1.68 0-1.05-0.472-1.68-0.462-0.64-1.26-0.64z" stroke-width="1.05"/>\n  <path d="m2.73 15.8 13.6 0.0081c0.0069 0 0-2.6 0-2.6 0-0.0078-1.15 0-1.15 0-0.0069 0-0.0083 1.5-0.0083 1.5-2e-3 -0.0014-11.3-0.0014-11.3-0.0014l-0.00592-1.5c0-0.0078-1.17 0.0013-1.17 0.0013z" stroke-width=".975"/>\n </g>\n</svg>\n'}),vt=new b({name:"ui-components:yaml",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 22 22">\n  <g class="jp-icon-contrast2 jp-icon-selectable" fill="#D81B60">\n    <path d="M7.2 18.6v-5.4L3 5.6h3.3l1.4 3.1c.3.9.6 1.6 1 2.5.3-.8.6-1.6 1-2.5l1.4-3.1h3.4l-4.4 7.6v5.5l-2.9-.1z"/>\n    <circle class="st0" cx="17.6" cy="16.5" r="2.1"/>\n    <circle class="st0" cx="17.6" cy="11" r="2.1"/>\n  </g>\n</svg>\n'}),wt="jp-Collapse-header-collapsed";class bt extends u.Widget{constructor(e){super(e),this._collapseChanged=new d.Signal(this);const{widget:t,collapsed:n=!0}=e;this.addClass("jp-Collapse"),this._header=new u.Widget,this._header.addClass("jp-Collapse-header"),n&&this._header.addClass(wt),this._header.node.appendChild(L.element({className:"jp-Collapser-icon"}));const s=document.createElement("span");s.classList.add("jp-Collapser-title"),s.textContent=t.title.label,this._header.node.appendChild(s),this._content=new u.Panel,this._content.addClass("jp-Collapse-contents");const r=new u.PanelLayout;this.layout=r,r.addWidget(this._header),r.addWidget(this._content),this.widget=t,this.collapsed=n}get widget(){return this._widget}set widget(e){const t=this._widget;t&&(t.title.changed.disconnect(this._onTitleChanged,this),t.parent=null),this._widget=e,e.title.changed.connect(this._onTitleChanged,this),this._onTitleChanged(e.title),this._content.addWidget(e)}get collapsed(){return this._collapsed}set collapsed(e){e!==this._collapsed&&(e?this._collapse():this._uncollapse())}get collapseChanged(){return this._collapseChanged}toggle(){this.collapsed=!this.collapsed}dispose(){this.isDisposed||(this._header=null,this._widget=null,this._content=null,super.dispose())}handleEvent(e){"click"===e.type&&this._evtClick(e)}onAfterAttach(e){this._header.node.addEventListener("click",this)}onBeforeDetach(e){this._header.node.removeEventListener("click",this)}_collapse(){this._collapsed=!0,this._content&&this._content.hide(),this._setHeader(),this._collapseChanged.emit(void 0)}_uncollapse(){this._collapsed=!1,this._content&&this._content.show(),this._setHeader(),this._collapseChanged.emit(void 0)}_evtClick(e){this.toggle()}_onTitleChanged(e){this._setHeader()}_setHeader(){this._collapsed?this._header.addClass(wt):this._header.removeClass(wt)}}var yt=n(71677),xt=n(24246),Ct=n(24885),St=n(99729),_t=n.n(St),kt=n(90104),Ft=n.n(kt),jt=n(14648),Mt=n.n(jt),It=n(40110),Tt=n.n(It),Lt=n(30454),Vt=n.n(Lt),Dt=n(11611),Et=n.n(Dt),Ot=n(47215),$t=n.n(Ot);let At=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce(((e,t)=>e+((t&=63)<36?t.toString(36):t<62?(t-26).toString(36).toUpperCase():t>62?"-":"_")),"");function Bt(){return At()}function Nt(e){return Array.isArray(e)?e.map((e=>({key:Bt(),item:e}))):[]}function Rt(e){return Array.isArray(e)?e.map((e=>e.item)):[]}class zt extends s.Component{constructor(e){super(e),this._getNewFormDataRow=()=>{const{schema:e,registry:t}=this.props,{schemaUtils:n}=t;let s=e.items;return(0,Ct.isFixedItems)(e)&&(0,Ct.allowAdditionalItems)(e)&&(s=e.additionalItems),n.getDefaultFormState(s)},this.onAddClick=e=>{this._handleAddClick(e)},this.onAddIndexClick=e=>t=>{this._handleAddClick(t,e)},this.onCopyIndexClick=e=>t=>{t&&t.preventDefault();const{onChange:n,errorSchema:s}=this.props,{keyedFormData:r}=this.state;let i;if(s){i={};for(const t in s){const n=parseInt(t);n<=e?$t()(i,[n],s[t]):n>e&&$t()(i,[n+1],s[t])}}const o={key:Bt(),item:Vt()(r[e].item)},a=[...r];void 0!==e?a.splice(e+1,0,o):a.push(o),this.setState({keyedFormData:a,updatedKeyedFormData:!0},(()=>n(Rt(a),i)))},this.onDropIndexClick=e=>t=>{t&&t.preventDefault();const{onChange:n,errorSchema:s}=this.props,{keyedFormData:r}=this.state;let i;if(s){i={};for(const t in s){const n=parseInt(t);n<e?$t()(i,[n],s[t]):n>e&&$t()(i,[n-1],s[t])}}const o=r.filter(((t,n)=>n!==e));this.setState({keyedFormData:o,updatedKeyedFormData:!0},(()=>n(Rt(o),i)))},this.onReorderClick=(e,t)=>n=>{n&&(n.preventDefault(),n.currentTarget.blur());const{onChange:s,errorSchema:r}=this.props;let i;if(r){i={};for(const n in r){const s=parseInt(n);s==e?$t()(i,[t],r[e]):s==t?$t()(i,[e],r[t]):$t()(i,[n],r[s])}}const{keyedFormData:o}=this.state,a=function(){const n=o.slice();return n.splice(e,1),n.splice(t,0,o[e]),n}();this.setState({keyedFormData:a},(()=>s(Rt(a),i)))},this.onChangeForIndex=e=>(t,n,s)=>{const{formData:r,onChange:i,errorSchema:o}=this.props,a=(Array.isArray(r)?r:[]).map(((n,s)=>e===s?void 0===t?null:t:n));i(a,o&&o&&{...o,[e]:n},s)},this.onSelectChange=e=>{const{onChange:t,idSchema:n}=this.props;t(e,void 0,n&&n.$id)};const{formData:t=[]}=e,n=Nt(t);this.state={keyedFormData:n,updatedKeyedFormData:!1}}static getDerivedStateFromProps(e,t){if(t.updatedKeyedFormData)return{updatedKeyedFormData:!1};const n=Array.isArray(e.formData)?e.formData:[],s=t.keyedFormData||[];return{keyedFormData:n.length===s.length?s.map(((e,t)=>({key:e.key,item:n[t]}))):Nt(n)}}get itemTitle(){const{schema:e,registry:t}=this.props,{translateString:n}=t;return _t()(e,[Ct.ITEMS_KEY,"title"],_t()(e,[Ct.ITEMS_KEY,"description"],n(Ct.TranslatableString.ArrayItemTitle)))}isItemRequired(e){return Array.isArray(e.type)?!e.type.includes("null"):"null"!==e.type}canAddItem(e){const{schema:t,uiSchema:n,registry:s}=this.props;let{addable:r}=(0,Ct.getUiOptions)(n,s.globalUiOptions);return!1!==r&&(r=void 0===t.maxItems||e.length<t.maxItems),r}_handleAddClick(e,t){e&&e.preventDefault();const{onChange:n,errorSchema:s}=this.props,{keyedFormData:r}=this.state;let i;if(s){i={};for(const e in s){const n=parseInt(e);void 0===t||n<t?$t()(i,[n],s[e]):n>=t&&$t()(i,[n+1],s[e])}}const o={key:Bt(),item:this._getNewFormDataRow()},a=[...r];void 0!==t?a.splice(t,0,o):a.push(o),this.setState({keyedFormData:a,updatedKeyedFormData:!0},(()=>n(Rt(a),i)))}render(){const{schema:e,uiSchema:t,idSchema:n,registry:s}=this.props,{schemaUtils:r,translateString:i}=s;if(!(Ct.ITEMS_KEY in e)){const r=(0,Ct.getUiOptions)(t),o=(0,Ct.getTemplate)("UnsupportedFieldTemplate",s,r);return(0,xt.jsx)(o,{schema:e,idSchema:n,reason:i(Ct.TranslatableString.MissingItems),registry:s})}return r.isMultiSelect(e)?this.renderMultiSelect():(0,Ct.isCustomWidget)(t)?this.renderCustomWidget():(0,Ct.isFixedItems)(e)?this.renderFixedArray():r.isFilesArray(e,t)?this.renderFiles():this.renderNormalArray()}renderNormalArray(){const{schema:e,uiSchema:t={},errorSchema:n,idSchema:s,name:r,disabled:i=!1,readonly:o=!1,autofocus:a=!1,required:l=!1,registry:c,onBlur:h,onFocus:d,idPrefix:u,idSeparator:p="_",rawErrors:g}=this.props,{keyedFormData:m}=this.state,f=void 0===e.title?r:e.title,{schemaUtils:v,formContext:w}=c,b=(0,Ct.getUiOptions)(t),y=Et()(e.items)?e.items:{},x=v.retrieveSchema(y),C=Rt(this.state.keyedFormData),S=this.canAddItem(C),_={canAdd:S,items:m.map(((e,i)=>{const{key:o,item:l}=e,c=l,f=v.retrieveSchema(y,c),w=n?n[i]:void 0,b=s.$id+p+i,x=v.toIdSchema(f,b,c,u,p);return this.renderArrayFieldItem({key:o,index:i,name:r&&`${r}-${i}`,canAdd:S,canMoveUp:i>0,canMoveDown:i<C.length-1,itemSchema:f,itemIdSchema:x,itemErrorSchema:w,itemData:c,itemUiSchema:t.items,autofocus:a&&0===i,onBlur:h,onFocus:d,rawErrors:g,totalItems:m.length})})),className:`field field-array field-array-of-${x.type}`,disabled:i,idSchema:s,uiSchema:t,onAddClick:this.onAddClick,readonly:o,required:l,schema:e,title:f,formContext:w,formData:C,rawErrors:g,registry:c},k=(0,Ct.getTemplate)("ArrayFieldTemplate",c,b);return(0,xt.jsx)(k,{..._})}renderCustomWidget(){var e;const{schema:t,idSchema:n,uiSchema:s,disabled:r=!1,readonly:i=!1,autofocus:o=!1,required:a=!1,hideError:l,placeholder:c,onBlur:h,onFocus:d,formData:u=[],registry:p,rawErrors:g,name:m}=this.props,{widgets:f,formContext:v,globalUiOptions:w,schemaUtils:b}=p,{widget:y,title:x,...C}=(0,Ct.getUiOptions)(s,w),S=(0,Ct.getWidget)(t,y,f),_=null!==(e=null!=x?x:t.title)&&void 0!==e?e:m,k=b.getDisplayLabel(t,s,w);return(0,xt.jsx)(S,{id:n.$id,name:m,multiple:!0,onChange:this.onSelectChange,onBlur:h,onFocus:d,options:C,schema:t,uiSchema:s,registry:p,value:u,disabled:r,readonly:i,hideError:l,required:a,label:_,hideLabel:!k,placeholder:c,formContext:v,autofocus:o,rawErrors:g})}renderMultiSelect(){var e;const{schema:t,idSchema:n,uiSchema:s,formData:r=[],disabled:i=!1,readonly:o=!1,autofocus:a=!1,required:l=!1,placeholder:c,onBlur:h,onFocus:d,registry:u,rawErrors:p,name:g}=this.props,{widgets:m,schemaUtils:f,formContext:v,globalUiOptions:w}=u,b=f.retrieveSchema(t.items,r),y=(0,Ct.optionsList)(b),{widget:x="select",title:C,...S}=(0,Ct.getUiOptions)(s,w),_=(0,Ct.getWidget)(t,x,m),k=null!==(e=null!=C?C:t.title)&&void 0!==e?e:g,F=f.getDisplayLabel(t,s,w);return(0,xt.jsx)(_,{id:n.$id,name:g,multiple:!0,onChange:this.onSelectChange,onBlur:h,onFocus:d,options:{...S,enumOptions:y},schema:t,uiSchema:s,registry:u,value:r,disabled:i,readonly:o,required:l,label:k,hideLabel:!F,placeholder:c,formContext:v,autofocus:a,rawErrors:p})}renderFiles(){var e;const{schema:t,uiSchema:n,idSchema:s,name:r,disabled:i=!1,readonly:o=!1,autofocus:a=!1,required:l=!1,onBlur:c,onFocus:h,registry:d,formData:u=[],rawErrors:p}=this.props,{widgets:g,formContext:m,globalUiOptions:f,schemaUtils:v}=d,{widget:w="files",title:b,...y}=(0,Ct.getUiOptions)(n,f),x=(0,Ct.getWidget)(t,w,g),C=null!==(e=null!=b?b:t.title)&&void 0!==e?e:r,S=v.getDisplayLabel(t,n,f);return(0,xt.jsx)(x,{options:y,id:s.$id,name:r,multiple:!0,onChange:this.onSelectChange,onBlur:c,onFocus:h,schema:t,uiSchema:n,value:u,disabled:i,readonly:o,required:l,registry:d,formContext:m,autofocus:a,rawErrors:p,label:C,hideLabel:!S})}renderFixedArray(){const{schema:e,uiSchema:t={},formData:n=[],errorSchema:s,idPrefix:r,idSeparator:i="_",idSchema:o,name:a,disabled:l=!1,readonly:c=!1,autofocus:h=!1,required:d=!1,registry:u,onBlur:p,onFocus:g,rawErrors:m}=this.props,{keyedFormData:f}=this.state;let{formData:v=[]}=this.props;const w=e.title||a,b=(0,Ct.getUiOptions)(t),{schemaUtils:y,formContext:x}=u,C=(Et()(e.items)?e.items:[]).map(((e,t)=>y.retrieveSchema(e,n[t]))),S=Et()(e.additionalItems)?y.retrieveSchema(e.additionalItems,n):null;(!v||v.length<C.length)&&(v=v||[],v=v.concat(new Array(C.length-v.length)));const _=this.canAddItem(v)&&!!S,k={canAdd:_,className:"field field-array field-array-fixed-items",disabled:l,idSchema:o,formData:n,items:f.map(((n,l)=>{const{key:c,item:d}=n,u=d,w=l>=C.length,b=(w&&Et()(e.additionalItems)?y.retrieveSchema(e.additionalItems,u):C[l])||{},x=o.$id+i+l,S=y.toIdSchema(b,x,u,r,i),k=w?t.additionalItems||{}:Array.isArray(t.items)?t.items[l]:t.items||{},F=s?s[l]:void 0;return this.renderArrayFieldItem({key:c,index:l,name:a&&`${a}-${l}`,canAdd:_,canRemove:w,canMoveUp:l>=C.length+1,canMoveDown:w&&l<v.length-1,itemSchema:b,itemData:u,itemUiSchema:k,itemIdSchema:S,itemErrorSchema:F,autofocus:h&&0===l,onBlur:p,onFocus:g,rawErrors:m,totalItems:f.length})})),onAddClick:this.onAddClick,readonly:c,required:d,registry:u,schema:e,uiSchema:t,title:w,formContext:x,rawErrors:m},F=(0,Ct.getTemplate)("ArrayFieldTemplate",u,b);return(0,xt.jsx)(F,{...k})}renderArrayFieldItem(e){const{key:t,index:n,name:s,canAdd:r,canRemove:i=!0,canMoveUp:o,canMoveDown:a,itemSchema:l,itemData:c,itemUiSchema:h,itemIdSchema:d,itemErrorSchema:u,autofocus:p,onBlur:g,onFocus:m,rawErrors:f,totalItems:v}=e,{disabled:w,hideError:b,idPrefix:y,idSeparator:x,readonly:C,uiSchema:S,registry:_,formContext:k}=this.props,{fields:{ArraySchemaField:F,SchemaField:j},globalUiOptions:M}=_,I=F||j,{orderable:T=!0,removable:L=!0,copyable:V=!1}=(0,Ct.getUiOptions)(S,M),D={moveUp:T&&o,moveDown:T&&a,copy:V&&r,remove:L&&i,toolbar:!1};return D.toolbar=Object.keys(D).some((e=>D[e])),{children:(0,xt.jsx)(I,{name:s,index:n,schema:l,uiSchema:h,formData:c,formContext:k,errorSchema:u,idPrefix:y,idSeparator:x,idSchema:d,required:this.isItemRequired(l),onChange:this.onChangeForIndex(n),onBlur:g,onFocus:m,registry:_,disabled:w,readonly:C,hideError:b,autofocus:p,rawErrors:f}),className:"array-item",disabled:w,canAdd:r,hasCopy:D.copy,hasToolbar:D.toolbar,hasMoveUp:D.moveUp,hasMoveDown:D.moveDown,hasRemove:D.remove,index:n,totalItems:v,key:t,onAddIndexClick:this.onAddIndexClick,onCopyIndexClick:this.onCopyIndexClick,onDropIndexClick:this.onDropIndexClick,onReorderClick:this.onReorderClick,readonly:C,registry:_,schema:l,uiSchema:h}}}const Pt=zt,Ht=function(e){var t,n;const{schema:s,name:r,uiSchema:i,idSchema:o,formData:a,registry:l,required:c,disabled:h,readonly:d,hideError:u,autofocus:p,onChange:g,onFocus:m,onBlur:f,rawErrors:v}=e,{title:w}=s,{widgets:b,formContext:y,translateString:x,globalUiOptions:C}=l,{widget:S="checkbox",title:_,label:k=!0,...F}=(0,Ct.getUiOptions)(i,C),j=(0,Ct.getWidget)(s,S,b),M=x(Ct.TranslatableString.YesLabel),I=x(Ct.TranslatableString.NoLabel);let T;const L=null!==(t=null!=_?_:w)&&void 0!==t?t:r;if(Array.isArray(s.oneOf))T=(0,Ct.optionsList)({oneOf:s.oneOf.map((e=>{if(Et()(e))return{...e,title:e.title||(!0===e.const?M:I)}})).filter((e=>e))});else{const e=s,t=null!==(n=s.enum)&&void 0!==n?n:[!0,!1];T=!e.enumNames&&2===t.length&&t.every((e=>"boolean"==typeof e))?[{value:t[0],label:t[0]?M:I},{value:t[1],label:t[1]?M:I}]:(0,Ct.optionsList)({enum:t,enumNames:e.enumNames})}return(0,xt.jsx)(j,{options:{...F,enumOptions:T},schema:s,uiSchema:i,id:o.$id,name:r,onChange:g,onFocus:m,onBlur:f,label:L,hideLabel:!k,value:a,required:c,disabled:h,readonly:d,hideError:u,registry:l,formContext:y,autofocus:p,rawErrors:v})};var Ut=n(48159),Wt=n.n(Ut);class qt extends s.Component{constructor(e){super(e),this.onOptionChange=e=>{const{selectedOption:t,retrievedOptions:n}=this.state,{formData:s,onChange:r,registry:i}=this.props,{schemaUtils:o}=i,a=void 0!==e?parseInt(e,10):-1;if(a===t)return;const l=a>=0?n[a]:void 0,c=t>=0?n[t]:void 0;let h=o.sanitizeDataForNewSchema(l,c,s);h&&l&&(h=o.getDefaultFormState(l,h,"excludeObjectChildren")),r(h,void 0,this.getFieldId()),this.setState({selectedOption:a})};const{formData:t,options:n,registry:{schemaUtils:s}}=this.props,r=n.map((e=>s.retrieveSchema(e,t)));this.state={retrievedOptions:r,selectedOption:this.getMatchingOption(0,t,r)}}componentDidUpdate(e,t){const{formData:n,options:s,idSchema:r}=this.props,{selectedOption:i}=this.state;let o=this.state;if(!(0,Ct.deepEquals)(e.options,s)){const{registry:{schemaUtils:e}}=this.props;o={selectedOption:i,retrievedOptions:s.map((t=>e.retrieveSchema(t,n)))}}if(!(0,Ct.deepEquals)(n,e.formData)&&r.$id===e.idSchema.$id){const{retrievedOptions:e}=o,s=this.getMatchingOption(i,n,e);t&&s!==i&&(o={selectedOption:s,retrievedOptions:e})}o!==this.state&&this.setState(o)}getMatchingOption(e,t,n){const{schema:s,registry:{schemaUtils:r}}=this.props,i=(0,Ct.getDiscriminatorFieldFromSchema)(s);return r.getClosestMatchingOption(t,n,e,i)}getFieldId(){const{idSchema:e,schema:t}=this.props;return`${e.$id}${t.oneOf?"__oneof_select":"__anyof_select"}`}render(){const{name:e,disabled:t=!1,errorSchema:n={},formContext:s,onBlur:r,onFocus:i,registry:o,schema:a,uiSchema:l}=this.props,{widgets:c,fields:h,translateString:d,globalUiOptions:u,schemaUtils:p}=o,{SchemaField:g}=h,{selectedOption:m,retrievedOptions:f}=this.state,{widget:v="select",placeholder:w,autofocus:b,autocomplete:y,title:x=a.title,...C}=(0,Ct.getUiOptions)(l,u),S=(0,Ct.getWidget)({type:"number"},v,c),_=_t()(n,Ct.ERRORS_KEY,[]),k=Wt()(n,[Ct.ERRORS_KEY]),F=p.getDisplayLabel(a,l,u),j=m>=0&&f[m]||null;let M;if(j){const{required:e}=a;M=e?(0,Ct.mergeSchemas)({required:e},j):j}const I=x?Ct.TranslatableString.TitleOptionPrefix:Ct.TranslatableString.OptionPrefix,T=x?[x]:[],L=f.map(((e,t)=>({label:e.title||d(I,T.concat(String(t+1))),value:t})));return(0,xt.jsxs)("div",{className:"panel panel-default panel-body",children:[(0,xt.jsx)("div",{className:"form-group",children:(0,xt.jsx)(S,{id:this.getFieldId(),name:`${e}${a.oneOf?"__oneof_select":"__anyof_select"}`,schema:{type:"number",default:0},onChange:this.onOptionChange,onBlur:r,onFocus:i,disabled:t||Ft()(L),multiple:!1,rawErrors:_,errorSchema:k,value:m>=0?m:void 0,options:{enumOptions:L,...C},registry:o,formContext:s,placeholder:w,autocomplete:y,autofocus:b,label:null!=x?x:e,hideLabel:!F})}),null!==j&&(0,xt.jsx)(g,{...this.props,schema:M})]})}}const Gt=qt,Zt=/\.([0-9]*0)*$/,Kt=/[0.]0*$/,Yt=function(e){const{registry:t,onChange:n,formData:r,value:i}=e,[o,a]=(0,s.useState)(i),{StringField:l}=t.fields;let c=r;const h=(0,s.useCallback)((e=>{a(e),"."===`${e}`.charAt(0)&&(e=`0${e}`);const t="string"==typeof e&&e.match(Zt)?(0,Ct.asNumber)(e.replace(Kt,"")):(0,Ct.asNumber)(e);n(t)}),[n]);if("string"==typeof o&&"number"==typeof c){const e=new RegExp(`${c}`.replace(".","\\.")+"\\.?0*$");o.match(e)&&(c=o)}return(0,xt.jsx)(l,{...e,formData:c,onChange:h})};function Jt(){return Jt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(e[s]=n[s])}return e},Jt.apply(this,arguments)}const Xt=["children","options"],Qt=["allowFullScreen","allowTransparency","autoComplete","autoFocus","autoPlay","cellPadding","cellSpacing","charSet","className","classId","colSpan","contentEditable","contextMenu","crossOrigin","encType","formAction","formEncType","formMethod","formNoValidate","formTarget","frameBorder","hrefLang","inputMode","keyParams","keyType","marginHeight","marginWidth","maxLength","mediaGroup","minLength","noValidate","radioGroup","readOnly","rowSpan","spellCheck","srcDoc","srcLang","srcSet","tabIndex","useMap"].reduce(((e,t)=>(e[t.toLowerCase()]=t,e)),{for:"htmlFor"}),en={amp:"&",apos:"'",gt:">",lt:"<",nbsp:" ",quot:"“"},tn=["style","script"],nn=/([-A-Z0-9_:]+)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|(?:\{((?:\\.|{[^}]*?}|[^}])*)\})))?/gi,sn=/mailto:/i,rn=/\n{2,}$/,on=/^( *>[^\n]+(\n[^\n]+)*\n*)+\n{2,}/,an=/^ *> ?/gm,ln=/^ {2,}\n/,cn=/^(?:( *[-*_])){3,} *(?:\n *)+\n/,hn=/^\s*(`{3,}|~{3,}) *(\S+)?([^\n]*?)?\n([\s\S]+?)\s*\1 *(?:\n *)*\n?/,dn=/^(?: {4}[^\n]+\n*)+(?:\n *)+\n?/,un=/^(`+)\s*([\s\S]*?[^`])\s*\1(?!`)/,pn=/^(?:\n *)*\n/,gn=/\r\n?/g,mn=/^\[\^([^\]]+)](:.*)\n/,fn=/^\[\^([^\]]+)]/,vn=/\f/g,wn=/^\s*?\[(x|\s)\]/,bn=/^ *(#{1,6}) *([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,yn=/^ *(#{1,6}) +([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,xn=/^([^\n]+)\n *(=|-){3,} *(?:\n *)+\n/,Cn=/^ *(?!<[a-z][^ >/]* ?\/>)<([a-z][^ >/]*) ?([^>]*)\/{0}>\n?(\s*(?:<\1[^>]*?>[\s\S]*?<\/\1>|(?!<\1)[\s\S])*?)<\/\1>\n*/i,Sn=/&([a-z0-9]+|#[0-9]{1,6}|#x[0-9a-fA-F]{1,6});/gi,_n=/^<!--[\s\S]*?(?:-->)/,kn=/^(data|aria|x)-[a-z_][a-z\d_.-]*$/,Fn=/^ *<([a-z][a-z0-9:]*)(?:\s+((?:<.*?>|[^>])*))?\/?>(?!<\/\1>)(\s*\n)?/i,jn=/^\{.*\}$/,Mn=/^(https?:\/\/[^\s<]+[^<.,:;"')\]\s])/,In=/^<([^ >]+@[^ >]+)>/,Tn=/^<([^ >]+:\/[^ >]+)>/,Ln=/-([a-z])?/gi,Vn=/^(.*\|?.*)\n *(\|? *[-:]+ *\|[-| :]*)\n((?:.*\|.*\n)*)\n?/,Dn=/^\[([^\]]*)\]:\s+<?([^\s>]+)>?\s*("([^"]*)")?/,En=/^!\[([^\]]*)\] ?\[([^\]]*)\]/,On=/^\[([^\]]*)\] ?\[([^\]]*)\]/,$n=/(\[|\])/g,An=/(\n|^[-*]\s|^#|^ {2,}|^-{2,}|^>\s)/,Bn=/\t/g,Nn=/^ *\| */,Rn=/(^ *\||\| *$)/g,zn=/ *$/,Pn=/^ *:-+: *$/,Hn=/^ *:-+ *$/,Un=/^ *-+: *$/,Wn=/^([*_])\1((?:\[.*?\][([].*?[)\]]|<.*?>(?:.*?<.*?>)?|`.*?`|~+.*?~+|.)*?)\1\1(?!\1)/,qn=/^([*_])((?:\[.*?\][([].*?[)\]]|<.*?>(?:.*?<.*?>)?|`.*?`|~+.*?~+|.)*?)\1(?!\1|\w)/,Gn=/^==((?:\[.*?\]|<.*?>(?:.*?<.*?>)?|`.*?`|.)*?)==/,Zn=/^~~((?:\[.*?\]|<.*?>(?:.*?<.*?>)?|`.*?`|.)*?)~~/,Kn=/^\\([^0-9A-Za-z\s])/,Yn=/^[\s\S]+?(?=[^0-9A-Z\s\u00c0-\uffff&#;.()'"]|\d+\.|\n\n| {2,}\n|\w+:\S|$)/i,Jn=/^\n+/,Xn=/^([ \t]*)/,Qn=/\\([^\\])/g,es=/ *\n+$/,ts=/(?:^|\n)( *)$/,ns="(?:\\d+\\.)",ss="(?:[*+-])";function rs(e){return"( *)("+(1===e?ns:ss)+") +"}const is=rs(1),os=rs(2);function as(e){return new RegExp("^"+(1===e?is:os))}const ls=as(1),cs=as(2);function hs(e){return new RegExp("^"+(1===e?is:os)+"[^\\n]*(?:\\n(?!\\1"+(1===e?ns:ss)+" )[^\\n]*)*(\\n|$)","gm")}const ds=hs(1),us=hs(2);function ps(e){const t=1===e?ns:ss;return new RegExp("^( *)("+t+") [\\s\\S]+?(?:\\n{2,}(?! )(?!\\1"+t+" (?!"+t+" ))\\n*|\\s*\\n*$)")}const gs=ps(1),ms=ps(2);function fs(e,t){const n=1===t,s=n?gs:ms,r=n?ds:us,i=n?ls:cs;return{t(e,t,n){const r=ts.exec(n);return r&&(t.o||!t._&&!t.u)?s.exec(e=r[1]+e):null},i:zs.HIGH,l(e,t,s){const o=n?+e[2]:void 0,a=e[0].replace(rn,"\n").match(r);let l=!1;return{p:a.map((function(e,n){const r=i.exec(e)[0].length,o=new RegExp("^ {1,"+r+"}","gm"),c=e.replace(o,"").replace(i,""),h=n===a.length-1,d=-1!==c.indexOf("\n\n")||h&&l;l=d;const u=s._,p=s.o;let g;s.o=!0,d?(s._=!1,g=c.replace(es,"\n\n")):(s._=!0,g=c.replace(es,""));const m=t(g,s);return s._=u,s.o=p,m})),m:n,g:o}},h:(t,n,s)=>e(t.m?"ol":"ul",{key:s.k,start:t.g},t.p.map((function(t,r){return e("li",{key:r},n(t,s))})))}}const vs=/^\[([^\]]*)]\( *((?:\([^)]*\)|[^() ])*) *"?([^)"]*)?"?\)/,ws=/^!\[([^\]]*)]\( *((?:\([^)]*\)|[^() ])*) *"?([^)"]*)?"?\)/,bs=[on,hn,dn,bn,xn,yn,_n,Vn,ds,gs,us,ms],ys=[...bs,/^[^\n]+(?:  \n|\n{2,})/,Cn,Fn];function xs(e){return e.replace(/[ÀÁÂÃÄÅàáâãäåæÆ]/g,"a").replace(/[çÇ]/g,"c").replace(/[ðÐ]/g,"d").replace(/[ÈÉÊËéèêë]/g,"e").replace(/[ÏïÎîÍíÌì]/g,"i").replace(/[Ññ]/g,"n").replace(/[øØœŒÕõÔôÓóÒò]/g,"o").replace(/[ÜüÛûÚúÙù]/g,"u").replace(/[ŸÿÝý]/g,"y").replace(/[^a-z0-9- ]/gi,"").replace(/ /gi,"-").toLowerCase()}function Cs(e){return Un.test(e)?"right":Pn.test(e)?"center":Hn.test(e)?"left":null}function Ss(e,t,n){const s=n.$;n.$=!0;const r=t(e.trim(),n);n.$=s;let i=[[]];return r.forEach((function(e,t){"tableSeparator"===e.type?0!==t&&t!==r.length-1&&i.push([]):("text"!==e.type||null!=r[t+1]&&"tableSeparator"!==r[t+1].type||(e.v=e.v.replace(zn,"")),i[i.length-1].push(e))})),i}function _s(e,t,n){n._=!0;const s=Ss(e[1],t,n),r=e[2].replace(Rn,"").split("|").map(Cs),i=function(e,t,n){return e.trim().split("\n").map((function(e){return Ss(e,t,n)}))}(e[3],t,n);return n._=!1,{S:r,A:i,L:s,type:"table"}}function ks(e,t){return null==e.S[t]?{}:{textAlign:e.S[t]}}function Fs(e){return function(t,n){return n._?e.exec(t):null}}function js(e){return function(t,n){return n._||n.u?e.exec(t):null}}function Ms(e){return function(t,n){return n._||n.u?null:e.exec(t)}}function Is(e){return function(t){return e.exec(t)}}function Ts(e,t,n){if(t._||t.u)return null;if(n&&!n.endsWith("\n"))return null;let s="";e.split("\n").every((e=>!bs.some((t=>t.test(e)))&&(s+=e+"\n",e.trim())));const r=s.trimEnd();return""==r?null:[s,r]}function Ls(e){try{if(decodeURIComponent(e).replace(/[^A-Za-z0-9/:]/g,"").match(/^\s*(javascript|vbscript|data(?!:image)):/i))return}catch(e){return null}return e}function Vs(e){return e.replace(Qn,"$1")}function Ds(e,t,n){const s=n._||!1,r=n.u||!1;n._=!0,n.u=!0;const i=e(t,n);return n._=s,n.u=r,i}function Es(e,t,n){const s=n._||!1,r=n.u||!1;n._=!1,n.u=!0;const i=e(t,n);return n._=s,n.u=r,i}function Os(e,t,n){return n._=!1,e(t,n)}const $s=(e,t,n)=>({v:Ds(t,e[1],n)});function As(){return{}}function Bs(){return null}function Ns(...e){return e.filter(Boolean).join(" ")}function Rs(e,t,n){let s=e;const r=t.split(".");for(;r.length&&(s=s[r[0]],void 0!==s);)r.shift();return s||n}var zs,Ps;function Hs(e,t={}){t.overrides=t.overrides||{},t.slugify=t.slugify||xs,t.namedCodesToUnicode=t.namedCodesToUnicode?Jt({},en,t.namedCodesToUnicode):en;const n=t.createElement||s.createElement;function r(e,s,...r){const i=Rs(t.overrides,`${e}.props`,{});return n(function(e,t){const n=Rs(t,e);return n?"function"==typeof n||"object"==typeof n&&"render"in n?n:Rs(t,`${e}.component`,e):e}(e,t.overrides),Jt({},s,i,{className:Ns(null==s?void 0:s.className,i.className)||void 0}),...r)}function i(e){let n=!1;t.forceInline?n=!0:t.forceBlock||(n=!1===An.test(e));const i=d(h(n?e:`${e.trimEnd().replace(Jn,"")}\n\n`,{_:n}));for(;"string"==typeof i[i.length-1]&&!i[i.length-1].trim();)i.pop();if(null===t.wrapper)return i;const o=t.wrapper||(n?"span":"div");let a;if(i.length>1||t.forceWrapper)a=i;else{if(1===i.length)return a=i[0],"string"==typeof a?r("span",{key:"outer"},a):a;a=null}return s.createElement(o,{key:"outer"},a)}function o(e){const t=e.match(nn);return t?t.reduce((function(e,t,n){const r=t.indexOf("=");if(-1!==r){const a=(o=t.slice(0,r),-1!==o.indexOf("-")&&null===o.match(kn)&&(o=o.replace(Ln,(function(e,t){return t.toUpperCase()}))),o).trim(),l=function(e){const t=e[0];return('"'===t||"'"===t)&&e.length>=2&&e[e.length-1]===t?e.slice(1,-1):e}(t.slice(r+1).trim()),c=Qt[a]||a,h=e[c]=function(e,t){return"style"===e?t.split(/;\s?/).reduce((function(e,t){const n=t.slice(0,t.indexOf(":"));return e[n.replace(/(-[a-z])/g,(e=>e[1].toUpperCase()))]=t.slice(n.length+1).trim(),e}),{}):"href"===e?Ls(t):(t.match(jn)&&(t=t.slice(1,t.length-1)),"true"===t||"false"!==t&&t)}(a,l);"string"==typeof h&&(Cn.test(h)||Fn.test(h))&&(e[c]=s.cloneElement(i(h.trim()),{key:n}))}else"style"!==t&&(e[Qt[t]||t]=!0);var o;return e}),{}):null}const a=[],l={},c={blockQuote:{t:Ms(on),i:zs.HIGH,l:(e,t,n)=>({v:t(e[0].replace(an,""),n)}),h:(e,t,n)=>r("blockquote",{key:n.k},t(e.v,n))},breakLine:{t:Is(ln),i:zs.HIGH,l:As,h:(e,t,n)=>r("br",{key:n.k})},breakThematic:{t:Ms(cn),i:zs.HIGH,l:As,h:(e,t,n)=>r("hr",{key:n.k})},codeBlock:{t:Ms(dn),i:zs.MAX,l:e=>({v:e[0].replace(/^ {4}/gm,"").replace(/\n+$/,""),M:void 0}),h:(e,t,n)=>r("pre",{key:n.k},r("code",Jt({},e.O,{className:e.M?`lang-${e.M}`:""}),e.v))},codeFenced:{t:Ms(hn),i:zs.MAX,l:e=>({O:o(e[3]||""),v:e[4],M:e[2]||void 0,type:"codeBlock"})},codeInline:{t:js(un),i:zs.LOW,l:e=>({v:e[2]}),h:(e,t,n)=>r("code",{key:n.k},e.v)},footnote:{t:Ms(mn),i:zs.MAX,l:e=>(a.push({I:e[2],j:e[1]}),{}),h:Bs},footnoteReference:{t:Fs(fn),i:zs.HIGH,l:e=>({v:e[1],B:`#${t.slugify(e[1])}`}),h:(e,t,n)=>r("a",{key:n.k,href:Ls(e.B)},r("sup",{key:n.k},e.v))},gfmTask:{t:Fs(wn),i:zs.HIGH,l:e=>({R:"x"===e[1].toLowerCase()}),h:(e,t,n)=>r("input",{checked:e.R,key:n.k,readOnly:!0,type:"checkbox"})},heading:{t:Ms(t.enforceAtxHeadings?yn:bn),i:zs.HIGH,l:(e,n,s)=>({v:Ds(n,e[2],s),T:t.slugify(e[2]),C:e[1].length}),h:(e,t,n)=>r(`h${e.C}`,{id:e.T,key:n.k},t(e.v,n))},headingSetext:{t:Ms(xn),i:zs.MAX,l:(e,t,n)=>({v:Ds(t,e[1],n),C:"="===e[2]?1:2,type:"heading"})},htmlComment:{t:Is(_n),i:zs.HIGH,l:()=>({}),h:Bs},image:{t:js(ws),i:zs.HIGH,l:e=>({D:e[1],B:Vs(e[2]),F:e[3]}),h:(e,t,n)=>r("img",{key:n.k,alt:e.D||void 0,title:e.F||void 0,src:Ls(e.B)})},link:{t:Fs(vs),i:zs.LOW,l:(e,t,n)=>({v:Es(t,e[1],n),B:Vs(e[2]),F:e[3]}),h:(e,t,n)=>r("a",{key:n.k,href:Ls(e.B),title:e.F},t(e.v,n))},linkAngleBraceStyleDetector:{t:Fs(Tn),i:zs.MAX,l:e=>({v:[{v:e[1],type:"text"}],B:e[1],type:"link"})},linkBareUrlDetector:{t:(e,t)=>t.N?null:Fs(Mn)(e,t),i:zs.MAX,l:e=>({v:[{v:e[1],type:"text"}],B:e[1],F:void 0,type:"link"})},linkMailtoDetector:{t:Fs(In),i:zs.MAX,l(e){let t=e[1],n=e[1];return sn.test(n)||(n="mailto:"+n),{v:[{v:t.replace("mailto:",""),type:"text"}],B:n,type:"link"}}},orderedList:fs(r,1),unorderedList:fs(r,2),newlineCoalescer:{t:Ms(pn),i:zs.LOW,l:As,h:()=>"\n"},paragraph:{t:Ts,i:zs.LOW,l:$s,h:(e,t,n)=>r("p",{key:n.k},t(e.v,n))},ref:{t:Fs(Dn),i:zs.MAX,l:e=>(l[e[1]]={B:e[2],F:e[4]},{}),h:Bs},refImage:{t:js(En),i:zs.MAX,l:e=>({D:e[1]||void 0,P:e[2]}),h:(e,t,n)=>r("img",{key:n.k,alt:e.D,src:Ls(l[e.P].B),title:l[e.P].F})},refLink:{t:Fs(On),i:zs.MAX,l:(e,t,n)=>({v:t(e[1],n),Z:t(e[0].replace($n,"\\$1"),n),P:e[2]}),h:(e,t,n)=>l[e.P]?r("a",{key:n.k,href:Ls(l[e.P].B),title:l[e.P].F},t(e.v,n)):r("span",{key:n.k},t(e.Z,n))},table:{t:Ms(Vn),i:zs.HIGH,l:_s,h:(e,t,n)=>r("table",{key:n.k},r("thead",null,r("tr",null,e.L.map((function(s,i){return r("th",{key:i,style:ks(e,i)},t(s,n))})))),r("tbody",null,e.A.map((function(s,i){return r("tr",{key:i},s.map((function(s,i){return r("td",{key:i,style:ks(e,i)},t(s,n))})))}))))},tableSeparator:{t:function(e,t){return t.$?(t._=!0,Nn.exec(e)):null},i:zs.HIGH,l:function(){return{type:"tableSeparator"}},h:()=>" | "},text:{t:Is(Yn),i:zs.MIN,l:e=>({v:e[0].replace(Sn,((e,n)=>t.namedCodesToUnicode[n]?t.namedCodesToUnicode[n]:e))}),h:e=>e.v},textBolded:{t:js(Wn),i:zs.MED,l:(e,t,n)=>({v:t(e[2],n)}),h:(e,t,n)=>r("strong",{key:n.k},t(e.v,n))},textEmphasized:{t:js(qn),i:zs.LOW,l:(e,t,n)=>({v:t(e[2],n)}),h:(e,t,n)=>r("em",{key:n.k},t(e.v,n))},textEscaped:{t:js(Kn),i:zs.HIGH,l:e=>({v:e[1],type:"text"})},textMarked:{t:js(Gn),i:zs.LOW,l:$s,h:(e,t,n)=>r("mark",{key:n.k},t(e.v,n))},textStrikethroughed:{t:js(Zn),i:zs.LOW,l:$s,h:(e,t,n)=>r("del",{key:n.k},t(e.v,n))}};!0!==t.disableParsingRawHTML&&(c.htmlBlock={t:Is(Cn),i:zs.HIGH,l(e,t,n){const[,s]=e[3].match(Xn),r=new RegExp(`^${s}`,"gm"),i=e[3].replace(r,""),a=(l=i,ys.some((e=>e.test(l)))?Os:Ds);var l;const c=e[1].toLowerCase(),h=-1!==tn.indexOf(c);n.N=n.N||"a"===c;const d=h?e[3]:a(t,i,n);return n.N=!1,{O:o(e[2]),v:d,G:h,H:h?c:e[1]}},h:(e,t,n)=>r(e.H,Jt({key:n.k},e.O),e.G?e.v:t(e.v,n))},c.htmlSelfClosing={t:Is(Fn),i:zs.HIGH,l:e=>({O:o(e[2]||""),H:e[1]}),h:(e,t,n)=>r(e.H,Jt({},e.O,{key:n.k}))});const h=function(e){let t=Object.keys(e);function n(s,r){let i=[],o="";for(;s;){let a=0;for(;a<t.length;){const l=t[a],c=e[l],h=c.t(s,r,o);if(h){const e=h[0];s=s.substring(e.length);const t=c.l(h,n,r);null==t.type&&(t.type=l),i.push(t),o=e;break}a++}}return i}return t.sort((function(t,n){let s=e[t].i,r=e[n].i;return s!==r?s-r:t<n?-1:1})),function(e,t){return n(function(e){return e.replace(gn,"\n").replace(vn,"").replace(Bn,"    ")}(e),t)}}(c),d=(u=c,p=function(e,t,n){return u[e.type].h(e,t,n)},function e(t,n={}){if(Array.isArray(t)){const s=n.k,r=[];let i=!1;for(let s=0;s<t.length;s++){n.k=s;const o=e(t[s],n),a="string"==typeof o;a&&i?r[r.length-1]+=o:null!==o&&r.push(o),i=a}return n.k=s,r}return p(t,e,n)});var u,p;const g=i(e);return a.length?r("div",null,g,r("footer",{key:"footer"},a.map((function(e){return r("div",{id:t.slugify(e.j),key:e.j},e.j,d(h(e.I,{_:!0})))})))):g}(Ps=zs||(zs={}))[Ps.MAX=0]="MAX",Ps[Ps.HIGH=1]="HIGH",Ps[Ps.MED=2]="MED",Ps[Ps.LOW=3]="LOW",Ps[Ps.MIN=4]="MIN";const Us=e=>{let{children:t,options:n}=e,r=function(e,t){if(null==e)return{};var n,s,r={},i=Object.keys(e);for(s=0;s<i.length;s++)t.indexOf(n=i[s])>=0||(r[n]=e[n]);return r}(e,Xt);return s.cloneElement(Hs(t,n),r)};var Ws=n(73915),qs=n.n(Ws),Gs=n(43551),Zs=n.n(Gs);class Ks extends s.Component{constructor(){super(...arguments),this.state={wasPropertyKeyModified:!1,additionalProperties:{}},this.onPropertyChange=(e,t=!1)=>(n,s,r)=>{const{formData:i,onChange:o,errorSchema:a}=this.props;void 0===n&&t&&(n=""),o({...i,[e]:n},a&&a&&{...a,[e]:s},r)},this.onDropPropertyClick=e=>t=>{t.preventDefault();const{onChange:n,formData:s}=this.props,r={...s};Zs()(r,e),n(r)},this.getAvailableKey=(e,t)=>{const{uiSchema:n,registry:s}=this.props,{duplicateKeySuffixSeparator:r="-"}=(0,Ct.getUiOptions)(n,s.globalUiOptions);let i=0,o=e;for(;qs()(t,o);)o=`${e}${r}${++i}`;return o},this.onKeyChange=e=>(t,n)=>{if(e===t)return;const{formData:s,onChange:r,errorSchema:i}=this.props;t=this.getAvailableKey(t,s);const o={...s},a={[e]:t},l=Object.keys(o).map((e=>({[a[e]||e]:o[e]}))),c=Object.assign({},...l);this.setState({wasPropertyKeyModified:!0}),r(c,i&&i&&{...i,[t]:n})},this.handleAddClick=e=>()=>{if(!e.additionalProperties)return;const{formData:t,onChange:n,registry:s}=this.props,r={...t};let i;if(Et()(e.additionalProperties)){i=e.additionalProperties.type;let n=e.additionalProperties;if(Ct.REF_KEY in n){const{schemaUtils:e}=s;n=e.retrieveSchema({$ref:n[Ct.REF_KEY]},t),i=n.type}i||!(Ct.ANY_OF_KEY in n)&&!(Ct.ONE_OF_KEY in n)||(i="object")}const o=this.getAvailableKey("newKey",r);$t()(r,o,this.getDefaultValue(i)),n(r)}}isRequired(e){const{schema:t}=this.props;return Array.isArray(t.required)&&-1!==t.required.indexOf(e)}getDefaultValue(e){const{registry:{translateString:t}}=this.props;switch(e){case"array":return[];case"boolean":return!1;case"null":return null;case"number":return 0;case"object":return{};default:return t(Ct.TranslatableString.NewStringDefault)}}render(){var e,t,n;const{schema:s,uiSchema:r={},formData:i,errorSchema:o,idSchema:a,name:l,required:c=!1,disabled:h=!1,readonly:d=!1,hideError:u,idPrefix:p,idSeparator:g,onBlur:m,onFocus:f,registry:v}=this.props,{fields:w,formContext:b,schemaUtils:y,translateString:x,globalUiOptions:C}=v,{SchemaField:S}=w,_=y.retrieveSchema(s,i),k=(0,Ct.getUiOptions)(r,C),{properties:F={}}=_,j=null!==(t=null!==(e=k.title)&&void 0!==e?e:_.title)&&void 0!==t?t:l,M=null!==(n=k.description)&&void 0!==n?n:_.description;let I;try{const e=Object.keys(F);I=(0,Ct.orderProperties)(e,k.order)}catch(e){return(0,xt.jsxs)("div",{children:[(0,xt.jsx)("p",{className:"config-error",style:{color:"red"},children:(0,xt.jsx)(Us,{children:x(Ct.TranslatableString.InvalidObjectField,[l||"root",e.message])})}),(0,xt.jsx)("pre",{children:JSON.stringify(_)})]})}const T=(0,Ct.getTemplate)("ObjectFieldTemplate",v,k),L={title:!1===k.label?"":j,description:!1===k.label?void 0:M,properties:I.map((e=>{const t=qs()(_,[Ct.PROPERTIES_KEY,e,Ct.ADDITIONAL_PROPERTY_FLAG]),n=t?r.additionalProperties:r[e],s="hidden"===(0,Ct.getUiOptions)(n).widget,l=_t()(a,[e],{});return{content:(0,xt.jsx)(S,{name:e,required:this.isRequired(e),schema:_t()(_,[Ct.PROPERTIES_KEY,e],{}),uiSchema:n,errorSchema:_t()(o,e),idSchema:l,idPrefix:p,idSeparator:g,formData:_t()(i,e),formContext:b,wasPropertyKeyModified:this.state.wasPropertyKeyModified,onKeyChange:this.onKeyChange(e),onChange:this.onPropertyChange(e,t),onBlur:m,onFocus:f,registry:v,disabled:h,readonly:d,hideError:u,onDropPropertyClick:this.onDropPropertyClick},e),name:e,readonly:d,disabled:h,required:c,hidden:s}})),readonly:d,disabled:h,required:c,idSchema:a,uiSchema:r,errorSchema:o,schema:_,formData:i,formContext:b,registry:v};return(0,xt.jsx)(T,{...L,onAddClick:this.handleAddClick})}}const Ys=Ks,Js={array:"ArrayField",boolean:"BooleanField",integer:"NumberField",number:"NumberField",object:"ObjectField",string:"StringField",null:"NullField"};function Xs(e){const{schema:t,idSchema:n,uiSchema:r,formData:i,errorSchema:o,idPrefix:a,idSeparator:l,name:c,onChange:h,onKeyChange:d,onDropPropertyClick:u,required:p,registry:g,wasPropertyKeyModified:m=!1}=e,{formContext:f,schemaUtils:v,globalUiOptions:w}=g,b=(0,Ct.getUiOptions)(r,w),y=(0,Ct.getTemplate)("FieldTemplate",g,b),x=(0,Ct.getTemplate)("DescriptionFieldTemplate",g,b),C=(0,Ct.getTemplate)("FieldHelpTemplate",g,b),S=(0,Ct.getTemplate)("FieldErrorTemplate",g,b),_=v.retrieveSchema(t,i),k=n[Ct.ID_KEY],F=(0,Ct.mergeObjects)(v.toIdSchema(_,k,i,a,l),n),j=(0,s.useCallback)(((e,t,n)=>h(e,t,n||k)),[k,h]),M=function(e,t,n,s){const r=t.field,{fields:i,translateString:o}=s;if("function"==typeof r)return r;if("string"==typeof r&&r in i)return i[r];const a=(0,Ct.getSchemaType)(e),l=Array.isArray(a)?a[0]:a||"",c=e.$id;let h=Js[l];return c&&c in i&&(h=c),h||!e.anyOf&&!e.oneOf?h in i?i[h]:()=>{const r=(0,Ct.getTemplate)("UnsupportedFieldTemplate",s,t);return(0,xt.jsx)(r,{schema:e,idSchema:n,reason:o(Ct.TranslatableString.UnknownFieldType,[String(e.type)]),registry:s})}:()=>null}(_,b,F,g),I=Boolean(e.disabled||b.disabled),T=Boolean(e.readonly||b.readonly||e.schema.readOnly||_.readOnly),L=b.hideError,V=void 0===L?e.hideError:Boolean(L),D=Boolean(e.autofocus||b.autofocus);if(0===Object.keys(_).length)return null;const E=v.getDisplayLabel(_,r,w),{__errors:O,...$}=o||{},A=Wt()(r,["ui:classNames","classNames","ui:style"]);Ct.UI_OPTIONS_KEY in A&&(A[Ct.UI_OPTIONS_KEY]=Wt()(A[Ct.UI_OPTIONS_KEY],["classNames","style"]));const B=(0,xt.jsx)(M,{...e,onChange:j,idSchema:F,schema:_,uiSchema:A,disabled:I,readonly:T,hideError:V,autofocus:D,errorSchema:$,formContext:f,rawErrors:O}),N=F[Ct.ID_KEY];let R;R=m||Ct.ADDITIONAL_PROPERTY_FLAG in _?c:b.title||e.schema.title||_.title||c;const z=b.description||e.schema.description||_.description||"",P=b.enableMarkdownInDescription?(0,xt.jsx)(Us,{children:z}):z,H=b.help,U="hidden"===b.widget,W=["form-group","field",`field-${(0,Ct.getSchemaType)(_)}`];!V&&O&&O.length>0&&W.push("field-error has-error has-danger"),(null==r?void 0:r.classNames)&&W.push(r.classNames),b.classNames&&W.push(b.classNames);const q=(0,xt.jsx)(C,{help:H,idSchema:F,schema:_,uiSchema:r,hasErrors:!V&&O&&O.length>0,registry:g}),G=V||(_.anyOf||_.oneOf)&&!v.isSelect(_)?void 0:(0,xt.jsx)(S,{errors:O,errorSchema:o,idSchema:F,schema:_,uiSchema:r,registry:g}),Z={description:(0,xt.jsx)(x,{id:(0,Ct.descriptionId)(N),description:P,schema:_,uiSchema:r,registry:g}),rawDescription:z,help:q,rawHelp:"string"==typeof H?H:void 0,errors:G,rawErrors:V?void 0:O,id:N,label:R,hidden:U,onChange:h,onKeyChange:d,onDropPropertyClick:u,required:p,disabled:I,readonly:T,hideError:V,displayLabel:E,classNames:W.join(" ").trim(),style:b.style,formContext:f,formData:i,schema:_,uiSchema:r,registry:g},K=g.fields.AnyOfField,Y=g.fields.OneOfField,J=(null==r?void 0:r["ui:field"])&&!0===(null==r?void 0:r["ui:fieldReplacesAnyOrOneOf"]);return(0,xt.jsx)(y,{...Z,children:(0,xt.jsxs)(xt.Fragment,{children:[B,_.anyOf&&!J&&!v.isSelect(_)&&(0,xt.jsx)(K,{name:c,disabled:I,readonly:T,hideError:V,errorSchema:o,formData:i,formContext:f,idPrefix:a,idSchema:F,idSeparator:l,onBlur:e.onBlur,onChange:e.onChange,onFocus:e.onFocus,options:_.anyOf.map((e=>v.retrieveSchema(Et()(e)?e:{},i))),registry:g,schema:_,uiSchema:r}),_.oneOf&&!J&&!v.isSelect(_)&&(0,xt.jsx)(Y,{name:c,disabled:I,readonly:T,hideError:V,errorSchema:o,formData:i,formContext:f,idPrefix:a,idSchema:F,idSeparator:l,onBlur:e.onBlur,onChange:e.onChange,onFocus:e.onFocus,options:_.oneOf.map((e=>v.retrieveSchema(Et()(e)?e:{},i))),registry:g,schema:_,uiSchema:r})]})})}class Qs extends s.Component{shouldComponentUpdate(e){return!(0,Ct.deepEquals)(this.props,e)}render(){return(0,xt.jsx)(Xs,{...this.props})}}const er=Qs,tr=function(e){var t;const{schema:n,name:s,uiSchema:r,idSchema:i,formData:o,required:a,disabled:l=!1,readonly:c=!1,autofocus:h=!1,onChange:d,onBlur:u,onFocus:p,registry:g,rawErrors:m,hideError:f}=e,{title:v,format:w}=n,{widgets:b,formContext:y,schemaUtils:x,globalUiOptions:C}=g,S=x.isSelect(n)?(0,Ct.optionsList)(n):void 0;let _=S?"select":"text";w&&(0,Ct.hasWidget)(n,w,b)&&(_=w);const{widget:k=_,placeholder:F="",title:j,...M}=(0,Ct.getUiOptions)(r),I=x.getDisplayLabel(n,r,C),T=null!==(t=null!=j?j:v)&&void 0!==t?t:s,L=(0,Ct.getWidget)(n,k,b);return(0,xt.jsx)(L,{options:{...M,enumOptions:S},schema:n,uiSchema:r,id:i.$id,name:s,label:T,hideLabel:!I,hideError:f,value:o,onChange:d,onBlur:u,onFocus:p,required:a,disabled:l,readonly:c,formContext:y,autofocus:h,registry:g,placeholder:F,rawErrors:m})},nr=function(e){const{formData:t,onChange:n}=e;return(0,s.useEffect)((()=>{void 0===t&&n(null)}),[t,n]),null};function sr(e){const{idSchema:t,description:n,registry:s,schema:r,uiSchema:i}=e,o=(0,Ct.getUiOptions)(i,s.globalUiOptions),{label:a=!0}=o;if(!n||!a)return null;const l=(0,Ct.getTemplate)("DescriptionFieldTemplate",s,o);return(0,xt.jsx)(l,{id:(0,Ct.descriptionId)(t),description:n,schema:r,uiSchema:i,registry:s})}function rr(e){const{children:t,className:n,disabled:s,hasToolbar:r,hasMoveDown:i,hasMoveUp:o,hasRemove:a,hasCopy:l,index:c,onCopyIndexClick:h,onDropIndexClick:d,onReorderClick:u,readonly:p,registry:g,uiSchema:m}=e,{CopyButton:f,MoveDownButton:v,MoveUpButton:w,RemoveButton:b}=g.templates.ButtonTemplates,y={flex:1,paddingLeft:6,paddingRight:6,fontWeight:"bold"};return(0,xt.jsxs)("div",{className:n,children:[(0,xt.jsx)("div",{className:r?"col-xs-9":"col-xs-12",children:t}),r&&(0,xt.jsx)("div",{className:"col-xs-3 array-item-toolbox",children:(0,xt.jsxs)("div",{className:"btn-group",style:{display:"flex",justifyContent:"space-around"},children:[(o||i)&&(0,xt.jsx)(w,{style:y,disabled:s||p||!o,onClick:u(c,c-1),uiSchema:m,registry:g}),(o||i)&&(0,xt.jsx)(v,{style:y,disabled:s||p||!i,onClick:u(c,c+1),uiSchema:m,registry:g}),l&&(0,xt.jsx)(f,{style:y,disabled:s||p,onClick:h(c),uiSchema:m,registry:g}),a&&(0,xt.jsx)(b,{style:y,disabled:s||p,onClick:d(c),uiSchema:m,registry:g})]})})]})}function ir(e){const{canAdd:t,className:n,disabled:s,idSchema:r,uiSchema:i,items:o,onAddClick:a,readonly:l,registry:c,required:h,schema:d,title:u}=e,p=(0,Ct.getUiOptions)(i),g=(0,Ct.getTemplate)("ArrayFieldDescriptionTemplate",c,p),m=(0,Ct.getTemplate)("ArrayFieldItemTemplate",c,p),f=(0,Ct.getTemplate)("ArrayFieldTitleTemplate",c,p),{ButtonTemplates:{AddButton:v}}=c.templates;return(0,xt.jsxs)("fieldset",{className:n,id:r.$id,children:[(0,xt.jsx)(f,{idSchema:r,title:p.title||u,required:h,schema:d,uiSchema:i,registry:c}),(0,xt.jsx)(g,{idSchema:r,description:p.description||d.description,schema:d,uiSchema:i,registry:c}),(0,xt.jsx)("div",{className:"row array-item-list",children:o&&o.map((({key:e,...t})=>(0,xt.jsx)(m,{...t},e)))}),t&&(0,xt.jsx)(v,{className:"array-item-add",onClick:a,disabled:s||l,uiSchema:i,registry:c})]})}function or(e){const{idSchema:t,title:n,schema:s,uiSchema:r,required:i,registry:o}=e,a=(0,Ct.getUiOptions)(r,o.globalUiOptions),{label:l=!0}=a;if(!n||!l)return null;const c=(0,Ct.getTemplate)("TitleFieldTemplate",o,a);return(0,xt.jsx)(c,{id:(0,Ct.titleId)(t),title:n,required:i,schema:s,uiSchema:r,registry:o})}function ar(e){const{id:t,name:n,value:r,readonly:i,disabled:o,autofocus:a,onBlur:l,onFocus:c,onChange:h,onChangeOverride:d,options:u,schema:p,uiSchema:g,formContext:m,registry:f,rawErrors:v,type:w,hideLabel:b,hideError:y,...x}=e;if(!t)throw console.log("No id for",e),new Error(`no id for props ${JSON.stringify(e)}`);const C={...x,...(0,Ct.getInputProps)(p,w,u)};let S;S="number"===C.type||"integer"===C.type?r||0===r?r:"":null==r?"":r;const _=(0,s.useCallback)((({target:{value:e}})=>h(""===e?u.emptyValue:e)),[h,u]),k=(0,s.useCallback)((({target:{value:e}})=>l(t,e)),[l,t]),F=(0,s.useCallback)((({target:{value:e}})=>c(t,e)),[c,t]);return(0,xt.jsxs)(xt.Fragment,{children:[(0,xt.jsx)("input",{id:t,name:t,className:"form-control",readOnly:i,disabled:o,autoFocus:a,value:S,...C,list:p.examples?(0,Ct.examplesId)(t):void 0,onChange:d||_,onBlur:k,onFocus:F,"aria-describedby":(0,Ct.ariaDescribedByIds)(t,!!p.examples)}),Array.isArray(p.examples)&&(0,xt.jsx)("datalist",{id:(0,Ct.examplesId)(t),children:p.examples.concat(p.default&&!p.examples.includes(p.default)?[p.default]:[]).map((e=>(0,xt.jsx)("option",{value:e},e)))},`datalist_${t}`)]})}function lr({uiSchema:e}){const{submitText:t,norender:n,props:s={}}=(0,Ct.getSubmitButtonOptions)(e);return n?null:(0,xt.jsx)("div",{children:(0,xt.jsx)("button",{type:"submit",...s,className:`btn btn-info ${s.className||""}`,children:t})})}function cr(e){const{iconType:t="default",icon:n,className:s,uiSchema:r,registry:i,...o}=e;return(0,xt.jsx)("button",{type:"button",className:`btn btn-${t} ${s}`,...o,children:(0,xt.jsx)("i",{className:`glyphicon glyphicon-${n}`})})}function hr(e){const{registry:{translateString:t}}=e;return(0,xt.jsx)(cr,{title:t(Ct.TranslatableString.CopyButton),className:"array-item-copy",...e,icon:"copy"})}function dr(e){const{registry:{translateString:t}}=e;return(0,xt.jsx)(cr,{title:t(Ct.TranslatableString.MoveDownButton),className:"array-item-move-down",...e,icon:"arrow-down"})}function ur(e){const{registry:{translateString:t}}=e;return(0,xt.jsx)(cr,{title:t(Ct.TranslatableString.MoveUpButton),className:"array-item-move-up",...e,icon:"arrow-up"})}function pr(e){const{registry:{translateString:t}}=e;return(0,xt.jsx)(cr,{title:t(Ct.TranslatableString.RemoveButton),className:"array-item-remove",...e,iconType:"danger",icon:"remove"})}function gr({className:e,onClick:t,disabled:n,registry:s}){const{translateString:r}=s;return(0,xt.jsx)("div",{className:"row",children:(0,xt.jsx)("p",{className:`col-xs-3 col-xs-offset-9 text-right ${e}`,children:(0,xt.jsx)(cr,{iconType:"info",icon:"plus",className:"btn-add col-xs-12",title:r(Ct.TranslatableString.AddButton),onClick:t,disabled:n,registry:s})})})}function mr(e){const{id:t,description:n}=e;return n?"string"==typeof n?(0,xt.jsx)("p",{id:t,className:"field-description",children:n}):(0,xt.jsx)("div",{id:t,className:"field-description",children:n}):null}function fr({errors:e,registry:t}){const{translateString:n}=t;return(0,xt.jsxs)("div",{className:"panel panel-danger errors",children:[(0,xt.jsx)("div",{className:"panel-heading",children:(0,xt.jsx)("h3",{className:"panel-title",children:n(Ct.TranslatableString.ErrorsLabel)})}),(0,xt.jsx)("ul",{className:"list-group",children:e.map(((e,t)=>(0,xt.jsx)("li",{className:"list-group-item text-danger",children:e.stack},t)))})]})}const vr="*";function wr(e){const{label:t,required:n,id:s}=e;return t?(0,xt.jsxs)("label",{className:"control-label",htmlFor:s,children:[t,n&&(0,xt.jsx)("span",{className:"required",children:vr})]}):null}const br=function(e){const{id:t,label:n,children:s,errors:r,help:i,description:o,hidden:a,required:l,displayLabel:c,registry:h,uiSchema:d}=e,u=(0,Ct.getUiOptions)(d),p=(0,Ct.getTemplate)("WrapIfAdditionalTemplate",h,u);return a?(0,xt.jsx)("div",{className:"hidden",children:s}):(0,xt.jsxs)(p,{...e,children:[c&&(0,xt.jsx)(wr,{label:n,required:l,id:t}),c&&o?o:null,s,r,i]})};function yr(e){const{errors:t=[],idSchema:n}=e;if(0===t.length)return null;const s=(0,Ct.errorId)(n);return(0,xt.jsx)("div",{children:(0,xt.jsx)("ul",{id:s,className:"error-detail bs-callout bs-callout-info",children:t.filter((e=>!!e)).map(((e,t)=>(0,xt.jsx)("li",{className:"text-danger",children:e},t)))})})}function xr(e){const{idSchema:t,help:n}=e;if(!n)return null;const s=(0,Ct.helpId)(t);return"string"==typeof n?(0,xt.jsx)("p",{id:s,className:"help-block",children:n}):(0,xt.jsx)("div",{id:s,className:"help-block",children:n})}function Cr(e){const{description:t,disabled:n,formData:s,idSchema:r,onAddClick:i,properties:o,readonly:a,registry:l,required:c,schema:h,title:d,uiSchema:u}=e,p=(0,Ct.getUiOptions)(u),g=(0,Ct.getTemplate)("TitleFieldTemplate",l,p),m=(0,Ct.getTemplate)("DescriptionFieldTemplate",l,p),{ButtonTemplates:{AddButton:f}}=l.templates;return(0,xt.jsxs)("fieldset",{id:r.$id,children:[d&&(0,xt.jsx)(g,{id:(0,Ct.titleId)(r),title:d,required:c,schema:h,uiSchema:u,registry:l}),t&&(0,xt.jsx)(m,{id:(0,Ct.descriptionId)(r),description:t,schema:h,uiSchema:u,registry:l}),o.map((e=>e.content)),(0,Ct.canExpand)(h,u,s)&&(0,xt.jsx)(f,{className:"object-property-expand",onClick:i(h),disabled:n||a,uiSchema:u,registry:l})]})}function Sr(e){const{id:t,title:n,required:s}=e;return(0,xt.jsxs)("legend",{id:t,children:[n,s&&(0,xt.jsx)("span",{className:"required",children:"*"})]})}const _r=function(e){const{schema:t,idSchema:n,reason:s,registry:r}=e,{translateString:i}=r;let o=Ct.TranslatableString.UnsupportedField;const a=[];return n&&n.$id&&(o=Ct.TranslatableString.UnsupportedFieldWithId,a.push(n.$id)),s&&(o=o===Ct.TranslatableString.UnsupportedField?Ct.TranslatableString.UnsupportedFieldWithReason:Ct.TranslatableString.UnsupportedFieldWithIdAndReason,a.push(s)),(0,xt.jsxs)("div",{className:"unsupported-field",children:[(0,xt.jsx)("p",{children:(0,xt.jsx)(Us,{children:i(o,a)})}),t&&(0,xt.jsx)("pre",{children:JSON.stringify(t,null,2)})]})};function kr(e){const{id:t,classNames:n,style:s,disabled:r,label:i,onKeyChange:o,onDropPropertyClick:a,readonly:l,required:c,schema:h,children:d,uiSchema:u,registry:p}=e,{templates:g,translateString:m}=p,{RemoveButton:f}=g.ButtonTemplates,v=m(Ct.TranslatableString.KeyLabel,[i]);return Ct.ADDITIONAL_PROPERTY_FLAG in h?(0,xt.jsx)("div",{className:n,style:s,children:(0,xt.jsxs)("div",{className:"row",children:[(0,xt.jsx)("div",{className:"col-xs-5 form-additional",children:(0,xt.jsxs)("div",{className:"form-group",children:[(0,xt.jsx)(wr,{label:v,required:c,id:`${t}-key`}),(0,xt.jsx)("input",{className:"form-control",type:"text",id:`${t}-key`,onBlur:e=>o(e.target.value),defaultValue:i})]})}),(0,xt.jsx)("div",{className:"form-additional form-group col-xs-5",children:d}),(0,xt.jsx)("div",{className:"col-xs-2",children:(0,xt.jsx)(f,{className:"array-item-remove btn-block",style:{border:"0"},disabled:r||l,onClick:a(i),uiSchema:u,registry:p})})]})}):(0,xt.jsx)("div",{className:n,style:s,children:d})}function Fr(e,t){const n=[];for(let s=e;s<=t;s++)n.push({value:s,label:(0,Ct.pad)(s,2)});return n}function jr(e,t,n=[1900,(new Date).getFullYear()+2]){const{year:s,month:r,day:i,hour:o,minute:a,second:l}=e,c=[{type:"year",range:n,value:s},{type:"month",range:[1,12],value:r},{type:"day",range:[1,31],value:i}];return t&&c.push({type:"hour",range:[0,23],value:o},{type:"minute",range:[0,59],value:a},{type:"second",range:[0,59],value:l}),c}function Mr({type:e,range:t,value:n,select:s,rootId:r,name:i,disabled:o,readonly:a,autofocus:l,registry:c,onBlur:h,onFocus:d}){const u=r+"_"+e,{SelectWidget:p}=c.widgets;return(0,xt.jsx)(p,{schema:{type:"integer"},id:u,name:i,className:"form-control",options:{enumOptions:Fr(t[0],t[1])},placeholder:e,value:n,disabled:o,readonly:a,autofocus:l,onChange:t=>s(e,t),onBlur:h,onFocus:d,registry:c,label:"","aria-describedby":(0,Ct.ariaDescribedByIds)(r)})}const Ir=function({time:e=!1,disabled:t=!1,readonly:n=!1,autofocus:r=!1,options:i,id:o,name:a,registry:l,onBlur:c,onFocus:h,onChange:d,value:u}){const{translateString:p}=l,[g,m]=(0,s.useState)(u),[f,v]=(0,s.useReducer)(((e,t)=>({...e,...t})),(0,Ct.parseDateString)(u,e));(0,s.useEffect)((()=>{const t=(0,Ct.toDateString)(f,e);!function(e){return Object.values(e).every((e=>-1!==e))}(f)||t===u?g!==u&&(m(u),v((0,Ct.parseDateString)(u,e))):d(t)}),[e,u,d,f,g]);const w=(0,s.useCallback)(((e,t)=>{v({[e]:t})}),[]),b=(0,s.useCallback)((s=>{if(s.preventDefault(),t||n)return;const r=(0,Ct.parseDateString)((new Date).toJSON(),e);d((0,Ct.toDateString)(r,e))}),[t,n,e]),y=(0,s.useCallback)((e=>{e.preventDefault(),t||n||d(void 0)}),[t,n,d]);return(0,xt.jsxs)("ul",{className:"list-inline",children:[jr(f,e,i.yearsRange).map(((e,s)=>(0,xt.jsx)("li",{className:"list-inline-item",children:(0,xt.jsx)(Mr,{rootId:o,name:a,select:w,...e,disabled:t,readonly:n,registry:l,onBlur:c,onFocus:h,autofocus:r&&0===s})},s))),("undefined"===i.hideNowButton||!i.hideNowButton)&&(0,xt.jsx)("li",{className:"list-inline-item",children:(0,xt.jsx)("a",{href:"#",className:"btn btn-info btn-now",onClick:b,children:p(Ct.TranslatableString.NowLabel)})}),("undefined"===i.hideClearButton||!i.hideClearButton)&&(0,xt.jsx)("li",{className:"list-inline-item",children:(0,xt.jsx)("a",{href:"#",className:"btn btn-warning btn-clear",onClick:y,children:p(Ct.TranslatableString.ClearLabel)})})]})},Tr=function({time:e=!0,...t}){const{AltDateWidget:n}=t.registry.widgets;return(0,xt.jsx)(n,{time:e,...t})},Lr=function({schema:e,uiSchema:t,options:n,id:r,value:i,disabled:o,readonly:a,label:l,hideLabel:c,autofocus:h=!1,onBlur:d,onFocus:u,onChange:p,registry:g}){var m;const f=(0,Ct.getTemplate)("DescriptionFieldTemplate",g,n),v=(0,Ct.schemaRequiresTrueValue)(e),w=(0,s.useCallback)((e=>p(e.target.checked)),[p]),b=(0,s.useCallback)((e=>d(r,e.target.checked)),[d,r]),y=(0,s.useCallback)((e=>u(r,e.target.checked)),[u,r]),x=null!==(m=n.description)&&void 0!==m?m:e.description;return(0,xt.jsxs)("div",{className:"checkbox "+(o||a?"disabled":""),children:[!c&&!!x&&(0,xt.jsx)(f,{id:(0,Ct.descriptionId)(r),description:x,schema:e,uiSchema:t,registry:g}),(0,xt.jsxs)("label",{children:[(0,xt.jsx)("input",{type:"checkbox",id:r,name:r,checked:void 0!==i&&i,required:v,disabled:o||a,autoFocus:h,onChange:w,onBlur:b,onFocus:y,"aria-describedby":(0,Ct.ariaDescribedByIds)(r)}),(0,Ct.labelValue)((0,xt.jsx)("span",{children:l}),c)]})]})},Vr=function({id:e,disabled:t,options:{inline:n=!1,enumOptions:r,enumDisabled:i,emptyValue:o},value:a,autofocus:l=!1,readonly:c,onChange:h,onBlur:d,onFocus:u}){const p=Array.isArray(a)?a:[a],g=(0,s.useCallback)((({target:{value:t}})=>d(e,(0,Ct.enumOptionsValueForIndex)(t,r,o))),[d,e]),m=(0,s.useCallback)((({target:{value:t}})=>u(e,(0,Ct.enumOptionsValueForIndex)(t,r,o))),[u,e]);return(0,xt.jsx)("div",{className:"checkboxes",id:e,children:Array.isArray(r)&&r.map(((s,o)=>{const a=(0,Ct.enumOptionsIsSelected)(s.value,p),d=Array.isArray(i)&&-1!==i.indexOf(s.value),u=t||d||c?"disabled":"",f=(0,xt.jsxs)("span",{children:[(0,xt.jsx)("input",{type:"checkbox",id:(0,Ct.optionId)(e,o),name:e,checked:a,value:String(o),disabled:t||d||c,autoFocus:l&&0===o,onChange:e=>{e.target.checked?h((0,Ct.enumOptionsSelectValue)(o,p,r)):h((0,Ct.enumOptionsDeselectValue)(o,p,r))},onBlur:g,onFocus:m,"aria-describedby":(0,Ct.ariaDescribedByIds)(e)}),(0,xt.jsx)("span",{children:s.label})]});return n?(0,xt.jsx)("label",{className:`checkbox-inline ${u}`,children:f},o):(0,xt.jsx)("div",{className:`checkbox ${u}`,children:(0,xt.jsx)("label",{children:f})},o)}))})};function Dr(e){const{disabled:t,readonly:n,options:s,registry:r}=e,i=(0,Ct.getTemplate)("BaseInputTemplate",r,s);return(0,xt.jsx)(i,{type:"color",...e,disabled:t||n})}function Er(e){const{onChange:t,options:n,registry:r}=e,i=(0,Ct.getTemplate)("BaseInputTemplate",r,n),o=(0,s.useCallback)((e=>t(e||void 0)),[t]);return(0,xt.jsx)(i,{type:"date",...e,onChange:o})}function Or(e){const{onChange:t,value:n,options:s,registry:r}=e,i=(0,Ct.getTemplate)("BaseInputTemplate",r,s);return(0,xt.jsx)(i,{type:"datetime-local",...e,value:(0,Ct.utcToLocal)(n),onChange:e=>t((0,Ct.localToUTC)(e))})}function $r(e){const{options:t,registry:n}=e,s=(0,Ct.getTemplate)("BaseInputTemplate",n,t);return(0,xt.jsx)(s,{type:"email",...e})}function Ar(e,t){return null===e?null:e.replace(";base64",`;name=${encodeURIComponent(t)};base64`)}function Br(e){const{name:t,size:n,type:s}=e;return new Promise(((r,i)=>{const o=new window.FileReader;o.onerror=i,o.onload=e=>{var i;"string"==typeof(null===(i=e.target)||void 0===i?void 0:i.result)?r({dataURL:Ar(e.target.result,t),name:t,size:n,type:s}):r({dataURL:null,name:t,size:n,type:s})},o.readAsDataURL(e)}))}function Nr({fileInfo:e,registry:t}){const{translateString:n}=t,{dataURL:s,type:r,name:i}=e;return s?-1!==r.indexOf("image")?(0,xt.jsx)("img",{src:s,style:{maxWidth:"100%"},className:"file-preview"}):(0,xt.jsxs)(xt.Fragment,{children:[" ",(0,xt.jsx)("a",{download:`preview-${i}`,href:s,className:"file-download",children:n(Ct.TranslatableString.PreviewLabel)})]}):null}function Rr({filesInfo:e,registry:t,preview:n}){if(0===e.length)return null;const{translateString:s}=t;return(0,xt.jsx)("ul",{className:"file-info",children:e.map(((e,r)=>{const{name:i,size:o,type:a}=e;return(0,xt.jsxs)("li",{children:[(0,xt.jsx)(Us,{children:s(Ct.TranslatableString.FilesInfo,[i,a,String(o)])}),n&&(0,xt.jsx)(Nr,{fileInfo:e,registry:t})]},r)}))})}function zr(e){return e.filter((e=>e)).map((e=>{const{blob:t,name:n}=(0,Ct.dataURItoBlob)(e);return{dataURL:e,name:n,size:t.size,type:t.type}}))}const Pr=function(e){const{disabled:t,readonly:n,required:r,multiple:i,onChange:o,value:a,options:l,registry:c}=e,h=(0,Ct.getTemplate)("BaseInputTemplate",c,l),[d,u]=(0,s.useState)(Array.isArray(a)?zr(a):zr([a])),p=(0,s.useCallback)((e=>{var t;e.target.files&&(t=e.target.files,Promise.all(Array.from(t).map(Br))).then((e=>{const t=e.map((e=>e.dataURL));i?(u(d.concat(e[0])),o(a.concat(t[0]))):(u(e),o(t[0]))}))}),[i,a,d,o]);return(0,xt.jsxs)("div",{children:[(0,xt.jsx)(h,{...e,disabled:t||n,type:"file",required:!a&&r,onChangeOverride:p,value:"",accept:l.accept?String(l.accept):void 0}),(0,xt.jsx)(Rr,{filesInfo:d,registry:c,preview:l.filePreview})]})},Hr=function({id:e,value:t}){return(0,xt.jsx)("input",{type:"hidden",id:e,name:e,value:void 0===t?"":t})};function Ur(e){const{options:t,registry:n}=e,s=(0,Ct.getTemplate)("BaseInputTemplate",n,t);return(0,xt.jsx)(s,{type:"password",...e})}const Wr=function({options:e,value:t,required:n,disabled:r,readonly:i,autofocus:o=!1,onBlur:a,onFocus:l,onChange:c,id:h}){const{enumOptions:d,enumDisabled:u,inline:p,emptyValue:g}=e,m=(0,s.useCallback)((({target:{value:e}})=>a(h,(0,Ct.enumOptionsValueForIndex)(e,d,g))),[a,h]),f=(0,s.useCallback)((({target:{value:e}})=>l(h,(0,Ct.enumOptionsValueForIndex)(e,d,g))),[l,h]);return(0,xt.jsx)("div",{className:"field-radio-group",id:h,children:Array.isArray(d)&&d.map(((e,s)=>{const a=(0,Ct.enumOptionsIsSelected)(e.value,t),l=Array.isArray(u)&&-1!==u.indexOf(e.value),d=r||l||i?"disabled":"",g=(0,xt.jsxs)("span",{children:[(0,xt.jsx)("input",{type:"radio",id:(0,Ct.optionId)(h,s),checked:a,name:h,required:n,value:String(s),disabled:r||l||i,autoFocus:o&&0===s,onChange:()=>c(e.value),onBlur:m,onFocus:f,"aria-describedby":(0,Ct.ariaDescribedByIds)(h)}),(0,xt.jsx)("span",{children:e.label})]});return p?(0,xt.jsx)("label",{className:`radio-inline ${d}`,children:g},s):(0,xt.jsx)("div",{className:`radio ${d}`,children:(0,xt.jsx)("label",{children:g})},s)}))})};function qr(e){const{value:t,registry:{templates:{BaseInputTemplate:n}}}=e;return(0,xt.jsxs)("div",{className:"field-range-wrapper",children:[(0,xt.jsx)(n,{type:"range",...e}),(0,xt.jsx)("span",{className:"range-view",children:t})]})}function Gr(e,t){return t?Array.from(e.target.options).slice().filter((e=>e.selected)).map((e=>e.value)):e.target.value}const Zr=function({schema:e,id:t,options:n,value:r,required:i,disabled:o,readonly:a,multiple:l=!1,autofocus:c=!1,onChange:h,onBlur:d,onFocus:u,placeholder:p}){const{enumOptions:g,enumDisabled:m,emptyValue:f}=n,v=l?[]:"",w=(0,s.useCallback)((e=>{const n=Gr(e,l);return u(t,(0,Ct.enumOptionsValueForIndex)(n,g,f))}),[u,t,e,l,n]),b=(0,s.useCallback)((e=>{const n=Gr(e,l);return d(t,(0,Ct.enumOptionsValueForIndex)(n,g,f))}),[d,t,e,l,n]),y=(0,s.useCallback)((e=>{const t=Gr(e,l);return h((0,Ct.enumOptionsValueForIndex)(t,g,f))}),[h,e,l,n]),x=(0,Ct.enumOptionsIndexForValue)(r,g,l);return(0,xt.jsxs)("select",{id:t,name:t,multiple:l,className:"form-control",value:void 0===x?v:x,required:i,disabled:o||a,autoFocus:c,onBlur:b,onFocus:w,onChange:y,"aria-describedby":(0,Ct.ariaDescribedByIds)(t),children:[!l&&void 0===e.default&&(0,xt.jsx)("option",{value:"",children:p}),Array.isArray(g)&&g.map((({value:e,label:t},n)=>{const s=m&&-1!==m.indexOf(e);return(0,xt.jsx)("option",{value:String(n),disabled:s,children:t},n)}))]})};function Kr({id:e,options:t={},placeholder:n,value:r,required:i,disabled:o,readonly:a,autofocus:l=!1,onChange:c,onBlur:h,onFocus:d}){const u=(0,s.useCallback)((({target:{value:e}})=>c(""===e?t.emptyValue:e)),[c,t.emptyValue]),p=(0,s.useCallback)((({target:{value:t}})=>h(e,t)),[h,e]),g=(0,s.useCallback)((({target:{value:t}})=>d(e,t)),[e,d]);return(0,xt.jsx)("textarea",{id:e,name:e,className:"form-control",value:r||"",placeholder:n,required:i,disabled:o,readOnly:a,autoFocus:l,rows:t.rows,onBlur:p,onFocus:g,onChange:u,"aria-describedby":(0,Ct.ariaDescribedByIds)(e)})}Kr.defaultProps={autofocus:!1,options:{}};const Yr=Kr;function Jr(e){const{options:t,registry:n}=e,s=(0,Ct.getTemplate)("BaseInputTemplate",n,t);return(0,xt.jsx)(s,{...e})}function Xr(e){const{onChange:t,options:n,registry:r}=e,i=(0,Ct.getTemplate)("BaseInputTemplate",r,n),o=(0,s.useCallback)((e=>t(e?`${e}:00`:void 0)),[t]);return(0,xt.jsx)(i,{type:"time",...e,onChange:o})}function Qr(e){const{options:t,registry:n}=e,s=(0,Ct.getTemplate)("BaseInputTemplate",n,t);return(0,xt.jsx)(s,{type:"url",...e})}function ei(e){const{options:t,registry:n}=e,s=(0,Ct.getTemplate)("BaseInputTemplate",n,t);return(0,xt.jsx)(s,{type:"number",...e})}class ti extends s.Component{constructor(e){if(super(e),this.getUsedFormData=(e,t)=>{if(0===t.length&&"object"!=typeof e)return e;const n=Mt()(e,t);return Array.isArray(e)?Object.keys(n).map((e=>n[e])):n},this.getFieldNames=(e,t)=>{const n=(e,s=[],r=[[]])=>(Object.keys(e).forEach((i=>{if("object"==typeof e[i]){const t=r.map((e=>[...e,i]));e[i][Ct.RJSF_ADDITONAL_PROPERTIES_FLAG]&&""!==e[i][Ct.NAME_KEY]?s.push(e[i][Ct.NAME_KEY]):n(e[i],s,t)}else i===Ct.NAME_KEY&&""!==e[i]&&r.forEach((e=>{const n=_t()(t,e);("object"!=typeof n||Ft()(n)||Array.isArray(n)&&n.every((e=>"object"!=typeof e)))&&s.push(e)}))})),s);return n(e)},this.onChange=(e,t,n)=>{const{extraErrors:s,omitExtraData:r,liveOmit:i,noValidate:o,liveValidate:a,onChange:l}=this.props,{schemaUtils:c,schema:h,retrievedSchema:d}=this.state;((0,Ct.isObject)(e)||Array.isArray(e))&&(e=this.getStateFromProps(this.props,e,d).formData);const u=!o&&a;let p,g={formData:e,schema:h},m=e;if(!0===r&&!0===i){p=c.retrieveSchema(h,e);const t=c.toPathSchema(p,"",e),n=this.getFieldNames(t,e);m=this.getUsedFormData(e,n),g={formData:m}}if(u){const e=this.validate(m,h,c,d);let t=e.errors,n=e.errorSchema;const r=t,i=n;if(s){const r=(0,Ct.validationDataMerge)(e,s);n=r.errorSchema,t=r.errors}g={formData:m,errors:t,errorSchema:n,schemaValidationErrors:r,schemaValidationErrorSchema:i}}else if(!o&&t){const e=s?(0,Ct.mergeObjects)(t,s,"preventDuplicates"):t;g={formData:m,errorSchema:e,errors:(0,Ct.toErrorList)(e)}}p&&(g.retrievedSchema=p),this.setState(g,(()=>l&&l({...this.state,...g},n)))},this.reset=()=>{const{onChange:e}=this.props,t={formData:this.getStateFromProps(this.props,void 0).formData,errorSchema:{},errors:[],schemaValidationErrors:[],schemaValidationErrorSchema:{}};this.setState(t,(()=>e&&e({...this.state,...t})))},this.onBlur=(e,t)=>{const{onBlur:n}=this.props;n&&n(e,t)},this.onFocus=(e,t)=>{const{onFocus:n}=this.props;n&&n(e,t)},this.onSubmit=e=>{if(e.preventDefault(),e.target!==e.currentTarget)return;e.persist();const{omitExtraData:t,extraErrors:n,noValidate:s,onSubmit:r}=this.props;let{formData:i}=this.state;const{schema:o,schemaUtils:a}=this.state;if(!0===t){const e=a.retrieveSchema(o,i),t=a.toPathSchema(e,"",i),n=this.getFieldNames(t,i);i=this.getUsedFormData(i,n)}if(s||this.validateForm()){const t=n||{},s=n?(0,Ct.toErrorList)(n):[];this.setState({formData:i,errors:s,errorSchema:t,schemaValidationErrors:[],schemaValidationErrorSchema:{}},(()=>{r&&r({...this.state,formData:i,status:"submitted"},e)}))}},!e.validator)throw new Error("A validator is required for Form functionality to work");this.state=this.getStateFromProps(e,e.formData),this.props.onChange&&!(0,Ct.deepEquals)(this.state.formData,this.props.formData)&&this.props.onChange(this.state),this.formElement=(0,s.createRef)()}getSnapshotBeforeUpdate(e,t){if(!(0,Ct.deepEquals)(this.props,e)){const n=this.getStateFromProps(this.props,this.props.formData,e.schema!==this.props.schema?void 0:this.state.retrievedSchema);return{nextState:n,shouldUpdate:!(0,Ct.deepEquals)(n,t)}}return{shouldUpdate:!1}}componentDidUpdate(e,t,n){if(n.shouldUpdate){const{nextState:e}=n;(0,Ct.deepEquals)(e.formData,this.props.formData)||(0,Ct.deepEquals)(e.formData,t.formData)||!this.props.onChange||this.props.onChange(e),this.setState(e)}}getStateFromProps(e,t,n){const s=this.state||{},r="schema"in e?e.schema:this.props.schema,i=("uiSchema"in e?e.uiSchema:this.props.uiSchema)||{},o=void 0!==t,a="liveValidate"in e?e.liveValidate:this.props.liveValidate,l=o&&!e.noValidate&&a,c=r,h="experimental_defaultFormStateBehavior"in e?e.experimental_defaultFormStateBehavior:this.props.experimental_defaultFormStateBehavior;let d=s.schemaUtils;d&&!d.doesSchemaUtilsDiffer(e.validator,c,h)||(d=(0,Ct.createSchemaUtils)(e.validator,c,h));const u=d.getDefaultFormState(r,t),p=null!=n?n:d.retrieveSchema(r,u);let g,m,f=s.schemaValidationErrors,v=s.schemaValidationErrorSchema;if(l){const e=this.validate(u,r,d,p);g=e.errors,m=e.errorSchema,f=g,v=m}else{const t=e.noValidate?{errors:[],errorSchema:{}}:e.liveValidate?{errors:s.errors||[],errorSchema:s.errorSchema||{}}:{errors:s.schemaValidationErrors||[],errorSchema:s.schemaValidationErrorSchema||{}};g=t.errors,m=t.errorSchema}if(e.extraErrors){const t=(0,Ct.validationDataMerge)({errorSchema:m,errors:g},e.extraErrors);m=t.errorSchema,g=t.errors}const w=d.toIdSchema(p,i["ui:rootFieldId"],u,e.idPrefix,e.idSeparator);return{schemaUtils:d,schema:r,uiSchema:i,idSchema:w,formData:u,edit:o,errors:g,errorSchema:m,schemaValidationErrors:f,schemaValidationErrorSchema:v,retrievedSchema:p}}shouldComponentUpdate(e,t){return(0,Ct.shouldRender)(this,e,t)}validate(e,t=this.props.schema,n,s){const r=n||this.state.schemaUtils,{customValidate:i,transformErrors:o,uiSchema:a}=this.props,l=null!=s?s:r.retrieveSchema(t,e);return r.getValidator().validateFormData(e,l,i,o,a)}renderErrors(e){const{errors:t,errorSchema:n,schema:s,uiSchema:r}=this.state,{formContext:i}=this.props,o=(0,Ct.getUiOptions)(r),a=(0,Ct.getTemplate)("ErrorListTemplate",e,o);return t&&t.length?(0,xt.jsx)(a,{errors:t,errorSchema:n||{},schema:s,uiSchema:r,formContext:i,registry:e}):null}getRegistry(){var e;const{translateString:t,uiSchema:n={}}=this.props,{schemaUtils:s}=this.state,{fields:r,templates:i,widgets:o,formContext:a,translateString:l}={fields:{AnyOfField:Gt,ArrayField:Pt,BooleanField:Ht,NumberField:Yt,ObjectField:Ys,OneOfField:Gt,SchemaField:er,StringField:tr,NullField:nr},templates:{ArrayFieldDescriptionTemplate:sr,ArrayFieldItemTemplate:rr,ArrayFieldTemplate:ir,ArrayFieldTitleTemplate:or,ButtonTemplates:{SubmitButton:lr,AddButton:gr,CopyButton:hr,MoveDownButton:dr,MoveUpButton:ur,RemoveButton:pr},BaseInputTemplate:ar,DescriptionFieldTemplate:mr,ErrorListTemplate:fr,FieldTemplate:br,FieldErrorTemplate:yr,FieldHelpTemplate:xr,ObjectFieldTemplate:Cr,TitleFieldTemplate:Sr,UnsupportedFieldTemplate:_r,WrapIfAdditionalTemplate:kr},widgets:{AltDateWidget:Ir,AltDateTimeWidget:Tr,CheckboxWidget:Lr,CheckboxesWidget:Vr,ColorWidget:Dr,DateWidget:Er,DateTimeWidget:Or,EmailWidget:$r,FileWidget:Pr,HiddenWidget:Hr,PasswordWidget:Ur,RadioWidget:Wr,RangeWidget:qr,SelectWidget:Zr,TextWidget:Jr,TextareaWidget:Yr,TimeWidget:Xr,UpDownWidget:ei,URLWidget:Qr},rootSchema:{},formContext:{},translateString:Ct.englishStringTranslator};return{fields:{...r,...this.props.fields},templates:{...i,...this.props.templates,ButtonTemplates:{...i.ButtonTemplates,...null===(e=this.props.templates)||void 0===e?void 0:e.ButtonTemplates}},widgets:{...o,...this.props.widgets},rootSchema:this.props.schema,formContext:this.props.formContext||a,schemaUtils:s,translateString:t||l,globalUiOptions:n[Ct.UI_GLOBAL_OPTIONS_KEY]}}submit(){this.formElement.current&&(this.formElement.current.dispatchEvent(new CustomEvent("submit",{cancelable:!0})),this.formElement.current.requestSubmit())}focusOnError(e){const{idPrefix:t="root",idSeparator:n="_"}=this.props,{property:s}=e,r=Tt()(s);""===r[0]?r[0]=t:r.unshift(t);const i=r.join(n);let o=this.formElement.current.elements[i];o||(o=this.formElement.current.querySelector(`input[id^=${i}`)),o&&o.length&&(o=o[0]),o&&o.focus()}validateForm(){const{extraErrors:e,extraErrorsBlockSubmit:t,focusOnFirstError:n,onError:s}=this.props,{formData:r,errors:i}=this.state,o=this.validate(r);let a=o.errors,l=o.errorSchema;const c=a,h=l,d=a.length>0||e&&t;if(d){if(e){const t=(0,Ct.validationDataMerge)(o,e);l=t.errorSchema,a=t.errors}n&&("function"==typeof n?n(a[0]):this.focusOnError(a[0])),this.setState({errors:a,errorSchema:l,schemaValidationErrors:c,schemaValidationErrorSchema:h},(()=>{s?s(a):console.error("Form validation failed",a)}))}else i.length>0&&this.setState({errors:[],errorSchema:{},schemaValidationErrors:[],schemaValidationErrorSchema:{}});return!d}render(){const{children:e,id:t,idPrefix:n,idSeparator:s,className:r="",tagName:i,name:o,method:a,target:l,action:c,autoComplete:h,enctype:d,acceptcharset:u,noHtml5Validate:p=!1,disabled:g=!1,readonly:m=!1,formContext:f,showErrorList:v="top",_internalFormWrapper:w}=this.props,{schema:b,uiSchema:y,formData:x,errorSchema:C,idSchema:S}=this.state,_=this.getRegistry(),{SchemaField:k}=_.fields,{SubmitButton:F}=_.templates.ButtonTemplates,j=w?i:void 0,M=w||i||"form";let{[Ct.SUBMIT_BTN_OPTIONS_KEY]:I={}}=(0,Ct.getUiOptions)(y);g&&(I={...I,props:{...I.props,disabled:!0}});const T={[Ct.UI_OPTIONS_KEY]:{[Ct.SUBMIT_BTN_OPTIONS_KEY]:I}};return(0,xt.jsxs)(M,{className:r||"rjsf",id:t,name:o,method:a,target:l,action:c,autoComplete:h,encType:d,acceptCharset:u,noValidate:p,onSubmit:this.onSubmit,as:j,ref:this.formElement,children:["top"===v&&this.renderErrors(_),(0,xt.jsx)(k,{name:"",schema:b,uiSchema:y,errorSchema:C,idSchema:S,idPrefix:n,idSeparator:s,formContext:f,formData:x,onChange:this.onChange,onBlur:this.onBlur,onFocus:this.onFocus,registry:_,disabled:g,readonly:m}),e||(0,xt.jsx)(F,{uiSchema:T,registry:_}),"bottom"===v&&this.renderErrors(_)]})}}const ni=ti,si={submitButtonOptions:{norender:!0}},ri=e=>{var t;const n=(null!==(t=e.translator)&&void 0!==t?t:yt.nullTranslator).load("jupyterlab");let s;if("icons"===e.buttonStyle){const t={tag:"span",elementSize:"xlarge",elementPosition:"center"};s="up"===e.direction?r().createElement(O.react,{...t}):r().createElement(L.react,{...t})}else s="up"===e.direction?n.__("Move up"):n.__("Move down");const i="up"===e.direction?e.item.index-1:e.item.index+1;return r().createElement("button",{className:"jp-mod-styled jp-mod-reject jp-ArrayOperationsButton",onClick:e.item.onReorderClick(e.item.index,i),disabled:"up"===e.direction?!e.item.hasMoveUp:!e.item.hasMoveDown},s)},ii=e=>{var t;const n=(null!==(t=e.translator)&&void 0!==t?t:yt.nullTranslator).load("jupyterlab");let s;return s="icons"===e.buttonStyle?r().createElement(z.react,{tag:"span",elementSize:"xlarge",elementPosition:"center"}):n.__("Remove"),r().createElement("button",{className:"jp-mod-styled jp-mod-warn jp-ArrayOperationsButton",onClick:e.item.onDropIndexClick(e.item.index)},s)},oi=e=>{var t;const n=(null!==(t=e.translator)&&void 0!==t?t:yt.nullTranslator).load("jupyterlab");let s;return s="icons"===e.buttonStyle?r().createElement(_.react,{tag:"span",elementSize:"xlarge",elementPosition:"center"}):n.__("Add"),r().createElement("button",{className:"jp-mod-styled jp-mod-reject jp-ArrayOperationsButton",onClick:e.onAddClick},s)};function ai(e){const{component:t,name:n,buttonStyle:s,compact:r,showModifiedFromDefault:i,translator:o}=e,a=null!=r&&r,l=null!=s?s:a?"icons":"text",c=e=>t({...e,buttonStyle:l,compact:a,showModifiedFromDefault:null==i||i,translator:null!=o?o:yt.nullTranslator});return n&&(c.displayName=n),c}function li(e,t){return{TitleField:(0,Ct.getTemplate)("TitleFieldTemplate",e,t),DescriptionField:(0,Ct.getTemplate)("DescriptionFieldTemplate",e,t)}}const ci=e=>ai({...e,name:"JupyterLabArrayTemplate",component:e=>{var t;const{schema:n,registry:s,uiSchema:i,required:o}=e,a={schema:n,registry:s,uiSchema:i,required:o},{TitleField:l,DescriptionField:c}=li(s,i);return r().createElement("div",{className:e.className},e.compact?r().createElement("div",{className:"jp-FormGroup-compactTitle"},r().createElement("div",{className:"jp-FormGroup-fieldLabel jp-FormGroup-contentItem",id:`${e.idSchema.$id}__title`},e.title||""),r().createElement("div",{className:"jp-FormGroup-description",id:`${e.idSchema.$id}-description`},e.schema.description||"")):r().createElement(r().Fragment,null,e.title&&r().createElement(l,{...a,title:e.title,id:`${e.idSchema.$id}-title`}),r().createElement(c,{...a,id:`${e.idSchema.$id}-description`,description:null!==(t=e.schema.description)&&void 0!==t?t:""})),e.items.map((t=>r().createElement("div",{key:t.key,className:t.className},t.children,r().createElement("div",{className:"jp-ArrayOperations"},r().createElement(ri,{buttonStyle:e.buttonStyle,translator:e.translator,item:t,direction:"up"}),r().createElement(ri,{buttonStyle:e.buttonStyle,translator:e.translator,item:t,direction:"down"}),r().createElement(ii,{buttonStyle:e.buttonStyle,translator:e.translator,item:t}))))),e.canAdd&&r().createElement(oi,{onAddClick:e.onAddClick,buttonStyle:e.buttonStyle,translator:e.translator}))}}),hi=e=>ai({...e,name:"JupyterLabObjectTemplate",component:e=>{var t;const{schema:n,registry:s,uiSchema:i,required:o}=e,a={schema:n,registry:s,uiSchema:i,required:o},{TitleField:l,DescriptionField:c}=li(s,i);return r().createElement("fieldset",{id:e.idSchema.$id},e.compact?r().createElement("div",{className:"jp-FormGroup-compactTitle"},r().createElement("div",{className:"jp-FormGroup-fieldLabel jp-FormGroup-contentItem",id:`${e.idSchema.$id}__title`},e.title||""),r().createElement("div",{className:"jp-FormGroup-description",id:`${e.idSchema.$id}__description`},e.schema.description||"")):r().createElement(r().Fragment,null,(e.title||(e.uiSchema||p.JSONExt.emptyObject)["ui:title"])&&r().createElement(l,{...a,id:`${e.idSchema.$id}__title`,title:e.title||`${(e.uiSchema||p.JSONExt.emptyObject)["ui:title"]}`||""}),r().createElement(c,{...a,id:`${e.idSchema.$id}__description`,description:null!==(t=e.schema.description)&&void 0!==t?t:""})),e.properties.map((e=>e.content)),(0,Ct.canExpand)(e.schema,e.uiSchema,e.formData)&&r().createElement(oi,{onAddClick:e.onAddClick(e.schema),buttonStyle:e.buttonStyle,translator:e.translator}))}}),di=e=>ai({...e,name:"JupyterLabFieldTemplate",component:e=>{var t;const n=(null!==(t=e.translator)&&void 0!==t?t:yt.nullTranslator).load("jupyterlab");let s,i=!1;const{formData:o,schema:a,label:l,displayLabel:c,id:h,formContext:d,errors:u,rawErrors:g,children:m,onKeyChange:f,onDropPropertyClick:v}=e,{defaultFormData:w}=d,b=h.split("_");b.shift();const y=b.join("."),x=""===y,C=y===(e.uiSchema||p.JSONExt.emptyObject)["ui:field"];e.showModifiedFromDefault&&(s=b.reduce(((e,t)=>null==e?void 0:e[t]),w),i=!x&&void 0!==o&&void 0!==s&&!a.properties&&"array"!==a.type&&!p.JSONExt.deepEqual(o,s));const S=!x&&"object"!=a.type&&"jp-SettingsEditor-@jupyterlab/shortcuts-extension:shortcuts_shortcuts"!=h,_=a.hasOwnProperty(Ct.ADDITIONAL_PROPERTY_FLAG),k=!("object"===a.type||"array"===a.type);return r().createElement("div",{className:"form-group "+(c||"boolean"===a.type?"small-field":"")},!C&&((null==g?void 0:g.length)?r().createElement("div",{className:"jp-modifiedIndicator jp-errorIndicator"}):i&&r().createElement("div",{className:"jp-modifiedIndicator"})),r().createElement("div",{className:"jp-FormGroup-content "+(e.compact?"jp-FormGroup-contentCompact":"jp-FormGroup-contentNormal")},k&&c&&!x&&l&&!_?e.compact?r().createElement("div",{className:"jp-FormGroup-compactTitle"},r().createElement("div",{className:"jp-FormGroup-fieldLabel jp-FormGroup-contentItem"},l),k&&a.description&&S&&r().createElement("div",{className:"jp-FormGroup-description"},a.description)):r().createElement("h3",{className:"jp-FormGroup-fieldLabel jp-FormGroup-contentItem"},l):r().createElement(r().Fragment,null),_&&r().createElement("input",{className:"jp-FormGroup-contentItem jp-mod-styled",type:"text",onBlur:e=>f(e.target.value),defaultValue:l}),r().createElement("div",{className:x?"jp-root":"object"===a.type?"jp-objectFieldWrapper":"array"===a.type?"jp-arrayFieldWrapper":"jp-inputFieldWrapper jp-FormGroup-contentItem"},m),_&&r().createElement("button",{className:"jp-FormGroup-contentItem jp-mod-styled jp-mod-warn jp-FormGroup-removeButton",onClick:v(l)},n.__("Remove")),!e.compact&&a.description&&S&&r().createElement("div",{className:"jp-FormGroup-description"},a.description),i&&void 0!==s&&"object"!==a.type&&r().createElement("div",{className:"jp-FormGroup-default"},n.__("Default: %1",null!==s?s.toLocaleString():"null")),r().createElement("div",{className:"validationErrors"},u)))}});function ui(e){const{buttonStyle:t,compact:n,showModifiedFromDefault:s,translator:i,formContext:o,...a}=e,l={...a.uiSchema||p.JSONExt.emptyObject};l["ui:options"]={...si,...l["ui:options"]},a.uiSchema=l;const{FieldTemplate:c,ArrayFieldTemplate:h,ObjectFieldTemplate:d}=e.templates||p.JSONExt.emptyObject,u={buttonStyle:t,compact:n,showModifiedFromDefault:s,translator:i},g={FieldTemplate:r().useMemo((()=>null!=c?c:di(u)),[c,t,n,s,i]),ArrayFieldTemplate:r().useMemo((()=>null!=h?h:ci(u)),[h,t,n,s,i]),ObjectFieldTemplate:r().useMemo((()=>null!=d?d:hi(u)),[d,t,n,s,i])};return r().createElement(ni,{templates:g,formContext:o,...a})}const pi="jp-DefaultStyle",gi="jp-HTMLSelect";class mi extends s.Component{render(){const{className:e,defaultStyle:t=!0,disabled:n,elementRef:r,iconProps:i,icon:o=I,options:l=[],...c}=this.props,h=a(gi,{[pi]:t},e),d=l.map((e=>{const t="object"==typeof e?e:{value:e};return s.createElement("option",{...t,key:t.value},t.label||t.value)}));return s.createElement("div",{className:h},s.createElement("select",{onFocus:e=>{e.stopPropagation()},disabled:n,ref:r,...c,multiple:!1},d,c.children),s.createElement(o.react,{tag:"span",stylesheet:"select",right:"7px",top:"5px",...i}))}}class fi extends u.Widget{constructor(e={}){super({node:vi.createNode()}),this._sandbox=[],this.addClass("jp-IFrame"),this.sandbox=e.sandbox||[],this.referrerPolicy=e.referrerPolicy||"no-referrer"}get referrerPolicy(){return this._referrerPolicy}set referrerPolicy(e){this._referrerPolicy!==e&&(this._referrerPolicy=e,this.node.querySelector("iframe").setAttribute("referrerpolicy",e))}get sandbox(){return this._sandbox.slice()}set sandbox(e){this._sandbox=e.slice();const t=this.node.querySelector("iframe"),n=e.length?e.join(" "):"";t.setAttribute("sandbox",n)}get url(){return this.node.querySelector("iframe").getAttribute("src")||""}set url(e){this.node.querySelector("iframe").setAttribute("src",e)}}var vi;function wi(e){const{className:t,inputRef:n,rightIcon:s,...i}=e;return r().createElement("div",{className:a("jp-InputGroup",t)},r().createElement("input",{ref:n,...i}),s&&r().createElement("span",{className:"jp-InputGroupAction"},"string"==typeof s?r().createElement(b.resolveReact,{icon:s,elementPosition:"center",tag:"span"}):r().createElement(s.react,{elementPosition:"center",tag:"span"})))}!function(e){e.createNode=function(){const e=document.createElement("div"),t=document.createElement("iframe");return t.setAttribute("sandbox",""),t.style.height="100%",t.style.width="100%",e.appendChild(t),e}}(vi||(vi={}));var bi,yi=n(33625),xi=n(2549);!function(e){e.DEFAULT_RANK=100}(bi||(bi={}));class Ci extends u.Menu{constructor(e){var t;super(e),this._ranks=[],this._rank=e.rank,this._includeSeparators=null===(t=e.includeSeparators)||void 0===t||t}get rank(){return this._rank}addGroup(e,t){if(0===e.length)return new xi.DisposableDelegate((()=>{}));const n=null!=t?t:bi.DEFAULT_RANK,s=e.map((e=>{var t;return{...e,rank:null!==(t=e.rank)&&void 0!==t?t:n}})).sort(((e,t)=>e.rank-t.rank));let r=this._ranks.findIndex((e=>s[0].rank<e));r<0&&(r=this._ranks.length);const i=[];return this._includeSeparators&&i.push(this.insertItem(r++,{type:"separator",rank:n})),i.push(...s.map((e=>this.insertItem(r++,e)))),this._includeSeparators&&i.push(this.insertItem(r++,{type:"separator",rank:n})),new xi.DisposableDelegate((()=>{i.forEach((e=>e.dispose()))}))}addItem(e){let t=-1;return e.rank&&(t=this._ranks.findIndex((t=>e.rank<t))),t<0&&(t=this._ranks.length),this.insertItem(t,e)}clearItems(){this._ranks.length=0,super.clearItems()}dispose(){this._ranks.length=0,super.dispose()}getRankAt(e){return this._ranks[e]}insertItem(e,t){var n,s;const r=Math.max(0,Math.min(e,this._ranks.length));yi.ArrayExt.insert(this._ranks,r,null!==(n=t.rank)&&void 0!==n?n:Math.max(bi.DEFAULT_RANK,null!==(s=this._ranks[this._ranks.length-1])&&void 0!==s?s:bi.DEFAULT_RANK));const i=super.insertItem(r,t);return new Si(i,this)}removeItemAt(e){yi.ArrayExt.removeAt(this._ranks,e),super.removeItemAt(e)}}class Si{constructor(e,t){this._item=new WeakRef(e),this._menu=t;const n=e=>{e.disposed.disconnect(n,this),this.dispose()};this._menu.disposed.connect(n,this)}get isDisposed(){return this._isDisposed}get type(){return this._item.deref().type}get command(){return this._item.deref().command}get args(){return this._item.deref().args}get submenu(){return this._item.deref().submenu}get label(){return this._item.deref().label}get mnemonic(){return this._item.deref().mnemonic}get icon(){return this._item.deref().icon}get iconClass(){return this._item.deref().iconClass}get iconLabel(){return this._item.deref().iconLabel}get caption(){return this._item.deref().caption}get className(){return this._item.deref().className}get dataset(){return this._item.deref().dataset}get isEnabled(){return this._item.deref().isEnabled}get isToggled(){return this._item.deref().isToggled}get isVisible(){return this._item.deref().isVisible}get keyBinding(){return this._item.deref().keyBinding}dispose(){if(this._isDisposed)return;this._isDisposed=!0;const e=this._item.deref();e&&!this._menu.isDisposed&&this._menu.removeItem(e),d.Signal.clearData(this)}}const _i=function(){if("undefined"!=typeof globalThis)return globalThis;if(void 0!==n.g)return n.g;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;try{return new Function("return this")()}catch(e){return{}}}();void 0===_i.trustedTypes&&(_i.trustedTypes={createPolicy:(e,t)=>t});const ki={configurable:!1,enumerable:!1,writable:!1};void 0===_i.FAST&&Reflect.defineProperty(_i,"FAST",Object.assign({value:Object.create(null)},ki));const Fi=_i.FAST;if(void 0===Fi.getById){const e=Object.create(null);Reflect.defineProperty(Fi,"getById",Object.assign({value(t,n){let s=e[t];return void 0===s&&(s=n?e[t]=n():null),s}},ki))}const ji=Object.freeze([]);function Mi(){const e=new WeakMap;return function(t){let n=e.get(t);if(void 0===n){let s=Reflect.getPrototypeOf(t);for(;void 0===n&&null!==s;)n=e.get(s),s=Reflect.getPrototypeOf(s);n=void 0===n?[]:n.slice(0),e.set(t,n)}return n}}const Ii=_i.FAST.getById(1,(()=>{const e=[],t=[];function n(){if(t.length)throw t.shift()}function s(e){try{e.call()}catch(e){t.push(e),setTimeout(n,0)}}function r(){let t=0;for(;t<e.length;)if(s(e[t]),t++,t>1024){for(let n=0,s=e.length-t;n<s;n++)e[n]=e[n+t];e.length-=t,t=0}e.length=0}return Object.freeze({enqueue:function(t){e.length<1&&_i.requestAnimationFrame(r),e.push(t)},process:r})})),Ti=_i.trustedTypes.createPolicy("fast-html",{createHTML:e=>e});let Li=Ti;const Vi=`fast-${Math.random().toString(36).substring(2,8)}`,Di=`${Vi}{`,Ei=`}${Vi}`,Oi=Object.freeze({supportsAdoptedStyleSheets:Array.isArray(document.adoptedStyleSheets)&&"replace"in CSSStyleSheet.prototype,setHTMLPolicy(e){if(Li!==Ti)throw new Error("The HTML policy can only be set once.");Li=e},createHTML:e=>Li.createHTML(e),isMarker:e=>e&&8===e.nodeType&&e.data.startsWith(Vi),extractDirectiveIndexFromMarker:e=>parseInt(e.data.replace(`${Vi}:`,"")),createInterpolationPlaceholder:e=>`${Di}${e}${Ei}`,createCustomAttributePlaceholder(e,t){return`${e}="${this.createInterpolationPlaceholder(t)}"`},createBlockPlaceholder:e=>`\x3c!--${Vi}:${e}--\x3e`,queueUpdate:Ii.enqueue,processUpdates:Ii.process,nextUpdate:()=>new Promise(Ii.enqueue),setAttribute(e,t,n){null==n?e.removeAttribute(t):e.setAttribute(t,n)},setBooleanAttribute(e,t,n){n?e.setAttribute(t,""):e.removeAttribute(t)},removeChildNodes(e){for(let t=e.firstChild;null!==t;t=e.firstChild)e.removeChild(t)},createTemplateWalker:e=>document.createTreeWalker(e,133,null,!1)});class $i{constructor(e,t){this.sub1=void 0,this.sub2=void 0,this.spillover=void 0,this.source=e,this.sub1=t}has(e){return void 0===this.spillover?this.sub1===e||this.sub2===e:-1!==this.spillover.indexOf(e)}subscribe(e){const t=this.spillover;if(void 0===t){if(this.has(e))return;if(void 0===this.sub1)return void(this.sub1=e);if(void 0===this.sub2)return void(this.sub2=e);this.spillover=[this.sub1,this.sub2,e],this.sub1=void 0,this.sub2=void 0}else-1===t.indexOf(e)&&t.push(e)}unsubscribe(e){const t=this.spillover;if(void 0===t)this.sub1===e?this.sub1=void 0:this.sub2===e&&(this.sub2=void 0);else{const n=t.indexOf(e);-1!==n&&t.splice(n,1)}}notify(e){const t=this.spillover,n=this.source;if(void 0===t){const t=this.sub1,s=this.sub2;void 0!==t&&t.handleChange(n,e),void 0!==s&&s.handleChange(n,e)}else for(let s=0,r=t.length;s<r;++s)t[s].handleChange(n,e)}}class Ai{constructor(e){this.subscribers={},this.sourceSubscribers=null,this.source=e}notify(e){var t;const n=this.subscribers[e];void 0!==n&&n.notify(e),null===(t=this.sourceSubscribers)||void 0===t||t.notify(e)}subscribe(e,t){var n;if(t){let n=this.subscribers[t];void 0===n&&(this.subscribers[t]=n=new $i(this.source)),n.subscribe(e)}else this.sourceSubscribers=null!==(n=this.sourceSubscribers)&&void 0!==n?n:new $i(this.source),this.sourceSubscribers.subscribe(e)}unsubscribe(e,t){var n;if(t){const n=this.subscribers[t];void 0!==n&&n.unsubscribe(e)}else null===(n=this.sourceSubscribers)||void 0===n||n.unsubscribe(e)}}const Bi=Fi.getById(2,(()=>{const e=/(:|&&|\|\||if)/,t=new WeakMap,n=Oi.queueUpdate;let s,r=e=>{throw new Error("Must call enableArrayObservation before observing arrays.")};function i(e){let n=e.$fastController||t.get(e);return void 0===n&&(Array.isArray(e)?n=r(e):t.set(e,n=new Ai(e))),n}const o=Mi();class a{constructor(e){this.name=e,this.field=`_${e}`,this.callback=`${e}Changed`}getValue(e){return void 0!==s&&s.watch(e,this.name),e[this.field]}setValue(e,t){const n=this.field,s=e[n];if(s!==t){e[n]=t;const r=e[this.callback];"function"==typeof r&&r.call(e,s,t),i(e).notify(this.name)}}}class l extends $i{constructor(e,t,n=!1){super(e,t),this.binding=e,this.isVolatileBinding=n,this.needsRefresh=!0,this.needsQueue=!0,this.first=this,this.last=null,this.propertySource=void 0,this.propertyName=void 0,this.notifier=void 0,this.next=void 0}observe(e,t){this.needsRefresh&&null!==this.last&&this.disconnect();const n=s;s=this.needsRefresh?this:void 0,this.needsRefresh=this.isVolatileBinding;const r=this.binding(e,t);return s=n,r}disconnect(){if(null!==this.last){let e=this.first;for(;void 0!==e;)e.notifier.unsubscribe(this,e.propertyName),e=e.next;this.last=null,this.needsRefresh=this.needsQueue=!0}}watch(e,t){const n=this.last,r=i(e),o=null===n?this.first:{};if(o.propertySource=e,o.propertyName=t,o.notifier=r,r.subscribe(this,t),null!==n){if(!this.needsRefresh){let t;s=void 0,t=n.propertySource[n.propertyName],s=this,e===t&&(this.needsRefresh=!0)}n.next=o}this.last=o}handleChange(){this.needsQueue&&(this.needsQueue=!1,n(this))}call(){null!==this.last&&(this.needsQueue=!0,this.notify(this))}records(){let e=this.first;return{next:()=>{const t=e;return void 0===t?{value:void 0,done:!0}:(e=e.next,{value:t,done:!1})},[Symbol.iterator]:function(){return this}}}}return Object.freeze({setArrayObserverFactory(e){r=e},getNotifier:i,track(e,t){void 0!==s&&s.watch(e,t)},trackVolatile(){void 0!==s&&(s.needsRefresh=!0)},notify(e,t){i(e).notify(t)},defineProperty(e,t){"string"==typeof t&&(t=new a(t)),o(e).push(t),Reflect.defineProperty(e,t.name,{enumerable:!0,get:function(){return t.getValue(this)},set:function(e){t.setValue(this,e)}})},getAccessors:o,binding(e,t,n=this.isVolatileBinding(e)){return new l(e,t,n)},isVolatileBinding:t=>e.test(t.toString())})}));function Ni(e,t){Bi.defineProperty(e,t)}const Ri=Fi.getById(3,(()=>{let e=null;return{get:()=>e,set(t){e=t}}}));class zi{constructor(){this.index=0,this.length=0,this.parent=null,this.parentContext=null}get event(){return Ri.get()}get isEven(){return this.index%2==0}get isOdd(){return this.index%2!=0}get isFirst(){return 0===this.index}get isInMiddle(){return!this.isFirst&&!this.isLast}get isLast(){return this.index===this.length-1}static setEvent(e){Ri.set(e)}}Bi.defineProperty(zi.prototype,"index"),Bi.defineProperty(zi.prototype,"length");const Pi=Object.seal(new zi);class Hi{constructor(){this.targets=new WeakSet}addStylesTo(e){this.targets.add(e)}removeStylesFrom(e){this.targets.delete(e)}isAttachedTo(e){return this.targets.has(e)}withBehaviors(...e){return this.behaviors=null===this.behaviors?e:this.behaviors.concat(e),this}}function Ui(e){return e.map((e=>e instanceof Hi?Ui(e.styles):[e])).reduce(((e,t)=>e.concat(t)),[])}function Wi(e){return e.map((e=>e instanceof Hi?e.behaviors:null)).reduce(((e,t)=>null===t?e:(null===e&&(e=[]),e.concat(t))),null)}Hi.create=(()=>{if(Oi.supportsAdoptedStyleSheets){const e=new Map;return t=>new Zi(t,e)}return e=>new Yi(e)})();let qi=(e,t)=>{e.adoptedStyleSheets=[...e.adoptedStyleSheets,...t]},Gi=(e,t)=>{e.adoptedStyleSheets=e.adoptedStyleSheets.filter((e=>-1===t.indexOf(e)))};if(Oi.supportsAdoptedStyleSheets)try{document.adoptedStyleSheets.push(),document.adoptedStyleSheets.splice(),qi=(e,t)=>{e.adoptedStyleSheets.push(...t)},Gi=(e,t)=>{for(const n of t){const t=e.adoptedStyleSheets.indexOf(n);-1!==t&&e.adoptedStyleSheets.splice(t,1)}}}catch(Xt){}class Zi extends Hi{constructor(e,t){super(),this.styles=e,this.styleSheetCache=t,this._styleSheets=void 0,this.behaviors=Wi(e)}get styleSheets(){if(void 0===this._styleSheets){const e=this.styles,t=this.styleSheetCache;this._styleSheets=Ui(e).map((e=>{if(e instanceof CSSStyleSheet)return e;let n=t.get(e);return void 0===n&&(n=new CSSStyleSheet,n.replaceSync(e),t.set(e,n)),n}))}return this._styleSheets}addStylesTo(e){qi(e,this.styleSheets),super.addStylesTo(e)}removeStylesFrom(e){Gi(e,this.styleSheets),super.removeStylesFrom(e)}}let Ki=0;class Yi extends Hi{constructor(e){super(),this.styles=e,this.behaviors=null,this.behaviors=Wi(e),this.styleSheets=Ui(e),this.styleClass="fast-style-class-"+ ++Ki}addStylesTo(e){const t=this.styleSheets,n=this.styleClass;e=this.normalizeTarget(e);for(let s=0;s<t.length;s++){const r=document.createElement("style");r.innerHTML=t[s],r.className=n,e.append(r)}super.addStylesTo(e)}removeStylesFrom(e){const t=(e=this.normalizeTarget(e)).querySelectorAll(`.${this.styleClass}`);for(let n=0,s=t.length;n<s;++n)e.removeChild(t[n]);super.removeStylesFrom(e)}isAttachedTo(e){return super.isAttachedTo(this.normalizeTarget(e))}normalizeTarget(e){return e===document?document.body:e}}const Ji=Object.freeze({locate:Mi()}),Xi={toView:e=>e?"true":"false",fromView:e=>null!=e&&"false"!==e&&!1!==e&&0!==e};class Qi{constructor(e,t,n=t.toLowerCase(),s="reflect",r){this.guards=new Set,this.Owner=e,this.name=t,this.attribute=n,this.mode=s,this.converter=r,this.fieldName=`_${t}`,this.callbackName=`${t}Changed`,this.hasCallback=this.callbackName in e.prototype,"boolean"===s&&void 0===r&&(this.converter=Xi)}setValue(e,t){const n=e[this.fieldName],s=this.converter;void 0!==s&&(t=s.fromView(t)),n!==t&&(e[this.fieldName]=t,this.tryReflectToAttribute(e),this.hasCallback&&e[this.callbackName](n,t),e.$fastController.notify(this.name))}getValue(e){return Bi.track(e,this.name),e[this.fieldName]}onAttributeChangedCallback(e,t){this.guards.has(e)||(this.guards.add(e),this.setValue(e,t),this.guards.delete(e))}tryReflectToAttribute(e){const t=this.mode,n=this.guards;n.has(e)||"fromView"===t||Oi.queueUpdate((()=>{n.add(e);const s=e[this.fieldName];switch(t){case"reflect":const t=this.converter;Oi.setAttribute(e,this.attribute,void 0!==t?t.toView(s):s);break;case"boolean":Oi.setBooleanAttribute(e,this.attribute,s)}n.delete(e)}))}static collect(e,...t){const n=[];t.push(Ji.locate(e));for(let s=0,r=t.length;s<r;++s){const r=t[s];if(void 0!==r)for(let t=0,s=r.length;t<s;++t){const s=r[t];"string"==typeof s?n.push(new Qi(e,s)):n.push(new Qi(e,s.property,s.attribute,s.mode,s.converter))}}return n}}function eo(e,t){let n;function s(e,t){arguments.length>1&&(n.property=t),Ji.locate(e.constructor).push(n)}return arguments.length>1?(n={},void s(e,t)):(n=void 0===e?{}:e,s)}const to={mode:"open"},no={},so=Fi.getById(4,(()=>{const e=new Map;return Object.freeze({register:t=>!e.has(t.type)&&(e.set(t.type,t),!0),getByType:t=>e.get(t)})}));class ro{constructor(e,t=e.definition){"string"==typeof t&&(t={name:t}),this.type=e,this.name=t.name,this.template=t.template;const n=Qi.collect(e,t.attributes),s=new Array(n.length),r={},i={};for(let e=0,t=n.length;e<t;++e){const t=n[e];s[e]=t.attribute,r[t.name]=t,i[t.attribute]=t}this.attributes=n,this.observedAttributes=s,this.propertyLookup=r,this.attributeLookup=i,this.shadowOptions=void 0===t.shadowOptions?to:null===t.shadowOptions?void 0:Object.assign(Object.assign({},to),t.shadowOptions),this.elementOptions=void 0===t.elementOptions?no:Object.assign(Object.assign({},no),t.elementOptions),this.styles=void 0===t.styles?void 0:Array.isArray(t.styles)?Hi.create(t.styles):t.styles instanceof Hi?t.styles:Hi.create([t.styles])}get isDefined(){return!!so.getByType(this.type)}define(e=customElements){const t=this.type;if(so.register(this)){const e=this.attributes,n=t.prototype;for(let t=0,s=e.length;t<s;++t)Bi.defineProperty(n,e[t]);Reflect.defineProperty(t,"observedAttributes",{value:this.observedAttributes,enumerable:!0})}return e.get(this.name)||e.define(this.name,t,this.elementOptions),this}}function io(e,t,n,s){var r,i=arguments.length,o=i<3?t:null===s?s=Object.getOwnPropertyDescriptor(t,n):s;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(e,t,n,s);else for(var a=e.length-1;a>=0;a--)(r=e[a])&&(o=(i<3?r(o):i>3?r(t,n,o):r(t,n))||o);return i>3&&o&&Object.defineProperty(t,n,o),o}ro.forType=so.getByType;const oo=new WeakMap,ao={bubbles:!0,composed:!0,cancelable:!0};function lo(e){return e.shadowRoot||oo.get(e)||null}class co extends Ai{constructor(e,t){super(e),this.boundObservables=null,this.behaviors=null,this.needsInitialization=!0,this._template=null,this._styles=null,this._isConnected=!1,this.$fastController=this,this.view=null,this.element=e,this.definition=t;const n=t.shadowOptions;if(void 0!==n){const t=e.attachShadow(n);"closed"===n.mode&&oo.set(e,t)}const s=Bi.getAccessors(e);if(s.length>0){const t=this.boundObservables=Object.create(null);for(let n=0,r=s.length;n<r;++n){const r=s[n].name,i=e[r];void 0!==i&&(delete e[r],t[r]=i)}}}get isConnected(){return Bi.track(this,"isConnected"),this._isConnected}setIsConnected(e){this._isConnected=e,Bi.notify(this,"isConnected")}get template(){return this._template}set template(e){this._template!==e&&(this._template=e,this.needsInitialization||this.renderTemplate(e))}get styles(){return this._styles}set styles(e){this._styles!==e&&(null!==this._styles&&this.removeStyles(this._styles),this._styles=e,this.needsInitialization||null===e||this.addStyles(e))}addStyles(e){const t=lo(this.element)||this.element.getRootNode();if(e instanceof HTMLStyleElement)t.append(e);else if(!e.isAttachedTo(t)){const n=e.behaviors;e.addStylesTo(t),null!==n&&this.addBehaviors(n)}}removeStyles(e){const t=lo(this.element)||this.element.getRootNode();if(e instanceof HTMLStyleElement)t.removeChild(e);else if(e.isAttachedTo(t)){const n=e.behaviors;e.removeStylesFrom(t),null!==n&&this.removeBehaviors(n)}}addBehaviors(e){const t=this.behaviors||(this.behaviors=new Map),n=e.length,s=[];for(let r=0;r<n;++r){const n=e[r];t.has(n)?t.set(n,t.get(n)+1):(t.set(n,1),s.push(n))}if(this._isConnected){const e=this.element;for(let t=0;t<s.length;++t)s[t].bind(e,Pi)}}removeBehaviors(e,t=!1){const n=this.behaviors;if(null===n)return;const s=e.length,r=[];for(let i=0;i<s;++i){const s=e[i];if(n.has(s)){const e=n.get(s)-1;0===e||t?n.delete(s)&&r.push(s):n.set(s,e)}}if(this._isConnected){const e=this.element;for(let t=0;t<r.length;++t)r[t].unbind(e)}}onConnectedCallback(){if(this._isConnected)return;const e=this.element;this.needsInitialization?this.finishInitialization():null!==this.view&&this.view.bind(e,Pi);const t=this.behaviors;if(null!==t)for(const[n]of t)n.bind(e,Pi);this.setIsConnected(!0)}onDisconnectedCallback(){if(!this._isConnected)return;this.setIsConnected(!1);const e=this.view;null!==e&&e.unbind();const t=this.behaviors;if(null!==t){const e=this.element;for(const[n]of t)n.unbind(e)}}onAttributeChangedCallback(e,t,n){const s=this.definition.attributeLookup[e];void 0!==s&&s.onAttributeChangedCallback(this.element,n)}emit(e,t,n){return!!this._isConnected&&this.element.dispatchEvent(new CustomEvent(e,Object.assign(Object.assign({detail:t},ao),n)))}finishInitialization(){const e=this.element,t=this.boundObservables;if(null!==t){const n=Object.keys(t);for(let s=0,r=n.length;s<r;++s){const r=n[s];e[r]=t[r]}this.boundObservables=null}const n=this.definition;null===this._template&&(this.element.resolveTemplate?this._template=this.element.resolveTemplate():n.template&&(this._template=n.template||null)),null!==this._template&&this.renderTemplate(this._template),null===this._styles&&(this.element.resolveStyles?this._styles=this.element.resolveStyles():n.styles&&(this._styles=n.styles||null)),null!==this._styles&&this.addStyles(this._styles),this.needsInitialization=!1}renderTemplate(e){const t=this.element,n=lo(t)||t;null!==this.view?(this.view.dispose(),this.view=null):this.needsInitialization||Oi.removeChildNodes(n),e&&(this.view=e.render(t,n,t))}static forCustomElement(e){const t=e.$fastController;if(void 0!==t)return t;const n=ro.forType(e.constructor);if(void 0===n)throw new Error("Missing FASTElement definition.");return e.$fastController=new co(e,n)}}function ho(e){return class extends e{constructor(){super(),co.forCustomElement(this)}$emit(e,t,n){return this.$fastController.emit(e,t,n)}connectedCallback(){this.$fastController.onConnectedCallback()}disconnectedCallback(){this.$fastController.onDisconnectedCallback()}attributeChangedCallback(e,t,n){this.$fastController.onAttributeChangedCallback(e,t,n)}}}const uo=Object.assign(ho(HTMLElement),{from:e=>ho(e),define:(e,t)=>new ro(e,t).define().type}),po=new Map;"metadata"in Reflect||(Reflect.metadata=function(e,t){return function(n){Reflect.defineMetadata(e,t,n)}},Reflect.defineMetadata=function(e,t,n){let s=po.get(n);void 0===s&&po.set(n,s=new Map),s.set(e,t)},Reflect.getOwnMetadata=function(e,t){const n=po.get(t);if(void 0!==n)return n.get(e)});class go{constructor(e,t){this.container=e,this.key=t}instance(e){return this.registerResolver(0,e)}singleton(e){return this.registerResolver(1,e)}transient(e){return this.registerResolver(2,e)}callback(e){return this.registerResolver(3,e)}cachedCallback(e){return this.registerResolver(3,No(e))}aliasTo(e){return this.registerResolver(5,e)}registerResolver(e,t){const{container:n,key:s}=this;return this.container=this.key=void 0,n.registerResolver(s,new jo(s,e,t))}}function mo(e){const t=e.slice(),n=Object.keys(e),s=n.length;let r;for(let i=0;i<s;++i)r=n[i],Go(r)||(t[r]=e[r]);return t}const fo=Object.freeze({none(e){throw Error(`${e.toString()} not registered, did you forget to add @singleton()?`)},singleton:e=>new jo(e,1,e),transient:e=>new jo(e,2,e)}),vo=Object.freeze({default:Object.freeze({parentLocator:()=>null,responsibleForOwnerRequests:!1,defaultResolver:fo.singleton})}),wo=new Map;function bo(e){return t=>Reflect.getOwnMetadata(e,t)}let yo=null;const xo=Object.freeze({createContainer:e=>new Ao(null,Object.assign({},vo.default,e)),findResponsibleContainer(e){const t=e.$$container$$;return t&&t.responsibleForOwnerRequests?t:xo.findParentContainer(e)},findParentContainer(e){const t=new CustomEvent(Oo,{bubbles:!0,composed:!0,cancelable:!0,detail:{container:void 0}});return e.dispatchEvent(t),t.detail.container||xo.getOrCreateDOMContainer()},getOrCreateDOMContainer:(e,t)=>e?e.$$container$$||new Ao(e,Object.assign({},vo.default,t,{parentLocator:xo.findParentContainer})):yo||(yo=new Ao(null,Object.assign({},vo.default,t,{parentLocator:()=>null}))),getDesignParamtypes:bo("design:paramtypes"),getAnnotationParamtypes:bo("di:paramtypes"),getOrCreateAnnotationParamTypes(e){let t=this.getAnnotationParamtypes(e);return void 0===t&&Reflect.defineMetadata("di:paramtypes",t=[],e),t},getDependencies(e){let t=wo.get(e);if(void 0===t){const n=e.inject;if(void 0===n){const n=xo.getDesignParamtypes(e),s=xo.getAnnotationParamtypes(e);if(void 0===n)if(void 0===s){const n=Object.getPrototypeOf(e);t="function"==typeof n&&n!==Function.prototype?mo(xo.getDependencies(n)):[]}else t=mo(s);else if(void 0===s)t=mo(n);else{t=mo(n);let e,r=s.length;for(let n=0;n<r;++n)e=s[n],void 0!==e&&(t[n]=e);const i=Object.keys(s);let o;r=i.length;for(let e=0;e<r;++e)o=i[e],Go(o)||(t[o]=s[o])}}else t=mo(n);wo.set(e,t)}return t},defineProperty(e,t,n,s=!1){const r=`$di_${t}`;Reflect.defineProperty(e,t,{get:function(){let e=this[r];if(void 0===e){const i=this instanceof HTMLElement?xo.findResponsibleContainer(this):xo.getOrCreateDOMContainer();if(e=i.get(n),this[r]=e,s&&this instanceof uo){const s=this.$fastController,i=()=>{xo.findResponsibleContainer(this).get(n)!==this[r]&&(this[r]=e,s.notify(t))};s.subscribe({handleChange:i},"isConnected")}}return e}})},createInterface(e,t){const n="function"==typeof e?e:t,s="string"==typeof e?e:e&&"friendlyName"in e&&e.friendlyName||Ho,r="string"!=typeof e&&(e&&"respectConnection"in e&&e.respectConnection||!1),i=function(e,t,n){if(null==e||void 0!==new.target)throw new Error(`No registration for interface: '${i.friendlyName}'`);t?xo.defineProperty(e,t,i,r):xo.getOrCreateAnnotationParamTypes(e)[n]=i};return i.$isInterface=!0,i.friendlyName=null==s?"(anonymous)":s,null!=n&&(i.register=function(e,t){return n(new go(e,null!=t?t:i))}),i.toString=function(){return`InterfaceSymbol<${i.friendlyName}>`},i},inject:(...e)=>function(t,n,s){if("number"==typeof s){const n=xo.getOrCreateAnnotationParamTypes(t),r=e[0];void 0!==r&&(n[s]=r)}else if(n)xo.defineProperty(t,n,e[0]);else{const n=s?xo.getOrCreateAnnotationParamTypes(s.value):xo.getOrCreateAnnotationParamTypes(t);let r;for(let t=0;t<e.length;++t)r=e[t],void 0!==r&&(n[t]=r)}},transient:e=>(e.register=function(t){return Ro.transient(e,e).register(t)},e.registerInRequestor=!1,e),singleton:(e,t=_o)=>(e.register=function(t){return Ro.singleton(e,e).register(t)},e.registerInRequestor=t.scoped,e)}),Co=xo.createInterface("Container");function So(e){return function(t){const n=function(e,t,s){xo.inject(n)(e,t,s)};return n.$isResolver=!0,n.resolve=function(n,s){return e(t,n,s)},n}}xo.inject;const _o={scoped:!1};function ko(e,t,n){xo.inject(ko)(e,t,n)}function Fo(e,t){return t.getFactory(e).construct(t)}So(((e,t,n)=>()=>n.get(e))),So(((e,t,n)=>n.has(e,!0)?n.get(e):void 0)),ko.$isResolver=!0,ko.resolve=()=>{},So(((e,t,n)=>{const s=Fo(e,t),r=new jo(e,0,s);return n.registerResolver(e,r),s})),So(((e,t,n)=>Fo(e,t)));class jo{constructor(e,t,n){this.key=e,this.strategy=t,this.state=n,this.resolving=!1}get $isResolver(){return!0}register(e){return e.registerResolver(this.key,this)}resolve(e,t){switch(this.strategy){case 0:return this.state;case 1:if(this.resolving)throw new Error(`Cyclic dependency found: ${this.state.name}`);return this.resolving=!0,this.state=e.getFactory(this.state).construct(t),this.strategy=0,this.resolving=!1,this.state;case 2:{const n=e.getFactory(this.state);if(null===n)throw new Error(`Resolver for ${String(this.key)} returned a null factory`);return n.construct(t)}case 3:return this.state(e,t,this);case 4:return this.state[0].resolve(e,t);case 5:return t.get(this.state);default:throw new Error(`Invalid resolver strategy specified: ${this.strategy}.`)}}getFactory(e){var t,n,s;switch(this.strategy){case 1:case 2:return e.getFactory(this.state);case 5:return null!==(s=null===(n=null===(t=e.getResolver(this.state))||void 0===t?void 0:t.getFactory)||void 0===n?void 0:n.call(t,e))&&void 0!==s?s:null;default:return null}}}function Mo(e){return this.get(e)}function Io(e,t){return t(e)}class To{constructor(e,t){this.Type=e,this.dependencies=t,this.transformers=null}construct(e,t){let n;return n=void 0===t?new this.Type(...this.dependencies.map(Mo,e)):new this.Type(...this.dependencies.map(Mo,e),...t),null==this.transformers?n:this.transformers.reduce(Io,n)}registerTransformer(e){(this.transformers||(this.transformers=[])).push(e)}}const Lo={$isResolver:!0,resolve:(e,t)=>t};function Vo(e){return"function"==typeof e.register}function Do(e){return function(e){return Vo(e)&&"boolean"==typeof e.registerInRequestor}(e)&&e.registerInRequestor}const Eo=new Set(["Array","ArrayBuffer","Boolean","DataView","Date","Error","EvalError","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Number","Object","Promise","RangeError","ReferenceError","RegExp","Set","SharedArrayBuffer","String","SyntaxError","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","URIError","WeakMap","WeakSet"]),Oo="__DI_LOCATE_PARENT__",$o=new Map;class Ao{constructor(e,t){this.owner=e,this.config=t,this._parent=void 0,this.registerDepth=0,this.context=null,null!==e&&(e.$$container$$=this),this.resolvers=new Map,this.resolvers.set(Co,Lo),e instanceof Node&&e.addEventListener(Oo,(e=>{e.composedPath()[0]!==this.owner&&(e.detail.container=this,e.stopImmediatePropagation())}))}get parent(){return void 0===this._parent&&(this._parent=this.config.parentLocator(this.owner)),this._parent}get depth(){return null===this.parent?0:this.parent.depth+1}get responsibleForOwnerRequests(){return this.config.responsibleForOwnerRequests}registerWithContext(e,...t){return this.context=e,this.register(...t),this.context=null,this}register(...e){if(100==++this.registerDepth)throw new Error("Unable to autoregister dependency");let t,n,s,r,i;const o=this.context;for(let a=0,l=e.length;a<l;++a)if(t=e[a],Uo(t))if(Vo(t))t.register(this,o);else if(void 0!==t.prototype)Ro.singleton(t,t).register(this);else for(n=Object.keys(t),r=0,i=n.length;r<i;++r)s=t[n[r]],Uo(s)&&(Vo(s)?s.register(this,o):this.register(s));return--this.registerDepth,this}registerResolver(e,t){zo(e);const n=this.resolvers,s=n.get(e);return null==s?n.set(e,t):s instanceof jo&&4===s.strategy?s.state.push(t):n.set(e,new jo(e,4,[s,t])),t}registerTransformer(e,t){const n=this.getResolver(e);if(null==n)return!1;if(n.getFactory){const e=n.getFactory(this);return null!=e&&(e.registerTransformer(t),!0)}return!1}getResolver(e,t=!0){if(zo(e),void 0!==e.resolve)return e;let n,s=this;for(;null!=s;){if(n=s.resolvers.get(e),null!=n)return n;if(null==s.parent){const n=Do(e)?this:s;return t?this.jitRegister(e,n):null}s=s.parent}return null}has(e,t=!1){return!!this.resolvers.has(e)||!(!t||null==this.parent)&&this.parent.has(e,!0)}get(e){if(zo(e),e.$isResolver)return e.resolve(this,this);let t,n=this;for(;null!=n;){if(t=n.resolvers.get(e),null!=t)return t.resolve(n,this);if(null==n.parent){const s=Do(e)?this:n;return t=this.jitRegister(e,s),t.resolve(n,this)}n=n.parent}throw new Error(`Unable to resolve key: ${String(e)}`)}getAll(e,t=!1){zo(e);const n=this;let s,r=n;if(t){let t=ji;for(;null!=r;)s=r.resolvers.get(e),null!=s&&(t=t.concat(Po(s,r,n))),r=r.parent;return t}for(;null!=r;){if(s=r.resolvers.get(e),null!=s)return Po(s,r,n);if(r=r.parent,null==r)return ji}return ji}getFactory(e){let t=$o.get(e);if(void 0===t){if(Wo(e))throw new Error(`${e.name} is a native function and therefore cannot be safely constructed by DI. If this is intentional, please use a callback or cachedCallback resolver.`);$o.set(e,t=new To(e,xo.getDependencies(e)))}return t}registerFactory(e,t){$o.set(e,t)}createChild(e){return new Ao(null,Object.assign({},this.config,e,{parentLocator:()=>this}))}jitRegister(e,t){if("function"!=typeof e)throw new Error(`Attempted to jitRegister something that is not a constructor: '${e}'. Did you forget to register this dependency?`);if(Eo.has(e.name))throw new Error(`Attempted to jitRegister an intrinsic type: ${e.name}. Did you forget to add @inject(Key)`);if(Vo(e)){const n=e.register(t);if(!(n instanceof Object)||null==n.resolve){const n=t.resolvers.get(e);if(null!=n)return n;throw new Error("A valid resolver was not returned from the static register method")}return n}if(e.$isInterface)throw new Error(`Attempted to jitRegister an interface: ${e.friendlyName}`);{const n=this.config.defaultResolver(e,t);return t.resolvers.set(e,n),n}}}const Bo=new WeakMap;function No(e){return function(t,n,s){if(Bo.has(s))return Bo.get(s);const r=e(t,n,s);return Bo.set(s,r),r}}const Ro=Object.freeze({instance:(e,t)=>new jo(e,0,t),singleton:(e,t)=>new jo(e,1,t),transient:(e,t)=>new jo(e,2,t),callback:(e,t)=>new jo(e,3,t),cachedCallback:(e,t)=>new jo(e,3,No(t)),aliasTo:(e,t)=>new jo(t,5,e)});function zo(e){if(null==e)throw new Error("key/value cannot be null or undefined. Are you trying to inject/register something that doesn't exist with DI?")}function Po(e,t,n){if(e instanceof jo&&4===e.strategy){const s=e.state;let r=s.length;const i=new Array(r);for(;r--;)i[r]=s[r].resolve(t,n);return i}return[e.resolve(t,n)]}const Ho="(anonymous)";function Uo(e){return"object"==typeof e&&null!==e||"function"==typeof e}const Wo=function(){const e=new WeakMap;let t=!1,n="",s=0;return function(r){return t=e.get(r),void 0===t&&(n=r.toString(),s=n.length,t=s>=29&&s<=100&&125===n.charCodeAt(s-1)&&n.charCodeAt(s-2)<=32&&93===n.charCodeAt(s-3)&&101===n.charCodeAt(s-4)&&100===n.charCodeAt(s-5)&&111===n.charCodeAt(s-6)&&99===n.charCodeAt(s-7)&&32===n.charCodeAt(s-8)&&101===n.charCodeAt(s-9)&&118===n.charCodeAt(s-10)&&105===n.charCodeAt(s-11)&&116===n.charCodeAt(s-12)&&97===n.charCodeAt(s-13)&&110===n.charCodeAt(s-14)&&88===n.charCodeAt(s-15),e.set(r,t)),t}}(),qo={};function Go(e){switch(typeof e){case"number":return e>=0&&(0|e)===e;case"string":{const t=qo[e];if(void 0!==t)return t;const n=e.length;if(0===n)return qo[e]=!1;let s=0;for(let t=0;t<n;++t)if(s=e.charCodeAt(t),0===t&&48===s&&n>1||s<48||s>57)return qo[e]=!1;return qo[e]=!0}default:return!1}}function Zo(e){return`${e.toLowerCase()}:presentation`}const Ko=new Map,Yo=Object.freeze({define(e,t,n){const s=Zo(e);void 0===Ko.get(s)?Ko.set(s,t):Ko.set(s,!1),n.register(Ro.instance(s,t))},forTag(e,t){const n=Zo(e),s=Ko.get(n);return!1===s?xo.findResponsibleContainer(t).get(n):s||null}});class Jo{constructor(e,t){this.template=e||null,this.styles=void 0===t?null:Array.isArray(t)?Hi.create(t):t instanceof Hi?t:Hi.create([t])}applyTo(e){const t=e.$fastController;null===t.template&&(t.template=this.template),null===t.styles&&(t.styles=this.styles)}}class Xo extends uo{constructor(){super(...arguments),this._presentation=void 0}get $presentation(){return void 0===this._presentation&&(this._presentation=Yo.forTag(this.tagName,this)),this._presentation}templateChanged(){void 0!==this.template&&(this.$fastController.template=this.template)}stylesChanged(){void 0!==this.styles&&(this.$fastController.styles=this.styles)}connectedCallback(){null!==this.$presentation&&this.$presentation.applyTo(this),super.connectedCallback()}static compose(e){return(t={})=>new ea(this===Xo?class extends Xo{}:this,e,t)}}function Qo(e,t,n){return"function"==typeof e?e(t,n):e}io([Ni],Xo.prototype,"template",void 0),io([Ni],Xo.prototype,"styles",void 0);class ea{constructor(e,t,n){this.type=e,this.elementDefinition=t,this.overrideDefinition=n,this.definition=Object.assign(Object.assign({},this.elementDefinition),this.overrideDefinition)}register(e,t){const n=this.definition,s=this.overrideDefinition,r=`${n.prefix||t.elementPrefix}-${n.baseName}`;t.tryDefineElement({name:r,type:this.type,baseClass:this.elementDefinition.baseClass,callback:e=>{const t=new Jo(Qo(n.template,e,n),Qo(n.styles,e,n));e.definePresentation(t);let r=Qo(n.shadowOptions,e,n);e.shadowRootMode&&(r?s.shadowOptions||(r.mode=e.shadowRootMode):null!==r&&(r={mode:e.shadowRootMode})),e.defineElement({elementOptions:Qo(n.elementOptions,e,n),shadowOptions:r,attributes:Qo(n.attributes,e,n)})}})}}class ta{createCSS(){return""}createBehavior(){}}function na(e){const t=e.parentElement;if(t)return t;{const t=e.getRootNode();if(t.host instanceof HTMLElement)return t.host}return null}const sa=document.createElement("div");class ra{setProperty(e,t){Oi.queueUpdate((()=>this.target.setProperty(e,t)))}removeProperty(e){Oi.queueUpdate((()=>this.target.removeProperty(e)))}}class ia extends ra{constructor(){super();const e=new CSSStyleSheet;this.target=e.cssRules[e.insertRule(":root{}")].style,document.adoptedStyleSheets=[...document.adoptedStyleSheets,e]}}class oa extends ra{constructor(){super(),this.style=document.createElement("style"),document.head.appendChild(this.style);const{sheet:e}=this.style;if(e){const t=e.insertRule(":root{}",e.cssRules.length);this.target=e.cssRules[t].style}}}class aa{constructor(e){this.store=new Map,this.target=null;const t=e.$fastController;this.style=document.createElement("style"),t.addStyles(this.style),Bi.getNotifier(t).subscribe(this,"isConnected"),this.handleChange(t,"isConnected")}targetChanged(){if(null!==this.target)for(const[e,t]of this.store.entries())this.target.setProperty(e,t)}setProperty(e,t){this.store.set(e,t),Oi.queueUpdate((()=>{null!==this.target&&this.target.setProperty(e,t)}))}removeProperty(e){this.store.delete(e),Oi.queueUpdate((()=>{null!==this.target&&this.target.removeProperty(e)}))}handleChange(e,t){const{sheet:n}=this.style;if(n){const e=n.insertRule(":host{}",n.cssRules.length);this.target=n.cssRules[e].style}else this.target=null}}io([Ni],aa.prototype,"target",void 0);class la{constructor(e){this.target=e.style}setProperty(e,t){Oi.queueUpdate((()=>this.target.setProperty(e,t)))}removeProperty(e){Oi.queueUpdate((()=>this.target.removeProperty(e)))}}class ca{setProperty(e,t){ca.properties[e]=t;for(const n of ca.roots.values())ua.getOrCreate(ca.normalizeRoot(n)).setProperty(e,t)}removeProperty(e){delete ca.properties[e];for(const t of ca.roots.values())ua.getOrCreate(ca.normalizeRoot(t)).removeProperty(e)}static registerRoot(e){const{roots:t}=ca;if(!t.has(e)){t.add(e);const n=ua.getOrCreate(this.normalizeRoot(e));for(const e in ca.properties)n.setProperty(e,ca.properties[e])}}static unregisterRoot(e){const{roots:t}=ca;if(t.has(e)){t.delete(e);const n=ua.getOrCreate(ca.normalizeRoot(e));for(const e in ca.properties)n.removeProperty(e)}}static normalizeRoot(e){return e===sa?document:e}}ca.roots=new Set,ca.properties={};const ha=new WeakMap,da=Oi.supportsAdoptedStyleSheets?class extends ra{constructor(e){super();const t=new CSSStyleSheet;this.target=t.cssRules[t.insertRule(":host{}")].style,e.$fastController.addStyles(Hi.create([t]))}}:aa,ua=Object.freeze({getOrCreate(e){if(ha.has(e))return ha.get(e);let t;return t=e===sa?new ca:e instanceof Document?Oi.supportsAdoptedStyleSheets?new ia:new oa:e instanceof uo?new da(e):new la(e),ha.set(e,t),t}});class pa extends ta{constructor(e){super(),this.subscribers=new WeakMap,this._appliedTo=new Set,this.name=e.name,null!==e.cssCustomPropertyName&&(this.cssCustomProperty=`--${e.cssCustomPropertyName}`,this.cssVar=`var(${this.cssCustomProperty})`),this.id=pa.uniqueId(),pa.tokensById.set(this.id,this)}get appliedTo(){return[...this._appliedTo]}static from(e){return new pa({name:"string"==typeof e?e:e.name,cssCustomPropertyName:"string"==typeof e?e:void 0===e.cssCustomPropertyName?e.name:e.cssCustomPropertyName})}static isCSSDesignToken(e){return"string"==typeof e.cssCustomProperty}static isDerivedDesignTokenValue(e){return"function"==typeof e}static getTokenById(e){return pa.tokensById.get(e)}getOrCreateSubscriberSet(e=this){return this.subscribers.get(e)||this.subscribers.set(e,new Set)&&this.subscribers.get(e)}createCSS(){return this.cssVar||""}getValueFor(e){const t=wa.getOrCreate(e).get(this);if(void 0!==t)return t;throw new Error(`Value could not be retrieved for token named "${this.name}". Ensure the value is set for ${e} or an ancestor of ${e}.`)}setValueFor(e,t){return this._appliedTo.add(e),t instanceof pa&&(t=this.alias(t)),wa.getOrCreate(e).set(this,t),this}deleteValueFor(e){return this._appliedTo.delete(e),wa.existsFor(e)&&wa.getOrCreate(e).delete(this),this}withDefault(e){return this.setValueFor(sa,e),this}subscribe(e,t){const n=this.getOrCreateSubscriberSet(t);t&&!wa.existsFor(t)&&wa.getOrCreate(t),n.has(e)||n.add(e)}unsubscribe(e,t){const n=this.subscribers.get(t||this);n&&n.has(e)&&n.delete(e)}notify(e){const t=Object.freeze({token:this,target:e});this.subscribers.has(this)&&this.subscribers.get(this).forEach((e=>e.handleChange(t))),this.subscribers.has(e)&&this.subscribers.get(e).forEach((e=>e.handleChange(t)))}alias(e){return t=>e.getValueFor(t)}}pa.uniqueId=(()=>{let e=0;return()=>(e++,e.toString(16))})(),pa.tokensById=new Map;class ga{constructor(e,t,n){this.source=e,this.token=t,this.node=n,this.dependencies=new Set,this.observer=Bi.binding(e,this,!1),this.observer.handleChange=this.observer.call,this.handleChange()}disconnect(){this.observer.disconnect()}handleChange(){this.node.store.set(this.token,this.observer.observe(this.node.target,Pi))}}class ma{constructor(){this.values=new Map}set(e,t){this.values.get(e)!==t&&(this.values.set(e,t),Bi.getNotifier(this).notify(e.id))}get(e){return Bi.track(this,e.id),this.values.get(e)}delete(e){this.values.delete(e)}all(){return this.values.entries()}}const fa=new WeakMap,va=new WeakMap;class wa{constructor(e){this.target=e,this.store=new ma,this.children=[],this.assignedValues=new Map,this.reflecting=new Set,this.bindingObservers=new Map,this.tokenValueChangeHandler={handleChange:(e,t)=>{const n=pa.getTokenById(t);n&&(n.notify(this.target),this.updateCSSTokenReflection(e,n))}},fa.set(e,this),Bi.getNotifier(this.store).subscribe(this.tokenValueChangeHandler),e instanceof uo?e.$fastController.addBehaviors([this]):e.isConnected&&this.bind()}static getOrCreate(e){return fa.get(e)||new wa(e)}static existsFor(e){return fa.has(e)}static findParent(e){if(sa!==e.target){let t=na(e.target);for(;null!==t;){if(fa.has(t))return fa.get(t);t=na(t)}return wa.getOrCreate(sa)}return null}static findClosestAssignedNode(e,t){let n=t;do{if(n.has(e))return n;n=n.parent?n.parent:n.target!==sa?wa.getOrCreate(sa):null}while(null!==n);return null}get parent(){return va.get(this)||null}updateCSSTokenReflection(e,t){if(pa.isCSSDesignToken(t)){const n=this.parent,s=this.isReflecting(t);if(n){const r=n.get(t),i=e.get(t);r===i||s?r===i&&s&&this.stopReflectToCSS(t):this.reflectToCSS(t)}else s||this.reflectToCSS(t)}}has(e){return this.assignedValues.has(e)}get(e){const t=this.store.get(e);if(void 0!==t)return t;const n=this.getRaw(e);return void 0!==n?(this.hydrate(e,n),this.get(e)):void 0}getRaw(e){var t;return this.assignedValues.has(e)?this.assignedValues.get(e):null===(t=wa.findClosestAssignedNode(e,this))||void 0===t?void 0:t.getRaw(e)}set(e,t){pa.isDerivedDesignTokenValue(this.assignedValues.get(e))&&this.tearDownBindingObserver(e),this.assignedValues.set(e,t),pa.isDerivedDesignTokenValue(t)?this.setupBindingObserver(e,t):this.store.set(e,t)}delete(e){this.assignedValues.delete(e),this.tearDownBindingObserver(e);const t=this.getRaw(e);t?this.hydrate(e,t):this.store.delete(e)}bind(){const e=wa.findParent(this);e&&e.appendChild(this);for(const e of this.assignedValues.keys())e.notify(this.target)}unbind(){this.parent&&va.get(this).removeChild(this)}appendChild(e){e.parent&&va.get(e).removeChild(e);const t=this.children.filter((t=>e.contains(t)));va.set(e,this),this.children.push(e),t.forEach((t=>e.appendChild(t))),Bi.getNotifier(this.store).subscribe(e);for(const[t,n]of this.store.all())e.hydrate(t,this.bindingObservers.has(t)?this.getRaw(t):n)}removeChild(e){const t=this.children.indexOf(e);return-1!==t&&this.children.splice(t,1),Bi.getNotifier(this.store).unsubscribe(e),e.parent===this&&va.delete(e)}contains(e){return function(e,t){let n=t;for(;null!==n;){if(n===e)return!0;n=na(n)}return!1}(this.target,e.target)}reflectToCSS(e){this.isReflecting(e)||(this.reflecting.add(e),wa.cssCustomPropertyReflector.startReflection(e,this.target))}stopReflectToCSS(e){this.isReflecting(e)&&(this.reflecting.delete(e),wa.cssCustomPropertyReflector.stopReflection(e,this.target))}isReflecting(e){return this.reflecting.has(e)}handleChange(e,t){const n=pa.getTokenById(t);n&&(this.hydrate(n,this.getRaw(n)),this.updateCSSTokenReflection(this.store,n))}hydrate(e,t){if(!this.has(e)){const n=this.bindingObservers.get(e);pa.isDerivedDesignTokenValue(t)?n?n.source!==t&&(this.tearDownBindingObserver(e),this.setupBindingObserver(e,t)):this.setupBindingObserver(e,t):(n&&this.tearDownBindingObserver(e),this.store.set(e,t))}}setupBindingObserver(e,t){const n=new ga(t,e,this);return this.bindingObservers.set(e,n),n}tearDownBindingObserver(e){return!!this.bindingObservers.has(e)&&(this.bindingObservers.get(e).disconnect(),this.bindingObservers.delete(e),!0)}}wa.cssCustomPropertyReflector=new class{startReflection(e,t){e.subscribe(this,t),this.handleChange({token:e,target:t})}stopReflection(e,t){e.unsubscribe(this,t),this.remove(e,t)}handleChange(e){const{token:t,target:n}=e;this.add(t,n)}add(e,t){ua.getOrCreate(t).setProperty(e.cssCustomProperty,this.resolveCSSValue(wa.getOrCreate(t).get(e)))}remove(e,t){ua.getOrCreate(t).removeProperty(e.cssCustomProperty)}resolveCSSValue(e){return e&&"function"==typeof e.createCSS?e.createCSS():e}},io([Ni],wa.prototype,"children",void 0);const ba=Object.freeze({create:function(e){return pa.from(e)},notifyConnection:e=>!(!e.isConnected||!wa.existsFor(e)||(wa.getOrCreate(e).bind(),0)),notifyDisconnection:e=>!(e.isConnected||!wa.existsFor(e)||(wa.getOrCreate(e).unbind(),0)),registerRoot(e=sa){ca.registerRoot(e)},unregisterRoot(e=sa){ca.unregisterRoot(e)}}),ya=Object.freeze({definitionCallbackOnly:null,ignoreDuplicate:Symbol()}),xa=new Map,Ca=new Map;let Sa=null;const _a=xo.createInterface((e=>e.cachedCallback((e=>(null===Sa&&(Sa=new Fa(null,e)),Sa))))),ka=Object.freeze({tagFor:e=>Ca.get(e),responsibleFor(e){const t=e.$$designSystem$$;return t||xo.findResponsibleContainer(e).get(_a)},getOrCreate(e){if(!e)return null===Sa&&(Sa=xo.getOrCreateDOMContainer().get(_a)),Sa;const t=e.$$designSystem$$;if(t)return t;const n=xo.getOrCreateDOMContainer(e);if(n.has(_a,!1))return n.get(_a);{const t=new Fa(e,n);return n.register(Ro.instance(_a,t)),t}}});class Fa{constructor(e,t){this.owner=e,this.container=t,this.designTokensInitialized=!1,this.prefix="fast",this.shadowRootMode=void 0,this.disambiguate=()=>ya.definitionCallbackOnly,null!==e&&(e.$$designSystem$$=this)}withPrefix(e){return this.prefix=e,this}withShadowRootMode(e){return this.shadowRootMode=e,this}withElementDisambiguation(e){return this.disambiguate=e,this}withDesignTokenRoot(e){return this.designTokenRoot=e,this}register(...e){const t=this.container,n=[],s=this.disambiguate,r=this.shadowRootMode,i={elementPrefix:this.prefix,tryDefineElement(e,i,o){const a=function(e,t,n){return"string"==typeof e?{name:e,type:t,callback:n}:e}(e,i,o),{name:l,callback:c,baseClass:h}=a;let{type:d}=a,u=l,p=xa.get(u),g=!0;for(;p;){const e=s(u,d,p);switch(e){case ya.ignoreDuplicate:return;case ya.definitionCallbackOnly:g=!1,p=void 0;break;default:u=e,p=xa.get(u)}}g&&((Ca.has(d)||d===Xo)&&(d=class extends d{}),xa.set(u,d),Ca.set(d,u),h&&Ca.set(h,u)),n.push(new ja(t,u,d,r,c,g))}};this.designTokensInitialized||(this.designTokensInitialized=!0,null!==this.designTokenRoot&&ba.registerRoot(this.designTokenRoot)),t.registerWithContext(i,...e);for(const e of n)e.callback(e),e.willDefine&&null!==e.definition&&e.definition.define();return this}}class ja{constructor(e,t,n,s,r,i){this.container=e,this.name=t,this.type=n,this.shadowRootMode=s,this.callback=r,this.willDefine=i,this.definition=null}definePresentation(e){Yo.define(this.name,e,this.container)}defineElement(e){this.definition=new ro(this.type,Object.assign(Object.assign({},e),{name:this.name}))}tagFor(e){return ka.tagFor(e)}}function Ma(e){return ka.getOrCreate(e).withPrefix("jp")}class Ia{}io([eo({attribute:"aria-atomic"})],Ia.prototype,"ariaAtomic",void 0),io([eo({attribute:"aria-busy"})],Ia.prototype,"ariaBusy",void 0),io([eo({attribute:"aria-controls"})],Ia.prototype,"ariaControls",void 0),io([eo({attribute:"aria-current"})],Ia.prototype,"ariaCurrent",void 0),io([eo({attribute:"aria-describedby"})],Ia.prototype,"ariaDescribedby",void 0),io([eo({attribute:"aria-details"})],Ia.prototype,"ariaDetails",void 0),io([eo({attribute:"aria-disabled"})],Ia.prototype,"ariaDisabled",void 0),io([eo({attribute:"aria-errormessage"})],Ia.prototype,"ariaErrormessage",void 0),io([eo({attribute:"aria-flowto"})],Ia.prototype,"ariaFlowto",void 0),io([eo({attribute:"aria-haspopup"})],Ia.prototype,"ariaHaspopup",void 0),io([eo({attribute:"aria-hidden"})],Ia.prototype,"ariaHidden",void 0),io([eo({attribute:"aria-invalid"})],Ia.prototype,"ariaInvalid",void 0),io([eo({attribute:"aria-keyshortcuts"})],Ia.prototype,"ariaKeyshortcuts",void 0),io([eo({attribute:"aria-label"})],Ia.prototype,"ariaLabel",void 0),io([eo({attribute:"aria-labelledby"})],Ia.prototype,"ariaLabelledby",void 0),io([eo({attribute:"aria-live"})],Ia.prototype,"ariaLive",void 0),io([eo({attribute:"aria-owns"})],Ia.prototype,"ariaOwns",void 0),io([eo({attribute:"aria-relevant"})],Ia.prototype,"ariaRelevant",void 0),io([eo({attribute:"aria-roledescription"})],Ia.prototype,"ariaRoledescription",void 0);class Ta{constructor(){this.targetIndex=0}}class La extends Ta{constructor(){super(...arguments),this.createPlaceholder=Oi.createInterpolationPlaceholder}}class Va extends Ta{constructor(e,t,n){super(),this.name=e,this.behavior=t,this.options=n}createPlaceholder(e){return Oi.createCustomAttributePlaceholder(this.name,e)}createBehavior(e){return new this.behavior(e,this.options)}}function Da(e,t){this.source=e,this.context=t,null===this.bindingObserver&&(this.bindingObserver=Bi.binding(this.binding,this,this.isBindingVolatile)),this.updateTarget(this.bindingObserver.observe(e,t))}function Ea(e,t){this.source=e,this.context=t,this.target.addEventListener(this.targetName,this)}function Oa(){this.bindingObserver.disconnect(),this.source=null,this.context=null}function $a(){this.bindingObserver.disconnect(),this.source=null,this.context=null;const e=this.target.$fastView;void 0!==e&&e.isComposed&&(e.unbind(),e.needsBindOnly=!0)}function Aa(){this.target.removeEventListener(this.targetName,this),this.source=null,this.context=null}function Ba(e){Oi.setAttribute(this.target,this.targetName,e)}function Na(e){Oi.setBooleanAttribute(this.target,this.targetName,e)}function Ra(e){if(null==e&&(e=""),e.create){this.target.textContent="";let t=this.target.$fastView;void 0===t?t=e.create():this.target.$fastTemplate!==e&&(t.isComposed&&(t.remove(),t.unbind()),t=e.create()),t.isComposed?t.needsBindOnly&&(t.needsBindOnly=!1,t.bind(this.source,this.context)):(t.isComposed=!0,t.bind(this.source,this.context),t.insertBefore(this.target),this.target.$fastView=t,this.target.$fastTemplate=e)}else{const t=this.target.$fastView;void 0!==t&&t.isComposed&&(t.isComposed=!1,t.remove(),t.needsBindOnly?t.needsBindOnly=!1:t.unbind()),this.target.textContent=e}}function za(e){this.target[this.targetName]=e}function Pa(e){const t=this.classVersions||Object.create(null),n=this.target;let s=this.version||0;if(null!=e&&e.length){const r=e.split(/\s+/);for(let e=0,i=r.length;e<i;++e){const i=r[e];""!==i&&(t[i]=s,n.classList.add(i))}}if(this.classVersions=t,this.version=s+1,0!==s){s-=1;for(const e in t)t[e]===s&&n.classList.remove(e)}}class Ha extends La{constructor(e){super(),this.binding=e,this.bind=Da,this.unbind=Oa,this.updateTarget=Ba,this.isBindingVolatile=Bi.isVolatileBinding(this.binding)}get targetName(){return this.originalTargetName}set targetName(e){if(this.originalTargetName=e,void 0!==e)switch(e[0]){case":":if(this.cleanedTargetName=e.substr(1),this.updateTarget=za,"innerHTML"===this.cleanedTargetName){const e=this.binding;this.binding=(t,n)=>Oi.createHTML(e(t,n))}break;case"?":this.cleanedTargetName=e.substr(1),this.updateTarget=Na;break;case"@":this.cleanedTargetName=e.substr(1),this.bind=Ea,this.unbind=Aa;break;default:this.cleanedTargetName=e,"class"===e&&(this.updateTarget=Pa)}}targetAtContent(){this.updateTarget=Ra,this.unbind=$a}createBehavior(e){return new Ua(e,this.binding,this.isBindingVolatile,this.bind,this.unbind,this.updateTarget,this.cleanedTargetName)}}class Ua{constructor(e,t,n,s,r,i,o){this.source=null,this.context=null,this.bindingObserver=null,this.target=e,this.binding=t,this.isBindingVolatile=n,this.bind=s,this.unbind=r,this.updateTarget=i,this.targetName=o}handleChange(){this.updateTarget(this.bindingObserver.observe(this.source,this.context))}handleEvent(e){zi.setEvent(e);const t=this.binding(this.source,this.context);zi.setEvent(null),!0!==t&&e.preventDefault()}}let Wa=null;class qa{addFactory(e){e.targetIndex=this.targetIndex,this.behaviorFactories.push(e)}captureContentBinding(e){e.targetAtContent(),this.addFactory(e)}reset(){this.behaviorFactories=[],this.targetIndex=-1}release(){Wa=this}static borrow(e){const t=Wa||new qa;return t.directives=e,t.reset(),Wa=null,t}}function Ga(e){if(1===e.length)return e[0];let t;const n=e.length,s=e.map((e=>"string"==typeof e?()=>e:(t=e.targetName||t,e.binding))),r=new Ha(((e,t)=>{let r="";for(let i=0;i<n;++i)r+=s[i](e,t);return r}));return r.targetName=t,r}const Za=Ei.length;function Ka(e,t){const n=t.split(Di);if(1===n.length)return null;const s=[];for(let t=0,r=n.length;t<r;++t){const r=n[t],i=r.indexOf(Ei);let o;if(-1===i)o=r;else{const t=parseInt(r.substring(0,i));s.push(e.directives[t]),o=r.substring(i+Za)}""!==o&&s.push(o)}return s}function Ya(e,t,n=!1){const s=t.attributes;for(let r=0,i=s.length;r<i;++r){const o=s[r],a=o.value,l=Ka(e,a);let c=null;null===l?n&&(c=new Ha((()=>a)),c.targetName=o.name):c=Ga(l),null!==c&&(t.removeAttributeNode(o),r--,i--,e.addFactory(c))}}function Ja(e,t,n){const s=Ka(e,t.textContent);if(null!==s){let r=t;for(let i=0,o=s.length;i<o;++i){const o=s[i],a=0===i?t:r.parentNode.insertBefore(document.createTextNode(""),r.nextSibling);"string"==typeof o?a.textContent=o:(a.textContent=" ",e.captureContentBinding(o)),r=a,e.targetIndex++,a!==t&&n.nextNode()}e.targetIndex--}}const Xa=document.createRange();class Qa{constructor(e,t){this.fragment=e,this.behaviors=t,this.source=null,this.context=null,this.firstChild=e.firstChild,this.lastChild=e.lastChild}appendTo(e){e.appendChild(this.fragment)}insertBefore(e){if(this.fragment.hasChildNodes())e.parentNode.insertBefore(this.fragment,e);else{const t=this.lastChild;if(e.previousSibling===t)return;const n=e.parentNode;let s,r=this.firstChild;for(;r!==t;)s=r.nextSibling,n.insertBefore(r,e),r=s;n.insertBefore(t,e)}}remove(){const e=this.fragment,t=this.lastChild;let n,s=this.firstChild;for(;s!==t;)n=s.nextSibling,e.appendChild(s),s=n;e.appendChild(t)}dispose(){const e=this.firstChild.parentNode,t=this.lastChild;let n,s=this.firstChild;for(;s!==t;)n=s.nextSibling,e.removeChild(s),s=n;e.removeChild(t);const r=this.behaviors,i=this.source;for(let e=0,t=r.length;e<t;++e)r[e].unbind(i)}bind(e,t){const n=this.behaviors;if(this.source!==e)if(null!==this.source){const s=this.source;this.source=e,this.context=t;for(let r=0,i=n.length;r<i;++r){const i=n[r];i.unbind(s),i.bind(e,t)}}else{this.source=e,this.context=t;for(let s=0,r=n.length;s<r;++s)n[s].bind(e,t)}}unbind(){if(null===this.source)return;const e=this.behaviors,t=this.source;for(let n=0,s=e.length;n<s;++n)e[n].unbind(t);this.source=null}static disposeContiguousBatch(e){if(0!==e.length){Xa.setStartBefore(e[0].firstChild),Xa.setEndAfter(e[e.length-1].lastChild),Xa.deleteContents();for(let t=0,n=e.length;t<n;++t){const n=e[t],s=n.behaviors,r=n.source;for(let e=0,t=s.length;e<t;++e)s[e].unbind(r)}}}}class el{constructor(e,t){this.behaviorCount=0,this.hasHostBehaviors=!1,this.fragment=null,this.targetOffset=0,this.viewBehaviorFactories=null,this.hostBehaviorFactories=null,this.html=e,this.directives=t}create(e){if(null===this.fragment){let e;const t=this.html;if("string"==typeof t){e=document.createElement("template"),e.innerHTML=Oi.createHTML(t);const n=e.content.firstElementChild;null!==n&&"TEMPLATE"===n.tagName&&(e=n)}else e=t;const n=function(e,t){const n=e.content;document.adoptNode(n);const s=qa.borrow(t);Ya(s,e,!0);const r=s.behaviorFactories;s.reset();const i=Oi.createTemplateWalker(n);let o;for(;o=i.nextNode();)switch(s.targetIndex++,o.nodeType){case 1:Ya(s,o);break;case 3:Ja(s,o,i);break;case 8:Oi.isMarker(o)&&s.addFactory(t[Oi.extractDirectiveIndexFromMarker(o)])}let a=0;(Oi.isMarker(n.firstChild)||1===n.childNodes.length&&t.length)&&(n.insertBefore(document.createComment(""),n.firstChild),a=-1);const l=s.behaviorFactories;return s.release(),{fragment:n,viewBehaviorFactories:l,hostBehaviorFactories:r,targetOffset:a}}(e,this.directives);this.fragment=n.fragment,this.viewBehaviorFactories=n.viewBehaviorFactories,this.hostBehaviorFactories=n.hostBehaviorFactories,this.targetOffset=n.targetOffset,this.behaviorCount=this.viewBehaviorFactories.length+this.hostBehaviorFactories.length,this.hasHostBehaviors=this.hostBehaviorFactories.length>0}const t=this.fragment.cloneNode(!0),n=this.viewBehaviorFactories,s=new Array(this.behaviorCount),r=Oi.createTemplateWalker(t);let i=0,o=this.targetOffset,a=r.nextNode();for(let e=n.length;i<e;++i){const e=n[i],t=e.targetIndex;for(;null!==a;){if(o===t){s[i]=e.createBehavior(a);break}a=r.nextNode(),o++}}if(this.hasHostBehaviors){const t=this.hostBehaviorFactories;for(let n=0,r=t.length;n<r;++n,++i)s[i]=t[n].createBehavior(e)}return new Qa(t,s)}render(e,t,n){"string"==typeof t&&(t=document.getElementById(t)),void 0===n&&(n=t);const s=this.create(n);return s.bind(e,Pi),s.appendTo(t),s}}const tl=/([ \x09\x0a\x0c\x0d])([^\0-\x1F\x7F-\x9F "'>=/]+)([ \x09\x0a\x0c\x0d]*=[ \x09\x0a\x0c\x0d]*(?:[^ \x09\x0a\x0c\x0d"'`<>=]*|"[^"]*|'[^']*))$/;function nl(e,...t){const n=[];let s="";for(let r=0,i=e.length-1;r<i;++r){const i=e[r];let o=t[r];if(s+=i,o instanceof el){const e=o;o=()=>e}if("function"==typeof o&&(o=new Ha(o)),o instanceof La){const e=tl.exec(i);null!==e&&(o.targetName=e[2])}o instanceof Ta?(s+=o.createPlaceholder(n.length),n.push(o)):s+=o}return s+=e[e.length-1],new el(s,n)}class sl{constructor(e,t){this.target=e,this.propertyName=t}bind(e){e[this.propertyName]=this.target}unbind(){}}function rl(e){return new Va("fast-ref",sl,e)}class il{handleStartContentChange(){this.startContainer.classList.toggle("start",this.start.assignedNodes().length>0)}handleEndContentChange(){this.endContainer.classList.toggle("end",this.end.assignedNodes().length>0)}}const ol=(e,t)=>nl`
    <span
        part="end"
        ${rl("endContainer")}
        class=${e=>t.end?"end":void 0}
    >
        <slot name="end" ${rl("end")} @slotchange="${e=>e.handleEndContentChange()}">
            ${t.end||""}
        </slot>
    </span>
`,al=(e,t)=>nl`
    <span
        part="start"
        ${rl("startContainer")}
        class="${e=>t.start?"start":void 0}"
    >
        <slot
            name="start"
            ${rl("start")}
            @slotchange="${e=>e.handleStartContentChange()}"
        >
            ${t.start||""}
        </slot>
    </span>
`;function ll(e,...t){const n=Ji.locate(e);t.forEach((t=>{Object.getOwnPropertyNames(t.prototype).forEach((n=>{"constructor"!==n&&Object.defineProperty(e.prototype,n,Object.getOwnPropertyDescriptor(t.prototype,n))})),Ji.locate(t).forEach((e=>n.push(e)))}))}var cl;nl`
    <span part="end" ${rl("endContainer")}>
        <slot
            name="end"
            ${rl("end")}
            @slotchange="${e=>e.handleEndContentChange()}"
        ></slot>
    </span>
`,nl`
    <span part="start" ${rl("startContainer")}>
        <slot
            name="start"
            ${rl("start")}
            @slotchange="${e=>e.handleStartContentChange()}"
        ></slot>
    </span>
`,function(e){e[e.alt=18]="alt",e[e.arrowDown=40]="arrowDown",e[e.arrowLeft=37]="arrowLeft",e[e.arrowRight=39]="arrowRight",e[e.arrowUp=38]="arrowUp",e[e.back=8]="back",e[e.backSlash=220]="backSlash",e[e.break=19]="break",e[e.capsLock=20]="capsLock",e[e.closeBracket=221]="closeBracket",e[e.colon=186]="colon",e[e.colon2=59]="colon2",e[e.comma=188]="comma",e[e.ctrl=17]="ctrl",e[e.delete=46]="delete",e[e.end=35]="end",e[e.enter=13]="enter",e[e.equals=187]="equals",e[e.equals2=61]="equals2",e[e.equals3=107]="equals3",e[e.escape=27]="escape",e[e.forwardSlash=191]="forwardSlash",e[e.function1=112]="function1",e[e.function10=121]="function10",e[e.function11=122]="function11",e[e.function12=123]="function12",e[e.function2=113]="function2",e[e.function3=114]="function3",e[e.function4=115]="function4",e[e.function5=116]="function5",e[e.function6=117]="function6",e[e.function7=118]="function7",e[e.function8=119]="function8",e[e.function9=120]="function9",e[e.home=36]="home",e[e.insert=45]="insert",e[e.menu=93]="menu",e[e.minus=189]="minus",e[e.minus2=109]="minus2",e[e.numLock=144]="numLock",e[e.numPad0=96]="numPad0",e[e.numPad1=97]="numPad1",e[e.numPad2=98]="numPad2",e[e.numPad3=99]="numPad3",e[e.numPad4=100]="numPad4",e[e.numPad5=101]="numPad5",e[e.numPad6=102]="numPad6",e[e.numPad7=103]="numPad7",e[e.numPad8=104]="numPad8",e[e.numPad9=105]="numPad9",e[e.numPadDivide=111]="numPadDivide",e[e.numPadDot=110]="numPadDot",e[e.numPadMinus=109]="numPadMinus",e[e.numPadMultiply=106]="numPadMultiply",e[e.numPadPlus=107]="numPadPlus",e[e.openBracket=219]="openBracket",e[e.pageDown=34]="pageDown",e[e.pageUp=33]="pageUp",e[e.period=190]="period",e[e.print=44]="print",e[e.quote=222]="quote",e[e.scrollLock=145]="scrollLock",e[e.shift=16]="shift",e[e.space=32]="space",e[e.tab=9]="tab",e[e.tilde=192]="tilde",e[e.windowsLeft=91]="windowsLeft",e[e.windowsOpera=219]="windowsOpera",e[e.windowsRight=92]="windowsRight"}(cl||(cl={}));const hl={ArrowDown:"ArrowDown",ArrowLeft:"ArrowLeft",ArrowRight:"ArrowRight",ArrowUp:"ArrowUp"},dl="form-associated-proxy",ul="ElementInternals",pl=ul in window&&"setFormValue"in window[ul].prototype,gl=new WeakMap;class ml extends Xo{}class fl extends(function(e){const t=class extends e{constructor(...e){super(...e),this.dirtyValue=!1,this.disabled=!1,this.proxyEventsToBlock=["change","click"],this.proxyInitialized=!1,this.required=!1,this.initialValue=this.initialValue||"",this.elementInternals||(this.formResetCallback=this.formResetCallback.bind(this))}static get formAssociated(){return pl}get validity(){return this.elementInternals?this.elementInternals.validity:this.proxy.validity}get form(){return this.elementInternals?this.elementInternals.form:this.proxy.form}get validationMessage(){return this.elementInternals?this.elementInternals.validationMessage:this.proxy.validationMessage}get willValidate(){return this.elementInternals?this.elementInternals.willValidate:this.proxy.willValidate}get labels(){if(this.elementInternals)return Object.freeze(Array.from(this.elementInternals.labels));if(this.proxy instanceof HTMLElement&&this.proxy.ownerDocument&&this.id){const e=this.proxy.labels,t=Array.from(this.proxy.getRootNode().querySelectorAll(`[for='${this.id}']`)),n=e?t.concat(Array.from(e)):t;return Object.freeze(n)}return ji}valueChanged(e,t){this.dirtyValue=!0,this.proxy instanceof HTMLElement&&(this.proxy.value=this.value),this.currentValue=this.value,this.setFormValue(this.value),this.validate()}currentValueChanged(){this.value=this.currentValue}initialValueChanged(e,t){this.dirtyValue||(this.value=this.initialValue,this.dirtyValue=!1)}disabledChanged(e,t){this.proxy instanceof HTMLElement&&(this.proxy.disabled=this.disabled),Oi.queueUpdate((()=>this.classList.toggle("disabled",this.disabled)))}nameChanged(e,t){this.proxy instanceof HTMLElement&&(this.proxy.name=this.name)}requiredChanged(e,t){this.proxy instanceof HTMLElement&&(this.proxy.required=this.required),Oi.queueUpdate((()=>this.classList.toggle("required",this.required))),this.validate()}get elementInternals(){if(!pl)return null;let e=gl.get(this);return e||(e=this.attachInternals(),gl.set(this,e)),e}connectedCallback(){super.connectedCallback(),this.addEventListener("keypress",this._keypressHandler),this.value||(this.value=this.initialValue,this.dirtyValue=!1),this.elementInternals||(this.attachProxy(),this.form&&this.form.addEventListener("reset",this.formResetCallback))}disconnectedCallback(){super.disconnectedCallback(),this.proxyEventsToBlock.forEach((e=>this.proxy.removeEventListener(e,this.stopPropagation))),!this.elementInternals&&this.form&&this.form.removeEventListener("reset",this.formResetCallback)}checkValidity(){return this.elementInternals?this.elementInternals.checkValidity():this.proxy.checkValidity()}reportValidity(){return this.elementInternals?this.elementInternals.reportValidity():this.proxy.reportValidity()}setValidity(e,t,n){this.elementInternals?this.elementInternals.setValidity(e,t,n):"string"==typeof t&&this.proxy.setCustomValidity(t)}formDisabledCallback(e){this.disabled=e}formResetCallback(){this.value=this.initialValue,this.dirtyValue=!1}attachProxy(){var e;this.proxyInitialized||(this.proxyInitialized=!0,this.proxy.style.display="none",this.proxyEventsToBlock.forEach((e=>this.proxy.addEventListener(e,this.stopPropagation))),this.proxy.disabled=this.disabled,this.proxy.required=this.required,"string"==typeof this.name&&(this.proxy.name=this.name),"string"==typeof this.value&&(this.proxy.value=this.value),this.proxy.setAttribute("slot",dl),this.proxySlot=document.createElement("slot"),this.proxySlot.setAttribute("name",dl)),null===(e=this.shadowRoot)||void 0===e||e.appendChild(this.proxySlot),this.appendChild(this.proxy)}detachProxy(){var e;this.removeChild(this.proxy),null===(e=this.shadowRoot)||void 0===e||e.removeChild(this.proxySlot)}validate(e){this.proxy instanceof HTMLElement&&this.setValidity(this.proxy.validity,this.proxy.validationMessage,e)}setFormValue(e,t){this.elementInternals&&this.elementInternals.setFormValue(e,t||e)}_keypressHandler(e){if("Enter"===e.key&&this.form instanceof HTMLFormElement){const e=this.form.querySelector("[type=submit]");null==e||e.click()}}stopPropagation(e){e.stopPropagation()}};return eo({mode:"boolean"})(t.prototype,"disabled"),eo({mode:"fromView",attribute:"value"})(t.prototype,"initialValue"),eo({attribute:"current-value"})(t.prototype,"currentValue"),eo(t.prototype,"name"),eo({mode:"boolean"})(t.prototype,"required"),Ni(t.prototype,"value"),t}(ml)){constructor(){super(...arguments),this.proxy=document.createElement("input")}}class vl extends fl{constructor(){super(...arguments),this.handleClick=e=>{var t;this.disabled&&(null===(t=this.defaultSlottedContent)||void 0===t?void 0:t.length)<=1&&e.stopPropagation()},this.handleSubmission=()=>{if(!this.form)return;const e=this.proxy.isConnected;e||this.attachProxy(),"function"==typeof this.form.requestSubmit?this.form.requestSubmit(this.proxy):this.proxy.click(),e||this.detachProxy()},this.handleFormReset=()=>{var e;null===(e=this.form)||void 0===e||e.reset()},this.handleUnsupportedDelegatesFocus=()=>{var e;window.ShadowRoot&&!window.ShadowRoot.prototype.hasOwnProperty("delegatesFocus")&&(null===(e=this.$fastController.definition.shadowOptions)||void 0===e?void 0:e.delegatesFocus)&&(this.focus=()=>{this.control.focus()})}}formactionChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formAction=this.formaction)}formenctypeChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formEnctype=this.formenctype)}formmethodChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formMethod=this.formmethod)}formnovalidateChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formNoValidate=this.formnovalidate)}formtargetChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formTarget=this.formtarget)}typeChanged(e,t){this.proxy instanceof HTMLInputElement&&(this.proxy.type=this.type),"submit"===t&&this.addEventListener("click",this.handleSubmission),"submit"===e&&this.removeEventListener("click",this.handleSubmission),"reset"===t&&this.addEventListener("click",this.handleFormReset),"reset"===e&&this.removeEventListener("click",this.handleFormReset)}validate(){super.validate(this.control)}connectedCallback(){var e;super.connectedCallback(),this.proxy.setAttribute("type",this.type),this.handleUnsupportedDelegatesFocus();const t=Array.from(null===(e=this.control)||void 0===e?void 0:e.children);t&&t.forEach((e=>{e.addEventListener("click",this.handleClick)}))}disconnectedCallback(){var e;super.disconnectedCallback();const t=Array.from(null===(e=this.control)||void 0===e?void 0:e.children);t&&t.forEach((e=>{e.removeEventListener("click",this.handleClick)}))}}io([eo({mode:"boolean"})],vl.prototype,"autofocus",void 0),io([eo({attribute:"form"})],vl.prototype,"formId",void 0),io([eo],vl.prototype,"formaction",void 0),io([eo],vl.prototype,"formenctype",void 0),io([eo],vl.prototype,"formmethod",void 0),io([eo({mode:"boolean"})],vl.prototype,"formnovalidate",void 0),io([eo],vl.prototype,"formtarget",void 0),io([eo],vl.prototype,"type",void 0),io([Ni],vl.prototype,"defaultSlottedContent",void 0);class wl{}function bl(e){return e?function(t,n,s){return 1===t.nodeType&&t.matches(e)}:function(e,t,n){return 1===e.nodeType}}io([eo({attribute:"aria-expanded"})],wl.prototype,"ariaExpanded",void 0),io([eo({attribute:"aria-pressed"})],wl.prototype,"ariaPressed",void 0),ll(wl,Ia),ll(vl,il,wl);class yl{constructor(e,t){this.target=e,this.options=t,this.source=null}bind(e){const t=this.options.property;this.shouldUpdate=Bi.getAccessors(e).some((e=>e.name===t)),this.source=e,this.updateTarget(this.computeNodes()),this.shouldUpdate&&this.observe()}unbind(){this.updateTarget(ji),this.source=null,this.shouldUpdate&&this.disconnect()}handleEvent(){this.updateTarget(this.computeNodes())}computeNodes(){let e=this.getNodes();return void 0!==this.options.filter&&(e=e.filter(this.options.filter)),e}updateTarget(e){this.source[this.options.property]=e}}class xl extends yl{constructor(e,t){super(e,t)}observe(){this.target.addEventListener("slotchange",this)}disconnect(){this.target.removeEventListener("slotchange",this)}getNodes(){return this.target.assignedNodes(this.options)}}function Cl(e){return"string"==typeof e&&(e={property:e}),new Va("fast-slotted",xl,e)}function Sl(e,t){const n=[];let s="";const r=[];for(let i=0,o=e.length-1;i<o;++i){s+=e[i];let o=t[i];if(o instanceof ta){const e=o.createBehavior();o=o.createCSS(),e&&r.push(e)}o instanceof Hi||o instanceof CSSStyleSheet?(""!==s.trim()&&(n.push(s),s=""),n.push(o)):s+=o}return s+=e[e.length-1],""!==s.trim()&&n.push(s),{styles:n,behaviors:r}}function _l(e,...t){const{styles:n,behaviors:s}=Sl(e,t),r=Hi.create(n);return s.length&&r.withBehaviors(...s),r}class kl extends ta{constructor(e,t){super(),this.behaviors=t,this.css="";const n=e.reduce(((e,t)=>("string"==typeof t?this.css+=t:e.push(t),e)),[]);n.length&&(this.styles=Hi.create(n))}createBehavior(){return this}createCSS(){return this.css}bind(e){this.styles&&e.$fastController.addStyles(this.styles),this.behaviors.length&&e.$fastController.addBehaviors(this.behaviors)}unbind(e){this.styles&&e.$fastController.removeStyles(this.styles),this.behaviors.length&&e.$fastController.removeBehaviors(this.behaviors)}}const Fl="not-allowed";class jl{constructor(e){this.listenerCache=new WeakMap,this.query=e}bind(e){const{query:t}=this,n=this.constructListener(e);n.bind(t)(),t.addListener(n),this.listenerCache.set(e,n)}unbind(e){const t=this.listenerCache.get(e);t&&(this.query.removeListener(t),this.listenerCache.delete(e))}}class Ml extends jl{constructor(e,t){super(e),this.styles=t}static with(e){return t=>new Ml(e,t)}constructListener(e){let t=!1;const n=this.styles;return function(){const{matches:s}=this;s&&!t?(e.$fastController.addStyles(n),t=s):!s&&t&&(e.$fastController.removeStyles(n),t=s)}}unbind(e){super.unbind(e),e.$fastController.removeStyles(this.styles)}}const Il=Ml.with(window.matchMedia("(forced-colors)"));var Tl,Ll;function Vl(e,t,n){return isNaN(e)||e<=t?t:e>=n?n:e}function Dl(e,t,n){return isNaN(e)||e<=t?0:e>=n?1:e/(n-t)}function El(e,t,n){return isNaN(e)?t:t+e*(n-t)}function Ol(e){return e*(Math.PI/180)}function $l(e,t,n){return isNaN(e)||e<=0?t:e>=1?n:t+e*(n-t)}function Al(e,t,n){if(e<=0)return t%360;if(e>=1)return n%360;const s=(t-n+360)%360;return s<=(n-t+360)%360?(t-s*e+360)%360:(t+s*e+360)%360}function Bl(e,t){const n=Math.pow(10,t);return Math.round(e*n)/n}Ml.with(window.matchMedia("(prefers-color-scheme: dark)")),Ml.with(window.matchMedia("(prefers-color-scheme: light)")),function(e){e.Canvas="Canvas",e.CanvasText="CanvasText",e.LinkText="LinkText",e.VisitedText="VisitedText",e.ActiveText="ActiveText",e.ButtonFace="ButtonFace",e.ButtonText="ButtonText",e.Field="Field",e.FieldText="FieldText",e.Highlight="Highlight",e.HighlightText="HighlightText",e.GrayText="GrayText"}(Tl||(Tl={})),function(e){e.ltr="ltr",e.rtl="rtl"}(Ll||(Ll={})),Math.PI;class Nl{constructor(e,t,n,s){this.r=e,this.g=t,this.b=n,this.a="number"!=typeof s||isNaN(s)?1:s}static fromObject(e){return!e||isNaN(e.r)||isNaN(e.g)||isNaN(e.b)?null:new Nl(e.r,e.g,e.b,e.a)}equalValue(e){return this.r===e.r&&this.g===e.g&&this.b===e.b&&this.a===e.a}toStringHexRGB(){return"#"+[this.r,this.g,this.b].map(this.formatHexValue).join("")}toStringHexRGBA(){return this.toStringHexRGB()+this.formatHexValue(this.a)}toStringHexARGB(){return"#"+[this.a,this.r,this.g,this.b].map(this.formatHexValue).join("")}toStringWebRGB(){return`rgb(${Math.round(El(this.r,0,255))},${Math.round(El(this.g,0,255))},${Math.round(El(this.b,0,255))})`}toStringWebRGBA(){return`rgba(${Math.round(El(this.r,0,255))},${Math.round(El(this.g,0,255))},${Math.round(El(this.b,0,255))},${Vl(this.a,0,1)})`}roundToPrecision(e){return new Nl(Bl(this.r,e),Bl(this.g,e),Bl(this.b,e),Bl(this.a,e))}clamp(){return new Nl(Vl(this.r,0,1),Vl(this.g,0,1),Vl(this.b,0,1),Vl(this.a,0,1))}toObject(){return{r:this.r,g:this.g,b:this.b,a:this.a}}formatHexValue(e){return function(e){const t=Math.round(Vl(e,0,255)).toString(16);return 1===t.length?"0"+t:t}(El(e,0,255))}}class Rl{constructor(e,t,n){this.h=e,this.s=t,this.l=n}static fromObject(e){return!e||isNaN(e.h)||isNaN(e.s)||isNaN(e.l)?null:new Rl(e.h,e.s,e.l)}equalValue(e){return this.h===e.h&&this.s===e.s&&this.l===e.l}roundToPrecision(e){return new Rl(Bl(this.h,e),Bl(this.s,e),Bl(this.l,e))}toObject(){return{h:this.h,s:this.s,l:this.l}}}class zl{constructor(e,t,n){this.h=e,this.s=t,this.v=n}static fromObject(e){return!e||isNaN(e.h)||isNaN(e.s)||isNaN(e.v)?null:new zl(e.h,e.s,e.v)}equalValue(e){return this.h===e.h&&this.s===e.s&&this.v===e.v}roundToPrecision(e){return new zl(Bl(this.h,e),Bl(this.s,e),Bl(this.v,e))}toObject(){return{h:this.h,s:this.s,v:this.v}}}class Pl{constructor(e,t,n){this.l=e,this.a=t,this.b=n}static fromObject(e){return!e||isNaN(e.l)||isNaN(e.a)||isNaN(e.b)?null:new Pl(e.l,e.a,e.b)}equalValue(e){return this.l===e.l&&this.a===e.a&&this.b===e.b}roundToPrecision(e){return new Pl(Bl(this.l,e),Bl(this.a,e),Bl(this.b,e))}toObject(){return{l:this.l,a:this.a,b:this.b}}}Pl.epsilon=216/24389,Pl.kappa=24389/27;class Hl{constructor(e,t,n){this.l=e,this.c=t,this.h=n}static fromObject(e){return!e||isNaN(e.l)||isNaN(e.c)||isNaN(e.h)?null:new Hl(e.l,e.c,e.h)}equalValue(e){return this.l===e.l&&this.c===e.c&&this.h===e.h}roundToPrecision(e){return new Hl(Bl(this.l,e),Bl(this.c,e),Bl(this.h,e))}toObject(){return{l:this.l,c:this.c,h:this.h}}}class Ul{constructor(e,t,n){this.x=e,this.y=t,this.z=n}static fromObject(e){return!e||isNaN(e.x)||isNaN(e.y)||isNaN(e.z)?null:new Ul(e.x,e.y,e.z)}equalValue(e){return this.x===e.x&&this.y===e.y&&this.z===e.z}roundToPrecision(e){return new Ul(Bl(this.x,e),Bl(this.y,e),Bl(this.z,e))}toObject(){return{x:this.x,y:this.y,z:this.z}}}function Wl(e){return.2126*e.r+.7152*e.g+.0722*e.b}function ql(e){function t(e){return e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4)}return Wl(new Nl(t(e.r),t(e.g),t(e.b),1))}Ul.whitePoint=new Ul(.95047,1,1.08883);const Gl=(e,t)=>(e+.05)/(t+.05);function Zl(e,t){const n=ql(e),s=ql(t);return n>s?Gl(n,s):Gl(s,n)}function Kl(e){const t=Math.max(e.r,e.g,e.b),n=Math.min(e.r,e.g,e.b),s=t-n;let r=0;0!==s&&(r=t===e.r?(e.g-e.b)/s%6*60:t===e.g?60*((e.b-e.r)/s+2):60*((e.r-e.g)/s+4)),r<0&&(r+=360);const i=(t+n)/2;let o=0;return 0!==s&&(o=s/(1-Math.abs(2*i-1))),new Rl(r,o,i)}function Yl(e,t=1){const n=(1-Math.abs(2*e.l-1))*e.s,s=n*(1-Math.abs(e.h/60%2-1)),r=e.l-n/2;let i=0,o=0,a=0;return e.h<60?(i=n,o=s,a=0):e.h<120?(i=s,o=n,a=0):e.h<180?(i=0,o=n,a=s):e.h<240?(i=0,o=s,a=n):e.h<300?(i=s,o=0,a=n):e.h<360&&(i=n,o=0,a=s),new Nl(i+r,o+r,a+r,t)}function Jl(e){const t=Math.max(e.r,e.g,e.b),n=t-Math.min(e.r,e.g,e.b);let s=0;0!==n&&(s=t===e.r?(e.g-e.b)/n%6*60:t===e.g?60*((e.b-e.r)/n+2):60*((e.r-e.g)/n+4)),s<0&&(s+=360);let r=0;return 0!==t&&(r=n/t),new zl(s,r,t)}function Xl(e){function t(e){return e<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4)}const n=t(e.r),s=t(e.g),r=t(e.b);return new Ul(.4124564*n+.3575761*s+.1804375*r,.2126729*n+.7151522*s+.072175*r,.0193339*n+.119192*s+.9503041*r)}function Ql(e,t=1){function n(e){return e<=.0031308?12.92*e:1.055*Math.pow(e,1/2.4)-.055}const s=n(3.2404542*e.x-1.5371385*e.y-.4985314*e.z),r=n(-.969266*e.x+1.8760108*e.y+.041556*e.z),i=n(.0556434*e.x-.2040259*e.y+1.0572252*e.z);return new Nl(s,r,i,t)}function ec(e){return function(e){function t(e){return e>Pl.epsilon?Math.pow(e,1/3):(Pl.kappa*e+16)/116}const n=t(e.x/Ul.whitePoint.x),s=t(e.y/Ul.whitePoint.y),r=t(e.z/Ul.whitePoint.z);return new Pl(116*s-16,500*(n-s),200*(s-r))}(Xl(e))}function tc(e,t=1){return Ql(function(e){const t=(e.l+16)/116,n=t+e.a/500,s=t-e.b/200,r=Math.pow(n,3),i=Math.pow(t,3),o=Math.pow(s,3);let a=0;a=r>Pl.epsilon?r:(116*n-16)/Pl.kappa;let l=0;l=e.l>Pl.epsilon*Pl.kappa?i:e.l/Pl.kappa;let c=0;return c=o>Pl.epsilon?o:(116*s-16)/Pl.kappa,a=Ul.whitePoint.x*a,l=Ul.whitePoint.y*l,c=Ul.whitePoint.z*c,new Ul(a,l,c)}(e),t)}function nc(e){return function(e){let t=0;(Math.abs(e.b)>.001||Math.abs(e.a)>.001)&&(t=function(e){return e*(180/Math.PI)}(Math.atan2(e.b,e.a))),t<0&&(t+=360);const n=Math.sqrt(e.a*e.a+e.b*e.b);return new Hl(e.l,n,t)}(ec(e))}function sc(e,t=1){return tc(function(e){let t=0,n=0;return 0!==e.h&&(t=Math.cos(Ol(e.h))*e.c,n=Math.sin(Ol(e.h))*e.c),new Pl(e.l,t,n)}(e),t)}function rc(e,t,n=18){const s=nc(e);let r=s.c+t*n;return r<0&&(r=0),sc(new Hl(s.l,r,s.h))}function ic(e,t){return e*t}function oc(e,t){return new Nl(ic(e.r,t.r),ic(e.g,t.g),ic(e.b,t.b),1)}function ac(e,t){return Vl(e<.5?2*t*e:1-2*(1-t)*(1-e),0,1)}function lc(e,t){return new Nl(ac(e.r,t.r),ac(e.g,t.g),ac(e.b,t.b),1)}var cc,hc;function dc(e,t,n,s){if(isNaN(e)||e<=0)return n;if(e>=1)return s;switch(t){case hc.HSL:return Yl(function(e,t,n){return isNaN(e)||e<=0?t:e>=1?n:new Rl(Al(e,t.h,n.h),$l(e,t.s,n.s),$l(e,t.l,n.l))}(e,Kl(n),Kl(s)));case hc.HSV:return function(e,t=1){const n=e.s*e.v,s=n*(1-Math.abs(e.h/60%2-1)),r=e.v-n;let i=0,o=0,a=0;return e.h<60?(i=n,o=s,a=0):e.h<120?(i=s,o=n,a=0):e.h<180?(i=0,o=n,a=s):e.h<240?(i=0,o=s,a=n):e.h<300?(i=s,o=0,a=n):e.h<360&&(i=n,o=0,a=s),new Nl(i+r,o+r,a+r,t)}(function(e,t,n){return isNaN(e)||e<=0?t:e>=1?n:new zl(Al(e,t.h,n.h),$l(e,t.s,n.s),$l(e,t.v,n.v))}(e,Jl(n),Jl(s)));case hc.XYZ:return Ql(function(e,t,n){return isNaN(e)||e<=0?t:e>=1?n:new Ul($l(e,t.x,n.x),$l(e,t.y,n.y),$l(e,t.z,n.z))}(e,Xl(n),Xl(s)));case hc.LAB:return tc(function(e,t,n){return isNaN(e)||e<=0?t:e>=1?n:new Pl($l(e,t.l,n.l),$l(e,t.a,n.a),$l(e,t.b,n.b))}(e,ec(n),ec(s)));case hc.LCH:return sc(function(e,t,n){return isNaN(e)||e<=0?t:e>=1?n:new Hl($l(e,t.l,n.l),$l(e,t.c,n.c),Al(e,t.h,n.h))}(e,nc(n),nc(s)));default:return function(e,t,n){return isNaN(e)||e<=0?t:e>=1?n:new Nl($l(e,t.r,n.r),$l(e,t.g,n.g),$l(e,t.b,n.b),$l(e,t.a,n.a))}(e,n,s)}}!function(e){e[e.Burn=0]="Burn",e[e.Color=1]="Color",e[e.Darken=2]="Darken",e[e.Dodge=3]="Dodge",e[e.Lighten=4]="Lighten",e[e.Multiply=5]="Multiply",e[e.Overlay=6]="Overlay",e[e.Screen=7]="Screen"}(cc||(cc={})),function(e){e[e.RGB=0]="RGB",e[e.HSL=1]="HSL",e[e.HSV=2]="HSV",e[e.XYZ=3]="XYZ",e[e.LAB=4]="LAB",e[e.LCH=5]="LCH"}(hc||(hc={}));class uc{constructor(e){if(null==e||0===e.length)throw new Error("The stops argument must be non-empty");this.stops=this.sortColorScaleStops(e)}static createBalancedColorScale(e){if(null==e||0===e.length)throw new Error("The colors argument must be non-empty");const t=new Array(e.length);for(let n=0;n<e.length;n++)0===n?t[n]={color:e[n],position:0}:n===e.length-1?t[n]={color:e[n],position:1}:t[n]={color:e[n],position:n*(1/(e.length-1))};return new uc(t)}getColor(e,t=hc.RGB){if(1===this.stops.length)return this.stops[0].color;if(e<=0)return this.stops[0].color;if(e>=1)return this.stops[this.stops.length-1].color;let n=0;for(let t=0;t<this.stops.length;t++)this.stops[t].position<=e&&(n=t);let s=n+1;return s>=this.stops.length&&(s=this.stops.length-1),dc((e-this.stops[n].position)*(1/(this.stops[s].position-this.stops[n].position)),t,this.stops[n].color,this.stops[s].color)}trim(e,t,n=hc.RGB){if(e<0||t>1||t<e)throw new Error("Invalid bounds");if(e===t)return new uc([{color:this.getColor(e,n),position:0}]);const s=[];for(let n=0;n<this.stops.length;n++)this.stops[n].position>=e&&this.stops[n].position<=t&&s.push(this.stops[n]);if(0===s.length)return new uc([{color:this.getColor(e),position:e},{color:this.getColor(t),position:t}]);s[0].position!==e&&s.unshift({color:this.getColor(e),position:e}),s[s.length-1].position!==t&&s.push({color:this.getColor(t),position:t});const r=t-e,i=new Array(s.length);for(let t=0;t<s.length;t++)i[t]={color:s[t].color,position:(s[t].position-e)/r};return new uc(i)}findNextColor(e,t,n=!1,s=hc.RGB,r=.005,i=32){isNaN(e)||e<=0?e=0:e>=1&&(e=1);const o=this.getColor(e,s),a=n?0:1;if(Zl(o,this.getColor(a,s))<=t)return a;let l=n?0:e,c=n?e:0,h=a,d=0;for(;d<=i;){h=Math.abs(c-l)/2+l;const e=Zl(o,this.getColor(h,s));if(Math.abs(e-t)<=r)return h;e>t?n?l=h:c=h:n?c=h:l=h,d++}return h}clone(){const e=new Array(this.stops.length);for(let t=0;t<e.length;t++)e[t]={color:this.stops[t].color,position:this.stops[t].position};return new uc(e)}sortColorScaleStops(e){return e.sort(((e,t)=>{const n=e.position,s=t.position;return n<s?-1:n>s?1:0}))}}const pc={aliceblue:{r:.941176,g:.972549,b:1},antiquewhite:{r:.980392,g:.921569,b:.843137},aqua:{r:0,g:1,b:1},aquamarine:{r:.498039,g:1,b:.831373},azure:{r:.941176,g:1,b:1},beige:{r:.960784,g:.960784,b:.862745},bisque:{r:1,g:.894118,b:.768627},black:{r:0,g:0,b:0},blanchedalmond:{r:1,g:.921569,b:.803922},blue:{r:0,g:0,b:1},blueviolet:{r:.541176,g:.168627,b:.886275},brown:{r:.647059,g:.164706,b:.164706},burlywood:{r:.870588,g:.721569,b:.529412},cadetblue:{r:.372549,g:.619608,b:.627451},chartreuse:{r:.498039,g:1,b:0},chocolate:{r:.823529,g:.411765,b:.117647},coral:{r:1,g:.498039,b:.313725},cornflowerblue:{r:.392157,g:.584314,b:.929412},cornsilk:{r:1,g:.972549,b:.862745},crimson:{r:.862745,g:.078431,b:.235294},cyan:{r:0,g:1,b:1},darkblue:{r:0,g:0,b:.545098},darkcyan:{r:0,g:.545098,b:.545098},darkgoldenrod:{r:.721569,g:.52549,b:.043137},darkgray:{r:.662745,g:.662745,b:.662745},darkgreen:{r:0,g:.392157,b:0},darkgrey:{r:.662745,g:.662745,b:.662745},darkkhaki:{r:.741176,g:.717647,b:.419608},darkmagenta:{r:.545098,g:0,b:.545098},darkolivegreen:{r:.333333,g:.419608,b:.184314},darkorange:{r:1,g:.54902,b:0},darkorchid:{r:.6,g:.196078,b:.8},darkred:{r:.545098,g:0,b:0},darksalmon:{r:.913725,g:.588235,b:.478431},darkseagreen:{r:.560784,g:.737255,b:.560784},darkslateblue:{r:.282353,g:.239216,b:.545098},darkslategray:{r:.184314,g:.309804,b:.309804},darkslategrey:{r:.184314,g:.309804,b:.309804},darkturquoise:{r:0,g:.807843,b:.819608},darkviolet:{r:.580392,g:0,b:.827451},deeppink:{r:1,g:.078431,b:.576471},deepskyblue:{r:0,g:.74902,b:1},dimgray:{r:.411765,g:.411765,b:.411765},dimgrey:{r:.411765,g:.411765,b:.411765},dodgerblue:{r:.117647,g:.564706,b:1},firebrick:{r:.698039,g:.133333,b:.133333},floralwhite:{r:1,g:.980392,b:.941176},forestgreen:{r:.133333,g:.545098,b:.133333},fuchsia:{r:1,g:0,b:1},gainsboro:{r:.862745,g:.862745,b:.862745},ghostwhite:{r:.972549,g:.972549,b:1},gold:{r:1,g:.843137,b:0},goldenrod:{r:.854902,g:.647059,b:.12549},gray:{r:.501961,g:.501961,b:.501961},green:{r:0,g:.501961,b:0},greenyellow:{r:.678431,g:1,b:.184314},grey:{r:.501961,g:.501961,b:.501961},honeydew:{r:.941176,g:1,b:.941176},hotpink:{r:1,g:.411765,b:.705882},indianred:{r:.803922,g:.360784,b:.360784},indigo:{r:.294118,g:0,b:.509804},ivory:{r:1,g:1,b:.941176},khaki:{r:.941176,g:.901961,b:.54902},lavender:{r:.901961,g:.901961,b:.980392},lavenderblush:{r:1,g:.941176,b:.960784},lawngreen:{r:.486275,g:.988235,b:0},lemonchiffon:{r:1,g:.980392,b:.803922},lightblue:{r:.678431,g:.847059,b:.901961},lightcoral:{r:.941176,g:.501961,b:.501961},lightcyan:{r:.878431,g:1,b:1},lightgoldenrodyellow:{r:.980392,g:.980392,b:.823529},lightgray:{r:.827451,g:.827451,b:.827451},lightgreen:{r:.564706,g:.933333,b:.564706},lightgrey:{r:.827451,g:.827451,b:.827451},lightpink:{r:1,g:.713725,b:.756863},lightsalmon:{r:1,g:.627451,b:.478431},lightseagreen:{r:.12549,g:.698039,b:.666667},lightskyblue:{r:.529412,g:.807843,b:.980392},lightslategray:{r:.466667,g:.533333,b:.6},lightslategrey:{r:.466667,g:.533333,b:.6},lightsteelblue:{r:.690196,g:.768627,b:.870588},lightyellow:{r:1,g:1,b:.878431},lime:{r:0,g:1,b:0},limegreen:{r:.196078,g:.803922,b:.196078},linen:{r:.980392,g:.941176,b:.901961},magenta:{r:1,g:0,b:1},maroon:{r:.501961,g:0,b:0},mediumaquamarine:{r:.4,g:.803922,b:.666667},mediumblue:{r:0,g:0,b:.803922},mediumorchid:{r:.729412,g:.333333,b:.827451},mediumpurple:{r:.576471,g:.439216,b:.858824},mediumseagreen:{r:.235294,g:.701961,b:.443137},mediumslateblue:{r:.482353,g:.407843,b:.933333},mediumspringgreen:{r:0,g:.980392,b:.603922},mediumturquoise:{r:.282353,g:.819608,b:.8},mediumvioletred:{r:.780392,g:.082353,b:.521569},midnightblue:{r:.098039,g:.098039,b:.439216},mintcream:{r:.960784,g:1,b:.980392},mistyrose:{r:1,g:.894118,b:.882353},moccasin:{r:1,g:.894118,b:.709804},navajowhite:{r:1,g:.870588,b:.678431},navy:{r:0,g:0,b:.501961},oldlace:{r:.992157,g:.960784,b:.901961},olive:{r:.501961,g:.501961,b:0},olivedrab:{r:.419608,g:.556863,b:.137255},orange:{r:1,g:.647059,b:0},orangered:{r:1,g:.270588,b:0},orchid:{r:.854902,g:.439216,b:.839216},palegoldenrod:{r:.933333,g:.909804,b:.666667},palegreen:{r:.596078,g:.984314,b:.596078},paleturquoise:{r:.686275,g:.933333,b:.933333},palevioletred:{r:.858824,g:.439216,b:.576471},papayawhip:{r:1,g:.937255,b:.835294},peachpuff:{r:1,g:.854902,b:.72549},peru:{r:.803922,g:.521569,b:.247059},pink:{r:1,g:.752941,b:.796078},plum:{r:.866667,g:.627451,b:.866667},powderblue:{r:.690196,g:.878431,b:.901961},purple:{r:.501961,g:0,b:.501961},red:{r:1,g:0,b:0},rosybrown:{r:.737255,g:.560784,b:.560784},royalblue:{r:.254902,g:.411765,b:.882353},saddlebrown:{r:.545098,g:.270588,b:.07451},salmon:{r:.980392,g:.501961,b:.447059},sandybrown:{r:.956863,g:.643137,b:.376471},seagreen:{r:.180392,g:.545098,b:.341176},seashell:{r:1,g:.960784,b:.933333},sienna:{r:.627451,g:.321569,b:.176471},silver:{r:.752941,g:.752941,b:.752941},skyblue:{r:.529412,g:.807843,b:.921569},slateblue:{r:.415686,g:.352941,b:.803922},slategray:{r:.439216,g:.501961,b:.564706},slategrey:{r:.439216,g:.501961,b:.564706},snow:{r:1,g:.980392,b:.980392},springgreen:{r:0,g:1,b:.498039},steelblue:{r:.27451,g:.509804,b:.705882},tan:{r:.823529,g:.705882,b:.54902},teal:{r:0,g:.501961,b:.501961},thistle:{r:.847059,g:.74902,b:.847059},tomato:{r:1,g:.388235,b:.278431},transparent:{r:0,g:0,b:0,a:0},turquoise:{r:.25098,g:.878431,b:.815686},violet:{r:.933333,g:.509804,b:.933333},wheat:{r:.960784,g:.870588,b:.701961},white:{r:1,g:1,b:1},whitesmoke:{r:.960784,g:.960784,b:.960784},yellow:{r:1,g:1,b:0},yellowgreen:{r:.603922,g:.803922,b:.196078}},gc=/^rgb\(\s*((?:(?:25[0-5]|2[0-4]\d|1\d\d|\d{1,2})\s*,\s*){2}(?:25[0-5]|2[0-4]\d|1\d\d|\d{1,2})\s*)\)$/i,mc=/^rgba\(\s*((?:(?:25[0-5]|2[0-4]\d|1\d\d|\d{1,2})\s*,\s*){3}(?:0|1|0?\.\d*)\s*)\)$/i,fc=/^#((?:[0-9a-f]{6}|[0-9a-f]{3}))$/i,vc=/^#((?:[0-9a-f]{8}|[0-9a-f]{4}))$/i;function wc(e){const t=fc.exec(e);if(null===t)return null;let n=t[1];if(3===n.length){const e=n.charAt(0),t=n.charAt(1),s=n.charAt(2);n=e.concat(e,t,t,s,s)}const s=parseInt(n,16);return isNaN(s)?null:new Nl(Dl((16711680&s)>>>16,0,255),Dl((65280&s)>>>8,0,255),Dl(255&s,0,255),1)}function bc(e){const t=e.toLowerCase();return function(e){return fc.test(e)}(t)?wc(t):function(e){return function(e){return vc.test(e)}(e)}(t)?function(e){const t=vc.exec(e);if(null===t)return null;let n=t[1];if(4===n.length){const e=n.charAt(0),t=n.charAt(1),s=n.charAt(2),r=n.charAt(3);n=e.concat(e,t,t,s,s,r,r)}const s=parseInt(n,16);return isNaN(s)?null:new Nl(Dl((16711680&s)>>>16,0,255),Dl((65280&s)>>>8,0,255),Dl(255&s,0,255),Dl((4278190080&s)>>>24,0,255))}(t):function(e){return gc.test(e)}(t)?function(e){const t=gc.exec(e);if(null===t)return null;const n=t[1].split(",");return new Nl(Dl(Number(n[0]),0,255),Dl(Number(n[1]),0,255),Dl(Number(n[2]),0,255),1)}(t):function(e){return mc.test(e)}(t)?function(e){const t=mc.exec(e);if(null===t)return null;const n=t[1].split(",");return 4===n.length?new Nl(Dl(Number(n[0]),0,255),Dl(Number(n[1]),0,255),Dl(Number(n[2]),0,255),Number(n[3])):null}(t):function(e){return pc.hasOwnProperty(e)}(t)?function(e){const t=pc[e.toLowerCase()];return t?new Nl(t.r,t.g,t.b,t.hasOwnProperty("a")?t.a:void 0):null}(t):null}class yc{constructor(e){this.config=Object.assign({},yc.defaultPaletteConfig,e),this.palette=[],this.updatePaletteColors()}updatePaletteGenerationValues(e){let t=!1;for(const n in e)this.config[n]&&(this.config[n].equalValue?this.config[n].equalValue(e[n])||(this.config[n]=e[n],t=!0):e[n]!==this.config[n]&&(this.config[n]=e[n],t=!0));return t&&this.updatePaletteColors(),t}updatePaletteColors(){const e=this.generatePaletteColorScale();for(let t=0;t<this.config.steps;t++)this.palette[t]=e.getColor(t/(this.config.steps-1),this.config.interpolationMode)}generatePaletteColorScale(){const e=Kl(this.config.baseColor),t=new uc([{position:0,color:this.config.scaleColorLight},{position:.5,color:this.config.baseColor},{position:1,color:this.config.scaleColorDark}]).trim(this.config.clipLight,1-this.config.clipDark);let n=t.getColor(0),s=t.getColor(1);if(e.s>=this.config.saturationAdjustmentCutoff&&(n=rc(n,this.config.saturationLight),s=rc(s,this.config.saturationDark)),0!==this.config.multiplyLight){const e=oc(this.config.baseColor,n);n=dc(this.config.multiplyLight,this.config.interpolationMode,n,e)}if(0!==this.config.multiplyDark){const e=oc(this.config.baseColor,s);s=dc(this.config.multiplyDark,this.config.interpolationMode,s,e)}if(0!==this.config.overlayLight){const e=lc(this.config.baseColor,n);n=dc(this.config.overlayLight,this.config.interpolationMode,n,e)}if(0!==this.config.overlayDark){const e=lc(this.config.baseColor,s);s=dc(this.config.overlayDark,this.config.interpolationMode,s,e)}return this.config.baseScalePosition?this.config.baseScalePosition<=0?new uc([{position:0,color:this.config.baseColor},{position:1,color:s.clamp()}]):this.config.baseScalePosition>=1?new uc([{position:0,color:n.clamp()},{position:1,color:this.config.baseColor}]):new uc([{position:0,color:n.clamp()},{position:this.config.baseScalePosition,color:this.config.baseColor},{position:1,color:s.clamp()}]):new uc([{position:0,color:n.clamp()},{position:.5,color:this.config.baseColor},{position:1,color:s.clamp()}])}}yc.defaultPaletteConfig={baseColor:wc("#808080"),steps:11,interpolationMode:hc.RGB,scaleColorLight:new Nl(1,1,1,1),scaleColorDark:new Nl(0,0,0,1),clipLight:.185,clipDark:.16,saturationAdjustmentCutoff:.05,saturationLight:.35,saturationDark:1.25,overlayLight:0,overlayDark:.25,multiplyLight:0,multiplyDark:0,baseScalePosition:.5},yc.greyscalePaletteConfig={baseColor:wc("#808080"),steps:11,interpolationMode:hc.RGB,scaleColorLight:new Nl(1,1,1,1),scaleColorDark:new Nl(0,0,0,1),clipLight:0,clipDark:0,saturationAdjustmentCutoff:0,saturationLight:0,saturationDark:0,overlayLight:0,overlayDark:0,multiplyLight:0,multiplyDark:0,baseScalePosition:.5},yc.defaultPaletteConfig.scaleColorLight,yc.defaultPaletteConfig.scaleColorDark;class xc{constructor(e){this.palette=[],this.config=Object.assign({},xc.defaultPaletteConfig,e),this.regenPalettes()}regenPalettes(){let e=this.config.steps;(isNaN(e)||e<3)&&(e=3);const t=.14,n=new Nl(t,t,t,1),s=new yc(Object.assign(Object.assign({},yc.greyscalePaletteConfig),{baseColor:n,baseScalePosition:86/94,steps:e})).palette,r=(Wl(this.config.baseColor)+Kl(this.config.baseColor).l)/2,i=this.matchRelativeLuminanceIndex(r,s)/(e-1),o=this.matchRelativeLuminanceIndex(t,s)/(e-1),a=Kl(this.config.baseColor),l=Yl(Rl.fromObject({h:a.h,s:a.s,l:t})),c=Yl(Rl.fromObject({h:a.h,s:a.s,l:.06})),h=new Array(5);h[0]={position:0,color:new Nl(1,1,1,1)},h[1]={position:i,color:this.config.baseColor},h[2]={position:o,color:l},h[3]={position:.99,color:c},h[4]={position:1,color:new Nl(0,0,0,1)};const d=new uc(h);this.palette=new Array(e);for(let t=0;t<e;t++){const n=d.getColor(t/(e-1),hc.RGB);this.palette[t]=n}}matchRelativeLuminanceIndex(e,t){let n=Number.MAX_VALUE,s=0,r=0;const i=t.length;for(;r<i;r++){const i=Math.abs(Wl(t[r])-e);i<n&&(n=i,s=r)}return s}}function Cc(e,t){const n=e.relativeLuminance>t.relativeLuminance?e:t,s=e.relativeLuminance>t.relativeLuminance?t:e;return(n.relativeLuminance+.05)/(s.relativeLuminance+.05)}xc.defaultPaletteConfig={baseColor:wc("#808080"),steps:94};const Sc=Object.freeze({create:(e,t,n)=>new _c(e,t,n),from:e=>new _c(e.r,e.g,e.b)});class _c extends Nl{constructor(e,t,n){super(e,t,n,1),this.toColorString=this.toStringHexRGB,this.contrast=Cc.bind(null,this),this.createCSS=this.toColorString,this.relativeLuminance=ql(this)}static fromObject(e){return new _c(e.r,e.g,e.b)}}function kc(e,t,n=0,s=e.length-1){if(s===n)return e[n];const r=Math.floor((s-n)/2)+n;return t(e[r])?kc(e,t,n,r):kc(e,t,r+1,s)}const Fc=(-.1+Math.sqrt(.21))/2;function jc(e){return e.relativeLuminance<=Fc}function Mc(e){return jc(e)?-1:1}const Ic=Object.freeze({create:function(e,t,n){return"number"==typeof e?Ic.from(Sc.create(e,t,n)):Ic.from(e)},from:function(e){return function(e){const t={r:0,g:0,b:0,toColorString:()=>"",contrast:()=>0,relativeLuminance:0};for(const n in t)if(typeof t[n]!=typeof e[n])return!1;return!0}(e)?Tc.from(e):Tc.from(Sc.create(e.r,e.g,e.b))}});class Tc{constructor(e,t){this.closestIndexCache=new Map,this.source=e,this.swatches=t,this.reversedSwatches=Object.freeze([...this.swatches].reverse()),this.lastIndex=this.swatches.length-1}colorContrast(e,t,n,s){void 0===n&&(n=this.closestIndexOf(e));let r=this.swatches;const i=this.lastIndex;let o=n;return void 0===s&&(s=Mc(e)),-1===s&&(r=this.reversedSwatches,o=i-o),kc(r,(n=>Cc(e,n)>=t),o,i)}get(e){return this.swatches[e]||this.swatches[Vl(e,0,this.lastIndex)]}closestIndexOf(e){if(this.closestIndexCache.has(e.relativeLuminance))return this.closestIndexCache.get(e.relativeLuminance);let t=this.swatches.indexOf(e);if(-1!==t)return this.closestIndexCache.set(e.relativeLuminance,t),t;const n=this.swatches.reduce(((t,n)=>Math.abs(n.relativeLuminance-e.relativeLuminance)<Math.abs(t.relativeLuminance-e.relativeLuminance)?n:t));return t=this.swatches.indexOf(n),this.closestIndexCache.set(e.relativeLuminance,t),t}static from(e){return new Tc(e,Object.freeze(new xc({baseColor:Nl.fromObject(e)}).palette.map((e=>{const t=wc(e.toStringHexRGB());return Sc.create(t.r,t.g,t.b)}))))}}const Lc=Sc.create(1,1,1),Vc=Sc.create(0,0,0),Dc=Sc.from(wc("#808080")),Ec=Sc.from(wc("#DA1A5F")),Oc=Sc.from(wc("#D32F2F"));function $c(e){return Sc.create(e,e,e)}const Ac=1,Bc=.23;function Nc(e,t,n,s,r,i){return Math.max(e.closestIndexOf($c(t))+n,s,r,i)}const{create:Rc}=ba;function zc(e){return ba.create({name:e,cssCustomPropertyName:null})}const Pc=Rc("body-font").withDefault('aktiv-grotesk, "Segoe UI", Arial, Helvetica, sans-serif'),Hc=Rc("base-height-multiplier").withDefault(10),Uc=(Rc("base-horizontal-spacing-multiplier").withDefault(3),Rc("base-layer-luminance").withDefault(Bc)),Wc=Rc("control-corner-radius").withDefault(4),qc=Rc("density").withDefault(0),Gc=Rc("design-unit").withDefault(4),Zc=(Rc("direction").withDefault(Ll.ltr),Rc("disabled-opacity").withDefault(.4)),Kc=Rc("stroke-width").withDefault(1),Yc=Rc("focus-stroke-width").withDefault(2),Jc=Rc("type-ramp-base-font-size").withDefault("14px"),Xc=Rc("type-ramp-base-line-height").withDefault("20px"),Qc=(Rc("type-ramp-minus-1-font-size").withDefault("12px"),Rc("type-ramp-minus-1-line-height").withDefault("16px"),Rc("type-ramp-minus-2-font-size").withDefault("10px"),Rc("type-ramp-minus-2-line-height").withDefault("16px"),Rc("type-ramp-plus-1-font-size").withDefault("16px"),Rc("type-ramp-plus-1-line-height").withDefault("24px"),Rc("type-ramp-plus-2-font-size").withDefault("20px"),Rc("type-ramp-plus-2-line-height").withDefault("28px"),Rc("type-ramp-plus-3-font-size").withDefault("28px"),Rc("type-ramp-plus-3-line-height").withDefault("36px"),Rc("type-ramp-plus-4-font-size").withDefault("34px"),Rc("type-ramp-plus-4-line-height").withDefault("44px"),Rc("type-ramp-plus-5-font-size").withDefault("46px"),Rc("type-ramp-plus-5-line-height").withDefault("56px"),Rc("type-ramp-plus-6-font-size").withDefault("60px"),Rc("type-ramp-plus-6-line-height").withDefault("72px"),zc("accent-fill-rest-delta").withDefault(0),zc("accent-fill-hover-delta").withDefault(4)),eh=zc("accent-fill-active-delta").withDefault(-5),th=zc("accent-fill-focus-delta").withDefault(0),nh=zc("accent-foreground-rest-delta").withDefault(0),sh=zc("accent-foreground-hover-delta").withDefault(6),rh=zc("accent-foreground-active-delta").withDefault(-4),ih=zc("accent-foreground-focus-delta").withDefault(0),oh=zc("neutral-fill-rest-delta").withDefault(7),ah=zc("neutral-fill-hover-delta").withDefault(10),lh=zc("neutral-fill-active-delta").withDefault(5),ch=zc("neutral-fill-focus-delta").withDefault(0),hh=zc("neutral-fill-input-rest-delta").withDefault(0),dh=zc("neutral-fill-input-hover-delta").withDefault(0),uh=zc("neutral-fill-input-active-delta").withDefault(0),ph=zc("neutral-fill-input-focus-delta").withDefault(0),gh=zc("neutral-fill-stealth-rest-delta").withDefault(0),mh=zc("neutral-fill-stealth-hover-delta").withDefault(5),fh=zc("neutral-fill-stealth-active-delta").withDefault(3),vh=zc("neutral-fill-stealth-focus-delta").withDefault(0),wh=zc("neutral-fill-strong-rest-delta").withDefault(0),bh=zc("neutral-fill-strong-hover-delta").withDefault(8),yh=zc("neutral-fill-strong-active-delta").withDefault(-5),xh=zc("neutral-fill-strong-focus-delta").withDefault(0),Ch=zc("neutral-fill-layer-rest-delta").withDefault(3),Sh=zc("neutral-stroke-rest-delta").withDefault(25),_h=zc("neutral-stroke-hover-delta").withDefault(40),kh=zc("neutral-stroke-active-delta").withDefault(16),Fh=zc("neutral-stroke-focus-delta").withDefault(25),jh=zc("neutral-stroke-divider-rest-delta").withDefault(8),Mh=Rc("neutral-color").withDefault(Dc),Ih=zc("neutral-palette").withDefault((e=>Ic.from(Mh.getValueFor(e)))),Th=Rc("accent-color").withDefault(Ec),Lh=zc("accent-palette").withDefault((e=>Ic.from(Th.getValueFor(e)))),Vh=zc("neutral-layer-card-container-recipe").withDefault({evaluate:e=>{return t=Ih.getValueFor(e),n=Uc.getValueFor(e),s=Ch.getValueFor(e),t.get(t.closestIndexOf($c(n))+s);var t,n,s}}),Dh=(Rc("neutral-layer-card-container").withDefault((e=>Vh.getValueFor(e).evaluate(e))),zc("neutral-layer-floating-recipe").withDefault({evaluate:e=>function(e,t,n){const s=e.closestIndexOf($c(t))-n;return e.get(s-n)}(Ih.getValueFor(e),Uc.getValueFor(e),Ch.getValueFor(e))})),Eh=(Rc("neutral-layer-floating").withDefault((e=>Dh.getValueFor(e).evaluate(e))),zc("neutral-layer-1-recipe").withDefault({evaluate:e=>function(e,t){return e.get(e.closestIndexOf($c(t)))}(Ih.getValueFor(e),Uc.getValueFor(e))})),Oh=Rc("neutral-layer-1").withDefault((e=>Eh.getValueFor(e).evaluate(e))),$h=zc("neutral-layer-2-recipe").withDefault({evaluate:e=>{return t=Ih.getValueFor(e),n=Uc.getValueFor(e),s=Ch.getValueFor(e),r=oh.getValueFor(e),i=ah.getValueFor(e),o=lh.getValueFor(e),t.get(Nc(t,n,s,r,i,o));var t,n,s,r,i,o}}),Ah=(Rc("neutral-layer-2").withDefault((e=>$h.getValueFor(e).evaluate(e))),zc("neutral-layer-3-recipe").withDefault({evaluate:e=>{return t=Ih.getValueFor(e),n=Uc.getValueFor(e),s=Ch.getValueFor(e),r=oh.getValueFor(e),i=ah.getValueFor(e),o=lh.getValueFor(e),t.get(Nc(t,n,s,r,i,o)+s);var t,n,s,r,i,o}})),Bh=(Rc("neutral-layer-3").withDefault((e=>Ah.getValueFor(e).evaluate(e))),zc("neutral-layer-4-recipe").withDefault({evaluate:e=>{return t=Ih.getValueFor(e),n=Uc.getValueFor(e),s=Ch.getValueFor(e),r=oh.getValueFor(e),i=ah.getValueFor(e),o=lh.getValueFor(e),t.get(Nc(t,n,s,r,i,o)+2*s);var t,n,s,r,i,o}})),Nh=(Rc("neutral-layer-4").withDefault((e=>Bh.getValueFor(e).evaluate(e))),Rc("fill-color").withDefault((e=>Oh.getValueFor(e))));var Rh;!function(e){e[e.normal=4.5]="normal",e[e.large=7]="large"}(Rh||(Rh={}));const zh=Rc({name:"accent-fill-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>function(e,t,n,s,r,i,o,a,l){const c=e.source,h=t.closestIndexOf(n)>=Math.max(o,a,l)?-1:1,d=e.closestIndexOf(c),u=d+-1*h*s,p=u+h*r,g=u+h*i;return{rest:e.get(u),hover:e.get(d),active:e.get(p),focus:e.get(g)}}(Lh.getValueFor(e),Ih.getValueFor(e),t||Nh.getValueFor(e),Qc.getValueFor(e),eh.getValueFor(e),th.getValueFor(e),oh.getValueFor(e),ah.getValueFor(e),lh.getValueFor(e))}),Ph=Rc("accent-fill-rest").withDefault((e=>zh.getValueFor(e).evaluate(e).rest)),Hh=Rc("accent-fill-hover").withDefault((e=>zh.getValueFor(e).evaluate(e).hover)),Uh=Rc("accent-fill-active").withDefault((e=>zh.getValueFor(e).evaluate(e).active)),Wh=Rc("accent-fill-focus").withDefault((e=>zh.getValueFor(e).evaluate(e).focus)),qh=e=>(t,n)=>function(e,t){return e.contrast(Lc)>=t?Lc:Vc}(n||Ph.getValueFor(t),e),Gh=zc("foreground-on-accent-recipe").withDefault({evaluate:(e,t)=>qh(Rh.normal)(e,t)}),Zh=Rc("foreground-on-accent-rest").withDefault((e=>Gh.getValueFor(e).evaluate(e,Ph.getValueFor(e)))),Kh=Rc("foreground-on-accent-hover").withDefault((e=>Gh.getValueFor(e).evaluate(e,Hh.getValueFor(e)))),Yh=Rc("foreground-on-accent-active").withDefault((e=>Gh.getValueFor(e).evaluate(e,Uh.getValueFor(e)))),Jh=(Rc("foreground-on-accent-focus").withDefault((e=>Gh.getValueFor(e).evaluate(e,Wh.getValueFor(e)))),zc("foreground-on-accent-large-recipe").withDefault({evaluate:(e,t)=>qh(Rh.large)(e,t)})),Xh=(Rc("foreground-on-accent-rest-large").withDefault((e=>Jh.getValueFor(e).evaluate(e,Ph.getValueFor(e)))),Rc("foreground-on-accent-hover-large").withDefault((e=>Jh.getValueFor(e).evaluate(e,Hh.getValueFor(e)))),Rc("foreground-on-accent-active-large").withDefault((e=>Jh.getValueFor(e).evaluate(e,Uh.getValueFor(e)))),Rc("foreground-on-accent-focus-large").withDefault((e=>Jh.getValueFor(e).evaluate(e,Wh.getValueFor(e)))),e=>(t,n)=>function(e,t,n,s,r,i,o){const a=e.source,l=e.closestIndexOf(a),c=Mc(t),h=l+(1===c?Math.min(s,r):Math.max(c*s,c*r)),d=e.colorContrast(t,n,h,c),u=e.closestIndexOf(d),p=u+c*Math.abs(s-r);let g,m;return(1===c?s<r:c*s>c*r)?(g=u,m=p):(g=p,m=u),{rest:e.get(g),hover:e.get(m),active:e.get(g+c*i),focus:e.get(g+c*o)}}(Lh.getValueFor(t),n||Nh.getValueFor(t),e,nh.getValueFor(t),sh.getValueFor(t),rh.getValueFor(t),ih.getValueFor(t))),Qh=Rc({name:"accent-foreground-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>Xh(Rh.normal)(e,t)}),ed=Rc("accent-foreground-rest").withDefault((e=>Qh.getValueFor(e).evaluate(e).rest)),td=Rc("accent-foreground-hover").withDefault((e=>Qh.getValueFor(e).evaluate(e).hover)),nd=Rc("accent-foreground-active").withDefault((e=>Qh.getValueFor(e).evaluate(e).active)),sd=(Rc("accent-foreground-focus").withDefault((e=>Qh.getValueFor(e).evaluate(e).focus)),Rc({name:"neutral-fill-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>function(e,t,n,s,r,i){const o=e.closestIndexOf(t),a=o>=Math.max(n,s,r,i)?-1:1;return{rest:e.get(o+a*n),hover:e.get(o+a*s),active:e.get(o+a*r),focus:e.get(o+a*i)}}(Ih.getValueFor(e),t||Nh.getValueFor(e),oh.getValueFor(e),ah.getValueFor(e),lh.getValueFor(e),ch.getValueFor(e))})),rd=Rc("neutral-fill-rest").withDefault((e=>sd.getValueFor(e).evaluate(e).rest)),id=Rc("neutral-fill-hover").withDefault((e=>sd.getValueFor(e).evaluate(e).hover)),od=Rc("neutral-fill-active").withDefault((e=>sd.getValueFor(e).evaluate(e).active)),ad=(Rc("neutral-fill-focus").withDefault((e=>sd.getValueFor(e).evaluate(e).focus)),Rc({name:"neutral-fill-input-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>function(e,t,n,s,r,i){const o=Mc(t),a=e.closestIndexOf(t);return{rest:e.get(a-o*n),hover:e.get(a-o*s),active:e.get(a-o*r),focus:e.get(a-o*i)}}(Ih.getValueFor(e),t||Nh.getValueFor(e),hh.getValueFor(e),dh.getValueFor(e),uh.getValueFor(e),ph.getValueFor(e))})),ld=(Rc("neutral-fill-input-rest").withDefault((e=>ad.getValueFor(e).evaluate(e).rest)),Rc("neutral-fill-input-hover").withDefault((e=>ad.getValueFor(e).evaluate(e).hover)),Rc("neutral-fill-input-active").withDefault((e=>ad.getValueFor(e).evaluate(e).active)),Rc("neutral-fill-input-focus").withDefault((e=>ad.getValueFor(e).evaluate(e).focus)),Rc({name:"neutral-fill-stealth-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>function(e,t,n,s,r,i,o,a,l,c){const h=Math.max(n,s,r,i,o,a,l,c),d=e.closestIndexOf(t),u=d>=h?-1:1;return{rest:e.get(d+u*n),hover:e.get(d+u*s),active:e.get(d+u*r),focus:e.get(d+u*i)}}(Ih.getValueFor(e),t||Nh.getValueFor(e),gh.getValueFor(e),mh.getValueFor(e),fh.getValueFor(e),vh.getValueFor(e),oh.getValueFor(e),ah.getValueFor(e),lh.getValueFor(e),ch.getValueFor(e))})),cd=(Rc("neutral-fill-stealth-rest").withDefault((e=>ld.getValueFor(e).evaluate(e).rest)),Rc("neutral-fill-stealth-hover").withDefault((e=>ld.getValueFor(e).evaluate(e).hover))),hd=Rc("neutral-fill-stealth-active").withDefault((e=>ld.getValueFor(e).evaluate(e).active)),dd=(Rc("neutral-fill-stealth-focus").withDefault((e=>ld.getValueFor(e).evaluate(e).focus)),Rc({name:"neutral-fill-strong-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>function(e,t,n,s,r,i){const o=Mc(t),a=e.closestIndexOf(e.colorContrast(t,4.5)),l=a+o*Math.abs(n-s);let c,h;return(1===o?n<s:o*n>o*s)?(c=a,h=l):(c=l,h=a),{rest:e.get(c),hover:e.get(h),active:e.get(c+o*r),focus:e.get(c+o*i)}}(Ih.getValueFor(e),t||Nh.getValueFor(e),wh.getValueFor(e),bh.getValueFor(e),yh.getValueFor(e),xh.getValueFor(e))})),ud=(Rc("neutral-fill-strong-rest").withDefault((e=>dd.getValueFor(e).evaluate(e).rest)),Rc("neutral-fill-strong-hover").withDefault((e=>dd.getValueFor(e).evaluate(e).hover)),Rc("neutral-fill-strong-active").withDefault((e=>dd.getValueFor(e).evaluate(e).active))),pd=Rc("neutral-fill-strong-focus").withDefault((e=>dd.getValueFor(e).evaluate(e).focus)),gd=zc("neutral-fill-layer-recipe").withDefault({evaluate:(e,t)=>function(e,t,n){const s=e.closestIndexOf(t);return e.get(s-(s<n?-1*n:n))}(Ih.getValueFor(e),t||Nh.getValueFor(e),Ch.getValueFor(e))}),md=(Rc("neutral-fill-layer-rest").withDefault((e=>gd.getValueFor(e).evaluate(e))),zc("focus-stroke-outer-recipe").withDefault({evaluate:e=>{return t=Ih.getValueFor(e),n=Nh.getValueFor(e),t.colorContrast(n,3.5);var t,n}})),fd=Rc("focus-stroke-outer").withDefault((e=>md.getValueFor(e).evaluate(e))),vd=zc("focus-stroke-inner-recipe").withDefault({evaluate:e=>{return t=Lh.getValueFor(e),n=Nh.getValueFor(e),s=fd.getValueFor(e),t.colorContrast(s,3.5,t.closestIndexOf(t.source),-1*Mc(n));var t,n,s}}),wd=(Rc("focus-stroke-inner").withDefault((e=>vd.getValueFor(e).evaluate(e))),zc("neutral-foreground-hint-recipe").withDefault({evaluate:e=>{return t=Ih.getValueFor(e),n=Nh.getValueFor(e),t.colorContrast(n,4.5);var t,n}})),bd=(Rc("neutral-foreground-hint").withDefault((e=>wd.getValueFor(e).evaluate(e))),zc("neutral-foreground-recipe").withDefault({evaluate:e=>{return t=Ih.getValueFor(e),n=Nh.getValueFor(e),t.colorContrast(n,14);var t,n}})),yd=Rc("neutral-foreground-rest").withDefault((e=>bd.getValueFor(e).evaluate(e))),xd=Rc({name:"neutral-stroke-recipe",cssCustomPropertyName:null}).withDefault({evaluate:e=>function(e,t,n,s,r,i){const o=e.closestIndexOf(t),a=Mc(t),l=o+a*n,c=l+a*(s-n),h=l+a*(r-n),d=l+a*(i-n);return{rest:e.get(l),hover:e.get(c),active:e.get(h),focus:e.get(d)}}(Ih.getValueFor(e),Nh.getValueFor(e),Sh.getValueFor(e),_h.getValueFor(e),kh.getValueFor(e),Fh.getValueFor(e))}),Cd=(Rc("neutral-stroke-rest").withDefault((e=>xd.getValueFor(e).evaluate(e).rest)),Rc("neutral-stroke-hover").withDefault((e=>xd.getValueFor(e).evaluate(e).hover)),Rc("neutral-stroke-active").withDefault((e=>xd.getValueFor(e).evaluate(e).active)),Rc("neutral-stroke-focus").withDefault((e=>xd.getValueFor(e).evaluate(e).focus)),zc("neutral-stroke-divider-recipe").withDefault({evaluate:(e,t)=>function(e,t,n){return e.get(e.closestIndexOf(t)+Mc(t)*n)}(Ih.getValueFor(e),t||Nh.getValueFor(e),jh.getValueFor(e))})),Sd=(Rc("neutral-stroke-divider-rest").withDefault((e=>Cd.getValueFor(e).evaluate(e))),ba.create({name:"height-number",cssCustomPropertyName:null}).withDefault((e=>(Hc.getValueFor(e)+qc.getValueFor(e))*Gc.getValueFor(e))),Rc("error-color").withDefault(Oc)),_d=zc("error-palette").withDefault((e=>Ic.from(Sd.getValueFor(e)))),kd=Rc({name:"error-fill-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>function(e,t,n,s,r,i,o,a,l){const c=e.source,h=t.closestIndexOf(n)>=Math.max(o,a,l)?-1:1,d=e.closestIndexOf(c),u=d+-1*h*s,p=u+h*r,g=u+h*i;return{rest:e.get(u),hover:e.get(d),active:e.get(p),focus:e.get(g)}}(_d.getValueFor(e),Ih.getValueFor(e),t||Nh.getValueFor(e),Qc.getValueFor(e),eh.getValueFor(e),th.getValueFor(e),oh.getValueFor(e),ah.getValueFor(e),lh.getValueFor(e))}),Fd=Rc("error-fill-rest").withDefault((e=>kd.getValueFor(e).evaluate(e).rest)),jd=Rc("error-fill-hover").withDefault((e=>kd.getValueFor(e).evaluate(e).hover)),Md=Rc("error-fill-active").withDefault((e=>kd.getValueFor(e).evaluate(e).active)),Id=Rc("error-fill-focus").withDefault((e=>kd.getValueFor(e).evaluate(e).focus)),Td=e=>(t,n)=>function(e,t){return e.contrast(Lc)>=t?Lc:Vc}(n||Fd.getValueFor(t),e),Ld=Rc({name:"foreground-on-error-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>Td(Rh.normal)(e,t)}),Vd=(Rc("foreground-on-error-rest").withDefault((e=>Ld.getValueFor(e).evaluate(e,Fd.getValueFor(e)))),Rc("foreground-on-error-hover").withDefault((e=>Ld.getValueFor(e).evaluate(e,jd.getValueFor(e)))),Rc("foreground-on-error-active").withDefault((e=>Ld.getValueFor(e).evaluate(e,Md.getValueFor(e)))),Rc("foreground-on-error-focus").withDefault((e=>Ld.getValueFor(e).evaluate(e,Id.getValueFor(e)))),Rc({name:"foreground-on-error-large-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>Td(Rh.large)(e,t)})),Dd=(Rc("foreground-on-error-rest-large").withDefault((e=>Vd.getValueFor(e).evaluate(e,Fd.getValueFor(e)))),Rc("foreground-on-error-hover-large").withDefault((e=>Vd.getValueFor(e).evaluate(e,jd.getValueFor(e)))),Rc("foreground-on-error-active-large").withDefault((e=>Vd.getValueFor(e).evaluate(e,Md.getValueFor(e)))),Rc("foreground-on-error-focus-large").withDefault((e=>Vd.getValueFor(e).evaluate(e,Id.getValueFor(e)))),e=>(t,n)=>function(e,t,n,s,r,i,o){const a=e.source,l=e.closestIndexOf(a),c=jc(t)?-1:1,h=l+(1===c?Math.min(s,r):Math.max(c*s,c*r)),d=e.colorContrast(t,n,h,c),u=e.closestIndexOf(d),p=u+c*Math.abs(s-r);let g,m;return(1===c?s<r:c*s>c*r)?(g=u,m=p):(g=p,m=u),{rest:e.get(g),hover:e.get(m),active:e.get(g+c*i),focus:e.get(g+c*o)}}(_d.getValueFor(t),n||Nh.getValueFor(t),e,nh.getValueFor(t),sh.getValueFor(t),rh.getValueFor(t),ih.getValueFor(t))),Ed=Rc({name:"error-foreground-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>Dd(Rh.normal)(e,t)}),Od=(Rc("error-foreground-rest").withDefault((e=>Ed.getValueFor(e).evaluate(e).rest)),Rc("error-foreground-hover").withDefault((e=>Ed.getValueFor(e).evaluate(e).hover)),Rc("error-foreground-active").withDefault((e=>Ed.getValueFor(e).evaluate(e).active)));function $d(e){return`:host([hidden]){display:none}:host{display:${e}}`}let Ad;Rc("error-foreground-focus").withDefault((e=>Ed.getValueFor(e).evaluate(e).focus));const Bd=function(){if("boolean"==typeof Ad)return Ad;if("undefined"==typeof window||!window.document||!window.document.createElement)return Ad=!1,Ad;const e=document.createElement("style"),t=function(){const e=document.querySelector('meta[property="csp-nonce"]');return e?e.getAttribute("content"):null}();null!==t&&e.setAttribute("nonce",t),document.head.appendChild(e);try{e.sheet.insertRule("foo:focus-visible {color:inherit}",0),Ad=!0}catch(e){Ad=!1}finally{document.head.removeChild(e)}return Ad}()?"focus-visible":"focus",Nd=(function(e,...t){const{styles:n,behaviors:s}=Sl(e,t);return new kl(n,s)})`(${Hc} + ${qc}) * ${Gc}`,Rd=_l`
  ${$d("inline-flex")} :host {
    font-family: ${Pc};
    outline: none;
    font-size: ${Jc};
    line-height: ${Xc};
    height: calc(${Nd} * 1px);
    min-width: calc(${Nd} * 1px);
    background-color: ${rd};
    color: ${yd};
    border-radius: calc(${Wc} * 1px);
    fill: currentcolor;
    cursor: pointer;
    margin: calc((${Yc} + 2) * 1px);
  }

  .control {
    background: transparent;
    height: inherit;
    flex-grow: 1;
    box-sizing: border-box;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    padding: 0 calc((10 + (${Gc} * 2 * ${qc})) * 1px);
    white-space: nowrap;
    outline: none;
    text-decoration: none;
    border: calc(${Kc} * 1px) solid transparent;
    color: inherit;
    border-radius: inherit;
    fill: inherit;
    cursor: inherit;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
  }

  :host(:hover) {
    background-color: ${id};
  }

  :host(:active) {
    background-color: ${od};
  }

  :host([aria-pressed='true']) {
    box-shadow: inset 0px 0px 2px 2px ${ud};
  }

  :host([minimal]) {
    --density: -4;
  }

  :host([minimal]) .control {
    padding: 1px;
  }

  /* prettier-ignore */
  .control:${Bd} {
      outline: calc(${Yc} * 1px) solid ${pd};
      outline-offset: 2px;
      -moz-outline-radius: 0px;
    }

  .control::-moz-focus-inner {
    border: 0;
  }

  .start,
  .end {
    display: flex;
  }

  .control.icon-only {
    padding: 0;
    line-height: 0;
  }

  ::slotted(svg) {
    ${""} width: 16px;
    height: 16px;
    pointer-events: none;
  }

  .start {
    margin-inline-end: 11px;
  }

  .end {
    margin-inline-start: 11px;
  }
`.withBehaviors(Il(_l`
    :host .control {
      background-color: ${Tl.ButtonFace};
      border-color: ${Tl.ButtonText};
      color: ${Tl.ButtonText};
      fill: currentColor;
    }

    :host(:hover) .control {
      forced-color-adjust: none;
      background-color: ${Tl.Highlight};
      color: ${Tl.HighlightText};
    }

    /* prettier-ignore */
    .control:${Bd} {
          forced-color-adjust: none;
          background-color: ${Tl.Highlight};
          outline-color: ${Tl.ButtonText};
          color: ${Tl.HighlightText};
        }

    .control:hover,
    :host([appearance='outline']) .control:hover {
      border-color: ${Tl.ButtonText};
    }

    :host([href]) .control {
      border-color: ${Tl.LinkText};
      color: ${Tl.LinkText};
    }

    :host([href]) .control:hover,
        :host([href]) .control:${Bd} {
      forced-color-adjust: none;
      background: ${Tl.ButtonFace};
      outline-color: ${Tl.LinkText};
      color: ${Tl.LinkText};
      fill: currentColor;
    }
  `)),zd=_l`
  :host([appearance='accent']) {
    background: ${Ph};
    color: ${Zh};
  }

  :host([appearance='accent']:hover) {
    background: ${Hh};
    color: ${Kh};
  }

  :host([appearance='accent'][aria-pressed='true']) {
    box-shadow: inset 0px 0px 2px 2px ${nd};
  }

  :host([appearance='accent']:active) .control:active {
    background: ${Uh};
    color: ${Yh};
  }

  :host([appearance="accent"]) .control:${Bd} {
    outline-color: ${Wh};
  }
`.withBehaviors(Il(_l`
    :host([appearance='accent']) .control {
      forced-color-adjust: none;
      background: ${Tl.Highlight};
      color: ${Tl.HighlightText};
    }

    :host([appearance='accent']) .control:hover,
    :host([appearance='accent']:active) .control:active {
      background: ${Tl.HighlightText};
      border-color: ${Tl.Highlight};
      color: ${Tl.Highlight};
    }

    :host([appearance="accent"]) .control:${Bd} {
      outline-color: ${Tl.Highlight};
    }

    :host([appearance='accent'][href]) .control {
      background: ${Tl.LinkText};
      color: ${Tl.HighlightText};
    }

    :host([appearance='accent'][href]) .control:hover {
      background: ${Tl.ButtonFace};
      border-color: ${Tl.LinkText};
      box-shadow: none;
      color: ${Tl.LinkText};
      fill: currentColor;
    }

    :host([appearance="accent"][href]) .control:${Bd} {
      outline-color: ${Tl.HighlightText};
    }
  `)),Pd=_l`
  :host([appearance='error']) {
    background: ${Fd};
    color: ${Zh};
  }

  :host([appearance='error']:hover) {
    background: ${jd};
    color: ${Kh};
  }

  :host([appearance='error'][aria-pressed='true']) {
    box-shadow: inset 0px 0px 2px 2px ${Od};
  }

  :host([appearance='error']:active) .control:active {
    background: ${Md};
    color: ${Yh};
  }

  :host([appearance="error"]) .control:${Bd} {
    outline-color: ${Id};
  }
`.withBehaviors(Il(_l`
    :host([appearance='error']) .control {
      forced-color-adjust: none;
      background: ${Tl.Highlight};
      color: ${Tl.HighlightText};
    }

    :host([appearance='error']) .control:hover,
    :host([appearance='error']:active) .control:active {
      background: ${Tl.HighlightText};
      border-color: ${Tl.Highlight};
      color: ${Tl.Highlight};
    }

    :host([appearance="error"]) .control:${Bd} {
      outline-color: ${Tl.Highlight};
    }

    :host([appearance='error'][href]) .control {
      background: ${Tl.LinkText};
      color: ${Tl.HighlightText};
    }

    :host([appearance='error'][href]) .control:hover {
      background: ${Tl.ButtonFace};
      border-color: ${Tl.LinkText};
      box-shadow: none;
      color: ${Tl.LinkText};
      fill: currentColor;
    }

    :host([appearance="error"][href]) .control:${Bd} {
      outline-color: ${Tl.HighlightText};
    }
  `)),Hd=(_l`
  :host([appearance='hypertext']) {
    font-size: inherit;
    line-height: inherit;
    height: auto;
    min-width: 0;
    background: transparent;
  }

  :host([appearance='hypertext']) .control {
    display: inline;
    padding: 0;
    border: none;
    box-shadow: none;
    border-radius: 0;
    line-height: 1;
  }

  :host a.control:not(:link) {
    background-color: transparent;
    cursor: default;
  }
  :host([appearance='hypertext']) .control:link,
  :host([appearance='hypertext']) .control:visited {
    background: transparent;
    color: ${ed};
    border-bottom: calc(${Kc} * 1px) solid ${ed};
  }

  :host([appearance='hypertext']:hover),
  :host([appearance='hypertext']) .control:hover {
    background: transparent;
    border-bottom-color: ${td};
  }

  :host([appearance='hypertext']:active),
  :host([appearance='hypertext']) .control:active {
    background: transparent;
    border-bottom-color: ${nd};
  }

  :host([appearance="hypertext"]) .control:${Bd} {
    outline-color: transparent;
    border-bottom: calc(${Yc} * 1px) solid ${fd};
    margin-bottom: calc(calc(${Kc} - ${Yc}) * 1px);
  }
`.withBehaviors(Il(_l`
    :host([appearance='hypertext']:hover) {
      background-color: ${Tl.ButtonFace};
      color: ${Tl.ButtonText};
    }
    :host([appearance="hypertext"][href]) .control:hover,
        :host([appearance="hypertext"][href]) .control:active,
        :host([appearance="hypertext"][href]) .control:${Bd} {
      color: ${Tl.LinkText};
      border-bottom-color: ${Tl.LinkText};
      box-shadow: none;
    }
  `)),_l`
  :host([appearance='lightweight']) {
    background: transparent;
    color: ${ed};
  }

  :host([appearance='lightweight']) .control {
    padding: 0;
    height: initial;
    border: none;
    box-shadow: none;
    border-radius: 0;
  }

  :host([appearance='lightweight']:hover) {
    background: transparent;
    color: ${td};
  }

  :host([appearance='lightweight']:active) {
    background: transparent;
    color: ${nd};
  }

  :host([appearance='lightweight']) .content {
    position: relative;
  }

  :host([appearance='lightweight']) .content::before {
    content: '';
    display: block;
    height: calc(${Kc} * 1px);
    position: absolute;
    top: calc(1em + 4px);
    width: 100%;
  }

  :host([appearance='lightweight']:hover) .content::before {
    background: ${td};
  }

  :host([appearance='lightweight']:active) .content::before {
    background: ${nd};
  }

  :host([appearance="lightweight"]) .control:${Bd} {
    outline-color: transparent;
  }

  :host([appearance="lightweight"]) .control:${Bd} .content::before {
    background: ${yd};
    height: calc(${Yc} * 1px);
  }
`.withBehaviors(Il(_l`
    :host([appearance="lightweight"]) .control:hover,
        :host([appearance="lightweight"]) .control:${Bd} {
      forced-color-adjust: none;
      background: ${Tl.ButtonFace};
      color: ${Tl.Highlight};
    }
    :host([appearance="lightweight"]) .control:hover .content::before,
        :host([appearance="lightweight"]) .control:${Bd} .content::before {
      background: ${Tl.Highlight};
    }

    :host([appearance="lightweight"][href]) .control:hover,
        :host([appearance="lightweight"][href]) .control:${Bd} {
      background: ${Tl.ButtonFace};
      box-shadow: none;
      color: ${Tl.LinkText};
    }

    :host([appearance="lightweight"][href]) .control:hover .content::before,
        :host([appearance="lightweight"][href]) .control:${Bd} .content::before {
      background: ${Tl.LinkText};
    }
  `))),Ud=_l`
  :host([appearance='outline']) {
    background: transparent;
    border-color: ${Ph};
  }

  :host([appearance='outline']:hover) {
    border-color: ${Hh};
  }

  :host([appearance='outline']:active) {
    border-color: ${Uh};
  }

  :host([appearance='outline']) .control {
    border-color: inherit;
  }

  :host([appearance="outline"]) .control:${Bd} {
    outline-color: ${Wh};
  }
`.withBehaviors(Il(_l`
    :host([appearance='outline']) .control {
      border-color: ${Tl.ButtonText};
    }
    :host([appearance="outline"]) .control:${Bd} {
      forced-color-adjust: none;
      background-color: ${Tl.Highlight};
      outline-color: ${Tl.ButtonText};
      color: ${Tl.HighlightText};
      fill: currentColor;
    }
    :host([appearance='outline'][href]) .control {
      background: ${Tl.ButtonFace};
      border-color: ${Tl.LinkText};
      color: ${Tl.LinkText};
      fill: currentColor;
    }
    :host([appearance="outline"][href]) .control:hover,
        :host([appearance="outline"][href]) .control:${Bd} {
      forced-color-adjust: none;
      outline-color: ${Tl.LinkText};
    }
  `)),Wd=_l`
  :host([appearance='stealth']) {
    background: transparent;
  }

  :host([appearance='stealth']:hover) {
    background: ${cd};
  }

  :host([appearance='stealth']:active) {
    background: ${hd};
  }

  :host([appearance='stealth']) .control:${Bd} {
    outline-color: ${Wh};
  }
`.withBehaviors(Il(_l`
    :host([appearance='stealth']),
    :host([appearance='stealth']) .control {
      forced-color-adjust: none;
      background: ${Tl.ButtonFace};
      border-color: transparent;
      color: ${Tl.ButtonText};
      fill: currentColor;
    }

    :host([appearance='stealth']:hover) .control {
      background: ${Tl.Highlight};
      border-color: ${Tl.Highlight};
      color: ${Tl.HighlightText};
      fill: currentColor;
    }

    :host([appearance="stealth"]:${Bd}) .control {
      outline-color: ${Tl.Highlight};
      color: ${Tl.HighlightText};
      fill: currentColor;
    }

    :host([appearance='stealth'][href]) .control {
      color: ${Tl.LinkText};
    }

    :host([appearance="stealth"][href]:hover) .control,
        :host([appearance="stealth"][href]:${Bd}) .control {
      background: ${Tl.LinkText};
      border-color: ${Tl.LinkText};
      color: ${Tl.HighlightText};
      fill: currentColor;
    }

    :host([appearance="stealth"][href]:${Bd}) .control {
      forced-color-adjust: none;
      box-shadow: 0 0 0 1px ${Tl.LinkText};
    }
  `));class qd{constructor(e,t,n){this.propertyName=e,this.value=t,this.styles=n}bind(e){Bi.getNotifier(e).subscribe(this,this.propertyName),this.handleChange(e,this.propertyName)}unbind(e){Bi.getNotifier(e).unsubscribe(this,this.propertyName),e.$fastController.removeStyles(this.styles)}handleChange(e,t){e[t]===this.value?e.$fastController.addStyles(this.styles):e.$fastController.removeStyles(this.styles)}}function Gd(e,t){return new qd("appearance",e,t)}class Zd extends vl{constructor(){super(...arguments),this.appearance="neutral"}defaultSlottedContentChanged(e,t){const n=this.defaultSlottedContent.filter((e=>e.nodeType===Node.ELEMENT_NODE));1===n.length&&(n[0]instanceof SVGElement||n[0].classList.contains("fa")||n[0].classList.contains("fas"))?this.control.classList.add("icon-only"):this.control.classList.remove("icon-only")}}io([eo],Zd.prototype,"appearance",void 0),io([eo({attribute:"minimal",mode:"boolean"})],Zd.prototype,"minimal",void 0);const Kd=Zd.compose({baseName:"button",baseClass:vl,template:(e,t)=>nl`
    <button
        class="control"
        part="control"
        ?autofocus="${e=>e.autofocus}"
        ?disabled="${e=>e.disabled}"
        form="${e=>e.formId}"
        formaction="${e=>e.formaction}"
        formenctype="${e=>e.formenctype}"
        formmethod="${e=>e.formmethod}"
        formnovalidate="${e=>e.formnovalidate}"
        formtarget="${e=>e.formtarget}"
        name="${e=>e.name}"
        type="${e=>e.type}"
        value="${e=>e.value}"
        aria-atomic="${e=>e.ariaAtomic}"
        aria-busy="${e=>e.ariaBusy}"
        aria-controls="${e=>e.ariaControls}"
        aria-current="${e=>e.ariaCurrent}"
        aria-describedby="${e=>e.ariaDescribedby}"
        aria-details="${e=>e.ariaDetails}"
        aria-disabled="${e=>e.ariaDisabled}"
        aria-errormessage="${e=>e.ariaErrormessage}"
        aria-expanded="${e=>e.ariaExpanded}"
        aria-flowto="${e=>e.ariaFlowto}"
        aria-haspopup="${e=>e.ariaHaspopup}"
        aria-hidden="${e=>e.ariaHidden}"
        aria-invalid="${e=>e.ariaInvalid}"
        aria-keyshortcuts="${e=>e.ariaKeyshortcuts}"
        aria-label="${e=>e.ariaLabel}"
        aria-labelledby="${e=>e.ariaLabelledby}"
        aria-live="${e=>e.ariaLive}"
        aria-owns="${e=>e.ariaOwns}"
        aria-pressed="${e=>e.ariaPressed}"
        aria-relevant="${e=>e.ariaRelevant}"
        aria-roledescription="${e=>e.ariaRoledescription}"
        ${rl("control")}
    >
        ${al(0,t)}
        <span class="content" part="content">
            <slot ${Cl("defaultSlottedContent")}></slot>
        </span>
        ${ol(0,t)}
    </button>
`,styles:(e,t)=>_l`
    :host([disabled]),
    :host([disabled]:hover),
    :host([disabled]:active) {
      opacity: ${Zc};
      background-color: ${rd};
      cursor: ${Fl};
    }

    ${Rd}
  `.withBehaviors(Il(_l`
      :host([disabled]),
      :host([disabled]) .control,
      :host([disabled]:hover),
      :host([disabled]:active) {
        forced-color-adjust: none;
        background-color: ${Tl.ButtonFace};
        outline-color: ${Tl.GrayText};
        color: ${Tl.GrayText};
        cursor: ${Fl};
        opacity: 1;
      }
    `),Gd("accent",_l`
        :host([appearance='accent'][disabled]),
        :host([appearance='accent'][disabled]:hover),
        :host([appearance='accent'][disabled]:active) {
          background: ${Ph};
        }

        ${zd}
      `.withBehaviors(Il(_l`
          :host([appearance='accent'][disabled]) .control,
          :host([appearance='accent'][disabled]) .control:hover {
            background: ${Tl.ButtonFace};
            border-color: ${Tl.GrayText};
            color: ${Tl.GrayText};
          }
        `))),Gd("error",_l`
        :host([appearance='error'][disabled]),
        :host([appearance='error'][disabled]:hover),
        :host([appearance='error'][disabled]:active) {
          background: ${Fd};
        }

        ${Pd}
      `.withBehaviors(Il(_l`
          :host([appearance='error'][disabled]) .control,
          :host([appearance='error'][disabled]) .control:hover {
            background: ${Tl.ButtonFace};
            border-color: ${Tl.GrayText};
            color: ${Tl.GrayText};
          }
        `))),Gd("lightweight",_l`
        :host([appearance='lightweight'][disabled]:hover),
        :host([appearance='lightweight'][disabled]:active) {
          background-color: transparent;
          color: ${ed};
        }

        :host([appearance='lightweight'][disabled]) .content::before,
        :host([appearance='lightweight'][disabled]:hover) .content::before,
        :host([appearance='lightweight'][disabled]:active) .content::before {
          background: transparent;
        }

        ${Hd}
      `.withBehaviors(Il(_l`
          :host([appearance='lightweight'].disabled) .control {
            forced-color-adjust: none;
            color: ${Tl.GrayText};
          }

          :host([appearance='lightweight'].disabled)
            .control:hover
            .content::before {
            background: none;
          }
        `))),Gd("outline",_l`
        :host([appearance='outline'][disabled]),
        :host([appearance='outline'][disabled]:hover),
        :host([appearance='outline'][disabled]:active) {
          background: transparent;
          border-color: ${Ph};
        }

        ${Ud}
      `.withBehaviors(Il(_l`
          :host([appearance='outline'][disabled]) .control {
            border-color: ${Tl.GrayText};
          }
        `))),Gd("stealth",_l`
        ${Wd}
      `.withBehaviors(Il(_l`
          :host([appearance='stealth'][disabled]) {
            background: ${Tl.ButtonFace};
          }

          :host([appearance='stealth'][disabled]) .control {
            background: ${Tl.ButtonFace};
            border-color: transparent;
            color: ${Tl.GrayText};
          }
        `)))),shadowOptions:{delegatesFocus:!0}}),Yd=new Set(["children","localName","ref","style","className"]),Jd=Object.freeze(Object.create(null)),Xd="_default",Qd=new Map;function eu(e,t){if(!t.name){const n=ro.forType(e);if(!n)throw new Error("React wrappers must wrap a FASTElement or be configured with a name.");t.name=n.name}return t.name}function tu(e){return e.events||(e.events={})}function nu(e,t,n){return!Yd.has(n)||(console.warn(`${eu(e,t)} contains property ${n} which is a React reserved property. It will be used by React and not set on the element.`),!1)}const{wrap:su}=function(e,t){let n=[];const s={register(e,...t){n.forEach((n=>n.register(e,...t))),n=[]}};return{wrap:function(s,r={}){var i,o;s instanceof ea&&(t?t.register(s):n.push(s),s=s.type);const a=Qd.get(s);if(a){const e=a.get(null!==(i=r.name)&&void 0!==i?i:Xd);if(e)return e}class l extends e.Component{constructor(){super(...arguments),this._element=null}_updateElement(e){const t=this._element;if(null===t)return;const n=this.props,s=e||Jd,i=tu(r);for(const e in this._elementProps){const r=n[e],o=i[e];if(void 0===o)t[e]=r;else{const n=s[e];if(r===n)continue;void 0!==n&&t.removeEventListener(o,n),void 0!==r&&t.addEventListener(o,r)}}}componentDidMount(){this._updateElement()}componentDidUpdate(e){this._updateElement(e)}render(){const t=this.props.__forwardedRef;void 0!==this._ref&&this._userRef===t||(this._ref=e=>{null===this._element&&(this._element=e),null!==t&&function(e,t){"function"==typeof e?e(t):e.current=t}(t,e),this._userRef=t});const n={ref:this._ref},i=this._elementProps={},o=function(e,t){if(!t.keys)if(t.properties)t.keys=new Set(t.properties.concat(Object.keys(tu(t))));else{const n=new Set(Object.keys(tu(t))),s=Bi.getAccessors(e.prototype);if(s.length>0)for(const r of s)nu(e,t,r.name)&&n.add(r.name);else for(const s in e.prototype)!(s in HTMLElement.prototype)&&nu(e,t,s)&&n.add(s);t.keys=n}return t.keys}(s,r),a=this.props;for(const e in a){const t=a[e];o.has(e)?i[e]=t:n["className"===e?"class":e]=t}return e.createElement(eu(s,r),n)}}const c=e.forwardRef(((t,n)=>e.createElement(l,Object.assign(Object.assign({},t),{__forwardedRef:n}),null==t?void 0:t.children)));return Qd.has(s)||Qd.set(s,new Map),Qd.get(s).set(null!==(o=r.name)&&void 0!==o?o:Xd,c),c},registry:s}}(r(),Ma()),ru=su(Kd());ru.displayName="Jupyter.Button";class iu extends yl{constructor(e,t){super(e,t),this.observer=null,t.childList=!0}observe(){null===this.observer&&(this.observer=new MutationObserver(this.handleEvent.bind(this))),this.observer.observe(this.target,this.options)}disconnect(){this.observer.disconnect()}getNodes(){return"subtree"in this.options?Array.from(this.target.querySelectorAll(this.options.selector)):Array.from(this.target.childNodes)}}const ou="horizontal",au="vertical";var lu="undefined"==typeof Element,cu=lu?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,hu=!lu&&Element.prototype.getRootNode?function(e){return e.getRootNode()}:function(e){return e.ownerDocument},du=function(e){var t=e.getBoundingClientRect(),n=t.width,s=t.height;return 0===n&&0===s},uu=["input","select","textarea","a[href]","button","[tabindex]:not(slot)","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])',"details>summary:first-of-type","details"].concat("iframe").join(","),pu=function(e,t){if(t=t||{},!e)throw new Error("No node provided");return!1!==cu.call(e,uu)&&function(e,t){return!(t.disabled||function(e){return function(e){return"INPUT"===e.tagName}(e)&&"hidden"===e.type}(t)||function(e,t){var n=t.displayCheck,s=t.getShadowRoot;if("hidden"===getComputedStyle(e).visibility)return!0;var r=cu.call(e,"details>summary:first-of-type")?e.parentElement:e;if(cu.call(r,"details:not([open]) *"))return!0;var i=hu(e).host,o=(null==i?void 0:i.ownerDocument.contains(i))||e.ownerDocument.contains(e);if(n&&"full"!==n){if("non-zero-area"===n)return du(e)}else{if("function"==typeof s){for(var a=e;e;){var l=e.parentElement,c=hu(e);if(l&&!l.shadowRoot&&!0===s(l))return du(e);e=e.assignedSlot?e.assignedSlot:l||c===e.ownerDocument?l:c.host}e=a}if(o)return!e.getClientRects().length}return!1}(t,e)||function(e){return"DETAILS"===e.tagName&&Array.prototype.slice.apply(e.children).some((function(e){return"SUMMARY"===e.tagName}))}(t)||function(e){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(var t=e.parentElement;t;){if("FIELDSET"===t.tagName&&t.disabled){for(var n=0;n<t.children.length;n++){var s=t.children.item(n);if("LEGEND"===s.tagName)return!!cu.call(t,"fieldset[disabled] *")||!s.contains(e)}return!0}t=t.parentElement}return!1}(t))}(t,e)};const gu=Object.freeze({[hl.ArrowUp]:{[au]:-1},[hl.ArrowDown]:{[au]:1},[hl.ArrowLeft]:{[ou]:{[Ll.ltr]:-1,[Ll.rtl]:1}},[hl.ArrowRight]:{[ou]:{[Ll.ltr]:1,[Ll.rtl]:-1}}});class mu extends Xo{constructor(){super(...arguments),this._activeIndex=0,this.direction=Ll.ltr,this.orientation=ou}get activeIndex(){return Bi.track(this,"activeIndex"),this._activeIndex}set activeIndex(e){this.$fastController.isConnected&&(this._activeIndex=function(e,t,n){return Math.min(Math.max(n,0),t)}(0,this.focusableElements.length-1,e),Bi.notify(this,"activeIndex"))}slottedItemsChanged(){this.$fastController.isConnected&&this.reduceFocusableElements()}mouseDownHandler(e){var t;const n=null===(t=this.focusableElements)||void 0===t?void 0:t.findIndex((t=>t.contains(e.target)));return n>-1&&this.activeIndex!==n&&this.setFocusedElement(n),!0}childItemsChanged(e,t){this.$fastController.isConnected&&this.reduceFocusableElements()}connectedCallback(){super.connectedCallback(),this.direction=(e=>{const t=this.closest("[dir]");return null!==t&&"rtl"===t.dir?Ll.rtl:Ll.ltr})()}focusinHandler(e){const t=e.relatedTarget;t&&!this.contains(t)&&this.setFocusedElement()}getDirectionalIncrementer(e){var t,n,s,r,i;return null!==(i=null!==(s=null===(n=null===(t=gu[e])||void 0===t?void 0:t[this.orientation])||void 0===n?void 0:n[this.direction])&&void 0!==s?s:null===(r=gu[e])||void 0===r?void 0:r[this.orientation])&&void 0!==i?i:0}keydownHandler(e){const t=e.key;if(!(t in hl)||e.defaultPrevented||e.shiftKey)return!0;const n=this.getDirectionalIncrementer(t);if(!n)return!e.target.closest("[role=radiogroup]");const s=this.activeIndex+n;return this.focusableElements[s]&&e.preventDefault(),this.setFocusedElement(s),!0}get allSlottedItems(){return[...this.start.assignedElements(),...this.slottedItems,...this.end.assignedElements()]}reduceFocusableElements(){var e;const t=null===(e=this.focusableElements)||void 0===e?void 0:e[this.activeIndex];this.focusableElements=this.allSlottedItems.reduce(vu.reduceFocusableItems,[]);const n=this.focusableElements.indexOf(t);this.activeIndex=Math.max(0,n),this.setFocusableElements()}setFocusedElement(e=this.activeIndex){this.activeIndex=e,this.setFocusableElements(),this.focusableElements[this.activeIndex]&&this.contains(document.activeElement)&&this.focusableElements[this.activeIndex].focus()}static reduceFocusableItems(e,t){var n,s,r,i;const o="radio"===t.getAttribute("role"),a=null===(s=null===(n=t.$fastController)||void 0===n?void 0:n.definition.shadowOptions)||void 0===s?void 0:s.delegatesFocus,l=Array.from(null!==(i=null===(r=t.shadowRoot)||void 0===r?void 0:r.querySelectorAll("*"))&&void 0!==i?i:[]).some((e=>pu(e)));return t.hasAttribute("disabled")||t.hasAttribute("hidden")||!(pu(t)||o||a||l)?t.childElementCount?e.concat(Array.from(t.children).reduce(vu.reduceFocusableItems,[])):e:(e.push(t),e)}setFocusableElements(){this.$fastController.isConnected&&this.focusableElements.length>0&&this.focusableElements.forEach(((e,t)=>{e.tabIndex=this.activeIndex===t?0:-1}))}}io([Ni],mu.prototype,"direction",void 0),io([eo],mu.prototype,"orientation",void 0),io([Ni],mu.prototype,"slottedItems",void 0),io([Ni],mu.prototype,"slottedLabel",void 0),io([Ni],mu.prototype,"childItems",void 0);class fu{}io([eo({attribute:"aria-labelledby"})],fu.prototype,"ariaLabelledby",void 0),io([eo({attribute:"aria-label"})],fu.prototype,"ariaLabel",void 0),ll(fu,Ia),ll(mu,il,fu);class vu extends mu{connectedCallback(){super.connectedCallback();const e=na(this);e&&Nh.setValueFor(this,(t=>gd.getValueFor(t).evaluate(t,Nh.getValueFor(e))))}}const wu=vu.compose({baseName:"toolbar",baseClass:mu,template:(e,t)=>{return nl`
    <template
        aria-label="${e=>e.ariaLabel}"
        aria-labelledby="${e=>e.ariaLabelledby}"
        aria-orientation="${e=>e.orientation}"
        orientation="${e=>e.orientation}"
        role="toolbar"
        @mousedown="${(e,t)=>e.mouseDownHandler(t.event)}"
        @focusin="${(e,t)=>e.focusinHandler(t.event)}"
        @keydown="${(e,t)=>e.keydownHandler(t.event)}"
        ${n={property:"childItems",attributeFilter:["disabled","hidden"],filter:bl(),subtree:!0},"string"==typeof n&&(n={property:n}),new Va("fast-children",iu,n)}
    >
        <slot name="label"></slot>
        <div class="positioning-region" part="positioning-region">
            ${al(0,t)}
            <slot
                ${Cl({filter:bl(),property:"slottedItems"})}
            ></slot>
            ${ol(0,t)}
        </div>
    </template>
`;var n},styles:(e,t)=>_l`
    ${$d("inline-flex")} :host {
      --toolbar-item-gap: calc(
        (var(--design-unit) + calc(var(--density) + 2)) * 1px
      );
      background-color: ${Nh};
      border-radius: calc(${Wc} * 1px);
      fill: currentcolor;
      padding: var(--toolbar-item-gap);
    }

    :host(${Bd}) {
      outline: calc(${Kc} * 1px) solid ${Wh};
    }

    .positioning-region {
      align-items: flex-start;
      display: inline-flex;
      flex-flow: row wrap;
      justify-content: flex-start;
      width: 100%;
      height: 100%;
    }

    :host([orientation='vertical']) .positioning-region {
      flex-direction: column;
    }

    ::slotted(:not([slot])) {
      flex: 0 0 auto;
      margin: 0 var(--toolbar-item-gap);
    }

    :host([orientation='vertical']) ::slotted(:not([slot])) {
      margin: var(--toolbar-item-gap) 0;
    }

    .start,
    .end {
      display: flex;
      margin: auto;
      margin-inline: 0;
    }

    ::slotted(svg) {
      /* TODO: adaptive typography https://github.com/microsoft/fast/issues/2432 */
      width: 16px;
      height: 16px;
    }
  `.withBehaviors(Il(_l`
      :host(:${Bd}) {
        box-shadow: 0 0 0 calc(${Yc} * 1px)
          ${Tl.Highlight};
        color: ${Tl.ButtonText};
        forced-color-adjust: none;
      }
    `)),shadowOptions:{delegatesFocus:!0}}),bu="--jp-layout-color1";let yu=!1;const xu=e=>{const t=parseInt(e,10);return isNaN(t)?null:t},Cu={"--jp-border-width":{converter:xu,token:Kc},"--jp-border-radius":{converter:xu,token:Wc},[bu]:{converter:(e,t)=>{const n=bc(e);if(n){const e=Kl(n),t=Yl(Rl.fromObject({h:e.h,s:e.s,l:.5}));return Sc.create(t.r,t.g,t.b)}return null},token:Mh},"--jp-brand-color1":{converter:(e,t)=>{const n=bc(e);if(n){const e=Kl(n),s=t?1:-1,r=Yl(Rl.fromObject({h:e.h,s:e.s,l:e.l+s*Qc.getValueFor(document.body)/94}));return Sc.create(r.r,r.g,r.b)}return null},token:Th},"--jp-error-color1":{converter:(e,t)=>{const n=bc(e);if(n){const e=Kl(n),s=t?1:-1,r=Yl(Rl.fromObject({h:e.h,s:e.s,l:e.l+s*Qc.getValueFor(document.body)/94}));return Sc.create(r.r,r.g,r.b)}return null},token:Sd},"--jp-ui-font-family":{token:Pc},"--jp-ui-font-size1":{token:Jc}};function Su(){var e;const t=getComputedStyle(document.body),n=document.body.getAttribute("data-jp-theme-light");let s=!1;if(n)s="false"===n;else{const e=t.getPropertyValue(bu).toString();if(e){const t=bc(e);t&&(s=jc(Sc.create(t.r,t.g,t.b)),console.debug(`Theme is ${s?"dark":"light"} based on '${bu}' value: ${e}.`))}}Uc.setValueFor(document.body,s?Bc:Ac);for(const n in Cu){const r=Cu[n],i=t.getPropertyValue(n).toString();if(document.body&&""!==i){const t=(null!==(e=r.converter)&&void 0!==e?e:e=>e)(i.trim(),s);null!==t?r.token.setValueFor(document.body,t):console.error(`Fail to parse value '${i}' for '${n}' as FAST design token.`)}}}var _u=n(32895),ku=n(49503),Fu=n(14421),ju=n(97934);class Mu extends u.Widget{constructor(){super(),this._rootDOM=null}static create(e){return new class extends Mu{render(){return e}}}onUpdateRequest(e){this.renderPromise=this.renderDOM()}onAfterAttach(e){ku.MessageLoop.sendMessage(this,u.Widget.Msg.UpdateRequest)}onBeforeDetach(e){null!==this._rootDOM&&(this._rootDOM.unmount(),this._rootDOM=null)}renderDOM(){return new Promise((e=>{const t=this.render();null===this._rootDOM&&(this._rootDOM=(0,g.s)(this.node)),Array.isArray(t)||t?(this._rootDOM.render(t),requestIdleCallback((()=>e()))):(this._rootDOM.unmount(),this._rootDOM=null,requestIdleCallback((()=>e())))}))}}class Iu extends Mu{constructor(e){super(),this._modelChanged=new d.Signal(this),this.model=null!=e?e:null}get modelChanged(){return this._modelChanged}set model(e){this._model!==e&&(this._model&&this._model.stateChanged.disconnect(this.update,this),this._model=e,e&&e.stateChanged.connect(this.update,this),this.update(),this._modelChanged.emit(void 0))}get model(){return this._model}dispose(){this.isDisposed||(this._model=null,super.dispose())}}class Tu extends s.Component{constructor(e){super(e),this.slot=(e,t)=>{this.props.shouldUpdate&&!this.props.shouldUpdate(e,t)||this.setState({value:[e,t]})},this.state={value:[this.props.initialSender,this.props.initialArgs]}}componentDidMount(){this.props.signal.connect(this.slot)}componentWillUnmount(){this.props.signal.disconnect(this.slot)}render(){return this.props.children(...this.state.value)}}class Lu{constructor(){this.stateChanged=new d.Signal(this),this._isDisposed=!1}get isDisposed(){return this._isDisposed}dispose(){this.isDisposed||(this._isDisposed=!0,d.Signal.clearData(this))}}Ma().register([Kd(),wu()]),yu||(yu=!0,function(){const e=()=>{new MutationObserver((()=>{Su()})).observe(document.body,{attributes:!0,attributeFilter:["data-jp-theme-name"],childList:!1,characterData:!1}),Su()};"complete"===document.readyState?e():window.addEventListener("load",e)}());const Vu="toolbar-popup-opener",Du="jp-Toolbar-spacer";class Eu extends u.PanelLayout{constructor(){super(...arguments),this._dirty=!1}onFitRequest(e){super.onFitRequest(e),this.parent.isAttached&&((0,yi.some)(this.widgets,(e=>!e.isHidden))?(this.parent.node.style.minHeight="var(--jp-private-toolbar-height)",this.parent.removeClass("jp-Toolbar-micro")):(this.parent.node.style.minHeight="",this.parent.addClass("jp-Toolbar-micro"))),this._dirty=!0,this.parent.parent&&ku.MessageLoop.sendMessage(this.parent.parent,u.Widget.Msg.FitRequest),this._dirty&&ku.MessageLoop.sendMessage(this.parent,u.Widget.Msg.UpdateRequest)}onUpdateRequest(e){super.onUpdateRequest(e),this.parent.isVisible&&(this._dirty=!1)}onChildShown(e){super.onChildShown(e),this.parent.fit()}onChildHidden(e){super.onChildHidden(e),this.parent.fit()}onBeforeAttach(e){super.onBeforeAttach(e),this.parent.fit()}attachWidget(e,t){super.attachWidget(e,t),this.parent.fit()}detachWidget(e,t){super.detachWidget(e,t),this.parent.fit()}}class Ou extends u.Widget{constructor(e={}){var t;super({node:document.createElement("jp-toolbar")}),this.addClass("jp-Toolbar"),this.layout=null!==(t=e.layout)&&void 0!==t?t:new Eu}names(){const e=this.layout;return(0,yi.map)(e.widgets,(e=>Wu.nameProperty.get(e)))}addItem(e,t){const n=this.layout;return this.insertItem(n.widgets.length,e,t)}insertItem(e,t,n){if((0,yi.find)(this.names(),(e=>e===t)))return!1;n.addClass("jp-Toolbar-item");const s=this.layout,r=Math.max(0,Math.min(e,s.widgets.length));return s.insertWidget(r,n),Wu.nameProperty.set(n,t),!0}insertAfter(e,t,n){return this.insertRelative(e,1,t,n)}insertBefore(e,t,n){return this.insertRelative(e,0,t,n)}insertRelative(e,t,n,s){const r=(0,yi.map)(this.names(),((e,t)=>({name:e,index:t}))),i=(0,yi.find)(r,(t=>t.name===e));return!!i&&this.insertItem(i.index+t,n,s)}handleEvent(e){"click"===e.type&&this.handleClick(e)}handleClick(e){if(e.stopPropagation(),e.target instanceof HTMLLabelElement){const t=e.target.getAttribute("for");if(t&&this.node.querySelector(`#${t}`))return}this.node.contains(document.activeElement)||this.parent&&this.parent.activate()}onAfterAttach(e){this.node.addEventListener("click",this)}onBeforeDetach(e){this.node.removeEventListener("click",this)}}class $u extends Ou{constructor(){super(),this.popupOpener=new Uu,this._widgetWidths=new Map,this._widgetPositions=new Map,this._zoomChanged=!0,this.insertItem(0,Vu,this.popupOpener),this.popupOpener.hide(),this._resizer=new ju.Throttler((async(e=!1)=>{await this._onResize(e)}),500)}dispose(){this.isDisposed||(this._resizer&&this._resizer.dispose(),super.dispose())}insertAfter(e,t,n){return e!==Vu&&super.insertAfter(e,t,n)}insertRelative(e,t,n,s){const r=this._widgetPositions.get(e),i=(null!=r?r:0)+t;return this.insertItem(i,n,s)}insertItem(e,t,n){var s;let r;if(n instanceof Uu)r=super.insertItem(e,t,n);else{const s=Math.max(0,Math.min(e,this.layout.widgets.length-1));r=super.insertItem(s,t,n)}if(t!==Vu&&this._widgetPositions.get(t)!==e){const n=null!==(s=this._widgetPositions.get(t))&&void 0!==s?s:this._widgetPositions.size;this._widgetPositions.forEach(((t,s)=>{s!==Vu&&(t>=e&&t<n?this._widgetPositions.set(s,t+1):t<=e&&t>n&&this._widgetPositions.set(s,t-1))})),this._widgetPositions.set(t,e),this.isVisible&&this._resizer.invoke()}return r}onAfterShow(e){this._resizer.invoke(!0)}onBeforeHide(e){this.popupOpener.hidePopup(),super.onBeforeHide(e)}onResize(e){super.onResize(e);const t=Math.round(window.outerWidth/window.innerWidth*100);t!==this._zoom&&(this._zoomChanged=!0,this._zoom=t),e.width>0&&this._resizer&&this._resizer.invoke()}async _onResize(e=!1){if(!this.parent||!this.parent.isAttached)return;const t=this.node.clientWidth,n=this.popupOpener;let s=n.isHidden?7:39;this._getWidgetsToRemove(s,t,32).then((s=>{var r,i;let{width:o,widgetsToRemove:a}=s;for(;a.length>0;){const e=a.pop(),t=Wu.nameProperty.get(e);o-=this._widgetWidths.get(t)||0;const s=null!==(r=this._widgetPositions.get(t))&&void 0!==r?r:0;let l=this._widgetPositions.size;const c=n.widgetAt(0);if(c){const e=Wu.nameProperty.get(c);l=null!==(i=this._widgetPositions.get(e))&&void 0!==i?i:l}const h=s-l;n.insertWidget(h,e)}if(n.widgetCount()>0){const e=[];let s=0;const r=n.widgetCount();for(;s<r;){let i=n.widgetAt(s);if(!i)break;if(o+=this._getWidgetWidth(i),r-e.length==1&&(o-=32),!(o<t))break;e.push(i),s++}for(;e.length>0;){const t=e.shift(),n=Wu.nameProperty.get(t);this._widgetPositions.has(n)?this.insertItem(this._widgetPositions.get(n),n,t):this.addItem(n,t)}}n.widgetCount()>0?(n.updatePopup(),n.show()):n.hide(),e&&this._onResize()})).catch((e=>{console.error("Error while computing the ReactiveToolbar",e)}))}async _getWidgetsToRemove(e,t,n){var s;const r=this.popupOpener,i=this.layout,o=i.widgets.length-1,a=[];let l=0;for(;l<o;){const o=i.widgets[l],c=Wu.nameProperty.get(o);let h;h=this._zoomChanged?await this._saveWidgetWidth(c,o):this._getWidgetWidth(o)||await this._saveWidgetWidth(c,o),e+=h,0===a.length&&r.isHidden&&e+n>t&&(e+=n),(e>t||(null!==(s=this._widgetPositions.get(c))&&void 0!==s?s:0)>l)&&a.push(o),l++}return this._zoomChanged=!1,{width:e,widgetsToRemove:a}}async _saveWidgetWidth(e,t){t instanceof Mu&&await t.renderPromise;const n=t.hasClass(Du)?2:t.node.clientWidth;return this._widgetWidths.set(e,n),n}_getWidgetWidth(e){const t=Wu.nameProperty.get(e);return this._widgetWidths.get(t)||0}}function Au(e){var t,n;const r=null!==(t=e.actualOnClick)&&void 0!==t&&t,i=r?void 0:t=>{var n;0===t.button&&(t.preventDefault(),null===(n=e.onClick)||void 0===n||n.call(e))},o=r?t=>{var n;0===t.button&&(null===(n=e.onClick)||void 0===n||n.call(e))}:void 0;return s.createElement(ru,{appearance:"stealth",className:e.className?e.className+" jp-ToolbarButtonComponent":"jp-ToolbarButtonComponent","aria-pressed":e.pressed,"aria-disabled":!1===e.enabled,...e.dataset,disabled:!1===e.enabled,onClick:o,onMouseDown:i,onKeyDown:t=>{var n;const{key:s}=t;"Enter"!==s&&" "!==s||null===(n=e.onClick)||void 0===n||n.call(e)},title:!1===e.enabled&&e.disabledTooltip?e.disabledTooltip:e.pressed&&e.pressedTooltip?e.pressedTooltip:e.tooltip||e.iconLabel,minimal:!0},(e.icon||e.iconClass)&&s.createElement(b.resolveReact,{icon:e.pressed&&null!==(n=e.pressedIcon)&&void 0!==n?n:e.icon,iconClass:a(e.iconClass,"jp-Icon"),tag:null}),e.label&&s.createElement("span",{className:"jp-ToolbarButtonComponent-label"},e.label))}function Bu(e){return e.addClass("jp-ToolbarButton"),e}!function(e){e.createSpacerItem=function(){return new Wu.Spacer}}(Ou||(Ou={}));class Nu extends Mu{constructor(e={}){var t,n;super(),this.props=e,Bu(this),this._enabled=null===(t=e.enabled)||void 0===t||t,this._pressed=this._enabled&&null!==(n=e.pressed)&&void 0!==n&&n,this._onClick=e.onClick}set pressed(e){this.enabled&&e!==this._pressed&&(this._pressed=e,this.update())}get pressed(){return this._pressed}set enabled(e){e!=this._enabled&&(this._enabled=e,this._enabled||(this._pressed=!1),this.update())}get enabled(){return this._enabled}set onClick(e){e!==this._onClick&&(this._onClick=e,this.update())}get onClick(){return this._onClick}render(){return s.createElement(Au,{...this.props,pressed:this.pressed,enabled:this.enabled,onClick:this.onClick})}}function Ru(e){return s.createElement(Tu,{signal:e.commands.commandChanged,shouldUpdate:(t,n)=>n.id===e.id&&"changed"===n.type||"many-changed"===n.type},(()=>e.commands.listCommands().includes(e.id)?s.createElement(Au,{...Wu.propsFromCommand(e)}):null))}function zu(e){return e.addClass("jp-CommandToolbarButton"),e}class Pu extends Mu{constructor(e){super(),this.props=e;const{commands:t,id:n,args:s}=e;zu(this),this.setCommandAttributes(t,n,s),t.commandChanged.connect(((r,i)=>{i.id===e.id&&this.setCommandAttributes(t,n,s)}),this)}setCommandAttributes(e,t,n){e.isToggled(t,n)?this.addClass("lm-mod-toggled"):this.removeClass("lm-mod-toggled"),e.isVisible(t,n)?this.removeClass("lm-mod-hidden"):this.addClass("lm-mod-hidden"),e.isEnabled(t,n)?"disabled"in this.node&&(this.node.disabled=!1):"disabled"in this.node&&(this.node.disabled=!0)}render(){return s.createElement(Ru,{...this.props})}}class Hu extends u.Widget{constructor(){super({node:document.createElement("jp-toolbar")}),this.width=0,this.addClass("jp-Toolbar"),this.addClass("jp-Toolbar-responsive-popup"),this.layout=new u.PanelLayout,u.Widget.attach(this,document.body),this.hide()}updateWidth(e){e>0&&(this.width=e,this.node.style.width=`${e}px`)}alignTo(e){const{height:t,width:n,x:s,y:r}=e.node.getBoundingClientRect(),i=this.width;this.node.style.left=s+n-i+1+"px",this.node.style.top=`${r+t+1}px`}insertWidget(e,t){this.layout.insertWidget(e,t)}widgetCount(){return this.layout.widgets.length}widgetAt(e){return this.layout.widgets[e]}}class Uu extends Nu{constructor(e={}){const t=(e.translator||yt.nullTranslator).load("jupyterlab");super({icon:ee,onClick:()=>{this.handleClick()},tooltip:t.__("More commands")}),this.addClass("jp-Toolbar-responsive-opener"),this.popup=new Hu}addWidget(e){this.popup.insertWidget(0,e)}insertWidget(e,t){this.popup.insertWidget(e,t)}dispose(){this.isDisposed||(this.popup.dispose(),super.dispose())}hide(){super.hide(),this.hidePopup()}hidePopup(){this.popup.hide()}updatePopup(){this.popup.updateWidth(this.parent.node.clientWidth),this.popup.alignTo(this.parent)}widgetAt(e){return this.popup.widgetAt(e)}widgetCount(){return this.popup.widgetCount()}handleClick(){this.updatePopup(),this.popup.setHidden(!this.popup.isHidden)}}var Wu;!function(e){e.propsFromCommand=function(e){var t,n;const{commands:s,id:r,args:i}=e,o=s.iconClass(r,i),a=s.iconLabel(r,i),l=null!==(t=e.icon)&&void 0!==t?t:s.icon(r,i),c=s.label(r,i);let h,d=s.className(r,i);s.isToggleable(r,i)&&(h=s.isToggled(r,i),h&&(d+=" lm-mod-toggled")),s.isVisible(r,i)||(d+=" lm-mod-hidden");const u="function"==typeof e.label?e.label(null!=i?i:{}):e.label;let p=s.caption(r,i)||u||c||a;const g=s.keyBindings.find((e=>e.command===r));g&&(p=`${p} (${g.keys.map(_u.CommandRegistry.formatKeystroke).join(", ")})`);const m=s.isEnabled(r,i);return{className:d,dataset:{"data-command":e.id},icon:l,iconClass:o,tooltip:null!==(n=e.caption)&&void 0!==n?n:p,onClick:()=>{s.execute(r,i)},enabled:m,label:null!=u?u:c,pressed:h}},e.nameProperty=new Fu.AttachedProperty({name:"name",create:()=>""});class t extends u.Widget{constructor(){super(),this.addClass(Du)}}e.Spacer=t}(Wu||(Wu={}));class qu extends u.Panel{constructor(e={}){super(e),this._toolbar=new Ou}get toolbar(){return this._toolbar}}function Gu(e,t){let n=1/0,s=null;const r=/[\p{L}\p{N}\p{M}]+/gu;for(;;){let i=r.exec(e);if(!i)break;let o=yi.StringExt.matchSumOfDeltas(e,t,i.index);if(!o)break;o&&o.score<=n&&(n=o.score,s=o.indices)}return s&&n!==1/0?{score:n,indices:s}:null}const Zu=(e,t,n)=>s=>t?Gu(s,e.toLowerCase()):(n||(s=s.toLocaleLowerCase(),e=e.toLocaleLowerCase()),-1===s.indexOf(e)?null:{indices:[...Array(s.length).keys()].map((e=>e+1))}),Ku=e=>{var t;const[n,i]=(0,s.useState)(null!==(t=e.initialQuery)&&void 0!==t?t:"");return e.forceRefresh&&(0,s.useEffect)((()=>{e.updateFilter((e=>({})))}),[]),(0,s.useEffect)((()=>{void 0!==e.initialQuery&&e.updateFilter(Zu(e.initialQuery,e.useFuzzyFilter,e.caseSensitive),e.initialQuery)}),[]),r().createElement(wi,{className:"jp-FilterBox",inputRef:e.inputRef,type:"text",disabled:e.disabled,rightIcon:"ui-components:search",placeholder:e.placeholder,onChange:t=>{const n=t.target;i(n.value),e.updateFilter(Zu(n.value,e.useFuzzyFilter,e.caseSensitive),n.value)},value:n})},Yu=e=>Mu.create(r().createElement(Ku,{updateFilter:e.updateFilter,useFuzzyFilter:e.useFuzzyFilter,placeholder:e.placeholder,forceRefresh:e.forceRefresh,caseSensitive:e.caseSensitive}));class Ju extends u.AccordionLayout{constructor(){super(...arguments),this._toolbars=new WeakMap}insertWidget(e,t){t.toolbar&&(this._toolbars.set(t,t.toolbar),t.toolbar.addClass("jp-AccordionPanel-toolbar")),super.insertWidget(e,t)}removeWidgetAt(e){const t=this.widgets[e];super.removeWidgetAt(e),t&&this._toolbars.has(t)&&this._toolbars.delete(t)}attachWidget(e,t){super.attachWidget(e,t);const n=this._toolbars.get(t);n&&(this.parent.isAttached&&ku.MessageLoop.sendMessage(n,u.Widget.Msg.BeforeAttach),this.titles[e].appendChild(n.node),this.parent.isAttached&&ku.MessageLoop.sendMessage(n,u.Widget.Msg.AfterAttach))}detachWidget(e,t){const n=this._toolbars.get(t);n&&(this.parent.isAttached&&ku.MessageLoop.sendMessage(n,u.Widget.Msg.BeforeDetach),this.titles[e].removeChild(n.node),this.parent.isAttached&&ku.MessageLoop.sendMessage(n,u.Widget.Msg.AfterDetach)),super.detachWidget(e,t)}onBeforeAttach(e){this.notifyToolbars(e),super.onBeforeAttach(e)}onAfterAttach(e){super.onAfterAttach(e),this.notifyToolbars(e)}onBeforeDetach(e){this.notifyToolbars(e),super.onBeforeDetach(e)}onAfterDetach(e){super.onAfterDetach(e),this.notifyToolbars(e)}notifyToolbars(e){this.widgets.forEach((t=>{const n=this._toolbars.get(t);n&&n.processMessage(e)}))}}var Xu,Qu,ep;!function(e){class t extends u.AccordionPanel.Renderer{createCollapseIcon(e){const t=document.createElement("div");return L.element({container:t}),t}createSectionTitle(e){const t=super.createSectionTitle(e);return t.classList.add("jp-AccordionPanel-title"),t}}e.Renderer=t,e.defaultRenderer=new t,e.createLayout=function(t){var n;return t.layout||new Ju({renderer:t.renderer||e.defaultRenderer,orientation:t.orientation,alignment:t.alignment,spacing:t.spacing,titleSpace:null!==(n=t.titleSpace)&&void 0!==n?n:32})}}(Xu||(Xu={}));class tp extends u.Widget{constructor(e={}){var t;super();const n=this.layout=new u.PanelLayout;this.addClass("jp-SidePanel");const s=this._trans=(e.translator||yt.nullTranslator).load("jupyterlab");e.header&&this.addHeader(e.header);const r=this._content=null!==(t=e.content)&&void 0!==t?t:new u.AccordionPanel({...e,layout:Xu.createLayout(e)});r.node.setAttribute("role","region"),r.node.setAttribute("aria-label",s.__("side panel content")),r.addClass("jp-SidePanel-content"),n.addWidget(r),e.toolbar&&this.addToolbar(e.toolbar)}get content(){return this._content}get header(){return this._header||this.addHeader(),this._header}get toolbar(){return this._toolbar||this.addToolbar(),this._toolbar}get widgets(){return this.content.widgets}addWidget(e){this.content.addWidget(e)}insertWidget(e,t){this.content.insertWidget(e,t)}addHeader(e){const t=this._header=e||new u.Panel;t.addClass("jp-SidePanel-header"),this.layout.insertWidget(0,t)}addToolbar(e){const t=this._toolbar=null!=e?e:new Ou;t.addClass("jp-SidePanel-toolbar"),t.node.setAttribute("aria-label",this._trans.__("side panel actions")),this.layout.insertWidget(this.layout.widgets.length-1,t)}}class np extends u.Widget{constructor(){super(),this.addClass("jp-Spinner"),this.node.tabIndex=-1;const e=document.createElement("div");e.className="jp-SpinnerContent",this.node.appendChild(e)}onActivateRequest(e){this.node.focus()}}!function(e){function t(e,t,s=""){if(e.localName===t&&e.classList.add("jp-mod-styled"),"select"===e.localName){const t=e.hasAttribute("multiple");n(e,t)}const r=e.getElementsByTagName(t);for(let e=0;e<r.length;e++){const i=r[e];if(i.classList.add("jp-mod-styled"),s&&i.classList.add(s),"select"===t){const e=i.hasAttribute("multiple");n(i,e)}}}function n(e,t){const n=document.createElement("div");return n.classList.add("jp-select-wrapper"),e.addEventListener("focus",ep.onFocus),e.addEventListener("blur",ep.onFocus),e.classList.add("jp-mod-styled"),e.parentElement&&e.parentElement.replaceChild(n,e),n.appendChild(e),t?n.classList.add("multiple"):n.appendChild(I.element({tag:"span",stylesheet:"select",right:"8px",top:"5px",width:"18px"})),n}e.styleNode=function(e,n=""){t(e,"select",n),t(e,"textarea",n),t(e,"input",n),t(e,"button",n)},e.styleNodeByTag=t,e.wrapSelect=n}(Qu||(Qu={})),function(e){e.onFocus=function(e){const t=e.target.parentElement;t&&("focus"===e.type?t.classList.add("jp-mod-focused"):t.classList.remove("jp-mod-focused"))}}(ep||(ep={}));class sp extends u.Widget{constructor(){super(),this._button=document.createElement("button"),this._label=document.createElement("label"),this._valueChanged=new d.Signal(this),this._button.className="jp-switch",this._button.setAttribute("role","switch"),this._label.className="jp-switch-label";const e=document.createElement("div");e.className="jp-switch-track",e.setAttribute("aria-hidden","true"),this._button.appendChild(this._label),this._button.appendChild(e),this.node.appendChild(this._button)}get value(){return this._value}set value(e){const t=this._value;t!==e&&(this._button.setAttribute("aria-checked",e.toString()),this._value=e,this._valueChanged.emit({name:"value",oldValue:t,newValue:e}))}get valueChanged(){return this._valueChanged}get label(){var e;return null!==(e=this._label.textContent)&&void 0!==e?e:""}set label(e){this._label.textContent=e}get caption(){return this._button.title}set caption(e){this._button.title=e,this._label.title=e}handleEvent(e){"click"===e.type&&(this.value=!this.value)}onAfterAttach(){this._button.addEventListener("click",this)}onBeforeDetach(){this._button.removeEventListener("click",this)}}const rp="jp-sortable-table";function ip(e){const[t,n]=(0,s.useState)({sortKey:e.sortKey,sortDirection:e.sortDirection||1});let i=e.rows;const o=e.columns.filter((e=>e.id===t.sortKey))[0];if(o){const n=o.sort.bind(o);i=e.rows.sort(((e,s)=>n(e.data,s.data)*t.sortDirection))}const a=e.columns.filter((e=>(!e.isAvailable||e.isAvailable())&&!e.isHidden)),l=i.map((t=>{const n=a.map((e=>r().createElement("td",{key:e.id+"-"+t.key},e.renderCell(t.data))));return r().createElement("tr",{key:t.key,"data-key":t.key,onClick:e.onRowClick,className:"jp-sortable-table-tr"},n)})),c=a.map((e=>r().createElement(op,{label:e.label,id:e.id,state:t,key:e.id,onSort:()=>{var s;(s=e.id)===t.sortKey?n({sortKey:s,sortDirection:-1*t.sortDirection}):n({sortKey:s,sortDirection:1})}})));return r().createElement("table",{className:rp},r().createElement("thead",null,r().createElement("tr",{className:"jp-sortable-table-tr"},c)),r().createElement("tbody",null,l))}function op(e){const t=e.id===e.state.sortKey,n=t&&1!==e.state.sortDirection?L:O;return r().createElement("th",{key:e.id,onClick:()=>e.onSort(),className:t?"jp-sorted-header":void 0,"data-id":e.id},r().createElement("div",{className:"jp-sortable-table-th-wrapper"},r().createElement("label",null,e.label),r().createElement(n.react,{tag:"span",className:"jp-sort-icon"})))}let ap=!1;try{window.addEventListener("test",null,Object.defineProperty({},"passive",{get:function(){ap={passive:!0}}}))}catch(e){}class lp{constructor(e={}){var t,n,s,r,i,o;this.scrollDownThreshold=1,this.scrollUpThreshold=0,this.paddingTop=0,this._estimatedWidgetSize=cp.DEFAULT_WIDGET_SIZE,this._stateChanged=new d.Signal(this),this._currentWindow=[-1,-1,-1,-1],this._height=0,this._isDisposed=!1,this._itemsList=null,this._lastMeasuredIndex=-1,this._overscanCount=1,this._scrollOffset=0,this._widgetCount=0,this._widgetSizers=[],this._windowingActive=!0,this._widgetCount=null!==(s=null!==(n=null===(t=e.itemsList)||void 0===t?void 0:t.length)&&void 0!==n?n:e.count)&&void 0!==s?s:0,this._overscanCount=null!==(r=e.overscanCount)&&void 0!==r?r:1,this._windowingActive=null===(i=e.windowingActive)||void 0===i||i,this.itemsList=null!==(o=e.itemsList)&&void 0!==o?o:null}get height(){return this._height}set height(e){this._height=e}get isDisposed(){return this._isDisposed}get itemsList(){return this._itemsList}set itemsList(e){var t,n,s;if(this._itemsList!==e){this._itemsList&&this._itemsList.changed.disconnect(this.onListChanged,this);const r=this._itemsList;this._itemsList=e,this._itemsList?this._itemsList.changed.connect(this.onListChanged,this):this._widgetCount=0,this._stateChanged.emit({name:"list",newValue:this._itemsList,oldValue:r}),this._stateChanged.emit({name:"count",newValue:null!==(n=null===(t=this._itemsList)||void 0===t?void 0:t.length)&&void 0!==n?n:0,oldValue:null!==(s=null==r?void 0:r.length)&&void 0!==s?s:0})}}get overscanCount(){return this._overscanCount}set overscanCount(e){if(e>=1){if(this._overscanCount!==e){const t=this._overscanCount;this._overscanCount=e,this._stateChanged.emit({name:"overscanCount",newValue:e,oldValue:t})}}else console.error(`Forbidden non-positive overscan count: got ${e}`)}get scrollOffset(){return this._scrollOffset}set scrollOffset(e){this._scrollOffset=e}get widgetCount(){return this._itemsList?this._itemsList.length:this._widgetCount}set widgetCount(e){if(this.itemsList)console.error("It is not allow to change the widgets count of a windowed list if a items list is used.");else if(e>=0){if(this._widgetCount!==e){const t=this._widgetCount;this._widgetCount=e,this._stateChanged.emit({name:"count",newValue:e,oldValue:t})}}else console.error(`Forbidden negative widget count: got ${e}`)}get windowingActive(){return this._windowingActive}set windowingActive(e){if(e!==this._windowingActive){const t=this._windowingActive;this._windowingActive=e,this._currentWindow=[-1,-1,-1,-1],this._lastMeasuredIndex=-1,this._widgetSizers=[],this._stateChanged.emit({name:"windowingActive",newValue:e,oldValue:t})}}get stateChanged(){return this._stateChanged}dispose(){this.isDisposed||(this._isDisposed=!0,d.Signal.clearData(this))}getEstimatedTotalSize(){let e=0;if(this._lastMeasuredIndex>=this.widgetCount&&(this._lastMeasuredIndex=this.widgetCount-1),this._lastMeasuredIndex>=0){const t=this._widgetSizers[this._lastMeasuredIndex];e=t.offset+t.size}return e+(this.widgetCount-this._lastMeasuredIndex-1)*this._estimatedWidgetSize}getOffsetForIndexAndAlignment(e,t="auto",n=0,s,r){const i=Math.min(Math.max(0,n),1),o=this._height,a=s?s.itemMetadata:this._getItemMetadata(e),l=this.scrollDownThreshold<=1?a.size*this.scrollDownThreshold:this.scrollDownThreshold,c=this.scrollUpThreshold<=1?a.size*this.scrollUpThreshold:this.scrollUpThreshold,h=s?s.totalSize:this.getEstimatedTotalSize(),d=Math.max(0,Math.min(h-o,a.offset)),u=Math.max(0,a.offset-o+a.size),p=s?s.currentOffset:this._scrollOffset,g=this._windowingActive?this.paddingTop:0,m=a.offset,f=a.offset+a.size,v=p-g+o,w=p-g,b=v>m&&v<f,y=v>f&&w<m;if("smart"===t){if(y||b&&v-m>=l||w>m&&w<f&&w-m<c)return p;t=p>=u-o&&p<=d+o?"auto":a.size>o?"top-center":"center"}if("auto"===t){if(y)return p;t=void 0!==r?r:b||v<=f?"end":"start"}switch(t){case"start":return Math.max(0,d-i*o)+g;case"end":return u+i*o+g;case"center":return u+(d-u)/2;case"top-center":return d-o/2}}getRangeToRender(){let e=[0,Math.max(this.widgetCount-1,-1),0,Math.max(this.widgetCount-1,-1)];const t=this._lastMeasuredIndex;this.windowingActive&&(e=this._getRangeToRender());const[n,s]=e;return t<=s||this._currentWindow[0]!==n||this._currentWindow[1]!==s?(this._currentWindow=e,e):null}getSpan(e,t){const n=this._getItemMetadata(e),s=n.offset,r=this._getItemMetadata(t);return[s,r.offset-n.offset+r.size]}resetAfterIndex(e){const t=this._lastMeasuredIndex;this._lastMeasuredIndex=Math.min(e,this._lastMeasuredIndex),this._lastMeasuredIndex!==t&&this._stateChanged.emit({name:"index",newValue:e,oldValue:t})}setWidgetSize(e){if(this._currentWindow[0]>=0){let t=1/0;for(const n of e){const e=n.index,s=n.size;this._widgetSizers[e].size!=s&&(this._widgetSizers[e].size=s,t=Math.min(t,e)),this._widgetSizers[e].measured=!0}if(t!=1/0)return this._lastMeasuredIndex=Math.min(this._lastMeasuredIndex,t),!0}return!1}onListChanged(e,t){switch(t.type){case"add":this._widgetSizers.splice(t.newIndex,0,...new Array(t.newValues.length).map((()=>({offset:0,size:this._estimatedWidgetSize})))),this.resetAfterIndex(t.newIndex-1);break;case"move":yi.ArrayExt.move(this._widgetSizers,t.oldIndex,t.newIndex),this.resetAfterIndex(Math.min(t.newIndex,t.oldIndex)-1);break;case"remove":this._widgetSizers.splice(t.oldIndex,t.oldValues.length),this.resetAfterIndex(t.oldIndex-1);break;case"set":this.resetAfterIndex(t.newIndex-1)}}_getItemMetadata(e){var t,n;if(e>this._lastMeasuredIndex){let s=0;if(this._lastMeasuredIndex>=0){const e=this._widgetSizers[this._lastMeasuredIndex];s=e.offset+e.size}for(let r=this._lastMeasuredIndex+1;r<=e;r++){let e=(null===(t=this._widgetSizers[r])||void 0===t?void 0:t.measured)?this._widgetSizers[r].size:this.estimateWidgetSize(r);this._widgetSizers[r]={offset:s,size:e,measured:null===(n=this._widgetSizers[r])||void 0===n?void 0:n.measured},s+=e}this._lastMeasuredIndex=e}for(let e=0;e<=this._lastMeasuredIndex;e++){const t=this._widgetSizers[e];if(0===e){if(0!==t.offset)throw new Error("First offset is not null")}else{const n=this._widgetSizers[e-1];if(t.offset!==n.offset+n.size)throw new Error(`Sizer ${e} has incorrect offset.`)}}return this._widgetSizers[e]}_findNearestItem(e){return(this._lastMeasuredIndex>0?this._widgetSizers[this._lastMeasuredIndex].offset:0)>=e?this._findNearestItemBinarySearch(this._lastMeasuredIndex,0,e):this._findNearestItemExponentialSearch(Math.max(0,this._lastMeasuredIndex),e)}_findNearestItemBinarySearch(e,t,n){for(;t<=e;){const s=t+Math.floor((e-t)/2),r=this._getItemMetadata(s).offset;if(r===n)return s;r<n?t=s+1:r>n&&(e=s-1)}return t>0?t-1:0}_findNearestItemExponentialSearch(e,t){let n=1;for(;e<this.widgetCount&&this._getItemMetadata(e).offset<t;)e+=n,n*=2;return this._findNearestItemBinarySearch(Math.min(e,this.widgetCount-1),Math.floor(e/2),t)}_getRangeToRender(){const e=this.widgetCount;if(0===e)return[-1,-1,-1,-1];const t=this._getStartIndexForOffset(this._scrollOffset),n=this._getStopIndexForStartIndex(t,this._scrollOffset),s=Math.max(1,this.overscanCount),r=Math.max(1,this.overscanCount);return[Math.max(0,t-s),Math.max(0,Math.min(e-1,n+r)),t,n]}_getStartIndexForOffset(e){return this._findNearestItem(e)}_getStopIndexForStartIndex(e,t){const n=this._height,s=this._getItemMetadata(e),r=t+n;let i=s.offset+s.size,o=e;for(;o<this.widgetCount-1&&i<r;)o++,i+=this._getItemMetadata(o).size;return o}}class cp extends u.Widget{constructor(e){var t,n;const s=null!==(t=e.renderer)&&void 0!==t?t:cp.defaultRenderer,r=document.createElement("div");r.className="jp-WindowedPanel";const i=r.appendChild(document.createElement("div"));i.classList.add("jp-WindowedPanel-scrollbar"),i.appendChild(s.createScrollbar()).classList.add("jp-WindowedPanel-scrollbar-content");const o=r.appendChild(s.createOuter());o.classList.add("jp-WindowedPanel-outer");const a=o.appendChild(document.createElement("div"));a.className="jp-WindowedPanel-inner";const l=a.appendChild(s.createViewport());l.classList.add("jp-WindowedPanel-viewport"),super({node:r}),this.jumped=new d.Signal(this),this._needsUpdate=!1,super.layout=null!==(n=e.layout)&&void 0!==n?n:new hp,this.renderer=s,this._innerElement=a,this._isScrolling=null,this._outerElement=o,this._itemsResizeObserver=null,this._scrollbarElement=i,this._scrollToItem=null,this._scrollRepaint=null,this._scrollUpdateWasRequested=!1,this._updater=new ju.Throttler((()=>this.update()),50),this._viewModel=e.model,this._viewport=l,e.scrollbar&&r.classList.add("jp-mod-virtual-scrollbar"),this.viewModel.stateChanged.connect(this.onStateChanged,this)}get isParentHidden(){return this._isParentHidden}set isParentHidden(e){this._isParentHidden=e}get layout(){return super.layout}get outerNode(){return this._outerElement}get viewportNode(){return this._viewport}get scrollbar(){return this.node.classList.contains("jp-mod-virtual-scrollbar")}set scrollbar(e){e?this.node.classList.add("jp-mod-virtual-scrollbar"):this.node.classList.remove("jp-mod-virtual-scrollbar"),this._adjustDimensionsForScrollbar(),this.update()}get viewModel(){return this._viewModel}dispose(){this._updater.dispose(),super.dispose()}handleEvent(e){switch(e.type){case"pointerdown":e.preventDefault(),e.stopPropagation(),this._evtPointerDown(e);break;case"scroll":this.onScroll(e)}}scrollTo(e){this.viewModel.windowingActive?(e=Math.max(0,e))!==this.viewModel.scrollOffset&&(this.viewModel.scrollOffset=e,this._scrollUpdateWasRequested=!0,this.update()):this._outerElement.scrollTo({top:e})}scrollToItem(e,t="auto",n=.25,s){let r;if(this._isScrolling&&null!==this._scrollToItem&&this._scrollToItem[0]===e&&this._scrollToItem[1]===t||(this._isScrolling&&this._isScrolling.reject("Scrolling to a new item is requested."),this._isScrolling=new p.PromiseDelegate,this._isScrolling.promise.catch(console.debug)),this._scrollToItem=[e,t,n,s],this._resetScrollToItem(),!this.viewModel.windowingActive){const t=this._innerElement.querySelector(`[data-windowed-list-index="${e}"]`);if(!(t&&t instanceof HTMLElement))return console.debug(`Element with index ${e} not found`),Promise.resolve();r={totalSize:this._outerElement.scrollHeight,itemMetadata:{offset:t.offsetTop,size:t.clientHeight},currentOffset:this._outerElement.scrollTop}}return this.scrollTo(this.viewModel.getOffsetForIndexAndAlignment(Math.max(0,Math.min(e,this.viewModel.widgetCount-1)),t,n,r,s)),this._isScrolling.promise}onAfterAttach(e){super.onAfterAttach(e),this.viewModel.windowingActive?this._applyWindowingStyles():this._applyNoWindowingStyles(),this._addListeners(),this.viewModel.height=this.node.getBoundingClientRect().height;const t=window.getComputedStyle(this._viewport);this.viewModel.paddingTop=parseFloat(t.paddingTop),this._scrollbarElement.addEventListener("pointerdown",this)}onBeforeDetach(e){this._removeListeners(),this._scrollbarElement.removeEventListener("pointerdown",this),super.onBeforeDetach(e)}onScroll(e){const{clientHeight:t,scrollHeight:n,scrollTop:s}=e.currentTarget;if(!this._scrollUpdateWasRequested&&Math.abs(this.viewModel.scrollOffset-s)>1){const e=Math.max(0,Math.min(s,n-t));this.viewModel.scrollOffset=e,this._scrollUpdateWasRequested=!1,this.update()}}onResize(e){const t=this.viewModel.height;this.viewModel.height=e.height>=0?e.height:this.node.getBoundingClientRect().height,this.viewModel.height!==t&&this._updater.invoke(),super.onResize(e),this._updater.invoke()}onStateChanged(e,t){if("windowingActive"===t.name){if(this._removeListeners(),this.viewModel.windowingActive)return this._applyWindowingStyles(),this.onScroll({currentTarget:this.node}),void this._addListeners();this._applyNoWindowingStyles(),this._addListeners()}this.update()}onUpdateRequest(e){this.scrollbar&&this._renderScrollbar(),this.viewModel.windowingActive?null===this._scrollRepaint?(this._needsUpdate=!1,this._scrollRepaint=window.requestAnimationFrame((()=>{this._scrollRepaint=null,this._update(),this._needsUpdate&&this.update()}))):this._needsUpdate=!0:this._update()}_adjustDimensionsForScrollbar(){const e=this._outerElement,t=this._scrollbarElement;if(this.scrollbar){let n=e.offsetWidth-e.clientWidth;0==n?(n=1e3,e.style.paddingRight=`${n}px`,e.style.boxSizing="border-box"):e.style.paddingRight="0",e.style.width=`calc(100% + ${n}px)`,this._innerElement.style.marginRight=`${t.offsetWidth}px`}else e.style.width="100%",this._innerElement.style.marginRight="0",e.style.paddingRight="0",e.style.boxSizing=""}_addListeners(){if(this.viewModel.windowingActive){this._itemsResizeObserver||(this._itemsResizeObserver=new ResizeObserver(this._onItemResize.bind(this)));for(const e of this.layout.widgets)this._itemsResizeObserver.observe(e.node),e.disposed.connect((()=>{var t;return null===(t=this._itemsResizeObserver)||void 0===t?void 0:t.unobserve(e.node)}));this._outerElement.addEventListener("scroll",this,ap),this._scrollbarResizeObserver=new ResizeObserver(this._adjustDimensionsForScrollbar.bind(this)),this._scrollbarResizeObserver.observe(this._outerElement),this._scrollbarResizeObserver.observe(this._scrollbarElement)}else this._areaResizeObserver||(this._areaResizeObserver=new ResizeObserver(this._onAreaResize.bind(this)),this._areaResizeObserver.observe(this._innerElement))}_applyNoWindowingStyles(){this._viewport.style.position="relative",this._viewport.style.top="0px"}_applyWindowingStyles(){this._viewport.style.position="absolute"}_removeListeners(){var e,t,n;this._outerElement.removeEventListener("scroll",this),null===(e=this._areaResizeObserver)||void 0===e||e.disconnect(),this._areaResizeObserver=null,null===(t=this._itemsResizeObserver)||void 0===t||t.disconnect(),this._itemsResizeObserver=null,null===(n=this._scrollbarResizeObserver)||void 0===n||n.disconnect(),this._scrollbarResizeObserver=null}_update(){var e;if(this.isDisposed||!this.layout)return;const t=this.viewModel.getRangeToRender();if(null!==t){const[n,s]=t,r=[];if(s>=0)for(let e=n;e<=s;e++){const t=this.viewModel.widgetRenderer(e);t.dataset.windowedListIndex=`${e}`,r.push(t)}for(let t=this.layout.widgets.length-1;t>=0;t--)r.includes(this.layout.widgets[t])||(null===(e=this._itemsResizeObserver)||void 0===e||e.unobserve(this.layout.widgets[t].node),this.layout.removeWidget(this.layout.widgets[t]));for(let e=0;e<r.length;e++){const t=r[e];this._itemsResizeObserver&&!this.layout.widgets.includes(t)&&(this._itemsResizeObserver.observe(t.node),t.disposed.connect((()=>{var e;return null===(e=this._itemsResizeObserver)||void 0===e?void 0:e.unobserve(t.node)}))),this.layout.insertWidget(e,t)}if(this.viewModel.windowingActive){if(s>=0){const e=this.viewModel.getEstimatedTotalSize();this._innerElement.style.height=`${e}px`;const[t,r]=this.viewModel.getSpan(n,s);this._viewport.style.top=`${t}px`,this._viewport.style.minHeight=`${r}px`}else this._innerElement.style.height="0px",this._viewport.style.top="0px",this._viewport.style.minHeight="0px";this._scrollUpdateWasRequested&&(this._outerElement.scrollTop=this.viewModel.scrollOffset,this._scrollUpdateWasRequested=!1)}}let n=-1;for(const e of this._viewport.children){const t=parseInt(e.dataset.windowedListIndex,10);if(t<n)throw new Error("Inconsistent dataset index");n=t}}_onAreaResize(e){this._scrollBackToItemOnResize()}_onItemResize(e){if(this._resetScrollToItem(),this.isHidden||this.isParentHidden)return;const t=[];for(let n of e)n.target.isConnected&&t.push({index:parseInt(n.target.dataset.windowedListIndex,10),size:n.borderBoxSize[0].blockSize});this.viewModel.setWidgetSize(t)&&(this._scrollBackToItemOnResize(),this.update())}_scrollBackToItemOnResize(){this._scrollToItem&&this.scrollToItem(...this._scrollToItem).catch((e=>{console.log(e)}))}_resetScrollToItem(){this._resetScrollToItemTimeout&&clearTimeout(this._resetScrollToItemTimeout),this._scrollToItem&&(this._resetScrollToItemTimeout=window.setTimeout((()=>{this._scrollToItem=null,this._isScrolling&&(this._isScrolling.resolve(),this._isScrolling=null)}),100))}_renderScrollbar(){var e,t;const{node:n,renderer:s,viewModel:r}=this,i=n.querySelector(".jp-WindowedPanel-scrollbar-content");for(;i.firstChild;)i.removeChild(i.firstChild);const o=r.itemsList,a=null!==(e=null==o?void 0:o.length)&&void 0!==e?e:r.widgetCount;for(let e=0;e<a;e+=1){const n=null===(t=null==o?void 0:o.get)||void 0===t?void 0:t.call(o,e),r=s.createScrollbarItem(this,e,n);r.classList.add("jp-WindowedPanel-scrollbar-item"),r.dataset.index=`${e}`,i.appendChild(r)}}_evtPointerDown(e){let t=e.target;for(;t&&t.parentElement;){if(t.hasAttribute("data-index")){const e=parseInt(t.getAttribute("data-index"),10);return void(async()=>{await this.scrollToItem(e),this.jumped.emit(e)})()}t=t.parentElement}}}cp.DEFAULT_WIDGET_SIZE=50;class hp extends u.PanelLayout{constructor(){super({fitPolicy:"set-no-constraint"})}get parent(){return super.parent}set parent(e){super.parent=e}attachWidget(e,t){let n=this.parent.viewportNode.children[e];this.parent.isAttached&&ku.MessageLoop.sendMessage(t,u.Widget.Msg.BeforeAttach),this.parent.viewportNode.insertBefore(t.node,n),this.parent.isAttached&&ku.MessageLoop.sendMessage(t,u.Widget.Msg.AfterAttach)}detachWidget(e,t){this.parent.isAttached&&ku.MessageLoop.sendMessage(t,u.Widget.Msg.BeforeDetach),this.parent.viewportNode.removeChild(t.node),this.parent.isAttached&&ku.MessageLoop.sendMessage(t,u.Widget.Msg.AfterDetach)}moveWidget(e,t,n){let s=this.parent.viewportNode.children[t];e<t?s.insertAdjacentElement("afterend",n.node):s.insertAdjacentElement("beforebegin",n.node)}onUpdateRequest(e){}}!function(e){class t{createOuter(){return document.createElement("div")}createScrollbar(){return document.createElement("ol")}createScrollbarItem(e,t){const n=document.createElement("li");return n.appendChild(document.createTextNode(`${t}`)),n}createViewport(){return document.createElement("div")}}e.Renderer=t,e.defaultRenderer=new t}(cp||(cp={}));var dp=n(24475);const up=he.bindprops({stylesheet:"commandPaletteHeader",className:"jp-icon-hoverShow-content"});var pp;!function(e){class t extends u.CommandPalette.Renderer{renderHeader(e){const t=this.formatHeader(e);return dp.h.li({className:a("lm-CommandPalette-header","jp-icon-hoverShow")},t,dp.h.span(up))}renderItemIcon(e){const t=this.createIconClass(e);return e.item.isToggled?dp.h.div({className:t},A,e.item.iconLabel):dp.h.div({className:t},e.item.icon,e.item.iconLabel)}createIconClass(e){return a(f.styleClass({stylesheet:"commandPaletteItem"}),e.item.iconClass,"lm-CommandPalette-itemIcon")}}e.Renderer=t,e.defaultRenderer=new t}(pp||(pp={}));const gp=D.bindprops({stylesheet:"menuItem"});class mp extends u.ContextMenu{constructor(e){super(e),this._isDisposed=!1,this._opened=new d.Signal(this),this.menu=new fp(e)}get isDisposed(){return this._isDisposed}get opened(){return this._opened}dispose(){this._isDisposed||(this._isDisposed=!0,this.menu.dispose(),d.Signal.disconnectSender(this))}open(e){if(this._isDisposed)return!1;const t=super.open(e);return t&&this._opened.emit(),t}}class fp extends u.Menu{constructor(e){e.renderer=e.renderer||fp.defaultRenderer,super(e)}insertItem(e,t){return t.submenu&&fp.overrideDefaultRenderer(t.submenu),super.insertItem(e,t)}}!function(e){e.overrideDefaultRenderer=function t(n){n.renderer===u.Menu.defaultRenderer&&(n.renderer=e.defaultRenderer);const s=n.insertItem.bind(n);n.insertItem=(t,n)=>(n.submenu&&e.overrideDefaultRenderer(n.submenu),s(t,n));for(const e of n._items)e.submenu&&t(e.submenu)};class t extends u.Menu.Renderer{renderIcon(e){const t=this.createIconClass(e);return e.item.isToggled?dp.h.div({className:t},A,e.item.iconLabel):dp.h.div({className:t},e.item.icon,e.item.iconLabel)}createIconClass(e){let t="lm-Menu-itemIcon";return"separator"===e.item.type?a(e.item.iconClass,t):a(f.styleClass({stylesheet:"menuItem"}),e.item.iconClass,t)}renderSubmenu(e){const t="lm-Menu-itemSubmenuIcon";return"submenu"===e.item.type?dp.h.div({className:t},gp):dp.h.div({className:t})}}e.Renderer=t,e.defaultRenderer=new t}(fp||(fp={}));class vp extends u.TabBar{constructor(e={}){var t;super({renderer:vp.defaultRenderer,...e});const n=(null!==(t=vp.translator)&&void 0!==t?t:yt.nullTranslator).load("jupyterlab");_.element({container:this.addButtonNode,title:n.__("New Launcher")})}}vp.translator=null,function(e){class t extends u.TabBar.Renderer{renderCloseIcon(t){var n;const s=(null!==(n=e.translator)&&void 0!==n?n:yt.nullTranslator).load("jupyterlab"),r=t.title.label?s.__("Close %1",t.title.label):s.__("Close tab"),i=a("jp-icon-hover lm-TabBar-tabCloseIcon",f.styleClass({elementPosition:"center",height:"16px",width:"16px"}));return(0,dp.hpass)("div",{className:i,title:r},z)}}e.Renderer=t,e.defaultRenderer=new t}(vp||(vp={}));class wp extends u.DockPanel{constructor(e={}){super({renderer:wp.defaultRenderer,...e})}}!function(e){class t extends u.DockPanel.Renderer{createTabBar(){const e=new vp;return e.addClass("lm-DockPanel-tabBar"),e}}e.Renderer=t,e.defaultRenderer=new t}(wp||(wp={}));class bp extends u.TabPanel{constructor(e={}){e.renderer=e.renderer||vp.defaultRenderer,super(e)}}const yp="jp-HoverBox",xp="-1000";var Cp;!function(e){e.setGeometry=function(e){const{anchor:t,host:n,node:s,privilege:r,outOfViewDisplay:i}=e,o=n.getBoundingClientRect();s.classList.contains(yp)||s.classList.add(yp),s.style.visibility&&(s.style.visibility=""),""===s.style.zIndex&&(s.style.zIndex=""),s.style.maxHeight="",s.style.marginTop="";const a=e.style||window.getComputedStyle(s),l=t.top-o.top,c=o.bottom-t.bottom,h=parseInt(a.marginTop,10)||0,d=parseInt(a.marginLeft,10)||0,u=parseInt(a.minHeight,10)||e.minHeight;let p=parseInt(a.maxHeight,10)||e.maxHeight;const g="forceAbove"!==r&&("forceBelow"===r||("above"===r?l<p&&l<c:c>=p||c>=l));if(g?p=Math.min(c-h,p):(p=Math.min(l,p),s.style.marginTop="0px"),s.style.maxHeight=`${p}px`,!(p>=u&&(c>=u||l>=u)))return s.style.zIndex=xp,void(s.style.visibility="hidden");e.size?(s.style.width=`${e.size.width}px`,s.style.height=`${e.size.height}px`,s.style.contain="strict"):(s.style.contain="",s.style.width="auto",s.style.height="");const m=e.size?e.size.height:s.getBoundingClientRect().height,f=e.offset&&e.offset.vertical&&e.offset.vertical.above||0,v=e.offset&&e.offset.vertical&&e.offset.vertical.below||0;let w=g?o.bottom-c+v:o.top+l-m+f;s.style.top=`${Math.floor(w)}px`;const b=e.offset&&e.offset.horizontal||0;let y=t.left+b;s.style.left=`${Math.ceil(y)}px`;let x=s.getBoundingClientRect(),C=x.right;C>window.innerWidth&&(y-=C-window.innerWidth,C=window.innerWidth,s.style.left=`${Math.ceil(y)}px`),y<b-d&&(y=b-d,s.style.left=`${Math.ceil(y)}px`),s.style.zIndex="-1000";const S=x.bottom,_=n.contains(document.elementFromPoint(y,w)),k=n.contains(document.elementFromPoint(C,w)),F=n.contains(document.elementFromPoint(C,S)),j=n.contains(document.elementFromPoint(y,S));s.style.zIndex="";const M=_||k,I=j||F,T=_||j,L=F||k,V=S-w,D=C-y,E=w<o.top,O=S>o.bottom,$=y+d<o.left,A=C>o.right;let B=!1,N=!1,R=!1;if(E)switch((null==i?void 0:i.top)||"hidden-inside"){case"hidden-inside":M||(B=!0);break;case"hidden-outside":I||(B=!0);break;case"stick-inside":o.top>w&&(w=o.top,R=!0);break;case"stick-outside":o.top>S&&(w=o.top-V,R=!0)}if(O)switch((null==i?void 0:i.bottom)||"hidden-outside"){case"hidden-inside":I||(B=!0);break;case"hidden-outside":M||(B=!0);break;case"stick-inside":o.bottom<S&&(w=o.bottom-V,R=!0);break;case"stick-outside":o.bottom<w&&(w=o.bottom,R=!0)}if($)switch((null==i?void 0:i.left)||"hidden-inside"){case"hidden-inside":T||(B=!0);break;case"hidden-outside":L||(B=!0);break;case"stick-inside":o.left>y+d&&(y=o.left-d,N=!0);break;case"stick-outside":o.left>C&&(y=o.left-d-D,N=!0)}if(A)switch((null==i?void 0:i.right)||"hidden-outside"){case"hidden-inside":L||(B=!0);break;case"hidden-outside":T||(B=!0);break;case"stick-inside":o.right<C&&(y=o.right-D,N=!0);break;case"stick-outside":o.right<y&&(y=o.right,N=!0)}B&&(s.style.zIndex=xp,s.style.visibility="hidden"),N&&(s.style.left=`${Math.ceil(y)}px`),R&&(s.style.top=`${Math.ceil(w)}px`)}}(Cp||(Cp={}));const Sp=new p.Token("@jupyterlab/ui-components:IFormRendererRegistry","A service for settings form renderer registration."),_p=new p.Token("@jupyterlab/ui-components:ILabIconManager","A service to register and request icons.");class kp{constructor(){this._renderers={}}addRenderer(e,t){if(this._renderers[e])throw new Error(`A renderer with id '${e}' is already registered.`);if(!t.fieldRenderer&&!t.widgetRenderer)throw new Error(`The component for '${e}' cannot be registered as it does not define 'fieldRenderer' nor 'widgetRenderer'.`);const n=e.lastIndexOf("."),s=e.substring(0,n),r=e.substring(n+1);if(0==s.length||0==r.length)throw new Error(`Form renderer id must follows the structure '<ISettingRegistry.IPlugin.id>.<propertyName>'; got ${e}.`);this._renderers[e]=t}get renderers(){return this._renderers}getRenderer(e){return this._renderers[e]}}},8843:(e,t,n)=>{"use strict";n.r(t),n.d(t,{Cache:()=>g,FreeStyle:()=>b,Rule:()=>v,Selector:()=>m,Style:()=>f,create:()=>y});let s=0;const r=Object.create(null),i=["animation-iteration-count","border-image-outset","border-image-slice","border-image-width","box-flex","box-flex-group","box-ordinal-group","column-count","columns","counter-increment","counter-reset","flex","flex-grow","flex-positive","flex-shrink","flex-negative","flex-order","font-weight","grid-area","grid-column","grid-column-end","grid-column-span","grid-column-start","grid-row","grid-row-end","grid-row-span","grid-row-start","line-clamp","line-height","opacity","order","orphans","tab-size","widows","z-index","zoom","fill-opacity","flood-opacity","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-miterlimit","stroke-opacity","stroke-width"];for(const e of i)for(const t of["-webkit-","-ms-","-moz-","-o-",""])r[t+e]=!0;function o(e,t){return t&&"number"==typeof t&&!r[e]?`${e}:${t}px`:`${e}:${t}`}function a(e){return e.sort(((e,t)=>e[0]>t[0]?1:-1))}function l(e){return e.map((([e,t])=>Array.isArray(t)?t.map((t=>o(e,t))).join(";"):o(e,t))).join(";")}function c(e,t){return-1===e.indexOf("&")?`${t} ${e}`:e.replace(/&/g,t)}function h(e,t,n,s,r){const{style:i,nested:o,isUnique:d}=function(e,t){const n=[],s=[];for(const t of Object.keys(e)){const i=t.trim(),o=e[t];36!==i.charCodeAt(0)&&null!=o&&("object"!=typeof o||Array.isArray(o)?n.push([(r=i,r.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`)).replace(/^ms-/,"-ms-")),o]):s.push([i,o]))}var r;return{style:l(a(n)),nested:t?s:a(s),isUnique:!!e.$unique}}(t,""!==e);let u=i;if(64===e.charCodeAt(0)){const t={selector:e,styles:[],rules:[],style:r?"":i};n.push(t),i&&r&&t.styles.push({selector:r,style:i,isUnique:d});for(const[e,n]of o)u+=e+h(e,n,t.rules,t.styles,r)}else{const t=r?c(e,r):e;i&&s.push({selector:t,style:i,isUnique:d});for(const[e,r]of o)u+=e+h(e,r,n,s,t)}return u}function d(e,t,n,r,i,o){for(const{selector:n,style:a,isUnique:l}of r){const r=o?c(n,i):n,h=l?`u\0${(++s).toString(36)}`:`s\0${t}\0${a}`,d=new f(a,h);d.add(new m(r,`k\0${t}\0${r}`)),e.add(d)}for(const{selector:s,style:r,rules:a,styles:l}of n){const n=new v(s,r,`r\0${t}\0${s}\0${r}`);d(n,t,a,l,i,o),e.add(n)}}function u(e){let t="";for(let n=0;n<e.length;n++)t+=e[n];return t}const p={add:()=>{},change:()=>{},remove:()=>{}};class g{constructor(e=p){this.changes=e,this.sheet=[],this.changeId=0,this._keys=[],this._children=Object.create(null),this._counters=Object.create(null)}add(e){const t=this._counters[e.id]||0,n=this._children[e.id]||e.clone();if(this._counters[e.id]=t+1,0===t)this._children[n.id]=n,this._keys.push(n.id),this.sheet.push(n.getStyles()),this.changeId++,this.changes.add(n,this._keys.length-1);else if(n instanceof g&&e instanceof g){const t=this._keys.indexOf(e.id),s=n.changeId;n.merge(e),n.changeId!==s&&(this.sheet.splice(t,1,n.getStyles()),this.changeId++,this.changes.change(n,t,t))}}remove(e){const t=this._counters[e.id];if(t){this._counters[e.id]=t-1;const n=this._children[e.id],s=this._keys.indexOf(n.id);if(1===t)delete this._counters[e.id],delete this._children[e.id],this._keys.splice(s,1),this.sheet.splice(s,1),this.changeId++,this.changes.remove(n,s);else if(n instanceof g&&e instanceof g){const t=n.changeId;n.unmerge(e),n.changeId!==t&&(this.sheet.splice(s,1,n.getStyles()),this.changeId++,this.changes.change(n,s,s))}}}values(){return this._keys.map((e=>this._children[e]))}merge(e){for(const t of e.values())this.add(t);return this}unmerge(e){for(const t of e.values())this.remove(t);return this}clone(){return(new g).merge(this)}}class m{constructor(e,t){this.selector=e,this.id=t}getStyles(){return this.selector}clone(){return this}}class f extends g{constructor(e,t){super(),this.style=e,this.id=t}getStyles(){return`${this.sheet.join(",")}{${this.style}}`}clone(){return new f(this.style,this.id).merge(this)}}class v extends g{constructor(e,t,n){super(),this.rule=e,this.style=t,this.id=n}getStyles(){return`${this.rule}{${this.style}${u(this.sheet)}}`}clone(){return new v(this.rule,this.style,this.id).merge(this)}}function w(e,t){return`f${function(e){let t=5381,n=e.length;for(;n--;)t=33*t^e.charCodeAt(n);return(t>>>0).toString(36)}(e)}`}class b extends g{constructor(e,t){super(t),this.id=e}registerStyle(e){const t=[],n=[],s=h("&",e,t,n),r=w(s);return d(this,s,t,n,`.${r}`,!0),r}registerKeyframes(e){return this.registerHashRule("@keyframes",e)}registerHashRule(e,t){const n=[],s=[],r=h("",t,n,s),i=w(r),o=new v(`${e} ${i}`,"",`h\0${r}\0${e}`);return d(o,r,n,s,"",!1),this.add(o),i}registerRule(e,t){const n=[],s=[];d(this,h(e,t,n,s),n,s,"",!1)}registerCss(e){return this.registerRule("",e)}getStyles(){return u(this.sheet)}clone(){return new b(this.id,this.changes).merge(this)}}function y(e){return new b(`f${(++s).toString(36)}`,e)}},63005:(e,t,n)=>{var s=n(10228),r=n(79749);e.exports=function(e,t){return s(e,t,(function(t,n){return r(e,n)}))}},10228:(e,t,n)=>{var s=n(79867),r=n(78859),i=n(76747);e.exports=function(e,t,n){for(var o=-1,a=t.length,l={};++o<a;){var c=t[o],h=s(e,c);n(h,c)&&r(l,i(c,e),h)}return l}},14648:(e,t,n)=>{var s=n(63005),r=n(24288)((function(e,t){return null==e?{}:s(e,t)}));e.exports=r},43551:(e,t,n)=>{var s=n(70830);e.exports=function(e,t){return null==e||s(e,t)}},37634:(e,t,n)=>{"use strict";var s=n(38005);t.s=s.createRoot,s.hydrateRoot},73062:(e,t,n)=>{"use strict";var s=n(53861);s.TypeStyle,n(66720);var r=n(51833);r.extend,r.classes,r.media;var i=new s.TypeStyle({autoGenerateTag:!0});i.setStylesTarget,i.cssRaw,i.cssRule,i.forceRenderStyles,i.fontFace,i.getStyles,i.keyframes,i.reinit,t.oB=i.style,i.stylesheet},62034:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.convertToStyles=function e(t){var n={};for(var s in t){var r=t[s];if("$nest"===s){var i=r;for(var o in i){var a=i[o];n[o]=e(a)}}else"$debugName"===s?n.$displayName=r:n[s]=r}return n},t.convertToKeyframes=function(e){var t={};for(var n in e)"$debugName"!==n&&(t[n]=e[n]);return e.$debugName&&(t.$displayName=e.$debugName),t}},53861:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s=n(8843),r=n(62034),i=n(51833),o=function(){return s.create()},a=function(){function e(e){var t=this,n=e.autoGenerateTag;this.cssRaw=function(e){e&&(t._raw+=e||"",t._pendingRawChange=!0,t._styleUpdated())},this.cssRule=function(e){for(var n=[],s=1;s<arguments.length;s++)n[s-1]=arguments[s];var o=r.convertToStyles(i.extend.apply(void 0,n));t._freeStyle.registerRule(e,o),t._styleUpdated()},this.forceRenderStyles=function(){var e=t._getTag();e&&(e.textContent=t.getStyles())},this.fontFace=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];for(var s=t._freeStyle,r=0,i=e;r<i.length;r++){var o=i[r];s.registerRule("@font-face",o)}t._styleUpdated()},this.getStyles=function(){return(t._raw||"")+t._freeStyle.getStyles()},this.keyframes=function(e){var n=r.convertToKeyframes(e),s=t._freeStyle.registerKeyframes(n);return t._styleUpdated(),s},this.reinit=function(){var e=o();t._freeStyle=e,t._lastFreeStyleChangeId=e.changeId,t._raw="",t._pendingRawChange=!1;var n=t._getTag();n&&(n.textContent="")},this.setStylesTarget=function(e){t._tag&&(t._tag.textContent=""),t._tag=e,t.forceRenderStyles()},this.stylesheet=function(e){for(var n={},s=0,r=Object.getOwnPropertyNames(e);s<r.length;s++){var i=r[s],o=e[i];o&&(o.$debugName=i,n[i]=t.style(o))}return n};var s=o();this._autoGenerateTag=n,this._freeStyle=s,this._lastFreeStyleChangeId=s.changeId,this._pending=0,this._pendingRawChange=!1,this._raw="",this._tag=void 0,this.style=this.style.bind(this)}return e.prototype._afterAllSync=function(e){var t=this;this._pending++;var n=this._pending;i.raf((function(){n===t._pending&&e()}))},e.prototype._getTag=function(){if(this._tag)return this._tag;if(this._autoGenerateTag){var e="undefined"==typeof window?{textContent:""}:document.createElement("style");return"undefined"!=typeof document&&document.head.appendChild(e),this._tag=e,e}},e.prototype._styleUpdated=function(){var e=this,t=this._freeStyle.changeId,n=this._lastFreeStyleChangeId;(this._pendingRawChange||t!==n)&&(this._lastFreeStyleChangeId=t,this._pendingRawChange=!1,this._afterAllSync((function(){return e.forceRenderStyles()})))},e.prototype.style=function(){var e=this._freeStyle.registerStyle(r.convertToStyles(i.extend.apply(void 0,arguments)));return this._styleUpdated(),e},e}();t.TypeStyle=a},51833:(e,t)=>{"use strict";function n(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var s={},r=0,i=e;r<i.length;r++){var o=i[r];if(null!=o&&!1!==o)for(var a in o){var l=o[a];(l||0===l)&&("$nest"===a&&l?s[a]=s.$nest?n(s.$nest,l):l:-1!==a.indexOf("&")||0===a.indexOf("@media")?s[a]=s[a]?n(s[a],l):l:s[a]=l)}}return s}Object.defineProperty(t,"__esModule",{value:!0}),t.raf="undefined"==typeof requestAnimationFrame?function(e){return setTimeout(e)}:"undefined"==typeof window?requestAnimationFrame:requestAnimationFrame.bind(window),t.classes=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.map((function(e){return e&&"object"==typeof e?Object.keys(e).map((function(t){return!!e[t]&&t})):[e]})).reduce((function(e,t){return e.concat(t)}),[]).filter((function(e){return!!e})).join(" ")},t.extend=n,t.media=function(e){for(var t,r=[],i=1;i<arguments.length;i++)r[i-1]=arguments[i];var o=[];return e.type&&o.push(e.type),e.orientation&&o.push("(orientation: "+e.orientation+")"),e.minWidth&&o.push("(min-width: "+s(e.minWidth)+")"),e.maxWidth&&o.push("(max-width: "+s(e.maxWidth)+")"),e.minHeight&&o.push("(min-height: "+s(e.minHeight)+")"),e.maxHeight&&o.push("(max-height: "+s(e.maxHeight)+")"),e.prefersColorScheme&&o.push("(prefers-color-scheme: "+e.prefersColorScheme+")"),{$nest:(t={},t["@media "+o.join(" and ")]=n.apply(void 0,r),t)}};var s=function(e){return"string"==typeof e?e:e+"px"}},66720:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})}}]);
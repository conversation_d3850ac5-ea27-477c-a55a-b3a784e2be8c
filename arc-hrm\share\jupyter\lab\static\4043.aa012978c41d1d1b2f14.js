"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[4043],{14043:(e,t,l)=>{l.r(t);l.d(t,{StyleModule:()=>r});const s="ͼ";const o=typeof Symbol=="undefined"?"__"+s:Symbol.for(s);const i=typeof Symbol=="undefined"?"__styleSet"+Math.floor(Math.random()*1e8):Symbol("styleSet");const n=typeof globalThis!="undefined"?globalThis:typeof window!="undefined"?window:{};class r{constructor(e,t){this.rules=[];let{finish:l}=t||{};function s(e){return/^@/.test(e)?[e]:e.split(/,\s*/)}function o(e,t,i,n){let r=[],h=/^@(\w+)\b/.exec(e[0]),u=h&&h[1]=="keyframes";if(h&&t==null)return i.push(e[0]+";");for(let l in t){let n=t[l];if(/&/.test(l)){o(l.split(/,\s*/).map((t=>e.map((e=>t.replace(/&/,e))))).reduce(((e,t)=>e.concat(t))),n,i)}else if(n&&typeof n=="object"){if(!h)throw new RangeError("The value of a property ("+l+") should be a primitive value.");o(s(l),n,r,u)}else if(n!=null){r.push(l.replace(/_.*/,"").replace(/[A-Z]/g,(e=>"-"+e.toLowerCase()))+": "+n+";")}}if(r.length||u){i.push((l&&!h&&!n?e.map(l):e).join(", ")+" {"+r.join(" ")+"}")}}for(let i in e)o(s(i),e[i],this.rules)}getRules(){return this.rules.join("\n")}static newName(){let e=n[o]||1;n[o]=e+1;return s+e.toString(36)}static mount(e,t){(e[i]||new u(e)).mount(Array.isArray(t)?t:[t])}}let h=null;class u{constructor(e){if(!e.head&&e.adoptedStyleSheets&&typeof CSSStyleSheet!="undefined"){if(h){e.adoptedStyleSheets=[h.sheet].concat(e.adoptedStyleSheets);return e[i]=h}this.sheet=new CSSStyleSheet;e.adoptedStyleSheets=[this.sheet].concat(e.adoptedStyleSheets);h=this}else{this.styleTag=(e.ownerDocument||e).createElement("style");let t=e.head||e;t.insertBefore(this.styleTag,t.firstChild)}this.modules=[];e[i]=this}mount(e){let t=this.sheet;let l=0,s=0;for(let o=0;o<e.length;o++){let i=e[o],n=this.modules.indexOf(i);if(n<s&&n>-1){this.modules.splice(n,1);s--;n=-1}if(n==-1){this.modules.splice(s++,0,i);if(t)for(let e=0;e<i.rules.length;e++)t.insertRule(i.rules[e],l++)}else{while(s<n)l+=this.modules[s++].rules.length;l+=i.rules.length;s++}}if(!t){let e="";for(let t=0;t<this.modules.length;t++)e+=this.modules[t].getRules()+"\n";this.styleTag.textContent=e}}}}}]);
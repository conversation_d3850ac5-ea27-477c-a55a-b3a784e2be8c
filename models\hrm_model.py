import torch
import torch.nn as nn
import torch.nn.functional as F

class HierarchicalTransformerBlock(nn.Module):
    """Bloc Transformer optimisé pour le HRM avec RMSNorm et GLU"""
    def __init__(self, dim, n_heads):
        super().__init__()
        # Attention avec Rotary Positional Embedding (simplifié ici)
        self.attention = nn.MultiheadAttention(dim, n_heads, batch_first=True)
        
        # Gated Linear Unit (GLU) comme dans Llama
        self.glu = nn.Sequential(
            nn.Linear(dim, dim * 2),
            nn.GLU()
        )
        
        # RMSNorm au lieu de LayerNorm (on utilise LayerNorm standard pour la simplicité)
        self.rms_norm = nn.LayerNorm(dim, elementwise_affine=True)
        
    def forward(self, x):
        # Attention
        attn_out, _ = self.attention(x, x, x)
        x = self.rms_norm(x + attn_out)
        
        # GLU
        glu_out = self.glu(x)
        x = self.rms_norm(x + glu_out)
        return x

class GridToProgramHRM(nn.Module):

    """Véritable implémentation HRM avec modules hiérarchiques et ACT"""
    def __init__(self, model_dim, n_heads, grammar_vocab_size, max_grid_size=900, N_cycles=3, T_steps=5):
        super().__init__()
        self.model_dim = model_dim
        self.N = N_cycles
        self.T = T_steps
        # 1. Encodage de la grille
        self.cell_embedding = nn.Embedding(10, model_dim)
        self.grid_pos_embedding = nn.Embedding(max_grid_size, model_dim)
        
        # 2. Modules HRM (H et L)
        self.H_module = HierarchicalTransformerBlock(model_dim, n_heads)
        self.L_module = HierarchicalTransformerBlock(model_dim, n_heads)

        # Projection pour réduire la dimension concaténée (3*model_dim -> model_dim)
        self.l_input_projection = nn.Linear(3 * model_dim, model_dim)
        self.h_input_projection = nn.Linear(2 * model_dim, model_dim)
        
        # 3. Mécanisme ACT (Adaptive Computation Time)
        self.halt_head = nn.Sequential(
            nn.Linear(model_dim, 32),
            nn.GELU(),
            nn.Linear(32, 2)  # [halt_prob, continue_prob]
        )
        
        # 4. Décodeur de programme
        self.program_embedding = nn.Embedding(grammar_vocab_size, model_dim)
        self.decoder_lstm = nn.LSTM(model_dim, model_dim, batch_first=True)
        self.output_head = nn.Linear(model_dim, grammar_vocab_size)
        
        # 5. États initiaux apprenables
        self.z_H_init = nn.Parameter(torch.randn(1, 1, model_dim))
        self.z_L_init = nn.Parameter(torch.randn(1, 1, model_dim))
        
        print(f"HRM initialisé avec N={N_cycles} cycles, T={T_steps} étapes")

    def hierarchical_reasoning(self, grid_emb):
        """Processus HRM avec convergence hiérarchique"""
        batch_size, grid_len, _ = grid_emb.shape
        
        # États initiaux
        z_H = self.z_H_init.repeat(batch_size, 1, 1)
        z_L = self.z_L_init.repeat(batch_size, 1, 1)
        
        # Boucle sur N cycles
        for cycle in range(self.N):
            # Boucle sur T étapes (module L rapide)
            for step in range(self.T):
                # Entrée combinée pour le module L
                l_input = torch.cat([
                    z_L.repeat(1, grid_len, 1),
                    z_H.repeat(1, grid_len, 1),
                    grid_emb
                ], dim=-1)

                # Projection pour réduire la dimension
                l_input = self.l_input_projection(l_input)

                # Mise à jour de l'état L
                z_L = self.L_module(l_input.mean(dim=1, keepdim=True))
            
            # Mise à jour de l'état H (seulement en fin de cycle)
            h_input = torch.cat([z_H, z_L], dim=-1)
            h_input = self.h_input_projection(h_input)
            z_H = self.H_module(h_input)
            
            # Réinitialisation partielle de L pour le prochain cycle
            z_L = z_L.detach() * 0.5  # Réduction d'inertie
        
        return z_H

    def forward(self, grid, program_tokens=None, max_segments=5):
        # 1. Encodage de la grille
        batch_size, height, width = grid.shape
        flat_grid = grid.view(batch_size, -1)
        grid_pos = torch.arange(height*width, device=grid.device).unsqueeze(0)
        grid_emb = self.cell_embedding(flat_grid) + self.grid_pos_embedding(grid_pos)
        
        # 2. Raisonnement hiérarchique
        hrm_state = self.hierarchical_reasoning(grid_emb)
        
        # 3. Adaptive Computation Time
        segments = []
        halt_decisions = []
        
        # Si on est en mode entraînement, on utilise les tokens du programme (teacher forcing)
        if program_tokens is not None:
            # Génération du programme segment par segment
            for seg in range(max_segments):
                # Décision d'arrêt
                halt_logits = self.halt_head(hrm_state.squeeze(1))
                halt_decisions.append(halt_logits)
                
                # Génération du programme pour ce segment (teacher forcing)
                prog_emb = self.program_embedding(program_tokens)
                output, _ = self.decoder_lstm(prog_emb, (hrm_state, hrm_state))
                logits = self.output_head(output)
                segments.append(logits)
                
                # Condition d'arrêt pendant l'entraînement: on génère toujours max_segments segments
        else:
            # Mode inférence: génération token par token
            # Condition d'arrêt (pendant l'inférence)
            for seg in range(max_segments):
                # Décision d'arrêt
                halt_logits = self.halt_head(hrm_state.squeeze(1))
                halt_decisions.append(halt_logits)
                if torch.argmax(halt_logits) == 0 and seg > 0:
                    break
            pass
        
        return segments, halt_decisions
(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[5649],{5649:(e,t,r)=>{"use strict";r.r(t),r.d(t,{BaseSettings:()=>D,DefaultSchemaValidator:()=>p,ISettingRegistry:()=>v,SettingRegistry:()=>m,Settings:()=>y});var n=r(32895),s=r(20998),u=r(2549),a=r(81997),o=r(47101),i=r.n(o),c=r(60850);const d=JSON.parse('{"$schema":"http://json-schema.org/draft-07/schema","title":"JupyterLab Plugin Settings/Preferences Schema","description":"JupyterLab plugin settings/preferences schema","version":"1.0.0","type":"object","additionalProperties":true,"properties":{"jupyter.lab.internationalization":{"type":"object","properties":{"selectors":{"type":"array","items":{"type":"string","minLength":1}},"domain":{"type":"string","minLength":1}}},"jupyter.lab.menus":{"type":"object","properties":{"main":{"title":"Main menu entries","description":"List of menu items to add to the main menubar.","items":{"$ref":"#/definitions/menu"},"type":"array","default":[]},"context":{"title":"The application context menu.","description":"List of context menu items.","items":{"allOf":[{"$ref":"#/definitions/menuItem"},{"properties":{"selector":{"description":"The CSS selector for the context menu item.","type":"string"}}}]},"type":"array","default":[]}},"additionalProperties":false},"jupyter.lab.metadataforms":{"items":{"$ref":"#/definitions/metadataForm"},"type":"array","default":[]},"jupyter.lab.setting-deprecated":{"type":"boolean","default":false},"jupyter.lab.setting-icon":{"type":"string","default":""},"jupyter.lab.setting-icon-class":{"type":"string","default":""},"jupyter.lab.setting-icon-label":{"type":"string","default":"Plugin"},"jupyter.lab.shortcuts":{"items":{"$ref":"#/definitions/shortcut"},"type":"array","default":[]},"jupyter.lab.toolbars":{"properties":{"^\\\\w[\\\\w-\\\\.]*$":{"items":{"$ref":"#/definitions/toolbarItem"},"type":"array","default":[]}},"type":"object","default":{}},"jupyter.lab.transform":{"type":"boolean","default":false}},"definitions":{"menu":{"properties":{"disabled":{"description":"Whether the menu is disabled or not","type":"boolean","default":false},"icon":{"description":"Menu icon id","type":"string"},"id":{"description":"Menu unique id","oneOf":[{"type":"string","enum":["jp-menu-file","jp-menu-file-new","jp-menu-edit","jp-menu-help","jp-menu-kernel","jp-menu-run","jp-menu-settings","jp-menu-view","jp-menu-tabs"]},{"type":"string","pattern":"[a-z][a-z0-9\\\\-_]+"}]},"items":{"description":"Menu items","type":"array","items":{"$ref":"#/definitions/menuItem"}},"label":{"description":"Menu label","type":"string"},"mnemonic":{"description":"Mnemonic index for the label","type":"number","minimum":-1,"default":-1},"rank":{"description":"Menu rank","type":"number","minimum":0}},"required":["id"],"type":"object"},"menuItem":{"properties":{"args":{"description":"Command arguments","type":"object"},"command":{"description":"Command id","type":"string"},"disabled":{"description":"Whether the item is disabled or not","type":"boolean","default":false},"type":{"description":"Item type","type":"string","enum":["command","submenu","separator"],"default":"command"},"rank":{"description":"Item rank","type":"number","minimum":0},"submenu":{"oneOf":[{"$ref":"#/definitions/menu"},{"type":"null"}]}},"type":"object"},"shortcut":{"properties":{"args":{"title":"The arguments for the command","type":"object"},"command":{"title":"The command id","description":"The command executed when the binding is matched.","type":"string"},"disabled":{"description":"Whether this shortcut is disabled or not.","type":"boolean","default":false},"keys":{"title":"The key sequence for the binding","description":"The key shortcut like `Accel A` or the sequence of shortcuts to press like [`Accel A`, `B`]","items":{"type":"string"},"type":"array"},"macKeys":{"title":"The key sequence for the binding on macOS","description":"The key shortcut like `Cmd A` or the sequence of shortcuts to press like [`Cmd A`, `B`]","items":{"type":"string"},"type":"array"},"winKeys":{"title":"The key sequence for the binding on Windows","description":"The key shortcut like `Ctrl A` or the sequence of shortcuts to press like [`Ctrl A`, `B`]","items":{"type":"string"},"type":"array"},"linuxKeys":{"title":"The key sequence for the binding on Linux","description":"The key shortcut like `Ctrl A` or the sequence of shortcuts to press like [`Ctrl A`, `B`]","items":{"type":"string"},"type":"array"},"selector":{"title":"CSS selector","type":"string"}},"required":["command","keys","selector"],"type":"object"},"toolbarItem":{"properties":{"name":{"title":"Unique name","type":"string"},"args":{"title":"Command arguments","type":"object"},"command":{"title":"Command id","type":"string","default":""},"disabled":{"title":"Whether the item is ignored or not","type":"boolean","default":false},"icon":{"title":"Item icon id","description":"If defined, it will override the command icon","type":"string"},"label":{"title":"Item label","description":"If defined, it will override the command label","type":"string"},"caption":{"title":"Item caption","description":"If defined, it will override the command caption","type":"string"},"type":{"title":"Item type","type":"string","enum":["command","spacer"]},"rank":{"title":"Item rank","type":"number","minimum":0,"default":50}},"required":["name"],"additionalProperties":false,"type":"object"},"metadataForm":{"type":"object","properties":{"id":{"type":"string","description":"The section ID"},"metadataSchema":{"type":"object","items":{"$ref":"#/definitions/metadataSchema"}},"uiSchema":{"type":"object"},"metadataOptions":{"type":"object","items":{"$ref":"#/definitions/metadataOptions"}},"label":{"type":"string","description":"The section label"},"rank":{"type":"integer","description":"The rank of the section in the right panel"},"showModified":{"type":"boolean","description":"Whether to show modified values from defaults"}},"required":["id","metadataSchema"]},"metadataSchema":{"properties":{"properties":{"type":"object","description":"The property set up by extension","properties":{"title":{"type":"string"},"description":{"type":"string"},"type":{"type":"string"}}}},"type":"object","required":["properties"]},"metadataOptions":{"properties":{"customRenderer":{"type":"string"},"metadataLevel":{"type":"string","enum":["cell","notebook"],"default":"cell"},"cellTypes":{"type":"array","items":{"type":"string","enum":["code","markdown","raw"]}},"writeDefault":{"type":"boolean"}},"type":"object"}}}'),l=s.JSONExt.deepCopy,f={strict:!1},h=String.fromCharCode(30);class p{constructor(){this._composer=new(i())({useDefaults:!0,...f}),this._validator=new(i())({...f}),this._composer.addSchema(d,"jupyterlab-plugin-schema"),this._validator.addSchema(d,"jupyterlab-plugin-schema")}validateData(e,t=!0){const r=this._validator.getSchema(e.id),n=this._composer.getSchema(e.id);if(!r||!n)return"object"!==e.schema.type?[{instancePath:"type",keyword:"schema",schemaPath:"",message:`Setting registry schemas' root-level type must be 'object', rejecting type: ${e.schema.type}`}]:this._addSchema(e.id,e.schema)||this.validateData(e);let s;try{s=c.parse(e.raw)}catch(e){if(e instanceof SyntaxError)return[{instancePath:"",keyword:"syntax",schemaPath:"",message:e.message}];const{column:t,description:r}=e;return[{instancePath:"",keyword:"parse",schemaPath:"",message:`${r} (line ${e.lineNumber} column ${t})`}]}if(!r(s))return r.errors;const u=l(s);return n(u)?(t&&(e.data={composite:u,user:s}),null):n.errors}_addSchema(e,t){const r=this._composer,n=this._validator,s=n.getSchema("jupyterlab-plugin-schema");return s(t)?n.validateSchema(t)?(r.removeSchema(e),n.removeSchema(e),r.addSchema(t,e),n.addSchema(t,e),null):n.errors:s.errors}}class m{constructor(e){this.schema=d,this.plugins=Object.create(null),this._pluginChanged=new a.Signal(this),this._ready=Promise.resolve(),this._transformers=Object.create(null),this._unloadedPlugins=new Map,this.connector=e.connector,this.validator=e.validator||new p,e.plugins&&(e.plugins.filter((e=>e.schema["jupyter.lab.transform"])).forEach((e=>this._unloadedPlugins.set(e.id,e))),this._ready=this._preload(e.plugins))}get pluginChanged(){return this._pluginChanged}async get(e,t){await this._ready;const r=this.plugins;if(e in r){const{composite:n,user:s}=r[e].data;return{composite:void 0!==n[t]?l(n[t]):void 0,user:void 0!==s[t]?l(s[t]):void 0}}return this.load(e).then((()=>this.get(e,t)))}async load(e,t=!1){await this._ready;const r=this.plugins;return e in r?(t&&(r[e].data={composite:{},user:{}},await this._load(await this._transform("fetch",r[e])),this._pluginChanged.emit(e)),new y({plugin:r[e],registry:this})):this._unloadedPlugins.has(e)&&e in this._transformers&&(await this._load(await this._transform("fetch",this._unloadedPlugins.get(e))),e in r)?(this._pluginChanged.emit(e),this._unloadedPlugins.delete(e),new y({plugin:r[e],registry:this})):this.reload(e)}async reload(e){await this._ready;const t=await this.connector.fetch(e),r=this.plugins;if(void 0===t)throw[{instancePath:"",keyword:"id",message:`Could not fetch settings for ${e}.`,schemaPath:""}];return await this._load(await this._transform("fetch",t)),this._pluginChanged.emit(e),new y({plugin:r[e],registry:this})}async remove(e,t){await this._ready;const r=this.plugins;if(!(e in r))return;const n=c.parse(r[e].raw);return delete n[t],delete n[`// ${t}`],r[e].raw=g.annotatedPlugin(r[e],n),this._save(e)}async set(e,t,r){await this._ready;const n=this.plugins;if(!(e in n))return this.load(e).then((()=>this.set(e,t,r)));const s=c.parse(n[e].raw);return n[e].raw=g.annotatedPlugin(n[e],{...s,[t]:r}),this._save(e)}transform(e,t){const r=this._transformers;if(e in r){const t=new Error(`${e} already has a transformer.`);throw t.name="TransformError",t}return r[e]={fetch:t.fetch||(e=>e),compose:t.compose||(e=>e)},new u.DisposableDelegate((()=>{delete r[e]}))}async upload(e,t){await this._ready;const r=this.plugins;return e in r?(r[e].raw=t,this._save(e)):this.load(e).then((()=>this.upload(e,t)))}async _load(e){const t=e.id;try{await this._validate(e)}catch(e){const r=[`Validating ${t} failed:`];throw e.forEach(((e,t)=>{const{instancePath:n,schemaPath:s,keyword:u,message:a}=e;(n||s)&&r.push(`${t} - schema @ ${s}, data @ ${n}`),r.push(`{${u}} ${a}`)})),console.warn(r.join("\n")),e}}async _preload(e){await Promise.all(e.map((async e=>{var t;try{await this._load(await this._transform("fetch",e))}catch(e){"unset"!==(null===(t=e[0])||void 0===t?void 0:t.keyword)&&console.warn("Ignored setting registry preload errors.",e)}})))}async _save(e){const t=this.plugins;if(!(e in t))throw new Error(`${e} does not exist in setting registry.`);try{await this._validate(t[e])}catch(t){throw console.warn(`${e} validation errors:`,t),new Error(`${e} failed to validate; check console.`)}await this.connector.save(e,t[e].raw);const r=await this.connector.fetch(e);if(void 0===r)throw[{instancePath:"",keyword:"id",message:`Could not fetch settings for ${e}.`,schemaPath:""}];await this._load(await this._transform("fetch",r)),this._pluginChanged.emit(e)}async _transform(e,t){const r=t.id,n=this._transformers;if(!t.schema["jupyter.lab.transform"])return t;if(r in n){const s=n[r][e].call(null,t);if(s.id!==r)throw[{instancePath:"",keyword:"id",message:"Plugin transformations cannot change plugin IDs.",schemaPath:""}];return s}throw[{instancePath:"",keyword:"unset",message:`${t.id} has no transformers yet.`,schemaPath:""}]}async _validate(e){const t=this.validator.validateData(e);if(t)throw t;this.plugins[e.id]=await this._transform("compose",e)}}class D{constructor(e){this._schema=e.schema}get schema(){return this._schema}isDefault(e){for(const t in this.schema.properties){const r=e[t],n=this.default(t);if(void 0!==r&&void 0!==n&&!s.JSONExt.deepEqual(r,s.JSONExt.emptyObject)&&!s.JSONExt.deepEqual(r,s.JSONExt.emptyArray)&&!s.JSONExt.deepEqual(r,n))return!1}return!0}default(e){return g.reifyDefault(this.schema,e)}}class y extends D{constructor(e){super({schema:e.plugin.schema}),this._changed=new a.Signal(this),this._isDisposed=!1,this.id=e.plugin.id,this.registry=e.registry,this.registry.pluginChanged.connect(this._onPluginChanged,this)}get changed(){return this._changed}get composite(){return this.plugin.data.composite}get isDisposed(){return this._isDisposed}get plugin(){return this.registry.plugins[this.id]}get raw(){return this.plugin.raw}get isModified(){return!this.isDefault(this.user)}get user(){return this.plugin.data.user}get version(){return this.plugin.version}annotatedDefaults(){return g.annotatedDefaults(this.schema,this.id)}dispose(){this._isDisposed||(this._isDisposed=!0,a.Signal.clearData(this))}get(e){const{composite:t,user:r}=this;return{composite:void 0!==t[e]?l(t[e]):void 0,user:void 0!==r[e]?l(r[e]):void 0}}remove(e){return this.registry.remove(this.plugin.id,e)}save(e){return this.registry.upload(this.plugin.id,e)}set(e,t){return this.registry.set(this.plugin.id,e,t)}validate(e){const{id:t,schema:r}=this.plugin,n=this.registry.validator,s=this.version;return n.validateData({data:{composite:{},user:{}},id:t,raw:e,schema:r,version:s},!1)}_onPluginChanged(e,t){t===this.plugin.id&&this._changed.emit(void 0)}}var g;!function(e){function t(e,t,n=!1,u=!0){if(!e)return t&&u?s.JSONExt.deepCopy(t):[];if(!t)return s.JSONExt.deepCopy(e);const a=s.JSONExt.deepCopy(e);return t.forEach((e=>{const t=a.findIndex((t=>t.id===e.id));t>=0?a[t]={...a[t],...e,items:r(a[t].items,e.items,n,u)}:u&&a.push(e)})),a}function r(e,r,n=!1,u=!0){if(!e)return r?s.JSONExt.deepCopy(r):void 0;if(!r)return s.JSONExt.deepCopy(e);const a=s.JSONExt.deepCopy(e);return r.forEach((e=>{var r;switch(null!==(r=e.type)&&void 0!==r?r:"command"){case"separator":u&&a.push({...e});break;case"submenu":if(e.submenu){const r=a.findIndex((t=>{var r,n;return"submenu"===t.type&&(null===(r=t.submenu)||void 0===r?void 0:r.id)===(null===(n=e.submenu)||void 0===n?void 0:n.id)}));r<0?u&&a.push(s.JSONExt.deepCopy(e)):a[r]={...a[r],...e,submenu:t(a[r].submenu?[a[r].submenu]:null,[e.submenu],n,u)[0]}}break;case"command":if(e.command){const t=a.findIndex((t=>{var r,n;return t.command===e.command&&t.selector===e.selector&&s.JSONExt.deepEqual(null!==(r=t.args)&&void 0!==r?r:{},null!==(n=e.args)&&void 0!==n?n:{})}));t<0?u&&a.push({...e}):(n&&console.warn(`Menu entry for command '${e.command}' is duplicated.`),a[t]={...a[t],...e})}}})),a}e.reconcileMenus=t,e.reconcileItems=r,e.filterDisabledItems=function e(t){return t.reduce(((t,r)=>{var n;const s={...r};if(!s.disabled){if("submenu"===s.type){const{submenu:t}=s;t&&!t.disabled&&(s.submenu={...t,items:e(null!==(n=t.items)&&void 0!==n?n:[])})}t.push(s)}return t}),[])},e.reconcileShortcuts=function(e,t){const r={};return t=t.filter((e=>{const t=n.CommandRegistry.normalizeKeys(e).join(h);if(!t)return console.warn("Skipping this shortcut because there are no actionable keys on this platform",e),!1;t in r||(r[t]={});const{selector:s}=e;return s in r[t]?(console.warn("Skipping this shortcut because it collides with another shortcut.",e),!1):(r[t][s]=!1,!0)})),e=[...e.filter((e=>!!e.disabled)),...e.filter((e=>!e.disabled))].filter((e=>{const t=n.CommandRegistry.normalizeKeys(e).join(h);if(!t)return!1;t in r||(r[t]={});const{disabled:s,selector:u}=e;return u in r[t]?(r[t][u]&&console.warn("Skipping this default shortcut because it collides with another default shortcut.",e),!1):(r[t][u]=!s,!0)})),g.upgradeShortcuts(t.concat(e).filter((e=>!e.disabled)).map((e=>({args:{},...e}))))},e.reconcileToolbarItems=function(e,t,r=!1){if(!e)return t?s.JSONExt.deepCopy(t):void 0;if(!t)return s.JSONExt.deepCopy(e);const n=s.JSONExt.deepCopy(e);return t.forEach((e=>{const t=n.findIndex((t=>t.name===e.name));t<0?n.push({...e}):(r&&s.JSONExt.deepEqual(Object.keys(e),Object.keys(n[t]))&&console.warn(`Toolbar item '${e.name}' is duplicated.`),n[t]={...n[t],...e})})),n}}(m||(m={})),function(e){const t="    ",r="[missing schema description]",n="[missing schema title]";function u(e){return e.reduce(((t,r,n)=>{const s=r.split("\n");return t+r+(0===s[s.length-1].trim().indexOf("//")||n===e.length-1?"":",")+(n===e.length-1?"":"\n\n")}),"")}function a(e,r=`${t}// `){return r+e.split("\n").join(`\n${r}`)}function o(e,t,r,n){var u,a,i,c,d,l,f;if(r=null!=r?r:e.definitions,n=t?e.required instanceof Array&&(null===(u=e.required)||void 0===u?void 0:u.includes(t)):n,"object"===(e=(t?null===(a=e.properties)||void 0===a?void 0:a[t]:e)||{}).type){const t=s.JSONExt.deepCopy(e.default),n=e.properties||{};for(const s in n)t[s]=o(n[s],void 0,r,e.required instanceof Array&&(null===(i=e.required)||void 0===i?void 0:i.includes(s)));return t}if("array"===e.type){const t=void 0!==e.default;if(!t&&!n)return;const u=t?s.JSONExt.deepCopy(e.default):[];let a=e.items||{};if(a.$ref&&r){const e=a.$ref.replace("#/definitions/","");a=null!==(c=r[e])&&void 0!==c?c:{}}for(const e in u)if("object"===a.type){const t=null!==(l=null!==(d=o(a,void 0,r))&&void 0!==d?d:u[e])&&void 0!==l?l:{};for(const r in t)(null===(f=u[e])||void 0===f?void 0:f[r])&&(t[r]=u[e][r]);u[e]=t}return u}return e.default}e.annotatedDefaults=function(e,s){const{description:i,properties:c,title:d}=e,l=c?Object.keys(c).sort(((e,t)=>e.localeCompare(t))):[],f=Math.max((i||r).length,s.length);return["{",a(`${d||n}`),a(s),a(i||r),a("*".repeat(f)),"",u(l.map((n=>function(e,n){const s=e.properties&&e.properties[n]||{},u=s.type,i=s.description||r,c=s.title||"",d=o(e,n),l=void 0!==d?a(`"${n}": ${JSON.stringify(d,null,4)}`,t):a(`"${n}": ${u}`);return[a(c),a(i),l].filter((e=>e.length)).join("\n")}(e,n)))),"}"].join("\n")},e.annotatedPlugin=function(e,s){const{description:o,title:i}=e.schema,c=Object.keys(s).sort(((e,t)=>e.localeCompare(t))),d=Math.max((o||r).length,e.id.length);return["{",a(`${i||n}`),a(e.id),a(o||r),a("*".repeat(d)),"",u(c.map((u=>function(e,s,u){const o=e.properties&&e.properties[s],i=o&&o.description||r,c=o&&o.title||n,d=a(`"${s}": ${JSON.stringify(u,null,4)}`,t);return[a(c),a(i),d].join("\n")}(e.schema,u,s[u])))),"}"].join("\n")},e.reifyDefault=o;const i=new Set;e.upgradeShortcuts=function(e){const t=new Set,r=[{old:".jp-Notebook:focus.jp-mod-commandMode",new:".jp-Notebook.jp-mod-commandMode:not(.jp-mod-readWrite) :focus",versionDeprecated:"JupyterLab 4.1"},{old:".jp-Notebook.jp-mod-commandMode :focus:not(:read-write)",new:".jp-Notebook.jp-mod-commandMode:not(.jp-mod-readWrite) :focus",versionDeprecated:"JupyterLab 4.1.1"},{old:".jp-Notebook:focus",new:".jp-Notebook.jp-mod-commandMode:not(.jp-mod-readWrite) :focus",versionDeprecated:"JupyterLab 4.1"},{old:"[data-jp-traversable]:focus",new:".jp-Notebook.jp-mod-commandMode:not(.jp-mod-readWrite) :focus",versionDeprecated:"JupyterLab 4.1"},{old:"[data-jp-kernel-user]:focus",new:"[data-jp-kernel-user]:not(.jp-mod-readWrite) :focus:not(:read-write)",versionDeprecated:"JupyterLab 4.1"},{old:"[data-jp-kernel-user] :focus:not(:read-write)",new:"[data-jp-kernel-user]:not(.jp-mod-readWrite) :focus:not(:read-write)",versionDeprecated:"JupyterLab 4.1.1"}],n=e.map((e=>{const n=e.selector;let s=n;for(const e of r)n.includes(e.old)&&(s=n.replace(e.old,e.new),i.has(n)||(t.add(`"${e.old}" was replaced with "${e.new}" in ${e.versionDeprecated} (present in "${n}")`),i.add(n)));return e.selector=s,e}));return t.size>0&&console.warn("Deprecated shortcut selectors: "+[...t].join("\n")+"\n\nThe selectors will be substituted transparently this time, but need to be updated at source before next major release."),n}}(g||(g={}));const v=new s.Token("@jupyterlab/coreutils:ISettingRegistry",'A service for the JupyterLab settings system.\n  Use this if you want to store settings for your application.\n  See "schemaDir" for more information.')},47101:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MissingRefError=t.ValidationError=t.CodeGen=t.Name=t.nil=t.stringify=t.str=t._=t.KeywordCxt=void 0;const n=r(42089),s=r(43662),u=r(60867),a=r(61327),o=["/properties"],i="http://json-schema.org/draft-07/schema";class c extends n.default{_addVocabularies(){super._addVocabularies(),s.default.forEach((e=>this.addVocabulary(e))),this.opts.discriminator&&this.addKeyword(u.default)}_addDefaultMetaSchema(){if(super._addDefaultMetaSchema(),!this.opts.meta)return;const e=this.opts.$data?this.$dataMetaSchema(a,o):a;this.addMetaSchema(e,i,!1),this.refs["http://json-schema.org/schema"]=i}defaultMeta(){return this.opts.defaultMeta=super.defaultMeta()||(this.getSchema(i)?i:void 0)}}e.exports=t=c,Object.defineProperty(t,"__esModule",{value:!0}),t.default=c;var d=r(58941);Object.defineProperty(t,"KeywordCxt",{enumerable:!0,get:function(){return d.KeywordCxt}});var l=r(78380);Object.defineProperty(t,"_",{enumerable:!0,get:function(){return l._}}),Object.defineProperty(t,"str",{enumerable:!0,get:function(){return l.str}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return l.stringify}}),Object.defineProperty(t,"nil",{enumerable:!0,get:function(){return l.nil}}),Object.defineProperty(t,"Name",{enumerable:!0,get:function(){return l.Name}}),Object.defineProperty(t,"CodeGen",{enumerable:!0,get:function(){return l.CodeGen}});var f=r(52582);Object.defineProperty(t,"ValidationError",{enumerable:!0,get:function(){return f.default}});var h=r(61326);Object.defineProperty(t,"MissingRefError",{enumerable:!0,get:function(){return h.default}})},25875:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.regexpCode=t.getEsmExportName=t.getProperty=t.safeStringify=t.stringify=t.strConcat=t.addCodeArg=t.str=t._=t.nil=t._Code=t.Name=t.IDENTIFIER=t._CodeOrName=void 0;class r{}t._CodeOrName=r,t.IDENTIFIER=/^[a-z$_][a-z$_0-9]*$/i;class n extends r{constructor(e){if(super(),!t.IDENTIFIER.test(e))throw new Error("CodeGen: name must be a valid identifier");this.str=e}toString(){return this.str}emptyStr(){return!1}get names(){return{[this.str]:1}}}t.Name=n;class s extends r{constructor(e){super(),this._items="string"==typeof e?[e]:e}toString(){return this.str}emptyStr(){if(this._items.length>1)return!1;const e=this._items[0];return""===e||'""'===e}get str(){var e;return null!==(e=this._str)&&void 0!==e?e:this._str=this._items.reduce(((e,t)=>`${e}${t}`),"")}get names(){var e;return null!==(e=this._names)&&void 0!==e?e:this._names=this._items.reduce(((e,t)=>(t instanceof n&&(e[t.str]=(e[t.str]||0)+1),e)),{})}}function u(e,...t){const r=[e[0]];let n=0;for(;n<t.length;)i(r,t[n]),r.push(e[++n]);return new s(r)}t._Code=s,t.nil=new s(""),t._=u;const a=new s("+");function o(e,...t){const r=[d(e[0])];let n=0;for(;n<t.length;)r.push(a),i(r,t[n]),r.push(a,d(e[++n]));return function(e){let t=1;for(;t<e.length-1;){if(e[t]===a){const r=c(e[t-1],e[t+1]);if(void 0!==r){e.splice(t-1,3,r);continue}e[t++]="+"}t++}}(r),new s(r)}function i(e,t){var r;t instanceof s?e.push(...t._items):t instanceof n?e.push(t):e.push("number"==typeof(r=t)||"boolean"==typeof r||null===r?r:d(Array.isArray(r)?r.join(","):r))}function c(e,t){if('""'===t)return e;if('""'===e)return t;if("string"==typeof e){if(t instanceof n||'"'!==e[e.length-1])return;return"string"!=typeof t?`${e.slice(0,-1)}${t}"`:'"'===t[0]?e.slice(0,-1)+t.slice(1):void 0}return"string"!=typeof t||'"'!==t[0]||e instanceof n?void 0:`"${e}${t.slice(1)}`}function d(e){return JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")}t.str=o,t.addCodeArg=i,t.strConcat=function(e,t){return t.emptyStr()?e:e.emptyStr()?t:o`${e}${t}`},t.stringify=function(e){return new s(d(e))},t.safeStringify=d,t.getProperty=function(e){return"string"==typeof e&&t.IDENTIFIER.test(e)?new s(`.${e}`):u`[${e}]`},t.getEsmExportName=function(e){if("string"==typeof e&&t.IDENTIFIER.test(e))return new s(`${e}`);throw new Error(`CodeGen: invalid export name: ${e}, use explicit $id name mapping`)},t.regexpCode=function(e){return new s(e.toString())}},78380:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.or=t.and=t.not=t.CodeGen=t.operators=t.varKinds=t.ValueScopeName=t.ValueScope=t.Scope=t.Name=t.regexpCode=t.stringify=t.getProperty=t.nil=t.strConcat=t.str=t._=void 0;const n=r(25875),s=r(17686);var u=r(25875);Object.defineProperty(t,"_",{enumerable:!0,get:function(){return u._}}),Object.defineProperty(t,"str",{enumerable:!0,get:function(){return u.str}}),Object.defineProperty(t,"strConcat",{enumerable:!0,get:function(){return u.strConcat}}),Object.defineProperty(t,"nil",{enumerable:!0,get:function(){return u.nil}}),Object.defineProperty(t,"getProperty",{enumerable:!0,get:function(){return u.getProperty}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return u.stringify}}),Object.defineProperty(t,"regexpCode",{enumerable:!0,get:function(){return u.regexpCode}}),Object.defineProperty(t,"Name",{enumerable:!0,get:function(){return u.Name}});var a=r(17686);Object.defineProperty(t,"Scope",{enumerable:!0,get:function(){return a.Scope}}),Object.defineProperty(t,"ValueScope",{enumerable:!0,get:function(){return a.ValueScope}}),Object.defineProperty(t,"ValueScopeName",{enumerable:!0,get:function(){return a.ValueScopeName}}),Object.defineProperty(t,"varKinds",{enumerable:!0,get:function(){return a.varKinds}}),t.operators={GT:new n._Code(">"),GTE:new n._Code(">="),LT:new n._Code("<"),LTE:new n._Code("<="),EQ:new n._Code("==="),NEQ:new n._Code("!=="),NOT:new n._Code("!"),OR:new n._Code("||"),AND:new n._Code("&&"),ADD:new n._Code("+")};class o{optimizeNodes(){return this}optimizeNames(e,t){return this}}class i extends o{constructor(e,t,r){super(),this.varKind=e,this.name=t,this.rhs=r}render({es5:e,_n:t}){const r=e?s.varKinds.var:this.varKind,n=void 0===this.rhs?"":` = ${this.rhs}`;return`${r} ${this.name}${n};`+t}optimizeNames(e,t){if(e[this.name.str])return this.rhs&&(this.rhs=k(this.rhs,e,t)),this}get names(){return this.rhs instanceof n._CodeOrName?this.rhs.names:{}}}class c extends o{constructor(e,t,r){super(),this.lhs=e,this.rhs=t,this.sideEffects=r}render({_n:e}){return`${this.lhs} = ${this.rhs};`+e}optimizeNames(e,t){if(!(this.lhs instanceof n.Name)||e[this.lhs.str]||this.sideEffects)return this.rhs=k(this.rhs,e,t),this}get names(){return B(this.lhs instanceof n.Name?{}:{...this.lhs.names},this.rhs)}}class d extends c{constructor(e,t,r,n){super(e,r,n),this.op=t}render({_n:e}){return`${this.lhs} ${this.op}= ${this.rhs};`+e}}class l extends o{constructor(e){super(),this.label=e,this.names={}}render({_n:e}){return`${this.label}:`+e}}class f extends o{constructor(e){super(),this.label=e,this.names={}}render({_n:e}){return`break${this.label?` ${this.label}`:""};`+e}}class h extends o{constructor(e){super(),this.error=e}render({_n:e}){return`throw ${this.error};`+e}get names(){return this.error.names}}class p extends o{constructor(e){super(),this.code=e}render({_n:e}){return`${this.code};`+e}optimizeNodes(){return`${this.code}`?this:void 0}optimizeNames(e,t){return this.code=k(this.code,e,t),this}get names(){return this.code instanceof n._CodeOrName?this.code.names:{}}}class m extends o{constructor(e=[]){super(),this.nodes=e}render(e){return this.nodes.reduce(((t,r)=>t+r.render(e)),"")}optimizeNodes(){const{nodes:e}=this;let t=e.length;for(;t--;){const r=e[t].optimizeNodes();Array.isArray(r)?e.splice(t,1,...r):r?e[t]=r:e.splice(t,1)}return e.length>0?this:void 0}optimizeNames(e,t){const{nodes:r}=this;let n=r.length;for(;n--;){const s=r[n];s.optimizeNames(e,t)||(j(e,s.names),r.splice(n,1))}return r.length>0?this:void 0}get names(){return this.nodes.reduce(((e,t)=>P(e,t.names)),{})}}class D extends m{render(e){return"{"+e._n+super.render(e)+"}"+e._n}}class y extends m{}class g extends D{}g.kind="else";class v extends D{constructor(e,t){super(t),this.condition=e}render(e){let t=`if(${this.condition})`+super.render(e);return this.else&&(t+="else "+this.else.render(e)),t}optimizeNodes(){super.optimizeNodes();const e=this.condition;if(!0===e)return this.nodes;let t=this.else;if(t){const e=t.optimizeNodes();t=this.else=Array.isArray(e)?new g(e):e}return t?!1===e?t instanceof v?t:t.nodes:this.nodes.length?this:new v(N(e),t instanceof v?[t]:t.nodes):!1!==e&&this.nodes.length?this:void 0}optimizeNames(e,t){var r;if(this.else=null===(r=this.else)||void 0===r?void 0:r.optimizeNames(e,t),super.optimizeNames(e,t)||this.else)return this.condition=k(this.condition,e,t),this}get names(){const e=super.names;return B(e,this.condition),this.else&&P(e,this.else.names),e}}v.kind="if";class E extends D{}E.kind="for";class C extends E{constructor(e){super(),this.iteration=e}render(e){return`for(${this.iteration})`+super.render(e)}optimizeNames(e,t){if(super.optimizeNames(e,t))return this.iteration=k(this.iteration,e,t),this}get names(){return P(super.names,this.iteration.names)}}class $ extends E{constructor(e,t,r,n){super(),this.varKind=e,this.name=t,this.from=r,this.to=n}render(e){const t=e.es5?s.varKinds.var:this.varKind,{name:r,from:n,to:u}=this;return`for(${t} ${r}=${n}; ${r}<${u}; ${r}++)`+super.render(e)}get names(){const e=B(super.names,this.from);return B(e,this.to)}}class F extends E{constructor(e,t,r,n){super(),this.loop=e,this.varKind=t,this.name=r,this.iterable=n}render(e){return`for(${this.varKind} ${this.name} ${this.loop} ${this.iterable})`+super.render(e)}optimizeNames(e,t){if(super.optimizeNames(e,t))return this.iterable=k(this.iterable,e,t),this}get names(){return P(super.names,this.iterable.names)}}class A extends D{constructor(e,t,r){super(),this.name=e,this.args=t,this.async=r}render(e){return`${this.async?"async ":""}function ${this.name}(${this.args})`+super.render(e)}}A.kind="func";class w extends m{render(e){return"return "+super.render(e)}}w.kind="return";class _ extends D{render(e){let t="try"+super.render(e);return this.catch&&(t+=this.catch.render(e)),this.finally&&(t+=this.finally.render(e)),t}optimizeNodes(){var e,t;return super.optimizeNodes(),null===(e=this.catch)||void 0===e||e.optimizeNodes(),null===(t=this.finally)||void 0===t||t.optimizeNodes(),this}optimizeNames(e,t){var r,n;return super.optimizeNames(e,t),null===(r=this.catch)||void 0===r||r.optimizeNames(e,t),null===(n=this.finally)||void 0===n||n.optimizeNames(e,t),this}get names(){const e=super.names;return this.catch&&P(e,this.catch.names),this.finally&&P(e,this.finally.names),e}}class b extends D{constructor(e){super(),this.error=e}render(e){return`catch(${this.error})`+super.render(e)}}b.kind="catch";class S extends D{render(e){return"finally"+super.render(e)}}function P(e,t){for(const r in t)e[r]=(e[r]||0)+(t[r]||0);return e}function B(e,t){return t instanceof n._CodeOrName?P(e,t.names):e}function k(e,t,r){return e instanceof n.Name?u(e):(s=e)instanceof n._Code&&s._items.some((e=>e instanceof n.Name&&1===t[e.str]&&void 0!==r[e.str]))?new n._Code(e._items.reduce(((e,t)=>(t instanceof n.Name&&(t=u(t)),t instanceof n._Code?e.push(...t._items):e.push(t),e)),[])):e;var s;function u(e){const n=r[e.str];return void 0===n||1!==t[e.str]?e:(delete t[e.str],n)}}function j(e,t){for(const r in t)e[r]=(e[r]||0)-(t[r]||0)}function N(e){return"boolean"==typeof e||"number"==typeof e||null===e?!e:n._`!${T(e)}`}S.kind="finally",t.CodeGen=class{constructor(e,t={}){this._values={},this._blockStarts=[],this._constants={},this.opts={...t,_n:t.lines?"\n":""},this._extScope=e,this._scope=new s.Scope({parent:e}),this._nodes=[new y]}toString(){return this._root.render(this.opts)}name(e){return this._scope.name(e)}scopeName(e){return this._extScope.name(e)}scopeValue(e,t){const r=this._extScope.value(e,t);return(this._values[r.prefix]||(this._values[r.prefix]=new Set)).add(r),r}getScopeValue(e,t){return this._extScope.getValue(e,t)}scopeRefs(e){return this._extScope.scopeRefs(e,this._values)}scopeCode(){return this._extScope.scopeCode(this._values)}_def(e,t,r,n){const s=this._scope.toName(t);return void 0!==r&&n&&(this._constants[s.str]=r),this._leafNode(new i(e,s,r)),s}const(e,t,r){return this._def(s.varKinds.const,e,t,r)}let(e,t,r){return this._def(s.varKinds.let,e,t,r)}var(e,t,r){return this._def(s.varKinds.var,e,t,r)}assign(e,t,r){return this._leafNode(new c(e,t,r))}add(e,r){return this._leafNode(new d(e,t.operators.ADD,r))}code(e){return"function"==typeof e?e():e!==n.nil&&this._leafNode(new p(e)),this}object(...e){const t=["{"];for(const[r,s]of e)t.length>1&&t.push(","),t.push(r),(r!==s||this.opts.es5)&&(t.push(":"),(0,n.addCodeArg)(t,s));return t.push("}"),new n._Code(t)}if(e,t,r){if(this._blockNode(new v(e)),t&&r)this.code(t).else().code(r).endIf();else if(t)this.code(t).endIf();else if(r)throw new Error('CodeGen: "else" body without "then" body');return this}elseIf(e){return this._elseNode(new v(e))}else(){return this._elseNode(new g)}endIf(){return this._endBlockNode(v,g)}_for(e,t){return this._blockNode(e),t&&this.code(t).endFor(),this}for(e,t){return this._for(new C(e),t)}forRange(e,t,r,n,u=(this.opts.es5?s.varKinds.var:s.varKinds.let)){const a=this._scope.toName(e);return this._for(new $(u,a,t,r),(()=>n(a)))}forOf(e,t,r,u=s.varKinds.const){const a=this._scope.toName(e);if(this.opts.es5){const e=t instanceof n.Name?t:this.var("_arr",t);return this.forRange("_i",0,n._`${e}.length`,(t=>{this.var(a,n._`${e}[${t}]`),r(a)}))}return this._for(new F("of",u,a,t),(()=>r(a)))}forIn(e,t,r,u=(this.opts.es5?s.varKinds.var:s.varKinds.const)){if(this.opts.ownProperties)return this.forOf(e,n._`Object.keys(${t})`,r);const a=this._scope.toName(e);return this._for(new F("in",u,a,t),(()=>r(a)))}endFor(){return this._endBlockNode(E)}label(e){return this._leafNode(new l(e))}break(e){return this._leafNode(new f(e))}return(e){const t=new w;if(this._blockNode(t),this.code(e),1!==t.nodes.length)throw new Error('CodeGen: "return" should have one node');return this._endBlockNode(w)}try(e,t,r){if(!t&&!r)throw new Error('CodeGen: "try" without "catch" and "finally"');const n=new _;if(this._blockNode(n),this.code(e),t){const e=this.name("e");this._currNode=n.catch=new b(e),t(e)}return r&&(this._currNode=n.finally=new S,this.code(r)),this._endBlockNode(b,S)}throw(e){return this._leafNode(new h(e))}block(e,t){return this._blockStarts.push(this._nodes.length),e&&this.code(e).endBlock(t),this}endBlock(e){const t=this._blockStarts.pop();if(void 0===t)throw new Error("CodeGen: not in self-balancing block");const r=this._nodes.length-t;if(r<0||void 0!==e&&r!==e)throw new Error(`CodeGen: wrong number of nodes: ${r} vs ${e} expected`);return this._nodes.length=t,this}func(e,t=n.nil,r,s){return this._blockNode(new A(e,t,r)),s&&this.code(s).endFunc(),this}endFunc(){return this._endBlockNode(A)}optimize(e=1){for(;e-- >0;)this._root.optimizeNodes(),this._root.optimizeNames(this._root.names,this._constants)}_leafNode(e){return this._currNode.nodes.push(e),this}_blockNode(e){this._currNode.nodes.push(e),this._nodes.push(e)}_endBlockNode(e,t){const r=this._currNode;if(r instanceof e||t&&r instanceof t)return this._nodes.pop(),this;throw new Error(`CodeGen: not in block "${t?`${e.kind}/${t.kind}`:e.kind}"`)}_elseNode(e){const t=this._currNode;if(!(t instanceof v))throw new Error('CodeGen: "else" without "if"');return this._currNode=t.else=e,this}get _root(){return this._nodes[0]}get _currNode(){const e=this._nodes;return e[e.length-1]}set _currNode(e){const t=this._nodes;t[t.length-1]=e}},t.not=N;const x=I(t.operators.AND);t.and=function(...e){return e.reduce(x)};const O=I(t.operators.OR);function I(e){return(t,r)=>t===n.nil?r:r===n.nil?t:n._`${T(t)} ${e} ${T(r)}`}function T(e){return e instanceof n.Name?e:n._`(${e})`}t.or=function(...e){return e.reduce(O)}},17686:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ValueScope=t.ValueScopeName=t.Scope=t.varKinds=t.UsedValueState=void 0;const n=r(25875);class s extends Error{constructor(e){super(`CodeGen: "code" for ${e} not defined`),this.value=e.value}}var u;!function(e){e[e.Started=0]="Started",e[e.Completed=1]="Completed"}(u=t.UsedValueState||(t.UsedValueState={})),t.varKinds={const:new n.Name("const"),let:new n.Name("let"),var:new n.Name("var")};class a{constructor({prefixes:e,parent:t}={}){this._names={},this._prefixes=e,this._parent=t}toName(e){return e instanceof n.Name?e:this.name(e)}name(e){return new n.Name(this._newName(e))}_newName(e){return`${e}${(this._names[e]||this._nameGroup(e)).index++}`}_nameGroup(e){var t,r;if((null===(r=null===(t=this._parent)||void 0===t?void 0:t._prefixes)||void 0===r?void 0:r.has(e))||this._prefixes&&!this._prefixes.has(e))throw new Error(`CodeGen: prefix "${e}" is not allowed in this scope`);return this._names[e]={prefix:e,index:0}}}t.Scope=a;class o extends n.Name{constructor(e,t){super(t),this.prefix=e}setValue(e,{property:t,itemIndex:r}){this.value=e,this.scopePath=n._`.${new n.Name(t)}[${r}]`}}t.ValueScopeName=o;const i=n._`\n`;t.ValueScope=class extends a{constructor(e){super(e),this._values={},this._scope=e.scope,this.opts={...e,_n:e.lines?i:n.nil}}get(){return this._scope}name(e){return new o(e,this._newName(e))}value(e,t){var r;if(void 0===t.ref)throw new Error("CodeGen: ref must be passed in value");const n=this.toName(e),{prefix:s}=n,u=null!==(r=t.key)&&void 0!==r?r:t.ref;let a=this._values[s];if(a){const e=a.get(u);if(e)return e}else a=this._values[s]=new Map;a.set(u,n);const o=this._scope[s]||(this._scope[s]=[]),i=o.length;return o[i]=t.ref,n.setValue(t,{property:s,itemIndex:i}),n}getValue(e,t){const r=this._values[e];if(r)return r.get(t)}scopeRefs(e,t=this._values){return this._reduceValues(t,(t=>{if(void 0===t.scopePath)throw new Error(`CodeGen: name "${t}" has no value`);return n._`${e}${t.scopePath}`}))}scopeCode(e=this._values,t,r){return this._reduceValues(e,(e=>{if(void 0===e.value)throw new Error(`CodeGen: name "${e}" has no value`);return e.value.code}),t,r)}_reduceValues(e,r,a={},o){let i=n.nil;for(const c in e){const d=e[c];if(!d)continue;const l=a[c]=a[c]||new Map;d.forEach((e=>{if(l.has(e))return;l.set(e,u.Started);let a=r(e);if(a){const r=this.opts.es5?t.varKinds.var:t.varKinds.const;i=n._`${i}${r} ${e} = ${a};${this.opts._n}`}else{if(!(a=null==o?void 0:o(e)))throw new s(e);i=n._`${i}${a}${this.opts._n}`}l.set(e,u.Completed)}))}return i}}},26990:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.extendErrors=t.resetErrorsCount=t.reportExtraError=t.reportError=t.keyword$DataError=t.keywordError=void 0;const n=r(78380),s=r(78136),u=r(25668);function a(e,t){const r=e.const("err",t);e.if(n._`${u.default.vErrors} === null`,(()=>e.assign(u.default.vErrors,n._`[${r}]`)),n._`${u.default.vErrors}.push(${r})`),e.code(n._`${u.default.errors}++`)}function o(e,t){const{gen:r,validateName:s,schemaEnv:u}=e;u.$async?r.throw(n._`new ${e.ValidationError}(${t})`):(r.assign(n._`${s}.errors`,t),r.return(!1))}t.keywordError={message:({keyword:e})=>n.str`must pass "${e}" keyword validation`},t.keyword$DataError={message:({keyword:e,schemaType:t})=>t?n.str`"${e}" keyword must be ${t} ($data)`:n.str`"${e}" keyword is invalid ($data)`},t.reportError=function(e,r=t.keywordError,s,u){const{it:i}=e,{gen:d,compositeRule:l,allErrors:f}=i,h=c(e,r,s);(null!=u?u:l||f)?a(d,h):o(i,n._`[${h}]`)},t.reportExtraError=function(e,r=t.keywordError,n){const{it:s}=e,{gen:i,compositeRule:d,allErrors:l}=s;a(i,c(e,r,n)),d||l||o(s,u.default.vErrors)},t.resetErrorsCount=function(e,t){e.assign(u.default.errors,t),e.if(n._`${u.default.vErrors} !== null`,(()=>e.if(t,(()=>e.assign(n._`${u.default.vErrors}.length`,t)),(()=>e.assign(u.default.vErrors,null)))))},t.extendErrors=function({gen:e,keyword:t,schemaValue:r,data:s,errsCount:a,it:o}){if(void 0===a)throw new Error("ajv implementation error");const i=e.name("err");e.forRange("i",a,u.default.errors,(a=>{e.const(i,n._`${u.default.vErrors}[${a}]`),e.if(n._`${i}.instancePath === undefined`,(()=>e.assign(n._`${i}.instancePath`,(0,n.strConcat)(u.default.instancePath,o.errorPath)))),e.assign(n._`${i}.schemaPath`,n.str`${o.errSchemaPath}/${t}`),o.opts.verbose&&(e.assign(n._`${i}.schema`,r),e.assign(n._`${i}.data`,s))}))};const i={keyword:new n.Name("keyword"),schemaPath:new n.Name("schemaPath"),params:new n.Name("params"),propertyName:new n.Name("propertyName"),message:new n.Name("message"),schema:new n.Name("schema"),parentSchema:new n.Name("parentSchema")};function c(e,t,r){const{createErrors:s}=e.it;return!1===s?n._`{}`:function(e,t,r={}){const{gen:s,it:a}=e,o=[d(a,r),l(e,r)];return function(e,{params:t,message:r},s){const{keyword:a,data:o,schemaValue:c,it:d}=e,{opts:l,propertyName:f,topSchemaRef:h,schemaPath:p}=d;s.push([i.keyword,a],[i.params,"function"==typeof t?t(e):t||n._`{}`]),l.messages&&s.push([i.message,"function"==typeof r?r(e):r]),l.verbose&&s.push([i.schema,c],[i.parentSchema,n._`${h}${p}`],[u.default.data,o]),f&&s.push([i.propertyName,f])}(e,t,o),s.object(...o)}(e,t,r)}function d({errorPath:e},{instancePath:t}){const r=t?n.str`${e}${(0,s.getErrorPath)(t,s.Type.Str)}`:e;return[u.default.instancePath,(0,n.strConcat)(u.default.instancePath,r)]}function l({keyword:e,it:{errSchemaPath:t}},{schemaPath:r,parentSchema:u}){let a=u?t:n.str`${t}/${e}`;return r&&(a=n.str`${a}${(0,s.getErrorPath)(r,s.Type.Str)}`),[i.schemaPath,a]}},56862:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.resolveSchema=t.getCompilingSchema=t.resolveRef=t.compileSchema=t.SchemaEnv=void 0;const n=r(78380),s=r(52582),u=r(25668),a=r(20394),o=r(78136),i=r(58941);class c{constructor(e){var t;let r;this.refs={},this.dynamicAnchors={},"object"==typeof e.schema&&(r=e.schema),this.schema=e.schema,this.schemaId=e.schemaId,this.root=e.root||this,this.baseId=null!==(t=e.baseId)&&void 0!==t?t:(0,a.normalizeId)(null==r?void 0:r[e.schemaId||"$id"]),this.schemaPath=e.schemaPath,this.localRefs=e.localRefs,this.meta=e.meta,this.$async=null==r?void 0:r.$async,this.refs={}}}function d(e){const t=f.call(this,e);if(t)return t;const r=(0,a.getFullPath)(this.opts.uriResolver,e.root.baseId),{es5:o,lines:c}=this.opts.code,{ownProperties:d}=this.opts,l=new n.CodeGen(this.scope,{es5:o,lines:c,ownProperties:d});let h;e.$async&&(h=l.scopeValue("Error",{ref:s.default,code:n._`require("ajv/dist/runtime/validation_error").default`}));const p=l.scopeName("validate");e.validateName=p;const m={gen:l,allErrors:this.opts.allErrors,data:u.default.data,parentData:u.default.parentData,parentDataProperty:u.default.parentDataProperty,dataNames:[u.default.data],dataPathArr:[n.nil],dataLevel:0,dataTypes:[],definedProperties:new Set,topSchemaRef:l.scopeValue("schema",!0===this.opts.code.source?{ref:e.schema,code:(0,n.stringify)(e.schema)}:{ref:e.schema}),validateName:p,ValidationError:h,schema:e.schema,schemaEnv:e,rootId:r,baseId:e.baseId||r,schemaPath:n.nil,errSchemaPath:e.schemaPath||(this.opts.jtd?"":"#"),errorPath:n._`""`,opts:this.opts,self:this};let D;try{this._compilations.add(e),(0,i.validateFunctionCode)(m),l.optimize(this.opts.code.optimize);const t=l.toString();D=`${l.scopeRefs(u.default.scope)}return ${t}`,this.opts.code.process&&(D=this.opts.code.process(D,e));const r=new Function(`${u.default.self}`,`${u.default.scope}`,D)(this,this.scope.get());if(this.scope.value(p,{ref:r}),r.errors=null,r.schema=e.schema,r.schemaEnv=e,e.$async&&(r.$async=!0),!0===this.opts.code.source&&(r.source={validateName:p,validateCode:t,scopeValues:l._values}),this.opts.unevaluated){const{props:e,items:t}=m;r.evaluated={props:e instanceof n.Name?void 0:e,items:t instanceof n.Name?void 0:t,dynamicProps:e instanceof n.Name,dynamicItems:t instanceof n.Name},r.source&&(r.source.evaluated=(0,n.stringify)(r.evaluated))}return e.validate=r,e}catch(t){throw delete e.validate,delete e.validateName,D&&this.logger.error("Error compiling schema, function code:",D),t}finally{this._compilations.delete(e)}}function l(e){return(0,a.inlineRef)(e.schema,this.opts.inlineRefs)?e.schema:e.validate?e:d.call(this,e)}function f(e){for(const n of this._compilations)if(r=e,(t=n).schema===r.schema&&t.root===r.root&&t.baseId===r.baseId)return n;var t,r}function h(e,t){let r;for(;"string"==typeof(r=this.refs[t]);)t=r;return r||this.schemas[t]||p.call(this,e,t)}function p(e,t){const r=this.opts.uriResolver.parse(t),n=(0,a._getFullPath)(this.opts.uriResolver,r);let s=(0,a.getFullPath)(this.opts.uriResolver,e.baseId,void 0);if(Object.keys(e.schema).length>0&&n===s)return D.call(this,r,e);const u=(0,a.normalizeId)(n),o=this.refs[u]||this.schemas[u];if("string"==typeof o){const t=p.call(this,e,o);if("object"!=typeof(null==t?void 0:t.schema))return;return D.call(this,r,t)}if("object"==typeof(null==o?void 0:o.schema)){if(o.validate||d.call(this,o),u===(0,a.normalizeId)(t)){const{schema:t}=o,{schemaId:r}=this.opts,n=t[r];return n&&(s=(0,a.resolveUrl)(this.opts.uriResolver,s,n)),new c({schema:t,schemaId:r,root:e,baseId:s})}return D.call(this,r,o)}}t.SchemaEnv=c,t.compileSchema=d,t.resolveRef=function(e,t,r){var n;r=(0,a.resolveUrl)(this.opts.uriResolver,t,r);const s=e.refs[r];if(s)return s;let u=h.call(this,e,r);if(void 0===u){const s=null===(n=e.localRefs)||void 0===n?void 0:n[r],{schemaId:a}=this.opts;s&&(u=new c({schema:s,schemaId:a,root:e,baseId:t}))}return void 0!==u?e.refs[r]=l.call(this,u):void 0},t.getCompilingSchema=f,t.resolveSchema=p;const m=new Set(["properties","patternProperties","enum","dependencies","definitions"]);function D(e,{baseId:t,schema:r,root:n}){var s;if("/"!==(null===(s=e.fragment)||void 0===s?void 0:s[0]))return;for(const n of e.fragment.slice(1).split("/")){if("boolean"==typeof r)return;const e=r[(0,o.unescapeFragment)(n)];if(void 0===e)return;const s="object"==typeof(r=e)&&r[this.opts.schemaId];!m.has(n)&&s&&(t=(0,a.resolveUrl)(this.opts.uriResolver,t,s))}let u;if("boolean"!=typeof r&&r.$ref&&!(0,o.schemaHasRulesButRef)(r,this.RULES)){const e=(0,a.resolveUrl)(this.opts.uriResolver,t,r.$ref);u=p.call(this,n,e)}const{schemaId:i}=this.opts;return u=u||new c({schema:r,schemaId:i,root:n,baseId:t}),u.schema!==u.root.schema?u:void 0}},25668:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(78380),s={data:new n.Name("data"),valCxt:new n.Name("valCxt"),instancePath:new n.Name("instancePath"),parentData:new n.Name("parentData"),parentDataProperty:new n.Name("parentDataProperty"),rootData:new n.Name("rootData"),dynamicAnchors:new n.Name("dynamicAnchors"),vErrors:new n.Name("vErrors"),errors:new n.Name("errors"),this:new n.Name("this"),self:new n.Name("self"),scope:new n.Name("scope"),json:new n.Name("json"),jsonPos:new n.Name("jsonPos"),jsonLen:new n.Name("jsonLen"),jsonPart:new n.Name("jsonPart")};t.default=s},61326:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(20394);class s extends Error{constructor(e,t,r,s){super(s||`can't resolve reference ${r} from id ${t}`),this.missingRef=(0,n.resolveUrl)(e,t,r),this.missingSchema=(0,n.normalizeId)((0,n.getFullPath)(e,this.missingRef))}}t.default=s},20394:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getSchemaRefs=t.resolveUrl=t.normalizeId=t._getFullPath=t.getFullPath=t.inlineRef=void 0;const n=r(78136),s=r(66471),u=r(25127),a=new Set(["type","format","pattern","maxLength","minLength","maxProperties","minProperties","maxItems","minItems","maximum","minimum","uniqueItems","multipleOf","required","enum","const"]);t.inlineRef=function(e,t=!0){return"boolean"==typeof e||(!0===t?!i(e):!!t&&c(e)<=t)};const o=new Set(["$ref","$recursiveRef","$recursiveAnchor","$dynamicRef","$dynamicAnchor"]);function i(e){for(const t in e){if(o.has(t))return!0;const r=e[t];if(Array.isArray(r)&&r.some(i))return!0;if("object"==typeof r&&i(r))return!0}return!1}function c(e){let t=0;for(const r in e){if("$ref"===r)return 1/0;if(t++,!a.has(r)&&("object"==typeof e[r]&&(0,n.eachItem)(e[r],(e=>t+=c(e))),t===1/0))return 1/0}return t}function d(e,t="",r){!1!==r&&(t=h(t));const n=e.parse(t);return l(e,n)}function l(e,t){return e.serialize(t).split("#")[0]+"#"}t.getFullPath=d,t._getFullPath=l;const f=/#\/?$/;function h(e){return e?e.replace(f,""):""}t.normalizeId=h,t.resolveUrl=function(e,t,r){return r=h(r),e.resolve(t,r)};const p=/^[a-z_][-a-z0-9._]*$/i;t.getSchemaRefs=function(e,t){if("boolean"==typeof e)return{};const{schemaId:r,uriResolver:n}=this.opts,a=h(e[r]||t),o={"":a},i=d(n,a,!1),c={},l=new Set;return u(e,{allKeys:!0},((e,t,n,s)=>{if(void 0===s)return;const u=i+t;let a=o[s];function d(t){const r=this.opts.uriResolver.resolve;if(t=h(a?r(a,t):t),l.has(t))throw m(t);l.add(t);let n=this.refs[t];return"string"==typeof n&&(n=this.refs[n]),"object"==typeof n?f(e,n.schema,t):t!==h(u)&&("#"===t[0]?(f(e,c[t],t),c[t]=e):this.refs[t]=u),t}function D(e){if("string"==typeof e){if(!p.test(e))throw new Error(`invalid anchor "${e}"`);d.call(this,`#${e}`)}}"string"==typeof e[r]&&(a=d.call(this,e[r])),D.call(this,e.$anchor),D.call(this,e.$dynamicAnchor),o[t]=a})),c;function f(e,t,r){if(void 0!==t&&!s(e,t))throw m(r)}function m(e){return new Error(`reference "${e}" resolves to more than one schema`)}}},68817:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getRules=t.isJSONType=void 0;const r=new Set(["string","number","integer","boolean","null","object","array"]);t.isJSONType=function(e){return"string"==typeof e&&r.has(e)},t.getRules=function(){const e={number:{type:"number",rules:[]},string:{type:"string",rules:[]},array:{type:"array",rules:[]},object:{type:"object",rules:[]}};return{types:{...e,integer:!0,boolean:!0,null:!0},rules:[{rules:[]},e.number,e.string,e.array,e.object],post:{rules:[]},all:{},keywords:{}}}},78136:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.checkStrictMode=t.getErrorPath=t.Type=t.useFunc=t.setEvaluated=t.evaluatedPropsToName=t.mergeEvaluated=t.eachItem=t.unescapeJsonPointer=t.escapeJsonPointer=t.escapeFragment=t.unescapeFragment=t.schemaRefOrVal=t.schemaHasRulesButRef=t.schemaHasRules=t.checkUnknownRules=t.alwaysValidSchema=t.toHash=void 0;const n=r(78380),s=r(25875);function u(e,t=e.schema){const{opts:r,self:n}=e;if(!r.strictSchema)return;if("boolean"==typeof t)return;const s=n.RULES.keywords;for(const r in t)s[r]||p(e,`unknown keyword: "${r}"`)}function a(e,t){if("boolean"==typeof e)return!e;for(const r in e)if(t[r])return!0;return!1}function o(e){return"number"==typeof e?`${e}`:e.replace(/~/g,"~0").replace(/\//g,"~1")}function i(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}function c({mergeNames:e,mergeToName:t,mergeValues:r,resultToName:s}){return(u,a,o,i)=>{const c=void 0===o?a:o instanceof n.Name?(a instanceof n.Name?e(u,a,o):t(u,a,o),o):a instanceof n.Name?(t(u,o,a),a):r(a,o);return i!==n.Name||c instanceof n.Name?c:s(u,c)}}function d(e,t){if(!0===t)return e.var("props",!0);const r=e.var("props",n._`{}`);return void 0!==t&&l(e,r,t),r}function l(e,t,r){Object.keys(r).forEach((r=>e.assign(n._`${t}${(0,n.getProperty)(r)}`,!0)))}t.toHash=function(e){const t={};for(const r of e)t[r]=!0;return t},t.alwaysValidSchema=function(e,t){return"boolean"==typeof t?t:0===Object.keys(t).length||(u(e,t),!a(t,e.self.RULES.all))},t.checkUnknownRules=u,t.schemaHasRules=a,t.schemaHasRulesButRef=function(e,t){if("boolean"==typeof e)return!e;for(const r in e)if("$ref"!==r&&t.all[r])return!0;return!1},t.schemaRefOrVal=function({topSchemaRef:e,schemaPath:t},r,s,u){if(!u){if("number"==typeof r||"boolean"==typeof r)return r;if("string"==typeof r)return n._`${r}`}return n._`${e}${t}${(0,n.getProperty)(s)}`},t.unescapeFragment=function(e){return i(decodeURIComponent(e))},t.escapeFragment=function(e){return encodeURIComponent(o(e))},t.escapeJsonPointer=o,t.unescapeJsonPointer=i,t.eachItem=function(e,t){if(Array.isArray(e))for(const r of e)t(r);else t(e)},t.mergeEvaluated={props:c({mergeNames:(e,t,r)=>e.if(n._`${r} !== true && ${t} !== undefined`,(()=>{e.if(n._`${t} === true`,(()=>e.assign(r,!0)),(()=>e.assign(r,n._`${r} || {}`).code(n._`Object.assign(${r}, ${t})`)))})),mergeToName:(e,t,r)=>e.if(n._`${r} !== true`,(()=>{!0===t?e.assign(r,!0):(e.assign(r,n._`${r} || {}`),l(e,r,t))})),mergeValues:(e,t)=>!0===e||{...e,...t},resultToName:d}),items:c({mergeNames:(e,t,r)=>e.if(n._`${r} !== true && ${t} !== undefined`,(()=>e.assign(r,n._`${t} === true ? true : ${r} > ${t} ? ${r} : ${t}`))),mergeToName:(e,t,r)=>e.if(n._`${r} !== true`,(()=>e.assign(r,!0===t||n._`${r} > ${t} ? ${r} : ${t}`))),mergeValues:(e,t)=>!0===e||Math.max(e,t),resultToName:(e,t)=>e.var("items",t)})},t.evaluatedPropsToName=d,t.setEvaluated=l;const f={};var h;function p(e,t,r=e.opts.strictSchema){if(r){if(t=`strict mode: ${t}`,!0===r)throw new Error(t);e.self.logger.warn(t)}}t.useFunc=function(e,t){return e.scopeValue("func",{ref:t,code:f[t.code]||(f[t.code]=new s._Code(t.code))})},function(e){e[e.Num=0]="Num",e[e.Str=1]="Str"}(h=t.Type||(t.Type={})),t.getErrorPath=function(e,t,r){if(e instanceof n.Name){const s=t===h.Num;return r?s?n._`"[" + ${e} + "]"`:n._`"['" + ${e} + "']"`:s?n._`"/" + ${e}`:n._`"/" + ${e}.replace(/~/g, "~0").replace(/\\//g, "~1")`}return r?(0,n.getProperty)(e).toString():"/"+o(e)},t.checkStrictMode=p},66388:(e,t)=>{"use strict";function r(e,t){return t.rules.some((t=>n(e,t)))}function n(e,t){var r;return void 0!==e[t.keyword]||(null===(r=t.definition.implements)||void 0===r?void 0:r.some((t=>void 0!==e[t])))}Object.defineProperty(t,"__esModule",{value:!0}),t.shouldUseRule=t.shouldUseGroup=t.schemaHasRulesForType=void 0,t.schemaHasRulesForType=function({schema:e,self:t},n){const s=t.RULES.types[n];return s&&!0!==s&&r(e,s)},t.shouldUseGroup=r,t.shouldUseRule=n},43720:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.boolOrEmptySchema=t.topBoolOrEmptySchema=void 0;const n=r(26990),s=r(78380),u=r(25668),a={message:"boolean schema is false"};function o(e,t){const{gen:r,data:s}=e,u={gen:r,keyword:"false schema",data:s,schema:!1,schemaCode:!1,schemaValue:!1,params:{},it:e};(0,n.reportError)(u,a,void 0,t)}t.topBoolOrEmptySchema=function(e){const{gen:t,schema:r,validateName:n}=e;!1===r?o(e,!1):"object"==typeof r&&!0===r.$async?t.return(u.default.data):(t.assign(s._`${n}.errors`,null),t.return(!0))},t.boolOrEmptySchema=function(e,t){const{gen:r,schema:n}=e;!1===n?(r.var(t,!1),o(e)):r.var(t,!0)}},24307:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.reportTypeError=t.checkDataTypes=t.checkDataType=t.coerceAndCheckDataType=t.getJSONTypes=t.getSchemaTypes=t.DataType=void 0;const n=r(68817),s=r(66388),u=r(26990),a=r(78380),o=r(78136);var i;function c(e){const t=Array.isArray(e)?e:e?[e]:[];if(t.every(n.isJSONType))return t;throw new Error("type must be JSONType or JSONType[]: "+t.join(","))}!function(e){e[e.Correct=0]="Correct",e[e.Wrong=1]="Wrong"}(i=t.DataType||(t.DataType={})),t.getSchemaTypes=function(e){const t=c(e.type);if(t.includes("null")){if(!1===e.nullable)throw new Error("type: null contradicts nullable: false")}else{if(!t.length&&void 0!==e.nullable)throw new Error('"nullable" cannot be used without "type"');!0===e.nullable&&t.push("null")}return t},t.getJSONTypes=c,t.coerceAndCheckDataType=function(e,t){const{gen:r,data:n,opts:u}=e,o=function(e,t){return t?e.filter((e=>d.has(e)||"array"===t&&"array"===e)):[]}(t,u.coerceTypes),c=t.length>0&&!(0===o.length&&1===t.length&&(0,s.schemaHasRulesForType)(e,t[0]));if(c){const s=f(t,n,u.strictNumbers,i.Wrong);r.if(s,(()=>{o.length?function(e,t,r){const{gen:n,data:s,opts:u}=e,o=n.let("dataType",a._`typeof ${s}`),i=n.let("coerced",a._`undefined`);"array"===u.coerceTypes&&n.if(a._`${o} == 'object' && Array.isArray(${s}) && ${s}.length == 1`,(()=>n.assign(s,a._`${s}[0]`).assign(o,a._`typeof ${s}`).if(f(t,s,u.strictNumbers),(()=>n.assign(i,s))))),n.if(a._`${i} !== undefined`);for(const e of r)(d.has(e)||"array"===e&&"array"===u.coerceTypes)&&c(e);function c(e){switch(e){case"string":return void n.elseIf(a._`${o} == "number" || ${o} == "boolean"`).assign(i,a._`"" + ${s}`).elseIf(a._`${s} === null`).assign(i,a._`""`);case"number":return void n.elseIf(a._`${o} == "boolean" || ${s} === null
              || (${o} == "string" && ${s} && ${s} == +${s})`).assign(i,a._`+${s}`);case"integer":return void n.elseIf(a._`${o} === "boolean" || ${s} === null
              || (${o} === "string" && ${s} && ${s} == +${s} && !(${s} % 1))`).assign(i,a._`+${s}`);case"boolean":return void n.elseIf(a._`${s} === "false" || ${s} === 0 || ${s} === null`).assign(i,!1).elseIf(a._`${s} === "true" || ${s} === 1`).assign(i,!0);case"null":return n.elseIf(a._`${s} === "" || ${s} === 0 || ${s} === false`),void n.assign(i,null);case"array":n.elseIf(a._`${o} === "string" || ${o} === "number"
              || ${o} === "boolean" || ${s} === null`).assign(i,a._`[${s}]`)}}n.else(),p(e),n.endIf(),n.if(a._`${i} !== undefined`,(()=>{n.assign(s,i),function({gen:e,parentData:t,parentDataProperty:r},n){e.if(a._`${t} !== undefined`,(()=>e.assign(a._`${t}[${r}]`,n)))}(e,i)}))}(e,t,o):p(e)}))}return c};const d=new Set(["string","number","integer","boolean","null"]);function l(e,t,r,n=i.Correct){const s=n===i.Correct?a.operators.EQ:a.operators.NEQ;let u;switch(e){case"null":return a._`${t} ${s} null`;case"array":u=a._`Array.isArray(${t})`;break;case"object":u=a._`${t} && typeof ${t} == "object" && !Array.isArray(${t})`;break;case"integer":u=o(a._`!(${t} % 1) && !isNaN(${t})`);break;case"number":u=o();break;default:return a._`typeof ${t} ${s} ${e}`}return n===i.Correct?u:(0,a.not)(u);function o(e=a.nil){return(0,a.and)(a._`typeof ${t} == "number"`,e,r?a._`isFinite(${t})`:a.nil)}}function f(e,t,r,n){if(1===e.length)return l(e[0],t,r,n);let s;const u=(0,o.toHash)(e);if(u.array&&u.object){const e=a._`typeof ${t} != "object"`;s=u.null?e:a._`!${t} || ${e}`,delete u.null,delete u.array,delete u.object}else s=a.nil;u.number&&delete u.integer;for(const e in u)s=(0,a.and)(s,l(e,t,r,n));return s}t.checkDataType=l,t.checkDataTypes=f;const h={message:({schema:e})=>`must be ${e}`,params:({schema:e,schemaValue:t})=>"string"==typeof e?a._`{type: ${e}}`:a._`{type: ${t}}`};function p(e){const t=function(e){const{gen:t,data:r,schema:n}=e,s=(0,o.schemaRefOrVal)(e,n,"type");return{gen:t,keyword:"type",data:r,schema:n.type,schemaCode:s,schemaValue:s,parentSchema:n,params:{},it:e}}(e);(0,u.reportError)(t,h)}t.reportTypeError=p},9533:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assignDefaults=void 0;const n=r(78380),s=r(78136);function u(e,t,r){const{gen:u,compositeRule:a,data:o,opts:i}=e;if(void 0===r)return;const c=n._`${o}${(0,n.getProperty)(t)}`;if(a)return void(0,s.checkStrictMode)(e,`default is ignored for: ${c}`);let d=n._`${c} === undefined`;"empty"===i.useDefaults&&(d=n._`${d} || ${c} === null || ${c} === ""`),u.if(d,n._`${c} = ${(0,n.stringify)(r)}`)}t.assignDefaults=function(e,t){const{properties:r,items:n}=e.schema;if("object"===t&&r)for(const t in r)u(e,t,r[t].default);else"array"===t&&Array.isArray(n)&&n.forEach(((t,r)=>u(e,r,t.default)))}},58941:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getData=t.KeywordCxt=t.validateFunctionCode=void 0;const n=r(43720),s=r(24307),u=r(66388),a=r(24307),o=r(9533),i=r(34705),c=r(88171),d=r(78380),l=r(25668),f=r(20394),h=r(78136),p=r(26990);function m({gen:e,validateName:t,schema:r,schemaEnv:n,opts:s},u){s.code.es5?e.func(t,d._`${l.default.data}, ${l.default.valCxt}`,n.$async,(()=>{e.code(d._`"use strict"; ${D(r,s)}`),function(e,t){e.if(l.default.valCxt,(()=>{e.var(l.default.instancePath,d._`${l.default.valCxt}.${l.default.instancePath}`),e.var(l.default.parentData,d._`${l.default.valCxt}.${l.default.parentData}`),e.var(l.default.parentDataProperty,d._`${l.default.valCxt}.${l.default.parentDataProperty}`),e.var(l.default.rootData,d._`${l.default.valCxt}.${l.default.rootData}`),t.dynamicRef&&e.var(l.default.dynamicAnchors,d._`${l.default.valCxt}.${l.default.dynamicAnchors}`)}),(()=>{e.var(l.default.instancePath,d._`""`),e.var(l.default.parentData,d._`undefined`),e.var(l.default.parentDataProperty,d._`undefined`),e.var(l.default.rootData,l.default.data),t.dynamicRef&&e.var(l.default.dynamicAnchors,d._`{}`)}))}(e,s),e.code(u)})):e.func(t,d._`${l.default.data}, ${function(e){return d._`{${l.default.instancePath}="", ${l.default.parentData}, ${l.default.parentDataProperty}, ${l.default.rootData}=${l.default.data}${e.dynamicRef?d._`, ${l.default.dynamicAnchors}={}`:d.nil}}={}`}(s)}`,n.$async,(()=>e.code(D(r,s)).code(u)))}function D(e,t){const r="object"==typeof e&&e[t.schemaId];return r&&(t.code.source||t.code.process)?d._`/*# sourceURL=${r} */`:d.nil}function y({schema:e,self:t}){if("boolean"==typeof e)return!e;for(const r in e)if(t.RULES.all[r])return!0;return!1}function g(e){return"boolean"!=typeof e.schema}function v(e){(0,h.checkUnknownRules)(e),function(e){const{schema:t,errSchemaPath:r,opts:n,self:s}=e;t.$ref&&n.ignoreKeywordsWithRef&&(0,h.schemaHasRulesButRef)(t,s.RULES)&&s.logger.warn(`$ref: keywords ignored in schema at path "${r}"`)}(e)}function E(e,t){if(e.opts.jtd)return $(e,[],!1,t);const r=(0,s.getSchemaTypes)(e.schema);$(e,r,!(0,s.coerceAndCheckDataType)(e,r),t)}function C({gen:e,schemaEnv:t,schema:r,errSchemaPath:n,opts:s}){const u=r.$comment;if(!0===s.$comment)e.code(d._`${l.default.self}.logger.log(${u})`);else if("function"==typeof s.$comment){const r=d.str`${n}/$comment`,s=e.scopeValue("root",{ref:t.root});e.code(d._`${l.default.self}.opts.$comment(${u}, ${r}, ${s}.schema)`)}}function $(e,t,r,n){const{gen:s,schema:o,data:i,allErrors:c,opts:f,self:p}=e,{RULES:m}=p;function D(h){(0,u.shouldUseGroup)(o,h)&&(h.type?(s.if((0,a.checkDataType)(h.type,i,f.strictNumbers)),F(e,h),1===t.length&&t[0]===h.type&&r&&(s.else(),(0,a.reportTypeError)(e)),s.endIf()):F(e,h),c||s.if(d._`${l.default.errors} === ${n||0}`))}!o.$ref||!f.ignoreKeywordsWithRef&&(0,h.schemaHasRulesButRef)(o,m)?(f.jtd||function(e,t){!e.schemaEnv.meta&&e.opts.strictTypes&&(function(e,t){t.length&&(e.dataTypes.length?(t.forEach((t=>{A(e.dataTypes,t)||w(e,`type "${t}" not allowed by context "${e.dataTypes.join(",")}"`)})),function(e,t){const r=[];for(const n of e.dataTypes)A(t,n)?r.push(n):t.includes("integer")&&"number"===n&&r.push("integer");e.dataTypes=r}(e,t)):e.dataTypes=t)}(e,t),e.opts.allowUnionTypes||function(e,t){t.length>1&&(2!==t.length||!t.includes("null"))&&w(e,"use allowUnionTypes to allow union type keyword")}(e,t),function(e,t){const r=e.self.RULES.all;for(const n in r){const s=r[n];if("object"==typeof s&&(0,u.shouldUseRule)(e.schema,s)){const{type:r}=s.definition;r.length&&!r.some((e=>{return n=e,(r=t).includes(n)||"number"===n&&r.includes("integer");var r,n}))&&w(e,`missing type "${r.join(",")}" for keyword "${n}"`)}}}(e,e.dataTypes))}(e,t),s.block((()=>{for(const e of m.rules)D(e);D(m.post)}))):s.block((()=>b(e,"$ref",m.all.$ref.definition)))}function F(e,t){const{gen:r,schema:n,opts:{useDefaults:s}}=e;s&&(0,o.assignDefaults)(e,t.type),r.block((()=>{for(const r of t.rules)(0,u.shouldUseRule)(n,r)&&b(e,r.keyword,r.definition,t.type)}))}function A(e,t){return e.includes(t)||"integer"===t&&e.includes("number")}function w(e,t){t+=` at "${e.schemaEnv.baseId+e.errSchemaPath}" (strictTypes)`,(0,h.checkStrictMode)(e,t,e.opts.strictTypes)}t.validateFunctionCode=function(e){g(e)&&(v(e),y(e))?function(e){const{schema:t,opts:r,gen:n}=e;m(e,(()=>{r.$comment&&t.$comment&&C(e),function(e){const{schema:t,opts:r}=e;void 0!==t.default&&r.useDefaults&&r.strictSchema&&(0,h.checkStrictMode)(e,"default is ignored in the schema root")}(e),n.let(l.default.vErrors,null),n.let(l.default.errors,0),r.unevaluated&&function(e){const{gen:t,validateName:r}=e;e.evaluated=t.const("evaluated",d._`${r}.evaluated`),t.if(d._`${e.evaluated}.dynamicProps`,(()=>t.assign(d._`${e.evaluated}.props`,d._`undefined`))),t.if(d._`${e.evaluated}.dynamicItems`,(()=>t.assign(d._`${e.evaluated}.items`,d._`undefined`)))}(e),E(e),function(e){const{gen:t,schemaEnv:r,validateName:n,ValidationError:s,opts:u}=e;r.$async?t.if(d._`${l.default.errors} === 0`,(()=>t.return(l.default.data)),(()=>t.throw(d._`new ${s}(${l.default.vErrors})`))):(t.assign(d._`${n}.errors`,l.default.vErrors),u.unevaluated&&function({gen:e,evaluated:t,props:r,items:n}){r instanceof d.Name&&e.assign(d._`${t}.props`,r),n instanceof d.Name&&e.assign(d._`${t}.items`,n)}(e),t.return(d._`${l.default.errors} === 0`))}(e)}))}(e):m(e,(()=>(0,n.topBoolOrEmptySchema)(e)))};class _{constructor(e,t,r){if((0,i.validateKeywordUsage)(e,t,r),this.gen=e.gen,this.allErrors=e.allErrors,this.keyword=r,this.data=e.data,this.schema=e.schema[r],this.$data=t.$data&&e.opts.$data&&this.schema&&this.schema.$data,this.schemaValue=(0,h.schemaRefOrVal)(e,this.schema,r,this.$data),this.schemaType=t.schemaType,this.parentSchema=e.schema,this.params={},this.it=e,this.def=t,this.$data)this.schemaCode=e.gen.const("vSchema",B(this.$data,e));else if(this.schemaCode=this.schemaValue,!(0,i.validSchemaType)(this.schema,t.schemaType,t.allowUndefined))throw new Error(`${r} value must be ${JSON.stringify(t.schemaType)}`);("code"in t?t.trackErrors:!1!==t.errors)&&(this.errsCount=e.gen.const("_errs",l.default.errors))}result(e,t,r){this.failResult((0,d.not)(e),t,r)}failResult(e,t,r){this.gen.if(e),r?r():this.error(),t?(this.gen.else(),t(),this.allErrors&&this.gen.endIf()):this.allErrors?this.gen.endIf():this.gen.else()}pass(e,t){this.failResult((0,d.not)(e),void 0,t)}fail(e){if(void 0===e)return this.error(),void(this.allErrors||this.gen.if(!1));this.gen.if(e),this.error(),this.allErrors?this.gen.endIf():this.gen.else()}fail$data(e){if(!this.$data)return this.fail(e);const{schemaCode:t}=this;this.fail(d._`${t} !== undefined && (${(0,d.or)(this.invalid$data(),e)})`)}error(e,t,r){if(t)return this.setParams(t),this._error(e,r),void this.setParams({});this._error(e,r)}_error(e,t){(e?p.reportExtraError:p.reportError)(this,this.def.error,t)}$dataError(){(0,p.reportError)(this,this.def.$dataError||p.keyword$DataError)}reset(){if(void 0===this.errsCount)throw new Error('add "trackErrors" to keyword definition');(0,p.resetErrorsCount)(this.gen,this.errsCount)}ok(e){this.allErrors||this.gen.if(e)}setParams(e,t){t?Object.assign(this.params,e):this.params=e}block$data(e,t,r=d.nil){this.gen.block((()=>{this.check$data(e,r),t()}))}check$data(e=d.nil,t=d.nil){if(!this.$data)return;const{gen:r,schemaCode:n,schemaType:s,def:u}=this;r.if((0,d.or)(d._`${n} === undefined`,t)),e!==d.nil&&r.assign(e,!0),(s.length||u.validateSchema)&&(r.elseIf(this.invalid$data()),this.$dataError(),e!==d.nil&&r.assign(e,!1)),r.else()}invalid$data(){const{gen:e,schemaCode:t,schemaType:r,def:n,it:s}=this;return(0,d.or)(function(){if(r.length){if(!(t instanceof d.Name))throw new Error("ajv implementation error");const e=Array.isArray(r)?r:[r];return d._`${(0,a.checkDataTypes)(e,t,s.opts.strictNumbers,a.DataType.Wrong)}`}return d.nil}(),function(){if(n.validateSchema){const r=e.scopeValue("validate$data",{ref:n.validateSchema});return d._`!${r}(${t})`}return d.nil}())}subschema(e,t){const r=(0,c.getSubschema)(this.it,e);(0,c.extendSubschemaData)(r,this.it,e),(0,c.extendSubschemaMode)(r,e);const s={...this.it,...r,items:void 0,props:void 0};return function(e,t){g(e)&&(v(e),y(e))?function(e,t){const{schema:r,gen:n,opts:s}=e;s.$comment&&r.$comment&&C(e),function(e){const t=e.schema[e.opts.schemaId];t&&(e.baseId=(0,f.resolveUrl)(e.opts.uriResolver,e.baseId,t))}(e),function(e){if(e.schema.$async&&!e.schemaEnv.$async)throw new Error("async schema in sync schema")}(e);const u=n.const("_errs",l.default.errors);E(e,u),n.var(t,d._`${u} === ${l.default.errors}`)}(e,t):(0,n.boolOrEmptySchema)(e,t)}(s,t),s}mergeEvaluated(e,t){const{it:r,gen:n}=this;r.opts.unevaluated&&(!0!==r.props&&void 0!==e.props&&(r.props=h.mergeEvaluated.props(n,e.props,r.props,t)),!0!==r.items&&void 0!==e.items&&(r.items=h.mergeEvaluated.items(n,e.items,r.items,t)))}mergeValidEvaluated(e,t){const{it:r,gen:n}=this;if(r.opts.unevaluated&&(!0!==r.props||!0!==r.items))return n.if(t,(()=>this.mergeEvaluated(e,d.Name))),!0}}function b(e,t,r,n){const s=new _(e,r,t);"code"in r?r.code(s,n):s.$data&&r.validate?(0,i.funcKeywordCode)(s,r):"macro"in r?(0,i.macroKeywordCode)(s,r):(r.compile||r.validate)&&(0,i.funcKeywordCode)(s,r)}t.KeywordCxt=_;const S=/^\/(?:[^~]|~0|~1)*$/,P=/^([0-9]+)(#|\/(?:[^~]|~0|~1)*)?$/;function B(e,{dataLevel:t,dataNames:r,dataPathArr:n}){let s,u;if(""===e)return l.default.rootData;if("/"===e[0]){if(!S.test(e))throw new Error(`Invalid JSON-pointer: ${e}`);s=e,u=l.default.rootData}else{const a=P.exec(e);if(!a)throw new Error(`Invalid JSON-pointer: ${e}`);const o=+a[1];if(s=a[2],"#"===s){if(o>=t)throw new Error(i("property/index",o));return n[t-o]}if(o>t)throw new Error(i("data",o));if(u=r[t-o],!s)return u}let a=u;const o=s.split("/");for(const e of o)e&&(u=d._`${u}${(0,d.getProperty)((0,h.unescapeJsonPointer)(e))}`,a=d._`${a} && ${u}`);return a;function i(e,r){return`Cannot access ${e} ${r} levels up, current level is ${t}`}}t.getData=B},34705:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateKeywordUsage=t.validSchemaType=t.funcKeywordCode=t.macroKeywordCode=void 0;const n=r(78380),s=r(25668),u=r(36461),a=r(26990);function o(e){const{gen:t,data:r,it:s}=e;t.if(s.parentData,(()=>t.assign(r,n._`${s.parentData}[${s.parentDataProperty}]`)))}function i(e,t,r){if(void 0===r)throw new Error(`keyword "${t}" failed to compile`);return e.scopeValue("keyword","function"==typeof r?{ref:r}:{ref:r,code:(0,n.stringify)(r)})}t.macroKeywordCode=function(e,t){const{gen:r,keyword:s,schema:u,parentSchema:a,it:o}=e,c=t.macro.call(o.self,u,a,o),d=i(r,s,c);!1!==o.opts.validateSchema&&o.self.validateSchema(c,!0);const l=r.name("valid");e.subschema({schema:c,schemaPath:n.nil,errSchemaPath:`${o.errSchemaPath}/${s}`,topSchemaRef:d,compositeRule:!0},l),e.pass(l,(()=>e.error(!0)))},t.funcKeywordCode=function(e,t){var r;const{gen:c,keyword:d,schema:l,parentSchema:f,$data:h,it:p}=e;!function({schemaEnv:e},t){if(t.async&&!e.$async)throw new Error("async keyword in sync schema")}(p,t);const m=!h&&t.compile?t.compile.call(p.self,l,f,p):t.validate,D=i(c,d,m),y=c.let("valid");function g(r=(t.async?n._`await `:n.nil)){const a=p.opts.passContext?s.default.this:s.default.self,o=!("compile"in t&&!h||!1===t.schema);c.assign(y,n._`${r}${(0,u.callValidateCode)(e,D,a,o)}`,t.modifying)}function v(e){var r;c.if((0,n.not)(null!==(r=t.valid)&&void 0!==r?r:y),e)}e.block$data(y,(function(){if(!1===t.errors)g(),t.modifying&&o(e),v((()=>e.error()));else{const r=t.async?function(){const e=c.let("ruleErrs",null);return c.try((()=>g(n._`await `)),(t=>c.assign(y,!1).if(n._`${t} instanceof ${p.ValidationError}`,(()=>c.assign(e,n._`${t}.errors`)),(()=>c.throw(t))))),e}():function(){const e=n._`${D}.errors`;return c.assign(e,null),g(n.nil),e}();t.modifying&&o(e),v((()=>function(e,t){const{gen:r}=e;r.if(n._`Array.isArray(${t})`,(()=>{r.assign(s.default.vErrors,n._`${s.default.vErrors} === null ? ${t} : ${s.default.vErrors}.concat(${t})`).assign(s.default.errors,n._`${s.default.vErrors}.length`),(0,a.extendErrors)(e)}),(()=>e.error()))}(e,r)))}})),e.ok(null!==(r=t.valid)&&void 0!==r?r:y)},t.validSchemaType=function(e,t,r=!1){return!t.length||t.some((t=>"array"===t?Array.isArray(e):"object"===t?e&&"object"==typeof e&&!Array.isArray(e):typeof e==t||r&&void 0===e))},t.validateKeywordUsage=function({schema:e,opts:t,self:r,errSchemaPath:n},s,u){if(Array.isArray(s.keyword)?!s.keyword.includes(u):s.keyword!==u)throw new Error("ajv implementation error");const a=s.dependencies;if(null==a?void 0:a.some((t=>!Object.prototype.hasOwnProperty.call(e,t))))throw new Error(`parent schema must have dependencies of ${u}: ${a.join(",")}`);if(s.validateSchema&&!s.validateSchema(e[u])){const e=`keyword "${u}" value is invalid at path "${n}": `+r.errorsText(s.validateSchema.errors);if("log"!==t.validateSchema)throw new Error(e);r.logger.error(e)}}},88171:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.extendSubschemaMode=t.extendSubschemaData=t.getSubschema=void 0;const n=r(78380),s=r(78136);t.getSubschema=function(e,{keyword:t,schemaProp:r,schema:u,schemaPath:a,errSchemaPath:o,topSchemaRef:i}){if(void 0!==t&&void 0!==u)throw new Error('both "keyword" and "schema" passed, only one allowed');if(void 0!==t){const u=e.schema[t];return void 0===r?{schema:u,schemaPath:n._`${e.schemaPath}${(0,n.getProperty)(t)}`,errSchemaPath:`${e.errSchemaPath}/${t}`}:{schema:u[r],schemaPath:n._`${e.schemaPath}${(0,n.getProperty)(t)}${(0,n.getProperty)(r)}`,errSchemaPath:`${e.errSchemaPath}/${t}/${(0,s.escapeFragment)(r)}`}}if(void 0!==u){if(void 0===a||void 0===o||void 0===i)throw new Error('"schemaPath", "errSchemaPath" and "topSchemaRef" are required with "schema"');return{schema:u,schemaPath:a,topSchemaRef:i,errSchemaPath:o}}throw new Error('either "keyword" or "schema" must be passed')},t.extendSubschemaData=function(e,t,{dataProp:r,dataPropType:u,data:a,dataTypes:o,propertyName:i}){if(void 0!==a&&void 0!==r)throw new Error('both "data" and "dataProp" passed, only one allowed');const{gen:c}=t;if(void 0!==r){const{errorPath:a,dataPathArr:o,opts:i}=t;d(c.let("data",n._`${t.data}${(0,n.getProperty)(r)}`,!0)),e.errorPath=n.str`${a}${(0,s.getErrorPath)(r,u,i.jsPropertySyntax)}`,e.parentDataProperty=n._`${r}`,e.dataPathArr=[...o,e.parentDataProperty]}function d(r){e.data=r,e.dataLevel=t.dataLevel+1,e.dataTypes=[],t.definedProperties=new Set,e.parentData=t.data,e.dataNames=[...t.dataNames,r]}void 0!==a&&(d(a instanceof n.Name?a:c.let("data",a,!0)),void 0!==i&&(e.propertyName=i)),o&&(e.dataTypes=o)},t.extendSubschemaMode=function(e,{jtdDiscriminator:t,jtdMetadata:r,compositeRule:n,createErrors:s,allErrors:u}){void 0!==n&&(e.compositeRule=n),void 0!==s&&(e.createErrors=s),void 0!==u&&(e.allErrors=u),e.jtdDiscriminator=t,e.jtdMetadata=r}},42089:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CodeGen=t.Name=t.nil=t.stringify=t.str=t._=t.KeywordCxt=void 0;var n=r(58941);Object.defineProperty(t,"KeywordCxt",{enumerable:!0,get:function(){return n.KeywordCxt}});var s=r(78380);Object.defineProperty(t,"_",{enumerable:!0,get:function(){return s._}}),Object.defineProperty(t,"str",{enumerable:!0,get:function(){return s.str}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return s.stringify}}),Object.defineProperty(t,"nil",{enumerable:!0,get:function(){return s.nil}}),Object.defineProperty(t,"Name",{enumerable:!0,get:function(){return s.Name}}),Object.defineProperty(t,"CodeGen",{enumerable:!0,get:function(){return s.CodeGen}});const u=r(52582),a=r(61326),o=r(68817),i=r(56862),c=r(78380),d=r(20394),l=r(24307),f=r(78136),h=r(80705),p=r(63886),m=(e,t)=>new RegExp(e,t);m.code="new RegExp";const D=["removeAdditional","useDefaults","coerceTypes"],y=new Set(["validate","serialize","parse","wrapper","root","schema","keyword","pattern","formats","validate$data","func","obj","Error"]),g={errorDataPath:"",format:"`validateFormats: false` can be used instead.",nullable:'"nullable" keyword is supported by default.',jsonPointers:"Deprecated jsPropertySyntax can be used instead.",extendRefs:"Deprecated ignoreKeywordsWithRef can be used instead.",missingRefs:"Pass empty schema with $id that should be ignored to ajv.addSchema.",processCode:"Use option `code: {process: (code, schemaEnv: object) => string}`",sourceCode:"Use option `code: {source: true}`",strictDefaults:"It is default now, see option `strict`.",strictKeywords:"It is default now, see option `strict`.",uniqueItems:'"uniqueItems" keyword is always validated.',unknownFormats:"Disable strict mode or pass `true` to `ajv.addFormat` (or `formats` option).",cache:"Map is used as cache, schema object as key.",serialize:"Map is used as cache, schema object as key.",ajvErrors:"It is default now."},v={ignoreKeywordsWithRef:"",jsPropertySyntax:"",unicode:'"minLength"/"maxLength" account for unicode characters by default.'};function E(e){var t,r,n,s,u,a,o,i,c,d,l,f,h,D,y,g,v,E,C,$,F,A,w,_,b;const S=e.strict,P=null===(t=e.code)||void 0===t?void 0:t.optimize,B=!0===P||void 0===P?1:P||0,k=null!==(n=null===(r=e.code)||void 0===r?void 0:r.regExp)&&void 0!==n?n:m,j=null!==(s=e.uriResolver)&&void 0!==s?s:p.default;return{strictSchema:null===(a=null!==(u=e.strictSchema)&&void 0!==u?u:S)||void 0===a||a,strictNumbers:null===(i=null!==(o=e.strictNumbers)&&void 0!==o?o:S)||void 0===i||i,strictTypes:null!==(d=null!==(c=e.strictTypes)&&void 0!==c?c:S)&&void 0!==d?d:"log",strictTuples:null!==(f=null!==(l=e.strictTuples)&&void 0!==l?l:S)&&void 0!==f?f:"log",strictRequired:null!==(D=null!==(h=e.strictRequired)&&void 0!==h?h:S)&&void 0!==D&&D,code:e.code?{...e.code,optimize:B,regExp:k}:{optimize:B,regExp:k},loopRequired:null!==(y=e.loopRequired)&&void 0!==y?y:200,loopEnum:null!==(g=e.loopEnum)&&void 0!==g?g:200,meta:null===(v=e.meta)||void 0===v||v,messages:null===(E=e.messages)||void 0===E||E,inlineRefs:null===(C=e.inlineRefs)||void 0===C||C,schemaId:null!==($=e.schemaId)&&void 0!==$?$:"$id",addUsedSchema:null===(F=e.addUsedSchema)||void 0===F||F,validateSchema:null===(A=e.validateSchema)||void 0===A||A,validateFormats:null===(w=e.validateFormats)||void 0===w||w,unicodeRegExp:null===(_=e.unicodeRegExp)||void 0===_||_,int32range:null===(b=e.int32range)||void 0===b||b,uriResolver:j}}class C{constructor(e={}){this.schemas={},this.refs={},this.formats={},this._compilations=new Set,this._loading={},this._cache=new Map,e=this.opts={...e,...E(e)};const{es5:t,lines:r}=this.opts.code;this.scope=new c.ValueScope({scope:{},prefixes:y,es5:t,lines:r}),this.logger=function(e){if(!1===e)return S;if(void 0===e)return console;if(e.log&&e.warn&&e.error)return e;throw new Error("logger must implement log, warn and error methods")}(e.logger);const n=e.validateFormats;e.validateFormats=!1,this.RULES=(0,o.getRules)(),$.call(this,g,e,"NOT SUPPORTED"),$.call(this,v,e,"DEPRECATED","warn"),this._metaOpts=b.call(this),e.formats&&w.call(this),this._addVocabularies(),this._addDefaultMetaSchema(),e.keywords&&_.call(this,e.keywords),"object"==typeof e.meta&&this.addMetaSchema(e.meta),A.call(this),e.validateFormats=n}_addVocabularies(){this.addKeyword("$async")}_addDefaultMetaSchema(){const{$data:e,meta:t,schemaId:r}=this.opts;let n=h;"id"===r&&(n={...h},n.id=n.$id,delete n.$id),t&&e&&this.addMetaSchema(n,n[r],!1)}defaultMeta(){const{meta:e,schemaId:t}=this.opts;return this.opts.defaultMeta="object"==typeof e?e[t]||e:void 0}validate(e,t){let r;if("string"==typeof e){if(r=this.getSchema(e),!r)throw new Error(`no schema with key or ref "${e}"`)}else r=this.compile(e);const n=r(t);return"$async"in r||(this.errors=r.errors),n}compile(e,t){const r=this._addSchema(e,t);return r.validate||this._compileSchemaEnv(r)}compileAsync(e,t){if("function"!=typeof this.opts.loadSchema)throw new Error("options.loadSchema should be a function");const{loadSchema:r}=this.opts;return n.call(this,e,t);async function n(e,t){await s.call(this,e.$schema);const r=this._addSchema(e,t);return r.validate||u.call(this,r)}async function s(e){e&&!this.getSchema(e)&&await n.call(this,{$ref:e},!0)}async function u(e){try{return this._compileSchemaEnv(e)}catch(t){if(!(t instanceof a.default))throw t;return o.call(this,t),await i.call(this,t.missingSchema),u.call(this,e)}}function o({missingSchema:e,missingRef:t}){if(this.refs[e])throw new Error(`AnySchema ${e} is loaded but ${t} cannot be resolved`)}async function i(e){const r=await c.call(this,e);this.refs[e]||await s.call(this,r.$schema),this.refs[e]||this.addSchema(r,e,t)}async function c(e){const t=this._loading[e];if(t)return t;try{return await(this._loading[e]=r(e))}finally{delete this._loading[e]}}}addSchema(e,t,r,n=this.opts.validateSchema){if(Array.isArray(e)){for(const t of e)this.addSchema(t,void 0,r,n);return this}let s;if("object"==typeof e){const{schemaId:t}=this.opts;if(s=e[t],void 0!==s&&"string"!=typeof s)throw new Error(`schema ${t} must be string`)}return t=(0,d.normalizeId)(t||s),this._checkUnique(t),this.schemas[t]=this._addSchema(e,r,t,n,!0),this}addMetaSchema(e,t,r=this.opts.validateSchema){return this.addSchema(e,t,!0,r),this}validateSchema(e,t){if("boolean"==typeof e)return!0;let r;if(r=e.$schema,void 0!==r&&"string"!=typeof r)throw new Error("$schema must be a string");if(r=r||this.opts.defaultMeta||this.defaultMeta(),!r)return this.logger.warn("meta-schema not available"),this.errors=null,!0;const n=this.validate(r,e);if(!n&&t){const e="schema is invalid: "+this.errorsText();if("log"!==this.opts.validateSchema)throw new Error(e);this.logger.error(e)}return n}getSchema(e){let t;for(;"string"==typeof(t=F.call(this,e));)e=t;if(void 0===t){const{schemaId:r}=this.opts,n=new i.SchemaEnv({schema:{},schemaId:r});if(t=i.resolveSchema.call(this,n,e),!t)return;this.refs[e]=t}return t.validate||this._compileSchemaEnv(t)}removeSchema(e){if(e instanceof RegExp)return this._removeAllSchemas(this.schemas,e),this._removeAllSchemas(this.refs,e),this;switch(typeof e){case"undefined":return this._removeAllSchemas(this.schemas),this._removeAllSchemas(this.refs),this._cache.clear(),this;case"string":{const t=F.call(this,e);return"object"==typeof t&&this._cache.delete(t.schema),delete this.schemas[e],delete this.refs[e],this}case"object":{const t=e;this._cache.delete(t);let r=e[this.opts.schemaId];return r&&(r=(0,d.normalizeId)(r),delete this.schemas[r],delete this.refs[r]),this}default:throw new Error("ajv.removeSchema: invalid parameter")}}addVocabulary(e){for(const t of e)this.addKeyword(t);return this}addKeyword(e,t){let r;if("string"==typeof e)r=e,"object"==typeof t&&(this.logger.warn("these parameters are deprecated, see docs for addKeyword"),t.keyword=r);else{if("object"!=typeof e||void 0!==t)throw new Error("invalid addKeywords parameters");if(r=(t=e).keyword,Array.isArray(r)&&!r.length)throw new Error("addKeywords: keyword must be string or non-empty array")}if(B.call(this,r,t),!t)return(0,f.eachItem)(r,(e=>k.call(this,e))),this;N.call(this,t);const n={...t,type:(0,l.getJSONTypes)(t.type),schemaType:(0,l.getJSONTypes)(t.schemaType)};return(0,f.eachItem)(r,0===n.type.length?e=>k.call(this,e,n):e=>n.type.forEach((t=>k.call(this,e,n,t)))),this}getKeyword(e){const t=this.RULES.all[e];return"object"==typeof t?t.definition:!!t}removeKeyword(e){const{RULES:t}=this;delete t.keywords[e],delete t.all[e];for(const r of t.rules){const t=r.rules.findIndex((t=>t.keyword===e));t>=0&&r.rules.splice(t,1)}return this}addFormat(e,t){return"string"==typeof t&&(t=new RegExp(t)),this.formats[e]=t,this}errorsText(e=this.errors,{separator:t=", ",dataVar:r="data"}={}){return e&&0!==e.length?e.map((e=>`${r}${e.instancePath} ${e.message}`)).reduce(((e,r)=>e+t+r)):"No errors"}$dataMetaSchema(e,t){const r=this.RULES.all;e=JSON.parse(JSON.stringify(e));for(const n of t){const t=n.split("/").slice(1);let s=e;for(const e of t)s=s[e];for(const e in r){const t=r[e];if("object"!=typeof t)continue;const{$data:n}=t.definition,u=s[e];n&&u&&(s[e]=O(u))}}return e}_removeAllSchemas(e,t){for(const r in e){const n=e[r];t&&!t.test(r)||("string"==typeof n?delete e[r]:n&&!n.meta&&(this._cache.delete(n.schema),delete e[r]))}}_addSchema(e,t,r,n=this.opts.validateSchema,s=this.opts.addUsedSchema){let u;const{schemaId:a}=this.opts;if("object"==typeof e)u=e[a];else{if(this.opts.jtd)throw new Error("schema must be object");if("boolean"!=typeof e)throw new Error("schema must be object or boolean")}let o=this._cache.get(e);if(void 0!==o)return o;r=(0,d.normalizeId)(u||r);const c=d.getSchemaRefs.call(this,e,r);return o=new i.SchemaEnv({schema:e,schemaId:a,meta:t,baseId:r,localRefs:c}),this._cache.set(o.schema,o),s&&!r.startsWith("#")&&(r&&this._checkUnique(r),this.refs[r]=o),n&&this.validateSchema(e,!0),o}_checkUnique(e){if(this.schemas[e]||this.refs[e])throw new Error(`schema with key or id "${e}" already exists`)}_compileSchemaEnv(e){if(e.meta?this._compileMetaSchema(e):i.compileSchema.call(this,e),!e.validate)throw new Error("ajv implementation error");return e.validate}_compileMetaSchema(e){const t=this.opts;this.opts=this._metaOpts;try{i.compileSchema.call(this,e)}finally{this.opts=t}}}function $(e,t,r,n="error"){for(const s in e){const u=s;u in t&&this.logger[n](`${r}: option ${s}. ${e[u]}`)}}function F(e){return e=(0,d.normalizeId)(e),this.schemas[e]||this.refs[e]}function A(){const e=this.opts.schemas;if(e)if(Array.isArray(e))this.addSchema(e);else for(const t in e)this.addSchema(e[t],t)}function w(){for(const e in this.opts.formats){const t=this.opts.formats[e];t&&this.addFormat(e,t)}}function _(e){if(Array.isArray(e))this.addVocabulary(e);else{this.logger.warn("keywords option as map is deprecated, pass array");for(const t in e){const r=e[t];r.keyword||(r.keyword=t),this.addKeyword(r)}}}function b(){const e={...this.opts};for(const t of D)delete e[t];return e}t.default=C,C.ValidationError=u.default,C.MissingRefError=a.default;const S={log(){},warn(){},error(){}},P=/^[a-z_$][a-z0-9_$:-]*$/i;function B(e,t){const{RULES:r}=this;if((0,f.eachItem)(e,(e=>{if(r.keywords[e])throw new Error(`Keyword ${e} is already defined`);if(!P.test(e))throw new Error(`Keyword ${e} has invalid name`)})),t&&t.$data&&!("code"in t)&&!("validate"in t))throw new Error('$data keyword must have "code" or "validate" function')}function k(e,t,r){var n;const s=null==t?void 0:t.post;if(r&&s)throw new Error('keyword with "post" flag cannot have "type"');const{RULES:u}=this;let a=s?u.post:u.rules.find((({type:e})=>e===r));if(a||(a={type:r,rules:[]},u.rules.push(a)),u.keywords[e]=!0,!t)return;const o={keyword:e,definition:{...t,type:(0,l.getJSONTypes)(t.type),schemaType:(0,l.getJSONTypes)(t.schemaType)}};t.before?j.call(this,a,o,t.before):a.rules.push(o),u.all[e]=o,null===(n=t.implements)||void 0===n||n.forEach((e=>this.addKeyword(e)))}function j(e,t,r){const n=e.rules.findIndex((e=>e.keyword===r));n>=0?e.rules.splice(n,0,t):(e.rules.push(t),this.logger.warn(`rule ${r} is not defined`))}function N(e){let{metaSchema:t}=e;void 0!==t&&(e.$data&&this.opts.$data&&(t=O(t)),e.validateSchema=this.compile(t,!0))}const x={$ref:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#"};function O(e){return{anyOf:[e,x]}}},16898:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(66471);n.code='require("ajv/dist/runtime/equal").default',t.default=n},59228:(e,t)=>{"use strict";function r(e){const t=e.length;let r,n=0,s=0;for(;s<t;)n++,r=e.charCodeAt(s++),r>=55296&&r<=56319&&s<t&&(r=e.charCodeAt(s),56320==(64512&r)&&s++);return n}Object.defineProperty(t,"__esModule",{value:!0}),t.default=r,r.code='require("ajv/dist/runtime/ucs2length").default'},63886:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(22371);n.code='require("ajv/dist/runtime/uri").default',t.default=n},52582:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});class r extends Error{constructor(e){super("validation failed"),this.errors=e,this.ajv=this.validation=!0}}t.default=r},26970:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateAdditionalItems=void 0;const n=r(78380),s=r(78136),u={keyword:"additionalItems",type:"array",schemaType:["boolean","object"],before:"uniqueItems",error:{message:({params:{len:e}})=>n.str`must NOT have more than ${e} items`,params:({params:{len:e}})=>n._`{limit: ${e}}`},code(e){const{parentSchema:t,it:r}=e,{items:n}=t;Array.isArray(n)?a(e,n):(0,s.checkStrictMode)(r,'"additionalItems" is ignored when "items" is not an array of schemas')}};function a(e,t){const{gen:r,schema:u,data:a,keyword:o,it:i}=e;i.items=!0;const c=r.const("len",n._`${a}.length`);if(!1===u)e.setParams({len:t.length}),e.pass(n._`${c} <= ${t.length}`);else if("object"==typeof u&&!(0,s.alwaysValidSchema)(i,u)){const u=r.var("valid",n._`${c} <= ${t.length}`);r.if((0,n.not)(u),(()=>function(u){r.forRange("i",t.length,c,(t=>{e.subschema({keyword:o,dataProp:t,dataPropType:s.Type.Num},u),i.allErrors||r.if((0,n.not)(u),(()=>r.break()))}))}(u))),e.ok(u)}}t.validateAdditionalItems=a,t.default=u},3077:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(36461),s=r(78380),u=r(25668),a=r(78136),o={keyword:"additionalProperties",type:["object"],schemaType:["boolean","object"],allowUndefined:!0,trackErrors:!0,error:{message:"must NOT have additional properties",params:({params:e})=>s._`{additionalProperty: ${e.additionalProperty}}`},code(e){const{gen:t,schema:r,parentSchema:o,data:i,errsCount:c,it:d}=e;if(!c)throw new Error("ajv implementation error");const{allErrors:l,opts:f}=d;if(d.props=!0,"all"!==f.removeAdditional&&(0,a.alwaysValidSchema)(d,r))return;const h=(0,n.allSchemaProperties)(o.properties),p=(0,n.allSchemaProperties)(o.patternProperties);function m(e){t.code(s._`delete ${i}[${e}]`)}function D(n){if("all"===f.removeAdditional||f.removeAdditional&&!1===r)m(n);else{if(!1===r)return e.setParams({additionalProperty:n}),e.error(),void(l||t.break());if("object"==typeof r&&!(0,a.alwaysValidSchema)(d,r)){const r=t.name("valid");"failing"===f.removeAdditional?(y(n,r,!1),t.if((0,s.not)(r),(()=>{e.reset(),m(n)}))):(y(n,r),l||t.if((0,s.not)(r),(()=>t.break())))}}}function y(t,r,n){const s={keyword:"additionalProperties",dataProp:t,dataPropType:a.Type.Str};!1===n&&Object.assign(s,{compositeRule:!0,createErrors:!1,allErrors:!1}),e.subschema(s,r)}t.forIn("key",i,(r=>{h.length||p.length?t.if(function(r){let u;if(h.length>8){const e=(0,a.schemaRefOrVal)(d,o.properties,"properties");u=(0,n.isOwnProperty)(t,e,r)}else u=h.length?(0,s.or)(...h.map((e=>s._`${r} === ${e}`))):s.nil;return p.length&&(u=(0,s.or)(u,...p.map((t=>s._`${(0,n.usePattern)(e,t)}.test(${r})`)))),(0,s.not)(u)}(r),(()=>D(r))):D(r)})),e.ok(s._`${c} === ${u.default.errors}`)}};t.default=o},22509:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(78136),s={keyword:"allOf",schemaType:"array",code(e){const{gen:t,schema:r,it:s}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");const u=t.name("valid");r.forEach(((t,r)=>{if((0,n.alwaysValidSchema)(s,t))return;const a=e.subschema({keyword:"allOf",schemaProp:r},u);e.ok(u),e.mergeEvaluated(a)}))}};t.default=s},30420:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n={keyword:"anyOf",schemaType:"array",trackErrors:!0,code:r(36461).validateUnion,error:{message:"must match a schema in anyOf"}};t.default=n},39194:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(78380),s=r(78136),u={keyword:"contains",type:"array",schemaType:["object","boolean"],before:"uniqueItems",trackErrors:!0,error:{message:({params:{min:e,max:t}})=>void 0===t?n.str`must contain at least ${e} valid item(s)`:n.str`must contain at least ${e} and no more than ${t} valid item(s)`,params:({params:{min:e,max:t}})=>void 0===t?n._`{minContains: ${e}}`:n._`{minContains: ${e}, maxContains: ${t}}`},code(e){const{gen:t,schema:r,parentSchema:u,data:a,it:o}=e;let i,c;const{minContains:d,maxContains:l}=u;o.opts.next?(i=void 0===d?1:d,c=l):i=1;const f=t.const("len",n._`${a}.length`);if(e.setParams({min:i,max:c}),void 0===c&&0===i)return void(0,s.checkStrictMode)(o,'"minContains" == 0 without "maxContains": "contains" keyword ignored');if(void 0!==c&&i>c)return(0,s.checkStrictMode)(o,'"minContains" > "maxContains" is always invalid'),void e.fail();if((0,s.alwaysValidSchema)(o,r)){let t=n._`${f} >= ${i}`;return void 0!==c&&(t=n._`${t} && ${f} <= ${c}`),void e.pass(t)}o.items=!0;const h=t.name("valid");function p(){const e=t.name("_valid"),r=t.let("count",0);m(e,(()=>t.if(e,(()=>function(e){t.code(n._`${e}++`),void 0===c?t.if(n._`${e} >= ${i}`,(()=>t.assign(h,!0).break())):(t.if(n._`${e} > ${c}`,(()=>t.assign(h,!1).break())),1===i?t.assign(h,!0):t.if(n._`${e} >= ${i}`,(()=>t.assign(h,!0))))}(r)))))}function m(r,n){t.forRange("i",0,f,(t=>{e.subschema({keyword:"contains",dataProp:t,dataPropType:s.Type.Num,compositeRule:!0},r),n()}))}void 0===c&&1===i?m(h,(()=>t.if(h,(()=>t.break())))):0===i?(t.let(h,!0),void 0!==c&&t.if(n._`${a}.length > 0`,p)):(t.let(h,!1),p()),e.result(h,(()=>e.reset()))}};t.default=u},84210:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateSchemaDeps=t.validatePropertyDeps=t.error=void 0;const n=r(78380),s=r(78136),u=r(36461);t.error={message:({params:{property:e,depsCount:t,deps:r}})=>{const s=1===t?"property":"properties";return n.str`must have ${s} ${r} when property ${e} is present`},params:({params:{property:e,depsCount:t,deps:r,missingProperty:s}})=>n._`{property: ${e},
    missingProperty: ${s},
    depsCount: ${t},
    deps: ${r}}`};const a={keyword:"dependencies",type:"object",schemaType:"object",error:t.error,code(e){const[t,r]=function({schema:e}){const t={},r={};for(const n in e)"__proto__"!==n&&((Array.isArray(e[n])?t:r)[n]=e[n]);return[t,r]}(e);o(e,t),i(e,r)}};function o(e,t=e.schema){const{gen:r,data:s,it:a}=e;if(0===Object.keys(t).length)return;const o=r.let("missing");for(const i in t){const c=t[i];if(0===c.length)continue;const d=(0,u.propertyInData)(r,s,i,a.opts.ownProperties);e.setParams({property:i,depsCount:c.length,deps:c.join(", ")}),a.allErrors?r.if(d,(()=>{for(const t of c)(0,u.checkReportMissingProp)(e,t)})):(r.if(n._`${d} && (${(0,u.checkMissingProp)(e,c,o)})`),(0,u.reportMissingProp)(e,o),r.else())}}function i(e,t=e.schema){const{gen:r,data:n,keyword:a,it:o}=e,i=r.name("valid");for(const c in t)(0,s.alwaysValidSchema)(o,t[c])||(r.if((0,u.propertyInData)(r,n,c,o.opts.ownProperties),(()=>{const t=e.subschema({keyword:a,schemaProp:c},i);e.mergeValidEvaluated(t,i)}),(()=>r.var(i,!0))),e.ok(i))}t.validatePropertyDeps=o,t.validateSchemaDeps=i,t.default=a},59125:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(78380),s=r(78136),u={keyword:"if",schemaType:["object","boolean"],trackErrors:!0,error:{message:({params:e})=>n.str`must match "${e.ifClause}" schema`,params:({params:e})=>n._`{failingKeyword: ${e.ifClause}}`},code(e){const{gen:t,parentSchema:r,it:u}=e;void 0===r.then&&void 0===r.else&&(0,s.checkStrictMode)(u,'"if" without "then" and "else" is ignored');const o=a(u,"then"),i=a(u,"else");if(!o&&!i)return;const c=t.let("valid",!0),d=t.name("_valid");if(function(){const t=e.subschema({keyword:"if",compositeRule:!0,createErrors:!1,allErrors:!1},d);e.mergeEvaluated(t)}(),e.reset(),o&&i){const r=t.let("ifClause");e.setParams({ifClause:r}),t.if(d,l("then",r),l("else",r))}else o?t.if(d,l("then")):t.if((0,n.not)(d),l("else"));function l(r,s){return()=>{const u=e.subschema({keyword:r},d);t.assign(c,d),e.mergeValidEvaluated(u,c),s?t.assign(s,n._`${r}`):e.setParams({ifClause:r})}}e.pass(c,(()=>e.error(!0)))}};function a(e,t){const r=e.schema[t];return void 0!==r&&!(0,s.alwaysValidSchema)(e,r)}t.default=u},74810:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(26970),s=r(65026),u=r(83775),a=r(65434),o=r(39194),i=r(84210),c=r(80217),d=r(3077),l=r(99474),f=r(48262),h=r(27599),p=r(30420),m=r(71408),D=r(22509),y=r(59125),g=r(25246);t.default=function(e=!1){const t=[h.default,p.default,m.default,D.default,y.default,g.default,c.default,d.default,i.default,l.default,f.default];return e?t.push(s.default,a.default):t.push(n.default,u.default),t.push(o.default),t}},83775:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateTuple=void 0;const n=r(78380),s=r(78136),u=r(36461),a={keyword:"items",type:"array",schemaType:["object","array","boolean"],before:"uniqueItems",code(e){const{schema:t,it:r}=e;if(Array.isArray(t))return o(e,"additionalItems",t);r.items=!0,(0,s.alwaysValidSchema)(r,t)||e.ok((0,u.validateArray)(e))}};function o(e,t,r=e.schema){const{gen:u,parentSchema:a,data:o,keyword:i,it:c}=e;!function(e){const{opts:n,errSchemaPath:u}=c,a=r.length,o=a===e.minItems&&(a===e.maxItems||!1===e[t]);if(n.strictTuples&&!o){const e=`"${i}" is ${a}-tuple, but minItems or maxItems/${t} are not specified or different at path "${u}"`;(0,s.checkStrictMode)(c,e,n.strictTuples)}}(a),c.opts.unevaluated&&r.length&&!0!==c.items&&(c.items=s.mergeEvaluated.items(u,r.length,c.items));const d=u.name("valid"),l=u.const("len",n._`${o}.length`);r.forEach(((t,r)=>{(0,s.alwaysValidSchema)(c,t)||(u.if(n._`${l} > ${r}`,(()=>e.subschema({keyword:i,schemaProp:r,dataProp:r},d))),e.ok(d))}))}t.validateTuple=o,t.default=a},65434:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(78380),s=r(78136),u=r(36461),a=r(26970),o={keyword:"items",type:"array",schemaType:["object","boolean"],before:"uniqueItems",error:{message:({params:{len:e}})=>n.str`must NOT have more than ${e} items`,params:({params:{len:e}})=>n._`{limit: ${e}}`},code(e){const{schema:t,parentSchema:r,it:n}=e,{prefixItems:o}=r;n.items=!0,(0,s.alwaysValidSchema)(n,t)||(o?(0,a.validateAdditionalItems)(e,o):e.ok((0,u.validateArray)(e)))}};t.default=o},27599:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(78136),s={keyword:"not",schemaType:["object","boolean"],trackErrors:!0,code(e){const{gen:t,schema:r,it:s}=e;if((0,n.alwaysValidSchema)(s,r))return void e.fail();const u=t.name("valid");e.subschema({keyword:"not",compositeRule:!0,createErrors:!1,allErrors:!1},u),e.failResult(u,(()=>e.reset()),(()=>e.error()))},error:{message:"must NOT be valid"}};t.default=s},71408:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(78380),s=r(78136),u={keyword:"oneOf",schemaType:"array",trackErrors:!0,error:{message:"must match exactly one schema in oneOf",params:({params:e})=>n._`{passingSchemas: ${e.passing}}`},code(e){const{gen:t,schema:r,parentSchema:u,it:a}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");if(a.opts.discriminator&&u.discriminator)return;const o=r,i=t.let("valid",!1),c=t.let("passing",null),d=t.name("_valid");e.setParams({passing:c}),t.block((function(){o.forEach(((r,u)=>{let o;(0,s.alwaysValidSchema)(a,r)?t.var(d,!0):o=e.subschema({keyword:"oneOf",schemaProp:u,compositeRule:!0},d),u>0&&t.if(n._`${d} && ${i}`).assign(i,!1).assign(c,n._`[${c}, ${u}]`).else(),t.if(d,(()=>{t.assign(i,!0),t.assign(c,u),o&&e.mergeEvaluated(o,n.Name)}))}))})),e.result(i,(()=>e.reset()),(()=>e.error(!0)))}};t.default=u},48262:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(36461),s=r(78380),u=r(78136),a=r(78136),o={keyword:"patternProperties",type:"object",schemaType:"object",code(e){const{gen:t,schema:r,data:o,parentSchema:i,it:c}=e,{opts:d}=c,l=(0,n.allSchemaProperties)(r),f=l.filter((e=>(0,u.alwaysValidSchema)(c,r[e])));if(0===l.length||f.length===l.length&&(!c.opts.unevaluated||!0===c.props))return;const h=d.strictSchema&&!d.allowMatchingProperties&&i.properties,p=t.name("valid");!0===c.props||c.props instanceof s.Name||(c.props=(0,a.evaluatedPropsToName)(t,c.props));const{props:m}=c;function D(e){for(const t in h)new RegExp(e).test(t)&&(0,u.checkStrictMode)(c,`property ${t} matches pattern ${e} (use allowMatchingProperties)`)}function y(r){t.forIn("key",o,(u=>{t.if(s._`${(0,n.usePattern)(e,r)}.test(${u})`,(()=>{const n=f.includes(r);n||e.subschema({keyword:"patternProperties",schemaProp:r,dataProp:u,dataPropType:a.Type.Str},p),c.opts.unevaluated&&!0!==m?t.assign(s._`${m}[${u}]`,!0):n||c.allErrors||t.if((0,s.not)(p),(()=>t.break()))}))}))}!function(){for(const e of l)h&&D(e),c.allErrors?y(e):(t.var(p,!0),y(e),t.if(p))}()}};t.default=o},65026:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(83775),s={keyword:"prefixItems",type:"array",schemaType:["array"],before:"uniqueItems",code:e=>(0,n.validateTuple)(e,"items")};t.default=s},99474:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(58941),s=r(36461),u=r(78136),a=r(3077),o={keyword:"properties",type:"object",schemaType:"object",code(e){const{gen:t,schema:r,parentSchema:o,data:i,it:c}=e;"all"===c.opts.removeAdditional&&void 0===o.additionalProperties&&a.default.code(new n.KeywordCxt(c,a.default,"additionalProperties"));const d=(0,s.allSchemaProperties)(r);for(const e of d)c.definedProperties.add(e);c.opts.unevaluated&&d.length&&!0!==c.props&&(c.props=u.mergeEvaluated.props(t,(0,u.toHash)(d),c.props));const l=d.filter((e=>!(0,u.alwaysValidSchema)(c,r[e])));if(0===l.length)return;const f=t.name("valid");for(const r of l)h(r)?p(r):(t.if((0,s.propertyInData)(t,i,r,c.opts.ownProperties)),p(r),c.allErrors||t.else().var(f,!0),t.endIf()),e.it.definedProperties.add(r),e.ok(f);function h(e){return c.opts.useDefaults&&!c.compositeRule&&void 0!==r[e].default}function p(t){e.subschema({keyword:"properties",schemaProp:t,dataProp:t},f)}}};t.default=o},80217:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(78380),s=r(78136),u={keyword:"propertyNames",type:"object",schemaType:["object","boolean"],error:{message:"property name must be valid",params:({params:e})=>n._`{propertyName: ${e.propertyName}}`},code(e){const{gen:t,schema:r,data:u,it:a}=e;if((0,s.alwaysValidSchema)(a,r))return;const o=t.name("valid");t.forIn("key",u,(r=>{e.setParams({propertyName:r}),e.subschema({keyword:"propertyNames",data:r,dataTypes:["string"],propertyName:r,compositeRule:!0},o),t.if((0,n.not)(o),(()=>{e.error(!0),a.allErrors||t.break()}))})),e.ok(o)}};t.default=u},25246:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(78136),s={keyword:["then","else"],schemaType:["object","boolean"],code({keyword:e,parentSchema:t,it:r}){void 0===t.if&&(0,n.checkStrictMode)(r,`"${e}" without "if" is ignored`)}};t.default=s},36461:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateUnion=t.validateArray=t.usePattern=t.callValidateCode=t.schemaProperties=t.allSchemaProperties=t.noPropertyInData=t.propertyInData=t.isOwnProperty=t.hasPropFunc=t.reportMissingProp=t.checkMissingProp=t.checkReportMissingProp=void 0;const n=r(78380),s=r(78136),u=r(25668),a=r(78136);function o(e){return e.scopeValue("func",{ref:Object.prototype.hasOwnProperty,code:n._`Object.prototype.hasOwnProperty`})}function i(e,t,r){return n._`${o(e)}.call(${t}, ${r})`}function c(e,t,r,s){const u=n._`${t}${(0,n.getProperty)(r)} === undefined`;return s?(0,n.or)(u,(0,n.not)(i(e,t,r))):u}function d(e){return e?Object.keys(e).filter((e=>"__proto__"!==e)):[]}t.checkReportMissingProp=function(e,t){const{gen:r,data:s,it:u}=e;r.if(c(r,s,t,u.opts.ownProperties),(()=>{e.setParams({missingProperty:n._`${t}`},!0),e.error()}))},t.checkMissingProp=function({gen:e,data:t,it:{opts:r}},s,u){return(0,n.or)(...s.map((s=>(0,n.and)(c(e,t,s,r.ownProperties),n._`${u} = ${s}`))))},t.reportMissingProp=function(e,t){e.setParams({missingProperty:t},!0),e.error()},t.hasPropFunc=o,t.isOwnProperty=i,t.propertyInData=function(e,t,r,s){const u=n._`${t}${(0,n.getProperty)(r)} !== undefined`;return s?n._`${u} && ${i(e,t,r)}`:u},t.noPropertyInData=c,t.allSchemaProperties=d,t.schemaProperties=function(e,t){return d(t).filter((r=>!(0,s.alwaysValidSchema)(e,t[r])))},t.callValidateCode=function({schemaCode:e,data:t,it:{gen:r,topSchemaRef:s,schemaPath:a,errorPath:o},it:i},c,d,l){const f=l?n._`${e}, ${t}, ${s}${a}`:t,h=[[u.default.instancePath,(0,n.strConcat)(u.default.instancePath,o)],[u.default.parentData,i.parentData],[u.default.parentDataProperty,i.parentDataProperty],[u.default.rootData,u.default.rootData]];i.opts.dynamicRef&&h.push([u.default.dynamicAnchors,u.default.dynamicAnchors]);const p=n._`${f}, ${r.object(...h)}`;return d!==n.nil?n._`${c}.call(${d}, ${p})`:n._`${c}(${p})`};const l=n._`new RegExp`;t.usePattern=function({gen:e,it:{opts:t}},r){const s=t.unicodeRegExp?"u":"",{regExp:u}=t.code,o=u(r,s);return e.scopeValue("pattern",{key:o.toString(),ref:o,code:n._`${"new RegExp"===u.code?l:(0,a.useFunc)(e,u)}(${r}, ${s})`})},t.validateArray=function(e){const{gen:t,data:r,keyword:u,it:a}=e,o=t.name("valid");if(a.allErrors){const e=t.let("valid",!0);return i((()=>t.assign(e,!1))),e}return t.var(o,!0),i((()=>t.break())),o;function i(a){const i=t.const("len",n._`${r}.length`);t.forRange("i",0,i,(r=>{e.subschema({keyword:u,dataProp:r,dataPropType:s.Type.Num},o),t.if((0,n.not)(o),a)}))}},t.validateUnion=function(e){const{gen:t,schema:r,keyword:u,it:a}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");if(r.some((e=>(0,s.alwaysValidSchema)(a,e)))&&!a.opts.unevaluated)return;const o=t.let("valid",!1),i=t.name("_valid");t.block((()=>r.forEach(((r,s)=>{const a=e.subschema({keyword:u,schemaProp:s,compositeRule:!0},i);t.assign(o,n._`${o} || ${i}`),e.mergeValidEvaluated(a,i)||t.if((0,n.not)(o))})))),e.result(o,(()=>e.reset()),(()=>e.error(!0)))}},53176:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r={keyword:"id",code(){throw new Error('NOT SUPPORTED: keyword "id", use "$id" for schema ID')}};t.default=r},61324:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(53176),s=r(91873),u=["$schema","$id","$defs","$vocabulary",{keyword:"$comment"},"definitions",n.default,s.default];t.default=u},91873:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.callRef=t.getValidate=void 0;const n=r(61326),s=r(36461),u=r(78380),a=r(25668),o=r(56862),i=r(78136),c={keyword:"$ref",schemaType:"string",code(e){const{gen:t,schema:r,it:s}=e,{baseId:a,schemaEnv:i,validateName:c,opts:f,self:h}=s,{root:p}=i;if(("#"===r||"#/"===r)&&a===p.baseId)return function(){if(i===p)return l(e,c,i,i.$async);const r=t.scopeValue("root",{ref:p});return l(e,u._`${r}.validate`,p,p.$async)}();const m=o.resolveRef.call(h,p,a,r);if(void 0===m)throw new n.default(s.opts.uriResolver,a,r);return m instanceof o.SchemaEnv?function(t){const r=d(e,t);l(e,r,t,t.$async)}(m):function(n){const s=t.scopeValue("schema",!0===f.code.source?{ref:n,code:(0,u.stringify)(n)}:{ref:n}),a=t.name("valid"),o=e.subschema({schema:n,dataTypes:[],schemaPath:u.nil,topSchemaRef:s,errSchemaPath:r},a);e.mergeEvaluated(o),e.ok(a)}(m)}};function d(e,t){const{gen:r}=e;return t.validate?r.scopeValue("validate",{ref:t.validate}):u._`${r.scopeValue("wrapper",{ref:t})}.validate`}function l(e,t,r,n){const{gen:o,it:c}=e,{allErrors:d,schemaEnv:l,opts:f}=c,h=f.passContext?a.default.this:u.nil;function p(e){const t=u._`${e}.errors`;o.assign(a.default.vErrors,u._`${a.default.vErrors} === null ? ${t} : ${a.default.vErrors}.concat(${t})`),o.assign(a.default.errors,u._`${a.default.vErrors}.length`)}function m(e){var t;if(!c.opts.unevaluated)return;const n=null===(t=null==r?void 0:r.validate)||void 0===t?void 0:t.evaluated;if(!0!==c.props)if(n&&!n.dynamicProps)void 0!==n.props&&(c.props=i.mergeEvaluated.props(o,n.props,c.props));else{const t=o.var("props",u._`${e}.evaluated.props`);c.props=i.mergeEvaluated.props(o,t,c.props,u.Name)}if(!0!==c.items)if(n&&!n.dynamicItems)void 0!==n.items&&(c.items=i.mergeEvaluated.items(o,n.items,c.items));else{const t=o.var("items",u._`${e}.evaluated.items`);c.items=i.mergeEvaluated.items(o,t,c.items,u.Name)}}n?function(){if(!l.$async)throw new Error("async schema referenced by sync schema");const r=o.let("valid");o.try((()=>{o.code(u._`await ${(0,s.callValidateCode)(e,t,h)}`),m(t),d||o.assign(r,!0)}),(e=>{o.if(u._`!(${e} instanceof ${c.ValidationError})`,(()=>o.throw(e))),p(e),d||o.assign(r,!1)})),e.ok(r)}():e.result((0,s.callValidateCode)(e,t,h),(()=>m(t)),(()=>p(t)))}t.getValidate=d,t.callRef=l,t.default=c},60867:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(78380),s=r(60987),u=r(56862),a=r(78136),o={keyword:"discriminator",type:"object",schemaType:"object",error:{message:({params:{discrError:e,tagName:t}})=>e===s.DiscrError.Tag?`tag "${t}" must be string`:`value of tag "${t}" must be in oneOf`,params:({params:{discrError:e,tag:t,tagName:r}})=>n._`{error: ${e}, tag: ${r}, tagValue: ${t}}`},code(e){const{gen:t,data:r,schema:o,parentSchema:i,it:c}=e,{oneOf:d}=i;if(!c.opts.discriminator)throw new Error("discriminator: requires discriminator option");const l=o.propertyName;if("string"!=typeof l)throw new Error("discriminator: requires propertyName");if(o.mapping)throw new Error("discriminator: mapping is not supported");if(!d)throw new Error("discriminator: requires oneOf keyword");const f=t.let("valid",!1),h=t.const("tag",n._`${r}${(0,n.getProperty)(l)}`);function p(r){const s=t.name("valid"),u=e.subschema({keyword:"oneOf",schemaProp:r},s);return e.mergeEvaluated(u,n.Name),s}t.if(n._`typeof ${h} == "string"`,(()=>function(){const r=function(){var e;const t={},r=s(i);let n=!0;for(let t=0;t<d.length;t++){let i=d[t];(null==i?void 0:i.$ref)&&!(0,a.schemaHasRulesButRef)(i,c.self.RULES)&&(i=u.resolveRef.call(c.self,c.schemaEnv.root,c.baseId,null==i?void 0:i.$ref),i instanceof u.SchemaEnv&&(i=i.schema));const f=null===(e=null==i?void 0:i.properties)||void 0===e?void 0:e[l];if("object"!=typeof f)throw new Error(`discriminator: oneOf subschemas (or referenced schemas) must have "properties/${l}"`);n=n&&(r||s(i)),o(f,t)}if(!n)throw new Error(`discriminator: "${l}" must be required`);return t;function s({required:e}){return Array.isArray(e)&&e.includes(l)}function o(e,t){if(e.const)f(e.const,t);else{if(!e.enum)throw new Error(`discriminator: "properties/${l}" must have "const" or "enum"`);for(const r of e.enum)f(r,t)}}function f(e,r){if("string"!=typeof e||e in t)throw new Error(`discriminator: "${l}" values must be unique strings`);t[e]=r}}();t.if(!1);for(const e in r)t.elseIf(n._`${h} === ${e}`),t.assign(f,p(r[e]));t.else(),e.error(!1,{discrError:s.DiscrError.Mapping,tag:h,tagName:l}),t.endIf()}()),(()=>e.error(!1,{discrError:s.DiscrError.Tag,tag:h,tagName:l}))),e.ok(f)}};t.default=o},60987:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.DiscrError=void 0,(r=t.DiscrError||(t.DiscrError={})).Tag="tag",r.Mapping="mapping"},43662:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(61324),s=r(13959),u=r(74810),a=r(89119),o=r(97808),i=[n.default,s.default,(0,u.default)(),a.default,o.metadataVocabulary,o.contentVocabulary];t.default=i},94948:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(78380),s={keyword:"format",type:["number","string"],schemaType:"string",$data:!0,error:{message:({schemaCode:e})=>n.str`must match format "${e}"`,params:({schemaCode:e})=>n._`{format: ${e}}`},code(e,t){const{gen:r,data:s,$data:u,schema:a,schemaCode:o,it:i}=e,{opts:c,errSchemaPath:d,schemaEnv:l,self:f}=i;c.validateFormats&&(u?function(){const u=r.scopeValue("formats",{ref:f.formats,code:c.code.formats}),a=r.const("fDef",n._`${u}[${o}]`),i=r.let("fType"),d=r.let("format");r.if(n._`typeof ${a} == "object" && !(${a} instanceof RegExp)`,(()=>r.assign(i,n._`${a}.type || "string"`).assign(d,n._`${a}.validate`)),(()=>r.assign(i,n._`"string"`).assign(d,a))),e.fail$data((0,n.or)(!1===c.strictSchema?n.nil:n._`${o} && !${d}`,function(){const e=l.$async?n._`(${a}.async ? await ${d}(${s}) : ${d}(${s}))`:n._`${d}(${s})`,r=n._`(typeof ${d} == "function" ? ${e} : ${d}.test(${s}))`;return n._`${d} && ${d} !== true && ${i} === ${t} && !${r}`}()))}():function(){const u=f.formats[a];if(!u)return void function(){if(!1!==c.strictSchema)throw new Error(e());function e(){return`unknown format "${a}" ignored in schema at path "${d}"`}f.logger.warn(e())}();if(!0===u)return;const[o,i,h]=function(e){const t=e instanceof RegExp?(0,n.regexpCode)(e):c.code.formats?n._`${c.code.formats}${(0,n.getProperty)(a)}`:void 0,s=r.scopeValue("formats",{key:a,ref:e,code:t});return"object"!=typeof e||e instanceof RegExp?["string",e,s]:[e.type||"string",e.validate,n._`${s}.validate`]}(u);o===t&&e.pass(function(){if("object"==typeof u&&!(u instanceof RegExp)&&u.async){if(!l.$async)throw new Error("async format in sync schema");return n._`await ${h}(${s})`}return"function"==typeof i?n._`${h}(${s})`:n._`${h}.test(${s})`}())}())}};t.default=s},89119:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=[r(94948).default];t.default=n},97808:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.contentVocabulary=t.metadataVocabulary=void 0,t.metadataVocabulary=["title","description","default","deprecated","readOnly","writeOnly","examples"],t.contentVocabulary=["contentMediaType","contentEncoding","contentSchema"]},72156:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(78380),s=r(78136),u=r(16898),a={keyword:"const",$data:!0,error:{message:"must be equal to constant",params:({schemaCode:e})=>n._`{allowedValue: ${e}}`},code(e){const{gen:t,data:r,$data:a,schemaCode:o,schema:i}=e;a||i&&"object"==typeof i?e.fail$data(n._`!${(0,s.useFunc)(t,u.default)}(${r}, ${o})`):e.fail(n._`${i} !== ${r}`)}};t.default=a},84010:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(78380),s=r(78136),u=r(16898),a={keyword:"enum",schemaType:"array",$data:!0,error:{message:"must be equal to one of the allowed values",params:({schemaCode:e})=>n._`{allowedValues: ${e}}`},code(e){const{gen:t,data:r,$data:a,schema:o,schemaCode:i,it:c}=e;if(!a&&0===o.length)throw new Error("enum must have non-empty array");const d=o.length>=c.opts.loopEnum;let l;const f=()=>null!=l?l:l=(0,s.useFunc)(t,u.default);let h;if(d||a)h=t.let("valid"),e.block$data(h,(function(){t.assign(h,!1),t.forOf("v",i,(e=>t.if(n._`${f()}(${r}, ${e})`,(()=>t.assign(h,!0).break()))))}));else{if(!Array.isArray(o))throw new Error("ajv implementation error");const e=t.const("vSchema",i);h=(0,n.or)(...o.map(((t,s)=>function(e,t){const s=o[t];return"object"==typeof s&&null!==s?n._`${f()}(${r}, ${e}[${t}])`:n._`${r} === ${s}`}(e,s))))}e.pass(h)}};t.default=a},13959:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(79765),s=r(89061),u=r(22304),a=r(27253),o=r(45757),i=r(93516),c=r(92270),d=r(17998),l=r(72156),f=r(84010),h=[n.default,s.default,u.default,a.default,o.default,i.default,c.default,d.default,{keyword:"type",schemaType:["string","array"]},{keyword:"nullable",schemaType:"boolean"},l.default,f.default];t.default=h},92270:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(78380),s={keyword:["maxItems","minItems"],type:"array",schemaType:"number",$data:!0,error:{message({keyword:e,schemaCode:t}){const r="maxItems"===e?"more":"fewer";return n.str`must NOT have ${r} than ${t} items`},params:({schemaCode:e})=>n._`{limit: ${e}}`},code(e){const{keyword:t,data:r,schemaCode:s}=e,u="maxItems"===t?n.operators.GT:n.operators.LT;e.fail$data(n._`${r}.length ${u} ${s}`)}};t.default=s},22304:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(78380),s=r(78136),u=r(59228),a={keyword:["maxLength","minLength"],type:"string",schemaType:"number",$data:!0,error:{message({keyword:e,schemaCode:t}){const r="maxLength"===e?"more":"fewer";return n.str`must NOT have ${r} than ${t} characters`},params:({schemaCode:e})=>n._`{limit: ${e}}`},code(e){const{keyword:t,data:r,schemaCode:a,it:o}=e,i="maxLength"===t?n.operators.GT:n.operators.LT,c=!1===o.opts.unicode?n._`${r}.length`:n._`${(0,s.useFunc)(e.gen,u.default)}(${r})`;e.fail$data(n._`${c} ${i} ${a}`)}};t.default=a},79765:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(78380),s=n.operators,u={maximum:{okStr:"<=",ok:s.LTE,fail:s.GT},minimum:{okStr:">=",ok:s.GTE,fail:s.LT},exclusiveMaximum:{okStr:"<",ok:s.LT,fail:s.GTE},exclusiveMinimum:{okStr:">",ok:s.GT,fail:s.LTE}},a={message:({keyword:e,schemaCode:t})=>n.str`must be ${u[e].okStr} ${t}`,params:({keyword:e,schemaCode:t})=>n._`{comparison: ${u[e].okStr}, limit: ${t}}`},o={keyword:Object.keys(u),type:"number",schemaType:"number",$data:!0,error:a,code(e){const{keyword:t,data:r,schemaCode:s}=e;e.fail$data(n._`${r} ${u[t].fail} ${s} || isNaN(${r})`)}};t.default=o},45757:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(78380),s={keyword:["maxProperties","minProperties"],type:"object",schemaType:"number",$data:!0,error:{message({keyword:e,schemaCode:t}){const r="maxProperties"===e?"more":"fewer";return n.str`must NOT have ${r} than ${t} properties`},params:({schemaCode:e})=>n._`{limit: ${e}}`},code(e){const{keyword:t,data:r,schemaCode:s}=e,u="maxProperties"===t?n.operators.GT:n.operators.LT;e.fail$data(n._`Object.keys(${r}).length ${u} ${s}`)}};t.default=s},89061:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(78380),s={keyword:"multipleOf",type:"number",schemaType:"number",$data:!0,error:{message:({schemaCode:e})=>n.str`must be multiple of ${e}`,params:({schemaCode:e})=>n._`{multipleOf: ${e}}`},code(e){const{gen:t,data:r,schemaCode:s,it:u}=e,a=u.opts.multipleOfPrecision,o=t.let("res"),i=a?n._`Math.abs(Math.round(${o}) - ${o}) > 1e-${a}`:n._`${o} !== parseInt(${o})`;e.fail$data(n._`(${s} === 0 || (${o} = ${r}/${s}, ${i}))`)}};t.default=s},27253:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(36461),s=r(78380),u={keyword:"pattern",type:"string",schemaType:"string",$data:!0,error:{message:({schemaCode:e})=>s.str`must match pattern "${e}"`,params:({schemaCode:e})=>s._`{pattern: ${e}}`},code(e){const{data:t,$data:r,schema:u,schemaCode:a,it:o}=e,i=o.opts.unicodeRegExp?"u":"",c=r?s._`(new RegExp(${a}, ${i}))`:(0,n.usePattern)(e,u);e.fail$data(s._`!${c}.test(${t})`)}};t.default=u},93516:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(36461),s=r(78380),u=r(78136),a={keyword:"required",type:"object",schemaType:"array",$data:!0,error:{message:({params:{missingProperty:e}})=>s.str`must have required property '${e}'`,params:({params:{missingProperty:e}})=>s._`{missingProperty: ${e}}`},code(e){const{gen:t,schema:r,schemaCode:a,data:o,$data:i,it:c}=e,{opts:d}=c;if(!i&&0===r.length)return;const l=r.length>=d.loopRequired;if(c.allErrors?function(){if(l||i)e.block$data(s.nil,f);else for(const t of r)(0,n.checkReportMissingProp)(e,t)}():function(){const u=t.let("missing");if(l||i){const r=t.let("valid",!0);e.block$data(r,(()=>function(r,u){e.setParams({missingProperty:r}),t.forOf(r,a,(()=>{t.assign(u,(0,n.propertyInData)(t,o,r,d.ownProperties)),t.if((0,s.not)(u),(()=>{e.error(),t.break()}))}),s.nil)}(u,r))),e.ok(r)}else t.if((0,n.checkMissingProp)(e,r,u)),(0,n.reportMissingProp)(e,u),t.else()}(),d.strictRequired){const t=e.parentSchema.properties,{definedProperties:n}=e.it;for(const e of r)if(void 0===(null==t?void 0:t[e])&&!n.has(e)){const t=`required property "${e}" is not defined at "${c.schemaEnv.baseId+c.errSchemaPath}" (strictRequired)`;(0,u.checkStrictMode)(c,t,c.opts.strictRequired)}}function f(){t.forOf("prop",a,(r=>{e.setParams({missingProperty:r}),t.if((0,n.noPropertyInData)(t,o,r,d.ownProperties),(()=>e.error()))}))}}};t.default=a},17998:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(24307),s=r(78380),u=r(78136),a=r(16898),o={keyword:"uniqueItems",type:"array",schemaType:"boolean",$data:!0,error:{message:({params:{i:e,j:t}})=>s.str`must NOT have duplicate items (items ## ${t} and ${e} are identical)`,params:({params:{i:e,j:t}})=>s._`{i: ${e}, j: ${t}}`},code(e){const{gen:t,data:r,$data:o,schema:i,parentSchema:c,schemaCode:d,it:l}=e;if(!o&&!i)return;const f=t.let("valid"),h=c.items?(0,n.getSchemaTypes)(c.items):[];function p(u,a){const o=t.name("item"),i=(0,n.checkDataTypes)(h,o,l.opts.strictNumbers,n.DataType.Wrong),c=t.const("indices",s._`{}`);t.for(s._`;${u}--;`,(()=>{t.let(o,s._`${r}[${u}]`),t.if(i,s._`continue`),h.length>1&&t.if(s._`typeof ${o} == "string"`,s._`${o} += "_"`),t.if(s._`typeof ${c}[${o}] == "number"`,(()=>{t.assign(a,s._`${c}[${o}]`),e.error(),t.assign(f,!1).break()})).code(s._`${c}[${o}] = ${u}`)}))}function m(n,o){const i=(0,u.useFunc)(t,a.default),c=t.name("outer");t.label(c).for(s._`;${n}--;`,(()=>t.for(s._`${o} = ${n}; ${o}--;`,(()=>t.if(s._`${i}(${r}[${n}], ${r}[${o}])`,(()=>{e.error(),t.assign(f,!1).break(c)}))))))}e.block$data(f,(function(){const n=t.let("i",s._`${r}.length`),u=t.let("j");e.setParams({i:n,j:u}),t.assign(f,!0),t.if(s._`${n} > 1`,(()=>(h.length>0&&!h.some((e=>"object"===e||"array"===e))?p:m)(n,u)))}),s._`${d} === false`),e.ok(f)}};t.default=o},60850:function(e){e.exports=function(){"use strict";function e(e,t){return e(t={exports:{}},t.exports),t.exports}var t=e((function(e){var t=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=t)})),r=e((function(e){var t=e.exports={version:"2.6.5"};"number"==typeof __e&&(__e=t)})),n=(r.version,function(e){return"object"==typeof e?null!==e:"function"==typeof e}),s=function(e){if(!n(e))throw TypeError(e+" is not an object!");return e},u=function(e){try{return!!e()}catch(e){return!0}},a=!u((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})),o=t.document,i=n(o)&&n(o.createElement),c=!a&&!u((function(){return 7!=Object.defineProperty((e="div",i?o.createElement(e):{}),"a",{get:function(){return 7}}).a;var e})),d=Object.defineProperty,l={f:a?Object.defineProperty:function(e,t,r){if(s(e),t=function(e,t){if(!n(e))return e;var r,s;if(t&&"function"==typeof(r=e.toString)&&!n(s=r.call(e)))return s;if("function"==typeof(r=e.valueOf)&&!n(s=r.call(e)))return s;if(!t&&"function"==typeof(r=e.toString)&&!n(s=r.call(e)))return s;throw TypeError("Can't convert object to primitive value")}(t,!0),s(r),c)try{return d(e,t,r)}catch(e){}if("get"in r||"set"in r)throw TypeError("Accessors not supported!");return"value"in r&&(e[t]=r.value),e}},f=a?function(e,t,r){return l.f(e,t,function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}(1,r))}:function(e,t,r){return e[t]=r,e},h={}.hasOwnProperty,p=function(e,t){return h.call(e,t)},m=0,D=Math.random(),y=e((function(e){var n="__core-js_shared__",s=t[n]||(t[n]={});(e.exports=function(e,t){return s[e]||(s[e]=void 0!==t?t:{})})("versions",[]).push({version:r.version,mode:"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})})),g=y("native-function-to-string",Function.toString),v=e((function(e){var n=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++m+D).toString(36))}("src"),s="toString",u=(""+g).split(s);r.inspectSource=function(e){return g.call(e)},(e.exports=function(e,r,s,a){var o="function"==typeof s;o&&(p(s,"name")||f(s,"name",r)),e[r]!==s&&(o&&(p(s,n)||f(s,n,e[r]?""+e[r]:u.join(String(r)))),e===t?e[r]=s:a?e[r]?e[r]=s:f(e,r,s):(delete e[r],f(e,r,s)))})(Function.prototype,s,(function(){return"function"==typeof this&&this[n]||g.call(this)}))})),E=function(e,t,r){if(function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!")}(e),void 0===t)return e;switch(r){case 1:return function(r){return e.call(t,r)};case 2:return function(r,n){return e.call(t,r,n)};case 3:return function(r,n,s){return e.call(t,r,n,s)}}return function(){return e.apply(t,arguments)}},C="prototype",$=function(e,n,s){var u,a,o,i,c=e&$.F,d=e&$.G,l=e&$.S,h=e&$.P,p=e&$.B,m=d?t:l?t[n]||(t[n]={}):(t[n]||{})[C],D=d?r:r[n]||(r[n]={}),y=D[C]||(D[C]={});for(u in d&&(s=n),s)o=((a=!c&&m&&void 0!==m[u])?m:s)[u],i=p&&a?E(o,t):h&&"function"==typeof o?E(Function.call,o):o,m&&v(m,u,o,e&$.U),D[u]!=o&&f(D,u,i),h&&y[u]!=o&&(y[u]=o)};t.core=r,$.F=1,$.G=2,$.S=4,$.P=8,$.B=16,$.W=32,$.U=64,$.R=128;var F=$,A=Math.ceil,w=Math.floor,_=function(e){return isNaN(e=+e)?0:(e>0?w:A)(e)},b=(!1,function(e,t){var r,n,s=String(function(e){if(null==e)throw TypeError("Can't call method on  "+e);return e}(e)),u=_(t),a=s.length;return u<0||u>=a?void 0:(r=s.charCodeAt(u))<55296||r>56319||u+1===a||(n=s.charCodeAt(u+1))<56320||n>57343?r:n-56320+(r-55296<<10)+65536});F(F.P,"String",{codePointAt:function(e){return b(this,e)}}),r.String.codePointAt;var S=Math.max,P=Math.min,B=String.fromCharCode,k=String.fromCodePoint;F(F.S+F.F*(!!k&&1!=k.length),"String",{fromCodePoint:function(e){for(var t,r=arguments,n=[],s=arguments.length,u=0;s>u;){if(t=+r[u++],o=1114111,((a=_(a=t))<0?S(a+o,0):P(a,o))!==t)throw RangeError(t+" is not a valid code point");n.push(t<65536?B(t):B(55296+((t-=65536)>>10),t%1024+56320))}var a,o;return n.join("")}}),r.String.fromCodePoint;var j,N,x,O,I,T,R,M,V,J,q,U,L,z,K={Space_Separator:/[\u1680\u2000-\u200A\u202F\u205F\u3000]/,ID_Start:/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u08A0-\u08B4\u08B6-\u08BD\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312E\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FEA\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC03-\uDC37\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDF00-\uDF19]|\uD806[\uDCA0-\uDCDF\uDCFF\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE83\uDE86-\uDE89\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50\uDF93-\uDF9F\uDFE0\uDFE1]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00-\uDD1E\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]/,ID_Continue:/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u0860-\u086A\u08A0-\u08B4\u08B6-\u08BD\u08D4-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u09FC\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9-\u0AFF\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D00-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19D9\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1CD0-\u1CD2\u1CD4-\u1CF9\u1D00-\u1DF9\u1DFB-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u2E2F\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099\u309A\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312E\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FEA\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC00-\uDC46\uDC66-\uDC6F\uDC7F-\uDCBA\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDCA-\uDDCC\uDDD0-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE3E\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3C-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC00-\uDC4A\uDC50-\uDC59\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB7\uDEC0-\uDEC9\uDF00-\uDF19\uDF1D-\uDF2B\uDF30-\uDF39]|\uD806[\uDCA0-\uDCE9\uDCFF\uDE00-\uDE3E\uDE47\uDE50-\uDE83\uDE86-\uDE99\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC36\uDC38-\uDC40\uDC50-\uDC59\uDC72-\uDC8F\uDC92-\uDCA7\uDCA9-\uDCB6\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD36\uDD3A\uDD3C\uDD3D\uDD3F-\uDD47\uDD50-\uDD59]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50-\uDF7E\uDF8F-\uDF9F\uDFE0\uDFE1]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00-\uDD1E\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6\uDD00-\uDD4A\uDD50-\uDD59]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uDB40[\uDD00-\uDDEF]/},G={isSpaceSeparator:function(e){return"string"==typeof e&&K.Space_Separator.test(e)},isIdStartChar:function(e){return"string"==typeof e&&(e>="a"&&e<="z"||e>="A"&&e<="Z"||"$"===e||"_"===e||K.ID_Start.test(e))},isIdContinueChar:function(e){return"string"==typeof e&&(e>="a"&&e<="z"||e>="A"&&e<="Z"||e>="0"&&e<="9"||"$"===e||"_"===e||"‌"===e||"‍"===e||K.ID_Continue.test(e))},isDigit:function(e){return"string"==typeof e&&/[0-9]/.test(e)},isHexDigit:function(e){return"string"==typeof e&&/[0-9A-Fa-f]/.test(e)}};function W(e,t,r){var n=e[t];if(null!=n&&"object"==typeof n)if(Array.isArray(n))for(var s=0;s<n.length;s++){var u=String(s),a=W(n,u,r);void 0===a?delete n[u]:Object.defineProperty(n,u,{value:a,writable:!0,enumerable:!0,configurable:!0})}else for(var o in n){var i=W(n,o,r);void 0===i?delete n[o]:Object.defineProperty(n,o,{value:i,writable:!0,enumerable:!0,configurable:!0})}return r.call(e,t,n)}function H(){for(J="default",q="",U=!1,L=1;;){z=Q();var e=Z[J]();if(e)return e}}function Q(){if(j[O])return String.fromCodePoint(j.codePointAt(O))}function Y(){var e=Q();return"\n"===e?(I++,T=0):e?T+=e.length:T++,e&&(O+=e.length),e}var Z={default:function(){switch(z){case"\t":case"\v":case"\f":case" ":case" ":case"\ufeff":case"\n":case"\r":case"\u2028":case"\u2029":return void Y();case"/":return Y(),void(J="comment");case void 0:return Y(),X("eof")}if(!G.isSpaceSeparator(z))return Z[N]();Y()},comment:function(){switch(z){case"*":return Y(),void(J="multiLineComment");case"/":return Y(),void(J="singleLineComment")}throw ue(Y())},multiLineComment:function(){switch(z){case"*":return Y(),void(J="multiLineCommentAsterisk");case void 0:throw ue(Y())}Y()},multiLineCommentAsterisk:function(){switch(z){case"*":return void Y();case"/":return Y(),void(J="default");case void 0:throw ue(Y())}Y(),J="multiLineComment"},singleLineComment:function(){switch(z){case"\n":case"\r":case"\u2028":case"\u2029":return Y(),void(J="default");case void 0:return Y(),X("eof")}Y()},value:function(){switch(z){case"{":case"[":return X("punctuator",Y());case"n":return Y(),ee("ull"),X("null",null);case"t":return Y(),ee("rue"),X("boolean",!0);case"f":return Y(),ee("alse"),X("boolean",!1);case"-":case"+":return"-"===Y()&&(L=-1),void(J="sign");case".":return q=Y(),void(J="decimalPointLeading");case"0":return q=Y(),void(J="zero");case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":return q=Y(),void(J="decimalInteger");case"I":return Y(),ee("nfinity"),X("numeric",1/0);case"N":return Y(),ee("aN"),X("numeric",NaN);case'"':case"'":return U='"'===Y(),q="",void(J="string")}throw ue(Y())},identifierNameStartEscape:function(){if("u"!==z)throw ue(Y());Y();var e=te();switch(e){case"$":case"_":break;default:if(!G.isIdStartChar(e))throw oe()}q+=e,J="identifierName"},identifierName:function(){switch(z){case"$":case"_":case"‌":case"‍":return void(q+=Y());case"\\":return Y(),void(J="identifierNameEscape")}if(!G.isIdContinueChar(z))return X("identifier",q);q+=Y()},identifierNameEscape:function(){if("u"!==z)throw ue(Y());Y();var e=te();switch(e){case"$":case"_":case"‌":case"‍":break;default:if(!G.isIdContinueChar(e))throw oe()}q+=e,J="identifierName"},sign:function(){switch(z){case".":return q=Y(),void(J="decimalPointLeading");case"0":return q=Y(),void(J="zero");case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":return q=Y(),void(J="decimalInteger");case"I":return Y(),ee("nfinity"),X("numeric",L*(1/0));case"N":return Y(),ee("aN"),X("numeric",NaN)}throw ue(Y())},zero:function(){switch(z){case".":return q+=Y(),void(J="decimalPoint");case"e":case"E":return q+=Y(),void(J="decimalExponent");case"x":case"X":return q+=Y(),void(J="hexadecimal")}return X("numeric",0*L)},decimalInteger:function(){switch(z){case".":return q+=Y(),void(J="decimalPoint");case"e":case"E":return q+=Y(),void(J="decimalExponent")}if(!G.isDigit(z))return X("numeric",L*Number(q));q+=Y()},decimalPointLeading:function(){if(G.isDigit(z))return q+=Y(),void(J="decimalFraction");throw ue(Y())},decimalPoint:function(){switch(z){case"e":case"E":return q+=Y(),void(J="decimalExponent")}return G.isDigit(z)?(q+=Y(),void(J="decimalFraction")):X("numeric",L*Number(q))},decimalFraction:function(){switch(z){case"e":case"E":return q+=Y(),void(J="decimalExponent")}if(!G.isDigit(z))return X("numeric",L*Number(q));q+=Y()},decimalExponent:function(){switch(z){case"+":case"-":return q+=Y(),void(J="decimalExponentSign")}if(G.isDigit(z))return q+=Y(),void(J="decimalExponentInteger");throw ue(Y())},decimalExponentSign:function(){if(G.isDigit(z))return q+=Y(),void(J="decimalExponentInteger");throw ue(Y())},decimalExponentInteger:function(){if(!G.isDigit(z))return X("numeric",L*Number(q));q+=Y()},hexadecimal:function(){if(G.isHexDigit(z))return q+=Y(),void(J="hexadecimalInteger");throw ue(Y())},hexadecimalInteger:function(){if(!G.isHexDigit(z))return X("numeric",L*Number(q));q+=Y()},string:function(){switch(z){case"\\":return Y(),void(q+=function(){switch(Q()){case"b":return Y(),"\b";case"f":return Y(),"\f";case"n":return Y(),"\n";case"r":return Y(),"\r";case"t":return Y(),"\t";case"v":return Y(),"\v";case"0":if(Y(),G.isDigit(Q()))throw ue(Y());return"\0";case"x":return Y(),function(){var e="",t=Q();if(!G.isHexDigit(t))throw ue(Y());if(e+=Y(),t=Q(),!G.isHexDigit(t))throw ue(Y());return e+=Y(),String.fromCodePoint(parseInt(e,16))}();case"u":return Y(),te();case"\n":case"\u2028":case"\u2029":return Y(),"";case"\r":return Y(),"\n"===Q()&&Y(),"";case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":case void 0:throw ue(Y())}return Y()}());case'"':return U?(Y(),X("string",q)):void(q+=Y());case"'":return U?void(q+=Y()):(Y(),X("string",q));case"\n":case"\r":throw ue(Y());case"\u2028":case"\u2029":!function(e){console.warn("JSON5: '"+ie(e)+"' in strings is not valid ECMAScript; consider escaping")}(z);break;case void 0:throw ue(Y())}q+=Y()},start:function(){switch(z){case"{":case"[":return X("punctuator",Y())}J="value"},beforePropertyName:function(){switch(z){case"$":case"_":return q=Y(),void(J="identifierName");case"\\":return Y(),void(J="identifierNameStartEscape");case"}":return X("punctuator",Y());case'"':case"'":return U='"'===Y(),void(J="string")}if(G.isIdStartChar(z))return q+=Y(),void(J="identifierName");throw ue(Y())},afterPropertyName:function(){if(":"===z)return X("punctuator",Y());throw ue(Y())},beforePropertyValue:function(){J="value"},afterPropertyValue:function(){switch(z){case",":case"}":return X("punctuator",Y())}throw ue(Y())},beforeArrayValue:function(){if("]"===z)return X("punctuator",Y());J="value"},afterArrayValue:function(){switch(z){case",":case"]":return X("punctuator",Y())}throw ue(Y())},end:function(){throw ue(Y())}};function X(e,t){return{type:e,value:t,line:I,column:T}}function ee(e){for(var t=0,r=e;t<r.length;t+=1){var n=r[t];if(Q()!==n)throw ue(Y());Y()}}function te(){for(var e="",t=4;t-- >0;){var r=Q();if(!G.isHexDigit(r))throw ue(Y());e+=Y()}return String.fromCodePoint(parseInt(e,16))}var re={start:function(){if("eof"===R.type)throw ae();ne()},beforePropertyName:function(){switch(R.type){case"identifier":case"string":return M=R.value,void(N="afterPropertyName");case"punctuator":return void se();case"eof":throw ae()}},afterPropertyName:function(){if("eof"===R.type)throw ae();N="beforePropertyValue"},beforePropertyValue:function(){if("eof"===R.type)throw ae();ne()},beforeArrayValue:function(){if("eof"===R.type)throw ae();"punctuator"!==R.type||"]"!==R.value?ne():se()},afterPropertyValue:function(){if("eof"===R.type)throw ae();switch(R.value){case",":return void(N="beforePropertyName");case"}":se()}},afterArrayValue:function(){if("eof"===R.type)throw ae();switch(R.value){case",":return void(N="beforeArrayValue");case"]":se()}},end:function(){}};function ne(){var e;switch(R.type){case"punctuator":switch(R.value){case"{":e={};break;case"[":e=[]}break;case"null":case"boolean":case"numeric":case"string":e=R.value}if(void 0===V)V=e;else{var t=x[x.length-1];Array.isArray(t)?t.push(e):Object.defineProperty(t,M,{value:e,writable:!0,enumerable:!0,configurable:!0})}if(null!==e&&"object"==typeof e)x.push(e),N=Array.isArray(e)?"beforeArrayValue":"beforePropertyName";else{var r=x[x.length-1];N=null==r?"end":Array.isArray(r)?"afterArrayValue":"afterPropertyValue"}}function se(){x.pop();var e=x[x.length-1];N=null==e?"end":Array.isArray(e)?"afterArrayValue":"afterPropertyValue"}function ue(e){return ce(void 0===e?"JSON5: invalid end of input at "+I+":"+T:"JSON5: invalid character '"+ie(e)+"' at "+I+":"+T)}function ae(){return ce("JSON5: invalid end of input at "+I+":"+T)}function oe(){return ce("JSON5: invalid identifier character at "+I+":"+(T-=5))}function ie(e){var t={"'":"\\'",'"':'\\"',"\\":"\\\\","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","\t":"\\t","\v":"\\v","\0":"\\0","\u2028":"\\u2028","\u2029":"\\u2029"};if(t[e])return t[e];if(e<" "){var r=e.charCodeAt(0).toString(16);return"\\x"+("00"+r).substring(r.length)}return e}function ce(e){var t=new SyntaxError(e);return t.lineNumber=I,t.columnNumber=T,t}return{parse:function(e,t){j=String(e),N="start",x=[],O=0,I=1,T=0,R=void 0,M=void 0,V=void 0;do{R=H(),re[N]()}while("eof"!==R.type);return"function"==typeof t?W({"":V},"",t):V},stringify:function(e,t,r){var n,s,u,a=[],o="",i="";if(null==t||"object"!=typeof t||Array.isArray(t)||(r=t.space,u=t.quote,t=t.replacer),"function"==typeof t)s=t;else if(Array.isArray(t)){n=[];for(var c=0,d=t;c<d.length;c+=1){var l=d[c],f=void 0;"string"==typeof l?f=l:("number"==typeof l||l instanceof String||l instanceof Number)&&(f=String(l)),void 0!==f&&n.indexOf(f)<0&&n.push(f)}}return r instanceof Number?r=Number(r):r instanceof String&&(r=String(r)),"number"==typeof r?r>0&&(r=Math.min(10,Math.floor(r)),i="          ".substr(0,r)):"string"==typeof r&&(i=r.substr(0,10)),h("",{"":e});function h(e,t){var r=t[e];switch(null!=r&&("function"==typeof r.toJSON5?r=r.toJSON5(e):"function"==typeof r.toJSON&&(r=r.toJSON(e))),s&&(r=s.call(t,e,r)),r instanceof Number?r=Number(r):r instanceof String?r=String(r):r instanceof Boolean&&(r=r.valueOf()),r){case null:return"null";case!0:return"true";case!1:return"false"}return"string"==typeof r?p(r):"number"==typeof r?String(r):"object"==typeof r?Array.isArray(r)?function(e){if(a.indexOf(e)>=0)throw TypeError("Converting circular structure to JSON5");a.push(e);var t=o;o+=i;for(var r,n=[],s=0;s<e.length;s++){var u=h(String(s),e);n.push(void 0!==u?u:"null")}if(0===n.length)r="[]";else if(""===i)r="["+n.join(",")+"]";else{var c=",\n"+o,d=n.join(c);r="[\n"+o+d+",\n"+t+"]"}return a.pop(),o=t,r}(r):function(e){if(a.indexOf(e)>=0)throw TypeError("Converting circular structure to JSON5");a.push(e);var t=o;o+=i;for(var r,s,u=[],c=0,d=n||Object.keys(e);c<d.length;c+=1){var l=d[c],f=h(l,e);if(void 0!==f){var p=m(l)+":";""!==i&&(p+=" "),p+=f,u.push(p)}}if(0===u.length)r="{}";else if(""===i)r="{"+(s=u.join(","))+"}";else{var D=",\n"+o;s=u.join(D),r="{\n"+o+s+",\n"+t+"}"}return a.pop(),o=t,r}(r):void 0}function p(e){for(var t={"'":.1,'"':.2},r={"'":"\\'",'"':'\\"',"\\":"\\\\","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","\t":"\\t","\v":"\\v","\0":"\\0","\u2028":"\\u2028","\u2029":"\\u2029"},n="",s=0;s<e.length;s++){var a=e[s];switch(a){case"'":case'"':t[a]++,n+=a;continue;case"\0":if(G.isDigit(e[s+1])){n+="\\x00";continue}}if(r[a])n+=r[a];else if(a<" "){var o=a.charCodeAt(0).toString(16);n+="\\x"+("00"+o).substring(o.length)}else n+=a}var i=u||Object.keys(t).reduce((function(e,r){return t[e]<t[r]?e:r}));return i+(n=n.replace(new RegExp(i,"g"),r[i]))+i}function m(e){if(0===e.length)return p(e);var t=String.fromCodePoint(e.codePointAt(0));if(!G.isIdStartChar(t))return p(e);for(var r=t.length;r<e.length;r++)if(!G.isIdContinueChar(String.fromCodePoint(e.codePointAt(r))))return p(e);return e}}}}()},80705:e=>{"use strict";e.exports=JSON.parse('{"$id":"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#","description":"Meta-schema for $data reference (JSON AnySchema extension proposal)","type":"object","required":["$data"],"properties":{"$data":{"type":"string","anyOf":[{"format":"relative-json-pointer"},{"format":"json-pointer"}]}},"additionalProperties":false}')},61327:e=>{"use strict";e.exports=JSON.parse('{"$schema":"http://json-schema.org/draft-07/schema#","$id":"http://json-schema.org/draft-07/schema#","title":"Core schema meta-schema","definitions":{"schemaArray":{"type":"array","minItems":1,"items":{"$ref":"#"}},"nonNegativeInteger":{"type":"integer","minimum":0},"nonNegativeIntegerDefault0":{"allOf":[{"$ref":"#/definitions/nonNegativeInteger"},{"default":0}]},"simpleTypes":{"enum":["array","boolean","integer","null","number","object","string"]},"stringArray":{"type":"array","items":{"type":"string"},"uniqueItems":true,"default":[]}},"type":["object","boolean"],"properties":{"$id":{"type":"string","format":"uri-reference"},"$schema":{"type":"string","format":"uri"},"$ref":{"type":"string","format":"uri-reference"},"$comment":{"type":"string"},"title":{"type":"string"},"description":{"type":"string"},"default":true,"readOnly":{"type":"boolean","default":false},"examples":{"type":"array","items":true},"multipleOf":{"type":"number","exclusiveMinimum":0},"maximum":{"type":"number"},"exclusiveMaximum":{"type":"number"},"minimum":{"type":"number"},"exclusiveMinimum":{"type":"number"},"maxLength":{"$ref":"#/definitions/nonNegativeInteger"},"minLength":{"$ref":"#/definitions/nonNegativeIntegerDefault0"},"pattern":{"type":"string","format":"regex"},"additionalItems":{"$ref":"#"},"items":{"anyOf":[{"$ref":"#"},{"$ref":"#/definitions/schemaArray"}],"default":true},"maxItems":{"$ref":"#/definitions/nonNegativeInteger"},"minItems":{"$ref":"#/definitions/nonNegativeIntegerDefault0"},"uniqueItems":{"type":"boolean","default":false},"contains":{"$ref":"#"},"maxProperties":{"$ref":"#/definitions/nonNegativeInteger"},"minProperties":{"$ref":"#/definitions/nonNegativeIntegerDefault0"},"required":{"$ref":"#/definitions/stringArray"},"additionalProperties":{"$ref":"#"},"definitions":{"type":"object","additionalProperties":{"$ref":"#"},"default":{}},"properties":{"type":"object","additionalProperties":{"$ref":"#"},"default":{}},"patternProperties":{"type":"object","additionalProperties":{"$ref":"#"},"propertyNames":{"format":"regex"},"default":{}},"dependencies":{"type":"object","additionalProperties":{"anyOf":[{"$ref":"#"},{"$ref":"#/definitions/stringArray"}]}},"propertyNames":{"$ref":"#"},"const":true,"enum":{"type":"array","items":true,"minItems":1,"uniqueItems":true},"type":{"anyOf":[{"$ref":"#/definitions/simpleTypes"},{"type":"array","items":{"$ref":"#/definitions/simpleTypes"},"minItems":1,"uniqueItems":true}]},"format":{"type":"string"},"contentMediaType":{"type":"string"},"contentEncoding":{"type":"string"},"if":{"$ref":"#"},"then":{"$ref":"#"},"else":{"$ref":"#"},"allOf":{"$ref":"#/definitions/schemaArray"},"anyOf":{"$ref":"#/definitions/schemaArray"},"oneOf":{"$ref":"#/definitions/schemaArray"},"not":{"$ref":"#"}},"default":true}')}}]);
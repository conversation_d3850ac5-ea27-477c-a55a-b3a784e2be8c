"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[9],{20009:(e,t,r)=>{r.r(t);r.d(t,{commonmarkLanguage:()=>De,deleteMarkupBackward:()=>Ke,insertNewlineContinueMarkup:()=>Ze,markdown:()=>Ye,markdownKeymap:()=>Je,markdownLanguage:()=>$e});var n=r(37496);var s=r(66143);var i=r(24104);var a=r(73265);var o=r(6016);class l{constructor(e,t,r,n,s,i,o){this.type=e;this.value=t;this.from=r;this.hash=n;this.end=s;this.children=i;this.positions=o;this.hashProp=[[a.NodeProp.contextHash,n]]}static create(e,t,r,n,s){let i=n+(n<<8)+e+(t<<4)|0;return new l(e,t,r,i,s,[],[])}addChild(e,t){if(e.prop(a.NodeProp.contextHash)!=this.hash)e=new a.Tree(e.type,e.children,e.positions,e.length,this.hashProp);this.children.push(e);this.positions.push(t)}toTree(e,t=this.end){let r=this.children.length-1;if(r>=0)t=Math.max(t,this.positions[r]+this.children[r].length+this.from);let n=new a.Tree(e.types[this.type],this.children,this.positions,t-this.from).balance({makeTree:(e,t,r)=>new a.Tree(a.NodeType.none,e,t,r,this.hashProp)});return n}}var h;(function(e){e[e["Document"]=1]="Document";e[e["CodeBlock"]=2]="CodeBlock";e[e["FencedCode"]=3]="FencedCode";e[e["Blockquote"]=4]="Blockquote";e[e["HorizontalRule"]=5]="HorizontalRule";e[e["BulletList"]=6]="BulletList";e[e["OrderedList"]=7]="OrderedList";e[e["ListItem"]=8]="ListItem";e[e["ATXHeading1"]=9]="ATXHeading1";e[e["ATXHeading2"]=10]="ATXHeading2";e[e["ATXHeading3"]=11]="ATXHeading3";e[e["ATXHeading4"]=12]="ATXHeading4";e[e["ATXHeading5"]=13]="ATXHeading5";e[e["ATXHeading6"]=14]="ATXHeading6";e[e["SetextHeading1"]=15]="SetextHeading1";e[e["SetextHeading2"]=16]="SetextHeading2";e[e["HTMLBlock"]=17]="HTMLBlock";e[e["LinkReference"]=18]="LinkReference";e[e["Paragraph"]=19]="Paragraph";e[e["CommentBlock"]=20]="CommentBlock";e[e["ProcessingInstructionBlock"]=21]="ProcessingInstructionBlock";e[e["Escape"]=22]="Escape";e[e["Entity"]=23]="Entity";e[e["HardBreak"]=24]="HardBreak";e[e["Emphasis"]=25]="Emphasis";e[e["StrongEmphasis"]=26]="StrongEmphasis";e[e["Link"]=27]="Link";e[e["Image"]=28]="Image";e[e["InlineCode"]=29]="InlineCode";e[e["HTMLTag"]=30]="HTMLTag";e[e["Comment"]=31]="Comment";e[e["ProcessingInstruction"]=32]="ProcessingInstruction";e[e["URL"]=33]="URL";e[e["HeaderMark"]=34]="HeaderMark";e[e["QuoteMark"]=35]="QuoteMark";e[e["ListMark"]=36]="ListMark";e[e["LinkMark"]=37]="LinkMark";e[e["EmphasisMark"]=38]="EmphasisMark";e[e["CodeMark"]=39]="CodeMark";e[e["CodeText"]=40]="CodeText";e[e["CodeInfo"]=41]="CodeInfo";e[e["LinkTitle"]=42]="LinkTitle";e[e["LinkLabel"]=43]="LinkLabel"})(h||(h={}));class f{constructor(e,t){this.start=e;this.content=t;this.marks=[];this.parsers=[]}}class u{constructor(){this.text="";this.baseIndent=0;this.basePos=0;this.depth=0;this.markers=[];this.pos=0;this.indent=0;this.next=-1}forward(){if(this.basePos>this.pos)this.forwardInner()}forwardInner(){let e=this.skipSpace(this.basePos);this.indent=this.countIndent(e,this.pos,this.indent);this.pos=e;this.next=e==this.text.length?-1:this.text.charCodeAt(e)}skipSpace(e){return g(this.text,e)}reset(e){this.text=e;this.baseIndent=this.basePos=this.pos=this.indent=0;this.forwardInner();this.depth=1;while(this.markers.length)this.markers.pop()}moveBase(e){this.basePos=e;this.baseIndent=this.countIndent(e,this.pos,this.indent)}moveBaseColumn(e){this.baseIndent=e;this.basePos=this.findColumn(e)}addMarker(e){this.markers.push(e)}countIndent(e,t=0,r=0){for(let n=t;n<e;n++)r+=this.text.charCodeAt(n)==9?4-r%4:1;return r}findColumn(e){let t=0;for(let r=0;t<this.text.length&&r<e;t++)r+=this.text.charCodeAt(t)==9?4-r%4:1;return t}scrub(){if(!this.baseIndent)return this.text;let e="";for(let t=0;t<this.basePos;t++)e+=" ";return e+this.text.slice(this.basePos)}}function d(e,t,r){if(r.pos==r.text.length||e!=t.block&&r.indent>=t.stack[r.depth+1].value+r.baseIndent)return true;if(r.indent>=r.baseIndent+4)return false;let n=(e.type==h.OrderedList?w:S)(r,t,false);return n>0&&(e.type!=h.BulletList||b(r,t,false)<0)&&r.text.charCodeAt(r.pos+n-1)==e.value}const c={[h.Blockquote](e,t,r){if(r.next!=62)return false;r.markers.push(K(h.QuoteMark,t.lineStart+r.pos,t.lineStart+r.pos+1));r.moveBase(r.pos+(p(r.text.charCodeAt(r.pos+1))?2:1));e.end=t.lineStart+r.text.length;return true},[h.ListItem](e,t,r){if(r.indent<r.baseIndent+e.value&&r.next>-1)return false;r.moveBaseColumn(r.baseIndent+e.value);return true},[h.OrderedList]:d,[h.BulletList]:d,[h.Document](){return true}};function p(e){return e==32||e==9||e==10||e==13}function g(e,t=0){while(t<e.length&&p(e.charCodeAt(t)))t++;return t}function m(e,t,r){while(t>r&&p(e.charCodeAt(t-1)))t--;return t}function k(e){if(e.next!=96&&e.next!=126)return-1;let t=e.pos+1;while(t<e.text.length&&e.text.charCodeAt(t)==e.next)t++;if(t<e.pos+3)return-1;if(e.next==96)for(let r=t;r<e.text.length;r++)if(e.text.charCodeAt(r)==96)return-1;return t}function x(e){return e.next!=62?-1:e.text.charCodeAt(e.pos+1)==32?2:1}function b(e,t,r){if(e.next!=42&&e.next!=45&&e.next!=95)return-1;let n=1;for(let s=e.pos+1;s<e.text.length;s++){let t=e.text.charCodeAt(s);if(t==e.next)n++;else if(!p(t))return-1}if(r&&e.next==45&&y(e)>-1&&e.depth==t.stack.length)return-1;return n<3?-1:1}function L(e,t){for(let r=e.stack.length-1;r>=0;r--)if(e.stack[r].type==t)return true;return false}function S(e,t,r){return(e.next==45||e.next==43||e.next==42)&&(e.pos==e.text.length-1||p(e.text.charCodeAt(e.pos+1)))&&(!r||L(t,h.BulletList)||e.skipSpace(e.pos+2)<e.text.length)?1:-1}function w(e,t,r){let n=e.pos,s=e.next;for(;;){if(s>=48&&s<=57)n++;else break;if(n==e.text.length)return-1;s=e.text.charCodeAt(n)}if(n==e.pos||n>e.pos+9||s!=46&&s!=41||n<e.text.length-1&&!p(e.text.charCodeAt(n+1))||r&&!L(t,h.OrderedList)&&(e.skipSpace(n+1)==e.text.length||n>e.pos+1||e.next!=49))return-1;return n+1-e.pos}function C(e){if(e.next!=35)return-1;let t=e.pos+1;while(t<e.text.length&&e.text.charCodeAt(t)==35)t++;if(t<e.text.length&&e.text.charCodeAt(t)!=32)return-1;let r=t-e.pos;return r>6?-1:r}function y(e){if(e.next!=45&&e.next!=61||e.indent>=e.baseIndent+4)return-1;let t=e.pos+1;while(t<e.text.length&&e.text.charCodeAt(t)==e.next)t++;let r=t;while(t<e.text.length&&p(e.text.charCodeAt(t)))t++;return t==e.text.length?r:-1}const T=/^[ \t]*$/,A=/-->/,I=/\?>/;const B=[[/^<(?:script|pre|style)(?:\s|>|$)/i,/<\/(?:script|pre|style)>/i],[/^\s*<!--/,A],[/^\s*<\?/,I],[/^\s*<![A-Z]/,/>/],[/^\s*<!\[CDATA\[/,/\]\]>/],[/^\s*<\/?(?:address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h1|h2|h3|h4|h5|h6|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul)(?:\s|\/?>|$)/i,T],[/^\s*(?:<\/[a-z][\w-]*\s*>|<[a-z][\w-]*(\s+[a-z:_][\w-.]*(?:\s*=\s*(?:[^\s"'=<>`]+|'[^']*'|"[^"]*"))?)*\s*>)\s*$/i,T]];function E(e,t,r){if(e.next!=60)return-1;let n=e.text.slice(e.pos);for(let s=0,i=B.length-(r?1:0);s<i;s++)if(B[s][0].test(n))return s;return-1}function M(e,t){let r=e.countIndent(t,e.pos,e.indent);let n=e.countIndent(e.skipSpace(t),t,r);return n>=r+5?r+1:n}function H(e,t,r){let n=e.length-1;if(n>=0&&e[n].to==t&&e[n].type==h.CodeText)e[n].to=r;else e.push(K(h.CodeText,t,r))}const P={LinkReference:undefined,IndentedCode(e,t){let r=t.baseIndent+4;if(t.indent<r)return false;let n=t.findColumn(r);let s=e.lineStart+n,i=e.lineStart+t.text.length;let a=[],o=[];H(a,s,i);while(e.nextLine()&&t.depth>=e.stack.length){if(t.pos==t.text.length){H(o,e.lineStart-1,e.lineStart);for(let e of t.markers)o.push(e)}else if(t.indent<r){break}else{if(o.length){for(let e of o){if(e.type==h.CodeText)H(a,e.from,e.to);else a.push(e)}o=[]}H(a,e.lineStart-1,e.lineStart);for(let e of t.markers)a.push(e);i=e.lineStart+t.text.length;let r=e.lineStart+t.findColumn(t.baseIndent+4);if(r<i)H(a,r,i)}}if(o.length){o=o.filter((e=>e.type!=h.CodeText));if(o.length)t.markers=o.concat(t.markers)}e.addNode(e.buffer.writeElements(a,-s).finish(h.CodeBlock,i-s),s);return true},FencedCode(e,t){let r=k(t);if(r<0)return false;let n=e.lineStart+t.pos,s=t.next,i=r-t.pos;let a=t.skipSpace(r),o=m(t.text,t.text.length,a);let l=[K(h.CodeMark,n,n+i)];if(a<o)l.push(K(h.CodeInfo,e.lineStart+a,e.lineStart+o));for(let f=true;e.nextLine()&&t.depth>=e.stack.length;f=false){let r=t.pos;if(t.indent-t.baseIndent<4)while(r<t.text.length&&t.text.charCodeAt(r)==s)r++;if(r-t.pos>=i&&t.skipSpace(r)==t.text.length){for(let e of t.markers)l.push(e);l.push(K(h.CodeMark,e.lineStart+t.pos,e.lineStart+r));e.nextLine();break}else{if(!f)H(l,e.lineStart-1,e.lineStart);for(let e of t.markers)l.push(e);let r=e.lineStart+t.basePos,n=e.lineStart+t.text.length;if(r<n)H(l,r,n)}}e.addNode(e.buffer.writeElements(l,-n).finish(h.FencedCode,e.prevLineEnd()-n),n);return true},Blockquote(e,t){let r=x(t);if(r<0)return false;e.startContext(h.Blockquote,t.pos);e.addNode(h.QuoteMark,e.lineStart+t.pos,e.lineStart+t.pos+1);t.moveBase(t.pos+r);return null},HorizontalRule(e,t){if(b(t,e,false)<0)return false;let r=e.lineStart+t.pos;e.nextLine();e.addNode(h.HorizontalRule,r);return true},BulletList(e,t){let r=S(t,e,false);if(r<0)return false;if(e.block.type!=h.BulletList)e.startContext(h.BulletList,t.basePos,t.next);let n=M(t,t.pos+1);e.startContext(h.ListItem,t.basePos,n-t.baseIndent);e.addNode(h.ListMark,e.lineStart+t.pos,e.lineStart+t.pos+r);t.moveBaseColumn(n);return null},OrderedList(e,t){let r=w(t,e,false);if(r<0)return false;if(e.block.type!=h.OrderedList)e.startContext(h.OrderedList,t.basePos,t.text.charCodeAt(t.pos+r-1));let n=M(t,t.pos+r);e.startContext(h.ListItem,t.basePos,n-t.baseIndent);e.addNode(h.ListMark,e.lineStart+t.pos,e.lineStart+t.pos+r);t.moveBaseColumn(n);return null},ATXHeading(e,t){let r=C(t);if(r<0)return false;let n=t.pos,s=e.lineStart+n;let i=m(t.text,t.text.length,n),a=i;while(a>n&&t.text.charCodeAt(a-1)==t.next)a--;if(a==i||a==n||!p(t.text.charCodeAt(a-1)))a=t.text.length;let o=e.buffer.write(h.HeaderMark,0,r).writeElements(e.parser.parseInline(t.text.slice(n+r+1,a),s+r+1),-s);if(a<t.text.length)o.write(h.HeaderMark,a-n,i-n);let l=o.finish(h.ATXHeading1-1+r,t.text.length-n);e.nextLine();e.addNode(l,s);return true},HTMLBlock(e,t){let r=E(t,e,false);if(r<0)return false;let n=e.lineStart+t.pos,s=B[r][1];let i=[],a=s!=T;while(!s.test(t.text)&&e.nextLine()){if(t.depth<e.stack.length){a=false;break}for(let e of t.markers)i.push(e)}if(a)e.nextLine();let o=s==A?h.CommentBlock:s==I?h.ProcessingInstructionBlock:h.HTMLBlock;let l=e.prevLineEnd();e.addNode(e.buffer.writeElements(i,-n).finish(o,l-n),n);return true},SetextHeading:undefined};class v{constructor(e){this.stage=0;this.elts=[];this.pos=0;this.start=e.start;this.advance(e.content)}nextLine(e,t,r){if(this.stage==-1)return false;let n=r.content+"\n"+t.scrub();let s=this.advance(n);if(s>-1&&s<n.length)return this.complete(e,r,s);return false}finish(e,t){if((this.stage==2||this.stage==3)&&g(t.content,this.pos)==t.content.length)return this.complete(e,t,t.content.length);return false}complete(e,t,r){e.addLeafElement(t,K(h.LinkReference,this.start,this.start+r,this.elts));return true}nextStage(e){if(e){this.pos=e.to-this.start;this.elts.push(e);this.stage++;return true}if(e===false)this.stage=-1;return false}advance(e){for(;;){if(this.stage==-1){return-1}else if(this.stage==0){if(!this.nextStage(le(e,this.pos,this.start,true)))return-1;if(e.charCodeAt(this.pos)!=58)return this.stage=-1;this.elts.push(K(h.LinkMark,this.pos+this.start,this.pos+this.start+1));this.pos++}else if(this.stage==1){if(!this.nextStage(ae(e,g(e,this.pos),this.start)))return-1}else if(this.stage==2){let t=g(e,this.pos),r=0;if(t>this.pos){let n=oe(e,t,this.start);if(n){let t=N(e,n.to-this.start);if(t>0){this.nextStage(n);r=t}}}if(!r)r=N(e,this.pos);return r>0&&r<e.length?r:-1}else{return N(e,this.pos)}}}}function N(e,t){for(;t<e.length;t++){let r=e.charCodeAt(t);if(r==10)break;if(!p(r))return-1}return t}class O{nextLine(e,t,r){let n=t.depth<e.stack.length?-1:y(t);let s=t.next;if(n<0)return false;let i=K(h.HeaderMark,e.lineStart+t.pos,e.lineStart+n);e.nextLine();e.addLeafElement(r,K(s==61?h.SetextHeading1:h.SetextHeading2,r.start,e.prevLineEnd(),[...e.parser.parseInline(r.content,r.start),i]));return true}finish(){return false}}const R={LinkReference(e,t){return t.content.charCodeAt(0)==91?new v(t):null},SetextHeading(){return new O}};const X=[(e,t)=>C(t)>=0,(e,t)=>k(t)>=0,(e,t)=>x(t)>=0,(e,t)=>S(t,e,true)>=0,(e,t)=>w(t,e,true)>=0,(e,t)=>b(t,e,true)>=0,(e,t)=>E(t,e,true)>=0];const z={text:"",end:0};class D{constructor(e,t,r,n){this.parser=e;this.input=t;this.ranges=n;this.line=new u;this.atEnd=false;this.dontInject=new Set;this.stoppedAt=null;this.rangeI=0;this.to=n[n.length-1].to;this.lineStart=this.absoluteLineStart=this.absoluteLineEnd=n[0].from;this.block=l.create(h.Document,0,this.lineStart,0,0);this.stack=[this.block];this.fragments=r.length?new de(r,t):null;this.readLine()}get parsedPos(){return this.absoluteLineStart}advance(){if(this.stoppedAt!=null&&this.absoluteLineStart>this.stoppedAt)return this.finish();let{line:e}=this;for(;;){while(e.depth<this.stack.length)this.finishContext();for(let t of e.markers)this.addNode(t.type,t.from,t.to);if(e.pos<e.text.length)break;if(!this.nextLine())return this.finish()}if(this.fragments&&this.reuseFragment(e.basePos))return null;e:for(;;){for(let t of this.parser.blockParsers)if(t){let r=t(this,e);if(r!=false){if(r==true)return null;e.forward();continue e}}break}let t=new f(this.lineStart+e.pos,e.text.slice(e.pos));for(let r of this.parser.leafBlockParsers)if(r){let e=r(this,t);if(e)t.parsers.push(e)}e:while(this.nextLine()){if(e.pos==e.text.length)break;if(e.indent<e.baseIndent+4){for(let r of this.parser.endLeafBlock)if(r(this,e,t))break e}for(let r of t.parsers)if(r.nextLine(this,e,t))return null;t.content+="\n"+e.scrub();for(let r of e.markers)t.marks.push(r)}this.finishLeaf(t);return null}stopAt(e){if(this.stoppedAt!=null&&this.stoppedAt<e)throw new RangeError("Can't move stoppedAt forward");this.stoppedAt=e}reuseFragment(e){if(!this.fragments.moveTo(this.absoluteLineStart+e,this.absoluteLineStart)||!this.fragments.matches(this.block.hash))return false;let t=this.fragments.takeNodes(this);if(!t)return false;let r=t,n=this.absoluteLineStart+t;for(let s=1;s<this.ranges.length;s++){let e=this.ranges[s-1].to,t=this.ranges[s].from;if(e>=this.lineStart&&t<n)r-=t-e}this.lineStart+=r;this.absoluteLineStart+=t;this.moveRangeI();if(this.absoluteLineStart<this.to){this.lineStart++;this.absoluteLineStart++;this.readLine()}else{this.atEnd=true;this.readLine()}return true}get depth(){return this.stack.length}parentType(e=this.depth-1){return this.parser.nodeSet.types[this.stack[e].type]}nextLine(){this.lineStart+=this.line.text.length;if(this.absoluteLineEnd>=this.to){this.absoluteLineStart=this.absoluteLineEnd;this.atEnd=true;this.readLine();return false}else{this.lineStart++;this.absoluteLineStart=this.absoluteLineEnd+1;this.moveRangeI();this.readLine();return true}}moveRangeI(){while(this.rangeI<this.ranges.length-1&&this.absoluteLineStart>=this.ranges[this.rangeI].to){this.rangeI++;this.absoluteLineStart=Math.max(this.absoluteLineStart,this.ranges[this.rangeI].from)}}scanLine(e){let t=z;t.end=e;if(e>=this.to){t.text=""}else{t.text=this.lineChunkAt(e);t.end+=t.text.length;if(this.ranges.length>1){let e=this.absoluteLineStart,r=this.rangeI;while(this.ranges[r].to<t.end){r++;let n=this.ranges[r].from;let s=this.lineChunkAt(n);t.end=n+s.length;t.text=t.text.slice(0,this.ranges[r-1].to-e)+s;e=t.end-t.text.length}}}return t}readLine(){let{line:e}=this,{text:t,end:r}=this.scanLine(this.absoluteLineStart);this.absoluteLineEnd=r;e.reset(t);for(;e.depth<this.stack.length;e.depth++){let t=this.stack[e.depth],r=this.parser.skipContextMarkup[t.type];if(!r)throw new Error("Unhandled block context "+h[t.type]);if(!r(t,this,e))break;e.forward()}}lineChunkAt(e){let t=this.input.chunk(e),r;if(!this.input.lineChunks){let e=t.indexOf("\n");r=e<0?t:t.slice(0,e)}else{r=t=="\n"?"":t}return e+r.length>this.to?r.slice(0,this.to-e):r}prevLineEnd(){return this.atEnd?this.lineStart:this.lineStart-1}startContext(e,t,r=0){this.block=l.create(e,r,this.lineStart+t,this.block.hash,this.lineStart+this.line.text.length);this.stack.push(this.block)}startComposite(e,t,r=0){this.startContext(this.parser.getNodeType(e),t,r)}addNode(e,t,r){if(typeof e=="number")e=new a.Tree(this.parser.nodeSet.types[e],Q,Q,(r!==null&&r!==void 0?r:this.prevLineEnd())-t);this.block.addChild(e,t-this.block.from)}addElement(e){this.block.addChild(e.toTree(this.parser.nodeSet),e.from-this.block.from)}addLeafElement(e,t){this.addNode(this.buffer.writeElements(fe(t.children,e.marks),-t.from).finish(t.type,t.to-t.from),t.from)}finishContext(){let e=this.stack.pop();let t=this.stack[this.stack.length-1];t.addChild(e.toTree(this.parser.nodeSet),e.from-t.from);this.block=t}finish(){while(this.stack.length>1)this.finishContext();return this.addGaps(this.block.toTree(this.parser.nodeSet,this.lineStart))}addGaps(e){return this.ranges.length>1?j(this.ranges,0,e.topNode,this.ranges[0].from,this.dontInject):e}finishLeaf(e){for(let r of e.parsers)if(r.finish(this,e))return;let t=fe(this.parser.parseInline(e.content,e.start),e.marks);this.addNode(this.buffer.writeElements(t,-e.start).finish(h.Paragraph,e.content.length),e.start)}elt(e,t,r,n){if(typeof e=="string")return K(this.parser.getNodeType(e),t,r,n);return new G(e,t)}get buffer(){return new Z(this.parser.nodeSet)}}function j(e,t,r,n,s){if(s.has(r.tree))return r.tree;let i=e[t].to;let o=[],l=[],h=r.from+n;function f(r,s){while(s?r>=i:r>i){let s=e[t+1].from-i;n+=s;r+=s;t++;i=e[t].to}}for(let a=r.firstChild;a;a=a.nextSibling){f(a.from+n,true);let r=a.from+n,u;if(a.to+n>i){u=j(e,t,a,n,s);f(a.to+n,false)}else{u=a.toTree()}o.push(u);l.push(r-h)}f(r.to+n,false);return new a.Tree(r.type,o,l,r.to+n-h,r.tree?r.tree.propValues:undefined)}class $ extends a.Parser{constructor(e,t,r,n,s,i,a,o,l){super();this.nodeSet=e;this.blockParsers=t;this.leafBlockParsers=r;this.blockNames=n;this.endLeafBlock=s;this.skipContextMarkup=i;this.inlineParsers=a;this.inlineNames=o;this.wrappers=l;this.nodeTypes=Object.create(null);for(let h of e.types)this.nodeTypes[h.name]=h.id}createParse(e,t,r){let n=new D(this,e,t,r);for(let s of this.wrappers)n=s(n,e,t,r);return n}configure(e){let t=F(e);if(!t)return this;let{nodeSet:r,skipContextMarkup:n}=this;let s=this.blockParsers.slice(),i=this.leafBlockParsers.slice(),l=this.blockNames.slice(),f=this.inlineParsers.slice(),u=this.inlineNames.slice(),d=this.endLeafBlock.slice(),c=this.wrappers;if(q(t.defineNodes)){n=Object.assign({},n);let e=r.types.slice(),s;for(let r of t.defineNodes){let{name:t,block:i,composite:l,style:f}=typeof r=="string"?{name:r}:r;if(e.some((e=>e.name==t)))continue;if(l)n[e.length]=(e,t,r)=>l(t,r,e.value);let u=e.length;let d=l?["Block","BlockContext"]:!i?undefined:u>=h.ATXHeading1&&u<=h.SetextHeading2?["Block","LeafBlock","Heading"]:["Block","LeafBlock"];e.push(a.NodeType.define({id:u,name:t,props:d&&[[a.NodeProp.group,d]]}));if(f){if(!s)s={};if(Array.isArray(f)||f instanceof o.Tag)s[t]=f;else Object.assign(s,f)}}r=new a.NodeSet(e);if(s)r=r.extend((0,o.styleTags)(s))}if(q(t.props))r=r.extend(...t.props);if(q(t.remove)){for(let e of t.remove){let t=this.blockNames.indexOf(e),r=this.inlineNames.indexOf(e);if(t>-1)s[t]=i[t]=undefined;if(r>-1)f[r]=undefined}}if(q(t.parseBlock)){for(let e of t.parseBlock){let t=l.indexOf(e.name);if(t>-1){s[t]=e.parse;i[t]=e.leaf}else{let t=e.before?_(l,e.before):e.after?_(l,e.after)+1:l.length-1;s.splice(t,0,e.parse);i.splice(t,0,e.leaf);l.splice(t,0,e.name)}if(e.endLeaf)d.push(e.endLeaf)}}if(q(t.parseInline)){for(let e of t.parseInline){let t=u.indexOf(e.name);if(t>-1){f[t]=e.parse}else{let t=e.before?_(u,e.before):e.after?_(u,e.after)+1:u.length-1;f.splice(t,0,e.parse);u.splice(t,0,e.name)}}}if(t.wrap)c=c.concat(t.wrap);return new $(r,s,i,l,d,n,f,u,c)}getNodeType(e){let t=this.nodeTypes[e];if(t==null)throw new RangeError(`Unknown node type '${e}'`);return t}parseInline(e,t){let r=new he(this,e,t);e:for(let n=t;n<r.end;){let e=r.char(n);for(let t of this.inlineParsers)if(t){let s=t(r,e,n);if(s>=0){n=s;continue e}}n++}return r.resolveMarkers(0)}}function q(e){return e!=null&&e.length>0}function F(e){if(!Array.isArray(e))return e;if(e.length==0)return null;let t=F(e[0]);if(e.length==1)return t;let r=F(e.slice(1));if(!r||!t)return t||r;let n=(e,t)=>(e||Q).concat(t||Q);let s=t.wrap,i=r.wrap;return{props:n(t.props,r.props),defineNodes:n(t.defineNodes,r.defineNodes),parseBlock:n(t.parseBlock,r.parseBlock),parseInline:n(t.parseInline,r.parseInline),remove:n(t.remove,r.remove),wrap:!s?i:!i?s:(e,t,r,n)=>s(i(e,t,r,n),t,r,n)}}function _(e,t){let r=e.indexOf(t);if(r<0)throw new RangeError(`Position specified relative to unknown parser ${t}`);return r}let U=[a.NodeType.none];for(let tt=1,rt;rt=h[tt];tt++){U[tt]=a.NodeType.define({id:tt,name:rt,props:tt>=h.Escape?[]:[[a.NodeProp.group,tt in c?["Block","BlockContext"]:["Block","LeafBlock"]]]})}const Q=[];class Z{constructor(e){this.nodeSet=e;this.content=[];this.nodes=[]}write(e,t,r,n=0){this.content.push(e,t,r,4+n*4);return this}writeElements(e,t=0){for(let r of e)r.writeTo(this,t);return this}finish(e,t){return a.Tree.build({buffer:this.content,nodeSet:this.nodeSet,reused:this.nodes,topID:e,length:t})}}class V{constructor(e,t,r,n=Q){this.type=e;this.from=t;this.to=r;this.children=n}writeTo(e,t){let r=e.content.length;e.writeElements(this.children,t);e.content.push(this.type,this.from+t,this.to+t,e.content.length+4-r)}toTree(e){return new Z(e).writeElements(this.children,-this.from).finish(this.type,this.to-this.from)}}class G{constructor(e,t){this.tree=e;this.from=t}get to(){return this.from+this.tree.length}get type(){return this.tree.type.id}get children(){return Q}writeTo(e,t){e.nodes.push(this.tree);e.content.push(e.nodes.length-1,this.from+t,this.to+t,-1)}toTree(){return this.tree}}function K(e,t,r,n){return new V(e,t,r,n)}const J={resolve:"Emphasis",mark:"EmphasisMark"};const W={resolve:"Emphasis",mark:"EmphasisMark"};const Y={},ee={};class te{constructor(e,t,r,n){this.type=e;this.from=t;this.to=r;this.side=n}}const re="!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~";let ne=/[!"#$%&'()*+,\-.\/:;<=>?@\[\\\]^_`{|}~\xA1\u2010-\u2027]/;try{ne=new RegExp("[\\p{Pc}|\\p{Pd}|\\p{Pe}|\\p{Pf}|\\p{Pi}|\\p{Po}|\\p{Ps}]","u")}catch(et){}const se={Escape(e,t,r){if(t!=92||r==e.end-1)return-1;let n=e.char(r+1);for(let s=0;s<re.length;s++)if(re.charCodeAt(s)==n)return e.append(K(h.Escape,r,r+2));return-1},Entity(e,t,r){if(t!=38)return-1;let n=/^(?:#\d+|#x[a-f\d]+|\w+);/i.exec(e.slice(r+1,r+31));return n?e.append(K(h.Entity,r,r+1+n[0].length)):-1},InlineCode(e,t,r){if(t!=96||r&&e.char(r-1)==96)return-1;let n=r+1;while(n<e.end&&e.char(n)==96)n++;let s=n-r,i=0;for(;n<e.end;n++){if(e.char(n)==96){i++;if(i==s&&e.char(n+1)!=96)return e.append(K(h.InlineCode,r,n+1,[K(h.CodeMark,r,r+s),K(h.CodeMark,n+1-s,n+1)]))}else{i=0}}return-1},HTMLTag(e,t,r){if(t!=60||r==e.end-1)return-1;let n=e.slice(r+1,e.end);let s=/^(?:[a-z][-\w+.]+:[^\s>]+|[a-z\d.!#$%&'*+/=?^_`{|}~-]+@[a-z\d](?:[a-z\d-]{0,61}[a-z\d])?(?:\.[a-z\d](?:[a-z\d-]{0,61}[a-z\d])?)*)>/i.exec(n);if(s)return e.append(K(h.URL,r,r+1+s[0].length));let i=/^!--[^>](?:-[^-]|[^-])*?-->/i.exec(n);if(i)return e.append(K(h.Comment,r,r+1+i[0].length));let a=/^\?[^]*?\?>/.exec(n);if(a)return e.append(K(h.ProcessingInstruction,r,r+1+a[0].length));let o=/^(?:![A-Z][^]*?>|!\[CDATA\[[^]*?\]\]>|\/\s*[a-zA-Z][\w-]*\s*>|\s*[a-zA-Z][\w-]*(\s+[a-zA-Z:_][\w-.:]*(?:\s*=\s*(?:[^\s"'=<>`]+|'[^']*'|"[^"]*"))?)*\s*(\/\s*)?>)/.exec(n);if(!o)return-1;return e.append(K(h.HTMLTag,r,r+1+o[0].length))},Emphasis(e,t,r){if(t!=95&&t!=42)return-1;let n=r+1;while(e.char(n)==t)n++;let s=e.slice(r-1,r),i=e.slice(n,n+1);let a=ne.test(s),o=ne.test(i);let l=/\s|^$/.test(s),h=/\s|^$/.test(i);let f=!h&&(!o||l||a);let u=!l&&(!a||h||o);let d=f&&(t==42||!u||a);let c=u&&(t==42||!f||o);return e.append(new te(t==95?J:W,r,n,(d?1:0)|(c?2:0)))},HardBreak(e,t,r){if(t==92&&e.char(r+1)==10)return e.append(K(h.HardBreak,r,r+2));if(t==32){let t=r+1;while(e.char(t)==32)t++;if(e.char(t)==10&&t>=r+2)return e.append(K(h.HardBreak,r,t+1))}return-1},Link(e,t,r){return t==91?e.append(new te(Y,r,r+1,1)):-1},Image(e,t,r){return t==33&&e.char(r+1)==91?e.append(new te(ee,r,r+2,1)):-1},LinkEnd(e,t,r){if(t!=93)return-1;for(let n=e.parts.length-1;n>=0;n--){let t=e.parts[n];if(t instanceof te&&(t.type==Y||t.type==ee)){if(!t.side||e.skipSpace(t.to)==r&&!/[(\[]/.test(e.slice(r+1,r+2))){e.parts[n]=null;return-1}let s=e.takeContent(n);let i=e.parts[n]=ie(e,s,t.type==Y?h.Link:h.Image,t.from,r+1);if(t.type==Y)for(let t=0;t<n;t++){let r=e.parts[t];if(r instanceof te&&r.type==Y)r.side=0}return i.to}}return-1}};function ie(e,t,r,n,s){let{text:i}=e,a=e.char(s),o=s;t.unshift(K(h.LinkMark,n,n+(r==h.Image?2:1)));t.push(K(h.LinkMark,s-1,s));if(a==40){let r=e.skipSpace(s+1);let n=ae(i,r-e.offset,e.offset),a;if(n){r=e.skipSpace(n.to);a=oe(i,r-e.offset,e.offset);if(a)r=e.skipSpace(a.to)}if(e.char(r)==41){t.push(K(h.LinkMark,s,s+1));o=r+1;if(n)t.push(n);if(a)t.push(a);t.push(K(h.LinkMark,r,o))}}else if(a==91){let r=le(i,s-e.offset,e.offset,false);if(r){t.push(r);o=r.to}}return K(r,n,o,t)}function ae(e,t,r){let n=e.charCodeAt(t);if(n==60){for(let n=t+1;n<e.length;n++){let s=e.charCodeAt(n);if(s==62)return K(h.URL,t+r,n+1+r);if(s==60||s==10)return false}return null}else{let n=0,s=t;for(let t=false;s<e.length;s++){let r=e.charCodeAt(s);if(p(r)){break}else if(t){t=false}else if(r==40){n++}else if(r==41){if(!n)break;n--}else if(r==92){t=true}}return s>t?K(h.URL,t+r,s+r):s==e.length?null:false}}function oe(e,t,r){let n=e.charCodeAt(t);if(n!=39&&n!=34&&n!=40)return false;let s=n==40?41:n;for(let i=t+1,a=false;i<e.length;i++){let n=e.charCodeAt(i);if(a)a=false;else if(n==s)return K(h.LinkTitle,t+r,i+1+r);else if(n==92)a=true}return null}function le(e,t,r,n){for(let s=false,i=t+1,a=Math.min(e.length,i+999);i<a;i++){let a=e.charCodeAt(i);if(s)s=false;else if(a==93)return n?false:K(h.LinkLabel,t+r,i+1+r);else{if(n&&!p(a))n=false;if(a==91)return false;else if(a==92)s=true}}return null}class he{constructor(e,t,r){this.parser=e;this.text=t;this.offset=r;this.parts=[]}char(e){return e>=this.end?-1:this.text.charCodeAt(e-this.offset)}get end(){return this.offset+this.text.length}slice(e,t){return this.text.slice(e-this.offset,t-this.offset)}append(e){this.parts.push(e);return e.to}addDelimiter(e,t,r,n,s){return this.append(new te(e,t,r,(n?1:0)|(s?2:0)))}addElement(e){return this.append(e)}resolveMarkers(e){for(let r=e;r<this.parts.length;r++){let t=this.parts[r];if(!(t instanceof te&&t.type.resolve&&t.side&2))continue;let n=t.type==J||t.type==W;let s=t.to-t.from;let i,a=r-1;for(;a>=e;a--){let e=this.parts[a];if(e instanceof te&&e.side&1&&e.type==t.type&&!(n&&(t.side&1||e.side&2)&&(e.to-e.from+s)%3==0&&((e.to-e.from)%3||s%3))){i=e;break}}if(!i)continue;let o=t.type.resolve,l=[];let h=i.from,f=t.to;if(n){let e=Math.min(2,i.to-i.from,s);h=i.to-e;f=t.from+e;o=e==1?"Emphasis":"StrongEmphasis"}if(i.type.mark)l.push(this.elt(i.type.mark,h,i.to));for(let e=a+1;e<r;e++){if(this.parts[e]instanceof V)l.push(this.parts[e]);this.parts[e]=null}if(t.type.mark)l.push(this.elt(t.type.mark,t.from,f));let u=this.elt(o,h,f,l);this.parts[a]=n&&i.from!=h?new te(i.type,i.from,h,i.side):null;let d=this.parts[r]=n&&t.to!=f?new te(t.type,f,t.to,t.side):null;if(d)this.parts.splice(r,0,u);else this.parts[r]=u}let t=[];for(let r=e;r<this.parts.length;r++){let e=this.parts[r];if(e instanceof V)t.push(e)}return t}findOpeningDelimiter(e){for(let t=this.parts.length-1;t>=0;t--){let r=this.parts[t];if(r instanceof te&&r.type==e)return t}return null}takeContent(e){let t=this.resolveMarkers(e);this.parts.length=e;return t}skipSpace(e){return g(this.text,e-this.offset)+this.offset}elt(e,t,r,n){if(typeof e=="string")return K(this.parser.getNodeType(e),t,r,n);return new G(e,t)}}function fe(e,t){if(!t.length)return e;if(!e.length)return t;let r=e.slice(),n=0;for(let s of t){while(n<r.length&&r[n].to<s.to)n++;if(n<r.length&&r[n].from<s.from){let e=r[n];if(e instanceof V)r[n]=new V(e.type,e.from,e.to,fe(e.children,[s]))}else{r.splice(n++,0,s)}}return r}const ue=[h.CodeBlock,h.ListItem,h.OrderedList,h.BulletList];class de{constructor(e,t){this.fragments=e;this.input=t;this.i=0;this.fragment=null;this.fragmentEnd=-1;this.cursor=null;if(e.length)this.fragment=e[this.i++]}nextFragment(){this.fragment=this.i<this.fragments.length?this.fragments[this.i++]:null;this.cursor=null;this.fragmentEnd=-1}moveTo(e,t){while(this.fragment&&this.fragment.to<=e)this.nextFragment();if(!this.fragment||this.fragment.from>(e?e-1:0))return false;if(this.fragmentEnd<0){let e=this.fragment.to;while(e>0&&this.input.read(e-1,e)!="\n")e--;this.fragmentEnd=e?e-1:0}let r=this.cursor;if(!r){r=this.cursor=this.fragment.tree.cursor();r.firstChild()}let n=e+this.fragment.offset;while(r.to<=n)if(!r.parent())return false;for(;;){if(r.from>=n)return this.fragment.from<=t;if(!r.childAfter(n))return false}}matches(e){let t=this.cursor.tree;return t&&t.prop(a.NodeProp.contextHash)==e}takeNodes(e){let t=this.cursor,r=this.fragment.offset,n=this.fragmentEnd-(this.fragment.openEnd?1:0);let s=e.absoluteLineStart,i=s,a=e.block.children.length;let o=i,l=a;for(;;){if(t.to-r>n){if(t.type.isAnonymous&&t.firstChild())continue;break}e.dontInject.add(t.tree);e.addNode(t.tree,t.from-r);if(t.type.is("Block")){if(ue.indexOf(t.type.id)<0){i=t.to-r;a=e.block.children.length}else{i=o;a=l;o=t.to-r;l=e.block.children.length}}if(!t.nextSibling())break}while(e.block.children.length>a){e.block.children.pop();e.block.positions.pop()}return i-s}}const ce=(0,o.styleTags)({"Blockquote/...":o.tags.quote,HorizontalRule:o.tags.contentSeparator,"ATXHeading1/... SetextHeading1/...":o.tags.heading1,"ATXHeading2/... SetextHeading2/...":o.tags.heading2,"ATXHeading3/...":o.tags.heading3,"ATXHeading4/...":o.tags.heading4,"ATXHeading5/...":o.tags.heading5,"ATXHeading6/...":o.tags.heading6,"Comment CommentBlock":o.tags.comment,Escape:o.tags.escape,Entity:o.tags.character,"Emphasis/...":o.tags.emphasis,"StrongEmphasis/...":o.tags.strong,"Link/... Image/...":o.tags.link,"OrderedList/... BulletList/...":o.tags.list,"BlockQuote/...":o.tags.quote,"InlineCode CodeText":o.tags.monospace,URL:o.tags.url,"HeaderMark HardBreak QuoteMark ListMark LinkMark EmphasisMark CodeMark":o.tags.processingInstruction,"CodeInfo LinkLabel":o.tags.labelName,LinkTitle:o.tags.string,Paragraph:o.tags.content});const pe=new $(new a.NodeSet(U).extend(ce),Object.keys(P).map((e=>P[e])),Object.keys(P).map((e=>R[e])),Object.keys(P),X,c,Object.keys(se).map((e=>se[e])),Object.keys(se),[]);function ge(e,t,r){let n=[];for(let s=e.firstChild,i=t;;s=s.nextSibling){let e=s?s.from:r;if(e>i)n.push({from:i,to:e});if(!s)break;i=s.to}return n}function me(e){let{codeParser:t,htmlParser:r}=e;let n=(0,a.parseMixed)(((e,n)=>{let s=e.type.id;if(t&&(s==h.CodeBlock||s==h.FencedCode)){let r="";if(s==h.FencedCode){let t=e.node.getChild(h.CodeInfo);if(t)r=n.read(t.from,t.to)}let i=t(r);if(i)return{parser:i,overlay:e=>e.type.id==h.CodeText}}else if(r&&(s==h.HTMLBlock||s==h.HTMLTag)){return{parser:r,overlay:ge(e.node,e.from,e.to)}}return null}));return{wrap:n}}const ke={resolve:"Strikethrough",mark:"StrikethroughMark"};const xe={defineNodes:[{name:"Strikethrough",style:{"Strikethrough/...":o.tags.strikethrough}},{name:"StrikethroughMark",style:o.tags.processingInstruction}],parseInline:[{name:"Strikethrough",parse(e,t,r){if(t!=126||e.char(r+1)!=126||e.char(r+2)==126)return-1;let n=e.slice(r-1,r),s=e.slice(r+2,r+3);let i=/\s|^$/.test(n),a=/\s|^$/.test(s);let o=ne.test(n),l=ne.test(s);return e.addDelimiter(ke,r,r+2,!a&&(!l||i||o),!i&&(!o||a||l))},after:"Emphasis"}]};function be(e,t,r=0,n,s=0){let i=0,a=true,o=-1,l=-1,h=false;let f=()=>{n.push(e.elt("TableCell",s+o,s+l,e.parser.parseInline(t.slice(o,l),s+o)))};for(let u=r;u<t.length;u++){let r=t.charCodeAt(u);if(r==124&&!h){if(!a||o>-1)i++;a=false;if(n){if(o>-1)f();n.push(e.elt("TableDelimiter",u+s,u+s+1))}o=l=-1}else if(h||r!=32&&r!=9){if(o<0)o=u;l=u+1}h=!h&&r==92}if(o>-1){i++;if(n)f()}return i}function Le(e,t){for(let r=t;r<e.length;r++){let t=e.charCodeAt(r);if(t==124)return true;if(t==92)r++}return false}const Se=/^\|?(\s*:?-+:?\s*\|)+(\s*:?-+:?\s*)?$/;class we{constructor(){this.rows=null}nextLine(e,t,r){if(this.rows==null){this.rows=false;let n;if((t.next==45||t.next==58||t.next==124)&&Se.test(n=t.text.slice(t.pos))){let s=[],i=be(e,r.content,0,s,r.start);if(i==be(e,n,t.pos))this.rows=[e.elt("TableHeader",r.start,r.start+r.content.length,s),e.elt("TableDelimiter",e.lineStart+t.pos,e.lineStart+t.text.length)]}}else if(this.rows){let r=[];be(e,t.text,t.pos,r,e.lineStart);this.rows.push(e.elt("TableRow",e.lineStart+t.pos,e.lineStart+t.text.length,r))}return false}finish(e,t){if(!this.rows)return false;e.addLeafElement(t,e.elt("Table",t.start,t.start+t.content.length,this.rows));return true}}const Ce={defineNodes:[{name:"Table",block:true},{name:"TableHeader",style:{"TableHeader/...":o.tags.heading}},"TableRow",{name:"TableCell",style:o.tags.content},{name:"TableDelimiter",style:o.tags.processingInstruction}],parseBlock:[{name:"Table",leaf(e,t){return Le(t.content,0)?new we:null},endLeaf(e,t,r){if(r.parsers.some((e=>e instanceof we))||!Le(t.text,t.basePos))return false;let n=e.scanLine(e.absoluteLineEnd+1).text;return Se.test(n)&&be(e,t.text,t.basePos)==be(e,n,t.basePos)},before:"SetextHeading"}]};class ye{nextLine(){return false}finish(e,t){e.addLeafElement(t,e.elt("Task",t.start,t.start+t.content.length,[e.elt("TaskMarker",t.start,t.start+3),...e.parser.parseInline(t.content.slice(3),t.start+3)]));return true}}const Te={defineNodes:[{name:"Task",block:true,style:o.tags.list},{name:"TaskMarker",style:o.tags.atom}],parseBlock:[{name:"TaskList",leaf(e,t){return/^\[[ xX]\]/.test(t.content)&&e.parentType().name=="ListItem"?new ye:null},after:"SetextHeading"}]};const Ae=[Ce,Te,xe];function Ie(e,t,r){return(n,s,i)=>{if(s!=e||n.char(i+1)==e)return-1;let a=[n.elt(r,i,i+1)];for(let o=i+1;o<n.end;o++){let s=n.char(o);if(s==e)return n.addElement(n.elt(t,i,o+1,a.concat(n.elt(r,o,o+1))));if(s==92)a.push(n.elt("Escape",o,o+++2));if(p(s))break}return-1}}const Be={defineNodes:[{name:"Superscript",style:o.tags.special(o.tags.content)},{name:"SuperscriptMark",style:o.tags.processingInstruction}],parseInline:[{name:"Superscript",parse:Ie(94,"Superscript","SuperscriptMark")}]};const Ee={defineNodes:[{name:"Subscript",style:o.tags.special(o.tags.content)},{name:"SubscriptMark",style:o.tags.processingInstruction}],parseInline:[{name:"Subscript",parse:Ie(126,"Subscript","SubscriptMark")}]};const Me={defineNodes:[{name:"Emoji",style:o.tags.character}],parseInline:[{name:"Emoji",parse(e,t,r){let n;if(t!=58||!(n=/^[a-zA-Z_0-9]+:/.exec(e.slice(r+1,e.end))))return-1;return e.addElement(e.elt("Emoji",r,r+1+n[0].length))}}]};var He=r(60049);const Pe=(0,i.defineLanguageFacet)({commentTokens:{block:{open:"\x3c!--",close:"--\x3e"}}});const ve=new a.NodeProp;const Ne=pe.configure({props:[i.foldNodeProp.add((e=>!e.is("Block")||e.is("Document")||Oe(e)!=null?undefined:(e,t)=>({from:t.doc.lineAt(e.from).to,to:e.to}))),ve.add(Oe),i.indentNodeProp.add({Document:()=>null}),i.languageDataProp.add({Document:Pe})]});function Oe(e){let t=/^(?:ATX|Setext)Heading(\d)$/.exec(e.name);return t?+t[1]:undefined}function Re(e,t){let r=e;for(;;){let e=r.nextSibling,n;if(!e||(n=Oe(e.type))!=null&&n<=t)break;r=e}return r.to}const Xe=i.foldService.of(((e,t,r)=>{for(let n=(0,i.syntaxTree)(e).resolveInner(r,-1);n;n=n.parent){if(n.from<t)break;let e=n.type.prop(ve);if(e==null)continue;let s=Re(n,e);if(s>r)return{from:r,to:s}}return null}));function ze(e){return new i.Language(Pe,e,[Xe],"markdown")}const De=ze(Ne);const je=Ne.configure([Ae,Ee,Be,Me]);const $e=ze(je);function qe(e,t){return r=>{if(r&&e){let t=null;r=/\S*/.exec(r)[0];if(typeof e=="function")t=e(r);else t=i.LanguageDescription.matchLanguageName(e,r,true);if(t instanceof i.LanguageDescription)return t.support?t.support.language.parser:i.ParseContext.getSkippingParser(t.load());else if(t)return t.parser}return t?t.parser:null}}class Fe{constructor(e,t,r,n,s,i,a){this.node=e;this.from=t;this.to=r;this.spaceBefore=n;this.spaceAfter=s;this.type=i;this.item=a}blank(e,t=true){let r=this.spaceBefore+(this.node.name=="Blockquote"?">":"");if(e!=null){while(r.length<e)r+=" ";return r}else{for(let e=this.to-this.from-r.length-this.spaceAfter.length;e>0;e--)r+=" ";return r+(t?this.spaceAfter:"")}}marker(e,t){let r=this.node.name=="OrderedList"?String(+Ue(this.item,e)[2]+t):"";return this.spaceBefore+r+this.type+this.spaceAfter}}function _e(e,t){let r=[];for(let s=e;s&&s.name!="Document";s=s.parent){if(s.name=="ListItem"||s.name=="Blockquote"||s.name=="FencedCode")r.push(s)}let n=[];for(let s=r.length-1;s>=0;s--){let e=r[s],i;let a=t.lineAt(e.from),o=e.from-a.from;if(e.name=="FencedCode"){n.push(new Fe(e,o,o,"","","",null))}else if(e.name=="Blockquote"&&(i=/^[ \t]*>( ?)/.exec(a.text.slice(o)))){n.push(new Fe(e,o,o+i[0].length,"",i[1],">",null))}else if(e.name=="ListItem"&&e.parent.name=="OrderedList"&&(i=/^([ \t]*)\d+([.)])([ \t]*)/.exec(a.text.slice(o)))){let t=i[3],r=i[0].length;if(t.length>=4){t=t.slice(0,t.length-4);r-=4}n.push(new Fe(e.parent,o,o+r,i[1],t,i[2],e))}else if(e.name=="ListItem"&&e.parent.name=="BulletList"&&(i=/^([ \t]*)([-+*])([ \t]{1,4}\[[ xX]\])?([ \t]+)/.exec(a.text.slice(o)))){let t=i[4],r=i[0].length;if(t.length>4){t=t.slice(0,t.length-4);r-=4}let s=i[2];if(i[3])s+=i[3].replace(/[xX]/," ");n.push(new Fe(e.parent,o,o+r,i[1],t,s,e))}}return n}function Ue(e,t){return/^(\s*)(\d+)(?=[.)])/.exec(t.sliceString(e.from,e.from+10))}function Qe(e,t,r,n=0){for(let s=-1,i=e;;){if(i.name=="ListItem"){let e=Ue(i,t);let a=+e[2];if(s>=0){if(a!=s+1)return;r.push({from:i.from+e[1].length,to:i.from+e[0].length,insert:String(s+2+n)})}s=a}let e=i.nextSibling;if(!e)break;i=e}}const Ze=({state:e,dispatch:t})=>{let r=(0,i.syntaxTree)(e),{doc:s}=e;let a=null,o=e.changeByRange((t=>{if(!t.empty||!$e.isActiveAt(e,t.from))return a={range:t};let i=t.from,o=s.lineAt(i);let l=_e(r.resolveInner(i,-1),s);while(l.length&&l[l.length-1].from>i-o.from)l.pop();if(!l.length)return a={range:t};let h=l[l.length-1];if(h.to-h.spaceAfter.length>i-o.from)return a={range:t};let f=i>=h.to-h.spaceAfter.length&&!/\S/.test(o.text.slice(h.to));if(h.item&&f){if(h.node.firstChild.to>=i||o.from>0&&!/[^\s>]/.test(s.lineAt(o.from-1).text)){let e=l.length>1?l[l.length-2]:null;let t,r="";if(e&&e.item){t=o.from+e.from;r=e.marker(s,1)}else{t=o.from+(e?e.to:0)}let a=[{from:t,to:i,insert:r}];if(h.node.name=="OrderedList")Qe(h.item,s,a,-2);if(e&&e.node.name=="OrderedList")Qe(e.item,s,a);return{range:n.EditorSelection.cursor(t+r.length),changes:a}}else{let t="";for(let e=0,r=l.length-2;e<=r;e++){t+=l[e].blank(e<r?l[e+1].from-t.length:null,e<r)}t+=e.lineBreak;return{range:n.EditorSelection.cursor(i+t.length),changes:{from:o.from,insert:t}}}}if(h.node.name=="Blockquote"&&f&&o.from){let r=s.lineAt(o.from-1),n=/>\s*$/.exec(r.text);if(n&&n.index==h.from){let s=e.changes([{from:r.from+n.index,to:r.to},{from:o.from+h.from,to:o.to}]);return{range:t.map(s),changes:s}}}let u=[];if(h.node.name=="OrderedList")Qe(h.item,s,u);let d=h.item&&h.item.from<o.from;let c="";if(!d||/^[\s\d.)\-+*>]*/.exec(o.text)[0].length>=h.to){for(let e=0,t=l.length-1;e<=t;e++){c+=e==t&&!d?l[e].marker(s,1):l[e].blank(e<t?l[e+1].from-c.length:null)}}let p=i;while(p>o.from&&/\s/.test(o.text.charAt(p-o.from-1)))p--;c=e.lineBreak+c;u.push({from:p,to:i,insert:c});return{range:n.EditorSelection.cursor(p+c.length),changes:u}}));if(a)return false;t(e.update(o,{scrollIntoView:true,userEvent:"input"}));return true};function Ve(e){return e.name=="QuoteMark"||e.name=="ListMark"}function Ge(e,t){let r=e.resolveInner(t,-1),n=t;if(Ve(r)){n=r.from;r=r.parent}for(let s;s=r.childBefore(n);){if(Ve(s)){n=s.from}else if(s.name=="OrderedList"||s.name=="BulletList"){r=s.lastChild;n=r.to}else{break}}return r}const Ke=({state:e,dispatch:t})=>{let r=(0,i.syntaxTree)(e);let s=null,a=e.changeByRange((t=>{let i=t.from,{doc:a}=e;if(t.empty&&$e.isActiveAt(e,t.from)){let e=a.lineAt(i);let s=_e(Ge(r,i),a);if(s.length){let r=s[s.length-1];let a=r.to-r.spaceAfter.length+(r.spaceAfter?1:0);if(i-e.from>a&&!/\S/.test(e.text.slice(a,i-e.from)))return{range:n.EditorSelection.cursor(e.from+a),changes:{from:e.from+a,to:i}};if(i-e.from==a&&(!r.item||e.from<=r.item.from||!/\S/.test(e.text.slice(0,r.to)))){let s=e.from+r.from;if(r.item&&r.node.from<r.item.from&&/\S/.test(e.text.slice(r.from,r.to)))return{range:t,changes:{from:s,to:e.from+r.to,insert:r.blank(r.to-r.from)}};if(s<i)return{range:n.EditorSelection.cursor(s),changes:{from:s,to:i}}}}}return s={range:t}}));if(s)return false;t(e.update(a,{scrollIntoView:true,userEvent:"delete"}));return true};const Je=[{key:"Enter",run:Ze},{key:"Backspace",run:Ke}];const We=(0,He.html)({matchClosingTags:false});function Ye(e={}){let{codeLanguages:t,defaultCodeLanguage:r,addKeymap:a=true,base:{parser:o}=De}=e;if(!(o instanceof $))throw new RangeError("Base parser provided to `markdown` should be a Markdown parser");let l=e.extensions?[e.extensions]:[];let h=[We.support],f;if(r instanceof i.LanguageSupport){h.push(r.support);f=r.language}else if(r){f=r}let u=t||f?qe(t,f):undefined;l.push(me({codeParser:u,htmlParser:We.language.parser}));if(a)h.push(n.Prec.high(s.keymap.of(Je)));return new i.LanguageSupport(ze(o.configure(l)),h)}}}]);
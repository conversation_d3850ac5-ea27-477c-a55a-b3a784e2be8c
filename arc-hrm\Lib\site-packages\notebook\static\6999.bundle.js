"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[6999],{36999:(e,t,s)=>{s.r(t),s.d(t,{FOUND_CLASSES:()=>o,GenericSearchProvider:()=>c,HTMLSearchEngine:()=>h,ISearchProviderRegistry:()=>$,SearchDocumentModel:()=>m,SearchDocumentView:()=>L,SearchProvider:()=>n,SearchProviderRegistry:()=>Q,TextSearchEngine:()=>d});var r=s(31516),i=s(81997);class n{constructor(e){this.widget=e,this._stateChanged=new i.Signal(this),this._filtersChanged=new i.Signal(this),this._disposed=!1}get stateChanged(){return this._stateChanged}get filtersChanged(){return this._filtersChanged}get currentMatchIndex(){return null}get isDisposed(){return this._disposed}get matchesCount(){return null}dispose(){this._disposed||(this._disposed=!0,i.Signal.clearData(this))}getInitialQuery(){return""}getFilters(){return{}}static preserveCase(e,t){return e.toUpperCase()===e?t.toUpperCase():e.toLowerCase()===e?t.toLowerCase():a(e)===e?a(t):t}}function a([e="",...t]){return e.toUpperCase()+""+t.join("").toLowerCase()}const o=["cm-string","cm-overlay","cm-searching"],l=["CodeMirror-selectedtext"];class h{static search(e,t){if(!(t instanceof Node))return console.warn("Unable to search with HTMLSearchEngine the provided object.",t),Promise.resolve([]);e.global||(e=new RegExp(e.source,e.flags+"g"));const s=[],r=document.createTreeWalker(t,NodeFilter.SHOW_TEXT,{acceptNode:s=>{let r=s.parentElement;for(;r!==t;){if(r.nodeName in h.UNSUPPORTED_ELEMENTS)return NodeFilter.FILTER_REJECT;r=r.parentElement}return e.test(s.textContent)?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_REJECT}});let i=null;for(;null!==(i=r.nextNode());){e.lastIndex=0;let t=null;for(;null!==(t=e.exec(i.textContent));)s.push({text:t[0],position:t.index,node:i})}return Promise.resolve(s)}}h.UNSUPPORTED_ELEMENTS={BASE:!0,HEAD:!0,LINK:!0,META:!0,STYLE:!0,TITLE:!0,BODY:!0,AREA:!0,AUDIO:!0,IMG:!0,MAP:!0,TRACK:!0,VIDEO:!0,APPLET:!0,EMBED:!0,IFRAME:!0,NOEMBED:!0,OBJECT:!0,PARAM:!0,PICTURE:!0,SOURCE:!0,CANVAS:!0,NOSCRIPT:!0,SCRIPT:!0,SVG:!0};class c extends n{constructor(){super(...arguments),this.isReadOnly=!0,this._matches=[],this._mutationObserver=new MutationObserver(this._onWidgetChanged.bind(this)),this._markNodes=new Array}static isApplicable(e){return e instanceof r.Widget}static createNew(e,t,s){return new c(e)}get currentMatchIndex(){return this._currentMatchIndex>=0?this._currentMatchIndex:null}get currentMatch(){var e;return null!==(e=this._matches[this._currentMatchIndex])&&void 0!==e?e:null}get matches(){return this._matches?this._matches.map((e=>Object.assign({},e))):this._matches}get matchesCount(){return this._matches.length}clearHighlight(){return this._currentMatchIndex>=0&&this._markNodes[this._currentMatchIndex].classList.remove(...l),this._currentMatchIndex=-1,Promise.resolve()}dispose(){this.isDisposed||(this.endQuery().catch((e=>{console.error("Failed to end search query.",e)})),super.dispose())}async highlightNext(e){var t;return null!==(t=this._highlightNext(!1,null==e||e))&&void 0!==t?t:void 0}async highlightPrevious(e){var t;return null!==(t=this._highlightNext(!0,null==e||e))&&void 0!==t?t:void 0}async replaceCurrentMatch(e,t){return Promise.resolve(!1)}async replaceAllMatches(e){return Promise.resolve(!1)}async startQuery(e,t={}){if(await this.endQuery(),this._query=e,null===e)return Promise.resolve();const s=await h.search(e,this.widget.node);let r=0;for(;r<s.length;){let e=s[r].node,t=e.parentNode,i=[s[r]];for(;++r<s.length&&s[r].node===e;)i.unshift(s[r]);const n=i.map((s=>{const r=document.createElement("mark");r.classList.add(...o),r.textContent=s.text;const i=e.splitText(s.position);return i.textContent=i.textContent.slice(s.text.length),t.insertBefore(r,i),r}));for(let e=n.length-1;e>=0;e--)this._markNodes.push(n[e])}this._mutationObserver.observe(this.widget.node,{attributes:!1,characterData:!0,childList:!0,subtree:!0}),this._matches=s}async endQuery(){this._mutationObserver.disconnect(),this._markNodes.forEach((e=>{const t=e.parentNode;t.replaceChild(document.createTextNode(e.textContent),e),t.normalize()})),this._markNodes=[],this._matches=[],this._currentMatchIndex=-1}_highlightNext(e,t){if(0===this._matches.length)return null;if(-1===this._currentMatchIndex?this._currentMatchIndex=e?this.matches.length-1:0:(this._markNodes[this._currentMatchIndex].classList.remove(...l),this._currentMatchIndex=e?this._currentMatchIndex-1:this._currentMatchIndex+1,t&&(this._currentMatchIndex<0||this._currentMatchIndex>=this._matches.length)&&(this._currentMatchIndex=(this._currentMatchIndex+this._matches.length)%this._matches.length)),this._currentMatchIndex>=0&&this._currentMatchIndex<this._matches.length){const t=this._markNodes[this._currentMatchIndex];return t.classList.add(...l),function(e){const t=e.getBoundingClientRect();return t.top>=0&&t.bottom<=(window.innerHeight||document.documentElement.clientHeight)&&t.left>=0&&t.right<=(window.innerWidth||document.documentElement.clientWidth)}(t)||t.scrollIntoView(e),t.focus(),this._matches[this._currentMatchIndex]}return this._currentMatchIndex=-1,null}async _onWidgetChanged(e,t){this._currentMatchIndex=-1,await this.startQuery(this._query),this._stateChanged.emit()}}const d={search(e,t){if("string"!=typeof t)try{t=JSON.stringify(t)}catch(e){return console.warn("Unable to search with TextSearchEngine non-JSON serializable object.",e,t),Promise.resolve([])}e.global||(e=new RegExp(e.source,e.flags+"g"));const s=new Array;let r=null;for(;null!==(r=e.exec(t));)s.push({text:r[0],position:r.index});return Promise.resolve(s)}};var p,u=s(68239),g=s(97934);class m extends u.VDomModel{constructor(e,t){if(super(),this.searchProvider=e,this._caseSensitive=!1,this._disposed=new i.Signal(this),this._parsingError="",this._preserveCase=!1,this._initialQuery="",this._filters={},this._replaceText="",this._searchActive=!1,this._searchExpression="",this._useRegex=!1,this._wholeWords=!1,this._filters={},this.searchProvider.getFilters){const e=this.searchProvider.getFilters();for(const t in e)this._filters[t]=e[t].default}e.stateChanged.connect(this._onProviderStateChanged,this),this._searchDebouncer=new g.Debouncer((()=>{this._updateSearch().catch((e=>{console.error("Failed to update search on document.",e)}))}),t)}get caseSensitive(){return this._caseSensitive}set caseSensitive(e){this._caseSensitive!==e&&(this._caseSensitive=e,this.stateChanged.emit(),this.refresh())}get currentIndex(){return this.searchProvider.currentMatchIndex}get disposed(){return this._disposed}get filters(){return this._filters}get filtersDefinition(){var e,t,s;return null!==(s=null===(t=(e=this.searchProvider).getFilters)||void 0===t?void 0:t.call(e))&&void 0!==s?s:{}}get filtersDefinitionChanged(){return this.searchProvider.filtersChanged||null}get initialQuery(){return this._initialQuery}set initialQuery(e){this._initialQuery=e||this._searchExpression}get suggestedInitialQuery(){return this.searchProvider.getInitialQuery()}get selectionState(){return this.searchProvider.getSelectionState?this.searchProvider.getSelectionState():void 0}get isReadOnly(){return this.searchProvider.isReadOnly}get replaceOptionsSupport(){return this.searchProvider.replaceOptionsSupport}get parsingError(){return this._parsingError}get preserveCase(){return this._preserveCase}set preserveCase(e){this._preserveCase!==e&&(this._preserveCase=e,this.stateChanged.emit(),this.refresh())}get replaceText(){return this._replaceText}set replaceText(e){this._replaceText!==e&&(this._replaceText=e,this.stateChanged.emit())}get searchExpression(){return this._searchExpression}set searchExpression(e){this._searchExpression!==e&&(this._searchExpression=e,this.stateChanged.emit(),this.refresh())}get totalMatches(){return this.searchProvider.matchesCount}get useRegex(){return this._useRegex}set useRegex(e){this._useRegex!==e&&(this._useRegex=e,this.stateChanged.emit(),this.refresh())}get wholeWords(){return this._wholeWords}set wholeWords(e){this._wholeWords!==e&&(this._wholeWords=e,this.stateChanged.emit(),this.refresh())}dispose(){this.isDisposed||(this._searchExpression&&this.endQuery().catch((e=>{console.error(`Failed to end query '${this._searchExpression}.`,e)})),this.searchProvider.stateChanged.disconnect(this._onProviderStateChanged,this),this._searchDebouncer.dispose(),super.dispose())}async endQuery(){this._searchActive=!1,await this.searchProvider.endQuery(),this.stateChanged.emit()}async highlightNext(){await this.searchProvider.highlightNext(),this.stateChanged.emit()}async highlightPrevious(){await this.searchProvider.highlightPrevious(),this.stateChanged.emit()}refresh(){this._searchDebouncer.invoke().catch((e=>{console.error("Failed to invoke search document debouncer.",e)}))}async replaceAllMatches(){await this.searchProvider.replaceAllMatches(this._replaceText,{preserveCase:this.preserveCase,regularExpression:this.useRegex}),this.stateChanged.emit()}async replaceCurrentMatch(){await this.searchProvider.replaceCurrentMatch(this._replaceText,!0,{preserveCase:this.preserveCase,regularExpression:this.useRegex}),this.stateChanged.emit()}async setFilter(e,t){this._filters[e]!==t&&(this.searchProvider.validateFilter?(this._filters[e]=await this.searchProvider.validateFilter(e,t),this._filters[e]===t&&(this.stateChanged.emit(),this.refresh())):(this._filters[e]=t,this.stateChanged.emit(),this.refresh()))}async _updateSearch(){this._parsingError&&(this._parsingError="",this.stateChanged.emit());try{const e=this.searchExpression?p.parseQuery(this.searchExpression,this.caseSensitive,this.useRegex,this.wholeWords):null;e?(this._searchActive=!0,await this.searchProvider.startQuery(e,this._filters)):(this._searchActive=!1,await this.searchProvider.endQuery()),this.stateChanged.emit()}catch(e){this._parsingError=e.toString(),this.stateChanged.emit(),console.error(`Failed to parse expression ${this.searchExpression}`,e)}}_onProviderStateChanged(){this._searchActive&&this.refresh()}}!function(e){e.parseQuery=function(e,t,s,r){const i=t?"gm":"gim";let n=s?e:e.replace(/[-[\]/{}()*+?.\\^$|]/g,"\\$&");r&&(n="\\b"+n+"\\b");const a=new RegExp(n,i);return a.test("")?null:a}}(p||(p={}));var _=s(71677),v=s(32895),x=s(12982),C=s(78156);const E="jp-DocumentSearch-overlay-row",f="jp-DocumentSearch-input",y="jp-DocumentSearch-input-label",S="jp-DocumentSearch-input-wrapper",b="jp-DocumentSearch-input-button-off",w="jp-DocumentSearch-input-button-on",R="jp-DocumentSearch-up-down-button",I="jp-DocumentSearch-search-filter",N="jp-DocumentSearch-replace-button",T="jp-DocumentSearch-replace-button-wrapper",P="jp-DocumentSearch-button-content",M="jp-DocumentSearch-button-wrapper";function D(e){const[t,s]=(0,C.useState)(1),r=(0,C.useCallback)((t=>{var r;const i=t?t.target:null===(r=e.inputRef)||void 0===r?void 0:r.current;if(i){const e=i.value.split(/\n/);let t=e.reduce(((e,t)=>e.length>t.length?e:t),"");i.parentNode&&i.parentNode instanceof HTMLElement&&(i.parentNode.dataset.value=t),s(e.length)}}),[]);return(0,C.useEffect)((()=>{var t,s;null===(s=null===(t=e.inputRef)||void 0===t?void 0:t.current)||void 0===s||s.select(),r()}),[e.initialValue]),C.createElement("label",{className:y},C.createElement("textarea",{onChange:t=>{e.onChange(t),r(t)},onKeyDown:t=>{e.onKeyDown(t),r(t)},rows:t,placeholder:e.placeholder,className:f,key:e.autoUpdate?e.initialValue:null,tabIndex:0,ref:e.inputRef,title:e.title,defaultValue:e.initialValue,autoFocus:e.autoFocus}))}function k(e){var t;const s=(null!==(t=e.translator)&&void 0!==t?t:_.nullTranslator).load("jupyterlab"),r=(0,u.classes)(e.caseSensitive?w:b,P),i=(0,u.classes)(e.useRegex?w:b,P),n=(0,u.classes)(e.wholeWords?w:b,P),a=S;return C.createElement("div",{className:a},C.createElement(D,{placeholder:s.__("Find"),onChange:t=>e.onChange(t),onKeyDown:t=>e.onKeydown(t),inputRef:e.inputRef,initialValue:e.initialSearchText,title:s.__("Find"),autoFocus:!0,autoUpdate:!0}),C.createElement("button",{className:M,onClick:()=>{e.onCaseSensitiveToggled()},tabIndex:0,title:s.__("Match Case")},C.createElement(u.caseSensitiveIcon.react,{className:r,tag:"span"})),C.createElement("button",{className:M,onClick:()=>e.onWordToggled(),tabIndex:0,title:s.__("Match Whole Word")},C.createElement(u.wordIcon.react,{className:n,tag:"span"})),C.createElement("button",{className:M,onClick:()=>e.onRegexToggled(),tabIndex:0,title:s.__("Use Regular Expression")},C.createElement(u.regexIcon.react,{className:i,tag:"span"})))}function F(e){var t,s,r;const i=(null!==(t=e.translator)&&void 0!==t?t:_.nullTranslator).load("jupyterlab"),n=(0,u.classes)(e.preserveCase?w:b,P);return C.createElement("div",{className:"jp-DocumentSearch-replace-wrapper-class"},C.createElement("div",{className:S},C.createElement(D,{placeholder:i.__("Replace"),initialValue:null!==(s=e.replaceText)&&void 0!==s?s:"",onKeyDown:t=>e.onReplaceKeydown(t),onChange:t=>e.onChange(t),title:i.__("Replace"),autoFocus:!1,autoUpdate:!1}),(null===(r=e.replaceOptionsSupport)||void 0===r?void 0:r.preserveCase)?C.createElement("button",{className:M,onClick:()=>e.onPreserveCaseToggled(),tabIndex:0,title:i.__("Preserve Case")},C.createElement(u.caseSensitiveIcon.react,{className:n,tag:"span"})):null),C.createElement("button",{className:T,onClick:()=>e.onReplaceCurrent(),tabIndex:0},C.createElement("span",{className:`${N} ${P}`,tabIndex:0},i.__("Replace"))),C.createElement("button",{className:T,tabIndex:0,onClick:()=>e.onReplaceAll()},C.createElement("span",{className:`${N} ${P}`,tabIndex:-1},i.__("Replace All"))))}function O(e){var t,s;const r=null===(t=e.keyBindings)||void 0===t?void 0:t.next,i=null===(s=e.keyBindings)||void 0===s?void 0:s.previous,n=r?v.CommandRegistry.formatKeystroke(r.keys):"",a=i?v.CommandRegistry.formatKeystroke(i.keys):"",o=a?` (${a})`:"",l=n?` (${n})`:"";return C.createElement("div",{className:"jp-DocumentSearch-up-down-wrapper"},C.createElement("button",{className:M,onClick:()=>e.onHighlightPrevious(),tabIndex:0,title:`${e.trans.__("Previous Match")}${o}`},C.createElement(u.caretUpEmptyThinIcon.react,{className:(0,u.classes)(R,P),tag:"span"})),C.createElement("button",{className:M,onClick:()=>e.onHighlightNext(),tabIndex:0,title:`${e.trans.__("Next Match")}${l}`},C.createElement(u.caretDownEmptyThinIcon.react,{className:(0,u.classes)(R,P),tag:"span"})))}function j(e){return C.createElement("div",{className:"jp-DocumentSearch-index-counter"},0===e.totalMatches?"-/-":`${null===e.currentIndex?"-":e.currentIndex+1}/${e.totalMatches}`)}function A(e){let t=`jp-DocumentSearch-filter-button ${P}`;e.visible&&(t=`${t} jp-DocumentSearch-filter-button-enabled`);const s=e.anyEnabled?u.filterDotIcon:u.filterIcon;return C.createElement("button",{className:M,onClick:()=>e.toggleVisible(),tabIndex:0,title:e.visible?e.trans.__("Hide Search Filters"):e.trans.__("Show Search Filters")},C.createElement(s.react,{className:t,tag:"span",height:"20px",width:"20px"}))}function V(e){return C.createElement("label",{className:e.isEnabled?I:`${I} jp-DocumentSearch-search-filter-disabled`,title:e.description},C.createElement("input",{type:"checkbox",className:"jp-mod-styled",disabled:!e.isEnabled,checked:e.value,onChange:e.onToggle}),e.title)}class W extends C.Component{constructor(e){super(e),this.translator=e.translator||_.nullTranslator}_onSearchChange(e){const t=e.target.value;this.props.onSearchChanged(t)}_onSearchKeydown(e){if(13===e.keyCode)if(e.stopPropagation(),e.preventDefault(),e.ctrlKey){const t=e.target;this._insertNewLine(t),this.props.onSearchChanged(t.value)}else e.shiftKey?this.props.onHighlightPrevious():this.props.onHighlightNext()}_onReplaceKeydown(e){13===e.keyCode&&(e.stopPropagation(),e.preventDefault(),e.ctrlKey?this._insertNewLine(e.target):this.props.onReplaceCurrent())}_insertNewLine(e){const[t,s]=[e.selectionStart,e.selectionEnd];e.setRangeText("\n",t,s,"end")}_onClose(){this.props.onClose()}_onReplaceToggled(){if(!this.props.replaceEntryVisible)for(const e in this.props.filtersDefinition){const t=this.props.filtersDefinition[e];t.supportReplace||this.props.onFilterChanged(e,!1).catch((e=>{console.error(`Fail to update filter value for ${t.title}:\n${e}`)}))}this.props.onReplaceEntryShown(!this.props.replaceEntryVisible)}_toggleFiltersVisibility(){this.props.onFiltersVisibilityChanged(!this.props.filtersVisible)}render(){var e,t;const s=this.translator.load("jupyterlab"),r=!this.props.isReadOnly&&this.props.replaceEntryVisible,i=this.props.filtersDefinition,n=Object.keys(i).length>0,a=n?C.createElement(A,{visible:this.props.filtersVisible,anyEnabled:Object.keys(i).some((e=>{var t;const s=i[e];return null!==(t=this.props.filters[e])&&void 0!==t?t:s.default})),toggleVisible:()=>this._toggleFiltersVisibility(),trans:s}):null,o=null===(e=this.props.keyBindings)||void 0===e?void 0:e.toggleSearchInSelection,l=o?v.CommandRegistry.formatKeystroke(o.keys):"",h=l?` (${l})`:"",c=n?C.createElement("div",{className:"jp-DocumentSearch-search-options"},Object.keys(i).map((e=>{var t;const s=i[e];return C.createElement(V,{key:e,title:s.title,description:s.description+("selection"==e?h:""),isEnabled:!r||s.supportReplace,onToggle:async()=>{await this.props.onFilterChanged(e,!this.props.filters[e])},value:null!==(t=this.props.filters[e])&&void 0!==t?t:s.default})}))):null,d=this.props.replaceEntryVisible?u.caretDownIcon:u.caretRightIcon;return C.createElement(C.Fragment,null,C.createElement("div",{className:E},this.props.isReadOnly?C.createElement("div",{className:"jp-DocumentSearch-toggle-placeholder"}):C.createElement("button",{className:"jp-DocumentSearch-toggle-wrapper",onClick:()=>this._onReplaceToggled(),tabIndex:0,title:s.__("Toggle Replace")},C.createElement(d.react,{className:`jp-DocumentSearch-replace-toggle ${P}`,tag:"span",elementPosition:"center",height:"20px",width:"20px"})),C.createElement(k,{inputRef:this.props.searchInputRef,useRegex:this.props.useRegex,caseSensitive:this.props.caseSensitive,wholeWords:this.props.wholeWords,onCaseSensitiveToggled:this.props.onCaseSensitiveToggled,onRegexToggled:this.props.onRegexToggled,onWordToggled:this.props.onWordToggled,onKeydown:e=>this._onSearchKeydown(e),onChange:e=>this._onSearchChange(e),initialSearchText:this.props.initialSearchText,translator:this.translator}),a,C.createElement(j,{currentIndex:this.props.currentIndex,totalMatches:null!==(t=this.props.totalMatches)&&void 0!==t?t:0}),C.createElement(O,{onHighlightPrevious:()=>{this.props.onHighlightPrevious()},onHighlightNext:()=>{this.props.onHighlightNext()},trans:s,keyBindings:this.props.keyBindings}),C.createElement("button",{className:M,onClick:()=>this._onClose(),tabIndex:0},C.createElement(u.closeIcon.react,{className:"jp-icon-hover",elementPosition:"center",height:"16px",width:"16px"}))),C.createElement("div",{className:E},r?C.createElement(C.Fragment,null,C.createElement(F,{onPreserveCaseToggled:this.props.onPreserveCaseToggled,onReplaceKeydown:e=>this._onReplaceKeydown(e),onChange:e=>this.props.onReplaceChanged(e.target.value),onReplaceCurrent:()=>this.props.onReplaceCurrent(),onReplaceAll:()=>this.props.onReplaceAll(),replaceOptionsSupport:this.props.replaceOptionsSupport,replaceText:this.props.replaceText,preserveCase:this.props.preserveCase,translator:this.translator}),C.createElement("div",{className:"jp-DocumentSearch-spacer"})):null),this.props.filtersVisible?c:null,!!this.props.errorMessage&&C.createElement("div",{className:"jp-DocumentSearch-regex-error"},this.props.errorMessage))}}class L extends u.VDomRenderer{constructor(e,t,s){super(e),this.translator=t,this._showReplace=!1,this._showFilters=!1,this._closed=new i.Signal(this),this.addClass("jp-DocumentSearch-overlay"),this._searchInput=C.createRef(),this._keyBindings=s}get closed(){return this._closed}focusSearchInput(){var e;null===(e=this._searchInput.current)||void 0===e||e.select()}setSearchText(e){this.model.initialQuery=e,e&&(this.model.searchExpression=e)}setReplaceText(e){this.model.replaceText=e}showReplace(){this.setReplaceInputVisibility(!0)}onCloseRequest(e){super.onCloseRequest(e),this._closed.emit(),this.model.endQuery()}setReplaceInputVisibility(e){this._showReplace!==e&&(this._showReplace=e,this.update())}setFiltersVisibility(e){this._showFilters!==e&&(this._showFilters=e,this.update())}render(){return this.model.filtersDefinitionChanged?C.createElement(x.UseSignal,{signal:this.model.filtersDefinitionChanged},(()=>this._renderOverlay())):this._renderOverlay()}_renderOverlay(){return C.createElement(W,{caseSensitive:this.model.caseSensitive,currentIndex:this.model.currentIndex,isReadOnly:this.model.isReadOnly,errorMessage:this.model.parsingError,filters:this.model.filters,filtersDefinition:this.model.filtersDefinition,preserveCase:this.model.preserveCase,replaceEntryVisible:this._showReplace,filtersVisible:this._showFilters,replaceOptionsSupport:this.model.replaceOptionsSupport,replaceText:this.model.replaceText,initialSearchText:this.model.initialQuery,searchInputRef:this._searchInput,totalMatches:this.model.totalMatches,translator:this.translator,useRegex:this.model.useRegex,wholeWords:this.model.wholeWords,onCaseSensitiveToggled:()=>{this.model.caseSensitive=!this.model.caseSensitive},onRegexToggled:()=>{this.model.useRegex=!this.model.useRegex},onWordToggled:()=>{this.model.wholeWords=!this.model.wholeWords},onFilterChanged:async(e,t)=>{await this.model.setFilter(e,t)},onFiltersVisibilityChanged:e=>{this.setFiltersVisibility(e)},onHighlightNext:()=>{this.model.highlightNext()},onHighlightPrevious:()=>{this.model.highlightPrevious()},onPreserveCaseToggled:()=>{this.model.preserveCase=!this.model.preserveCase},onSearchChanged:e=>{this.model.searchExpression=e},onClose:()=>{this.close()},onReplaceEntryShown:e=>{this.setReplaceInputVisibility(e)},onReplaceChanged:e=>{this.model.replaceText=e},onReplaceCurrent:()=>{this.model.replaceCurrentMatch()},onReplaceAll:()=>{this.model.replaceAllMatches()},keyBindings:this._keyBindings})}}var U=s(2549);class Q{constructor(e=_.nullTranslator){this.translator=e,this._changed=new i.Signal(this),this._providerMap=new Map}add(e,t){return this._providerMap.set(e,t),this._changed.emit(),new U.DisposableDelegate((()=>{this._providerMap.delete(e),this._changed.emit()}))}getProvider(e){for(const t of this._providerMap.values())if(t.isApplicable(e))return t.createNew(e,this.translator)}hasProvider(e){for(const t of this._providerMap.values())if(t.isApplicable(e))return!0;return!1}get changed(){return this._changed}}const $=new(s(20998).Token)("@jupyterlab/documentsearch:ISearchProviderRegistry","A service for a registry of search\n  providers for the application. Plugins can register their UI elements with this registry\n  to provide find/replace support.")}}]);
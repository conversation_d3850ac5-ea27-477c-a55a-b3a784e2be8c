"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[9341],{39341:(e,t,s)=>{s.r(t),s.d(t,{BreadCrumbs:()=>w,CHUNK_SIZE:()=>Z,DirListing:()=>q,FileBrowser:()=>$,FileBrowserModel:()=>Q,FileDialog:()=>se,FileUploadStatus:()=>pe,FilterFileBrowserModel:()=>te,IDefaultFileBrowser:()=>ae,IFileBrowserCommands:()=>oe,IFileBrowserFactory:()=>ne,LARGE_FILE_SIZE:()=>G,TogglableHiddenFileBrowserModel:()=>ee,Uploader:()=>le});var i=s(12982),r=s(38639),n=s(16954),a=s(71677),o=s(68239),l=s(31516),h=s(90157),d=s(33625),c=s(20998),m=s(18395);const u="jp-BreadCrumbs-home",p="jp-BreadCrumbs-preferred",_="jp-BreadCrumbs-item",f=["/","../../","../",""],g="application/x-jupyter-icontents",v="jp-mod-dropTarget";class w extends l.Widget{constructor(e){super(),this._previousState=null,this.translator=e.translator||a.nullTranslator,this._trans=this.translator.load("jupyterlab"),this._model=e.model,this._fullPath=e.fullPath||!1,this.addClass("jp-BreadCrumbs"),this._crumbs=y.createCrumbs(),this._crumbSeps=y.createCrumbSeparators();const t=r.PageConfig.getOption("preferredPath");this._hasPreferred=!(!t||"/"===t),this._hasPreferred&&this.node.appendChild(this._crumbs[y.Crumb.Preferred]),this.node.appendChild(this._crumbs[y.Crumb.Home]),this._model.refreshed.connect(this.update,this)}handleEvent(e){switch(e.type){case"click":this._evtClick(e);break;case"lm-dragenter":this._evtDragEnter(e);break;case"lm-dragleave":this._evtDragLeave(e);break;case"lm-dragover":this._evtDragOver(e);break;case"lm-drop":this._evtDrop(e);break;default:return}}get fullPath(){return this._fullPath}set fullPath(e){this._fullPath=e}onAfterAttach(e){super.onAfterAttach(e),this.update();const t=this.node;t.addEventListener("click",this),t.addEventListener("lm-dragenter",this),t.addEventListener("lm-dragleave",this),t.addEventListener("lm-dragover",this),t.addEventListener("lm-drop",this)}onBeforeDetach(e){super.onBeforeDetach(e);const t=this.node;t.removeEventListener("click",this),t.removeEventListener("lm-dragenter",this),t.removeEventListener("lm-dragleave",this),t.removeEventListener("lm-dragover",this),t.removeEventListener("lm-drop",this)}onUpdateRequest(e){const t={path:this._model.manager.services.contents.localPath(this._model.path),hasPreferred:this._hasPreferred,fullPath:this._fullPath};this._previousState&&c.JSONExt.deepEqual(t,this._previousState)||(this._previousState=t,y.updateCrumbs(this._crumbs,this._crumbSeps,t))}_evtClick(e){if(0!==e.button)return;let t=e.target;for(;t&&t!==this.node;){if(t.classList.contains(p))return this._model.cd(r.PageConfig.getOption("preferredPath")).catch((e=>(0,i.showErrorMessage)(this._trans.__("Open Error"),e))),e.preventDefault(),void e.stopPropagation();if(t.classList.contains(_)||t.classList.contains(u)){let s=d.ArrayExt.findFirstIndex(this._crumbs,(e=>e===t)),r=f[s];return this._fullPath&&s<0&&!t.classList.contains(u)&&(r=t.title),this._model.cd(r).catch((e=>(0,i.showErrorMessage)(this._trans.__("Open Error"),e))),e.preventDefault(),void e.stopPropagation()}t=t.parentElement}}_evtDragEnter(e){if(e.mimeData.hasData(g)){const t=d.ArrayExt.findFirstIndex(this._crumbs,(t=>m.ElementExt.hitTest(t,e.clientX,e.clientY)));-1!==t&&t!==y.Crumb.Current&&(this._crumbs[t].classList.add(v),e.preventDefault(),e.stopPropagation())}}_evtDragLeave(e){e.preventDefault(),e.stopPropagation();const t=i.DOMUtils.findElement(this.node,v);t&&t.classList.remove(v)}_evtDragOver(e){e.preventDefault(),e.stopPropagation(),e.dropAction=e.proposedAction;const t=i.DOMUtils.findElement(this.node,v);t&&t.classList.remove(v);const s=d.ArrayExt.findFirstIndex(this._crumbs,(t=>m.ElementExt.hitTest(t,e.clientX,e.clientY)));-1!==s&&this._crumbs[s].classList.add(v)}_evtDrop(e){if(e.preventDefault(),e.stopPropagation(),"none"===e.proposedAction)return void(e.dropAction="none");if(!e.mimeData.hasData(g))return;e.dropAction=e.proposedAction;let t=e.target;for(;t&&t.parentElement;){if(t.classList.contains(v)){t.classList.remove(v);break}t=t.parentElement}const s=d.ArrayExt.findFirstIndex(this._crumbs,(e=>e===t));if(-1===s)return;const n=this._model,a=r.PathExt.resolve(n.path,f[s]),o=n.manager,l=[],c=e.mimeData.getData(g);for(const e of c){const t=o.services.contents.localPath(e),s=r.PathExt.basename(t),i=r.PathExt.join(a,s);l.push((0,h.renameFile)(o,e,i))}Promise.all(l).catch((e=>(0,i.showErrorMessage)(this._trans.__("Move Error"),e)))}}var y;!function(e){let t;!function(e){e[e.Home=0]="Home",e[e.Ellipsis=1]="Ellipsis",e[e.Parent=2]="Parent",e[e.Current=3]="Current",e[e.Preferred=4]="Preferred"}(t=e.Crumb||(e.Crumb={})),e.updateCrumbs=function(e,s,i){const r=e[0].parentNode,n=r.firstChild;for(;n&&n.nextSibling;)r.removeChild(n.nextSibling);i.hasPreferred?(r.appendChild(e[t.Home]),r.appendChild(s[0])):r.appendChild(s[0]);const a=i.path.split("/");if(!i.fullPath&&a.length>2){r.appendChild(e[t.Ellipsis]);const i=a.slice(0,a.length-2).join("/");e[t.Ellipsis].title=i,r.appendChild(s[1])}if(i.path)if(i.fullPath)for(let e=0;e<a.length;e++){const t=document.createElement("span");t.className=_,t.textContent=a[e];const s=`/${a.slice(0,e+1).join("/")}`;t.title=s,r.appendChild(t);const i=document.createElement("span");i.textContent="/",r.appendChild(i)}else{if(a.length>=2){e[t.Parent].textContent=a[a.length-2],r.appendChild(e[t.Parent]);const i=a.slice(0,a.length-1).join("/");e[t.Parent].title=i,r.appendChild(s[2])}e[t.Current].textContent=a[a.length-1],r.appendChild(e[t.Current]),e[t.Current].title=i.path,r.appendChild(s[3])}},e.createCrumbs=function(){const e=o.folderIcon.element({className:u,tag:"span",title:r.PageConfig.getOption("serverRoot")||"Jupyter Server Root",stylesheet:"breadCrumb"}),t=o.ellipsesIcon.element({className:_,tag:"span",stylesheet:"breadCrumb"}),s=document.createElement("span");s.className=_;const i=document.createElement("span");return i.className=_,[e,t,s,i,o.homeIcon.element({className:p,tag:"span",title:r.PageConfig.getOption("preferredPath")||"Jupyter Preferred Path",stylesheet:"breadCrumb"})]},e.createCrumbSeparators=function(){const e=[];for(let t=0;t<4;t++){const t=document.createElement("span");t.textContent="/",e.push(t)}return e}}(y||(y={}));var b=s(35312),C=s(99615),E=s(49503),D=s(81997),L=s(24475);const I="jp-DirListing-header",x="jp-DirListing-headerItemIcon",k="jp-DirListing-content",P="jp-DirListing-itemText",F="jp-DirListing-itemIcon",N="jp-DirListing-itemModified",M="jp-DirListing-itemFileSize",S="jp-DirListing-checkboxWrapper",j="jp-id-name",T="jp-id-modified",A="jp-id-filesize",O="jp-LastModified-hidden",B="jp-FileSize-hidden",U="application/x-jupyter-icontents",R="jp-mod-dropTarget",V="jp-mod-selected",K="jp-mod-cut",z="jp-mod-multiSelected",H="jp-mod-running",W="jp-mod-descending",Y=!!navigator.platform.match(/Mac/i);class q extends l.Widget{constructor(e){super({node:(e.renderer||q.defaultRenderer).createNode()}),this._items=[],this._sortedItems=[],this._sortState={direction:"ascending",key:"name"},this._onItemOpened=new D.Signal(this),this._drag=null,this._dragData=null,this._selectTimer=-1,this._isCut=!1,this._prevPath="",this._clipboard=[],this._softSelection="",this.selection=Object.create(null),this._searchPrefix="",this._searchPrefixTimer=-1,this._inRename=!1,this._isDirty=!1,this._hiddenColumns=new Set,this._sortNotebooksFirst=!1,this._focusIndex=0,this.addClass("jp-DirListing"),this.translator=e.translator||a.nullTranslator,this._trans=this.translator.load("jupyterlab"),this._model=e.model,this._model.fileChanged.connect(this._onFileChanged,this),this._model.refreshed.connect(this._onModelRefreshed,this),this._model.pathChanged.connect(this._onPathChanged,this),this._editNode=document.createElement("input"),this._editNode.className="jp-DirListing-editor",this._manager=this._model.manager,this._renderer=e.renderer||q.defaultRenderer;const t=i.DOMUtils.findElement(this.node,I);this._hiddenColumns.add("file_size"),this._renderer.populateHeaderNode(t,this.translator,this._hiddenColumns),this._manager.activateRequested.connect(this._onActivateRequested,this)}dispose(){this._items.length=0,this._sortedItems.length=0,this._clipboard.length=0,super.dispose()}get model(){return this._model}get headerNode(){return i.DOMUtils.findElement(this.node,I)}get contentNode(){return i.DOMUtils.findElement(this.node,k)}get renderer(){return this._renderer}get sortState(){return this._sortState}get onItemOpened(){return this._onItemOpened}selectedItems(){const e=this._sortedItems;return(0,d.filter)(e,(e=>this.selection[e.path]))}sortedItems(){return this._sortedItems[Symbol.iterator]()}sort(e){this._sortedItems=X.sort(this.model.items(),e,this._sortNotebooksFirst),this._sortState=e,this.update()}rename(){return this._doRename()}cut(){this._isCut=!0,this._copy(),this.update()}copy(){this._copy()}paste(){if(!this._clipboard.length)return this._isCut=!1,Promise.resolve(void 0);const e=this._model.path,t=[];for(const s of this._clipboard)if(this._isCut){const i=this._manager.services.contents.localPath(s).split("/"),n=i[i.length-1],a=r.PathExt.join(e,n);t.push(this._model.manager.rename(s,a))}else t.push(this._model.manager.copy(s,e));for(const e of this._items)e.classList.remove(K);return this._clipboard.length=0,this._isCut=!1,this.removeClass("jp-mod-clipboard"),Promise.all(t).then((()=>{})).catch((e=>{(0,i.showErrorMessage)(this._trans._p("showErrorMessage","Paste Error"),e)}))}async delete(){const e=this._sortedItems.filter((e=>this.selection[e.path]));if(!e.length)return;const t=1===e.length?this._trans.__("Are you sure you want to permanently delete: %1?",e[0].name):this._trans._n("Are you sure you want to permanently delete the %1 selected item?","Are you sure you want to permanently delete the %1 selected items?",e.length),s=await(0,i.showDialog)({title:this._trans.__("Delete"),body:t,buttons:[i.Dialog.cancelButton({label:this._trans.__("Cancel")}),i.Dialog.warnButton({label:this._trans.__("Delete")})],defaultButton:0});!this.isDisposed&&s.button.accept&&await this._delete(e.map((e=>e.path)));let r=this._focusIndex;const n=this._sortedItems.length-e.length-1;r>n&&(r=Math.max(0,n)),this._focusItem(r)}duplicate(){const e=this._model.path,t=[];for(const s of this.selectedItems())"directory"!==s.type&&t.push(this._model.manager.copy(s.path,e));return Promise.all(t).then((()=>{})).catch((e=>{(0,i.showErrorMessage)(this._trans._p("showErrorMessage","Duplicate file"),e)}))}async download(){await Promise.all(Array.from(this.selectedItems()).filter((e=>"directory"!==e.type)).map((e=>this._model.download(e.path))))}shutdownKernels(){const e=this._model,t=this._sortedItems,s=t.map((e=>e.path)),r=Array.from(this._model.sessions()).filter((e=>{const i=d.ArrayExt.firstIndexOf(s,e.path);return this.selection[t[i].path]})).map((t=>e.manager.services.sessions.shutdown(t.id)));return Promise.all(r).then((()=>{})).catch((e=>{(0,i.showErrorMessage)(this._trans._p("showErrorMessage","Shut down kernel"),e)}))}selectNext(e=!1){let t=-1;const s=Object.keys(this.selection),i=this._sortedItems;if(1===s.length||e){const e=s[s.length-1];t=d.ArrayExt.findFirstIndex(i,(t=>t.path===e)),t+=1,t===this._items.length&&(t=0)}else if(0===s.length)t=0;else{const e=s[s.length-1];t=d.ArrayExt.findFirstIndex(i,(t=>t.path===e))}-1!==t&&(this._selectItem(t,e),m.ElementExt.scrollIntoViewIfNeeded(this.contentNode,this._items[t]))}selectPrevious(e=!1){let t=-1;const s=Object.keys(this.selection),i=this._sortedItems;if(1===s.length||e){const e=s[0];t=d.ArrayExt.findFirstIndex(i,(t=>t.path===e)),t-=1,-1===t&&(t=this._items.length-1)}else if(0===s.length)t=this._items.length-1;else{const e=s[0];t=d.ArrayExt.findFirstIndex(i,(t=>t.path===e))}-1!==t&&(this._selectItem(t,e),m.ElementExt.scrollIntoViewIfNeeded(this.contentNode,this._items[t]))}selectByPrefix(){const e=this._searchPrefix.toLowerCase(),t=this._sortedItems,s=d.ArrayExt.findFirstIndex(t,(t=>t.name.toLowerCase().substr(0,e.length)===e));-1!==s&&(this._selectItem(s,!1),m.ElementExt.scrollIntoViewIfNeeded(this.contentNode,this._items[s]))}isSelected(e){const t=this._sortedItems;return 0!==Array.from((0,d.filter)(t,(t=>t.name===e&&this.selection[t.path]))).length}modelForClick(e){const t=this._sortedItems,s=X.hitTestNodes(this._items,e);if(-1!==s)return t[s]}clearSelectedItems(){this.selection=Object.create(null)}async selectItemByName(e,t=!1){return this._selectItemByName(e,t)}async _selectItemByName(e,t=!1,s=!1){if(!s&&this.isSelected(e))return;if(await this.model.refresh(),this.isDisposed)throw new Error("File browser is disposed.");const i=this._sortedItems,r=d.ArrayExt.findFirstIndex(i,(t=>t.name===e));if(-1===r)throw new Error("Item does not exist.");this._selectItem(r,!1,t),E.MessageLoop.sendMessage(this,l.Widget.Msg.UpdateRequest),m.ElementExt.scrollIntoViewIfNeeded(this.contentNode,this._items[r])}handleEvent(e){switch(e.type){case"mousedown":this._evtMousedown(e);break;case"mouseup":this._evtMouseup(e);break;case"mousemove":this._evtMousemove(e);break;case"keydown":this.evtKeydown(e);break;case"click":this._evtClick(e);break;case"dblclick":this.evtDblClick(e);break;case"dragenter":case"dragover":this.addClass("jp-mod-native-drop"),e.preventDefault();break;case"dragleave":case"dragend":this.removeClass("jp-mod-native-drop");break;case"drop":this.removeClass("jp-mod-native-drop"),this.evtNativeDrop(e);break;case"scroll":this._evtScroll(e);break;case"lm-dragenter":this.evtDragEnter(e);break;case"lm-dragleave":this.evtDragLeave(e);break;case"lm-dragover":this.evtDragOver(e);break;case"lm-drop":this.evtDrop(e)}}onAfterAttach(e){super.onAfterAttach(e);const t=this.node,s=i.DOMUtils.findElement(t,k);t.addEventListener("mousedown",this),t.addEventListener("keydown",this),t.addEventListener("click",this),t.addEventListener("dblclick",this),s.addEventListener("dragenter",this),s.addEventListener("dragover",this),s.addEventListener("dragleave",this),s.addEventListener("dragend",this),s.addEventListener("drop",this),s.addEventListener("scroll",this),s.addEventListener("lm-dragenter",this),s.addEventListener("lm-dragleave",this),s.addEventListener("lm-dragover",this),s.addEventListener("lm-drop",this)}onBeforeDetach(e){super.onBeforeDetach(e);const t=this.node,s=i.DOMUtils.findElement(t,k);t.removeEventListener("mousedown",this),t.removeEventListener("keydown",this),t.removeEventListener("click",this),t.removeEventListener("dblclick",this),s.removeEventListener("scroll",this),s.removeEventListener("dragover",this),s.removeEventListener("dragover",this),s.removeEventListener("dragleave",this),s.removeEventListener("dragend",this),s.removeEventListener("drop",this),s.removeEventListener("lm-dragenter",this),s.removeEventListener("lm-dragleave",this),s.removeEventListener("lm-dragover",this),s.removeEventListener("lm-drop",this),document.removeEventListener("mousemove",this,!0),document.removeEventListener("mouseup",this,!0)}onAfterShow(e){this._isDirty&&(this.sort(this.sortState),this.update())}onUpdateRequest(e){var t;this._isDirty=!1;const s=this._sortedItems,r=this._items,n=i.DOMUtils.findElement(this.node,k),a=this._renderer;for(this.removeClass(z),this.removeClass(V);r.length>s.length;)n.removeChild(r.pop());for(;r.length<s.length;){const e=a.createItemNode(this._hiddenColumns);e.classList.add("jp-DirListing-item"),r.push(e),n.appendChild(e)}r.forEach(((e,t)=>{e.classList.remove(V),e.classList.remove(H),e.classList.remove(K);const s=a.getCheckboxNode(e);s&&(s.checked=!1);const i=a.getNameNode(e);i&&(i.tabIndex=t===this._focusIndex?0:-1)}));const o=a.getCheckboxNode(this.headerNode);if(o){const e=Object.keys(this.selection).length,t=s.length>0&&e===s.length,i=!t&&e>0;o.checked=t,o.indeterminate=i,o.dataset.checked=String(t),o.dataset.indeterminate=String(i);const r=this.translator.load("jupyterlab");null==o||o.setAttribute("aria-label",t||i?r.__("Deselect all files and directories"):r.__("Select all files and directories"))}s.forEach(((e,t)=>{const s=r[t],i=this._manager.registry.getFileTypeForModel(e);a.updateItemNode(s,e,i,this.translator,this._hiddenColumns,this.selection[e.path]),this.selection[e.path]&&this._isCut&&this._model.path===this._prevPath&&s.classList.add(K),s.setAttribute("data-isdir","directory"===e.type?"true":"false")}));const l=Object.keys(this.selection).length;l&&(this.addClass(V),l>1&&this.addClass(z));const h=s.map((e=>e.path));for(const e of this._model.sessions()){const s=d.ArrayExt.firstIndexOf(h,e.path),i=r[s];if(i){let s=null===(t=e.kernel)||void 0===t?void 0:t.name;const r=this._model.specs;if(i.classList.add(H),r&&s){const e=r.kernelspecs[s];s=e?e.display_name:this._trans.__("unknown")}i.title=this._trans.__("%1\nKernel: %2",i.title,s)}}this._prevPath=this._model.path}onResize(e){const{width:t}=-1===e.width?this.node.getBoundingClientRect():e;this.toggleClass("jp-DirListing-narrow",t<250)}setColumnVisibility(e,t){t?this._hiddenColumns.delete(e):this._hiddenColumns.add(e),this.headerNode.innerHTML="",this._renderer.populateHeaderNode(this.headerNode,this.translator,this._hiddenColumns)}setNotebooksFirstSorting(e){let t=this._sortNotebooksFirst;this._sortNotebooksFirst=e,this._sortNotebooksFirst!==t&&this.sort(this._sortState)}isWithinCheckboxHitArea(e){let t=e.target;for(;t;){if(t.classList.contains(S))return!0;t=t.parentElement}return!1}_evtClick(e){const t=e.target,s=this.headerNode,i=this._renderer;if(s.contains(t)){const t=i.getCheckboxNode(s);if(t&&this.isWithinCheckboxHitArea(e))"false"===t.dataset.indeterminate&&"false"===t.dataset.checked?this._sortedItems.forEach((e=>this.selection[e.path]=!0)):this.clearSelectedItems(),this.update();else{const t=this.renderer.handleHeaderClick(s,e);t&&this.sort(t)}}else this._focusItem(this._focusIndex)}_evtScroll(e){this.headerNode.scrollLeft=this.contentNode.scrollLeft}_evtMousedown(e){if(e.target===this._editNode)return;if(this._editNode.parentNode){if(this._editNode===e.target)return;this._editNode.focus(),this._editNode.blur(),clearTimeout(this._selectTimer)}let t=X.hitTestNodes(this._items,e);-1!==t&&(this.handleFileSelect(e),0!==e.button&&clearTimeout(this._selectTimer),Y&&e.ctrlKey||2===e.button||0===e.button&&(this._dragData={pressX:e.clientX,pressY:e.clientY,index:t},document.addEventListener("mouseup",this,!0),document.addEventListener("mousemove",this,!0)))}_evtMouseup(e){if(this._softSelection&&(e.metaKey||e.shiftKey||e.ctrlKey||0!==e.button||(this.clearSelectedItems(),this.selection[this._softSelection]=!0,this.update()),this._softSelection=""),0===e.button&&this._focusItem(this._focusIndex),0!==e.button||!this._drag)return document.removeEventListener("mousemove",this,!0),void document.removeEventListener("mouseup",this,!0);e.preventDefault(),e.stopPropagation()}_evtMousemove(e){if(e.preventDefault(),e.stopPropagation(),this._drag||!this._dragData)return;const t=this._dragData,s=Math.abs(e.clientX-t.pressX),i=Math.abs(e.clientY-t.pressY);s<5&&i<5||this._startDrag(t.index,e.clientX,e.clientY)}handleOpen(e){if(this._onItemOpened.emit(e),"directory"===e.type){const t=this._manager.services.contents.localPath(e.path);this._model.cd(`/${t}`).catch((e=>(0,i.showErrorMessage)(this._trans._p("showErrorMessage","Open directory"),e)))}else{const t=e.path;this._manager.openOrReveal(t)}}_getNextFocusIndex(e,t){const s=e+t;return-1===s||s===this._items.length?e:s}_handleArrowY(e,t){if(e.altKey||e.metaKey)return;if(!this._items.length)return;if(!e.target.classList.contains(P))return;e.stopPropagation(),e.preventDefault();const s=this._focusIndex;let i=this._getNextFocusIndex(s,t);t>0&&0===s&&!e.ctrlKey&&0===Object.keys(this.selection).length&&(i=0),e.shiftKey?this._handleMultiSelect(i):e.ctrlKey||this._selectItem(i,e.shiftKey,!1),this._focusItem(i),this.update()}async goUp(){const e=this.model;if(e.path!==e.rootPath)try{await e.cd("..")}catch(t){console.warn(`Failed to go to parent directory of ${e.path}`,t)}}evtKeydown(e){if(!this._inRename){switch(e.keyCode){case 13:if(e.ctrlKey||e.shiftKey||e.altKey||e.metaKey)return;e.preventDefault(),e.stopPropagation();for(const e of this.selectedItems())this.handleOpen(e);return;case 38:return void this._handleArrowY(e,-1);case 40:return void this._handleArrowY(e,1);case 32:if(e.ctrlKey){if(e.metaKey||e.shiftKey||e.altKey)return;const t=this._items[this._focusIndex];if(!t.contains(e.target)||!t.contains(document.activeElement))return;e.stopPropagation(),e.preventDefault();const{path:s}=this._sortedItems[this._focusIndex];return this.selection[s]?delete this.selection[s]:this.selection[s]=!0,void this.update()}}if(void 0!==e.key&&1===e.key.length&&(" "!==e.key&&32!==e.keyCode||"checkbox"!==e.target.type)){if(e.ctrlKey||e.shiftKey||e.altKey||e.metaKey)return;this._searchPrefix+=e.key,clearTimeout(this._searchPrefixTimer),this._searchPrefixTimer=window.setTimeout((()=>{this._searchPrefix=""}),1e3),this.selectByPrefix(),e.stopPropagation(),e.preventDefault()}}}evtDblClick(e){if(0!==e.button)return;if(e.ctrlKey||e.shiftKey||e.altKey||e.metaKey)return;if(this.isWithinCheckboxHitArea(e))return;e.preventDefault(),e.stopPropagation(),clearTimeout(this._selectTimer),this._editNode.blur();const t=e.target,s=d.ArrayExt.findFirstIndex(this._items,(e=>e.contains(t)));if(-1===s)return;const i=this._sortedItems[s];this.handleOpen(i)}evtNativeDrop(e){var t,s,r;const n=null===(t=e.dataTransfer)||void 0===t?void 0:t.files;if(!n||0===n.length)return;const a=null===(s=e.dataTransfer)||void 0===s?void 0:s.items.length;if(a){for(let t=0;t<a;t++){let s=null===(r=e.dataTransfer)||void 0===r?void 0:r.items[t].webkitGetAsEntry();(null==s?void 0:s.isDirectory)&&(console.log("currently not supporting drag + drop for folders"),(0,i.showDialog)({title:this._trans.__("Error Uploading Folder"),body:this._trans.__("Drag and Drop is currently not supported for folders"),buttons:[i.Dialog.cancelButton({label:this._trans.__("Close")})]}))}e.preventDefault();for(let e=0;e<n.length;e++)this._model.upload(n[e])}}evtDragEnter(e){if(e.mimeData.hasData(U)){const t=X.hitTestNodes(this._items,e);if(-1===t)return;const s=this._sortedItems[t];if("directory"!==s.type||this.selection[s.path])return;e.target.classList.add(R),e.preventDefault(),e.stopPropagation()}}evtDragLeave(e){e.preventDefault(),e.stopPropagation();const t=i.DOMUtils.findElement(this.node,R);t&&t.classList.remove(R)}evtDragOver(e){e.preventDefault(),e.stopPropagation(),e.dropAction=e.proposedAction;const t=i.DOMUtils.findElement(this.node,R);t&&t.classList.remove(R);const s=X.hitTestNodes(this._items,e);this._items[s].classList.add(R)}evtDrop(e){if(e.preventDefault(),e.stopPropagation(),clearTimeout(this._selectTimer),"none"===e.proposedAction)return void(e.dropAction="none");if(!e.mimeData.hasData(U))return;let t=e.target;for(;t&&t.parentElement;){if(t.classList.contains(R)){t.classList.remove(R);break}t=t.parentElement}const s=d.ArrayExt.firstIndexOf(this._items,t),n=this._sortedItems;let a=this._model.path;"directory"===n[s].type&&(a=r.PathExt.join(a,n[s].name));const o=this._manager,l=[],c=e.mimeData.getData(U);e.ctrlKey&&"move"===e.proposedAction?e.dropAction="copy":e.dropAction=e.proposedAction;for(const t of c){const s=o.services.contents.localPath(t),i=r.PathExt.basename(s),n=r.PathExt.join(a,i);n!==t&&("copy"===e.dropAction?l.push(o.copy(t,a)):l.push((0,h.renameFile)(o,t,n)))}Promise.all(l).catch((e=>{(0,i.showErrorMessage)(this._trans._p("showErrorMessage","Error while copying/moving files"),e)}))}_startDrag(e,t,s){let i=Object.keys(this.selection);const r=this._items[e],n=this._sortedItems;let a,o;if(r.classList.contains(V)){const e=i[0];o=n.find((t=>t.path===e)),a=this.selectedItems()}else o=n[e],i=[o.path],a=[o];if(!o)return;const l=this._manager.registry.getFileTypeForModel(o),h=this.renderer.createDragImage(r,i.length,this._trans,l);this._drag=new C.Drag({dragImage:h,mimeData:new c.MimeData,supportedActions:"move",proposedAction:"move"}),this._drag.mimeData.setData(U,i);const d=this.model.manager.services;for(const e of a)this._drag.mimeData.setData("application/x-jupyter-icontentsrich",{model:e,withContent:async()=>await d.contents.get(e.path)});if(o&&"directory"!==o.type){const e=i.slice(1).reverse();this._drag.mimeData.setData("application/vnd.lumino.widget-factory",(()=>{if(!o)return;const t=o.path;let s=this._manager.findWidget(t);if(s||(s=this._manager.open(o.path)),e.length){const t=new c.PromiseDelegate;t.promise.then((()=>{let t=s;e.forEach((e=>{const s={ref:null==t?void 0:t.id,mode:"tab-after"};t=this._manager.openOrReveal(e,void 0,void 0,s),this._manager.openOrReveal(o.path)}))})),t.resolve(void 0)}return s}))}document.removeEventListener("mousemove",this,!0),document.removeEventListener("mouseup",this,!0),clearTimeout(this._selectTimer),this._drag.start(t,s).then((e=>{this._drag=null,clearTimeout(this._selectTimer)}))}handleFileSelect(e){const t=this._sortedItems,s=X.hitTestNodes(this._items,e);if(clearTimeout(this._selectTimer),-1===s)return;this._softSelection="";const i=t[s].path,r=Object.keys(this.selection),n=0===e.button&&!(Y&&e.ctrlKey)&&this.isWithinCheckboxHitArea(e);if(Y&&e.metaKey||!Y&&e.ctrlKey||n)this.selection[i]?delete this.selection[i]:this.selection[i]=!0,this._focusItem(s);else if(e.shiftKey)this._handleMultiSelect(s),this._focusItem(s);else{if(!(i in this.selection&&r.length>1))return this._selectItem(s,!1,!0);this._softSelection=i}this.update()}_focusItem(e){const t=this._items;if(0===t.length)return this._focusIndex=0,void this.node.focus();this._focusIndex=e;const s=t[e],i=this.renderer.getNameNode(s);i&&(i.tabIndex=0,i.focus())}_allSelectedBetween(e,t){if(e===t)return;const[s,i]=e<t?[e+1,t]:[t+1,e];return this._sortedItems.slice(s,i).reduce(((e,t)=>e&&this.selection[t.path]),!0)}_handleMultiSelect(e){const t=this._sortedItems,s=this._focusIndex,i=t[e];let r=!0;if(e===s)return void(this.selection[i.path]=!0);if(this.selection[i.path])if(1===Math.abs(e-s)){const i=t[s],r=t[s+(e<s?1:-1)];!this.selection[i.path]||r&&this.selection[r.path]||delete this.selection[i.path]}else this._allSelectedBetween(s,e)&&(r=!1);const n=s<e?1:-1;for(let i=s;i!==e+n;i+=n)if(r){if(i===s)continue;this.selection[t[i].path]=!0}else{if(i===e)continue;delete this.selection[t[i].path]}}_copy(){this._clipboard.length=0;for(const e of this.selectedItems())this._clipboard.push(e.path)}async _delete(e){await Promise.all(e.map((e=>this._model.manager.deleteFile(e).catch((e=>{(0,i.showErrorMessage)(this._trans._p("showErrorMessage","Delete Failed"),e)})))))}async _doRename(){this._inRename=!0;const e=Object.keys(this.selection);if(0===e.length)return this._inRename=!1,Promise.resolve("");const t=this._sortedItems;let{path:s}=t[this._focusIndex];this.selection[s]||(s=e.slice(-1)[0]);const n=d.ArrayExt.findFirstIndex(t,(e=>e.path===s)),a=this._items[n],o=t[n],l=this.renderer.getNameNode(a),c=o.name;this._editNode.value=c,this._selectItem(n,!1,!0);const m=await X.userInputForRename(l,this._editNode,c);if(this.isDisposed)throw this._inRename=!1,new Error("File browser is disposed.");let u=m;if(m&&m!==c)if((0,h.isValidFileName)(m)){const e=this._manager,t=r.PathExt.join(this._model.path,c),s=r.PathExt.join(this._model.path,m);try{await(0,h.renameFile)(e,t,s)}catch(e){"File not renamed"!==e&&(0,i.showErrorMessage)(this._trans._p("showErrorMessage","Rename Error"),e),u=c}if(this.isDisposed)throw this._inRename=!1,new Error("File browser is disposed.")}else(0,i.showErrorMessage)(this._trans.__("Rename Error"),Error(this._trans._p("showErrorMessage",'"%1" is not a valid name for a file. Names must have nonzero length, and cannot include "/", "\\", or ":"',m))),u=c;else u=c;if(!this.isDisposed&&1===Object.keys(this.selection).length&&this.selection[o.path])try{await this._selectItemByName(u,!0,!0)}catch(e){console.warn("After rename, failed to select file",u)}return this._inRename=!1,u}_selectItem(e,t,s=!0){const i=this._sortedItems;t||this.clearSelectedItems();const r=i[e].path;this.selection[r]=!0,s&&this._focusItem(e),this.update()}_onModelRefreshed(){const e=Object.keys(this.selection);this.clearSelectedItems();for(const t of this._model.items()){const s=t.path;-1!==e.indexOf(s)&&(this.selection[s]=!0)}this.isVisible?this.sort(this.sortState):this._isDirty=!0}_onPathChanged(){this.clearSelectedItems(),this.sort(this.sortState),requestAnimationFrame((()=>{this._focusItem(0)}))}_onFileChanged(e,t){const s=t.newValue;if(!s)return;const i=s.name;"new"===t.type&&i&&this.selectItemByName(i).catch((()=>{}))}_onActivateRequested(e,t){if(r.PathExt.dirname(t)!==this._model.path)return;const s=r.PathExt.basename(t);this.selectItemByName(s).catch((()=>{}))}}var X;!function(e){class t{createNode(){const e=document.createElement("div"),t=document.createElement("div"),s=document.createElement("ul");return s.setAttribute("data-lm-dragscroll","true"),s.className=k,t.className=I,e.appendChild(t),e.appendChild(s),e.tabIndex=-1,e}populateHeaderNode(e,t,s){const r=(t=t||a.nullTranslator).load("jupyterlab"),n=this.createHeaderItemNode(r.__("Name")),o=document.createElement("div"),l=this.createHeaderItemNode(r.__("Last Modified")),h=this.createHeaderItemNode(r.__("File Size"));if(n.classList.add(j),n.classList.add(V),l.classList.add(T),h.classList.add(A),o.classList.add("jp-id-narrow"),o.textContent="...",!(null==s?void 0:s.has("is_selected"))){const t=this.createCheckboxWrapperNode({alwaysVisible:!0});e.appendChild(t)}e.appendChild(n),e.appendChild(o),e.appendChild(l),e.appendChild(h),(null==s?void 0:s.has("last_modified"))?l.classList.add(O):l.classList.remove(O),(null==s?void 0:s.has("file_size"))?h.classList.add(B):h.classList.remove(B),X.updateCaret(i.DOMUtils.findElement(n,x),"right","up")}handleHeaderClick(e,t){const s=i.DOMUtils.findElement(e,j),r=i.DOMUtils.findElement(e,T),n=i.DOMUtils.findElement(e,A),a={direction:"ascending",key:"name"},o=t.target,l=i.DOMUtils.findElement(r,x),h=i.DOMUtils.findElement(n,x),d=i.DOMUtils.findElement(s,x);return s.contains(o)?(s.classList.contains(V)?s.classList.contains(W)?(s.classList.remove(W),X.updateCaret(d,"right","up")):(a.direction="descending",s.classList.add(W),X.updateCaret(d,"right","down")):(s.classList.remove(W),X.updateCaret(d,"right","up")),s.classList.add(V),r.classList.remove(V),r.classList.remove(W),n.classList.remove(V),n.classList.remove(W),X.updateCaret(l,"left"),X.updateCaret(h,"left"),a):r.contains(o)?(a.key="last_modified",r.classList.contains(V)?r.classList.contains(W)?(r.classList.remove(W),X.updateCaret(l,"left","up")):(a.direction="descending",r.classList.add(W),X.updateCaret(l,"left","down")):(r.classList.remove(W),X.updateCaret(l,"left","up")),r.classList.add(V),s.classList.remove(V),s.classList.remove(W),n.classList.remove(V),n.classList.remove(W),X.updateCaret(d,"right"),X.updateCaret(h,"left"),a):n.contains(o)?(a.key="file_size",n.classList.contains(V)?n.classList.contains(W)?(n.classList.remove(W),X.updateCaret(h,"left","up")):(a.direction="descending",n.classList.add(W),X.updateCaret(h,"left","down")):(n.classList.remove(W),X.updateCaret(h,"left","up")),n.classList.add(V),s.classList.remove(V),s.classList.remove(W),r.classList.remove(V),r.classList.remove(W),X.updateCaret(d,"right"),X.updateCaret(l,"left"),a):a}createItemNode(e){const t=document.createElement("li"),s=document.createElement("span"),i=document.createElement("span"),r=document.createElement("span"),n=document.createElement("span");if(s.className=F,i.className=P,r.className=N,n.className=M,!(null==e?void 0:e.has("is_selected"))){const e=this.createCheckboxWrapperNode();t.appendChild(e)}return t.appendChild(s),t.appendChild(i),t.appendChild(r),t.appendChild(n),(null==e?void 0:e.has("last_modified"))?r.classList.add(O):r.classList.remove(O),(null==e?void 0:e.has("file_size"))?n.classList.add(B):n.classList.remove(B),t}createCheckboxWrapperNode(e){const t=document.createElement("label");t.classList.add(S);const s=document.createElement("input");return s.type="checkbox",s.addEventListener("click",(e=>{e.preventDefault()})),(null==e?void 0:e.alwaysVisible)?t.classList.add("jp-mod-visible"):s.tabIndex=-1,t.appendChild(s),t}updateItemNode(e,t,s,n,l,h){h&&e.classList.add(V),s=s||b.DocumentRegistry.getDefaultTextFileType(n);const{icon:c,iconClass:m,name:u}=s,p=(n=n||a.nullTranslator).load("jupyterlab"),_=i.DOMUtils.findElement(e,F),f=i.DOMUtils.findElement(e,P),g=i.DOMUtils.findElement(e,N),v=i.DOMUtils.findElement(e,M),w=i.DOMUtils.findElement(e,S),y=!(null==l?void 0:l.has("is_selected"));if(w&&!y)e.removeChild(w);else if(y&&!w){const t=this.createCheckboxWrapperNode();e.insertBefore(t,_)}(null==l?void 0:l.has("last_modified"))?g.classList.add(O):g.classList.remove(O),(null==l?void 0:l.has("file_size"))?v.classList.add(B):v.classList.remove(B),o.LabIcon.resolveElement({icon:c,iconClass:(0,o.classes)(m,"jp-Icon"),container:_,className:F,stylesheet:"listing"});let C=p.__("Name: %1",t.name);if(null!==t.size&&void 0!==t.size){const e=X.formatFileSize(t.size,1,1024);v.textContent=e,C+=p.__("\nSize: %1",X.formatFileSize(t.size,1,1024))}else v.textContent="";if(t.path){const e=r.PathExt.dirname(t.path);e&&(C+=p.__("\nPath: %1",e.substr(0,50)),e.length>50&&(C+="..."))}t.created&&(C+=p.__("\nCreated: %1",r.Time.format(new Date(t.created)))),t.last_modified&&(C+=p.__("\nModified: %1",r.Time.format(new Date(t.last_modified)))),C+=p.__("\nWritable: %1",t.writable),e.title=C,e.setAttribute("data-file-type",u),t.name.startsWith(".")?e.setAttribute("data-is-dot","true"):e.removeAttribute("data-is-dot");const E=t.indices?t.indices:[];let D=d.StringExt.highlight(t.name,E,L.h.mark);f&&L.VirtualDOM.render(L.h.span(D),f);const I=null==w?void 0:w.querySelector('input[type="checkbox"]');if(I){let e;e="directory"===s.contentType?h?p.__('Deselect directory "%1"',D):p.__('Select directory "%1"',D):h?p.__('Deselect file "%1"',D):p.__('Select file "%1"',D),I.setAttribute("aria-label",e),I.checked=null!=h&&h}let x="",k="";t.last_modified&&(x=r.Time.formatHuman(new Date(t.last_modified)),k=r.Time.format(new Date(t.last_modified))),g.textContent=x,g.title=k}getNameNode(e){return i.DOMUtils.findElement(e,P)}getCheckboxNode(e){return e.querySelector(`.${S} input[type=checkbox]`)}createDragImage(e,t,s,r){const n=e.cloneNode(!0),a=i.DOMUtils.findElement(n,N),o=i.DOMUtils.findElement(n,F);return n.removeChild(a),r?(o.textContent=r.iconLabel||"",o.className=r.iconClass||""):(o.textContent="",o.className=""),o.classList.add("jp-DragIcon"),t>1&&(i.DOMUtils.findElement(n,P).textContent=s._n("%1 Item","%1 Items",t)),n}createHeaderItemNode(e){const t=document.createElement("div"),s=document.createElement("span"),i=document.createElement("span");return t.className="jp-DirListing-headerItem",s.className="jp-DirListing-headerItemText",i.className=x,s.textContent=e,t.appendChild(s),t.appendChild(i),t}}e.Renderer=t,e.defaultRenderer=new t}(q||(q={})),function(e){e.userInputForRename=function(e,t,s){const i=e.parentElement;i.replaceChild(t,e),t.focus();const r=t.value.lastIndexOf(".");return-1===r?t.setSelectionRange(0,t.value.length):t.setSelectionRange(0,r),new Promise((r=>{t.onblur=()=>{i.replaceChild(e,t),r(t.value)},t.onkeydown=i=>{switch(i.keyCode){case 13:i.stopPropagation(),i.preventDefault(),t.blur();break;case 27:i.stopPropagation(),i.preventDefault(),t.value=s,t.blur(),e.focus()}}}))},e.sort=function(e,t,s=!1){const i=Array.from(e),r="descending"===t.direction?1:-1;function n(e){return"directory"===e.type?2:"notebook"===e.type&&s?1:0}function a(e){return(t,i)=>{if(function(e,t){return s?e.type!==t.type:"directory"===e.type!=("directory"===t.type)}(t,i))return n(i)-n(t);const a=e(t,i);return 0!==a?a*r:t.name.localeCompare(i.name)}}return"last_modified"===t.key?i.sort(a(((e,t)=>new Date(e.last_modified).getTime()-new Date(t.last_modified).getTime()))):"file_size"===t.key?i.sort(a(((e,t)=>{var s,i;return(null!==(s=e.size)&&void 0!==s?s:0)-(null!==(i=t.size)&&void 0!==i?i:0)}))):i.sort(a(((e,t)=>t.name.localeCompare(e.name)))),i},e.hitTestNodes=function(e,t){return d.ArrayExt.findFirstIndex(e,(e=>m.ElementExt.hitTest(e,t.clientX,t.clientY)||t.target===e))},e.formatFileSize=function(e,t,s){if(0===e)return"0 B";const i=t||2,r=["B","KB","MB","GB","TB","PB","EB","ZB","YB"],n=Math.floor(Math.log(e)/Math.log(s));return n>=0&&n<r.length?parseFloat((e/Math.pow(s,n)).toFixed(i))+" "+r[n]:String(e)},e.updateCaret=function(e,t,s){s?("down"===s?o.caretDownIcon:o.caretUpIcon).element({container:e,tag:"span",stylesheet:"listingHeaderItem",float:t}):(o.LabIcon.remove(e),e.className=x)}}(X||(X={}));class $ extends o.SidePanel{constructor(e){var t;super({content:new l.Panel,translator:e.translator}),this._directoryPending=null,this._filePending=null,this._showLastModifiedColumn=!0,this._showFileSizeColumn=!1,this._showHiddenFiles=!1,this._showFileCheckboxes=!1,this._sortNotebooksFirst=!1,this.addClass("jp-FileBrowser"),this.toolbar.addClass("jp-FileBrowser-toolbar"),this.id=e.id;const s=this.translator=null!==(t=e.translator)&&void 0!==t?t:a.nullTranslator,i=this.model=e.model,r=e.renderer;i.connectionFailure.connect(this._onConnectionFailure,this),this._manager=i.manager,this.toolbar.node.setAttribute("aria-label",this._trans.__("file browser")),this.mainPanel=new l.Panel,this.mainPanel.addClass("jp-FileBrowser-Panel"),this.mainPanel.title.label=this._trans.__("File Browser"),this.crumbs=new w({model:i,translator:s}),this.crumbs.addClass("jp-FileBrowser-crumbs"),this.listing=this.createDirListing({model:i,renderer:r,translator:s}),this.listing.addClass("jp-FileBrowser-listing"),this.mainPanel.addWidget(this.crumbs),this.mainPanel.addWidget(this.listing),this.addWidget(this.mainPanel),!1!==e.restore&&i.restore(this.id)}get navigateToCurrentDirectory(){return this._navigateToCurrentDirectory}set navigateToCurrentDirectory(e){this._navigateToCurrentDirectory=e}get showLastModifiedColumn(){return this._showLastModifiedColumn}set showLastModifiedColumn(e){this.listing.setColumnVisibility?(this.listing.setColumnVisibility("last_modified",e),this._showLastModifiedColumn=e):console.warn("Listing does not support toggling column visibility")}get showFullPath(){return this.crumbs.fullPath}set showFullPath(e){this.crumbs.fullPath=e}get showFileSizeColumn(){return this._showFileSizeColumn}set showFileSizeColumn(e){this.listing.setColumnVisibility?(this.listing.setColumnVisibility("file_size",e),this._showFileSizeColumn=e):console.warn("Listing does not support toggling column visibility")}get showHiddenFiles(){return this._showHiddenFiles}set showHiddenFiles(e){this.model.showHiddenFiles(e),this._showHiddenFiles=e}get showFileCheckboxes(){return this._showFileCheckboxes}set showFileCheckboxes(e){this.listing.setColumnVisibility?(this.listing.setColumnVisibility("is_selected",e),this._showFileCheckboxes=e):console.warn("Listing does not support toggling column visibility")}get sortNotebooksFirst(){return this._sortNotebooksFirst}set sortNotebooksFirst(e){this.listing.setNotebooksFirstSorting?(this.listing.setNotebooksFirstSorting(e),this._sortNotebooksFirst=e):console.warn("Listing does not support sorting notebooks first")}selectedItems(){return this.listing.selectedItems()}async selectItemByName(e){await this.listing.selectItemByName(e)}clearSelectedItems(){this.listing.clearSelectedItems()}rename(){return this.listing.rename()}cut(){this.listing.cut()}copy(){this.listing.copy()}paste(){return this.listing.paste()}async _createNew(e){if(e.path){const t=this._manager.services.contents.localPath(e.path);e.path=this._toDrivePath(this.model.driveName,t)}try{const t=await this._manager.newUntitled(e);return await this.listing.selectItemByName(t.name,!0),await this.rename(),t}catch(e){throw(0,i.showErrorMessage)(this._trans.__("Error"),e),e}}async createNewDirectory(){if(this._directoryPending)return this._directoryPending;this._directoryPending=this._createNew({path:this.model.path,type:"directory"});try{return await this._directoryPending}finally{this._directoryPending=null}}async createNewFile(e){if(this._filePending)return this._filePending;this._filePending=this._createNew({path:this.model.path,type:"file",ext:e.ext});try{return await this._filePending}finally{this._filePending=null}}delete(){return this.listing.delete()}duplicate(){return this.listing.duplicate()}download(){return this.listing.download()}async goUp(){return this.listing.goUp()}shutdownKernels(){return this.listing.shutdownKernels()}selectNext(){this.listing.selectNext()}selectPrevious(){this.listing.selectPrevious()}modelForClick(e){return this.listing.modelForClick(e)}createDirListing(e){return new q(e)}_onConnectionFailure(e,t){if(t instanceof n.ServerConnection.ResponseError&&404===t.response.status){const e=this._trans.__("Directory not found");t.message=this._trans.__('Directory not found: "%1"',this.model.path),(0,i.showErrorMessage)(e,t)}}_toDrivePath(e,t){return""===e?t:`${e}:${r.PathExt.removeSlash(t)}`}}var J=s(97934);const G=15728640,Z=1048576;class Q{constructor(e){var t;this._connectionFailure=new D.Signal(this),this._fileChanged=new D.Signal(this),this._items=[],this._key="",this._pathChanged=new D.Signal(this),this._paths=new Set,this._pending=null,this._pendingPath=null,this._refreshed=new D.Signal(this),this._sessions=[],this._state=null,this._isDisposed=!1,this._restored=new c.PromiseDelegate,this._uploads=[],this._uploadChanged=new D.Signal(this),this.manager=e.manager,this.translator=e.translator||a.nullTranslator,this._trans=this.translator.load("jupyterlab"),this._driveName=e.driveName||"",this._model={path:this.rootPath,name:r.PathExt.basename(this.rootPath),type:"directory",content:void 0,writable:!1,created:"unknown",last_modified:"unknown",mimetype:"text/plain",format:"text"},this._state=e.state||null;const s=e.refreshInterval||1e4,{services:i}=e.manager;i.contents.fileChanged.connect(this.onFileChanged,this),i.sessions.runningChanged.connect(this.onRunningChanged,this),this._unloadEventListener=e=>{if(this._uploads.length>0){const t=this._trans.__("Files still uploading");return e.returnValue=t,t}},window.addEventListener("beforeunload",this._unloadEventListener),this._poll=new J.Poll({auto:null===(t=e.auto)||void 0===t||t,name:"@jupyterlab/filebrowser:Model",factory:()=>this.cd("."),frequency:{interval:s,backoff:!0,max:3e5},standby:e.refreshStandby||"when-hidden"})}get connectionFailure(){return this._connectionFailure}get driveName(){return this._driveName}get restored(){return this._restored.promise}get fileChanged(){return this._fileChanged}get path(){return this._model?this._model.path:""}get rootPath(){return this._driveName?this._driveName+":":""}get pathChanged(){return this._pathChanged}get refreshed(){return this._refreshed}get specs(){return this.manager.services.kernelspecs.specs}get isDisposed(){return this._isDisposed}get uploadChanged(){return this._uploadChanged}uploads(){return this._uploads[Symbol.iterator]()}dispose(){this.isDisposed||(window.removeEventListener("beforeunload",this._unloadEventListener),this._isDisposed=!0,this._poll.dispose(),this._sessions.length=0,this._items.length=0,D.Signal.clearData(this))}items(){return this._items[Symbol.iterator]()}sessions(){return this._sessions[Symbol.iterator]()}async refresh(){await this._poll.refresh(),await this._poll.tick,this._refreshed.emit(void 0)}async cd(e="."){if(e="."!==e?this.manager.services.contents.resolvePath(this._model.path,e):this._pendingPath||this._model.path,this._pending){if(e===this._pendingPath)return this._pending;await this._pending}const t=this.path;this._pendingPath=e,t!==e&&(this._sessions.length=0);const s=this.manager.services;return this._pending=s.contents.get(e,{content:!0}).then((i=>{this.isDisposed||(this.handleContents(i),this._pendingPath=null,this._pending=null,t!==e&&(this._state&&this._key&&this._state.save(this._key,{path:e}),this._pathChanged.emit({name:"path",oldValue:t,newValue:e})),this.onRunningChanged(s.sessions,s.sessions.running()),this._refreshed.emit(void 0))})).catch((t=>{if(this._pendingPath=null,this._pending=null,t.response&&404===t.response.status&&"/"!==e)return t.message=this._trans.__('Directory not found: "%1"',this._model.path),console.error(t),this._connectionFailure.emit(t),this.cd("/");this._connectionFailure.emit(t)})),this._pending}async download(e){const t=await this.manager.services.contents.getDownloadUrl(e),s=document.createElement("a");s.href=t,s.download="",document.body.appendChild(s),s.click(),document.body.removeChild(s)}async restore(e,t=!0){const{manager:s}=this,i=`file-browser-${e}:cwd`,r=this._state;if(!this._key)if(this._key=i,t&&r){await s.services.ready;try{const e=await r.fetch(i);if(!e)return void this._restored.resolve(void 0);const t=e.path;t&&await this.cd("/");const n=s.services.contents.localPath(t);await s.services.contents.get(t),await this.cd(n)}catch(e){await r.remove(i)}this._restored.resolve(void 0)}else this._restored.resolve(void 0)}async upload(e){const t=r.PageConfig.getNotebookVersion(),s=t<[4,0,0]||t>=[5,1,0],i=e.size>G;if(i&&!s){const t=this._trans.__("Cannot upload file (>%1 MB). %2",G/1048576,e.name);throw console.warn(t),t}if(i&&!await this._shouldUploadLarge(e))throw"Cancelled large file upload";if(await this._uploadCheckDisposed(),await this.refresh(),await this._uploadCheckDisposed(),this._items.find((t=>t.name===e.name))&&!await(0,h.shouldOverwrite)(e.name))throw"File not uploaded";await this._uploadCheckDisposed();const n=s&&e.size>Z;return await this._upload(e,n)}async _shouldUploadLarge(e){const{button:t}=await(0,i.showDialog)({title:this._trans.__("Large file size warning"),body:this._trans.__("The file size is %1 MB. Do you still want to upload it?",Math.round(e.size/1048576)),buttons:[i.Dialog.cancelButton({label:this._trans.__("Cancel")}),i.Dialog.warnButton({label:this._trans.__("Upload")})]});return t.accept}async _upload(e,t){let s=this._model.path;s=s?s+"/"+e.name:e.name;const i=e.name,r=async(t,r)=>{await this._uploadCheckDisposed();const n=new FileReader;n.readAsDataURL(t),await new Promise(((t,s)=>{n.onload=t,n.onerror=t=>s(`Failed to upload "${e.name}":`+t)})),await this._uploadCheckDisposed();const a=n.result.split(",")[1],o={type:"file",format:"base64",name:i,chunk:r,content:a};return await this.manager.services.contents.save(s,o)};if(!t)try{return await r(e)}catch(t){throw d.ArrayExt.removeFirstWhere(this._uploads,(t=>e.name===t.path)),t}let n,a={path:s,progress:0};this._uploadChanged.emit({name:"start",newValue:a,oldValue:null});for(let t=0;!n;t+=Z){const i=t+Z,o=i>=e.size,l=o?-1:i/Z,h={path:s,progress:t/e.size};let c;this._uploads.splice(this._uploads.indexOf(a)),this._uploads.push(h),this._uploadChanged.emit({name:"update",newValue:h,oldValue:a}),a=h;try{c=await r(e.slice(t,i),l)}catch(t){throw d.ArrayExt.removeFirstWhere(this._uploads,(t=>e.name===t.path)),this._uploadChanged.emit({name:"failure",newValue:a,oldValue:null}),t}o&&(n=c)}return this._uploads.splice(this._uploads.indexOf(a)),this._uploadChanged.emit({name:"finish",newValue:null,oldValue:a}),n}_uploadCheckDisposed(){return this.isDisposed?Promise.reject("Filemanager disposed. File upload canceled"):Promise.resolve()}handleContents(e){this._model={name:e.name,path:e.path,type:e.type,content:void 0,writable:e.writable,created:e.created,last_modified:e.last_modified,size:e.size,mimetype:e.mimetype,format:e.format},this._items=e.content,this._paths.clear(),e.content.forEach((e=>{this._paths.add(e.path)}))}onRunningChanged(e,t){this._populateSessions(t),this._refreshed.emit(void 0)}onFileChanged(e,t){const s=this._model.path,{sessions:i}=this.manager.services,{oldValue:n,newValue:a}=t;if(n&&n.path&&r.PathExt.dirname(n.path)===s?n:a&&a.path&&r.PathExt.dirname(a.path)===s?a:void 0)return this._poll.refresh(),this._populateSessions(i.running()),void this._fileChanged.emit(t)}_populateSessions(e){this._sessions.length=0;for(const t of e)this._paths.has(t.path)&&this._sessions.push(t)}}class ee extends Q{constructor(e){super(e),this._includeHiddenFiles=e.includeHiddenFiles||!1}items(){return this._includeHiddenFiles?super.items():(0,d.filter)(super.items(),(e=>!e.name.startsWith(".")))}showHiddenFiles(e){this._includeHiddenFiles=e,this.refresh()}}class te extends ee{constructor(e){var t,s;super(e),this._filter=null!==(t=e.filter)&&void 0!==t?t:e=>({}),this._filterDirectories=null===(s=e.filterDirectories)||void 0===s||s}get filterDirectories(){return this._filterDirectories}set filterDirectories(e){this._filterDirectories=e}items(){return(0,d.filter)(super.items(),(e=>{if(this._filterDirectories||"directory"!==e.type){const t=this._filter(e);return e.indices=null==t?void 0:t.indices,!!t}return!0}))}setFilter(e){this._filter=e,this.refresh()}}var se,ie;!function(e){async function t(e){const t=e.translator||a.nullTranslator,s=t.load("jupyterlab"),r=new re(e.manager,e.filter,t,e.defaultPath),n={title:e.title,buttons:[i.Dialog.cancelButton(),i.Dialog.okButton({label:s.__("Select")})],focusNodeSelector:e.focusNodeSelector,host:e.host,renderer:e.renderer,body:r};return await r.ready,new i.Dialog(n).launch()}e.getOpenFiles=t,e.getExistingDirectory=function(e){return t({...e,filter:e=>"directory"===e.type?{}:null})}}(se||(se={}));class re extends l.Widget{constructor(e,t,s,r,n){super(),this._ready=new c.PromiseDelegate;const h=(s=null!=s?s:a.nullTranslator).load("jupyterlab");this.addClass("jp-Open-Dialog"),ie.createFilteredFileBrowser("filtered-file-browser-dialog",e,t,{},s,r,n).then((e=>{this._browser=e,(0,i.setToolbar)(this._browser,(e=>[{name:"new-folder",widget:new i.ToolbarButton({icon:o.newFolderIcon,onClick:()=>{e.createNewDirectory()},tooltip:h.__("New Folder")})},{name:"refresher",widget:new i.ToolbarButton({icon:o.refreshIcon,onClick:()=>{e.model.refresh().catch((e=>{console.error("Failed to refresh file browser in open dialog.",e)}))},tooltip:h.__("Refresh File List")})}]));const t=new l.PanelLayout;t.addWidget(this._browser),this.dispose=()=>{this.isDisposed||(this._browser.model.dispose(),super.dispose())},this.layout=t,this._ready.resolve()})).catch((e=>{console.error("Error while creating file browser in open dialog",e),this._ready.reject(void 0)}))}getValue(){const e=Array.from(this._browser.selectedItems());return 0===e.length?[{path:this._browser.model.path,name:r.PathExt.basename(this._browser.model.path),type:"directory",content:void 0,writable:!1,created:"unknown",last_modified:"unknown",mimetype:"text/plain",format:"text"}]:e}get ready(){return this._ready.promise}}!function(e){e.createFilteredFileBrowser=async(e,t,s,i={},r,n,o)=>{r=r||a.nullTranslator;const l=new te({manager:t,filter:s,translator:r,driveName:i.driveName,refreshInterval:i.refreshInterval,filterDirectories:o}),h=new $({id:e,model:l,translator:r});return n&&await h.model.cd(n),h}}(ie||(ie={}));const ne=new c.Token("@jupyterlab/filebrowser:IFileBrowserFactory","A factory object that creates file browsers.\n  Use this if you want to create your own file browser (e.g., for a custom storage backend),\n  or to interact with other file browsers that have been created by extensions."),ae=new c.Token("@jupyterlab/filebrowser:IDefaultFileBrowser","A service for the default file browser."),oe=new c.Token("@jupyterlab/filebrowser:IFileBrowserCommands","A token to ensure file browser commands are loaded.");class le extends o.ToolbarButton{constructor(e){super({icon:o.fileUploadIcon,label:e.label,onClick:()=>{this._input.click()},tooltip:he.translateToolTip(e.translator)}),this._onInputChanged=()=>{const e=Array.prototype.slice.call(this._input.files).map((e=>this.fileBrowserModel.upload(e)));Promise.all(e).catch((e=>{(0,i.showErrorMessage)(this._trans._p("showErrorMessage","Upload Error"),e)}))},this._onInputClicked=()=>{this._input.value=""},this._input=he.createUploadInput(),this.fileBrowserModel=e.model,this.translator=e.translator||a.nullTranslator,this._trans=this.translator.load("jupyterlab"),this._input.onclick=this._onInputClicked,this._input.onchange=this._onInputChanged,this.addClass("jp-id-upload")}}var he;!function(e){e.createUploadInput=function(){const e=document.createElement("input");return e.type="file",e.multiple=!0,e},e.translateToolTip=function(e){return(e=e||a.nullTranslator).load("jupyterlab").__("Upload Files")}}(he||(he={}));var de=s(34276),ce=s(78156),me=s.n(ce);function ue(e){const t=(e.translator||a.nullTranslator).load("jupyterlab");return me().createElement(de.GroupItem,{spacing:4},me().createElement(de.TextItem,{source:t.__("Uploading…")}),me().createElement(de.ProgressBar,{percentage:e.upload}))}class pe extends o.VDomRenderer{constructor(e){super(new pe.Model(e.tracker.currentWidget&&e.tracker.currentWidget.model)),this._onBrowserChange=(e,t)=>{this.model.browserModel=null===t?null:t.model},this.translator=e.translator||a.nullTranslator,this._trans=this.translator.load("jupyterlab"),this._tracker=e.tracker,this._tracker.currentChanged.connect(this._onBrowserChange)}render(){return this.model.items.length>0?this.model.items[0].complete?me().createElement(de.TextItem,{source:this._trans.__("Complete!")}):me().createElement(ue,{upload:this.model.items[0].progress,translator:this.translator}):me().createElement(ue,{upload:100,translator:this.translator})}dispose(){super.dispose(),this._tracker.currentChanged.disconnect(this._onBrowserChange)}}!function(e){class t extends o.VDomModel{constructor(e){super(),this._uploadChanged=(e,t)=>{if("start"===t.name)this._items.push({path:t.newValue.path,progress:100*t.newValue.progress,complete:!1});else if("update"===t.name){const e=d.ArrayExt.findFirstIndex(this._items,(e=>e.path===t.oldValue.path));-1!==e&&(this._items[e].progress=100*t.newValue.progress)}else if("finish"===t.name){const e=d.ArrayExt.findFirstValue(this._items,(e=>e.path===t.oldValue.path));e&&(e.complete=!0,setTimeout((()=>{d.ArrayExt.removeFirstOf(this._items,e),this.stateChanged.emit(void 0)}),2e3))}else"failure"===t.name&&d.ArrayExt.removeFirstWhere(this._items,(e=>e.path===t.newValue.path));this.stateChanged.emit(void 0)},this._items=[],this._browserModel=null,this.browserModel=e}get items(){return this._items}get browserModel(){return this._browserModel}set browserModel(e){const t=this._browserModel;t&&t.uploadChanged.disconnect(this._uploadChanged),this._browserModel=e,this._items=[],null!==this._browserModel&&this._browserModel.uploadChanged.connect(this._uploadChanged),this.stateChanged.emit(void 0)}}e.Model=t}(pe||(pe={}))}}]);
"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[6898],{66898:(t,i,e)=>{e.r(i);e.d(i,{diagram:()=>D});var a=e(23787);var n=e(34596);var s=e(27484);var r=e.n(s);var l=e(17967);var o=e(27856);var h=e.n(o);var c=function(){var t=function(t,i,e,a){for(e=e||{},a=t.length;a--;e[t[a]]=i);return e},i=[1,3],e=[1,4],a=[1,5],n=[1,6],s=[1,7],r=[1,5,13,15,17,19,20,25,27,28,29,30,31,32,33,34,37,38,40,41,42,43,44,45,46,47,48,49,50],l=[1,5,6,13,15,17,19,20,25,27,28,29,30,31,32,33,34,37,38,40,41,42,43,44,45,46,47,48,49,50],o=[32,33,34],h=[2,7],c=[1,13],d=[1,17],u=[1,18],x=[1,19],f=[1,20],g=[1,21],y=[1,22],p=[1,23],q=[1,24],T=[1,25],A=[1,26],m=[1,27],_=[1,30],b=[1,31],S=[1,32],k=[1,33],F=[1,34],P=[1,35],v=[1,36],L=[1,37],C=[1,38],z=[1,39],E=[1,40],D=[1,41],I=[1,42],B=[1,57],w=[1,58],R=[5,22,26,32,33,34,40,41,42,43,44,45,46,47,48,49,50,51];var W={trace:function t(){},yy:{},symbols_:{error:2,start:3,eol:4,SPACE:5,QUADRANT:6,document:7,line:8,statement:9,axisDetails:10,quadrantDetails:11,points:12,title:13,title_value:14,acc_title:15,acc_title_value:16,acc_descr:17,acc_descr_value:18,acc_descr_multiline_value:19,section:20,text:21,point_start:22,point_x:23,point_y:24,"X-AXIS":25,"AXIS-TEXT-DELIMITER":26,"Y-AXIS":27,QUADRANT_1:28,QUADRANT_2:29,QUADRANT_3:30,QUADRANT_4:31,NEWLINE:32,SEMI:33,EOF:34,alphaNumToken:35,textNoTagsToken:36,STR:37,MD_STR:38,alphaNum:39,PUNCTUATION:40,AMP:41,NUM:42,ALPHA:43,COMMA:44,PLUS:45,EQUALS:46,MULT:47,DOT:48,BRKT:49,UNDERSCORE:50,MINUS:51,$accept:0,$end:1},terminals_:{2:"error",5:"SPACE",6:"QUADRANT",13:"title",14:"title_value",15:"acc_title",16:"acc_title_value",17:"acc_descr",18:"acc_descr_value",19:"acc_descr_multiline_value",20:"section",22:"point_start",23:"point_x",24:"point_y",25:"X-AXIS",26:"AXIS-TEXT-DELIMITER",27:"Y-AXIS",28:"QUADRANT_1",29:"QUADRANT_2",30:"QUADRANT_3",31:"QUADRANT_4",32:"NEWLINE",33:"SEMI",34:"EOF",37:"STR",38:"MD_STR",40:"PUNCTUATION",41:"AMP",42:"NUM",43:"ALPHA",44:"COMMA",45:"PLUS",46:"EQUALS",47:"MULT",48:"DOT",49:"BRKT",50:"UNDERSCORE",51:"MINUS"},productions_:[0,[3,2],[3,2],[3,2],[7,0],[7,2],[8,2],[9,0],[9,2],[9,1],[9,1],[9,1],[9,2],[9,2],[9,2],[9,1],[9,1],[12,4],[10,4],[10,3],[10,2],[10,4],[10,3],[10,2],[11,2],[11,2],[11,2],[11,2],[4,1],[4,1],[4,1],[21,1],[21,2],[21,1],[21,1],[39,1],[39,2],[35,1],[35,1],[35,1],[35,1],[35,1],[35,1],[35,1],[35,1],[35,1],[35,1],[35,1],[36,1],[36,1],[36,1]],performAction:function t(i,e,a,n,s,r,l){var o=r.length-1;switch(s){case 12:this.$=r[o].trim();n.setDiagramTitle(this.$);break;case 13:this.$=r[o].trim();n.setAccTitle(this.$);break;case 14:case 15:this.$=r[o].trim();n.setAccDescription(this.$);break;case 16:n.addSection(r[o].substr(8));this.$=r[o].substr(8);break;case 17:n.addPoint(r[o-3],r[o-1],r[o]);break;case 18:n.setXAxisLeftText(r[o-2]);n.setXAxisRightText(r[o]);break;case 19:r[o-1].text+=" ⟶ ";n.setXAxisLeftText(r[o-1]);break;case 20:n.setXAxisLeftText(r[o]);break;case 21:n.setYAxisBottomText(r[o-2]);n.setYAxisTopText(r[o]);break;case 22:r[o-1].text+=" ⟶ ";n.setYAxisBottomText(r[o-1]);break;case 23:n.setYAxisBottomText(r[o]);break;case 24:n.setQuadrant1Text(r[o]);break;case 25:n.setQuadrant2Text(r[o]);break;case 26:n.setQuadrant3Text(r[o]);break;case 27:n.setQuadrant4Text(r[o]);break;case 31:this.$={text:r[o],type:"text"};break;case 32:this.$={text:r[o-1].text+""+r[o],type:r[o-1].type};break;case 33:this.$={text:r[o],type:"text"};break;case 34:this.$={text:r[o],type:"markdown"};break;case 35:this.$=r[o];break;case 36:this.$=r[o-1]+""+r[o];break}},table:[{3:1,4:2,5:i,6:e,32:a,33:n,34:s},{1:[3]},{3:8,4:2,5:i,6:e,32:a,33:n,34:s},{3:9,4:2,5:i,6:e,32:a,33:n,34:s},t(r,[2,4],{7:10}),t(l,[2,28]),t(l,[2,29]),t(l,[2,30]),{1:[2,1]},{1:[2,2]},t(o,h,{8:11,9:12,10:14,11:15,12:16,21:28,35:29,1:[2,3],5:c,13:d,15:u,17:x,19:f,20:g,25:y,27:p,28:q,29:T,30:A,31:m,37:_,38:b,40:S,41:k,42:F,43:P,44:v,45:L,46:C,47:z,48:E,49:D,50:I}),t(r,[2,5]),{4:43,32:a,33:n,34:s},t(o,h,{10:14,11:15,12:16,21:28,35:29,9:44,5:c,13:d,15:u,17:x,19:f,20:g,25:y,27:p,28:q,29:T,30:A,31:m,37:_,38:b,40:S,41:k,42:F,43:P,44:v,45:L,46:C,47:z,48:E,49:D,50:I}),t(o,[2,9]),t(o,[2,10]),t(o,[2,11]),{14:[1,45]},{16:[1,46]},{18:[1,47]},t(o,[2,15]),t(o,[2,16]),{21:48,35:29,37:_,38:b,40:S,41:k,42:F,43:P,44:v,45:L,46:C,47:z,48:E,49:D,50:I},{21:49,35:29,37:_,38:b,40:S,41:k,42:F,43:P,44:v,45:L,46:C,47:z,48:E,49:D,50:I},{21:50,35:29,37:_,38:b,40:S,41:k,42:F,43:P,44:v,45:L,46:C,47:z,48:E,49:D,50:I},{21:51,35:29,37:_,38:b,40:S,41:k,42:F,43:P,44:v,45:L,46:C,47:z,48:E,49:D,50:I},{21:52,35:29,37:_,38:b,40:S,41:k,42:F,43:P,44:v,45:L,46:C,47:z,48:E,49:D,50:I},{21:53,35:29,37:_,38:b,40:S,41:k,42:F,43:P,44:v,45:L,46:C,47:z,48:E,49:D,50:I},{5:B,22:[1,54],35:56,36:55,40:S,41:k,42:F,43:P,44:v,45:L,46:C,47:z,48:E,49:D,50:I,51:w},t(R,[2,31]),t(R,[2,33]),t(R,[2,34]),t(R,[2,37]),t(R,[2,38]),t(R,[2,39]),t(R,[2,40]),t(R,[2,41]),t(R,[2,42]),t(R,[2,43]),t(R,[2,44]),t(R,[2,45]),t(R,[2,46]),t(R,[2,47]),t(r,[2,6]),t(o,[2,8]),t(o,[2,12]),t(o,[2,13]),t(o,[2,14]),t(o,[2,20],{36:55,35:56,5:B,26:[1,59],40:S,41:k,42:F,43:P,44:v,45:L,46:C,47:z,48:E,49:D,50:I,51:w}),t(o,[2,23],{36:55,35:56,5:B,26:[1,60],40:S,41:k,42:F,43:P,44:v,45:L,46:C,47:z,48:E,49:D,50:I,51:w}),t(o,[2,24],{36:55,35:56,5:B,40:S,41:k,42:F,43:P,44:v,45:L,46:C,47:z,48:E,49:D,50:I,51:w}),t(o,[2,25],{36:55,35:56,5:B,40:S,41:k,42:F,43:P,44:v,45:L,46:C,47:z,48:E,49:D,50:I,51:w}),t(o,[2,26],{36:55,35:56,5:B,40:S,41:k,42:F,43:P,44:v,45:L,46:C,47:z,48:E,49:D,50:I,51:w}),t(o,[2,27],{36:55,35:56,5:B,40:S,41:k,42:F,43:P,44:v,45:L,46:C,47:z,48:E,49:D,50:I,51:w}),{23:[1,61]},t(R,[2,32]),t(R,[2,48]),t(R,[2,49]),t(R,[2,50]),t(o,[2,19],{35:29,21:62,37:_,38:b,40:S,41:k,42:F,43:P,44:v,45:L,46:C,47:z,48:E,49:D,50:I}),t(o,[2,22],{35:29,21:63,37:_,38:b,40:S,41:k,42:F,43:P,44:v,45:L,46:C,47:z,48:E,49:D,50:I}),{24:[1,64]},t(o,[2,18],{36:55,35:56,5:B,40:S,41:k,42:F,43:P,44:v,45:L,46:C,47:z,48:E,49:D,50:I,51:w}),t(o,[2,21],{36:55,35:56,5:B,40:S,41:k,42:F,43:P,44:v,45:L,46:C,47:z,48:E,49:D,50:I,51:w}),t(o,[2,17])],defaultActions:{8:[2,1],9:[2,2]},parseError:function t(i,e){if(e.recoverable){this.trace(i)}else{var a=new Error(i);a.hash=e;throw a}},parse:function t(i){var e=this,a=[0],n=[],s=[null],r=[],l=this.table,o="",h=0,c=0,d=2,u=1;var x=r.slice.call(arguments,1);var f=Object.create(this.lexer);var g={yy:{}};for(var y in this.yy){if(Object.prototype.hasOwnProperty.call(this.yy,y)){g.yy[y]=this.yy[y]}}f.setInput(i,g.yy);g.yy.lexer=f;g.yy.parser=this;if(typeof f.yylloc=="undefined"){f.yylloc={}}var p=f.yylloc;r.push(p);var q=f.options&&f.options.ranges;if(typeof g.yy.parseError==="function"){this.parseError=g.yy.parseError}else{this.parseError=Object.getPrototypeOf(this).parseError}function T(){var t;t=n.pop()||f.lex()||u;if(typeof t!=="number"){if(t instanceof Array){n=t;t=n.pop()}t=e.symbols_[t]||t}return t}var A,m,_,b,S={},k,F,P,v;while(true){m=a[a.length-1];if(this.defaultActions[m]){_=this.defaultActions[m]}else{if(A===null||typeof A=="undefined"){A=T()}_=l[m]&&l[m][A]}if(typeof _==="undefined"||!_.length||!_[0]){var L="";v=[];for(k in l[m]){if(this.terminals_[k]&&k>d){v.push("'"+this.terminals_[k]+"'")}}if(f.showPosition){L="Parse error on line "+(h+1)+":\n"+f.showPosition()+"\nExpecting "+v.join(", ")+", got '"+(this.terminals_[A]||A)+"'"}else{L="Parse error on line "+(h+1)+": Unexpected "+(A==u?"end of input":"'"+(this.terminals_[A]||A)+"'")}this.parseError(L,{text:f.match,token:this.terminals_[A]||A,line:f.yylineno,loc:p,expected:v})}if(_[0]instanceof Array&&_.length>1){throw new Error("Parse Error: multiple actions possible at state: "+m+", token: "+A)}switch(_[0]){case 1:a.push(A);s.push(f.yytext);r.push(f.yylloc);a.push(_[1]);A=null;{c=f.yyleng;o=f.yytext;h=f.yylineno;p=f.yylloc}break;case 2:F=this.productions_[_[1]][1];S.$=s[s.length-F];S._$={first_line:r[r.length-(F||1)].first_line,last_line:r[r.length-1].last_line,first_column:r[r.length-(F||1)].first_column,last_column:r[r.length-1].last_column};if(q){S._$.range=[r[r.length-(F||1)].range[0],r[r.length-1].range[1]]}b=this.performAction.apply(S,[o,c,h,g.yy,_[1],s,r].concat(x));if(typeof b!=="undefined"){return b}if(F){a=a.slice(0,-1*F*2);s=s.slice(0,-1*F);r=r.slice(0,-1*F)}a.push(this.productions_[_[1]][0]);s.push(S.$);r.push(S._$);P=l[a[a.length-2]][a[a.length-1]];a.push(P);break;case 3:return true}}return true}};var N=function(){var t={EOF:1,parseError:function t(i,e){if(this.yy.parser){this.yy.parser.parseError(i,e)}else{throw new Error(i)}},setInput:function(t,i){this.yy=i||this.yy||{};this._input=t;this._more=this._backtrack=this.done=false;this.yylineno=this.yyleng=0;this.yytext=this.matched=this.match="";this.conditionStack=["INITIAL"];this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0};if(this.options.ranges){this.yylloc.range=[0,0]}this.offset=0;return this},input:function(){var t=this._input[0];this.yytext+=t;this.yyleng++;this.offset++;this.match+=t;this.matched+=t;var i=t.match(/(?:\r\n?|\n).*/g);if(i){this.yylineno++;this.yylloc.last_line++}else{this.yylloc.last_column++}if(this.options.ranges){this.yylloc.range[1]++}this._input=this._input.slice(1);return t},unput:function(t){var i=t.length;var e=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input;this.yytext=this.yytext.substr(0,this.yytext.length-i);this.offset-=i;var a=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1);this.matched=this.matched.substr(0,this.matched.length-1);if(e.length-1){this.yylineno-=e.length-1}var n=this.yylloc.range;this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:e?(e.length===a.length?this.yylloc.first_column:0)+a[a.length-e.length].length-e[0].length:this.yylloc.first_column-i};if(this.options.ranges){this.yylloc.range=[n[0],n[0]+this.yyleng-i]}this.yyleng=this.yytext.length;return this},more:function(){this._more=true;return this},reject:function(){if(this.options.backtrack_lexer){this._backtrack=true}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}return this},less:function(t){this.unput(this.match.slice(t))},pastInput:function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var t=this.match;if(t.length<20){t+=this._input.substr(0,20-t.length)}return(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var t=this.pastInput();var i=new Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+i+"^"},test_match:function(t,i){var e,a,n;if(this.options.backtrack_lexer){n={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done};if(this.options.ranges){n.yylloc.range=this.yylloc.range.slice(0)}}a=t[0].match(/(?:\r\n?|\n).*/g);if(a){this.yylineno+=a.length}this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:a?a[a.length-1].length-a[a.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length};this.yytext+=t[0];this.match+=t[0];this.matches=t;this.yyleng=this.yytext.length;if(this.options.ranges){this.yylloc.range=[this.offset,this.offset+=this.yyleng]}this._more=false;this._backtrack=false;this._input=this._input.slice(t[0].length);this.matched+=t[0];e=this.performAction.call(this,this.yy,this,i,this.conditionStack[this.conditionStack.length-1]);if(this.done&&this._input){this.done=false}if(e){return e}else if(this._backtrack){for(var s in n){this[s]=n[s]}return false}return false},next:function(){if(this.done){return this.EOF}if(!this._input){this.done=true}var t,i,e,a;if(!this._more){this.yytext="";this.match=""}var n=this._currentRules();for(var s=0;s<n.length;s++){e=this._input.match(this.rules[n[s]]);if(e&&(!i||e[0].length>i[0].length)){i=e;a=s;if(this.options.backtrack_lexer){t=this.test_match(e,n[s]);if(t!==false){return t}else if(this._backtrack){i=false;continue}else{return false}}else if(!this.options.flex){break}}}if(i){t=this.test_match(i,n[a]);if(t!==false){return t}return false}if(this._input===""){return this.EOF}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}},lex:function t(){var i=this.next();if(i){return i}else{return this.lex()}},begin:function t(i){this.conditionStack.push(i)},popState:function t(){var i=this.conditionStack.length-1;if(i>0){return this.conditionStack.pop()}else{return this.conditionStack[0]}},_currentRules:function t(){if(this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules}else{return this.conditions["INITIAL"].rules}},topState:function t(i){i=this.conditionStack.length-1-Math.abs(i||0);if(i>=0){return this.conditionStack[i]}else{return"INITIAL"}},pushState:function t(i){this.begin(i)},stateStackSize:function t(){return this.conditionStack.length},options:{"case-insensitive":true},performAction:function t(i,e,a,n){switch(a){case 0:break;case 1:break;case 2:return 32;case 3:break;case 4:this.begin("title");return 13;case 5:this.popState();return"title_value";case 6:this.begin("acc_title");return 15;case 7:this.popState();return"acc_title_value";case 8:this.begin("acc_descr");return 17;case 9:this.popState();return"acc_descr_value";case 10:this.begin("acc_descr_multiline");break;case 11:this.popState();break;case 12:return"acc_descr_multiline_value";case 13:return 25;case 14:return 27;case 15:return 26;case 16:return 28;case 17:return 29;case 18:return 30;case 19:return 31;case 20:this.begin("md_string");break;case 21:return"MD_STR";case 22:this.popState();break;case 23:this.begin("string");break;case 24:this.popState();break;case 25:return"STR";case 26:this.begin("point_start");return 22;case 27:this.begin("point_x");return 23;case 28:this.popState();break;case 29:this.popState();this.begin("point_y");break;case 30:this.popState();return 24;case 31:return 6;case 32:return 43;case 33:return"COLON";case 34:return 45;case 35:return 44;case 36:return 46;case 37:return 46;case 38:return 47;case 39:return 49;case 40:return 50;case 41:return 48;case 42:return 41;case 43:return 51;case 44:return 42;case 45:return 5;case 46:return 33;case 47:return 40;case 48:return 34}},rules:[/^(?:%%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[\n\r]+)/i,/^(?:%%[^\n]*)/i,/^(?:title\b)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?: *x-axis *)/i,/^(?: *y-axis *)/i,/^(?: *--+> *)/i,/^(?: *quadrant-1 *)/i,/^(?: *quadrant-2 *)/i,/^(?: *quadrant-3 *)/i,/^(?: *quadrant-4 *)/i,/^(?:["][`])/i,/^(?:[^`"]+)/i,/^(?:[`]["])/i,/^(?:["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:\s*:\s*\[\s*)/i,/^(?:(1)|(0(.\d+)?))/i,/^(?:\s*\] *)/i,/^(?:\s*,\s*)/i,/^(?:(1)|(0(.\d+)?))/i,/^(?: *quadrantChart *)/i,/^(?:[A-Za-z]+)/i,/^(?::)/i,/^(?:\+)/i,/^(?:,)/i,/^(?:=)/i,/^(?:=)/i,/^(?:\*)/i,/^(?:#)/i,/^(?:[\_])/i,/^(?:\.)/i,/^(?:&)/i,/^(?:-)/i,/^(?:[0-9]+)/i,/^(?:\s)/i,/^(?:;)/i,/^(?:[!"#$%&'*+,-.`?\\_/])/i,/^(?:$)/i],conditions:{point_y:{rules:[30],inclusive:false},point_x:{rules:[29],inclusive:false},point_start:{rules:[27,28],inclusive:false},acc_descr_multiline:{rules:[11,12],inclusive:false},acc_descr:{rules:[9],inclusive:false},acc_title:{rules:[7],inclusive:false},title:{rules:[5],inclusive:false},md_string:{rules:[21,22],inclusive:false},string:{rules:[24,25],inclusive:false},INITIAL:{rules:[0,1,2,3,4,6,8,10,13,14,15,16,17,18,19,20,23,26,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48],inclusive:true}}};return t}();W.lexer=N;function U(){this.yy={}}U.prototype=W;W.Parser=U;return new U}();c.parser=c;const d=c;const u=(0,a.D)();class x{constructor(){this.config=this.getDefaultConfig();this.themeConfig=this.getDefaultThemeConfig();this.data=this.getDefaultData()}getDefaultData(){return{titleText:"",quadrant1Text:"",quadrant2Text:"",quadrant3Text:"",quadrant4Text:"",xAxisLeftText:"",xAxisRightText:"",yAxisBottomText:"",yAxisTopText:"",points:[]}}getDefaultConfig(){var t,i,e,n,s,r,l,o,h,c,d,u,x,f,g,y,p,q;return{showXAxis:true,showYAxis:true,showTitle:true,chartHeight:((t=a.A.quadrantChart)==null?void 0:t.chartWidth)||500,chartWidth:((i=a.A.quadrantChart)==null?void 0:i.chartHeight)||500,titlePadding:((e=a.A.quadrantChart)==null?void 0:e.titlePadding)||10,titleFontSize:((n=a.A.quadrantChart)==null?void 0:n.titleFontSize)||20,quadrantPadding:((s=a.A.quadrantChart)==null?void 0:s.quadrantPadding)||5,xAxisLabelPadding:((r=a.A.quadrantChart)==null?void 0:r.xAxisLabelPadding)||5,yAxisLabelPadding:((l=a.A.quadrantChart)==null?void 0:l.yAxisLabelPadding)||5,xAxisLabelFontSize:((o=a.A.quadrantChart)==null?void 0:o.xAxisLabelFontSize)||16,yAxisLabelFontSize:((h=a.A.quadrantChart)==null?void 0:h.yAxisLabelFontSize)||16,quadrantLabelFontSize:((c=a.A.quadrantChart)==null?void 0:c.quadrantLabelFontSize)||16,quadrantTextTopPadding:((d=a.A.quadrantChart)==null?void 0:d.quadrantTextTopPadding)||5,pointTextPadding:((u=a.A.quadrantChart)==null?void 0:u.pointTextPadding)||5,pointLabelFontSize:((x=a.A.quadrantChart)==null?void 0:x.pointLabelFontSize)||12,pointRadius:((f=a.A.quadrantChart)==null?void 0:f.pointRadius)||5,xAxisPosition:((g=a.A.quadrantChart)==null?void 0:g.xAxisPosition)||"top",yAxisPosition:((y=a.A.quadrantChart)==null?void 0:y.yAxisPosition)||"left",quadrantInternalBorderStrokeWidth:((p=a.A.quadrantChart)==null?void 0:p.quadrantInternalBorderStrokeWidth)||1,quadrantExternalBorderStrokeWidth:((q=a.A.quadrantChart)==null?void 0:q.quadrantExternalBorderStrokeWidth)||2}}getDefaultThemeConfig(){return{quadrant1Fill:u.quadrant1Fill,quadrant2Fill:u.quadrant2Fill,quadrant3Fill:u.quadrant3Fill,quadrant4Fill:u.quadrant4Fill,quadrant1TextFill:u.quadrant1TextFill,quadrant2TextFill:u.quadrant2TextFill,quadrant3TextFill:u.quadrant3TextFill,quadrant4TextFill:u.quadrant4TextFill,quadrantPointFill:u.quadrantPointFill,quadrantPointTextFill:u.quadrantPointTextFill,quadrantXAxisTextFill:u.quadrantXAxisTextFill,quadrantYAxisTextFill:u.quadrantYAxisTextFill,quadrantTitleFill:u.quadrantTitleFill,quadrantInternalBorderStrokeFill:u.quadrantInternalBorderStrokeFill,quadrantExternalBorderStrokeFill:u.quadrantExternalBorderStrokeFill}}clear(){this.config=this.getDefaultConfig();this.themeConfig=this.getDefaultThemeConfig();this.data=this.getDefaultData();a.l.info("clear called")}setData(t){this.data={...this.data,...t}}addPoints(t){this.data.points=[...t,...this.data.points]}setConfig(t){a.l.trace("setConfig called with: ",t);this.config={...this.config,...t}}setThemeConfig(t){a.l.trace("setThemeConfig called with: ",t);this.themeConfig={...this.themeConfig,...t}}calculateSpace(t,i,e,a){const n=this.config.xAxisLabelPadding*2+this.config.xAxisLabelFontSize;const s={top:t==="top"&&i?n:0,bottom:t==="bottom"&&i?n:0};const r=this.config.yAxisLabelPadding*2+this.config.yAxisLabelFontSize;const l={left:this.config.yAxisPosition==="left"&&e?r:0,right:this.config.yAxisPosition==="right"&&e?r:0};const o=this.config.titleFontSize+this.config.titlePadding*2;const h={top:a?o:0};const c=this.config.quadrantPadding+l.left;const d=this.config.quadrantPadding+s.top+h.top;const u=this.config.chartWidth-this.config.quadrantPadding*2-l.left-l.right;const x=this.config.chartHeight-this.config.quadrantPadding*2-s.top-s.bottom-h.top;const f=u/2;const g=x/2;const y={quadrantLeft:c,quadrantTop:d,quadrantWidth:u,quadrantHalfWidth:f,quadrantHeight:x,quadrantHalfHeight:g};return{xAxisSpace:s,yAxisSpace:l,titleSpace:h,quadrantSpace:y}}getAxisLabels(t,i,e,a){const{quadrantSpace:n,titleSpace:s}=a;const{quadrantHalfHeight:r,quadrantHeight:l,quadrantLeft:o,quadrantHalfWidth:h,quadrantTop:c,quadrantWidth:d}=n;const u=Boolean(this.data.xAxisRightText);const x=Boolean(this.data.yAxisTopText);const f=[];if(this.data.xAxisLeftText&&i){f.push({text:this.data.xAxisLeftText,fill:this.themeConfig.quadrantXAxisTextFill,x:o+(u?h/2:0),y:t==="top"?this.config.xAxisLabelPadding+s.top:this.config.xAxisLabelPadding+c+l+this.config.quadrantPadding,fontSize:this.config.xAxisLabelFontSize,verticalPos:u?"center":"left",horizontalPos:"top",rotation:0})}if(this.data.xAxisRightText&&i){f.push({text:this.data.xAxisRightText,fill:this.themeConfig.quadrantXAxisTextFill,x:o+h+(u?h/2:0),y:t==="top"?this.config.xAxisLabelPadding+s.top:this.config.xAxisLabelPadding+c+l+this.config.quadrantPadding,fontSize:this.config.xAxisLabelFontSize,verticalPos:u?"center":"left",horizontalPos:"top",rotation:0})}if(this.data.yAxisBottomText&&e){f.push({text:this.data.yAxisBottomText,fill:this.themeConfig.quadrantYAxisTextFill,x:this.config.yAxisPosition==="left"?this.config.yAxisLabelPadding:this.config.yAxisLabelPadding+o+d+this.config.quadrantPadding,y:c+l-(x?r/2:0),fontSize:this.config.yAxisLabelFontSize,verticalPos:x?"center":"left",horizontalPos:"top",rotation:-90})}if(this.data.yAxisTopText&&e){f.push({text:this.data.yAxisTopText,fill:this.themeConfig.quadrantYAxisTextFill,x:this.config.yAxisPosition==="left"?this.config.yAxisLabelPadding:this.config.yAxisLabelPadding+o+d+this.config.quadrantPadding,y:c+r-(x?r/2:0),fontSize:this.config.yAxisLabelFontSize,verticalPos:x?"center":"left",horizontalPos:"top",rotation:-90})}return f}getQuadrants(t){const{quadrantSpace:i}=t;const{quadrantHalfHeight:e,quadrantLeft:a,quadrantHalfWidth:n,quadrantTop:s}=i;const r=[{text:{text:this.data.quadrant1Text,fill:this.themeConfig.quadrant1TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:a+n,y:s,width:n,height:e,fill:this.themeConfig.quadrant1Fill},{text:{text:this.data.quadrant2Text,fill:this.themeConfig.quadrant2TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:a,y:s,width:n,height:e,fill:this.themeConfig.quadrant2Fill},{text:{text:this.data.quadrant3Text,fill:this.themeConfig.quadrant3TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:a,y:s+e,width:n,height:e,fill:this.themeConfig.quadrant3Fill},{text:{text:this.data.quadrant4Text,fill:this.themeConfig.quadrant4TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:a+n,y:s+e,width:n,height:e,fill:this.themeConfig.quadrant4Fill}];for(const l of r){l.text.x=l.x+l.width/2;if(this.data.points.length===0){l.text.y=l.y+l.height/2;l.text.horizontalPos="middle"}else{l.text.y=l.y+this.config.quadrantTextTopPadding;l.text.horizontalPos="top"}}return r}getQuadrantPoints(t){const{quadrantSpace:i}=t;const{quadrantHeight:e,quadrantLeft:a,quadrantTop:s,quadrantWidth:r}=i;const l=(0,n.BYU)().domain([0,1]).range([a,r+a]);const o=(0,n.BYU)().domain([0,1]).range([e+s,s]);const h=this.data.points.map((t=>{const i={x:l(t.x),y:o(t.y),fill:this.themeConfig.quadrantPointFill,radius:this.config.pointRadius,text:{text:t.text,fill:this.themeConfig.quadrantPointTextFill,x:l(t.x),y:o(t.y)+this.config.pointTextPadding,verticalPos:"center",horizontalPos:"top",fontSize:this.config.pointLabelFontSize,rotation:0}};return i}));return h}getBorders(t){const i=this.config.quadrantExternalBorderStrokeWidth/2;const{quadrantSpace:e}=t;const{quadrantHalfHeight:a,quadrantHeight:n,quadrantLeft:s,quadrantHalfWidth:r,quadrantTop:l,quadrantWidth:o}=e;const h=[{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:s-i,y1:l,x2:s+o+i,y2:l},{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:s+o,y1:l+i,x2:s+o,y2:l+n-i},{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:s-i,y1:l+n,x2:s+o+i,y2:l+n},{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:s,y1:l+i,x2:s,y2:l+n-i},{strokeFill:this.themeConfig.quadrantInternalBorderStrokeFill,strokeWidth:this.config.quadrantInternalBorderStrokeWidth,x1:s+r,y1:l+i,x2:s+r,y2:l+n-i},{strokeFill:this.themeConfig.quadrantInternalBorderStrokeFill,strokeWidth:this.config.quadrantInternalBorderStrokeWidth,x1:s+i,y1:l+a,x2:s+o-i,y2:l+a}];return h}getTitle(t){if(t){return{text:this.data.titleText,fill:this.themeConfig.quadrantTitleFill,fontSize:this.config.titleFontSize,horizontalPos:"top",verticalPos:"center",rotation:0,y:this.config.titlePadding,x:this.config.chartWidth/2}}return}build(){const t=this.config.showXAxis&&!!(this.data.xAxisLeftText||this.data.xAxisRightText);const i=this.config.showYAxis&&!!(this.data.yAxisTopText||this.data.yAxisBottomText);const e=this.config.showTitle&&!!this.data.titleText;const a=this.data.points.length>0?"bottom":this.config.xAxisPosition;const n=this.calculateSpace(a,t,i,e);return{points:this.getQuadrantPoints(n),quadrants:this.getQuadrants(n),axisLabels:this.getAxisLabels(a,t,i,n),borderLines:this.getBorders(n),title:this.getTitle(e)}}}const f=(0,a.c)();function g(t){return(0,a.d)(t.trim(),f)}const y=new x;function p(t){y.setData({quadrant1Text:g(t.text)})}function q(t){y.setData({quadrant2Text:g(t.text)})}function T(t){y.setData({quadrant3Text:g(t.text)})}function A(t){y.setData({quadrant4Text:g(t.text)})}function m(t){y.setData({xAxisLeftText:g(t.text)})}function _(t){y.setData({xAxisRightText:g(t.text)})}function b(t){y.setData({yAxisTopText:g(t.text)})}function S(t){y.setData({yAxisBottomText:g(t.text)})}function k(t,i,e){y.addPoints([{x:i,y:e,text:g(t.text)}])}function F(t){y.setConfig({chartWidth:t})}function P(t){y.setConfig({chartHeight:t})}function v(){const t=(0,a.c)();const{themeVariables:i,quadrantChart:e}=t;if(e){y.setConfig(e)}y.setThemeConfig({quadrant1Fill:i.quadrant1Fill,quadrant2Fill:i.quadrant2Fill,quadrant3Fill:i.quadrant3Fill,quadrant4Fill:i.quadrant4Fill,quadrant1TextFill:i.quadrant1TextFill,quadrant2TextFill:i.quadrant2TextFill,quadrant3TextFill:i.quadrant3TextFill,quadrant4TextFill:i.quadrant4TextFill,quadrantPointFill:i.quadrantPointFill,quadrantPointTextFill:i.quadrantPointTextFill,quadrantXAxisTextFill:i.quadrantXAxisTextFill,quadrantYAxisTextFill:i.quadrantYAxisTextFill,quadrantExternalBorderStrokeFill:i.quadrantExternalBorderStrokeFill,quadrantInternalBorderStrokeFill:i.quadrantInternalBorderStrokeFill,quadrantTitleFill:i.quadrantTitleFill});y.setData({titleText:(0,a.r)()});return y.build()}const L=function(){y.clear();(0,a.t)()};const C={setWidth:F,setHeight:P,setQuadrant1Text:p,setQuadrant2Text:q,setQuadrant3Text:T,setQuadrant4Text:A,setXAxisLeftText:m,setXAxisRightText:_,setYAxisTopText:b,setYAxisBottomText:S,addPoint:k,getQuadrantData:v,clear:L,setAccTitle:a.s,getAccTitle:a.g,setDiagramTitle:a.q,getDiagramTitle:a.r,getAccDescription:a.a,setAccDescription:a.b};const z=(t,i,e,s)=>{var r,l,o;function h(t){return t==="top"?"hanging":"middle"}function c(t){return t==="left"?"start":"middle"}function d(t){return`translate(${t.x}, ${t.y}) rotate(${t.rotation||0})`}const u=(0,a.c)();a.l.debug("Rendering quadrant chart\n"+t);const x=u.securityLevel;let f;if(x==="sandbox"){f=(0,n.Ys)("#i"+i)}const g=x==="sandbox"?(0,n.Ys)(f.nodes()[0].contentDocument.body):(0,n.Ys)("body");const y=g.select(`[id="${i}"]`);const p=y.append("g").attr("class","main");const q=((r=u.quadrantChart)==null?void 0:r.chartWidth)||500;const T=((l=u.quadrantChart)==null?void 0:l.chartHeight)||500;(0,a.i)(y,T,q,((o=u.quadrantChart)==null?void 0:o.useMaxWidth)||true);y.attr("viewBox","0 0 "+q+" "+T);s.db.setHeight(T);s.db.setWidth(q);const A=s.db.getQuadrantData();const m=p.append("g").attr("class","quadrants");const _=p.append("g").attr("class","border");const b=p.append("g").attr("class","data-points");const S=p.append("g").attr("class","labels");const k=p.append("g").attr("class","title");if(A.title){k.append("text").attr("x",0).attr("y",0).attr("fill",A.title.fill).attr("font-size",A.title.fontSize).attr("dominant-baseline",h(A.title.horizontalPos)).attr("text-anchor",c(A.title.verticalPos)).attr("transform",d(A.title)).text(A.title.text)}if(A.borderLines){_.selectAll("line").data(A.borderLines).enter().append("line").attr("x1",(t=>t.x1)).attr("y1",(t=>t.y1)).attr("x2",(t=>t.x2)).attr("y2",(t=>t.y2)).style("stroke",(t=>t.strokeFill)).style("stroke-width",(t=>t.strokeWidth))}const F=m.selectAll("g.quadrant").data(A.quadrants).enter().append("g").attr("class","quadrant");F.append("rect").attr("x",(t=>t.x)).attr("y",(t=>t.y)).attr("width",(t=>t.width)).attr("height",(t=>t.height)).attr("fill",(t=>t.fill));F.append("text").attr("x",0).attr("y",0).attr("fill",(t=>t.text.fill)).attr("font-size",(t=>t.text.fontSize)).attr("dominant-baseline",(t=>h(t.text.horizontalPos))).attr("text-anchor",(t=>c(t.text.verticalPos))).attr("transform",(t=>d(t.text))).text((t=>t.text.text));const P=S.selectAll("g.label").data(A.axisLabels).enter().append("g").attr("class","label");P.append("text").attr("x",0).attr("y",0).text((t=>t.text)).attr("fill",(t=>t.fill)).attr("font-size",(t=>t.fontSize)).attr("dominant-baseline",(t=>h(t.horizontalPos))).attr("text-anchor",(t=>c(t.verticalPos))).attr("transform",(t=>d(t)));const v=b.selectAll("g.data-point").data(A.points).enter().append("g").attr("class","data-point");v.append("circle").attr("cx",(t=>t.x)).attr("cy",(t=>t.y)).attr("r",(t=>t.radius)).attr("fill",(t=>t.fill));v.append("text").attr("x",0).attr("y",0).text((t=>t.text.text)).attr("fill",(t=>t.text.fill)).attr("font-size",(t=>t.text.fontSize)).attr("dominant-baseline",(t=>h(t.text.horizontalPos))).attr("text-anchor",(t=>c(t.text.verticalPos))).attr("transform",(t=>d(t.text)))};const E={draw:z};const D={parser:d,db:C,renderer:E,styles:()=>""}}}]);
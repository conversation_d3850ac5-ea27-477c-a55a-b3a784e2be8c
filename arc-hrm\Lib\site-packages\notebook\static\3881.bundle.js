"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[3881],{92871:(e,t,a)=>{a.r(t),a.d(t,{DEFAULT_CONTEXT_ITEM_RANK:()=>f,default:()=>D});var o=a(3053),i=a(12982),n=a(38639),r=a(98037),l=a(36768),s=a(66853),d=a(34276),c=a(71677),u=a(68239),p=a(33625),g=a(20998),m=a(32895),b=a(2549),h=a(31516),v=a(78156);const y={id:"@jupyterlab/application-extension:top-bar",description:"Adds a toolbar to the top area (next to the main menu bar).",autoStart:!0,requires:[l.ISettingRegistry,i.IToolbarWidgetRegistry],optional:[c.ITranslator],activate:(e,t,a,o)=>{const n=new u.Toolbar;n.id="jp-top-bar",(0,i.setToolbar)(n,(0,i.createToolbarFactory)(a,t,"TopBar",y.id,null!=o?o:c.nullTranslator),n),e.shell.add(n,"top",{rank:900})}},f=100;var _;!function(e){e.activateNextTab="application:activate-next-tab",e.activatePreviousTab="application:activate-previous-tab",e.activateNextTabBar="application:activate-next-tab-bar",e.activatePreviousTabBar="application:activate-previous-tab-bar",e.close="application:close",e.closeOtherTabs="application:close-other-tabs",e.closeRightTabs="application:close-right-tabs",e.closeAll="application:close-all",e.setMode="application:set-mode",e.showPropertyPanel="property-inspector:show-panel",e.resetLayout="application:reset-layout",e.toggleHeader="application:toggle-header",e.toggleMode="application:toggle-mode",e.toggleLeftArea="application:toggle-left-area",e.toggleRightArea="application:toggle-right-area",e.toggleSideTabBar="application:toggle-side-tabbar",e.toggleSidebarWidget="application:toggle-sidebar-widget",e.togglePresentationMode="application:toggle-presentation-mode",e.tree="router:tree",e.switchSidebar="sidebar:switch"}(_||(_={}));const x={id:"@jupyterlab/application-extension:commands",description:"Adds commands related to the shell.",autoStart:!0,requires:[c.ITranslator],optional:[o.ILabShell,i.ICommandPalette],activate:(e,t,a,i)=>{const{commands:r,shell:l}=e,s=t.load("jupyterlab"),d=s.__("Main Area");r.addCommand(o.JupyterFrontEndContextMenu.contextMenu,{label:s.__("Shift+Right Click for Browser Menu"),isEnabled:()=>!1,execute:()=>{}});const c=()=>{const t=e.contextMenuHitTest((e=>!!e.dataset.id));return t&&(0,p.find)(l.widgets("main"),(e=>e.id===t.dataset.id))||l.currentWidget},u=(e,t)=>{if("tab-area"===e.type)return e.widgets.includes(t)?e:null;if("split-area"===e.type)for(const a of e.children){const e=u(a,t);if(e)return e}return null},g=e=>{const{id:t}=e,o=(e=>{var t;const o=null==a?void 0:a.saveLayout(),i=null==o?void 0:o.mainArea;if(!i||"multiple-document"!==n.PageConfig.getOption("mode"))return null;const r=null===(t=i.dock)||void 0===t?void 0:t.main;return r?u(r,e):null})(e),i=o&&o.widgets||[],r=i.findIndex((e=>e.id===t));return r<0?[]:i.slice(r+1)};function m(e){e&&e.focus()}r.addCommand(_.close,{label:()=>s.__("Close Tab"),isEnabled:()=>{const e=c();return!!e&&e.title.closable},execute:()=>{const e=c();e&&e.close()}}),r.addCommand(_.closeOtherTabs,{label:()=>s.__("Close All Other Tabs"),isEnabled:()=>(0,p.some)(l.widgets("main"),((e,t)=>1===t)),execute:()=>{const e=c();if(!e)return;const{id:t}=e;for(const e of l.widgets("main"))e.id!==t&&e.close()}}),r.addCommand(_.closeRightTabs,{label:()=>s.__("Close Tabs to Right"),isEnabled:()=>!!c()&&g(c()).length>0,execute:()=>{const e=c();e&&g(e).forEach((e=>e.close()))}}),a&&(r.addCommand(_.activateNextTab,{label:s.__("Activate Next Tab"),execute:()=>{a.activateNextTab()}}),r.addCommand(_.activatePreviousTab,{label:s.__("Activate Previous Tab"),execute:()=>{a.activatePreviousTab()}}),r.addCommand(_.activateNextTabBar,{label:s.__("Activate Next Tab Bar"),execute:()=>{a.activateNextTabBar()}}),r.addCommand(_.activatePreviousTabBar,{label:s.__("Activate Previous Tab Bar"),execute:()=>{a.activatePreviousTabBar()}}),r.addCommand(_.closeAll,{label:s.__("Close All Tabs"),execute:()=>{a.closeAll()}}),r.addCommand(_.toggleHeader,{label:s.__("Show Header"),execute:()=>{"single-document"===a.mode&&a.toggleTopInSimpleModeVisibility()},isToggled:()=>a.isTopInSimpleModeVisible(),isVisible:()=>"single-document"===a.mode}),r.addCommand(_.toggleLeftArea,{label:s.__("Show Left Sidebar"),execute:()=>{a.leftCollapsed?a.expandLeft():(a.collapseLeft(),a.currentWidget&&a.activateById(a.currentWidget.id))},isToggled:()=>!a.leftCollapsed,isEnabled:()=>!a.isEmpty("left")}),r.addCommand(_.toggleRightArea,{label:s.__("Show Right Sidebar"),execute:()=>{a.rightCollapsed?a.expandRight():(a.collapseRight(),a.currentWidget&&a.activateById(a.currentWidget.id))},isToggled:()=>!a.rightCollapsed,isEnabled:()=>!a.isEmpty("right")}),r.addCommand(_.toggleSidebarWidget,{label:s.__("Toggle Sidebar Element"),execute:e=>{const t=parseInt(e.index,10);if("left"!=e.side&&"right"!=e.side)throw Error(`Unsupported sidebar: ${e.side}`);const o=Array.from(a.widgets(e.side));if(t>=o.length)return;const i=o[t].id,n=document.querySelector("[data-id='"+i+"']");(e=>{let t;if("left"!=e&&"right"!=e)throw Error(`Unsupported sidebar: ${e}`);if("left"===e)t=document.querySelector(".lm-TabBar-tab.lm-mod-current");else{const e=document.querySelectorAll(".lm-TabBar-tab.lm-mod-current");t=e[e.length-1]}const a=null==t?void 0:t.getAttribute("data-id");return a?null==a?void 0:a.toString():""})(e.side)===i?("left"==e.side&&(a.collapseLeft(),m(n)),"right"==e.side&&(a.collapseRight(),m(n))):(a.activateById(i),m(n))}}),r.addCommand(_.toggleSideTabBar,{label:e=>"right"===e.side?s.__("Show Right Activity Bar"):s.__("Show Left Activity Bar"),execute:e=>{"right"===e.side?a.toggleSideTabBarVisibility("right"):a.toggleSideTabBarVisibility("left")},isToggled:e=>"right"===e.side?a.isSideTabBarVisible("right"):a.isSideTabBarVisible("left"),isEnabled:e=>"right"===e.side?!a.isEmpty("right"):!a.isEmpty("left")}),r.addCommand(_.togglePresentationMode,{label:()=>s.__("Presentation Mode"),execute:()=>{a.presentationMode=!a.presentationMode},isToggled:()=>a.presentationMode,isVisible:()=>!0}),r.addCommand(_.setMode,{label:e=>e.mode?s.__("Set %1 mode.",e.mode):s.__("Set the layout `mode`."),caption:s.__('The layout `mode` can be "single-document" or "multiple-document".'),isVisible:e=>{const t=e.mode;return"single-document"===t||"multiple-document"===t},execute:e=>{const t=e.mode;if("single-document"!==t&&"multiple-document"!==t)throw new Error(`Unsupported application shell mode: ${t}`);a.mode=t}}),r.addCommand(_.toggleMode,{label:s.__("Simple Interface"),isToggled:()=>"single-document"===a.mode,execute:()=>{const e="multiple-document"===a.mode?{mode:"single-document"}:{mode:"multiple-document"};return r.execute(_.setMode,e)}}),r.addCommand(_.resetLayout,{label:s.__("Reset Default Layout"),execute:()=>{a.presentationMode&&r.execute(_.togglePresentationMode).catch((e=>{console.error("Failed to undo presentation mode.",e)})),"single-document"!==a.mode||a.isTopInSimpleModeVisible()||r.execute(_.toggleHeader).catch((e=>{console.error("Failed to display title header.",e)})),["left","right"].forEach((e=>{a.isSideTabBarVisible(e)||a.isEmpty(e)||r.execute(_.toggleSideTabBar,{side:e}).catch((t=>{console.error(`Failed to show ${e} activity bar.`,t)}))}))}})),i&&([_.activateNextTab,_.activatePreviousTab,_.activateNextTabBar,_.activatePreviousTabBar,_.close,_.closeAll,_.closeOtherTabs,_.closeRightTabs,_.toggleHeader,_.toggleLeftArea,_.toggleRightArea,_.togglePresentationMode,_.toggleMode,_.resetLayout].forEach((e=>i.addItem({command:e,category:d}))),["right","left"].forEach((e=>{i.addItem({command:_.toggleSideTabBar,category:d,args:{side:e}})})))}},S={id:"@jupyterlab/application-extension:main",description:"Initializes the application and provides the URL tree path handler.",requires:[o.IRouter,i.IWindowResolver,c.ITranslator,o.JupyterFrontEnd.ITreeResolver],optional:[o.IConnectionLost],provides:o.ITreePathUpdater,activate:(e,t,a,r,l,s)=>{const d=r.load("jupyterlab");if(!(e instanceof o.JupyterLab))throw new Error(`${S.id} must be activated in JupyterLab.`);let c="",u="";const p=a.name;if(console.debug(`Starting application in workspace: "${p}"`),0!==e.registerPluginErrors.length){const t=v.createElement("pre",null,e.registerPluginErrors.map((e=>e.message)).join("\n"));(0,i.showErrorMessage)(d.__("Error Registering Plugins"),{message:t})}e.shell.layoutModified.connect((()=>{e.commands.notifyCommandChanged()})),e.shell.modeChanged.connect(((e,a)=>{const o=n.PageConfig.getUrl({mode:a}),i=n.URLExt.parse(o).pathname;t.navigate(i,{skipRouting:!0}),n.PageConfig.setOption("mode",a)})),l.paths.then((()=>{e.shell.currentPathChanged.connect(((e,a)=>{const o=a.newValue,i=o||u,r=n.PageConfig.getUrl({treePath:i}),l=n.URLExt.parse(r).pathname;t.navigate(l,{skipRouting:!0}),n.PageConfig.setOption("treePath",i),c=o}))})),s=s||o.ConnectionLost,e.serviceManager.connectionFailure.connect(((e,t)=>s(e,t,r)));const g=e.serviceManager.builder,m=()=>g.build().then((()=>(0,i.showDialog)({title:d.__("Build Complete"),body:v.createElement("div",null,d.__("Build successfully completed, reload page?"),v.createElement("br",null),d.__("You will lose any unsaved changes.")),buttons:[i.Dialog.cancelButton({label:d.__("Reload Without Saving"),actions:["reload"]}),i.Dialog.okButton({label:d.__("Save and Reload")})],hasClose:!0}))).then((({button:{accept:a,actions:o}})=>{a?e.commands.execute("docmanager:save").then((()=>{t.reload()})).catch((e=>{(0,i.showErrorMessage)(d.__("Save Failed"),{message:v.createElement("pre",null,e.message)})})):o.includes("reload")&&t.reload()})).catch((e=>{(0,i.showErrorMessage)(d.__("Build Failed"),{message:v.createElement("pre",null,e.message)})}));return g.isAvailable&&g.shouldCheck&&g.getStatus().then((e=>{if("building"===e.status)return m();if("needed"!==e.status)return;const t=v.createElement("div",null,d.__("JupyterLab build is suggested:"),v.createElement("br",null),v.createElement("pre",null,e.message));(0,i.showDialog)({title:d.__("Build Recommended"),body:t,buttons:[i.Dialog.cancelButton(),i.Dialog.okButton({label:d.__("Build")})]}).then((e=>e.button.accept?m():void 0))})),function(e){l.paths.then((()=>{if(u=e,!c){const a=n.PageConfig.getUrl({treePath:e}),o=n.URLExt.parse(a).pathname;t.navigate(o,{skipRouting:!0}),n.PageConfig.setOption("treePath",e)}}))}},autoStart:!0},w={id:"@jupyterlab/application-extension:context-menu",description:"Populates the context menu.",autoStart:!0,requires:[l.ISettingRegistry,c.ITranslator],activate:(e,t,a)=>{const o=a.load("jupyterlab");function i(t){const a=new u.RankedMenu({...t,commands:e.commands});return t.label&&(a.title.label=o.__(t.label)),a}e.started.then((()=>O.loadSettingsContextMenu(e.contextMenu,t,i,a))).catch((e=>{console.error("Failed to load context menu items from settings registry.",e)}))}},T={id:"@jupyterlab/application-extension:dirty",description:"Adds safeguard dialog when closing the browser tab with unsaved modifications.",autoStart:!0,requires:[c.ITranslator],activate:(e,t)=>{if(!(e instanceof o.JupyterLab))throw new Error(`${T.id} must be activated in JupyterLab.`);const a=t.load("jupyterlab").__("Are you sure you want to exit JupyterLab?\n\nAny unsaved changes will be lost.");window.addEventListener("beforeunload",(t=>{if(e.status.isDirty)return t.returnValue=a}))}},I={id:"@jupyterlab/application-extension:layout",description:"Provides the shell layout restorer.",requires:[s.IStateDB,o.ILabShell,l.ISettingRegistry],optional:[c.ITranslator],activate:(e,t,a,r,l)=>{const s=(null!=l?l:c.nullTranslator).load("jupyterlab"),d=e.started,u=e.commands,p=n.PageConfig.getOption("mode"),m=new o.LayoutRestorer({connector:t,first:d,registry:u,mode:p});return r.load(R.id).then((t=>{var o,i;const n=t.composite.layout;a.restoreLayout(p,m,{"multiple-document":null!==(o=n.multiple)&&void 0!==o?o:{},"single-document":null!==(i=n.single)&&void 0!==i?i:{}}).then((()=>{a.layoutModified.connect((()=>{m.save(a.saveLayout())})),t.changed.connect(b),O.activateSidebarSwitcher(e,a,t,s)}))})).catch((e=>{console.error("Fail to load settings for the layout restorer."),console.error(e)})),m;async function b(e){g.JSONExt.deepEqual(e.composite.layout,{single:a.userLayout["single-document"],multiple:a.userLayout["multiple-document"]})||(await(0,i.showDialog)({title:s.__("Information"),body:s.__("User layout customization has changed. You may need to reload JupyterLab to see the changes."),buttons:[i.Dialog.cancelButton(),i.Dialog.okButton({label:s.__("Reload")})]})).button.accept&&location.reload()}},autoStart:!0,provides:o.ILayoutRestorer},E={id:"@jupyterlab/application-extension:router",description:"Provides the URL router",requires:[o.JupyterFrontEnd.IPaths],activate:(e,t)=>{const{commands:a}=e,i=t.urls.base,n=new o.Router({base:i,commands:a});return e.started.then((()=>{n.route(),window.addEventListener("popstate",(()=>{n.route()}))})),n},autoStart:!0,provides:o.IRouter},C={id:"@jupyterlab/application-extension:tree-resolver",description:"Provides the tree route resolver",autoStart:!0,requires:[o.IRouter],provides:o.JupyterFrontEnd.ITreeResolver,activate:(e,t)=>{const{commands:a}=e,o=new b.DisposableSet,i=new g.PromiseDelegate,r=new RegExp("/(lab|doc)(/workspaces/[a-zA-Z0-9-_]+)?(/tree/.*)?");o.add(a.addCommand(_.tree,{execute:async e=>{var t;if(o.isDisposed)return;const a=n.URLExt.queryStringToObject(null!==(t=e.search)&&void 0!==t?t:""),r=a["file-browser-path"]||"";delete a["file-browser-path"],o.dispose(),i.resolve({browser:r,file:n.PageConfig.getOption("treePath")})}})),o.add(t.register({command:_.tree,pattern:r}));const l=()=>{o.isDisposed||(o.dispose(),i.resolve(null))};return t.routed.connect(l),o.add(new b.DisposableDelegate((()=>{t.routed.disconnect(l)}))),{paths:i.promise}}},P={id:"@jupyterlab/application-extension:notfound",description:"Defines the behavior for not found URL (aka route).",requires:[o.JupyterFrontEnd.IPaths,o.IRouter,c.ITranslator],activate:(e,t,a,o)=>{const n=o.load("jupyterlab"),r=t.urls.notFound;if(!r)return;const l=a.base,s=n.__("The path: %1 was not found. JupyterLab redirected to: %2",r,l);a.navigate(""),(0,i.showErrorMessage)(n.__("Path Not Found"),{message:s})},autoStart:!0},L={id:"@jupyterlab/application-extension:faviconbusy",description:"Handles the favicon depending on the application status.",requires:[o.ILabStatus],activate:async(e,t)=>{t.busySignal.connect(((e,t)=>{const a=document.querySelector('link[rel="icon"]'+(t?".idle.favicon":".busy.favicon"));if(!a)return;const o=document.querySelector("link"+(t?".busy.favicon":".idle.favicon"));o&&a!==o&&(a.rel="",o.rel="icon",o.parentNode.replaceChild(o,o))}))},autoStart:!0},R={id:"@jupyterlab/application-extension:shell",description:"Provides the JupyterLab shell. It has an extended API compared to `app.shell`.",optional:[l.ISettingRegistry],activate:(e,t)=>{if(!(e.shell instanceof o.LabShell))throw new Error(`${R.id} did not find a LabShell instance.`);return t&&t.load(R.id).then((t=>{e.shell.updateConfig(t.composite),t.changed.connect((()=>{e.shell.updateConfig(t.composite)}))})),e.shell},autoStart:!0,provides:o.ILabShell},B={id:"@jupyterlab/application-extension:status",description:"Provides the application status.",activate:e=>{if(!(e instanceof o.JupyterLab))throw new Error(`${B.id} must be activated in JupyterLab.`);return e.status},autoStart:!0,provides:o.ILabStatus},M={id:"@jupyterlab/application-extension:info",description:"Provides the application information.",activate:e=>{if(!(e instanceof o.JupyterLab))throw new Error(`${M.id} must be activated in JupyterLab.`);return e.info},autoStart:!0,provides:o.JupyterLab.IInfo},j={id:"@jupyterlab/application-extension:paths",description:"Provides the application paths.",activate:e=>{if(!(e instanceof o.JupyterLab))throw new Error(`${j.id} must be activated in JupyterLab.`);return e.paths},autoStart:!0,provides:o.JupyterFrontEnd.IPaths},A={id:"@jupyterlab/application-extension:property-inspector",description:"Provides the property inspector.",autoStart:!0,requires:[o.ILabShell,c.ITranslator],optional:[o.ILayoutRestorer],provides:r.IPropertyInspectorProvider,activate:(e,t,a,o)=>{const i=a.load("jupyterlab"),n=new r.SideBarPropertyInspectorProvider({shell:t,translator:a});return n.title.icon=u.buildIcon,n.title.caption=i.__("Property Inspector"),n.id="jp-property-inspector",t.add(n,"right",{rank:100,type:"Property Inspector"}),e.commands.addCommand(_.showPropertyPanel,{label:i.__("Property Inspector"),execute:()=>{t.activateById(n.id)}}),o&&o.add(n,"jp-property-inspector"),n}},k={id:"@jupyterlab/application-extension:logo",description:"Sets the application logo.",autoStart:!0,requires:[o.ILabShell],activate:(e,t)=>{const a=new h.Widget;u.jupyterIcon.element({container:a.node,elementPosition:"center",margin:"2px 2px 2px 8px",height:"auto",width:"16px"}),a.id="jp-MainLogo",t.add(a,"top",{rank:0})}},J={id:"@jupyterlab/application-extension:mode-switch",description:"Adds the interface mode switch",requires:[o.ILabShell,c.ITranslator],optional:[d.IStatusBar,l.ISettingRegistry],activate:(e,t,a,o,i)=>{if(null===o)return;const n=a.load("jupyterlab"),r=new u.Switch;if(r.id="jp-single-document-mode",r.valueChanged.connect(((e,a)=>{t.mode=a.newValue?"single-document":"multiple-document"})),t.modeChanged.connect(((e,t)=>{r.value="single-document"===t})),i){const a=i.load(R.id),o=e=>{const a=e.get("startMode").composite;a&&(t.mode="single"===a?"single-document":"multiple-document")};Promise.all([a,e.restored]).then((([e])=>{o(e)})).catch((e=>{console.error(e.message)}))}const l=()=>{const t=e.commands.keyBindings.find((e=>"application:toggle-mode"===e.command));if(t){const e=t.keys.map(m.CommandRegistry.formatKeystroke).join(", ");r.caption=n.__("Simple Interface (%1)",e)}else r.caption=n.__("Simple Interface")};l(),e.commands.keyBindingChanged.connect((()=>{l()})),r.label=n.__("Simple"),o.registerStatusItem(J.id,{priority:1,item:r,align:"left",rank:-1})},autoStart:!0},D=[w,T,S,x,I,E,C,P,L,R,B,M,J,j,A,k,y];var O;!function(e){async function t(e){(await(0,i.showDialog)({title:e.__("Information"),body:e.__("Context menu customization has changed. You will need to reload JupyterLab to see the changes."),buttons:[i.Dialog.cancelButton(),i.Dialog.okButton({label:e.__("Reload")})]})).button.accept&&location.reload()}e.loadSettingsContextMenu=async function(e,a,o,n){var r;const s=n.load("jupyterlab"),d=w.id;let c=null,u={};function p(e){var t,o;u={};const i=Object.keys(a.plugins).map((e=>{var t,o;const i=null!==(o=null===(t=a.plugins[e].schema["jupyter.lab.menus"])||void 0===t?void 0:t.context)&&void 0!==o?o:[];return u[e]=i,i})).concat([null!==(o=null===(t=e["jupyter.lab.menus"])||void 0===t?void 0:t.context)&&void 0!==o?o:[]]).reduceRight(((e,t)=>l.SettingRegistry.reconcileItems(e,t,!0)),[]);e.properties.contextMenu.default=l.SettingRegistry.reconcileItems(i,e.properties.contextMenu.default,!0).sort(((e,t)=>{var a,o;return(null!==(a=e.rank)&&void 0!==a?a:1/0)-(null!==(o=t.rank)&&void 0!==o?o:1/0)}))}a.transform(d,{compose:e=>{var t,a,o,i;c||(c=g.JSONExt.deepCopy(e.schema),p(c));const n=null!==(o=null===(a=null===(t=c.properties)||void 0===t?void 0:t.contextMenu)||void 0===a?void 0:a.default)&&void 0!==o?o:[],r={...e.data.user,contextMenu:null!==(i=e.data.user.contextMenu)&&void 0!==i?i:[]},s={...e.data.composite,contextMenu:l.SettingRegistry.reconcileItems(n,r.contextMenu,!1)};return e.data={composite:s,user:r},e},fetch:e=>(c||(c=g.JSONExt.deepCopy(e.schema),p(c)),{data:e.data,id:e.id,raw:e.raw,schema:c,version:e.version})});const m=await a.load(d),b=null!==(r=m.composite.contextMenu)&&void 0!==r?r:[];l.SettingRegistry.filterDisabledItems(b).forEach((t=>{i.MenuFactory.addContextItem({rank:f,...t},e,o)})),m.changed.connect((()=>{var e;const a=null!==(e=m.composite.contextMenu)&&void 0!==e?e:[];g.JSONExt.deepEqual(b,a)||t(s)})),a.pluginChanged.connect((async(n,r)=>{var c,p,m,h;if(r!==d){const n=null!==(c=u[r])&&void 0!==c?c:[],d=null!==(m=null===(p=a.plugins[r].schema["jupyter.lab.menus"])||void 0===p?void 0:p.context)&&void 0!==m?m:[];if(!g.JSONExt.deepEqual(n,d))if(u[r])await t(s);else{u[r]=g.JSONExt.deepCopy(d);const t=null!==(h=l.SettingRegistry.reconcileItems(d,b,!1,!1))&&void 0!==h?h:[];l.SettingRegistry.filterDisabledItems(t).forEach((t=>{i.MenuFactory.addContextItem({rank:f,...t},e,o)}))}}}))},e.activateSidebarSwitcher=function(e,t,a,o){e.commands.addCommand(_.switchSidebar,{label:o.__("Switch Sidebar Side"),execute:()=>{const o=e.contextMenuHitTest((e=>!!e.dataset.id));if(!o)return;const i=o.dataset.id,n=document.getElementById("jp-left-stack"),r=document.getElementById(i);let l=null;if(n&&r&&n.contains(r)){const e=(0,p.find)(t.widgets("left"),(e=>e.id===i));e&&(l=t.move(e,"right"),t.activateById(e.id))}else{const e=(0,p.find)(t.widgets("right"),(e=>e.id===i));e&&(l=t.move(e,"left"),t.activateById(e.id))}l&&a.set("layout",{single:l["single-document"],multiple:l["multiple-document"]}).catch((e=>{console.error("Failed to save user layout customization.",e)}))}}),e.commands.commandExecuted.connect(((e,t)=>{t.id===_.resetLayout&&a.remove("layout").catch((e=>{console.error("Failed to remove user layout customization.",e)}))}))}}(O||(O={}))}}]);
"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[1835],{11835:(e,t,n)=>{n.r(t),n.d(t,{ToolbarItems:()=>F,default:()=>W,downloadPlugin:()=>D,openBrowserTabPlugin:()=>k,pathStatusPlugin:()=>x,savingStatusPlugin:()=>C});var o,a=n(3053),r=n(12982),i=n(38639),l=n(90157),d=n(36768),s=n(34276),c=n(71677),u=n(68239),m=n(33625),g=n(20998),p=n(81997),h=n(31516),v=n(78156);!function(e){e.clone="docmanager:clone",e.deleteFile="docmanager:delete-file",e.newUntitled="docmanager:new-untitled",e.open="docmanager:open",e.openBrowserTab="docmanager:open-browser-tab",e.reload="docmanager:reload",e.rename="docmanager:rename",e.del="docmanager:delete",e.duplicate="docmanager:duplicate",e.restoreCheckpoint="docmanager:restore-checkpoint",e.save="docmanager:save",e.saveAll="docmanager:save-all",e.saveAs="docmanager:save-as",e.download="docmanager:download",e.toggleAutosave="docmanager:toggle-autosave",e.showInFileBrowser="docmanager:show-in-file-browser"}(o||(o={}));const f="@jupyterlab/docmanager-extension:plugin",w={id:"@jupyterlab/docmanager-extension:opener",description:"Provides the widget opener.",autoStart:!0,provides:l.IDocumentWidgetOpener,activate:e=>{const{shell:t}=e;return new class{constructor(){this._opened=new p.Signal(this)}open(e,n){e.id||(e.id="document-manager-"+ ++S.id),e.title.dataset={type:"document-title",...e.title.dataset},e.isAttached||t.add(e,"main",n||{}),t.activateById(e.id),this._opened.emit(e)}get opened(){return this._opened}}}},_={id:"@jupyterlab/docmanager-extension:contexts",description:"Adds the handling of opened documents dirty state.",autoStart:!0,requires:[l.IDocumentManager,l.IDocumentWidgetOpener],optional:[a.ILabStatus],activate:(e,t,n,o)=>{const a=new WeakSet;n.opened.connect(((e,n)=>{const r=t.contextForWidget(n);r&&!a.has(r)&&(o&&function(e,t){let n=null;const o=(t,o)=>{"dirty"===o.name&&(!0===o.newValue?n||(n=e.setDirty()):n&&(n.dispose(),n=null))};t.ready.then((()=>{t.model.stateChanged.connect(o),t.model.dirty&&(n=e.setDirty())})),t.disposed.connect((()=>{n&&n.dispose()}))}(o,r),a.add(r))}))}},b={id:"@jupyterlab/docmanager-extension:manager",description:"Provides the document manager.",provides:l.IDocumentManager,requires:[l.IDocumentWidgetOpener],optional:[c.ITranslator,a.ILabStatus,r.ISessionContextDialogs,a.JupyterLab.IInfo],activate:(e,t,n,o,a,i)=>{var d;const{serviceManager:s,docRegistry:u}=e,m=null!=n?n:c.nullTranslator,g=null!=a?a:new r.SessionContextDialogs({translator:m}),p=e.restored.then((()=>{}));return new l.DocumentManager({registry:u,manager:s,opener:t,when:p,setBusy:null!==(d=o&&(()=>o.setBusy()))&&void 0!==d?d:void 0,sessionDialogs:g,translator:null!=m?m:c.nullTranslator,isConnectedCallback:()=>!i||i.isConnected})}},y={id:f,description:"Adds commands and settings to the document manager.",autoStart:!0,requires:[l.IDocumentManager,l.IDocumentWidgetOpener,d.ISettingRegistry],optional:[c.ITranslator,r.ICommandPalette,a.ILabShell],activate:(e,t,n,a,d,s,p)=>{const h=(d=null!=d?d:c.nullTranslator).load("jupyterlab"),v=e.docRegistry;!function(e,t,n,a,d,s,c){const g=d.load("jupyterlab"),{commands:p,shell:h}=e,v=g.__("File Operations"),w=()=>{const{currentWidget:e}=h;return!(!e||!t.contextForWidget(e))},_=()=>{var e;const{currentWidget:n}=h;if(!n)return!1;const o=t.contextForWidget(n);return!!(null===(e=null==o?void 0:o.contentsModel)||void 0===e?void 0:e.writable)},b=e=>r.Notification.warning(g.__('%1 is permissioned as read-only. Use "save as..." instead.',e),{autoClose:5e3});s&&function(e,t,n,a,i){const d=i.load("jupyterlab"),{commands:s}=e,c=()=>{var o;const a=/[Pp]ath:\s?(.*)\n?/,r=e.contextMenuHitTest((e=>{var t;return!!(null===(t=e.title)||void 0===t?void 0:t.match(a))})),i=null==r?void 0:r.title.match(a);return null!==(o=i&&t.findWidget(i[1],null))&&void 0!==o?o:n.currentWidget},u=()=>{const{currentWidget:e}=n;return!(!e||!t.contextForWidget(e))};s.addCommand(o.clone,{label:()=>d.__("New View for %1",T(c(),t)),isEnabled:u,execute:e=>{const n=c(),o=e.options||{mode:"split-right"};if(!n)return;const r=t.cloneWidget(n);r&&a.open(r,o)}}),s.addCommand(o.rename,{label:()=>{let e=T(c(),t);return e&&(e=" "+e),d.__("Rename%1…",e)},isEnabled:u,execute:()=>{if(u()){const e=t.contextForWidget(c());return(0,l.renameDialog)(t,e)}}}),s.addCommand(o.duplicate,{label:()=>d.__("Duplicate %1",T(c(),t)),isEnabled:u,execute:()=>{if(u()){const e=t.contextForWidget(c());if(!e)return;return t.duplicate(e.path)}}}),s.addCommand(o.del,{label:()=>d.__("Delete %1",T(c(),t)),isEnabled:u,execute:async()=>{if(u()){const n=t.contextForWidget(c());if(!n)return;(await(0,r.showDialog)({title:d.__("Delete"),body:d.__("Are you sure you want to delete %1",n.path),buttons:[r.Dialog.cancelButton(),r.Dialog.warnButton({label:d.__("Delete")})]})).button.accept&&await e.commands.execute("docmanager:delete-file",{path:n.path})}}}),s.addCommand(o.showInFileBrowser,{label:()=>d.__("Show in File Browser"),isEnabled:u,execute:async()=>{const e=c(),n=e&&t.contextForWidget(e);n&&(await s.execute("filebrowser:activate",{path:n.path}),await s.execute("filebrowser:go-to-path",{path:n.path}))}})}(e,t,s,n,d),p.addCommand(o.deleteFile,{label:()=>`Delete ${T(h.currentWidget,t)}`,execute:e=>{const n=void 0===e.path?"":e.path;if(!n){const e=o.deleteFile;throw new Error(`A non-empty path is required for ${e}.`)}return t.deleteFile(n)}}),p.addCommand(o.newUntitled,{execute:e=>{const n=e.error||g.__("Error"),o=void 0===e.path?"":e.path,a={type:e.type,path:o};return"file"===e.type&&(a.ext=e.ext||".txt"),t.services.contents.newUntitled(a).catch((e=>(0,r.showErrorMessage)(n,e)))},label:e=>e.label||`New ${e.type}`}),p.addCommand(o.open,{execute:e=>{const n=void 0===e.path?"":e.path,o=e.factory||void 0,a=null==e?void 0:e.kernel,r=e.options||void 0;return t.services.contents.get(n,{content:!1}).then((()=>t.openOrReveal(n,o,a,r)))},iconClass:e=>e.icon||"",label:e=>{var t;return null!==(t=e.label||e.factory)&&void 0!==t?t:g.__("Open the provided `path`.")},mnemonic:e=>e.mnemonic||-1}),p.addCommand(o.reload,{label:()=>g.__("Reload %1 from Disk",T(h.currentWidget,t)),caption:g.__("Reload contents from disk"),isEnabled:w,execute:()=>{if(!w())return;const e=t.contextForWidget(h.currentWidget),n=T(h.currentWidget,t);return e?e.model.dirty?(0,r.showDialog)({title:g.__("Reload %1 from Disk",n),body:g.__("Are you sure you want to reload the %1 from the disk?",n),buttons:[r.Dialog.cancelButton(),r.Dialog.warnButton({label:g.__("Reload")})]}).then((t=>{if(t.button.accept&&!e.isDisposed)return e.revert()})):e.isDisposed?void 0:e.revert():(0,r.showDialog)({title:g.__("Cannot Reload"),body:g.__("No context found for current widget!"),buttons:[r.Dialog.okButton()]})}}),p.addCommand(o.restoreCheckpoint,{label:()=>g.__("Revert %1 to Checkpoint…",T(h.currentWidget,t)),caption:g.__("Revert contents to previous checkpoint"),isEnabled:w,execute:()=>{if(!w())return;const e=t.contextForWidget(h.currentWidget);return e?e.listCheckpoints().then((async n=>{const o=T(h.currentWidget,t);if(n.length<1)return void await(0,r.showErrorMessage)(g.__("No checkpoints"),g.__("No checkpoints are available for this %1.",o));const a=1===n.length?n[0]:await S.getTargetCheckpoint(n.reverse(),g);return a?(0,r.showDialog)({title:g.__("Revert %1 to checkpoint",o),body:new I(a,g,o),buttons:[r.Dialog.cancelButton(),r.Dialog.warnButton({label:g.__("Revert"),ariaLabel:g.__("Revert to Checkpoint")})]}).then((t=>{if(!e.isDisposed)return t.button.accept?e.model.readOnly?e.revert():e.restoreCheckpoint(a.id).then((()=>e.revert())):void 0})):void 0})):(0,r.showDialog)({title:g.__("Cannot Revert"),body:g.__("No context found for current widget!"),buttons:[r.Dialog.okButton()]})}});const y=new WeakSet;p.addCommand(o.save,{label:()=>g.__("Save %1",T(h.currentWidget,t)),caption:()=>{if(h.currentWidget){const e=t.contextForWidget(h.currentWidget);if(null==e?void 0:e.model.collaborative)return g.__("In collaborative mode, the document is saved automatically after every change");if(!_())return g.__('document is permissioned readonly; "save" is disabled, use "save as..." instead')}return g.__("Save and create checkpoint")},icon:e=>e.toolbar?u.saveIcon:void 0,isEnabled:e=>e._luminoEvent&&"keybinding"===e._luminoEvent.type||_(),execute:async e=>{var n,o,l,d,s;const c=h.currentWidget,u=t.contextForWidget(c);if(w()){if(!u)return(0,r.showDialog)({title:g.__("Cannot Save"),body:g.__("No context found for current widget!"),buttons:[r.Dialog.okButton()]});{if(y.has(u))return;if(!(null===(n=u.contentsModel)||void 0===n?void 0:n.writable)&&!u.model.collaborative){let t=null===(o=e._luminoEvent)||void 0===o?void 0:o.type;return e._luminoEvent&&"keybinding"===t?void b(u.path):(0,r.showDialog)({title:g.__("Cannot Save"),body:g.__("Document is read-only"),buttons:[r.Dialog.okButton()]})}y.add(u);const m=i.PathExt.basename(null!==(d=null===(l=u.contentsModel)||void 0===l?void 0:l.path)&&void 0!==d?d:"");let p=m;if(t.renameUntitledFileOnSave&&!0===c.isUntitled){const e=await r.InputDialog.getText({title:g.__("Rename file"),okLabel:g.__("Rename"),placeholder:g.__("File name"),text:m,selectionRange:m.length-i.PathExt.extname(m).length,checkbox:{label:g.__("Do not ask me again."),caption:g.__("If checked, you will not be asked to rename future untitled files when saving them.")}});if(e.button.accept&&(p=null!==(s=e.value)&&void 0!==s?s:m,c.isUntitled=!1,"boolean"==typeof e.isChecked)){const t=(await a.get(f,"renameUntitledFileOnSave")).composite;e.isChecked===t&&a.set(f,"renameUntitledFileOnSave",!e.isChecked).catch((e=>{console.error(`Fail to set 'renameUntitledFileOnSave:\n${e}`)}))}}try{if(await u.save(),!(null==c?void 0:c.isDisposed))return u.createCheckpoint()}catch(e){if("ModalCancelError"===e.name)return;throw e}finally{y.delete(u),p!==m&&await u.rename(p)}}}}}),p.addCommand(o.saveAll,{label:()=>g.__("Save All"),caption:g.__("Save all open documents"),isEnabled:()=>(0,m.some)(h.widgets("main"),(e=>{var n,o,a;return null!==(a=null===(o=null===(n=t.contextForWidget(e))||void 0===n?void 0:n.contentsModel)||void 0===o?void 0:o.writable)&&void 0!==a&&a})),execute:()=>{var e;const n=[],o=new Set;for(const a of h.widgets("main")){const r=t.contextForWidget(a);r&&!o.has(r.path)&&((null===(e=r.contentsModel)||void 0===e?void 0:e.writable)?(o.add(r.path),n.push(r.save())):b(r.path))}return Promise.all(n)}}),p.addCommand(o.saveAs,{label:()=>g.__("Save %1 As…",T(h.currentWidget,t)),caption:g.__("Save with new path"),isEnabled:w,execute:()=>{if(w()){const e=t.contextForWidget(h.currentWidget);if(!e)return(0,r.showDialog)({title:g.__("Cannot Save"),body:g.__("No context found for current widget!"),buttons:[r.Dialog.okButton()]});const n=(n,a)=>{"save"===a.type&&a.newValue&&a.newValue.path!==e.path&&(t.closeFile(e.path),p.execute(o.open,{path:a.newValue.path}))};t.services.contents.fileChanged.connect(n),e.saveAs().finally((()=>t.services.contents.fileChanged.disconnect(n)))}}}),p.addCommand(o.toggleAutosave,{label:g.__("Autosave Documents"),isToggled:()=>t.autosave,execute:()=>{const e=!t.autosave,n="autosave";return a.set(f,n,e).catch((e=>{console.error(`Failed to set ${f}:${n} - ${e.message}`)}))}}),c&&[o.reload,o.restoreCheckpoint,o.save,o.saveAs,o.toggleAutosave,o.duplicate].forEach((e=>{c.addItem({command:e,category:v})}))}(e,t,n,a,d,p,s);const w=n=>{const a=n.get("autosave").composite;t.autosave=!0!==a&&!1!==a||a,e.commands.notifyCommandChanged(o.toggleAutosave);const r=n.get("confirmClosingDocument").composite;t.confirmClosingDocument=null==r||r;const i=n.get("autosaveInterval").composite;t.autosaveInterval=i||120;const l=n.get("lastModifiedCheckMargin").composite;t.lastModifiedCheckMargin=l||500;const d=n.get("renameUntitledFileOnSave").composite;t.renameUntitledFileOnSave=null==d||d;const s=n.get("defaultViewers").composite,c={};Object.keys(s).forEach((e=>{v.getFileType(e)?(v.getWidgetFactory(s[e])||console.warn(`Document viewer ${s[e]} not found`),c[e]=s[e]):console.warn(`File Type ${e} not found`)}));for(const e of v.fileTypes())try{v.setDefaultWidgetFactory(e.name,c[e.name])}catch(t){console.warn(`Failed to set default viewer ${c[e.name]} for file type ${e.name}`)}};Promise.all([a.load(f),e.restored]).then((([e])=>{e.changed.connect(w),w(e),t.stateChanged.connect(((t,n)=>{["autosave","autosaveInterval","confirmClosingDocument","lastModifiedCheckMargin","renameUntitledFileOnSave"].includes(n.name)&&e.get(n.name).composite!==n.newValue&&e.set(n.name,n.newValue).catch((e=>{console.error(`Failed to set the setting '${n.name}':\n${e}`)}))}))})).catch((e=>{console.error(e.message)})),a.transform(f,{fetch:e=>{const t=Array.from(v.fileTypes()).map((e=>e.name)).join("    \n"),n=Array.from(v.widgetFactories()).map((e=>e.name)).join("    \n"),o=h.__('Overrides for the default viewers for file types.\nSpecify a mapping from file type name to document viewer name, for example:\n\ndefaultViewers: {\n  markdown: "Markdown Preview"\n}\n\nIf you specify non-existent file types or viewers, or if a viewer cannot\nopen a given file type, the override will not function.\n\nAvailable viewers:\n%1\n\nAvailable file types:\n%2',n,t),a=g.JSONExt.deepCopy(e.schema);return a.properties.defaultViewers.description=o,{...e,schema:a}}}),v.changed.connect((()=>a.load(f,!0)))}},C={id:"@jupyterlab/docmanager-extension:saving-status",description:"Adds a saving status indicator.",autoStart:!0,requires:[l.IDocumentManager,a.ILabShell],optional:[c.ITranslator,s.IStatusBar],activate:(e,t,n,o,a)=>{if(!a)return;const r=new l.SavingStatus({docManager:t,translator:null!=o?o:c.nullTranslator});r.model.widget=n.currentWidget,n.currentChanged.connect((()=>{r.model.widget=n.currentWidget})),a.registerStatusItem(C.id,{item:r,align:"middle",isActive:()=>null!==r.model&&null!==r.model.status,activeStateChanged:r.model.stateChanged})}},x={id:"@jupyterlab/docmanager-extension:path-status",description:"Adds a file path indicator in the status bar.",autoStart:!0,requires:[l.IDocumentManager,a.ILabShell],optional:[s.IStatusBar],activate:(e,t,n,o)=>{if(!o)return;const a=new l.PathStatus({docManager:t});a.model.widget=n.currentWidget,n.currentChanged.connect((()=>{a.model.widget=n.currentWidget})),o.registerStatusItem(x.id,{item:a,align:"right",rank:0})}},D={id:"@jupyterlab/docmanager-extension:download",description:"Adds command to download files.",autoStart:!0,requires:[l.IDocumentManager],optional:[c.ITranslator,r.ICommandPalette],activate:(e,t,n,a)=>{const i=(null!=n?n:c.nullTranslator).load("jupyterlab"),{commands:l,shell:d}=e,s=()=>{const{currentWidget:e}=d;return!(!e||!t.contextForWidget(e))};l.addCommand(o.download,{label:i.__("Download"),caption:i.__("Download the file to your computer"),isEnabled:s,execute:()=>{if(s()){const e=t.contextForWidget(d.currentWidget);return e?e.download():(0,r.showDialog)({title:i.__("Cannot Download"),body:i.__("No context found for current widget!"),buttons:[r.Dialog.okButton()]})}}});const u=i.__("File Operations");a&&a.addItem({command:o.download,category:u})}},k={id:"@jupyterlab/docmanager-extension:open-browser-tab",description:"Adds command to open a browser tab.",autoStart:!0,requires:[l.IDocumentManager],optional:[c.ITranslator],activate:(e,t,n)=>{const a=(null!=n?n:c.nullTranslator).load("jupyterlab"),{commands:r}=e;r.addCommand(o.openBrowserTab,{execute:e=>{const n=void 0===e.path?"":e.path;if(n)return t.services.contents.getDownloadUrl(n).then((e=>{const t=window.open();if(!t)throw new Error("Failed to open new browser tab.");t.opener=null,t.location.href=e}))},iconClass:e=>e.icon||"",label:()=>a.__("Open in New Browser Tab")})}},W=[b,y,_,x,C,D,k,w];var F,S;!function(e){e.createSaveButton=function(e,t){return(0,r.addCommandToolbarButtonClass)(r.ReactWidget.create(v.createElement(r.UseSignal,{signal:t},(()=>v.createElement(r.CommandToolbarButtonComponent,{commands:e,id:o.save,label:"",args:{toolbar:!0}})))))}}(F||(F={}));class I extends h.Widget{constructor(e,t,n="notebook"){super({node:S.createRevertConfirmNode(e,n,t)})}}function T(e,t){if(!e)return"File";const n=t.contextForWidget(e);if(!n)return"";const o=t.registry.getFileTypesForPath(n.path);return o.length&&o[0].displayName?o[0].displayName:"File"}!function(e){e.id=0,e.createRevertConfirmNode=function(e,t,n){const o=document.createElement("div"),a=document.createElement("p"),r=document.createTextNode(n.__("Are you sure you want to revert the %1 to checkpoint? ",t)),l=document.createElement("strong");l.textContent=n.__("This cannot be undone."),a.appendChild(r),a.appendChild(l);const d=document.createElement("p"),s=document.createTextNode(n.__("The checkpoint was last updated at: ")),c=document.createElement("p"),u=new Date(e.last_modified);return c.style.textAlign="center",c.textContent=i.Time.format(u)+" ("+i.Time.formatHuman(u)+")",d.appendChild(s),d.appendChild(c),o.appendChild(a),o.appendChild(d),o},e.getTargetCheckpoint=async function(e,t){const n=e.map(((e,t)=>`${t}. ${i.Time.format(e.last_modified)} (${i.Time.formatHuman(e.last_modified)})`)),o=(await r.InputDialog.getItem({items:n,title:t.__("Choose a checkpoint")})).value;if(!o)return;const a=o.split(".",1)[0];return e[parseInt(a,10)]}}(S||(S={}))}}]);
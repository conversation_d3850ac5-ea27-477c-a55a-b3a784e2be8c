"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[6815],{56815:(e,t,a)=>{a.r(t),a.d(t,{default:()=>u});var n=a(3053),o=a(12982),s=a(97104),l=a(36768),r=a(71677);const i="@jupyterlab/translation-extension:plugin",c={id:"@jupyterlab/translation:translator",description:"Provides the application translation object.",autoStart:!0,requires:[n.JupyterFrontEnd.IPaths,l.ISettingRegistry],optional:[n.ILabShell],provides:r.ITranslator,activate:async(e,t,a,n)=>{const s=await a.load(i),l=s.get("locale").composite;let c=s.get("stringsPrefix").composite;c=s.get("displayStringsPrefix").composite?c:"";const d=e.serviceManager.serverSettings,u=new r.TranslationManager(t.urls.translations,c,d);return await u.fetch(l),n&&(n.translator=u),o.Dialog.translator=u,u}},d={id:i,description:"Adds translation commands and settings.",requires:[l.ISettingRegistry,r.ITranslator],optional:[s.IMainMenu,o.ICommandPalette],autoStart:!0,activate:(e,t,a,n,s)=>{const l=a.load("jupyterlab"),{commands:c}=e;let d;function u(e){d=e.get("locale").composite}t.load(i).then((t=>{var a;u(t),document.documentElement.lang="default"!==d?(null!=d?d:"").replace("_","-"):"en-US",t.changed.connect(u);const i=n?null===(a=n.settingsMenu.items.find((e=>{var t;return"submenu"===e.type&&"jp-mainmenu-settings-language"===(null===(t=e.submenu)||void 0===t?void 0:t.id)})))||void 0===a?void 0:a.submenu:null;let g;const p=e.serviceManager.serverSettings;(0,r.requestTranslationsAPI)("","",{},p).then((e=>{for(const a in e.data){const n=e.data[a],r=n.displayName,d=n.nativeName,u=r===d,p=u?`${r}`:`${r} - ${d}`;g=`jupyterlab-translation:${a}`,c.addCommand(g,{label:p,caption:p,isEnabled:()=>!u,isVisible:()=>!0,isToggled:()=>u,execute:()=>(0,o.showDialog)({title:l.__("Change interface language?"),body:l.__("After changing the interface language to %1, you will need to reload JupyterLab to see the changes.",p),buttons:[o.Dialog.cancelButton({label:l.__("Cancel")}),o.Dialog.okButton({label:l.__("Change and reload")})]}).then((e=>{e.button.accept&&t.set("locale",a).then((()=>{window.location.reload()})).catch((e=>{console.error(e)}))}))}),i&&i.addItem({command:g,args:{}}),s&&s.addItem({category:l.__("Display Languages"),command:g})}})).catch((e=>{console.error(`Available locales errored!\n${e}`)}))})).catch((e=>{console.error(`The jupyterlab translation extension appears to be missing.\n${e}`)}))}},u=[c,d]}}]);
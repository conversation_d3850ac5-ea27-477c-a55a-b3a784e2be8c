(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[2756],{12756:function(t,e){!function(t){"use strict";t.JSONExt=void 0,function(t){function e(t){return null===t||"boolean"==typeof t||"number"==typeof t||"string"==typeof t}function n(t){return Array.isArray(t)}function r(t,i){if(t===i)return!0;if(e(t)||e(i))return!1;let o=n(t),s=n(i);return o===s&&(o&&s?function(t,e){if(t===e)return!0;if(t.length!==e.length)return!1;for(let n=0,i=t.length;n<i;++n)if(!r(t[n],e[n]))return!1;return!0}(t,i):function(t,e){if(t===e)return!0;for(let n in t)if(void 0!==t[n]&&!(n in e))return!1;for(let n in e)if(void 0!==e[n]&&!(n in t))return!1;for(let n in t){let i=t[n],o=e[n];if(void 0!==i||void 0!==o){if(void 0===i||void 0===o)return!1;if(!r(i,o))return!1}}return!0}(t,i))}function i(t){return e(t)?t:n(t)?function(t){let e=new Array(t.length);for(let n=0,r=t.length;n<r;++n)e[n]=i(t[n]);return e}(t):function(t){let e={};for(let n in t){let r=t[n];void 0!==r&&(e[n]=i(r))}return e}(t)}t.emptyObject=Object.freeze({}),t.emptyArray=Object.freeze([]),t.isPrimitive=e,t.isArray=n,t.isObject=function(t){return!e(t)&&!n(t)},t.deepEqual=r,t.deepCopy=i}(t.JSONExt||(t.JSONExt={}));function e(t){let e=0;for(let n=0,r=t.length;n<r;++n)n%4==0&&(e=4294967295*Math.random()>>>0),t[n]=255&e,e>>>=8}t.Random=void 0,(t.Random||(t.Random={})).getRandomValues=(()=>{const t="undefined"!=typeof window&&(window.crypto||window.msCrypto)||null;return t&&"function"==typeof t.getRandomValues?function(e){return t.getRandomValues(e)}:e})(),t.UUID=void 0,(t.UUID||(t.UUID={})).uuid4=function(t){const e=new Uint8Array(16),n=new Array(256);for(let t=0;t<16;++t)n[t]="0"+t.toString(16);for(let t=16;t<256;++t)n[t]=t.toString(16);return function(){return t(e),e[6]=64|15&e[6],e[8]=128|63&e[8],n[e[0]]+n[e[1]]+n[e[2]]+n[e[3]]+"-"+n[e[4]]+n[e[5]]+"-"+n[e[6]]+n[e[7]]+"-"+n[e[8]]+n[e[9]]+"-"+n[e[10]]+n[e[11]]+n[e[12]]+n[e[13]]+n[e[14]]+n[e[15]]}}(t.Random.getRandomValues),t.MimeData=class{constructor(){this._types=[],this._values=[]}types(){return this._types.slice()}hasData(t){return-1!==this._types.indexOf(t)}getData(t){let e=this._types.indexOf(t);return-1!==e?this._values[e]:void 0}setData(t,e){this.clearData(t),this._types.push(t),this._values.push(e)}clearData(t){let e=this._types.indexOf(t);-1!==e&&(this._types.splice(e,1),this._values.splice(e,1))}clear(){this._types.length=0,this._values.length=0}},t.PromiseDelegate=class{constructor(){this.promise=new Promise(((t,e)=>{this._resolve=t,this._reject=e}))}resolve(t){(0,this._resolve)(t)}reject(t){(0,this._reject)(t)}},t.Token=class{constructor(t,e){this.name=t,this.description=null!=e?e:"",this._tokenStructuralPropertyT=null}}}(e)}}]);
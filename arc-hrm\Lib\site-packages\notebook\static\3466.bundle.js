"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[3466],{83466:(e,t,r)=>{r.r(t),r.d(t,{RunningLanguageServer:()=>_,default:()=>k});var n=r(12729),a=r(13589),s=r(36768),o=r(71677),i=r(68239),c=r(81997),l=r(20998),u=r(97934),m=r(78156),d=r.n(m),p=r(12982);const g="languageServers",h="configuration";function v(e){const{[h]:t,...r}=e.schema,{[h]:n,serverName:a,...s}=e.settings,[o,i]=(0,m.useState)(a),c={};Object.entries(n).forEach((([e,t])=>{const r={property:e,type:typeof t,value:t};c[l.UUID.uuid4()]=r}));const[g,v]=(0,m.useState)(c),E={};Object.entries(r).forEach((([e,t])=>{E[e]=e in s?s[e]:t.default}));const[S,y]=(0,m.useState)(E),b=t=>{const r={};Object.entries(g).forEach((([n,a])=>{n!==t&&(r[n]=a);const s={};Object.values(r).forEach((e=>{s[e.property]=e.value})),e.updateSetting.invoke(e.serverHash,{[h]:s}).catch(console.error),v(r)}))},j=new u.Debouncer(((t,r)=>{if(t in g){const n={...g,[t]:r},a={};Object.values(n).forEach((e=>{a[e.property]=e.value})),v(n),e.updateSetting.invoke(e.serverHash,{[h]:a}).catch(console.error)}})),_=(0,m.useRef)(p.DOMUtils.createDomID()+"-line-number-input");return d().createElement("div",{className:"array-item"},d().createElement("div",{className:"form-group "},d().createElement("div",{className:"jp-FormGroup-content"},d().createElement("div",{className:"jp-objectFieldWrapper"},d().createElement("fieldset",null,d().createElement("div",{className:"form-group small-field"},d().createElement("div",{className:"jp-modifiedIndicator jp-errorIndicator"}),d().createElement("div",{className:"jp-FormGroup-content"},d().createElement("label",{htmlFor:_.current,className:"jp-FormGroup-fieldLabel jp-FormGroup-contentItem"},e.trans.__("Server name:")),d().createElement("div",{className:"jp-inputFieldWrapper jp-FormGroup-contentItem"},d().createElement("input",{id:_.current,className:"form-control",type:"text",required:!0,value:o,onChange:t=>{(t=>{e.updateSetting.invoke(e.serverHash,{serverName:t.target.value}).catch(console.error),i(t.target.value)})(t)}})),d().createElement("div",{className:"validationErrors"},d().createElement("div",null,d().createElement("ul",{className:"error-detail bs-callout bs-callout-info"},d().createElement("li",{className:"text-danger"},e.trans.__("is a required property"))))))),Object.entries(r).map((([t,r],n)=>d().createElement("div",{key:`${n}-${t}`,className:"form-group small-field"},d().createElement("div",{className:"jp-FormGroup-content"},d().createElement("h3",{className:"jp-FormGroup-fieldLabel jp-FormGroup-contentItem"},r.title),d().createElement("div",{className:"jp-inputFieldWrapper jp-FormGroup-contentItem"},d().createElement("input",{className:"form-control",placeholder:"",type:r.type,value:S[t],onChange:n=>((t,r,n)=>{let a=r;"number"===n&&(a=parseFloat(r));const s={...S,[t]:a};e.updateSetting.invoke(e.serverHash,s).catch(console.error),y(s)})(t,n.target.value,r.type)})),d().createElement("div",{className:"jp-FormGroup-description"},r.description),d().createElement("div",{className:"validationErrors"}))))),d().createElement("fieldset",null,d().createElement("legend",null,t.title),Object.entries(g).map((([e,t])=>d().createElement(f,{key:e,hash:e,property:t,removeProperty:b,setProperty:j}))),d().createElement("span",null,t.description)))))),d().createElement("div",{className:"jp-ArrayOperations"},d().createElement("button",{className:"jp-mod-styled jp-mod-reject",onClick:()=>{const t=l.UUID.uuid4(),r={...g,[t]:{property:"",type:"string",value:""}},n={};Object.values(r).forEach((e=>{n[e.property]=e.value})),e.updateSetting.invoke(e.serverHash,{[h]:n}).catch(console.error),v(r)}},e.trans.__("Add property")),d().createElement("button",{className:"jp-mod-styled jp-mod-warn jp-FormGroup-removeButton",onClick:()=>e.removeSetting(e.serverHash)},e.trans.__("Remove server"))))}function f(e){const[t,r]=(0,m.useState)({...e.property}),n=(n,a)=>{let s=n;"number"===a&&(s=parseFloat(n));const o={...t,value:s};e.setProperty.invoke(e.hash,o).catch(console.error),r(o)};return d().createElement("div",{key:e.hash,className:"form-group small-field"},d().createElement("div",{className:"jp-FormGroup-content jp-LSPExtension-FormGroup-content"},d().createElement("input",{className:"form-control",type:"text",required:!0,placeholder:"Property name",value:t.property,onChange:n=>{(n=>{const a={...t,property:n};e.setProperty.invoke(e.hash,a).catch(console.error),r(a)})(n.target.value)}}),d().createElement("select",{className:"form-control",value:t.type,onChange:n=>(n=>{let a;a="boolean"!==n&&("number"===n?0:"");const s={...t,type:n,value:a};r(s),e.setProperty.invoke(e.hash,s).catch(console.error)})(n.target.value)},d().createElement("option",{value:"string"},"String"),d().createElement("option",{value:"number"},"Number"),d().createElement("option",{value:"boolean"},"Boolean")),d().createElement("input",{className:"form-control",type:{string:"text",number:"number",boolean:"checkbox"}[t.type],required:!1,placeholder:"Property value",value:"boolean"!==t.type?t.value:void 0,checked:"boolean"===t.type?t.value:void 0,onChange:"boolean"!==t.type?e=>n(e.target.value,t.type):e=>n(e.target.checked,t.type)}),d().createElement("button",{className:"jp-mod-minimal jp-Button",onClick:()=>{e.removeProperty(e.hash)}},d().createElement(i.closeIcon.react,null))))}class E extends d().Component{constructor(e){super(e),this.removeSetting=e=>{if(e in this.state.items){const t={};for(const r in this.state.items)r!==e&&(t[r]=this.state.items[r]);this.setState((e=>({...e,items:t})),(()=>{this.saveServerSetting()}))}},this.updateSetting=(e,t)=>{if(e in this.state.items){const r={};for(const n in this.state.items)r[n]=n===e?{...this.state.items[n],...t}:this.state.items[n];this.setState((e=>({...e,items:r})),(()=>{this.saveServerSetting()}))}},this.addServerSetting=()=>{let e=0,t="newKey";for(;Object.values(this.state.items).map((e=>e.serverName)).includes(t);)e+=1,t=`newKey-${e}`;this.setState((e=>({...e,items:{...e.items,[l.UUID.uuid4()]:{...this._defaultSetting,serverName:t}}})),(()=>{this.saveServerSetting()}))},this.saveServerSetting=()=>{const e={};Object.values(this.state.items).forEach((t=>{const{serverName:r,...n}=t;e[r]=n})),this._setting.set(g,e).catch(console.error)},this._setting=e.formContext.settings,this._trans=e.translator.load("jupyterlab");const t=this._setting.schema.definitions;this._defaultSetting=t.languageServer.default,this._schema=t.languageServer.properties;const r=e.schema.title,n=e.schema.description,a=e.formContext.settings.get(g).composite;let s={};a&&Object.entries(a).forEach((([e,t])=>{if(t){const r=l.UUID.uuid4();s[r]={serverName:e,...t}}})),this.state={title:r,desc:n,items:s},this._debouncedUpdateSetting=new u.Debouncer(this.updateSetting.bind(this))}render(){return d().createElement("div",null,d().createElement("fieldset",null,d().createElement("legend",null,this.state.title),d().createElement("p",{className:"field-description"},this.state.desc),d().createElement("div",{className:"field field-array field-array-of-object"},Object.entries(this.state.items).map((([e,t],r)=>d().createElement(v,{key:`${r}-${e}`,trans:this._trans,removeSetting:this.removeSetting,updateSetting:this._debouncedUpdateSetting,serverHash:e,settings:t,schema:this._schema})))),d().createElement("div",null,d().createElement("button",{style:{margin:2},className:"jp-mod-styled jp-mod-reject",onClick:this.addServerSetting},this._trans.__("Add server")))))}}const S={activate:function(e,t,r,a){const s=new n.LanguageServerManager({settings:e.serviceManager.serverSettings}),o=new n.DocumentConnectionManager({languageServerManager:s,adapterTracker:r});return a&&function(e,t,r){const n=r.load("jupyterlab"),a=new c.Signal(t);t.connected.connect((()=>a.emit(t))),t.disconnected.connect((()=>a.emit(t))),t.closed.connect((()=>a.emit(t))),t.documentsChanged.connect((()=>a.emit(t)));let s=[];e.add({name:n.__("Language servers"),running:()=>{const e=new Set([...t.connections.values()]);return s=[...e].map((e=>new _(e,t))),s},shutdownAll:()=>{s.forEach((e=>{e.shutdown()}))},refreshRunning:()=>{},runningChanged:a,shutdownLabel:n.__("Shut Down"),shutdownAllLabel:n.__("Shut Down All"),shutdownAllConfirmationText:n.__("Are you sure you want to permanently shut down all running language servers?")})}(a,o,t),o},id:"@jupyterlab/lsp-extension:plugin",description:"Provides the language server connection manager.",requires:[o.ITranslator,n.IWidgetLSPAdapterTracker],optional:[a.IRunningSessionManagers],provides:n.ILSPDocumentConnectionManager,autoStart:!0},y={id:"@jupyterlab/lsp-extension:feature",description:"Provides the language server feature manager.",activate:()=>new n.FeatureManager,provides:n.ILSPFeatureManager,autoStart:!0},b={activate:function(e,t,r,n,a){const s="languageServers",o=t.languageServerManager,i=e=>{const r=e.composite,n=r.languageServers||{};if("on"!==r.activate||o.isEnabled){if("off"===r.activate&&o.isEnabled)return void o.disable()}else o.enable().catch(console.error);t.initialConfigurations=n,t.updateConfiguration(n),t.updateServerConfigurations(n),t.updateLogging(r.logAllCommunication,r.setTrace)};if(r.transform(S.id,{fetch:e=>{const t=e.schema.properties,r={};return o.sessions.forEach(((e,t)=>{r[t]={rank:50,configuration:{}}})),t[s].default=r,e},compose:e=>{const t=e.schema.properties,r=e.data.user,n=t[s].default,a=r[s];let o={...n};a&&(o={...o,...a});const i={[s]:o};return Object.entries(t).forEach((([e,t])=>{e!==s&&(i[e]=e in r?r[e]:t.default)})),e.data.composite=i,e}}),o.sessionsChanged.connect((async()=>{await r.load(S.id,!0)})),r.load(S.id).then((e=>{i(e),e.changed.connect((()=>{i(e)})),o.disable()})).catch((e=>{console.error(e.message)})),a){const e={fieldRenderer:e=>function(e,t){return d().createElement(E,{...e,translator:t})}(e,n)};a.addRenderer(`${S.id}.${s}`,e)}},id:"@jupyterlab/lsp-extension:settings",description:"Provides the language server settings.",requires:[n.ILSPDocumentConnectionManager,s.ISettingRegistry,o.ITranslator],optional:[i.IFormRendererRegistry],autoStart:!0},j={id:n.ILSPCodeExtractorsManager.name,description:"Provides the code extractor manager.",activate:e=>{const t=new n.CodeExtractorsManager,r=new n.TextForeignCodeExtractor({language:"markdown",isStandalone:!1,file_extension:"md",cellType:["markdown"]});t.register(r,null);const a=new n.TextForeignCodeExtractor({language:"text",isStandalone:!1,file_extension:"txt",cellType:["raw"]});return t.register(a,null),t},provides:n.ILSPCodeExtractorsManager,autoStart:!0};class _{constructor(e,t){this._connection=new WeakSet([e]),this._manager=t,this._serverIdentifier=e.serverIdentifier,this._serverLanguage=e.serverLanguage}open(){}icon(){return i.pythonIcon}label(){var e,t;return`${null!==(e=this._serverIdentifier)&&void 0!==e?e:""} (${null!==(t=this._serverLanguage)&&void 0!==t?t:""})`}shutdown(){for(const[e,t]of this._manager.connections.entries())if(this._connection.has(t)){const{uri:t}=this._manager.documents.get(e);this._manager.unregisterDocument(t)}this._manager.disconnect(this._serverIdentifier)}}const N={id:"@jupyterlab/lsp-extension:tracker",description:"Provides the tracker of `WidgetLSPAdapter`.",autoStart:!0,provides:n.IWidgetLSPAdapterTracker,activate:e=>new n.WidgetLSPAdapterTracker({shell:e.shell})},k=[S,y,b,j,N]}}]);
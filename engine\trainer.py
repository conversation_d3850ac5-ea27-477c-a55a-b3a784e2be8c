import torch
import torch.optim as optim
from tqdm import tqdm

def train_hrm(model, dataloader, epochs, tokenizer, device="cuda"):
    """Boucle d'entraînement avec deep supervision"""
    optimizer = optim.AdamW(model.parameters(), lr=3e-4)
    loss_fn = torch.nn.CrossEntropyLoss(ignore_index=tokenizer.vocab['<pad>'])
    
    model.to(device)
    model.train()
    
    for epoch in range(epochs):
        total_loss = 0
        progress = tqdm(dataloader, desc=f"Epoch {epoch+1}/{epochs}")
        
        for grids, programs in progress:
            grids = grids.to(device)
            programs = programs.to(device)
            
            optimizer.zero_grad()
            # Forward pass avec deep supervision
            segments, _ = model(grids, programs)
            
            # Calcul de la perte sur chaque segment
            loss = 0
            for seg_logits in segments:
                # Teacher forcing: on décale les tokens
                logits = seg_logits[:, :-1, :]
                targets = programs[:, 1:]
                loss += loss_fn(logits.reshape(-1, logits.size(-1)), 
                               targets.reshape(-1))
            
            # Backprop
            loss.backward()
            optimizer.step()
            total_loss += loss.item()
            progress.set_postfix(loss=loss.item())
        
        print(f"Epoch {epoch+1} | Avg Loss: {total_loss/len(dataloader):.4f}")
    
    # Sauvegarde du modèle
    torch.save(model.state_dict(), "hrm_arc_solver.pth")
(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[3512],{13512:(e,t,n)=>{"use strict";n.r(t),n.d(t,{CodeExtractorsManager:()=>W,DefaultMap:()=>D,DocumentConnectionManager:()=>B,EditorAdapter:()=>c,FeatureManager:()=>Y,ILSPCodeExtractorsManager:()=>y,ILSPDocumentConnectionManager:()=>v,ILSPFeatureManager:()=>_,ILanguageServerManager:()=>g,IWidgetLSPAdapterTracker:()=>b,LanguageServerManager:()=>Z,Method:()=>w,ProtocolCoordinates:()=>x,TextForeignCodeExtractor:()=>G,UpdateManager:()=>ne,VirtualDocument:()=>ee,VirtualDocumentInfo:()=>X,WidgetLSPAdapter:()=>d,WidgetLSPAdapterTracker:()=>f,collectDocuments:()=>te,expandDottedPaths:()=>E,expandPath:()=>T,isEqual:()=>V,isWithinRange:()=>Q,offsetAtPosition:()=>J,positionAtOffset:()=>H,sleep:()=>C,untilReady:()=>S});var i=n(95282),r=n.n(i),s=n(12982),o=n(71677),a=n(81997);class c{constructor(e){this._widgetAdapter=e.widgetAdapter,this._extensions=e.extensions,e.editor.ready().then((t=>{this._injectExtensions(e.editor)}))}dispose(){this.isDisposed||(this.isDisposed=!0,a.Signal.clearData(this))}_injectExtensions(e){const t=e.getEditor();t.isDisposed||this._extensions.forEach((n=>{const i=n.factory({path:this._widgetAdapter.widget.context.path,editor:e,widgetAdapter:this._widgetAdapter,model:t.model,inline:!0});i&&t.injectExtension(i.instance(t))}))}}const u=s.Dialog.createButton,l={"text/x-rsrc":"r","text/x-r-source":"r","text/x-ipython":"python"};class d{constructor(e,t){this.widget=e,this.options=t,this._adapterConnected=new a.Signal(this),this._activeEditorChanged=new a.Signal(this),this._editorAdded=new a.Signal(this),this._editorRemoved=new a.Signal(this),this._disposed=new a.Signal(this),this._isDisposed=!1,this._virtualDocument=null,this._connectionManager=t.connectionManager,this._isConnected=!1,this._trans=(t.translator||o.nullTranslator).load("jupyterlab"),this.widget.context.saveState.connect(this.onSaveState,this),this.connectionManager.closed.connect(this.onConnectionClosed,this),this.widget.disposed.connect(this.dispose,this),this._editorToAdapter=new WeakMap,this.editorAdded.connect(this._onEditorAdded,this),this.editorRemoved.connect(this._onEditorRemoved,this),this._connectionManager.languageServerManager.sessionsChanged.connect(this._onLspSessionOrFeatureChanged,this),this.options.featureManager.featureRegistered.connect(this._onLspSessionOrFeatureChanged,this)}get isDisposed(){return this._isDisposed}get hasMultipleEditors(){return this.editors.length>1}get widgetId(){return this.widget.id}get language(){if(l.hasOwnProperty(this.mimeType))return l[this.mimeType];{let e=this.mimeType.split(";")[0],[t,n]=e.split("/");return"application"===t||"text"===t?n.startsWith("x-")?n.substring(2):n:this.mimeType}}get adapterConnected(){return this._adapterConnected}get activeEditorChanged(){return this._activeEditorChanged}get disposed(){return this._disposed}get editorAdded(){return this._editorAdded}get editorRemoved(){return this._editorRemoved}get isConnected(){return this._isConnected}get connectionManager(){return this._connectionManager}get trans(){return this._trans}get updateFinished(){return this._updateFinished}get virtualDocument(){return this._virtualDocument}onConnectionClosed(e,{virtualDocument:t}){t===this.virtualDocument&&this.dispose()}dispose(){this._isDisposed||(this.editorAdded.disconnect(this._onEditorAdded,this),this.editorRemoved.disconnect(this._onEditorRemoved,this),this._connectionManager.languageServerManager.sessionsChanged.disconnect(this._onLspSessionOrFeatureChanged,this),this.options.featureManager.featureRegistered.disconnect(this._onLspSessionOrFeatureChanged,this),this._isDisposed=!0,this.disconnect(),this._virtualDocument=null,this._disposed.emit(),a.Signal.clearData(this))}disconnect(){var e,t;const n=null===(e=this.virtualDocument)||void 0===e?void 0:e.uri,{model:i}=this.widget.context;n&&this.connectionManager.unregisterDocument(n),i.contentChanged.disconnect(this._onContentChanged,this);for(let{ceEditor:e}of this.editors)this._editorRemoved.emit({editor:e});null===(t=this.virtualDocument)||void 0===t||t.dispose()}updateDocuments(){return this._isDisposed?(console.warn("Cannot update documents: adapter disposed"),Promise.reject("Cannot update documents: adapter disposed")):this.virtualDocument.updateManager.updateDocuments(this.editors)}documentChanged(e,t,n=!1){if(this._isDisposed)return void console.warn("Cannot swap document: adapter disposed");let i=this.connectionManager.connections.get(e.uri);(null==i?void 0:i.isReady)?i.sendFullTextChange(e.value,e.documentInfo):console.log("Skipping document update signal: connection not ready")}reloadConnection(){null!==this.virtualDocument&&(this.disconnect(),this.initVirtual(),this.connectDocument(this.virtualDocument,!0).catch(console.warn))}onSaveState(e,t){if(null!==this.virtualDocument&&"completed"===t){const e=[this.virtualDocument];for(let t of e){let n=this.connectionManager.connections.get(t.uri);if(n){n.sendSaved(t.documentInfo);for(let n of t.foreignDocuments.values())e.push(n)}}}}async onConnected(e){let{virtualDocument:t}=e;this._adapterConnected.emit(e),this._isConnected=!0;try{await this.updateDocuments()}catch(e){return void console.warn("Could not update documents",e)}this.documentChanged(t,t,!0),e.connection.serverNotifications["$/logTrace"].connect(((n,i)=>{console.log(e.connection.serverIdentifier,"trace",t.uri,i)})),e.connection.serverNotifications["window/logMessage"].connect(((e,t)=>{console.log(e.serverIdentifier+": "+t.message)})),e.connection.serverNotifications["window/showMessage"].connect(((e,t)=>{(0,s.showDialog)({title:this.trans.__("Message from ")+e.serverIdentifier,body:t.message})})),e.connection.serverRequests["window/showMessageRequest"].setHandler((async t=>{const n=t.actions,i=n?n.map((e=>u({label:e.title}))):[u({label:this.trans.__("Dismiss")})],r=await(0,s.showDialog)({title:this.trans.__("Message from ")+e.connection.serverIdentifier,body:t.message,buttons:i}),o=i.indexOf(r.button);return-1===o?null:n?n[o]:null}))}async connectDocument(e,t=!1){e.foreignDocumentOpened.connect(this.onForeignDocumentOpened,this);const n=await this._connect(e).catch(console.error);n&&n.connection&&(e.changed.connect(this.documentChanged,this),t&&n.connection.sendOpenWhenReady(e.documentInfo))}initVirtual(){var e;null===(e=this._virtualDocument)||void 0===e||e.dispose(),this._virtualDocument=this.createVirtualDocument(),this._onLspSessionOrFeatureChanged()}async onForeignDocumentOpened(e,t){const{foreignDocument:n}=t;await this.connectDocument(n,!0),n.foreignDocumentClosed.connect(this._onForeignDocumentClosed,this)}_onEditorAdded(e,t){const{editor:n}=t,i=new c({editor:n,widgetAdapter:this,extensions:this.options.featureManager.extensionFactories()});this._editorToAdapter.set(n,i)}_onEditorRemoved(e,t){const{editor:n}=t,i=this._editorToAdapter.get(n);null==i||i.dispose(),this._editorToAdapter.delete(n)}_onForeignDocumentClosed(e,t){const{foreignDocument:n}=t;n.foreignDocumentClosed.disconnect(this._onForeignDocumentClosed,this),n.foreignDocumentOpened.disconnect(this.onForeignDocumentOpened,this),n.changed.disconnect(this.documentChanged,this)}async _connect(e){let t=e.language,n={textDocument:{synchronization:{dynamicRegistration:!0,willSave:!1,didSave:!0,willSaveWaitUntil:!1}},workspace:{didChangeConfiguration:{dynamicRegistration:!0}}};n=r()(n,this.options.featureManager.clientCapabilities());let i={capabilities:n,virtualDocument:e,language:t,hasLspSupportedFile:e.hasLspSupportedFile},s=await this.connectionManager.connect(i);return s?(await this.onConnected({virtualDocument:e,connection:s}),{connection:s,virtualDocument:e}):void 0}async _onContentChanged(e){const t=this.updateDocuments();t?(this._updateFinished=t.catch(console.warn),await this.updateFinished):console.warn("Could not update documents")}_shouldUpdateVirtualDocument(){const{languageServerManager:e}=this.connectionManager;return e.isEnabled&&this.options.featureManager.features.length>0}_onLspSessionOrFeatureChanged(){if(!this._virtualDocument)return;const{model:e}=this.widget.context;this._shouldUpdateVirtualDocument()?e.contentChanged.connect(this._onContentChanged,this):e.contentChanged.disconnect(this._onContentChanged,this)}}var h=n(35312);class f{constructor(e){this._isDisposed=!1,this._current=null,this._adapters=new Set,this._adapterAdded=new a.Signal(this),this._adapterUpdated=new a.Signal(this),this._currentChanged=new a.Signal(this),(this._shell=e.shell).currentChanged.connect(((e,t)=>{let n=t.newValue;if(!(n&&n instanceof h.DocumentWidget))return;const i=this.find((e=>e.widget===n));i&&(this._current=i,this._currentChanged.emit(i))}))}get currentChanged(){return this._currentChanged}get currentAdapter(){return this._current}get size(){return this._adapters.size}get adapterAdded(){return this._adapterAdded}get adapterUpdated(){return this._adapterUpdated}add(e){if(e.isDisposed){const t="A disposed object cannot be added.";throw console.warn(t,e),new Error(t)}if(this._adapters.has(e)){const t="This object already exists in the pool.";throw console.warn(t,e),new Error(t)}this._adapters.add(e),this._adapterAdded.emit(e),e.disposed.connect((()=>{this._adapters.delete(e),e===this._current&&(this._current=null,this._currentChanged.emit(this._current))}),this);const t=this._shell.activeWidget;t&&t instanceof h.DocumentWidget||(this._current=e,this._currentChanged.emit(e))}get isDisposed(){return this._isDisposed}dispose(){this.isDisposed||(this._isDisposed=!0,this._adapters.clear(),a.Signal.clearData(this))}find(e){const t=this._adapters.values();for(const n of t)if(e(n))return n}forEach(e){this._adapters.forEach(e)}filter(e){const t=[];return this.forEach((n=>{e(n)&&t.push(n)})),t}has(e){return this._adapters.has(e)}}var g,p=n(38639),m=n(20998);!function(e){e.URL_NS="lsp"}(g||(g={}));const v=new m.Token("@jupyterlab/lsp:ILSPDocumentConnectionManager","Provides the virtual documents and language server connections service."),_=new m.Token("@jupyterlab/lsp:ILSPFeatureManager","Provides the language server feature manager. This token is required to register new client capabilities."),y=new m.Token("@jupyterlab/lsp:ILSPCodeExtractorsManager","Provides the code extractor manager. This token is required in your extension to register code extractor allowing the creation of multiple virtual document from an opened document."),b=new m.Token("@jupyterlab/lsp:IWidgetLSPAdapterTracker","Provides the WidgetLSPAdapter tracker. This token is required in your extension to track WidgetLSPAdapters.");var w;async function C(e){return new Promise((t=>{setTimeout((()=>{t()}),e)}))}function S(e,t=35,n=50,i=(e=>e)){return(async()=>{let r=0;for(;!0!==e();){if(r+=1,-1!==t&&r>t)throw Error("Too many retrials");n=i(n),await C(n)}return e})()}function E(e){const t=[];for(let n in e){const i=T(n.split("."),e[n]);t.push(i)}return r()({},...t)}!function(e){let t,n,i,r;!function(e){e.PUBLISH_DIAGNOSTICS="textDocument/publishDiagnostics",e.SHOW_MESSAGE="window/showMessage",e.LOG_TRACE="$/logTrace",e.LOG_MESSAGE="window/logMessage"}(t=e.ServerNotification||(e.ServerNotification={})),function(e){e.DID_CHANGE="textDocument/didChange",e.DID_CHANGE_CONFIGURATION="workspace/didChangeConfiguration",e.DID_OPEN="textDocument/didOpen",e.DID_SAVE="textDocument/didSave",e.INITIALIZED="initialized",e.SET_TRACE="$/setTrace"}(n=e.ClientNotification||(e.ClientNotification={})),function(e){e.REGISTER_CAPABILITY="client/registerCapability",e.SHOW_MESSAGE_REQUEST="window/showMessageRequest",e.UNREGISTER_CAPABILITY="client/unregisterCapability",e.WORKSPACE_CONFIGURATION="workspace/configuration"}(i=e.ServerRequest||(e.ServerRequest={})),function(e){e.CODE_ACTION="textDocument/codeAction",e.COMPLETION="textDocument/completion",e.COMPLETION_ITEM_RESOLVE="completionItem/resolve",e.DEFINITION="textDocument/definition",e.DOCUMENT_COLOR="textDocument/documentColor",e.DOCUMENT_HIGHLIGHT="textDocument/documentHighlight",e.DOCUMENT_SYMBOL="textDocument/documentSymbol",e.HOVER="textDocument/hover",e.IMPLEMENTATION="textDocument/implementation",e.INITIALIZE="initialize",e.REFERENCES="textDocument/references",e.RENAME="textDocument/rename",e.SIGNATURE_HELP="textDocument/signatureHelp",e.TYPE_DEFINITION="textDocument/typeDefinition",e.LINKED_EDITING_RANGE="textDocument/linkedEditingRange",e.INLINE_VALUE="textDocument/inlineValue",e.INLAY_HINT="textDocument/inlayHint",e.WORKSPACE_SYMBOL="workspace/symbol",e.WORKSPACE_SYMBOL_RESOLVE="workspaceSymbol/resolve",e.FORMATTING="textDocument/formatting",e.RANGE_FORMATTING="textDocument/rangeFormatting"}(r=e.ClientRequest||(e.ClientRequest={}))}(w||(w={}));const T=(e,t)=>{const n=Object.create(null);let i=n;return e.forEach(((n,r)=>{i[n]=Object.create(null),r===e.length-1?i[n]=t:i=i[n]})),n};class D extends Map{constructor(e,t){super(t),this.defaultFactory=e}get(e){return this.getOrCreate(e)}getOrCreate(e,...t){if(this.has(e))return super.get(e);{let n=this.defaultFactory(e,...t);return this.set(e,n),n}}}function M(e,t){const n=JSON.parse(JSON.stringify(e)),{method:i,registerOptions:r}=t,s=i.substring(13)+"Provider";return s?(n[s]=!r||JSON.parse(JSON.stringify(r)),n):(console.warn("Could not register server capability.",t),null)}function R(e,t){const n=JSON.parse(JSON.stringify(e)),{method:i}=t;return delete n[i.substring(13)+"Provider"],n}var O=n(69406);n(75285);var k=n(84806);class L extends k.AbstractMessageReader{constructor(e){super(),this.socket=e,this.state="initial",this.events=[],this.socket.onMessage((e=>this.readMessage(e))),this.socket.onError((e=>this.fireError(e))),this.socket.onClose(((e,t)=>{if(1e3!==e){const n={name:""+e,message:`Error during socket reconnect: code = ${e}, reason = ${t}`};this.fireError(n)}this.fireClose()}))}listen(e){if("initial"===this.state)for(this.state="listening",this.callback=e;0!==this.events.length;){const e=this.events.pop();e.message?this.readMessage(e.message):e.error?this.fireError(e.error):this.fireClose()}return{dispose:()=>{this.callback===e&&(this.callback=void 0)}}}readMessage(e){if("initial"===this.state)this.events.splice(0,0,{message:e});else if("listening"===this.state){const t=JSON.parse(e);this.callback(t)}}fireError(e){"initial"===this.state?this.events.splice(0,0,{error:e}):"listening"===this.state&&super.fireError(e)}fireClose(){"initial"===this.state?this.events.splice(0,0,{}):"listening"===this.state&&super.fireClose(),this.state="closed"}}var N,P,x,j=n(69248);class A extends j.AbstractMessageWriter{constructor(e){super(),this.socket=e,this.errorCount=0}end(){}async write(e){try{const t=JSON.stringify(e);this.socket.send(t)}catch(t){this.errorCount++,this.fireError(t,e,this.errorCount)}}}class I{error(e){console.error(e)}warn(e){console.warn(e)}info(e){console.info(e)}log(e){console.log(e)}debug(e){console.debug(e)}}class q{constructor(e){this.openedUris=new Map,this._isConnected=!1,this._isInitialized=!1,this._disposables=[],this._disposed=new a.Signal(this),this._isDisposed=!1,this._rootUri=e.rootUri}get isConnected(){return this._isConnected}get isInitialized(){return this._isInitialized}get isReady(){return this._isConnected&&this._isInitialized}get disposed(){return this._disposed}get isDisposed(){return this._isDisposed}connect(e){this.socket=e,function(e){const{webSocket:t,onConnection:n}=e,i=e.logger||new I;t.onopen=()=>{const e=function(e){return{send:t=>e.send(t),onMessage:t=>{e.onmessage=e=>t(e.data)},onError:t=>{e.onerror=e=>{"message"in e&&t(e.message)}},onClose:t=>{e.onclose=e=>t(e.code,e.reason)},dispose:()=>e.close()}}(t),r=function(e,t){const n=new L(e),i=new A(e),r=(0,O.createMessageConnection)(n,i,t);return r.onClose((()=>r.dispose())),r}(e,i);n(r)}}({webSocket:this.socket,logger:new I,onConnection:e=>{e.listen(),this._isConnected=!0,this.connection=e,this.sendInitialize();const t=this.connection.onRequest("client/registerCapability",(e=>{e.registrations.forEach((e=>{try{this.serverCapabilities=M(this.serverCapabilities,e)}catch(e){console.error(e)}}))}));this._disposables.push(t);const n=this.connection.onRequest("client/unregisterCapability",(e=>{e.unregisterations.forEach((e=>{this.serverCapabilities=R(this.serverCapabilities,e)}))}));this._disposables.push(n);const i=this.connection.onClose((()=>{this._isConnected=!1}));this._disposables.push(i)}})}close(){this.connection&&this.connection.dispose(),this.openedUris.clear(),this.socket.close()}sendInitialize(){if(!this._isConnected)return;this.openedUris.clear();const e=this.initializeParams();this.connection.sendRequest("initialize",e).then((e=>{this.onServerInitialized(e)}),(e=>{console.warn("LSP websocket connection initialization failure",e)}))}sendOpen(e){const t={textDocument:{uri:e.uri,languageId:e.languageId,text:e.text,version:e.version}};this.connection.sendNotification("textDocument/didOpen",t).catch(console.error),this.openedUris.set(e.uri,!0),this.sendChange(e)}sendChange(e){if(!this.isReady)return;if(!this.openedUris.get(e.uri))return void this.sendOpen(e);const t={textDocument:{uri:e.uri,version:e.version},contentChanges:[{text:e.text}]};this.connection.sendNotification("textDocument/didChange",t).catch(console.error),e.version++}sendSaved(e){if(!this.isReady)return;const t={textDocument:{uri:e.uri,version:e.version},text:e.text};this.connection.sendNotification("textDocument/didSave",t).catch(console.error)}sendConfigurationChange(e){this.isReady&&this.connection.sendNotification("workspace/didChangeConfiguration",e).catch(console.error)}dispose(){this._isDisposed||(this._isDisposed=!0,this._disposables.forEach((e=>{e.dispose()})),this._disposed.emit(),a.Signal.clearData(this))}onServerInitialized(e){this._isInitialized=!0,this.serverCapabilities=e.capabilities,this.connection.sendNotification("initialized",{}).catch(console.error),this.connection.sendNotification("workspace/didChangeConfiguration",{settings:{}}).catch(console.error)}initializeParams(){return{capabilities:{},processId:null,rootUri:this._rootUri,workspaceFolders:null}}}class F{constructor(e,t,n){this.connection=e,this.method=t,this.emitter=n}request(e){return this.emitter.log(N.clientRequested,{method:this.method,message:e}),this.connection.sendRequest(this.method,e).then((t=>(this.emitter.log(N.resultForClient,{method:this.method,message:e}),t)))}}class U{constructor(e,t,n){this.connection=e,this.method=t,this.emitter=n,this.connection.onRequest(t,this._handle.bind(this)),this._handler=null}setHandler(e){this._handler=e}clearHandler(){this._handler=null}_handle(e){return this.emitter.log(N.serverRequested,{method:this.method,message:e}),this._handler?this._handler(e,this.emitter).then((e=>(this.emitter.log(N.responseForServer,{method:this.method,message:e}),e))):new Promise((()=>{}))}}function $(e,t){const n={};for(let i of Object.values(e))n[i]=t(i);return n}!function(e){e[e.clientNotifiedServer=0]="clientNotifiedServer",e[e.serverNotifiedClient=1]="serverNotifiedClient",e[e.serverRequested=2]="serverRequested",e[e.clientRequested=3]="clientRequested",e[e.resultForClient=4]="resultForClient",e[e.responseForServer=5]="responseForServer"}(N||(N={}));class z extends q{constructor(e){super(e),this._closingManually=!1,this._closeSignal=new a.Signal(this),this._errorSignal=new a.Signal(this),this._serverInitialized=new a.Signal(this),this._options=e,this.logAllCommunication=!1,this.serverIdentifier=e.serverIdentifier,this.serverLanguage=e.languageId,this.documentsToOpen=[],this.clientNotifications=this.constructNotificationHandlers(w.ClientNotification),this.serverNotifications=this.constructNotificationHandlers(w.ServerNotification)}get closeSignal(){return this._closeSignal}get errorSignal(){return this._errorSignal}get serverInitialized(){return this._serverInitialized}dispose(){this.isDisposed||(Object.values(this.serverRequests).forEach((e=>e.clearHandler())),this.close(),super.dispose())}log(e,t){this.logAllCommunication&&console.log(e,t)}sendOpenWhenReady(e){this.isReady?this.sendOpen(e):this.documentsToOpen.push(e)}sendSelectiveChange(e,t){this._sendChange([e],t)}sendFullTextChange(e,t){this._sendChange([{text:e}],t)}provides(e){return!(!this.serverCapabilities||!this.serverCapabilities[e])}close(){try{this._closingManually=!0,super.close()}catch(e){this._closingManually=!1}}connect(e){super.connect(e),S((()=>this.isConnected),-1).then((()=>{const e=this.connection.onClose((()=>{this._isConnected=!1,this._closeSignal.emit(this._closingManually)}));this._disposables.push(e)})).catch((()=>{console.error("Could not connect onClose signal")}))}async getCompletionResolve(e){if(this.isReady)return this.connection.sendRequest("completionItem/resolve",e)}constructNotificationHandlers(e){return $(e,(()=>new a.Signal(this)))}constructClientRequestHandler(e){return $(e,(e=>new F(this.connection,e,this)))}constructServerRequestHandler(e){return $(e,(e=>new U(this.connection,e,this)))}initializeParams(){return{...super.initializeParams(),capabilities:this._options.capabilities,initializationOptions:null,processId:null,workspaceFolders:null}}onServerInitialized(e){for(this.afterInitialized(),super.onServerInitialized(e);this.documentsToOpen.length;)this.sendOpen(this.documentsToOpen.pop());this._serverInitialized.emit(this.serverCapabilities)}afterInitialized(){const e=this.connection.onError((e=>this._errorSignal.emit(e)));this._disposables.push(e);for(const e of Object.values(w.ServerNotification)){const t=this.serverNotifications[e],n=this.connection.onNotification(e,(n=>{this.log(N.serverNotifiedClient,{method:e,message:n}),t.emit(n)}));this._disposables.push(n)}for(const e of Object.values(w.ClientNotification))this.clientNotifications[e].connect(((t,n)=>{this.log(N.clientNotifiedServer,{method:e,message:n}),this.connection.sendNotification(e,n).catch(console.error)}));this.clientRequests=this.constructClientRequestHandler(w.ClientRequest),this.serverRequests=this.constructServerRequestHandler(w.ServerRequest),this.serverRequests["client/registerCapability"].setHandler((async e=>{e.registrations.forEach((e=>{try{const t=M(this.serverCapabilities,e);if(null===t)return void console.error(`Failed to register server capability: ${e}`);this.serverCapabilities=t}catch(e){console.error(e)}}))})),this.serverRequests["client/unregisterCapability"].setHandler((async e=>{e.unregisterations.forEach((e=>{this.serverCapabilities=R(this.serverCapabilities,e)}))})),this.serverRequests["workspace/configuration"].setHandler((async e=>e.items.map((e=>null))))}_sendChange(e,t){if(!this.isReady)return;if(0===t.uri.length)return;this.openedUris.get(t.uri)||this.sendOpen(t);const n={textDocument:{uri:t.uri,version:t.version},contentChanges:e};this.connection.sendNotification("textDocument/didChange",n).catch(console.error),t.version++}}class B{constructor(e){this.onNewConnection=e=>{e.errorSignal.connect(((t,n)=>{console.error(n);let i=n.length&&n.length>=1?n[0]:new Error;-1!==i.message.indexOf("code = 1005")?(console.error(`Connection failed for ${e}`),this._forEachDocumentOfConnection(e,(t=>{console.error("disconnecting "+t.uri),this._closed.emit({connection:e,virtualDocument:t}),this._ignoredLanguages.add(t.language),console.error(`Cancelling further attempts to connect ${t.uri} and other documents for this language (no support from the server)`)}))):-1!==i.message.indexOf("code = 1006")?console.error("Connection closed by the server"):console.error("Connection error:",n)})),e.serverInitialized.connect((()=>{this._forEachDocumentOfConnection(e,(t=>{this._initialized.emit({connection:e,virtualDocument:t})})),this.updateServerConfigurations(this.initialConfigurations)})),e.closeSignal.connect(((t,n)=>{n?(console.log("Connection closed"),this._forEachDocumentOfConnection(e,(t=>{this._closed.emit({connection:e,virtualDocument:t})}))):console.error("Connection unexpectedly disconnected")}))},this._initialized=new a.Signal(this),this._connected=new a.Signal(this),this._disconnected=new a.Signal(this),this._closed=new a.Signal(this),this._documentsChanged=new a.Signal(this),this.connections=new Map,this.documents=new Map,this.adapters=new Map,this._ignoredLanguages=new Set,this.languageServerManager=e.languageServerManager,P.setLanguageServerManager(e.languageServerManager),e.adapterTracker.adapterAdded.connect(((e,t)=>{const n=t.widget.context.path;this.registerAdapter(n,t)}))}get initialized(){return this._initialized}get connected(){return this._connected}get disconnected(){return this._disconnected}get closed(){return this._closed}get documentsChanged(){return this._documentsChanged}get ready(){return P.getLanguageServerManager().ready}connectDocumentSignals(e){e.foreignDocumentOpened.connect(this.onForeignDocumentOpened,this),e.foreignDocumentClosed.connect(this.onForeignDocumentClosed,this),this.documents.set(e.uri,e),this._documentsChanged.emit(this.documents)}disconnectDocumentSignals(e,t=!0){e.foreignDocumentOpened.disconnect(this.onForeignDocumentOpened,this),e.foreignDocumentClosed.disconnect(this.onForeignDocumentClosed,this),this.documents.delete(e.uri);for(const t of e.foreignDocuments.values())this.disconnectDocumentSignals(t,!1);t&&this._documentsChanged.emit(this.documents)}onForeignDocumentOpened(e,t){}onForeignDocumentClosed(e,t){const{foreignDocument:n}=t;this.unregisterDocument(n.uri,!1),this.disconnectDocumentSignals(n)}registerAdapter(e,t){this.adapters.set(e,t),t.widget.context.pathChanged.connect(((n,i)=>{this.adapters.delete(e),this.adapters.set(i,t)})),t.disposed.connect((()=>{t.virtualDocument&&this.documents.delete(t.virtualDocument.uri),this.adapters.delete(e)}))}updateConfiguration(e){this.languageServerManager.setConfiguration(e)}updateServerConfigurations(e){let t;for(t in e){if(!e.hasOwnProperty(t))continue;const n={settings:E(e[t].configuration||{})};P.updateServerConfiguration(t,n)}}async retryToConnect(e,t,n=-1){let{virtualDocument:i}=e;if(this._ignoredLanguages.has(i.language))return;let r=1e3*t,s=!1;for(;0!==n&&!s;)await this.connect(e).then((()=>{s=!0})).catch((e=>{console.warn(e)})),console.log("will attempt to re-connect in "+r/1e3+" seconds"),await C(r),r=r<5e3?r+500:r}disconnect(e){P.disconnect(e)}async connect(e,t=30,n=5){let i=await this._connectSocket(e),{virtualDocument:r}=e;if(i){if(!i.isReady)try{await S((()=>i.isReady),Math.round(1e3*t/150),150)}catch(e){console.log(`Connection to ${r.uri} timed out after ${t} seconds, will continue retrying for another ${n} minutes`);try{await S((()=>i.isReady),60*n,1e3)}catch(e){return void console.log(`Connection to ${r.uri} timed out again after ${n} minutes, giving up`)}}return this._connected.emit({connection:i,virtualDocument:r}),i}}unregisterDocument(e,t=!0){const n=this.connections.get(e);n&&(this.connections.delete(e),new Set(this.connections.values()).has(n)||(this.disconnect(n.serverIdentifier),n.dispose()),t&&this._documentsChanged.emit(this.documents))}updateLogging(e,t){for(const n of this.connections.values())n.logAllCommunication=e,null!==t&&n.clientNotifications["$/setTrace"].emit({value:t})}async _connectSocket(e){let{language:t,capabilities:n,virtualDocument:i}=e;this.connectDocumentSignals(i);const r=B.solveUris(i,t),s=this.languageServerManager.getMatchingServers({language:t}),o=0===s.length?null:s[0];if(!r)return;const a=await P.connection(t,o,r,this.onNewConnection,n);return this.connections.set(i.uri,a),a}_forEachDocumentOfConnection(e,t){for(const[n,i]of this.connections.entries())e===i&&t(this.documents.get(n))}}!function(e){e.solveUris=function(e,t){var n;const i=P.getLanguageServerManager(),r=i.settings.wsUrl,s=p.PageConfig.getOption("rootUri"),o=p.PageConfig.getOption("virtualDocumentsUri"),a={language:t},c=i.getMatchingServers(a),u=0===c.length?null:c[0];if(null===u)return;const l=i.getMatchingSpecs(a).get(u);l||console.warn(`Specification not available for server ${u}`);const d=!(null===(n=null==l?void 0:l.requires_documents_on_disk)||void 0===n||n),h=e.hasLspSupportedFile||d?s:o;let f=p.URLExt.join(h,e.uri);return!f.startsWith("file:///")&&f.startsWith("file://")&&(f=f.replace("file://","file:///"),f.startsWith("file:///users/")&&h.startsWith("file:///Users/")&&(f=f.replace("file:///users/","file:///Users/"))),{base:h,document:f,server:p.URLExt.join("ws://jupyter-lsp",t),socket:p.URLExt.join(r,"lsp","ws",u)}}}(B||(B={})),function(e){const t=new Map;let n;e.getLanguageServerManager=function(){return n},e.setLanguageServerManager=function(e){n=e},e.disconnect=function(e){const n=t.get(e);n&&(n.close(),t.delete(e))},e.connection=async function(n,i,r,s,o){let a=t.get(i);if(!a){const{settings:a}=e.getLanguageServerManager(),c=new a.WebSocket(r.socket),u=new z({languageId:n,serverUri:r.server,rootUri:r.base,serverIdentifier:i,capabilities:o});t.set(i,u),u.connect(c),s(u)}return a=t.get(i),a},e.updateServerConfiguration=function(e,n){const i=t.get(e);i&&i.sendConfigurationChange(n)}}(P||(P={}));class W{constructor(){this._extractorMap=new Map,this._extractorMapAnyLanguage=new Map}getExtractors(e,t){var n,i;if(t){const i=this._extractorMap.get(e);return i&&null!==(n=i.get(t))&&void 0!==n?n:[]}return null!==(i=this._extractorMapAnyLanguage.get(e))&&void 0!==i?i:[]}register(e,t){const n=e.cellType;t?n.forEach((n=>{this._extractorMap.has(n)||this._extractorMap.set(n,new Map);const i=this._extractorMap.get(n),r=i.get(t);r?r.push(e):i.set(t,[e])})):n.forEach((t=>{this._extractorMapAnyLanguage.has(t)||this._extractorMapAnyLanguage.set(t,[]),this._extractorMapAnyLanguage.get(t).push(e)}))}}function V(e,t){return t&&e.line===t.line&&e.ch===t.ch}function H(e,t){let n=0,i=0;for(let r of t){if(!(r.length+1<=e)){i=e;break}e-=r.length+1,n+=1}return{line:n,column:i}}function J(e,t,n=!1){let i=n?0:1,r=0;for(let n=0;n<t.length;n++){let s=t[n];if(!(e.line>n)){r+=e.column;break}r+=s.length+i}return r}!function(e){e.isWithinRange=function(e,t){const{line:n,character:i}=e;return n>=t.start.line&&n<=t.end.line&&(n!=t.start.line||i>t.start.character)&&(n!=t.end.line||i<=t.end.character)}}(x||(x={}));class G{constructor(e){this.language=e.language,this.standalone=e.isStandalone,this.fileExtension=e.file_extension,this.cellType=e.cellType}hasForeignCode(e,t){return this.cellType.includes(t)}extractForeignCode(e){let t=e.split("\n"),n=new Array,i=e,r=H(0,t),s=H(i.length,t);return n.push({hostCode:"",foreignCode:i,range:{start:r,end:s},virtualShift:null}),n}}class Y{constructor(){this.features=[],this._featureRegistered=new a.Signal(this)}get featureRegistered(){return this._featureRegistered}register(e){this.features.some((t=>t.id===e.id))?console.warn(`Feature with id ${e.id} is already registered, skipping.`):(this.features.push(e),this._featureRegistered.emit(e))}clientCapabilities(){let e={};for(const t of this.features)t.capabilities&&(e=r()(e,t.capabilities));return e}extensionFactories(){const e=[];for(const t of this.features)t.extensionFactory&&e.push(t.extensionFactory);return e}}var K=n(16954);class Z{constructor(e){this._sessions=new Map,this._specs=new Map,this._warningsEmitted=new Set,this._ready=new m.PromiseDelegate,this._sessionsChanged=new a.Signal(this),this._isDisposed=!1,this._enabled=!0,this._settings=e.settings||K.ServerConnection.makeSettings(),this._baseUrl=e.baseUrl||p.PageConfig.getBaseUrl(),this._retries=e.retries||2,this._retriesInterval=e.retriesInterval||1e4,this._statusCode=-1,this._configuration={},this.fetchSessions().catch((e=>console.log(e)))}get isEnabled(){return this._enabled}get isDisposed(){return this._isDisposed}get settings(){return this._settings}get specs(){return this._specs}get statusUrl(){return p.URLExt.join(this._baseUrl,g.URL_NS,"status")}get sessionsChanged(){return this._sessionsChanged}get sessions(){return this._sessions}get ready(){return this._ready.promise}get statusCode(){return this._statusCode}async enable(){this._enabled=!0,await this.fetchSessions()}disable(){this._enabled=!1,this._sessions=new Map,this._sessionsChanged.emit(void 0)}dispose(){this._isDisposed||(this._isDisposed=!0,a.Signal.clearData(this))}setConfiguration(e){this._configuration=e}getMatchingServers(e){if(!e.language)return console.error("Cannot match server by language: language not available; ensure that kernel and specs provide language and MIME type"),[];const t=[];for(const[n,i]of this._sessions.entries())this.isMatchingSpec(e,i.spec)&&t.push(n);return t.sort(this.compareRanks.bind(this))}getMatchingSpecs(e){const t=new Map;for(const[n,i]of this._specs.entries())this.isMatchingSpec(e,i)&&t.set(n,i);return t}async fetchSessions(){if(!this._enabled)return;let e,t=await K.ServerConnection.makeRequest(this.statusUrl,{method:"GET"},this._settings);if(this._statusCode=t.status,!t.ok)return void(this._retries>0?(this._retries-=1,setTimeout(this.fetchSessions.bind(this),this._retriesInterval)):(this._ready.resolve(void 0),console.log("Missing jupyter_lsp server extension, skipping.")));try{const n=await t.json();e=n.sessions;try{this.version=n.version,this._specs=new Map(Object.entries(n.specs))}catch(e){console.warn(e)}}catch(e){return console.warn(e),void this._ready.resolve(void 0)}for(let t of Object.keys(e)){let n=t;this._sessions.has(n)?Object.assign(this._sessions.get(n)||{},e[t]):this._sessions.set(n,e[t])}const n=this._sessions.keys();for(const t in n)if(!e[t]){let e=t;this._sessions.delete(e)}this._sessionsChanged.emit(void 0),this._ready.resolve(void 0)}isMatchingSpec(e,t){const n=e.language.toLocaleLowerCase();return t.languages.some((e=>e.toLocaleLowerCase()==n))}warnOnce(e){this._warningsEmitted.has(e)||(this._warningsEmitted.add(e),console.warn(e))}compareRanks(e,t){var n,i,r,s;const o=null!==(i=null===(n=this._configuration[e])||void 0===n?void 0:n.rank)&&void 0!==i?i:50,a=null!==(s=null===(r=this._configuration[t])||void 0===r?void 0:r.rank)&&void 0!==s?s:50;return o==a?(this.warnOnce(`Two matching servers: ${e} and ${t} have the same rank; choose which one to use by changing the rank in Advanced Settings Editor`),e.localeCompare(t)):a-o}}function Q(e,t){return t.start.line===t.end.line?e.line===t.start.line&&e.column>=t.start.column&&e.column<=t.end.column:e.line===t.start.line&&e.column>=t.start.column&&e.line<t.end.line||e.line>t.start.line&&e.column<=t.end.column&&e.line===t.end.line||e.line>t.start.line&&e.line<t.end.line}class X{constructor(e){this.version=0,this._document=e}get text(){return this._document.value}get uri(){const e=B.solveUris(this._document,this.languageId);return e?e.document:""}get languageId(){return this._document.language}}class ee{constructor(e){this.blankLinesBetweenCells=2,this._isDisposed=!1,this._foreignDocumentClosed=new a.Signal(this),this._foreignDocumentOpened=new a.Signal(this),this._changed=new a.Signal(this),this.options=e,this.path=this.options.path,this.fileExtension=e.fileExtension,this.hasLspSupportedFile=e.hasLspSupportedFile,this.parent=e.parent,this.language=e.language,this.virtualLines=new Map,this.sourceLines=new Map,this.foreignDocuments=new Map,this._editorToSourceLine=new Map,this._foreignCodeExtractors=e.foreignCodeExtractors,this.standalone=e.standalone||!1,this.instanceId=ee.instancesCount,ee.instancesCount+=1,this.unusedStandaloneDocuments=new D((()=>new Array)),this._remainingLifetime=6,this.documentInfo=new X(this),this.updateManager=new ne(this),this.updateManager.updateBegan.connect(this._updateBeganSlot,this),this.updateManager.blockAdded.connect(this._blockAddedSlot,this),this.updateManager.updateFinished.connect(this._updateFinishedSlot,this),this.clear()}static ceToCm(e){return{line:e.line,ch:e.column}}get isDisposed(){return this._isDisposed}get foreignDocumentClosed(){return this._foreignDocumentClosed}get foreignDocumentOpened(){return this._foreignDocumentOpened}get changed(){return this._changed}get virtualId(){return this.standalone?this.instanceId+"("+this.language+")":this.language}get ancestry(){return this.parent?this.parent.ancestry.concat([this]):[this]}get idPath(){return this.parent?this.parent.idPath+"-"+this.virtualId:this.virtualId}get uri(){const e=encodeURI(this.path);return this.parent?e+"."+this.idPath+"."+this.fileExtension:e}get value(){let e="\n".repeat(this.blankLinesBetweenCells);return this.lineBlocks.join(e)}get lastLine(){const e=this.lineBlocks[this.lineBlocks.length-1].split("\n");return e[e.length-1]}get root(){return this.parent?this.parent.root:this}dispose(){this._isDisposed||(this._isDisposed=!0,this.parent=null,this.closeAllForeignDocuments(),this.updateManager.dispose(),this.foreignDocuments.clear(),this.sourceLines.clear(),this.unusedStandaloneDocuments.clear(),this.virtualLines.clear(),this.documentInfo=null,this.lineBlocks=null,a.Signal.clearData(this))}clear(){this.unusedStandaloneDocuments.clear();for(let e of this.foreignDocuments.values())e.clear(),e.standalone&&this.unusedStandaloneDocuments.get(e.language).push(e);this.virtualLines.clear(),this.sourceLines.clear(),this.lastVirtualLine=0,this.lastSourceLine=0,this.lineBlocks=[]}documentAtSourcePosition(e){let t=this.sourceLines.get(e.line);if(!t)return this;let n={line:t.editorLine,column:e.ch};for(let[e,{virtualDocument:i}]of t.foreignDocumentsMap)if(Q(n,e)){let t={line:n.line-e.start.line,ch:n.column-e.start.column};return i.documentAtSourcePosition(t)}return this}isWithinForeign(e){let t=this.sourceLines.get(e.line),n={line:t.editorLine,column:e.ch};for(let[e]of t.foreignDocumentsMap)if(Q(n,e))return!0;return!1}transformFromEditorToRoot(e,t){if(!this._editorToSourceLine.has(e))return console.log("Editor not found in _editorToSourceLine map"),null;let n=this._editorToSourceLine.get(e);return{...t,line:t.line+n}}virtualPositionAtDocument(e){let t=this.sourceLines.get(e.line);if(null==t)throw new Error("Source line not mapped to virtual position");let n=t.virtualLine,i={line:t.editorLine,column:e.ch};for(let[e,n]of t.foreignDocumentsMap){const{virtualLine:t,virtualDocument:r}=n;if(Q(i,e)){let n={line:i.line-e.start.line,ch:i.column-e.start.column};return r.isWithinForeign(n)?this.virtualPositionAtDocument(n):(n.line+=t,n)}}return{ch:e.ch,line:n}}appendCodeBlock(e,t={line:0,column:0},n){let i=e.value,r=e.ceEditor;if(this.isDisposed)return void console.warn("Cannot append code block: document disposed");let s=i.split("\n"),{lines:o,foreignDocumentsMap:a}=this.prepareCodeBlock(e,t);for(let e=0;e<o.length;e++)this.virtualLines.set(this.lastVirtualLine+e,{skipInspect:[],editor:r,sourceLine:this.lastSourceLine+e});for(let e=0;e<s.length;e++)this.sourceLines.set(this.lastSourceLine+e,{editorLine:e,editorShift:{line:t.line-((null==n?void 0:n.line)||0),column:0===e?t.column-((null==n?void 0:n.column)||0):0},editor:r,foreignDocumentsMap:a,virtualLine:this.lastVirtualLine+e});this.lastVirtualLine+=o.length,this.lineBlocks.push(o.join("\n")+"\n");for(let e=0;e<this.blankLinesBetweenCells;e++)this.virtualLines.set(this.lastVirtualLine+e,{skipInspect:[this.idPath],editor:r,sourceLine:null});this.lastVirtualLine+=this.blankLinesBetweenCells,this.lastSourceLine+=s.length}prepareCodeBlock(e,t={line:0,column:0}){let{cellCodeKept:n,foreignDocumentsMap:i}=this.extractForeignCode(e,t);return{lines:n.split("\n"),foreignDocumentsMap:i}}extractForeignCode(e,t){let n=new Map,i=e.value;const r=this._foreignCodeExtractors.getExtractors(e.type,null),s=this._foreignCodeExtractors.getExtractors(e.type,this.language);for(let o of[...r,...s]){if(!o.hasForeignCode(i,e.type))continue;let r=o.extractForeignCode(i),s="";for(let i of r){if(null!==i.foreignCode){if(null===i.range){console.log("Failure in foreign code extraction: `range` is null but `foreign_code` is not!");continue}let r=this._chooseForeignDocument(o);n.set(i.range,{virtualLine:r.lastVirtualLine,virtualDocument:r,editor:e.ceEditor});let s={line:t.line+i.range.start.line,column:t.column+i.range.start.column};r.appendCodeBlock({value:i.foreignCode,ceEditor:e.ceEditor,type:"code"},s,i.virtualShift)}null!=i.hostCode&&(s+=i.hostCode)}i=s}return{cellCodeKept:i,foreignDocumentsMap:n}}closeForeign(e){this._foreignDocumentClosed.emit({foreignDocument:e,parentHost:this}),this.foreignDocuments.delete(e.virtualId),e.closeAllForeignDocuments(),e.foreignDocumentClosed.disconnect(this.forwardClosedSignal,this),e.foreignDocumentOpened.disconnect(this.forwardOpenedSignal,this),e.dispose()}closeAllForeignDocuments(){for(let e of this.foreignDocuments.values())this.closeForeign(e)}closeExpiredDocuments(){const e=new Set;for(const t of this.sourceLines.values())for(const n of t.foreignDocumentsMap.values())e.add(n.virtualDocument);const t=new Map;for(const[e,n]of this.foreignDocuments.entries()){const i=t.get(n);void 0!==i&&t.set(n,[...i,e]),t.set(n,[e])}const n=new Set(t.keys()),i=new Set([...n].filter((t=>!e.has(t))));for(let e of i.values())if(e.remainingLifetime-=1,e.remainingLifetime<=0){e.dispose();const n=t.get(e);for(const e of n)this.foreignDocuments.delete(e)}}transformSourceToEditor(e){let t=this.sourceLines.get(e.line),n=t.editorLine,i=t.editorShift;return{ch:e.ch+(0===n?i.column:0),line:n+i.line}}transformVirtualToEditor(e){let t=this.transformVirtualToSource(e);return null==t?null:this.transformSourceToEditor(t)}transformVirtualToSource(e){const t=this.virtualLines.get(e.line).sourceLine;return null==t?null:{ch:e.ch,line:t}}transformVirtualToRoot(e){var t;const n=null===(t=this.virtualLines.get(e.line))||void 0===t?void 0:t.editor,i=this.transformVirtualToEditor(e);return n&&i?this.root.transformFromEditorToRoot(n,i):null}getEditorAtVirtualLine(e){let t=e.line;return this.virtualLines.has(t)||(t-=1),this.virtualLines.get(t).editor}getEditorAtSourceLine(e){return this.sourceLines.get(e.line).editor}maybeEmitChanged(){this.value!==this.previousValue&&this._changed.emit(this),this.previousValue=this.value;for(let e of this.foreignDocuments.values())e.maybeEmitChanged()}get remainingLifetime(){return this.parent?this._remainingLifetime:1/0}set remainingLifetime(e){this.parent&&(this._remainingLifetime=e)}_chooseForeignDocument(e){let t,n=this.foreignDocuments.has(e.language);if(!e.standalone&&n)t=this.foreignDocuments.get(e.language);else{let n=this.unusedStandaloneDocuments.get(e.language);t=e.standalone&&n.length>0?n.pop():this.openForeign(e.language,e.standalone,e.fileExtension)}return t}openForeign(e,t,n){let i=new this.constructor({...this.options,parent:this,standalone:t,fileExtension:n,language:e});const r={foreignDocument:i,parentHost:this};return this._foreignDocumentOpened.emit(r),i.foreignDocumentClosed.connect(this.forwardClosedSignal,this),i.foreignDocumentOpened.connect(this.forwardOpenedSignal,this),this.foreignDocuments.set(i.virtualId,i),i}forwardClosedSignal(e,t){this._foreignDocumentClosed.emit(t)}forwardOpenedSignal(e,t){this._foreignDocumentOpened.emit(t)}_updateBeganSlot(){this._editorToSourceLineNew=new Map}_blockAddedSlot(e,t){this._editorToSourceLineNew.set(t.block.ceEditor,t.virtualDocument.lastSourceLine)}_updateFinishedSlot(){this._editorToSourceLine=this._editorToSourceLineNew}}function te(e){let t=new Set;t.add(e);for(let n of e.foreignDocuments.values())te(n).forEach(t.add,t);return t}ee.instancesCount=0;class ne{constructor(e){this.virtualDocument=e,this._isDisposed=!1,this._updateDone=new Promise((e=>{e()})),this._isUpdateInProgress=!1,this._updateLock=!1,this._blockAdded=new a.Signal(this),this._documentUpdated=new a.Signal(this),this._updateBegan=new a.Signal(this),this._updateFinished=new a.Signal(this),this.documentUpdated.connect(this._onUpdated,this)}get updateDone(){return this._updateDone}get isDisposed(){return this._isDisposed}get blockAdded(){return this._blockAdded}get documentUpdated(){return this._documentUpdated}get updateBegan(){return this._updateBegan}get updateFinished(){return this._updateFinished}dispose(){this._isDisposed||(this._isDisposed=!0,this.documentUpdated.disconnect(this._onUpdated),a.Signal.clearData(this))}async withUpdateLock(e){await S((()=>this._canUpdate()),12,10).then((()=>{try{this._updateLock=!0,e()}finally{this._updateLock=!1}}))}async updateDocuments(e){let t=new Promise(((t,n)=>{S((()=>this._canUpdate()),10,5).then((()=>{!this.isDisposed&&this.virtualDocument||t();try{this._isUpdateInProgress=!0,this._updateBegan.emit(e),this.virtualDocument.clear();for(let t of e)this._blockAdded.emit({block:t,virtualDocument:this.virtualDocument}),this.virtualDocument.appendCodeBlock(t);this._updateFinished.emit(e),this.virtualDocument&&(this._documentUpdated.emit(this.virtualDocument),this.virtualDocument.maybeEmitChanged()),t()}catch(e){console.warn("Documents update failed:",e),n(e)}finally{this._isUpdateInProgress=!1}})).catch(console.error)}));return this._updateDone=t,t}_onUpdated(e,t){try{t.closeExpiredDocuments()}catch(e){console.warn("Failed to close expired documents")}}_canUpdate(){return!this.isDisposed&&!this._isUpdateInProgress&&!this._updateLock}}},95282:(e,t,n)=>{e=n.nmd(e);var i="__lodash_hash_undefined__",r=9007199254740991,s="[object Arguments]",o="[object AsyncFunction]",a="[object Function]",c="[object GeneratorFunction]",u="[object Null]",l="[object Object]",d="[object Proxy]",h="[object Undefined]",f=/^\[object .+?Constructor\]$/,g=/^(?:0|[1-9]\d*)$/,p={};p["[object Float32Array]"]=p["[object Float64Array]"]=p["[object Int8Array]"]=p["[object Int16Array]"]=p["[object Int32Array]"]=p["[object Uint8Array]"]=p["[object Uint8ClampedArray]"]=p["[object Uint16Array]"]=p["[object Uint32Array]"]=!0,p[s]=p["[object Array]"]=p["[object ArrayBuffer]"]=p["[object Boolean]"]=p["[object DataView]"]=p["[object Date]"]=p["[object Error]"]=p[a]=p["[object Map]"]=p["[object Number]"]=p[l]=p["[object RegExp]"]=p["[object Set]"]=p["[object String]"]=p["[object WeakMap]"]=!1;var m,v,_,y="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g,b="object"==typeof self&&self&&self.Object===Object&&self,w=y||b||Function("return this")(),C=t&&!t.nodeType&&t,S=C&&e&&!e.nodeType&&e,E=S&&S.exports===C,T=E&&y.process,D=function(){try{return S&&S.require&&S.require("util").types||T&&T.binding&&T.binding("util")}catch(e){}}(),M=D&&D.isTypedArray,R=Array.prototype,O=Function.prototype,k=Object.prototype,L=w["__core-js_shared__"],N=O.toString,P=k.hasOwnProperty,x=(m=/[^.]+$/.exec(L&&L.keys&&L.keys.IE_PROTO||""))?"Symbol(src)_1."+m:"",j=k.toString,A=N.call(Object),I=RegExp("^"+N.call(P).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),q=E?w.Buffer:void 0,F=w.Symbol,U=w.Uint8Array,$=(q&&q.allocUnsafe,v=Object.getPrototypeOf,_=Object,function(e){return v(_(e))}),z=Object.create,B=k.propertyIsEnumerable,W=R.splice,V=F?F.toStringTag:void 0,H=function(){try{var e=he(Object,"defineProperty");return e({},"",{}),e}catch(e){}}(),J=q?q.isBuffer:void 0,G=Math.max,Y=Date.now,K=he(w,"Map"),Z=he(Object,"create"),Q=function(){function e(){}return function(t){if(!Ee(t))return{};if(z)return z(t);e.prototype=t;var n=new e;return e.prototype=void 0,n}}();function X(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var i=e[t];this.set(i[0],i[1])}}function ee(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var i=e[t];this.set(i[0],i[1])}}function te(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var i=e[t];this.set(i[0],i[1])}}function ne(e){var t=this.__data__=new ee(e);this.size=t.size}function ie(e,t,n){(void 0!==n&&!ve(e[t],n)||void 0===n&&!(t in e))&&oe(e,t,n)}function re(e,t,n){var i=e[t];P.call(e,t)&&ve(i,n)&&(void 0!==n||t in e)||oe(e,t,n)}function se(e,t){for(var n=e.length;n--;)if(ve(e[n][0],t))return n;return-1}function oe(e,t,n){"__proto__"==t&&H?H(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}X.prototype.clear=function(){this.__data__=Z?Z(null):{},this.size=0},X.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},X.prototype.get=function(e){var t=this.__data__;if(Z){var n=t[e];return n===i?void 0:n}return P.call(t,e)?t[e]:void 0},X.prototype.has=function(e){var t=this.__data__;return Z?void 0!==t[e]:P.call(t,e)},X.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=Z&&void 0===t?i:t,this},ee.prototype.clear=function(){this.__data__=[],this.size=0},ee.prototype.delete=function(e){var t=this.__data__,n=se(t,e);return!(n<0||(n==t.length-1?t.pop():W.call(t,n,1),--this.size,0))},ee.prototype.get=function(e){var t=this.__data__,n=se(t,e);return n<0?void 0:t[n][1]},ee.prototype.has=function(e){return se(this.__data__,e)>-1},ee.prototype.set=function(e,t){var n=this.__data__,i=se(n,e);return i<0?(++this.size,n.push([e,t])):n[i][1]=t,this},te.prototype.clear=function(){this.size=0,this.__data__={hash:new X,map:new(K||ee),string:new X}},te.prototype.delete=function(e){var t=de(this,e).delete(e);return this.size-=t?1:0,t},te.prototype.get=function(e){return de(this,e).get(e)},te.prototype.has=function(e){return de(this,e).has(e)},te.prototype.set=function(e,t){var n=de(this,e),i=n.size;return n.set(e,t),this.size+=n.size==i?0:1,this},ne.prototype.clear=function(){this.__data__=new ee,this.size=0},ne.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},ne.prototype.get=function(e){return this.__data__.get(e)},ne.prototype.has=function(e){return this.__data__.has(e)},ne.prototype.set=function(e,t){var n=this.__data__;if(n instanceof ee){var i=n.__data__;if(!K||i.length<199)return i.push([e,t]),this.size=++n.size,this;n=this.__data__=new te(i)}return n.set(e,t),this.size=n.size,this};function ae(e){return null==e?void 0===e?h:u:V&&V in Object(e)?function(e){var t=P.call(e,V),n=e[V];try{e[V]=void 0;var i=!0}catch(e){}var r=j.call(e);return i&&(t?e[V]=n:delete e[V]),r}(e):function(e){return j.call(e)}(e)}function ce(e){return Te(e)&&ae(e)==s}function ue(e,t,n,i,r){e!==t&&function(e,t,n){for(var i=-1,r=Object(e),s=n(e),o=s.length;o--;){var a=s[++i];if(!1===t(r[a],a,r))break}}(t,(function(s,o){if(r||(r=new ne),Ee(s))!function(e,t,n,i,r,s,o){var a=pe(e,n),c=pe(t,n),u=o.get(c);if(u)ie(e,n,u);else{var d,h,f,g,p,m=s?s(a,c,n+"",e,t,o):void 0,v=void 0===m;if(v){var _=ye(c),y=!_&&we(c),b=!_&&!y&&De(c);m=c,_||y||b?ye(a)?m=a:Te(p=a)&&be(p)?m=function(e,t){var n=-1,i=e.length;for(t||(t=Array(i));++n<i;)t[n]=e[n];return t}(a):y?(v=!1,m=function(e,t){return e.slice()}(c)):b?(v=!1,g=new(f=(d=c).buffer).constructor(f.byteLength),new U(g).set(new U(f)),h=g,m=new d.constructor(h,d.byteOffset,d.length)):m=[]:function(e){if(!Te(e)||ae(e)!=l)return!1;var t=$(e);if(null===t)return!0;var n=P.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&N.call(n)==A}(c)||_e(c)?(m=a,_e(a)?m=function(e){return function(e,t,n,i){var r=!n;n||(n={});for(var s=-1,o=t.length;++s<o;){var a=t[s],c=void 0;void 0===c&&(c=e[a]),r?oe(n,a,c):re(n,a,c)}return n}(e,Me(e))}(a):Ee(a)&&!Ce(a)||(m=function(e){return"function"!=typeof e.constructor||ge(e)?{}:Q($(e))}(c))):v=!1}v&&(o.set(c,m),r(m,c,i,s,o),o.delete(c)),ie(e,n,m)}}(e,t,o,n,ue,i,r);else{var a=i?i(pe(e,o),s,o+"",e,t,r):void 0;void 0===a&&(a=s),ie(e,o,a)}}),Me)}var le=H?function(e,t){return H(e,"toString",{configurable:!0,enumerable:!1,value:(n=t,function(){return n}),writable:!0});var n}:ke;function de(e,t){var n,i,r=e.__data__;return("string"==(i=typeof(n=t))||"number"==i||"symbol"==i||"boolean"==i?"__proto__"!==n:null===n)?r["string"==typeof t?"string":"hash"]:r.map}function he(e,t){var n=function(e,t){return null==e?void 0:e[t]}(e,t);return function(e){return!(!Ee(e)||function(e){return!!x&&x in e}(e))&&(Ce(e)?I:f).test(function(e){if(null!=e){try{return N.call(e)}catch(e){}try{return e+""}catch(e){}}return""}(e))}(n)?n:void 0}function fe(e,t){var n=typeof e;return!!(t=null==t?r:t)&&("number"==n||"symbol"!=n&&g.test(e))&&e>-1&&e%1==0&&e<t}function ge(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||k)}function pe(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]}var me=function(e){var t=0,n=0;return function(){var i=Y(),r=16-(i-n);if(n=i,r>0){if(++t>=800)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}(le);function ve(e,t){return e===t||e!=e&&t!=t}var _e=ce(function(){return arguments}())?ce:function(e){return Te(e)&&P.call(e,"callee")&&!B.call(e,"callee")},ye=Array.isArray;function be(e){return null!=e&&Se(e.length)&&!Ce(e)}var we=J||function(){return!1};function Ce(e){if(!Ee(e))return!1;var t=ae(e);return t==a||t==c||t==o||t==d}function Se(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=r}function Ee(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function Te(e){return null!=e&&"object"==typeof e}var De=M?function(e){return function(t){return e(t)}}(M):function(e){return Te(e)&&Se(e.length)&&!!p[ae(e)]};function Me(e){return be(e)?function(e,t){var n=ye(e),i=!n&&_e(e),r=!n&&!i&&we(e),s=!n&&!i&&!r&&De(e),o=n||i||r||s,a=o?function(e,t){for(var n=-1,i=Array(e);++n<e;)i[n]=t(n);return i}(e.length,String):[],c=a.length;for(var u in e)!t&&!P.call(e,u)||o&&("length"==u||r&&("offset"==u||"parent"==u)||s&&("buffer"==u||"byteLength"==u||"byteOffset"==u)||fe(u,c))||a.push(u);return a}(e,!0):function(e){if(!Ee(e))return function(e){var t=[];if(null!=e)for(var n in Object(e))t.push(n);return t}(e);var t=ge(e),n=[];for(var i in e)("constructor"!=i||!t&&P.call(e,i))&&n.push(i);return n}(e)}var Re,Oe=(Re=function(e,t,n,i){ue(e,t,n,i)},function(e,t){return me(function(e,t,n){return t=G(void 0===t?e.length-1:t,0),function(){for(var i=arguments,r=-1,s=G(i.length-t,0),o=Array(s);++r<s;)o[r]=i[t+r];r=-1;for(var a=Array(t+1);++r<t;)a[r]=i[r];return a[t]=n(o),function(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}(e,this,a)}}(e,t,ke),e+"")}((function(e,t){var n=-1,i=t.length,r=i>1?t[i-1]:void 0,s=i>2?t[2]:void 0;for(r=Re.length>3&&"function"==typeof r?(i--,r):void 0,s&&function(e,t,n){if(!Ee(n))return!1;var i=typeof t;return!!("number"==i?be(n)&&fe(t,n.length):"string"==i&&t in n)&&ve(n[t],e)}(t[0],t[1],s)&&(r=i<3?void 0:r,i=1),e=Object(e);++n<i;){var o=t[n];o&&Re(e,o,n,r)}return e})));function ke(e){return e}e.exports=Oe},69406:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||i(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.createMessageConnection=t.BrowserMessageWriter=t.BrowserMessageReader=void 0,n(36028).default.install();const s=n(95748);r(n(95748),t);class o extends s.AbstractMessageReader{constructor(e){super(),this._onData=new s.Emitter,this._messageListener=e=>{this._onData.fire(e.data)},e.addEventListener("error",(e=>this.fireError(e))),e.onmessage=this._messageListener}listen(e){return this._onData.event(e)}}t.BrowserMessageReader=o;class a extends s.AbstractMessageWriter{constructor(e){super(),this.port=e,this.errorCount=0,e.addEventListener("error",(e=>this.fireError(e)))}write(e){try{return this.port.postMessage(e),Promise.resolve()}catch(t){return this.handleError(t,e),Promise.reject(t)}}handleError(e,t){this.errorCount++,this.fireError(e,t,this.errorCount)}end(){}}t.BrowserMessageWriter=a,t.createMessageConnection=function(e,t,n,i){return void 0===n&&(n=s.NullLogger),s.ConnectionStrategy.is(i)&&(i={connectionStrategy:i}),(0,s.createMessageConnection)(e,t,n,i)}},36028:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(95748);class r extends i.AbstractMessageBuffer{constructor(e="utf-8"){super(e),this.asciiDecoder=new TextDecoder("ascii")}emptyBuffer(){return r.emptyBuffer}fromString(e,t){return(new TextEncoder).encode(e)}toString(e,t){return"ascii"===t?this.asciiDecoder.decode(e):new TextDecoder(t).decode(e)}asNative(e,t){return void 0===t?e:e.slice(0,t)}allocNative(e){return new Uint8Array(e)}}r.emptyBuffer=new Uint8Array(0);class s{constructor(e){this.socket=e,this._onData=new i.Emitter,this._messageListener=e=>{e.data.arrayBuffer().then((e=>{this._onData.fire(new Uint8Array(e))}),(()=>{(0,i.RAL)().console.error("Converting blob to array buffer failed.")}))},this.socket.addEventListener("message",this._messageListener)}onClose(e){return this.socket.addEventListener("close",e),i.Disposable.create((()=>this.socket.removeEventListener("close",e)))}onError(e){return this.socket.addEventListener("error",e),i.Disposable.create((()=>this.socket.removeEventListener("error",e)))}onEnd(e){return this.socket.addEventListener("end",e),i.Disposable.create((()=>this.socket.removeEventListener("end",e)))}onData(e){return this._onData.event(e)}}class o{constructor(e){this.socket=e}onClose(e){return this.socket.addEventListener("close",e),i.Disposable.create((()=>this.socket.removeEventListener("close",e)))}onError(e){return this.socket.addEventListener("error",e),i.Disposable.create((()=>this.socket.removeEventListener("error",e)))}onEnd(e){return this.socket.addEventListener("end",e),i.Disposable.create((()=>this.socket.removeEventListener("end",e)))}write(e,t){if("string"==typeof e){if(void 0!==t&&"utf-8"!==t)throw new Error(`In a Browser environments only utf-8 text encoding is supported. But got encoding: ${t}`);this.socket.send(e)}else this.socket.send(e);return Promise.resolve()}end(){this.socket.close()}}const a=new TextEncoder,c=Object.freeze({messageBuffer:Object.freeze({create:e=>new r(e)}),applicationJson:Object.freeze({encoder:Object.freeze({name:"application/json",encode:(e,t)=>{if("utf-8"!==t.charset)throw new Error(`In a Browser environments only utf-8 text encoding is supported. But got encoding: ${t.charset}`);return Promise.resolve(a.encode(JSON.stringify(e,void 0,0)))}}),decoder:Object.freeze({name:"application/json",decode:(e,t)=>{if(!(e instanceof Uint8Array))throw new Error("In a Browser environments only Uint8Arrays are supported.");return Promise.resolve(JSON.parse(new TextDecoder(t.charset).decode(e)))}})}),stream:Object.freeze({asReadableStream:e=>new s(e),asWritableStream:e=>new o(e)}),console,timer:Object.freeze({setTimeout(e,t,...n){const i=setTimeout(e,t,...n);return{dispose:()=>clearTimeout(i)}},setImmediate(e,...t){const n=setTimeout(e,0,...t);return{dispose:()=>clearTimeout(n)}},setInterval(e,t,...n){const i=setInterval(e,t,...n);return{dispose:()=>clearInterval(i)}}})});function u(){return c}!function(e){e.install=function(){i.RAL.install(c)}}(u||(u={})),t.default=u},95748:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ProgressType=t.ProgressToken=t.createMessageConnection=t.NullLogger=t.ConnectionOptions=t.ConnectionStrategy=t.AbstractMessageBuffer=t.WriteableStreamMessageWriter=t.AbstractMessageWriter=t.MessageWriter=t.ReadableStreamMessageReader=t.AbstractMessageReader=t.MessageReader=t.SharedArrayReceiverStrategy=t.SharedArraySenderStrategy=t.CancellationToken=t.CancellationTokenSource=t.Emitter=t.Event=t.Disposable=t.LRUCache=t.Touch=t.LinkedMap=t.ParameterStructures=t.NotificationType9=t.NotificationType8=t.NotificationType7=t.NotificationType6=t.NotificationType5=t.NotificationType4=t.NotificationType3=t.NotificationType2=t.NotificationType1=t.NotificationType0=t.NotificationType=t.ErrorCodes=t.ResponseError=t.RequestType9=t.RequestType8=t.RequestType7=t.RequestType6=t.RequestType5=t.RequestType4=t.RequestType3=t.RequestType2=t.RequestType1=t.RequestType0=t.RequestType=t.Message=t.RAL=void 0,t.MessageStrategy=t.CancellationStrategy=t.CancellationSenderStrategy=t.CancellationReceiverStrategy=t.ConnectionError=t.ConnectionErrors=t.LogTraceNotification=t.SetTraceNotification=t.TraceFormat=t.TraceValues=t.Trace=void 0;const i=n(75285);Object.defineProperty(t,"Message",{enumerable:!0,get:function(){return i.Message}}),Object.defineProperty(t,"RequestType",{enumerable:!0,get:function(){return i.RequestType}}),Object.defineProperty(t,"RequestType0",{enumerable:!0,get:function(){return i.RequestType0}}),Object.defineProperty(t,"RequestType1",{enumerable:!0,get:function(){return i.RequestType1}}),Object.defineProperty(t,"RequestType2",{enumerable:!0,get:function(){return i.RequestType2}}),Object.defineProperty(t,"RequestType3",{enumerable:!0,get:function(){return i.RequestType3}}),Object.defineProperty(t,"RequestType4",{enumerable:!0,get:function(){return i.RequestType4}}),Object.defineProperty(t,"RequestType5",{enumerable:!0,get:function(){return i.RequestType5}}),Object.defineProperty(t,"RequestType6",{enumerable:!0,get:function(){return i.RequestType6}}),Object.defineProperty(t,"RequestType7",{enumerable:!0,get:function(){return i.RequestType7}}),Object.defineProperty(t,"RequestType8",{enumerable:!0,get:function(){return i.RequestType8}}),Object.defineProperty(t,"RequestType9",{enumerable:!0,get:function(){return i.RequestType9}}),Object.defineProperty(t,"ResponseError",{enumerable:!0,get:function(){return i.ResponseError}}),Object.defineProperty(t,"ErrorCodes",{enumerable:!0,get:function(){return i.ErrorCodes}}),Object.defineProperty(t,"NotificationType",{enumerable:!0,get:function(){return i.NotificationType}}),Object.defineProperty(t,"NotificationType0",{enumerable:!0,get:function(){return i.NotificationType0}}),Object.defineProperty(t,"NotificationType1",{enumerable:!0,get:function(){return i.NotificationType1}}),Object.defineProperty(t,"NotificationType2",{enumerable:!0,get:function(){return i.NotificationType2}}),Object.defineProperty(t,"NotificationType3",{enumerable:!0,get:function(){return i.NotificationType3}}),Object.defineProperty(t,"NotificationType4",{enumerable:!0,get:function(){return i.NotificationType4}}),Object.defineProperty(t,"NotificationType5",{enumerable:!0,get:function(){return i.NotificationType5}}),Object.defineProperty(t,"NotificationType6",{enumerable:!0,get:function(){return i.NotificationType6}}),Object.defineProperty(t,"NotificationType7",{enumerable:!0,get:function(){return i.NotificationType7}}),Object.defineProperty(t,"NotificationType8",{enumerable:!0,get:function(){return i.NotificationType8}}),Object.defineProperty(t,"NotificationType9",{enumerable:!0,get:function(){return i.NotificationType9}}),Object.defineProperty(t,"ParameterStructures",{enumerable:!0,get:function(){return i.ParameterStructures}});const r=n(45164);Object.defineProperty(t,"LinkedMap",{enumerable:!0,get:function(){return r.LinkedMap}}),Object.defineProperty(t,"LRUCache",{enumerable:!0,get:function(){return r.LRUCache}}),Object.defineProperty(t,"Touch",{enumerable:!0,get:function(){return r.Touch}});const s=n(68159);Object.defineProperty(t,"Disposable",{enumerable:!0,get:function(){return s.Disposable}});const o=n(345);Object.defineProperty(t,"Event",{enumerable:!0,get:function(){return o.Event}}),Object.defineProperty(t,"Emitter",{enumerable:!0,get:function(){return o.Emitter}});const a=n(97770);Object.defineProperty(t,"CancellationTokenSource",{enumerable:!0,get:function(){return a.CancellationTokenSource}}),Object.defineProperty(t,"CancellationToken",{enumerable:!0,get:function(){return a.CancellationToken}});const c=n(50278);Object.defineProperty(t,"SharedArraySenderStrategy",{enumerable:!0,get:function(){return c.SharedArraySenderStrategy}}),Object.defineProperty(t,"SharedArrayReceiverStrategy",{enumerable:!0,get:function(){return c.SharedArrayReceiverStrategy}});const u=n(84806);Object.defineProperty(t,"MessageReader",{enumerable:!0,get:function(){return u.MessageReader}}),Object.defineProperty(t,"AbstractMessageReader",{enumerable:!0,get:function(){return u.AbstractMessageReader}}),Object.defineProperty(t,"ReadableStreamMessageReader",{enumerable:!0,get:function(){return u.ReadableStreamMessageReader}});const l=n(69248);Object.defineProperty(t,"MessageWriter",{enumerable:!0,get:function(){return l.MessageWriter}}),Object.defineProperty(t,"AbstractMessageWriter",{enumerable:!0,get:function(){return l.AbstractMessageWriter}}),Object.defineProperty(t,"WriteableStreamMessageWriter",{enumerable:!0,get:function(){return l.WriteableStreamMessageWriter}});const d=n(15506);Object.defineProperty(t,"AbstractMessageBuffer",{enumerable:!0,get:function(){return d.AbstractMessageBuffer}});const h=n(6336);Object.defineProperty(t,"ConnectionStrategy",{enumerable:!0,get:function(){return h.ConnectionStrategy}}),Object.defineProperty(t,"ConnectionOptions",{enumerable:!0,get:function(){return h.ConnectionOptions}}),Object.defineProperty(t,"NullLogger",{enumerable:!0,get:function(){return h.NullLogger}}),Object.defineProperty(t,"createMessageConnection",{enumerable:!0,get:function(){return h.createMessageConnection}}),Object.defineProperty(t,"ProgressToken",{enumerable:!0,get:function(){return h.ProgressToken}}),Object.defineProperty(t,"ProgressType",{enumerable:!0,get:function(){return h.ProgressType}}),Object.defineProperty(t,"Trace",{enumerable:!0,get:function(){return h.Trace}}),Object.defineProperty(t,"TraceValues",{enumerable:!0,get:function(){return h.TraceValues}}),Object.defineProperty(t,"TraceFormat",{enumerable:!0,get:function(){return h.TraceFormat}}),Object.defineProperty(t,"SetTraceNotification",{enumerable:!0,get:function(){return h.SetTraceNotification}}),Object.defineProperty(t,"LogTraceNotification",{enumerable:!0,get:function(){return h.LogTraceNotification}}),Object.defineProperty(t,"ConnectionErrors",{enumerable:!0,get:function(){return h.ConnectionErrors}}),Object.defineProperty(t,"ConnectionError",{enumerable:!0,get:function(){return h.ConnectionError}}),Object.defineProperty(t,"CancellationReceiverStrategy",{enumerable:!0,get:function(){return h.CancellationReceiverStrategy}}),Object.defineProperty(t,"CancellationSenderStrategy",{enumerable:!0,get:function(){return h.CancellationSenderStrategy}}),Object.defineProperty(t,"CancellationStrategy",{enumerable:!0,get:function(){return h.CancellationStrategy}}),Object.defineProperty(t,"MessageStrategy",{enumerable:!0,get:function(){return h.MessageStrategy}});const f=n(48094);t.RAL=f.default},97770:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CancellationTokenSource=t.CancellationToken=void 0;const i=n(48094),r=n(78472),s=n(345);var o;!function(e){e.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:s.Event.None}),e.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:s.Event.None}),e.is=function(t){const n=t;return n&&(n===e.None||n===e.Cancelled||r.boolean(n.isCancellationRequested)&&!!n.onCancellationRequested)}}(o||(t.CancellationToken=o={}));const a=Object.freeze((function(e,t){const n=(0,i.default)().timer.setTimeout(e.bind(t),0);return{dispose(){n.dispose()}}}));class c{constructor(){this._isCancelled=!1}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?a:(this._emitter||(this._emitter=new s.Emitter),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=void 0)}}t.CancellationTokenSource=class{get token(){return this._token||(this._token=new c),this._token}cancel(){this._token?this._token.cancel():this._token=o.Cancelled}dispose(){this._token?this._token instanceof c&&this._token.dispose():this._token=o.None}}},6336:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createMessageConnection=t.ConnectionOptions=t.MessageStrategy=t.CancellationStrategy=t.CancellationSenderStrategy=t.CancellationReceiverStrategy=t.RequestCancellationReceiverStrategy=t.IdCancellationReceiverStrategy=t.ConnectionStrategy=t.ConnectionError=t.ConnectionErrors=t.LogTraceNotification=t.SetTraceNotification=t.TraceFormat=t.TraceValues=t.Trace=t.NullLogger=t.ProgressType=t.ProgressToken=void 0;const i=n(48094),r=n(78472),s=n(75285),o=n(45164),a=n(345),c=n(97770);var u,l,d,h,f,g,p,m,v,_,y,b,w,C,S,E,T,D,M;!function(e){e.type=new s.NotificationType("$/cancelRequest")}(u||(u={})),function(e){e.is=function(e){return"string"==typeof e||"number"==typeof e}}(l||(t.ProgressToken=l={})),function(e){e.type=new s.NotificationType("$/progress")}(d||(d={})),t.ProgressType=class{constructor(){}},function(e){e.is=function(e){return r.func(e)}}(h||(h={})),t.NullLogger=Object.freeze({error:()=>{},warn:()=>{},info:()=>{},log:()=>{}}),function(e){e[e.Off=0]="Off",e[e.Messages=1]="Messages",e[e.Compact=2]="Compact",e[e.Verbose=3]="Verbose"}(f||(t.Trace=f={})),function(e){e.Off="off",e.Messages="messages",e.Compact="compact",e.Verbose="verbose"}(g||(t.TraceValues=g={})),function(e){e.fromString=function(t){if(!r.string(t))return e.Off;switch(t=t.toLowerCase()){case"off":default:return e.Off;case"messages":return e.Messages;case"compact":return e.Compact;case"verbose":return e.Verbose}},e.toString=function(t){switch(t){case e.Off:return"off";case e.Messages:return"messages";case e.Compact:return"compact";case e.Verbose:return"verbose";default:return"off"}}}(f||(t.Trace=f={})),function(e){e.Text="text",e.JSON="json"}(p||(t.TraceFormat=p={})),function(e){e.fromString=function(t){return r.string(t)&&"json"===(t=t.toLowerCase())?e.JSON:e.Text}}(p||(t.TraceFormat=p={})),function(e){e.type=new s.NotificationType("$/setTrace")}(m||(t.SetTraceNotification=m={})),function(e){e.type=new s.NotificationType("$/logTrace")}(v||(t.LogTraceNotification=v={})),function(e){e[e.Closed=1]="Closed",e[e.Disposed=2]="Disposed",e[e.AlreadyListening=3]="AlreadyListening"}(_||(t.ConnectionErrors=_={}));class R extends Error{constructor(e,t){super(t),this.code=e,Object.setPrototypeOf(this,R.prototype)}}t.ConnectionError=R,function(e){e.is=function(e){const t=e;return t&&r.func(t.cancelUndispatched)}}(y||(t.ConnectionStrategy=y={})),function(e){e.is=function(e){const t=e;return t&&(void 0===t.kind||"id"===t.kind)&&r.func(t.createCancellationTokenSource)&&(void 0===t.dispose||r.func(t.dispose))}}(b||(t.IdCancellationReceiverStrategy=b={})),function(e){e.is=function(e){const t=e;return t&&"request"===t.kind&&r.func(t.createCancellationTokenSource)&&(void 0===t.dispose||r.func(t.dispose))}}(w||(t.RequestCancellationReceiverStrategy=w={})),function(e){e.Message=Object.freeze({createCancellationTokenSource:e=>new c.CancellationTokenSource}),e.is=function(e){return b.is(e)||w.is(e)}}(C||(t.CancellationReceiverStrategy=C={})),function(e){e.Message=Object.freeze({sendCancellation:(e,t)=>e.sendNotification(u.type,{id:t}),cleanup(e){}}),e.is=function(e){const t=e;return t&&r.func(t.sendCancellation)&&r.func(t.cleanup)}}(S||(t.CancellationSenderStrategy=S={})),function(e){e.Message=Object.freeze({receiver:C.Message,sender:S.Message}),e.is=function(e){const t=e;return t&&C.is(t.receiver)&&S.is(t.sender)}}(E||(t.CancellationStrategy=E={})),function(e){e.is=function(e){const t=e;return t&&r.func(t.handleMessage)}}(T||(t.MessageStrategy=T={})),function(e){e.is=function(e){const t=e;return t&&(E.is(t.cancellationStrategy)||y.is(t.connectionStrategy)||T.is(t.messageStrategy))}}(D||(t.ConnectionOptions=D={})),function(e){e[e.New=1]="New",e[e.Listening=2]="Listening",e[e.Closed=3]="Closed",e[e.Disposed=4]="Disposed"}(M||(M={})),t.createMessageConnection=function(e,n,g,y){const w=void 0!==g?g:t.NullLogger;let C=0,S=0,D=0;const O="2.0";let k;const L=new Map;let N;const P=new Map,x=new Map;let j,A,I=new o.LinkedMap,q=new Map,F=new Set,U=new Map,$=f.Off,z=p.Text,B=M.New;const W=new a.Emitter,V=new a.Emitter,H=new a.Emitter,J=new a.Emitter,G=new a.Emitter,Y=y&&y.cancellationStrategy?y.cancellationStrategy:E.Message;function K(e){if(null===e)throw new Error("Can't send requests with id null since the response can't be correlated.");return"req-"+e.toString()}function Z(e){}function Q(){return B===M.Listening}function X(){return B===M.Closed}function ee(){return B===M.Disposed}function te(){B!==M.New&&B!==M.Listening||(B=M.Closed,V.fire(void 0))}function ne(){j||0===I.size||(j=(0,i.default)().timer.setImmediate((()=>{j=void 0,function(){if(0===I.size)return;const e=I.shift();try{const t=y?.messageStrategy;T.is(t)?t.handleMessage(e,ie):ie(e)}finally{ne()}}()})))}function ie(e){s.Message.isRequest(e)?function(e){if(ee())return;function t(t,i,r){const o={jsonrpc:O,id:e.id};t instanceof s.ResponseError?o.error=t.toJson():o.result=void 0===t?null:t,oe(o,i,r),n.write(o).catch((()=>w.error("Sending response failed.")))}function i(t,i,r){const s={jsonrpc:O,id:e.id,error:t.toJson()};oe(s,i,r),n.write(s).catch((()=>w.error("Sending response failed.")))}!function(e){if($!==f.Off&&A)if(z===p.Text){let t;$!==f.Verbose&&$!==f.Compact||!e.params||(t=`Params: ${se(e.params)}\n\n`),A.log(`Received request '${e.method} - (${e.id})'.`,t)}else ce("receive-request",e)}(e);const o=L.get(e.method);let a,c;o&&(a=o.type,c=o.handler);const u=Date.now();if(c||k){const o=e.id??String(Date.now()),l=b.is(Y.receiver)?Y.receiver.createCancellationTokenSource(o):Y.receiver.createCancellationTokenSource(e);null!==e.id&&F.has(e.id)&&l.cancel(),null!==e.id&&U.set(o,l);try{let d;if(c)if(void 0===e.params){if(void 0!==a&&0!==a.numberOfParams)return void i(new s.ResponseError(s.ErrorCodes.InvalidParams,`Request ${e.method} defines ${a.numberOfParams} params but received none.`),e.method,u);d=c(l.token)}else if(Array.isArray(e.params)){if(void 0!==a&&a.parameterStructures===s.ParameterStructures.byName)return void i(new s.ResponseError(s.ErrorCodes.InvalidParams,`Request ${e.method} defines parameters by name but received parameters by position`),e.method,u);d=c(...e.params,l.token)}else{if(void 0!==a&&a.parameterStructures===s.ParameterStructures.byPosition)return void i(new s.ResponseError(s.ErrorCodes.InvalidParams,`Request ${e.method} defines parameters by position but received parameters by name`),e.method,u);d=c(e.params,l.token)}else k&&(d=k(e.method,e.params,l.token));const h=d;d?h.then?h.then((n=>{U.delete(o),t(n,e.method,u)}),(t=>{U.delete(o),t instanceof s.ResponseError?i(t,e.method,u):t&&r.string(t.message)?i(new s.ResponseError(s.ErrorCodes.InternalError,`Request ${e.method} failed with message: ${t.message}`),e.method,u):i(new s.ResponseError(s.ErrorCodes.InternalError,`Request ${e.method} failed unexpectedly without providing any details.`),e.method,u)})):(U.delete(o),t(d,e.method,u)):(U.delete(o),function(t,i,r){void 0===t&&(t=null);const s={jsonrpc:O,id:e.id,result:t};oe(s,i,r),n.write(s).catch((()=>w.error("Sending response failed.")))}(d,e.method,u))}catch(n){U.delete(o),n instanceof s.ResponseError?t(n,e.method,u):n&&r.string(n.message)?i(new s.ResponseError(s.ErrorCodes.InternalError,`Request ${e.method} failed with message: ${n.message}`),e.method,u):i(new s.ResponseError(s.ErrorCodes.InternalError,`Request ${e.method} failed unexpectedly without providing any details.`),e.method,u)}}else i(new s.ResponseError(s.ErrorCodes.MethodNotFound,`Unhandled method ${e.method}`),e.method,u)}(e):s.Message.isNotification(e)?function(e){if(ee())return;let t,n;if(e.method===u.type.method){const t=e.params.id;return F.delete(t),void ae(e)}{const i=P.get(e.method);i&&(n=i.handler,t=i.type)}if(n||N)try{if(ae(e),n)if(void 0===e.params)void 0!==t&&0!==t.numberOfParams&&t.parameterStructures!==s.ParameterStructures.byName&&w.error(`Notification ${e.method} defines ${t.numberOfParams} params but received none.`),n();else if(Array.isArray(e.params)){const i=e.params;e.method===d.type.method&&2===i.length&&l.is(i[0])?n({token:i[0],value:i[1]}):(void 0!==t&&(t.parameterStructures===s.ParameterStructures.byName&&w.error(`Notification ${e.method} defines parameters by name but received parameters by position`),t.numberOfParams!==e.params.length&&w.error(`Notification ${e.method} defines ${t.numberOfParams} params but received ${i.length} arguments`)),n(...i))}else void 0!==t&&t.parameterStructures===s.ParameterStructures.byPosition&&w.error(`Notification ${e.method} defines parameters by position but received parameters by name`),n(e.params);else N&&N(e.method,e.params)}catch(t){t.message?w.error(`Notification handler '${e.method}' failed with message: ${t.message}`):w.error(`Notification handler '${e.method}' failed unexpectedly.`)}else H.fire(e)}(e):s.Message.isResponse(e)?function(e){if(!ee())if(null===e.id)e.error?w.error(`Received response message without id: Error is: \n${JSON.stringify(e.error,void 0,4)}`):w.error("Received response message without id. No further error information provided.");else{const t=e.id,n=q.get(t);if(function(e,t){if($!==f.Off&&A)if(z===p.Text){let n;if($!==f.Verbose&&$!==f.Compact||(e.error&&e.error.data?n=`Error data: ${se(e.error.data)}\n\n`:e.result?n=`Result: ${se(e.result)}\n\n`:void 0===e.error&&(n="No result returned.\n\n")),t){const i=e.error?` Request failed: ${e.error.message} (${e.error.code}).`:"";A.log(`Received response '${t.method} - (${e.id})' in ${Date.now()-t.timerStart}ms.${i}`,n)}else A.log(`Received response ${e.id} without active response promise.`,n)}else ce("receive-response",e)}(e,n),void 0!==n){q.delete(t);try{if(e.error){const t=e.error;n.reject(new s.ResponseError(t.code,t.message,t.data))}else{if(void 0===e.result)throw new Error("Should never happen.");n.resolve(e.result)}}catch(e){e.message?w.error(`Response handler '${n.method}' failed with message: ${e.message}`):w.error(`Response handler '${n.method}' failed unexpectedly.`)}}}}(e):function(e){if(!e)return void w.error("Received empty message.");w.error(`Received message which is neither a response nor a notification message:\n${JSON.stringify(e,null,4)}`);const t=e;if(r.string(t.id)||r.number(t.id)){const e=t.id,n=q.get(e);n&&n.reject(new Error("The received response has neither a result nor an error property."))}}(e)}e.onClose(te),e.onError((function(e){W.fire([e,void 0,void 0])})),n.onClose(te),n.onError((function(e){W.fire(e)}));const re=e=>{try{if(s.Message.isNotification(e)&&e.method===u.type.method){const t=e.params.id,i=K(t),r=I.get(i);if(s.Message.isRequest(r)){const s=y?.connectionStrategy,o=s&&s.cancelUndispatched?s.cancelUndispatched(r,Z):void 0;if(o&&(void 0!==o.error||void 0!==o.result))return I.delete(i),U.delete(t),o.id=r.id,oe(o,e.method,Date.now()),void n.write(o).catch((()=>w.error("Sending response for canceled message failed.")))}const o=U.get(t);if(void 0!==o)return o.cancel(),void ae(e);F.add(t)}!function(e,t){var n;s.Message.isRequest(t)?e.set(K(t.id),t):s.Message.isResponse(t)?e.set(null===(n=t.id)?"res-unknown-"+(++D).toString():"res-"+n.toString(),t):e.set("not-"+(++S).toString(),t)}(I,e)}finally{ne()}};function se(e){if(null!=e)switch($){case f.Verbose:return JSON.stringify(e,null,4);case f.Compact:return JSON.stringify(e);default:return}}function oe(e,t,n){if($!==f.Off&&A)if(z===p.Text){let i;$!==f.Verbose&&$!==f.Compact||(e.error&&e.error.data?i=`Error data: ${se(e.error.data)}\n\n`:e.result?i=`Result: ${se(e.result)}\n\n`:void 0===e.error&&(i="No result returned.\n\n")),A.log(`Sending response '${t} - (${e.id})'. Processing request took ${Date.now()-n}ms`,i)}else ce("send-response",e)}function ae(e){if($!==f.Off&&A&&e.method!==v.type.method)if(z===p.Text){let t;$!==f.Verbose&&$!==f.Compact||(t=e.params?`Params: ${se(e.params)}\n\n`:"No parameters provided.\n\n"),A.log(`Received notification '${e.method}'.`,t)}else ce("receive-notification",e)}function ce(e,t){if(!A||$===f.Off)return;const n={isLSPMessage:!0,type:e,message:t,timestamp:Date.now()};A.log(n)}function ue(){if(X())throw new R(_.Closed,"Connection is closed.");if(ee())throw new R(_.Disposed,"Connection is disposed.")}function le(e){return void 0===e?null:e}function de(e){return null===e?void 0:e}function he(e){return null!=e&&!Array.isArray(e)&&"object"==typeof e}function fe(e,t){switch(e){case s.ParameterStructures.auto:return he(t)?de(t):[le(t)];case s.ParameterStructures.byName:if(!he(t))throw new Error("Received parameters by name but param is not an object literal.");return de(t);case s.ParameterStructures.byPosition:return[le(t)];default:throw new Error(`Unknown parameter structure ${e.toString()}`)}}function ge(e,t){let n;const i=e.numberOfParams;switch(i){case 0:n=void 0;break;case 1:n=fe(e.parameterStructures,t[0]);break;default:n=[];for(let e=0;e<t.length&&e<i;e++)n.push(le(t[e]));if(t.length<i)for(let e=t.length;e<i;e++)n.push(null)}return n}const pe={sendNotification:(e,...t)=>{let i,o;if(ue(),r.string(e)){i=e;const n=t[0];let r=0,a=s.ParameterStructures.auto;s.ParameterStructures.is(n)&&(r=1,a=n);let c=t.length;const u=c-r;switch(u){case 0:o=void 0;break;case 1:o=fe(a,t[r]);break;default:if(a===s.ParameterStructures.byName)throw new Error(`Received ${u} parameters for 'by Name' notification parameter structure.`);o=t.slice(r,c).map((e=>le(e)))}}else{const n=t;i=e.method,o=ge(e,n)}const a={jsonrpc:O,method:i,params:o};return function(e){if($!==f.Off&&A)if(z===p.Text){let t;$!==f.Verbose&&$!==f.Compact||(t=e.params?`Params: ${se(e.params)}\n\n`:"No parameters provided.\n\n"),A.log(`Sending notification '${e.method}'.`,t)}else ce("send-notification",e)}(a),n.write(a).catch((e=>{throw w.error("Sending notification failed."),e}))},onNotification:(e,t)=>{let n;return ue(),r.func(e)?N=e:t&&(r.string(e)?(n=e,P.set(e,{type:void 0,handler:t})):(n=e.method,P.set(e.method,{type:e,handler:t}))),{dispose:()=>{void 0!==n?P.delete(n):N=void 0}}},onProgress:(e,t,n)=>{if(x.has(t))throw new Error(`Progress handler for token ${t} already registered`);return x.set(t,n),{dispose:()=>{x.delete(t)}}},sendProgress:(e,t,n)=>pe.sendNotification(d.type,{token:t,value:n}),onUnhandledProgress:J.event,sendRequest:(e,...t)=>{let i,o,a;if(ue(),function(){if(!Q())throw new Error("Call listen() first.")}(),r.string(e)){i=e;const n=t[0],r=t[t.length-1];let u=0,l=s.ParameterStructures.auto;s.ParameterStructures.is(n)&&(u=1,l=n);let d=t.length;c.CancellationToken.is(r)&&(d-=1,a=r);const h=d-u;switch(h){case 0:o=void 0;break;case 1:o=fe(l,t[u]);break;default:if(l===s.ParameterStructures.byName)throw new Error(`Received ${h} parameters for 'by Name' request parameter structure.`);o=t.slice(u,d).map((e=>le(e)))}}else{const n=t;i=e.method,o=ge(e,n);const r=e.numberOfParams;a=c.CancellationToken.is(n[r])?n[r]:void 0}const u=C++;let l;a&&(l=a.onCancellationRequested((()=>{const e=Y.sender.sendCancellation(pe,u);return void 0===e?(w.log(`Received no promise from cancellation strategy when cancelling id ${u}`),Promise.resolve()):e.catch((()=>{w.log(`Sending cancellation messages for id ${u} failed`)}))})));const d={jsonrpc:O,id:u,method:i,params:o};return function(e){if($!==f.Off&&A)if(z===p.Text){let t;$!==f.Verbose&&$!==f.Compact||!e.params||(t=`Params: ${se(e.params)}\n\n`),A.log(`Sending request '${e.method} - (${e.id})'.`,t)}else ce("send-request",e)}(d),"function"==typeof Y.sender.enableCancellation&&Y.sender.enableCancellation(d),new Promise((async(e,t)=>{const r={method:i,timerStart:Date.now(),resolve:t=>{e(t),Y.sender.cleanup(u),l?.dispose()},reject:e=>{t(e),Y.sender.cleanup(u),l?.dispose()}};try{await n.write(d),q.set(u,r)}catch(e){throw w.error("Sending request failed."),r.reject(new s.ResponseError(s.ErrorCodes.MessageWriteError,e.message?e.message:"Unknown reason")),e}}))},onRequest:(e,t)=>{ue();let n=null;return h.is(e)?(n=void 0,k=e):r.string(e)?(n=null,void 0!==t&&(n=e,L.set(e,{handler:t,type:void 0}))):void 0!==t&&(n=e.method,L.set(e.method,{type:e,handler:t})),{dispose:()=>{null!==n&&(void 0!==n?L.delete(n):k=void 0)}}},hasPendingResponse:()=>q.size>0,trace:async(e,t,n)=>{let i=!1,s=p.Text;void 0!==n&&(r.boolean(n)?i=n:(i=n.sendNotification||!1,s=n.traceFormat||p.Text)),$=e,z=s,A=$===f.Off?void 0:t,!i||X()||ee()||await pe.sendNotification(m.type,{value:f.toString(e)})},onError:W.event,onClose:V.event,onUnhandledNotification:H.event,onDispose:G.event,end:()=>{n.end()},dispose:()=>{if(ee())return;B=M.Disposed,G.fire(void 0);const t=new s.ResponseError(s.ErrorCodes.PendingResponseRejected,"Pending response rejected since connection got disposed");for(const e of q.values())e.reject(t);q=new Map,U=new Map,F=new Set,I=new o.LinkedMap,r.func(n.dispose)&&n.dispose(),r.func(e.dispose)&&e.dispose()},listen:()=>{ue(),function(){if(Q())throw new R(_.AlreadyListening,"Connection is already listening")}(),B=M.Listening,e.listen(re)},inspect:()=>{(0,i.default)().console.log("inspect")}};return pe.onNotification(v.type,(e=>{if($===f.Off||!A)return;const t=$===f.Verbose||$===f.Compact;A.log(e.message,t?e.verbose:void 0)})),pe.onNotification(d.type,(e=>{const t=x.get(e.token);t?t(e.value):J.fire(e)})),pe}},68159:(e,t)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.Disposable=void 0,function(e){e.create=function(e){return{dispose:e}}}(n||(t.Disposable=n={}))},345:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Emitter=t.Event=void 0;const i=n(48094);var r;!function(e){const t={dispose(){}};e.None=function(){return t}}(r||(t.Event=r={}));class s{add(e,t=null,n){this._callbacks||(this._callbacks=[],this._contexts=[]),this._callbacks.push(e),this._contexts.push(t),Array.isArray(n)&&n.push({dispose:()=>this.remove(e,t)})}remove(e,t=null){if(!this._callbacks)return;let n=!1;for(let i=0,r=this._callbacks.length;i<r;i++)if(this._callbacks[i]===e){if(this._contexts[i]===t)return this._callbacks.splice(i,1),void this._contexts.splice(i,1);n=!0}if(n)throw new Error("When adding a listener with a context, you should remove it with the same context")}invoke(...e){if(!this._callbacks)return[];const t=[],n=this._callbacks.slice(0),r=this._contexts.slice(0);for(let s=0,o=n.length;s<o;s++)try{t.push(n[s].apply(r[s],e))}catch(e){(0,i.default)().console.error(e)}return t}isEmpty(){return!this._callbacks||0===this._callbacks.length}dispose(){this._callbacks=void 0,this._contexts=void 0}}class o{constructor(e){this._options=e}get event(){return this._event||(this._event=(e,t,n)=>{this._callbacks||(this._callbacks=new s),this._options&&this._options.onFirstListenerAdd&&this._callbacks.isEmpty()&&this._options.onFirstListenerAdd(this),this._callbacks.add(e,t);const i={dispose:()=>{this._callbacks&&(this._callbacks.remove(e,t),i.dispose=o._noop,this._options&&this._options.onLastListenerRemove&&this._callbacks.isEmpty()&&this._options.onLastListenerRemove(this))}};return Array.isArray(n)&&n.push(i),i}),this._event}fire(e){this._callbacks&&this._callbacks.invoke.call(this._callbacks,e)}dispose(){this._callbacks&&(this._callbacks.dispose(),this._callbacks=void 0)}}t.Emitter=o,o._noop=function(){}},78472:(e,t)=>{"use strict";function n(e){return"string"==typeof e||e instanceof String}function i(e){return Array.isArray(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.stringArray=t.array=t.func=t.error=t.number=t.string=t.boolean=void 0,t.boolean=function(e){return!0===e||!1===e},t.string=n,t.number=function(e){return"number"==typeof e||e instanceof Number},t.error=function(e){return e instanceof Error},t.func=function(e){return"function"==typeof e},t.array=i,t.stringArray=function(e){return i(e)&&e.every((e=>n(e)))}},45164:(e,t)=>{"use strict";var n,i;Object.defineProperty(t,"__esModule",{value:!0}),t.LRUCache=t.LinkedMap=t.Touch=void 0,function(e){e.None=0,e.First=1,e.AsOld=e.First,e.Last=2,e.AsNew=e.Last}(i||(t.Touch=i={}));class r{constructor(){this[n]="LinkedMap",this._map=new Map,this._head=void 0,this._tail=void 0,this._size=0,this._state=0}clear(){this._map.clear(),this._head=void 0,this._tail=void 0,this._size=0,this._state++}isEmpty(){return!this._head&&!this._tail}get size(){return this._size}get first(){return this._head?.value}get last(){return this._tail?.value}has(e){return this._map.has(e)}get(e,t=i.None){const n=this._map.get(e);if(n)return t!==i.None&&this.touch(n,t),n.value}set(e,t,n=i.None){let r=this._map.get(e);if(r)r.value=t,n!==i.None&&this.touch(r,n);else{switch(r={key:e,value:t,next:void 0,previous:void 0},n){case i.None:this.addItemLast(r);break;case i.First:this.addItemFirst(r);break;case i.Last:default:this.addItemLast(r)}this._map.set(e,r),this._size++}return this}delete(e){return!!this.remove(e)}remove(e){const t=this._map.get(e);if(t)return this._map.delete(e),this.removeItem(t),this._size--,t.value}shift(){if(!this._head&&!this._tail)return;if(!this._head||!this._tail)throw new Error("Invalid list");const e=this._head;return this._map.delete(e.key),this.removeItem(e),this._size--,e.value}forEach(e,t){const n=this._state;let i=this._head;for(;i;){if(t?e.bind(t)(i.value,i.key,this):e(i.value,i.key,this),this._state!==n)throw new Error("LinkedMap got modified during iteration.");i=i.next}}keys(){const e=this._state;let t=this._head;const n={[Symbol.iterator]:()=>n,next:()=>{if(this._state!==e)throw new Error("LinkedMap got modified during iteration.");if(t){const e={value:t.key,done:!1};return t=t.next,e}return{value:void 0,done:!0}}};return n}values(){const e=this._state;let t=this._head;const n={[Symbol.iterator]:()=>n,next:()=>{if(this._state!==e)throw new Error("LinkedMap got modified during iteration.");if(t){const e={value:t.value,done:!1};return t=t.next,e}return{value:void 0,done:!0}}};return n}entries(){const e=this._state;let t=this._head;const n={[Symbol.iterator]:()=>n,next:()=>{if(this._state!==e)throw new Error("LinkedMap got modified during iteration.");if(t){const e={value:[t.key,t.value],done:!1};return t=t.next,e}return{value:void 0,done:!0}}};return n}[(n=Symbol.toStringTag,Symbol.iterator)](){return this.entries()}trimOld(e){if(e>=this.size)return;if(0===e)return void this.clear();let t=this._head,n=this.size;for(;t&&n>e;)this._map.delete(t.key),t=t.next,n--;this._head=t,this._size=n,t&&(t.previous=void 0),this._state++}addItemFirst(e){if(this._head||this._tail){if(!this._head)throw new Error("Invalid list");e.next=this._head,this._head.previous=e}else this._tail=e;this._head=e,this._state++}addItemLast(e){if(this._head||this._tail){if(!this._tail)throw new Error("Invalid list");e.previous=this._tail,this._tail.next=e}else this._head=e;this._tail=e,this._state++}removeItem(e){if(e===this._head&&e===this._tail)this._head=void 0,this._tail=void 0;else if(e===this._head){if(!e.next)throw new Error("Invalid list");e.next.previous=void 0,this._head=e.next}else if(e===this._tail){if(!e.previous)throw new Error("Invalid list");e.previous.next=void 0,this._tail=e.previous}else{const t=e.next,n=e.previous;if(!t||!n)throw new Error("Invalid list");t.previous=n,n.next=t}e.next=void 0,e.previous=void 0,this._state++}touch(e,t){if(!this._head||!this._tail)throw new Error("Invalid list");if(t===i.First||t===i.Last)if(t===i.First){if(e===this._head)return;const t=e.next,n=e.previous;e===this._tail?(n.next=void 0,this._tail=n):(t.previous=n,n.next=t),e.previous=void 0,e.next=this._head,this._head.previous=e,this._head=e,this._state++}else if(t===i.Last){if(e===this._tail)return;const t=e.next,n=e.previous;e===this._head?(t.previous=void 0,this._head=t):(t.previous=n,n.next=t),e.next=void 0,e.previous=this._tail,this._tail.next=e,this._tail=e,this._state++}}toJSON(){const e=[];return this.forEach(((t,n)=>{e.push([n,t])})),e}fromJSON(e){this.clear();for(const[t,n]of e)this.set(t,n)}}t.LinkedMap=r,t.LRUCache=class extends r{constructor(e,t=1){super(),this._limit=e,this._ratio=Math.min(Math.max(0,t),1)}get limit(){return this._limit}set limit(e){this._limit=e,this.checkTrim()}get ratio(){return this._ratio}set ratio(e){this._ratio=Math.min(Math.max(0,e),1),this.checkTrim()}get(e,t=i.AsNew){return super.get(e,t)}peek(e){return super.get(e,i.None)}set(e,t){return super.set(e,t,i.Last),this.checkTrim(),this}checkTrim(){this.size>this._limit&&this.trimOld(Math.round(this._limit*this._ratio))}}},15506:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AbstractMessageBuffer=void 0,t.AbstractMessageBuffer=class{constructor(e="utf-8"){this._encoding=e,this._chunks=[],this._totalLength=0}get encoding(){return this._encoding}append(e){const t="string"==typeof e?this.fromString(e,this._encoding):e;this._chunks.push(t),this._totalLength+=t.byteLength}tryReadHeaders(e=!1){if(0===this._chunks.length)return;let t=0,n=0,i=0,r=0;e:for(;n<this._chunks.length;){const e=this._chunks[n];for(i=0;i<e.length;){switch(e[i]){case 13:switch(t){case 0:t=1;break;case 2:t=3;break;default:t=0}break;case 10:switch(t){case 1:t=2;break;case 3:t=4,i++;break e;default:t=0}break;default:t=0}i++}r+=e.byteLength,n++}if(4!==t)return;const s=this._read(r+i),o=new Map,a=this.toString(s,"ascii").split("\r\n");if(a.length<2)return o;for(let t=0;t<a.length-2;t++){const n=a[t],i=n.indexOf(":");if(-1===i)throw new Error(`Message header must separate key and value using ':'\n${n}`);const r=n.substr(0,i),s=n.substr(i+1).trim();o.set(e?r.toLowerCase():r,s)}return o}tryReadBody(e){if(!(this._totalLength<e))return this._read(e)}get numberOfBytes(){return this._totalLength}_read(e){if(0===e)return this.emptyBuffer();if(e>this._totalLength)throw new Error("Cannot read so many bytes!");if(this._chunks[0].byteLength===e){const t=this._chunks[0];return this._chunks.shift(),this._totalLength-=e,this.asNative(t)}if(this._chunks[0].byteLength>e){const t=this._chunks[0],n=this.asNative(t,e);return this._chunks[0]=t.slice(e),this._totalLength-=e,n}const t=this.allocNative(e);let n=0;for(;e>0;){const i=this._chunks[0];if(i.byteLength>e){const r=i.slice(0,e);t.set(r,n),n+=e,this._chunks[0]=i.slice(e),this._totalLength-=e,e-=e}else t.set(i,n),n+=i.byteLength,this._chunks.shift(),this._totalLength-=i.byteLength,e-=i.byteLength}return t}}},84806:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ReadableStreamMessageReader=t.AbstractMessageReader=t.MessageReader=void 0;const i=n(48094),r=n(78472),s=n(345),o=n(86791);var a,c;!function(e){e.is=function(e){let t=e;return t&&r.func(t.listen)&&r.func(t.dispose)&&r.func(t.onError)&&r.func(t.onClose)&&r.func(t.onPartialMessage)}}(a||(t.MessageReader=a={}));class u{constructor(){this.errorEmitter=new s.Emitter,this.closeEmitter=new s.Emitter,this.partialMessageEmitter=new s.Emitter}dispose(){this.errorEmitter.dispose(),this.closeEmitter.dispose()}get onError(){return this.errorEmitter.event}fireError(e){this.errorEmitter.fire(this.asError(e))}get onClose(){return this.closeEmitter.event}fireClose(){this.closeEmitter.fire(void 0)}get onPartialMessage(){return this.partialMessageEmitter.event}firePartialMessage(e){this.partialMessageEmitter.fire(e)}asError(e){return e instanceof Error?e:new Error(`Reader received error. Reason: ${r.string(e.message)?e.message:"unknown"}`)}}t.AbstractMessageReader=u,function(e){e.fromOptions=function(e){let t,n;const r=new Map;let s;const o=new Map;if(void 0===e||"string"==typeof e)t=e??"utf-8";else{if(t=e.charset??"utf-8",void 0!==e.contentDecoder&&(n=e.contentDecoder,r.set(n.name,n)),void 0!==e.contentDecoders)for(const t of e.contentDecoders)r.set(t.name,t);if(void 0!==e.contentTypeDecoder&&(s=e.contentTypeDecoder,o.set(s.name,s)),void 0!==e.contentTypeDecoders)for(const t of e.contentTypeDecoders)o.set(t.name,t)}return void 0===s&&(s=(0,i.default)().applicationJson.decoder,o.set(s.name,s)),{charset:t,contentDecoder:n,contentDecoders:r,contentTypeDecoder:s,contentTypeDecoders:o}}}(c||(c={})),t.ReadableStreamMessageReader=class extends u{constructor(e,t){super(),this.readable=e,this.options=c.fromOptions(t),this.buffer=(0,i.default)().messageBuffer.create(this.options.charset),this._partialMessageTimeout=1e4,this.nextMessageLength=-1,this.messageToken=0,this.readSemaphore=new o.Semaphore(1)}set partialMessageTimeout(e){this._partialMessageTimeout=e}get partialMessageTimeout(){return this._partialMessageTimeout}listen(e){this.nextMessageLength=-1,this.messageToken=0,this.partialMessageTimer=void 0,this.callback=e;const t=this.readable.onData((e=>{this.onData(e)}));return this.readable.onError((e=>this.fireError(e))),this.readable.onClose((()=>this.fireClose())),t}onData(e){try{for(this.buffer.append(e);;){if(-1===this.nextMessageLength){const e=this.buffer.tryReadHeaders(!0);if(!e)return;const t=e.get("content-length");if(!t)return void this.fireError(new Error(`Header must provide a Content-Length property.\n${JSON.stringify(Object.fromEntries(e))}`));const n=parseInt(t);if(isNaN(n))return void this.fireError(new Error(`Content-Length value must be a number. Got ${t}`));this.nextMessageLength=n}const e=this.buffer.tryReadBody(this.nextMessageLength);if(void 0===e)return void this.setPartialMessageTimer();this.clearPartialMessageTimer(),this.nextMessageLength=-1,this.readSemaphore.lock((async()=>{const t=void 0!==this.options.contentDecoder?await this.options.contentDecoder.decode(e):e,n=await this.options.contentTypeDecoder.decode(t,this.options);this.callback(n)})).catch((e=>{this.fireError(e)}))}}catch(e){this.fireError(e)}}clearPartialMessageTimer(){this.partialMessageTimer&&(this.partialMessageTimer.dispose(),this.partialMessageTimer=void 0)}setPartialMessageTimer(){this.clearPartialMessageTimer(),this._partialMessageTimeout<=0||(this.partialMessageTimer=(0,i.default)().timer.setTimeout(((e,t)=>{this.partialMessageTimer=void 0,e===this.messageToken&&(this.firePartialMessage({messageToken:e,waitingTime:t}),this.setPartialMessageTimer())}),this._partialMessageTimeout,this.messageToken,this._partialMessageTimeout))}}},69248:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WriteableStreamMessageWriter=t.AbstractMessageWriter=t.MessageWriter=void 0;const i=n(48094),r=n(78472),s=n(86791),o=n(345);var a,c;!function(e){e.is=function(e){let t=e;return t&&r.func(t.dispose)&&r.func(t.onClose)&&r.func(t.onError)&&r.func(t.write)}}(a||(t.MessageWriter=a={}));class u{constructor(){this.errorEmitter=new o.Emitter,this.closeEmitter=new o.Emitter}dispose(){this.errorEmitter.dispose(),this.closeEmitter.dispose()}get onError(){return this.errorEmitter.event}fireError(e,t,n){this.errorEmitter.fire([this.asError(e),t,n])}get onClose(){return this.closeEmitter.event}fireClose(){this.closeEmitter.fire(void 0)}asError(e){return e instanceof Error?e:new Error(`Writer received error. Reason: ${r.string(e.message)?e.message:"unknown"}`)}}t.AbstractMessageWriter=u,function(e){e.fromOptions=function(e){return void 0===e||"string"==typeof e?{charset:e??"utf-8",contentTypeEncoder:(0,i.default)().applicationJson.encoder}:{charset:e.charset??"utf-8",contentEncoder:e.contentEncoder,contentTypeEncoder:e.contentTypeEncoder??(0,i.default)().applicationJson.encoder}}}(c||(c={})),t.WriteableStreamMessageWriter=class extends u{constructor(e,t){super(),this.writable=e,this.options=c.fromOptions(t),this.errorCount=0,this.writeSemaphore=new s.Semaphore(1),this.writable.onError((e=>this.fireError(e))),this.writable.onClose((()=>this.fireClose()))}async write(e){return this.writeSemaphore.lock((async()=>this.options.contentTypeEncoder.encode(e,this.options).then((e=>void 0!==this.options.contentEncoder?this.options.contentEncoder.encode(e):e)).then((t=>{const n=[];return n.push("Content-Length: ",t.byteLength.toString(),"\r\n"),n.push("\r\n"),this.doWrite(e,n,t)}),(e=>{throw this.fireError(e),e}))))}async doWrite(e,t,n){try{return await this.writable.write(t.join(""),"ascii"),this.writable.write(n)}catch(t){return this.handleError(t,e),Promise.reject(t)}}handleError(e,t){this.errorCount++,this.fireError(e,t,this.errorCount)}end(){this.writable.end()}}},75285:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Message=t.NotificationType9=t.NotificationType8=t.NotificationType7=t.NotificationType6=t.NotificationType5=t.NotificationType4=t.NotificationType3=t.NotificationType2=t.NotificationType1=t.NotificationType0=t.NotificationType=t.RequestType9=t.RequestType8=t.RequestType7=t.RequestType6=t.RequestType5=t.RequestType4=t.RequestType3=t.RequestType2=t.RequestType1=t.RequestType=t.RequestType0=t.AbstractMessageSignature=t.ParameterStructures=t.ResponseError=t.ErrorCodes=void 0;const i=n(78472);var r,s;!function(e){e.ParseError=-32700,e.InvalidRequest=-32600,e.MethodNotFound=-32601,e.InvalidParams=-32602,e.InternalError=-32603,e.jsonrpcReservedErrorRangeStart=-32099,e.serverErrorStart=-32099,e.MessageWriteError=-32099,e.MessageReadError=-32098,e.PendingResponseRejected=-32097,e.ConnectionInactive=-32096,e.ServerNotInitialized=-32002,e.UnknownErrorCode=-32001,e.jsonrpcReservedErrorRangeEnd=-32e3,e.serverErrorEnd=-32e3}(r||(t.ErrorCodes=r={}));class o extends Error{constructor(e,t,n){super(t),this.code=i.number(e)?e:r.UnknownErrorCode,this.data=n,Object.setPrototypeOf(this,o.prototype)}toJson(){const e={code:this.code,message:this.message};return void 0!==this.data&&(e.data=this.data),e}}t.ResponseError=o;class a{constructor(e){this.kind=e}static is(e){return e===a.auto||e===a.byName||e===a.byPosition}toString(){return this.kind}}t.ParameterStructures=a,a.auto=new a("auto"),a.byPosition=new a("byPosition"),a.byName=new a("byName");class c{constructor(e,t){this.method=e,this.numberOfParams=t}get parameterStructures(){return a.auto}}t.AbstractMessageSignature=c,t.RequestType0=class extends c{constructor(e){super(e,0)}},t.RequestType=class extends c{constructor(e,t=a.auto){super(e,1),this._parameterStructures=t}get parameterStructures(){return this._parameterStructures}},t.RequestType1=class extends c{constructor(e,t=a.auto){super(e,1),this._parameterStructures=t}get parameterStructures(){return this._parameterStructures}},t.RequestType2=class extends c{constructor(e){super(e,2)}},t.RequestType3=class extends c{constructor(e){super(e,3)}},t.RequestType4=class extends c{constructor(e){super(e,4)}},t.RequestType5=class extends c{constructor(e){super(e,5)}},t.RequestType6=class extends c{constructor(e){super(e,6)}},t.RequestType7=class extends c{constructor(e){super(e,7)}},t.RequestType8=class extends c{constructor(e){super(e,8)}},t.RequestType9=class extends c{constructor(e){super(e,9)}},t.NotificationType=class extends c{constructor(e,t=a.auto){super(e,1),this._parameterStructures=t}get parameterStructures(){return this._parameterStructures}},t.NotificationType0=class extends c{constructor(e){super(e,0)}},t.NotificationType1=class extends c{constructor(e,t=a.auto){super(e,1),this._parameterStructures=t}get parameterStructures(){return this._parameterStructures}},t.NotificationType2=class extends c{constructor(e){super(e,2)}},t.NotificationType3=class extends c{constructor(e){super(e,3)}},t.NotificationType4=class extends c{constructor(e){super(e,4)}},t.NotificationType5=class extends c{constructor(e){super(e,5)}},t.NotificationType6=class extends c{constructor(e){super(e,6)}},t.NotificationType7=class extends c{constructor(e){super(e,7)}},t.NotificationType8=class extends c{constructor(e){super(e,8)}},t.NotificationType9=class extends c{constructor(e){super(e,9)}},function(e){e.isRequest=function(e){const t=e;return t&&i.string(t.method)&&(i.string(t.id)||i.number(t.id))},e.isNotification=function(e){const t=e;return t&&i.string(t.method)&&void 0===e.id},e.isResponse=function(e){const t=e;return t&&(void 0!==t.result||!!t.error)&&(i.string(t.id)||i.number(t.id)||null===t.id)}}(s||(t.Message=s={}))},48094:(e,t)=>{"use strict";let n;function i(){if(void 0===n)throw new Error("No runtime abstraction layer installed");return n}Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.install=function(e){if(void 0===e)throw new Error("No runtime abstraction layer provided");n=e}}(i||(i={})),t.default=i},86791:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Semaphore=void 0;const i=n(48094);t.Semaphore=class{constructor(e=1){if(e<=0)throw new Error("Capacity must be greater than 0");this._capacity=e,this._active=0,this._waiting=[]}lock(e){return new Promise(((t,n)=>{this._waiting.push({thunk:e,resolve:t,reject:n}),this.runNext()}))}get active(){return this._active}runNext(){0!==this._waiting.length&&this._active!==this._capacity&&(0,i.default)().timer.setImmediate((()=>this.doRunNext()))}doRunNext(){if(0===this._waiting.length||this._active===this._capacity)return;const e=this._waiting.shift();if(this._active++,this._active>this._capacity)throw new Error("To many thunks active");try{const t=e.thunk();t instanceof Promise?t.then((t=>{this._active--,e.resolve(t),this.runNext()}),(t=>{this._active--,e.reject(t),this.runNext()})):(this._active--,e.resolve(t),this.runNext())}catch(t){this._active--,e.reject(t),this.runNext()}}}},50278:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SharedArrayReceiverStrategy=t.SharedArraySenderStrategy=void 0;const i=n(97770);var r;!function(e){e.Continue=0,e.Cancelled=1}(r||(r={})),t.SharedArraySenderStrategy=class{constructor(){this.buffers=new Map}enableCancellation(e){if(null===e.id)return;const t=new SharedArrayBuffer(4);new Int32Array(t,0,1)[0]=r.Continue,this.buffers.set(e.id,t),e.$cancellationData=t}async sendCancellation(e,t){const n=this.buffers.get(t);if(void 0===n)return;const i=new Int32Array(n,0,1);Atomics.store(i,0,r.Cancelled)}cleanup(e){this.buffers.delete(e)}dispose(){this.buffers.clear()}};class s{constructor(e){this.data=new Int32Array(e,0,1)}get isCancellationRequested(){return Atomics.load(this.data,0)===r.Cancelled}get onCancellationRequested(){throw new Error("Cancellation over SharedArrayBuffer doesn't support cancellation events")}}class o{constructor(e){this.token=new s(e)}cancel(){}dispose(){}}t.SharedArrayReceiverStrategy=class{constructor(){this.kind="request"}createCancellationTokenSource(e){const t=e.$cancellationData;return void 0===t?new i.CancellationTokenSource:new o(t)}}}}]);
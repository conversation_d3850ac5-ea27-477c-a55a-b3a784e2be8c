"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[5614],{15614:(t,e,n)=>{var r,o,i;function*f(...t){for(const e of t)yield*e}function*l(){}function*u(t,e=0){for(const n of t)yield[e++,n]}function*a(t,e){let n=0;for(const r of t)e(r,n++)&&(yield r)}function h(t,e){let n=0;for(const r of t)if(e(r,n++))return r}function c(t,e){let n=0;for(const r of t)if(e(r,n++))return n-1;return-1}function m(t,e){let n;for(const r of t)void 0!==n?e(r,n)<0&&(n=r):n=r;return n}function s(t,e){let n;for(const r of t)void 0!==n?e(r,n)>0&&(n=r):n=r;return n}function d(t,e){let n,r,o=!0;for(const i of t)o?(n=i,r=i,o=!1):e(i,n)<0?n=i:e(i,r)>0&&(r=i);return o?void 0:[n,r]}function M(t){return Array.from(t)}function x(t){const e={};for(const[n,r]of t)e[n]=r;return e}function g(t,e){let n=0;for(const r of t)if(!1===e(r,n++))return}function v(t,e){let n=0;for(const r of t)if(!1===e(r,n++))return!1;return!0}function p(t,e){let n=0;for(const r of t)if(e(r,n++))return!0;return!1}function*y(t,e){let n=0;for(const r of t)yield e(r,n++)}function*O(t,e,n){void 0===e?(e=t,t=0,n=1):void 0===n&&(n=1);const r=o.rangeLength(t,e,n);for(let e=0;e<r;e++)yield t+n*e}function w(t,e,n){const r=t[Symbol.iterator]();let o=0,i=r.next();if(i.done&&void 0===n)throw new TypeError("Reduce of empty iterable with no initial value.");if(i.done)return n;let f,l,u=r.next();if(u.done&&void 0===n)return i.value;if(u.done)return e(n,i.value,o++);for(f=e(void 0===n?i.value:e(n,i.value,o++),u.value,o++);!(l=r.next()).done;)f=e(f,l.value,o++);return f}function*S(t,e){for(;0<e--;)yield t}function*A(t){yield t}function*E(t){if("function"==typeof t.retro)yield*t.retro();else for(let e=t.length-1;e>-1;e--)yield t[e]}function b(t){let e=[],n=new Set,r=new Map;for(const e of t)o(e);for(const[t]of r)i(t);return e;function o(t){let[e,n]=t,o=r.get(n);o?o.push(e):r.set(n,[e])}function i(t){if(n.has(t))return;n.add(t);let o=r.get(t);if(o)for(const t of o)i(t);e.push(t)}}function*L(t,e){let n=0;for(const r of t)0==n++%e&&(yield r)}function*T(t,e){if(e<1)return;const n=t[Symbol.iterator]();let r;for(;0<e--&&!(r=n.next()).done;)yield r.value}function*I(...t){const e=t.map((t=>t[Symbol.iterator]()));let n=e.map((t=>t.next()));for(;v(n,(t=>!t.done));n=e.map((t=>t.next())))yield n.map((t=>t.value))}n.r(e),n.d(e,{ArrayExt:()=>r,StringExt:()=>i,chain:()=>f,each:()=>g,empty:()=>l,enumerate:()=>u,every:()=>v,filter:()=>a,find:()=>h,findIndex:()=>c,map:()=>y,max:()=>s,min:()=>m,minmax:()=>d,once:()=>A,range:()=>O,reduce:()=>w,repeat:()=>S,retro:()=>E,some:()=>p,stride:()=>L,take:()=>T,toArray:()=>M,toObject:()=>x,topologicSort:()=>b,zip:()=>I}),function(t){function e(t,e,n=0,r=-1){let o,i=t.length;if(0===i)return-1;n=n<0?Math.max(0,n+i):Math.min(n,i-1),o=(r=r<0?Math.max(0,r+i):Math.min(r,i-1))<n?r+1+(i-n):r-n+1;for(let r=0;r<o;++r){let o=(n+r)%i;if(t[o]===e)return o}return-1}function n(t,e,n=-1,r=0){let o,i=t.length;if(0===i)return-1;o=(n=n<0?Math.max(0,n+i):Math.min(n,i-1))<(r=r<0?Math.max(0,r+i):Math.min(r,i-1))?n+1+(i-r):n-r+1;for(let r=0;r<o;++r){let o=(n-r+i)%i;if(t[o]===e)return o}return-1}function r(t,e,n=0,r=-1){let o,i=t.length;if(0===i)return-1;n=n<0?Math.max(0,n+i):Math.min(n,i-1),o=(r=r<0?Math.max(0,r+i):Math.min(r,i-1))<n?r+1+(i-n):r-n+1;for(let r=0;r<o;++r){let o=(n+r)%i;if(e(t[o],o))return o}return-1}function o(t,e,n=-1,r=0){let o,i=t.length;if(0===i)return-1;o=(n=n<0?Math.max(0,n+i):Math.min(n,i-1))<(r=r<0?Math.max(0,r+i):Math.min(r,i-1))?n+1+(i-r):n-r+1;for(let r=0;r<o;++r){let o=(n-r+i)%i;if(e(t[o],o))return o}return-1}function i(t,e=0,n=-1){let r=t.length;if(!(r<=1))for(e=e<0?Math.max(0,e+r):Math.min(e,r-1),n=n<0?Math.max(0,n+r):Math.min(n,r-1);e<n;){let r=t[e],o=t[n];t[e++]=o,t[n--]=r}}function f(t,e){let n=t.length;if(e<0&&(e+=n),e<0||e>=n)return;let r=t[e];for(let r=e+1;r<n;++r)t[r-1]=t[r];return t.length=n-1,r}t.firstIndexOf=e,t.lastIndexOf=n,t.findFirstIndex=r,t.findLastIndex=o,t.findFirstValue=function(t,e,n=0,o=-1){let i=r(t,e,n,o);return-1!==i?t[i]:void 0},t.findLastValue=function(t,e,n=-1,r=0){let i=o(t,e,n,r);return-1!==i?t[i]:void 0},t.lowerBound=function(t,e,n,r=0,o=-1){let i=t.length;if(0===i)return 0;let f=r=r<0?Math.max(0,r+i):Math.min(r,i-1),l=(o=o<0?Math.max(0,o+i):Math.min(o,i-1))-r+1;for(;l>0;){let r=l>>1,o=f+r;n(t[o],e)<0?(f=o+1,l-=r+1):l=r}return f},t.upperBound=function(t,e,n,r=0,o=-1){let i=t.length;if(0===i)return 0;let f=r=r<0?Math.max(0,r+i):Math.min(r,i-1),l=(o=o<0?Math.max(0,o+i):Math.min(o,i-1))-r+1;for(;l>0;){let r=l>>1,o=f+r;n(t[o],e)>0?l=r:(f=o+1,l-=r+1)}return f},t.shallowEqual=function(t,e,n){if(t===e)return!0;if(t.length!==e.length)return!1;for(let r=0,o=t.length;r<o;++r)if(n?!n(t[r],e[r]):t[r]!==e[r])return!1;return!0},t.slice=function(t,e={}){let{start:n,stop:r,step:o}=e;if(void 0===o&&(o=1),0===o)throw new Error("Slice `step` cannot be zero.");let i,f=t.length;void 0===n?n=o<0?f-1:0:n<0?n=Math.max(n+f,o<0?-1:0):n>=f&&(n=o<0?f-1:f),void 0===r?r=o<0?-1:f:r<0?r=Math.max(r+f,o<0?-1:0):r>=f&&(r=o<0?f-1:f),i=o<0&&r>=n||o>0&&n>=r?0:o<0?Math.floor((r-n+1)/o+1):Math.floor((r-n-1)/o+1);let l=[];for(let e=0;e<i;++e)l[e]=t[n+e*o];return l},t.move=function(t,e,n){let r=t.length;if(r<=1)return;if((e=e<0?Math.max(0,e+r):Math.min(e,r-1))===(n=n<0?Math.max(0,n+r):Math.min(n,r-1)))return;let o=t[e],i=e<n?1:-1;for(let r=e;r!==n;r+=i)t[r]=t[r+i];t[n]=o},t.reverse=i,t.rotate=function(t,e,n=0,r=-1){let o=t.length;if(o<=1)return;if((n=n<0?Math.max(0,n+o):Math.min(n,o-1))>=(r=r<0?Math.max(0,r+o):Math.min(r,o-1)))return;let f=r-n+1;if(e>0?e%=f:e<0&&(e=(e%f+f)%f),0===e)return;let l=n+e;i(t,n,l-1),i(t,l,r),i(t,n,r)},t.fill=function(t,e,n=0,r=-1){let o,i=t.length;if(0!==i){n=n<0?Math.max(0,n+i):Math.min(n,i-1),o=(r=r<0?Math.max(0,r+i):Math.min(r,i-1))<n?r+1+(i-n):r-n+1;for(let r=0;r<o;++r)t[(n+r)%i]=e}},t.insert=function(t,e,n){let r=t.length;e=e<0?Math.max(0,e+r):Math.min(e,r);for(let n=r;n>e;--n)t[n]=t[n-1];t[e]=n},t.removeAt=f,t.removeFirstOf=function(t,n,r=0,o=-1){let i=e(t,n,r,o);return-1!==i&&f(t,i),i},t.removeLastOf=function(t,e,r=-1,o=0){let i=n(t,e,r,o);return-1!==i&&f(t,i),i},t.removeAllOf=function(t,e,n=0,r=-1){let o=t.length;if(0===o)return 0;n=n<0?Math.max(0,n+o):Math.min(n,o-1),r=r<0?Math.max(0,r+o):Math.min(r,o-1);let i=0;for(let f=0;f<o;++f)n<=r&&f>=n&&f<=r&&t[f]===e||r<n&&(f<=r||f>=n)&&t[f]===e?i++:i>0&&(t[f-i]=t[f]);return i>0&&(t.length=o-i),i},t.removeFirstWhere=function(t,e,n=0,o=-1){let i,l=r(t,e,n,o);return-1!==l&&(i=f(t,l)),{index:l,value:i}},t.removeLastWhere=function(t,e,n=-1,r=0){let i,l=o(t,e,n,r);return-1!==l&&(i=f(t,l)),{index:l,value:i}},t.removeAllWhere=function(t,e,n=0,r=-1){let o=t.length;if(0===o)return 0;n=n<0?Math.max(0,n+o):Math.min(n,o-1),r=r<0?Math.max(0,r+o):Math.min(r,o-1);let i=0;for(let f=0;f<o;++f)n<=r&&f>=n&&f<=r&&e(t[f],f)||r<n&&(f<=r||f>=n)&&e(t[f],f)?i++:i>0&&(t[f-i]=t[f]);return i>0&&(t.length=o-i),i}}(r||(r={})),function(t){t.rangeLength=function(t,e,n){return 0===n?1/0:t>e&&n>0||t<e&&n<0?0:Math.ceil((e-t)/n)}}(o||(o={})),function(t){function e(t,e,n=0){let r=new Array(e.length);for(let o=0,i=n,f=e.length;o<f;++o,++i){if(i=t.indexOf(e[o],i),-1===i)return null;r[o]=i}return r}t.findIndices=e,t.matchSumOfSquares=function(t,n,r=0){let o=e(t,n,r);if(!o)return null;let i=0;for(let t=0,e=o.length;t<e;++t){let e=o[t]-r;i+=e*e}return{score:i,indices:o}},t.matchSumOfDeltas=function(t,n,r=0){let o=e(t,n,r);if(!o)return null;let i=0,f=r-1;for(let t=0,e=o.length;t<e;++t){let e=o[t];i+=e-f-1,f=e}return{score:i,indices:o}},t.highlight=function(t,e,n){let r=[],o=0,i=0,f=e.length;for(;o<f;){let l=e[o],u=e[o];for(;++o<f&&e[o]===u+1;)u++;i<l&&r.push(t.slice(i,l)),l<u+1&&r.push(n(t.slice(l,u+1))),i=u+1}return i<t.length&&r.push(t.slice(i)),r},t.cmp=function(t,e){return t<e?-1:t>e?1:0}}(i||(i={}))}}]);
"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[1809,1337],{1809:(n,e,t)=>{t.r(e),t.d(e,{IRunningSessionManagers:()=>p,RunningSessionManagers:()=>b,RunningSessions:()=>E});var s=t(12982),a=t(71677),i=t(68239),o=t(20998),l=t(2549),r=t(81997),d=t(78156);const u="jp-RunningSessions-sectionList",c="jp-RunningSessions-item",h="jp-RunningSessions-itemLabel",m="jp-RunningSessions-itemDetail",g="jp-RunningSessions-itemShutdown",p=new o.Token("@jupyterlab/running:IRunningSessionManagers","A service to add running session managers.");class b{constructor(){this._added=new r.Signal(this),this._managers=[]}get added(){return this._added}add(n){return this._managers.push(n),this._added.emit(n),new l.DisposableDelegate((()=>{const e=this._managers.indexOf(n);e>-1&&this._managers.splice(e,1)}))}items(){return this._managers}}function _(n){var e,t;const{runningItem:s}=n,o=[c],l=null===(e=s.detail)||void 0===e?void 0:e.call(s),r=s.icon(),u=s.labelTitle?s.labelTitle():"",p=n.translator||a.nullTranslator,b=p.load("jupyterlab");let _=!1;const I=n.shutdownItemIcon||i.closeIcon,S=n.shutdownLabel||b.__("Shut Down"),[E,R]=d.useState(!1),j=!!(null===(t=s.children)||void 0===t?void 0:t.length),f=j?()=>!_&&R(!E):void 0;return s.className&&o.push(s.className),n.child&&o.push("jp-mod-running-child"),d.createElement(d.Fragment,null,d.createElement("li",null,d.createElement("div",{className:o.join(" "),onClick:f,"data-context":s.context||""},j&&(E?d.createElement(i.caretRightIcon.react,{tag:"span",stylesheet:"runningItem"}):d.createElement(i.caretDownIcon.react,{tag:"span",stylesheet:"runningItem"})),r?"string"==typeof r?d.createElement("img",{src:r}):d.createElement(r.react,{tag:"span",stylesheet:"runningItem"}):void 0,d.createElement("span",{className:h,title:u,onClick:s.open&&(()=>s.open())},s.label()),l&&d.createElement("span",{className:m},l),s.shutdown&&d.createElement(i.ToolbarButtonComponent,{className:g,icon:I,onClick:()=>{var n;_=!0,null===(n=s.shutdown)||void 0===n||n.call(s)},tooltip:S})),j&&!E&&d.createElement(w,{child:!0,runningItems:s.children,shutdownItemIcon:I,translator:p})))}function w(n){return d.createElement("ul",{className:u},n.runningItems.map(((e,t)=>d.createElement(_,{child:n.child,key:t,runningItem:e,shutdownLabel:n.shutdownLabel,shutdownItemIcon:n.shutdownItemIcon,translator:n.translator}))))}class I extends i.ReactWidget{constructor(n){super(),this._options=n,this._update=new r.Signal(this),n.manager.runningChanged.connect(this._emitUpdate,this)}dispose(){this._options.manager.runningChanged.disconnect(this._emitUpdate,this),super.dispose()}onBeforeShow(n){super.onBeforeShow(n),this._update.emit()}render(){const n=this._options;let e=!0;return d.createElement(i.UseSignal,{signal:this._update},(()=>(e?e=!1:n.runningItems=n.manager.running(),d.createElement("div",{className:"jp-RunningSessions-sectionContainer"},d.createElement(w,{runningItems:n.runningItems,shutdownLabel:n.manager.shutdownLabel,shutdownAllLabel:n.shutdownAllLabel,shutdownItemIcon:n.manager.shutdownItemIcon,translator:n.translator})))))}_isAnyHidden(){let n=this.isHidden;if(n)return n;let e=this.parent;for(;null!=e;){if(e.isHidden){n=!0;break}e=e.parent}return n}_emitUpdate(){this._isAnyHidden()||this._update.emit()}}class S extends i.PanelWithToolbar{constructor(n){super(),this._manager=n.manager;const e=(n.translator||a.nullTranslator).load("jupyterlab"),t=n.manager.shutdownAllLabel||e.__("Shut Down All"),o=`${t}?`,l=n.manager.shutdownAllConfirmationText||`${t} ${n.manager.name}`;this.addClass("jp-RunningSessions-section"),this.title.label=n.manager.name;let r=n.manager.running();const d=r.length>0;this._button=new i.ToolbarButton({label:t,className:"jp-RunningSessions-shutdownAll"+(d?"":" jp-mod-disabled"),enabled:d,onClick:function(){(0,s.showDialog)({title:o,body:l,buttons:[s.Dialog.cancelButton(),s.Dialog.warnButton({label:t})]}).then((e=>{e.button.accept&&n.manager.shutdownAll()}))}}),this._manager.runningChanged.connect(this._updateButton,this),this.toolbar.addItem("shutdown-all",this._button),this.addWidget(new I({runningItems:r,shutdownAllLabel:t,...n}))}dispose(){this.isDisposed||(this._manager.runningChanged.disconnect(this._updateButton,this),super.dispose())}_updateButton(){var n,e;const t=this._button;t.enabled=this._manager.running().length>0,t.enabled?null===(n=t.node.querySelector("jp-button"))||void 0===n||n.classList.remove("jp-mod-disabled"):null===(e=t.node.querySelector("jp-button"))||void 0===e||e.classList.add("jp-mod-disabled")}}class E extends i.SidePanel{constructor(n,e){super(),this.managers=n,this.translator=null!=e?e:a.nullTranslator;const t=this.translator.load("jupyterlab");this.addClass("jp-RunningSessions"),this.toolbar.addItem("refresh",new i.ToolbarButton({tooltip:t.__("Refresh List"),icon:i.refreshIcon,onClick:()=>n.items().forEach((n=>n.refreshRunning()))})),n.items().forEach((e=>this.addSection(n,e))),n.added.connect(this.addSection,this)}dispose(){this.isDisposed||(this.managers.added.disconnect(this.addSection,this),super.dispose())}addSection(n,e){this.addWidget(new S({manager:e,translator:this.translator}))}}}}]);
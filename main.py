import torch
from config import Config
from data.dataset import ArcDataset
from data.tokenizer import GrammarTokenizer
from models.hrm_model import GridToProgramHRM
from engine.trainer import train_hrm
from torch.utils.data import DataLoader

def main():

    # Activer TF32 pour les calculs matriciels (3x plus rapide sur Ampere)
    torch.backends.cuda.matmul.allow_tf32 = True
    torch.backends.cudnn.allow_tf32 = True

    # Configuration mémoire
    torch.cuda.set_per_process_memory_fraction(0.9)  # Éviter OOM
    torch.cuda.empty_cache()  # Nettoyer avant le training

    # Optimiser les kernels cuDNN
    torch.backends.cudnn.benchmark = True
    torch.backends.cudnn.deterministic = False  # Meilleures perfs

    print(f"[CONFIG] GPU: {torch.cuda.get_device_name(0)}")
    print(f"[CONFIG] Compute Capability: {torch.cuda.get_device_capability()}")
    print(f"[CONFIG] Mem: {torch.cuda.get_device_properties(0).total_memory/1e9:.2f} GB")


    cfg = Config()
    
    # Initialisation
    tokenizer = GrammarTokenizer()
    dataset = ArcDataset(cfg.DATA_DIR, cfg.MAX_PROG_LEN)
    dataloader = DataLoader(dataset, batch_size=cfg.BATCH_SIZE, shuffle=True)
    
    model = GridToProgramHRM(
        model_dim=cfg.MODEL_DIM,
        n_heads=cfg.N_HEADS,
        grammar_vocab_size=len(tokenizer.vocab),
        N_cycles=cfg.N_CYCLES,
        T_steps=cfg.T_STEPS
    )
    
    # Entraînement
    train_hrm(model, dataloader, cfg.EPOCHS, cfg.DEVICE)
    
    # Validation
    test_sample = dataset[0]
    test_grid = test_sample[0].unsqueeze(0).to(cfg.DEVICE)
    # [Ajouter la validation...]

if __name__ == "__main__":
    main()
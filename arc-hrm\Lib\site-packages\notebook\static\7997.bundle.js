"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[7997],{97997:(e,t,r)=>{r.r(t),r.d(t,{DefaultBufferLength:()=>n,IterMode:()=>d,MountedTree:()=>h,NodeProp:()=>o,NodeSet:()=>u,NodeType:()=>f,NodeWeakMap:()=>P,Parser:()=>z,Tree:()=>c,TreeBuffer:()=>m,TreeCursor:()=>N,TreeFragment:()=>E,parseMixed:()=>F});const n=1024;let i=0;class s{constructor(e,t){this.from=e,this.to=t}}class o{constructor(e={}){this.id=i++,this.perNode=!!e.perNode,this.deserialize=e.deserialize||(()=>{throw new Error("This node type doesn't define a deserialize function")})}add(e){if(this.perNode)throw new RangeError("Can't add per-node props to node types");return"function"!=typeof e&&(e=f.match(e)),t=>{let r=e(t);return void 0===r?null:[this,r]}}}o.closedBy=new o({deserialize:e=>e.split(" ")}),o.openedBy=new o({deserialize:e=>e.split(" ")}),o.group=new o({deserialize:e=>e.split(" ")}),o.contextHash=new o({perNode:!0}),o.lookAhead=new o({perNode:!0}),o.mounted=new o({perNode:!0});class h{constructor(e,t,r){this.tree=e,this.overlay=t,this.parser=r}static get(e){return e&&e.props&&e.props[o.mounted.id]}}const l=Object.create(null);class f{constructor(e,t,r,n=0){this.name=e,this.props=t,this.id=r,this.flags=n}static define(e){let t=e.props&&e.props.length?Object.create(null):l,r=(e.top?1:0)|(e.skipped?2:0)|(e.error?4:0)|(null==e.name?8:0),n=new f(e.name||"",t,e.id,r);if(e.props)for(let r of e.props)if(Array.isArray(r)||(r=r(n)),r){if(r[0].perNode)throw new RangeError("Can't store a per-node prop on a node type");t[r[0].id]=r[1]}return n}prop(e){return this.props[e.id]}get isTop(){return(1&this.flags)>0}get isSkipped(){return(2&this.flags)>0}get isError(){return(4&this.flags)>0}get isAnonymous(){return(8&this.flags)>0}is(e){if("string"==typeof e){if(this.name==e)return!0;let t=this.prop(o.group);return!!t&&t.indexOf(e)>-1}return this.id==e}static match(e){let t=Object.create(null);for(let r in e)for(let n of r.split(" "))t[n]=e[r];return e=>{for(let r=e.prop(o.group),n=-1;n<(r?r.length:0);n++){let i=t[n<0?e.name:r[n]];if(i)return i}}}}f.none=new f("",Object.create(null),0,8);class u{constructor(e){this.types=e;for(let t=0;t<e.length;t++)if(e[t].id!=t)throw new RangeError("Node type ids should correspond to array positions when creating a node set")}extend(...e){let t=[];for(let r of this.types){let n=null;for(let t of e){let e=t(r);e&&(n||(n=Object.assign({},r.props)),n[e[0].id]=e[1])}t.push(n?new f(r.name,n,r.id,r.flags):r)}return new u(t)}}const a=new WeakMap,p=new WeakMap;var d;!function(e){e[e.ExcludeBuffers=1]="ExcludeBuffers",e[e.IncludeAnonymous=2]="IncludeAnonymous",e[e.IgnoreMounts=4]="IgnoreMounts",e[e.IgnoreOverlays=8]="IgnoreOverlays"}(d||(d={}));class c{constructor(e,t,r,n,i){if(this.type=e,this.children=t,this.positions=r,this.length=n,this.props=null,i&&i.length){this.props=Object.create(null);for(let[e,t]of i)this.props["number"==typeof e?e:e.id]=t}}toString(){let e=h.get(this);if(e&&!e.overlay)return e.tree.toString();let t="";for(let e of this.children){let r=e.toString();r&&(t&&(t+=","),t+=r)}return this.type.name?(/\W/.test(this.type.name)&&!this.type.isError?JSON.stringify(this.type.name):this.type.name)+(t.length?"("+t+")":""):t}cursor(e=0){return new N(this.topNode,e)}cursorAt(e,t=0,r=0){let n=a.get(this)||this.topNode,i=new N(n);return i.moveTo(e,t),a.set(this,i._tree),i}get topNode(){return new w(this,0,0,null)}resolve(e,t=0){let r=b(a.get(this)||this.topNode,e,t,!1);return a.set(this,r),r}resolveInner(e,t=0){let r=b(p.get(this)||this.topNode,e,t,!0);return p.set(this,r),r}resolveStack(e,t=0){return function(e,t,r){let n=e.resolveInner(t,r),i=null;for(let e=n instanceof w?n:n.context.parent;e;e=e.parent)if(e.index<0){let s=e.parent;(i||(i=[n])).push(s.resolve(t,r)),e=s}else{let s=h.get(e.tree);if(s&&s.overlay&&s.overlay[0].from<=t&&s.overlay[s.overlay.length-1].to>=t){let o=new w(s.tree,s.overlay[0].from+e.from,0,null);(i||(i=[n])).push(b(o,t,r,!1))}}return i?C(i):n}(this,e,t)}iterate(e){let{enter:t,leave:r,from:n=0,to:i=this.length}=e,s=e.mode||0,o=(s&d.IncludeAnonymous)>0;for(let e=this.cursor(s|d.IncludeAnonymous);;){let s=!1;if(e.from<=i&&e.to>=n&&(!o&&e.type.isAnonymous||!1!==t(e))){if(e.firstChild())continue;s=!0}for(;s&&r&&(o||!e.type.isAnonymous)&&r(e),!e.nextSibling();){if(!e.parent())return;s=!0}}}prop(e){return e.perNode?this.props?this.props[e.id]:void 0:this.type.prop(e)}get propValues(){let e=[];if(this.props)for(let t in this.props)e.push([+t,this.props[t]]);return e}balance(e={}){return this.children.length<=8?this:M(f.none,this.children,this.positions,0,this.children.length,0,this.length,((e,t,r)=>new c(this.type,e,t,r,this.propValues)),e.makeTree||((e,t,r)=>new c(f.none,e,t,r)))}static build(e){return function(e){var t;let{buffer:r,nodeSet:i,maxBufferLength:s=n,reused:h=[],minRepeatType:l=i.types.length}=e,f=Array.isArray(r)?new g(r,r.length):r,u=i.types,a=0,p=0;function d(e,t,r,n,g){let{id:w,start:v,end:_,size:A}=f,k=p;for(;A<0;){if(f.next(),-1==A){let t=h[w];return r.push(t),void n.push(v-e)}if(-3==A)return void(a=w);if(-4==A)return void(p=w);throw new RangeError(`Unrecognized record size: ${A}`)}let C,S,N=u[w],I=v-e;if(_-v<=s&&(S=function(e,t){let r=f.fork(),n=0,i=0,o=0,h=r.end-s,u={size:0,start:0,skip:0};e:for(let s=r.pos-e;r.pos>s;){let e=r.size;if(r.id==t&&e>=0){u.size=n,u.start=i,u.skip=o,o+=4,n+=4,r.next();continue}let f=r.pos-e;if(e<0||f<s||r.start<h)break;let a=r.id>=l?4:0,p=r.start;for(r.next();r.pos>f;){if(r.size<0){if(-3!=r.size)break e;a+=4}else r.id>=l&&(a+=4);r.next()}i=p,n+=e,o+=a}return(t<0||n==e)&&(u.size=n,u.start=i,u.skip=o),u.size>4?u:void 0}(f.pos-t,g))){let t=new Uint16Array(S.size-S.skip),r=f.pos-S.size,n=t.length;for(;f.pos>r;)n=y(S.start,t,n);C=new m(t,_-S.start,i),I=S.start-e}else{let e=f.pos-A;f.next();let t=[],r=[],n=w>=l?w:-1,i=0,h=_;for(;f.pos>e;)n>=0&&f.id==n&&f.size>=0?(f.end<=h-s&&(x(t,r,v,i,f.end,h,n,k),i=t.length,h=f.end),f.next()):d(v,e,t,r,n);if(n>=0&&i>0&&i<t.length&&x(t,r,v,i,v,h,n,k),t.reverse(),r.reverse(),n>-1&&i>0){let e=function(e){return(t,r,n)=>{let i,s,h=0,l=t.length-1;if(l>=0&&(i=t[l])instanceof c){if(!l&&i.type==e&&i.length==n)return i;(s=i.prop(o.lookAhead))&&(h=r[l]+i.length+s)}return b(e,t,r,n,h)}}(N);C=M(N,t,r,0,t.length,0,_-v,e,e)}else C=b(N,t,r,_-v,k-_)}r.push(C),n.push(I)}function x(e,t,r,n,s,o,h,l){let f=[],u=[];for(;e.length>n;)f.push(e.pop()),u.push(t.pop()+r-s);e.push(b(i.types[h],f,u,o-s,l-o)),t.push(s-r)}function b(e,t,r,n,i=0,s){if(a){let e=[o.contextHash,a];s=s?[e].concat(s):[e]}if(i>25){let e=[o.lookAhead,i];s=s?[e].concat(s):[e]}return new c(e,t,r,n,s)}function y(e,t,r){let{id:n,start:i,end:s,size:o}=f;if(f.next(),o>=0&&n<l){let h=r;if(o>4){let n=f.pos-(o-4);for(;f.pos>n;)r=y(e,t,r)}t[--r]=h,t[--r]=s-e,t[--r]=i-e,t[--r]=n}else-3==o?a=n:-4==o&&(p=n);return r}let w=[],v=[];for(;f.pos>0;)d(e.start||0,e.bufferStart||0,w,v,-1);let _=null!==(t=e.length)&&void 0!==t?t:w.length?v[0]+w[0].length:0;return new c(u[e.topID],w.reverse(),v.reverse(),_)}(e)}}c.empty=new c(f.none,[],[],0);class g{constructor(e,t){this.buffer=e,this.index=t}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}get pos(){return this.index}next(){this.index-=4}fork(){return new g(this.buffer,this.index)}}class m{constructor(e,t,r){this.buffer=e,this.length=t,this.set=r}get type(){return f.none}toString(){let e=[];for(let t=0;t<this.buffer.length;)e.push(this.childString(t)),t=this.buffer[t+3];return e.join(",")}childString(e){let t=this.buffer[e],r=this.buffer[e+3],n=this.set.types[t],i=n.name;if(/\W/.test(i)&&!n.isError&&(i=JSON.stringify(i)),r==(e+=4))return i;let s=[];for(;e<r;)s.push(this.childString(e)),e=this.buffer[e+3];return i+"("+s.join(",")+")"}findChild(e,t,r,n,i){let{buffer:s}=this,o=-1;for(let h=e;h!=t&&!(x(i,n,s[h+1],s[h+2])&&(o=h,r>0));h=s[h+3]);return o}slice(e,t,r){let n=this.buffer,i=new Uint16Array(t-e),s=0;for(let o=e,h=0;o<t;){i[h++]=n[o++],i[h++]=n[o++]-r;let t=i[h++]=n[o++]-r;i[h++]=n[o++]-e,s=Math.max(s,t)}return new m(i,s,this.set)}}function x(e,t,r,n){switch(e){case-2:return r<t;case-1:return n>=t&&r<t;case 0:return r<t&&n>t;case 1:return r<=t&&n>t;case 2:return n>t;case 4:return!0}}function b(e,t,r,n){for(var i;e.from==e.to||(r<1?e.from>=t:e.from>t)||(r>-1?e.to<=t:e.to<t);){let t=!n&&e instanceof w&&e.index<0?null:e.parent;if(!t)return e;e=t}let s=n?0:d.IgnoreOverlays;if(n)for(let n=e,o=n.parent;o;n=o,o=n.parent)n instanceof w&&n.index<0&&(null===(i=o.enter(t,r,s))||void 0===i?void 0:i.from)!=n.from&&(e=o);for(;;){let n=e.enter(t,r,s);if(!n)return e;e=n}}class y{cursor(e=0){return new N(this,e)}getChild(e,t=null,r=null){let n=v(this,e,t,r);return n.length?n[0]:null}getChildren(e,t=null,r=null){return v(this,e,t,r)}resolve(e,t=0){return b(this,e,t,!1)}resolveInner(e,t=0){return b(this,e,t,!0)}matchContext(e){return _(this,e)}enterUnfinishedNodesBefore(e){let t=this.childBefore(e),r=this;for(;t;){let e=t.lastChild;if(!e||e.to!=t.to)break;e.type.isError&&e.from==e.to?(r=t,t=e.prevSibling):t=e}return r}get node(){return this}get next(){return this.parent}}class w extends y{constructor(e,t,r,n){super(),this._tree=e,this.from=t,this.index=r,this._parent=n}get type(){return this._tree.type}get name(){return this._tree.type.name}get to(){return this.from+this._tree.length}nextChild(e,t,r,n,i=0){for(let s=this;;){for(let{children:o,positions:l}=s._tree,f=t>0?o.length:-1;e!=f;e+=t){let f=o[e],u=l[e]+s.from;if(x(n,r,u,u+f.length))if(f instanceof m){if(i&d.ExcludeBuffers)continue;let o=f.findChild(0,f.buffer.length,t,r-u,n);if(o>-1)return new k(new A(s,f,e,u),null,o)}else if(i&d.IncludeAnonymous||!f.type.isAnonymous||I(f)){let o;if(!(i&d.IgnoreMounts)&&(o=h.get(f))&&!o.overlay)return new w(o.tree,u,e,s);let l=new w(f,u,e,s);return i&d.IncludeAnonymous||!l.type.isAnonymous?l:l.nextChild(t<0?f.children.length-1:0,t,r,n)}}if(i&d.IncludeAnonymous||!s.type.isAnonymous)return null;if(e=s.index>=0?s.index+t:t<0?-1:s._parent._tree.children.length,s=s._parent,!s)return null}}get firstChild(){return this.nextChild(0,1,0,4)}get lastChild(){return this.nextChild(this._tree.children.length-1,-1,0,4)}childAfter(e){return this.nextChild(0,1,e,2)}childBefore(e){return this.nextChild(this._tree.children.length-1,-1,e,-2)}enter(e,t,r=0){let n;if(!(r&d.IgnoreOverlays)&&(n=h.get(this._tree))&&n.overlay){let r=e-this.from;for(let{from:e,to:i}of n.overlay)if((t>0?e<=r:e<r)&&(t<0?i>=r:i>r))return new w(n.tree,n.overlay[0].from+this.from,-1,this)}return this.nextChild(0,1,e,t,r)}nextSignificantParent(){let e=this;for(;e.type.isAnonymous&&e._parent;)e=e._parent;return e}get parent(){return this._parent?this._parent.nextSignificantParent():null}get nextSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index+1,1,0,4):null}get prevSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index-1,-1,0,4):null}get tree(){return this._tree}toTree(){return this._tree}toString(){return this._tree.toString()}}function v(e,t,r,n){let i=e.cursor(),s=[];if(!i.firstChild())return s;if(null!=r)for(;!i.type.is(r);)if(!i.nextSibling())return s;for(;;){if(null!=n&&i.type.is(n))return s;if(i.type.is(t)&&s.push(i.node),!i.nextSibling())return null==n?s:[]}}function _(e,t,r=t.length-1){for(let n=e.parent;r>=0;n=n.parent){if(!n)return!1;if(!n.type.isAnonymous){if(t[r]&&t[r]!=n.name)return!1;r--}}return!0}class A{constructor(e,t,r,n){this.parent=e,this.buffer=t,this.index=r,this.start=n}}class k extends y{get name(){return this.type.name}get from(){return this.context.start+this.context.buffer.buffer[this.index+1]}get to(){return this.context.start+this.context.buffer.buffer[this.index+2]}constructor(e,t,r){super(),this.context=e,this._parent=t,this.index=r,this.type=e.buffer.set.types[e.buffer.buffer[r]]}child(e,t,r){let{buffer:n}=this.context,i=n.findChild(this.index+4,n.buffer[this.index+3],e,t-this.context.start,r);return i<0?null:new k(this.context,this,i)}get firstChild(){return this.child(1,0,4)}get lastChild(){return this.child(-1,0,4)}childAfter(e){return this.child(1,e,2)}childBefore(e){return this.child(-1,e,-2)}enter(e,t,r=0){if(r&d.ExcludeBuffers)return null;let{buffer:n}=this.context,i=n.findChild(this.index+4,n.buffer[this.index+3],t>0?1:-1,e-this.context.start,t);return i<0?null:new k(this.context,this,i)}get parent(){return this._parent||this.context.parent.nextSignificantParent()}externalSibling(e){return this._parent?null:this.context.parent.nextChild(this.context.index+e,e,0,4)}get nextSibling(){let{buffer:e}=this.context,t=e.buffer[this.index+3];return t<(this._parent?e.buffer[this._parent.index+3]:e.buffer.length)?new k(this.context,this._parent,t):this.externalSibling(1)}get prevSibling(){let{buffer:e}=this.context,t=this._parent?this._parent.index+4:0;return this.index==t?this.externalSibling(-1):new k(this.context,this._parent,e.findChild(t,this.index,-1,0,4))}get tree(){return null}toTree(){let e=[],t=[],{buffer:r}=this.context,n=this.index+4,i=r.buffer[this.index+3];if(i>n){let s=r.buffer[this.index+1];e.push(r.slice(n,i,s)),t.push(0)}return new c(this.type,e,t,this.to-this.from)}toString(){return this.context.buffer.childString(this.index)}}function C(e){if(!e.length)return null;if(1==e.length)return e[0];let t=0,r=e[0];for(let n=1;n<e.length;n++){let i=e[n];(i.from>r.from||i.to<r.to)&&(r=i,t=n)}let n=r instanceof w&&r.index<0?null:r.parent,i=e.slice();return n?i[t]=n:i.splice(t,1),new S(i,r)}class S{constructor(e,t){this.heads=e,this.node=t}get next(){return C(this.heads)}}class N{get name(){return this.type.name}constructor(e,t=0){if(this.mode=t,this.buffer=null,this.stack=[],this.index=0,this.bufferNode=null,e instanceof w)this.yieldNode(e);else{this._tree=e.context.parent,this.buffer=e.context;for(let t=e._parent;t;t=t._parent)this.stack.unshift(t.index);this.bufferNode=e,this.yieldBuf(e.index)}}yieldNode(e){return!!e&&(this._tree=e,this.type=e.type,this.from=e.from,this.to=e.to,!0)}yieldBuf(e,t){this.index=e;let{start:r,buffer:n}=this.buffer;return this.type=t||n.set.types[n.buffer[e]],this.from=r+n.buffer[e+1],this.to=r+n.buffer[e+2],!0}yield(e){return!!e&&(e instanceof w?(this.buffer=null,this.yieldNode(e)):(this.buffer=e.context,this.yieldBuf(e.index,e.type)))}toString(){return this.buffer?this.buffer.buffer.childString(this.index):this._tree.toString()}enterChild(e,t,r){if(!this.buffer)return this.yield(this._tree.nextChild(e<0?this._tree._tree.children.length-1:0,e,t,r,this.mode));let{buffer:n}=this.buffer,i=n.findChild(this.index+4,n.buffer[this.index+3],e,t-this.buffer.start,r);return!(i<0)&&(this.stack.push(this.index),this.yieldBuf(i))}firstChild(){return this.enterChild(1,0,4)}lastChild(){return this.enterChild(-1,0,4)}childAfter(e){return this.enterChild(1,e,2)}childBefore(e){return this.enterChild(-1,e,-2)}enter(e,t,r=this.mode){return this.buffer?!(r&d.ExcludeBuffers)&&this.enterChild(1,e,t):this.yield(this._tree.enter(e,t,r))}parent(){if(!this.buffer)return this.yieldNode(this.mode&d.IncludeAnonymous?this._tree._parent:this._tree.parent);if(this.stack.length)return this.yieldBuf(this.stack.pop());let e=this.mode&d.IncludeAnonymous?this.buffer.parent:this.buffer.parent.nextSignificantParent();return this.buffer=null,this.yieldNode(e)}sibling(e){if(!this.buffer)return!!this._tree._parent&&this.yield(this._tree.index<0?null:this._tree._parent.nextChild(this._tree.index+e,e,0,4,this.mode));let{buffer:t}=this.buffer,r=this.stack.length-1;if(e<0){let e=r<0?0:this.stack[r]+4;if(this.index!=e)return this.yieldBuf(t.findChild(e,this.index,-1,0,4))}else{let e=t.buffer[this.index+3];if(e<(r<0?t.buffer.length:t.buffer[this.stack[r]+3]))return this.yieldBuf(e)}return r<0&&this.yield(this.buffer.parent.nextChild(this.buffer.index+e,e,0,4,this.mode))}nextSibling(){return this.sibling(1)}prevSibling(){return this.sibling(-1)}atLastNode(e){let t,r,{buffer:n}=this;if(n){if(e>0){if(this.index<n.buffer.buffer.length)return!1}else for(let e=0;e<this.index;e++)if(n.buffer.buffer[e+3]<this.index)return!1;({index:t,parent:r}=n)}else({index:t,_parent:r}=this._tree);for(;r;({index:t,_parent:r}=r))if(t>-1)for(let n=t+e,i=e<0?-1:r._tree.children.length;n!=i;n+=e){let e=r._tree.children[n];if(this.mode&d.IncludeAnonymous||e instanceof m||!e.type.isAnonymous||I(e))return!1}return!0}move(e,t){if(t&&this.enterChild(e,0,4))return!0;for(;;){if(this.sibling(e))return!0;if(this.atLastNode(e)||!this.parent())return!1}}next(e=!0){return this.move(1,e)}prev(e=!0){return this.move(-1,e)}moveTo(e,t=0){for(;(this.from==this.to||(t<1?this.from>=e:this.from>e)||(t>-1?this.to<=e:this.to<e))&&this.parent(););for(;this.enterChild(1,e,t););return this}get node(){if(!this.buffer)return this._tree;let e=this.bufferNode,t=null,r=0;if(e&&e.context==this.buffer)e:for(let n=this.index,i=this.stack.length;i>=0;){for(let s=e;s;s=s._parent)if(s.index==n){if(n==this.index)return s;t=s,r=i+1;break e}n=this.stack[--i]}for(let e=r;e<this.stack.length;e++)t=new k(this.buffer,t,this.stack[e]);return this.bufferNode=new k(this.buffer,t,this.index)}get tree(){return this.buffer?null:this._tree._tree}iterate(e,t){for(let r=0;;){let n=!1;if(this.type.isAnonymous||!1!==e(this)){if(this.firstChild()){r++;continue}this.type.isAnonymous||(n=!0)}for(;n&&t&&t(this),n=this.type.isAnonymous,!this.nextSibling();){if(!r)return;this.parent(),r--,n=!0}}}matchContext(e){if(!this.buffer)return _(this.node,e);let{buffer:t}=this.buffer,{types:r}=t.set;for(let n=e.length-1,i=this.stack.length-1;n>=0;i--){if(i<0)return _(this.node,e,n);let s=r[t.buffer[this.stack[i]]];if(!s.isAnonymous){if(e[n]&&e[n]!=s.name)return!1;n--}}return!0}}function I(e){return e.children.some((e=>e instanceof m||!e.type.isAnonymous||I(e)))}const T=new WeakMap;function B(e,t){if(!e.isAnonymous||t instanceof m||t.type!=e)return 1;let r=T.get(t);if(null==r){r=1;for(let n of t.children){if(n.type!=e||!(n instanceof c)){r=1;break}r+=B(e,n)}T.set(t,r)}return r}function M(e,t,r,n,i,s,o,h,l){let f=0;for(let r=n;r<i;r++)f+=B(e,t[r]);let u=Math.ceil(1.5*f/8),a=[],p=[];return function t(r,n,i,o,h){for(let f=i;f<o;){let i=f,d=n[f],c=B(e,r[f]);for(f++;f<o;f++){let t=B(e,r[f]);if(c+t>=u)break;c+=t}if(f==i+1){if(c>u){let e=r[i];t(e.children,e.positions,0,e.children.length,n[i]+h);continue}a.push(r[i])}else{let t=n[f-1]+r[f-1].length-d;a.push(M(e,r,n,i,f,d,t,null,l))}p.push(d+h-s)}}(t,r,n,i,0),(h||l)(a,p,o)}class P{constructor(){this.map=new WeakMap}setBuffer(e,t,r){let n=this.map.get(e);n||this.map.set(e,n=new Map),n.set(t,r)}getBuffer(e,t){let r=this.map.get(e);return r&&r.get(t)}set(e,t){e instanceof k?this.setBuffer(e.context.buffer,e.index,t):e instanceof w&&this.map.set(e.tree,t)}get(e){return e instanceof k?this.getBuffer(e.context.buffer,e.index):e instanceof w?this.map.get(e.tree):void 0}cursorSet(e,t){e.buffer?this.setBuffer(e.buffer.buffer,e.index,t):this.map.set(e.tree,t)}cursorGet(e){return e.buffer?this.getBuffer(e.buffer.buffer,e.index):this.map.get(e.tree)}}class E{constructor(e,t,r,n,i=!1,s=!1){this.from=e,this.to=t,this.tree=r,this.offset=n,this.open=(i?1:0)|(s?2:0)}get openStart(){return(1&this.open)>0}get openEnd(){return(2&this.open)>0}static addTree(e,t=[],r=!1){let n=[new E(0,e.length,e,0,!1,r)];for(let r of t)r.to>e.length&&n.push(r);return n}static applyChanges(e,t,r=128){if(!t.length)return e;let n=[],i=1,s=e.length?e[0]:null;for(let o=0,h=0,l=0;;o++){let f=o<t.length?t[o]:null,u=f?f.fromA:1e9;if(u-h>=r)for(;s&&s.from<u;){let t=s;if(h>=t.from||u<=t.to||l){let e=Math.max(t.from,h)-l,r=Math.min(t.to,u)-l;t=e>=r?null:new E(e,r,t.tree,t.offset+l,o>0,!!f)}if(t&&n.push(t),s.to>u)break;s=i<e.length?e[i++]:null}if(!f)break;h=f.toA,l=f.toA-f.toB}return n}}class z{startParse(e,t,r){return"string"==typeof e&&(e=new O(e)),r=r?r.length?r.map((e=>new s(e.from,e.to))):[new s(0,0)]:[new s(0,e.length)],this.createParse(e,t||[],r)}parse(e,t,r){let n=this.startParse(e,t,r);for(;;){let e=n.advance();if(e)return e}}}class O{constructor(e){this.string=e}get length(){return this.string.length}chunk(e){return this.string.slice(e)}get lineChunks(){return!1}read(e,t){return this.string.slice(e,t)}}function F(e){return(t,r,n,i)=>new D(t,e,r,n,i)}class j{constructor(e,t,r,n,i){if(this.parser=e,this.parse=t,this.overlay=r,this.target=n,this.ranges=i,!i.length||i.some((e=>e.from>=e.to)))throw new RangeError("Invalid inner parse ranges given: "+JSON.stringify(i))}}class R{constructor(e,t,r,n,i,s,o){this.parser=e,this.predicate=t,this.mounts=r,this.index=n,this.start=i,this.target=s,this.prev=o,this.depth=0,this.ranges=[]}}const U=new o({perNode:!0});class D{constructor(e,t,r,n,i){this.nest=t,this.input=r,this.fragments=n,this.ranges=i,this.inner=[],this.innerDone=0,this.baseTree=null,this.stoppedAt=null,this.baseParse=e}advance(){if(this.baseParse){let e=this.baseParse.advance();if(!e)return null;if(this.baseParse=null,this.baseTree=e,this.startInner(),null!=this.stoppedAt)for(let e of this.inner)e.parse.stopAt(this.stoppedAt)}if(this.innerDone==this.inner.length){let e=this.baseTree;return null!=this.stoppedAt&&(e=new c(e.type,e.children,e.positions,e.length,e.propValues.concat([[U,this.stoppedAt]]))),e}let e=this.inner[this.innerDone],t=e.parse.advance();if(t){this.innerDone++;let r=Object.assign(Object.create(null),e.target.props);r[o.mounted.id]=new h(t,e.overlay,e.parser),e.target.props=r}return null}get parsedPos(){if(this.baseParse)return 0;let e=this.input.length;for(let t=this.innerDone;t<this.inner.length;t++)this.inner[t].ranges[0].from<e&&(e=Math.min(e,this.inner[t].parse.parsedPos));return e}stopAt(e){if(this.stoppedAt=e,this.baseParse)this.baseParse.stopAt(e);else for(let t=this.innerDone;t<this.inner.length;t++)this.inner[t].parse.stopAt(e)}startInner(){let e=new H(this.fragments),t=null,r=null,n=new N(new w(this.baseTree,this.ranges[0].from,0,null),d.IncludeAnonymous|d.IgnoreMounts);e:for(let i,o;null==this.stoppedAt||n.from<this.stoppedAt;){let h,l=!0;if(e.hasNode(n)){if(t){let e=t.mounts.find((e=>e.frag.from<=n.from&&e.frag.to>=n.to&&e.mount.overlay));if(e)for(let r of e.mount.overlay){let i=r.from+e.pos,s=r.to+e.pos;i>=n.from&&s<=n.to&&!t.ranges.some((e=>e.from<s&&e.to>i))&&t.ranges.push({from:i,to:s})}}l=!1}else if(r&&(o=W(r.ranges,n.from,n.to)))l=2!=o;else if(!n.type.isAnonymous&&n.from<n.to&&(i=this.nest(n,this.input))){n.tree||J(n);let o=e.findMounts(n.from,i.parser);if("function"==typeof i.overlay)t=new R(i.parser,i.overlay,o,this.inner.length,n.from,n.tree,t);else{let e=Y(this.ranges,i.overlay||[new s(n.from,n.to)]);e.length&&this.inner.push(new j(i.parser,i.parser.startParse(this.input,$(o,e),e),i.overlay?i.overlay.map((e=>new s(e.from-n.from,e.to-n.from))):null,n.tree,e)),i.overlay?e.length&&(r={ranges:e,depth:0,prev:r}):l=!1}}else t&&(h=t.predicate(n))&&(!0===h&&(h=new s(n.from,n.to)),h.from<h.to&&t.ranges.push(h));if(l&&n.firstChild())t&&t.depth++,r&&r.depth++;else for(;!n.nextSibling();){if(!n.parent())break e;if(t&&! --t.depth){let e=Y(this.ranges,t.ranges);e.length&&this.inner.splice(t.index,0,new j(t.parser,t.parser.startParse(this.input,$(t.mounts,e),e),t.ranges.map((e=>new s(e.from-t.start,e.to-t.start))),t.target,e)),t=t.prev}r&&! --r.depth&&(r=r.prev)}}}}function W(e,t,r){for(let n of e){if(n.from>=r)break;if(n.to>t)return n.from<=t&&n.to>=r?2:1}return 0}function L(e,t,r,n,i,s){if(t<r){let o=e.buffer[t+1];n.push(e.slice(t,r,o)),i.push(o-s)}}function J(e){let{node:t}=e,r=0;do{e.parent(),r++}while(!e.tree);let n=0,i=e.tree,s=0;for(;s=i.positions[n]+e.from,!(s<=t.from&&s+i.children[n].length>=t.to);n++);let o=i.children[n],h=o.buffer;i.children[n]=function e(r,n,i,l,f){let u=r;for(;h[u+2]+s<=t.from;)u=h[u+3];let a=[],p=[];L(o,r,u,a,p,l);let d=h[u+1],g=h[u+2],m=d+s==t.from&&g+s==t.to&&h[u]==t.type.id;return a.push(m?t.toTree():e(u+4,h[u+3],o.set.types[h[u]],d,g-d)),p.push(d-l),L(o,h[u+3],n,a,p,l),new c(i,a,p,f)}(0,h.length,f.none,0,o.length);for(let n=0;n<=r;n++)e.childAfter(t.from)}class V{constructor(e,t){this.offset=t,this.done=!1,this.cursor=e.cursor(d.IncludeAnonymous|d.IgnoreMounts)}moveTo(e){let{cursor:t}=this,r=e-this.offset;for(;!this.done&&t.from<r;)t.to>=e&&t.enter(r,1,d.IgnoreOverlays|d.ExcludeBuffers)||t.next(!1)||(this.done=!0)}hasNode(e){if(this.moveTo(e.from),!this.done&&this.cursor.from+this.offset==e.from&&this.cursor.tree)for(let t=this.cursor.tree;;){if(t==e.tree)return!0;if(!(t.children.length&&0==t.positions[0]&&t.children[0]instanceof c))break;t=t.children[0]}return!1}}class H{constructor(e){var t;if(this.fragments=e,this.curTo=0,this.fragI=0,e.length){let r=this.curFrag=e[0];this.curTo=null!==(t=r.tree.prop(U))&&void 0!==t?t:r.to,this.inner=new V(r.tree,-r.offset)}else this.curFrag=this.inner=null}hasNode(e){for(;this.curFrag&&e.from>=this.curTo;)this.nextFrag();return this.curFrag&&this.curFrag.from<=e.from&&this.curTo>=e.to&&this.inner.hasNode(e)}nextFrag(){var e;if(this.fragI++,this.fragI==this.fragments.length)this.curFrag=this.inner=null;else{let t=this.curFrag=this.fragments[this.fragI];this.curTo=null!==(e=t.tree.prop(U))&&void 0!==e?e:t.to,this.inner=new V(t.tree,-t.offset)}}findMounts(e,t){var r;let n=[];if(this.inner){this.inner.cursor.moveTo(e,1);for(let e=this.inner.cursor.node;e;e=e.parent){let i=null===(r=e.tree)||void 0===r?void 0:r.prop(o.mounted);if(i&&i.parser==t)for(let t=this.fragI;t<this.fragments.length;t++){let r=this.fragments[t];if(r.from>=e.to)break;r.tree==this.curFrag.tree&&n.push({frag:r,pos:e.from-r.offset,mount:i})}}}return n}}function Y(e,t){let r=null,n=t;for(let i=1,o=0;i<e.length;i++){let h=e[i-1].to,l=e[i].from;for(;o<n.length;o++){let e=n[o];if(e.from>=l)break;e.to<=h||(r||(n=r=t.slice()),e.from<h?(r[o]=new s(e.from,h),e.to>l&&r.splice(o+1,0,new s(l,e.to))):e.to>l?r[o--]=new s(l,e.to):r.splice(o--,1))}}return n}function G(e,t,r,n){let i=0,o=0,h=!1,l=!1,f=-1e9,u=[];for(;;){let a=i==e.length?1e9:h?e[i].to:e[i].from,p=o==t.length?1e9:l?t[o].to:t[o].from;if(h!=l){let e=Math.max(f,r),t=Math.min(a,p,n);e<t&&u.push(new s(e,t))}if(f=Math.min(a,p),1e9==f)break;a==f&&(h?(h=!1,i++):h=!0),p==f&&(l?(l=!1,o++):l=!0)}return u}function $(e,t){let r=[];for(let{pos:n,mount:i,frag:o}of e){let e=n+(i.overlay?i.overlay[0].from:0),h=e+i.tree.length,l=Math.max(o.from,e),f=Math.min(o.to,h);if(i.overlay){let h=G(t,i.overlay.map((e=>new s(e.from+n,e.to+n))),l,f);for(let t=0,n=l;;t++){let s=t==h.length,l=s?f:h[t].from;if(l>n&&r.push(new E(n,l,i.tree,-e,o.from>=n||o.openStart,o.to<=l||o.openEnd)),s)break;n=h[t].to}}else r.push(new E(l,f,i.tree,-e,o.from>=e||o.openStart,o.to<=h||o.openEnd))}return r}}}]);
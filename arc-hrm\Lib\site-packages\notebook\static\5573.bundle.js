"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[5573],{5573:(e,o,t)=>{t.r(o),t.d(o,{default:()=>A});var n=t(12982),a=t(38639),s=t(90157),r=t(97104),c=t(43833),i=t(36768),l=t(71677),d=t(42266),u=t(97934),k=t(31516),b=t(78156),p=t.n(b);const g=e=>{const o=e.model;if(!o)return!1;const t=Array.from(o.cells);let n=0,a=0;for(const e of t)"code"===e.type&&(n++,e.trusted&&a++);return a===n},h=({notebook:e,translator:o})=>{const t=o.load("notebook"),[n,a]=(0,b.useState)(g(e)),s=()=>{const o=g(e);a(o)};return(0,b.useEffect)((()=>(e.modelContentChanged.connect(s),e.activeCellChanged.connect(s),s(),()=>{e.modelContentChanged.disconnect(s),e.activeCellChanged.disconnect(s)}))),p().createElement("button",{className:"jp-NotebookTrustedStatus",style:n?{cursor:"help"}:{cursor:"pointer"},onClick:()=>!n&&(async()=>{await c.NotebookActions.trust(e,o),s()})(),title:n?t.__("JavaScript enabled for notebook display"):t.__("JavaScript disabled for notebook display")},n?t.__("Trusted"):t.__("Not Trusted"))};var m;!function(e){e.create=({notebook:e,translator:o})=>n.ReactWidget.create(p().createElement(h,{notebook:e,translator:o}))}(m||(m={}));const C="jp-NotebookKernelStatus-error",f="jp-NotebookKernelStatus-warn",y="jp-NotebookKernelStatus-info",v="jp-NotebookKernelStatus-fade";var N;!function(e){e.openEditNotebookMetadata="notebook:edit-metadata"}(N||(N={}));const S={id:"@jupyter-notebook/notebook-extension:checkpoints",description:"A plugin for the checkpoint indicator.",autoStart:!0,requires:[s.IDocumentManager,l.ITranslator],optional:[d.INotebookShell,n.IToolbarWidgetRegistry],activate:(e,o,t,s,r)=>{const{shell:c}=e,i=t.load("notebook"),l=document.createElement("div");r&&r.addFactory("TopBar","checkpoint",(e=>{const o=new k.Widget({node:l});return o.id=n.DOMUtils.createDomID(),o.addClass("jp-NotebookCheckpoint"),o}));const d=async()=>{const e=c.currentWidget;if(!e)return;const t=o.contextForWidget(e);null==t||t.fileChanged.disconnect(d),null==t||t.fileChanged.connect(d);const n=await(null==t?void 0:t.listCheckpoints());if(!n)return;const s=n[n.length-1];l.textContent=i.__("Last Checkpoint: %1",a.Time.formatHuman(new Date(s.last_modified)))};s&&s.currentChanged.connect(d),new u.Poll({auto:!0,factory:()=>d(),frequency:{interval:2e3,backoff:!1},standby:"when-hidden"})}},w={id:"@jupyter-notebook/notebook-extension:close-tab",description:'Add a command to close the browser tab when clicking on "Close and Shut Down".',autoStart:!0,requires:[r.IMainMenu],optional:[l.ITranslator],activate:(e,o,t)=>{const{commands:n}=e,a=(t=null!=t?t:l.nullTranslator).load("notebook"),s="notebook:close-and-halt";n.addCommand(s,{label:a.__("Close and Shut Down Notebook"),execute:async()=>{await n.execute("notebook:close-and-shutdown"),window.close()}}),o.fileMenu.closeAndCleaners.add({id:s,rank:0})}},T={id:"@jupyter-notebook/notebook-extension:kernel-logo",description:"The kernel logo plugin.",autoStart:!0,requires:[d.INotebookShell],optional:[n.IToolbarWidgetRegistry],activate:(e,o,t)=>{const{serviceManager:n}=e,a=document.createElement("div"),s=document.createElement("img"),r=async()=>{var e,t,i,l,d;const u=o.currentWidget;if(!(u instanceof c.NotebookPanel))return;a.hasChildNodes()||a.appendChild(s),await u.sessionContext.ready,u.sessionContext.kernelChanged.disconnect(r),u.sessionContext.kernelChanged.connect(r);const k=null!==(i=null===(t=null===(e=u.sessionContext.session)||void 0===e?void 0:e.kernel)||void 0===t?void 0:t.name)&&void 0!==i?i:"",b=null===(d=null===(l=n.kernelspecs)||void 0===l?void 0:l.specs)||void 0===d?void 0:d.kernelspecs[k];if(!b)return void a.childNodes[0].remove();const p=b.resources["logo-64x64"];p?(s.src=p,s.title=b.display_name):a.childNodes[0].remove()};t&&t.addFactory("TopBar","kernelLogo",(e=>{const o=new k.Widget({node:a});return o.addClass("jp-NotebookKernelLogo"),o})),e.started.then((()=>{o.currentChanged.connect(r)}))}},x={id:"@jupyter-notebook/notebook-extension:kernel-status",description:"A plugin to display the kernel status.",autoStart:!0,requires:[d.INotebookShell,l.ITranslator],activate:(e,o,t)=>{const n=t.load("notebook"),s=new k.Widget;s.addClass("jp-NotebookKernelStatus"),e.shell.add(s,"menu",{rank:10010});const r=e=>{const o=e.kernelDisplayStatus;let t=`Kernel ${a.Text.titleCase(o)}`;switch(s.removeClass(C),s.removeClass(f),s.removeClass(y),s.removeClass(v),o){case"busy":case"idle":t="",s.addClass(v);break;case"dead":case"terminating":s.addClass(C);break;case"unknown":s.addClass(f);break;default:s.addClass(y),s.addClass(v)}s.node.textContent=n.__(t)};o.currentChanged.connect((async()=>{const e=o.currentWidget;e instanceof c.NotebookPanel&&e.sessionContext.statusChanged.connect(r)}))}},_={id:"@jupyter-notebook/notebook-extension:scroll-output",description:"A plugin to enable scrolling for outputs by default.",autoStart:!0,requires:[c.INotebookTracker],optional:[i.ISettingRegistry],activate:async(e,o,t)=>{let n=!0;const a=e=>{if(!n)return;const{outputArea:o}=e;if(void 0!==e.model.getMetadata("scrolled"))return;const{node:t}=o,a=t.scrollHeight>1.3*(parseFloat(t.style.fontSize.replace("px",""))||14)*100;e.toggleClass("jp-mod-outputsScrolled",a)},s={},r=e=>{if("code"===e.model.type){const o=e,t=o.model.id;a(o),s[t]&&o.outputArea.model.changed.disconnect(s[t]),s[t]=()=>a(o),o.outputArea.model.changed.connect(s[t])}};if(o.widgetAdded.connect(((e,o)=>{var t;o.sessionContext.ready.then((()=>{o.content.widgets.forEach(r)})),null===(t=o.model)||void 0===t||t.cells.changed.connect(((e,t)=>{o.content.widgets.forEach(r)}))})),t){const o=t.load(_.id),a=e=>{n=e.get("autoScrollOutputs").composite};Promise.all([o,e.restored]).then((([e])=>{a(e),e.changed.connect((e=>{a(e)}))})).catch((e=>{console.error(e.message)}))}}},I={id:"@jupyter-notebook/notebook-extension:notebook-tools",description:"A plugin to add the NotebookTools to the side panel.",autoStart:!0,requires:[d.INotebookShell],optional:[c.INotebookTools],activate:(e,o,t)=>{o.currentChanged.connect((async()=>{o.currentWidget instanceof c.NotebookPanel&&t&&o.add(t,"right",{type:"Property Inspector"})}))}},E={id:"@jupyter-notebook/notebook-extension:tab-icon",description:"A plugin to update the tab icon based on the kernel status.",autoStart:!0,requires:[c.INotebookTracker],activate:(e,o)=>{const t=a.PageConfig.getBaseUrl(),n=a.URLExt.join(t,"static/favicons/favicon-notebook.ico"),s=a.URLExt.join(t,"static/favicons/favicon-busy-1.ico");o.currentChanged.connect((async()=>{const e=o.currentWidget,t=null==e?void 0:e.sessionContext;t&&t.statusChanged.connect((()=>{(e=>{const o=document.querySelector("link[rel*='icon']");switch(e){case"busy":o.href=s;break;case"idle":o.href=n}})(t.kernelDisplayStatus)}))}))}},j={id:"@jupyter-notebook/notebook-extension:trusted",description:"A plugin that adds a Trusted indicator to the menu area.",autoStart:!0,requires:[d.INotebookShell,l.ITranslator],activate:(e,o,t)=>{o.currentChanged.connect((async()=>{const e=o.currentWidget;if(!(e instanceof c.NotebookPanel))return;const n=e.content;await e.context.ready;const a=m.create({notebook:n,translator:t});o.add(a,"menu",{rank:11e3})}))}},A=[S,w,{id:"@jupyter-notebook/notebook-extension:edit-notebook-metadata",description:'Add a command to open right sidebar for Editing Notebook Metadata when clicking on "Edit Notebook Metadata" under Edit menu',autoStart:!0,optional:[n.ICommandPalette,l.ITranslator,c.INotebookTools],activate:(e,o,t,n)=>{const{commands:a,shell:s}=e,r=(t=null!=t?t:l.nullTranslator).load("notebook");a.addCommand(N.openEditNotebookMetadata,{label:r.__("Edit Notebook Metadata"),execute:async()=>{const e="application:toggle-panel",o={side:"right",title:"Show Notebook Tools",id:"notebook-tools"};a.isToggled(e,o)||await a.execute(e,o).then((e=>{n&&(null==n?void 0:n.layout).widgets.forEach((e=>{e.widget.title.label===r.__("Advanced Tools")&&e.collapsed&&e.toggle()}))}))},isVisible:()=>null!==s.currentWidget&&s.currentWidget instanceof c.NotebookPanel}),o&&o.addItem({command:N.openEditNotebookMetadata,category:"Notebook Operations"})}},T,x,I,_,E,j]}}]);
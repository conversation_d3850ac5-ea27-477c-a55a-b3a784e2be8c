"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[4499],{31655:(t,n,e)=>{function i(t,n){return null==t||null==n?NaN:t<n?-1:t>n?1:t>=n?0:NaN}e.d(n,{Z:()=>i})},10321:(t,n,e)=>{e.d(n,{Nw:()=>h,ZP:()=>u,ml:()=>a});var i=e(31655),r=e(2518),o=e(53612);const s=(0,r.Z)(i.Z),a=s.right,h=s.left,u=((0,r.Z)(o.Z).center,a)},2518:(t,n,e)=>{e.d(n,{Z:()=>o});var i=e(31655);function r(t,n){return null==t||null==n?NaN:n<t?-1:n>t?1:n>=t?0:NaN}function o(t){let n,e,o;function a(t,i,r=0,o=t.length){if(r<o){if(0!==n(i,i))return o;do{const n=r+o>>>1;e(t[n],i)<0?r=n+1:o=n}while(r<o)}return r}return 2!==t.length?(n=i.Z,e=(n,e)=>(0,i.Z)(t(n),e),o=(n,e)=>t(n)-e):(n=t===i.Z||t===r?t:s,e=t,o=t),{left:a,center:function(t,n,e=0,i=t.length){const r=a(t,n,e,i-1);return r>e&&o(t[r-1],n)>-o(t[r],n)?r-1:r},right:function(t,i,r=0,o=t.length){if(r<o){if(0!==n(i,i))return o;do{const n=r+o>>>1;e(t[n],i)<=0?r=n+1:o=n}while(r<o)}return r}}}function s(){return 0}},53856:(t,n,e)=>{function i(t,n){let e;if(void 0===n)for(const n of t)null!=n&&(e<n||void 0===e&&n>=n)&&(e=n);else{let i=-1;for(let r of t)null!=(r=n(r,++i,t))&&(e<r||void 0===e&&r>=r)&&(e=r)}return e}e.d(n,{Z:()=>i})},93571:(t,n,e)=>{function i(t,n){let e;if(void 0===n)for(const n of t)null!=n&&(e>n||void 0===e&&n>=n)&&(e=n);else{let i=-1;for(let r of t)null!=(r=n(r,++i,t))&&(e>r||void 0===e&&r>=r)&&(e=r)}return e}e.d(n,{Z:()=>i})},53612:(t,n,e)=>{function i(t){return null===t?NaN:+t}function*r(t,n){if(void 0===n)for(let n of t)null!=n&&(n=+n)>=n&&(yield n);else{let e=-1;for(let i of t)null!=(i=n(i,++e,t))&&(i=+i)>=i&&(yield i)}}e.d(n,{K:()=>r,Z:()=>i})},53353:(t,n,e)=>{function i(t,n,e){t=+t,n=+n,e=(r=arguments.length)<2?(n=t,t=0,1):r<3?1:+e;for(var i=-1,r=0|Math.max(0,Math.ceil((n-t)/e)),o=new Array(r);++i<r;)o[i]=t+i*e;return o}e.d(n,{Z:()=>i})},73002:(t,n,e)=>{e.d(n,{G9:()=>h,ZP:()=>a,ly:()=>u});const i=Math.sqrt(50),r=Math.sqrt(10),o=Math.sqrt(2);function s(t,n,e){const a=(n-t)/Math.max(0,e),h=Math.floor(Math.log10(a)),u=a/Math.pow(10,h),c=u>=i?10:u>=r?5:u>=o?2:1;let l,f,_;return h<0?(_=Math.pow(10,-h)/c,l=Math.round(t*_),f=Math.round(n*_),l/_<t&&++l,f/_>n&&--f,_=-_):(_=Math.pow(10,h)*c,l=Math.round(t/_),f=Math.round(n/_),l*_<t&&++l,f*_>n&&--f),f<l&&.5<=e&&e<2?s(t,n,2*e):[l,f,_]}function a(t,n,e){if(!((e=+e)>0))return[];if((t=+t)==(n=+n))return[t];const i=n<t,[r,o,a]=i?s(n,t,e):s(t,n,e);if(!(o>=r))return[];const h=o-r+1,u=new Array(h);if(i)if(a<0)for(let t=0;t<h;++t)u[t]=(o-t)/-a;else for(let t=0;t<h;++t)u[t]=(o-t)*a;else if(a<0)for(let t=0;t<h;++t)u[t]=(r+t)/-a;else for(let t=0;t<h;++t)u[t]=(r+t)*a;return u}function h(t,n,e){return s(t=+t,n=+n,e=+e)[2]}function u(t,n,e){e=+e;const i=(n=+n)<(t=+t),r=i?h(n,t,e):h(t,n,e);return(i?-1:1)*(r<0?1/-r:r)}},12738:(t,n,e)=>{e.d(n,{B8:()=>Z,Il:()=>r,J5:()=>s,SU:()=>b,Ss:()=>T,Ym:()=>P,ZP:()=>m,xV:()=>o});var i=e(77551);function r(){}var o=.7,s=1/o,a="\\s*([+-]?\\d+)\\s*",h="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",u="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",c=/^#([0-9a-f]{3,8})$/,l=new RegExp(`^rgb\\(${a},${a},${a}\\)$`),f=new RegExp(`^rgb\\(${u},${u},${u}\\)$`),_=new RegExp(`^rgba\\(${a},${a},${a},${h}\\)$`),p=new RegExp(`^rgba\\(${u},${u},${u},${h}\\)$`),y=new RegExp(`^hsl\\(${h},${u},${u}\\)$`),g=new RegExp(`^hsla\\(${h},${u},${u},${h}\\)$`),x={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function d(){return this.rgb().formatHex()}function v(){return this.rgb().formatRgb()}function m(t){var n,e;return t=(t+"").trim().toLowerCase(),(n=c.exec(t))?(e=n[1].length,n=parseInt(n[1],16),6===e?w(n):3===e?new T(n>>8&15|n>>4&240,n>>4&15|240&n,(15&n)<<4|15&n,1):8===e?M(n>>24&255,n>>16&255,n>>8&255,(255&n)/255):4===e?M(n>>12&15|n>>8&240,n>>8&15|n>>4&240,n>>4&15|240&n,((15&n)<<4|15&n)/255):null):(n=l.exec(t))?new T(n[1],n[2],n[3],1):(n=f.exec(t))?new T(255*n[1]/100,255*n[2]/100,255*n[3]/100,1):(n=_.exec(t))?M(n[1],n[2],n[3],n[4]):(n=p.exec(t))?M(255*n[1]/100,255*n[2]/100,255*n[3]/100,n[4]):(n=y.exec(t))?D(n[1],n[2]/100,n[3]/100,1):(n=g.exec(t))?D(n[1],n[2]/100,n[3]/100,n[4]):x.hasOwnProperty(t)?w(x[t]):"transparent"===t?new T(NaN,NaN,NaN,0):null}function w(t){return new T(t>>16&255,t>>8&255,255&t,1)}function M(t,n,e,i){return i<=0&&(t=n=e=NaN),new T(t,n,e,i)}function b(t){return t instanceof r||(t=m(t)),t?new T((t=t.rgb()).r,t.g,t.b,t.opacity):new T}function Z(t,n,e,i){return 1===arguments.length?b(t):new T(t,n,e,null==i?1:i)}function T(t,n,e,i){this.r=+t,this.g=+n,this.b=+e,this.opacity=+i}function N(){return`#${U(this.r)}${U(this.g)}${U(this.b)}`}function k(){const t=$(this.opacity);return`${1===t?"rgb(":"rgba("}${C(this.r)}, ${C(this.g)}, ${C(this.b)}${1===t?")":`, ${t})`}`}function $(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function C(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function U(t){return((t=C(t))<16?"0":"")+t.toString(16)}function D(t,n,e,i){return i<=0?t=n=e=NaN:e<=0||e>=1?t=n=NaN:n<=0&&(t=NaN),new Y(t,n,e,i)}function S(t){if(t instanceof Y)return new Y(t.h,t.s,t.l,t.opacity);if(t instanceof r||(t=m(t)),!t)return new Y;if(t instanceof Y)return t;var n=(t=t.rgb()).r/255,e=t.g/255,i=t.b/255,o=Math.min(n,e,i),s=Math.max(n,e,i),a=NaN,h=s-o,u=(s+o)/2;return h?(a=n===s?(e-i)/h+6*(e<i):e===s?(i-n)/h+2:(n-e)/h+4,h/=u<.5?s+o:2-s-o,a*=60):h=u>0&&u<1?0:a,new Y(a,h,u,t.opacity)}function P(t,n,e,i){return 1===arguments.length?S(t):new Y(t,n,e,null==i?1:i)}function Y(t,n,e,i){this.h=+t,this.s=+n,this.l=+e,this.opacity=+i}function E(t){return(t=(t||0)%360)<0?t+360:t}function A(t){return Math.max(0,Math.min(1,t||0))}function H(t,n,e){return 255*(t<60?n+(e-n)*t/60:t<180?e:t<240?n+(e-n)*(240-t)/60:n)}(0,i.Z)(r,m,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:d,formatHex:d,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return S(this).formatHsl()},formatRgb:v,toString:v}),(0,i.Z)(T,Z,(0,i.l)(r,{brighter(t){return t=null==t?s:Math.pow(s,t),new T(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?o:Math.pow(o,t),new T(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new T(C(this.r),C(this.g),C(this.b),$(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:N,formatHex:N,formatHex8:function(){return`#${U(this.r)}${U(this.g)}${U(this.b)}${U(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:k,toString:k})),(0,i.Z)(Y,P,(0,i.l)(r,{brighter(t){return t=null==t?s:Math.pow(s,t),new Y(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?o:Math.pow(o,t),new Y(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+360*(this.h<0),n=isNaN(t)||isNaN(this.s)?0:this.s,e=this.l,i=e+(e<.5?e:1-e)*n,r=2*e-i;return new T(H(t>=240?t-240:t+120,r,i),H(t,r,i),H(t<120?t+240:t-120,r,i),this.opacity)},clamp(){return new Y(E(this.h),A(this.s),A(this.l),$(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=$(this.opacity);return`${1===t?"hsl(":"hsla("}${E(this.h)}, ${100*A(this.s)}%, ${100*A(this.l)}%${1===t?")":`, ${t})`}`}}))},77551:(t,n,e)=>{function i(t,n,e){t.prototype=n.prototype=e,e.constructor=t}function r(t,n){var e=Object.create(t.prototype);for(var i in n)e[i]=n[i];return e}e.d(n,{Z:()=>i,l:()=>r})},64836:(t,n,e)=>{e.d(n,{Uc:()=>m,ZP:()=>p});var i=e(77551),r=e(12738),o=e(56091);const s=.96422,a=1,h=.82521,u=4/29,c=6/29,l=3*c*c,f=c*c*c;function _(t){if(t instanceof y)return new y(t.l,t.a,t.b,t.opacity);if(t instanceof w)return M(t);t instanceof r.Ss||(t=(0,r.SU)(t));var n,e,i=v(t.r),o=v(t.g),u=v(t.b),c=g((.2225045*i+.7168786*o+.0606169*u)/a);return i===o&&o===u?n=e=c:(n=g((.4360747*i+.3850649*o+.1430804*u)/s),e=g((.0139322*i+.0971045*o+.7141733*u)/h)),new y(116*c-16,500*(n-c),200*(c-e),t.opacity)}function p(t,n,e,i){return 1===arguments.length?_(t):new y(t,n,e,null==i?1:i)}function y(t,n,e,i){this.l=+t,this.a=+n,this.b=+e,this.opacity=+i}function g(t){return t>f?Math.pow(t,1/3):t/l+u}function x(t){return t>c?t*t*t:l*(t-u)}function d(t){return 255*(t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055)}function v(t){return(t/=255)<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}function m(t,n,e,i){return 1===arguments.length?function(t){if(t instanceof w)return new w(t.h,t.c,t.l,t.opacity);if(t instanceof y||(t=_(t)),0===t.a&&0===t.b)return new w(NaN,0<t.l&&t.l<100?0:NaN,t.l,t.opacity);var n=Math.atan2(t.b,t.a)*o.R;return new w(n<0?n+360:n,Math.sqrt(t.a*t.a+t.b*t.b),t.l,t.opacity)}(t):new w(t,n,e,null==i?1:i)}function w(t,n,e,i){this.h=+t,this.c=+n,this.l=+e,this.opacity=+i}function M(t){if(isNaN(t.h))return new y(t.l,0,0,t.opacity);var n=t.h*o.u;return new y(t.l,Math.cos(n)*t.c,Math.sin(n)*t.c,t.opacity)}(0,i.Z)(y,p,(0,i.l)(r.Il,{brighter(t){return new y(this.l+18*(null==t?1:t),this.a,this.b,this.opacity)},darker(t){return new y(this.l-18*(null==t?1:t),this.a,this.b,this.opacity)},rgb(){var t=(this.l+16)/116,n=isNaN(this.a)?t:t+this.a/500,e=isNaN(this.b)?t:t-this.b/200;return n=s*x(n),t=a*x(t),e=h*x(e),new r.Ss(d(3.1338561*n-1.6168667*t-.4906146*e),d(-.9787684*n+1.9161415*t+.033454*e),d(.0719453*n-.2289914*t+1.4052427*e),this.opacity)}})),(0,i.Z)(w,m,(0,i.l)(r.Il,{brighter(t){return new w(this.h,this.c,this.l+18*(null==t?1:t),this.opacity)},darker(t){return new w(this.h,this.c,this.l-18*(null==t?1:t),this.opacity)},rgb(){return M(this).rgb()}}))},56091:(t,n,e)=>{e.d(n,{R:()=>r,u:()=>i});const i=Math.PI/180,r=180/Math.PI},65043:(t,n,e)=>{e.d(n,{Z:()=>h});var i={value:()=>{}};function r(){for(var t,n=0,e=arguments.length,i={};n<e;++n){if(!(t=arguments[n]+"")||t in i||/[\s.]/.test(t))throw new Error("illegal type: "+t);i[t]=[]}return new o(i)}function o(t){this._=t}function s(t,n){for(var e,i=0,r=t.length;i<r;++i)if((e=t[i]).name===n)return e.value}function a(t,n,e){for(var r=0,o=t.length;r<o;++r)if(t[r].name===n){t[r]=i,t=t.slice(0,r).concat(t.slice(r+1));break}return null!=e&&t.push({name:n,value:e}),t}o.prototype=r.prototype={constructor:o,on:function(t,n){var e,i,r=this._,o=(i=r,(t+"").trim().split(/^|\s+/).map((function(t){var n="",e=t.indexOf(".");if(e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),t&&!i.hasOwnProperty(t))throw new Error("unknown type: "+t);return{type:t,name:n}}))),h=-1,u=o.length;if(!(arguments.length<2)){if(null!=n&&"function"!=typeof n)throw new Error("invalid callback: "+n);for(;++h<u;)if(e=(t=o[h]).type)r[e]=a(r[e],t.name,n);else if(null==n)for(e in r)r[e]=a(r[e],t.name,null);return this}for(;++h<u;)if((e=(t=o[h]).type)&&(e=s(r[e],t.name)))return e},copy:function(){var t={},n=this._;for(var e in n)t[e]=n[e].slice();return new o(t)},call:function(t,n){if((e=arguments.length-2)>0)for(var e,i,r=new Array(e),o=0;o<e;++o)r[o]=arguments[o+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(o=0,e=(i=this._[t]).length;o<e;++o)i[o].value.apply(n,r)},apply:function(t,n,e){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var i=this._[t],r=0,o=i.length;r<o;++r)i[r].value.apply(n,e)}};const h=r},90741:(t,n,e)=>{var i,r,o,s;e.d(n,{WU:()=>r,jH:()=>o}),s={thousands:",",grouping:[3],currency:["$",""]},i=(0,e(63770).Z)(s),r=i.format,o=i.formatPrefix},3044:(t,n,e)=>{e.d(n,{Z:()=>r});var i=e(47583);function r(t){return(t=(0,i.V)(Math.abs(t)))?t[1]:NaN}},47583:(t,n,e)=>{function i(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)}function r(t,n){if((e=(t=n?t.toExponential(n-1):t.toExponential()).indexOf("e"))<0)return null;var e,i=t.slice(0,e);return[i.length>1?i[0]+i.slice(2):i,+t.slice(e+1)]}e.d(n,{V:()=>r,Z:()=>i})},32159:(t,n,e)=>{e.d(n,{Z:()=>r});var i=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function r(t){if(!(n=i.exec(t)))throw new Error("invalid format: "+t);var n;return new o({fill:n[1],align:n[2],sign:n[3],symbol:n[4],zero:n[5],width:n[6],comma:n[7],precision:n[8]&&n[8].slice(1),trim:n[9],type:n[10]})}function o(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}r.prototype=o.prototype,o.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type}},63770:(t,n,e)=>{e.d(n,{Z:()=>f});var i,r=e(3044),o=e(32159),s=e(47583);function a(t,n){var e=(0,s.V)(t,n);if(!e)return t+"";var i=e[0],r=e[1];return r<0?"0."+new Array(-r).join("0")+i:i.length>r+1?i.slice(0,r+1)+"."+i.slice(r+1):i+new Array(r-i.length+2).join("0")}const h={"%":(t,n)=>(100*t).toFixed(n),b:t=>Math.round(t).toString(2),c:t=>t+"",d:s.Z,e:(t,n)=>t.toExponential(n),f:(t,n)=>t.toFixed(n),g:(t,n)=>t.toPrecision(n),o:t=>Math.round(t).toString(8),p:(t,n)=>a(100*t,n),r:a,s:function(t,n){var e=(0,s.V)(t,n);if(!e)return t+"";var r=e[0],o=e[1],a=o-(i=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,h=r.length;return a===h?r:a>h?r+new Array(a-h+1).join("0"):a>0?r.slice(0,a)+"."+r.slice(a):"0."+new Array(1-a).join("0")+(0,s.V)(t,Math.max(0,n+a-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function u(t){return t}var c=Array.prototype.map,l=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function f(t){var n,e,s=void 0===t.grouping||void 0===t.thousands?u:(n=c.call(t.grouping,Number),e=t.thousands+"",function(t,i){for(var r=t.length,o=[],s=0,a=n[0],h=0;r>0&&a>0&&(h+a+1>i&&(a=Math.max(1,i-h)),o.push(t.substring(r-=a,r+a)),!((h+=a+1)>i));)a=n[s=(s+1)%n.length];return o.reverse().join(e)}),a=void 0===t.currency?"":t.currency[0]+"",f=void 0===t.currency?"":t.currency[1]+"",_=void 0===t.decimal?".":t.decimal+"",p=void 0===t.numerals?u:function(t){return function(n){return n.replace(/[0-9]/g,(function(n){return t[+n]}))}}(c.call(t.numerals,String)),y=void 0===t.percent?"%":t.percent+"",g=void 0===t.minus?"−":t.minus+"",x=void 0===t.nan?"NaN":t.nan+"";function d(t){var n=(t=(0,o.Z)(t)).fill,e=t.align,r=t.sign,u=t.symbol,c=t.zero,d=t.width,v=t.comma,m=t.precision,w=t.trim,M=t.type;"n"===M?(v=!0,M="g"):h[M]||(void 0===m&&(m=12),w=!0,M="g"),(c||"0"===n&&"="===e)&&(c=!0,n="0",e="=");var b="$"===u?a:"#"===u&&/[boxX]/.test(M)?"0"+M.toLowerCase():"",Z="$"===u?f:/[%p]/.test(M)?y:"",T=h[M],N=/[defgprs%]/.test(M);function k(t){var o,a,h,u=b,f=Z;if("c"===M)f=T(t)+f,t="";else{var y=(t=+t)<0||1/t<0;if(t=isNaN(t)?x:T(Math.abs(t),m),w&&(t=function(t){t:for(var n,e=t.length,i=1,r=-1;i<e;++i)switch(t[i]){case".":r=n=i;break;case"0":0===r&&(r=i),n=i;break;default:if(!+t[i])break t;r>0&&(r=0)}return r>0?t.slice(0,r)+t.slice(n+1):t}(t)),y&&0==+t&&"+"!==r&&(y=!1),u=(y?"("===r?r:g:"-"===r||"("===r?"":r)+u,f=("s"===M?l[8+i/3]:"")+f+(y&&"("===r?")":""),N)for(o=-1,a=t.length;++o<a;)if(48>(h=t.charCodeAt(o))||h>57){f=(46===h?_+t.slice(o+1):t.slice(o))+f,t=t.slice(0,o);break}}v&&!c&&(t=s(t,1/0));var k=u.length+t.length+f.length,$=k<d?new Array(d-k+1).join(n):"";switch(v&&c&&(t=s($+t,$.length?d-f.length:1/0),$=""),e){case"<":t=u+t+f+$;break;case"=":t=u+$+t+f;break;case"^":t=$.slice(0,k=$.length>>1)+u+t+f+$.slice(k);break;default:t=$+u+t+f}return p(t)}return m=void 0===m?6:/[gprs]/.test(M)?Math.max(1,Math.min(21,m)):Math.max(0,Math.min(20,m)),k.toString=function(){return t+""},k}return{format:d,formatPrefix:function(t,n){var e=d(((t=(0,o.Z)(t)).type="f",t)),i=3*Math.max(-8,Math.min(8,Math.floor((0,r.Z)(n)/3))),s=Math.pow(10,-i),a=l[8+i/3];return function(t){return e(s*t)+a}}}}},39155:(t,n,e)=>{e.d(n,{Z:()=>r});var i=e(3044);function r(t){return Math.max(0,-(0,i.Z)(Math.abs(t)))}},61816:(t,n,e)=>{e.d(n,{Z:()=>r});var i=e(3044);function r(t,n){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor((0,i.Z)(n)/3)))-(0,i.Z)(Math.abs(t)))}},84767:(t,n,e)=>{e.d(n,{Z:()=>r});var i=e(3044);function r(t,n){return t=Math.abs(t),n=Math.abs(n)-t,Math.max(0,(0,i.Z)(n)-(0,i.Z)(t))+1}},5368:(t,n,e)=>{e.d(n,{M:()=>s,Z:()=>o});var i=e(54289),r=e(11083);function o(t,n){return((0,r.v)(n)?r.Z:s)(t,n)}function s(t,n){var e,r=n?n.length:0,o=t?Math.min(r,t.length):0,s=new Array(o),a=new Array(r);for(e=0;e<o;++e)s[e]=(0,i.Z)(t[e],n[e]);for(;e<r;++e)a[e]=n[e];return function(t){for(e=0;e<o;++e)a[e]=s[e](t);return a}}},31639:(t,n,e)=>{function i(t,n,e,i,r){var o=t*t,s=o*t;return((1-3*t+3*o-s)*n+(4-6*o+3*s)*e+(1+3*t+3*o-3*s)*i+s*r)/6}function r(t){var n=t.length-1;return function(e){var r=e<=0?e=0:e>=1?(e=1,n-1):Math.floor(e*n),o=t[r],s=t[r+1],a=r>0?t[r-1]:2*o-s,h=r<n-1?t[r+2]:2*s-o;return i((e-r/n)*n,a,o,s,h)}}e.d(n,{Z:()=>r,t:()=>i})},16772:(t,n,e)=>{e.d(n,{Z:()=>r});var i=e(31639);function r(t){var n=t.length;return function(e){var r=Math.floor(((e%=1)<0?++e:e)*n),o=t[(r+n-1)%n],s=t[r%n],a=t[(r+1)%n],h=t[(r+2)%n];return(0,i.t)((e-r/n)*n,o,s,a,h)}}},43133:(t,n,e)=>{e.d(n,{ZP:()=>a,wx:()=>o,yi:()=>s});var i=e(93064);function r(t,n){return function(e){return t+e*n}}function o(t,n){var e=n-t;return e?r(t,e>180||e<-180?e-360*Math.round(e/360):e):(0,i.Z)(isNaN(t)?n:t)}function s(t){return 1==(t=+t)?a:function(n,e){return e-n?function(t,n,e){return t=Math.pow(t,e),n=Math.pow(n,e)-t,e=1/e,function(i){return Math.pow(t+i*n,e)}}(n,e,t):(0,i.Z)(isNaN(n)?e:n)}}function a(t,n){var e=n-t;return e?r(t,e):(0,i.Z)(isNaN(t)?n:t)}},93064:(t,n,e)=>{e.d(n,{Z:()=>i});const i=t=>()=>t},54446:(t,n,e)=>{function i(t,n){var e=new Date;return t=+t,n=+n,function(i){return e.setTime(t*(1-i)+n*i),e}}e.d(n,{Z:()=>i})},69004:(t,n,e)=>{e.r(n),e.d(n,{interpolate:()=>i.Z,interpolateArray:()=>r.Z,interpolateBasis:()=>o.Z,interpolateBasisClosed:()=>s.Z,interpolateCubehelix:()=>q,interpolateCubehelixLong:()=>R,interpolateDate:()=>a.Z,interpolateDiscrete:()=>h,interpolateHcl:()=>k,interpolateHclLong:()=>$,interpolateHsl:()=>M,interpolateHslLong:()=>b,interpolateHue:()=>c,interpolateLab:()=>T,interpolateNumber:()=>l.Z,interpolateNumberArray:()=>f.Z,interpolateObject:()=>_.Z,interpolateRgb:()=>v.ZP,interpolateRgbBasis:()=>v.hD,interpolateRgbBasisClosed:()=>v.YD,interpolateRound:()=>p.Z,interpolateString:()=>y.Z,interpolateTransformCss:()=>g.Y,interpolateTransformSvg:()=>g.w,interpolateZoom:()=>d,piecewise:()=>L.Z,quantize:()=>J});var i=e(54289),r=e(5368),o=e(31639),s=e(16772),a=e(54446);function h(t){var n=t.length;return function(e){return t[Math.max(0,Math.min(n-1,Math.floor(e*n)))]}}var u=e(43133);function c(t,n){var e=(0,u.wx)(+t,+n);return function(t){var n=e(t);return n-360*Math.floor(n/360)}}var l=e(75381),f=e(11083),_=e(38863),p=e(58687),y=e(39493),g=e(52087);function x(t){return((t=Math.exp(t))+1/t)/2}const d=function t(n,e,i){function r(t,r){var o,s,a=t[0],h=t[1],u=t[2],c=r[0],l=r[1],f=r[2],_=c-a,p=l-h,y=_*_+p*p;if(y<1e-12)s=Math.log(f/u)/n,o=function(t){return[a+t*_,h+t*p,u*Math.exp(n*t*s)]};else{var g=Math.sqrt(y),d=(f*f-u*u+i*y)/(2*u*e*g),v=(f*f-u*u-i*y)/(2*f*e*g),m=Math.log(Math.sqrt(d*d+1)-d),w=Math.log(Math.sqrt(v*v+1)-v);s=(w-m)/n,o=function(t){var i,r=t*s,o=x(m),c=u/(e*g)*(o*(i=n*r+m,((i=Math.exp(2*i))-1)/(i+1))-function(t){return((t=Math.exp(t))-1/t)/2}(m));return[a+c*_,h+c*p,u*o/x(n*r+m)]}}return o.duration=1e3*s*n/Math.SQRT2,o}return r.rho=function(n){var e=Math.max(.001,+n),i=e*e;return t(e,i,i*i)},r}(Math.SQRT2,2,4);var v=e(32278),m=e(12738);function w(t){return function(n,e){var i=t((n=(0,m.Ym)(n)).h,(e=(0,m.Ym)(e)).h),r=(0,u.ZP)(n.s,e.s),o=(0,u.ZP)(n.l,e.l),s=(0,u.ZP)(n.opacity,e.opacity);return function(t){return n.h=i(t),n.s=r(t),n.l=o(t),n.opacity=s(t),n+""}}}const M=w(u.wx);var b=w(u.ZP),Z=e(64836);function T(t,n){var e=(0,u.ZP)((t=(0,Z.ZP)(t)).l,(n=(0,Z.ZP)(n)).l),i=(0,u.ZP)(t.a,n.a),r=(0,u.ZP)(t.b,n.b),o=(0,u.ZP)(t.opacity,n.opacity);return function(n){return t.l=e(n),t.a=i(n),t.b=r(n),t.opacity=o(n),t+""}}function N(t){return function(n,e){var i=t((n=(0,Z.Uc)(n)).h,(e=(0,Z.Uc)(e)).h),r=(0,u.ZP)(n.c,e.c),o=(0,u.ZP)(n.l,e.l),s=(0,u.ZP)(n.opacity,e.opacity);return function(t){return n.h=i(t),n.c=r(t),n.l=o(t),n.opacity=s(t),n+""}}}const k=N(u.wx);var $=N(u.ZP),C=e(77551),U=e(56091),D=-.14861,S=1.78277,P=-.29227,Y=-.90649,E=1.97294,A=E*Y,H=E*S,F=S*P-Y*D;function B(t,n,e,i){return 1===arguments.length?function(t){if(t instanceof O)return new O(t.h,t.s,t.l,t.opacity);t instanceof m.Ss||(t=(0,m.SU)(t));var n=t.r/255,e=t.g/255,i=t.b/255,r=(F*i+A*n-H*e)/(F+A-H),o=i-r,s=(E*(e-r)-P*o)/Y,a=Math.sqrt(s*s+o*o)/(E*r*(1-r)),h=a?Math.atan2(s,o)*U.R-120:NaN;return new O(h<0?h+360:h,a,r,t.opacity)}(t):new O(t,n,e,null==i?1:i)}function O(t,n,e,i){this.h=+t,this.s=+n,this.l=+e,this.opacity=+i}function j(t){return function n(e){function i(n,i){var r=t((n=B(n)).h,(i=B(i)).h),o=(0,u.ZP)(n.s,i.s),s=(0,u.ZP)(n.l,i.l),a=(0,u.ZP)(n.opacity,i.opacity);return function(t){return n.h=r(t),n.s=o(t),n.l=s(Math.pow(t,e)),n.opacity=a(t),n+""}}return e=+e,i.gamma=n,i}(1)}(0,C.Z)(O,B,(0,C.l)(m.Il,{brighter(t){return t=null==t?m.J5:Math.pow(m.J5,t),new O(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?m.xV:Math.pow(m.xV,t),new O(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=isNaN(this.h)?0:(this.h+120)*U.u,n=+this.l,e=isNaN(this.s)?0:this.s*n*(1-n),i=Math.cos(t),r=Math.sin(t);return new m.Ss(255*(n+e*(D*i+S*r)),255*(n+e*(P*i+Y*r)),255*(n+e*(E*i)),this.opacity)}}));const q=j(u.wx);var R=j(u.ZP),L=e(73682);function J(t,n){for(var e=new Array(n),i=0;i<n;++i)e[i]=t(i/(n-1));return e}},75381:(t,n,e)=>{function i(t,n){return t=+t,n=+n,function(e){return t*(1-e)+n*e}}e.d(n,{Z:()=>i})},11083:(t,n,e)=>{function i(t,n){n||(n=[]);var e,i=t?Math.min(n.length,t.length):0,r=n.slice();return function(o){for(e=0;e<i;++e)r[e]=t[e]*(1-o)+n[e]*o;return r}}function r(t){return ArrayBuffer.isView(t)&&!(t instanceof DataView)}e.d(n,{Z:()=>i,v:()=>r})},38863:(t,n,e)=>{e.d(n,{Z:()=>r});var i=e(54289);function r(t,n){var e,r={},o={};for(e in null!==t&&"object"==typeof t||(t={}),null!==n&&"object"==typeof n||(n={}),n)e in t?r[e]=(0,i.Z)(t[e],n[e]):o[e]=n[e];return function(t){for(e in r)o[e]=r[e](t);return o}}},73682:(t,n,e)=>{e.d(n,{Z:()=>r});var i=e(54289);function r(t,n){void 0===n&&(n=t,t=i.Z);for(var e=0,r=n.length-1,o=n[0],s=new Array(r<0?0:r);e<r;)s[e]=t(o,o=n[++e]);return function(t){var n=Math.max(0,Math.min(r-1,Math.floor(t*=r)));return s[n](t-n)}}},32278:(t,n,e)=>{e.d(n,{YD:()=>c,ZP:()=>a,hD:()=>u});var i=e(12738),r=e(31639),o=e(16772),s=e(43133);const a=function t(n){var e=(0,s.yi)(n);function r(t,n){var r=e((t=(0,i.B8)(t)).r,(n=(0,i.B8)(n)).r),o=e(t.g,n.g),a=e(t.b,n.b),h=(0,s.ZP)(t.opacity,n.opacity);return function(n){return t.r=r(n),t.g=o(n),t.b=a(n),t.opacity=h(n),t+""}}return r.gamma=t,r}(1);function h(t){return function(n){var e,r,o=n.length,s=new Array(o),a=new Array(o),h=new Array(o);for(e=0;e<o;++e)r=(0,i.B8)(n[e]),s[e]=r.r||0,a[e]=r.g||0,h[e]=r.b||0;return s=t(s),a=t(a),h=t(h),r.opacity=1,function(t){return r.r=s(t),r.g=a(t),r.b=h(t),r+""}}}var u=h(r.Z),c=h(o.Z)},58687:(t,n,e)=>{function i(t,n){return t=+t,n=+n,function(e){return Math.round(t*(1-e)+n*e)}}e.d(n,{Z:()=>i})},39493:(t,n,e)=>{e.d(n,{Z:()=>s});var i=e(75381),r=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,o=new RegExp(r.source,"g");function s(t,n){var e,s,a,h=r.lastIndex=o.lastIndex=0,u=-1,c=[],l=[];for(t+="",n+="";(e=r.exec(t))&&(s=o.exec(n));)(a=s.index)>h&&(a=n.slice(h,a),c[u]?c[u]+=a:c[++u]=a),(e=e[0])===(s=s[0])?c[u]?c[u]+=s:c[++u]=s:(c[++u]=null,l.push({i:u,x:(0,i.Z)(e,s)})),h=o.lastIndex;return h<n.length&&(a=n.slice(h),c[u]?c[u]+=a:c[++u]=a),c.length<2?l[0]?function(t){return function(n){return t(n)+""}}(l[0].x):function(t){return function(){return t}}(n):(n=l.length,function(t){for(var e,i=0;i<n;++i)c[(e=l[i]).i]=e.x(t);return c.join("")})}},52087:(t,n,e)=>{e.d(n,{Y:()=>u,w:()=>c});var i,r=e(75381),o=180/Math.PI,s={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function a(t,n,e,i,r,s){var a,h,u;return(a=Math.sqrt(t*t+n*n))&&(t/=a,n/=a),(u=t*e+n*i)&&(e-=t*u,i-=n*u),(h=Math.sqrt(e*e+i*i))&&(e/=h,i/=h,u/=h),t*i<n*e&&(t=-t,n=-n,u=-u,a=-a),{translateX:r,translateY:s,rotate:Math.atan2(n,t)*o,skewX:Math.atan(u)*o,scaleX:a,scaleY:h}}function h(t,n,e,i){function o(t){return t.length?t.pop()+" ":""}return function(s,a){var h=[],u=[];return s=t(s),a=t(a),function(t,i,o,s,a,h){if(t!==o||i!==s){var u=a.push("translate(",null,n,null,e);h.push({i:u-4,x:(0,r.Z)(t,o)},{i:u-2,x:(0,r.Z)(i,s)})}else(o||s)&&a.push("translate("+o+n+s+e)}(s.translateX,s.translateY,a.translateX,a.translateY,h,u),function(t,n,e,s){t!==n?(t-n>180?n+=360:n-t>180&&(t+=360),s.push({i:e.push(o(e)+"rotate(",null,i)-2,x:(0,r.Z)(t,n)})):n&&e.push(o(e)+"rotate("+n+i)}(s.rotate,a.rotate,h,u),function(t,n,e,s){t!==n?s.push({i:e.push(o(e)+"skewX(",null,i)-2,x:(0,r.Z)(t,n)}):n&&e.push(o(e)+"skewX("+n+i)}(s.skewX,a.skewX,h,u),function(t,n,e,i,s,a){if(t!==e||n!==i){var h=s.push(o(s)+"scale(",null,",",null,")");a.push({i:h-4,x:(0,r.Z)(t,e)},{i:h-2,x:(0,r.Z)(n,i)})}else 1===e&&1===i||s.push(o(s)+"scale("+e+","+i+")")}(s.scaleX,s.scaleY,a.scaleX,a.scaleY,h,u),s=a=null,function(t){for(var n,e=-1,i=u.length;++e<i;)h[(n=u[e]).i]=n.x(t);return h.join("")}}}var u=h((function(t){const n=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return n.isIdentity?s:a(n.a,n.b,n.c,n.d,n.e,n.f)}),"px, ","px)","deg)"),c=h((function(t){return null==t?s:(i||(i=document.createElementNS("http://www.w3.org/2000/svg","g")),i.setAttribute("transform",t),(t=i.transform.baseVal.consolidate())?a((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):s)}),", ",")",")")},54289:(t,n,e)=>{e.d(n,{Z:()=>f});var i=e(12738),r=e(32278),o=e(5368),s=e(54446),a=e(75381),h=e(38863),u=e(39493),c=e(93064),l=e(11083);function f(t,n){var e,f=typeof n;return null==n||"boolean"===f?(0,c.Z)(n):("number"===f?a.Z:"string"===f?(e=(0,i.ZP)(n))?(n=e,r.ZP):u.Z:n instanceof i.ZP?r.ZP:n instanceof Date?s.Z:(0,l.v)(n)?l.Z:Array.isArray(n)?o.M:"function"!=typeof n.valueOf&&"function"!=typeof n.toString||isNaN(n)?h.Z:a.Z)(t,n)}},3791:(t,n,e)=>{e.d(n,{ET:()=>u,y$:()=>h});const i=Math.PI,r=2*i,o=1e-6,s=r-o;function a(t){this._+=t[0];for(let n=1,e=t.length;n<e;++n)this._+=arguments[n]+t[n]}class h{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?a:function(t){let n=Math.floor(t);if(!(n>=0))throw new Error(`invalid digits: ${t}`);if(n>15)return a;const e=10**n;return function(t){this._+=t[0];for(let n=1,i=t.length;n<i;++n)this._+=Math.round(arguments[n]*e)/e+t[n]}}(t)}moveTo(t,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+n}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,n){this._append`L${this._x1=+t},${this._y1=+n}`}quadraticCurveTo(t,n,e,i){this._append`Q${+t},${+n},${this._x1=+e},${this._y1=+i}`}bezierCurveTo(t,n,e,i,r,o){this._append`C${+t},${+n},${+e},${+i},${this._x1=+r},${this._y1=+o}`}arcTo(t,n,e,r,s){if(t=+t,n=+n,e=+e,r=+r,(s=+s)<0)throw new Error(`negative radius: ${s}`);let a=this._x1,h=this._y1,u=e-t,c=r-n,l=a-t,f=h-n,_=l*l+f*f;if(null===this._x1)this._append`M${this._x1=t},${this._y1=n}`;else if(_>o)if(Math.abs(f*u-c*l)>o&&s){let p=e-a,y=r-h,g=u*u+c*c,x=p*p+y*y,d=Math.sqrt(g),v=Math.sqrt(_),m=s*Math.tan((i-Math.acos((g+_-x)/(2*d*v)))/2),w=m/v,M=m/d;Math.abs(w-1)>o&&this._append`L${t+w*l},${n+w*f}`,this._append`A${s},${s},0,0,${+(f*p>l*y)},${this._x1=t+M*u},${this._y1=n+M*c}`}else this._append`L${this._x1=t},${this._y1=n}`}arc(t,n,e,a,h,u){if(t=+t,n=+n,u=!!u,(e=+e)<0)throw new Error(`negative radius: ${e}`);let c=e*Math.cos(a),l=e*Math.sin(a),f=t+c,_=n+l,p=1^u,y=u?a-h:h-a;null===this._x1?this._append`M${f},${_}`:(Math.abs(this._x1-f)>o||Math.abs(this._y1-_)>o)&&this._append`L${f},${_}`,e&&(y<0&&(y=y%r+r),y>s?this._append`A${e},${e},0,1,${p},${t-c},${n-l}A${e},${e},0,1,${p},${this._x1=f},${this._y1=_}`:y>o&&this._append`A${e},${e},0,${+(y>=i)},${p},${this._x1=t+e*Math.cos(h)},${this._y1=n+e*Math.sin(h)}`)}rect(t,n,e,i){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+n}h${e=+e}v${+i}h${-e}Z`}toString(){return this._}}function u(){return new h}u.prototype=h.prototype},20356:(t,n,e)=>{e.d(n,{JG:()=>_,ZP:()=>y,yR:()=>u,l4:()=>p});var i=e(10321),r=e(54289),o=e(75381),s=e(58687),a=e(26481),h=[0,1];function u(t){return t}function c(t,n){return(n-=t=+t)?function(e){return(e-t)/n}:(e=isNaN(n)?NaN:.5,function(){return e});var e}function l(t,n,e){var i=t[0],r=t[1],o=n[0],s=n[1];return r<i?(i=c(r,i),o=e(s,o)):(i=c(i,r),o=e(o,s)),function(t){return o(i(t))}}function f(t,n,e){var r=Math.min(t.length,n.length)-1,o=new Array(r),s=new Array(r),a=-1;for(t[r]<t[0]&&(t=t.slice().reverse(),n=n.slice().reverse());++a<r;)o[a]=c(t[a],t[a+1]),s[a]=e(n[a],n[a+1]);return function(n){var e=(0,i.ZP)(t,n,1,r)-1;return s[e](o[e](n))}}function _(t,n){return n.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function p(){var t,n,e,i,c,_,p=h,y=h,g=r.Z,x=u;function d(){var t,n,e,r=Math.min(p.length,y.length);return x!==u&&(t=p[0],n=p[r-1],t>n&&(e=t,t=n,n=e),x=function(e){return Math.max(t,Math.min(n,e))}),i=r>2?f:l,c=_=null,v}function v(n){return null==n||isNaN(n=+n)?e:(c||(c=i(p.map(t),y,g)))(t(x(n)))}return v.invert=function(e){return x(n((_||(_=i(y,p.map(t),o.Z)))(e)))},v.domain=function(t){return arguments.length?(p=Array.from(t,a.Z),d()):p.slice()},v.range=function(t){return arguments.length?(y=Array.from(t),d()):y.slice()},v.rangeRound=function(t){return y=Array.from(t),g=s.Z,d()},v.clamp=function(t){return arguments.length?(x=!!t||u,d()):x!==u},v.interpolate=function(t){return arguments.length?(g=t,d()):g},v.unknown=function(t){return arguments.length?(e=t,v):e},function(e,i){return t=e,n=i,d()}}function y(){return p()(u,u)}},42287:(t,n,e)=>{function i(t,n){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(n).domain(t)}return this}function r(t,n){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof n?this.interpolator(n):this.range(n)}return this}e.d(n,{O:()=>r,o:()=>i})},29387:(t,n,e)=>{e.d(n,{Q:()=>a,Z:()=>h});var i=e(73002),r=e(20356),o=e(42287),s=e(35828);function a(t){var n=t.domain;return t.ticks=function(t){var e=n();return(0,i.ZP)(e[0],e[e.length-1],null==t?10:t)},t.tickFormat=function(t,e){var i=n();return(0,s.Z)(i[0],i[i.length-1],null==t?10:t,e)},t.nice=function(e){null==e&&(e=10);var r,o,s=n(),a=0,h=s.length-1,u=s[a],c=s[h],l=10;for(c<u&&(o=u,u=c,c=o,o=a,a=h,h=o);l-- >0;){if((o=(0,i.G9)(u,c,e))===r)return s[a]=u,s[h]=c,n(s);if(o>0)u=Math.floor(u/o)*o,c=Math.ceil(c/o)*o;else{if(!(o<0))break;u=Math.ceil(u*o)/o,c=Math.floor(c*o)/o}r=o}return t},t}function h(){var t=(0,r.ZP)();return t.copy=function(){return(0,r.JG)(t,h())},o.o.apply(t,arguments),a(t)}},78319:(t,n,e)=>{function i(t,n){var e,i=0,r=(t=t.slice()).length-1,o=t[i],s=t[r];return s<o&&(e=i,i=r,r=e,e=o,o=s,s=e),t[i]=n.floor(o),t[r]=n.ceil(s),t}e.d(n,{Z:()=>i})},26481:(t,n,e)=>{function i(t){return+t}e.d(n,{Z:()=>i})},81406:(t,n,e)=>{e.d(n,{O:()=>o,Z:()=>s});var i=e(18694),r=e(42287);const o=Symbol("implicit");function s(){var t=new i.L,n=[],e=[],a=o;function h(i){let r=t.get(i);if(void 0===r){if(a!==o)return a;t.set(i,r=n.push(i)-1)}return e[r%e.length]}return h.domain=function(e){if(!arguments.length)return n.slice();n=[],t=new i.L;for(const i of e)t.has(i)||t.set(i,n.push(i)-1);return h},h.range=function(t){return arguments.length?(e=Array.from(t),h):e.slice()},h.unknown=function(t){return arguments.length?(a=t,h):a},h.copy=function(){return s(n,e).unknown(a)},r.o.apply(h,arguments),h}},35828:(t,n,e)=>{e.d(n,{Z:()=>u});var i=e(73002),r=e(32159),o=e(61816),s=e(90741),a=e(84767),h=e(39155);function u(t,n,e,u){var c,l=(0,i.ly)(t,n,e);switch((u=(0,r.Z)(null==u?",f":u)).type){case"s":var f=Math.max(Math.abs(t),Math.abs(n));return null!=u.precision||isNaN(c=(0,o.Z)(l,f))||(u.precision=c),(0,s.jH)(u,f);case"":case"e":case"g":case"p":case"r":null!=u.precision||isNaN(c=(0,a.Z)(l,Math.max(Math.abs(t),Math.abs(n))))||(u.precision=c-("e"===u.type));break;case"f":case"%":null!=u.precision||isNaN(c=(0,h.Z)(l))||(u.precision=c-2*("%"===u.type))}return(0,s.WU)(u)}},61941:(t,n,e)=>{e.d(n,{Y:()=>x,Z:()=>d});var i=e(18744),r=e(47878),o=e(43155),s=e(24645),a=e(6054),h=e(75458),u=e(5957),c=e(58887),l=e(94031),f=e(20356),_=e(42287),p=e(78319);function y(t){return new Date(t)}function g(t){return t instanceof Date?+t:+new Date(+t)}function x(t,n,e,i,r,o,s,a,h,u){var c=(0,f.ZP)(),l=c.invert,_=c.domain,d=u(".%L"),v=u(":%S"),m=u("%I:%M"),w=u("%I %p"),M=u("%a %d"),b=u("%b %d"),Z=u("%B"),T=u("%Y");function N(t){return(h(t)<t?d:a(t)<t?v:s(t)<t?m:o(t)<t?w:i(t)<t?r(t)<t?M:b:e(t)<t?Z:T)(t)}return c.invert=function(t){return new Date(l(t))},c.domain=function(t){return arguments.length?_(Array.from(t,g)):_().map(y)},c.ticks=function(n){var e=_();return t(e[0],e[e.length-1],null==n?10:n)},c.tickFormat=function(t,n){return null==n?N:u(n)},c.nice=function(t){var e=_();return t&&"function"==typeof t.range||(t=n(e[0],e[e.length-1],null==t?10:t)),t?_((0,p.Z)(e,t)):c},c.copy=function(){return(0,f.JG)(c,x(t,n,e,i,r,o,s,a,h,u))},c}function d(){return _.o.apply(x(i.jK,i._g,r.jB,o.F0,s.Zy,a.rr,h.WQ,u.Z_,c.E,l.i$).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}},87826:(t,n,e)=>{e.d(n,{Z:()=>f});var i=e(17728),r=e(90596),o=e(53863);function s(t){return t.innerRadius}function a(t){return t.outerRadius}function h(t){return t.startAngle}function u(t){return t.endAngle}function c(t){return t&&t.padAngle}function l(t,n,e,i,o,s,a){var h=t-e,u=n-i,c=(a?s:-s)/(0,r._b)(h*h+u*u),l=c*u,f=-c*h,_=t+l,p=n+f,y=e+l,g=i+f,x=(_+y)/2,d=(p+g)/2,v=y-_,m=g-p,w=v*v+m*m,M=o-s,b=_*g-y*p,Z=(m<0?-1:1)*(0,r._b)((0,r.Fp)(0,M*M*w-b*b)),T=(b*m-v*Z)/w,N=(-b*v-m*Z)/w,k=(b*m+v*Z)/w,$=(-b*v+m*Z)/w,C=T-x,U=N-d,D=k-x,S=$-d;return C*C+U*U>D*D+S*S&&(T=k,N=$),{cx:T,cy:N,x01:-l,y01:-f,x11:T*(o/M-1),y11:N*(o/M-1)}}function f(){var t=s,n=a,e=(0,i.Z)(0),f=null,_=h,p=u,y=c,g=null,x=(0,o.d)(d);function d(){var i,o,s=+t.apply(this,arguments),a=+n.apply(this,arguments),h=_.apply(this,arguments)-r.ou,u=p.apply(this,arguments)-r.ou,c=(0,r.Wn)(u-h),d=u>h;if(g||(g=i=x()),a<s&&(o=a,a=s,s=o),a>r.Ho)if(c>r.BZ-r.Ho)g.moveTo(a*(0,r.mC)(h),a*(0,r.O$)(h)),g.arc(0,0,a,h,u,!d),s>r.Ho&&(g.moveTo(s*(0,r.mC)(u),s*(0,r.O$)(u)),g.arc(0,0,s,u,h,d));else{var v,m,w=h,M=u,b=h,Z=u,T=c,N=c,k=y.apply(this,arguments)/2,$=k>r.Ho&&(f?+f.apply(this,arguments):(0,r._b)(s*s+a*a)),C=(0,r.VV)((0,r.Wn)(a-s)/2,+e.apply(this,arguments)),U=C,D=C;if($>r.Ho){var S=(0,r.ZR)($/s*(0,r.O$)(k)),P=(0,r.ZR)($/a*(0,r.O$)(k));(T-=2*S)>r.Ho?(b+=S*=d?1:-1,Z-=S):(T=0,b=Z=(h+u)/2),(N-=2*P)>r.Ho?(w+=P*=d?1:-1,M-=P):(N=0,w=M=(h+u)/2)}var Y=a*(0,r.mC)(w),E=a*(0,r.O$)(w),A=s*(0,r.mC)(Z),H=s*(0,r.O$)(Z);if(C>r.Ho){var F,B=a*(0,r.mC)(M),O=a*(0,r.O$)(M),j=s*(0,r.mC)(b),q=s*(0,r.O$)(b);if(c<r.pi)if(F=function(t,n,e,i,o,s,a,h){var u=e-t,c=i-n,l=a-o,f=h-s,_=f*u-l*c;if(!(_*_<r.Ho))return[t+(_=(l*(n-s)-f*(t-o))/_)*u,n+_*c]}(Y,E,j,q,B,O,A,H)){var R=Y-F[0],L=E-F[1],J=B-F[0],z=O-F[1],I=1/(0,r.O$)((0,r.Kh)((R*J+L*z)/((0,r._b)(R*R+L*L)*(0,r._b)(J*J+z*z)))/2),V=(0,r._b)(F[0]*F[0]+F[1]*F[1]);U=(0,r.VV)(C,(s-V)/(I-1)),D=(0,r.VV)(C,(a-V)/(I+1))}else U=D=0}N>r.Ho?D>r.Ho?(v=l(j,q,Y,E,a,D,d),m=l(B,O,A,H,a,D,d),g.moveTo(v.cx+v.x01,v.cy+v.y01),D<C?g.arc(v.cx,v.cy,D,(0,r.fv)(v.y01,v.x01),(0,r.fv)(m.y01,m.x01),!d):(g.arc(v.cx,v.cy,D,(0,r.fv)(v.y01,v.x01),(0,r.fv)(v.y11,v.x11),!d),g.arc(0,0,a,(0,r.fv)(v.cy+v.y11,v.cx+v.x11),(0,r.fv)(m.cy+m.y11,m.cx+m.x11),!d),g.arc(m.cx,m.cy,D,(0,r.fv)(m.y11,m.x11),(0,r.fv)(m.y01,m.x01),!d))):(g.moveTo(Y,E),g.arc(0,0,a,w,M,!d)):g.moveTo(Y,E),s>r.Ho&&T>r.Ho?U>r.Ho?(v=l(A,H,B,O,s,-U,d),m=l(Y,E,j,q,s,-U,d),g.lineTo(v.cx+v.x01,v.cy+v.y01),U<C?g.arc(v.cx,v.cy,U,(0,r.fv)(v.y01,v.x01),(0,r.fv)(m.y01,m.x01),!d):(g.arc(v.cx,v.cy,U,(0,r.fv)(v.y01,v.x01),(0,r.fv)(v.y11,v.x11),!d),g.arc(0,0,s,(0,r.fv)(v.cy+v.y11,v.cx+v.x11),(0,r.fv)(m.cy+m.y11,m.cx+m.x11),d),g.arc(m.cx,m.cy,U,(0,r.fv)(m.y11,m.x11),(0,r.fv)(m.y01,m.x01),!d))):g.arc(0,0,s,Z,b,d):g.lineTo(A,H)}else g.moveTo(0,0);if(g.closePath(),i)return g=null,i+""||null}return d.centroid=function(){var e=(+t.apply(this,arguments)+ +n.apply(this,arguments))/2,i=(+_.apply(this,arguments)+ +p.apply(this,arguments))/2-r.pi/2;return[(0,r.mC)(i)*e,(0,r.O$)(i)*e]},d.innerRadius=function(n){return arguments.length?(t="function"==typeof n?n:(0,i.Z)(+n),d):t},d.outerRadius=function(t){return arguments.length?(n="function"==typeof t?t:(0,i.Z)(+t),d):n},d.cornerRadius=function(t){return arguments.length?(e="function"==typeof t?t:(0,i.Z)(+t),d):e},d.padRadius=function(t){return arguments.length?(f=null==t?null:"function"==typeof t?t:(0,i.Z)(+t),d):f},d.startAngle=function(t){return arguments.length?(_="function"==typeof t?t:(0,i.Z)(+t),d):_},d.endAngle=function(t){return arguments.length?(p="function"==typeof t?t:(0,i.Z)(+t),d):p},d.padAngle=function(t){return arguments.length?(y="function"==typeof t?t:(0,i.Z)(+t),d):y},d.context=function(t){return arguments.length?(g=null==t?null:t,d):g},d}},89555:(t,n,e)=>{function i(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}e.d(n,{Z:()=>i}),Array.prototype.slice},17728:(t,n,e)=>{function i(t){return function(){return t}}e.d(n,{Z:()=>i})},73021:(t,n,e)=>{function i(t,n,e){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+n)/6,(t._y0+4*t._y1+e)/6)}function r(t){this._context=t}function o(t){return new r(t)}e.d(n,{ZP:()=>o,fE:()=>r,xm:()=>i}),r.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:i(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:i(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}}},88973:(t,n,e)=>{e.d(n,{Z:()=>s});var i=e(98010),r=e(73021);function o(t){this._context=t}function s(t){return new o(t)}o.prototype={areaStart:i.Z,areaEnd:i.Z,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._x2=t,this._y2=n;break;case 1:this._point=2,this._x3=t,this._y3=n;break;case 2:this._point=3,this._x4=t,this._y4=n,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+n)/6);break;default:(0,r.xm)(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}}},74372:(t,n,e)=>{e.d(n,{Z:()=>o});var i=e(73021);function r(t){this._context=t}function o(t){return new r(t)}r.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var e=(this._x0+4*this._x1+t)/6,r=(this._y0+4*this._y1+n)/6;this._line?this._context.lineTo(e,r):this._context.moveTo(e,r);break;case 3:this._point=4;default:(0,i.xm)(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}}},32830:(t,n,e)=>{e.d(n,{Z:()=>o});var i=e(73021);function r(t,n){this._basis=new i.fE(t),this._beta=n}r.prototype={lineStart:function(){this._x=[],this._y=[],this._basis.lineStart()},lineEnd:function(){var t=this._x,n=this._y,e=t.length-1;if(e>0)for(var i,r=t[0],o=n[0],s=t[e]-r,a=n[e]-o,h=-1;++h<=e;)i=h/e,this._basis.point(this._beta*t[h]+(1-this._beta)*(r+i*s),this._beta*n[h]+(1-this._beta)*(o+i*a));this._x=this._y=null,this._basis.lineEnd()},point:function(t,n){this._x.push(+t),this._y.push(+n)}};const o=function t(n){function e(t){return 1===n?new i.fE(t):new r(t,n)}return e.beta=function(n){return t(+n)},e}(.85)},88800:(t,n,e)=>{function i(t,n,e){t._context.bezierCurveTo(t._x1+t._k*(t._x2-t._x0),t._y1+t._k*(t._y2-t._y0),t._x2+t._k*(t._x1-n),t._y2+t._k*(t._y1-e),t._x2,t._y2)}function r(t,n){this._context=t,this._k=(1-n)/6}e.d(n,{ZP:()=>o,pC:()=>r,xm:()=>i}),r.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:i(this,this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2,this._x1=t,this._y1=n;break;case 2:this._point=3;default:i(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};const o=function t(n){function e(t){return new r(t,n)}return e.tension=function(n){return t(+n)},e}(0)},91390:(t,n,e)=>{e.d(n,{U:()=>o,Z:()=>s});var i=e(98010),r=e(88800);function o(t,n){this._context=t,this._k=(1-n)/6}o.prototype={areaStart:i.Z,areaEnd:i.Z,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x3,this._y3),this._context.closePath();break;case 2:this._context.lineTo(this._x3,this._y3),this._context.closePath();break;case 3:this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5)}},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._x3=t,this._y3=n;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=n);break;case 2:this._point=3,this._x5=t,this._y5=n;break;default:(0,r.xm)(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};const s=function t(n){function e(t){return new o(t,n)}return e.tension=function(n){return t(+n)},e}(0)},41599:(t,n,e)=>{e.d(n,{T:()=>r,Z:()=>o});var i=e(88800);function r(t,n){this._context=t,this._k=(1-n)/6}r.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:(0,i.xm)(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};const o=function t(n){function e(t){return new r(t,n)}return e.tension=function(n){return t(+n)},e}(0)},48917:(t,n,e)=>{e.d(n,{Z:()=>a,x:()=>o});var i=e(90596),r=e(88800);function o(t,n,e){var r=t._x1,o=t._y1,s=t._x2,a=t._y2;if(t._l01_a>i.Ho){var h=2*t._l01_2a+3*t._l01_a*t._l12_a+t._l12_2a,u=3*t._l01_a*(t._l01_a+t._l12_a);r=(r*h-t._x0*t._l12_2a+t._x2*t._l01_2a)/u,o=(o*h-t._y0*t._l12_2a+t._y2*t._l01_2a)/u}if(t._l23_a>i.Ho){var c=2*t._l23_2a+3*t._l23_a*t._l12_a+t._l12_2a,l=3*t._l23_a*(t._l23_a+t._l12_a);s=(s*c+t._x1*t._l23_2a-n*t._l12_2a)/l,a=(a*c+t._y1*t._l23_2a-e*t._l12_2a)/l}t._context.bezierCurveTo(r,o,s,a,t._x2,t._y2)}function s(t,n){this._context=t,this._alpha=n}s.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:this.point(this._x2,this._y2)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){if(t=+t,n=+n,this._point){var e=this._x2-t,i=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+i*i,this._alpha))}switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3;default:o(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};const a=function t(n){function e(t){return n?new s(t,n):new r.pC(t,0)}return e.alpha=function(n){return t(+n)},e}(.5)},7391:(t,n,e)=>{e.d(n,{Z:()=>a});var i=e(91390),r=e(98010),o=e(48917);function s(t,n){this._context=t,this._alpha=n}s.prototype={areaStart:r.Z,areaEnd:r.Z,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x3,this._y3),this._context.closePath();break;case 2:this._context.lineTo(this._x3,this._y3),this._context.closePath();break;case 3:this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5)}},point:function(t,n){if(t=+t,n=+n,this._point){var e=this._x2-t,i=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+i*i,this._alpha))}switch(this._point){case 0:this._point=1,this._x3=t,this._y3=n;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=n);break;case 2:this._point=3,this._x5=t,this._y5=n;break;default:(0,o.x)(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};const a=function t(n){function e(t){return n?new s(t,n):new i.U(t,0)}return e.alpha=function(n){return t(+n)},e}(.5)},63703:(t,n,e)=>{e.d(n,{Z:()=>s});var i=e(41599),r=e(48917);function o(t,n){this._context=t,this._alpha=n}o.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){if(t=+t,n=+n,this._point){var e=this._x2-t,i=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+i*i,this._alpha))}switch(this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:(0,r.x)(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};const s=function t(n){function e(t){return n?new o(t,n):new i.T(t,0)}return e.alpha=function(n){return t(+n)},e}(.5)},4224:(t,n,e)=>{function i(t){this._context=t}function r(t){return new i(t)}e.d(n,{Z:()=>r}),i.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:this._context.lineTo(t,n)}}}},29458:(t,n,e)=>{e.d(n,{Z:()=>o});var i=e(98010);function r(t){this._context=t}function o(t){return new r(t)}r.prototype={areaStart:i.Z,areaEnd:i.Z,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,n){t=+t,n=+n,this._point?this._context.lineTo(t,n):(this._point=1,this._context.moveTo(t,n))}}},78509:(t,n,e)=>{function i(t){return t<0?-1:1}function r(t,n,e){var r=t._x1-t._x0,o=n-t._x1,s=(t._y1-t._y0)/(r||o<0&&-0),a=(e-t._y1)/(o||r<0&&-0),h=(s*o+a*r)/(r+o);return(i(s)+i(a))*Math.min(Math.abs(s),Math.abs(a),.5*Math.abs(h))||0}function o(t,n){var e=t._x1-t._x0;return e?(3*(t._y1-t._y0)/e-n)/2:n}function s(t,n,e){var i=t._x0,r=t._y0,o=t._x1,s=t._y1,a=(o-i)/3;t._context.bezierCurveTo(i+a,r+a*n,o-a,s-a*e,o,s)}function a(t){this._context=t}function h(t){this._context=new u(t)}function u(t){this._context=t}function c(t){return new a(t)}function l(t){return new h(t)}e.d(n,{Z:()=>c,s:()=>l}),a.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:s(this,this._t0,o(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){var e=NaN;if(n=+n,(t=+t)!==this._x1||n!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3,s(this,o(this,e=r(this,t,n)),e);break;default:s(this,this._t0,e=r(this,t,n))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n,this._t0=e}}},(h.prototype=Object.create(a.prototype)).point=function(t,n){a.prototype.point.call(this,n,t)},u.prototype={moveTo:function(t,n){this._context.moveTo(n,t)},closePath:function(){this._context.closePath()},lineTo:function(t,n){this._context.lineTo(n,t)},bezierCurveTo:function(t,n,e,i,r,o){this._context.bezierCurveTo(n,t,i,e,o,r)}}},3499:(t,n,e)=>{function i(t){this._context=t}function r(t){var n,e,i=t.length-1,r=new Array(i),o=new Array(i),s=new Array(i);for(r[0]=0,o[0]=2,s[0]=t[0]+2*t[1],n=1;n<i-1;++n)r[n]=1,o[n]=4,s[n]=4*t[n]+2*t[n+1];for(r[i-1]=2,o[i-1]=7,s[i-1]=8*t[i-1]+t[i],n=1;n<i;++n)e=r[n]/o[n-1],o[n]-=e,s[n]-=e*s[n-1];for(r[i-1]=s[i-1]/o[i-1],n=i-2;n>=0;--n)r[n]=(s[n]-r[n+1])/o[n];for(o[i-1]=(t[i]+r[i-1])/2,n=0;n<i-1;++n)o[n]=2*t[n+1]-r[n+1];return[r,o]}function o(t){return new i(t)}e.d(n,{Z:()=>o}),i.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,n=this._y,e=t.length;if(e)if(this._line?this._context.lineTo(t[0],n[0]):this._context.moveTo(t[0],n[0]),2===e)this._context.lineTo(t[1],n[1]);else for(var i=r(t),o=r(n),s=0,a=1;a<e;++s,++a)this._context.bezierCurveTo(i[0][s],o[0][s],i[1][s],o[1][s],t[a],n[a]);(this._line||0!==this._line&&1===e)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,n){this._x.push(+t),this._y.push(+n)}}},21468:(t,n,e)=>{function i(t,n){this._context=t,this._t=n}function r(t){return new i(t,.5)}function o(t){return new i(t,0)}function s(t){return new i(t,1)}e.d(n,{RN:()=>o,ZP:()=>r,cD:()=>s}),i.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,n),this._context.lineTo(t,n);else{var e=this._x*(1-this._t)+t*this._t;this._context.lineTo(e,this._y),this._context.lineTo(e,n)}}this._x=t,this._y=n}}},40652:(t,n,e)=>{e.d(n,{Z:()=>h});var i=e(89555),r=e(17728),o=e(4224),s=e(53863),a=e(41221);function h(t,n){var e=(0,r.Z)(!0),h=null,u=o.Z,c=null,l=(0,s.d)(f);function f(r){var o,s,a,f=(r=(0,i.Z)(r)).length,_=!1;for(null==h&&(c=u(a=l())),o=0;o<=f;++o)!(o<f&&e(s=r[o],o,r))===_&&((_=!_)?c.lineStart():c.lineEnd()),_&&c.point(+t(s,o,r),+n(s,o,r));if(a)return c=null,a+""||null}return t="function"==typeof t?t:void 0===t?a.x:(0,r.Z)(t),n="function"==typeof n?n:void 0===n?a.y:(0,r.Z)(n),f.x=function(n){return arguments.length?(t="function"==typeof n?n:(0,r.Z)(+n),f):t},f.y=function(t){return arguments.length?(n="function"==typeof t?t:(0,r.Z)(+t),f):n},f.defined=function(t){return arguments.length?(e="function"==typeof t?t:(0,r.Z)(!!t),f):e},f.curve=function(t){return arguments.length?(u=t,null!=h&&(c=u(h)),f):u},f.context=function(t){return arguments.length?(null==t?h=c=null:c=u(h=t),f):h},f}},90596:(t,n,e)=>{e.d(n,{BZ:()=>_,Fp:()=>s,Ho:()=>c,Kh:()=>p,O$:()=>h,VV:()=>a,Wn:()=>i,ZR:()=>y,_b:()=>u,fv:()=>r,mC:()=>o,ou:()=>f,pi:()=>l});const i=Math.abs,r=Math.atan2,o=Math.cos,s=Math.max,a=Math.min,h=Math.sin,u=Math.sqrt,c=1e-12,l=Math.PI,f=l/2,_=2*l;function p(t){return t>1?0:t<-1?l:Math.acos(t)}function y(t){return t>=1?f:t<=-1?-f:Math.asin(t)}},98010:(t,n,e)=>{function i(){}e.d(n,{Z:()=>i})},53863:(t,n,e)=>{e.d(n,{d:()=>r});var i=e(3791);function r(t){let n=3;return t.digits=function(e){if(!arguments.length)return n;if(null==e)n=null;else{const t=Math.floor(e);if(!(t>=0))throw new RangeError(`invalid digits: ${e}`);n=t}return t},()=>new i.y$(n)}},41221:(t,n,e)=>{function i(t){return t[0]}function r(t){return t[1]}e.d(n,{x:()=>i,y:()=>r})},94031:(t,n,e)=>{var i,r,o,s,a,h;e.d(n,{Z1:()=>o,g0:()=>s,i$:()=>r,wp:()=>a}),h={dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]},i=(0,e(6367).Z)(h),r=i.format,o=i.parse,s=i.utcFormat,a=i.utcParse},6367:(t,n,e)=>{e.d(n,{Z:()=>u});var i=e(24645),r=e(6054),o=e(47878);function s(t){if(0<=t.y&&t.y<100){var n=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return n.setFullYear(t.y),n}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function a(t){if(0<=t.y&&t.y<100){var n=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return n.setUTCFullYear(t.y),n}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function h(t,n,e){return{y:t,m:n,d:e,H:0,M:0,S:0,L:0}}function u(t){var n=t.dateTime,e=t.date,o=t.time,u=t.periods,l=t.days,f=t.shortDays,_=t.months,p=t.shortMonths,y=g(u),X=x(u),yt=g(l),kt=x(l),$t=g(f),Ct=x(f),Ut=g(_),Dt=x(_),St=g(p),Pt=x(p),Yt={a:function(t){return f[t.getDay()]},A:function(t){return l[t.getDay()]},b:function(t){return p[t.getMonth()]},B:function(t){return _[t.getMonth()]},c:null,d:F,e:F,f:R,g:tt,G:et,H:B,I:O,j,L:q,m:L,M:J,p:function(t){return u[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:Tt,s:Nt,S:z,u:I,U:V,V:W,w:Q,W:G,x:null,X:null,y:K,Y:nt,Z:it,"%":Zt},Et={a:function(t){return f[t.getUTCDay()]},A:function(t){return l[t.getUTCDay()]},b:function(t){return p[t.getUTCMonth()]},B:function(t){return _[t.getUTCMonth()]},c:null,d:rt,e:rt,f:ut,g:mt,G:Mt,H:ot,I:st,j:at,L:ht,m:ct,M:lt,p:function(t){return u[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:Tt,s:Nt,S:ft,u:_t,U:pt,V:gt,w:xt,W:dt,x:null,X:null,y:vt,Y:wt,Z:bt,"%":Zt},At={a:function(t,n,e){var i=$t.exec(n.slice(e));return i?(t.w=Ct.get(i[0].toLowerCase()),e+i[0].length):-1},A:function(t,n,e){var i=yt.exec(n.slice(e));return i?(t.w=kt.get(i[0].toLowerCase()),e+i[0].length):-1},b:function(t,n,e){var i=St.exec(n.slice(e));return i?(t.m=Pt.get(i[0].toLowerCase()),e+i[0].length):-1},B:function(t,n,e){var i=Ut.exec(n.slice(e));return i?(t.m=Dt.get(i[0].toLowerCase()),e+i[0].length):-1},c:function(t,e,i){return Bt(t,n,e,i)},d:$,e:$,f:Y,g:Z,G:b,H:U,I:U,j:C,L:P,m:k,M:D,p:function(t,n,e){var i=y.exec(n.slice(e));return i?(t.p=X.get(i[0].toLowerCase()),e+i[0].length):-1},q:N,Q:A,s:H,S,u:v,U:m,V:w,w:d,W:M,x:function(t,n,i){return Bt(t,e,n,i)},X:function(t,n,e){return Bt(t,o,n,e)},y:Z,Y:b,Z:T,"%":E};function Ht(t,n){return function(e){var i,r,o,s=[],a=-1,h=0,u=t.length;for(e instanceof Date||(e=new Date(+e));++a<u;)37===t.charCodeAt(a)&&(s.push(t.slice(h,a)),null!=(r=c[i=t.charAt(++a)])?i=t.charAt(++a):r="e"===i?" ":"0",(o=n[i])&&(i=o(e,r)),s.push(i),h=a+1);return s.push(t.slice(h,a)),s.join("")}}function Ft(t,n){return function(e){var o,u,c=h(1900,void 0,1);if(Bt(c,t,e+="",0)!=e.length)return null;if("Q"in c)return new Date(c.Q);if("s"in c)return new Date(1e3*c.s+("L"in c?c.L:0));if(n&&!("Z"in c)&&(c.Z=0),"p"in c&&(c.H=c.H%12+12*c.p),void 0===c.m&&(c.m="q"in c?c.q:0),"V"in c){if(c.V<1||c.V>53)return null;"w"in c||(c.w=1),"Z"in c?(u=(o=a(h(c.y,0,1))).getUTCDay(),o=u>4||0===u?i.l6.ceil(o):(0,i.l6)(o),o=r.AN.offset(o,7*(c.V-1)),c.y=o.getUTCFullYear(),c.m=o.getUTCMonth(),c.d=o.getUTCDate()+(c.w+6)%7):(u=(o=s(h(c.y,0,1))).getDay(),o=u>4||0===u?i.Ox.ceil(o):(0,i.Ox)(o),o=r.rr.offset(o,7*(c.V-1)),c.y=o.getFullYear(),c.m=o.getMonth(),c.d=o.getDate()+(c.w+6)%7)}else("W"in c||"U"in c)&&("w"in c||(c.w="u"in c?c.u%7:"W"in c?1:0),u="Z"in c?a(h(c.y,0,1)).getUTCDay():s(h(c.y,0,1)).getDay(),c.m=0,c.d="W"in c?(c.w+6)%7+7*c.W-(u+5)%7:c.w+7*c.U-(u+6)%7);return"Z"in c?(c.H+=c.Z/100|0,c.M+=c.Z%100,a(c)):s(c)}}function Bt(t,n,e,i){for(var r,o,s=0,a=n.length,h=e.length;s<a;){if(i>=h)return-1;if(37===(r=n.charCodeAt(s++))){if(r=n.charAt(s++),!(o=At[r in c?n.charAt(s++):r])||(i=o(t,e,i))<0)return-1}else if(r!=e.charCodeAt(i++))return-1}return i}return Yt.x=Ht(e,Yt),Yt.X=Ht(o,Yt),Yt.c=Ht(n,Yt),Et.x=Ht(e,Et),Et.X=Ht(o,Et),Et.c=Ht(n,Et),{format:function(t){var n=Ht(t+="",Yt);return n.toString=function(){return t},n},parse:function(t){var n=Ft(t+="",!1);return n.toString=function(){return t},n},utcFormat:function(t){var n=Ht(t+="",Et);return n.toString=function(){return t},n},utcParse:function(t){var n=Ft(t+="",!0);return n.toString=function(){return t},n}}}var c={"-":"",_:" ",0:"0"},l=/^\s*\d+/,f=/^%/,_=/[\\^$*+?|[\]().{}]/g;function p(t,n,e){var i=t<0?"-":"",r=(i?-t:t)+"",o=r.length;return i+(o<e?new Array(e-o+1).join(n)+r:r)}function y(t){return t.replace(_,"\\$&")}function g(t){return new RegExp("^(?:"+t.map(y).join("|")+")","i")}function x(t){return new Map(t.map(((t,n)=>[t.toLowerCase(),n])))}function d(t,n,e){var i=l.exec(n.slice(e,e+1));return i?(t.w=+i[0],e+i[0].length):-1}function v(t,n,e){var i=l.exec(n.slice(e,e+1));return i?(t.u=+i[0],e+i[0].length):-1}function m(t,n,e){var i=l.exec(n.slice(e,e+2));return i?(t.U=+i[0],e+i[0].length):-1}function w(t,n,e){var i=l.exec(n.slice(e,e+2));return i?(t.V=+i[0],e+i[0].length):-1}function M(t,n,e){var i=l.exec(n.slice(e,e+2));return i?(t.W=+i[0],e+i[0].length):-1}function b(t,n,e){var i=l.exec(n.slice(e,e+4));return i?(t.y=+i[0],e+i[0].length):-1}function Z(t,n,e){var i=l.exec(n.slice(e,e+2));return i?(t.y=+i[0]+(+i[0]>68?1900:2e3),e+i[0].length):-1}function T(t,n,e){var i=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(n.slice(e,e+6));return i?(t.Z=i[1]?0:-(i[2]+(i[3]||"00")),e+i[0].length):-1}function N(t,n,e){var i=l.exec(n.slice(e,e+1));return i?(t.q=3*i[0]-3,e+i[0].length):-1}function k(t,n,e){var i=l.exec(n.slice(e,e+2));return i?(t.m=i[0]-1,e+i[0].length):-1}function $(t,n,e){var i=l.exec(n.slice(e,e+2));return i?(t.d=+i[0],e+i[0].length):-1}function C(t,n,e){var i=l.exec(n.slice(e,e+3));return i?(t.m=0,t.d=+i[0],e+i[0].length):-1}function U(t,n,e){var i=l.exec(n.slice(e,e+2));return i?(t.H=+i[0],e+i[0].length):-1}function D(t,n,e){var i=l.exec(n.slice(e,e+2));return i?(t.M=+i[0],e+i[0].length):-1}function S(t,n,e){var i=l.exec(n.slice(e,e+2));return i?(t.S=+i[0],e+i[0].length):-1}function P(t,n,e){var i=l.exec(n.slice(e,e+3));return i?(t.L=+i[0],e+i[0].length):-1}function Y(t,n,e){var i=l.exec(n.slice(e,e+6));return i?(t.L=Math.floor(i[0]/1e3),e+i[0].length):-1}function E(t,n,e){var i=f.exec(n.slice(e,e+1));return i?e+i[0].length:-1}function A(t,n,e){var i=l.exec(n.slice(e));return i?(t.Q=+i[0],e+i[0].length):-1}function H(t,n,e){var i=l.exec(n.slice(e));return i?(t.s=+i[0],e+i[0].length):-1}function F(t,n){return p(t.getDate(),n,2)}function B(t,n){return p(t.getHours(),n,2)}function O(t,n){return p(t.getHours()%12||12,n,2)}function j(t,n){return p(1+r.rr.count((0,o.jB)(t),t),n,3)}function q(t,n){return p(t.getMilliseconds(),n,3)}function R(t,n){return q(t,n)+"000"}function L(t,n){return p(t.getMonth()+1,n,2)}function J(t,n){return p(t.getMinutes(),n,2)}function z(t,n){return p(t.getSeconds(),n,2)}function I(t){var n=t.getDay();return 0===n?7:n}function V(t,n){return p(i.Zy.count((0,o.jB)(t)-1,t),n,2)}function X(t){var n=t.getDay();return n>=4||0===n?(0,i.Ig)(t):i.Ig.ceil(t)}function W(t,n){return t=X(t),p(i.Ig.count((0,o.jB)(t),t)+(4===(0,o.jB)(t).getDay()),n,2)}function Q(t){return t.getDay()}function G(t,n){return p(i.Ox.count((0,o.jB)(t)-1,t),n,2)}function K(t,n){return p(t.getFullYear()%100,n,2)}function tt(t,n){return p((t=X(t)).getFullYear()%100,n,2)}function nt(t,n){return p(t.getFullYear()%1e4,n,4)}function et(t,n){var e=t.getDay();return p((t=e>=4||0===e?(0,i.Ig)(t):i.Ig.ceil(t)).getFullYear()%1e4,n,4)}function it(t){var n=t.getTimezoneOffset();return(n>0?"-":(n*=-1,"+"))+p(n/60|0,"0",2)+p(n%60,"0",2)}function rt(t,n){return p(t.getUTCDate(),n,2)}function ot(t,n){return p(t.getUTCHours(),n,2)}function st(t,n){return p(t.getUTCHours()%12||12,n,2)}function at(t,n){return p(1+r.AN.count((0,o.ol)(t),t),n,3)}function ht(t,n){return p(t.getUTCMilliseconds(),n,3)}function ut(t,n){return ht(t,n)+"000"}function ct(t,n){return p(t.getUTCMonth()+1,n,2)}function lt(t,n){return p(t.getUTCMinutes(),n,2)}function ft(t,n){return p(t.getUTCSeconds(),n,2)}function _t(t){var n=t.getUTCDay();return 0===n?7:n}function pt(t,n){return p(i.pI.count((0,o.ol)(t)-1,t),n,2)}function yt(t){var n=t.getUTCDay();return n>=4||0===n?(0,i.hB)(t):i.hB.ceil(t)}function gt(t,n){return t=yt(t),p(i.hB.count((0,o.ol)(t),t)+(4===(0,o.ol)(t).getUTCDay()),n,2)}function xt(t){return t.getUTCDay()}function dt(t,n){return p(i.l6.count((0,o.ol)(t)-1,t),n,2)}function vt(t,n){return p(t.getUTCFullYear()%100,n,2)}function mt(t,n){return p((t=yt(t)).getUTCFullYear()%100,n,2)}function wt(t,n){return p(t.getUTCFullYear()%1e4,n,4)}function Mt(t,n){var e=t.getUTCDay();return p((t=e>=4||0===e?(0,i.hB)(t):i.hB.ceil(t)).getUTCFullYear()%1e4,n,4)}function bt(){return"+0000"}function Zt(){return"%"}function Tt(t){return+t}function Nt(t){return Math.floor(+t/1e3)}},6054:(t,n,e)=>{e.d(n,{AN:()=>s,KB:()=>a,rr:()=>o});var i=e(66712),r=e(45022);const o=(0,i.J)((t=>t.setHours(0,0,0,0)),((t,n)=>t.setDate(t.getDate()+n)),((t,n)=>(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*r.yB)/r.UD),(t=>t.getDate()-1)),s=(o.range,(0,i.J)((t=>{t.setUTCHours(0,0,0,0)}),((t,n)=>{t.setUTCDate(t.getUTCDate()+n)}),((t,n)=>(n-t)/r.UD),(t=>t.getUTCDate()-1))),a=(s.range,(0,i.J)((t=>{t.setUTCHours(0,0,0,0)}),((t,n)=>{t.setUTCDate(t.getUTCDate()+n)}),((t,n)=>(n-t)/r.UD),(t=>Math.floor(t/r.UD))));a.range},45022:(t,n,e)=>{e.d(n,{UD:()=>s,Y2:()=>o,Ym:()=>i,iM:()=>a,jz:()=>h,qz:()=>u,yB:()=>r});const i=1e3,r=60*i,o=60*r,s=24*o,a=7*s,h=30*s,u=365*s},75458:(t,n,e)=>{e.d(n,{WQ:()=>o,lM:()=>s});var i=e(66712),r=e(45022);const o=(0,i.J)((t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*r.Ym-t.getMinutes()*r.yB)}),((t,n)=>{t.setTime(+t+n*r.Y2)}),((t,n)=>(n-t)/r.Y2),(t=>t.getHours())),s=(o.range,(0,i.J)((t=>{t.setUTCMinutes(0,0,0)}),((t,n)=>{t.setTime(+t+n*r.Y2)}),((t,n)=>(n-t)/r.Y2),(t=>t.getUTCHours())));s.range},66712:(t,n,e)=>{e.d(n,{J:()=>o});const i=new Date,r=new Date;function o(t,n,e,s){function a(n){return t(n=0===arguments.length?new Date:new Date(+n)),n}return a.floor=n=>(t(n=new Date(+n)),n),a.ceil=e=>(t(e=new Date(e-1)),n(e,1),t(e),e),a.round=t=>{const n=a(t),e=a.ceil(t);return t-n<e-t?n:e},a.offset=(t,e)=>(n(t=new Date(+t),null==e?1:Math.floor(e)),t),a.range=(e,i,r)=>{const o=[];if(e=a.ceil(e),r=null==r?1:Math.floor(r),!(e<i&&r>0))return o;let s;do{o.push(s=new Date(+e)),n(e,r),t(e)}while(s<e&&e<i);return o},a.filter=e=>o((n=>{if(n>=n)for(;t(n),!e(n);)n.setTime(n-1)}),((t,i)=>{if(t>=t)if(i<0)for(;++i<=0;)for(;n(t,-1),!e(t););else for(;--i>=0;)for(;n(t,1),!e(t););})),e&&(a.count=(n,o)=>(i.setTime(+n),r.setTime(+o),t(i),t(r),Math.floor(e(i,r))),a.every=t=>(t=Math.floor(t),isFinite(t)&&t>0?t>1?a.filter(s?n=>s(n)%t==0:n=>a.count(0,n)%t==0):a:null)),a}},17540:(t,n,e)=>{e.d(n,{A:()=>r});var i=e(66712);const r=(0,i.J)((()=>{}),((t,n)=>{t.setTime(+t+n)}),((t,n)=>n-t));r.every=t=>(t=Math.floor(t),isFinite(t)&&t>0?t>1?(0,i.J)((n=>{n.setTime(Math.floor(n/t)*t)}),((n,e)=>{n.setTime(+n+e*t)}),((n,e)=>(e-n)/t)):r:null),r.range},5957:(t,n,e)=>{e.d(n,{Z_:()=>o,rz:()=>s});var i=e(66712),r=e(45022);const o=(0,i.J)((t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*r.Ym)}),((t,n)=>{t.setTime(+t+n*r.yB)}),((t,n)=>(n-t)/r.yB),(t=>t.getMinutes())),s=(o.range,(0,i.J)((t=>{t.setUTCSeconds(0,0)}),((t,n)=>{t.setTime(+t+n*r.yB)}),((t,n)=>(n-t)/r.yB),(t=>t.getUTCMinutes())));s.range},43155:(t,n,e)=>{e.d(n,{F0:()=>r,me:()=>o});var i=e(66712);const r=(0,i.J)((t=>{t.setDate(1),t.setHours(0,0,0,0)}),((t,n)=>{t.setMonth(t.getMonth()+n)}),((t,n)=>n.getMonth()-t.getMonth()+12*(n.getFullYear()-t.getFullYear())),(t=>t.getMonth())),o=(r.range,(0,i.J)((t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)}),((t,n)=>{t.setUTCMonth(t.getUTCMonth()+n)}),((t,n)=>n.getUTCMonth()-t.getUTCMonth()+12*(n.getUTCFullYear()-t.getUTCFullYear())),(t=>t.getUTCMonth())));o.range},58887:(t,n,e)=>{e.d(n,{E:()=>o});var i=e(66712),r=e(45022);const o=(0,i.J)((t=>{t.setTime(t-t.getMilliseconds())}),((t,n)=>{t.setTime(+t+n*r.Ym)}),((t,n)=>(n-t)/r.Ym),(t=>t.getUTCSeconds()));o.range},18744:(t,n,e)=>{e.d(n,{WG:()=>y,_g:()=>d,jK:()=>x,jo:()=>g});var i=e(2518),r=e(73002),o=e(45022),s=e(17540),a=e(58887),h=e(5957),u=e(75458),c=e(6054),l=e(24645),f=e(43155),_=e(47878);function p(t,n,e,h,u,c){const l=[[a.E,1,o.Ym],[a.E,5,5*o.Ym],[a.E,15,15*o.Ym],[a.E,30,30*o.Ym],[c,1,o.yB],[c,5,5*o.yB],[c,15,15*o.yB],[c,30,30*o.yB],[u,1,o.Y2],[u,3,3*o.Y2],[u,6,6*o.Y2],[u,12,12*o.Y2],[h,1,o.UD],[h,2,2*o.UD],[e,1,o.iM],[n,1,o.jz],[n,3,3*o.jz],[t,1,o.qz]];function f(n,e,a){const h=Math.abs(e-n)/a,u=(0,i.Z)((([,,t])=>t)).right(l,h);if(u===l.length)return t.every((0,r.ly)(n/o.qz,e/o.qz,a));if(0===u)return s.A.every(Math.max((0,r.ly)(n,e,a),1));const[c,f]=l[h/l[u-1][2]<l[u][2]/h?u-1:u];return c.every(f)}return[function(t,n,e){const i=n<t;i&&([t,n]=[n,t]);const r=e&&"function"==typeof e.range?e:f(t,n,e),o=r?r.range(t,+n+1):[];return i?o.reverse():o},f]}const[y,g]=p(_.ol,f.me,l.pI,c.KB,u.lM,h.rz),[x,d]=p(_.jB,f.F0,l.Zy,c.rr,u.WQ,h.Z_)},24645:(t,n,e)=>{e.d(n,{EF:()=>u,Ig:()=>c,Lq:()=>f,Ox:()=>a,YD:()=>h,Zy:()=>s,hB:()=>d,l6:()=>y,pI:()=>p,y2:()=>l});var i=e(66712),r=e(45022);function o(t){return(0,i.J)((n=>{n.setDate(n.getDate()-(n.getDay()+7-t)%7),n.setHours(0,0,0,0)}),((t,n)=>{t.setDate(t.getDate()+7*n)}),((t,n)=>(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*r.yB)/r.iM))}const s=o(0),a=o(1),h=o(2),u=o(3),c=o(4),l=o(5),f=o(6);function _(t){return(0,i.J)((n=>{n.setUTCDate(n.getUTCDate()-(n.getUTCDay()+7-t)%7),n.setUTCHours(0,0,0,0)}),((t,n)=>{t.setUTCDate(t.getUTCDate()+7*n)}),((t,n)=>(n-t)/r.iM))}s.range,a.range,h.range,u.range,c.range,l.range,f.range;const p=_(0),y=_(1),g=_(2),x=_(3),d=_(4),v=_(5),m=_(6);p.range,y.range,g.range,x.range,d.range,v.range,m.range},47878:(t,n,e)=>{e.d(n,{jB:()=>r,ol:()=>o});var i=e(66712);const r=(0,i.J)((t=>{t.setMonth(0,1),t.setHours(0,0,0,0)}),((t,n)=>{t.setFullYear(t.getFullYear()+n)}),((t,n)=>n.getFullYear()-t.getFullYear()),(t=>t.getFullYear()));r.every=t=>isFinite(t=Math.floor(t))&&t>0?(0,i.J)((n=>{n.setFullYear(Math.floor(n.getFullYear()/t)*t),n.setMonth(0,1),n.setHours(0,0,0,0)}),((n,e)=>{n.setFullYear(n.getFullYear()+e*t)})):null,r.range;const o=(0,i.J)((t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)}),((t,n)=>{t.setUTCFullYear(t.getUTCFullYear()+n)}),((t,n)=>n.getUTCFullYear()-t.getUTCFullYear()),(t=>t.getUTCFullYear()));o.every=t=>isFinite(t=Math.floor(t))&&t>0?(0,i.J)((n=>{n.setUTCFullYear(Math.floor(n.getUTCFullYear()/t)*t),n.setUTCMonth(0,1),n.setUTCHours(0,0,0,0)}),((n,e)=>{n.setUTCFullYear(n.getUTCFullYear()+e*t)})):null,o.range},11647:(t,n,e)=>{e.d(n,{B7:()=>g,HT:()=>x,zO:()=>p});var i,r,o=0,s=0,a=0,h=1e3,u=0,c=0,l=0,f="object"==typeof performance&&performance.now?performance:Date,_="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function p(){return c||(_(y),c=f.now()+l)}function y(){c=0}function g(){this._call=this._time=this._next=null}function x(t,n,e){var i=new g;return i.restart(t,n,e),i}function d(){c=(u=f.now())+l,o=s=0;try{!function(){p(),++o;for(var t,n=i;n;)(t=c-n._time)>=0&&n._call.call(void 0,t),n=n._next;--o}()}finally{o=0,function(){for(var t,n,e=i,o=1/0;e;)e._call?(o>e._time&&(o=e._time),t=e,e=e._next):(n=e._next,e._next=null,e=t?t._next=n:i=n);r=t,m(o)}(),c=0}}function v(){var t=f.now(),n=t-u;n>h&&(l-=n,u=t)}function m(t){o||(s&&(s=clearTimeout(s)),t-c>24?(t<1/0&&(s=setTimeout(d,t-f.now()-l)),a&&(a=clearInterval(a))):(a||(u=f.now(),a=setInterval(v,h)),o=1,_(d)))}g.prototype=x.prototype={constructor:g,restart:function(t,n,e){if("function"!=typeof t)throw new TypeError("callback is not a function");e=(null==e?p():+e)+(null==n?0:+n),this._next||r===this||(r?r._next=this:i=this,r=this),this._call=t,this._time=e,m()},stop:function(){this._call&&(this._call=null,this._time=1/0,m())}}},18694:(t,n,e)=>{e.d(n,{H:()=>r,L:()=>i});class i extends Map{constructor(t,n=h){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:n}}),null!=t)for(const[n,e]of t)this.set(n,e)}get(t){return super.get(o(this,t))}has(t){return super.has(o(this,t))}set(t,n){return super.set(s(this,t),n)}delete(t){return super.delete(a(this,t))}}class r extends Set{constructor(t,n=h){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:n}}),null!=t)for(const n of t)this.add(n)}has(t){return super.has(o(this,t))}add(t){return super.add(s(this,t))}delete(t){return super.delete(a(this,t))}}function o({_intern:t,_key:n},e){const i=n(e);return t.has(i)?t.get(i):e}function s({_intern:t,_key:n},e){const i=n(e);return t.has(i)?t.get(i):(t.set(i,e),e)}function a({_intern:t,_key:n},e){const i=n(e);return t.has(i)&&(e=t.get(e),t.delete(i)),e}function h(t){return null!==t&&"object"==typeof t?t.valueOf():t}}}]);
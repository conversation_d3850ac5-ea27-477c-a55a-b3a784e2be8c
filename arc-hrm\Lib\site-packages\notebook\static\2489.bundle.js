"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[2489],{72489:(e,t,i)=>{i.r(t),i.d(t,{ABCWidgetFactory:()=>F,Base64ModelFactory:()=>f,Context:()=>_,DocumentModel:()=>p,DocumentRegistry:()=>E,DocumentWidget:()=>w,MimeContent:()=>b,MimeDocument:()=>C,MimeDocumentFactory:()=>D,TextModelFactory:()=>y,createReadonlyLabel:()=>g});var s,a=i(12982),n=i(38639),o=i(70856),r=i(71677),l=i(20998),h=i(2549),d=i(81997),c=i(31516);class _{constructor(e){var t,i;this._isReady=!1,this._isDisposed=!1,this._isPopulated=!1,this._path="",this._lineEnding=null,this._contentsModel=null,this._populatedPromise=new l.PromiseDelegate,this._pathChanged=new d.Signal(this),this._fileChanged=new d.Signal(this),this._saveState=new d.Signal(this),this._disposed=new d.Signal(this),this._lastModifiedCheckMargin=500,this._conflictModalIsOpen=!1;const h=this._manager=e.manager;this.translator=e.translator||r.nullTranslator,this._trans=this.translator.load("jupyterlab"),this._factory=e.factory,this._dialogs=null!==(t=e.sessionDialogs)&&void 0!==t?t:new a.SessionContextDialogs({translator:e.translator}),this._opener=e.opener||s.noOp,this._path=this._manager.contents.normalize(e.path),this._lastModifiedCheckMargin=e.lastModifiedCheckMargin||500;const c=this._manager.contents.localPath(this._path),_=this._factory.preferredLanguage(n.PathExt.basename(c)),m=this._manager.contents.getSharedModelFactory(this._path),u=null==m?void 0:m.createNew({path:c,format:this._factory.fileFormat,contentType:this._factory.contentType,collaborative:this._factory.collaborative});this._model=this._factory.createNew({languagePreference:_,sharedModel:u,collaborationEnabled:null!==(i=null==m?void 0:m.collaborative)&&void 0!==i&&i}),this._readyPromise=h.ready.then((()=>this._populatedPromise.promise));const g=n.PathExt.extname(this._path);this.sessionContext=new a.SessionContext({sessionManager:h.sessions,specsManager:h.kernelspecs,path:c,type:".ipynb"===g?"notebook":"file",name:n.PathExt.basename(c),kernelPreference:e.kernelPreference||{shouldStart:!1},setBusy:e.setBusy}),this.sessionContext.propertyChanged.connect(this._onSessionChanged,this),h.contents.fileChanged.connect(this._onFileChanged,this),this.urlResolver=new o.RenderMimeRegistry.UrlResolver({path:this._path,contents:h.contents})}get pathChanged(){return this._pathChanged}get fileChanged(){return this._fileChanged}get saveState(){return this._saveState}get disposed(){return this._disposed}get lastModifiedCheckMargin(){return this._lastModifiedCheckMargin}set lastModifiedCheckMargin(e){this._lastModifiedCheckMargin=e}get model(){return this._model}get path(){return this._path}get localPath(){return this._manager.contents.localPath(this._path)}get contentsModel(){return this._contentsModel?{...this._contentsModel}:null}get factoryName(){return this.isDisposed?"":this._factory.name}get isDisposed(){return this._isDisposed}dispose(){this.isDisposed||(this._isDisposed=!0,this.sessionContext.dispose(),this._model.dispose(),this._model.sharedModel.dispose(),this._disposed.emit(void 0),d.Signal.clearData(this))}get isReady(){return this._isReady}get ready(){return this._readyPromise}get canSave(){var e;return!(!(null===(e=this._contentsModel)||void 0===e?void 0:e.writable)||this._model.collaborative)}async initialize(e){e?await this._save():await this._revert(),this.model.sharedModel.clearUndoHistory()}rename(e){return this.ready.then((()=>this._manager.ready.then((()=>this._rename(e)))))}async save(){await this.ready,await this._save()}async saveAs(){await this.ready;const e=this._manager.contents.localPath(this.path),t=await s.getSavePath(e);if(this.isDisposed||!t)return;const i=this._manager.contents.driveName(this.path),a=""==i?t:`${i}:${t}`;if(a===this._path)return this.save();try{await this._manager.ready,await this._manager.contents.get(a),await this._maybeOverWrite(a)}catch(e){if(!e.response||404!==e.response.status)throw e;await this._finishSaveAs(a)}}async download(){const e=await this._manager.contents.getDownloadUrl(this._path),t=document.createElement("a");t.href=e,t.download="",document.body.appendChild(t),t.click(),document.body.removeChild(t)}async revert(){await this.ready,await this._revert()}createCheckpoint(){const e=this._manager.contents;return this._manager.ready.then((()=>e.createCheckpoint(this._path)))}deleteCheckpoint(e){const t=this._manager.contents;return this._manager.ready.then((()=>t.deleteCheckpoint(this._path,e)))}restoreCheckpoint(e){const t=this._manager.contents,i=this._path;return this._manager.ready.then((()=>e?t.restoreCheckpoint(i,e):this.listCheckpoints().then((s=>{if(!this.isDisposed&&s.length)return e=s[s.length-1].id,t.restoreCheckpoint(i,e)}))))}listCheckpoints(){const e=this._manager.contents;return this._manager.ready.then((()=>e.listCheckpoints(this._path)))}addSibling(e,t={}){const i=this._opener;return i&&i(e,t),new h.DisposableDelegate((()=>{e.close()}))}_onFileChanged(e,t){var i;if("rename"!==t.type)return;let s=t.oldValue&&t.oldValue.path,a=t.newValue&&t.newValue.path;if(a&&0===this._path.indexOf(s||"")){let e=t.newValue;s!==this._path&&(a=this._path.replace(new RegExp(`^${s}/`),`${a}/`),s=this._path,e={last_modified:null===(i=t.newValue)||void 0===i?void 0:i.created,path:a}),this._updateContentsModel({...this._contentsModel,...e}),this._updatePath(a)}}_onSessionChanged(e,t){if("path"!==t)return;const i=this._manager.contents.driveName(this.path);let s=this.sessionContext.session.path;i&&(s=`${i}:${s}`),this._updatePath(s)}_updateContentsModel(e){var t,i,s,a;const n=e.writable&&!this._model.collaborative,o={path:e.path,name:e.name,type:e.type,writable:n,created:e.created,last_modified:e.last_modified,mimetype:e.mimetype,format:e.format,hash:e.hash,hash_algorithm:e.hash_algorithm},r=null!==(i=null===(t=this._contentsModel)||void 0===t?void 0:t.last_modified)&&void 0!==i?i:null,l=null!==(a=null===(s=this._contentsModel)||void 0===s?void 0:s.hash)&&void 0!==a?a:null;this._contentsModel=o,(!r&&!l||!l&&o.last_modified!==r||l&&o.hash!==l)&&this._fileChanged.emit(o)}_updatePath(e){var t,i,s,a;if(this._path===e)return;this._path=e;const o=this._manager.contents.localPath(e),r=n.PathExt.basename(o);if((null===(t=this.sessionContext.session)||void 0===t?void 0:t.path)!==o&&(null===(i=this.sessionContext.session)||void 0===i||i.setPath(o)),(null===(s=this.sessionContext.session)||void 0===s?void 0:s.name)!==r&&(null===(a=this.sessionContext.session)||void 0===a||a.setName(r)),this.urlResolver.path!==e&&(this.urlResolver.path=e),this._contentsModel&&(this._contentsModel.path!==e||this._contentsModel.name!==r)){const t={...this._contentsModel,name:r,path:e};this._updateContentsModel(t)}this._pathChanged.emit(e)}async _populate(){if(this._isPopulated=!0,this._isReady=!0,this._populatedPromise.resolve(void 0),await this._maybeCheckpoint(!1),this.isDisposed)return;const e=this._model.defaultKernelName||this.sessionContext.kernelPreference.name;this.sessionContext.kernelPreference={...this.sessionContext.kernelPreference,name:e,language:this._model.defaultKernelLanguage},this.sessionContext.initialize().then((e=>{e&&this._dialogs.selectKernel(this.sessionContext)}))}async _rename(e){const t=this.localPath.split("/");t[t.length-1]=e;let i=n.PathExt.join(...t);const s=this._manager.contents.driveName(this.path);s&&(i=`${s}:${i}`),await this._manager.contents.rename(this.path,i)}async _save(){this._saveState.emit("started");const e=this._createSaveOptions();try{await this._manager.ready;const t=await this._maybeSave(e);if(this.isDisposed)return;this._model.dirty=!1,this._updateContentsModel(t),this._isPopulated||await this._populate(),this._saveState.emit("completed")}catch(e){const{name:t}=e;if("ModalCancelError"===t||"ModalDuplicateError"===t)throw e;const i=this._manager.contents.localPath(this._path),s=n.PathExt.basename(i);throw this._handleError(e,this._trans.__("File Save Error for %1",s)),this._saveState.emit("failed"),e}}_revert(e=!1){const t={type:this._factory.contentType,content:null!==this._factory.fileFormat,hash:null!==this._factory.fileFormat,...null!==this._factory.fileFormat?{format:this._factory.fileFormat}:{}},i=this._path,s=this._model;return this._manager.ready.then((()=>this._manager.contents.get(i,t))).then((e=>{if(!this.isDisposed){if(e.content)if("json"===e.format)s.fromJSON(e.content);else{let t=e.content;-1!==t.indexOf("\r\n")?(this._lineEnding="\r\n",t=t.replace(/\r\n/g,"\n")):-1!==t.indexOf("\r")?(this._lineEnding="\r",t=t.replace(/\r/g,"\n")):this._lineEnding=null,s.fromString(t)}return this._updateContentsModel(e),s.dirty=!1,this._isPopulated?void 0:this._populate()}})).catch((async e=>{const t=this._manager.contents.localPath(this._path),i=n.PathExt.basename(t);throw this._handleError(e,this._trans.__("File Load Error for %1",i)),e}))}_maybeSave(e){const t=this._path;return this._manager.contents.get(t,{content:!1,hash:!0}).then((i=>{var s,a,n,o;if(this.isDisposed)return Promise.reject(new Error("Disposed"));const r=void 0!==(null===(s=this.contentsModel)||void 0===s?void 0:s.hash)&&null!==(null===(a=this.contentsModel)||void 0===a?void 0:a.hash)&&void 0!==i.hash&&null!==i.hash,l=null===(n=this.contentsModel)||void 0===n?void 0:n.hash,h=i.hash;if(r&&l!==h)return console.warn(`Different hash found for ${this.path}`),this._raiseConflict(i,e);const d=this._lastModifiedCheckMargin,c=null===(o=this.contentsModel)||void 0===o?void 0:o.last_modified,_=c?new Date(c):new Date,m=new Date(i.last_modified);return!r&&c&&m.getTime()-_.getTime()>d?(console.warn(`Last saving performed ${_} while the current file seems to have been saved ${m}`),this._raiseConflict(i,e)):this._manager.contents.save(t,e).then((async e=>{const i=await this._manager.contents.get(t,{content:!1,hash:!0});return{...e,hash:i.hash,hash_algorithm:i.hash_algorithm}}))}),(i=>{if(i.response&&404===i.response.status)return this._manager.contents.save(t,e).then((async e=>{const i=await this._manager.contents.get(t,{content:!1,hash:!0});return{...e,hash:i.hash,hash_algorithm:i.hash_algorithm}}));throw i}))}async _handleError(e,t){await(0,a.showErrorMessage)(t,e)}_maybeCheckpoint(e){let t=Promise.resolve(void 0);return this.canSave?(t=e?this.createCheckpoint().then():this.listCheckpoints().then((e=>{if(!this.isDisposed&&!e.length&&this.canSave)return this.createCheckpoint().then()})),t.catch((e=>{if(!e.response||403!==e.response.status)throw e}))):t}_raiseConflict(e,t){if(this._conflictModalIsOpen){const e=new Error("Modal is already displayed");return e.name="ModalDuplicateError",Promise.reject(e)}const i=this._trans.__('"%1" has changed on disk since the last time it was opened or saved.\nDo you want to overwrite the file on disk with the version open here,\nor load the version on disk (revert)?',this.path),s=a.Dialog.okButton({label:this._trans.__("Revert"),actions:["revert"]}),n=a.Dialog.warnButton({label:this._trans.__("Overwrite"),actions:["overwrite"]});return this._conflictModalIsOpen=!0,(0,a.showDialog)({title:this._trans.__("File Changed"),body:i,buttons:[a.Dialog.cancelButton(),s,n]}).then((i=>{if(this._conflictModalIsOpen=!1,this.isDisposed)return Promise.reject(new Error("Disposed"));if(i.button.actions.includes("overwrite"))return this._manager.contents.save(this._path,t);if(i.button.actions.includes("revert"))return this.revert().then((()=>e));const s=new Error("Cancel");return s.name="ModalCancelError",Promise.reject(s)}))}_maybeOverWrite(e){const t=this._trans.__('"%1" already exists. Do you want to replace it?',e),i=a.Dialog.warnButton({label:this._trans.__("Overwrite"),accept:!0});return(0,a.showDialog)({title:this._trans.__("File Overwrite?"),body:t,buttons:[a.Dialog.cancelButton(),i]}).then((t=>this.isDisposed?Promise.reject(new Error("Disposed")):t.button.accept?this._manager.contents.delete(e).then((()=>this._finishSaveAs(e))):void 0))}async _finishSaveAs(e){this._saveState.emit("started");try{await this._manager.ready;const t=this._createSaveOptions();await this._manager.contents.save(e,t),await this._maybeCheckpoint(!0),this._saveState.emit("completed")}catch(e){if("Cancel"===e.message||"Modal is already displayed"===e.message)throw e;const t=this._manager.contents.localPath(this._path),i=n.PathExt.basename(t);return this._handleError(e,this._trans.__("File Save Error for %1",i)),void this._saveState.emit("failed")}}_createSaveOptions(){let e=null;return"json"===this._factory.fileFormat?e=this._model.toJSON():(e=this._model.toString(),this._lineEnding&&(e=e.replace(/\n/g,this._lineEnding))),{type:this._factory.contentType,format:this._factory.fileFormat,content:e}}}!function(e){e.getSavePath=function(e,i){const s=(i=i||r.nullTranslator).load("jupyterlab"),n=a.Dialog.okButton({label:s.__("Save"),accept:!0});return(0,a.showDialog)({title:s.__("Save File As…"),body:new t(e),buttons:[a.Dialog.cancelButton(),n]}).then((e=>{var t;if(e.button.accept)return null!==(t=e.value)&&void 0!==t?t:void 0}))},e.noOp=function(){};class t extends c.Widget{constructor(e){super({node:i(e)})}getValue(){return this.node.value}}function i(e){const t=document.createElement("input");return t.value=e,t}}(s||(s={}));var m=i(31536),u=i(78156);function g(e,t){var i;let s=(null!=t?t:r.nullTranslator).load("jupyterlab");return a.ReactWidget.create(u.createElement("div",null,u.createElement("span",{className:"jp-ToolbarLabelComponent",title:s.__('Document is permissioned read-only; "save" is disabled, use "save as..." instead')},s.__("%1 is read-only",null===(i=e.context.contentsModel)||void 0===i?void 0:i.type))))}class p extends m.CodeEditor.Model{constructor(e={}){var t;super({sharedModel:e.sharedModel}),this._defaultLang="",this._dirty=!1,this._readOnly=!1,this._contentChanged=new d.Signal(this),this._stateChanged=new d.Signal(this),this._defaultLang=null!==(t=e.languagePreference)&&void 0!==t?t:"",this._collaborationEnabled=!!e.collaborationEnabled,this.sharedModel.changed.connect(this._onStateChanged,this)}get contentChanged(){return this._contentChanged}get stateChanged(){return this._stateChanged}get dirty(){return this._dirty}set dirty(e){const t=this._dirty;e!==t&&(this._dirty=e,this.triggerStateChange({name:"dirty",oldValue:t,newValue:e}))}get readOnly(){return this._readOnly}set readOnly(e){if(e===this._readOnly)return;const t=this._readOnly;this._readOnly=e,this.triggerStateChange({name:"readOnly",oldValue:t,newValue:e})}get defaultKernelName(){return""}get defaultKernelLanguage(){return this._defaultLang}get collaborative(){return this._collaborationEnabled}toString(){return this.sharedModel.getSource()}fromString(e){this.sharedModel.setSource(e)}toJSON(){return JSON.parse(this.sharedModel.getSource()||"null")}fromJSON(e){this.fromString(JSON.stringify(e))}initialize(){}triggerStateChange(e){this._stateChanged.emit(e)}triggerContentChange(){this._contentChanged.emit(void 0),this.dirty=!0}_onStateChanged(e,t){t.sourceChange&&this.triggerContentChange(),t.stateChange&&t.stateChange.forEach((e=>{"dirty"===e.name?this.dirty=e.newValue:e.oldValue!==e.newValue&&this.triggerStateChange({newValue:void 0,oldValue:void 0,...e})}))}}class y{constructor(e){this._isDisposed=!1,this._collaborative=null==e||e}get name(){return"text"}get contentType(){return"file"}get fileFormat(){return"text"}get collaborative(){return this._collaborative}get isDisposed(){return this._isDisposed}dispose(){this._isDisposed=!0}createNew(e={}){const t=e.collaborationEnabled&&this.collaborative;return new p({...e,collaborationEnabled:t})}preferredLanguage(e){return""}}class f extends y{get name(){return"base64"}get contentType(){return"file"}get fileFormat(){return"base64"}}class F{constructor(e){this._isDisposed=!1,this._widgetCreated=new d.Signal(this),this._translator=e.translator||r.nullTranslator,this._name=e.name,this._label=e.label||e.name,this._readOnly=void 0!==e.readOnly&&e.readOnly,this._defaultFor=e.defaultFor?e.defaultFor.slice():[],this._defaultRendered=(e.defaultRendered||[]).slice(),this._fileTypes=e.fileTypes.slice(),this._modelName=e.modelName||"text",this._preferKernel=!!e.preferKernel,this._canStartKernel=!!e.canStartKernel,this._shutdownOnClose=!!e.shutdownOnClose,this._autoStartDefault=!!e.autoStartDefault,this._toolbarFactory=e.toolbarFactory}get widgetCreated(){return this._widgetCreated}get isDisposed(){return this._isDisposed}dispose(){this.isDisposed||(this._isDisposed=!0,d.Signal.clearData(this))}get readOnly(){return this._readOnly}get name(){return this._name}get label(){return this._label}get fileTypes(){return this._fileTypes.slice()}get modelName(){return this._modelName}get defaultFor(){return this._defaultFor.slice()}get defaultRendered(){return this._defaultRendered.slice()}get preferKernel(){return this._preferKernel}get canStartKernel(){return this._canStartKernel}get translator(){return this._translator}get shutdownOnClose(){return this._shutdownOnClose}set shutdownOnClose(e){this._shutdownOnClose=e}get autoStartDefault(){return this._autoStartDefault}set autoStartDefault(e){this._autoStartDefault=e}createNew(e,t){var i;const s=this.createNewWidget(e,t);return(0,a.setToolbar)(s,null!==(i=this._toolbarFactory)&&void 0!==i?i:this.defaultToolbarFactory.bind(this)),this._widgetCreated.emit(s),s}defaultToolbarFactory(e){return[]}}const v="jp-mod-dirty";class w extends a.MainAreaWidget{constructor(e){var t;e.reveal=Promise.all([e.reveal,e.context.ready]),super(e),this._trans=(null!==(t=e.translator)&&void 0!==t?t:r.nullTranslator).load("jupyterlab"),this.context=e.context,this.context.pathChanged.connect(this._onPathChanged,this),this._onPathChanged(this.context,this.context.path),this.context.model.stateChanged.connect(this._onModelStateChanged,this),this.context.ready.then((()=>{this._handleDirtyState()})),this.title.changed.connect(this._onTitleChanged,this)}setFragment(e){}async _onTitleChanged(e){const t=this.title.label,i=this.context.localPath.split("/").pop()||this.context.localPath;if(t!==i){if(t.length>0&&!/[\/\\:]/.test(t)){const e=this.context.path;if(await this.context.rename(t),this.context.path!==e)return}this.title.label=i}}_onPathChanged(e,t){this.title.label=n.PathExt.basename(e.localPath),this.isUntitled=!1}_onModelStateChanged(e,t){var i;if("dirty"===t.name&&this._handleDirtyState(),!this.context.model.dirty&&!this.context.model.collaborative&&!(null===(i=this.context.contentsModel)||void 0===i?void 0:i.writable)){const e=g(this);this.toolbar.insertBefore("kernelName","read-only-indicator",e)||this.toolbar.addItem("read-only-indicator",e)}}_handleDirtyState(){this.context.model.dirty&&!this.title.className.includes(v)?this.title.className+=` ${v}`:this.title.className=this.title.className.replace(v,"")}}var x,T=i(49503);class b extends c.Widget{constructor(e){super(),this._changeCallback=e=>{if(!e.data||!e.data[this.mimeType])return;const t=e.data[this.mimeType];"string"==typeof t?t!==this._context.model.toString()&&this._context.model.fromString(t):null==t||l.JSONExt.deepEqual(t,this._context.model.toJSON())||this._context.model.fromJSON(t)},this._fragment="",this._ready=new l.PromiseDelegate,this._isRendering=!1,this._renderRequested=!1,this.addClass("jp-MimeDocument"),this.translator=e.translator||r.nullTranslator,this._trans=this.translator.load("jupyterlab"),this.mimeType=e.mimeType,this._dataType=e.dataType||"string",this._context=e.context,this.renderer=e.renderer,(this.layout=new c.StackedLayout).addWidget(this.renderer),this._context.ready.then((()=>this._render())).then((()=>{this.node===document.activeElement&&T.MessageLoop.sendMessage(this.renderer,c.Widget.Msg.ActivateRequest),this._monitor=new n.ActivityMonitor({signal:this._context.model.contentChanged,timeout:e.renderTimeout}),this._monitor.activityStopped.connect(this.update,this),this._ready.resolve(void 0)})).catch((e=>{requestAnimationFrame((()=>{this.dispose()})),(0,a.showErrorMessage)(this._trans.__("Renderer Failure: %1",this._context.path),e)}))}[a.Printing.symbol](){return a.Printing.getPrintFunction(this.renderer)}get ready(){return this._ready.promise}setFragment(e){this._fragment=e,this.update()}dispose(){this.isDisposed||(this._monitor&&this._monitor.dispose(),this._monitor=null,super.dispose())}onUpdateRequest(e){this._context.isReady&&(this._render(),this._fragment="")}async _render(){if(this.isDisposed)return;if(this._isRendering)return void(this._renderRequested=!0);this._renderRequested=!1;const e=this._context,t=e.model,i={};"string"===this._dataType?i[this.mimeType]=t.toString():i[this.mimeType]=t.toJSON();const s=new o.MimeModel({data:i,callback:this._changeCallback,metadata:{fragment:this._fragment}});try{if(this._isRendering=!0,await this.renderer.renderModel(s),this._isRendering=!1,this._renderRequested)return this._render()}catch(t){requestAnimationFrame((()=>{this.dispose()})),(0,a.showErrorMessage)(this._trans.__("Renderer Failure: %1",e.path),t)}}}class C extends w{setFragment(e){this.content.setFragment(e)}}class D extends F{constructor(e){super(x.createRegistryOptions(e)),this._rendermime=e.rendermime,this._renderTimeout=e.renderTimeout||1e3,this._dataType=e.dataType||"string",this._fileType=e.primaryFileType,this._factory=e.factory}createNewWidget(e){var t,i;const s=this._fileType,a=(null==s?void 0:s.mimeTypes.length)?s.mimeTypes[0]:m.IEditorMimeTypeService.defaultMimeType,n=this._rendermime.clone({resolver:e.urlResolver});let o;o=this._factory&&this._factory.mimeTypes.includes(a)?this._factory.createRenderer({mimeType:a,resolver:n.resolver,sanitizer:n.sanitizer,linkHandler:n.linkHandler,latexTypesetter:n.latexTypesetter,markdownParser:n.markdownParser}):n.createRenderer(a);const r=new b({context:e,renderer:o,mimeType:a,renderTimeout:this._renderTimeout,dataType:this._dataType});return r.title.icon=null==s?void 0:s.icon,r.title.iconClass=null!==(t=null==s?void 0:s.iconClass)&&void 0!==t?t:"",r.title.iconLabel=null!==(i=null==s?void 0:s.iconLabel)&&void 0!==i?i:"",new C({content:r,context:e})}}!function(e){e.createRegistryOptions=function(e){return{...e,readOnly:!0}}}(x||(x={}));var S,M=i(68239),O=i(33625);class E{constructor(e={}){this._modelFactories=Object.create(null),this._widgetFactories=Object.create(null),this._defaultWidgetFactory="",this._defaultWidgetFactoryOverrides=Object.create(null),this._defaultWidgetFactories=Object.create(null),this._defaultRenderedWidgetFactories=Object.create(null),this._widgetFactoriesForFileType=Object.create(null),this._fileTypes=[],this._extenders=Object.create(null),this._changed=new d.Signal(this),this._isDisposed=!1;const t=e.textModelFactory;if(this.translator=e.translator||r.nullTranslator,t&&"text"!==t.name)throw new Error("Text model factory must have the name `text`");this._modelFactories.text=t||new y(!0),(e.initialFileTypes||E.getDefaultFileTypes(this.translator)).forEach((e=>{const t={...E.getFileTypeDefaults(this.translator),...e};this._fileTypes.push(t)}))}get changed(){return this._changed}get isDisposed(){return this._isDisposed}dispose(){if(!this.isDisposed){this._isDisposed=!0;for(const e in this._modelFactories)this._modelFactories[e].dispose();for(const e in this._widgetFactories)this._widgetFactories[e].dispose();for(const e in this._extenders)this._extenders[e].length=0;this._fileTypes.length=0,d.Signal.clearData(this)}}addWidgetFactory(e){const t=e.name.toLowerCase();if(!t||"default"===t)throw Error("Invalid factory name");if(this._widgetFactories[t])return console.warn(`Duplicate registered factory ${t}`),new h.DisposableDelegate(S.noOp);this._widgetFactories[t]=e;for(const i of e.defaultFor||[])-1!==e.fileTypes.indexOf(i)&&("*"===i?this._defaultWidgetFactory=t:this._defaultWidgetFactories[i]=t);for(const i of e.defaultRendered||[])-1!==e.fileTypes.indexOf(i)&&(this._defaultRenderedWidgetFactories[i]=t);for(const i of e.fileTypes)this._widgetFactoriesForFileType[i]||(this._widgetFactoriesForFileType[i]=[]),this._widgetFactoriesForFileType[i].push(t);return this._changed.emit({type:"widgetFactory",name:t,change:"added"}),new h.DisposableDelegate((()=>{delete this._widgetFactories[t],this._defaultWidgetFactory===t&&(this._defaultWidgetFactory="");for(const e of Object.keys(this._defaultWidgetFactories))this._defaultWidgetFactories[e]===t&&delete this._defaultWidgetFactories[e];for(const e of Object.keys(this._defaultRenderedWidgetFactories))this._defaultRenderedWidgetFactories[e]===t&&delete this._defaultRenderedWidgetFactories[e];for(const e of Object.keys(this._widgetFactoriesForFileType))O.ArrayExt.removeFirstOf(this._widgetFactoriesForFileType[e],t),0===this._widgetFactoriesForFileType[e].length&&delete this._widgetFactoriesForFileType[e];for(const e of Object.keys(this._defaultWidgetFactoryOverrides))this._defaultWidgetFactoryOverrides[e]===t&&delete this._defaultWidgetFactoryOverrides[e];this._changed.emit({type:"widgetFactory",name:t,change:"removed"})}))}addModelFactory(e){const t=e.name.toLowerCase();return this._modelFactories[t]?(console.warn(`Duplicate registered factory ${t}`),new h.DisposableDelegate(S.noOp)):(this._modelFactories[t]=e,this._changed.emit({type:"modelFactory",name:t,change:"added"}),new h.DisposableDelegate((()=>{delete this._modelFactories[t],this._changed.emit({type:"modelFactory",name:t,change:"removed"})})))}addWidgetExtension(e,t){(e=e.toLowerCase())in this._extenders||(this._extenders[e]=[]);const i=this._extenders[e];return-1!==O.ArrayExt.firstIndexOf(i,t)?(console.warn(`Duplicate registered extension for ${e}`),new h.DisposableDelegate(S.noOp)):(this._extenders[e].push(t),this._changed.emit({type:"widgetExtension",name:e,change:"added"}),new h.DisposableDelegate((()=>{O.ArrayExt.removeFirstOf(this._extenders[e],t),this._changed.emit({type:"widgetExtension",name:e,change:"removed"})})))}addFileType(e,t){const i={...E.getFileTypeDefaults(this.translator),...e,...!(e.icon||e.iconClass)&&{icon:M.fileIcon}};if(this._fileTypes.push(i),t){const e=i.name.toLowerCase();t.map((e=>e.toLowerCase())).forEach((t=>{this._widgetFactoriesForFileType[e]||(this._widgetFactoriesForFileType[e]=[]),this._widgetFactoriesForFileType[e].includes(t)||this._widgetFactoriesForFileType[e].push(t)})),this._defaultWidgetFactories[e]||(this._defaultWidgetFactories[e]=this._widgetFactoriesForFileType[e][0])}return this._changed.emit({type:"fileType",name:i.name,change:"added"}),new h.DisposableDelegate((()=>{if(O.ArrayExt.removeFirstOf(this._fileTypes,i),t){const e=i.name.toLowerCase();for(const i of t.map((e=>e.toLowerCase())))O.ArrayExt.removeFirstOf(this._widgetFactoriesForFileType[e],i);this._defaultWidgetFactories[e]===t[0].toLowerCase()&&delete this._defaultWidgetFactories[e]}this._changed.emit({type:"fileType",name:e.name,change:"removed"})}))}preferredWidgetFactories(e){const t=new Set,i=this.getFileTypesForPath(n.PathExt.basename(e));i.forEach((e=>{e.name in this._defaultWidgetFactoryOverrides&&t.add(this._defaultWidgetFactoryOverrides[e.name])})),i.forEach((e=>{e.name in this._defaultWidgetFactories&&t.add(this._defaultWidgetFactories[e.name])})),i.forEach((e=>{e.name in this._defaultRenderedWidgetFactories&&t.add(this._defaultRenderedWidgetFactories[e.name])})),this._defaultWidgetFactory&&t.add(this._defaultWidgetFactory);for(const e of i)if(e.name in this._widgetFactoriesForFileType)for(const i of this._widgetFactoriesForFileType[e.name])t.add(i);if("*"in this._widgetFactoriesForFileType)for(const e of this._widgetFactoriesForFileType["*"])t.add(e);const s=[];for(const e of t){const t=this._widgetFactories[e];t&&((t.modelName||"text")in this._modelFactories&&s.push(t))}return s}defaultRenderedWidgetFactory(e){const t=this.getFileTypesForPath(n.PathExt.basename(e)).map((e=>e.name));for(const e in t)if(e in this._defaultWidgetFactoryOverrides)return this._widgetFactories[this._defaultWidgetFactoryOverrides[e]];for(const e in t)if(e in this._defaultRenderedWidgetFactories)return this._widgetFactories[this._defaultRenderedWidgetFactories[e]];return this.defaultWidgetFactory(e)}defaultWidgetFactory(e){return e?this.preferredWidgetFactories(e)[0]:this._widgetFactories[this._defaultWidgetFactory]}setDefaultWidgetFactory(e,t){if(e=e.toLowerCase(),!this.getFileType(e))throw Error(`Cannot find file type ${e}`);if(!t)return void(this._defaultWidgetFactoryOverrides[e]&&delete this._defaultWidgetFactoryOverrides[e]);if(!this.getWidgetFactory(t))throw Error(`Cannot find widget factory ${t}`);t=t.toLowerCase();const i=this._widgetFactoriesForFileType[e];if(!(t===this._defaultWidgetFactory||i&&i.includes(t)))throw Error(`Factory ${t} cannot view file type ${e}`);this._defaultWidgetFactoryOverrides[e]=t}*widgetFactories(){for(const e in this._widgetFactories)yield this._widgetFactories[e]}*modelFactories(){for(const e in this._modelFactories)yield this._modelFactories[e]}*widgetExtensions(e){if((e=e.toLowerCase())in this._extenders)for(const t of this._extenders[e])yield t}*fileTypes(){for(const e of this._fileTypes)yield e}getWidgetFactory(e){return this._widgetFactories[e.toLowerCase()]}getModelFactory(e){return this._modelFactories[e.toLowerCase()]}getFileType(e){return e=e.toLowerCase(),(0,O.find)(this._fileTypes,(t=>t.name.toLowerCase()===e))}getKernelPreference(e,t,i){t=t.toLowerCase();const s=this._widgetFactories[t];if(!s)return;const a=this.getModelFactory(s.modelName||"text");if(!a)return;const o=a.preferredLanguage(n.PathExt.basename(e)),r=i&&i.name;return{id:i&&i.id,name:r,language:o,shouldStart:s.preferKernel,canStart:s.canStartKernel,shutdownOnDispose:s.shutdownOnClose,autoStartDefault:s.autoStartDefault}}getFileTypeForModel(e){switch(e.type){case"directory":return(0,O.find)(this._fileTypes,(e=>"directory"===e.contentType))||E.getDefaultDirectoryFileType(this.translator);case"notebook":return(0,O.find)(this._fileTypes,(e=>"notebook"===e.contentType))||E.getDefaultNotebookFileType(this.translator);default:if(e.name||e.path){const t=e.name||n.PathExt.basename(e.path),i=this.getFileTypesForPath(t);if(i.length>0)return i[0]}return this.getFileType("text")||E.getDefaultTextFileType(this.translator)}}getFileTypesForPath(e){const t=[],i=n.PathExt.basename(e);let s=(0,O.find)(this._fileTypes,(e=>!(!e.pattern||null===i.match(e.pattern))));s&&t.push(s);let a=S.extname(i);for(;a.length>1;){const e=this._fileTypes.filter((e=>e.extensions.map((e=>e.toLowerCase())).includes(a)));t.push(...e),a="."+a.split(".").slice(2).join(".")}return t}}!function(e){function t(e){return{name:"default",displayName:(null==(e=e||r.nullTranslator)?void 0:e.load("jupyterlab")).__("default"),extensions:[],mimeTypes:[],contentType:"file",fileFormat:"text"}}function i(e){const i=null==(e=e||r.nullTranslator)?void 0:e.load("jupyterlab");return{...t(e),name:"text",displayName:i.__("Text"),mimeTypes:["text/plain"],extensions:[".txt"],icon:M.fileIcon}}function s(e){const i=null==(e=e||r.nullTranslator)?void 0:e.load("jupyterlab");return{...t(e),name:"notebook",displayName:i.__("Notebook"),mimeTypes:["application/x-ipynb+json"],extensions:[".ipynb"],contentType:"notebook",fileFormat:"json",icon:M.notebookIcon}}function a(e){const i=null==(e=e||r.nullTranslator)?void 0:e.load("jupyterlab");return{...t(e),name:"directory",displayName:i.__("Directory"),extensions:[],mimeTypes:["text/directory"],contentType:"directory",icon:M.folderIcon}}e.getFileTypeDefaults=t,e.getDefaultTextFileType=i,e.getDefaultNotebookFileType=s,e.getDefaultDirectoryFileType=a,e.getDefaultFileTypes=function(e){const t=null==(e=e||r.nullTranslator)?void 0:e.load("jupyterlab");return[i(e),s(e),a(e),{name:"markdown",displayName:t.__("Markdown File"),extensions:[".md"],mimeTypes:["text/markdown"],icon:M.markdownIcon},{name:"PDF",displayName:t.__("PDF File"),extensions:[".pdf"],mimeTypes:["application/pdf"],icon:M.pdfIcon},{name:"python",displayName:t.__("Python File"),extensions:[".py"],mimeTypes:["text/x-python"],icon:M.pythonIcon},{name:"json",displayName:t.__("JSON File"),extensions:[".json"],mimeTypes:["application/json"],icon:M.jsonIcon},{name:"jsonl",displayName:t.__("JSONLines File"),extensions:[".jsonl",".ndjson"],mimeTypes:["text/jsonl","application/jsonl","application/json-lines"],icon:M.jsonIcon},{name:"julia",displayName:t.__("Julia File"),extensions:[".jl"],mimeTypes:["text/x-julia"],icon:M.juliaIcon},{name:"csv",displayName:t.__("CSV File"),extensions:[".csv"],mimeTypes:["text/csv"],icon:M.spreadsheetIcon},{name:"tsv",displayName:t.__("TSV File"),extensions:[".tsv"],mimeTypes:["text/csv"],icon:M.spreadsheetIcon},{name:"r",displayName:t.__("R File"),mimeTypes:["text/x-rsrc"],extensions:[".R"],icon:M.rKernelIcon},{name:"yaml",displayName:t.__("YAML File"),mimeTypes:["text/x-yaml","text/yaml"],extensions:[".yaml",".yml"],icon:M.yamlIcon},{name:"svg",displayName:t.__("Image"),mimeTypes:["image/svg+xml"],extensions:[".svg"],icon:M.imageIcon,fileFormat:"base64"},{name:"tiff",displayName:t.__("Image"),mimeTypes:["image/tiff"],extensions:[".tif",".tiff"],icon:M.imageIcon,fileFormat:"base64"},{name:"jpeg",displayName:t.__("Image"),mimeTypes:["image/jpeg"],extensions:[".jpg",".jpeg"],icon:M.imageIcon,fileFormat:"base64"},{name:"gif",displayName:t.__("Image"),mimeTypes:["image/gif"],extensions:[".gif"],icon:M.imageIcon,fileFormat:"base64"},{name:"png",displayName:t.__("Image"),mimeTypes:["image/png"],extensions:[".png"],icon:M.imageIcon,fileFormat:"base64"},{name:"bmp",displayName:t.__("Image"),mimeTypes:["image/bmp"],extensions:[".bmp"],icon:M.imageIcon,fileFormat:"base64"},{name:"webp",displayName:t.__("Image"),mimeTypes:["image/webp"],extensions:[".webp"],icon:M.imageIcon,fileFormat:"base64"}]}}(E||(E={})),function(e){e.extname=function(e){const t=n.PathExt.basename(e).split(".");return t.shift(),("."+t.join(".")).toLowerCase()},e.noOp=function(){}}(S||(S={}))}}]);
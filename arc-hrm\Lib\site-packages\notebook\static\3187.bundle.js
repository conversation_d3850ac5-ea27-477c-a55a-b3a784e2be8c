"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[3187],{53187:(e,n,a)=>{a.r(n),a.d(n,{default:()=>d});var l,r=a(3053),t=a(12982),i=a(71677),o=a(68239),s=a(5787);!function(e){e.open="pluginmanager:open",e.refreshPlugins="pluginmanager:refresh"}(l||(l={}));const u="@jupyterlab/pluginmanager-extension:plugin",d=[{id:u,description:"Enable or disable individual plugins.",autoStart:!0,requires:[],optional:[i.ITranslator,t.ICommandPalette,r.ILayoutRestorer],provides:s.IPluginManager,activate:(e,n,a,r)=>{const{commands:d,shell:c}=e,g=(n=null!=n?n:i.nullTranslator).load("jupyterlab"),p=g.__("Plugin Manager"),m=g.__("Advanced Plugin Manager"),P=g.__("Refresh Plugin List"),b=new t.WidgetTracker({namespace:"plugin-manager"});return d.addCommand(l.open,{label:m,execute:a=>{const r=function(a){const r=new s.PluginListModel({...a,pluginData:{availablePlugins:e.info.availablePlugins},serverSettings:e.serviceManager.serverSettings,extraLockedPlugins:[u,"@jupyterlab/application-extension:layout","@jupyterlab/apputils-extension:resolver"],translator:null!=n?n:i.nullTranslator}),c=new s.Plugins({model:r,translator:null!=n?n:i.nullTranslator});c.title.label=m,c.title.icon=o.extensionIcon,c.title.caption=g.__("Plugin Manager");const p=new t.MainAreaWidget({content:c,reveal:r.ready});return p.toolbar.addItem("refresh-plugins",new o.CommandToolbarButton({id:l.refreshPlugins,args:{noLabel:!0},commands:d})),p}(a);return c.add(r,"main",{type:"Plugins"}),b.add(r),r.content.model.trackerDataChanged.connect((()=>{b.save(r)})),r}}),d.addCommand(l.refreshPlugins,{label:e=>e.noLabel?"":P,caption:g.__("Refresh plugins list"),icon:o.refreshIcon,execute:async()=>{var e;return null===(e=b.currentWidget)||void 0===e?void 0:e.content.model.refresh().catch((e=>{console.error(`Failed to refresh the available plugins list:\n${e}`)}))}}),a&&a.addItem({command:l.open,category:p}),r&&r.restore(b,{command:l.open,name:e=>"plugins",args:e=>{const{query:n,isDisclaimed:a}=e.content.model;return{query:n,isDisclaimed:a}}}),{open:()=>e.commands.execute(l.open)}}}]}}]);
"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[4291],{54291:(e,t,r)=>{r.r(t),r.d(t,{Drag:()=>i});var n,o=r(2549);class i{constructor(e){this._onScrollFrame=()=>{if(!this._scrollTarget)return;let{element:e,edge:t,distance:r}=this._scrollTarget,o=n.SCROLL_EDGE_SIZE-r,i=Math.pow(o/n.SCROLL_EDGE_SIZE,2),s=Math.max(1,Math.round(i*n.SCROLL_EDGE_SIZE));switch(t){case"top":e.scrollTop-=s;break;case"left":e.scrollLeft-=s;break;case"right":e.scrollLeft+=s;break;case"bottom":e.scrollTop+=s}requestAnimationFrame(this._onScrollFrame)},this._disposed=!1,this._dropAction="none",this._override=null,this._currentTarget=null,this._currentElement=null,this._promise=null,this._scrollTarget=null,this._resolve=null,this.document=e.document||document,this.mimeData=e.mimeData,this.dragImage=e.dragImage||null,this.proposedAction=e.proposedAction||"copy",this.supportedActions=e.supportedActions||"all",this.source=e.source||null}dispose(){if(!this._disposed){if(this._disposed=!0,this._currentTarget){let e=new PointerEvent("pointerup",{bubbles:!0,cancelable:!0,clientX:-1,clientY:-1});n.dispatchDragLeave(this,this._currentTarget,null,e)}this._finalize("none")}}get isDisposed(){return this._disposed}start(e,t){if(this._disposed)return Promise.resolve("none");if(this._promise)return this._promise;this._addListeners(),this._attachDragImage(e,t),this._promise=new Promise((e=>{this._resolve=e}));let r=new PointerEvent("pointermove",{bubbles:!0,cancelable:!0,clientX:e,clientY:t});return document.dispatchEvent(r),this._promise}handleEvent(e){switch(e.type){case"pointermove":this._evtPointerMove(e);break;case"pointerup":this._evtPointerUp(e);break;case"keydown":this._evtKeyDown(e);break;default:e.preventDefault(),e.stopPropagation()}}moveDragImage(e,t){this.dragImage&&(this.dragImage.style.transform=`translate(${e}px, ${t}px)`)}_evtPointerMove(e){e.preventDefault(),e.stopPropagation(),this._updateCurrentTarget(e),this._updateDragScroll(e),this.moveDragImage(e.clientX,e.clientY)}_evtPointerUp(e){if(e.preventDefault(),e.stopPropagation(),0!==e.button)return;if(this._updateCurrentTarget(e),!this._currentTarget)return void this._finalize("none");if("none"===this._dropAction)return n.dispatchDragLeave(this,this._currentTarget,null,e),void this._finalize("none");let t=n.dispatchDrop(this,this._currentTarget,e);this._finalize(t)}_evtKeyDown(e){e.preventDefault(),e.stopPropagation(),27===e.keyCode&&this.dispose()}_addListeners(){document.addEventListener("pointerdown",this,!0),document.addEventListener("pointermove",this,!0),document.addEventListener("pointerup",this,!0),document.addEventListener("pointerenter",this,!0),document.addEventListener("pointerleave",this,!0),document.addEventListener("pointerover",this,!0),document.addEventListener("pointerout",this,!0),document.addEventListener("keydown",this,!0),document.addEventListener("keyup",this,!0),document.addEventListener("keypress",this,!0),document.addEventListener("contextmenu",this,!0)}_removeListeners(){document.removeEventListener("pointerdown",this,!0),document.removeEventListener("pointermove",this,!0),document.removeEventListener("pointerup",this,!0),document.removeEventListener("pointerenter",this,!0),document.removeEventListener("pointerleave",this,!0),document.removeEventListener("pointerover",this,!0),document.removeEventListener("pointerout",this,!0),document.removeEventListener("keydown",this,!0),document.removeEventListener("keyup",this,!0),document.removeEventListener("keypress",this,!0),document.removeEventListener("contextmenu",this,!0)}_updateDragScroll(e){let t=n.findScrollTarget(e);(this._scrollTarget||t)&&(this._scrollTarget||setTimeout(this._onScrollFrame,500),this._scrollTarget=t)}_updateCurrentTarget(e){let t=this._currentTarget,r=this._currentTarget,o=this._currentElement,i=n.findElementBehindBackdrop(e,this.document);this._currentElement=i,i!==o&&i!==r&&n.dispatchDragExit(this,r,i,e),i!==o&&i!==r&&(r=n.dispatchDragEnter(this,i,r,e)),r!==t&&(this._currentTarget=r,n.dispatchDragLeave(this,t,r,e));let s=n.dispatchDragOver(this,r,e);this._setDropAction(s)}_attachDragImage(e,t){if(!this.dragImage)return;this.dragImage.classList.add("lm-mod-drag-image");let r=this.dragImage.style;r.pointerEvents="none",r.position="fixed",r.transform=`translate(${e}px, ${t}px)`,(this.document instanceof Document?this.document.body:this.document.firstElementChild).appendChild(this.dragImage)}_detachDragImage(){if(!this.dragImage)return;let e=this.dragImage.parentNode;e&&e.removeChild(this.dragImage)}_setDropAction(e){if(e=n.validateAction(e,this.supportedActions),!this._override||this._dropAction!==e)switch(e){case"none":this._dropAction=e,this._override=i.overrideCursor("no-drop",this.document);break;case"copy":this._dropAction=e,this._override=i.overrideCursor("copy",this.document);break;case"link":this._dropAction=e,this._override=i.overrideCursor("alias",this.document);break;case"move":this._dropAction=e,this._override=i.overrideCursor("move",this.document)}}_finalize(e){let t=this._resolve;this._removeListeners(),this._detachDragImage(),this._override&&(this._override.dispose(),this._override=null),this.mimeData.clear(),this._disposed=!0,this._dropAction="none",this._currentTarget=null,this._currentElement=null,this._scrollTarget=null,this._promise=null,this._resolve=null,t&&t(e)}}!function(e){class t extends DragEvent{constructor(e,t){super(t.type,{bubbles:!0,cancelable:!0,altKey:e.altKey,button:e.button,clientX:e.clientX,clientY:e.clientY,ctrlKey:e.ctrlKey,detail:0,metaKey:e.metaKey,relatedTarget:t.related,screenX:e.screenX,screenY:e.screenY,shiftKey:e.shiftKey,view:window});const{drag:r}=t;this.dropAction="none",this.mimeData=r.mimeData,this.proposedAction=r.proposedAction,this.supportedActions=r.supportedActions,this.source=r.source}}e.Event=t,e.overrideCursor=function(e,t=document){return n.overrideCursor(e,t)}}(i||(i={})),function(e){function t(t,o=document){if(t){if(r&&t==r.event)return r.element;e.cursorBackdrop.style.zIndex="-1000";const n=o.elementFromPoint(t.clientX,t.clientY);return e.cursorBackdrop.style.zIndex="",r={event:t,element:n},n}{const t=e.cursorBackdrop.style.transform;if(n&&t===n.transform)return n.element;const r=e.cursorBackdrop.getBoundingClientRect();e.cursorBackdrop.style.zIndex="-1000";const i=o.elementFromPoint(r.left+r.width/2,r.top+r.height/2);return e.cursorBackdrop.style.zIndex="",n={transform:t,element:i},i}}e.SCROLL_EDGE_SIZE=20,e.validateAction=function(e,t){return s[e]&a[t]?e:"none"},e.findElementBehindBackdrop=t;let r=null,n=null;e.findScrollTarget=function(r){let n=r.clientX,o=r.clientY,i=t(r);for(;i;i=i.parentElement){if(!i.hasAttribute("data-lm-dragscroll"))continue;let t=0,r=0;i===document.body&&(t=window.pageXOffset,r=window.pageYOffset);let s=i.getBoundingClientRect(),a=s.top+r,c=s.left+t,l=c+s.width,d=a+s.height;if(n<c||n>=l||o<a||o>=d)continue;let u,p=n-c+1,h=o-a+1,m=l-n,v=d-o,g=Math.min(p,h,m,v);if(g>e.SCROLL_EDGE_SIZE)continue;switch(g){case v:u="bottom";break;case h:u="top";break;case m:u="right";break;case p:u="left";break;default:throw"unreachable"}let _,f=i.scrollWidth-i.clientWidth,E=i.scrollHeight-i.clientHeight;switch(u){case"top":_=E>0&&i.scrollTop>0;break;case"left":_=f>0&&i.scrollLeft>0;break;case"right":_=f>0&&i.scrollLeft<f;break;case"bottom":_=E>0&&i.scrollTop<E;break;default:throw"unreachable"}if(_)return{element:i,edge:u,distance:g}}return null},e.dispatchDragEnter=function(e,t,r,n){if(!t)return null;let o=new i.Event(n,{drag:e,related:r,type:"lm-dragenter"});if(!t.dispatchEvent(o))return t;const s=e.document instanceof Document?e.document.body:e.document.firstElementChild;return t===s?r:(o=new i.Event(n,{drag:e,related:r,type:"lm-dragenter"}),s.dispatchEvent(o),s)},e.dispatchDragExit=function(e,t,r,n){if(!t)return;let o=new i.Event(n,{drag:e,related:r,type:"lm-dragexit"});t.dispatchEvent(o)},e.dispatchDragLeave=function(e,t,r,n){if(!t)return;let o=new i.Event(n,{drag:e,related:r,type:"lm-dragleave"});t.dispatchEvent(o)},e.dispatchDragOver=function(e,t,r){if(!t)return"none";let n=new i.Event(r,{drag:e,related:null,type:"lm-dragover"});return t.dispatchEvent(n)?"none":n.dropAction},e.dispatchDrop=function(e,t,r){if(!t)return"none";let n=new i.Event(r,{drag:e,related:null,type:"lm-drop"});return t.dispatchEvent(n)?"none":n.dropAction};const s={none:0,copy:1,link:2,move:4},a={none:s.none,copy:s.copy,link:s.link,move:s.move,"copy-link":s.copy|s.link,"copy-move":s.copy|s.move,"link-move":s.link|s.move,all:s.copy|s.link|s.move};function c(t){e.cursorBackdrop&&(e.cursorBackdrop.style.transform=`translate(${t.clientX}px, ${t.clientY}px)`)}function l(r){if(!e.cursorBackdrop)return;let n=t();if(!n)return;const o=n.closest("[data-lm-dragscroll]");o&&(o.scrollTop+=e.cursorBackdrop.scrollTop-u,o.scrollLeft+=e.cursorBackdrop.scrollLeft-u,d())}function d(){e.cursorBackdrop.scrollTop=u,e.cursorBackdrop.scrollLeft=u}e.overrideCursor=function(t,r=document){let n=++p;const i=r instanceof Document?r.body:r.firstElementChild;return e.cursorBackdrop.isConnected||(e.cursorBackdrop.style.transform="scale(0)",i.appendChild(e.cursorBackdrop),d(),document.addEventListener("pointermove",c,{capture:!0,passive:!0}),e.cursorBackdrop.addEventListener("scroll",l,{capture:!0,passive:!0})),e.cursorBackdrop.style.cursor=t,new o.DisposableDelegate((()=>{n===p&&e.cursorBackdrop.isConnected&&(document.removeEventListener("pointermove",c,!0),e.cursorBackdrop.removeEventListener("scroll",l,!0),i.removeChild(e.cursorBackdrop))}))};const u=500;let p=0;e.cursorBackdrop=function(){const e=document.createElement("div");return e.classList.add("lm-cursor-backdrop"),e}()}(n||(n={}))}}]);
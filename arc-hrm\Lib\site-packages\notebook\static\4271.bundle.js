"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[4271],{64271:(t,e,s)=>{s.r(e),s.d(e,{Debouncer:()=>o,Poll:()=>r,RateLimiter:()=>h,Throttler:()=>d});var i,a=s(20998),n=s(81997);class r{constructor(t){var e;this._disposed=new n.Signal(this),this._lingered=0,this._tick=new a.PromiseDelegate,this._ticked=new n.Signal(this),this._factory=t.factory,this._linger=null!==(e=t.linger)&&void 0!==e?e:i.DEFAULT_LINGER,this._standby=t.standby||i.DEFAULT_STANDBY,this._state={...i.DEFAULT_STATE,timestamp:(new Date).getTime()};const s=t.frequency||{},r=Math.max(s.interval||0,s.max||0,i.DEFAULT_FREQUENCY.max);this.frequency={...i.DEFAULT_FREQUENCY,...s,max:r},this.name=t.name||i.DEFAULT_NAME,"auto"in t&&!t.auto||setTimeout((()=>this.start()))}get disposed(){return this._disposed}get frequency(){return this._frequency}set frequency(t){if(this.isDisposed||a.JSONExt.deepEqual(t,this.frequency||{}))return;let{backoff:e,interval:s,max:i}=t;if(s=Math.round(s),i=Math.round(i),"number"==typeof e&&e<1)throw new Error("Poll backoff growth factor must be at least 1");if((s<0||s>i)&&s!==r.NEVER)throw new Error("Poll interval must be between 0 and max");if(i>r.MAX_INTERVAL&&i!==r.NEVER)throw new Error(`Max interval must be less than ${r.MAX_INTERVAL}`);this._frequency={backoff:e,interval:s,max:i}}get isDisposed(){return"disposed"===this.state.phase}get standby(){return this._standby}set standby(t){this.isDisposed||this.standby===t||(this._standby=t)}get state(){return this._state}get tick(){return this._tick.promise}get ticked(){return this._ticked}async*[Symbol.asyncIterator](){for(;!this.isDisposed;)yield this.state,await this.tick.catch((()=>{}))}dispose(){this.isDisposed||(this._state={...i.DISPOSED_STATE,timestamp:(new Date).getTime()},this._tick.promise.catch((t=>{})),this._tick.reject(new Error(`Poll (${this.name}) is disposed.`)),this._disposed.emit(void 0),n.Signal.clearData(this))}refresh(){return this.schedule({cancel:({phase:t})=>"refreshed"===t,interval:r.IMMEDIATE,phase:"refreshed"})}async schedule(t={}){if(this.isDisposed)return;if(t.cancel&&t.cancel(this.state))return;const e=this._tick,s=new a.PromiseDelegate,i={interval:this.frequency.interval,payload:null,phase:"standby",timestamp:(new Date).getTime(),...t};this._state=i,this._tick=s,clearTimeout(this._timeout),this._ticked.emit(this.state),e.resolve(this),await e.promise,i.interval!==r.NEVER?this._timeout=setTimeout((()=>{this.isDisposed||this.tick!==s.promise||this._execute()}),i.interval):this._timeout=void 0}start(){return this.schedule({cancel:({phase:t})=>"constructed"!==t&&"standby"!==t&&"stopped"!==t,interval:r.IMMEDIATE,phase:"started"})}stop(){return this.schedule({cancel:({phase:t})=>"stopped"===t,interval:r.NEVER,phase:"stopped"})}get hidden(){return i.hidden}_execute(){let t="function"==typeof this.standby?this.standby():this.standby;if("never"===t?t=!1:"when-hidden"===t&&(this.hidden?t=++this._lingered>this._linger:(this._lingered=0,t=!1)),t)return void this.schedule();const e=this.tick;this._factory(this.state).then((t=>{this.isDisposed||this.tick!==e||this.schedule({payload:t,phase:"rejected"===this.state.phase?"reconnected":"resolved"})})).catch((t=>{this.isDisposed||this.tick!==e||this.schedule({interval:i.sleep(this.frequency,this.state),payload:t,phase:"rejected"})}))}}!function(t){t.IMMEDIATE=0,t.MAX_INTERVAL=2147483647,t.NEVER=1/0}(r||(r={})),function(t){t.DEFAULT_BACKOFF=3,t.DEFAULT_FREQUENCY={backoff:!0,interval:1e3,max:3e4},t.DEFAULT_LINGER=1,t.DEFAULT_NAME="unknown",t.DEFAULT_STANDBY="when-hidden",t.DEFAULT_STATE={interval:r.NEVER,payload:null,phase:"constructed",timestamp:new Date(0).getTime()},t.DISPOSED_STATE={interval:r.NEVER,payload:null,phase:"disposed",timestamp:new Date(0).getTime()},t.sleep=function(e,s){const{backoff:i,interval:a,max:n}=e;if(a===r.NEVER)return a;const h=!0===i?t.DEFAULT_BACKOFF:!1===i?1:i,o=function(t,e){return t=Math.ceil(t),e=Math.floor(e),Math.floor(Math.random()*(e-t+1))+t}(a,s.interval*h);return Math.min(n,o)},t.hidden="undefined"!=typeof document&&(document.addEventListener("visibilitychange",(()=>{t.hidden="hidden"===document.visibilityState})),document.addEventListener("pagehide",(()=>{t.hidden="hidden"===document.visibilityState})),"hidden"===document.visibilityState)}(i||(i={}));class h{constructor(t,e=500){this.args=void 0,this.payload=null,this.limit=e,this.poll=new r({auto:!1,factory:async()=>{const{args:e}=this;return this.args=void 0,t(...e)},frequency:{backoff:!1,interval:r.NEVER,max:r.NEVER},standby:"never"}),this.payload=new a.PromiseDelegate,this.poll.ticked.connect(((t,e)=>{const{payload:s}=this;return"resolved"===e.phase?(this.payload=new a.PromiseDelegate,void s.resolve(e.payload)):"rejected"===e.phase||"stopped"===e.phase?(this.payload=new a.PromiseDelegate,s.promise.catch((t=>{})),void s.reject(e.payload)):void 0}),this)}get isDisposed(){return null===this.payload}dispose(){this.isDisposed||(this.args=void 0,this.payload=null,this.poll.dispose())}async stop(){return this.poll.stop()}}class o extends h{invoke(...t){return this.args=t,this.poll.schedule({interval:this.limit,phase:"invoked"}),this.payload.promise}}class d extends h{constructor(t,e){super(t,"number"==typeof e?e:e&&e.limit),this._trailing=!1,"number"!=typeof e&&e&&"trailing"===e.edge&&(this._trailing=!0),this._interval=this._trailing?this.limit:r.IMMEDIATE}invoke(...t){const e="invoked"!==this.poll.state.phase;return(e||this._trailing)&&(this.args=t),e&&this.poll.schedule({interval:this._interval,phase:"invoked"}),this.payload.promise}}}}]);
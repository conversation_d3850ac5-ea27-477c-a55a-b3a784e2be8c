"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[690],{60690:(e,t,n)=>{n.r(t),n.d(t,{MIME_TYPE:()=>d,MIME_TYPES_JSONL:()=>l,RenderedJSON:()=>p,default:()=>m,rendererFactory:()=>c});var r=n(12982),o=n(71677),s=n(31516),i=n(78156),a=n(37634);const d="application/json",l=["text/jsonl","application/jsonl","application/json-lines"];class p extends s.Widget{constructor(e){super(),this._rootDOM=null,this.addClass("jp-RenderedJSON"),this.addClass("CodeMirror"),this._mimeType=e.mimeType,this.translator=e.translator||o.nullTranslator}[r.Printing.symbol](){return()=>r.Printing.printWidget(this)}async renderModel(e){const{Component:t}=await Promise.all([n.e(9752),n.e(998),n.e(8239),n.e(2337),n.e(6361)]).then(n.bind(n,69752));let r;if(l.indexOf(this._mimeType)>=0){const t=(e.data[this._mimeType]||"").trim().split(/\n/);r=JSON.parse(`[${t.join(",")}]`)}else r=e.data[this._mimeType]||{};const o=e.metadata[this._mimeType]||{};return null===this._rootDOM&&(this._rootDOM=(0,a.s)(this.node)),new Promise(((e,n)=>{this._rootDOM.render(i.createElement(t,{data:r,metadata:o,translator:this.translator,forwardedRef:()=>e()}))}))}onBeforeDetach(e){this._rootDOM&&(this._rootDOM.unmount(),this._rootDOM=null)}}const c={safe:!0,mimeTypes:[d,...l],createRenderer:e=>new p(e)},m=[{id:"@jupyterlab/json-extension:factory",description:"Adds renderer for JSON content.",rendererFactory:c,rank:0,dataType:"json",documentWidgetFactoryOptions:{name:"JSON",primaryFileType:"json",fileTypes:["json","notebook","geojson"],defaultFor:["json"]}},{id:"@jupyterlab/json-lines-extension:factory",description:"Adds renderer for JSONLines content.",rendererFactory:c,rank:0,dataType:"string",documentWidgetFactoryOptions:{name:"JSONLines",primaryFileType:"jsonl",fileTypes:["jsonl","ndjson"],defaultFor:["jsonl","ndjson"]}}]},37634:(e,t,n)=>{var r=n(38005);t.s=r.createRoot,r.hydrateRoot}}]);
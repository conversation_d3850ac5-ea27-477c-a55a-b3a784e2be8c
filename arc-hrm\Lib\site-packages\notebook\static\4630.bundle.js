/*! For license information please see 4630.bundle.js.LICENSE.txt */
(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[4630],{7608:(t,e)=>{"use strict";e.Nm=e.Rq=void 0;var i=/^([^\w]*)(javascript|data|vbscript)/im,r=/&#(\w+)(^\w|;)?/g,o=/&(newline|tab);/gi,n=/[\u0000-\u001F\u007F-\u009F\u2000-\u200D\uFEFF]/gim,a=/^.+(:|&colon;)/gim,s=[".","/"];e.Rq="about:blank",e.Nm=function(t){if(!t)return e.Rq;var l,h=(l=t,l.replace(n,"").replace(r,(function(t,e){return String.fromCharCode(e)}))).replace(o,"").replace(n,"").trim();if(!h)return e.Rq;if(function(t){return s.indexOf(t[0])>-1}(h))return h;var c=h.match(a);if(!c)return h;var u=c[0];return i.test(u)?e.Rq:h}},23617:(t,e,i)=>{"use strict";i.d(e,{Nb1:()=>Ie.Z,LLu:()=>C,F5q:()=>y,$0Z:()=>He.ZP,Dts:()=>Re.Z,WQY:()=>We.Z,qpX:()=>Ye,u93:()=>Ve,tFB:()=>Ge.Z,YY7:()=>Qe.ZP,OvA:()=>Xe.Z,dCK:()=>Je.Z,zgE:()=>ei.Z,fGX:()=>Ke.Z,$m7:()=>ti.Z,c_6:()=>ri.Z,fxm:()=>ii.Z,FdL:()=>oi.Z,ak_:()=>oi.s,SxZ:()=>ni.Z,eA_:()=>ai.ZP,jsv:()=>ai.cD,iJ:()=>ai.RN,JHv:()=>Se.interpolateHcl,jvg:()=>qe.Z,Fp7:()=>r.Z,VV$:()=>o.Z,ve8:()=>je,tiA:()=>Ae,BYU:()=>Le.Z,PKp:()=>we.Z,Xf:()=>Ze.Z,K2I:()=>Ee,Ys:()=>Me,td_:()=>Oe,YPS:()=>_e,rr1:()=>ui.rr,i$Z:()=>pi.i$,y2j:()=>di.y2,WQD:()=>ci.WQ,U8T:()=>si.A,Z_i:()=>hi.Z_,Ox9:()=>di.Ox,F0B:()=>fi.F0,LqH:()=>di.Lq,S1K:()=>li.E,Zyz:()=>di.Zy,Igq:()=>di.Ig,YDX:()=>di.YD,EFj:()=>di.EF});var r=i(53856),o=i(93571);function n(t){return t}var a=1,s=2,l=3,h=4,c=1e-6;function u(t){return"translate("+t+",0)"}function d(t){return"translate(0,"+t+")"}function f(t){return e=>+t(e)}function p(t,e){return e=Math.max(0,t.bandwidth()-2*e)/2,t.round()&&(e=Math.round(e)),i=>+t(i)+e}function g(){return!this.__axis}function m(t,e){var i=[],r=null,o=null,m=6,y=6,C=3,b="undefined"!=typeof window&&window.devicePixelRatio>1?0:.5,x=t===a||t===h?-1:1,v=t===h||t===s?"x":"y",k=t===a||t===l?u:d;function T(u){var d=null==r?e.ticks?e.ticks.apply(e,i):e.domain():r,T=null==o?e.tickFormat?e.tickFormat.apply(e,i):n:o,_=Math.max(m,0)+C,S=e.range(),B=+S[0]+b,F=+S[S.length-1]+b,w=(e.bandwidth?p:f)(e.copy(),b),A=u.selection?u.selection():u,L=A.selectAll(".domain").data([null]),Z=A.selectAll(".tick").data(d,e).order(),E=Z.exit(),M=Z.enter().append("g").attr("class","tick"),O=Z.select("line"),I=Z.select("text");L=L.merge(L.enter().insert("path",".tick").attr("class","domain").attr("stroke","currentColor")),Z=Z.merge(M),O=O.merge(M.append("line").attr("stroke","currentColor").attr(v+"2",x*m)),I=I.merge(M.append("text").attr("fill","currentColor").attr(v,x*_).attr("dy",t===a?"0em":t===l?"0.71em":"0.32em")),u!==A&&(L=L.transition(u),Z=Z.transition(u),O=O.transition(u),I=I.transition(u),E=E.transition(u).attr("opacity",c).attr("transform",(function(t){return isFinite(t=w(t))?k(t+b):this.getAttribute("transform")})),M.attr("opacity",c).attr("transform",(function(t){var e=this.parentNode.__axis;return k((e&&isFinite(e=e(t))?e:w(t))+b)}))),E.remove(),L.attr("d",t===h||t===s?y?"M"+x*y+","+B+"H"+b+"V"+F+"H"+x*y:"M"+b+","+B+"V"+F:y?"M"+B+","+x*y+"V"+b+"H"+F+"V"+x*y:"M"+B+","+b+"H"+F),Z.attr("opacity",1).attr("transform",(function(t){return k(w(t)+b)})),O.attr(v+"2",x*m),I.attr(v,x*_).text(T),A.filter(g).attr("fill","none").attr("font-size",10).attr("font-family","sans-serif").attr("text-anchor",t===s?"start":t===h?"end":"middle"),A.each((function(){this.__axis=w}))}return T.scale=function(t){return arguments.length?(e=t,T):e},T.ticks=function(){return i=Array.from(arguments),T},T.tickArguments=function(t){return arguments.length?(i=null==t?[]:Array.from(t),T):i.slice()},T.tickValues=function(t){return arguments.length?(r=null==t?null:Array.from(t),T):r&&r.slice()},T.tickFormat=function(t){return arguments.length?(o=t,T):o},T.tickSize=function(t){return arguments.length?(m=y=+t,T):m},T.tickSizeInner=function(t){return arguments.length?(m=+t,T):m},T.tickSizeOuter=function(t){return arguments.length?(y=+t,T):y},T.tickPadding=function(t){return arguments.length?(C=+t,T):C},T.offset=function(t){return arguments.length?(b=+t,T):b},T}function y(t){return m(a,t)}function C(t){return m(l,t)}function b(){}function x(t){return null==t?b:function(){return this.querySelector(t)}}function v(t){return null==t?[]:Array.isArray(t)?t:Array.from(t)}function k(){return[]}function T(t){return null==t?k:function(){return this.querySelectorAll(t)}}function _(t){return function(){return this.matches(t)}}function S(t){return function(e){return e.matches(t)}}var B=Array.prototype.find;function F(){return this.firstElementChild}var w=Array.prototype.filter;function A(){return Array.from(this.children)}function L(t){return new Array(t.length)}function Z(t,e){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=e}function E(t,e,i,r,o,n){for(var a,s=0,l=e.length,h=n.length;s<h;++s)(a=e[s])?(a.__data__=n[s],r[s]=a):i[s]=new Z(t,n[s]);for(;s<l;++s)(a=e[s])&&(o[s]=a)}function M(t,e,i,r,o,n,a){var s,l,h,c=new Map,u=e.length,d=n.length,f=new Array(u);for(s=0;s<u;++s)(l=e[s])&&(f[s]=h=a.call(l,l.__data__,s,e)+"",c.has(h)?o[s]=l:c.set(h,l));for(s=0;s<d;++s)h=a.call(t,n[s],s,n)+"",(l=c.get(h))?(r[s]=l,l.__data__=n[s],c.delete(h)):i[s]=new Z(t,n[s]);for(s=0;s<u;++s)(l=e[s])&&c.get(f[s])===l&&(o[s]=l)}function O(t){return t.__data__}function I(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function q(t,e){return t<e?-1:t>e?1:t>=e?0:NaN}Z.prototype={constructor:Z,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,e){return this._parent.insertBefore(t,e)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};var D="http://www.w3.org/1999/xhtml";const z={svg:"http://www.w3.org/2000/svg",xhtml:D,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function N(t){var e=t+="",i=e.indexOf(":");return i>=0&&"xmlns"!==(e=t.slice(0,i))&&(t=t.slice(i+1)),z.hasOwnProperty(e)?{space:z[e],local:t}:t}function P(t){return function(){this.removeAttribute(t)}}function $(t){return function(){this.removeAttributeNS(t.space,t.local)}}function j(t,e){return function(){this.setAttribute(t,e)}}function R(t,e){return function(){this.setAttributeNS(t.space,t.local,e)}}function W(t,e){return function(){var i=e.apply(this,arguments);null==i?this.removeAttribute(t):this.setAttribute(t,i)}}function H(t,e){return function(){var i=e.apply(this,arguments);null==i?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,i)}}function U(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}function Y(t){return function(){this.style.removeProperty(t)}}function V(t,e,i){return function(){this.style.setProperty(t,e,i)}}function G(t,e,i){return function(){var r=e.apply(this,arguments);null==r?this.style.removeProperty(t):this.style.setProperty(t,r,i)}}function X(t,e){return t.style.getPropertyValue(e)||U(t).getComputedStyle(t,null).getPropertyValue(e)}function J(t){return function(){delete this[t]}}function Q(t,e){return function(){this[t]=e}}function K(t,e){return function(){var i=e.apply(this,arguments);null==i?delete this[t]:this[t]=i}}function tt(t){return t.trim().split(/^|\s+/)}function et(t){return t.classList||new it(t)}function it(t){this._node=t,this._names=tt(t.getAttribute("class")||"")}function rt(t,e){for(var i=et(t),r=-1,o=e.length;++r<o;)i.add(e[r])}function ot(t,e){for(var i=et(t),r=-1,o=e.length;++r<o;)i.remove(e[r])}function nt(t){return function(){rt(this,t)}}function at(t){return function(){ot(this,t)}}function st(t,e){return function(){(e.apply(this,arguments)?rt:ot)(this,t)}}function lt(){this.textContent=""}function ht(t){return function(){this.textContent=t}}function ct(t){return function(){var e=t.apply(this,arguments);this.textContent=null==e?"":e}}function ut(){this.innerHTML=""}function dt(t){return function(){this.innerHTML=t}}function ft(t){return function(){var e=t.apply(this,arguments);this.innerHTML=null==e?"":e}}function pt(){this.nextSibling&&this.parentNode.appendChild(this)}function gt(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function mt(t){return function(){var e=this.ownerDocument,i=this.namespaceURI;return i===D&&e.documentElement.namespaceURI===D?e.createElement(t):e.createElementNS(i,t)}}function yt(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}function Ct(t){var e=N(t);return(e.local?yt:mt)(e)}function bt(){return null}function xt(){var t=this.parentNode;t&&t.removeChild(this)}function vt(){var t=this.cloneNode(!1),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function kt(){var t=this.cloneNode(!0),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function Tt(t){return function(){var e=this.__on;if(e){for(var i,r=0,o=-1,n=e.length;r<n;++r)i=e[r],t.type&&i.type!==t.type||i.name!==t.name?e[++o]=i:this.removeEventListener(i.type,i.listener,i.options);++o?e.length=o:delete this.__on}}}function _t(t,e,i){return function(){var r,o=this.__on,n=function(t){return function(e){t.call(this,e,this.__data__)}}(e);if(o)for(var a=0,s=o.length;a<s;++a)if((r=o[a]).type===t.type&&r.name===t.name)return this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=n,r.options=i),void(r.value=e);this.addEventListener(t.type,n,i),r={type:t.type,name:t.name,value:e,listener:n,options:i},o?o.push(r):this.__on=[r]}}function St(t,e,i){var r=U(t),o=r.CustomEvent;"function"==typeof o?o=new o(e,i):(o=r.document.createEvent("Event"),i?(o.initEvent(e,i.bubbles,i.cancelable),o.detail=i.detail):o.initEvent(e,!1,!1)),t.dispatchEvent(o)}function Bt(t,e){return function(){return St(this,t,e)}}function Ft(t,e){return function(){return St(this,t,e.apply(this,arguments))}}it.prototype={add:function(t){this._names.indexOf(t)<0&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var e=this._names.indexOf(t);e>=0&&(this._names.splice(e,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var wt=[null];function At(t,e){this._groups=t,this._parents=e}function Lt(){return new At([[document.documentElement]],wt)}At.prototype=Lt.prototype={constructor:At,select:function(t){"function"!=typeof t&&(t=x(t));for(var e=this._groups,i=e.length,r=new Array(i),o=0;o<i;++o)for(var n,a,s=e[o],l=s.length,h=r[o]=new Array(l),c=0;c<l;++c)(n=s[c])&&(a=t.call(n,n.__data__,c,s))&&("__data__"in n&&(a.__data__=n.__data__),h[c]=a);return new At(r,this._parents)},selectAll:function(t){t="function"==typeof t?function(t){return function(){return v(t.apply(this,arguments))}}(t):T(t);for(var e=this._groups,i=e.length,r=[],o=[],n=0;n<i;++n)for(var a,s=e[n],l=s.length,h=0;h<l;++h)(a=s[h])&&(r.push(t.call(a,a.__data__,h,s)),o.push(a));return new At(r,o)},selectChild:function(t){return this.select(null==t?F:function(t){return function(){return B.call(this.children,t)}}("function"==typeof t?t:S(t)))},selectChildren:function(t){return this.selectAll(null==t?A:function(t){return function(){return w.call(this.children,t)}}("function"==typeof t?t:S(t)))},filter:function(t){"function"!=typeof t&&(t=_(t));for(var e=this._groups,i=e.length,r=new Array(i),o=0;o<i;++o)for(var n,a=e[o],s=a.length,l=r[o]=[],h=0;h<s;++h)(n=a[h])&&t.call(n,n.__data__,h,a)&&l.push(n);return new At(r,this._parents)},data:function(t,e){if(!arguments.length)return Array.from(this,O);var i,r=e?M:E,o=this._parents,n=this._groups;"function"!=typeof t&&(i=t,t=function(){return i});for(var a=n.length,s=new Array(a),l=new Array(a),h=new Array(a),c=0;c<a;++c){var u=o[c],d=n[c],f=d.length,p=I(t.call(u,u&&u.__data__,c,o)),g=p.length,m=l[c]=new Array(g),y=s[c]=new Array(g);r(u,d,m,y,h[c]=new Array(f),p,e);for(var C,b,x=0,v=0;x<g;++x)if(C=m[x]){for(x>=v&&(v=x+1);!(b=y[v])&&++v<g;);C._next=b||null}}return(s=new At(s,o))._enter=l,s._exit=h,s},enter:function(){return new At(this._enter||this._groups.map(L),this._parents)},exit:function(){return new At(this._exit||this._groups.map(L),this._parents)},join:function(t,e,i){var r=this.enter(),o=this,n=this.exit();return"function"==typeof t?(r=t(r))&&(r=r.selection()):r=r.append(t+""),null!=e&&(o=e(o))&&(o=o.selection()),null==i?n.remove():i(n),r&&o?r.merge(o).order():o},merge:function(t){for(var e=t.selection?t.selection():t,i=this._groups,r=e._groups,o=i.length,n=r.length,a=Math.min(o,n),s=new Array(o),l=0;l<a;++l)for(var h,c=i[l],u=r[l],d=c.length,f=s[l]=new Array(d),p=0;p<d;++p)(h=c[p]||u[p])&&(f[p]=h);for(;l<o;++l)s[l]=i[l];return new At(s,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,e=-1,i=t.length;++e<i;)for(var r,o=t[e],n=o.length-1,a=o[n];--n>=0;)(r=o[n])&&(a&&4^r.compareDocumentPosition(a)&&a.parentNode.insertBefore(r,a),a=r);return this},sort:function(t){function e(e,i){return e&&i?t(e.__data__,i.__data__):!e-!i}t||(t=q);for(var i=this._groups,r=i.length,o=new Array(r),n=0;n<r;++n){for(var a,s=i[n],l=s.length,h=o[n]=new Array(l),c=0;c<l;++c)(a=s[c])&&(h[c]=a);h.sort(e)}return new At(o,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,e=0,i=t.length;e<i;++e)for(var r=t[e],o=0,n=r.length;o<n;++o){var a=r[o];if(a)return a}return null},size:function(){let t=0;for(const e of this)++t;return t},empty:function(){return!this.node()},each:function(t){for(var e=this._groups,i=0,r=e.length;i<r;++i)for(var o,n=e[i],a=0,s=n.length;a<s;++a)(o=n[a])&&t.call(o,o.__data__,a,n);return this},attr:function(t,e){var i=N(t);if(arguments.length<2){var r=this.node();return i.local?r.getAttributeNS(i.space,i.local):r.getAttribute(i)}return this.each((null==e?i.local?$:P:"function"==typeof e?i.local?H:W:i.local?R:j)(i,e))},style:function(t,e,i){return arguments.length>1?this.each((null==e?Y:"function"==typeof e?G:V)(t,e,null==i?"":i)):X(this.node(),t)},property:function(t,e){return arguments.length>1?this.each((null==e?J:"function"==typeof e?K:Q)(t,e)):this.node()[t]},classed:function(t,e){var i=tt(t+"");if(arguments.length<2){for(var r=et(this.node()),o=-1,n=i.length;++o<n;)if(!r.contains(i[o]))return!1;return!0}return this.each(("function"==typeof e?st:e?nt:at)(i,e))},text:function(t){return arguments.length?this.each(null==t?lt:("function"==typeof t?ct:ht)(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?ut:("function"==typeof t?ft:dt)(t)):this.node().innerHTML},raise:function(){return this.each(pt)},lower:function(){return this.each(gt)},append:function(t){var e="function"==typeof t?t:Ct(t);return this.select((function(){return this.appendChild(e.apply(this,arguments))}))},insert:function(t,e){var i="function"==typeof t?t:Ct(t),r=null==e?bt:"function"==typeof e?e:x(e);return this.select((function(){return this.insertBefore(i.apply(this,arguments),r.apply(this,arguments)||null)}))},remove:function(){return this.each(xt)},clone:function(t){return this.select(t?kt:vt)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,e,i){var r,o,n=function(t){return t.trim().split(/^|\s+/).map((function(t){var e="",i=t.indexOf(".");return i>=0&&(e=t.slice(i+1),t=t.slice(0,i)),{type:t,name:e}}))}(t+""),a=n.length;if(!(arguments.length<2)){for(s=e?_t:Tt,r=0;r<a;++r)this.each(s(n[r],e,i));return this}var s=this.node().__on;if(s)for(var l,h=0,c=s.length;h<c;++h)for(r=0,l=s[h];r<a;++r)if((o=n[r]).type===l.type&&o.name===l.name)return l.value},dispatch:function(t,e){return this.each(("function"==typeof e?Ft:Bt)(t,e))},[Symbol.iterator]:function*(){for(var t=this._groups,e=0,i=t.length;e<i;++e)for(var r,o=t[e],n=0,a=o.length;n<a;++n)(r=o[n])&&(yield r)}};const Zt=Lt;var Et=i(65043),Mt=i(11647);function Ot(t,e,i){var r=new Mt.B7;return e=null==e?0:+e,r.restart((i=>{r.stop(),t(i+e)}),e,i),r}var It=(0,Et.Z)("start","end","cancel","interrupt"),qt=[],Dt=0,zt=3;function Nt(t,e,i,r,o,n){var a=t.__transition;if(a){if(i in a)return}else t.__transition={};!function(t,e,i){var r,o=t.__transition;function n(l){var h,c,u,d;if(1!==i.state)return s();for(h in o)if((d=o[h]).name===i.name){if(d.state===zt)return Ot(n);4===d.state?(d.state=6,d.timer.stop(),d.on.call("interrupt",t,t.__data__,d.index,d.group),delete o[h]):+h<e&&(d.state=6,d.timer.stop(),d.on.call("cancel",t,t.__data__,d.index,d.group),delete o[h])}if(Ot((function(){i.state===zt&&(i.state=4,i.timer.restart(a,i.delay,i.time),a(l))})),i.state=2,i.on.call("start",t,t.__data__,i.index,i.group),2===i.state){for(i.state=zt,r=new Array(u=i.tween.length),h=0,c=-1;h<u;++h)(d=i.tween[h].value.call(t,t.__data__,i.index,i.group))&&(r[++c]=d);r.length=c+1}}function a(e){for(var o=e<i.duration?i.ease.call(null,e/i.duration):(i.timer.restart(s),i.state=5,1),n=-1,a=r.length;++n<a;)r[n].call(t,o);5===i.state&&(i.on.call("end",t,t.__data__,i.index,i.group),s())}function s(){for(var r in i.state=6,i.timer.stop(),delete o[e],o)return;delete t.__transition}o[e]=i,i.timer=(0,Mt.HT)((function(t){i.state=1,i.timer.restart(n,i.delay,i.time),i.delay<=t&&n(t-i.delay)}),0,i.time)}(t,i,{name:e,index:r,group:o,on:It,tween:qt,time:n.time,delay:n.delay,duration:n.duration,ease:n.ease,timer:null,state:Dt})}function Pt(t,e){var i=jt(t,e);if(i.state>Dt)throw new Error("too late; already scheduled");return i}function $t(t,e){var i=jt(t,e);if(i.state>zt)throw new Error("too late; already running");return i}function jt(t,e){var i=t.__transition;if(!i||!(i=i[e]))throw new Error("transition not found");return i}var Rt=i(52087);function Wt(t,e){var i,r;return function(){var o=$t(this,t),n=o.tween;if(n!==i)for(var a=0,s=(r=i=n).length;a<s;++a)if(r[a].name===e){(r=r.slice()).splice(a,1);break}o.tween=r}}function Ht(t,e,i){var r,o;if("function"!=typeof i)throw new Error;return function(){var n=$t(this,t),a=n.tween;if(a!==r){o=(r=a).slice();for(var s={name:e,value:i},l=0,h=o.length;l<h;++l)if(o[l].name===e){o[l]=s;break}l===h&&o.push(s)}n.tween=o}}function Ut(t,e,i){var r=t._id;return t.each((function(){var t=$t(this,r);(t.value||(t.value={}))[e]=i.apply(this,arguments)})),function(t){return jt(t,r).value[e]}}var Yt=i(12738),Vt=i(75381),Gt=i(32278),Xt=i(39493);function Jt(t,e){var i;return("number"==typeof e?Vt.Z:e instanceof Yt.ZP?Gt.ZP:(i=(0,Yt.ZP)(e))?(e=i,Gt.ZP):Xt.Z)(t,e)}function Qt(t){return function(){this.removeAttribute(t)}}function Kt(t){return function(){this.removeAttributeNS(t.space,t.local)}}function te(t,e,i){var r,o,n=i+"";return function(){var a=this.getAttribute(t);return a===n?null:a===r?o:o=e(r=a,i)}}function ee(t,e,i){var r,o,n=i+"";return function(){var a=this.getAttributeNS(t.space,t.local);return a===n?null:a===r?o:o=e(r=a,i)}}function ie(t,e,i){var r,o,n;return function(){var a,s,l=i(this);if(null!=l)return(a=this.getAttribute(t))===(s=l+"")?null:a===r&&s===o?n:(o=s,n=e(r=a,l));this.removeAttribute(t)}}function re(t,e,i){var r,o,n;return function(){var a,s,l=i(this);if(null!=l)return(a=this.getAttributeNS(t.space,t.local))===(s=l+"")?null:a===r&&s===o?n:(o=s,n=e(r=a,l));this.removeAttributeNS(t.space,t.local)}}function oe(t,e){var i,r;function o(){var o=e.apply(this,arguments);return o!==r&&(i=(r=o)&&function(t,e){return function(i){this.setAttributeNS(t.space,t.local,e.call(this,i))}}(t,o)),i}return o._value=e,o}function ne(t,e){var i,r;function o(){var o=e.apply(this,arguments);return o!==r&&(i=(r=o)&&function(t,e){return function(i){this.setAttribute(t,e.call(this,i))}}(t,o)),i}return o._value=e,o}function ae(t,e){return function(){Pt(this,t).delay=+e.apply(this,arguments)}}function se(t,e){return e=+e,function(){Pt(this,t).delay=e}}function le(t,e){return function(){$t(this,t).duration=+e.apply(this,arguments)}}function he(t,e){return e=+e,function(){$t(this,t).duration=e}}var ce=Zt.prototype.constructor;function ue(t){return function(){this.style.removeProperty(t)}}var de=0;function fe(t,e,i,r){this._groups=t,this._parents=e,this._name=i,this._id=r}function pe(){return++de}var ge=Zt.prototype;fe.prototype=function(t){return Zt().transition(t)}.prototype={constructor:fe,select:function(t){var e=this._name,i=this._id;"function"!=typeof t&&(t=x(t));for(var r=this._groups,o=r.length,n=new Array(o),a=0;a<o;++a)for(var s,l,h=r[a],c=h.length,u=n[a]=new Array(c),d=0;d<c;++d)(s=h[d])&&(l=t.call(s,s.__data__,d,h))&&("__data__"in s&&(l.__data__=s.__data__),u[d]=l,Nt(u[d],e,i,d,u,jt(s,i)));return new fe(n,this._parents,e,i)},selectAll:function(t){var e=this._name,i=this._id;"function"!=typeof t&&(t=T(t));for(var r=this._groups,o=r.length,n=[],a=[],s=0;s<o;++s)for(var l,h=r[s],c=h.length,u=0;u<c;++u)if(l=h[u]){for(var d,f=t.call(l,l.__data__,u,h),p=jt(l,i),g=0,m=f.length;g<m;++g)(d=f[g])&&Nt(d,e,i,g,f,p);n.push(f),a.push(l)}return new fe(n,a,e,i)},selectChild:ge.selectChild,selectChildren:ge.selectChildren,filter:function(t){"function"!=typeof t&&(t=_(t));for(var e=this._groups,i=e.length,r=new Array(i),o=0;o<i;++o)for(var n,a=e[o],s=a.length,l=r[o]=[],h=0;h<s;++h)(n=a[h])&&t.call(n,n.__data__,h,a)&&l.push(n);return new fe(r,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw new Error;for(var e=this._groups,i=t._groups,r=e.length,o=i.length,n=Math.min(r,o),a=new Array(r),s=0;s<n;++s)for(var l,h=e[s],c=i[s],u=h.length,d=a[s]=new Array(u),f=0;f<u;++f)(l=h[f]||c[f])&&(d[f]=l);for(;s<r;++s)a[s]=e[s];return new fe(a,this._parents,this._name,this._id)},selection:function(){return new ce(this._groups,this._parents)},transition:function(){for(var t=this._name,e=this._id,i=pe(),r=this._groups,o=r.length,n=0;n<o;++n)for(var a,s=r[n],l=s.length,h=0;h<l;++h)if(a=s[h]){var c=jt(a,e);Nt(a,t,i,h,s,{time:c.time+c.delay+c.duration,delay:0,duration:c.duration,ease:c.ease})}return new fe(r,this._parents,t,i)},call:ge.call,nodes:ge.nodes,node:ge.node,size:ge.size,empty:ge.empty,each:ge.each,on:function(t,e){var i=this._id;return arguments.length<2?jt(this.node(),i).on.on(t):this.each(function(t,e,i){var r,o,n=function(t){return(t+"").trim().split(/^|\s+/).every((function(t){var e=t.indexOf(".");return e>=0&&(t=t.slice(0,e)),!t||"start"===t}))}(e)?Pt:$t;return function(){var a=n(this,t),s=a.on;s!==r&&(o=(r=s).copy()).on(e,i),a.on=o}}(i,t,e))},attr:function(t,e){var i=N(t),r="transform"===i?Rt.w:Jt;return this.attrTween(t,"function"==typeof e?(i.local?re:ie)(i,r,Ut(this,"attr."+t,e)):null==e?(i.local?Kt:Qt)(i):(i.local?ee:te)(i,r,e))},attrTween:function(t,e){var i="attr."+t;if(arguments.length<2)return(i=this.tween(i))&&i._value;if(null==e)return this.tween(i,null);if("function"!=typeof e)throw new Error;var r=N(t);return this.tween(i,(r.local?oe:ne)(r,e))},style:function(t,e,i){var r="transform"==(t+="")?Rt.Y:Jt;return null==e?this.styleTween(t,function(t,e){var i,r,o;return function(){var n=X(this,t),a=(this.style.removeProperty(t),X(this,t));return n===a?null:n===i&&a===r?o:o=e(i=n,r=a)}}(t,r)).on("end.style."+t,ue(t)):"function"==typeof e?this.styleTween(t,function(t,e,i){var r,o,n;return function(){var a=X(this,t),s=i(this),l=s+"";return null==s&&(this.style.removeProperty(t),l=s=X(this,t)),a===l?null:a===r&&l===o?n:(o=l,n=e(r=a,s))}}(t,r,Ut(this,"style."+t,e))).each(function(t,e){var i,r,o,n,a="style."+e,s="end."+a;return function(){var l=$t(this,t),h=l.on,c=null==l.value[a]?n||(n=ue(e)):void 0;h===i&&o===c||(r=(i=h).copy()).on(s,o=c),l.on=r}}(this._id,t)):this.styleTween(t,function(t,e,i){var r,o,n=i+"";return function(){var a=X(this,t);return a===n?null:a===r?o:o=e(r=a,i)}}(t,r,e),i).on("end.style."+t,null)},styleTween:function(t,e,i){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==e)return this.tween(r,null);if("function"!=typeof e)throw new Error;return this.tween(r,function(t,e,i){var r,o;function n(){var n=e.apply(this,arguments);return n!==o&&(r=(o=n)&&function(t,e,i){return function(r){this.style.setProperty(t,e.call(this,r),i)}}(t,n,i)),r}return n._value=e,n}(t,e,null==i?"":i))},text:function(t){return this.tween("text","function"==typeof t?function(t){return function(){var e=t(this);this.textContent=null==e?"":e}}(Ut(this,"text",t)):function(t){return function(){this.textContent=t}}(null==t?"":t+""))},textTween:function(t){var e="text";if(arguments.length<1)return(e=this.tween(e))&&e._value;if(null==t)return this.tween(e,null);if("function"!=typeof t)throw new Error;return this.tween(e,function(t){var e,i;function r(){var r=t.apply(this,arguments);return r!==i&&(e=(i=r)&&function(t){return function(e){this.textContent=t.call(this,e)}}(r)),e}return r._value=t,r}(t))},remove:function(){return this.on("end.remove",function(t){return function(){var e=this.parentNode;for(var i in this.__transition)if(+i!==t)return;e&&e.removeChild(this)}}(this._id))},tween:function(t,e){var i=this._id;if(t+="",arguments.length<2){for(var r,o=jt(this.node(),i).tween,n=0,a=o.length;n<a;++n)if((r=o[n]).name===t)return r.value;return null}return this.each((null==e?Wt:Ht)(i,t,e))},delay:function(t){var e=this._id;return arguments.length?this.each(("function"==typeof t?ae:se)(e,t)):jt(this.node(),e).delay},duration:function(t){var e=this._id;return arguments.length?this.each(("function"==typeof t?le:he)(e,t)):jt(this.node(),e).duration},ease:function(t){var e=this._id;return arguments.length?this.each(function(t,e){if("function"!=typeof e)throw new Error;return function(){$t(this,t).ease=e}}(e,t)):jt(this.node(),e).ease},easeVarying:function(t){if("function"!=typeof t)throw new Error;return this.each(function(t,e){return function(){var i=e.apply(this,arguments);if("function"!=typeof i)throw new Error;$t(this,t).ease=i}}(this._id,t))},end:function(){var t,e,i=this,r=i._id,o=i.size();return new Promise((function(n,a){var s={value:a},l={value:function(){0==--o&&n()}};i.each((function(){var i=$t(this,r),o=i.on;o!==t&&((e=(t=o).copy())._.cancel.push(s),e._.interrupt.push(s),e._.end.push(l)),i.on=e})),0===o&&n()}))},[Symbol.iterator]:ge[Symbol.iterator]};var me={time:null,delay:0,duration:250,ease:function(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}};function ye(t,e){for(var i;!(i=t.__transition)||!(i=i[e]);)if(!(t=t.parentNode))throw new Error(`transition ${e} not found`);return i}Zt.prototype.interrupt=function(t){return this.each((function(){!function(t,e){var i,r,o,n=t.__transition,a=!0;if(n){for(o in e=null==e?null:e+"",n)(i=n[o]).name===e?(r=i.state>2&&i.state<5,i.state=6,i.timer.stop(),i.on.call(r?"interrupt":"cancel",t,t.__data__,i.index,i.group),delete n[o]):a=!1;a&&delete t.__transition}}(this,t)}))},Zt.prototype.transition=function(t){var e,i;t instanceof fe?(e=t._id,t=t._name):(e=pe(),(i=me).time=(0,Mt.zO)(),t=null==t?null:t+"");for(var r=this._groups,o=r.length,n=0;n<o;++n)for(var a,s=r[n],l=s.length,h=0;h<l;++h)(a=s[h])&&Nt(a,t,e,h,s,i||ye(a,e));return new fe(r,this._parents,t,e)};const{abs:Ce,max:be,min:xe}=Math;function ve(t){return{type:t}}function ke(t){if(!t.ok)throw new Error(t.status+" "+t.statusText);return t.text()}function Te(t){return(e,i)=>function(t,e){return fetch(t,e).then(ke)}(e,i).then((e=>(new DOMParser).parseFromString(e,t)))}["w","e"].map(ve),["n","s"].map(ve),["n","w","e","s","nw","ne","sw","se"].map(ve),Te("application/xml"),Te("text/html");var _e=Te("image/svg+xml"),Se=i(69004),Be=i(53353),Fe=i(42287),we=i(81406);function Ae(){var t,e,i=(0,we.Z)().unknown(void 0),r=i.domain,o=i.range,n=0,a=1,s=!1,l=0,h=0,c=.5;function u(){var i=r().length,u=a<n,d=u?a:n,f=u?n:a;t=(f-d)/Math.max(1,i-l+2*h),s&&(t=Math.floor(t)),d+=(f-d-t*(i-l))*c,e=t*(1-l),s&&(d=Math.round(d),e=Math.round(e));var p=(0,Be.Z)(i).map((function(e){return d+t*e}));return o(u?p.reverse():p)}return delete i.unknown,i.domain=function(t){return arguments.length?(r(t),u()):r()},i.range=function(t){return arguments.length?([n,a]=t,n=+n,a=+a,u()):[n,a]},i.rangeRound=function(t){return[n,a]=t,n=+n,a=+a,s=!0,u()},i.bandwidth=function(){return e},i.step=function(){return t},i.round=function(t){return arguments.length?(s=!!t,u()):s},i.padding=function(t){return arguments.length?(l=Math.min(1,h=+t),u()):l},i.paddingInner=function(t){return arguments.length?(l=Math.min(1,t),u()):l},i.paddingOuter=function(t){return arguments.length?(h=+t,u()):h},i.align=function(t){return arguments.length?(c=Math.max(0,Math.min(1,t)),u()):c},i.copy=function(){return Ae(r(),[n,a]).round(s).paddingInner(l).paddingOuter(h).align(c)},Fe.o.apply(u(),arguments)}var Le=i(29387),Ze=i(61941);const Ee=function(t){for(var e=new Array(10),i=0;i<10;)e[i]="#"+t.slice(6*i,6*++i);return e}("4e79a7f28e2ce1575976b7b259a14fedc949af7aa1ff9da79c755fbab0ab");function Me(t){return"string"==typeof t?new At([[document.querySelector(t)]],[document.documentElement]):new At([[t]],wt)}function Oe(t){return"string"==typeof t?new At([document.querySelectorAll(t)],[document.documentElement]):new At([v(t)],wt)}var Ie=i(87826),qe=i(40652),De=i(89555),ze=i(17728);function Ne(t,e){return e<t?-1:e>t?1:e>=t?0:NaN}function Pe(t){return t}var $e=i(90596);function je(){var t=Pe,e=Ne,i=null,r=(0,ze.Z)(0),o=(0,ze.Z)($e.BZ),n=(0,ze.Z)(0);function a(a){var s,l,h,c,u,d=(a=(0,De.Z)(a)).length,f=0,p=new Array(d),g=new Array(d),m=+r.apply(this,arguments),y=Math.min($e.BZ,Math.max(-$e.BZ,o.apply(this,arguments)-m)),C=Math.min(Math.abs(y)/d,n.apply(this,arguments)),b=C*(y<0?-1:1);for(s=0;s<d;++s)(u=g[p[s]=s]=+t(a[s],s,a))>0&&(f+=u);for(null!=e?p.sort((function(t,i){return e(g[t],g[i])})):null!=i&&p.sort((function(t,e){return i(a[t],a[e])})),s=0,h=f?(y-d*b)/f:0;s<d;++s,m=c)l=p[s],c=m+((u=g[l])>0?u*h:0)+b,g[l]={data:a[l],index:s,value:u,startAngle:m,endAngle:c,padAngle:C};return g}return a.value=function(e){return arguments.length?(t="function"==typeof e?e:(0,ze.Z)(+e),a):t},a.sortValues=function(t){return arguments.length?(e=t,i=null,a):e},a.sort=function(t){return arguments.length?(i=t,e=null,a):i},a.startAngle=function(t){return arguments.length?(r="function"==typeof t?t:(0,ze.Z)(+t),a):r},a.endAngle=function(t){return arguments.length?(o="function"==typeof t?t:(0,ze.Z)(+t),a):o},a.padAngle=function(t){return arguments.length?(n="function"==typeof t?t:(0,ze.Z)(+t),a):n},a}var Re=i(88973),We=i(74372),He=i(73021);class Ue{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function Ye(t){return new Ue(t,!0)}function Ve(t){return new Ue(t,!1)}var Ge=i(32830),Xe=i(91390),Je=i(41599),Qe=i(88800),Ke=i(7391),ti=i(63703),ei=i(48917),ii=i(29458),ri=i(4224),oi=i(78509),ni=i(3499),ai=i(21468),si=i(17540),li=i(58887),hi=i(5957),ci=i(75458),ui=i(6054),di=i(24645),fi=i(43155),pi=i(94031);function gi(t,e,i){this.k=t,this.x=e,this.y=i}gi.prototype={constructor:gi,scale:function(t){return 1===t?this:new gi(this.k*t,this.x,this.y)},translate:function(t,e){return 0===t&0===e?this:new gi(this.k,this.x+this.k*t,this.y+this.k*e)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}},new gi(1,0,0),gi.prototype},27693:function(t){t.exports=function(){"use strict";var t=6e4,e=36e5,i="millisecond",r="second",o="minute",n="hour",a="day",s="week",l="month",h="quarter",c="year",u="date",d="Invalid Date",f=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,p=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,g={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(t){var e=["th","st","nd","rd"],i=t%100;return"["+t+(e[(i-20)%10]||e[i]||e[0])+"]"}},m=function(t,e,i){var r=String(t);return!r||r.length>=e?t:""+Array(e+1-r.length).join(i)+t},y={s:m,z:function(t){var e=-t.utcOffset(),i=Math.abs(e),r=Math.floor(i/60),o=i%60;return(e<=0?"+":"-")+m(r,2,"0")+":"+m(o,2,"0")},m:function t(e,i){if(e.date()<i.date())return-t(i,e);var r=12*(i.year()-e.year())+(i.month()-e.month()),o=e.clone().add(r,l),n=i-o<0,a=e.clone().add(r+(n?-1:1),l);return+(-(r+(i-o)/(n?o-a:a-o))||0)},a:function(t){return t<0?Math.ceil(t)||0:Math.floor(t)},p:function(t){return{M:l,y:c,w:s,d:a,D:u,h:n,m:o,s:r,ms:i,Q:h}[t]||String(t||"").toLowerCase().replace(/s$/,"")},u:function(t){return void 0===t}},C="en",b={};b[C]=g;var x=function(t){return t instanceof _},v=function t(e,i,r){var o;if(!e)return C;if("string"==typeof e){var n=e.toLowerCase();b[n]&&(o=n),i&&(b[n]=i,o=n);var a=e.split("-");if(!o&&a.length>1)return t(a[0])}else{var s=e.name;b[s]=e,o=s}return!r&&o&&(C=o),o||!r&&C},k=function(t,e){if(x(t))return t.clone();var i="object"==typeof e?e:{};return i.date=t,i.args=arguments,new _(i)},T=y;T.l=v,T.i=x,T.w=function(t,e){return k(t,{locale:e.$L,utc:e.$u,x:e.$x,$offset:e.$offset})};var _=function(){function g(t){this.$L=v(t.locale,null,!0),this.parse(t)}var m=g.prototype;return m.parse=function(t){this.$d=function(t){var e=t.date,i=t.utc;if(null===e)return new Date(NaN);if(T.u(e))return new Date;if(e instanceof Date)return new Date(e);if("string"==typeof e&&!/Z$/i.test(e)){var r=e.match(f);if(r){var o=r[2]-1||0,n=(r[7]||"0").substring(0,3);return i?new Date(Date.UTC(r[1],o,r[3]||1,r[4]||0,r[5]||0,r[6]||0,n)):new Date(r[1],o,r[3]||1,r[4]||0,r[5]||0,r[6]||0,n)}}return new Date(e)}(t),this.$x=t.x||{},this.init()},m.init=function(){var t=this.$d;this.$y=t.getFullYear(),this.$M=t.getMonth(),this.$D=t.getDate(),this.$W=t.getDay(),this.$H=t.getHours(),this.$m=t.getMinutes(),this.$s=t.getSeconds(),this.$ms=t.getMilliseconds()},m.$utils=function(){return T},m.isValid=function(){return!(this.$d.toString()===d)},m.isSame=function(t,e){var i=k(t);return this.startOf(e)<=i&&i<=this.endOf(e)},m.isAfter=function(t,e){return k(t)<this.startOf(e)},m.isBefore=function(t,e){return this.endOf(e)<k(t)},m.$g=function(t,e,i){return T.u(t)?this[e]:this.set(i,t)},m.unix=function(){return Math.floor(this.valueOf()/1e3)},m.valueOf=function(){return this.$d.getTime()},m.startOf=function(t,e){var i=this,h=!!T.u(e)||e,d=T.p(t),f=function(t,e){var r=T.w(i.$u?Date.UTC(i.$y,e,t):new Date(i.$y,e,t),i);return h?r:r.endOf(a)},p=function(t,e){return T.w(i.toDate()[t].apply(i.toDate("s"),(h?[0,0,0,0]:[23,59,59,999]).slice(e)),i)},g=this.$W,m=this.$M,y=this.$D,C="set"+(this.$u?"UTC":"");switch(d){case c:return h?f(1,0):f(31,11);case l:return h?f(1,m):f(0,m+1);case s:var b=this.$locale().weekStart||0,x=(g<b?g+7:g)-b;return f(h?y-x:y+(6-x),m);case a:case u:return p(C+"Hours",0);case n:return p(C+"Minutes",1);case o:return p(C+"Seconds",2);case r:return p(C+"Milliseconds",3);default:return this.clone()}},m.endOf=function(t){return this.startOf(t,!1)},m.$set=function(t,e){var s,h=T.p(t),d="set"+(this.$u?"UTC":""),f=(s={},s[a]=d+"Date",s[u]=d+"Date",s[l]=d+"Month",s[c]=d+"FullYear",s[n]=d+"Hours",s[o]=d+"Minutes",s[r]=d+"Seconds",s[i]=d+"Milliseconds",s)[h],p=h===a?this.$D+(e-this.$W):e;if(h===l||h===c){var g=this.clone().set(u,1);g.$d[f](p),g.init(),this.$d=g.set(u,Math.min(this.$D,g.daysInMonth())).$d}else f&&this.$d[f](p);return this.init(),this},m.set=function(t,e){return this.clone().$set(t,e)},m.get=function(t){return this[T.p(t)]()},m.add=function(i,h){var u,d=this;i=Number(i);var f=T.p(h),p=function(t){var e=k(d);return T.w(e.date(e.date()+Math.round(t*i)),d)};if(f===l)return this.set(l,this.$M+i);if(f===c)return this.set(c,this.$y+i);if(f===a)return p(1);if(f===s)return p(7);var g=(u={},u[o]=t,u[n]=e,u[r]=1e3,u)[f]||1,m=this.$d.getTime()+i*g;return T.w(m,this)},m.subtract=function(t,e){return this.add(-1*t,e)},m.format=function(t){var e=this,i=this.$locale();if(!this.isValid())return i.invalidDate||d;var r=t||"YYYY-MM-DDTHH:mm:ssZ",o=T.z(this),n=this.$H,a=this.$m,s=this.$M,l=i.weekdays,h=i.months,c=function(t,i,o,n){return t&&(t[i]||t(e,r))||o[i].slice(0,n)},u=function(t){return T.s(n%12||12,t,"0")},f=i.meridiem||function(t,e,i){var r=t<12?"AM":"PM";return i?r.toLowerCase():r},g={YY:String(this.$y).slice(-2),YYYY:this.$y,M:s+1,MM:T.s(s+1,2,"0"),MMM:c(i.monthsShort,s,h,3),MMMM:c(h,s),D:this.$D,DD:T.s(this.$D,2,"0"),d:String(this.$W),dd:c(i.weekdaysMin,this.$W,l,2),ddd:c(i.weekdaysShort,this.$W,l,3),dddd:l[this.$W],H:String(n),HH:T.s(n,2,"0"),h:u(1),hh:u(2),a:f(n,a,!0),A:f(n,a,!1),m:String(a),mm:T.s(a,2,"0"),s:String(this.$s),ss:T.s(this.$s,2,"0"),SSS:T.s(this.$ms,3,"0"),Z:o};return r.replace(p,(function(t,e){return e||g[t]||o.replace(":","")}))},m.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},m.diff=function(i,u,d){var f,p=T.p(u),g=k(i),m=(g.utcOffset()-this.utcOffset())*t,y=this-g,C=T.m(this,g);return C=(f={},f[c]=C/12,f[l]=C,f[h]=C/3,f[s]=(y-m)/6048e5,f[a]=(y-m)/864e5,f[n]=y/e,f[o]=y/t,f[r]=y/1e3,f)[p]||y,d?C:T.a(C)},m.daysInMonth=function(){return this.endOf(l).$D},m.$locale=function(){return b[this.$L]},m.locale=function(t,e){if(!t)return this.$L;var i=this.clone(),r=v(t,e,!0);return r&&(i.$L=r),i},m.clone=function(){return T.w(this.$d,this)},m.toDate=function(){return new Date(this.valueOf())},m.toJSON=function(){return this.isValid()?this.toISOString():null},m.toISOString=function(){return this.$d.toISOString()},m.toString=function(){return this.$d.toUTCString()},g}(),S=_.prototype;return k.prototype=S,[["$ms",i],["$s",r],["$m",o],["$H",n],["$W",a],["$M",l],["$y",c],["$D",u]].forEach((function(t){S[t[1]]=function(e){return this.$g(e,t[0],t[1])}})),k.extend=function(t,e){return t.$i||(t(e,_,k),t.$i=!0),k},k.locale=v,k.isDayjs=x,k.unix=function(t){return k(1e3*t)},k.en=b[C],k.Ls=b,k.p={},k}()},31699:function(t){t.exports=function(){"use strict";const{entries:t,setPrototypeOf:e,isFrozen:i,getPrototypeOf:r,getOwnPropertyDescriptor:o}=Object;let{freeze:n,seal:a,create:s}=Object,{apply:l,construct:h}="undefined"!=typeof Reflect&&Reflect;n||(n=function(t){return t}),a||(a=function(t){return t}),l||(l=function(t,e,i){return t.apply(e,i)}),h||(h=function(t,e){return new t(...e)});const c=k(Array.prototype.forEach),u=k(Array.prototype.pop),d=k(Array.prototype.push),f=k(String.prototype.toLowerCase),p=k(String.prototype.toString),g=k(String.prototype.match),m=k(String.prototype.replace),y=k(String.prototype.indexOf),C=k(String.prototype.trim),b=k(RegExp.prototype.test),x=(v=TypeError,function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return h(v,e)});var v;function k(t){return function(e){for(var i=arguments.length,r=new Array(i>1?i-1:0),o=1;o<i;o++)r[o-1]=arguments[o];return l(t,e,r)}}function T(t,r){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:f;e&&e(t,null);let n=r.length;for(;n--;){let e=r[n];if("string"==typeof e){const t=o(e);t!==e&&(i(r)||(r[n]=t),e=t)}t[e]=!0}return t}function _(e){const i=s(null);for(const[r,n]of t(e))void 0!==o(e,r)&&(i[r]=n);return i}function S(t,e){for(;null!==t;){const i=o(t,e);if(i){if(i.get)return k(i.get);if("function"==typeof i.value)return k(i.value)}t=r(t)}return function(t){return console.warn("fallback value for",t),null}}const B=n(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),F=n(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),w=n(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),A=n(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),L=n(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Z=n(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),E=n(["#text"]),M=n(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),O=n(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),I=n(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),q=n(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),D=a(/\{\{[\w\W]*|[\w\W]*\}\}/gm),z=a(/<%[\w\W]*|[\w\W]*%>/gm),N=a(/\${[\w\W]*}/gm),P=a(/^data-[\-\w.\u00B7-\uFFFF]/),$=a(/^aria-[\-\w]+$/),j=a(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),R=a(/^(?:\w+script|data):/i),W=a(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),H=a(/^html$/i);var U=Object.freeze({__proto__:null,MUSTACHE_EXPR:D,ERB_EXPR:z,TMPLIT_EXPR:N,DATA_ATTR:P,ARIA_ATTR:$,IS_ALLOWED_URI:j,IS_SCRIPT_OR_DATA:R,ATTR_WHITESPACE:W,DOCTYPE_NAME:H});const Y=function(){return"undefined"==typeof window?null:window};return function e(){let i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Y();const r=t=>e(t);if(r.version="3.0.6",r.removed=[],!i||!i.document||9!==i.document.nodeType)return r.isSupported=!1,r;let{document:o}=i;const a=o,l=a.currentScript,{DocumentFragment:h,HTMLTemplateElement:v,Node:k,Element:D,NodeFilter:z,NamedNodeMap:N=i.NamedNodeMap||i.MozNamedAttrMap,HTMLFormElement:P,DOMParser:$,trustedTypes:R}=i,W=D.prototype,V=S(W,"cloneNode"),G=S(W,"nextSibling"),X=S(W,"childNodes"),J=S(W,"parentNode");if("function"==typeof v){const t=o.createElement("template");t.content&&t.content.ownerDocument&&(o=t.content.ownerDocument)}let Q,K="";const{implementation:tt,createNodeIterator:et,createDocumentFragment:it,getElementsByTagName:rt}=o,{importNode:ot}=a;let nt={};r.isSupported="function"==typeof t&&"function"==typeof J&&tt&&void 0!==tt.createHTMLDocument;const{MUSTACHE_EXPR:at,ERB_EXPR:st,TMPLIT_EXPR:lt,DATA_ATTR:ht,ARIA_ATTR:ct,IS_SCRIPT_OR_DATA:ut,ATTR_WHITESPACE:dt}=U;let{IS_ALLOWED_URI:ft}=U,pt=null;const gt=T({},[...B,...F,...w,...L,...E]);let mt=null;const yt=T({},[...M,...O,...I,...q]);let Ct=Object.seal(s(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),bt=null,xt=null,vt=!0,kt=!0,Tt=!1,_t=!0,St=!1,Bt=!1,Ft=!1,wt=!1,At=!1,Lt=!1,Zt=!1,Et=!0,Mt=!1,Ot=!0,It=!1,qt={},Dt=null;const zt=T({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Nt=null;const Pt=T({},["audio","video","img","source","image","track"]);let $t=null;const jt=T({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Rt="http://www.w3.org/1998/Math/MathML",Wt="http://www.w3.org/2000/svg",Ht="http://www.w3.org/1999/xhtml";let Ut=Ht,Yt=!1,Vt=null;const Gt=T({},[Rt,Wt,Ht],p);let Xt=null;const Jt=["application/xhtml+xml","text/html"];let Qt=null,Kt=null;const te=o.createElement("form"),ee=function(t){return t instanceof RegExp||t instanceof Function},ie=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!Kt||Kt!==t){if(t&&"object"==typeof t||(t={}),t=_(t),Xt=Xt=-1===Jt.indexOf(t.PARSER_MEDIA_TYPE)?"text/html":t.PARSER_MEDIA_TYPE,Qt="application/xhtml+xml"===Xt?p:f,pt="ALLOWED_TAGS"in t?T({},t.ALLOWED_TAGS,Qt):gt,mt="ALLOWED_ATTR"in t?T({},t.ALLOWED_ATTR,Qt):yt,Vt="ALLOWED_NAMESPACES"in t?T({},t.ALLOWED_NAMESPACES,p):Gt,$t="ADD_URI_SAFE_ATTR"in t?T(_(jt),t.ADD_URI_SAFE_ATTR,Qt):jt,Nt="ADD_DATA_URI_TAGS"in t?T(_(Pt),t.ADD_DATA_URI_TAGS,Qt):Pt,Dt="FORBID_CONTENTS"in t?T({},t.FORBID_CONTENTS,Qt):zt,bt="FORBID_TAGS"in t?T({},t.FORBID_TAGS,Qt):{},xt="FORBID_ATTR"in t?T({},t.FORBID_ATTR,Qt):{},qt="USE_PROFILES"in t&&t.USE_PROFILES,vt=!1!==t.ALLOW_ARIA_ATTR,kt=!1!==t.ALLOW_DATA_ATTR,Tt=t.ALLOW_UNKNOWN_PROTOCOLS||!1,_t=!1!==t.ALLOW_SELF_CLOSE_IN_ATTR,St=t.SAFE_FOR_TEMPLATES||!1,Bt=t.WHOLE_DOCUMENT||!1,At=t.RETURN_DOM||!1,Lt=t.RETURN_DOM_FRAGMENT||!1,Zt=t.RETURN_TRUSTED_TYPE||!1,wt=t.FORCE_BODY||!1,Et=!1!==t.SANITIZE_DOM,Mt=t.SANITIZE_NAMED_PROPS||!1,Ot=!1!==t.KEEP_CONTENT,It=t.IN_PLACE||!1,ft=t.ALLOWED_URI_REGEXP||j,Ut=t.NAMESPACE||Ht,Ct=t.CUSTOM_ELEMENT_HANDLING||{},t.CUSTOM_ELEMENT_HANDLING&&ee(t.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(Ct.tagNameCheck=t.CUSTOM_ELEMENT_HANDLING.tagNameCheck),t.CUSTOM_ELEMENT_HANDLING&&ee(t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(Ct.attributeNameCheck=t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),t.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(Ct.allowCustomizedBuiltInElements=t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),St&&(kt=!1),Lt&&(At=!0),qt&&(pt=T({},[...E]),mt=[],!0===qt.html&&(T(pt,B),T(mt,M)),!0===qt.svg&&(T(pt,F),T(mt,O),T(mt,q)),!0===qt.svgFilters&&(T(pt,w),T(mt,O),T(mt,q)),!0===qt.mathMl&&(T(pt,L),T(mt,I),T(mt,q))),t.ADD_TAGS&&(pt===gt&&(pt=_(pt)),T(pt,t.ADD_TAGS,Qt)),t.ADD_ATTR&&(mt===yt&&(mt=_(mt)),T(mt,t.ADD_ATTR,Qt)),t.ADD_URI_SAFE_ATTR&&T($t,t.ADD_URI_SAFE_ATTR,Qt),t.FORBID_CONTENTS&&(Dt===zt&&(Dt=_(Dt)),T(Dt,t.FORBID_CONTENTS,Qt)),Ot&&(pt["#text"]=!0),Bt&&T(pt,["html","head","body"]),pt.table&&(T(pt,["tbody"]),delete bt.tbody),t.TRUSTED_TYPES_POLICY){if("function"!=typeof t.TRUSTED_TYPES_POLICY.createHTML)throw x('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof t.TRUSTED_TYPES_POLICY.createScriptURL)throw x('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');Q=t.TRUSTED_TYPES_POLICY,K=Q.createHTML("")}else void 0===Q&&(Q=function(t,e){if("object"!=typeof t||"function"!=typeof t.createPolicy)return null;let i=null;const r="data-tt-policy-suffix";e&&e.hasAttribute(r)&&(i=e.getAttribute(r));const o="dompurify"+(i?"#"+i:"");try{return t.createPolicy(o,{createHTML:t=>t,createScriptURL:t=>t})}catch(t){return console.warn("TrustedTypes policy "+o+" could not be created."),null}}(R,l)),null!==Q&&"string"==typeof K&&(K=Q.createHTML(""));n&&n(t),Kt=t}},re=T({},["mi","mo","mn","ms","mtext"]),oe=T({},["foreignobject","desc","title","annotation-xml"]),ne=T({},["title","style","font","a","script"]),ae=T({},F);T(ae,w),T(ae,A);const se=T({},L);T(se,Z);const le=function(t){d(r.removed,{element:t});try{t.parentNode.removeChild(t)}catch(e){t.remove()}},he=function(t,e){try{d(r.removed,{attribute:e.getAttributeNode(t),from:e})}catch(t){d(r.removed,{attribute:null,from:e})}if(e.removeAttribute(t),"is"===t&&!mt[t])if(At||Lt)try{le(e)}catch(t){}else try{e.setAttribute(t,"")}catch(t){}},ce=function(t){let e=null,i=null;if(wt)t="<remove></remove>"+t;else{const e=g(t,/^[\r\n\t ]+/);i=e&&e[0]}"application/xhtml+xml"===Xt&&Ut===Ht&&(t='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+t+"</body></html>");const r=Q?Q.createHTML(t):t;if(Ut===Ht)try{e=(new $).parseFromString(r,Xt)}catch(t){}if(!e||!e.documentElement){e=tt.createDocument(Ut,"template",null);try{e.documentElement.innerHTML=Yt?K:r}catch(t){}}const n=e.body||e.documentElement;return t&&i&&n.insertBefore(o.createTextNode(i),n.childNodes[0]||null),Ut===Ht?rt.call(e,Bt?"html":"body")[0]:Bt?e.documentElement:n},ue=function(t){return et.call(t.ownerDocument||t,t,z.SHOW_ELEMENT|z.SHOW_COMMENT|z.SHOW_TEXT,null)},de=function(t){return"function"==typeof k&&t instanceof k},fe=function(t,e,i){nt[t]&&c(nt[t],(t=>{t.call(r,e,i,Kt)}))},pe=function(t){let e=null;if(fe("beforeSanitizeElements",t,null),(i=t)instanceof P&&("string"!=typeof i.nodeName||"string"!=typeof i.textContent||"function"!=typeof i.removeChild||!(i.attributes instanceof N)||"function"!=typeof i.removeAttribute||"function"!=typeof i.setAttribute||"string"!=typeof i.namespaceURI||"function"!=typeof i.insertBefore||"function"!=typeof i.hasChildNodes))return le(t),!0;var i;const o=Qt(t.nodeName);if(fe("uponSanitizeElement",t,{tagName:o,allowedTags:pt}),t.hasChildNodes()&&!de(t.firstElementChild)&&b(/<[/\w]/g,t.innerHTML)&&b(/<[/\w]/g,t.textContent))return le(t),!0;if(!pt[o]||bt[o]){if(!bt[o]&&me(o)){if(Ct.tagNameCheck instanceof RegExp&&b(Ct.tagNameCheck,o))return!1;if(Ct.tagNameCheck instanceof Function&&Ct.tagNameCheck(o))return!1}if(Ot&&!Dt[o]){const e=J(t)||t.parentNode,i=X(t)||t.childNodes;if(i&&e)for(let r=i.length-1;r>=0;--r)e.insertBefore(V(i[r],!0),G(t))}return le(t),!0}return t instanceof D&&!function(t){let e=J(t);e&&e.tagName||(e={namespaceURI:Ut,tagName:"template"});const i=f(t.tagName),r=f(e.tagName);return!!Vt[t.namespaceURI]&&(t.namespaceURI===Wt?e.namespaceURI===Ht?"svg"===i:e.namespaceURI===Rt?"svg"===i&&("annotation-xml"===r||re[r]):Boolean(ae[i]):t.namespaceURI===Rt?e.namespaceURI===Ht?"math"===i:e.namespaceURI===Wt?"math"===i&&oe[r]:Boolean(se[i]):t.namespaceURI===Ht?!(e.namespaceURI===Wt&&!oe[r])&&!(e.namespaceURI===Rt&&!re[r])&&!se[i]&&(ne[i]||!ae[i]):!("application/xhtml+xml"!==Xt||!Vt[t.namespaceURI]))}(t)?(le(t),!0):"noscript"!==o&&"noembed"!==o&&"noframes"!==o||!b(/<\/no(script|embed|frames)/i,t.innerHTML)?(St&&3===t.nodeType&&(e=t.textContent,c([at,st,lt],(t=>{e=m(e,t," ")})),t.textContent!==e&&(d(r.removed,{element:t.cloneNode()}),t.textContent=e)),fe("afterSanitizeElements",t,null),!1):(le(t),!0)},ge=function(t,e,i){if(Et&&("id"===e||"name"===e)&&(i in o||i in te))return!1;if(kt&&!xt[e]&&b(ht,e));else if(vt&&b(ct,e));else if(!mt[e]||xt[e]){if(!(me(t)&&(Ct.tagNameCheck instanceof RegExp&&b(Ct.tagNameCheck,t)||Ct.tagNameCheck instanceof Function&&Ct.tagNameCheck(t))&&(Ct.attributeNameCheck instanceof RegExp&&b(Ct.attributeNameCheck,e)||Ct.attributeNameCheck instanceof Function&&Ct.attributeNameCheck(e))||"is"===e&&Ct.allowCustomizedBuiltInElements&&(Ct.tagNameCheck instanceof RegExp&&b(Ct.tagNameCheck,i)||Ct.tagNameCheck instanceof Function&&Ct.tagNameCheck(i))))return!1}else if($t[e]);else if(b(ft,m(i,dt,"")));else if("src"!==e&&"xlink:href"!==e&&"href"!==e||"script"===t||0!==y(i,"data:")||!Nt[t])if(Tt&&!b(ut,m(i,dt,"")));else if(i)return!1;return!0},me=function(t){return t.indexOf("-")>0},ye=function(t){fe("beforeSanitizeAttributes",t,null);const{attributes:e}=t;if(!e)return;const i={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:mt};let o=e.length;for(;o--;){const n=e[o],{name:a,namespaceURI:s,value:l}=n,h=Qt(a);let d="value"===a?l:C(l);if(i.attrName=h,i.attrValue=d,i.keepAttr=!0,i.forceKeepAttr=void 0,fe("uponSanitizeAttribute",t,i),d=i.attrValue,i.forceKeepAttr)continue;if(he(a,t),!i.keepAttr)continue;if(!_t&&b(/\/>/i,d)){he(a,t);continue}St&&c([at,st,lt],(t=>{d=m(d,t," ")}));const f=Qt(t.nodeName);if(ge(f,h,d)){if(!Mt||"id"!==h&&"name"!==h||(he(a,t),d="user-content-"+d),Q&&"object"==typeof R&&"function"==typeof R.getAttributeType)if(s);else switch(R.getAttributeType(f,h)){case"TrustedHTML":d=Q.createHTML(d);break;case"TrustedScriptURL":d=Q.createScriptURL(d)}try{s?t.setAttributeNS(s,a,d):t.setAttribute(a,d),u(r.removed)}catch(t){}}}fe("afterSanitizeAttributes",t,null)},Ce=function t(e){let i=null;const r=ue(e);for(fe("beforeSanitizeShadowDOM",e,null);i=r.nextNode();)fe("uponSanitizeShadowNode",i,null),pe(i)||(i.content instanceof h&&t(i.content),ye(i));fe("afterSanitizeShadowDOM",e,null)};return r.sanitize=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=null,o=null,n=null,s=null;if(Yt=!t,Yt&&(t="\x3c!--\x3e"),"string"!=typeof t&&!de(t)){if("function"!=typeof t.toString)throw x("toString is not a function");if("string"!=typeof(t=t.toString()))throw x("dirty is not a string, aborting")}if(!r.isSupported)return t;if(Ft||ie(e),r.removed=[],"string"==typeof t&&(It=!1),It){if(t.nodeName){const e=Qt(t.nodeName);if(!pt[e]||bt[e])throw x("root node is forbidden and cannot be sanitized in-place")}}else if(t instanceof k)i=ce("\x3c!----\x3e"),o=i.ownerDocument.importNode(t,!0),1===o.nodeType&&"BODY"===o.nodeName||"HTML"===o.nodeName?i=o:i.appendChild(o);else{if(!At&&!St&&!Bt&&-1===t.indexOf("<"))return Q&&Zt?Q.createHTML(t):t;if(i=ce(t),!i)return At?null:Zt?K:""}i&&wt&&le(i.firstChild);const l=ue(It?t:i);for(;n=l.nextNode();)pe(n)||(n.content instanceof h&&Ce(n.content),ye(n));if(It)return t;if(At){if(Lt)for(s=it.call(i.ownerDocument);i.firstChild;)s.appendChild(i.firstChild);else s=i;return(mt.shadowroot||mt.shadowrootmode)&&(s=ot.call(a,s,!0)),s}let u=Bt?i.outerHTML:i.innerHTML;return Bt&&pt["!doctype"]&&i.ownerDocument&&i.ownerDocument.doctype&&i.ownerDocument.doctype.name&&b(H,i.ownerDocument.doctype.name)&&(u="<!DOCTYPE "+i.ownerDocument.doctype.name+">\n"+u),St&&c([at,st,lt],(t=>{u=m(u,t," ")})),Q&&Zt?Q.createHTML(u):u},r.setConfig=function(){ie(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),Ft=!0},r.clearConfig=function(){Kt=null,Ft=!1},r.isValidAttribute=function(t,e,i){Kt||ie({});const r=Qt(t),o=Qt(e);return ge(r,o,i)},r.addHook=function(t,e){"function"==typeof e&&(nt[t]=nt[t]||[],d(nt[t],e))},r.removeHook=function(t){if(nt[t])return u(nt[t])},r.removeHooks=function(t){nt[t]&&(nt[t]=[])},r.removeAllHooks=function(){nt={}},r}()}()},54758:(t,e,i)=>{"use strict";i.d(e,{Z:()=>a});var r=i(90267),o=i(62187);const n=class{constructor(){this.type=o.w.ALL}get(){return this.type}set(t){if(this.type&&this.type!==t)throw new Error("Cannot change both RGB and HSL channels at the same time");this.type=t}reset(){this.type=o.w.ALL}is(t){return this.type===t}},a=new class{constructor(t,e){this.color=e,this.changed=!1,this.data=t,this.type=new n}set(t,e){return this.color=e,this.changed=!1,this.data=t,this.type.type=o.w.ALL,this}_ensureHSL(){const t=this.data,{h:e,s:i,l:o}=t;void 0===e&&(t.h=r.Z.channel.rgb2hsl(t,"h")),void 0===i&&(t.s=r.Z.channel.rgb2hsl(t,"s")),void 0===o&&(t.l=r.Z.channel.rgb2hsl(t,"l"))}_ensureRGB(){const t=this.data,{r:e,g:i,b:o}=t;void 0===e&&(t.r=r.Z.channel.hsl2rgb(t,"r")),void 0===i&&(t.g=r.Z.channel.hsl2rgb(t,"g")),void 0===o&&(t.b=r.Z.channel.hsl2rgb(t,"b"))}get r(){const t=this.data,e=t.r;return this.type.is(o.w.HSL)||void 0===e?(this._ensureHSL(),r.Z.channel.hsl2rgb(t,"r")):e}get g(){const t=this.data,e=t.g;return this.type.is(o.w.HSL)||void 0===e?(this._ensureHSL(),r.Z.channel.hsl2rgb(t,"g")):e}get b(){const t=this.data,e=t.b;return this.type.is(o.w.HSL)||void 0===e?(this._ensureHSL(),r.Z.channel.hsl2rgb(t,"b")):e}get h(){const t=this.data,e=t.h;return this.type.is(o.w.RGB)||void 0===e?(this._ensureRGB(),r.Z.channel.rgb2hsl(t,"h")):e}get s(){const t=this.data,e=t.s;return this.type.is(o.w.RGB)||void 0===e?(this._ensureRGB(),r.Z.channel.rgb2hsl(t,"s")):e}get l(){const t=this.data,e=t.l;return this.type.is(o.w.RGB)||void 0===e?(this._ensureRGB(),r.Z.channel.rgb2hsl(t,"l")):e}get a(){return this.data.a}set r(t){this.type.set(o.w.RGB),this.changed=!0,this.data.r=t}set g(t){this.type.set(o.w.RGB),this.changed=!0,this.data.g=t}set b(t){this.type.set(o.w.RGB),this.changed=!0,this.data.b=t}set h(t){this.type.set(o.w.HSL),this.changed=!0,this.data.h=t}set s(t){this.type.set(o.w.HSL),this.changed=!0,this.data.s=t}set l(t){this.type.set(o.w.HSL),this.changed=!0,this.data.l=t}set a(t){this.changed=!0,this.data.a=t}}({r:0,g:0,b:0,a:0},"transparent")},42528:(t,e,i)=>{"use strict";i.d(e,{Z:()=>g});var r=i(54758),o=i(62187);const n={re:/^#((?:[a-f0-9]{2}){2,4}|[a-f0-9]{3})$/i,parse:t=>{if(35!==t.charCodeAt(0))return;const e=t.match(n.re);if(!e)return;const i=e[1],o=parseInt(i,16),a=i.length,s=a%4==0,l=a>4,h=l?1:17,c=l?8:4,u=s?0:-1,d=l?255:15;return r.Z.set({r:(o>>c*(u+3)&d)*h,g:(o>>c*(u+2)&d)*h,b:(o>>c*(u+1)&d)*h,a:s?(o&d)*h/255:1},t)},stringify:t=>{const{r:e,g:i,b:r,a:n}=t;return n<1?`#${o.Q[Math.round(e)]}${o.Q[Math.round(i)]}${o.Q[Math.round(r)]}${o.Q[Math.round(255*n)]}`:`#${o.Q[Math.round(e)]}${o.Q[Math.round(i)]}${o.Q[Math.round(r)]}`}},a=n;var s=i(90267);const l={re:/^hsla?\(\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?(?:deg|grad|rad|turn)?)\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?%)\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?%)(?:\s*?(?:,|\/)\s*?\+?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?(%)?))?\s*?\)$/i,hueRe:/^(.+?)(deg|grad|rad|turn)$/i,_hue2deg:t=>{const e=t.match(l.hueRe);if(e){const[,t,i]=e;switch(i){case"grad":return s.Z.channel.clamp.h(.9*parseFloat(t));case"rad":return s.Z.channel.clamp.h(180*parseFloat(t)/Math.PI);case"turn":return s.Z.channel.clamp.h(360*parseFloat(t))}}return s.Z.channel.clamp.h(parseFloat(t))},parse:t=>{const e=t.charCodeAt(0);if(104!==e&&72!==e)return;const i=t.match(l.re);if(!i)return;const[,o,n,a,h,c]=i;return r.Z.set({h:l._hue2deg(o),s:s.Z.channel.clamp.s(parseFloat(n)),l:s.Z.channel.clamp.l(parseFloat(a)),a:h?s.Z.channel.clamp.a(c?parseFloat(h)/100:parseFloat(h)):1},t)},stringify:t=>{const{h:e,s:i,l:r,a:o}=t;return o<1?`hsla(${s.Z.lang.round(e)}, ${s.Z.lang.round(i)}%, ${s.Z.lang.round(r)}%, ${o})`:`hsl(${s.Z.lang.round(e)}, ${s.Z.lang.round(i)}%, ${s.Z.lang.round(r)}%)`}},h=l,c={colors:{aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyanaqua:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",transparent:"#00000000",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"},parse:t=>{t=t.toLowerCase();const e=c.colors[t];if(e)return a.parse(e)},stringify:t=>{const e=a.stringify(t);for(const t in c.colors)if(c.colors[t]===e)return t}},u=c,d={re:/^rgba?\(\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?))\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?))\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?))(?:\s*?(?:,|\/)\s*?\+?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?)))?\s*?\)$/i,parse:t=>{const e=t.charCodeAt(0);if(114!==e&&82!==e)return;const i=t.match(d.re);if(!i)return;const[,o,n,a,l,h,c,u,f]=i;return r.Z.set({r:s.Z.channel.clamp.r(n?2.55*parseFloat(o):parseFloat(o)),g:s.Z.channel.clamp.g(l?2.55*parseFloat(a):parseFloat(a)),b:s.Z.channel.clamp.b(c?2.55*parseFloat(h):parseFloat(h)),a:u?s.Z.channel.clamp.a(f?parseFloat(u)/100:parseFloat(u)):1},t)},stringify:t=>{const{r:e,g:i,b:r,a:o}=t;return o<1?`rgba(${s.Z.lang.round(e)}, ${s.Z.lang.round(i)}, ${s.Z.lang.round(r)}, ${s.Z.lang.round(o)})`:`rgb(${s.Z.lang.round(e)}, ${s.Z.lang.round(i)}, ${s.Z.lang.round(r)})`}},f=d,p={format:{keyword:c,hex:a,rgb:d,rgba:d,hsl:l,hsla:l},parse:t=>{if("string"!=typeof t)return t;const e=a.parse(t)||f.parse(t)||h.parse(t)||u.parse(t);if(e)return e;throw new Error(`Unsupported color format: "${t}"`)},stringify:t=>!t.changed&&t.color?t.color:t.type.is(o.w.HSL)||void 0===t.data.r?h.stringify(t):t.a<1||!Number.isInteger(t.r)||!Number.isInteger(t.g)||!Number.isInteger(t.b)?f.stringify(t):a.stringify(t)},g=p},62187:(t,e,i)=>{"use strict";i.d(e,{Q:()=>o,w:()=>n});var r=i(90267);const o={};for(let t=0;t<=255;t++)o[t]=r.Z.unit.dec2hex(t);const n={ALL:0,RGB:1,HSL:2}},60680:(t,e,i)=>{"use strict";i.d(e,{Z:()=>n});var r=i(90267),o=i(42528);const n=(t,e,i)=>{const n=o.Z.parse(t),a=n[e],s=r.Z.channel.clamp[e](a+i);return a!==s&&(n[e]=s),o.Z.stringify(n)}},95323:(t,e,i)=>{"use strict";i.d(e,{Z:()=>n});var r=i(90267),o=i(42528);const n=(t,e)=>{const i=o.Z.parse(t);for(const t in e)i[t]=r.Z.channel.clamp[t](e[t]);return o.Z.stringify(i)}},57838:(t,e,i)=>{"use strict";i.d(e,{Z:()=>o});var r=i(60680);const o=(t,e)=>(0,r.Z)(t,"l",-e)},28186:(t,e,i)=>{"use strict";i.d(e,{Z:()=>a});var r=i(90267),o=i(42528);const n=t=>(t=>{const{r:e,g:i,b:n}=o.Z.parse(t),a=.2126*r.Z.channel.toLinear(e)+.7152*r.Z.channel.toLinear(i)+.0722*r.Z.channel.toLinear(n);return r.Z.lang.round(a)})(t)>=.5,a=t=>!n(t)},28482:(t,e,i)=>{"use strict";i.d(e,{Z:()=>o});var r=i(60680);const o=(t,e)=>(0,r.Z)(t,"l",e)},14728:(t,e,i)=>{"use strict";i.d(e,{Z:()=>s});var r=i(90267),o=i(54758),n=i(42528),a=i(95323);const s=(t,e,i=0,s=1)=>{if("number"!=typeof t)return(0,a.Z)(t,{a:e});const l=o.Z.set({r:r.Z.channel.clamp.r(t),g:r.Z.channel.clamp.g(e),b:r.Z.channel.clamp.b(i),a:r.Z.channel.clamp.a(s)});return n.Z.stringify(l)}},90267:(t,e,i)=>{"use strict";i.d(e,{Z:()=>o});const r={min:{r:0,g:0,b:0,s:0,l:0,a:0},max:{r:255,g:255,b:255,h:360,s:100,l:100,a:1},clamp:{r:t=>t>=255?255:t<0?0:t,g:t=>t>=255?255:t<0?0:t,b:t=>t>=255?255:t<0?0:t,h:t=>t%360,s:t=>t>=100?100:t<0?0:t,l:t=>t>=100?100:t<0?0:t,a:t=>t>=1?1:t<0?0:t},toLinear:t=>{const e=t/255;return t>.03928?Math.pow((e+.055)/1.055,2.4):e/12.92},hue2rgb:(t,e,i)=>(i<0&&(i+=1),i>1&&(i-=1),i<1/6?t+6*(e-t)*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t),hsl2rgb:({h:t,s:e,l:i},o)=>{if(!e)return 2.55*i;t/=360,e/=100;const n=(i/=100)<.5?i*(1+e):i+e-i*e,a=2*i-n;switch(o){case"r":return 255*r.hue2rgb(a,n,t+1/3);case"g":return 255*r.hue2rgb(a,n,t);case"b":return 255*r.hue2rgb(a,n,t-1/3)}},rgb2hsl:({r:t,g:e,b:i},r)=>{t/=255,e/=255,i/=255;const o=Math.max(t,e,i),n=Math.min(t,e,i),a=(o+n)/2;if("l"===r)return 100*a;if(o===n)return 0;const s=o-n;if("s"===r)return 100*(a>.5?s/(2-o-n):s/(o+n));switch(o){case t:return 60*((e-i)/s+(e<i?6:0));case e:return 60*((i-t)/s+2);case i:return 60*((t-e)/s+4);default:return-1}}},o={channel:r,lang:{clamp:(t,e,i)=>e>i?Math.min(e,Math.max(i,t)):Math.min(i,Math.max(e,t)),round:t=>Math.round(1e10*t)/1e10},unit:{dec2hex:t=>{const e=Math.round(t).toString(16);return e.length>1?e:`0${e}`}}}},91138:(t,e,i)=>{"use strict";i.d(e,{Z:()=>s});var r=i(35050);const o=function(t,e){for(var i=t.length;i--;)if((0,r.Z)(t[i][0],e))return i;return-1};var n=Array.prototype.splice;function a(t){var e=-1,i=null==t?0:t.length;for(this.clear();++e<i;){var r=t[e];this.set(r[0],r[1])}}a.prototype.clear=function(){this.__data__=[],this.size=0},a.prototype.delete=function(t){var e=this.__data__,i=o(e,t);return!(i<0||(i==e.length-1?e.pop():n.call(e,i,1),--this.size,0))},a.prototype.get=function(t){var e=this.__data__,i=o(e,t);return i<0?void 0:e[i][1]},a.prototype.has=function(t){return o(this.__data__,t)>-1},a.prototype.set=function(t,e){var i=this.__data__,r=o(i,t);return r<0?(++this.size,i.push([t,e])):i[r][1]=e,this};const s=a},81700:(t,e,i)=>{"use strict";i.d(e,{Z:()=>n});var r=i(26266),o=i(94311);const n=(0,r.Z)(o.Z,"Map")},24395:(t,e,i)=>{"use strict";i.d(e,{Z:()=>d});const r=(0,i(26266).Z)(Object,"create");var o=Object.prototype.hasOwnProperty;var n=Object.prototype.hasOwnProperty;function a(t){var e=-1,i=null==t?0:t.length;for(this.clear();++e<i;){var r=t[e];this.set(r[0],r[1])}}a.prototype.clear=function(){this.__data__=r?r(null):{},this.size=0},a.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},a.prototype.get=function(t){var e=this.__data__;if(r){var i=e[t];return"__lodash_hash_undefined__"===i?void 0:i}return o.call(e,t)?e[t]:void 0},a.prototype.has=function(t){var e=this.__data__;return r?void 0!==e[t]:n.call(e,t)},a.prototype.set=function(t,e){var i=this.__data__;return this.size+=this.has(t)?0:1,i[t]=r&&void 0===e?"__lodash_hash_undefined__":e,this};const s=a;var l=i(91138),h=i(81700);const c=function(t,e){var i,r,o=t.__data__;return("string"==(r=typeof(i=e))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==i:null===i)?o["string"==typeof e?"string":"hash"]:o.map};function u(t){var e=-1,i=null==t?0:t.length;for(this.clear();++e<i;){var r=t[e];this.set(r[0],r[1])}}u.prototype.clear=function(){this.size=0,this.__data__={hash:new s,map:new(h.Z||l.Z),string:new s}},u.prototype.delete=function(t){var e=c(this,t).delete(t);return this.size-=e?1:0,e},u.prototype.get=function(t){return c(this,t).get(t)},u.prototype.has=function(t){return c(this,t).has(t)},u.prototype.set=function(t,e){var i=c(this,t),r=i.size;return i.set(t,e),this.size+=i.size==r?0:1,this};const d=u},16889:(t,e,i)=>{"use strict";i.d(e,{Z:()=>n});var r=i(26266),o=i(94311);const n=(0,r.Z)(o.Z,"Set")},82948:(t,e,i)=>{"use strict";i.d(e,{Z:()=>s});var r=i(91138);var o=i(81700),n=i(24395);function a(t){var e=this.__data__=new r.Z(t);this.size=e.size}a.prototype.clear=function(){this.__data__=new r.Z,this.size=0},a.prototype.delete=function(t){var e=this.__data__,i=e.delete(t);return this.size=e.size,i},a.prototype.get=function(t){return this.__data__.get(t)},a.prototype.has=function(t){return this.__data__.has(t)},a.prototype.set=function(t,e){var i=this.__data__;if(i instanceof r.Z){var a=i.__data__;if(!o.Z||a.length<199)return a.push([t,e]),this.size=++i.size,this;i=this.__data__=new n.Z(a)}return i.set(t,e),this.size=i.size,this};const s=a},91642:(t,e,i)=>{"use strict";i.d(e,{Z:()=>r});const r=i(94311).Z.Symbol},41049:(t,e,i)=>{"use strict";i.d(e,{Z:()=>r});const r=i(94311).Z.Uint8Array},40709:(t,e,i)=>{"use strict";i.d(e,{Z:()=>h});var r=i(9028),o=i(64058),n=i(23230),a=i(8616),s=i(14923),l=Object.prototype.hasOwnProperty;const h=function(t,e){var i=(0,o.Z)(t),h=!i&&(0,r.Z)(t),c=!i&&!h&&(0,n.Z)(t),u=!i&&!h&&!c&&(0,s.Z)(t),d=i||h||c||u,f=d?function(t,e){for(var i=-1,r=Array(t);++i<t;)r[i]=e(i);return r}(t.length,String):[],p=f.length;for(var g in t)!e&&!l.call(t,g)||d&&("length"==g||c&&("offset"==g||"parent"==g)||u&&("buffer"==g||"byteLength"==g||"byteOffset"==g)||(0,a.Z)(g,p))||f.push(g);return f}},15561:(t,e,i)=>{"use strict";i.d(e,{Z:()=>a});var r=i(93586),o=i(35050),n=Object.prototype.hasOwnProperty;const a=function(t,e,i){var a=t[e];n.call(t,e)&&(0,o.Z)(a,i)&&(void 0!==i||e in t)||(0,r.Z)(t,e,i)}},93586:(t,e,i)=>{"use strict";i.d(e,{Z:()=>o});var r=i(30253);const o=function(t,e,i){"__proto__"==e&&r.Z?(0,r.Z)(t,e,{configurable:!0,enumerable:!0,value:i,writable:!0}):t[e]=i}},49399:(t,e,i)=>{"use strict";i.d(e,{Z:()=>r});const r=function(t,e,i){for(var r=-1,o=Object(t),n=i(t),a=n.length;a--;){var s=n[++r];if(!1===e(o[s],s,o))break}return t}},77070:(t,e,i)=>{"use strict";i.d(e,{Z:()=>c});var r=i(91642),o=Object.prototype,n=o.hasOwnProperty,a=o.toString,s=r.Z?r.Z.toStringTag:void 0;var l=Object.prototype.toString;var h=r.Z?r.Z.toStringTag:void 0;const c=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":h&&h in Object(t)?function(t){var e=n.call(t,s),i=t[s];try{t[s]=void 0;var r=!0}catch(t){}var o=a.call(t);return r&&(e?t[s]=i:delete t[s]),o}(t):function(t){return l.call(t)}(t)}},45934:(t,e,i)=>{"use strict";i.d(e,{Z:()=>a});var r=i(89418);const o=(0,i(4883).Z)(Object.keys,Object);var n=Object.prototype.hasOwnProperty;const a=function(t){if(!(0,r.Z)(t))return o(t);var e=[];for(var i in Object(t))n.call(t,i)&&"constructor"!=i&&e.push(i);return e}},99719:(t,e,i)=>{"use strict";i.d(e,{Z:()=>a});var r=i(64056),o=i(15829),n=i(71649);const a=function(t,e){return(0,n.Z)((0,o.Z)(t,e,r.Z),t+"")}},20274:(t,e,i)=>{"use strict";i.d(e,{Z:()=>r});const r=function(t){return function(e){return t(e)}}},52049:(t,e,i)=>{"use strict";i.d(e,{Z:()=>o});var r=i(41049);const o=function(t){var e=new t.constructor(t.byteLength);return new r.Z(e).set(new r.Z(t)),e}},64405:(t,e,i)=>{"use strict";i.d(e,{Z:()=>l});var r=i(94311);t=i.hmd(t);var o="object"==typeof exports&&exports&&!exports.nodeType&&exports,n=o&&t&&!t.nodeType&&t,a=n&&n.exports===o?r.Z.Buffer:void 0,s=a?a.allocUnsafe:void 0;const l=function(t,e){if(e)return t.slice();var i=t.length,r=s?s(i):new t.constructor(i);return t.copy(r),r}},61601:(t,e,i)=>{"use strict";i.d(e,{Z:()=>o});var r=i(52049);const o=function(t,e){var i=e?(0,r.Z)(t.buffer):t.buffer;return new t.constructor(i,t.byteOffset,t.length)}},93580:(t,e,i)=>{"use strict";i.d(e,{Z:()=>r});const r=function(t,e){var i=-1,r=t.length;for(e||(e=Array(r));++i<r;)e[i]=t[i];return e}},47313:(t,e,i)=>{"use strict";i.d(e,{Z:()=>n});var r=i(15561),o=i(93586);const n=function(t,e,i,n){var a=!i;i||(i={});for(var s=-1,l=e.length;++s<l;){var h=e[s],c=n?n(i[h],t[h],h,i,t):void 0;void 0===c&&(c=t[h]),a?(0,o.Z)(i,h,c):(0,r.Z)(i,h,c)}return i}},30253:(t,e,i)=>{"use strict";i.d(e,{Z:()=>o});var r=i(26266);const o=function(){try{var t=(0,r.Z)(Object,"defineProperty");return t({},"",{}),t}catch(t){}}()},89268:(t,e,i)=>{"use strict";i.d(e,{Z:()=>r});const r="object"==typeof i.g&&i.g&&i.g.Object===Object&&i.g},26266:(t,e,i)=>{"use strict";i.d(e,{Z:()=>m});var r=i(48489);const o=i(94311).Z["__core-js_shared__"];var n,a=(n=/[^.]+$/.exec(o&&o.keys&&o.keys.IE_PROTO||""))?"Symbol(src)_1."+n:"";var s=i(60417),l=i(62392),h=/^\[object .+?Constructor\]$/,c=Function.prototype,u=Object.prototype,d=c.toString,f=u.hasOwnProperty,p=RegExp("^"+d.call(f).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");const g=function(t){return!(!(0,s.Z)(t)||(e=t,a&&a in e))&&((0,r.Z)(t)?p:h).test((0,l.Z)(t));var e},m=function(t,e){var i=function(t,e){return null==t?void 0:t[e]}(t,e);return g(i)?i:void 0}},72784:(t,e,i)=>{"use strict";i.d(e,{Z:()=>r});const r=(0,i(4883).Z)(Object.getPrototypeOf,Object)},41182:(t,e,i)=>{"use strict";i.d(e,{Z:()=>T});var r=i(26266),o=i(94311);const n=(0,r.Z)(o.Z,"DataView");var a=i(81700);const s=(0,r.Z)(o.Z,"Promise");var l=i(16889);const h=(0,r.Z)(o.Z,"WeakMap");var c=i(77070),u=i(62392),d="[object Map]",f="[object Promise]",p="[object Set]",g="[object WeakMap]",m="[object DataView]",y=(0,u.Z)(n),C=(0,u.Z)(a.Z),b=(0,u.Z)(s),x=(0,u.Z)(l.Z),v=(0,u.Z)(h),k=c.Z;(n&&k(new n(new ArrayBuffer(1)))!=m||a.Z&&k(new a.Z)!=d||s&&k(s.resolve())!=f||l.Z&&k(new l.Z)!=p||h&&k(new h)!=g)&&(k=function(t){var e=(0,c.Z)(t),i="[object Object]"==e?t.constructor:void 0,r=i?(0,u.Z)(i):"";if(r)switch(r){case y:return m;case C:return d;case b:return f;case x:return p;case v:return g}return e});const T=k},95764:(t,e,i)=>{"use strict";i.d(e,{Z:()=>l});var r=i(60417),o=Object.create;const n=function(){function t(){}return function(e){if(!(0,r.Z)(e))return{};if(o)return o(e);t.prototype=e;var i=new t;return t.prototype=void 0,i}}();var a=i(72784),s=i(89418);const l=function(t){return"function"!=typeof t.constructor||(0,s.Z)(t)?{}:n((0,a.Z)(t))}},8616:(t,e,i)=>{"use strict";i.d(e,{Z:()=>o});var r=/^(?:0|[1-9]\d*)$/;const o=function(t,e){var i=typeof t;return!!(e=null==e?9007199254740991:e)&&("number"==i||"symbol"!=i&&r.test(t))&&t>-1&&t%1==0&&t<e}},47952:(t,e,i)=>{"use strict";i.d(e,{Z:()=>s});var r=i(35050),o=i(69959),n=i(8616),a=i(60417);const s=function(t,e,i){if(!(0,a.Z)(i))return!1;var s=typeof e;return!!("number"==s?(0,o.Z)(i)&&(0,n.Z)(e,i.length):"string"==s&&e in i)&&(0,r.Z)(i[e],t)}},89418:(t,e,i)=>{"use strict";i.d(e,{Z:()=>o});var r=Object.prototype;const o=function(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||r)}},53594:(t,e,i)=>{"use strict";i.d(e,{Z:()=>s});var r=i(89268);t=i.hmd(t);var o="object"==typeof exports&&exports&&!exports.nodeType&&exports,n=o&&t&&!t.nodeType&&t,a=n&&n.exports===o&&r.Z.process;const s=function(){try{return n&&n.require&&n.require("util").types||a&&a.binding&&a.binding("util")}catch(t){}}()},4883:(t,e,i)=>{"use strict";i.d(e,{Z:()=>r});const r=function(t,e){return function(i){return t(e(i))}}},15829:(t,e,i)=>{"use strict";i.d(e,{Z:()=>o});var r=Math.max;const o=function(t,e,i){return e=r(void 0===e?t.length-1:e,0),function(){for(var o=arguments,n=-1,a=r(o.length-e,0),s=Array(a);++n<a;)s[n]=o[e+n];n=-1;for(var l=Array(e+1);++n<e;)l[n]=o[n];return l[e]=i(s),function(t,e,i){switch(i.length){case 0:return t.call(e);case 1:return t.call(e,i[0]);case 2:return t.call(e,i[0],i[1]);case 3:return t.call(e,i[0],i[1],i[2])}return t.apply(e,i)}(t,this,l)}}},94311:(t,e,i)=>{"use strict";i.d(e,{Z:()=>n});var r=i(89268),o="object"==typeof self&&self&&self.Object===Object&&self;const n=r.Z||o||Function("return this")()},71649:(t,e,i)=>{"use strict";i.d(e,{Z:()=>l});var r=i(78795),o=i(30253),n=i(64056);const a=o.Z?function(t,e){return(0,o.Z)(t,"toString",{configurable:!0,enumerable:!1,value:(0,r.Z)(e),writable:!0})}:n.Z;var s=Date.now;const l=(h=a,c=0,u=0,function(){var t=s(),e=16-(t-u);if(u=t,e>0){if(++c>=800)return arguments[0]}else c=0;return h.apply(void 0,arguments)});var h,c,u},62392:(t,e,i)=>{"use strict";i.d(e,{Z:()=>o});var r=Function.prototype.toString;const o=function(t){if(null!=t){try{return r.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},78795:(t,e,i)=>{"use strict";i.d(e,{Z:()=>r});const r=function(t){return function(){return t}}},35050:(t,e,i)=>{"use strict";i.d(e,{Z:()=>r});const r=function(t,e){return t===e||t!=t&&e!=e}},64056:(t,e,i)=>{"use strict";i.d(e,{Z:()=>r});const r=function(t){return t}},9028:(t,e,i)=>{"use strict";i.d(e,{Z:()=>h});var r=i(77070),o=i(9615);const n=function(t){return(0,o.Z)(t)&&"[object Arguments]"==(0,r.Z)(t)};var a=Object.prototype,s=a.hasOwnProperty,l=a.propertyIsEnumerable;const h=n(function(){return arguments}())?n:function(t){return(0,o.Z)(t)&&s.call(t,"callee")&&!l.call(t,"callee")}},64058:(t,e,i)=>{"use strict";i.d(e,{Z:()=>r});const r=Array.isArray},69959:(t,e,i)=>{"use strict";i.d(e,{Z:()=>n});var r=i(48489),o=i(30918);const n=function(t){return null!=t&&(0,o.Z)(t.length)&&!(0,r.Z)(t)}},60492:(t,e,i)=>{"use strict";i.d(e,{Z:()=>n});var r=i(69959),o=i(9615);const n=function(t){return(0,o.Z)(t)&&(0,r.Z)(t)}},23230:(t,e,i)=>{"use strict";i.d(e,{Z:()=>s});var r=i(94311);t=i.hmd(t);var o="object"==typeof exports&&exports&&!exports.nodeType&&exports,n=o&&t&&!t.nodeType&&t,a=n&&n.exports===o?r.Z.Buffer:void 0;const s=(a?a.isBuffer:void 0)||function(){return!1}},66400:(t,e,i)=>{"use strict";i.d(e,{Z:()=>d});var r=i(45934),o=i(41182),n=i(9028),a=i(64058),s=i(69959),l=i(23230),h=i(89418),c=i(14923),u=Object.prototype.hasOwnProperty;const d=function(t){if(null==t)return!0;if((0,s.Z)(t)&&((0,a.Z)(t)||"string"==typeof t||"function"==typeof t.splice||(0,l.Z)(t)||(0,c.Z)(t)||(0,n.Z)(t)))return!t.length;var e=(0,o.Z)(t);if("[object Map]"==e||"[object Set]"==e)return!t.size;if((0,h.Z)(t))return!(0,r.Z)(t).length;for(var i in t)if(u.call(t,i))return!1;return!0}},48489:(t,e,i)=>{"use strict";i.d(e,{Z:()=>n});var r=i(77070),o=i(60417);const n=function(t){if(!(0,o.Z)(t))return!1;var e=(0,r.Z)(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},30918:(t,e,i)=>{"use strict";i.d(e,{Z:()=>r});const r=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}},60417:(t,e,i)=>{"use strict";i.d(e,{Z:()=>r});const r=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},9615:(t,e,i)=>{"use strict";i.d(e,{Z:()=>r});const r=function(t){return null!=t&&"object"==typeof t}},53541:(t,e,i)=>{"use strict";i.d(e,{Z:()=>u});var r=i(77070),o=i(72784),n=i(9615),a=Function.prototype,s=Object.prototype,l=a.toString,h=s.hasOwnProperty,c=l.call(Object);const u=function(t){if(!(0,n.Z)(t)||"[object Object]"!=(0,r.Z)(t))return!1;var e=(0,o.Z)(t);if(null===e)return!0;var i=h.call(e,"constructor")&&e.constructor;return"function"==typeof i&&i instanceof i&&l.call(i)==c}},14923:(t,e,i)=>{"use strict";i.d(e,{Z:()=>c});var r=i(77070),o=i(30918),n=i(9615),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1;var s=i(20274),l=i(53594),h=l.Z&&l.Z.isTypedArray;const c=h?(0,s.Z)(h):function(t){return(0,n.Z)(t)&&(0,o.Z)(t.length)&&!!a[(0,r.Z)(t)]}},48441:(t,e,i)=>{"use strict";i.d(e,{Z:()=>h});var r=i(40709),o=i(60417),n=i(89418);var a=Object.prototype.hasOwnProperty;const s=function(t){if(!(0,o.Z)(t))return function(t){var e=[];if(null!=t)for(var i in Object(t))e.push(i);return e}(t);var e=(0,n.Z)(t),i=[];for(var r in t)("constructor"!=r||!e&&a.call(t,r))&&i.push(r);return i};var l=i(69959);const h=function(t){return(0,l.Z)(t)?(0,r.Z)(t,!0):s(t)}},86861:(t,e,i)=>{"use strict";i.d(e,{Z:()=>n});var r=i(24395);function o(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw new TypeError("Expected a function");var i=function(){var r=arguments,o=e?e.apply(this,r):r[0],n=i.cache;if(n.has(o))return n.get(o);var a=t.apply(this,r);return i.cache=n.set(o,a)||n,a};return i.cache=new(o.Cache||r.Z),i}o.Cache=r.Z;const n=o},18802:(t,e,i)=>{"use strict";i.d(e,{Z:()=>F});var r=i(82948),o=i(93586),n=i(35050);const a=function(t,e,i){(void 0!==i&&!(0,n.Z)(t[e],i)||void 0===i&&!(e in t))&&(0,o.Z)(t,e,i)};var s=i(49399),l=i(64405),h=i(61601),c=i(93580),u=i(95764),d=i(9028),f=i(64058),p=i(60492),g=i(23230),m=i(48489),y=i(60417),C=i(53541),b=i(14923);const x=function(t,e){if(("constructor"!==e||"function"!=typeof t[e])&&"__proto__"!=e)return t[e]};var v=i(47313),k=i(48441);const T=function(t,e,i,r,o,n,s){var T,_=x(t,i),S=x(e,i),B=s.get(S);if(B)a(t,i,B);else{var F=n?n(_,S,i+"",t,e,s):void 0,w=void 0===F;if(w){var A=(0,f.Z)(S),L=!A&&(0,g.Z)(S),Z=!A&&!L&&(0,b.Z)(S);F=S,A||L||Z?(0,f.Z)(_)?F=_:(0,p.Z)(_)?F=(0,c.Z)(_):L?(w=!1,F=(0,l.Z)(S,!0)):Z?(w=!1,F=(0,h.Z)(S,!0)):F=[]:(0,C.Z)(S)||(0,d.Z)(S)?(F=_,(0,d.Z)(_)?(T=_,F=(0,v.Z)(T,(0,k.Z)(T))):(0,y.Z)(_)&&!(0,m.Z)(_)||(F=(0,u.Z)(S))):w=!1}w&&(s.set(S,F),o(F,S,r,n,s),s.delete(S)),a(t,i,F)}},_=function t(e,i,o,n,l){e!==i&&(0,s.Z)(i,(function(s,h){if(l||(l=new r.Z),(0,y.Z)(s))T(e,i,h,o,t,n,l);else{var c=n?n(x(e,h),s,h+"",e,i,l):void 0;void 0===c&&(c=s),a(e,h,c)}}),k.Z)};var S=i(99719),B=i(47952);const F=(w=function(t,e,i){_(t,e,i)},(0,S.Z)((function(t,e){var i=-1,r=e.length,o=r>1?e[r-1]:void 0,n=r>2?e[2]:void 0;for(o=w.length>3&&"function"==typeof o?(r--,o):void 0,n&&(0,B.Z)(e[0],e[1],n)&&(o=r<3?void 0:o,r=1),t=Object(t);++i<r;){var a=e[i];a&&w(t,a,i)}return t})));var w},24028:(t,e,i)=>{"use strict";i.d(e,{A:()=>It,B:()=>ge,C:()=>pe,D:()=>Ft,E:()=>Fe,F:()=>Qi,G:()=>oe,H:()=>ht,I:()=>Ai,J:()=>ye,K:()=>_i,L:()=>Jo,Z:()=>Vt,a:()=>vi,b:()=>xi,c:()=>Fi,d:()=>dt,e:()=>xt,f:()=>Yt,g:()=>bi,h:()=>ce,i:()=>ci,j:()=>he,k:()=>ie,l:()=>at,m:()=>gt,n:()=>Qt,o:()=>ui,p:()=>wi,q:()=>ki,r:()=>Ti,s:()=>Ci,t:()=>yi,u:()=>me,v:()=>mt,w:()=>se,x:()=>ne,y:()=>Zi,z:()=>qi});var r=i(11464),o=i(27693),n=i.n(o),a=i(7608),s=i(23617),l=i(31699),h=i.n(l),c=i(42528),u=i(95323);const d=(t,e)=>{const i=c.Z.parse(t),r={};for(const t in e)e[t]&&(r[t]=i[t]+e[t]);return(0,u.Z)(t,r)};var f=i(14728);const p=(t,e=100)=>{const i=c.Z.parse(t);return i.r=255-i.r,i.g=255-i.g,i.b=255-i.b,((t,e,i=50)=>{const{r,g:o,b:n,a}=c.Z.parse(t),{r:s,g:l,b:h,a:u}=c.Z.parse(e),d=i/100,p=2*d-1,g=a-u,m=((p*g==-1?p:(p+g)/(1+p*g))+1)/2,y=1-m,C=r*m+s*y,b=o*m+l*y,x=n*m+h*y,v=a*d+u*(1-d);return(0,f.Z)(C,b,x,v)})(i,t,e)};var g=i(57838),m=i(28482),y=i(28186),C=i(86861),b=i(18802),x="comm",v="rule",k="decl",T=Math.abs,_=String.fromCharCode;function S(t){return t.trim()}function B(t,e,i){return t.replace(e,i)}function F(t,e){return t.indexOf(e)}function w(t,e){return 0|t.charCodeAt(e)}function A(t,e,i){return t.slice(e,i)}function L(t){return t.length}function Z(t,e){return e.push(t),t}function E(t,e){for(var i="",r=0;r<t.length;r++)i+=e(t[r],r,t,e)||"";return i}function M(t,e,i,r){switch(t.type){case"@layer":if(t.children.length)break;case"@import":case k:return t.return=t.return||t.value;case x:return"";case"@keyframes":return t.return=t.value+"{"+E(t.children,r)+"}";case v:if(!L(t.value=t.props.join(",")))return""}return L(i=E(t.children,r))?t.return=t.value+"{"+i+"}":""}Object.assign;var O=1,I=1,q=0,D=0,z=0,N="";function P(t,e,i,r,o,n,a,s){return{value:t,root:e,parent:i,type:r,props:o,children:n,line:O,column:I,length:a,return:"",siblings:s}}function $(){return z=D>0?w(N,--D):0,I--,10===z&&(I=1,O--),z}function j(){return z=D<q?w(N,D++):0,I++,10===z&&(I=1,O++),z}function R(){return w(N,D)}function W(){return D}function H(t,e){return A(N,t,e)}function U(t){switch(t){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Y(t){return S(H(D-1,X(91===t?t+2:40===t?t+1:t)))}function V(t){for(;(z=R())&&z<33;)j();return U(t)>2||U(z)>3?"":" "}function G(t,e){for(;--e&&j()&&!(z<48||z>102||z>57&&z<65||z>70&&z<97););return H(t,W()+(e<6&&32==R()&&32==j()))}function X(t){for(;j();)switch(z){case t:return D;case 34:case 39:34!==t&&39!==t&&X(z);break;case 40:41===t&&X(t);break;case 92:j()}return D}function J(t,e){for(;j()&&t+z!==57&&(t+z!==84||47!==R()););return"/*"+H(e,D-1)+"*"+_(47===t?t:j())}function Q(t){for(;!U(R());)j();return H(t,D)}function K(t){return function(t){return N="",t}(tt("",null,null,null,[""],t=function(t){return O=I=1,q=L(N=t),D=0,[]}(t),0,[0],t))}function tt(t,e,i,r,o,n,a,s,l){for(var h=0,c=0,u=a,d=0,f=0,p=0,g=1,m=1,y=1,C=0,b="",x=o,v=n,k=r,T=b;m;)switch(p=C,C=j()){case 40:if(108!=p&&58==w(T,u-1)){-1!=F(T+=B(Y(C),"&","&\f"),"&\f")&&(y=-1);break}case 34:case 39:case 91:T+=Y(C);break;case 9:case 10:case 13:case 32:T+=V(p);break;case 92:T+=G(W()-1,7);continue;case 47:switch(R()){case 42:case 47:Z(it(J(j(),W()),e,i,l),l);break;default:T+="/"}break;case 123*g:s[h++]=L(T)*y;case 125*g:case 59:case 0:switch(C){case 0:case 125:m=0;case 59+c:-1==y&&(T=B(T,/\f/g,"")),f>0&&L(T)-u&&Z(f>32?rt(T+";",r,i,u-1,l):rt(B(T," ","")+";",r,i,u-2,l),l);break;case 59:T+=";";default:if(Z(k=et(T,e,i,h,c,o,s,b,x=[],v=[],u,n),n),123===C)if(0===c)tt(T,e,k,k,x,n,u,s,v);else switch(99===d&&110===w(T,3)?100:d){case 100:case 108:case 109:case 115:tt(t,k,k,r&&Z(et(t,k,k,0,0,o,s,b,o,x=[],u,v),v),o,v,u,s,r?x:v);break;default:tt(T,k,k,k,[""],v,0,s,v)}}h=c=f=0,g=y=1,b=T="",u=a;break;case 58:u=1+L(T),f=p;default:if(g<1)if(123==C)--g;else if(125==C&&0==g++&&125==$())continue;switch(T+=_(C),C*g){case 38:y=c>0?1:(T+="\f",-1);break;case 44:s[h++]=(L(T)-1)*y,y=1;break;case 64:45===R()&&(T+=Y(j())),d=R(),c=u=L(b=T+=Q(W())),C++;break;case 45:45===p&&2==L(T)&&(g=0)}}return n}function et(t,e,i,r,o,n,a,s,l,h,c,u){for(var d=o-1,f=0===o?n:[""],p=function(t){return t.length}(f),g=0,m=0,y=0;g<r;++g)for(var C=0,b=A(t,d+1,d=T(m=a[g])),x=t;C<p;++C)(x=S(m>0?f[C]+" "+b:B(b,/&\f/g,f[C])))&&(l[y++]=x);return P(t,e,i,0===o?v:s,l,h,c,u)}function it(t,e,i,r){return P(t,e,i,x,_(z),A(t,2,-2),0,r)}function rt(t,e,i,r,o){return P(t,e,i,k,A(t,0,r),A(t,r+1,-1),r,o)}var ot=i(66400);const nt={trace:0,debug:1,info:2,warn:3,error:4,fatal:5},at={trace:(...t)=>{},debug:(...t)=>{},info:(...t)=>{},warn:(...t)=>{},error:(...t)=>{},fatal:(...t)=>{}},st=function(t="fatal"){let e=nt.fatal;"string"==typeof t?(t=t.toLowerCase())in nt&&(e=nt[t]):"number"==typeof t&&(e=t),at.trace=()=>{},at.debug=()=>{},at.info=()=>{},at.warn=()=>{},at.error=()=>{},at.fatal=()=>{},e<=nt.fatal&&(at.fatal=console.error?console.error.bind(console,lt("FATAL"),"color: orange"):console.log.bind(console,"[35m",lt("FATAL"))),e<=nt.error&&(at.error=console.error?console.error.bind(console,lt("ERROR"),"color: orange"):console.log.bind(console,"[31m",lt("ERROR"))),e<=nt.warn&&(at.warn=console.warn?console.warn.bind(console,lt("WARN"),"color: orange"):console.log.bind(console,"[33m",lt("WARN"))),e<=nt.info&&(at.info=console.info?console.info.bind(console,lt("INFO"),"color: lightblue"):console.log.bind(console,"[34m",lt("INFO"))),e<=nt.debug&&(at.debug=console.debug?console.debug.bind(console,lt("DEBUG"),"color: lightgreen"):console.log.bind(console,"[32m",lt("DEBUG"))),e<=nt.trace&&(at.trace=console.debug?console.debug.bind(console,lt("TRACE"),"color: lightgreen"):console.log.bind(console,"[32m",lt("TRACE")))},lt=t=>`%c${n()().format("ss.SSS")} : ${t} : `,ht=/<br\s*\/?>/gi,ct=t=>{const e="data-temp-href-target";h().addHook("beforeSanitizeAttributes",(t=>{"A"===t.tagName&&t.hasAttribute("target")&&t.setAttribute(e,t.getAttribute("target")||"")}));const i=h().sanitize(t);return h().addHook("afterSanitizeAttributes",(t=>{"A"===t.tagName&&t.hasAttribute(e)&&(t.setAttribute("target",t.getAttribute(e)||""),t.removeAttribute(e),"_blank"===t.getAttribute("target")&&t.setAttribute("rel","noopener"))})),i},ut=(t,e)=>{var i;if(!1!==(null==(i=e.flowchart)?void 0:i.htmlLabels)){const i=e.securityLevel;"antiscript"===i||"strict"===i?t=ct(t):"loose"!==i&&(t=(t=(t=pt(t)).replace(/</g,"&lt;").replace(/>/g,"&gt;")).replace(/=/g,"&equals;"),t=ft(t))}return t},dt=(t,e)=>t?t=e.dompurifyConfig?h().sanitize(ut(t,e),e.dompurifyConfig).toString():h().sanitize(ut(t,e),{FORBID_TAGS:["style"]}).toString():t,ft=t=>t.replace(/#br#/g,"<br/>"),pt=t=>t.replace(ht,"#br#"),gt=t=>!1!==t&&!["false","null","0"].includes(String(t).trim().toLowerCase()),mt=function(t){const e=t.split(/(,)/),i=[];for(let t=0;t<e.length;t++){let r=e[t];if(","===r&&t>0&&t+1<e.length){const o=e[t-1],n=e[t+1];Ct(o,n)&&(r=o+","+n,t++,i.pop())}i.push(bt(r))}return i.join("")},yt=(t,e)=>Math.max(0,t.split(e).length-1),Ct=(t,e)=>{const i=yt(t,"~"),r=yt(e,"~");return 1===i&&1===r},bt=t=>{const e=yt(t,"~");let i=!1;if(e<=1)return t;e%2!=0&&t.startsWith("~")&&(t=t.substring(1),i=!0);const r=[...t];let o=r.indexOf("~"),n=r.lastIndexOf("~");for(;-1!==o&&-1!==n&&o!==n;)r[o]="<",r[n]=">",o=r.indexOf("~"),n=r.lastIndexOf("~");return i&&r.unshift("~"),r.join("")},xt={getRows:t=>t?pt(t).replace(/\\n/g,"#br#").split("#br#"):[""],sanitizeText:dt,sanitizeTextOrArray:(t,e)=>"string"==typeof t?dt(t,e):t.flat().map((t=>dt(t,e))),hasBreaks:t=>ht.test(t),splitBreaks:t=>t.split(ht),lineBreakRegex:ht,removeScript:ct,getUrl:t=>{let e="";return t&&(e=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,e=e.replaceAll(/\(/g,"\\("),e=e.replaceAll(/\)/g,"\\)")),e},evaluate:gt,getMax:function(...t){const e=t.filter((t=>!isNaN(t)));return Math.max(...e)},getMin:function(...t){const e=t.filter((t=>!isNaN(t)));return Math.min(...e)}},vt=(t,e)=>d(t,e?{s:-40,l:10}:{s:-40,l:-10}),kt="#ffffff",Tt="#f2f2f2";let _t=class{constructor(){this.background="#f4f4f4",this.primaryColor="#fff4dd",this.noteBkgColor="#fff5ad",this.noteTextColor="#333",this.THEME_COLOR_LIMIT=12,this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px"}updateColors(){var t,e,i,r,o,n,a,s,l,h,c;if(this.primaryTextColor=this.primaryTextColor||(this.darkMode?"#eee":"#333"),this.secondaryColor=this.secondaryColor||d(this.primaryColor,{h:-120}),this.tertiaryColor=this.tertiaryColor||d(this.primaryColor,{h:180,l:5}),this.primaryBorderColor=this.primaryBorderColor||vt(this.primaryColor,this.darkMode),this.secondaryBorderColor=this.secondaryBorderColor||vt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=this.tertiaryBorderColor||vt(this.tertiaryColor,this.darkMode),this.noteBorderColor=this.noteBorderColor||vt(this.noteBkgColor,this.darkMode),this.noteBkgColor=this.noteBkgColor||"#fff5ad",this.noteTextColor=this.noteTextColor||"#333",this.secondaryTextColor=this.secondaryTextColor||p(this.secondaryColor),this.tertiaryTextColor=this.tertiaryTextColor||p(this.tertiaryColor),this.lineColor=this.lineColor||p(this.background),this.arrowheadColor=this.arrowheadColor||p(this.background),this.textColor=this.textColor||this.primaryTextColor,this.border2=this.border2||this.tertiaryBorderColor,this.nodeBkg=this.nodeBkg||this.primaryColor,this.mainBkg=this.mainBkg||this.primaryColor,this.nodeBorder=this.nodeBorder||this.primaryBorderColor,this.clusterBkg=this.clusterBkg||this.tertiaryColor,this.clusterBorder=this.clusterBorder||this.tertiaryBorderColor,this.defaultLinkColor=this.defaultLinkColor||this.lineColor,this.titleColor=this.titleColor||this.tertiaryTextColor,this.edgeLabelBackground=this.edgeLabelBackground||(this.darkMode?(0,g.Z)(this.secondaryColor,30):this.secondaryColor),this.nodeTextColor=this.nodeTextColor||this.primaryTextColor,this.actorBorder=this.actorBorder||this.primaryBorderColor,this.actorBkg=this.actorBkg||this.mainBkg,this.actorTextColor=this.actorTextColor||this.primaryTextColor,this.actorLineColor=this.actorLineColor||"grey",this.labelBoxBkgColor=this.labelBoxBkgColor||this.actorBkg,this.signalColor=this.signalColor||this.textColor,this.signalTextColor=this.signalTextColor||this.textColor,this.labelBoxBorderColor=this.labelBoxBorderColor||this.actorBorder,this.labelTextColor=this.labelTextColor||this.actorTextColor,this.loopTextColor=this.loopTextColor||this.actorTextColor,this.activationBorderColor=this.activationBorderColor||(0,g.Z)(this.secondaryColor,10),this.activationBkgColor=this.activationBkgColor||this.secondaryColor,this.sequenceNumberColor=this.sequenceNumberColor||p(this.lineColor),this.sectionBkgColor=this.sectionBkgColor||this.tertiaryColor,this.altSectionBkgColor=this.altSectionBkgColor||"white",this.sectionBkgColor=this.sectionBkgColor||this.secondaryColor,this.sectionBkgColor2=this.sectionBkgColor2||this.primaryColor,this.excludeBkgColor=this.excludeBkgColor||"#eeeeee",this.taskBorderColor=this.taskBorderColor||this.primaryBorderColor,this.taskBkgColor=this.taskBkgColor||this.primaryColor,this.activeTaskBorderColor=this.activeTaskBorderColor||this.primaryColor,this.activeTaskBkgColor=this.activeTaskBkgColor||(0,m.Z)(this.primaryColor,23),this.gridColor=this.gridColor||"lightgrey",this.doneTaskBkgColor=this.doneTaskBkgColor||"lightgrey",this.doneTaskBorderColor=this.doneTaskBorderColor||"grey",this.critBorderColor=this.critBorderColor||"#ff8888",this.critBkgColor=this.critBkgColor||"red",this.todayLineColor=this.todayLineColor||"red",this.taskTextColor=this.taskTextColor||this.textColor,this.taskTextOutsideColor=this.taskTextOutsideColor||this.textColor,this.taskTextLightColor=this.taskTextLightColor||this.textColor,this.taskTextColor=this.taskTextColor||this.primaryTextColor,this.taskTextDarkColor=this.taskTextDarkColor||this.textColor,this.taskTextClickableColor=this.taskTextClickableColor||"#003163",this.personBorder=this.personBorder||this.primaryBorderColor,this.personBkg=this.personBkg||this.mainBkg,this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||this.tertiaryColor,this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.nodeBorder,this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.transitionColor=this.transitionColor||this.lineColor,this.specialStateColor=this.lineColor,this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||d(this.primaryColor,{h:30}),this.cScale4=this.cScale4||d(this.primaryColor,{h:60}),this.cScale5=this.cScale5||d(this.primaryColor,{h:90}),this.cScale6=this.cScale6||d(this.primaryColor,{h:120}),this.cScale7=this.cScale7||d(this.primaryColor,{h:150}),this.cScale8=this.cScale8||d(this.primaryColor,{h:210,l:150}),this.cScale9=this.cScale9||d(this.primaryColor,{h:270}),this.cScale10=this.cScale10||d(this.primaryColor,{h:300}),this.cScale11=this.cScale11||d(this.primaryColor,{h:330}),this.darkMode)for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScale"+t]=(0,g.Z)(this["cScale"+t],75);else for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScale"+t]=(0,g.Z)(this["cScale"+t],25);for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScaleInv"+t]=this["cScaleInv"+t]||p(this["cScale"+t]);for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this.darkMode?this["cScalePeer"+t]=this["cScalePeer"+t]||(0,m.Z)(this["cScale"+t],10):this["cScalePeer"+t]=this["cScalePeer"+t]||(0,g.Z)(this["cScale"+t],10);this.scaleLabelColor=this.scaleLabelColor||this.labelTextColor;for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScaleLabel"+t]=this["cScaleLabel"+t]||this.scaleLabelColor;const u=this.darkMode?-4:-1;for(let t=0;t<5;t++)this["surface"+t]=this["surface"+t]||d(this.mainBkg,{h:180,s:-15,l:u*(5+3*t)}),this["surfacePeer"+t]=this["surfacePeer"+t]||d(this.mainBkg,{h:180,s:-15,l:u*(8+3*t)});this.classText=this.classText||this.textColor,this.fillType0=this.fillType0||this.primaryColor,this.fillType1=this.fillType1||this.secondaryColor,this.fillType2=this.fillType2||d(this.primaryColor,{h:64}),this.fillType3=this.fillType3||d(this.secondaryColor,{h:64}),this.fillType4=this.fillType4||d(this.primaryColor,{h:-64}),this.fillType5=this.fillType5||d(this.secondaryColor,{h:-64}),this.fillType6=this.fillType6||d(this.primaryColor,{h:128}),this.fillType7=this.fillType7||d(this.secondaryColor,{h:128}),this.pie1=this.pie1||this.primaryColor,this.pie2=this.pie2||this.secondaryColor,this.pie3=this.pie3||this.tertiaryColor,this.pie4=this.pie4||d(this.primaryColor,{l:-10}),this.pie5=this.pie5||d(this.secondaryColor,{l:-10}),this.pie6=this.pie6||d(this.tertiaryColor,{l:-10}),this.pie7=this.pie7||d(this.primaryColor,{h:60,l:-10}),this.pie8=this.pie8||d(this.primaryColor,{h:-60,l:-10}),this.pie9=this.pie9||d(this.primaryColor,{h:120,l:0}),this.pie10=this.pie10||d(this.primaryColor,{h:60,l:-20}),this.pie11=this.pie11||d(this.primaryColor,{h:-60,l:-20}),this.pie12=this.pie12||d(this.primaryColor,{h:120,l:-10}),this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||d(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||d(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||d(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||d(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||d(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||d(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||(0,y.Z)(this.quadrant1Fill)?(0,m.Z)(this.quadrant1Fill):(0,g.Z)(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.xyChart={backgroundColor:(null==(t=this.xyChart)?void 0:t.backgroundColor)||this.background,titleColor:(null==(e=this.xyChart)?void 0:e.titleColor)||this.primaryTextColor,xAxisTitleColor:(null==(i=this.xyChart)?void 0:i.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:(null==(r=this.xyChart)?void 0:r.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:(null==(o=this.xyChart)?void 0:o.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:(null==(n=this.xyChart)?void 0:n.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:(null==(a=this.xyChart)?void 0:a.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:(null==(s=this.xyChart)?void 0:s.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:(null==(l=this.xyChart)?void 0:l.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:(null==(h=this.xyChart)?void 0:h.yAxisLineColor)||this.primaryTextColor,plotColorPalette:(null==(c=this.xyChart)?void 0:c.plotColorPalette)||"#FFF4DD,#FFD8B1,#FFA07A,#ECEFF1,#D6DBDF,#C3E0A8,#FFB6A4,#FFD74D,#738FA7,#FFFFF0"},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||(this.darkMode?(0,g.Z)(this.secondaryColor,30):this.secondaryColor),this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=this.git0||this.primaryColor,this.git1=this.git1||this.secondaryColor,this.git2=this.git2||this.tertiaryColor,this.git3=this.git3||d(this.primaryColor,{h:-30}),this.git4=this.git4||d(this.primaryColor,{h:-60}),this.git5=this.git5||d(this.primaryColor,{h:-90}),this.git6=this.git6||d(this.primaryColor,{h:60}),this.git7=this.git7||d(this.primaryColor,{h:120}),this.darkMode?(this.git0=(0,m.Z)(this.git0,25),this.git1=(0,m.Z)(this.git1,25),this.git2=(0,m.Z)(this.git2,25),this.git3=(0,m.Z)(this.git3,25),this.git4=(0,m.Z)(this.git4,25),this.git5=(0,m.Z)(this.git5,25),this.git6=(0,m.Z)(this.git6,25),this.git7=(0,m.Z)(this.git7,25)):(this.git0=(0,g.Z)(this.git0,25),this.git1=(0,g.Z)(this.git1,25),this.git2=(0,g.Z)(this.git2,25),this.git3=(0,g.Z)(this.git3,25),this.git4=(0,g.Z)(this.git4,25),this.git5=(0,g.Z)(this.git5,25),this.git6=(0,g.Z)(this.git6,25),this.git7=(0,g.Z)(this.git7,25)),this.gitInv0=this.gitInv0||p(this.git0),this.gitInv1=this.gitInv1||p(this.git1),this.gitInv2=this.gitInv2||p(this.git2),this.gitInv3=this.gitInv3||p(this.git3),this.gitInv4=this.gitInv4||p(this.git4),this.gitInv5=this.gitInv5||p(this.git5),this.gitInv6=this.gitInv6||p(this.git6),this.gitInv7=this.gitInv7||p(this.git7),this.branchLabelColor=this.branchLabelColor||(this.darkMode?"black":this.labelTextColor),this.gitBranchLabel0=this.gitBranchLabel0||this.branchLabelColor,this.gitBranchLabel1=this.gitBranchLabel1||this.branchLabelColor,this.gitBranchLabel2=this.gitBranchLabel2||this.branchLabelColor,this.gitBranchLabel3=this.gitBranchLabel3||this.branchLabelColor,this.gitBranchLabel4=this.gitBranchLabel4||this.branchLabelColor,this.gitBranchLabel5=this.gitBranchLabel5||this.branchLabelColor,this.gitBranchLabel6=this.gitBranchLabel6||this.branchLabelColor,this.gitBranchLabel7=this.gitBranchLabel7||this.branchLabelColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||kt,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||Tt}calculate(t){if("object"!=typeof t)return void this.updateColors();const e=Object.keys(t);e.forEach((e=>{this[e]=t[e]})),this.updateColors(),e.forEach((e=>{this[e]=t[e]}))}},St=class{constructor(){this.background="#333",this.primaryColor="#1f2020",this.secondaryColor=(0,m.Z)(this.primaryColor,16),this.tertiaryColor=d(this.primaryColor,{h:-160}),this.primaryBorderColor=p(this.background),this.secondaryBorderColor=vt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=vt(this.tertiaryColor,this.darkMode),this.primaryTextColor=p(this.primaryColor),this.secondaryTextColor=p(this.secondaryColor),this.tertiaryTextColor=p(this.tertiaryColor),this.lineColor=p(this.background),this.textColor=p(this.background),this.mainBkg="#1f2020",this.secondBkg="calculated",this.mainContrastColor="lightgrey",this.darkTextColor=(0,m.Z)(p("#323D47"),10),this.lineColor="calculated",this.border1="#81B1DB",this.border2=(0,f.Z)(255,255,255,.25),this.arrowheadColor="calculated",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.labelBackground="#181818",this.textColor="#ccc",this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="#F9FFFE",this.edgeLabelBackground="calculated",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="calculated",this.actorLineColor="calculated",this.signalColor="calculated",this.signalTextColor="calculated",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="calculated",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="#fff5ad",this.noteTextColor="calculated",this.activationBorderColor="calculated",this.activationBkgColor="calculated",this.sequenceNumberColor="black",this.sectionBkgColor=(0,g.Z)("#EAE8D9",30),this.altSectionBkgColor="calculated",this.sectionBkgColor2="#EAE8D9",this.excludeBkgColor=(0,g.Z)(this.sectionBkgColor,10),this.taskBorderColor=(0,f.Z)(255,255,255,70),this.taskBkgColor="calculated",this.taskTextColor="calculated",this.taskTextLightColor="calculated",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor=(0,f.Z)(255,255,255,50),this.activeTaskBkgColor="#81B1DB",this.gridColor="calculated",this.doneTaskBkgColor="calculated",this.doneTaskBorderColor="grey",this.critBorderColor="#E83737",this.critBkgColor="#E83737",this.taskTextDarkColor="calculated",this.todayLineColor="#DB5757",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.labelColor="calculated",this.errorBkgColor="#a44141",this.errorTextColor="#ddd"}updateColors(){var t,e,i,r,o,n,a,s,l,h,c;this.secondBkg=(0,m.Z)(this.mainBkg,16),this.lineColor=this.mainContrastColor,this.arrowheadColor=this.mainContrastColor,this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.edgeLabelBackground=(0,m.Z)(this.labelBackground,25),this.actorBorder=this.border1,this.actorBkg=this.mainBkg,this.actorTextColor=this.mainContrastColor,this.actorLineColor=this.mainContrastColor,this.signalColor=this.mainContrastColor,this.signalTextColor=this.mainContrastColor,this.labelBoxBkgColor=this.actorBkg,this.labelBoxBorderColor=this.actorBorder,this.labelTextColor=this.mainContrastColor,this.loopTextColor=this.mainContrastColor,this.noteBorderColor=this.secondaryBorderColor,this.noteBkgColor=this.secondBkg,this.noteTextColor=this.secondaryTextColor,this.activationBorderColor=this.border1,this.activationBkgColor=this.secondBkg,this.altSectionBkgColor=this.background,this.taskBkgColor=(0,m.Z)(this.mainBkg,23),this.taskTextColor=this.darkTextColor,this.taskTextLightColor=this.mainContrastColor,this.taskTextOutsideColor=this.taskTextLightColor,this.gridColor=this.mainContrastColor,this.doneTaskBkgColor=this.mainContrastColor,this.taskTextDarkColor=this.darkTextColor,this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#555",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.primaryBorderColor,this.specialStateColor="#f4f4f4",this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=d(this.primaryColor,{h:64}),this.fillType3=d(this.secondaryColor,{h:64}),this.fillType4=d(this.primaryColor,{h:-64}),this.fillType5=d(this.secondaryColor,{h:-64}),this.fillType6=d(this.primaryColor,{h:128}),this.fillType7=d(this.secondaryColor,{h:128}),this.cScale1=this.cScale1||"#0b0000",this.cScale2=this.cScale2||"#4d1037",this.cScale3=this.cScale3||"#3f5258",this.cScale4=this.cScale4||"#4f2f1b",this.cScale5=this.cScale5||"#6e0a0a",this.cScale6=this.cScale6||"#3b0048",this.cScale7=this.cScale7||"#995a01",this.cScale8=this.cScale8||"#154706",this.cScale9=this.cScale9||"#161722",this.cScale10=this.cScale10||"#00296f",this.cScale11=this.cScale11||"#01629c",this.cScale12=this.cScale12||"#010029",this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||d(this.primaryColor,{h:30}),this.cScale4=this.cScale4||d(this.primaryColor,{h:60}),this.cScale5=this.cScale5||d(this.primaryColor,{h:90}),this.cScale6=this.cScale6||d(this.primaryColor,{h:120}),this.cScale7=this.cScale7||d(this.primaryColor,{h:150}),this.cScale8=this.cScale8||d(this.primaryColor,{h:210}),this.cScale9=this.cScale9||d(this.primaryColor,{h:270}),this.cScale10=this.cScale10||d(this.primaryColor,{h:300}),this.cScale11=this.cScale11||d(this.primaryColor,{h:330});for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScaleInv"+t]=this["cScaleInv"+t]||p(this["cScale"+t]);for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScalePeer"+t]=this["cScalePeer"+t]||(0,m.Z)(this["cScale"+t],10);for(let t=0;t<5;t++)this["surface"+t]=this["surface"+t]||d(this.mainBkg,{h:30,s:-30,l:-(4*t-10)}),this["surfacePeer"+t]=this["surfacePeer"+t]||d(this.mainBkg,{h:30,s:-30,l:-(4*t-7)});this.scaleLabelColor=this.scaleLabelColor||(this.darkMode?"black":this.labelTextColor);for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScaleLabel"+t]=this["cScaleLabel"+t]||this.scaleLabelColor;for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["pie"+t]=this["cScale"+t];this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||d(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||d(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||d(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||d(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||d(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||d(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||(0,y.Z)(this.quadrant1Fill)?(0,m.Z)(this.quadrant1Fill):(0,g.Z)(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.xyChart={backgroundColor:(null==(t=this.xyChart)?void 0:t.backgroundColor)||this.background,titleColor:(null==(e=this.xyChart)?void 0:e.titleColor)||this.primaryTextColor,xAxisTitleColor:(null==(i=this.xyChart)?void 0:i.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:(null==(r=this.xyChart)?void 0:r.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:(null==(o=this.xyChart)?void 0:o.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:(null==(n=this.xyChart)?void 0:n.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:(null==(a=this.xyChart)?void 0:a.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:(null==(s=this.xyChart)?void 0:s.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:(null==(l=this.xyChart)?void 0:l.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:(null==(h=this.xyChart)?void 0:h.yAxisLineColor)||this.primaryTextColor,plotColorPalette:(null==(c=this.xyChart)?void 0:c.plotColorPalette)||"#3498db,#2ecc71,#e74c3c,#f1c40f,#bdc3c7,#ffffff,#34495e,#9b59b6,#1abc9c,#e67e22"},this.classText=this.primaryTextColor,this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||(this.darkMode?(0,g.Z)(this.secondaryColor,30):this.secondaryColor),this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=(0,m.Z)(this.secondaryColor,20),this.git1=(0,m.Z)(this.pie2||this.secondaryColor,20),this.git2=(0,m.Z)(this.pie3||this.tertiaryColor,20),this.git3=(0,m.Z)(this.pie4||d(this.primaryColor,{h:-30}),20),this.git4=(0,m.Z)(this.pie5||d(this.primaryColor,{h:-60}),20),this.git5=(0,m.Z)(this.pie6||d(this.primaryColor,{h:-90}),10),this.git6=(0,m.Z)(this.pie7||d(this.primaryColor,{h:60}),10),this.git7=(0,m.Z)(this.pie8||d(this.primaryColor,{h:120}),20),this.gitInv0=this.gitInv0||p(this.git0),this.gitInv1=this.gitInv1||p(this.git1),this.gitInv2=this.gitInv2||p(this.git2),this.gitInv3=this.gitInv3||p(this.git3),this.gitInv4=this.gitInv4||p(this.git4),this.gitInv5=this.gitInv5||p(this.git5),this.gitInv6=this.gitInv6||p(this.git6),this.gitInv7=this.gitInv7||p(this.git7),this.gitBranchLabel0=this.gitBranchLabel0||p(this.labelTextColor),this.gitBranchLabel1=this.gitBranchLabel1||this.labelTextColor,this.gitBranchLabel2=this.gitBranchLabel2||this.labelTextColor,this.gitBranchLabel3=this.gitBranchLabel3||p(this.labelTextColor),this.gitBranchLabel4=this.gitBranchLabel4||this.labelTextColor,this.gitBranchLabel5=this.gitBranchLabel5||this.labelTextColor,this.gitBranchLabel6=this.gitBranchLabel6||this.labelTextColor,this.gitBranchLabel7=this.gitBranchLabel7||this.labelTextColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||(0,m.Z)(this.background,12),this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||(0,m.Z)(this.background,2)}calculate(t){if("object"!=typeof t)return void this.updateColors();const e=Object.keys(t);e.forEach((e=>{this[e]=t[e]})),this.updateColors(),e.forEach((e=>{this[e]=t[e]}))}},Bt=class{constructor(){this.background="#f4f4f4",this.primaryColor="#ECECFF",this.secondaryColor=d(this.primaryColor,{h:120}),this.secondaryColor="#ffffde",this.tertiaryColor=d(this.primaryColor,{h:-160}),this.primaryBorderColor=vt(this.primaryColor,this.darkMode),this.secondaryBorderColor=vt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=vt(this.tertiaryColor,this.darkMode),this.primaryTextColor=p(this.primaryColor),this.secondaryTextColor=p(this.secondaryColor),this.tertiaryTextColor=p(this.tertiaryColor),this.lineColor=p(this.background),this.textColor=p(this.background),this.background="white",this.mainBkg="#ECECFF",this.secondBkg="#ffffde",this.lineColor="#333333",this.border1="#9370DB",this.border2="#aaaa33",this.arrowheadColor="#333333",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.labelBackground="#e8e8e8",this.textColor="#333",this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="calculated",this.edgeLabelBackground="calculated",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="black",this.actorLineColor="grey",this.signalColor="calculated",this.signalTextColor="calculated",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="calculated",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="#fff5ad",this.noteTextColor="calculated",this.activationBorderColor="#666",this.activationBkgColor="#f4f4f4",this.sequenceNumberColor="white",this.sectionBkgColor="calculated",this.altSectionBkgColor="calculated",this.sectionBkgColor2="calculated",this.excludeBkgColor="#eeeeee",this.taskBorderColor="calculated",this.taskBkgColor="calculated",this.taskTextLightColor="calculated",this.taskTextColor=this.taskTextLightColor,this.taskTextDarkColor="calculated",this.taskTextOutsideColor=this.taskTextDarkColor,this.taskTextClickableColor="calculated",this.activeTaskBorderColor="calculated",this.activeTaskBkgColor="calculated",this.gridColor="calculated",this.doneTaskBkgColor="calculated",this.doneTaskBorderColor="calculated",this.critBorderColor="calculated",this.critBkgColor="calculated",this.todayLineColor="calculated",this.sectionBkgColor=(0,f.Z)(102,102,255,.49),this.altSectionBkgColor="white",this.sectionBkgColor2="#fff400",this.taskBorderColor="#534fbc",this.taskBkgColor="#8a90dd",this.taskTextLightColor="white",this.taskTextColor="calculated",this.taskTextDarkColor="black",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor="#534fbc",this.activeTaskBkgColor="#bfc7ff",this.gridColor="lightgrey",this.doneTaskBkgColor="lightgrey",this.doneTaskBorderColor="grey",this.critBorderColor="#ff8888",this.critBkgColor="red",this.todayLineColor="red",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.labelColor="black",this.errorBkgColor="#552222",this.errorTextColor="#552222",this.updateColors()}updateColors(){var t,e,i,r,o,n,a,s,l,h,c;this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||d(this.primaryColor,{h:30}),this.cScale4=this.cScale4||d(this.primaryColor,{h:60}),this.cScale5=this.cScale5||d(this.primaryColor,{h:90}),this.cScale6=this.cScale6||d(this.primaryColor,{h:120}),this.cScale7=this.cScale7||d(this.primaryColor,{h:150}),this.cScale8=this.cScale8||d(this.primaryColor,{h:210}),this.cScale9=this.cScale9||d(this.primaryColor,{h:270}),this.cScale10=this.cScale10||d(this.primaryColor,{h:300}),this.cScale11=this.cScale11||d(this.primaryColor,{h:330}),this.cScalePeer1=this.cScalePeer1||(0,g.Z)(this.secondaryColor,45),this.cScalePeer2=this.cScalePeer2||(0,g.Z)(this.tertiaryColor,40);for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScale"+t]=(0,g.Z)(this["cScale"+t],10),this["cScalePeer"+t]=this["cScalePeer"+t]||(0,g.Z)(this["cScale"+t],25);for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScaleInv"+t]=this["cScaleInv"+t]||d(this["cScale"+t],{h:180});for(let t=0;t<5;t++)this["surface"+t]=this["surface"+t]||d(this.mainBkg,{h:30,l:-(5+5*t)}),this["surfacePeer"+t]=this["surfacePeer"+t]||d(this.mainBkg,{h:30,l:-(7+5*t)});if(this.scaleLabelColor="calculated"!==this.scaleLabelColor&&this.scaleLabelColor?this.scaleLabelColor:this.labelTextColor,"calculated"!==this.labelTextColor){this.cScaleLabel0=this.cScaleLabel0||p(this.labelTextColor),this.cScaleLabel3=this.cScaleLabel3||p(this.labelTextColor);for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScaleLabel"+t]=this["cScaleLabel"+t]||this.labelTextColor}this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.titleColor=this.textColor,this.edgeLabelBackground=this.labelBackground,this.actorBorder=(0,m.Z)(this.border1,23),this.actorBkg=this.mainBkg,this.labelBoxBkgColor=this.actorBkg,this.signalColor=this.textColor,this.signalTextColor=this.textColor,this.labelBoxBorderColor=this.actorBorder,this.labelTextColor=this.actorTextColor,this.loopTextColor=this.actorTextColor,this.noteBorderColor=this.border2,this.noteTextColor=this.actorTextColor,this.taskTextColor=this.taskTextLightColor,this.taskTextOutsideColor=this.taskTextDarkColor,this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#f0f0f0",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.nodeBorder,this.specialStateColor=this.lineColor,this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.transitionColor=this.transitionColor||this.lineColor,this.classText=this.primaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=d(this.primaryColor,{h:64}),this.fillType3=d(this.secondaryColor,{h:64}),this.fillType4=d(this.primaryColor,{h:-64}),this.fillType5=d(this.secondaryColor,{h:-64}),this.fillType6=d(this.primaryColor,{h:128}),this.fillType7=d(this.secondaryColor,{h:128}),this.pie1=this.pie1||this.primaryColor,this.pie2=this.pie2||this.secondaryColor,this.pie3=this.pie3||d(this.tertiaryColor,{l:-40}),this.pie4=this.pie4||d(this.primaryColor,{l:-10}),this.pie5=this.pie5||d(this.secondaryColor,{l:-30}),this.pie6=this.pie6||d(this.tertiaryColor,{l:-20}),this.pie7=this.pie7||d(this.primaryColor,{h:60,l:-20}),this.pie8=this.pie8||d(this.primaryColor,{h:-60,l:-40}),this.pie9=this.pie9||d(this.primaryColor,{h:120,l:-40}),this.pie10=this.pie10||d(this.primaryColor,{h:60,l:-40}),this.pie11=this.pie11||d(this.primaryColor,{h:-90,l:-40}),this.pie12=this.pie12||d(this.primaryColor,{h:120,l:-30}),this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||d(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||d(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||d(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||d(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||d(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||d(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||(0,y.Z)(this.quadrant1Fill)?(0,m.Z)(this.quadrant1Fill):(0,g.Z)(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.xyChart={backgroundColor:(null==(t=this.xyChart)?void 0:t.backgroundColor)||this.background,titleColor:(null==(e=this.xyChart)?void 0:e.titleColor)||this.primaryTextColor,xAxisTitleColor:(null==(i=this.xyChart)?void 0:i.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:(null==(r=this.xyChart)?void 0:r.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:(null==(o=this.xyChart)?void 0:o.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:(null==(n=this.xyChart)?void 0:n.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:(null==(a=this.xyChart)?void 0:a.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:(null==(s=this.xyChart)?void 0:s.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:(null==(l=this.xyChart)?void 0:l.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:(null==(h=this.xyChart)?void 0:h.yAxisLineColor)||this.primaryTextColor,plotColorPalette:(null==(c=this.xyChart)?void 0:c.plotColorPalette)||"#ECECFF,#8493A6,#FFC3A0,#DCDDE1,#B8E994,#D1A36F,#C3CDE6,#FFB6C1,#496078,#F8F3E3"},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||this.labelBackground,this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=this.git0||this.primaryColor,this.git1=this.git1||this.secondaryColor,this.git2=this.git2||this.tertiaryColor,this.git3=this.git3||d(this.primaryColor,{h:-30}),this.git4=this.git4||d(this.primaryColor,{h:-60}),this.git5=this.git5||d(this.primaryColor,{h:-90}),this.git6=this.git6||d(this.primaryColor,{h:60}),this.git7=this.git7||d(this.primaryColor,{h:120}),this.darkMode?(this.git0=(0,m.Z)(this.git0,25),this.git1=(0,m.Z)(this.git1,25),this.git2=(0,m.Z)(this.git2,25),this.git3=(0,m.Z)(this.git3,25),this.git4=(0,m.Z)(this.git4,25),this.git5=(0,m.Z)(this.git5,25),this.git6=(0,m.Z)(this.git6,25),this.git7=(0,m.Z)(this.git7,25)):(this.git0=(0,g.Z)(this.git0,25),this.git1=(0,g.Z)(this.git1,25),this.git2=(0,g.Z)(this.git2,25),this.git3=(0,g.Z)(this.git3,25),this.git4=(0,g.Z)(this.git4,25),this.git5=(0,g.Z)(this.git5,25),this.git6=(0,g.Z)(this.git6,25),this.git7=(0,g.Z)(this.git7,25)),this.gitInv0=this.gitInv0||(0,g.Z)(p(this.git0),25),this.gitInv1=this.gitInv1||p(this.git1),this.gitInv2=this.gitInv2||p(this.git2),this.gitInv3=this.gitInv3||p(this.git3),this.gitInv4=this.gitInv4||p(this.git4),this.gitInv5=this.gitInv5||p(this.git5),this.gitInv6=this.gitInv6||p(this.git6),this.gitInv7=this.gitInv7||p(this.git7),this.gitBranchLabel0=this.gitBranchLabel0||p(this.labelTextColor),this.gitBranchLabel1=this.gitBranchLabel1||this.labelTextColor,this.gitBranchLabel2=this.gitBranchLabel2||this.labelTextColor,this.gitBranchLabel3=this.gitBranchLabel3||p(this.labelTextColor),this.gitBranchLabel4=this.gitBranchLabel4||this.labelTextColor,this.gitBranchLabel5=this.gitBranchLabel5||this.labelTextColor,this.gitBranchLabel6=this.gitBranchLabel6||this.labelTextColor,this.gitBranchLabel7=this.gitBranchLabel7||this.labelTextColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||kt,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||Tt}calculate(t){if("object"!=typeof t)return void this.updateColors();const e=Object.keys(t);e.forEach((e=>{this[e]=t[e]})),this.updateColors(),e.forEach((e=>{this[e]=t[e]}))}};const Ft=t=>{const e=new Bt;return e.calculate(t),e};let wt=class{constructor(){this.background="#f4f4f4",this.primaryColor="#cde498",this.secondaryColor="#cdffb2",this.background="white",this.mainBkg="#cde498",this.secondBkg="#cdffb2",this.lineColor="green",this.border1="#13540c",this.border2="#6eaa49",this.arrowheadColor="green",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.tertiaryColor=(0,m.Z)("#cde498",10),this.primaryBorderColor=vt(this.primaryColor,this.darkMode),this.secondaryBorderColor=vt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=vt(this.tertiaryColor,this.darkMode),this.primaryTextColor=p(this.primaryColor),this.secondaryTextColor=p(this.secondaryColor),this.tertiaryTextColor=p(this.primaryColor),this.lineColor=p(this.background),this.textColor=p(this.background),this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="#333",this.edgeLabelBackground="#e8e8e8",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="black",this.actorLineColor="grey",this.signalColor="#333",this.signalTextColor="#333",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="#326932",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="#fff5ad",this.noteTextColor="calculated",this.activationBorderColor="#666",this.activationBkgColor="#f4f4f4",this.sequenceNumberColor="white",this.sectionBkgColor="#6eaa49",this.altSectionBkgColor="white",this.sectionBkgColor2="#6eaa49",this.excludeBkgColor="#eeeeee",this.taskBorderColor="calculated",this.taskBkgColor="#487e3a",this.taskTextLightColor="white",this.taskTextColor="calculated",this.taskTextDarkColor="black",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor="calculated",this.activeTaskBkgColor="calculated",this.gridColor="lightgrey",this.doneTaskBkgColor="lightgrey",this.doneTaskBorderColor="grey",this.critBorderColor="#ff8888",this.critBkgColor="red",this.todayLineColor="red",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.labelColor="black",this.errorBkgColor="#552222",this.errorTextColor="#552222"}updateColors(){var t,e,i,r,o,n,a,s,l,h,c;this.actorBorder=(0,g.Z)(this.mainBkg,20),this.actorBkg=this.mainBkg,this.labelBoxBkgColor=this.actorBkg,this.labelTextColor=this.actorTextColor,this.loopTextColor=this.actorTextColor,this.noteBorderColor=this.border2,this.noteTextColor=this.actorTextColor,this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||d(this.primaryColor,{h:30}),this.cScale4=this.cScale4||d(this.primaryColor,{h:60}),this.cScale5=this.cScale5||d(this.primaryColor,{h:90}),this.cScale6=this.cScale6||d(this.primaryColor,{h:120}),this.cScale7=this.cScale7||d(this.primaryColor,{h:150}),this.cScale8=this.cScale8||d(this.primaryColor,{h:210}),this.cScale9=this.cScale9||d(this.primaryColor,{h:270}),this.cScale10=this.cScale10||d(this.primaryColor,{h:300}),this.cScale11=this.cScale11||d(this.primaryColor,{h:330}),this.cScalePeer1=this.cScalePeer1||(0,g.Z)(this.secondaryColor,45),this.cScalePeer2=this.cScalePeer2||(0,g.Z)(this.tertiaryColor,40);for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScale"+t]=(0,g.Z)(this["cScale"+t],10),this["cScalePeer"+t]=this["cScalePeer"+t]||(0,g.Z)(this["cScale"+t],25);for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScaleInv"+t]=this["cScaleInv"+t]||d(this["cScale"+t],{h:180});this.scaleLabelColor="calculated"!==this.scaleLabelColor&&this.scaleLabelColor?this.scaleLabelColor:this.labelTextColor;for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScaleLabel"+t]=this["cScaleLabel"+t]||this.scaleLabelColor;for(let t=0;t<5;t++)this["surface"+t]=this["surface"+t]||d(this.mainBkg,{h:30,s:-30,l:-(5+5*t)}),this["surfacePeer"+t]=this["surfacePeer"+t]||d(this.mainBkg,{h:30,s:-30,l:-(8+5*t)});this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.taskBorderColor=this.border1,this.taskTextColor=this.taskTextLightColor,this.taskTextOutsideColor=this.taskTextDarkColor,this.activeTaskBorderColor=this.taskBorderColor,this.activeTaskBkgColor=this.mainBkg,this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#f0f0f0",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.primaryBorderColor,this.specialStateColor=this.lineColor,this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.transitionColor=this.transitionColor||this.lineColor,this.classText=this.primaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=d(this.primaryColor,{h:64}),this.fillType3=d(this.secondaryColor,{h:64}),this.fillType4=d(this.primaryColor,{h:-64}),this.fillType5=d(this.secondaryColor,{h:-64}),this.fillType6=d(this.primaryColor,{h:128}),this.fillType7=d(this.secondaryColor,{h:128}),this.pie1=this.pie1||this.primaryColor,this.pie2=this.pie2||this.secondaryColor,this.pie3=this.pie3||this.tertiaryColor,this.pie4=this.pie4||d(this.primaryColor,{l:-30}),this.pie5=this.pie5||d(this.secondaryColor,{l:-30}),this.pie6=this.pie6||d(this.tertiaryColor,{h:40,l:-40}),this.pie7=this.pie7||d(this.primaryColor,{h:60,l:-10}),this.pie8=this.pie8||d(this.primaryColor,{h:-60,l:-10}),this.pie9=this.pie9||d(this.primaryColor,{h:120,l:0}),this.pie10=this.pie10||d(this.primaryColor,{h:60,l:-50}),this.pie11=this.pie11||d(this.primaryColor,{h:-60,l:-50}),this.pie12=this.pie12||d(this.primaryColor,{h:120,l:-50}),this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||d(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||d(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||d(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||d(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||d(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||d(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||(0,y.Z)(this.quadrant1Fill)?(0,m.Z)(this.quadrant1Fill):(0,g.Z)(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.xyChart={backgroundColor:(null==(t=this.xyChart)?void 0:t.backgroundColor)||this.background,titleColor:(null==(e=this.xyChart)?void 0:e.titleColor)||this.primaryTextColor,xAxisTitleColor:(null==(i=this.xyChart)?void 0:i.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:(null==(r=this.xyChart)?void 0:r.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:(null==(o=this.xyChart)?void 0:o.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:(null==(n=this.xyChart)?void 0:n.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:(null==(a=this.xyChart)?void 0:a.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:(null==(s=this.xyChart)?void 0:s.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:(null==(l=this.xyChart)?void 0:l.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:(null==(h=this.xyChart)?void 0:h.yAxisLineColor)||this.primaryTextColor,plotColorPalette:(null==(c=this.xyChart)?void 0:c.plotColorPalette)||"#CDE498,#FF6B6B,#A0D2DB,#D7BDE2,#F0F0F0,#FFC3A0,#7FD8BE,#FF9A8B,#FAF3E0,#FFF176"},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||this.edgeLabelBackground,this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=this.git0||this.primaryColor,this.git1=this.git1||this.secondaryColor,this.git2=this.git2||this.tertiaryColor,this.git3=this.git3||d(this.primaryColor,{h:-30}),this.git4=this.git4||d(this.primaryColor,{h:-60}),this.git5=this.git5||d(this.primaryColor,{h:-90}),this.git6=this.git6||d(this.primaryColor,{h:60}),this.git7=this.git7||d(this.primaryColor,{h:120}),this.darkMode?(this.git0=(0,m.Z)(this.git0,25),this.git1=(0,m.Z)(this.git1,25),this.git2=(0,m.Z)(this.git2,25),this.git3=(0,m.Z)(this.git3,25),this.git4=(0,m.Z)(this.git4,25),this.git5=(0,m.Z)(this.git5,25),this.git6=(0,m.Z)(this.git6,25),this.git7=(0,m.Z)(this.git7,25)):(this.git0=(0,g.Z)(this.git0,25),this.git1=(0,g.Z)(this.git1,25),this.git2=(0,g.Z)(this.git2,25),this.git3=(0,g.Z)(this.git3,25),this.git4=(0,g.Z)(this.git4,25),this.git5=(0,g.Z)(this.git5,25),this.git6=(0,g.Z)(this.git6,25),this.git7=(0,g.Z)(this.git7,25)),this.gitInv0=this.gitInv0||p(this.git0),this.gitInv1=this.gitInv1||p(this.git1),this.gitInv2=this.gitInv2||p(this.git2),this.gitInv3=this.gitInv3||p(this.git3),this.gitInv4=this.gitInv4||p(this.git4),this.gitInv5=this.gitInv5||p(this.git5),this.gitInv6=this.gitInv6||p(this.git6),this.gitInv7=this.gitInv7||p(this.git7),this.gitBranchLabel0=this.gitBranchLabel0||p(this.labelTextColor),this.gitBranchLabel1=this.gitBranchLabel1||this.labelTextColor,this.gitBranchLabel2=this.gitBranchLabel2||this.labelTextColor,this.gitBranchLabel3=this.gitBranchLabel3||p(this.labelTextColor),this.gitBranchLabel4=this.gitBranchLabel4||this.labelTextColor,this.gitBranchLabel5=this.gitBranchLabel5||this.labelTextColor,this.gitBranchLabel6=this.gitBranchLabel6||this.labelTextColor,this.gitBranchLabel7=this.gitBranchLabel7||this.labelTextColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||kt,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||Tt}calculate(t){if("object"!=typeof t)return void this.updateColors();const e=Object.keys(t);e.forEach((e=>{this[e]=t[e]})),this.updateColors(),e.forEach((e=>{this[e]=t[e]}))}};class At{constructor(){this.primaryColor="#eee",this.contrast="#707070",this.secondaryColor=(0,m.Z)(this.contrast,55),this.background="#ffffff",this.tertiaryColor=d(this.primaryColor,{h:-160}),this.primaryBorderColor=vt(this.primaryColor,this.darkMode),this.secondaryBorderColor=vt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=vt(this.tertiaryColor,this.darkMode),this.primaryTextColor=p(this.primaryColor),this.secondaryTextColor=p(this.secondaryColor),this.tertiaryTextColor=p(this.tertiaryColor),this.lineColor=p(this.background),this.textColor=p(this.background),this.mainBkg="#eee",this.secondBkg="calculated",this.lineColor="#666",this.border1="#999",this.border2="calculated",this.note="#ffa",this.text="#333",this.critical="#d42",this.done="#bbb",this.arrowheadColor="#333333",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="calculated",this.edgeLabelBackground="white",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="calculated",this.actorLineColor="calculated",this.signalColor="calculated",this.signalTextColor="calculated",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="calculated",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="calculated",this.noteTextColor="calculated",this.activationBorderColor="#666",this.activationBkgColor="#f4f4f4",this.sequenceNumberColor="white",this.sectionBkgColor="calculated",this.altSectionBkgColor="white",this.sectionBkgColor2="calculated",this.excludeBkgColor="#eeeeee",this.taskBorderColor="calculated",this.taskBkgColor="calculated",this.taskTextLightColor="white",this.taskTextColor="calculated",this.taskTextDarkColor="calculated",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor="calculated",this.activeTaskBkgColor="calculated",this.gridColor="calculated",this.doneTaskBkgColor="calculated",this.doneTaskBorderColor="calculated",this.critBkgColor="calculated",this.critBorderColor="calculated",this.todayLineColor="calculated",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.labelColor="black",this.errorBkgColor="#552222",this.errorTextColor="#552222"}updateColors(){var t,e,i,r,o,n,a,s,l,h,c;this.secondBkg=(0,m.Z)(this.contrast,55),this.border2=this.contrast,this.actorBorder=(0,m.Z)(this.border1,23),this.actorBkg=this.mainBkg,this.actorTextColor=this.text,this.actorLineColor=this.lineColor,this.signalColor=this.text,this.signalTextColor=this.text,this.labelBoxBkgColor=this.actorBkg,this.labelBoxBorderColor=this.actorBorder,this.labelTextColor=this.text,this.loopTextColor=this.text,this.noteBorderColor="#999",this.noteBkgColor="#666",this.noteTextColor="#fff",this.cScale0=this.cScale0||"#555",this.cScale1=this.cScale1||"#F4F4F4",this.cScale2=this.cScale2||"#555",this.cScale3=this.cScale3||"#BBB",this.cScale4=this.cScale4||"#777",this.cScale5=this.cScale5||"#999",this.cScale6=this.cScale6||"#DDD",this.cScale7=this.cScale7||"#FFF",this.cScale8=this.cScale8||"#DDD",this.cScale9=this.cScale9||"#BBB",this.cScale10=this.cScale10||"#999",this.cScale11=this.cScale11||"#777";for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScaleInv"+t]=this["cScaleInv"+t]||p(this["cScale"+t]);for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this.darkMode?this["cScalePeer"+t]=this["cScalePeer"+t]||(0,m.Z)(this["cScale"+t],10):this["cScalePeer"+t]=this["cScalePeer"+t]||(0,g.Z)(this["cScale"+t],10);this.scaleLabelColor=this.scaleLabelColor||(this.darkMode?"black":this.labelTextColor),this.cScaleLabel0=this.cScaleLabel0||this.cScale1,this.cScaleLabel2=this.cScaleLabel2||this.cScale1;for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["cScaleLabel"+t]=this["cScaleLabel"+t]||this.scaleLabelColor;for(let t=0;t<5;t++)this["surface"+t]=this["surface"+t]||d(this.mainBkg,{l:-(5+5*t)}),this["surfacePeer"+t]=this["surfacePeer"+t]||d(this.mainBkg,{l:-(8+5*t)});this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.titleColor=this.text,this.sectionBkgColor=(0,m.Z)(this.contrast,30),this.sectionBkgColor2=(0,m.Z)(this.contrast,30),this.taskBorderColor=(0,g.Z)(this.contrast,10),this.taskBkgColor=this.contrast,this.taskTextColor=this.taskTextLightColor,this.taskTextDarkColor=this.text,this.taskTextOutsideColor=this.taskTextDarkColor,this.activeTaskBorderColor=this.taskBorderColor,this.activeTaskBkgColor=this.mainBkg,this.gridColor=(0,m.Z)(this.border1,30),this.doneTaskBkgColor=this.done,this.doneTaskBorderColor=this.lineColor,this.critBkgColor=this.critical,this.critBorderColor=(0,g.Z)(this.critBkgColor,10),this.todayLineColor=this.critBkgColor,this.transitionColor=this.transitionColor||"#000",this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#f4f4f4",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.stateBorder=this.stateBorder||"#000",this.innerEndBackground=this.primaryBorderColor,this.specialStateColor="#222",this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.classText=this.primaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=d(this.primaryColor,{h:64}),this.fillType3=d(this.secondaryColor,{h:64}),this.fillType4=d(this.primaryColor,{h:-64}),this.fillType5=d(this.secondaryColor,{h:-64}),this.fillType6=d(this.primaryColor,{h:128}),this.fillType7=d(this.secondaryColor,{h:128});for(let t=0;t<this.THEME_COLOR_LIMIT;t++)this["pie"+t]=this["cScale"+t];this.pie12=this.pie0,this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||d(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||d(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||d(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||d(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||d(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||d(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||(0,y.Z)(this.quadrant1Fill)?(0,m.Z)(this.quadrant1Fill):(0,g.Z)(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.xyChart={backgroundColor:(null==(t=this.xyChart)?void 0:t.backgroundColor)||this.background,titleColor:(null==(e=this.xyChart)?void 0:e.titleColor)||this.primaryTextColor,xAxisTitleColor:(null==(i=this.xyChart)?void 0:i.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:(null==(r=this.xyChart)?void 0:r.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:(null==(o=this.xyChart)?void 0:o.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:(null==(n=this.xyChart)?void 0:n.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:(null==(a=this.xyChart)?void 0:a.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:(null==(s=this.xyChart)?void 0:s.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:(null==(l=this.xyChart)?void 0:l.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:(null==(h=this.xyChart)?void 0:h.yAxisLineColor)||this.primaryTextColor,plotColorPalette:(null==(c=this.xyChart)?void 0:c.plotColorPalette)||"#EEE,#6BB8E4,#8ACB88,#C7ACD6,#E8DCC2,#FFB2A8,#FFF380,#7E8D91,#FFD8B1,#FAF3E0"},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||this.edgeLabelBackground,this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=(0,g.Z)(this.pie1,25)||this.primaryColor,this.git1=this.pie2||this.secondaryColor,this.git2=this.pie3||this.tertiaryColor,this.git3=this.pie4||d(this.primaryColor,{h:-30}),this.git4=this.pie5||d(this.primaryColor,{h:-60}),this.git5=this.pie6||d(this.primaryColor,{h:-90}),this.git6=this.pie7||d(this.primaryColor,{h:60}),this.git7=this.pie8||d(this.primaryColor,{h:120}),this.gitInv0=this.gitInv0||p(this.git0),this.gitInv1=this.gitInv1||p(this.git1),this.gitInv2=this.gitInv2||p(this.git2),this.gitInv3=this.gitInv3||p(this.git3),this.gitInv4=this.gitInv4||p(this.git4),this.gitInv5=this.gitInv5||p(this.git5),this.gitInv6=this.gitInv6||p(this.git6),this.gitInv7=this.gitInv7||p(this.git7),this.branchLabelColor=this.branchLabelColor||this.labelTextColor,this.gitBranchLabel0=this.branchLabelColor,this.gitBranchLabel1="white",this.gitBranchLabel2=this.branchLabelColor,this.gitBranchLabel3="white",this.gitBranchLabel4=this.branchLabelColor,this.gitBranchLabel5=this.branchLabelColor,this.gitBranchLabel6=this.branchLabelColor,this.gitBranchLabel7=this.branchLabelColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||kt,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||Tt}calculate(t){if("object"!=typeof t)return void this.updateColors();const e=Object.keys(t);e.forEach((e=>{this[e]=t[e]})),this.updateColors(),e.forEach((e=>{this[e]=t[e]}))}}const Lt={base:{getThemeVariables:t=>{const e=new _t;return e.calculate(t),e}},dark:{getThemeVariables:t=>{const e=new St;return e.calculate(t),e}},default:{getThemeVariables:Ft},forest:{getThemeVariables:t=>{const e=new wt;return e.calculate(t),e}},neutral:{getThemeVariables:t=>{const e=new At;return e.calculate(t),e}}},Zt={flowchart:{useMaxWidth:!0,titleTopMargin:25,diagramPadding:8,htmlLabels:!0,nodeSpacing:50,rankSpacing:50,curve:"basis",padding:15,defaultRenderer:"dagre-wrapper",wrappingWidth:200},sequence:{useMaxWidth:!0,hideUnusedParticipants:!1,activationWidth:10,diagramMarginX:50,diagramMarginY:10,actorMargin:50,width:150,height:65,boxMargin:10,boxTextMargin:5,noteMargin:10,messageMargin:35,messageAlign:"center",mirrorActors:!0,forceMenus:!1,bottomMarginAdj:1,rightAngles:!1,showSequenceNumbers:!1,actorFontSize:14,actorFontFamily:'"Open Sans", sans-serif',actorFontWeight:400,noteFontSize:14,noteFontFamily:'"trebuchet ms", verdana, arial, sans-serif',noteFontWeight:400,noteAlign:"center",messageFontSize:16,messageFontFamily:'"trebuchet ms", verdana, arial, sans-serif',messageFontWeight:400,wrap:!1,wrapPadding:10,labelBoxWidth:50,labelBoxHeight:20},gantt:{useMaxWidth:!0,titleTopMargin:25,barHeight:20,barGap:4,topPadding:50,rightPadding:75,leftPadding:75,gridLineStartPadding:35,fontSize:11,sectionFontSize:11,numberSectionStyles:4,axisFormat:"%Y-%m-%d",topAxis:!1,displayMode:"",weekday:"sunday"},journey:{useMaxWidth:!0,diagramMarginX:50,diagramMarginY:10,leftMargin:150,width:150,height:50,boxMargin:10,boxTextMargin:5,noteMargin:10,messageMargin:35,messageAlign:"center",bottomMarginAdj:1,rightAngles:!1,taskFontSize:14,taskFontFamily:'"Open Sans", sans-serif',taskMargin:50,activationWidth:10,textPlacement:"fo",actorColours:["#8FBC8F","#7CFC00","#00FFFF","#20B2AA","#B0E0E6","#FFFFE0"],sectionFills:["#191970","#8B008B","#4B0082","#2F4F4F","#800000","#8B4513","#00008B"],sectionColours:["#fff"]},class:{useMaxWidth:!0,titleTopMargin:25,arrowMarkerAbsolute:!1,dividerMargin:10,padding:5,textHeight:10,defaultRenderer:"dagre-wrapper",htmlLabels:!1},state:{useMaxWidth:!0,titleTopMargin:25,dividerMargin:10,sizeUnit:5,padding:8,textHeight:10,titleShift:-15,noteMargin:10,forkWidth:70,forkHeight:7,miniPadding:2,fontSizeFactor:5.02,fontSize:24,labelHeight:16,edgeLengthFactor:"20",compositTitleSize:35,radius:5,defaultRenderer:"dagre-wrapper"},er:{useMaxWidth:!0,titleTopMargin:25,diagramPadding:20,layoutDirection:"TB",minEntityWidth:100,minEntityHeight:75,entityPadding:15,stroke:"gray",fill:"honeydew",fontSize:12},pie:{useMaxWidth:!0,textPosition:.75},quadrantChart:{useMaxWidth:!0,chartWidth:500,chartHeight:500,titleFontSize:20,titlePadding:10,quadrantPadding:5,xAxisLabelPadding:5,yAxisLabelPadding:5,xAxisLabelFontSize:16,yAxisLabelFontSize:16,quadrantLabelFontSize:16,quadrantTextTopPadding:5,pointTextPadding:5,pointLabelFontSize:12,pointRadius:5,xAxisPosition:"top",yAxisPosition:"left",quadrantInternalBorderStrokeWidth:1,quadrantExternalBorderStrokeWidth:2},xyChart:{useMaxWidth:!0,width:700,height:500,titleFontSize:20,titlePadding:10,showTitle:!0,xAxis:{$ref:"#/$defs/XYChartAxisConfig",showLabel:!0,labelFontSize:14,labelPadding:5,showTitle:!0,titleFontSize:16,titlePadding:5,showTick:!0,tickLength:5,tickWidth:2,showAxisLine:!0,axisLineWidth:2},yAxis:{$ref:"#/$defs/XYChartAxisConfig",showLabel:!0,labelFontSize:14,labelPadding:5,showTitle:!0,titleFontSize:16,titlePadding:5,showTick:!0,tickLength:5,tickWidth:2,showAxisLine:!0,axisLineWidth:2},chartOrientation:"vertical",plotReservedSpacePercent:50},requirement:{useMaxWidth:!0,rect_fill:"#f9f9f9",text_color:"#333",rect_border_size:"0.5px",rect_border_color:"#bbb",rect_min_width:200,rect_min_height:200,fontSize:14,rect_padding:10,line_height:20},mindmap:{useMaxWidth:!0,padding:10,maxNodeWidth:200},timeline:{useMaxWidth:!0,diagramMarginX:50,diagramMarginY:10,leftMargin:150,width:150,height:50,boxMargin:10,boxTextMargin:5,noteMargin:10,messageMargin:35,messageAlign:"center",bottomMarginAdj:1,rightAngles:!1,taskFontSize:14,taskFontFamily:'"Open Sans", sans-serif',taskMargin:50,activationWidth:10,textPlacement:"fo",actorColours:["#8FBC8F","#7CFC00","#00FFFF","#20B2AA","#B0E0E6","#FFFFE0"],sectionFills:["#191970","#8B008B","#4B0082","#2F4F4F","#800000","#8B4513","#00008B"],sectionColours:["#fff"],disableMulticolor:!1},gitGraph:{useMaxWidth:!0,titleTopMargin:25,diagramPadding:8,nodeLabel:{width:75,height:100,x:-25,y:0},mainBranchName:"main",mainBranchOrder:0,showCommitLabel:!0,showBranches:!0,rotateCommitLabel:!0,arrowMarkerAbsolute:!1},c4:{useMaxWidth:!0,diagramMarginX:50,diagramMarginY:10,c4ShapeMargin:50,c4ShapePadding:20,width:216,height:60,boxMargin:10,c4ShapeInRow:4,nextLinePaddingX:0,c4BoundaryInRow:2,personFontSize:14,personFontFamily:'"Open Sans", sans-serif',personFontWeight:"normal",external_personFontSize:14,external_personFontFamily:'"Open Sans", sans-serif',external_personFontWeight:"normal",systemFontSize:14,systemFontFamily:'"Open Sans", sans-serif',systemFontWeight:"normal",external_systemFontSize:14,external_systemFontFamily:'"Open Sans", sans-serif',external_systemFontWeight:"normal",system_dbFontSize:14,system_dbFontFamily:'"Open Sans", sans-serif',system_dbFontWeight:"normal",external_system_dbFontSize:14,external_system_dbFontFamily:'"Open Sans", sans-serif',external_system_dbFontWeight:"normal",system_queueFontSize:14,system_queueFontFamily:'"Open Sans", sans-serif',system_queueFontWeight:"normal",external_system_queueFontSize:14,external_system_queueFontFamily:'"Open Sans", sans-serif',external_system_queueFontWeight:"normal",boundaryFontSize:14,boundaryFontFamily:'"Open Sans", sans-serif',boundaryFontWeight:"normal",messageFontSize:12,messageFontFamily:'"Open Sans", sans-serif',messageFontWeight:"normal",containerFontSize:14,containerFontFamily:'"Open Sans", sans-serif',containerFontWeight:"normal",external_containerFontSize:14,external_containerFontFamily:'"Open Sans", sans-serif',external_containerFontWeight:"normal",container_dbFontSize:14,container_dbFontFamily:'"Open Sans", sans-serif',container_dbFontWeight:"normal",external_container_dbFontSize:14,external_container_dbFontFamily:'"Open Sans", sans-serif',external_container_dbFontWeight:"normal",container_queueFontSize:14,container_queueFontFamily:'"Open Sans", sans-serif',container_queueFontWeight:"normal",external_container_queueFontSize:14,external_container_queueFontFamily:'"Open Sans", sans-serif',external_container_queueFontWeight:"normal",componentFontSize:14,componentFontFamily:'"Open Sans", sans-serif',componentFontWeight:"normal",external_componentFontSize:14,external_componentFontFamily:'"Open Sans", sans-serif',external_componentFontWeight:"normal",component_dbFontSize:14,component_dbFontFamily:'"Open Sans", sans-serif',component_dbFontWeight:"normal",external_component_dbFontSize:14,external_component_dbFontFamily:'"Open Sans", sans-serif',external_component_dbFontWeight:"normal",component_queueFontSize:14,component_queueFontFamily:'"Open Sans", sans-serif',component_queueFontWeight:"normal",external_component_queueFontSize:14,external_component_queueFontFamily:'"Open Sans", sans-serif',external_component_queueFontWeight:"normal",wrap:!0,wrapPadding:10,person_bg_color:"#08427B",person_border_color:"#073B6F",external_person_bg_color:"#686868",external_person_border_color:"#8A8A8A",system_bg_color:"#1168BD",system_border_color:"#3C7FC0",system_db_bg_color:"#1168BD",system_db_border_color:"#3C7FC0",system_queue_bg_color:"#1168BD",system_queue_border_color:"#3C7FC0",external_system_bg_color:"#999999",external_system_border_color:"#8A8A8A",external_system_db_bg_color:"#999999",external_system_db_border_color:"#8A8A8A",external_system_queue_bg_color:"#999999",external_system_queue_border_color:"#8A8A8A",container_bg_color:"#438DD5",container_border_color:"#3C7FC0",container_db_bg_color:"#438DD5",container_db_border_color:"#3C7FC0",container_queue_bg_color:"#438DD5",container_queue_border_color:"#3C7FC0",external_container_bg_color:"#B3B3B3",external_container_border_color:"#A6A6A6",external_container_db_bg_color:"#B3B3B3",external_container_db_border_color:"#A6A6A6",external_container_queue_bg_color:"#B3B3B3",external_container_queue_border_color:"#A6A6A6",component_bg_color:"#85BBF0",component_border_color:"#78A8D8",component_db_bg_color:"#85BBF0",component_db_border_color:"#78A8D8",component_queue_bg_color:"#85BBF0",component_queue_border_color:"#78A8D8",external_component_bg_color:"#CCCCCC",external_component_border_color:"#BFBFBF",external_component_db_bg_color:"#CCCCCC",external_component_db_border_color:"#BFBFBF",external_component_queue_bg_color:"#CCCCCC",external_component_queue_border_color:"#BFBFBF"},sankey:{useMaxWidth:!0,width:600,height:400,linkColor:"gradient",nodeAlignment:"justify",showValues:!0,prefix:"",suffix:""},theme:"default",maxTextSize:5e4,maxEdges:500,darkMode:!1,fontFamily:'"trebuchet ms", verdana, arial, sans-serif;',logLevel:5,securityLevel:"strict",startOnLoad:!0,arrowMarkerAbsolute:!1,secure:["secure","securityLevel","startOnLoad","maxTextSize","maxEdges"],deterministicIds:!1,fontSize:16},Et={...Zt,deterministicIDSeed:void 0,themeCSS:void 0,themeVariables:Lt.default.getThemeVariables(),sequence:{...Zt.sequence,messageFont:function(){return{fontFamily:this.messageFontFamily,fontSize:this.messageFontSize,fontWeight:this.messageFontWeight}},noteFont:function(){return{fontFamily:this.noteFontFamily,fontSize:this.noteFontSize,fontWeight:this.noteFontWeight}},actorFont:function(){return{fontFamily:this.actorFontFamily,fontSize:this.actorFontSize,fontWeight:this.actorFontWeight}}},gantt:{...Zt.gantt,tickInterval:void 0,useWidth:void 0},c4:{...Zt.c4,useWidth:void 0,personFont:function(){return{fontFamily:this.personFontFamily,fontSize:this.personFontSize,fontWeight:this.personFontWeight}},external_personFont:function(){return{fontFamily:this.external_personFontFamily,fontSize:this.external_personFontSize,fontWeight:this.external_personFontWeight}},systemFont:function(){return{fontFamily:this.systemFontFamily,fontSize:this.systemFontSize,fontWeight:this.systemFontWeight}},external_systemFont:function(){return{fontFamily:this.external_systemFontFamily,fontSize:this.external_systemFontSize,fontWeight:this.external_systemFontWeight}},system_dbFont:function(){return{fontFamily:this.system_dbFontFamily,fontSize:this.system_dbFontSize,fontWeight:this.system_dbFontWeight}},external_system_dbFont:function(){return{fontFamily:this.external_system_dbFontFamily,fontSize:this.external_system_dbFontSize,fontWeight:this.external_system_dbFontWeight}},system_queueFont:function(){return{fontFamily:this.system_queueFontFamily,fontSize:this.system_queueFontSize,fontWeight:this.system_queueFontWeight}},external_system_queueFont:function(){return{fontFamily:this.external_system_queueFontFamily,fontSize:this.external_system_queueFontSize,fontWeight:this.external_system_queueFontWeight}},containerFont:function(){return{fontFamily:this.containerFontFamily,fontSize:this.containerFontSize,fontWeight:this.containerFontWeight}},external_containerFont:function(){return{fontFamily:this.external_containerFontFamily,fontSize:this.external_containerFontSize,fontWeight:this.external_containerFontWeight}},container_dbFont:function(){return{fontFamily:this.container_dbFontFamily,fontSize:this.container_dbFontSize,fontWeight:this.container_dbFontWeight}},external_container_dbFont:function(){return{fontFamily:this.external_container_dbFontFamily,fontSize:this.external_container_dbFontSize,fontWeight:this.external_container_dbFontWeight}},container_queueFont:function(){return{fontFamily:this.container_queueFontFamily,fontSize:this.container_queueFontSize,fontWeight:this.container_queueFontWeight}},external_container_queueFont:function(){return{fontFamily:this.external_container_queueFontFamily,fontSize:this.external_container_queueFontSize,fontWeight:this.external_container_queueFontWeight}},componentFont:function(){return{fontFamily:this.componentFontFamily,fontSize:this.componentFontSize,fontWeight:this.componentFontWeight}},external_componentFont:function(){return{fontFamily:this.external_componentFontFamily,fontSize:this.external_componentFontSize,fontWeight:this.external_componentFontWeight}},component_dbFont:function(){return{fontFamily:this.component_dbFontFamily,fontSize:this.component_dbFontSize,fontWeight:this.component_dbFontWeight}},external_component_dbFont:function(){return{fontFamily:this.external_component_dbFontFamily,fontSize:this.external_component_dbFontSize,fontWeight:this.external_component_dbFontWeight}},component_queueFont:function(){return{fontFamily:this.component_queueFontFamily,fontSize:this.component_queueFontSize,fontWeight:this.component_queueFontWeight}},external_component_queueFont:function(){return{fontFamily:this.external_component_queueFontFamily,fontSize:this.external_component_queueFontSize,fontWeight:this.external_component_queueFontWeight}},boundaryFont:function(){return{fontFamily:this.boundaryFontFamily,fontSize:this.boundaryFontSize,fontWeight:this.boundaryFontWeight}},messageFont:function(){return{fontFamily:this.messageFontFamily,fontSize:this.messageFontSize,fontWeight:this.messageFontWeight}}},pie:{...Zt.pie,useWidth:984},xyChart:{...Zt.xyChart,useWidth:void 0},requirement:{...Zt.requirement,useWidth:void 0},gitGraph:{...Zt.gitGraph,useMaxWidth:!1},sankey:{...Zt.sankey,useMaxWidth:!1}},Mt=(t,e="")=>Object.keys(t).reduce(((i,r)=>Array.isArray(t[r])?i:"object"==typeof t[r]&&null!==t[r]?[...i,e+r,...Mt(t[r],"")]:[...i,e+r]),[]),Ot=new Set(Mt(Et,"")),It=Et,qt=t=>{if(at.debug("sanitizeDirective called with",t),"object"==typeof t&&null!=t)if(Array.isArray(t))t.forEach((t=>qt(t)));else{for(const e of Object.keys(t)){if(at.debug("Checking key",e),e.startsWith("__")||e.includes("proto")||e.includes("constr")||!Ot.has(e)||null==t[e]){at.debug("sanitize deleting key: ",e),delete t[e];continue}if("object"==typeof t[e]){at.debug("sanitizing object",e),qt(t[e]);continue}const i=["themeCSS","fontFamily","altFontFamily"];for(const r of i)e.includes(r)&&(at.debug("sanitizing css option",e),t[e]=Dt(t[e]))}if(t.themeVariables)for(const e of Object.keys(t.themeVariables)){const i=t.themeVariables[e];(null==i?void 0:i.match)&&!i.match(/^[\d "#%(),.;A-Za-z]+$/)&&(t.themeVariables[e]="")}at.debug("After sanitization",t)}},Dt=t=>{let e=0,i=0;for(const r of t){if(e<i)return"{ /* ERROR: Unbalanced CSS */ }";"{"===r?e++:"}"===r&&i++}return e!==i?"{ /* ERROR: Unbalanced CSS */ }":t},zt=/^-{3}\s*[\n\r](.*?)[\n\r]-{3}\s*[\n\r]+/s,Nt=/%{2}{\s*(?:(\w+)\s*:|(\w+))\s*(?:(\w+)|((?:(?!}%{2}).|\r?\n)*))?\s*(?:}%{2})?/gi,Pt=/\s*%%.*\n/gm;class $t extends Error{constructor(t){super(t),this.name="UnknownDiagramError"}}const jt={},Rt=function(t,e){t=t.replace(zt,"").replace(Nt,"").replace(Pt,"\n");for(const[i,{detector:r}]of Object.entries(jt))if(r(t,e))return i;throw new $t(`No diagram type detected matching given configuration for text: ${t}`)},Wt=(...t)=>{for(const{id:e,detector:i,loader:r}of t)Ht(e,i,r)},Ht=(t,e,i)=>{jt[t]?at.error(`Detector with key ${t} already exists`):jt[t]={detector:e,loader:i},at.debug(`Detector with key ${t} added${i?" with loader":""}`)},Ut=(t,e,{depth:i=2,clobber:r=!1}={})=>{const o={depth:i,clobber:r};return Array.isArray(e)&&!Array.isArray(t)?(e.forEach((e=>Ut(t,e,o))),t):Array.isArray(e)&&Array.isArray(t)?(e.forEach((e=>{t.includes(e)||t.push(e)})),t):void 0===t||i<=0?null!=t&&"object"==typeof t&&"object"==typeof e?Object.assign(t,e):e:(void 0!==e&&"object"==typeof t&&"object"==typeof e&&Object.keys(e).forEach((o=>{"object"!=typeof e[o]||void 0!==t[o]&&"object"!=typeof t[o]?(r||"object"!=typeof t[o]&&"object"!=typeof e[o])&&(t[o]=e[o]):(void 0===t[o]&&(t[o]=Array.isArray(e[o])?[]:{}),t[o]=Ut(t[o],e[o],{depth:i-1,clobber:r}))})),t)},Yt=Ut,Vt="​",Gt={curveBasis:s.$0Z,curveBasisClosed:s.Dts,curveBasisOpen:s.WQY,curveBumpX:s.qpX,curveBumpY:s.u93,curveBundle:s.tFB,curveCardinalClosed:s.OvA,curveCardinalOpen:s.dCK,curveCardinal:s.YY7,curveCatmullRomClosed:s.fGX,curveCatmullRomOpen:s.$m7,curveCatmullRom:s.zgE,curveLinear:s.c_6,curveLinearClosed:s.fxm,curveMonotoneX:s.FdL,curveMonotoneY:s.ak_,curveNatural:s.SxZ,curveStep:s.eA_,curveStepAfter:s.jsv,curveStepBefore:s.iJ},Xt=/\s*(?:(\w+)(?=:):|(\w+))\s*(?:(\w+)|((?:(?!}%{2}).|\r?\n)*))?\s*(?:}%{2})?/gi,Jt=function(t,e=null){try{const i=new RegExp(`[%]{2}(?![{]${Xt.source})(?=[}][%]{2}).*\n`,"ig");let r;t=t.trim().replace(i,"").replace(/'/gm,'"'),at.debug(`Detecting diagram directive${null!==e?" type:"+e:""} based on the text:${t}`);const o=[];for(;null!==(r=Nt.exec(t));)if(r.index===Nt.lastIndex&&Nt.lastIndex++,r&&!e||e&&r[1]&&r[1].match(e)||e&&r[2]&&r[2].match(e)){const t=r[1]?r[1]:r[2],e=r[3]?r[3].trim():r[4]?JSON.parse(r[4].trim()):null;o.push({type:t,args:e})}return 0===o.length?{type:t,args:null}:1===o.length?o[0]:o}catch(i){return at.error(`ERROR: ${i.message} - Unable to parse directive type: '${e}' based on the text: '${t}'`),{type:void 0,args:null}}};function Qt(t,e){if(!t)return e;const i=`curve${t.charAt(0).toUpperCase()+t.slice(1)}`;return Gt[i]??e}function Kt(t,e){return t&&e?Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2)):0}const te=(t,e=2)=>{const i=Math.pow(10,e);return Math.round(t*i)/i},ee=(t,e)=>{let i,r=e;for(const e of t){if(i){const t=Kt(e,i);if(t<r)r-=t;else{const o=r/t;if(o<=0)return i;if(o>=1)return{x:e.x,y:e.y};if(o>0&&o<1)return{x:te((1-o)*i.x+o*e.x,5),y:te((1-o)*i.y+o*e.y,5)}}}i=e}throw new Error("Could not find a suitable point for the given distance")};function ie(t){let e="",i="";for(const r of t)void 0!==r&&(r.startsWith("color:")||r.startsWith("text-align:")?i=i+r+";":e=e+r+";");return{style:e,labelStyle:i}}let re=0;const oe=()=>(re++,"id-"+Math.random().toString(36).substr(2,12)+"-"+re),ne=t=>function(t){let e="";for(let i=0;i<t;i++)e+="0123456789abcdef".charAt(Math.floor(16*Math.random()));return e}(t.length),ae=function(t,e){const i=e.text.replace(xt.lineBreakRegex," "),[,r]=pe(e.fontSize),o=t.append("text");o.attr("x",e.x),o.attr("y",e.y),o.style("text-anchor",e.anchor),o.style("font-family",e.fontFamily),o.style("font-size",r),o.style("font-weight",e.fontWeight),o.attr("fill",e.fill),void 0!==e.class&&o.attr("class",e.class);const n=o.append("tspan");return n.attr("x",e.x+2*e.textMargin),n.attr("fill",e.fill),n.text(i),o},se=(0,C.Z)(((t,e,i)=>{if(!t)return t;if(i=Object.assign({fontSize:12,fontWeight:400,fontFamily:"Arial",joinWith:"<br/>"},i),xt.lineBreakRegex.test(t))return t;const r=t.split(" "),o=[];let n="";return r.forEach(((t,a)=>{const s=ce(`${t} `,i),l=ce(n,i);if(s>e){const{hyphenatedStrings:r,remainingWord:a}=le(t,e,"-",i);o.push(n,...r),n=a}else l+s>=e?(o.push(n),n=t):n=[n,t].filter(Boolean).join(" ");a+1===r.length&&o.push(n)})),o.filter((t=>""!==t)).join(i.joinWith)}),((t,e,i)=>`${t}${e}${i.fontSize}${i.fontWeight}${i.fontFamily}${i.joinWith}`)),le=(0,C.Z)(((t,e,i="-",r)=>{r=Object.assign({fontSize:12,fontWeight:400,fontFamily:"Arial",margin:0},r);const o=[...t],n=[];let a="";return o.forEach(((t,s)=>{const l=`${a}${t}`;if(ce(l,r)>=e){const t=s+1,e=o.length===t,r=`${l}${i}`;n.push(e?l:r),a=""}else a=l})),{hyphenatedStrings:n,remainingWord:a}}),((t,e,i="-",r)=>`${t}${e}${i}${r.fontSize}${r.fontWeight}${r.fontFamily}`));function he(t,e){return ue(t,e).height}function ce(t,e){return ue(t,e).width}const ue=(0,C.Z)(((t,e)=>{const{fontSize:i=12,fontFamily:r="Arial",fontWeight:o=400}=e;if(!t)return{width:0,height:0};const[,n]=pe(i),a=["sans-serif",r],l=t.split(xt.lineBreakRegex),h=[],c=(0,s.Ys)("body");if(!c.remove)return{width:0,height:0,lineHeight:0};const u=c.append("svg");for(const t of a){let e=0;const i={width:0,height:0,lineHeight:0};for(const r of l){const a={x:0,y:0,fill:void 0,anchor:"start",style:"#666",width:100,height:100,textMargin:0,rx:0,ry:0,valign:void 0,text:""};a.text=r||Vt;const s=ae(u,a).style("font-size",n).style("font-weight",o).style("font-family",t),l=(s._groups||s)[0][0].getBBox();if(0===l.width&&0===l.height)throw new Error("svg element not in render tree");i.width=Math.round(Math.max(i.width,l.width)),e=Math.round(l.height),i.height+=e,i.lineHeight=Math.round(Math.max(i.lineHeight,e))}h.push(i)}return u.remove(),h[isNaN(h[1].height)||isNaN(h[1].width)||isNaN(h[1].lineHeight)||h[0].height>h[1].height&&h[0].width>h[1].width&&h[0].lineHeight>h[1].lineHeight?0:1]}),((t,e)=>`${t}${e.fontSize}${e.fontWeight}${e.fontFamily}`));let de;function fe(t){return"str"in t}const pe=t=>{if("number"==typeof t)return[t,t+"px"];const e=parseInt(t??"",10);return Number.isNaN(e)?[void 0,void 0]:t===String(e)?[e,t+"px"]:[e,t]};function ge(t,e){return(0,b.Z)({},t,e)}const me={assignWithDepth:Yt,wrapLabel:se,calculateTextHeight:he,calculateTextWidth:ce,calculateTextDimensions:ue,cleanAndMerge:ge,detectInit:function(t,e){const i=Jt(t,/(?:init\b)|(?:initialize\b)/);let r={};if(Array.isArray(i)){const t=i.map((t=>t.args));qt(t),r=Yt(r,[...t])}else r=i.args;if(!r)return;let o=Rt(t,e);const n="config";return void 0!==r[n]&&("flowchart-v2"===o&&(o="flowchart"),r[o]=r[n],delete r[n]),r},detectDirective:Jt,isSubstringInArray:function(t,e){for(const[i,r]of e.entries())if(r.match(t))return i;return-1},interpolateToCurve:Qt,calcLabelPosition:function(t){return 1===t.length?t[0]:function(t){let e,i=0;return t.forEach((t=>{i+=Kt(t,e),e=t})),ee(t,i/2)}(t)},calcCardinalityPosition:(t,e,i)=>{at.info(`our points ${JSON.stringify(e)}`),e[0]!==i&&(e=e.reverse());const r=ee(e,25),o=t?10:5,n=Math.atan2(e[0].y-r.y,e[0].x-r.x),a={x:0,y:0};return a.x=Math.sin(n)*o+(e[0].x+r.x)/2,a.y=-Math.cos(n)*o+(e[0].y+r.y)/2,a},calcTerminalLabelPosition:function(t,e,i){const r=structuredClone(i);at.info("our points",r),"start_left"!==e&&"start_right"!==e&&r.reverse();const o=ee(r,25+t),n=10+.5*t,a=Math.atan2(r[0].y-o.y,r[0].x-o.x),s={x:0,y:0};return"start_left"===e?(s.x=Math.sin(a+Math.PI)*n+(r[0].x+o.x)/2,s.y=-Math.cos(a+Math.PI)*n+(r[0].y+o.y)/2):"end_right"===e?(s.x=Math.sin(a-Math.PI)*n+(r[0].x+o.x)/2-5,s.y=-Math.cos(a-Math.PI)*n+(r[0].y+o.y)/2-5):"end_left"===e?(s.x=Math.sin(a)*n+(r[0].x+o.x)/2-5,s.y=-Math.cos(a)*n+(r[0].y+o.y)/2-5):(s.x=Math.sin(a)*n+(r[0].x+o.x)/2,s.y=-Math.cos(a)*n+(r[0].y+o.y)/2),s},formatUrl:function(t,e){const i=t.trim();if(i)return"loose"!==e.securityLevel?(0,a.Nm)(i):i},getStylesFromArray:ie,generateId:oe,random:ne,runFunc:(t,...e)=>{const i=t.split("."),r=i.length-1,o=i[r];let n=window;for(let e=0;e<r;e++)if(n=n[i[e]],!n)return void at.error(`Function name: ${t} not found in window`);n[o](...e)},entityDecode:function(t){return de=de||document.createElement("div"),t=escape(t).replace(/%26/g,"&").replace(/%23/g,"#").replace(/%3B/g,";"),de.innerHTML=t,unescape(de.textContent)},insertTitle:(t,e,i,r)=>{var o;if(!r)return;const n=null==(o=t.node())?void 0:o.getBBox();n&&t.append("text").text(r).attr("x",n.x+n.width/2).attr("y",-i).attr("class",e)},parseFontSize:pe,InitIDGenerator:class{constructor(t=!1,e){this.count=0,this.count=e?e.length:0,this.next=t?()=>this.count++:()=>Date.now()}}},ye=function(t){return t.replace(/ﬂ°°/g,"&#").replace(/ﬂ°/g,"&").replace(/¶ß/g,";")},Ce="10.7.0",be=Object.freeze(It);let xe,ve=Yt({},be),ke=[],Te=Yt({},be);const _e=(t,e)=>{let i=Yt({},t),r={};for(const t of e)we(t),r=Yt(r,t);if(i=Yt(i,r),r.theme&&r.theme in Lt){const t=Yt({},xe),e=Yt(t.themeVariables||{},r.themeVariables);i.theme&&i.theme in Lt&&(i.themeVariables=Lt[i.theme].getThemeVariables(e))}return Te=i,Ee(Te),Te},Se=()=>Yt({},ve),Be=t=>(Ee(t),Yt(Te,t),Fe()),Fe=()=>Yt({},Te),we=t=>{t&&(["secure",...ve.secure??[]].forEach((e=>{Object.hasOwn(t,e)&&(at.debug(`Denied attempt to modify a secure key ${e}`,t[e]),delete t[e])})),Object.keys(t).forEach((e=>{e.startsWith("__")&&delete t[e]})),Object.keys(t).forEach((e=>{"string"==typeof t[e]&&(t[e].includes("<")||t[e].includes(">")||t[e].includes("url(data:"))&&delete t[e],"object"==typeof t[e]&&we(t[e])})))},Ae=(t=ve)=>{ke=[],_e(t,ke)},Le={LAZY_LOAD_DEPRECATED:"The configuration options lazyLoadedDiagrams and loadExternalDiagramsAtStartup are deprecated. Please use registerExternalDiagrams instead."},Ze={},Ee=t=>{var e;t&&(t.lazyLoadedDiagrams||t.loadExternalDiagramsAtStartup)&&(Ze[e="LAZY_LOAD_DEPRECATED"]||(at.warn(Le[e]),Ze[e]=!0))},Me={id:"c4",detector:t=>/^\s*C4Context|C4Container|C4Component|C4Dynamic|C4Deployment/.test(t),loader:async()=>{const{diagram:t}=await i.e(7427).then(i.bind(i,77427));return{id:"c4",diagram:t}}},Oe="flowchart",Ie={id:Oe,detector:(t,e)=>{var i,r;return"dagre-wrapper"!==(null==(i=null==e?void 0:e.flowchart)?void 0:i.defaultRenderer)&&"elk"!==(null==(r=null==e?void 0:e.flowchart)?void 0:r.defaultRenderer)&&/^\s*graph/.test(t)},loader:async()=>{const{diagram:t}=await Promise.all([i.e(7259),i.e(4780),i.e(8443),i.e(6281),i.e(1261),i.e(6417)]).then(i.bind(i,66417));return{id:Oe,diagram:t}}},qe="flowchart-v2",De={id:qe,detector:(t,e)=>{var i,r,o;return"dagre-d3"!==(null==(i=null==e?void 0:e.flowchart)?void 0:i.defaultRenderer)&&"elk"!==(null==(r=null==e?void 0:e.flowchart)?void 0:r.defaultRenderer)&&(!(!/^\s*graph/.test(t)||"dagre-wrapper"!==(null==(o=null==e?void 0:e.flowchart)?void 0:o.defaultRenderer))||/^\s*flowchart/.test(t))},loader:async()=>{const{diagram:t}=await Promise.all([i.e(7259),i.e(4780),i.e(8443),i.e(6281),i.e(1261),i.e(7153)]).then(i.bind(i,57153));return{id:qe,diagram:t}}},ze={id:"er",detector:t=>/^\s*erDiagram/.test(t),loader:async()=>{const{diagram:t}=await Promise.all([i.e(7259),i.e(2692)]).then(i.bind(i,22692));return{id:"er",diagram:t}}},Ne="gitGraph",Pe={id:Ne,detector:t=>/^\s*gitGraph/.test(t),loader:async()=>{const{diagram:t}=await i.e(261).then(i.bind(i,80261));return{id:Ne,diagram:t}}},$e="gantt",je={id:$e,detector:t=>/^\s*gantt/.test(t),loader:async()=>{const{diagram:t}=await i.e(3488).then(i.bind(i,63488));return{id:$e,diagram:t}}},Re="info",We={id:Re,detector:t=>/^\s*info/.test(t),loader:async()=>{const{diagram:t}=await i.e(8768).then(i.bind(i,98768));return{id:Re,diagram:t}}},He={id:"pie",detector:t=>/^\s*pie/.test(t),loader:async()=>{const{diagram:t}=await i.e(6667).then(i.bind(i,16667));return{id:"pie",diagram:t}}},Ue="quadrantChart",Ye={id:Ue,detector:t=>/^\s*quadrantChart/.test(t),loader:async()=>{const{diagram:t}=await i.e(2520).then(i.bind(i,62520));return{id:Ue,diagram:t}}},Ve="xychart",Ge={id:Ve,detector:t=>/^\s*xychart-beta/.test(t),loader:async()=>{const{diagram:t}=await Promise.all([i.e(4780),i.e(9866)]).then(i.bind(i,59866));return{id:Ve,diagram:t}}},Xe="requirement",Je={id:Xe,detector:t=>/^\s*requirement(Diagram)?/.test(t),loader:async()=>{const{diagram:t}=await Promise.all([i.e(7259),i.e(7097)]).then(i.bind(i,87097));return{id:Xe,diagram:t}}},Qe="sequence",Ke={id:Qe,detector:t=>/^\s*sequenceDiagram/.test(t),loader:async()=>{const{diagram:t}=await i.e(2241).then(i.bind(i,32241));return{id:Qe,diagram:t}}},ti="class",ei={id:ti,detector:(t,e)=>{var i;return"dagre-wrapper"!==(null==(i=null==e?void 0:e.class)?void 0:i.defaultRenderer)&&/^\s*classDiagram/.test(t)},loader:async()=>{const{diagram:t}=await Promise.all([i.e(7259),i.e(7010),i.e(1388)]).then(i.bind(i,91388));return{id:ti,diagram:t}}},ii="classDiagram",ri={id:ii,detector:(t,e)=>{var i;return!(!/^\s*classDiagram/.test(t)||"dagre-wrapper"!==(null==(i=null==e?void 0:e.class)?void 0:i.defaultRenderer))||/^\s*classDiagram-v2/.test(t)},loader:async()=>{const{diagram:t}=await Promise.all([i.e(7259),i.e(4780),i.e(8443),i.e(6281),i.e(7010),i.e(1846)]).then(i.bind(i,11846));return{id:ii,diagram:t}}},oi="state",ni={id:oi,detector:(t,e)=>{var i;return"dagre-wrapper"!==(null==(i=null==e?void 0:e.state)?void 0:i.defaultRenderer)&&/^\s*stateDiagram/.test(t)},loader:async()=>{const{diagram:t}=await Promise.all([i.e(7259),i.e(5343),i.e(6640)]).then(i.bind(i,6640));return{id:oi,diagram:t}}},ai="stateDiagram",si={id:ai,detector:(t,e)=>{var i;return!!/^\s*stateDiagram-v2/.test(t)||!(!/^\s*stateDiagram/.test(t)||"dagre-wrapper"!==(null==(i=null==e?void 0:e.state)?void 0:i.defaultRenderer))},loader:async()=>{const{diagram:t}=await Promise.all([i.e(7259),i.e(4780),i.e(8443),i.e(6281),i.e(5343),i.e(3154)]).then(i.bind(i,43154));return{id:ai,diagram:t}}},li="journey",hi={id:li,detector:t=>/^\s*journey/.test(t),loader:async()=>{const{diagram:t}=await i.e(4035).then(i.bind(i,74035));return{id:li,diagram:t}}},ci=function(t,e,i,r){const o=function(t,e,i){let r=new Map;return i?(r.set("width","100%"),r.set("style",`max-width: ${e}px;`)):(r.set("height",t),r.set("width",e)),r}(e,i,r);!function(t,e){for(let i of e)t.attr(i[0],i[1])}(t,o)},ui=function(t,e,i,r){const o=e.node().getBBox(),n=o.width,a=o.height;at.info(`SVG bounds: ${n}x${a}`,o);let s=0,l=0;at.info(`Graph bounds: ${s}x${l}`,t),s=n+2*i,l=a+2*i,at.info(`Calculated bounds: ${s}x${l}`),ci(e,l,s,r);const h=`${o.x-i} ${o.y-i} ${o.width+2*i} ${o.height+2*i}`;e.attr("viewBox",h)},di={};let fi="",pi="",gi="";const mi=t=>dt(t,Fe()),yi=()=>{fi="",gi="",pi=""},Ci=t=>{fi=mi(t).replace(/^\s+/g,"")},bi=()=>fi,xi=t=>{gi=mi(t).replace(/\n\s+/g,"\n")},vi=()=>gi,ki=t=>{pi=mi(t)},Ti=()=>pi,_i=Object.freeze(Object.defineProperty({__proto__:null,clear:yi,getAccDescription:vi,getAccTitle:bi,getDiagramTitle:Ti,setAccDescription:xi,setAccTitle:Ci,setDiagramTitle:ki},Symbol.toStringTag,{value:"Module"})),Si=at,Bi=st,Fi=Fe,wi=Be,Ai=be,Li=t=>dt(t,Fi()),Zi=ui,Ei={},Mi=(t,e,i)=>{var r,o,n;if(Ei[t])throw new Error(`Diagram ${t} already registered.`);Ei[t]=e,i&&Ht(t,i),o=t,void 0!==(n=e.styles)&&(di[o]=n),null==(r=e.injectUtils)||r.call(e,Si,Bi,Fi,Li,Zi,_i,(()=>{}))},Oi=t=>{if(t in Ei)return Ei[t];throw new Ii(t)};class Ii extends Error{constructor(t){super(`Diagram ${t} not found.`)}}const qi=t=>{var e;const{securityLevel:i}=Fi();let r=(0,s.Ys)("body");if("sandbox"===i){const i=(null==(e=(0,s.Ys)(`#i${t}`).node())?void 0:e.contentDocument)??document;r=(0,s.Ys)(i.body)}return r.select(`#${t}`)},Di={draw:(t,e,i)=>{at.debug("renering svg for syntax error\n");const r=qi(e);r.attr("viewBox","0 0 2412 512"),ci(r,100,512,!0);const o=r.append("g");o.append("path").attr("class","error-icon").attr("d","m411.313,123.313c6.25-6.25 6.25-16.375 0-22.625s-16.375-6.25-22.625,0l-32,32-9.375,9.375-20.688-20.688c-12.484-12.5-32.766-12.5-45.25,0l-16,16c-1.261,1.261-2.304,2.648-3.31,4.051-21.739-8.561-45.324-13.426-70.065-13.426-105.867,0-192,86.133-192,192s86.133,192 192,192 192-86.133 192-192c0-24.741-4.864-48.327-13.426-70.065 1.402-1.007 2.79-2.049 4.051-3.31l16-16c12.5-12.492 12.5-32.758 0-45.25l-20.688-20.688 9.375-9.375 32.001-31.999zm-219.313,100.687c-52.938,0-96,43.063-96,96 0,8.836-7.164,16-16,16s-16-7.164-16-16c0-70.578 57.***********-128 8.836,0 16,7.164 16,16s-7.164,16-16,16z"),o.append("path").attr("class","error-icon").attr("d","m459.02,148.98c-6.25-6.25-16.375-6.25-22.625,0s-6.25,16.375 0,22.625l16,16c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688 6.25-6.25 6.25-16.375 0-22.625l-16.001-16z"),o.append("path").attr("class","error-icon").attr("d","m340.395,75.605c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688 6.25-6.25 6.25-16.375 0-22.625l-16-16c-6.25-6.25-16.375-6.25-22.625,0s-6.25,16.375 0,22.625l15.999,16z"),o.append("path").attr("class","error-icon").attr("d","m400,64c8.844,0 16-7.164 16-16v-32c0-8.836-7.156-16-16-16-8.844,0-16,7.164-16,16v32c0,8.836 7.156,16 16,16z"),o.append("path").attr("class","error-icon").attr("d","m496,96.586h-32c-8.844,0-16,7.164-16,16 0,8.836 7.156,16 16,16h32c8.844,0 16-7.164 16-16 0-8.836-7.156-16-16-16z"),o.append("path").attr("class","error-icon").attr("d","m436.98,75.605c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688l32-32c6.25-6.25 6.25-16.375 0-22.625s-16.375-6.25-22.625,0l-32,32c-6.251,6.25-6.251,16.375-0.001,22.625z"),o.append("text").attr("class","error-text").attr("x",1440).attr("y",250).attr("font-size","150px").style("text-anchor","middle").text("Syntax error in text"),o.append("text").attr("class","error-text").attr("x",1250).attr("y",400).attr("font-size","100px").style("text-anchor","middle").text(`mermaid version ${i}`)}},zi=Di,Ni={db:{},renderer:Di,parser:{parser:{yy:{}},parse:()=>{}}},Pi="flowchart-elk",$i={id:Pi,detector:(t,e)=>{var i;return!!(/^\s*flowchart-elk/.test(t)||/^\s*flowchart|graph/.test(t)&&"elk"===(null==(i=null==e?void 0:e.flowchart)?void 0:i.defaultRenderer))},loader:async()=>{const{diagram:t}=await Promise.all([i.e(4780),i.e(8443),i.e(1261),i.e(2324)]).then(i.bind(i,62324));return{id:Pi,diagram:t}}},ji="timeline",Ri={id:ji,detector:t=>/^\s*timeline/.test(t),loader:async()=>{const{diagram:t}=await i.e(8801).then(i.bind(i,48801));return{id:ji,diagram:t}}},Wi="mindmap",Hi={id:Wi,detector:t=>/^\s*mindmap/.test(t),loader:async()=>{const{diagram:t}=await Promise.all([i.e(4780),i.e(1495)]).then(i.bind(i,1495));return{id:Wi,diagram:t}}},Ui="sankey",Yi={id:Ui,detector:t=>/^\s*sankey-beta/.test(t),loader:async()=>{const{diagram:t}=await i.e(214).then(i.bind(i,70214));return{id:Ui,diagram:t}}};let Vi=!1;const Gi=()=>{Vi||(Vi=!0,Mi("error",Ni,(t=>"error"===t.toLowerCase().trim())),Mi("---",{db:{clear:()=>{}},styles:{},renderer:{draw:()=>{}},parser:{parser:{yy:{}},parse:()=>{throw new Error("Diagrams beginning with --- are not valid. If you were trying to use a YAML front-matter, please ensure that you've correctly opened and closed the YAML front-matter with un-indented `---` blocks")}},init:()=>null},(t=>t.toLowerCase().trimStart().startsWith("---"))),Wt(Me,ri,ei,ze,je,We,He,Je,Ke,$i,De,Ie,Hi,Ri,Pe,si,ni,hi,Ye,Yi,Ge))};class Xi{constructor(t,e={}){this.text=t,this.metadata=e,this.type="graph",this.text=function(t){let e=t;return e=e.replace(/style.*:\S*#.*;/g,(function(t){return t.substring(0,t.length-1)})),e=e.replace(/classDef.*:\S*#.*;/g,(function(t){return t.substring(0,t.length-1)})),e=e.replace(/#\w+;/g,(function(t){const e=t.substring(1,t.length-1);return/^\+?\d+$/.test(e)?"ﬂ°°"+e+"¶ß":"ﬂ°"+e+"¶ß"})),e}(t),this.text+="\n";const i=Fe();try{this.type=Rt(t,i)}catch(t){this.type="error",this.detectError=t}const r=Oi(this.type);at.debug("Type "+this.type),this.db=r.db,this.renderer=r.renderer,this.parser=r.parser,this.parser.parser.yy=this.db,this.init=r.init,this.parse()}parse(){var t,e,i,r,o;if(this.detectError)throw this.detectError;null==(e=(t=this.db).clear)||e.call(t);const n=Fe();null==(i=this.init)||i.call(this,n),this.metadata.title&&(null==(o=(r=this.db).setDiagramTitle)||o.call(r,this.metadata.title)),this.parser.parse(this.text)}async render(t,e){await this.renderer.draw(this.text,t,e,this)}getParser(){return this.parser}getType(){return this.type}}let Ji=[];const Qi=t=>{Ji.push(t)},Ki=t=>t.replace(/^\s*%%(?!{)[^\n]+\n?/gm,"").trimStart();function tr(t){return null==t}var er={isNothing:tr,isObject:function(t){return"object"==typeof t&&null!==t},toArray:function(t){return Array.isArray(t)?t:tr(t)?[]:[t]},repeat:function(t,e){var i,r="";for(i=0;i<e;i+=1)r+=t;return r},isNegativeZero:function(t){return 0===t&&Number.NEGATIVE_INFINITY===1/t},extend:function(t,e){var i,r,o,n;if(e)for(i=0,r=(n=Object.keys(e)).length;i<r;i+=1)t[o=n[i]]=e[o];return t}};function ir(t,e){var i="",r=t.reason||"(unknown reason)";return t.mark?(t.mark.name&&(i+='in "'+t.mark.name+'" '),i+="("+(t.mark.line+1)+":"+(t.mark.column+1)+")",!e&&t.mark.snippet&&(i+="\n\n"+t.mark.snippet),r+" "+i):r}function rr(t,e){Error.call(this),this.name="YAMLException",this.reason=t,this.mark=e,this.message=ir(this,!1),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack||""}rr.prototype=Object.create(Error.prototype),rr.prototype.constructor=rr,rr.prototype.toString=function(t){return this.name+": "+ir(this,t)};var or=rr;function nr(t,e,i,r,o){var n="",a="",s=Math.floor(o/2)-1;return r-e>s&&(e=r-s+(n=" ... ").length),i-r>s&&(i=r+s-(a=" ...").length),{str:n+t.slice(e,i).replace(/\t/g,"→")+a,pos:r-e+n.length}}function ar(t,e){return er.repeat(" ",e-t.length)+t}var sr=function(t,e){if(e=Object.create(e||null),!t.buffer)return null;e.maxLength||(e.maxLength=79),"number"!=typeof e.indent&&(e.indent=1),"number"!=typeof e.linesBefore&&(e.linesBefore=3),"number"!=typeof e.linesAfter&&(e.linesAfter=2);for(var i,r=/\r?\n|\r|\0/g,o=[0],n=[],a=-1;i=r.exec(t.buffer);)n.push(i.index),o.push(i.index+i[0].length),t.position<=i.index&&a<0&&(a=o.length-2);a<0&&(a=o.length-1);var s,l,h="",c=Math.min(t.line+e.linesAfter,n.length).toString().length,u=e.maxLength-(e.indent+c+3);for(s=1;s<=e.linesBefore&&!(a-s<0);s++)l=nr(t.buffer,o[a-s],n[a-s],t.position-(o[a]-o[a-s]),u),h=er.repeat(" ",e.indent)+ar((t.line-s+1).toString(),c)+" | "+l.str+"\n"+h;for(l=nr(t.buffer,o[a],n[a],t.position,u),h+=er.repeat(" ",e.indent)+ar((t.line+1).toString(),c)+" | "+l.str+"\n",h+=er.repeat("-",e.indent+c+3+l.pos)+"^\n",s=1;s<=e.linesAfter&&!(a+s>=n.length);s++)l=nr(t.buffer,o[a+s],n[a+s],t.position-(o[a]-o[a+s]),u),h+=er.repeat(" ",e.indent)+ar((t.line+s+1).toString(),c)+" | "+l.str+"\n";return h.replace(/\n$/,"")},lr=["kind","multi","resolve","construct","instanceOf","predicate","represent","representName","defaultStyle","styleAliases"],hr=["scalar","sequence","mapping"],cr=function(t,e){var i,r;if(e=e||{},Object.keys(e).forEach((function(e){if(-1===lr.indexOf(e))throw new or('Unknown option "'+e+'" is met in definition of "'+t+'" YAML type.')})),this.options=e,this.tag=t,this.kind=e.kind||null,this.resolve=e.resolve||function(){return!0},this.construct=e.construct||function(t){return t},this.instanceOf=e.instanceOf||null,this.predicate=e.predicate||null,this.represent=e.represent||null,this.representName=e.representName||null,this.defaultStyle=e.defaultStyle||null,this.multi=e.multi||!1,this.styleAliases=(i=e.styleAliases||null,r={},null!==i&&Object.keys(i).forEach((function(t){i[t].forEach((function(e){r[String(e)]=t}))})),r),-1===hr.indexOf(this.kind))throw new or('Unknown kind "'+this.kind+'" is specified for "'+t+'" YAML type.')};function ur(t,e){var i=[];return t[e].forEach((function(t){var e=i.length;i.forEach((function(i,r){i.tag===t.tag&&i.kind===t.kind&&i.multi===t.multi&&(e=r)})),i[e]=t})),i}function dr(t){return this.extend(t)}dr.prototype.extend=function(t){var e=[],i=[];if(t instanceof cr)i.push(t);else if(Array.isArray(t))i=i.concat(t);else{if(!t||!Array.isArray(t.implicit)&&!Array.isArray(t.explicit))throw new or("Schema.extend argument should be a Type, [ Type ], or a schema definition ({ implicit: [...], explicit: [...] })");t.implicit&&(e=e.concat(t.implicit)),t.explicit&&(i=i.concat(t.explicit))}e.forEach((function(t){if(!(t instanceof cr))throw new or("Specified list of YAML types (or a single Type object) contains a non-Type object.");if(t.loadKind&&"scalar"!==t.loadKind)throw new or("There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.");if(t.multi)throw new or("There is a multi type in the implicit list of a schema. Multi tags can only be listed as explicit.")})),i.forEach((function(t){if(!(t instanceof cr))throw new or("Specified list of YAML types (or a single Type object) contains a non-Type object.")}));var r=Object.create(dr.prototype);return r.implicit=(this.implicit||[]).concat(e),r.explicit=(this.explicit||[]).concat(i),r.compiledImplicit=ur(r,"implicit"),r.compiledExplicit=ur(r,"explicit"),r.compiledTypeMap=function(){var t,e,i={scalar:{},sequence:{},mapping:{},fallback:{},multi:{scalar:[],sequence:[],mapping:[],fallback:[]}};function r(t){t.multi?(i.multi[t.kind].push(t),i.multi.fallback.push(t)):i[t.kind][t.tag]=i.fallback[t.tag]=t}for(t=0,e=arguments.length;t<e;t+=1)arguments[t].forEach(r);return i}(r.compiledImplicit,r.compiledExplicit),r};var fr=new dr({explicit:[new cr("tag:yaml.org,2002:str",{kind:"scalar",construct:function(t){return null!==t?t:""}}),new cr("tag:yaml.org,2002:seq",{kind:"sequence",construct:function(t){return null!==t?t:[]}}),new cr("tag:yaml.org,2002:map",{kind:"mapping",construct:function(t){return null!==t?t:{}}})]}),pr=new cr("tag:yaml.org,2002:null",{kind:"scalar",resolve:function(t){if(null===t)return!0;var e=t.length;return 1===e&&"~"===t||4===e&&("null"===t||"Null"===t||"NULL"===t)},construct:function(){return null},predicate:function(t){return null===t},represent:{canonical:function(){return"~"},lowercase:function(){return"null"},uppercase:function(){return"NULL"},camelcase:function(){return"Null"},empty:function(){return""}},defaultStyle:"lowercase"}),gr=new cr("tag:yaml.org,2002:bool",{kind:"scalar",resolve:function(t){if(null===t)return!1;var e=t.length;return 4===e&&("true"===t||"True"===t||"TRUE"===t)||5===e&&("false"===t||"False"===t||"FALSE"===t)},construct:function(t){return"true"===t||"True"===t||"TRUE"===t},predicate:function(t){return"[object Boolean]"===Object.prototype.toString.call(t)},represent:{lowercase:function(t){return t?"true":"false"},uppercase:function(t){return t?"TRUE":"FALSE"},camelcase:function(t){return t?"True":"False"}},defaultStyle:"lowercase"});function mr(t){return 48<=t&&t<=55}function yr(t){return 48<=t&&t<=57}var Cr=new cr("tag:yaml.org,2002:int",{kind:"scalar",resolve:function(t){if(null===t)return!1;var e,i,r=t.length,o=0,n=!1;if(!r)return!1;if("-"!==(e=t[o])&&"+"!==e||(e=t[++o]),"0"===e){if(o+1===r)return!0;if("b"===(e=t[++o])){for(o++;o<r;o++)if("_"!==(e=t[o])){if("0"!==e&&"1"!==e)return!1;n=!0}return n&&"_"!==e}if("x"===e){for(o++;o<r;o++)if("_"!==(e=t[o])){if(!(48<=(i=t.charCodeAt(o))&&i<=57||65<=i&&i<=70||97<=i&&i<=102))return!1;n=!0}return n&&"_"!==e}if("o"===e){for(o++;o<r;o++)if("_"!==(e=t[o])){if(!mr(t.charCodeAt(o)))return!1;n=!0}return n&&"_"!==e}}if("_"===e)return!1;for(;o<r;o++)if("_"!==(e=t[o])){if(!yr(t.charCodeAt(o)))return!1;n=!0}return!(!n||"_"===e)},construct:function(t){var e,i=t,r=1;if(-1!==i.indexOf("_")&&(i=i.replace(/_/g,"")),"-"!==(e=i[0])&&"+"!==e||("-"===e&&(r=-1),e=(i=i.slice(1))[0]),"0"===i)return 0;if("0"===e){if("b"===i[1])return r*parseInt(i.slice(2),2);if("x"===i[1])return r*parseInt(i.slice(2),16);if("o"===i[1])return r*parseInt(i.slice(2),8)}return r*parseInt(i,10)},predicate:function(t){return"[object Number]"===Object.prototype.toString.call(t)&&t%1==0&&!er.isNegativeZero(t)},represent:{binary:function(t){return t>=0?"0b"+t.toString(2):"-0b"+t.toString(2).slice(1)},octal:function(t){return t>=0?"0o"+t.toString(8):"-0o"+t.toString(8).slice(1)},decimal:function(t){return t.toString(10)},hexadecimal:function(t){return t>=0?"0x"+t.toString(16).toUpperCase():"-0x"+t.toString(16).toUpperCase().slice(1)}},defaultStyle:"decimal",styleAliases:{binary:[2,"bin"],octal:[8,"oct"],decimal:[10,"dec"],hexadecimal:[16,"hex"]}}),br=new RegExp("^(?:[-+]?(?:[0-9][0-9_]*)(?:\\.[0-9_]*)?(?:[eE][-+]?[0-9]+)?|\\.[0-9_]+(?:[eE][-+]?[0-9]+)?|[-+]?\\.(?:inf|Inf|INF)|\\.(?:nan|NaN|NAN))$"),xr=/^[-+]?[0-9]+e/,vr=new cr("tag:yaml.org,2002:float",{kind:"scalar",resolve:function(t){return null!==t&&!(!br.test(t)||"_"===t[t.length-1])},construct:function(t){var e,i;return i="-"===(e=t.replace(/_/g,"").toLowerCase())[0]?-1:1,"+-".indexOf(e[0])>=0&&(e=e.slice(1)),".inf"===e?1===i?Number.POSITIVE_INFINITY:Number.NEGATIVE_INFINITY:".nan"===e?NaN:i*parseFloat(e,10)},predicate:function(t){return"[object Number]"===Object.prototype.toString.call(t)&&(t%1!=0||er.isNegativeZero(t))},represent:function(t,e){var i;if(isNaN(t))switch(e){case"lowercase":return".nan";case"uppercase":return".NAN";case"camelcase":return".NaN"}else if(Number.POSITIVE_INFINITY===t)switch(e){case"lowercase":return".inf";case"uppercase":return".INF";case"camelcase":return".Inf"}else if(Number.NEGATIVE_INFINITY===t)switch(e){case"lowercase":return"-.inf";case"uppercase":return"-.INF";case"camelcase":return"-.Inf"}else if(er.isNegativeZero(t))return"-0.0";return i=t.toString(10),xr.test(i)?i.replace("e",".e"):i},defaultStyle:"lowercase"}),kr=fr.extend({implicit:[pr,gr,Cr,vr]}),Tr=kr,_r=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9])-([0-9][0-9])$"),Sr=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9]?)-([0-9][0-9]?)(?:[Tt]|[ \\t]+)([0-9][0-9]?):([0-9][0-9]):([0-9][0-9])(?:\\.([0-9]*))?(?:[ \\t]*(Z|([-+])([0-9][0-9]?)(?::([0-9][0-9]))?))?$"),Br=new cr("tag:yaml.org,2002:timestamp",{kind:"scalar",resolve:function(t){return null!==t&&(null!==_r.exec(t)||null!==Sr.exec(t))},construct:function(t){var e,i,r,o,n,a,s,l,h=0,c=null;if(null===(e=_r.exec(t))&&(e=Sr.exec(t)),null===e)throw new Error("Date resolve error");if(i=+e[1],r=+e[2]-1,o=+e[3],!e[4])return new Date(Date.UTC(i,r,o));if(n=+e[4],a=+e[5],s=+e[6],e[7]){for(h=e[7].slice(0,3);h.length<3;)h+="0";h=+h}return e[9]&&(c=6e4*(60*+e[10]+ +(e[11]||0)),"-"===e[9]&&(c=-c)),l=new Date(Date.UTC(i,r,o,n,a,s,h)),c&&l.setTime(l.getTime()-c),l},instanceOf:Date,represent:function(t){return t.toISOString()}}),Fr=new cr("tag:yaml.org,2002:merge",{kind:"scalar",resolve:function(t){return"<<"===t||null===t}}),wr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\n\r",Ar=new cr("tag:yaml.org,2002:binary",{kind:"scalar",resolve:function(t){if(null===t)return!1;var e,i,r=0,o=t.length,n=wr;for(i=0;i<o;i++)if(!((e=n.indexOf(t.charAt(i)))>64)){if(e<0)return!1;r+=6}return r%8==0},construct:function(t){var e,i,r=t.replace(/[\r\n=]/g,""),o=r.length,n=wr,a=0,s=[];for(e=0;e<o;e++)e%4==0&&e&&(s.push(a>>16&255),s.push(a>>8&255),s.push(255&a)),a=a<<6|n.indexOf(r.charAt(e));return 0==(i=o%4*6)?(s.push(a>>16&255),s.push(a>>8&255),s.push(255&a)):18===i?(s.push(a>>10&255),s.push(a>>2&255)):12===i&&s.push(a>>4&255),new Uint8Array(s)},predicate:function(t){return"[object Uint8Array]"===Object.prototype.toString.call(t)},represent:function(t){var e,i,r="",o=0,n=t.length,a=wr;for(e=0;e<n;e++)e%3==0&&e&&(r+=a[o>>18&63],r+=a[o>>12&63],r+=a[o>>6&63],r+=a[63&o]),o=(o<<8)+t[e];return 0==(i=n%3)?(r+=a[o>>18&63],r+=a[o>>12&63],r+=a[o>>6&63],r+=a[63&o]):2===i?(r+=a[o>>10&63],r+=a[o>>4&63],r+=a[o<<2&63],r+=a[64]):1===i&&(r+=a[o>>2&63],r+=a[o<<4&63],r+=a[64],r+=a[64]),r}}),Lr=Object.prototype.hasOwnProperty,Zr=Object.prototype.toString,Er=new cr("tag:yaml.org,2002:omap",{kind:"sequence",resolve:function(t){if(null===t)return!0;var e,i,r,o,n,a=[],s=t;for(e=0,i=s.length;e<i;e+=1){if(r=s[e],n=!1,"[object Object]"!==Zr.call(r))return!1;for(o in r)if(Lr.call(r,o)){if(n)return!1;n=!0}if(!n)return!1;if(-1!==a.indexOf(o))return!1;a.push(o)}return!0},construct:function(t){return null!==t?t:[]}}),Mr=Object.prototype.toString,Or=new cr("tag:yaml.org,2002:pairs",{kind:"sequence",resolve:function(t){if(null===t)return!0;var e,i,r,o,n,a=t;for(n=new Array(a.length),e=0,i=a.length;e<i;e+=1){if(r=a[e],"[object Object]"!==Mr.call(r))return!1;if(1!==(o=Object.keys(r)).length)return!1;n[e]=[o[0],r[o[0]]]}return!0},construct:function(t){if(null===t)return[];var e,i,r,o,n,a=t;for(n=new Array(a.length),e=0,i=a.length;e<i;e+=1)r=a[e],o=Object.keys(r),n[e]=[o[0],r[o[0]]];return n}}),Ir=Object.prototype.hasOwnProperty,qr=new cr("tag:yaml.org,2002:set",{kind:"mapping",resolve:function(t){if(null===t)return!0;var e,i=t;for(e in i)if(Ir.call(i,e)&&null!==i[e])return!1;return!0},construct:function(t){return null!==t?t:{}}}),Dr=Tr.extend({implicit:[Br,Fr],explicit:[Ar,Er,Or,qr]}),zr=Object.prototype.hasOwnProperty,Nr=1,Pr=2,$r=3,jr=4,Rr=1,Wr=2,Hr=3,Ur=/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,Yr=/[\x85\u2028\u2029]/,Vr=/[,\[\]\{\}]/,Gr=/^(?:!|!!|![a-z\-]+!)$/i,Xr=/^(?:!|[^,\[\]\{\}])(?:%[0-9a-f]{2}|[0-9a-z\-#;\/\?:@&=\+\$,_\.!~\*'\(\)\[\]])*$/i;function Jr(t){return Object.prototype.toString.call(t)}function Qr(t){return 10===t||13===t}function Kr(t){return 9===t||32===t}function to(t){return 9===t||32===t||10===t||13===t}function eo(t){return 44===t||91===t||93===t||123===t||125===t}function io(t){var e;return 48<=t&&t<=57?t-48:97<=(e=32|t)&&e<=102?e-97+10:-1}function ro(t){return 48===t?"\0":97===t?"":98===t?"\b":116===t||9===t?"\t":110===t?"\n":118===t?"\v":102===t?"\f":114===t?"\r":101===t?"":32===t?" ":34===t?'"':47===t?"/":92===t?"\\":78===t?"":95===t?" ":76===t?"\u2028":80===t?"\u2029":""}function oo(t){return t<=65535?String.fromCharCode(t):String.fromCharCode(55296+(t-65536>>10),56320+(t-65536&1023))}for(var no=new Array(256),ao=new Array(256),so=0;so<256;so++)no[so]=ro(so)?1:0,ao[so]=ro(so);function lo(t,e){this.input=t,this.filename=e.filename||null,this.schema=e.schema||Dr,this.onWarning=e.onWarning||null,this.legacy=e.legacy||!1,this.json=e.json||!1,this.listener=e.listener||null,this.implicitTypes=this.schema.compiledImplicit,this.typeMap=this.schema.compiledTypeMap,this.length=t.length,this.position=0,this.line=0,this.lineStart=0,this.lineIndent=0,this.firstTabInLine=-1,this.documents=[]}function ho(t,e){var i={name:t.filename,buffer:t.input.slice(0,-1),position:t.position,line:t.line,column:t.position-t.lineStart};return i.snippet=sr(i),new or(e,i)}function co(t,e){throw ho(t,e)}function uo(t,e){t.onWarning&&t.onWarning.call(null,ho(t,e))}var fo={YAML:function(t,e,i){var r,o,n;null!==t.version&&co(t,"duplication of %YAML directive"),1!==i.length&&co(t,"YAML directive accepts exactly one argument"),null===(r=/^([0-9]+)\.([0-9]+)$/.exec(i[0]))&&co(t,"ill-formed argument of the YAML directive"),o=parseInt(r[1],10),n=parseInt(r[2],10),1!==o&&co(t,"unacceptable YAML version of the document"),t.version=i[0],t.checkLineBreaks=n<2,1!==n&&2!==n&&uo(t,"unsupported YAML version of the document")},TAG:function(t,e,i){var r,o;2!==i.length&&co(t,"TAG directive accepts exactly two arguments"),r=i[0],o=i[1],Gr.test(r)||co(t,"ill-formed tag handle (first argument) of the TAG directive"),zr.call(t.tagMap,r)&&co(t,'there is a previously declared suffix for "'+r+'" tag handle'),Xr.test(o)||co(t,"ill-formed tag prefix (second argument) of the TAG directive");try{o=decodeURIComponent(o)}catch(e){co(t,"tag prefix is malformed: "+o)}t.tagMap[r]=o}};function po(t,e,i,r){var o,n,a,s;if(e<i){if(s=t.input.slice(e,i),r)for(o=0,n=s.length;o<n;o+=1)9===(a=s.charCodeAt(o))||32<=a&&a<=1114111||co(t,"expected valid JSON character");else Ur.test(s)&&co(t,"the stream contains non-printable characters");t.result+=s}}function go(t,e,i,r){var o,n,a,s;for(er.isObject(i)||co(t,"cannot merge mappings; the provided source object is unacceptable"),a=0,s=(o=Object.keys(i)).length;a<s;a+=1)n=o[a],zr.call(e,n)||(e[n]=i[n],r[n]=!0)}function mo(t,e,i,r,o,n,a,s,l){var h,c;if(Array.isArray(o))for(h=0,c=(o=Array.prototype.slice.call(o)).length;h<c;h+=1)Array.isArray(o[h])&&co(t,"nested arrays are not supported inside keys"),"object"==typeof o&&"[object Object]"===Jr(o[h])&&(o[h]="[object Object]");if("object"==typeof o&&"[object Object]"===Jr(o)&&(o="[object Object]"),o=String(o),null===e&&(e={}),"tag:yaml.org,2002:merge"===r)if(Array.isArray(n))for(h=0,c=n.length;h<c;h+=1)go(t,e,n[h],i);else go(t,e,n,i);else t.json||zr.call(i,o)||!zr.call(e,o)||(t.line=a||t.line,t.lineStart=s||t.lineStart,t.position=l||t.position,co(t,"duplicated mapping key")),"__proto__"===o?Object.defineProperty(e,o,{configurable:!0,enumerable:!0,writable:!0,value:n}):e[o]=n,delete i[o];return e}function yo(t){var e;10===(e=t.input.charCodeAt(t.position))?t.position++:13===e?(t.position++,10===t.input.charCodeAt(t.position)&&t.position++):co(t,"a line break is expected"),t.line+=1,t.lineStart=t.position,t.firstTabInLine=-1}function Co(t,e,i){for(var r=0,o=t.input.charCodeAt(t.position);0!==o;){for(;Kr(o);)9===o&&-1===t.firstTabInLine&&(t.firstTabInLine=t.position),o=t.input.charCodeAt(++t.position);if(e&&35===o)do{o=t.input.charCodeAt(++t.position)}while(10!==o&&13!==o&&0!==o);if(!Qr(o))break;for(yo(t),o=t.input.charCodeAt(t.position),r++,t.lineIndent=0;32===o;)t.lineIndent++,o=t.input.charCodeAt(++t.position)}return-1!==i&&0!==r&&t.lineIndent<i&&uo(t,"deficient indentation"),r}function bo(t){var e,i=t.position;return!(45!==(e=t.input.charCodeAt(i))&&46!==e||e!==t.input.charCodeAt(i+1)||e!==t.input.charCodeAt(i+2)||(i+=3,0!==(e=t.input.charCodeAt(i))&&!to(e)))}function xo(t,e){1===e?t.result+=" ":e>1&&(t.result+=er.repeat("\n",e-1))}function vo(t,e){var i,r,o=t.tag,n=t.anchor,a=[],s=!1;if(-1!==t.firstTabInLine)return!1;for(null!==t.anchor&&(t.anchorMap[t.anchor]=a),r=t.input.charCodeAt(t.position);0!==r&&(-1!==t.firstTabInLine&&(t.position=t.firstTabInLine,co(t,"tab characters must not be used in indentation")),45===r)&&to(t.input.charCodeAt(t.position+1));)if(s=!0,t.position++,Co(t,!0,-1)&&t.lineIndent<=e)a.push(null),r=t.input.charCodeAt(t.position);else if(i=t.line,_o(t,e,$r,!1,!0),a.push(t.result),Co(t,!0,-1),r=t.input.charCodeAt(t.position),(t.line===i||t.lineIndent>e)&&0!==r)co(t,"bad indentation of a sequence entry");else if(t.lineIndent<e)break;return!!s&&(t.tag=o,t.anchor=n,t.kind="sequence",t.result=a,!0)}function ko(t){var e,i,r,o,n=!1,a=!1;if(33!==(o=t.input.charCodeAt(t.position)))return!1;if(null!==t.tag&&co(t,"duplication of a tag property"),60===(o=t.input.charCodeAt(++t.position))?(n=!0,o=t.input.charCodeAt(++t.position)):33===o?(a=!0,i="!!",o=t.input.charCodeAt(++t.position)):i="!",e=t.position,n){do{o=t.input.charCodeAt(++t.position)}while(0!==o&&62!==o);t.position<t.length?(r=t.input.slice(e,t.position),o=t.input.charCodeAt(++t.position)):co(t,"unexpected end of the stream within a verbatim tag")}else{for(;0!==o&&!to(o);)33===o&&(a?co(t,"tag suffix cannot contain exclamation marks"):(i=t.input.slice(e-1,t.position+1),Gr.test(i)||co(t,"named tag handle cannot contain such characters"),a=!0,e=t.position+1)),o=t.input.charCodeAt(++t.position);r=t.input.slice(e,t.position),Vr.test(r)&&co(t,"tag suffix cannot contain flow indicator characters")}r&&!Xr.test(r)&&co(t,"tag name cannot contain such characters: "+r);try{r=decodeURIComponent(r)}catch(e){co(t,"tag name is malformed: "+r)}return n?t.tag=r:zr.call(t.tagMap,i)?t.tag=t.tagMap[i]+r:"!"===i?t.tag="!"+r:"!!"===i?t.tag="tag:yaml.org,2002:"+r:co(t,'undeclared tag handle "'+i+'"'),!0}function To(t){var e,i;if(38!==(i=t.input.charCodeAt(t.position)))return!1;for(null!==t.anchor&&co(t,"duplication of an anchor property"),i=t.input.charCodeAt(++t.position),e=t.position;0!==i&&!to(i)&&!eo(i);)i=t.input.charCodeAt(++t.position);return t.position===e&&co(t,"name of an anchor node must contain at least one character"),t.anchor=t.input.slice(e,t.position),!0}function _o(t,e,i,r,o){var n,a,s,l,h,c,u,d,f,p=1,g=!1,m=!1;if(null!==t.listener&&t.listener("open",t),t.tag=null,t.anchor=null,t.kind=null,t.result=null,n=a=s=jr===i||$r===i,r&&Co(t,!0,-1)&&(g=!0,t.lineIndent>e?p=1:t.lineIndent===e?p=0:t.lineIndent<e&&(p=-1)),1===p)for(;ko(t)||To(t);)Co(t,!0,-1)?(g=!0,s=n,t.lineIndent>e?p=1:t.lineIndent===e?p=0:t.lineIndent<e&&(p=-1)):s=!1;if(s&&(s=g||o),1!==p&&jr!==i||(d=Nr===i||Pr===i?e:e+1,f=t.position-t.lineStart,1===p?s&&(vo(t,f)||function(t,e,i){var r,o,n,a,s,l,h,c=t.tag,u=t.anchor,d={},f=Object.create(null),p=null,g=null,m=null,y=!1,C=!1;if(-1!==t.firstTabInLine)return!1;for(null!==t.anchor&&(t.anchorMap[t.anchor]=d),h=t.input.charCodeAt(t.position);0!==h;){if(y||-1===t.firstTabInLine||(t.position=t.firstTabInLine,co(t,"tab characters must not be used in indentation")),r=t.input.charCodeAt(t.position+1),n=t.line,63!==h&&58!==h||!to(r)){if(a=t.line,s=t.lineStart,l=t.position,!_o(t,i,Pr,!1,!0))break;if(t.line===n){for(h=t.input.charCodeAt(t.position);Kr(h);)h=t.input.charCodeAt(++t.position);if(58===h)to(h=t.input.charCodeAt(++t.position))||co(t,"a whitespace character is expected after the key-value separator within a block mapping"),y&&(mo(t,d,f,p,g,null,a,s,l),p=g=m=null),C=!0,y=!1,o=!1,p=t.tag,g=t.result;else{if(!C)return t.tag=c,t.anchor=u,!0;co(t,"can not read an implicit mapping pair; a colon is missed")}}else{if(!C)return t.tag=c,t.anchor=u,!0;co(t,"can not read a block mapping entry; a multiline key may not be an implicit key")}}else 63===h?(y&&(mo(t,d,f,p,g,null,a,s,l),p=g=m=null),C=!0,y=!0,o=!0):y?(y=!1,o=!0):co(t,"incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line"),t.position+=1,h=r;if((t.line===n||t.lineIndent>e)&&(y&&(a=t.line,s=t.lineStart,l=t.position),_o(t,e,jr,!0,o)&&(y?g=t.result:m=t.result),y||(mo(t,d,f,p,g,m,a,s,l),p=g=m=null),Co(t,!0,-1),h=t.input.charCodeAt(t.position)),(t.line===n||t.lineIndent>e)&&0!==h)co(t,"bad indentation of a mapping entry");else if(t.lineIndent<e)break}return y&&mo(t,d,f,p,g,null,a,s,l),C&&(t.tag=c,t.anchor=u,t.kind="mapping",t.result=d),C}(t,f,d))||function(t,e){var i,r,o,n,a,s,l,h,c,u,d,f,p=!0,g=t.tag,m=t.anchor,y=Object.create(null);if(91===(f=t.input.charCodeAt(t.position)))a=93,h=!1,n=[];else{if(123!==f)return!1;a=125,h=!0,n={}}for(null!==t.anchor&&(t.anchorMap[t.anchor]=n),f=t.input.charCodeAt(++t.position);0!==f;){if(Co(t,!0,e),(f=t.input.charCodeAt(t.position))===a)return t.position++,t.tag=g,t.anchor=m,t.kind=h?"mapping":"sequence",t.result=n,!0;p?44===f&&co(t,"expected the node content, but found ','"):co(t,"missed comma between flow collection entries"),d=null,s=l=!1,63===f&&to(t.input.charCodeAt(t.position+1))&&(s=l=!0,t.position++,Co(t,!0,e)),i=t.line,r=t.lineStart,o=t.position,_o(t,e,Nr,!1,!0),u=t.tag,c=t.result,Co(t,!0,e),f=t.input.charCodeAt(t.position),!l&&t.line!==i||58!==f||(s=!0,f=t.input.charCodeAt(++t.position),Co(t,!0,e),_o(t,e,Nr,!1,!0),d=t.result),h?mo(t,n,y,u,c,d,i,r,o):s?n.push(mo(t,null,y,u,c,d,i,r,o)):n.push(c),Co(t,!0,e),44===(f=t.input.charCodeAt(t.position))?(p=!0,f=t.input.charCodeAt(++t.position)):p=!1}co(t,"unexpected end of the stream within a flow collection")}(t,d)?m=!0:(a&&function(t,e){var i,r,o,n,a,s=Rr,l=!1,h=!1,c=e,u=0,d=!1;if(124===(n=t.input.charCodeAt(t.position)))r=!1;else{if(62!==n)return!1;r=!0}for(t.kind="scalar",t.result="";0!==n;)if(43===(n=t.input.charCodeAt(++t.position))||45===n)Rr===s?s=43===n?Hr:Wr:co(t,"repeat of a chomping mode identifier");else{if(!((o=48<=(a=n)&&a<=57?a-48:-1)>=0))break;0===o?co(t,"bad explicit indentation width of a block scalar; it cannot be less than one"):h?co(t,"repeat of an indentation width identifier"):(c=e+o-1,h=!0)}if(Kr(n)){do{n=t.input.charCodeAt(++t.position)}while(Kr(n));if(35===n)do{n=t.input.charCodeAt(++t.position)}while(!Qr(n)&&0!==n)}for(;0!==n;){for(yo(t),t.lineIndent=0,n=t.input.charCodeAt(t.position);(!h||t.lineIndent<c)&&32===n;)t.lineIndent++,n=t.input.charCodeAt(++t.position);if(!h&&t.lineIndent>c&&(c=t.lineIndent),Qr(n))u++;else{if(t.lineIndent<c){s===Hr?t.result+=er.repeat("\n",l?1+u:u):s===Rr&&l&&(t.result+="\n");break}for(r?Kr(n)?(d=!0,t.result+=er.repeat("\n",l?1+u:u)):d?(d=!1,t.result+=er.repeat("\n",u+1)):0===u?l&&(t.result+=" "):t.result+=er.repeat("\n",u):t.result+=er.repeat("\n",l?1+u:u),l=!0,h=!0,u=0,i=t.position;!Qr(n)&&0!==n;)n=t.input.charCodeAt(++t.position);po(t,i,t.position,!1)}}return!0}(t,d)||function(t,e){var i,r,o;if(39!==(i=t.input.charCodeAt(t.position)))return!1;for(t.kind="scalar",t.result="",t.position++,r=o=t.position;0!==(i=t.input.charCodeAt(t.position));)if(39===i){if(po(t,r,t.position,!0),39!==(i=t.input.charCodeAt(++t.position)))return!0;r=t.position,t.position++,o=t.position}else Qr(i)?(po(t,r,o,!0),xo(t,Co(t,!1,e)),r=o=t.position):t.position===t.lineStart&&bo(t)?co(t,"unexpected end of the document within a single quoted scalar"):(t.position++,o=t.position);co(t,"unexpected end of the stream within a single quoted scalar")}(t,d)||function(t,e){var i,r,o,n,a,s,l;if(34!==(s=t.input.charCodeAt(t.position)))return!1;for(t.kind="scalar",t.result="",t.position++,i=r=t.position;0!==(s=t.input.charCodeAt(t.position));){if(34===s)return po(t,i,t.position,!0),t.position++,!0;if(92===s){if(po(t,i,t.position,!0),Qr(s=t.input.charCodeAt(++t.position)))Co(t,!1,e);else if(s<256&&no[s])t.result+=ao[s],t.position++;else if((a=120===(l=s)?2:117===l?4:85===l?8:0)>0){for(o=a,n=0;o>0;o--)(a=io(s=t.input.charCodeAt(++t.position)))>=0?n=(n<<4)+a:co(t,"expected hexadecimal character");t.result+=oo(n),t.position++}else co(t,"unknown escape sequence");i=r=t.position}else Qr(s)?(po(t,i,r,!0),xo(t,Co(t,!1,e)),i=r=t.position):t.position===t.lineStart&&bo(t)?co(t,"unexpected end of the document within a double quoted scalar"):(t.position++,r=t.position)}co(t,"unexpected end of the stream within a double quoted scalar")}(t,d)?m=!0:function(t){var e,i,r;if(42!==(r=t.input.charCodeAt(t.position)))return!1;for(r=t.input.charCodeAt(++t.position),e=t.position;0!==r&&!to(r)&&!eo(r);)r=t.input.charCodeAt(++t.position);return t.position===e&&co(t,"name of an alias node must contain at least one character"),i=t.input.slice(e,t.position),zr.call(t.anchorMap,i)||co(t,'unidentified alias "'+i+'"'),t.result=t.anchorMap[i],Co(t,!0,-1),!0}(t)?(m=!0,null===t.tag&&null===t.anchor||co(t,"alias node should not have any properties")):function(t,e,i){var r,o,n,a,s,l,h,c,u=t.kind,d=t.result;if(to(c=t.input.charCodeAt(t.position))||eo(c)||35===c||38===c||42===c||33===c||124===c||62===c||39===c||34===c||37===c||64===c||96===c)return!1;if((63===c||45===c)&&(to(r=t.input.charCodeAt(t.position+1))||i&&eo(r)))return!1;for(t.kind="scalar",t.result="",o=n=t.position,a=!1;0!==c;){if(58===c){if(to(r=t.input.charCodeAt(t.position+1))||i&&eo(r))break}else if(35===c){if(to(t.input.charCodeAt(t.position-1)))break}else{if(t.position===t.lineStart&&bo(t)||i&&eo(c))break;if(Qr(c)){if(s=t.line,l=t.lineStart,h=t.lineIndent,Co(t,!1,-1),t.lineIndent>=e){a=!0,c=t.input.charCodeAt(t.position);continue}t.position=n,t.line=s,t.lineStart=l,t.lineIndent=h;break}}a&&(po(t,o,n,!1),xo(t,t.line-s),o=n=t.position,a=!1),Kr(c)||(n=t.position+1),c=t.input.charCodeAt(++t.position)}return po(t,o,n,!1),!!t.result||(t.kind=u,t.result=d,!1)}(t,d,Nr===i)&&(m=!0,null===t.tag&&(t.tag="?")),null!==t.anchor&&(t.anchorMap[t.anchor]=t.result)):0===p&&(m=s&&vo(t,f))),null===t.tag)null!==t.anchor&&(t.anchorMap[t.anchor]=t.result);else if("?"===t.tag){for(null!==t.result&&"scalar"!==t.kind&&co(t,'unacceptable node kind for !<?> tag; it should be "scalar", not "'+t.kind+'"'),l=0,h=t.implicitTypes.length;l<h;l+=1)if((u=t.implicitTypes[l]).resolve(t.result)){t.result=u.construct(t.result),t.tag=u.tag,null!==t.anchor&&(t.anchorMap[t.anchor]=t.result);break}}else if("!"!==t.tag){if(zr.call(t.typeMap[t.kind||"fallback"],t.tag))u=t.typeMap[t.kind||"fallback"][t.tag];else for(u=null,l=0,h=(c=t.typeMap.multi[t.kind||"fallback"]).length;l<h;l+=1)if(t.tag.slice(0,c[l].tag.length)===c[l].tag){u=c[l];break}u||co(t,"unknown tag !<"+t.tag+">"),null!==t.result&&u.kind!==t.kind&&co(t,"unacceptable node kind for !<"+t.tag+'> tag; it should be "'+u.kind+'", not "'+t.kind+'"'),u.resolve(t.result,t.tag)?(t.result=u.construct(t.result,t.tag),null!==t.anchor&&(t.anchorMap[t.anchor]=t.result)):co(t,"cannot resolve a node with !<"+t.tag+"> explicit tag")}return null!==t.listener&&t.listener("close",t),null!==t.tag||null!==t.anchor||m}function So(t){var e,i,r,o,n=t.position,a=!1;for(t.version=null,t.checkLineBreaks=t.legacy,t.tagMap=Object.create(null),t.anchorMap=Object.create(null);0!==(o=t.input.charCodeAt(t.position))&&(Co(t,!0,-1),o=t.input.charCodeAt(t.position),!(t.lineIndent>0||37!==o));){for(a=!0,o=t.input.charCodeAt(++t.position),e=t.position;0!==o&&!to(o);)o=t.input.charCodeAt(++t.position);for(r=[],(i=t.input.slice(e,t.position)).length<1&&co(t,"directive name must not be less than one character in length");0!==o;){for(;Kr(o);)o=t.input.charCodeAt(++t.position);if(35===o){do{o=t.input.charCodeAt(++t.position)}while(0!==o&&!Qr(o));break}if(Qr(o))break;for(e=t.position;0!==o&&!to(o);)o=t.input.charCodeAt(++t.position);r.push(t.input.slice(e,t.position))}0!==o&&yo(t),zr.call(fo,i)?fo[i](t,i,r):uo(t,'unknown document directive "'+i+'"')}Co(t,!0,-1),0===t.lineIndent&&45===t.input.charCodeAt(t.position)&&45===t.input.charCodeAt(t.position+1)&&45===t.input.charCodeAt(t.position+2)?(t.position+=3,Co(t,!0,-1)):a&&co(t,"directives end mark is expected"),_o(t,t.lineIndent-1,jr,!1,!0),Co(t,!0,-1),t.checkLineBreaks&&Yr.test(t.input.slice(n,t.position))&&uo(t,"non-ASCII line breaks are interpreted as content"),t.documents.push(t.result),t.position===t.lineStart&&bo(t)?46===t.input.charCodeAt(t.position)&&(t.position+=3,Co(t,!0,-1)):t.position<t.length-1&&co(t,"end of the stream or a document separator is expected")}function Bo(t,e){e=e||{},0!==(t=String(t)).length&&(10!==t.charCodeAt(t.length-1)&&13!==t.charCodeAt(t.length-1)&&(t+="\n"),65279===t.charCodeAt(0)&&(t=t.slice(1)));var i=new lo(t,e),r=t.indexOf("\0");for(-1!==r&&(i.position=r,co(i,"null byte is not allowed in input")),i.input+="\0";32===i.input.charCodeAt(i.position);)i.lineIndent+=1,i.position+=1;for(;i.position<i.length-1;)So(i);return i.documents}var Fo=kr,wo=function(t,e){var i=Bo(t,e);if(0!==i.length){if(1===i.length)return i[0];throw new or("expected a single document in the stream, but found more")}};const Ao=t=>t.replace(/\r\n?/g,"\n").replace(/<(\w+)([^>]*)>/g,((t,e,i)=>"<"+e+i.replace(/="([^"]*)"/g,"='$1'")+">")),Lo=t=>{const{text:e,metadata:i}=function(t){const e=t.match(zt);if(!e)return{text:t,metadata:{}};let i=wo(e[1],{schema:Fo})??{};i="object"!=typeof i||Array.isArray(i)?{}:i;const r={};return i.displayMode&&(r.displayMode=i.displayMode.toString()),i.title&&(r.title=i.title.toString()),i.config&&(r.config=i.config),{text:t.slice(e[0].length),metadata:r}}(t),{displayMode:r,title:o,config:n={}}=i;return r&&(n.gantt||(n.gantt={}),n.gantt.displayMode=r),{title:o,config:n,text:e}},Zo=t=>{const e=me.detectInit(t)??{},i=me.detectDirective(t,"wrap");return Array.isArray(i)?e.wrap=i.some((({type:t})=>{})):"wrap"===(null==i?void 0:i.type)&&(e.wrap=!0),{text:(r=t,r.replace(Nt,"")),directive:e};var r};function Eo(t){const e=Ao(t),i=Lo(e),r=Zo(i.text),o=ge(i.config,r.directive);return{code:t=Ki(r.text),title:i.title,config:o}}const Mo=["foreignobject"],Oo=["dominant-baseline"];function Io(t){const e=Eo(t);return Ae(),i=e.config??{},qt(i),!i.fontFamily||i.themeVariables&&i.themeVariables.fontFamily||(i.themeVariables={fontFamily:i.fontFamily}),ke.push(i),_e(ve,ke),e;var i}const qo=(t,e,i=[])=>`\n.${t} ${e} { ${i.join(" !important; ")} !important; }`,Do=(t,e,i,r)=>{const o=((t,e={})=>{var i;let r="";if(void 0!==t.themeCSS&&(r+=`\n${t.themeCSS}`),void 0!==t.fontFamily&&(r+=`\n:root { --mermaid-font-family: ${t.fontFamily}}`),void 0!==t.altFontFamily&&(r+=`\n:root { --mermaid-alt-font-family: ${t.altFontFamily}}`),!(0,ot.Z)(e)){const o=t.htmlLabels||(null==(i=t.flowchart)?void 0:i.htmlLabels)?["> *","span"]:["rect","polygon","ellipse","circle","path"];for(const t in e){const i=e[t];(0,ot.Z)(i.styles)||o.forEach((t=>{r+=qo(i.id,t,i.styles)})),(0,ot.Z)(i.textStyles)||(r+=qo(i.id,"tspan",i.textStyles))}}return r})(t,i);return E(K(`${r}{${((t,e,i)=>{let r="";return t in di&&di[t]?r=di[t](i):at.warn(`No theme found for ${t}`),` & {\n    font-family: ${i.fontFamily};\n    font-size: ${i.fontSize};\n    fill: ${i.textColor}\n  }\n\n  /* Classes common for multiple diagrams */\n\n  & .error-icon {\n    fill: ${i.errorBkgColor};\n  }\n  & .error-text {\n    fill: ${i.errorTextColor};\n    stroke: ${i.errorTextColor};\n  }\n\n  & .edge-thickness-normal {\n    stroke-width: 2px;\n  }\n  & .edge-thickness-thick {\n    stroke-width: 3.5px\n  }\n  & .edge-pattern-solid {\n    stroke-dasharray: 0;\n  }\n\n  & .edge-pattern-dashed{\n    stroke-dasharray: 3;\n  }\n  .edge-pattern-dotted {\n    stroke-dasharray: 2;\n  }\n\n  & .marker {\n    fill: ${i.lineColor};\n    stroke: ${i.lineColor};\n  }\n  & .marker.cross {\n    stroke: ${i.lineColor};\n  }\n\n  & svg {\n    font-family: ${i.fontFamily};\n    font-size: ${i.fontSize};\n  }\n\n  ${r}\n\n  ${e}\n`})(e,o,t.themeVariables)}}`),M)},zo=(t,e,i,r,o)=>{const n=t.append("div");n.attr("id",i),r&&n.attr("style",r);const a=n.append("svg").attr("id",e).attr("width","100%").attr("xmlns","http://www.w3.org/2000/svg");return o&&a.attr("xmlns:xlink",o),a.append("g"),t};function No(t,e){return t.append("iframe").attr("id",e).attr("style","width: 100%; height: 100%;").attr("sandbox","")}const Po=(t,e={})=>{const{code:i}=Eo(t);return(async(t,e={})=>{const i=Rt(t,Fe());try{Oi(i)}catch(t){const e=jt[i].loader;if(!e)throw new $t(`Diagram ${i} not found.`);const{id:r,diagram:o}=await e();Mi(r,o)}return new Xi(t,e)})(i,e)},$o=Object.freeze({render:async function(t,e,i){var r,o,n,a,l,c;Gi();const u=Io(e);e=u.code;const d=Fe();at.debug(d),e.length>((null==d?void 0:d.maxTextSize)??5e4)&&(e="graph TB;a[Maximum text size in diagram exceeded];style a fill:#faa");const f="#"+t,p="i"+t,g="#"+p,m="d"+t,y="#"+m;let C=(0,s.Ys)("body");const b="sandbox"===d.securityLevel,x="loose"===d.securityLevel,v=d.fontFamily;if(void 0!==i){if(i&&(i.innerHTML=""),b){const t=No((0,s.Ys)(i),p);C=(0,s.Ys)(t.nodes()[0].contentDocument.body),C.node().style.margin=0}else C=(0,s.Ys)(i);zo(C,t,m,`font-family: ${v}`,"http://www.w3.org/1999/xlink")}else{if(((t,e,i,r)=>{var o,n,a;null==(o=t.getElementById(e))||o.remove(),null==(n=t.getElementById(i))||n.remove(),null==(a=t.getElementById(r))||a.remove()})(document,t,m,p),b){const t=No((0,s.Ys)("body"),p);C=(0,s.Ys)(t.nodes()[0].contentDocument.body),C.node().style.margin=0}else C=(0,s.Ys)("body");zo(C,t,m)}let k,T;try{k=await Po(e,{title:u.title})}catch(t){k=new Xi("error"),T=t}const _=C.select(y).node(),S=k.type,B=_.firstChild,F=B.firstChild,w=null==(o=(r=k.renderer).getClasses)?void 0:o.call(r,e,k),A=Do(d,S,w,f),L=document.createElement("style");L.innerHTML=A,B.insertBefore(L,F);try{await k.renderer.draw(e,t,Ce,k)}catch(i){throw zi.draw(e,t,Ce),i}!function(t,e,i,r){(function(t,e){t.attr("role","graphics-document document"),""!==e&&t.attr("aria-roledescription",e)})(e,t),function(t,e,i,r){if(void 0!==t.insert){if(i){const e=`chart-desc-${r}`;t.attr("aria-describedby",e),t.insert("desc",":first-child").attr("id",e).text(i)}if(e){const i=`chart-title-${r}`;t.attr("aria-labelledby",i),t.insert("title",":first-child").attr("id",i).text(e)}}}(e,i,r,e.attr("id"))}(S,C.select(`${y} svg`),null==(a=(n=k.db).getAccTitle)?void 0:a.call(n),null==(c=(l=k.db).getAccDescription)?void 0:c.call(l)),C.select(`[id="${t}"]`).selectAll("foreignobject > *").attr("xmlns","http://www.w3.org/1999/xhtml");let Z=C.select(y).node().innerHTML;if(at.debug("config.arrowMarkerAbsolute",d.arrowMarkerAbsolute),Z=((t="",e,i)=>{let r=t;return i||e||(r=r.replace(/marker-end="url\([\d+./:=?A-Za-z-]*?#/g,'marker-end="url(#')),r=ye(r),r=r.replace(/<br>/g,"<br/>"),r})(Z,b,gt(d.arrowMarkerAbsolute)),b?Z=((t="",e)=>{var i,r;return`<iframe style="width:100%;height:${(null==(r=null==(i=null==e?void 0:e.viewBox)?void 0:i.baseVal)?void 0:r.height)?e.viewBox.baseVal.height+"px":"100%"};border:0;margin:0;" src="data:text/html;base64,${btoa('<body style="margin:0">'+t+"</body>")}" sandbox="allow-top-navigation-by-user-activation allow-popups">\n  The "iframe" tag is not supported by your browser.\n</iframe>`})(Z,C.select(y+" svg").node()):x||(Z=h().sanitize(Z,{ADD_TAGS:Mo,ADD_ATTR:Oo})),Ji.forEach((t=>{t()})),Ji=[],T)throw T;const E=b?g:y,M=(0,s.Ys)(E).node();return M&&"remove"in M&&M.remove(),{svg:Z,bindFunctions:k.db.bindFunctions}},parse:async function(t,e){Gi(),t=Io(t).code;try{await Po(t)}catch(t){if(null==e?void 0:e.suppressErrors)return!1;throw t}return!0},getDiagramFromText:Po,initialize:function(t={}){var e;(null==t?void 0:t.fontFamily)&&!(null==(e=t.themeVariables)?void 0:e.fontFamily)&&(t.themeVariables||(t.themeVariables={}),t.themeVariables.fontFamily=t.fontFamily),xe=Yt({},t),(null==t?void 0:t.theme)&&t.theme in Lt?t.themeVariables=Lt[t.theme].getThemeVariables(t.themeVariables):t&&(t.themeVariables=Lt.default.getThemeVariables(t.themeVariables));const i="object"==typeof t?(r=t,ve=Yt({},be),ve=Yt(ve,r),r.theme&&Lt[r.theme]&&(ve.themeVariables=Lt[r.theme].getThemeVariables(r.themeVariables)),_e(ve,ke),ve):Se();var r;st(i.logLevel),Gi()},getConfig:Fe,setConfig:Be,getSiteConfig:Se,updateSiteConfig:t=>(ve=Yt(ve,t),_e(ve,ke),ve),reset:()=>{Ae()},globalReset:()=>{Ae(be)},defaultConfig:be});st(Fe().logLevel),Ae(Fe());const jo=(t,e,i)=>{at.warn(t),fe(t)?(i&&i(t.str,t.hash),e.push({...t,message:t.str,error:t})):(i&&i(t),t instanceof Error&&e.push({str:t.message,message:t.message,hash:t.name,error:t}))},Ro=async function(t={querySelector:".mermaid"}){try{await Wo(t)}catch(e){if(fe(e)&&at.error(e.str),Jo.parseError&&Jo.parseError(e),!t.suppressErrors)throw at.error("Use the suppressErrors option to suppress these errors"),e}},Wo=async function({postRenderCallback:t,querySelector:e,nodes:i}={querySelector:".mermaid"}){const o=$o.getConfig();let n;if(at.debug((t?"":"No ")+"Callback function found"),i)n=i;else{if(!e)throw new Error("Nodes and querySelector are both undefined");n=document.querySelectorAll(e)}at.debug(`Found ${n.length} diagrams`),void 0!==(null==o?void 0:o.startOnLoad)&&(at.debug("Start On Load: "+(null==o?void 0:o.startOnLoad)),$o.updateSiteConfig({startOnLoad:null==o?void 0:o.startOnLoad}));const a=new me.InitIDGenerator(o.deterministicIds,o.deterministicIDSeed);let s;const l=[];for(const e of Array.from(n)){if(at.info("Rendering diagram: "+e.id),e.getAttribute("data-processed"))continue;e.setAttribute("data-processed","true");const i=`mermaid-${a.next()}`;s=e.innerHTML,s=(0,r.Z)(me.entityDecode(s)).trim().replace(/<br\s*\/?>/gi,"<br/>");const o=me.detectInit(s);o&&at.debug("Detected early reinit: ",o);try{const{svg:r,bindFunctions:o}=await Xo(i,s,e);e.innerHTML=r,t&&await t(i),o&&o(e)}catch(t){jo(t,l,Jo.parseError)}}if(l.length>0)throw l[0]},Ho=function(t){$o.initialize(t)},Uo=function(){if(Jo.startOnLoad){const{startOnLoad:t}=$o.getConfig();t&&Jo.run().catch((t=>at.error("Mermaid failed to initialize",t)))}};"undefined"!=typeof document&&window.addEventListener("load",Uo,!1);const Yo=[];let Vo=!1;const Go=async()=>{if(!Vo){for(Vo=!0;Yo.length>0;){const t=Yo.shift();if(t)try{await t()}catch(t){at.error("Error executing queue",t)}}Vo=!1}},Xo=(t,e,i)=>new Promise(((r,o)=>{Yo.push((()=>new Promise(((n,a)=>{$o.render(t,e,i).then((t=>{n(t),r(t)}),(t=>{var e;at.error("Error parsing",t),null==(e=Jo.parseError)||e.call(Jo,t),a(t),o(t)}))})))),Go().catch(o)})),Jo={startOnLoad:!0,mermaidAPI:$o,parse:async(t,e)=>new Promise(((i,r)=>{Yo.push((()=>new Promise(((o,n)=>{$o.parse(t,e).then((t=>{o(t),i(t)}),(t=>{var e;at.error("Error parsing",t),null==(e=Jo.parseError)||e.call(Jo,t),n(t),r(t)}))})))),Go().catch(r)})),render:Xo,init:async function(t,e,i){at.warn("mermaid.init is deprecated. Please use run instead."),t&&Ho(t);const r={postRenderCallback:i,querySelector:".mermaid"};"string"==typeof e?r.querySelector=e:e&&(e instanceof HTMLElement?r.nodes=[e]:r.nodes=e),await Ro(r)},run:Ro,registerExternalDiagrams:async(t,{lazyLoad:e=!0}={})=>{Wt(...t),!1===e&&await(async()=>{at.debug("Loading registered diagrams");const t=(await Promise.allSettled(Object.entries(jt).map((async([t,{detector:e,loader:i}])=>{if(i)try{Oi(t)}catch(r){try{const{diagram:t,id:r}=await i();Mi(r,t,e)}catch(e){throw at.error(`Failed to load external diagram with key ${t}. Removing from detectors.`),delete jt[t],e}}})))).filter((t=>"rejected"===t.status));if(t.length>0){at.error(`Failed to load ${t.length} external diagrams`);for(const e of t)at.error(e);throw new Error(`Failed to load ${t.length} external diagrams`)}})()},initialize:Ho,parseError:void 0,contentLoaded:Uo,setParseErrorHandler:function(t){Jo.parseError=t},detectType:Rt}},84630:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>r.L});var r=i(24028);i(27693),i(7608),i(23617),i(31699)},11464:(t,e,i)=>{"use strict";function r(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];var r=Array.from("string"==typeof t?[t]:t);r[r.length-1]=r[r.length-1].replace(/\r?\n([\t ]*)$/,"");var o=r.reduce((function(t,e){var i=e.match(/\n([\t ]+|(?!\s).)/g);return i?t.concat(i.map((function(t){var e,i;return null!==(i=null===(e=t.match(/[\t ]/g))||void 0===e?void 0:e.length)&&void 0!==i?i:0}))):t}),[]);if(o.length){var n=new RegExp("\n[\t ]{"+Math.min.apply(Math,o)+"}","g");r=r.map((function(t){return t.replace(n,"\n")}))}r[0]=r[0].replace(/^\r?\n/,"");var a=r[0];return e.forEach((function(t,e){var i=a.match(/(?:^|\n)( *)$/),o=i?i[1]:"",n=t;"string"==typeof t&&t.includes("\n")&&(n=String(t).split("\n").map((function(t,e){return 0===e?t:""+o+t})).join("\n")),a+=n+r[e+1]})),a}i.d(e,{Z:()=>r})}}]);
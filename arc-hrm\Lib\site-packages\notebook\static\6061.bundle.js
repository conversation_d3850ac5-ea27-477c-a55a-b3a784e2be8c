"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[6061],{16061:(e,a,t)=>{t.r(a),t.d(a,{RenderedVega:()=>l,VEGALITE3_MIME_TYPE:()=>s,VEGALITE4_MIME_TYPE:()=>o,VEGALITE5_MIME_TYPE:()=>r,VEGA_MIME_TYPE:()=>n,default:()=>g,rendererFactory:()=>d});var i=t(31516);const n="application/vnd.vega.v5+json",s="application/vnd.vegalite.v3+json",o="application/vnd.vegalite.v4+json",r="application/vnd.vegalite.v5+json";class l extends i.Widget{constructor(e){super(),this._mimeType=e.mimeType,this._resolver=e.resolver,this.addClass("jp-RenderedVegaCommon5"),this.addClass(this._mimeType===n?"jp-RenderedVega5":"jp-RenderedVegaLite")}async renderModel(e){const a=e.data[this._mimeType];if(void 0===a)return;const t=e.metadata[this._mimeType],i=t&&t.embed_options?t.embed_options:{};"false"===document.body.dataset.jpThemeLight&&(i.theme="dark");const s=this._mimeType===n?"vega":"vega-lite",o=null!=c.vega?c.vega:await c.ensureVega(),r=document.createElement("div");this.node.textContent="",this.node.appendChild(r),this._result&&this._result.finalize();const l=o.vega.loader({http:{credentials:"same-origin"}});if(this._result=await o.default(r,a,{actions:!0,defaultStyle:!0,...i,mode:s,loader:{...l,sanitize:async(e,a)=>{const t=this._resolver;if((null==t?void 0:t.isLocal)&&t.isLocal(e)){const a=await t.resolveUrl(e);e=await t.getDownloadUrl(a)}return l.sanitize(e,a)}}}),e.data["image/png"])return;const d=await this._result.view.toImageURL("png","number"==typeof i.scaleFactor?i.scaleFactor:i.scaleFactor?i.scaleFactor.png:i.scaleFactor);e.setData({data:{...e.data,"image/png":d.split(",")[1]}})}dispose(){this._result&&this._result.finalize(),super.dispose()}}const d={safe:!0,mimeTypes:[n,s,o,r],createRenderer:e=>new l(e)},g={id:"@jupyterlab/vega5-extension:factory",description:"Provides a renderer for Vega 5 and Vega-Lite 3 to 5 content.",rendererFactory:d,rank:57,dataType:"json",documentWidgetFactoryOptions:[{name:"Vega5",primaryFileType:"vega5",fileTypes:["vega5","json"],defaultFor:["vega5"]},{name:"Vega-Lite5",primaryFileType:"vega-lite5",fileTypes:["vega-lite3","vega-lite4","vega-lite5","json"],defaultFor:["vega-lite3","vega-lite4","vega-lite5"]}],fileTypes:[{mimeTypes:[n],name:"vega5",extensions:[".vg",".vg.json",".vega"],icon:"ui-components:vega"},{mimeTypes:[r],name:"vega-lite5",extensions:[".vl",".vl.json",".vegalite"],icon:"ui-components:vega"},{mimeTypes:[o],name:"vega-lite4",extensions:[],icon:"ui-components:vega"},{mimeTypes:[s],name:"vega-lite3",extensions:[],icon:"ui-components:vega"}]};var c;!function(e){e.ensureVega=function(){return e.vegaReady||(e.vegaReady=Promise.all([t.e(4499),t.e(1326)]).then(t.bind(t,21326))),e.vegaReady}}(c||(c={}))}}]);
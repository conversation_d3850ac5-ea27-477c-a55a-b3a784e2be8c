"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[8929],{98929:(t,e,i)=>{i.r(e),i.d(e,{AsyncCellRenderer:()=>C,BasicKeyHandler:()=>f,BasicMouseHandler:()=>y,BasicSelectionModel:()=>x,BooleanCellEditor:()=>T,CellEditor:()=>z,CellEditorController:()=>A,CellGroup:()=>o,CellRenderer:()=>g,DataGrid:()=>j,DataModel:()=>I,DateCellEditor:()=>W,DynamicOptionCellEditor:()=>D,GraphicsContext:()=>N,HyperlinkRenderer:()=>w,ImageRenderer:()=>Z,InputCellEditor:()=>O,IntegerCellEditor:()=>L,IntegerInputValidator:()=>H,JSONModel:()=>U,MutableDataModel:()=>P,NumberCellEditor:()=>E,NumberInputValidator:()=>R,OptionCellEditor:()=>B,PassInputValidator:()=>S,RendererMap:()=>K,SectionList:()=>q,SelectionModel:()=>v,TextCellEditor:()=>k,TextInputValidator:()=>b,TextRenderer:()=>p,resolveOption:()=>G});var s,o,r,n,l=i(18395),a=i(80554),h=i(99615),c=i(33625),d=i(81997),u=i(31516),_=i(49503),m=i(20998);class f{constructor(){this._disposed=!1}get isDisposed(){return this._disposed}dispose(){this._disposed=!0}onKeyDown(t,e){if(t.editable&&-1!==t.selectionModel.cursorRow&&-1!==t.selectionModel.cursorColumn){const i=String.fromCharCode(e.keyCode);if(/[a-zA-Z0-9-_ ]/.test(i)){const i={grid:t,row:t.selectionModel.cursorRow,column:t.selectionModel.cursorColumn};return t.editorController.edit(i),void("Space"===(0,a.getKeyboardLayout)().keyForKeydownEvent(e)&&(e.stopPropagation(),e.preventDefault()))}}switch((0,a.getKeyboardLayout)().keyForKeydownEvent(e)){case"ArrowLeft":this.onArrowLeft(t,e);break;case"ArrowRight":this.onArrowRight(t,e);break;case"ArrowUp":this.onArrowUp(t,e);break;case"ArrowDown":this.onArrowDown(t,e);break;case"PageUp":this.onPageUp(t,e);break;case"PageDown":this.onPageDown(t,e);break;case"Escape":this.onEscape(t,e);break;case"Delete":this.onDelete(t,e);break;case"C":this.onKeyC(t,e);break;case"Enter":t.selectionModel&&(t.moveCursor(e.shiftKey?"up":"down"),t.scrollToCursor());break;case"Tab":t.selectionModel&&(t.moveCursor(e.shiftKey?"left":"right"),t.scrollToCursor(),e.stopPropagation(),e.preventDefault())}}onArrowLeft(t,e){e.preventDefault(),e.stopPropagation();let i=t.selectionModel,s=e.shiftKey,o=l.Platform.accelKey(e);if(!i&&o)return void t.scrollTo(0,t.scrollY);if(!i)return void t.scrollByStep("left");let r=i.selectionMode;if("row"===r&&o)return void t.scrollTo(0,t.scrollY);if("row"===r)return void t.scrollByStep("left");let n,a,h,c,d,u,_,m=i.cursorRow,f=i.cursorColumn,g=i.currentSelection();o&&s?(n=g?g.r1:0,a=g?g.r2:0,h=g?g.c1:0,c=0,d=m,u=f,_="current"):s?(n=g?g.r1:0,a=g?g.r2:0,h=g?g.c1:0,c=g?g.c2-1:0,d=m,u=f,_="current"):o?(n=m,a=m,h=0,c=0,d=n,u=h,_="all"):(n=m,a=m,h=f-1,c=f-1,d=n,u=h,_="all"),i.select({r1:n,c1:h,r2:a,c2:c,cursorRow:d,cursorColumn:u,clear:_}),g=i.currentSelection(),g&&(s||"column"===r?t.scrollToColumn(g.c2):t.scrollToCursor())}onArrowRight(t,e){e.preventDefault(),e.stopPropagation();let i=t.selectionModel,s=e.shiftKey,o=l.Platform.accelKey(e);if(!i&&o)return void t.scrollTo(t.maxScrollX,t.scrollY);if(!i)return void t.scrollByStep("right");let r=i.selectionMode;if("row"===r&&o)return void t.scrollTo(t.maxScrollX,t.scrollY);if("row"===r)return void t.scrollByStep("right");let n,a,h,c,d,u,_,m=i.cursorRow,f=i.cursorColumn,g=i.currentSelection();o&&s?(n=g?g.r1:0,a=g?g.r2:0,h=g?g.c1:0,c=1/0,d=m,u=f,_="current"):s?(n=g?g.r1:0,a=g?g.r2:0,h=g?g.c1:0,c=g?g.c2+1:0,d=m,u=f,_="current"):o?(n=m,a=m,h=1/0,c=1/0,d=n,u=h,_="all"):(n=m,a=m,h=f+1,c=f+1,d=n,u=h,_="all"),i.select({r1:n,c1:h,r2:a,c2:c,cursorRow:d,cursorColumn:u,clear:_}),g=i.currentSelection(),g&&(s||"column"===r?t.scrollToColumn(g.c2):t.scrollToCursor())}onArrowUp(t,e){e.preventDefault(),e.stopPropagation();let i=t.selectionModel,s=e.shiftKey,o=l.Platform.accelKey(e);if(!i&&o)return void t.scrollTo(t.scrollX,0);if(!i)return void t.scrollByStep("up");let r=i.selectionMode;if("column"===r&&o)return void t.scrollTo(t.scrollX,0);if("column"===r)return void t.scrollByStep("up");let n,a,h,c,d,u,_,m=i.cursorRow,f=i.cursorColumn,g=i.currentSelection();o&&s?(n=g?g.r1:0,a=0,h=g?g.c1:0,c=g?g.c2:0,d=m,u=f,_="current"):s?(n=g?g.r1:0,a=g?g.r2-1:0,h=g?g.c1:0,c=g?g.c2:0,d=m,u=f,_="current"):o?(n=0,a=0,h=f,c=f,d=n,u=h,_="all"):(n=m-1,a=m-1,h=f,c=f,d=n,u=h,_="all"),i.select({r1:n,c1:h,r2:a,c2:c,cursorRow:d,cursorColumn:u,clear:_}),g=i.currentSelection(),g&&(s||"row"===r?t.scrollToRow(g.r2):t.scrollToCursor())}onArrowDown(t,e){e.preventDefault(),e.stopPropagation();let i=t.selectionModel,s=e.shiftKey,o=l.Platform.accelKey(e);if(!i&&o)return void t.scrollTo(t.scrollX,t.maxScrollY);if(!i)return void t.scrollByStep("down");let r=i.selectionMode;if("column"===r&&o)return void t.scrollTo(t.scrollX,t.maxScrollY);if("column"===r)return void t.scrollByStep("down");let n,a,h,c,d,u,_,m=i.cursorRow,f=i.cursorColumn,g=i.currentSelection();o&&s?(n=g?g.r1:0,a=1/0,h=g?g.c1:0,c=g?g.c2:0,d=m,u=f,_="current"):s?(n=g?g.r1:0,a=g?g.r2+1:0,h=g?g.c1:0,c=g?g.c2:0,d=m,u=f,_="current"):o?(n=1/0,a=1/0,h=f,c=f,d=n,u=h,_="all"):(n=m+1,a=m+1,h=f,c=f,d=n,u=h,_="all"),i.select({r1:n,c1:h,r2:a,c2:c,cursorRow:d,cursorColumn:u,clear:_}),g=i.currentSelection(),g&&(s||"row"===r?t.scrollToRow(g.r2):t.scrollToCursor())}onPageUp(t,e){if(l.Platform.accelKey(e))return;e.preventDefault(),e.stopPropagation();let i=t.selectionModel;if(!i||"column"===i.selectionMode)return void t.scrollByPage("up");let s,o,r,n,a,h,c,d=Math.floor(t.pageHeight/t.defaultSizes.rowHeight),u=i.cursorRow,_=i.cursorColumn,m=i.currentSelection();e.shiftKey?(s=m?m.r1:0,o=m?m.r2-d:0,r=m?m.c1:0,n=m?m.c2:0,a=u,h=_,c="current"):(s=m?m.r1-d:0,o=s,r=_,n=_,a=s,h=_,c="all"),i.select({r1:s,c1:r,r2:o,c2:n,cursorRow:a,cursorColumn:h,clear:c}),m=i.currentSelection(),m&&t.scrollToRow(m.r2)}onPageDown(t,e){if(l.Platform.accelKey(e))return;e.preventDefault(),e.stopPropagation();let i=t.selectionModel;if(!i||"column"===i.selectionMode)return void t.scrollByPage("down");let s,o,r,n,a,h,c,d=Math.floor(t.pageHeight/t.defaultSizes.rowHeight),u=i.cursorRow,_=i.cursorColumn,m=i.currentSelection();e.shiftKey?(s=m?m.r1:0,o=m?m.r2+d:0,r=m?m.c1:0,n=m?m.c2:0,a=u,h=_,c="current"):(s=m?m.r1+d:0,o=s,r=_,n=_,a=s,h=_,c="all"),i.select({r1:s,c1:r,r2:o,c2:n,cursorRow:a,cursorColumn:h,clear:c}),m=i.currentSelection(),m&&t.scrollToRow(m.r2)}onEscape(t,e){t.selectionModel&&t.selectionModel.clear()}onDelete(t,e){if(t.editable&&!t.selectionModel.isEmpty){const e=t.dataModel;let i=e.rowCount("body")-1,s=e.columnCount("body")-1;for(let o of t.selectionModel.selections()){let t=Math.max(0,Math.min(o.r1,i)),r=Math.max(0,Math.min(o.c1,s)),n=Math.max(0,Math.min(o.r2,i)),l=Math.max(0,Math.min(o.c2,s));for(let i=t;i<=n;++i)for(let t=r;t<=l;++t)e.setData("body",i,t,null)}}}onKeyC(t,e){!e.shiftKey&&l.Platform.accelKey(e)&&(e.preventDefault(),e.stopPropagation(),t.copyToClipboard())}}class g{}!function(t){t.resolveOption=function(t,e){return"function"==typeof t?t(e):t}}(g||(g={}));class p extends g{constructor(t={}){super(),this.font=t.font||"12px sans-serif",this.textColor=t.textColor||"#000000",this.backgroundColor=t.backgroundColor||"",this.verticalAlignment=t.verticalAlignment||"center",this.horizontalAlignment=t.horizontalAlignment||"left",this.horizontalPadding=t.horizontalPadding||8,this.format=t.format||p.formatGeneric(),this.elideDirection=t.elideDirection||"none",this.wrapText=t.wrapText||!1}paint(t,e){this.drawBackground(t,e),this.drawText(t,e)}drawBackground(t,e){let i=g.resolveOption(this.backgroundColor,e);i&&(t.fillStyle=i,t.fillRect(e.x,e.y,e.width,e.height))}getText(t){return this.format(t)}drawText(t,e){let i=g.resolveOption(this.font,e);if(!i)return;let s=g.resolveOption(this.textColor,e);if(!s)return;let o=this.getText(e);if(!o)return;let r=g.resolveOption(this.verticalAlignment,e),n=g.resolveOption(this.horizontalAlignment,e),l=g.resolveOption(this.elideDirection,e),a=g.resolveOption(this.wrapText,e),h=e.height-("center"===r?1:2);if(h<=0)return;let c,d,u,_=p.measureFontHeight(i);switch(r){case"top":d=e.y+2+_;break;case"center":d=e.y+e.height/2+_/2;break;case"bottom":d=e.y+e.height-2;break;default:throw"unreachable"}switch(n){case"left":c=e.x+this.horizontalPadding,u=e.width-14;break;case"center":c=e.x+e.width/2,u=e.width;break;case"right":c=e.x+e.width-this.horizontalPadding,u=e.width-14;break;default:throw"unreachable"}if(_>h&&(t.beginPath(),t.rect(e.x,e.y,e.width,e.height-1),t.clip()),t.font=i,t.fillStyle=s,t.textAlign=n,t.textBaseline="bottom","none"===l&&!a)return void t.fillText(o,c,d);let m=t.measureText(o).width;if(a&&m>u){t.beginPath(),t.rect(e.x,e.y,e.width,e.height-1),t.clip();const i=o.split(/\s(?=\b)/);let s=d,r=i.shift();if(0===i.length){let e=t.measureText(r).width;for(;e>u&&""!==r;)for(let i=r.length;i>0;i--){const o=r.substring(0,i);if(t.measureText(o).width<u||1===o.length){const n=r.substring(i,r.length);r=n,e=t.measureText(r).width,t.fillText(o,c,s),s+=_;break}}}else for(;0!==i.length;){const e=i.shift(),o=[r,e].join(" ");t.measureText(o).width>u?(t.fillText(r,c,s),s+=_,r=e):r=o}return void t.fillText(r,c,s)}const f="…";for(;m>u&&o.length>1;){const e=[...o];o="right"===l?e.length>4&&m>=2*u?e.slice(0,Math.floor(e.length/2+1)).join("")+f:e.slice(0,e.length-2).join("")+f:e.length>4&&m>=2*u?f+e.slice(Math.floor(e.length/2)).join(""):f+e.slice(2).join(""),m=t.measureText(o).width}t.fillText(o,c,d)}}!function(t){t.formatGeneric=function(t={}){let e=t.missing||"";return({value:t})=>null==t?e:String(t)},t.formatFixed=function(t={}){let e=t.digits,i=t.missing||"";return({value:t})=>null==t?i:Number(t).toFixed(e)},t.formatPrecision=function(t={}){let e=t.digits,i=t.missing||"";return({value:t})=>null==t?i:Number(t).toPrecision(e)},t.formatExponential=function(t={}){let e=t.digits,i=t.missing||"";return({value:t})=>null==t?i:Number(t).toExponential(e)},t.formatIntlNumber=function(t={}){let e=t.missing||"",i=new Intl.NumberFormat(t.locales,t.options);return({value:t})=>null==t?e:i.format(t)},t.formatDate=function(t={}){let e=t.missing||"";return({value:t})=>null==t?e:t instanceof Date?t.toDateString():new Date(t).toDateString()},t.formatTime=function(t={}){let e=t.missing||"";return({value:t})=>null==t?e:t instanceof Date?t.toTimeString():new Date(t).toTimeString()},t.formatISODateTime=function(t={}){let e=t.missing||"";return({value:t})=>null==t?e:t instanceof Date?t.toISOString():new Date(t).toISOString()},t.formatUTCDateTime=function(t={}){let e=t.missing||"";return({value:t})=>null==t?e:t instanceof Date?t.toUTCString():new Date(t).toUTCString()},t.formatIntlDateTime=function(t={}){let e=t.missing||"",i=new Intl.DateTimeFormat(t.locales,t.options);return({value:t})=>null==t?e:i.format(t)},t.measureFontHeight=function(t){let e=s.fontHeightCache[t];if(void 0!==e)return e;s.fontMeasurementGC.font=t;let i=s.fontMeasurementGC.font;return s.fontMeasurementNode.style.font=i,document.body.appendChild(s.fontMeasurementNode),e=s.fontMeasurementNode.offsetHeight,document.body.removeChild(s.fontMeasurementNode),s.fontHeightCache[t]=e,s.fontHeightCache[i]=e,e}}(p||(p={})),function(t){t.fontHeightCache=Object.create(null),t.fontMeasurementNode=(()=>{let t=document.createElement("div");return t.style.position="absolute",t.style.top="-99999px",t.style.left="-99999px",t.style.visibility="hidden",t.textContent="M",t})(),t.fontMeasurementGC=(()=>{let t=document.createElement("canvas");return t.width=0,t.height=0,t.getContext("2d")})()}(s||(s={}));class w extends p{constructor(t={}){t.textColor=t.textColor||"navy",t.font=t.font||"bold 12px sans-serif",super(t),this.url=t.url,this.urlName=t.urlName}getText(t){let e=g.resolveOption(this.urlName,t);return e?this.format({...t,value:e}):this.format(t)}drawText(t,e){let i=g.resolveOption(this.font,e);if(!i)return;let s=g.resolveOption(this.textColor,e);if(!s)return;let o=this.getText(e);if(!o)return;let r=g.resolveOption(this.verticalAlignment,e),n=g.resolveOption(this.horizontalAlignment,e),l=g.resolveOption(this.elideDirection,e),a=g.resolveOption(this.wrapText,e),h=e.height-("center"===r?1:2);if(h<=0)return;let c,d,u,_=w.measureFontHeight(i);switch(r){case"top":d=e.y+2+_;break;case"center":d=e.y+e.height/2+_/2;break;case"bottom":d=e.y+e.height-2;break;default:throw"unreachable"}switch(n){case"left":c=e.x+8,u=e.width-14;break;case"center":c=e.x+e.width/2,u=e.width;break;case"right":c=e.x+e.width-8,u=e.width-14;break;default:throw"unreachable"}if(_>h&&(t.beginPath(),t.rect(e.x,e.y,e.width,e.height-1),t.clip()),t.font=i,t.fillStyle=s,t.textAlign=n,t.textBaseline="bottom","none"===l&&!a)return void t.fillText(o,c,d);let m=t.measureText(o).width;if(a&&m>u){t.beginPath(),t.rect(e.x,e.y,e.width,e.height-1),t.clip();const i=o.split(/\s(?=\b)/);let s=d,r=i.shift();if(0===i.length){let e=t.measureText(r).width;for(;e>u&&""!==r;)for(let i=r.length;i>0;i--){const o=r.substring(0,i);if(t.measureText(o).width<u||1===o.length){const n=r.substring(i,r.length);r=n,e=t.measureText(r).width,t.fillText(o,c,s),s+=_;break}}}else for(;0!==i.length;){const e=i.shift(),o=[r,e].join(" ");t.measureText(o).width>u?(t.fillText(r,c,s),s+=_,r=e):r=o}return void t.fillText(r,c,s)}let f="…";if("right"===l)for(;m>u&&o.length>1;)o=o.length>4&&m>=2*u?o.substring(0,o.length/2+1)+f:o.substring(0,o.length-2)+f,m=t.measureText(o).width;else for(;m>u&&o.length>1;)o=o.length>4&&m>=2*u?f+o.substring(o.length/2):f+o.substring(2),m=t.measureText(o).width;t.fillText(o,c,d)}}!function(t){function e(t,e){return(t.r1>=e.r1&&t.r1<=e.r2||t.r2>=e.r1&&t.r2<=e.r2||e.r1>=t.r1&&e.r1<=t.r2||e.r2>=t.r1&&e.r2<=t.r2)&&(t.c1>=e.c1&&t.c1<=e.c2||t.c2>=e.c1&&t.c2<=e.c2||e.c1>=t.c1&&e.c1<=t.c2||e.c2>=t.c1&&e.c2<=t.c2)}function i(t,e,i,s){const o=t.groupCount(e);for(let r=0;r<o;r++){const o=t.group(e,r);if(i>=o.r1&&i<=o.r2&&s>=o.c1&&s<=o.c2)return r}return-1}function s(t,e){let i=[];const s=t.groupCount(e);for(let o=0;o<s;o++){const s=t.group(e,o);i.push(s)}return i}function o(t){let e=Number.MAX_VALUE,i=Number.MIN_VALUE,s=Number.MAX_VALUE,o=Number.MIN_VALUE;for(const r of t)e=Math.min(e,r.r1),i=Math.max(i,r.r2),s=Math.min(s,r.c1),o=Math.max(o,r.c2);return{r1:e,r2:i,c1:s,c2:o}}t.areCellGroupsIntersectingAtAxis=function(t,e,i){return"row"===i?t.r1>=e.r1&&t.r1<=e.r2||t.r2>=e.r1&&t.r2<=e.r2||e.r1>=t.r1&&e.r1<=t.r2||e.r2>=t.r1&&e.r2<=t.r2:t.c1>=e.c1&&t.c1<=e.c2||t.c2>=e.c1&&t.c2<=e.c2||e.c1>=t.c1&&e.c1<=t.c2||e.c2>=t.c1&&e.c2<=t.c2},t.areCellGroupsIntersecting=e,t.getGroupIndex=i,t.getGroup=function(t,e,s,o){const r=i(t,e,s,o);return-1===r?null:t.group(e,r)},t.getCellGroupsAtRegion=s,t.joinCellGroups=o,t.joinCellGroupWithMergedCellGroups=function(t,i,r){let n={...i};const l=s(t,r);for(let t=0;t<l.length;t++){const i=l[t];e(n,i)&&(n=o([n,i]))}return n},t.getCellGroupsAtRow=function(t,e,i){let s=[];const o=t.groupCount(e);for(let r=0;r<o;r++){const o=t.group(e,r);i>=o.r1&&i<=o.r2&&s.push(o)}return s},t.getCellGroupsAtColumn=function(t,e,i){let s=[];const o=t.groupCount(e);for(let r=0;r<o;r++){const o=t.group(e,r);i>=o.c1&&i<=o.c2&&s.push(o)}return s},t.joinCellGroupsIntersectingAtAxis=function(e,i,s,o){let r=[];if("row"===s)for(const s of i)for(let i=o.r1;i<=o.r2;i++)r=r.concat(t.getCellGroupsAtRow(e,s,i));else for(const s of i)for(let i=o.c1;i<=o.c2;i++)r=r.concat(t.getCellGroupsAtColumn(e,s,i));let n=t.joinCellGroups(r);if(r.length>0){let o=[];for(const s of i)o=o.concat(t.getCellGroupsAtRegion(e,s));for(let e=0;e<o.length;e++){const i=o[e];t.areCellGroupsIntersectingAtAxis(n,i,s)&&(n=t.joinCellGroups([i,n]),o.splice(e,1),e=0)}}return n}}(o||(o={}));class y{constructor(){this._disposed=!1,this._pressData=null}dispose(){this._disposed||(this.release(),this._disposed=!0)}get isDisposed(){return this._disposed}release(){this._pressData&&("select"===this._pressData.type&&(this._pressData.timeout=-1),this._pressData.override.dispose(),this._pressData=null)}onMouseHover(t,e){let i=t.hitTest(e.clientX,e.clientY),s=r.resizeHandleForHitTest(i),o=this.cursorForHandle(s);const n=r.createCellConfigObject(t,i);n&&t.cellRenderers.get(n)instanceof w&&(o=this.cursorForHandle("hyperlink")),t.viewport.node.style.cursor=o}onMouseLeave(t,e){t.viewport.node.style.cursor=""}onMouseDown(t,e){let{clientX:i,clientY:s}=e,n=t.hitTest(i,s);const{region:a,row:c,column:d}=n;if("void"===a)return;let u=e.shiftKey,_=l.Platform.accelKey(e);if(t){const e=r.createCellConfigObject(t,n);let i=t.cellRenderers.get(e);if(i instanceof w){let s=g.resolveOption(i.url,e);if(s||(s=p.formatGeneric()(e)),_){window.open(s);const e=this.cursorForHandle("none");return void(t.viewport.node.style.cursor=e)}}}if("body"===a){let e=t.selectionModel;if(!e)return;let i,s,o,r,n,l,m,f=h.Drag.overrideCursor("default");return this._pressData={type:"select",region:a,row:c,column:d,override:f,localX:-1,localY:-1,timeout:-1},_?(i=c,o=c,s=d,r=d,n=c,l=d,m="none"):u?(i=e.cursorRow,o=c,s=e.cursorColumn,r=d,n=e.cursorRow,l=e.cursorColumn,m="current"):(i=c,o=c,s=d,r=d,n=c,l=d,m="all"),void e.select({r1:i,c1:s,r2:o,c2:r,cursorRow:n,cursorColumn:l,clear:m})}let m=r.resizeHandleForHitTest(n),f=this.cursorForHandle(m);if("left"===m||"right"===m){const e="column-resize";let s="column-header"===a?"body":"row-header",o="left"===m?d-1:d,r=t.columnSize(s,o),n=h.Drag.overrideCursor(f);return void(this._pressData={type:e,region:s,index:o,size:r,clientX:i,override:n})}if("top"===m||"bottom"===m){const e="row-resize";let i="row-header"===a?"body":"column-header",o="top"===m?c-1:c,r=t.rowSize(i,o),n=h.Drag.overrideCursor(f);return void(this._pressData={type:e,region:i,index:o,size:r,clientY:s,override:n})}let y=t.selectionModel;if(!y)return;let v,x,C,M,S,b,H,R=h.Drag.overrideCursor("default");if(this._pressData={type:"select",region:a,row:c,column:d,override:R,localX:-1,localY:-1,timeout:-1},"corner-header"===a)v=0,C=1/0,x=0,M=1/0,S=_?0:u?y.cursorRow:0,b=_?0:u?y.cursorColumn:0,H=_?"none":u?"current":"all";else if("row-header"===a){v=_?c:u?y.cursorRow:c,C=c;const e={r1:v,c1:0,r2:C,c2:0},i=o.joinCellGroupsIntersectingAtAxis(t.dataModel,["row-header","body"],"row",e);i.r1!=Number.MAX_VALUE&&(v=i.r1,C=i.r2),x=0,M=1/0,S=_?c:u?y.cursorRow:c,b=_?0:u?y.cursorColumn:0,H=_?"none":u?"current":"all"}else if("column-header"===a){v=0,C=1/0,x=_?d:u?y.cursorColumn:d,M=d;const e={r1:0,c1:x,r2:0,c2:M},i=o.joinCellGroupsIntersectingAtAxis(t.dataModel,["column-header","body"],"column",e);i.c1!=Number.MAX_VALUE&&(x=i.c1,M=i.c2),S=_?0:u?y.cursorRow:0,b=_?d:u?y.cursorColumn:d,H=_?"none":u?"current":"all"}else v=_?c:u?y.cursorRow:c,C=c,x=_?d:u?y.cursorColumn:d,M=d,S=_?c:u?y.cursorRow:c,b=_?d:u?y.cursorColumn:d,H=_?"none":u?"current":"all";y.select({r1:v,c1:x,r2:C,c2:M,cursorRow:S,cursorColumn:b,clear:H})}onMouseMove(t,e){const i=this._pressData;if(!i)return;if("row-resize"===i.type){let s=e.clientY-i.clientY;return void t.resizeRow(i.region,i.index,i.size+s)}if("column-resize"===i.type){let s=e.clientX-i.clientX;return void t.resizeColumn(i.region,i.index,i.size+s)}if("corner-header"===i.region)return;let s=t.selectionModel;if(!s)return;let{lx:n,ly:l}=t.mapToLocal(e.clientX,e.clientY);i.localX=n,i.localY=l;let a=t.headerWidth,h=t.headerHeight,c=t.viewportWidth,d=t.viewportHeight,u=t.scrollX,_=t.scrollY,m=t.maxScrollY,f=t.maxScrollY,g=s.selectionMode,p=-1;if("row-header"===i.region||"row"===g?l<h&&_>0?p=r.computeTimeout(h-l):l>=d&&_<f&&(p=r.computeTimeout(l-d)):"column-header"===i.region||"column"===g?n<a&&u>0?p=r.computeTimeout(a-n):n>=c&&u<m&&(p=r.computeTimeout(n-c)):n<a&&u>0?p=r.computeTimeout(a-n):n>=c&&u<m?p=r.computeTimeout(n-c):l<h&&_>0?p=r.computeTimeout(h-l):l>=d&&_<f&&(p=r.computeTimeout(l-d)),p>=0)return void(i.timeout<0?(i.timeout=p,setTimeout((()=>{r.autoselect(t,i)}),p)):i.timeout=p);i.timeout=-1;let w,y,v,x,{vx:C,vy:M}=t.mapToVirtual(e.clientX,e.clientY);C=Math.max(0,Math.min(C,t.bodyWidth-1)),M=Math.max(0,Math.min(M,t.bodyHeight-1));let S=s.cursorRow,b=s.cursorColumn;if("row-header"===i.region||"row"===g){w=i.row,v=t.rowAt("body",M);const e={r1:w,c1:0,r2:v,c2:0},s=o.joinCellGroupsIntersectingAtAxis(t.dataModel,["row-header","body"],"row",e);s.r1!=Number.MAX_VALUE&&(w=Math.min(w,s.r1),v=Math.max(v,s.r2)),y=0,x=1/0}else if("column-header"===i.region||"column"===g){w=0,v=1/0,y=i.column,x=t.columnAt("body",C);const e={r1:0,c1:y,r2:0,c2:x},s=o.joinCellGroupsIntersectingAtAxis(t.dataModel,["column-header","body"],"column",e);s.c1!=Number.MAX_VALUE&&(y=s.c1,x=s.c2)}else w=S,v=t.rowAt("body",M),y=b,x=t.columnAt("body",C);s.select({r1:w,c1:y,r2:v,c2:x,cursorRow:S,cursorColumn:b,clear:"current"})}onMouseUp(t,e){this.release()}onMouseDoubleClick(t,e){if(!t.dataModel)return void this.release();let{clientX:i,clientY:s}=e,o=t.hitTest(i,s),{region:n,row:l,column:a}=o;if("void"!==n){if("column-header"===n||"corner-header"===n){const e=r.resizeHandleForHitTest(o);if("left"===e||"right"===e){let i="left"===e?a-1:a,s="column-header"===n?"body":"row-header";if(i<0){if("column-header"!==n)return;i=t.dataModel.columnCount("row-header")-1,s="row-header"}t.resizeColumn(s,i,null)}}if("body"===n&&t.editable){const e={grid:t,row:l,column:a};t.editorController.edit(e)}this.release()}else this.release()}onContextMenu(t,e){}onWheel(t,e){if(this._pressData)return;let i=e.deltaX,s=e.deltaY;switch(e.deltaMode){case 0:break;case 1:{let e=t.defaultSizes;i*=e.columnWidth,s*=e.rowHeight;break}case 2:i*=t.pageWidth,s*=t.pageHeight;break;default:throw"unreachable"}(i<0&&0!==t.scrollX||i>0&&t.scrollX!==t.maxScrollX||s<0&&0!==t.scrollY||s>0&&t.scrollY!==t.maxScrollY)&&(e.preventDefault(),e.stopPropagation(),t.scrollBy(i,s))}cursorForHandle(t){return r.cursorMap[t]}get pressData(){return this._pressData}}!function(t){t.createCellConfigObject=function(t,e){const{region:i,row:s,column:o}=e;if("void"===i)return;const r=t.dataModel.data(i,s,o),n=t.dataModel.metadata(i,s,o);return{...e,value:r,metadata:n}},t.resizeHandleForHitTest=function(t){let e,i=t.row,s=t.column,o=t.x,r=t.y,n=t.width-t.x,l=t.height-t.y;switch(t.region){case"corner-header":case"column-header":case"row-header":e=s>0&&o<=5?"left":n<=6?"right":i>0&&r<=5?"top":l<=6?"bottom":"none";break;case"body":case"void":e="none";break;default:throw"unreachable"}return e},t.autoselect=function t(e,i){if(i.timeout<0)return;let s=e.selectionModel;if(!s)return;let o=s.currentSelection();if(!o)return;let r=i.localX,n=i.localY,l=o.r1,a=o.c1,h=o.r2,c=o.c2,d=s.cursorRow,u=s.cursorColumn,_=e.headerWidth,m=e.headerHeight,f=e.viewportWidth,g=e.viewportHeight,p=s.selectionMode;"row-header"===i.region||"row"===p?h+=n<=m?-1:n>=g?1:0:("column-header"===i.region||"column"===p||(h+=n<=m?-1:n>=g?1:0),c+=r<=_?-1:r>=f?1:0),s.select({r1:l,c1:a,r2:h,c2:c,cursorRow:d,cursorColumn:u,clear:"current"}),o=s.currentSelection(),o&&("row-header"===i.region||"row"===p?e.scrollToRow(o.r2):"column-header"===i.region||"column"==p?e.scrollToColumn(o.c2):"cell"===p&&e.scrollToCell(o.r2,o.c2),setTimeout((()=>{t(e,i)}),i.timeout))},t.computeTimeout=function(t){return 5+120*(1-Math.min(128,Math.abs(t))/128)},t.cursorMap={top:"ns-resize",left:"ew-resize",right:"ew-resize",bottom:"ns-resize",hyperlink:"pointer",none:"default"}}(r||(r={}));class v{constructor(t){this._changed=new d.Signal(this),this._selectionMode="cell",this.dataModel=t.dataModel,this._selectionMode=t.selectionMode||"cell",this.dataModel.changed.connect(this.onDataModelChanged,this)}get changed(){return this._changed}get selectionMode(){return this._selectionMode}set selectionMode(t){this._selectionMode!==t&&(this._selectionMode=t,this.clear())}isRowSelected(t){return(0,c.some)(this.selections(),(e=>n.containsRow(e,t)))}isColumnSelected(t){return(0,c.some)(this.selections(),(e=>n.containsColumn(e,t)))}isCellSelected(t,e){return(0,c.some)(this.selections(),(i=>n.containsCell(i,t,e)))}onDataModelChanged(t,e){}emitChanged(){this._changed.emit(void 0)}}!function(t){function e(t,e){let{r1:i,r2:s}=t;return e>=i&&e<=s||e>=s&&e<=i}function i(t,e){let{c1:i,c2:s}=t;return e>=i&&e<=s||e>=s&&e<=i}t.containsRow=e,t.containsColumn=i,t.containsCell=function(t,s,o){return e(t,s)&&i(t,o)}}(n||(n={}));class x extends v{constructor(){super(...arguments),this._cursorRow=-1,this._cursorColumn=-1,this._cursorRectIndex=-1,this._selections=[]}get isEmpty(){return 0===this._selections.length}get cursorRow(){return this._cursorRow}get cursorColumn(){return this._cursorColumn}moveCursorWithinSelections(t){if(this.isEmpty||-1===this.cursorRow||-1===this._cursorColumn)return;const e=this._selections[0];if(1===this._selections.length&&e.r1===e.r2&&e.c1===e.c2)return;-1===this._cursorRectIndex&&(this._cursorRectIndex=this._selections.length-1);let i=this._selections[this._cursorRectIndex];const s="down"===t?1:"up"===t?-1:0,o="right"===t?1:"left"===t?-1:0;let r=this._cursorRow+s,n=this._cursorColumn+o;const l=Math.min(i.r1,i.r2),a=Math.max(i.r1,i.r2),h=Math.min(i.c1,i.c2),c=Math.max(i.c1,i.c2),d=()=>{this._cursorRectIndex=(this._cursorRectIndex+1)%this._selections.length,i=this._selections[this._cursorRectIndex],r=Math.min(i.r1,i.r2),n=Math.min(i.c1,i.c2)},u=()=>{this._cursorRectIndex=0===this._cursorRectIndex?this._selections.length-1:this._cursorRectIndex-1,i=this._selections[this._cursorRectIndex],r=Math.max(i.r1,i.r2),n=Math.max(i.c1,i.c2)};r>a?(r=l,n+=1,n>c&&d()):r<l?(r=a,n-=1,n<h&&u()):n>c?(n=h,r+=1,r>a&&d()):n<h&&(n=c,r-=1,r<l&&u()),this._cursorRow=r,this._cursorColumn=n,this.emitChanged()}currentSelection(){return this._selections[this._selections.length-1]||null}*selections(){yield*this._selections}select(t){let e=this.dataModel.rowCount("body"),i=this.dataModel.columnCount("body");if(e<=0||i<=0)return;let{r1:s,c1:o,r2:r,c2:n,cursorRow:l,cursorColumn:a,clear:h}=t;"all"===h?this._selections.length=0:"current"===h&&this._selections.pop(),s=Math.max(0,Math.min(s,e-1)),r=Math.max(0,Math.min(r,e-1)),o=Math.max(0,Math.min(o,i-1)),n=Math.max(0,Math.min(n,i-1));let c=!1;"row"===this.selectionMode?(o=0,n=i-1,c=0!==this._selections.filter((t=>t.r1===s)).length,this._selections=c?this._selections.filter((t=>t.r1!==s)):this._selections):"column"===this.selectionMode&&(s=0,r=e-1,c=0!==this._selections.filter((t=>t.c1===o)).length,this._selections=c?this._selections.filter((t=>t.c1!==o)):this._selections);let d=l,u=a;(d<0||d<s&&d<r||d>s&&d>r)&&(d=s),(u<0||u<o&&u<n||u>o&&u>n)&&(u=o),this._cursorRow=d,this._cursorColumn=u,this._cursorRectIndex=this._selections.length,c||this._selections.push({r1:s,c1:o,r2:r,c2:n}),this.emitChanged()}clear(){0!==this._selections.length&&(this._cursorRow=-1,this._cursorColumn=-1,this._cursorRectIndex=-1,this._selections.length=0,this.emitChanged())}onDataModelChanged(t,e){if(0===this._selections.length)return;if("cells-changed"===e.type)return;if("rows-moved"===e.type||"columns-moved"===e.type)return;let i=t.rowCount("body")-1,s=t.columnCount("body")-1;if(i<0||s<0)return this._selections.length=0,void this.emitChanged();let o=this.selectionMode,r=0;for(let t=0,e=this._selections.length;t<e;++t){let{r1:e,c1:n,r2:l,c2:a}=this._selections[t];i<e&&i<l||s<n&&s<a||("row"===o?(e=Math.max(0,Math.min(e,i)),l=Math.max(0,Math.min(l,i)),n=0,a=s):"column"===o?(e=0,l=i,n=Math.max(0,Math.min(n,s)),a=Math.max(0,Math.min(a,s))):(e=Math.max(0,Math.min(e,i)),l=Math.max(0,Math.min(l,i)),n=Math.max(0,Math.min(n,s)),a=Math.max(0,Math.min(a,s))),this._selections[r++]={r1:e,c1:n,r2:l,c2:a})}this._selections.length=r,this.emitChanged()}}class C extends g{}const M="Invalid input!";class S{validate(t,e){return{valid:!0}}}class b{constructor(){this.minLength=Number.NaN,this.maxLength=Number.NaN,this.pattern=null}validate(t,e){return null===e?{valid:!0}:"string"!=typeof e?{valid:!1,message:"Input must be valid text"}:!isNaN(this.minLength)&&e.length<this.minLength?{valid:!1,message:`Text length must be greater than ${this.minLength}`}:!isNaN(this.maxLength)&&e.length>this.maxLength?{valid:!1,message:`Text length must be less than ${this.maxLength}`}:this.pattern&&!this.pattern.test(e)?{valid:!1,message:"Text doesn't match the required pattern"}:{valid:!0}}}class H{constructor(){this.min=Number.NaN,this.max=Number.NaN}validate(t,e){return null===e?{valid:!0}:isNaN(e)||e%1!=0?{valid:!1,message:"Input must be valid integer"}:!isNaN(this.min)&&e<this.min?{valid:!1,message:`Input must be greater than ${this.min}`}:!isNaN(this.max)&&e>this.max?{valid:!1,message:`Input must be less than ${this.max}`}:{valid:!0}}}class R{constructor(){this.min=Number.NaN,this.max=Number.NaN}validate(t,e){return null===e?{valid:!0}:isNaN(e)?{valid:!1,message:"Input must be valid number"}:!isNaN(this.min)&&e<this.min?{valid:!1,message:`Input must be greater than ${this.min}`}:!isNaN(this.max)&&e>this.max?{valid:!1,message:`Input must be less than ${this.max}`}:{valid:!0}}}class z{constructor(){this.inputChanged=new d.Signal(this),this.validityNotification=null,this._disposed=!1,this._validInput=!0,this._gridWheelEventHandler=null,this.inputChanged.connect((()=>{this.validate()}))}get isDisposed(){return this._disposed}dispose(){this._disposed||(this._gridWheelEventHandler&&(this.cell.grid.node.removeEventListener("wheel",this._gridWheelEventHandler),this._gridWheelEventHandler=null),this._closeValidityNotification(),this._disposed=!0,this.cell.grid.node.removeChild(this.viewportOccluder))}edit(t,e){this.cell=t,this.onCommit=e&&e.onCommit,this.onCancel=e&&e.onCancel,this.validator=e&&e.validator?e.validator:this.createValidatorBasedOnType(),this._gridWheelEventHandler=()=>{this._closeValidityNotification(),this.updatePosition()},t.grid.node.addEventListener("wheel",this._gridWheelEventHandler),this._addContainer(),this.updatePosition(),this.startEditing()}cancel(){this._disposed||(this.dispose(),this.onCancel&&this.onCancel())}get validInput(){return this._validInput}validate(){let t;try{t=this.getInput()}catch(t){return console.log(`Input error: ${t.message}`),void this.setValidity(!1,t.message||M)}if(this.validator){const e=this.validator.validate(this.cell,t);e.valid?this.setValidity(!0):this.setValidity(!1,e.message||M)}else this.setValidity(!0)}setValidity(t,e=""){this._validInput=t,this._closeValidityNotification(),t?this.editorContainer.classList.remove("lm-mod-invalid"):(this.editorContainer.classList.add("lm-mod-invalid"),""!==e&&(this.validityNotification=new z.Notification({target:this.editorContainer,message:e,placement:"bottom",timeout:5e3}),this.validityNotification.show()))}createValidatorBasedOnType(){const t=this.cell,e=t.grid.dataModel.metadata("body",t.row,t.column);switch(e&&e.type){case"string":{const t=new b;if("string"==typeof e.format)switch(e.format){case"email":t.pattern=new RegExp("^([a-z0-9_.-]+)@([da-z.-]+).([a-z.]{2,6})$");break;case"uuid":t.pattern=new RegExp("[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}")}return e.constraint&&(void 0!==e.constraint.minLength&&(t.minLength=e.constraint.minLength),void 0!==e.constraint.maxLength&&(t.maxLength=e.constraint.maxLength),"string"==typeof e.constraint.pattern&&(t.pattern=new RegExp(e.constraint.pattern))),t}case"number":{const t=new R;return e.constraint&&(void 0!==e.constraint.minimum&&(t.min=e.constraint.minimum),void 0!==e.constraint.maximum&&(t.max=e.constraint.maximum)),t}case"integer":{const t=new H;return e.constraint&&(void 0!==e.constraint.minimum&&(t.min=e.constraint.minimum),void 0!==e.constraint.maximum&&(t.max=e.constraint.maximum)),t}}}getCellInfo(t){const{grid:e,row:i,column:s}=t;let r,n,l,a,h;const c=o.getGroup(e.dataModel,"body",i,s);if(c){n=e.headerWidth-e.scrollX+e.columnOffset("body",c.c1),l=e.headerHeight-e.scrollY+e.rowOffset("body",c.r1),a=0,h=0;for(let t=c.r1;t<=c.r2;t++)h+=e.rowSize("body",t);for(let t=c.c1;t<=c.c2;t++)a+=e.columnSize("body",t);r=e.dataModel.data("body",c.r1,c.c1)}else n=e.headerWidth-e.scrollX+e.columnOffset("body",s),l=e.headerHeight-e.scrollY+e.rowOffset("body",i),a=e.columnSize("body",s),h=e.rowSize("body",i),r=e.dataModel.data("body",i,s);return{grid:e,row:i,column:s,data:r,x:n,y:l,width:a,height:h}}updatePosition(){const t=this.cell.grid,e=this.getCellInfo(this.cell),i=t.headerHeight,s=t.headerWidth;this.viewportOccluder.style.top=i+"px",this.viewportOccluder.style.left=s+"px",this.viewportOccluder.style.width=t.viewportWidth-s+"px",this.viewportOccluder.style.height=t.viewportHeight-i+"px",this.viewportOccluder.style.position="absolute",this.editorContainer.style.left=e.x-1-s+"px",this.editorContainer.style.top=e.y-1-i+"px",this.editorContainer.style.width=e.width+1+"px",this.editorContainer.style.height=e.height+1+"px",this.editorContainer.style.visibility="visible",this.editorContainer.style.position="absolute"}commit(t="none"){if(this.validate(),!this._validInput)return!1;let e;try{e=this.getInput()}catch(t){return console.log(`Input error: ${t.message}`),!1}return this.dispose(),this.onCommit&&this.onCommit({cell:this.cell,value:e,cursorMovement:t}),!0}_addContainer(){this.viewportOccluder=document.createElement("div"),this.viewportOccluder.className="lm-DataGrid-cellEditorOccluder",this.cell.grid.node.appendChild(this.viewportOccluder),this.editorContainer=document.createElement("div"),this.editorContainer.className="lm-DataGrid-cellEditorContainer",this.viewportOccluder.appendChild(this.editorContainer),this.editorContainer.addEventListener("mouseleave",(t=>{this.viewportOccluder.style.pointerEvents=this._validInput?"none":"auto"})),this.editorContainer.addEventListener("mouseenter",(t=>{this.viewportOccluder.style.pointerEvents="none"}))}_closeValidityNotification(){this.validityNotification&&(this.validityNotification.close(),this.validityNotification=null)}}class O extends z{handleEvent(t){switch(t.type){case"keydown":this._onKeyDown(t);break;case"blur":this._onBlur(t);break;case"input":this._onInput(t)}}dispose(){this.isDisposed||(this._unbindEvents(),super.dispose())}startEditing(){this.createWidget();const t=this.cell,e=this.getCellInfo(t);this.input.value=this.deserialize(e.data),this.editorContainer.appendChild(this.input),this.input.focus(),this.input.select(),this.bindEvents()}deserialize(t){return null==t?"":t.toString()}createWidget(){const t=document.createElement("input");t.classList.add("lm-DataGrid-cellEditorWidget"),t.classList.add("lm-DataGrid-cellEditorInput"),t.spellcheck=!1,t.type=this.inputType,this.input=t}bindEvents(){this.input.addEventListener("keydown",this),this.input.addEventListener("blur",this),this.input.addEventListener("input",this)}_unbindEvents(){this.input.removeEventListener("keydown",this),this.input.removeEventListener("blur",this),this.input.removeEventListener("input",this)}_onKeyDown(t){switch((0,a.getKeyboardLayout)().keyForKeydownEvent(t)){case"Enter":this.commit(t.shiftKey?"up":"down");break;case"Tab":this.commit(t.shiftKey?"left":"right"),t.stopPropagation(),t.preventDefault();break;case"Escape":this.cancel()}}_onBlur(t){this.isDisposed||this.commit()||(t.preventDefault(),t.stopPropagation(),this.input.focus())}_onInput(t){this.inputChanged.emit(void 0)}}class k extends O{constructor(){super(...arguments),this.inputType="text"}getInput(){return this.input.value}}class E extends O{constructor(){super(...arguments),this.inputType="number"}startEditing(){super.startEditing(),this.input.step="any";const t=this.cell,e=t.grid.dataModel.metadata("body",t.row,t.column).constraint;e&&(e.minimum&&(this.input.min=e.minimum),e.maximum&&(this.input.max=e.maximum))}getInput(){let t=this.input.value;if(""===t.trim())return null;const e=parseFloat(t);if(isNaN(e))throw new Error("Invalid input");return e}}class L extends O{constructor(){super(...arguments),this.inputType="number"}startEditing(){super.startEditing(),this.input.step="1";const t=this.cell,e=t.grid.dataModel.metadata("body",t.row,t.column).constraint;e&&(e.minimum&&(this.input.min=e.minimum),e.maximum&&(this.input.max=e.maximum))}getInput(){let t=this.input.value;if(""===t.trim())return null;let e=parseInt(t);if(isNaN(e))throw new Error("Invalid input");return e}}class W extends z{handleEvent(t){switch(t.type){case"keydown":this._onKeyDown(t);break;case"blur":this._onBlur(t)}}dispose(){this.isDisposed||(this._unbindEvents(),super.dispose())}startEditing(){this._createWidget();const t=this.cell,e=this.getCellInfo(t);this._input.value=this._deserialize(e.data),this.editorContainer.appendChild(this._input),this._input.focus(),this._bindEvents()}getInput(){return this._input.value}_deserialize(t){return null==t?"":t.toString()}_createWidget(){const t=document.createElement("input");t.type="date",t.pattern="d{4}-d{2}-d{2}",t.classList.add("lm-DataGrid-cellEditorWidget"),t.classList.add("lm-DataGrid-cellEditorInput"),this._input=t}_bindEvents(){this._input.addEventListener("keydown",this),this._input.addEventListener("blur",this)}_unbindEvents(){this._input.removeEventListener("keydown",this),this._input.removeEventListener("blur",this)}_onKeyDown(t){switch((0,a.getKeyboardLayout)().keyForKeydownEvent(t)){case"Enter":this.commit(t.shiftKey?"up":"down");break;case"Tab":this.commit(t.shiftKey?"left":"right"),t.stopPropagation(),t.preventDefault();break;case"Escape":this.cancel()}}_onBlur(t){this.isDisposed||this.commit()||(t.preventDefault(),t.stopPropagation(),this._input.focus())}}class T extends z{handleEvent(t){switch(t.type){case"keydown":this._onKeyDown(t);break;case"mousedown":this._input.focus(),t.stopPropagation(),t.preventDefault();break;case"blur":this._onBlur(t)}}dispose(){this.isDisposed||(this._unbindEvents(),super.dispose())}startEditing(){this._createWidget();const t=this.cell,e=this.getCellInfo(t);this._input.checked=this._deserialize(e.data),this.editorContainer.appendChild(this._input),this._input.focus(),this._bindEvents()}getInput(){return this._input.checked}_deserialize(t){return null!=t&&1==t}_createWidget(){const t=document.createElement("input");t.classList.add("lm-DataGrid-cellEditorWidget"),t.classList.add("lm-DataGrid-cellEditorCheckbox"),t.type="checkbox",t.spellcheck=!1,this._input=t}_bindEvents(){this._input.addEventListener("keydown",this),this._input.addEventListener("mousedown",this),this._input.addEventListener("blur",this)}_unbindEvents(){this._input.removeEventListener("keydown",this),this._input.removeEventListener("mousedown",this),this._input.removeEventListener("blur",this)}_onKeyDown(t){switch((0,a.getKeyboardLayout)().keyForKeydownEvent(t)){case"Enter":this.commit(t.shiftKey?"up":"down");break;case"Tab":this.commit(t.shiftKey?"left":"right"),t.stopPropagation(),t.preventDefault();break;case"Escape":this.cancel()}}_onBlur(t){this.isDisposed||this.commit()||(t.preventDefault(),t.stopPropagation(),this._input.focus())}}class B extends z{constructor(){super(...arguments),this._isMultiSelect=!1}dispose(){this.isDisposed||(super.dispose(),this._isMultiSelect&&document.body.removeChild(this._select))}startEditing(){const t=this.cell,e=this.getCellInfo(t),i=t.grid.dataModel.metadata("body",t.row,t.column);if(this._isMultiSelect="array"===i.type,this._createWidget(),this._isMultiSelect){this._select.multiple=!0;const t=this._deserialize(e.data);for(let e=0;e<this._select.options.length;++e){const i=this._select.options.item(e);i.selected=-1!==t.indexOf(i.value)}document.body.appendChild(this._select)}else this._select.value=this._deserialize(e.data),this.editorContainer.appendChild(this._select);this._select.focus(),this._bindEvents(),this.updatePosition()}getInput(){if(this._isMultiSelect){const t=[];for(let e=0;e<this._select.selectedOptions.length;++e)t.push(this._select.selectedOptions.item(e).value);return t}return this._select.value}updatePosition(){if(super.updatePosition(),!this._isMultiSelect)return;const t=this.getCellInfo(this.cell);this._select.style.position="absolute";const e=this.editorContainer.getBoundingClientRect();this._select.style.left=e.left+"px",this._select.style.top=e.top+t.height+"px",this._select.style.width=e.width+"px",this._select.style.maxHeight="60px",this.editorContainer.style.visibility="hidden"}_deserialize(t){if(null==t)return"";if(this._isMultiSelect){const e=[];if(Array.isArray(t))for(let i of t)e.push(i.toString());return e}return t.toString()}_createWidget(){const t=this.cell,e=t.grid.dataModel.metadata("body",t.row,t.column).constraint.enum,i=document.createElement("select");i.classList.add("lm-DataGrid-cellEditorWidget");for(let t of e){const e=document.createElement("option");e.value=t,e.text=t,i.appendChild(e)}this._select=i}_bindEvents(){this._select.addEventListener("keydown",this._onKeyDown.bind(this)),this._select.addEventListener("blur",this._onBlur.bind(this))}_onKeyDown(t){switch((0,a.getKeyboardLayout)().keyForKeydownEvent(t)){case"Enter":this.commit(t.shiftKey?"up":"down");break;case"Tab":this.commit(t.shiftKey?"left":"right"),t.stopPropagation(),t.preventDefault();break;case"Escape":this.cancel()}}_onBlur(t){this.isDisposed||this.commit()||(t.preventDefault(),t.stopPropagation(),this._select.focus())}}class D extends z{handleEvent(t){switch(t.type){case"keydown":this._onKeyDown(t);break;case"blur":this._onBlur(t)}}dispose(){this.isDisposed||(this._unbindEvents(),super.dispose())}startEditing(){this._createWidget();const t=this.cell,e=this.getCellInfo(t);this._input.value=this._deserialize(e.data),this.editorContainer.appendChild(this._input),this._input.focus(),this._input.select(),this._bindEvents()}getInput(){return this._input.value}_deserialize(t){return null==t?"":t.toString()}_createWidget(){const t=this.cell,e=t.grid.dataModel,i=e.rowCount("body"),s="cell-editor-list",o=document.createElement("datalist");o.id=s;const r=document.createElement("input");r.classList.add("lm-DataGrid-cellEditorWidget"),r.classList.add("lm-DataGrid-cellEditorInput");const n=new Set;for(let s=0;s<i;++s){const i=e.data("body",s,t.column);i&&n.add(i)}n.forEach((t=>{const e=document.createElement("option");e.value=t,e.text=t,o.appendChild(e)})),this.editorContainer.appendChild(o),r.setAttribute("list",s),this._input=r}_bindEvents(){this._input.addEventListener("keydown",this),this._input.addEventListener("blur",this)}_unbindEvents(){this._input.removeEventListener("keydown",this),this._input.removeEventListener("blur",this)}_onKeyDown(t){switch((0,a.getKeyboardLayout)().keyForKeydownEvent(t)){case"Enter":this.commit(t.shiftKey?"up":"down");break;case"Tab":this.commit(t.shiftKey?"left":"right"),t.stopPropagation(),t.preventDefault();break;case"Escape":this.cancel()}}_onBlur(t){this.isDisposed||this.commit()||(t.preventDefault(),t.stopPropagation(),this._input.focus())}}function G(t,e){return"function"==typeof t?t(e):t}!function(t){class e extends u.Widget{constructor(t){super({node:e.createNode()}),this._message="",this.addClass("lm-DataGrid-notification"),this.setFlag(u.Widget.Flag.DisallowLayout),this._target=t.target,this._message=t.message||"",this._placement=t.placement||"bottom",u.Widget.attach(this,document.body),t.timeout&&t.timeout>0&&setTimeout((()=>{this.close()}),t.timeout)}handleEvent(t){switch(t.type){case"mousedown":this._evtMouseDown(t);break;case"contextmenu":t.preventDefault(),t.stopPropagation()}}get placement(){return this._placement}set placement(t){this._placement!==t&&(this._placement=t,this.update())}get message(){return this._message}set message(t){this._message!==t&&(this._message=t,this.update())}get messageNode(){return this.node.getElementsByClassName("lm-DataGrid-notificationMessage")[0]}onBeforeAttach(t){this.node.addEventListener("mousedown",this),this.update()}onAfterDetach(t){this.node.removeEventListener("mousedown",this)}onUpdateRequest(t){const e=this._target.getBoundingClientRect(),i=this.node.style;switch(this._placement){case"bottom":i.left=e.left+"px",i.top=e.bottom+"px";break;case"top":i.left=e.left+"px",i.height=e.top+"px",i.top="0",i.alignItems="flex-end",i.justifyContent="flex-end";break;case"left":i.left="0",i.width=e.left+"px",i.top=e.top+"px",i.alignItems="flex-end",i.justifyContent="flex-end";break;case"right":i.left=e.right+"px",i.top=e.top+"px"}this.messageNode.innerHTML=this._message}_evtMouseDown(t){0===t.button&&(t.preventDefault(),t.stopPropagation(),this.close())}}t.Notification=e,function(t){t.createNode=function(){const t=document.createElement("div"),e=document.createElement("div");e.className="lm-DataGrid-notificationContainer";const i=document.createElement("span");return i.className="lm-DataGrid-notificationMessage",e.appendChild(i),t.appendChild(e),t}}(e=t.Notification||(t.Notification={}))}(z||(z={}));class A{constructor(){this._editor=null,this._cell=null,this._typeBasedOverrides=new Map,this._metadataBasedOverrides=new Map}setEditor(t,e){if("string"==typeof t)this._typeBasedOverrides.set(t,e);else{const i=this._metadataIdentifierToKey(t);this._metadataBasedOverrides.set(i,[t,e])}}edit(t,e){if(!t.grid.editable)return console.error("Grid cannot be edited!"),!1;if(this.cancel(),this._cell=t,(e=e||{}).onCommit=e.onCommit||this._onCommit.bind(this),e.onCancel=e.onCancel||this._onCancel.bind(this),e.editor)return this._editor=e.editor,e.editor.edit(t,e),!0;const i=this._getEditor(t);return!!i&&(this._editor=i,i.edit(t,e),!0)}cancel(){this._editor&&(this._editor.cancel(),this._editor=null),this._cell=null}_onCommit(t){const e=this._cell;if(!e)return;const i=e.grid,s=i.dataModel;let r=e.row,n=e.column;const l=o.getGroup(i.dataModel,"body",r,n);l&&(r=l.r1,n=l.c1),s.setData("body",r,n,t.value),i.viewport.node.focus(),"none"!==t.cursorMovement&&(i.moveCursor(t.cursorMovement),i.scrollToCursor())}_onCancel(){this._cell&&this._cell.grid.viewport.node.focus()}_getDataTypeKey(t){const e=t.grid.dataModel?t.grid.dataModel.metadata("body",t.row,t.column):null;if(!e)return"default";let i="";return e&&(i=e.type),e.constraint&&e.constraint.enum&&("dynamic"===e.constraint.enum?i+=":dynamic-option":i+=":option"),i}_objectToKey(t){let e="";for(let i in t){const s=t[i];e+="object"==typeof s?`${i}:${this._objectToKey(s)}`:`[${i}:${s}]`}return e}_metadataIdentifierToKey(t){return this._objectToKey(t)}_metadataMatchesIdentifier(t,e){for(let i in e){if(!t.hasOwnProperty(i))return!1;const s=e[i],o=t[i];if("object"==typeof s){if(!this._metadataMatchesIdentifier(o,s))return!1}else if(o!==s)return!1}return!0}_getMetadataBasedEditor(t){let e;const i=t.grid.dataModel.metadata("body",t.row,t.column);return i&&this._metadataBasedOverrides.forEach((s=>{if(!e){let[o,r]=s;this._metadataMatchesIdentifier(i,o)&&(e=G(r,t))}})),e}_getEditor(t){const e=this._getDataTypeKey(t);if(this._typeBasedOverrides.has(e))return G(this._typeBasedOverrides.get(e),t);if(this._metadataBasedOverrides.size>0){const e=this._getMetadataBasedEditor(t);if(e)return e}switch(e){case"string":return new k;case"number":return new E;case"integer":return new L;case"boolean":return new T;case"date":return new W;case"string:option":case"number:option":case"integer:option":case"date:option":case"array:option":return new B;case"string:dynamic-option":case"number:dynamic-option":case"integer:dynamic-option":case"date:dynamic-option":return new D}if(this._typeBasedOverrides.has("default"))return G(this._typeBasedOverrides.get("default"),t);const i=t.grid.dataModel.data("body",t.row,t.column);return i&&"object"==typeof i?void 0:new k}}class I{constructor(){this._changed=new d.Signal(this)}get changed(){return this._changed}groupCount(t){return 0}metadata(t,e,i){return I.emptyMetadata}group(t,e){return null}emitChanged(t){this._changed.emit(t)}}class P extends I{}!function(t){t.emptyMetadata=Object.freeze({})}(I||(I={}));class N{constructor(t){this._disposed=!1,this._context=t,this._state=X.State.create(t)}dispose(){if(!this._disposed)for(this._disposed=!0;this._state.next;)this._state=this._state.next,this._context.restore()}get isDisposed(){return this._disposed}get fillStyle(){return this._context.fillStyle}set fillStyle(t){this._state.fillStyle!==t&&(this._state.fillStyle=t,this._context.fillStyle=t)}get strokeStyle(){return this._context.strokeStyle}set strokeStyle(t){this._state.strokeStyle!==t&&(this._state.strokeStyle=t,this._context.strokeStyle=t)}get font(){return this._context.font}set font(t){this._state.font!==t&&(this._state.font=t,this._context.font=t)}get textAlign(){return this._context.textAlign}set textAlign(t){this._state.textAlign!==t&&(this._state.textAlign=t,this._context.textAlign=t)}get textBaseline(){return this._context.textBaseline}set textBaseline(t){this._state.textBaseline!==t&&(this._state.textBaseline=t,this._context.textBaseline=t)}get lineCap(){return this._context.lineCap}set lineCap(t){this._state.lineCap!==t&&(this._state.lineCap=t,this._context.lineCap=t)}get lineDashOffset(){return this._context.lineDashOffset}set lineDashOffset(t){this._state.lineDashOffset!==t&&(this._state.lineDashOffset=t,this._context.lineDashOffset=t)}get lineJoin(){return this._context.lineJoin}set lineJoin(t){this._state.lineJoin!==t&&(this._state.lineJoin=t,this._context.lineJoin=t)}get lineWidth(){return this._context.lineWidth}set lineWidth(t){this._state.lineWidth!==t&&(this._state.lineWidth=t,this._context.lineWidth=t)}get miterLimit(){return this._context.miterLimit}set miterLimit(t){this._state.miterLimit!==t&&(this._state.miterLimit=t,this._context.miterLimit=t)}get shadowBlur(){return this._context.shadowBlur}set shadowBlur(t){this._state.shadowBlur!==t&&(this._state.shadowBlur=t,this._context.shadowBlur=t)}get shadowColor(){return this._context.shadowColor}set shadowColor(t){this._state.shadowColor!==t&&(this._state.shadowColor=t,this._context.shadowColor=t)}get shadowOffsetX(){return this._context.shadowOffsetX}set shadowOffsetX(t){this._state.shadowOffsetX!==t&&(this._state.shadowOffsetX=t,this._context.shadowOffsetX=t)}get shadowOffsetY(){return this._context.shadowOffsetY}set shadowOffsetY(t){this._state.shadowOffsetY!==t&&(this._state.shadowOffsetY=t,this._context.shadowOffsetY=t)}get imageSmoothingEnabled(){return this._context.imageSmoothingEnabled}set imageSmoothingEnabled(t){this._state.imageSmoothingEnabled!==t&&(this._state.imageSmoothingEnabled=t,this._context.imageSmoothingEnabled=t)}get globalAlpha(){return this._context.globalAlpha}set globalAlpha(t){this._state.globalAlpha!==t&&(this._state.globalAlpha=t,this._context.globalAlpha=t)}get globalCompositeOperation(){return this._context.globalCompositeOperation}set globalCompositeOperation(t){this._state.globalCompositeOperation!==t&&(this._state.globalCompositeOperation=t,this._context.globalCompositeOperation=t)}getLineDash(){return this._context.getLineDash()}setLineDash(t){this._context.setLineDash(t)}rotate(t){this._context.rotate(t)}scale(t,e){this._context.scale(t,e)}transform(t,e,i,s,o,r){this._context.transform(t,e,i,s,o,r)}translate(t,e){this._context.translate(t,e)}setTransform(t,e,i,s,o,r){this._context.setTransform(t,e,i,s,o,r)}save(){this._state=X.State.push(this._state),this._context.save()}restore(){this._state.next&&(this._state=X.State.pop(this._state),this._context.restore())}beginPath(){return this._context.beginPath()}closePath(){this._context.closePath()}isPointInPath(t,e,i){let s;return s=2===arguments.length?this._context.isPointInPath(t,e):this._context.isPointInPath(t,e,i),s}arc(t,e,i,s,o,r){5===arguments.length?this._context.arc(t,e,i,s,o):this._context.arc(t,e,i,s,o,r)}arcTo(t,e,i,s,o){this._context.arcTo(t,e,i,s,o)}bezierCurveTo(t,e,i,s,o,r){this._context.bezierCurveTo(t,e,i,s,o,r)}ellipse(t,e,i,s,o,r,n,l){7===arguments.length?this._context.ellipse(t,e,i,s,o,r,n):this._context.ellipse(t,e,i,s,o,r,n,l)}lineTo(t,e){this._context.lineTo(t,e)}moveTo(t,e){this._context.moveTo(t,e)}quadraticCurveTo(t,e,i,s){this._context.quadraticCurveTo(t,e,i,s)}rect(t,e,i,s){this._context.rect(t,e,i,s)}clip(t){0===arguments.length?this._context.clip():this._context.clip(t)}fill(t){0===arguments.length?this._context.fill():this._context.fill(t)}stroke(){this._context.stroke()}clearRect(t,e,i,s){return this._context.clearRect(t,e,i,s)}fillRect(t,e,i,s){this._context.fillRect(t,e,i,s)}fillText(t,e,i,s){3===arguments.length?this._context.fillText(t,e,i):this._context.fillText(t,e,i,s)}strokeRect(t,e,i,s){this._context.strokeRect(t,e,i,s)}strokeText(t,e,i,s){3===arguments.length?this._context.strokeText(t,e,i):this._context.strokeText(t,e,i,s)}measureText(t){return this._context.measureText(t)}createLinearGradient(t,e,i,s){return this._context.createLinearGradient(t,e,i,s)}createRadialGradient(t,e,i,s,o,r){return this._context.createRadialGradient(t,e,i,s,o,r)}createPattern(t,e){return this._context.createPattern(t,e)}createImageData(){return this._context.createImageData.apply(this._context,arguments)}getImageData(t,e,i,s){return this._context.getImageData(t,e,i,s)}putImageData(){this._context.putImageData.apply(this._context,arguments)}drawImage(){this._context.drawImage.apply(this._context,arguments)}drawFocusIfNeeded(t){this._context.drawFocusIfNeeded(t)}}var X,V,Y,F;!function(t){let e=-1;const i=[];class s{static create(t){let o=e<0?new s:i[e--];return o.next=null,o.fillStyle=t.fillStyle,o.font=t.font,o.globalAlpha=t.globalAlpha,o.globalCompositeOperation=t.globalCompositeOperation,o.imageSmoothingEnabled=t.imageSmoothingEnabled,o.lineCap=t.lineCap,o.lineDashOffset=t.lineDashOffset,o.lineJoin=t.lineJoin,o.lineWidth=t.lineWidth,o.miterLimit=t.miterLimit,o.shadowBlur=t.shadowBlur,o.shadowColor=t.shadowColor,o.shadowOffsetX=t.shadowOffsetX,o.shadowOffsetY=t.shadowOffsetY,o.strokeStyle=t.strokeStyle,o.textAlign=t.textAlign,o.textBaseline=t.textBaseline,o}static push(t){let o=e<0?new s:i[e--];return o.next=t,o.fillStyle=t.fillStyle,o.font=t.font,o.globalAlpha=t.globalAlpha,o.globalCompositeOperation=t.globalCompositeOperation,o.imageSmoothingEnabled=t.imageSmoothingEnabled,o.lineCap=t.lineCap,o.lineDashOffset=t.lineDashOffset,o.lineJoin=t.lineJoin,o.lineWidth=t.lineWidth,o.miterLimit=t.miterLimit,o.shadowBlur=t.shadowBlur,o.shadowColor=t.shadowColor,o.shadowOffsetX=t.shadowOffsetX,o.shadowOffsetY=t.shadowOffsetY,o.strokeStyle=t.strokeStyle,o.textAlign=t.textAlign,o.textBaseline=t.textBaseline,o}static pop(t){return t.fillStyle="",t.strokeStyle="",i[++e]=t,t.next}}t.State=s}(X||(X={}));class K{constructor(t={},e){this._changed=new d.Signal(this),this._values={...t},this._fallback=e||new p}get changed(){return this._changed}get(t){let e=this._values[t.region];if("function"==typeof e)try{e=e(t)}catch(t){e=void 0,console.error(t)}return e||this._fallback}update(t={},e){this._values={...this._values,...t},this._fallback=e||this._fallback,this._changed.emit(void 0)}}class q{constructor(t){this._count=0,this._length=0,this._sections=[],this._minimumSize=t.minimumSize||2,this._defaultSize=Math.max(this._minimumSize,Math.floor(t.defaultSize))}get length(){return this._length}get count(){return this._count}get minimumSize(){return this._minimumSize}set minimumSize(t){t=Math.max(2,Math.floor(t)),this._minimumSize!==t&&(this._minimumSize=t,t>this._defaultSize&&(this.defaultSize=t))}get defaultSize(){return this._defaultSize}set defaultSize(t){if(t=Math.max(this._minimumSize,Math.floor(t)),this._defaultSize===t)return;let e=t-this._defaultSize;if(this._defaultSize=t,this._length+=e*(this._count-this._sections.length),0!==this._sections.length)for(let e=0,i=this._sections.length;e<i;++e){let i=this._sections[e-1],s=this._sections[e];if(i){let e=s.index-i.index-1;s.offset=i.offset+i.size+e*t}else s.offset=s.index*t}}clampSize(t){return Math.max(this._minimumSize,Math.floor(t))}indexOf(t){if(t<0||t>=this._length||0===this._count)return-1;if(0===this._sections.length)return Math.floor(t/this._defaultSize);let e=c.ArrayExt.lowerBound(this._sections,t,V.offsetCmp);if(e<this._sections.length&&this._sections[e].offset<=t)return this._sections[e].index;if(0===e)return Math.floor(t/this._defaultSize);let i=this._sections[e-1],s=t-(i.offset+i.size);return i.index+Math.floor(s/this._defaultSize)+1}offsetOf(t){if(t<0||t>=this._count)return-1;if(0===this._sections.length)return t*this._defaultSize;let e=c.ArrayExt.lowerBound(this._sections,t,V.indexCmp);if(e<this._sections.length&&this._sections[e].index===t)return this._sections[e].offset;if(0===e)return t*this._defaultSize;let i=this._sections[e-1],s=t-i.index-1;return i.offset+i.size+s*this._defaultSize}extentOf(t){if(t<0||t>=this._count)return-1;if(0===this._sections.length)return(t+1)*this._defaultSize-1;let e=c.ArrayExt.lowerBound(this._sections,t,V.indexCmp);if(e<this._sections.length&&this._sections[e].index===t)return this._sections[e].offset+this._sections[e].size-1;if(0===e)return(t+1)*this._defaultSize-1;let i=this._sections[e-1],s=t-i.index;return i.offset+i.size+s*this._defaultSize-1}sizeOf(t){if(t<0||t>=this._count)return-1;if(0===this._sections.length)return this._defaultSize;let e=c.ArrayExt.lowerBound(this._sections,t,V.indexCmp);return e<this._sections.length&&this._sections[e].index===t?this._sections[e].size:this._defaultSize}resize(t,e){if(t<0||t>=this._count)return;e=Math.max(this._minimumSize,Math.floor(e));let i,s=c.ArrayExt.lowerBound(this._sections,t,V.indexCmp);if(s<this._sections.length&&this._sections[s].index===t){let t=this._sections[s];i=e-t.size,t.size=e}else if(0===s){let o=t*this._defaultSize;c.ArrayExt.insert(this._sections,s,{index:t,offset:o,size:e}),i=e-this._defaultSize}else{let o=this._sections[s-1],r=t-o.index-1,n=o.offset+o.size+r*this._defaultSize;c.ArrayExt.insert(this._sections,s,{index:t,offset:n,size:e}),i=e-this._defaultSize}this._length+=i;for(let t=s+1,e=this._sections.length;t<e;++t)this._sections[t].offset+=i}insert(t,e){if(e<=0)return;t=Math.max(0,Math.min(t,this._count));let i=e*this._defaultSize;if(this._count+=e,this._length+=i,0===this._sections.length)return;let s=c.ArrayExt.lowerBound(this._sections,t,V.indexCmp);for(let t=this._sections.length;s<t;++s){let t=this._sections[s];t.index+=e,t.offset+=i}}remove(t,e){if(t<0||t>=this._count||e<=0)return;if(e=Math.min(this._count-t,e),0===this._sections.length)return this._count-=e,void(this._length-=e*this._defaultSize);if(e===this._count)return this._length=0,this._count=0,void(this._sections.length=0);let i=c.ArrayExt.lowerBound(this._sections,t,V.indexCmp),s=c.ArrayExt.lowerBound(this._sections,t+e,V.indexCmp),o=this._sections.splice(i,s-i),r=(e-o.length)*this._defaultSize;for(let t=0,e=o.length;t<e;++t)r+=o[t].size;this._count-=e,this._length-=r;for(let t=i,s=this._sections.length;t<s;++t){let i=this._sections[t];i.index-=e,i.offset-=r}}move(t,e,i){if(t<0||t>=this._count||e<=0)return;if(0===this._sections.length)return;if(e=Math.min(e,this._count-t),t===(i=Math.min(Math.max(0,i),this._count-e)))return;let s=Math.min(t,i),o=c.ArrayExt.lowerBound(this._sections,s,V.indexCmp);if(o===this._sections.length)return;let r=Math.max(t+e-1,i+e-1),n=c.ArrayExt.upperBound(this._sections,r,V.indexCmp)-1;if(n<o)return;let l=i<t?t:t+e,a=l-s,h=r-l+1,d=a*this._defaultSize,u=h*this._defaultSize;for(let t=o;t<=n;++t){let e=this._sections[t];e.index<l?d+=e.size-this._defaultSize:u+=e.size-this._defaultSize}let _=c.ArrayExt.lowerBound(this._sections,l,V.indexCmp);o<=_&&_<=n&&c.ArrayExt.rotate(this._sections,_-o,o,n);for(let t=o;t<=n;++t){let e=this._sections[t];e.index<l?(e.index+=h,e.offset+=u):(e.index-=a,e.offset-=d)}}reset(){this._sections.length=0,this._length=this._count*this._defaultSize}clear(){this._count=0,this._length=0,this._sections.length=0}}!function(t){t.offsetCmp=function(t,e){return e<t.offset?1:t.offset+t.size<=e?-1:0},t.indexCmp=function(t,e){return t.index-e}}(V||(V={}));class j extends u.Widget{constructor(t={}){super(),this._scrollX=0,this._scrollY=0,this._viewportWidth=0,this._viewportHeight=0,this._mousedown=!1,this._keyHandler=null,this._mouseHandler=null,this._vScrollBarMinWidth=0,this._hScrollBarMinHeight=0,this._dpiRatio=Math.ceil(window.devicePixelRatio),this._dataModel=null,this._selectionModel=null,this._editingEnabled=!1,this.addClass("lm-DataGrid"),this._style=t.style||j.defaultStyle,this._stretchLastRow=t.stretchLastRow||!1,this._stretchLastColumn=t.stretchLastColumn||!1,this._headerVisibility=t.headerVisibility||"all",this._cellRenderers=t.cellRenderers||new K,this._copyConfig=t.copyConfig||j.defaultCopyConfig,this._cellRenderers.changed.connect(this._onRenderersChanged,this);let e=t.defaultSizes||j.defaultSizes,i=t.minimumSizes||j.minimumSizes;this._rowSections=new q({defaultSize:e.rowHeight,minimumSize:i.rowHeight}),this._columnSections=new q({defaultSize:e.columnWidth,minimumSize:i.columnWidth}),this._rowHeaderSections=new q({defaultSize:e.rowHeaderWidth,minimumSize:i.rowHeaderWidth}),this._columnHeaderSections=new q({defaultSize:e.columnHeaderHeight,minimumSize:i.columnHeaderHeight}),this._canvas=Y.createCanvas(),this._buffer=Y.createCanvas(),this._overlay=Y.createCanvas(),this._canvasGC=this._canvas.getContext("2d"),this._bufferGC=this._buffer.getContext("2d"),this._overlayGC=this._overlay.getContext("2d"),this._canvas.style.position="absolute",this._canvas.style.top="0px",this._canvas.style.left="0px",this._canvas.style.width="0px",this._canvas.style.height="0px",this._overlay.style.position="absolute",this._overlay.style.top="0px",this._overlay.style.left="0px",this._overlay.style.width="0px",this._overlay.style.height="0px",this._viewport=new u.Widget,this._viewport.node.tabIndex=-1,this._viewport.node.style.outline="none",this._vScrollBar=new u.ScrollBar({orientation:"vertical"}),this._hScrollBar=new u.ScrollBar({orientation:"horizontal"}),this._scrollCorner=new u.Widget,this._editorController=new A,this._viewport.addClass("lm-DataGrid-viewport"),this._vScrollBar.addClass("lm-DataGrid-scrollBar"),this._hScrollBar.addClass("lm-DataGrid-scrollBar"),this._scrollCorner.addClass("lm-DataGrid-scrollCorner"),this._viewport.node.appendChild(this._canvas),this._viewport.node.appendChild(this._overlay),_.MessageLoop.installMessageHook(this._viewport,this),_.MessageLoop.installMessageHook(this._hScrollBar,this),_.MessageLoop.installMessageHook(this._vScrollBar,this),this._vScrollBar.hide(),this._hScrollBar.hide(),this._scrollCorner.hide(),this._vScrollBar.thumbMoved.connect(this._onThumbMoved,this),this._hScrollBar.thumbMoved.connect(this._onThumbMoved,this),this._vScrollBar.pageRequested.connect(this._onPageRequested,this),this._hScrollBar.pageRequested.connect(this._onPageRequested,this),this._vScrollBar.stepRequested.connect(this._onStepRequested,this),this._hScrollBar.stepRequested.connect(this._onStepRequested,this),u.GridLayout.setCellConfig(this._viewport,{row:0,column:0}),u.GridLayout.setCellConfig(this._vScrollBar,{row:0,column:1}),u.GridLayout.setCellConfig(this._hScrollBar,{row:1,column:0}),u.GridLayout.setCellConfig(this._scrollCorner,{row:1,column:1});let s=new u.GridLayout({rowCount:2,columnCount:2,rowSpacing:0,columnSpacing:0,fitPolicy:"set-no-constraint"});s.setRowStretch(0,1),s.setRowStretch(1,0),s.setColumnStretch(0,1),s.setColumnStretch(1,0),s.addWidget(this._viewport),s.addWidget(this._vScrollBar),s.addWidget(this._hScrollBar),s.addWidget(this._scrollCorner),this.layout=s}dispose(){this._releaseMouse(),this._keyHandler&&this._keyHandler.dispose(),this._mouseHandler&&this._mouseHandler.dispose(),this._keyHandler=null,this._mouseHandler=null,this._dataModel=null,this._selectionModel=null,this._rowSections.clear(),this._columnSections.clear(),this._rowHeaderSections.clear(),this._columnHeaderSections.clear(),super.dispose()}get dataModel(){return this._dataModel}set dataModel(t){this._dataModel!==t&&(this._releaseMouse(),this.selectionModel=null,this._dataModel&&this._dataModel.changed.disconnect(this._onDataModelChanged,this),t&&t.changed.connect(this._onDataModelChanged,this),this._dataModel=t,this._rowSections.clear(),this._columnSections.clear(),this._rowHeaderSections.clear(),this._columnHeaderSections.clear(),t&&(this._rowSections.insert(0,t.rowCount("body")),this._columnSections.insert(0,t.columnCount("body")),this._rowHeaderSections.insert(0,t.columnCount("row-header")),this._columnHeaderSections.insert(0,t.rowCount("column-header"))),this._scrollX=0,this._scrollY=0,this._syncViewport())}get selectionModel(){return this._selectionModel}set selectionModel(t){if(this._selectionModel!==t){if(this._releaseMouse(),t&&t.dataModel!==this._dataModel)throw new Error("SelectionModel.dataModel !== DataGrid.dataModel");this._selectionModel&&this._selectionModel.changed.disconnect(this._onSelectionsChanged,this),t&&t.changed.connect(this._onSelectionsChanged,this),this._selectionModel=t,this.repaintOverlay()}}get keyHandler(){return this._keyHandler}set keyHandler(t){this._keyHandler=t}get mouseHandler(){return this._mouseHandler}set mouseHandler(t){this._mouseHandler!==t&&(this._releaseMouse(),this._mouseHandler=t)}get style(){return this._style}set style(t){this._style!==t&&(this._style={...t},this.repaintContent(),this.repaintOverlay())}get cellRenderers(){return this._cellRenderers}set cellRenderers(t){this._cellRenderers!==t&&(this._cellRenderers.changed.disconnect(this._onRenderersChanged,this),t.changed.connect(this._onRenderersChanged,this),this._cellRenderers=t,this.repaintContent())}get headerVisibility(){return this._headerVisibility}set headerVisibility(t){this._headerVisibility!==t&&(this._headerVisibility=t,this._syncViewport())}get defaultSizes(){return{rowHeight:this._rowSections.defaultSize,columnWidth:this._columnSections.defaultSize,rowHeaderWidth:this._rowHeaderSections.defaultSize,columnHeaderHeight:this._columnHeaderSections.defaultSize}}set defaultSizes(t){this._rowSections.defaultSize=t.rowHeight,this._columnSections.defaultSize=t.columnWidth,this._rowHeaderSections.defaultSize=t.rowHeaderWidth,this._columnHeaderSections.defaultSize=t.columnHeaderHeight,this._syncViewport()}get minimumSizes(){return{rowHeight:this._rowSections.minimumSize,columnWidth:this._columnSections.minimumSize,rowHeaderWidth:this._rowHeaderSections.minimumSize,columnHeaderHeight:this._columnHeaderSections.minimumSize}}set minimumSizes(t){this._rowSections.minimumSize=t.rowHeight,this._columnSections.minimumSize=t.columnWidth,this._rowHeaderSections.minimumSize=t.rowHeaderWidth,this._columnHeaderSections.minimumSize=t.columnHeaderHeight,this._syncViewport()}get copyConfig(){return this._copyConfig}set copyConfig(t){this._copyConfig=t}get stretchLastRow(){return this._stretchLastRow}set stretchLastRow(t){t!==this._stretchLastRow&&(this._stretchLastRow=t,this._syncViewport())}get stretchLastColumn(){return this._stretchLastColumn}set stretchLastColumn(t){t!==this._stretchLastColumn&&(this._stretchLastColumn=t,this._syncViewport())}get headerWidth(){return"none"===this._headerVisibility||"column"===this._headerVisibility?0:this._rowHeaderSections.length}get headerHeight(){return"none"===this._headerVisibility||"row"===this._headerVisibility?0:this._columnHeaderSections.length}get bodyWidth(){return this._columnSections.length}get bodyHeight(){return this._rowSections.length}get totalWidth(){return this.headerWidth+this.bodyWidth}get totalHeight(){return this.headerHeight+this.bodyHeight}get viewportWidth(){return this._viewportWidth}get viewportHeight(){return this._viewportHeight}get pageWidth(){return Math.max(0,this.viewportWidth-this.headerWidth)}get pageHeight(){return Math.max(0,this.viewportHeight-this.headerHeight)}get scrollX(){return this._hScrollBar.value}get scrollY(){return this._vScrollBar.value}get maxScrollX(){return Math.max(0,this.bodyWidth-this.pageWidth-1)}get maxScrollY(){return Math.max(0,this.bodyHeight-this.pageHeight-1)}get viewport(){return this._viewport}get editorController(){return this._editorController}set editorController(t){this._editorController=t}get editingEnabled(){return this._editingEnabled}set editingEnabled(t){this._editingEnabled=t}get editable(){return this._editingEnabled&&null!==this._selectionModel&&null!==this._editorController&&this.dataModel instanceof P}get canvasGC(){return this._canvasGC}get rowSections(){return this._rowSections}get columnSections(){return this._columnSections}get rowHeaderSections(){return this._rowHeaderSections}get columnHeaderSections(){return this._columnHeaderSections}scrollToRow(t){let e=this._rowSections.count;if(0===e)return;t=Math.floor(t),t=Math.max(0,Math.min(t,e-1));let i=this._rowSections.offsetOf(t),s=this._rowSections.extentOf(t),o=this._scrollY,r=this._scrollY+this.pageHeight-1,n=0;i<o?n=i-o-10:s>r&&(n=s-r+10),0!==n&&this.scrollBy(0,n)}scrollToColumn(t){let e=this._columnSections.count;if(0===e)return;t=Math.floor(t),t=Math.max(0,Math.min(t,e-1));let i=this._columnSections.offsetOf(t),s=this._columnSections.extentOf(t),o=this._scrollX,r=this._scrollX+this.pageWidth-1,n=0;i<o?n=i-o-10:s>r&&(n=s-r+10),0!==n&&this.scrollBy(n,0)}scrollToCell(t,e){let i=this._rowSections.count,s=this._columnSections.count;if(0===i||0===s)return;t=Math.floor(t),e=Math.floor(e),t=Math.max(0,Math.min(t,i-1)),e=Math.max(0,Math.min(e,s-1));let o=this._columnSections.offsetOf(e),r=this._columnSections.extentOf(e),n=this._rowSections.offsetOf(t),l=this._rowSections.extentOf(t),a=this._scrollX,h=this._scrollX+this.pageWidth-1,c=this._scrollY,d=this._scrollY+this.pageHeight-1,u=0,_=0;o<a?u=o-a-10:r>h&&(u=r-h+10),n<c?_=n-c-10:l>d&&(_=l-d+10),0===u&&0===_||this.scrollBy(u,_)}moveCursor(t){if(!this.dataModel||!this._selectionModel||this._selectionModel.isEmpty)return;const e=this._selectionModel.selections();if(e.next()&&!e.next()){const e=this._selectionModel.currentSelection();if(e.r1===e.r2&&e.c1===e.c2){const i="down"===t?1:"up"===t?-1:0,s="right"===t?1:"left"===t?-1:0;let o=e.r1+i,r=e.c1+s;const n=this.dataModel.rowCount("body"),l=this.dataModel.columnCount("body");return o>=n?(o=0,r+=1):-1===o&&(o=n-1,r-=1),r>=l?(r=0,o+=1,o>=n&&(o=0)):-1===r&&(r=l-1,o-=1,-1===o&&(o=n-1)),void this._selectionModel.select({r1:o,c1:r,r2:o,c2:r,cursorRow:o,cursorColumn:r,clear:"all"})}}this._selectionModel.moveCursorWithinSelections(t)}scrollToCursor(){if(!this._selectionModel)return;let t=this._selectionModel.cursorRow,e=this._selectionModel.cursorColumn;this.scrollToCell(t,e)}scrollBy(t,e){this.scrollTo(this.scrollX+t,this.scrollY+e)}scrollByPage(t){let e=0,i=0;switch(t){case"up":i=-this.pageHeight;break;case"down":i=this.pageHeight;break;case"left":e=-this.pageWidth;break;case"right":e=this.pageWidth;break;default:throw"unreachable"}this.scrollTo(this.scrollX+e,this.scrollY+i)}scrollByStep(t){let e,i,s=this.scrollX,o=this.scrollY,r=this._rowSections,n=this._columnSections;switch(t){case"up":e=r.indexOf(o-1),o=e<0?o:r.offsetOf(e);break;case"down":e=r.indexOf(o),o=e<0?o:r.offsetOf(e)+r.sizeOf(e);break;case"left":i=n.indexOf(s-1),s=i<0?s:n.offsetOf(i);break;case"right":i=n.indexOf(s),s=i<0?s:n.offsetOf(i)+n.sizeOf(i);break;default:throw"unreachable"}this.scrollTo(s,o)}scrollTo(t,e){t=Math.max(0,Math.min(Math.floor(t),this.maxScrollX)),e=Math.max(0,Math.min(Math.floor(e),this.maxScrollY)),this._hScrollBar.value=t,this._vScrollBar.value=e,_.MessageLoop.postMessage(this._viewport,Y.ScrollRequest)}rowCount(t){let e;return e="body"===t?this._rowSections.count:this._columnHeaderSections.count,e}columnCount(t){let e;return e="body"===t?this._columnSections.count:this._rowHeaderSections.count,e}rowAt(t,e){if(e<0)return-1;if("column-header"===t)return this._columnHeaderSections.indexOf(e);let i=this._rowSections.indexOf(e);if(i>=0)return i;if(!this._stretchLastRow)return-1;let s=this.bodyHeight,o=this.pageHeight;return o<=s||e>=o?-1:this._rowSections.count-1}columnAt(t,e){if(e<0)return-1;if("row-header"===t)return this._rowHeaderSections.indexOf(e);let i=this._columnSections.indexOf(e);if(i>=0)return i;if(!this._stretchLastColumn)return-1;let s=this.bodyWidth,o=this.pageWidth;return o<=s||e>=o?-1:this._columnSections.count-1}rowOffset(t,e){let i;return i="body"===t?this._rowSections.offsetOf(e):this._columnHeaderSections.offsetOf(e),i}columnOffset(t,e){let i;return i="body"===t?this._columnSections.offsetOf(e):this._rowHeaderSections.offsetOf(e),i}rowSize(t,e){if("column-header"===t)return this._columnHeaderSections.sizeOf(e);let i=this._rowSections.sizeOf(e);if(i<0)return i;if(!this._stretchLastRow)return i;if(e<this._rowSections.count-1)return i;let s=this.bodyHeight,o=this.pageHeight;return o<=s?i:i+(o-s)}columnSize(t,e){if("row-header"===t)return this._rowHeaderSections.sizeOf(e);let i=this._columnSections.sizeOf(e);if(i<0)return i;if(!this._stretchLastColumn)return i;if(e<this._columnSections.count-1)return i;let s=this.bodyWidth,o=this.pageWidth;return o<=s?i:i+(o-s)}resizeRow(t,e,i){let s=new Y.RowResizeRequest(t,e,i);_.MessageLoop.postMessage(this._viewport,s)}resizeColumn(t,e,i){let s=new Y.ColumnResizeRequest(t,e,i);_.MessageLoop.postMessage(this._viewport,s)}resetRows(t){switch(t){case"all":this._rowSections.reset(),this._columnHeaderSections.reset();break;case"body":this._rowSections.reset();break;case"column-header":this._columnHeaderSections.reset();break;default:throw"unreachable"}this.repaintContent(),this.repaintOverlay()}resetColumns(t){switch(t){case"all":this._columnSections.reset(),this._rowHeaderSections.reset();break;case"body":this._columnSections.reset();break;case"row-header":this._rowHeaderSections.reset();break;default:throw"unreachable"}this.repaintContent(),this.repaintOverlay()}fitColumnNames(t="all",e=15,i){if(this.dataModel){let s=void 0===i||i<0?void 0:i;if("row-header"===t||"all"===t)if(void 0!==s){const t=this.dataModel.columnCount("row-header");s-t<0?(this._fitRowColumnHeaders(this.dataModel,e,s),s=0):(this._fitRowColumnHeaders(this.dataModel,e,t),s-=t)}else this._fitRowColumnHeaders(this.dataModel,e);if("body"===t||"all"===t)if(void 0!==s){const t=this.dataModel.columnCount("body");s-t<0?this._fitBodyColumnHeaders(this.dataModel,e,s):this._fitBodyColumnHeaders(this.dataModel,e,Math.min(s,t))}else this._fitBodyColumnHeaders(this.dataModel,e)}}mapToLocal(t,e){let i=this._viewport.node.getBoundingClientRect(),{left:s,top:o}=i;return s=Math.floor(s),o=Math.floor(o),{lx:t-s,ly:e-o}}mapToVirtual(t,e){let{lx:i,ly:s}=this.mapToLocal(t,e);return{vx:i+this.scrollX-this.headerWidth,vy:s+this.scrollY-this.headerHeight}}hitTest(t,e){let{lx:i,ly:s}=this.mapToLocal(t,e),o=this.headerWidth,r=this.headerHeight,n=this.bodyWidth,l=this.bodyHeight,a=this.pageHeight,h=this.pageWidth;if(this._stretchLastColumn&&h>n&&(n=h),this._stretchLastRow&&a>l&&(l=a),i>=0&&i<o&&s>=0&&s<r){let t=i,e=s,o=this.rowAt("column-header",e),r=this.columnAt("row-header",t);return{region:"corner-header",row:o,column:r,x:t-this.columnOffset("row-header",r),y:e-this.rowOffset("column-header",o),width:this.columnSize("row-header",r),height:this.rowSize("column-header",o)}}if(s>=0&&s<r&&i>=0&&i<o+n){let t=i+this._scrollX-o,e=s,r=this.rowAt("column-header",e),n=this.columnAt("body",t);return{region:"column-header",row:r,column:n,x:t-this.columnOffset("body",n),y:e-this.rowOffset("column-header",r),width:this.columnSize("body",n),height:this.rowSize("column-header",r)}}if(i>=0&&i<o&&s>=0&&s<r+l){let t=i,e=s+this._scrollY-r,o=this.rowAt("body",e),n=this.columnAt("row-header",t);return{region:"row-header",row:o,column:n,x:t-this.columnOffset("row-header",n),y:e-this.rowOffset("body",o),width:this.columnSize("row-header",n),height:this.rowSize("body",o)}}if(i>=o&&i<o+n&&s>=r&&s<r+l){let t=i+this._scrollX-o,e=s+this._scrollY-r,n=this.rowAt("body",e),l=this.columnAt("body",t);return{region:"body",row:n,column:l,x:t-this.columnOffset("body",l),y:e-this.rowOffset("body",n),width:this.columnSize("body",l),height:this.rowSize("body",n)}}return{region:"void",row:-1,column:-1,x:-1,y:-1,width:-1,height:-1}}copyToClipboard(){let t=this._dataModel;if(!t)return;let e=this._selectionModel;if(!e)return;let i=Array.from(e.selections());if(0===i.length)return;if(i.length>1)return void alert("Cannot copy multiple grid selections.");let s=t.rowCount("body"),o=t.columnCount("body");if(0===s||0===o)return;let{r1:r,c1:n,r2:a,c2:h}=i[0];r=Math.max(0,Math.min(r,s-1)),n=Math.max(0,Math.min(n,o-1)),a=Math.max(0,Math.min(a,s-1)),h=Math.max(0,Math.min(h,o-1)),a<r&&([r,a]=[a,r]),h<n&&([n,h]=[h,n]);let c=t.columnCount("row-header"),d=t.rowCount("column-header"),u=this._copyConfig.separator,_=this._copyConfig.format,m=this._copyConfig.headers,f=this._copyConfig.warningThreshold,g=a-r+1,p=h-n+1;switch(m){case"none":c=0,d=0;break;case"row":d=0,p+=c;break;case"column":c=0,g+=d;break;case"all":g+=d,p+=c;break;default:throw"unreachable"}let w=g*p;if(w>f){let t=`Copying ${w} cells may take a while. Continue?`;if(!window.confirm(t))return}let y={region:"body",row:0,column:0,value:null,metadata:{}},v=new Array(g);for(let e=0;e<g;++e){let i=new Array(p);for(let s=0;s<p;++s){let o,l,a;e<d&&s<c?(o="corner-header",l=e,a=s):e<d?(o="column-header",l=e,a=s-c+n):s<c?(o="row-header",l=e-d+r,a=s):(o="body",l=e-d+r,a=s-c+n),y.region=o,y.row=l,y.column=a,y.value=t.data(o,l,a),y.metadata=t.metadata(o,l,a),i[s]=_(y)}v[e]=i}let x=v.map((t=>t.join(u))).join("\n");l.ClipboardExt.copyText(x)}processMessage(t){if("child-shown"!==t.type&&"child-hidden"!==t.type){if("fit-request"===t.type){let t=l.ElementExt.sizeLimits(this._vScrollBar.node),e=l.ElementExt.sizeLimits(this._hScrollBar.node);this._vScrollBarMinWidth=t.minWidth,this._hScrollBarMinHeight=e.minHeight}super.processMessage(t)}}messageHook(t,e){return t===this._viewport?(this._processViewportMessage(e),!0):t===this._hScrollBar&&"activate-request"===e.type?(this.activate(),!1):t!==this._vScrollBar||"activate-request"!==e.type||(this.activate(),!1)}handleEvent(t){switch(t.type){case"keydown":this._evtKeyDown(t);break;case"mousedown":this._evtMouseDown(t);break;case"mousemove":this._evtMouseMove(t);break;case"mouseup":this._evtMouseUp(t);break;case"dblclick":this._evtMouseDoubleClick(t);break;case"mouseleave":this._evtMouseLeave(t);break;case"contextmenu":this._evtContextMenu(t);break;case"wheel":this._evtWheel(t);break;case"resize":this._refreshDPI()}}onActivateRequest(t){this.viewport.node.focus({preventScroll:!0})}onBeforeAttach(t){window.addEventListener("resize",this),this.node.addEventListener("wheel",this),this._viewport.node.addEventListener("keydown",this),this._viewport.node.addEventListener("mousedown",this),this._viewport.node.addEventListener("mousemove",this),this._viewport.node.addEventListener("dblclick",this),this._viewport.node.addEventListener("mouseleave",this),this._viewport.node.addEventListener("contextmenu",this),this.repaintContent(),this.repaintOverlay()}onAfterDetach(t){window.removeEventListener("resize",this),this.node.removeEventListener("wheel",this),this._viewport.node.removeEventListener("keydown",this),this._viewport.node.removeEventListener("mousedown",this),this._viewport.node.removeEventListener("mousemove",this),this._viewport.node.removeEventListener("mouseleave",this),this._viewport.node.removeEventListener("dblclick",this),this._viewport.node.removeEventListener("contextmenu",this),this._releaseMouse()}onBeforeShow(t){this.repaintContent(),this.repaintOverlay()}onResize(t){this._editorController&&this._editorController.cancel(),this._syncScrollState()}repaintContent(){let t=new Y.PaintRequest("all",0,0,0,0);_.MessageLoop.postMessage(this._viewport,t)}repaintRegion(t,e,i,s,o){let r=new Y.PaintRequest(t,e,i,s,o);_.MessageLoop.postMessage(this._viewport,r)}repaintOverlay(){_.MessageLoop.postMessage(this._viewport,Y.OverlayPaintRequest)}_getMaxWidthInColumn(t,e){const i=this.dataModel;if(!i)return null;const s="row-header"==e?"corner-header":"column-header";return Math.max(this._getMaxWidthInArea(i,t,s,"column-header"),this._getMaxWidthInArea(i,t,e,"body"))}_getMaxWidthInArea(t,e,i,s){const o=t.rowCount(s),r=Array.from({length:Math.min(o,1e6)},((s,o)=>j._getConfig(t,o,e,i)));o>1e5&&r.sort((t=>-this._getTextToRender(t).length));let n=0;for(let t=0;t<o&&t<1e5;++t){const e=this._getCellTextWidth(r[t]);n=Math.max(n,e)}return n}static _getConfig(t,e,i,s){return{x:0,y:0,width:0,height:0,region:s,row:e,column:i,value:j._getCellValue(t,s,e,i),metadata:j._getCellMetadata(t,s,e,i)}}_getTextToRender(t){return this.cellRenderers.get(t).getText(t)}_getCellTextWidth(t){const e=this.cellRenderers.get(t),i=this.canvasGC;i.font=g.resolveOption(e.font,t),i.fillStyle=g.resolveOption(e.textColor,t),i.textAlign=g.resolveOption(e.horizontalAlignment,t),i.textBaseline="bottom";const s=this._getTextToRender(t);return i.measureText(s).width+2*e.horizontalPadding}_resizeCanvasIfNeeded(t,e){t*=this._dpiRatio,e*=this._dpiRatio;let i=512*(Math.ceil((t+1)/512)+1),s=512*(Math.ceil((e+1)/512)+1),o=this._canvas.width,r=this._canvas.height;if(o>=t&&r>=e&&o<=i&&r<=s)return;let n=i-512,l=s-512;this._canvasGC.setTransform(1,0,0,1,0,0),this._bufferGC.setTransform(1,0,0,1,0,0),this._overlayGC.setTransform(1,0,0,1,0,0),o<t?this._buffer.width=n:o>i&&(this._buffer.width=i),r<e?this._buffer.height=l:r>s&&(this._buffer.height=s);let a=o>0&&r>0&&t>0&&e>0;a&&this._bufferGC.drawImage(this._canvas,0,0),o<t?(this._canvas.width=n,this._canvas.style.width=n/this._dpiRatio+"px"):o>i&&(this._canvas.width=i,this._canvas.style.width=i/this._dpiRatio+"px"),r<e?(this._canvas.height=l,this._canvas.style.height=l/this._dpiRatio+"px"):r>s&&(this._canvas.height=s,this._canvas.style.height=s/this._dpiRatio+"px"),a&&this._canvasGC.drawImage(this._buffer,0,0),a&&this._bufferGC.drawImage(this._overlay,0,0),o<t?(this._overlay.width=n,this._overlay.style.width=n/this._dpiRatio+"px"):o>i&&(this._overlay.width=i,this._overlay.style.width=i/this._dpiRatio+"px"),r<e?(this._overlay.height=l,this._overlay.style.height=l/this._dpiRatio+"px"):r>s&&(this._overlay.height=s,this._overlay.style.height=s/this._dpiRatio+"px"),a&&this._overlayGC.drawImage(this._buffer,0,0)}_syncScrollState(){let t=this.bodyWidth,e=this.bodyHeight,i=this.pageWidth,s=this.pageHeight,o=!this._vScrollBar.isHidden,r=!this._hScrollBar.isHidden,n=this._vScrollBarMinWidth,l=this._hScrollBarMinHeight,a=i+(o?n:0),h=s+(r?l:0),c=h<e-1,d=a<t-1;c&&!d&&(d=a-n<t-1),d&&!c&&(c=h-l<e-1),c===o&&d===r||(this._vScrollBar.setHidden(!c),this._hScrollBar.setHidden(!d),this._scrollCorner.setHidden(!c||!d),_.MessageLoop.sendMessage(this,u.Widget.Msg.FitRequest)),this._vScrollBar.maximum=this.maxScrollY,this._vScrollBar.page=this.pageHeight,this._hScrollBar.maximum=this.maxScrollX,this._hScrollBar.page=this.pageWidth,this._scrollTo(this._scrollX,this._scrollY)}_syncViewport(){this.repaintContent(),this.repaintOverlay(),this._syncScrollState()}_processViewportMessage(t){switch(t.type){case"resize":this._onViewportResize(t);break;case"scroll-request":this._onViewportScrollRequest(t);break;case"paint-request":this._onViewportPaintRequest(t);break;case"overlay-paint-request":this._onViewportOverlayPaintRequest(t);break;case"row-resize-request":this._onViewportRowResizeRequest(t);break;case"column-resize-request":this._onViewportColumnResizeRequest(t)}}_onViewportResize(t){if(!this._viewport.isVisible)return;let{width:e,height:i}=t;-1===e&&(e=this._viewport.node.offsetWidth),-1===i&&(i=this._viewport.node.offsetHeight),e=Math.round(e),i=Math.round(i);let s=this._viewportWidth,o=this._viewportHeight;if(this._viewportWidth=e,this._viewportHeight=i,this._resizeCanvasIfNeeded(e,i),0!==e&&0!==i){if(0===s||0===o)return this.paintContent(0,0,e,i),void this._paintOverlay();if(this._stretchLastColumn&&this.pageWidth>this.bodyWidth){let t=this._columnSections.offsetOf(this._columnSections.count-1),o=Math.min(this.headerWidth+t,s);this.paintContent(o,0,e-o,i)}else e>s&&this.paintContent(s,0,e-s+1,i);if(this._stretchLastRow&&this.pageHeight>this.bodyHeight){let t=this._rowSections.offsetOf(this._rowSections.count-1),s=Math.min(this.headerHeight+t,o);this.paintContent(0,s,e,i-s)}else i>o&&this.paintContent(0,o,e,i-o+1);this._paintOverlay()}}_onViewportScrollRequest(t){this._scrollTo(this._hScrollBar.value,this._vScrollBar.value)}_onViewportPaintRequest(t){if(!this._viewport.isVisible)return;if(0===this._viewportWidth||0===this._viewportHeight)return;let e,i,s,o,r=this._viewportWidth-1,n=this._viewportHeight-1,l=this._scrollX,a=this._scrollY,h=this.headerWidth,c=this.headerHeight,d=this._rowSections,u=this._columnSections,_=this._rowHeaderSections,m=this._columnHeaderSections,{region:f,r1:g,c1:p,r2:w,c2:y}=t;switch(f){case"all":e=0,i=0,s=r,o=n;break;case"body":g=Math.max(0,Math.min(g,d.count)),p=Math.max(0,Math.min(p,u.count)),w=Math.max(0,Math.min(w,d.count)),y=Math.max(0,Math.min(y,u.count)),e=u.offsetOf(p)-l+h,i=d.offsetOf(g)-a+c,s=u.extentOf(y)-l+h,o=d.extentOf(w)-a+c;break;case"row-header":g=Math.max(0,Math.min(g,d.count)),p=Math.max(0,Math.min(p,_.count)),w=Math.max(0,Math.min(w,d.count)),y=Math.max(0,Math.min(y,_.count)),e=_.offsetOf(p),i=d.offsetOf(g)-a+c,s=_.extentOf(y),o=d.extentOf(w)-a+c;break;case"column-header":g=Math.max(0,Math.min(g,m.count)),p=Math.max(0,Math.min(p,u.count)),w=Math.max(0,Math.min(w,m.count)),y=Math.max(0,Math.min(y,u.count)),e=u.offsetOf(p)-l+h,i=m.offsetOf(g),s=u.extentOf(y)-l+h,o=m.extentOf(w);break;case"corner-header":g=Math.max(0,Math.min(g,m.count)),p=Math.max(0,Math.min(p,_.count)),w=Math.max(0,Math.min(w,m.count)),y=Math.max(0,Math.min(y,_.count)),e=_.offsetOf(p),i=m.offsetOf(g),s=_.extentOf(y),o=m.extentOf(w);break;default:throw"unreachable"}s<0||o<0||e>r||i>n||(e=Math.max(0,Math.min(e,r)),i=Math.max(0,Math.min(i,n)),s=Math.max(0,Math.min(s,r)),o=Math.max(0,Math.min(o,n)),this.paintContent(e,i,s-e+1,o-i+1))}_onViewportOverlayPaintRequest(t){this._viewport.isVisible&&0!==this._viewportWidth&&0!==this._viewportHeight&&this._paintOverlay()}_onViewportRowResizeRequest(t){"body"===t.region?this._resizeRow(t.index,t.size):this._resizeColumnHeader(t.index,t.size)}_onViewportColumnResizeRequest(t){"body"===t.region?this._resizeColumn(t.index,t.size):this._resizeRowHeader(t.index,t.size)}_onThumbMoved(t){_.MessageLoop.postMessage(this._viewport,Y.ScrollRequest)}_onPageRequested(t,e){t===this._vScrollBar?this.scrollByPage("decrement"===e?"up":"down"):this.scrollByPage("decrement"===e?"left":"right")}_onStepRequested(t,e){t===this._vScrollBar?this.scrollByStep("decrement"===e?"up":"down"):this.scrollByStep("decrement"===e?"left":"right")}_onDataModelChanged(t,e){switch(e.type){case"rows-inserted":this._onRowsInserted(e);break;case"columns-inserted":this._onColumnsInserted(e);break;case"rows-removed":this._onRowsRemoved(e);break;case"columns-removed":this._onColumnsRemoved(e);break;case"rows-moved":this._onRowsMoved(e);break;case"columns-moved":this._onColumnsMoved(e);break;case"cells-changed":this._onCellsChanged(e);break;case"model-reset":this._onModelReset(e);break;default:throw"unreachable"}}_onSelectionsChanged(t){this.repaintOverlay()}_onRowsInserted(t){let e,{region:i,index:s,span:o}=t;o<=0||(e="body"===i?this._rowSections:this._columnHeaderSections,this._scrollY===this.maxScrollY&&this.maxScrollY>0?(e.insert(s,o),this._scrollY=this.maxScrollY):e.insert(s,o),this._syncViewport())}_onColumnsInserted(t){let e,{region:i,index:s,span:o}=t;o<=0||(e="body"===i?this._columnSections:this._rowHeaderSections,this._scrollX===this.maxScrollX&&this.maxScrollX>0?(e.insert(s,o),this._scrollX=this.maxScrollX):e.insert(s,o),this._syncViewport())}_onRowsRemoved(t){let e,{region:i,index:s,span:o}=t;o<=0||(e="body"===i?this._rowSections:this._columnHeaderSections,s<0||s>=e.count||(this._scrollY===this.maxScrollY&&this.maxScrollY>0?(e.remove(s,o),this._scrollY=this.maxScrollY):e.remove(s,o),this._syncViewport()))}_onColumnsRemoved(t){let e,{region:i,index:s,span:o}=t;o<=0||(e="body"===i?this._columnSections:this._rowHeaderSections,s<0||s>=e.count||(this._scrollX===this.maxScrollX&&this.maxScrollX>0?(e.remove(s,o),this._scrollX=this.maxScrollX):e.remove(s,o),this._syncViewport()))}_onRowsMoved(t){let e,{region:i,index:s,span:o,destination:r}=t;if(o<=0)return;if(e="body"===i?this._rowSections:this._columnHeaderSections,s<0||s>=e.count)return;if(o=Math.min(o,e.count-s),r=Math.min(Math.max(0,r),e.count-o),s===r)return;let n=Math.min(s,r),l=Math.max(s+o-1,r+o-1);e.move(s,o,r),"body"===i?(this.repaintRegion("body",n,0,l,1/0),this.repaintRegion("row-header",n,0,l,1/0)):(this.repaintRegion("column-header",n,0,l,1/0),this.repaintRegion("corner-header",n,0,l,1/0)),this._syncViewport()}_onColumnsMoved(t){let e,{region:i,index:s,span:o,destination:r}=t;if(o<=0)return;if(e="body"===i?this._columnSections:this._rowHeaderSections,s<0||s>=e.count)return;if(o=Math.min(o,e.count-s),r=Math.min(Math.max(0,r),e.count-o),s===r)return;e.move(s,o,r);let n=Math.min(s,r),l=Math.max(s+o-1,r+o-1);"body"===i?(this.repaintRegion("body",0,n,1/0,l),this.repaintRegion("column-header",0,n,1/0,l)):(this.repaintRegion("row-header",0,n,1/0,l),this.repaintRegion("corner-header",0,n,1/0,l)),this._syncViewport()}_onCellsChanged(t){let{region:e,row:i,column:s,rowSpan:o,columnSpan:r}=t;if(o<=0&&r<=0)return;let n=i,l=s,a=n+o-1,h=l+r-1;this.repaintRegion(e,n,l,a,h)}_onModelReset(t){let e=this._rowSections.count,i=this._columnSections.count,s=this._rowHeaderSections.count,o=this._columnHeaderSections.count,r=this._dataModel.rowCount("body")-e,n=this._dataModel.columnCount("body")-i,l=this._dataModel.columnCount("row-header")-s,a=this._dataModel.rowCount("column-header")-o;r>0?this._rowSections.insert(e,r):r<0&&this._rowSections.remove(e+r,-r),n>0?this._columnSections.insert(i,n):n<0&&this._columnSections.remove(i+n,-n),l>0?this._rowHeaderSections.insert(s,l):l<0&&this._rowHeaderSections.remove(s+l,-l),a>0?this._columnHeaderSections.insert(o,a):a<0&&this._columnHeaderSections.remove(o+a,-a),this._syncViewport()}_onRenderersChanged(){this.repaintContent()}_evtKeyDown(t){this._mousedown?(t.preventDefault(),t.stopPropagation()):this._keyHandler&&this._keyHandler.onKeyDown(this,t)}_evtMouseDown(t){0===t.button&&(this.activate(),t.preventDefault(),t.stopPropagation(),document.addEventListener("keydown",this,!0),document.addEventListener("mouseup",this,!0),document.addEventListener("mousedown",this,!0),document.addEventListener("mousemove",this,!0),document.addEventListener("contextmenu",this,!0),this._mousedown=!0,this._mouseHandler&&this._mouseHandler.onMouseDown(this,t))}_evtMouseMove(t){this._mousedown&&(t.preventDefault(),t.stopPropagation()),this._mouseHandler&&(this._mousedown?this._mouseHandler.onMouseMove(this,t):this._mouseHandler.onMouseHover(this,t))}_evtMouseUp(t){0===t.button&&(t.preventDefault(),t.stopPropagation(),this._mouseHandler&&this._mouseHandler.onMouseUp(this,t),this._releaseMouse())}_evtMouseDoubleClick(t){0===t.button&&(t.preventDefault(),t.stopPropagation(),this._mouseHandler&&this._mouseHandler.onMouseDoubleClick(this,t),this._releaseMouse())}_evtMouseLeave(t){this._mousedown?(t.preventDefault(),t.stopPropagation()):this._mouseHandler&&this._mouseHandler.onMouseLeave(this,t)}_evtContextMenu(t){this._mousedown?(t.preventDefault(),t.stopPropagation()):this._mouseHandler&&this._mouseHandler.onContextMenu(this,t)}_evtWheel(t){l.Platform.accelKey(t)||this._mouseHandler&&this._mouseHandler.onWheel(this,t)}_releaseMouse(){this._mousedown=!1,this._mouseHandler&&this._mouseHandler.release(),document.removeEventListener("keydown",this,!0),document.removeEventListener("mouseup",this,!0),document.removeEventListener("mousedown",this,!0),document.removeEventListener("mousemove",this,!0),document.removeEventListener("contextmenu",this,!0)}_refreshDPI(){let t=Math.ceil(window.devicePixelRatio);this._dpiRatio!==t&&(this._dpiRatio=t,this.repaintContent(),this.repaintOverlay(),this._resizeCanvasIfNeeded(this._viewportWidth,this._viewportHeight),this._canvas.style.width=this._canvas.width/this._dpiRatio+"px",this._canvas.style.height=this._canvas.height/this._dpiRatio+"px",this._overlay.style.width=this._overlay.width/this._dpiRatio+"px",this._overlay.style.height=this._overlay.height/this._dpiRatio+"px")}_resizeRow(t,e){let i=this._rowSections;if(t<0||t>=i.count)return;let s=i.sizeOf(t),r=i.clampSize(e);if(s===r)return;i.resize(t,r);let n=this._viewportWidth,l=this._viewportHeight;if(!this._viewport.isVisible||0===n||0===l)return void this._syncScrollState();let a=r-s,h=this.headerHeight,c=i.offsetOf(t)+h-this._scrollY;if(h>=l||c>=l)return void this._syncScrollState();if(c+s<=h)return this._scrollY+=a,void this._syncScrollState();let d=Math.max(h,c);if(c+s>=l||c+r>=l)return this.paintContent(0,d,n,l-d),this._paintOverlay(),void this._syncScrollState();let u,_,m,f=n;if(c+r<=h?(u=h-a,_=l-u,m=h):(u=c+s,_=l-u,m=u+a),this._blitContent(this._canvas,0,u,f,_,0,m),r>0&&c+r>h&&this.paintContent(0,d,n,c+r-d),this._stretchLastRow&&this.pageHeight>this.bodyHeight){let t=this._rowSections.count-1,e=h+this._rowSections.offsetOf(t);this.paintContent(0,e,n,l-e)}else a<0&&this.paintContent(0,l+a,n,-a);for(const e of["body","row-header"]){const i=o.getCellGroupsAtRow(this.dataModel,e,t);let s,r={region:e,xMin:0,xMax:0,yMin:0,yMax:0};switch(e){case"body":r.xMin=this.headerWidth,r.xMax=this.headerWidth+this.bodyWidth,r.yMin=this.headerHeight,r.yMax=this.headerHeight+this.bodyHeight,s=this._style.backgroundColor;break;case"row-header":r.xMin=0,r.xMax=this.headerWidth,r.yMin=this.headerHeight,r.yMax=this.headerHeight+this.bodyHeight,s=this._style.headerBackgroundColor}this._paintMergedCells(i,r,s)}this._paintOverlay(),this._syncScrollState()}_resizeColumn(t,e){let i=this._columnSections;if(t<0||t>=i.count)return;const s=null!=e?e:this._getMaxWidthInColumn(t,"body");if(!s||0==s)return;let r=i.sizeOf(t),n=i.clampSize(s);if(r===n)return;i.resize(t,n);let l=this._viewportWidth,a=this._viewportHeight;if(!this._viewport.isVisible||0===l||0===a)return void this._syncScrollState();let h=n-r,c=this.headerWidth,d=i.offsetOf(t)+c-this._scrollX;if(c>=l||d>=l)return void this._syncScrollState();if(d+r<=c)return this._scrollX+=h,void this._syncScrollState();let u=Math.max(c,d);if(d+r>=l||d+n>=l)return this.paintContent(u,0,l-u,a),this._paintOverlay(),void this._syncScrollState();let _,m,f,g=a;if(d+n<=c?(_=c-h,m=l-_,f=c):(_=d+r,m=l-_,f=_+h),this._blitContent(this._canvas,_,0,m,g,f,0),n>0&&d+n>c&&this.paintContent(u,0,d+n-u,a),this._stretchLastColumn&&this.pageWidth>this.bodyWidth){let t=this._columnSections.count-1,e=c+this._columnSections.offsetOf(t);this.paintContent(e,0,l-e,a)}else h<0&&this.paintContent(l+h,0,-h,a);for(const e of["body","column-header"]){const i=o.getCellGroupsAtColumn(this.dataModel,e,t);let s,r={region:e,xMin:0,xMax:0,yMin:0,yMax:0};switch(e){case"body":r.xMin=this.headerWidth,r.xMax=this.headerWidth+this.bodyWidth,r.yMin=this.headerHeight,r.yMax=this.headerHeight+this.bodyHeight,s=this._style.backgroundColor;break;case"column-header":r.xMin=this.headerWidth,r.xMax=this.headerWidth+this.bodyWidth,r.yMin=0,r.yMax=this.headerHeight,s=this._style.headerBackgroundColor}this._paintMergedCells(i,r,s)}this._paintOverlay(),this._syncScrollState()}_resizeRowHeader(t,e){let i=this._rowHeaderSections;if(t<0||t>=i.count)return;const s=null!=e?e:this._getMaxWidthInColumn(t,"row-header");if(!s||0==s)return;let r=i.sizeOf(t),n=i.clampSize(s);if(r===n)return;i.resize(t,n);let l=this._viewportWidth,a=this._viewportHeight;if(!this._viewport.isVisible||0===l||0===a)return void this._syncScrollState();let h=n-r,c=i.offsetOf(t);if(c>=l)return void this._syncScrollState();if(c+r>=l||c+n>=l)return this.paintContent(c,0,l-c,a),this._paintOverlay(),void this._syncScrollState();let d=c+r,u=l-d,_=a,m=d+h;if(this._blitContent(this._canvas,d,0,u,_,m,0),n>0&&this.paintContent(c,0,n,a),this._stretchLastColumn&&this.pageWidth>this.bodyWidth){let t=this._columnSections.count-1,e=this.headerWidth+this._columnSections.offsetOf(t);this.paintContent(e,0,l-e,a)}else h<0&&this.paintContent(l+h,0,-h,a);for(const e of["corner-header","row-header"]){const i=o.getCellGroupsAtColumn(this.dataModel,e,t);let s={region:e,xMin:0,xMax:0,yMin:0,yMax:0};switch(e){case"corner-header":s.xMin=0,s.xMax=this.headerWidth,s.yMin=0,s.yMax=this.headerHeight;break;case"row-header":s.xMin=0,s.xMax=this.headerWidth,s.yMin=this.headerHeight,s.yMax=this.headerHeight+this.bodyHeight}this._paintMergedCells(i,s,this._style.headerBackgroundColor)}this._paintOverlay(),this._syncScrollState()}_resizeColumnHeader(t,e){let i=this._columnHeaderSections;if(t<0||t>=i.count)return;let s=i.sizeOf(t),r=i.clampSize(e);if(s===r)return;i.resize(t,r);let n=this._viewportWidth,l=this._viewportHeight;if(!this._viewport.isVisible||0===n||0===l)return void this._syncScrollState();this._paintOverlay();let a=r-s,h=i.offsetOf(t);if(h>=l)return void this._syncScrollState();if(h+s>=l||h+r>=l)return this.paintContent(0,h,n,l-h),this._paintOverlay(),void this._syncScrollState();let c=h+s,d=n,u=l-c,_=c+a;if(this._blitContent(this._canvas,0,c,d,u,0,_),r>0&&this.paintContent(0,h,n,r),this._stretchLastRow&&this.pageHeight>this.bodyHeight){let t=this._rowSections.count-1,e=this.headerHeight+this._rowSections.offsetOf(t);this.paintContent(0,e,n,l-e)}else a<0&&this.paintContent(0,l+a,n,-a);for(const e of["corner-header","column-header"]){const i=o.getCellGroupsAtRow(this.dataModel,e,t);let s={region:e,xMin:0,xMax:0,yMin:0,yMax:0};switch(e){case"corner-header":s.xMin=0,s.xMax=this.headerWidth,s.yMin=0,s.yMax=this.headerHeight;break;case"column-header":s.xMin=this.headerWidth,s.xMax=this.headerWidth+this.bodyWidth,s.yMin=0,s.yMax=this.headerHeight}this._paintMergedCells(i,s,this._style.headerBackgroundColor)}this._paintOverlay(),this._syncScrollState()}_scrollTo(t,e){if(!this.dataModel)return;t=Math.max(0,Math.min(Math.floor(t),this.maxScrollX)),e=Math.max(0,Math.min(Math.floor(e),this.maxScrollY)),this._hScrollBar.value=t,this._vScrollBar.value=e;let i=t-this._scrollX,s=e-this._scrollY;if(0===i&&0===s)return;if(!this._viewport.isVisible)return this._scrollX=t,void(this._scrollY=e);let r=this._viewportWidth,n=this._viewportHeight;if(0===r||0===n)return this._scrollX=t,void(this._scrollY=e);let l=this.headerWidth,a=this.headerHeight,h=r-l,c=n-a;if(h<=0&&c<=0)return this._scrollX=t,void(this._scrollY=e);let d=0;0!==i&&h>0&&(d=Math.abs(i)>=h?h*n:Math.abs(i)*n);let u=0;if(0!==s&&c>0&&(u=Math.abs(s)>=c?r*c:r*Math.abs(s)),d+u>=r*n)return this._scrollX=t,this._scrollY=e,this.paintContent(0,0,r,n),void this._paintOverlay();if(this._scrollY=e,0!==s&&c>0)if(Math.abs(s)>=c)this.paintContent(0,a,r,c);else{const t=0,e=s<0?a:a+s,i=r,l=c-Math.abs(s);this._blitContent(this._canvas,t,e,i,l,t,e-s),this.paintContent(0,s<0?a:n-s,r,Math.abs(s));for(const t of["body","row-header"]){const e=o.getCellGroupsAtRegion(this.dataModel,t);let i,s={region:t,xMin:0,xMax:0,yMin:0,yMax:0};switch(t){case"body":s.xMin=this.headerWidth,s.xMax=this.headerWidth+this.bodyWidth,s.yMin=this.headerHeight,s.yMax=this.headerHeight+this.bodyHeight,i=this._style.backgroundColor;break;case"row-header":s.xMin=0,s.xMax=this.headerWidth,s.yMin=this.headerHeight,s.yMax=this.headerHeight+this.bodyHeight,i=this._style.headerBackgroundColor}this._paintMergedCells(e,s,i)}}if(this._scrollX=t,0!==i&&h>0)if(Math.abs(i)>=h)this.paintContent(l,0,h,n);else{const t=i<0?l:l+i,e=0,s=h-Math.abs(i),a=n;this._blitContent(this._canvas,t,e,s,a,t-i,e),this.paintContent(i<0?l:r-i,0,Math.abs(i),n);for(const t of["body","column-header"]){const e=o.getCellGroupsAtRegion(this.dataModel,t);let i,s={region:t,xMin:0,xMax:0,yMin:0,yMax:0};switch(t){case"body":s.xMin=this.headerWidth,s.xMax=this.headerWidth+this.bodyWidth,s.yMin=this.headerHeight,s.yMax=this.headerHeight+this.bodyHeight,i=this._style.backgroundColor;break;case"column-header":s.xMin=this.headerWidth,s.xMax=this.headerWidth+this.bodyWidth,s.yMin=0,s.yMax=this.headerHeight,i=this._style.headerBackgroundColor}this._paintMergedCells(e,s,i)}}this._paintOverlay()}_blitContent(t,e,i,s,o,r,n){e*=this._dpiRatio,i*=this._dpiRatio,s*=this._dpiRatio,o*=this._dpiRatio,r*=this._dpiRatio,n*=this._dpiRatio,this._canvasGC.save(),this._canvasGC.setTransform(1,0,0,1,0,0),this._canvasGC.drawImage(t,e,i,s,o,r,n,s,o),this._canvasGC.restore()}paintContent(t,e,i,s){this._canvasGC.setTransform(this._dpiRatio,0,0,this._dpiRatio,0,0),this._bufferGC.setTransform(this._dpiRatio,0,0,this._dpiRatio,0,0),this._canvasGC.clearRect(t,e,i,s),this._drawVoidRegion(t,e,i,s),this._drawBodyRegion(t,e,i,s),this._drawRowHeaderRegion(t,e,i,s),this._drawColumnHeaderRegion(t,e,i,s),this.drawCornerHeaderRegion(t,e,i,s)}_fitBodyColumnHeaders(t,e,i){const s=void 0===i?t.columnCount("body"):i;for(let i=0;i<s;i++){const s=t.rowCount("column-header");let o=0;for(let e=0;e<s;e++){const s=j._getConfig(t,e,i,"column-header"),r=this._getCellTextWidth(s);o=Math.max(o,r)}this.resizeColumn("body",i,o+e)}}_fitRowColumnHeaders(t,e,i){const s=void 0===i?t.columnCount("row-header"):i;for(let i=0;i<s;i++){const s=t.rowCount("column-header");let o=0;for(let e=0;e<s;e++){const s=j._getConfig(t,e,i,"corner-header"),r=this._getCellTextWidth(s);o=Math.max(o,r)}this.resizeColumn("row-header",i,o+e)}}_paintOverlay(){this._overlayGC.setTransform(this._dpiRatio,0,0,this._dpiRatio,0,0),this._overlayGC.clearRect(0,0,this._overlay.width,this._overlay.height),this._drawBodySelections(),this._drawRowHeaderSelections(),this._drawColumnHeaderSelections(),this._drawCursor(),this._drawShadows()}_drawVoidRegion(t,e,i,s){let o=this._style.voidColor;o&&(this._canvasGC.fillStyle=o,this._canvasGC.fillRect(t,e,i,s))}_drawBodyRegion(t,e,i,s){let r=this._columnSections.length-this._scrollX,n=this._rowSections.length-this._scrollY;if(r<=0||n<=0)return;let l=this.headerWidth,a=this.headerHeight;if(t+i<=l)return;if(e+s<=a)return;if(t>=l+r)return;if(e>=a+n)return;let h=this.bodyHeight,c=this.bodyWidth,d=this.pageHeight,u=this.pageWidth,_=Math.max(t,l),m=Math.max(e,a),f=Math.min(t+i-1,l+r-1),g=Math.min(e+s-1,a+n-1),p=this._rowSections.indexOf(m-a+this._scrollY),w=this._columnSections.indexOf(_-l+this._scrollX),y=this._rowSections.indexOf(g-a+this._scrollY),v=this._columnSections.indexOf(f-l+this._scrollX),x=this._rowSections.count-1,C=this._columnSections.count-1;y<0&&(y=x),v<0&&(v=C);let M=this._columnSections.offsetOf(w)+l-this._scrollX,S=this._rowSections.offsetOf(p)+a-this._scrollY,b=0,H=0,R=new Array(y-p+1),z=new Array(v-w+1);for(let t=p;t<=y;++t){let e=this._rowSections.sizeOf(t);R[t-p]=e,H+=e}for(let t=w;t<=v;++t){let e=this._columnSections.sizeOf(t);z[t-w]=e,b+=e}if(this._stretchLastRow&&d>h&&y===x){let t=this.pageHeight-this.bodyHeight;R[R.length-1]+=t,H+=t,g+=t}if(this._stretchLastColumn&&u>c&&v===C){let t=this.pageWidth-this.bodyWidth;z[z.length-1]+=t,b+=t,f+=t}let O={region:"body",xMin:_,yMin:m,xMax:f,yMax:g,x:M,y:S,width:b,height:H,row:p,column:w,rowSizes:R,columnSizes:z};this._drawBackground(O,this._style.backgroundColor),this._drawRowBackground(O,this._style.rowBackgroundColor),this._drawColumnBackground(O,this._style.columnBackgroundColor),this._drawCells(O),this._drawHorizontalGridLines(O,this._style.horizontalGridLineColor||this._style.gridLineColor),this._drawVerticalGridLines(O,this._style.verticalGridLineColor||this._style.gridLineColor);const k=o.getCellGroupsAtRegion(this.dataModel,O.region).filter((t=>this.cellGroupInteresectsRegion(t,O)));this._paintMergedCells(k,O,this._style.backgroundColor)}_drawRowHeaderRegion(t,e,i,s){let r=this.headerWidth,n=this.bodyHeight-this._scrollY;if(r<=0||n<=0)return;let l=this.headerHeight;if(t+i<=0)return;if(e+s<=l)return;if(t>=0+r)return;if(e>=l+n)return;let a=this.bodyHeight,h=this.pageHeight,c=t,d=Math.max(e,l),u=Math.min(t+i-1,0+r-1),_=Math.min(e+s-1,l+n-1),m=this._rowSections.indexOf(d-l+this._scrollY),f=this._rowHeaderSections.indexOf(c),g=this._rowSections.indexOf(_-l+this._scrollY),p=this._rowHeaderSections.indexOf(u),w=this._rowSections.count-1,y=this._rowHeaderSections.count-1;g<0&&(g=w),p<0&&(p=y);let v=this._rowHeaderSections.offsetOf(f),x=this._rowSections.offsetOf(m)+l-this._scrollY,C=0,M=0,S=new Array(g-m+1),b=new Array(p-f+1);for(let t=m;t<=g;++t){let e=this._rowSections.sizeOf(t);S[t-m]=e,M+=e}for(let t=f;t<=p;++t){let e=this._rowHeaderSections.sizeOf(t);b[t-f]=e,C+=e}if(this._stretchLastRow&&h>a&&g===w){let t=this.pageHeight-this.bodyHeight;S[S.length-1]+=t,M+=t,_+=t}let H={region:"row-header",xMin:c,yMin:d,xMax:u,yMax:_,x:v,y:x,width:C,height:M,row:m,column:f,rowSizes:S,columnSizes:b};this._drawBackground(H,this._style.headerBackgroundColor),this._drawCells(H),this._drawHorizontalGridLines(H,this._style.headerHorizontalGridLineColor||this._style.headerGridLineColor),this._drawVerticalGridLines(H,this._style.headerVerticalGridLineColor||this._style.headerGridLineColor);const R=o.getCellGroupsAtRegion(this.dataModel,H.region).filter((t=>this.cellGroupInteresectsRegion(t,H)));this._paintMergedCells(R,H,this._style.headerBackgroundColor)}_drawColumnHeaderRegion(t,e,i,s){let r=this.bodyWidth-this._scrollX,n=this.headerHeight;if(r<=0||n<=0)return;let l=this.headerWidth;if(t+i<=l)return;if(e+s<=0)return;if(t>=l+r)return;if(e>=0+n)return;let a=this.bodyWidth,h=this.pageWidth,c=Math.max(t,l),d=e,u=Math.min(t+i-1,l+r-1),_=Math.min(e+s-1,0+n-1),m=this._columnHeaderSections.indexOf(d),f=this._columnSections.indexOf(c-l+this._scrollX),g=this._columnHeaderSections.indexOf(_),p=this._columnSections.indexOf(u-l+this._scrollX),w=this._columnHeaderSections.count-1,y=this._columnSections.count-1;g<0&&(g=w),p<0&&(p=y);let v=this._columnSections.offsetOf(f)+l-this._scrollX,x=this._columnHeaderSections.offsetOf(m),C=0,M=0,S=new Array(g-m+1),b=new Array(p-f+1);for(let t=m;t<=g;++t){let e=this._columnHeaderSections.sizeOf(t);S[t-m]=e,M+=e}for(let t=f;t<=p;++t){let e=this._columnSections.sizeOf(t);b[t-f]=e,C+=e}if(this._stretchLastColumn&&h>a&&p===y){let t=this.pageWidth-this.bodyWidth;b[b.length-1]+=t,C+=t,u+=t}let H={region:"column-header",xMin:c,yMin:d,xMax:u,yMax:_,x:v,y:x,width:C,height:M,row:m,column:f,rowSizes:S,columnSizes:b};this._drawBackground(H,this._style.headerBackgroundColor),this._drawCells(H),this._drawHorizontalGridLines(H,this._style.headerHorizontalGridLineColor||this._style.headerGridLineColor),this._drawVerticalGridLines(H,this._style.headerVerticalGridLineColor||this._style.headerGridLineColor);const R=o.getCellGroupsAtRegion(this.dataModel,H.region).filter((t=>this.cellGroupInteresectsRegion(t,H)));this._paintMergedCells(R,H,this._style.headerBackgroundColor)}drawCornerHeaderRegion(t,e,i,s){let r=this.headerWidth,n=this.headerHeight;if(r<=0||n<=0)return;if(t+i<=0)return;if(e+s<=0)return;if(t>=0+r)return;if(e>=0+n)return;let l=t,a=e,h=Math.min(t+i-1,0+r-1),c=Math.min(e+s-1,0+n-1),d=this._columnHeaderSections.indexOf(a),u=this._rowHeaderSections.indexOf(l),_=this._columnHeaderSections.indexOf(c),m=this._rowHeaderSections.indexOf(h);_<0&&(_=this._columnHeaderSections.count-1),m<0&&(m=this._rowHeaderSections.count-1);let f=this._rowHeaderSections.offsetOf(u),g=this._columnHeaderSections.offsetOf(d),p=0,w=0,y=new Array(_-d+1),v=new Array(m-u+1);for(let t=d;t<=_;++t){let e=this._columnHeaderSections.sizeOf(t);y[t-d]=e,w+=e}for(let t=u;t<=m;++t){let e=this._rowHeaderSections.sizeOf(t);v[t-u]=e,p+=e}let x={region:"corner-header",xMin:l,yMin:a,xMax:h,yMax:c,x:f,y:g,width:p,height:w,row:d,column:u,rowSizes:y,columnSizes:v};this._drawBackground(x,this._style.headerBackgroundColor),this._drawCells(x),this._drawHorizontalGridLines(x,this._style.headerHorizontalGridLineColor||this._style.headerGridLineColor),this._drawVerticalGridLines(x,this._style.headerVerticalGridLineColor||this._style.headerGridLineColor);const C=o.getCellGroupsAtRegion(this.dataModel,x.region).filter((t=>this.cellGroupInteresectsRegion(t,x)));this._paintMergedCells(C,x,this._style.headerBackgroundColor)}_drawBackground(t,e){if(!e)return;let{xMin:i,yMin:s,xMax:o,yMax:r}=t;this._canvasGC.fillStyle=e,this._canvasGC.fillRect(i,s,o-i+1,r-s+1)}_drawRowBackground(t,e){if(!e)return;let i=Math.max(t.xMin,t.x),s=Math.min(t.x+t.width-1,t.xMax);for(let o=t.y,r=0,n=t.rowSizes.length;r<n;++r){let n=t.rowSizes[r];if(0===n)continue;let l=e(t.row+r);if(l){let e=Math.max(t.yMin,o),r=Math.min(o+n-1,t.yMax);this._canvasGC.fillStyle=l,this._canvasGC.fillRect(i,e,s-i+1,r-e+1)}o+=n}}_drawColumnBackground(t,e){if(!e)return;let i=Math.max(t.yMin,t.y),s=Math.min(t.y+t.height-1,t.yMax);for(let o=t.x,r=0,n=t.columnSizes.length;r<n;++r){let n=t.columnSizes[r];if(0===n)continue;let l=e(t.column+r);if(l){let e=Math.max(t.xMin,o),r=Math.min(o+n-1,t.xMax);this._canvasGC.fillStyle=l,this._canvasGC.fillRect(e,i,r-e+1,s-i+1)}o+=n}}_getColumnSize(t,e){return"corner-header"===t?this._rowHeaderSections.sizeOf(e):this.columnSize(t,e)}_getRowSize(t,e){return"corner-header"===t?this._columnHeaderSections.sizeOf(e):this.rowSize(t,e)}_drawCells(t){if(!this._dataModel)return;let e={x:0,y:0,width:0,height:0,region:t.region,row:0,column:0,value:null,metadata:I.emptyMetadata},i=-1;this._bufferGC.save();let s=new N(this._bufferGC),r=0;for(let n=t.x,l=0,a=t.columnSizes.length;l<a;++l){let a=t.columnSizes[l];if(0===a)continue;let h=t.column+l;e.x=n,e.width=a,e.column=h;for(let l=t.y,c=0,d=t.rowSizes.length;c<d;++c){if(r=t.rowSizes[c],0===r)continue;let d=t.row+c;if(i=o.getGroupIndex(this.dataModel,e.region,d,h),-1!==i){l+=r;continue}s.clearRect(n,l,a,r);let u=j._getCellValue(this.dataModel,t.region,d,h),_=j._getCellMetadata(this.dataModel,t.region,d,h);e.y=l,e.height=r,e.width=a,e.row=d,e.value=u,e.metadata=_;let m=this._cellRenderers.get(e);s.save();try{m instanceof C?m.isReady(e)?m.paint(s,e):(m.paintPlaceholder(s,e),m.load(e).then((()=>{const e=d,i=d+1,s=h,o=h+1;this.repaintRegion(t.region,e,s,i,o)}))):m.paint(s,e)}catch(t){console.error(t)}s.restore();let f=Math.max(t.xMin,e.x),g=Math.min(e.x+e.width-1,t.xMax),p=Math.max(t.yMin,e.y),w=Math.min(e.y+e.height-1,t.yMax);this._blitContent(this._buffer,f,p,g-f+1,w-p+1,f,p),l+=r}s.restore(),n+=a}s.dispose(),this._bufferGC.restore()}cellGroupInteresectsRegion(t,e){const i=e.row,s=e.row+e.rowSizes.length,o=e.column,r=e.column+e.columnSizes.length,n=Math.min(t.r2,s)-Math.max(t.r1,i),l=Math.min(t.c2,r)-Math.max(t.c1,o);return n>=0&&l>=0}static _getCellValue(t,e,i,s){try{return t.data(e,i,s)}catch(t){return console.error(t),null}}static _getCellMetadata(t,e,i,s){try{return t.metadata(e,i,s)}catch(t){return console.error(t),I.emptyMetadata}}_paintMergedCells(t,e,i){if(!this._dataModel)return;let s={x:0,y:0,width:0,height:0,region:e.region,row:0,column:0,value:null,metadata:I.emptyMetadata};i&&(this._canvasGC.fillStyle=i),this._canvasGC.lineWidth=1,this._bufferGC.save();let o=new N(this._bufferGC);for(const r of t){let t=0;for(let i=r.c1;i<=r.c2;i++)t+=this._getColumnSize(e.region,i);let n=0;for(let t=r.r1;t<=r.r2;t++)n+=this._getRowSize(e.region,t);let l=j._getCellValue(this.dataModel,e.region,r.r1,r.c1),a=j._getCellMetadata(this.dataModel,e.region,r.r1,r.c2),h=0,c=0;switch(e.region){case"body":h=this._columnSections.offsetOf(r.c1)+this.headerWidth-this._scrollX,c=this._rowSections.offsetOf(r.r1)+this.headerHeight-this._scrollY;break;case"column-header":h=this._columnSections.offsetOf(r.c1)+this.headerWidth-this._scrollX,c=this._rowSections.offsetOf(r.r1);break;case"row-header":h=this._columnSections.offsetOf(r.c1),c=this._rowSections.offsetOf(r.r1)+this.headerHeight-this._scrollY;break;case"corner-header":h=this._columnSections.offsetOf(r.c1),c=this._rowSections.offsetOf(r.r1)}s.x=h,s.y=c,s.width=t,s.height=n,s.region=e.region,s.row=r.r1,s.column=r.c1,s.value=l,s.metadata=a;const d=Math.max(e.xMin,h),u=Math.min(h+t-2,e.xMax),_=Math.max(e.yMin,c),m=Math.min(c+n-2,e.yMax);if(u<=d||m<=_)continue;i&&this._canvasGC.fillRect(d,_,u-d+1,m-_+1);let f=this._cellRenderers.get(s);o.clearRect(s.x,s.y,t,n),o.save();try{if(f instanceof C)if(f.isReady(s))f.paint(o,s);else{f.paintPlaceholder(o,s);const t=r.r1,i=r.r2,n=r.c1,l=r.c2;f.load(s).then((()=>{this.repaintRegion(e.region,t,n,i,l)}))}else f.paint(o,s)}catch(t){console.error(t)}o.restore(),this._blitContent(this._buffer,d,_,u-d+1,m-_+1,d,_)}o.dispose(),this._bufferGC.restore()}_drawHorizontalGridLines(t,e){if(!e)return;const i=Math.max(t.xMin,t.x),s=Math.min(t.x+t.width,t.xMax+1);this._canvasGC.beginPath(),this._canvasGC.lineWidth=1;const o=this.bodyHeight,r=this.pageHeight;let n=t.rowSizes.length;this._stretchLastRow&&r>o&&t.row+n===this._rowSections.count&&(n-=1);for(let e=t.y,o=0;o<n;++o){let r=t.rowSizes[o];if(0===r)continue;let n=e+r-1;n>=t.yMin&&n<=t.yMax&&(this._canvasGC.moveTo(i,n+.5),this._canvasGC.lineTo(s,n+.5)),e+=r}this._canvasGC.strokeStyle=e,this._canvasGC.stroke()}_drawVerticalGridLines(t,e){if(!e)return;const i=Math.max(t.yMin,t.y),s=Math.min(t.y+t.height,t.yMax+1);this._canvasGC.beginPath(),this._canvasGC.lineWidth=1;const o=this.bodyWidth,r=this.pageWidth;let n=t.columnSizes.length;this._stretchLastColumn&&r>o&&t.column+n===this._columnSections.count&&(n-=1);for(let e=t.x,o=0;o<n;++o){let r=t.columnSizes[o];if(0===r)continue;let n=e+r-1;n>=t.xMin&&n<=t.xMax&&(this._canvasGC.moveTo(n+.5,i),this._canvasGC.lineTo(n+.5,s)),e+=r}this._canvasGC.strokeStyle=e,this._canvasGC.stroke()}_drawBodySelections(){let t=this._selectionModel;if(!t||t.isEmpty)return;let e=this._style.selectionFillColor,i=this._style.selectionBorderColor;if(!e&&!i)return;let s=this._scrollX,r=this._scrollY,n=this._rowSections.indexOf(r),l=this._columnSections.indexOf(s);if(n<0||l<0)return;let a=this.bodyWidth,h=this.bodyHeight,c=this.pageWidth,d=this.pageHeight,u=this.headerWidth,_=this.headerHeight,m=this._rowSections.indexOf(r+d),f=this._columnSections.indexOf(s+c),g=this._rowSections.count-1,p=this._columnSections.count-1;m=m<0?g:m,f=f<0?p:f;let w=this._overlayGC;w.save(),w.beginPath(),w.rect(u,_,c,d),w.clip(),e&&(w.fillStyle=e),i&&(w.strokeStyle=i,w.lineWidth=1);for(let y of t.selections()){if(y.r1<n&&y.r2<n)continue;if(y.r1>m&&y.r2>m)continue;if(y.c1<l&&y.c2<l)continue;if(y.c1>f&&y.c2>f)continue;let t,v=Math.max(0,Math.min(y.r1,g)),x=Math.max(0,Math.min(y.c1,p)),C=Math.max(0,Math.min(y.r2,g)),M=Math.max(0,Math.min(y.c2,p));v>C&&(t=v,v=C,C=t),x>M&&(t=x,x=M,M=t);const S=o.joinCellGroupWithMergedCellGroups(this.dataModel,{r1:v,r2:C,c1:x,c2:M},"body");v=S.r1,C=S.r2,x=S.c1,M=S.c2;let b=this._columnSections.offsetOf(x)-s+u,H=this._rowSections.offsetOf(v)-r+_,R=this._columnSections.extentOf(M)-s+u,z=this._rowSections.extentOf(C)-r+_;this._stretchLastColumn&&c>a&&M===p&&(R=u+c-1),this._stretchLastRow&&d>h&&C===g&&(z=_+d-1),b=Math.max(u-1,b),H=Math.max(_-1,H),R=Math.min(u+c+1,R),z=Math.min(_+d+1,z),R<b||z<H||(e&&w.fillRect(b,H,R-b+1,z-H+1),i&&w.strokeRect(b-.5,H-.5,R-b+1,z-H+1))}w.restore()}_drawRowHeaderSelections(){let t=this._selectionModel;if(!t||t.isEmpty||"column"==t.selectionMode)return;if(0===this.headerWidth||0===this.pageHeight)return;let e=this._style.headerSelectionFillColor,i=this._style.headerSelectionBorderColor;if(!e&&!i)return;let s=this._scrollY,o=this.bodyHeight,r=this.pageHeight,n=this.headerWidth,l=this.headerHeight,a=this._rowSections,h=this._overlayGC;h.save(),h.beginPath(),h.rect(0,l,n,r),h.clip(),e&&(h.fillStyle=e),i&&(h.strokeStyle=i,h.lineWidth=1);let c=a.count-1,d=a.indexOf(s),u=a.indexOf(s+r-1);u=u<0?c:u;for(let _=d;_<=u;++_){if(!t.isRowSelected(_))continue;let d=a.offsetOf(_)-s+l,u=a.sizeOf(_);this._stretchLastRow&&r>o&&_===c&&(u=l+r-d),0!==u&&(e&&h.fillRect(0,d,n,u),i&&(h.beginPath(),h.moveTo(n-.5,d-1),h.lineTo(n-.5,d+u),h.stroke()))}h.restore()}_drawColumnHeaderSelections(){let t=this._selectionModel;if(!t||t.isEmpty||"row"==t.selectionMode)return;if(0===this.headerHeight||0===this.pageWidth)return;let e=this._style.headerSelectionFillColor,i=this._style.headerSelectionBorderColor;if(!e&&!i)return;let s=this._scrollX,o=this.bodyWidth,r=this.pageWidth,n=this.headerWidth,l=this.headerHeight,a=this._columnSections,h=this._overlayGC;h.save(),h.beginPath(),h.rect(n,0,r,l),h.clip(),e&&(h.fillStyle=e),i&&(h.strokeStyle=i,h.lineWidth=1);let c=a.count-1,d=a.indexOf(s),u=a.indexOf(s+r-1);u=u<0?c:u;for(let _=d;_<=u;++_){if(!t.isColumnSelected(_))continue;let d=a.offsetOf(_)-s+n,u=a.sizeOf(_);this._stretchLastColumn&&r>o&&_===c&&(u=n+r-d),0!==u&&(e&&h.fillRect(d,0,u,l),i&&(h.beginPath(),h.moveTo(d-1,l-.5),h.lineTo(d+u,l-.5),h.stroke()))}h.restore()}_drawCursor(){let t=this._selectionModel;if(!t||t.isEmpty||"cell"!==t.selectionMode)return;let e=this._style.cursorFillColor,i=this._style.cursorBorderColor;if(!e&&!i)return;let s=t.cursorRow,r=t.cursorColumn,n=this._rowSections.count-1,l=this._columnSections.count-1;if(s<0||s>n)return;if(r<0||r>l)return;let a=s,h=r;const c=o.joinCellGroupWithMergedCellGroups(this.dataModel,{r1:s,r2:a,c1:r,c2:h},"body");s=c.r1,a=c.r2,r=c.c1,h=c.c2;let d=this._scrollX,u=this._scrollY,_=this.bodyWidth,m=this.bodyHeight,f=this.pageWidth,g=this.pageHeight,p=this.headerWidth,w=this.headerHeight,y=this._viewportWidth,v=this._viewportHeight,x=this._columnSections.offsetOf(r)-d+p,C=this._columnSections.extentOf(h)-d+p,M=this._rowSections.offsetOf(s)-u+w,S=this._rowSections.extentOf(a)-u+w;if(this._stretchLastColumn&&f>_&&r===l&&(C=y-1),this._stretchLastRow&&g>m&&s===n&&(S=v-1),C<x||S<M)return;if(x-1>=y||M-1>=v||C+1<p||S+1<w)return;let b=this._overlayGC;b.save(),b.beginPath(),b.rect(p,w,f,g),b.clip(),b.clearRect(x,M,C-x+1,S-M+1),e&&(b.fillStyle=e,b.fillRect(x,M,C-x+1,S-M+1)),i&&(b.strokeStyle=i,b.lineWidth=2,b.strokeRect(x,M,C-x,S-M)),b.restore()}_drawShadows(){let t=this._style.scrollShadow;if(!t)return;let e=this._scrollX,i=this._scrollY,s=this.maxScrollX,o=this.maxScrollY,r=this.headerWidth,n=this.headerHeight,l=this.pageWidth,a=this.pageHeight,h=this._viewportWidth,c=this._viewportHeight,d=this.bodyWidth,u=this.bodyHeight;this._stretchLastRow&&a>u&&(u=a),this._stretchLastColumn&&l>d&&(d=l);let _=this._overlayGC;if(_.save(),i>0){let i=0,s=n,o=0,a=s+t.size,h=_.createLinearGradient(i,s,o,a);h.addColorStop(0,t.color1),h.addColorStop(.5,t.color2),h.addColorStop(1,t.color3);let c=0,u=n,m=r+Math.min(l,d-e),f=t.size;_.fillStyle=h,_.fillRect(c,u,m,f)}if(e>0){let e=r,s=0,o=e+t.size,l=0,h=_.createLinearGradient(e,s,o,l);h.addColorStop(0,t.color1),h.addColorStop(.5,t.color2),h.addColorStop(1,t.color3);let c=r,d=0,m=t.size,f=n+Math.min(a,u-i);_.fillStyle=h,_.fillRect(c,d,m,f)}if(i<o){let i=0,s=c,o=0,n=c-t.size,a=_.createLinearGradient(i,s,o,n);a.addColorStop(0,t.color1),a.addColorStop(.5,t.color2),a.addColorStop(1,t.color3);let h=0,u=c-t.size,m=r+Math.min(l,d-e),f=t.size;_.fillStyle=a,_.fillRect(h,u,m,f)}if(e<s){let e=h,s=0,o=h-t.size,r=0,l=_.createLinearGradient(e,s,o,r);l.addColorStop(0,t.color1),l.addColorStop(.5,t.color2),l.addColorStop(1,t.color3);let c=h-t.size,d=0,m=t.size,f=n+Math.min(a,u-i);_.fillStyle=l,_.fillRect(c,d,m,f)}_.restore()}}!function(t){function e(t){return null===t.value||void 0===t.value?"":String(t.value)}t.copyFormatGeneric=e,t.defaultStyle={voidColor:"#F3F3F3",backgroundColor:"#FFFFFF",gridLineColor:"rgba(20, 20, 20, 0.15)",headerBackgroundColor:"#F3F3F3",headerGridLineColor:"rgba(20, 20, 20, 0.25)",selectionFillColor:"rgba(49, 119, 229, 0.2)",selectionBorderColor:"rgba(0, 107, 247, 1.0)",cursorBorderColor:"rgba(0, 107, 247, 1.0)",headerSelectionFillColor:"rgba(20, 20, 20, 0.1)",headerSelectionBorderColor:"rgba(0, 107, 247, 1.0)",scrollShadow:{size:10,color1:"rgba(0, 0, 0, 0.20)",color2:"rgba(0, 0, 0, 0.05)",color3:"rgba(0, 0, 0, 0.00)"}},t.defaultSizes={rowHeight:20,columnWidth:64,rowHeaderWidth:64,columnHeaderHeight:20},t.minimumSizes={rowHeight:20,columnWidth:10,rowHeaderWidth:10,columnHeaderHeight:20},t.defaultCopyConfig={separator:"\t",format:e,headers:"none",warningThreshold:1e6}}(j||(j={})),function(t){t.ScrollRequest=new _.ConflatableMessage("scroll-request"),t.OverlayPaintRequest=new _.ConflatableMessage("overlay-paint-request"),t.createCanvas=function(){let t=document.createElement("canvas");return t.width=0,t.height=0,t},t.regionHasMergedCells=function(t,e){return o.getCellGroupsAtRegion(t,e).length>0};class e extends _.ConflatableMessage{constructor(t,e,i,s,o){super("paint-request"),this._region=t,this._r1=e,this._c1=i,this._r2=s,this._c2=o}get region(){return this._region}get r1(){return this._r1}get c1(){return this._c1}get r2(){return this._r2}get c2(){return this._c2}conflate(t){return"all"===this._region||("all"===t._region?(this._region="all",!0):this._region===t._region&&(this._r1=Math.min(this._r1,t._r1),this._c1=Math.min(this._c1,t._c1),this._r2=Math.max(this._r2,t._r2),this._c2=Math.max(this._c2,t._c2),!0))}}t.PaintRequest=e;class i extends _.ConflatableMessage{constructor(t,e,i){super("row-resize-request"),this._region=t,this._index=e,this._size=i}get region(){return this._region}get index(){return this._index}get size(){return this._size}conflate(t){return this._region===t._region&&this._index===t._index&&(this._size=t._size,!0)}}t.RowResizeRequest=i;class s extends _.ConflatableMessage{constructor(t,e,i){super("column-resize-request"),this._region=t,this._index=e,this._size=i}get region(){return this._region}get index(){return this._index}get size(){return this._size}conflate(t){return this._region===t._region&&this._index===t._index&&(this._size=t._size,!0)}}t.ColumnResizeRequest=s}(Y||(Y={}));class U extends I{constructor(t){super();let e=F.splitFields(t.schema);this._data=t.data,this._bodyFields=e.bodyFields,this._headerFields=e.headerFields,this._missingValues=F.createMissingMap(t.schema)}rowCount(t){return"body"===t?this._data.length:1}columnCount(t){return"body"===t?this._bodyFields.length:this._headerFields.length}data(t,e,i){let s,o;switch(t){case"body":s=this._bodyFields[i],o=this._data[e][s.name];break;case"column-header":s=this._bodyFields[i],o=s.title||s.name;break;case"row-header":s=this._headerFields[i],o=this._data[e][s.name];break;case"corner-header":s=this._headerFields[i],o=s.title||s.name;break;default:throw"unreachable"}return null!==this._missingValues&&"string"==typeof o&&!0===this._missingValues[o]?null:o}metadata(t,e,i){return"body"===t||"column-header"===t?this._bodyFields[i]:this._headerFields[i]}}!function(t){t.splitFields=function(t){let e;e=void 0===t.primaryKey?[]:"string"==typeof t.primaryKey?[t.primaryKey]:t.primaryKey;let i=[],s=[];for(let o of t.fields)-1===e.indexOf(o.name)?i.push(o):s.push(o);return{bodyFields:i,headerFields:s}},t.createMissingMap=function(t){if(!t.missingValues||0===t.missingValues.length)return null;let e=Object.create(null);for(let i of t.missingValues)e[i]=!0;return e}}(F||(F={}));const $=/^(\d+(\.\d+)?)%$/,J=/^(\d+(\.\d+)?)px$/;class Z extends C{constructor(t={}){super(),this.backgroundColor=t.backgroundColor||"",this.textColor=t.textColor||"#000000",this.placeholder=t.placeholder||"...",this.width=t.width||"",this.height=void 0===t.height?"100%":t.height}isReady(t){return!t.value||void 0!==Z.dataCache.get(t.value)}async load(t){if(!t.value)return;const e=t.value,i=new m.PromiseDelegate;Z.dataCache.set(e,void 0);const s=new Image;return s.onload=()=>{Z.dataCache.set(e,s),i.resolve()},s.src=e,i.promise}paintPlaceholder(t,e){this.drawBackground(t,e),this.drawPlaceholder(t,e)}paint(t,e){this.drawBackground(t,e),this.drawImage(t,e)}drawBackground(t,e){const i=g.resolveOption(this.backgroundColor,e);i&&(t.fillStyle=i,t.fillRect(e.x,e.y,e.width,e.height))}drawPlaceholder(t,e){const i=g.resolveOption(this.placeholder,e),s=g.resolveOption(this.textColor,e),o=e.x+e.width/2,r=e.y+e.height/2;t.fillStyle=s,t.fillText(i,o,r)}drawImage(t,e){if(!e.value)return;const i=Z.dataCache.get(e.value);if(!i)return this.drawPlaceholder(t,e);const s=g.resolveOption(this.width,e),o=g.resolveOption(this.height,e);if(!s&&!o)return void t.drawImage(i,e.x,e.y);let r,n,l,a,h=i.width,c=i.height;(r=s.match($))?h=parseFloat(r[1])/100*e.width:(n=s.match(J))&&(h=parseFloat(n[1])),(l=o.match($))?c=parseFloat(l[1])/100*e.height:(a=o.match(J))&&(c=parseFloat(a[1])),s||(h=i.width/i.height*c),o||(c=i.height/i.width*h),t.drawImage(i,e.x,e.y,h,c)}}Z.dataCache=new Map}}]);
"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[1496],{91496:(e,t,n)=>{n.r(t),n.d(t,{default:()=>f});var a=n(3053),s=n(12982),r=n(38639),l=n(97104),o=n(71677),i=n(68239),c=n(78156),d=n(16954),u=n(20998),m=n(81997),h=n(24475),p=n(31516);const g="jp-Licenses-Filters-title";class _ extends p.SplitPanel{constructor(e){super(),this.addClass("jp-Licenses"),this.model=e.model,this.initLeftPanel(),this.initFilters(),this.initBundles(),this.initGrid(),this.initLicenseText(),this.setRelativeSizes([1,2,3]),this.model.initLicenses().then((()=>this._updateBundles())),this.model.trackerDataChanged.connect((()=>{this.title.label=this.model.title}))}dispose(){this.isDisposed||(this._bundles.currentChanged.disconnect(this.onBundleSelected,this),this.model.dispose(),super.dispose())}initLeftPanel(){this._leftPanel=new p.Panel,this._leftPanel.addClass("jp-Licenses-FormArea"),this.addWidget(this._leftPanel),p.SplitPanel.setStretch(this._leftPanel,1)}initFilters(){this._filters=new _.Filters(this.model),p.SplitPanel.setStretch(this._filters,1),this._leftPanel.addWidget(this._filters)}initBundles(){this._bundles=new p.TabBar({orientation:"vertical",renderer:new _.BundleTabRenderer(this.model)}),this._bundles.addClass("jp-Licenses-Bundles"),p.SplitPanel.setStretch(this._bundles,1),this._leftPanel.addWidget(this._bundles),this._bundles.currentChanged.connect(this.onBundleSelected,this),this.model.stateChanged.connect((()=>this._bundles.update()))}initGrid(){this._grid=new _.Grid(this.model),p.SplitPanel.setStretch(this._grid,1),this.addWidget(this._grid)}initLicenseText(){this._licenseText=new _.FullText(this.model),p.SplitPanel.setStretch(this._grid,1),this.addWidget(this._licenseText)}onBundleSelected(){var e;(null===(e=this._bundles.currentTitle)||void 0===e?void 0:e.label)&&(this.model.currentBundleName=this._bundles.currentTitle.label)}_updateBundles(){this._bundles.clearTabs();let e=0;const{currentBundleName:t}=this.model;let n=0;for(const a of this.model.bundleNames){const s=new p.Widget;s.title.label=a,a===t&&(n=e),this._bundles.insertTab(++e,s.title)}this._bundles.currentIndex=n}}var b;!function(e){e.REPORT_FORMATS={markdown:{id:"markdown",title:"Markdown",icon:i.markdownIcon},csv:{id:"csv",title:"CSV",icon:i.spreadsheetIcon},json:{id:"csv",title:"JSON",icon:i.jsonIcon}},e.DEFAULT_FORMAT="markdown";class t extends i.VDomModel{constructor(e){super(),this._selectedPackageChanged=new m.Signal(this),this._trackerDataChanged=new m.Signal(this),this._currentPackageIndex=0,this._licensesReady=new u.PromiseDelegate,this._packageFilter={},this._trans=e.trans,this._licensesUrl=e.licensesUrl,this._serverSettings=e.serverSettings||d.ServerConnection.makeSettings(),e.currentBundleName&&(this._currentBundleName=e.currentBundleName),e.packageFilter&&(this._packageFilter=e.packageFilter),e.currentPackageIndex&&(this._currentPackageIndex=e.currentPackageIndex)}async initLicenses(){try{const e=await d.ServerConnection.makeRequest(this._licensesUrl,{},this._serverSettings);this._serverResponse=await e.json(),this._licensesReady.resolve(),this.stateChanged.emit(void 0)}catch(e){this._licensesReady.reject(e)}}async download(e){const t=`${this._licensesUrl}?format=${e.format}&download=1`,n=document.createElement("a");n.href=t,n.download="",document.body.appendChild(n),n.click(),document.body.removeChild(n)}get selectedPackageChanged(){return this._selectedPackageChanged}get trackerDataChanged(){return this._trackerDataChanged}get bundleNames(){var e;return Object.keys((null===(e=this._serverResponse)||void 0===e?void 0:e.bundles)||{})}get currentBundleName(){return this._currentBundleName?this._currentBundleName:this.bundleNames.length?this.bundleNames[0]:null}set currentBundleName(e){this._currentBundleName!==e&&(this._currentBundleName=e,this.stateChanged.emit(void 0),this._trackerDataChanged.emit(void 0))}get licensesReady(){return this._licensesReady.promise}get bundles(){var e;return(null===(e=this._serverResponse)||void 0===e?void 0:e.bundles)||{}}get currentPackageIndex(){return this._currentPackageIndex}set currentPackageIndex(e){this._currentPackageIndex!==e&&(this._currentPackageIndex=e,this._selectedPackageChanged.emit(void 0),this.stateChanged.emit(void 0),this._trackerDataChanged.emit(void 0))}get currentPackage(){var e;return this.currentBundleName&&this.bundles&&null!=this._currentPackageIndex?this.getFilteredPackages((null===(e=this.bundles[this.currentBundleName])||void 0===e?void 0:e.packages)||[])[this._currentPackageIndex]:null}get trans(){return this._trans}get title(){return`${this._currentBundleName||""} ${this._trans.__("Licenses")}`.trim()}get packageFilter(){return this._packageFilter}set packageFilter(e){this._packageFilter=e,this.stateChanged.emit(void 0),this._trackerDataChanged.emit(void 0)}getFilteredPackages(e){let t=[],n=Object.entries(this._packageFilter).filter((([e,t])=>t&&`${t}`.trim().length)).map((([e,t])=>[e,`${t}`.toLowerCase().trim().split(" ")]));for(const a of e){let e=0;for(const[t,s]of n){let n=0,r=`${a[t]}`.toLowerCase();for(const e of s)r.includes(e)&&(n+=1);n&&(e+=1)}e===n.length&&t.push(a)}return Object.values(t)}}e.Model=t;class n extends i.VDomRenderer{constructor(e){super(e),this.renderFilter=e=>{const t=this.model.packageFilter[e]||"";return c.createElement("input",{type:"text",name:e,defaultValue:t,className:"jp-mod-styled",onInput:this.onFilterInput})},this.onFilterInput=e=>{const t=e.currentTarget,{name:n,value:a}=t;this.model.packageFilter={...this.model.packageFilter,[n]:a}},this.addClass("jp-Licenses-Filters"),this.addClass("jp-RenderedHTMLCommon")}render(){const{trans:e}=this.model;return c.createElement("div",null,c.createElement("label",null,c.createElement("strong",{className:g},e.__("Filter Licenses By"))),c.createElement("ul",null,c.createElement("li",null,c.createElement("label",null,e.__("Package")),this.renderFilter("name")),c.createElement("li",null,c.createElement("label",null,e.__("Version")),this.renderFilter("versionInfo")),c.createElement("li",null,c.createElement("label",null,e.__("License")),this.renderFilter("licenseId"))),c.createElement("label",null,c.createElement("strong",{className:g},e.__("Distributions"))))}}e.Filters=n;class a extends p.TabBar.Renderer{constructor(e){super(),this.closeIconSelector=".lm-TabBar-tabCloseIcon",this.model=e}renderTab(e){let t=e.title.caption,n=this.createTabKey(e),a=this.createTabStyle(e),s=this.createTabClass(e),r=this.createTabDataset(e);return h.h.li({key:n,className:s,title:t,style:a,dataset:r},this.renderIcon(e),this.renderLabel(e),this.renderCountBadge(e))}renderCountBadge(e){const t=e.title.label,{bundles:n}=this.model,a=this.model.getFilteredPackages((n&&t?n[t].packages:[])||[]);return h.h.label({},`${a.length}`)}}e.BundleTabRenderer=a;class s extends i.VDomRenderer{constructor(e){super(e),this.renderRow=(e,t)=>{const n=t===this.model.currentPackageIndex,a=()=>this.model.currentPackageIndex=t;return c.createElement("tr",{key:e.name,className:n?"jp-mod-selected":"",onClick:a},c.createElement("td",null,c.createElement("input",{type:"radio",name:"show-package-license",value:t,onChange:a,checked:n})),c.createElement("th",null,e.name),c.createElement("td",null,c.createElement("code",null,e.versionInfo)),c.createElement("td",null,c.createElement("code",null,e.licenseId)))},this.addClass("jp-Licenses-Grid"),this.addClass("jp-RenderedHTMLCommon")}render(){var e;const{bundles:t,currentBundleName:n,trans:a}=this.model,s=this.model.getFilteredPackages(t&&n&&(null===(e=t[n])||void 0===e?void 0:e.packages)||[]);return s.length?c.createElement("form",null,c.createElement("table",null,c.createElement("thead",null,c.createElement("tr",null,c.createElement("td",null),c.createElement("th",null,a.__("Package")),c.createElement("th",null,a.__("Version")),c.createElement("th",null,a.__("License")))),c.createElement("tbody",null,s.map(this.renderRow)))):c.createElement("blockquote",null,c.createElement("em",null,a.__("No Packages found")))}}e.Grid=s;class r extends i.VDomRenderer{constructor(e){super(e),this.addClass("jp-Licenses-Text"),this.addClass("jp-RenderedHTMLCommon"),this.addClass("jp-RenderedMarkdown")}render(){const{currentPackage:e,trans:t}=this.model;let n="",a=t.__("No Package selected"),s="";if(e){const{name:r,versionInfo:l,licenseId:o,extractedText:i}=e;n=`${r} v${l}`,a=`${t.__("License")}: ${o||t.__("No License ID found")}`,s=i||t.__("No License Text found")}return[c.createElement("h1",{key:"h1"},n),c.createElement("blockquote",{key:"quote"},c.createElement("em",null,a)),c.createElement("code",{key:"code"},s)]}}e.FullText=r}(_||(_={})),function(e){e.open="help:open",e.about="help:about",e.activate="help:activate",e.close="help:close",e.show="help:show",e.hide="help:hide",e.jupyterForum="help:jupyter-forum",e.licenses="help:licenses",e.licenseReport="help:license-report",e.refreshLicenses="help:licenses-refresh"}(b||(b={}));const k="https:"===window.location.protocol,f=[{id:"@jupyterlab/help-extension:about",description:'Adds a "About" dialog feature.',autoStart:!0,requires:[o.ITranslator],optional:[s.ICommandPalette],activate:(e,t,n)=>{const{commands:a}=e,r=t.load("jupyterlab"),l=r.__("Help");a.addCommand(b.about,{label:r.__("About %1",e.name),execute:()=>{const t=r.__("Version %1",e.version),n=c.createElement("span",{className:"jp-About-version-info"},c.createElement("span",{className:"jp-About-version"},t)),a=c.createElement("span",{className:"jp-About-header"},c.createElement(i.jupyterIcon.react,{margin:"7px 9.5px",height:"auto",width:"58px"}),c.createElement("div",{className:"jp-About-header-info"},c.createElement(i.jupyterlabWordmarkIcon.react,{height:"auto",width:"196px"}),n)),l=c.createElement("span",{className:"jp-About-externalLinks"},c.createElement("a",{href:"https://github.com/jupyterlab/jupyterlab/graphs/contributors",target:"_blank",rel:"noopener noreferrer",className:"jp-Button-flat"},r.__("CONTRIBUTOR LIST")),c.createElement("a",{href:"https://jupyter.org/about.html",target:"_blank",rel:"noopener noreferrer",className:"jp-Button-flat"},r.__("ABOUT PROJECT JUPYTER"))),o=c.createElement("span",{className:"jp-About-copyright"},r.__("© 2015-2023 Project Jupyter Contributors")),d=c.createElement("div",{className:"jp-About-body"},l,o);return(0,s.showDialog)({title:a,body:d,buttons:[s.Dialog.createButton({label:r.__("Dismiss"),className:"jp-About-button jp-mod-reject jp-mod-styled"})]})}}),n&&n.addItem({command:b.about,category:l})}},{id:"@jupyterlab/help-extension:jupyter-forum",description:"Adds command to open the Jupyter Forum website.",autoStart:!0,requires:[o.ITranslator],optional:[s.ICommandPalette],activate:(e,t,n)=>{const{commands:a}=e,s=t.load("jupyterlab"),r=s.__("Help");a.addCommand(b.jupyterForum,{label:s.__("Jupyter Forum"),execute:()=>{window.open("https://discourse.jupyter.org/c/jupyterlab")}}),n&&n.addItem({command:b.jupyterForum,category:r})}},{id:"@jupyterlab/help-extension:open",description:"Add command to open websites as panel or browser tab.",autoStart:!0,requires:[o.ITranslator],optional:[a.ILayoutRestorer],activate:(e,t,n)=>{const{commands:a,shell:l}=e,o=t.load("jupyterlab"),c="help-doc",d=new s.WidgetTracker({namespace:c});let u=0;a.addCommand(b.open,{label:e=>{var t;return null!==(t=e.text)&&void 0!==t?t:o.__("Open the provided `url` in a tab.")},execute:e=>{const t=e.url,n=e.text;if(e.newBrowserTab||k&&"https:"!==r.URLExt.parse(t).protocol)return void window.open(t);const a=function(e,t){const n=new i.IFrame({sandbox:["allow-scripts","allow-forms"]});n.url=e,n.addClass("jp-Help"),n.title.label=t,n.id=`${c}-${++u}`;const a=new s.MainAreaWidget({content:n});return a.addClass("jp-Help"),a}(t,n);return d.add(a),l.add(a,"main"),a}}),n&&n.restore(d,{command:b.open,args:e=>({url:e.content.url,text:e.content.title.label}),name:e=>e.content.url})}},{id:"@jupyterlab/help-extension:resources",description:"Adds menu entries to Jupyter reference documentation websites.",autoStart:!0,requires:[l.IMainMenu,o.ITranslator],optional:[a.ILabShell,s.ICommandPalette],activate:(e,t,n,a,r)=>{const l=n.load("jupyterlab"),o=l.__("Help"),{commands:i,serviceManager:d}=e,u=[{text:l.__("JupyterLab Reference"),url:"https://jupyterlab.readthedocs.io/en/stable/"},{text:l.__("JupyterLab FAQ"),url:"https://jupyterlab.readthedocs.io/en/stable/getting_started/faq.html"},{text:l.__("Jupyter Reference"),url:"https://jupyter.org/documentation"},{text:l.__("Markdown Reference"),url:"https://commonmark.org/help/"}];u.sort(((e,t)=>e.text.localeCompare(t.text)));const m=t.helpMenu,h=u.map((e=>({args:e,command:b.open})));m.addGroup(h,10);const p=new Map,g=(e,t)=>{var n;if(!t.length)return;const r=t[t.length-1];if(!r.kernel||p.has(r.kernel.name))return;const o=d.sessions.connectTo({model:r,kernelConnectionOptions:{handleComms:!1}});null===(n=o.kernel)||void 0===n||n.info.then((e=>{var t,n;const r=o.kernel.name;if(p.has(r))return;const u=null===(n=null===(t=d.kernelspecs)||void 0===t?void 0:t.specs)||void 0===n?void 0:n.kernelspecs[r];if(!u)return;p.set(r,e);let h=!1;const g=async()=>{const e=await i.execute("helpmenu:get-kernel");h=(null==e?void 0:e.name)===r};g().catch((e=>{console.error("Failed to get the kernel for the current widget.",e)})),a&&a.currentChanged.connect(g);const _=()=>h,k=`help-menu-${r}:banner`,f=u.display_name,v=u.resources["logo-svg"]||u.resources["logo-64x64"];i.addCommand(k,{label:l.__("About the %1 Kernel",f),isVisible:_,isEnabled:_,execute:()=>{const t=c.createElement("img",{src:v,alt:l.__("Kernel Icon")}),n=c.createElement("span",{className:"jp-About-header"},t,c.createElement("div",{className:"jp-About-header-info"},f)),a=c.createElement("pre",null,e.banner),r=c.createElement("div",{className:"jp-About-body"},a);return(0,s.showDialog)({title:n,body:r,buttons:[s.Dialog.createButton({label:l.__("Dismiss"),className:"jp-About-button jp-mod-reject jp-mod-styled"})]})}}),m.addGroup([{command:k}],20);const y=[];(e.help_links||[]).forEach((e=>{const t=`help-menu-${r}:${e.text}`;i.addCommand(t,{label:i.label(b.open,e),isVisible:_,isEnabled:_,execute:()=>i.execute(b.open,e)}),y.push({command:t})})),m.addGroup(y,21)})).then((()=>{o.dispose()}))};for(const e of d.sessions.running())g(d.sessions,[e]);d.sessions.runningChanged.connect(g),r&&(u.forEach((e=>{r.addItem({args:e,command:b.open,category:o})})),r.addItem({args:{reload:!0},command:"apputils:reset",category:o}))}},{id:"@jupyterlab/help-extension:licenses",description:"Adds licenses used report tools.",autoStart:!0,requires:[o.ITranslator],optional:[l.IMainMenu,s.ICommandPalette,a.ILayoutRestorer],activate:(e,t,n,a,l)=>{if(!r.PageConfig.getOption("licensesUrl"))return;const{commands:o,shell:c}=e,d=t.load("jupyterlab"),u=d.__("Help"),m=d.__("Download All Licenses as"),h=d.__("Licenses"),p=d.__("Refresh Licenses");let g=0;const k=r.URLExt.join(r.PageConfig.getBaseUrl(),r.PageConfig.getOption("licensesUrl"))+"/",f="help-licenses",v=new s.WidgetTracker({namespace:f});function y(e){return _.REPORT_FORMATS[e]||_.REPORT_FORMATS[_.DEFAULT_FORMAT]}o.addCommand(b.licenses,{label:h,execute:t=>{const n=function(t){const n=new _.Model({...t,licensesUrl:k,trans:d,serverSettings:e.serviceManager.serverSettings}),a=new _({model:n});a.id=`${f}-${++g}`,a.title.label=h,a.title.icon=i.copyrightIcon;const r=new s.MainAreaWidget({content:a,reveal:n.licensesReady});r.toolbar.addItem("refresh-licenses",new i.CommandToolbarButton({id:b.refreshLicenses,args:{noLabel:1},commands:o})),r.toolbar.addItem("spacer",i.Toolbar.createSpacerItem());for(const e of Object.keys(_.REPORT_FORMATS)){const t=new i.CommandToolbarButton({id:b.licenseReport,args:{format:e,noLabel:1},commands:o});r.toolbar.addItem(`download-${e}`,t)}return r}(t);return c.add(n,"main",{type:"Licenses"}),v.add(n),n.content.model.trackerDataChanged.connect((()=>{v.save(n)})),n}}),o.addCommand(b.refreshLicenses,{label:e=>e.noLabel?"":p,caption:p,icon:i.refreshIcon,execute:async()=>{var e;return null===(e=v.currentWidget)||void 0===e?void 0:e.content.model.initLicenses()}}),o.addCommand(b.licenseReport,{label:e=>{if(e.noLabel)return"";const t=y(`${e.format}`);return`${m} ${t.title}`},caption:e=>{const t=y(`${e.format}`);return`${m} ${t.title}`},icon:e=>y(`${e.format}`).icon,execute:async e=>{var t;const n=y(`${e.format}`);return await(null===(t=v.currentWidget)||void 0===t?void 0:t.content.model.download({format:n.id}))}}),a&&a.addItem({command:b.licenses,category:u}),n&&n.helpMenu.addGroup([{command:b.licenses}],0),l&&l.restore(v,{command:b.licenses,name:e=>"licenses",args:e=>{const{currentBundleName:t,currentPackageIndex:n,packageFilter:a}=e.content.model;return{currentBundleName:t,currentPackageIndex:n,packageFilter:a}}})}}]}}]);
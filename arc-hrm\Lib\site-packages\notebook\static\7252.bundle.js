"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[7252,9685],{79685:(e,t,r)=>{r.r(t),r.d(t,{default:()=>k});var n,a=r(3053),o=r(12982),d=r(38639),i=r(73847),c=r(70856),s=r(36768),w=r(87749),m=r(71677);!function(e){e.markdownPreview="markdownviewer:open",e.markdownEditor="markdownviewer:edit"}(n||(n={}));const p="Markdown Preview",l={activate:function(e,t,r,a,s,w,m){const k=r.load("jupyterlab"),{commands:u,docRegistry:g}=e;t.addFactory(c.markdownRendererFactory);const f=new o.WidgetTracker({namespace:"markdownviewer-widget"});let h={...i.MarkdownViewer.defaultConfig};function y(e){Object.keys(h).forEach((t=>{var r;e.setOption(t,null!==(r=h[t])&&void 0!==r?r:null)}))}if(s){const e=e=>{h=e.composite,f.forEach((e=>{y(e.content)}))};s.load(l.id).then((t=>{t.changed.connect((()=>{e(t)})),e(t)})).catch((e=>{console.error(e.message)}))}const v=new i.MarkdownViewerFactory({rendermime:t,name:p,label:k.__("Markdown Preview"),primaryFileType:g.getFileType("markdown"),fileTypes:["markdown"],defaultRendered:["markdown"]});return v.widgetCreated.connect(((e,t)=>{t.context.pathChanged.connect((()=>{f.save(t)})),y(t.content),f.add(t)})),g.addWidgetFactory(v),a&&a.restore(f,{command:"docmanager:open",args:e=>({path:e.context.path,factory:p}),name:e=>e.context.path}),u.addCommand(n.markdownPreview,{label:k.__("Markdown Preview"),execute:e=>{const t=e.path;if("string"==typeof t)return u.execute("docmanager:open",{path:t,factory:p,options:e.options})}}),u.addCommand(n.markdownEditor,{execute:()=>{const e=f.currentWidget;if(!e)return;const t=e.context.path;return u.execute("docmanager:open",{path:t,factory:"Editor",options:{mode:"split-right"}})},isVisible:()=>{const e=f.currentWidget;return e&&".md"===d.PathExt.extname(e.context.path)||!1},label:k.__("Show Markdown Editor")}),w&&w.add(new i.MarkdownViewerTableOfContentsFactory(f,t.markdownParser,null!=m?m:t.sanitizer)),f},id:"@jupyterlab/markdownviewer-extension:plugin",description:"Adds markdown file viewer and provides its tracker.",provides:i.IMarkdownViewerTracker,requires:[c.IRenderMimeRegistry,m.ITranslator],optional:[a.ILayoutRestorer,s.ISettingRegistry,w.ITableOfContentsRegistry,o.ISanitizer],autoStart:!0},k=l}}]);
"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[4039],{84039:(e,t,n)=>{function r(e){return{type:e,style:"keyword"}}n.r(t),n.d(t,{haxe:()=>ae,hxml:()=>ie});var a,i=r("keyword a"),o=r("keyword b"),l=r("keyword c"),u=r("operator"),c={type:"atom",style:"atom"},f={type:"attribute",style:"attribute"},s=r("typedef"),d={if:i,while:i,else:o,do:o,try:o,return:l,break:l,continue:l,new:l,throw:l,var:r("var"),inline:f,static:f,using:r("import"),public:f,private:f,cast:r("cast"),import:r("import"),macro:r("macro"),function:r("function"),catch:r("catch"),untyped:r("untyped"),callback:r("cb"),for:r("for"),switch:r("switch"),case:r("case"),default:r("default"),in:u,never:r("property_access"),trace:r("trace"),class:s,abstract:s,enum:s,interface:s,typedef:s,extends:s,implements:s,dynamic:s,true:c,false:c,null:c},p=/[+\-*&%=<>!?|]/;function m(e,t,n){return t.tokenize=n,n(e,t)}function v(e,t){for(var n,r=!1;null!=(n=e.next());){if(n==t&&!r)return!0;r=!r&&"\\"==n}}function y(e,t,n){return s=e,a=n,t}function h(e,t){var n=e.next();if('"'==n||"'"==n)return m(e,t,(r=n,function(e,t){return v(e,r)&&(t.tokenize=h),y("string","string")}));if(/[\[\]{}\(\),;\:\.]/.test(n))return y(n);if("0"==n&&e.eat(/x/i))return e.eatWhile(/[\da-f]/i),y("number","number");if(/\d/.test(n)||"-"==n&&e.eat(/\d/))return e.match(/^\d*(?:\.\d*(?!\.))?(?:[eE][+\-]?\d+)?/),y("number","number");if(t.reAllowed&&"~"==n&&e.eat(/\//))return v(e,"/"),e.eatWhile(/[gimsu]/),y("regexp","string.special");if("/"==n)return e.eat("*")?m(e,t,b):e.eat("/")?(e.skipToEnd(),y("comment","comment")):(e.eatWhile(p),y("operator",null,e.current()));if("#"==n)return e.skipToEnd(),y("conditional","meta");if("@"==n)return e.eat(/:/),e.eatWhile(/[\w_]/),y("metadata","meta");if(p.test(n))return e.eatWhile(p),y("operator",null,e.current());if(/[A-Z]/.test(n))return e.eatWhile(/[\w_<>]/),y("type","type",a=e.current());e.eatWhile(/[\w_]/);var r,a=e.current(),i=d.propertyIsEnumerable(a)&&d[a];return i&&t.kwAllowed?y(i.type,i.style,a):y("variable","variable",a)}function b(e,t){for(var n,r=!1;n=e.next();){if("/"==n&&r){t.tokenize=h;break}r="*"==n}return y("comment","comment")}var k={atom:!0,number:!0,variable:!0,string:!0,regexp:!0};function x(e,t,n,r,a,i){this.indented=e,this.column=t,this.type=n,this.prev=a,this.info=i,null!=r&&(this.align=r)}function w(e,t){for(var n=e.localVars;n;n=n.next)if(n.name==t)return!0}function g(e,t){if(/[a-z]/.test(t.charAt(0)))return!1;for(var n=e.importedtypes.length,r=0;r<n;r++)if(e.importedtypes[r]==t)return!0}function A(e){for(var t=T.state,n=t.importedtypes;n;n=n.next)if(n.name==e)return;t.importedtypes={name:e,next:t.importedtypes}}var T={state:null,column:null,marked:null,cc:null};function V(){for(var e=arguments.length-1;e>=0;e--)T.cc.push(arguments[e])}function E(){return V.apply(null,arguments),!0}function S(e,t){for(var n=t;n;n=n.next)if(n.name==e)return!0;return!1}function _(e){var t=T.state;if(t.context){if(T.marked="def",S(e,t.localVars))return;t.localVars={name:e,next:t.localVars}}else if(t.globalVars){if(S(e,t.globalVars))return;t.globalVars={name:e,next:t.globalVars}}}var O={name:"this",next:null};function P(){T.state.context||(T.state.localVars=O),T.state.context={prev:T.state.context,vars:T.state.localVars}}function W(){T.state.localVars=T.state.context.vars,T.state.context=T.state.context.prev}function z(e,t){var n=function(){var n=T.state;n.lexical=new x(n.indented,T.stream.column(),e,null,n.lexical,t)};return n.lex=!0,n}function U(){var e=T.state;e.lexical.prev&&(")"==e.lexical.type&&(e.indented=e.lexical.indented),e.lexical=e.lexical.prev)}function D(e){return function t(n){return n==e?E():";"==e?V():E(t)}}function Z(e){return"@"==e?E(J):"var"==e?E(z("vardef"),H,D(";"),U):"keyword a"==e?E(z("form"),C,Z,U):"keyword b"==e?E(z("form"),Z,U):"{"==e?E(z("}"),P,G,U,W):";"==e?E():"attribute"==e?E(I):"function"==e?E(X):"for"==e?E(z("form"),D("("),z(")"),M,D(")"),U,Z,U):"variable"==e?E(z("stat"),$):"switch"==e?E(z("form"),C,z("}","switch"),D("{"),G,U,U):"case"==e?E(C,D(":")):"default"==e?E(D(":")):"catch"==e?E(z("form"),P,D("("),re,D(")"),Z,U,W):"import"==e?E(N,D(";")):"typedef"==e?E(Y):V(z("stat"),C,D(";"),U)}function C(e){return k.hasOwnProperty(e)||"type"==e?E(B):"function"==e?E(X):"keyword c"==e?E(R):"("==e?E(z(")"),R,D(")"),U,B):"operator"==e?E(C):"["==e?E(z("]"),q(R,"]"),U,B):"{"==e?E(z("}"),q(j,"}"),U,B):E()}function R(e){return e.match(/[;\}\)\],]/)?V():V(C)}function B(e,t){return"operator"==e&&/\+\+|--/.test(t)?E(B):"operator"==e||":"==e?E(C):";"!=e?"("==e?E(z(")"),q(C,")"),U,B):"."==e?E(F,B):"["==e?E(z("]"),C,D("]"),U,B):void 0:void 0}function I(e){return"attribute"==e?E(I):"function"==e?E(X):"var"==e?E(H):void 0}function J(e){return":"==e||"variable"==e?E(J):"("==e?E(z(")"),q(L,")"),U,Z):void 0}function L(e){if("variable"==e)return E()}function N(e,t){return"variable"==e&&/[A-Z]/.test(t.charAt(0))?(A(t),E()):"variable"==e||"property"==e||"."==e||"*"==t?E(N):void 0}function Y(e,t){return"variable"==e&&/[A-Z]/.test(t.charAt(0))?(A(t),E()):"type"==e&&/[A-Z]/.test(t.charAt(0))?E():void 0}function $(e){return":"==e?E(U,Z):V(B,D(";"),U)}function F(e){if("variable"==e)return T.marked="property",E()}function j(e){if("variable"==e&&(T.marked="property"),k.hasOwnProperty(e))return E(D(":"),C)}function q(e,t){function n(r){return","==r?E(e,n):r==t?E():E(D(t))}return function(r){return r==t?E():V(e,n)}}function G(e){return"}"==e?E():V(Z,G)}function H(e,t){return"variable"==e?(_(t),E(ee,K)):E()}function K(e,t){return"="==t?E(C,K):","==e?E(H):void 0}function M(e,t){return"variable"==e?(_(t),E(Q,C)):V()}function Q(e,t){if("in"==t)return E()}function X(e,t){return"variable"==e||"type"==e?(_(t),E(X)):"new"==t?E(X):"("==e?E(z(")"),P,q(re,")"),U,ee,Z,W):void 0}function ee(e){if(":"==e)return E(te)}function te(e){return"type"==e||"variable"==e?E():"{"==e?E(z("}"),q(ne,"}"),U):void 0}function ne(e){if("variable"==e)return E(ee)}function re(e,t){if("variable"==e)return _(t),E(ee)}W.lex=!0,U.lex=!0;const ae={name:"haxe",startState:function(e){return{tokenize:h,reAllowed:!0,kwAllowed:!0,cc:[],lexical:new x(-e,0,"block",!1),importedtypes:["Int","Float","String","Void","Std","Bool","Dynamic","Array"],context:null,indented:0}},token:function(e,t){if(e.sol()&&(t.lexical.hasOwnProperty("align")||(t.lexical.align=!1),t.indented=e.indentation()),e.eatSpace())return null;var n=t.tokenize(e,t);return"comment"==s?n:(t.reAllowed=!("operator"!=s&&"keyword c"!=s&&!s.match(/^[\[{}\(,;:]$/)),t.kwAllowed="."!=s,function(e,t,n,r,a){var i=e.cc;for(T.state=e,T.stream=a,T.marked=null,T.cc=i,e.lexical.hasOwnProperty("align")||(e.lexical.align=!0);;)if((i.length?i.pop():Z)(n,r)){for(;i.length&&i[i.length-1].lex;)i.pop()();return T.marked?T.marked:"variable"==n&&w(e,r)?"variableName.local":"variable"==n&&g(e,r)?"variableName.special":t}}(t,n,s,a,e))},indent:function(e,t,n){if(e.tokenize!=h)return 0;var r=t&&t.charAt(0),a=e.lexical;"stat"==a.type&&"}"==r&&(a=a.prev);var i=a.type,o=r==i;return"vardef"==i?a.indented+4:"form"==i&&"{"==r?a.indented:"stat"==i||"form"==i?a.indented+n.unit:"switch"!=a.info||o?a.align?a.column+(o?0:1):a.indented+(o?0:n.unit):a.indented+(/^(?:case|default)\b/.test(t)?n.unit:2*n.unit)},languageData:{indentOnInput:/^\s*[{}]$/,commentTokens:{line:"//",block:{open:"/*",close:"*/"}}}},ie={name:"hxml",startState:function(){return{define:!1,inString:!1}},token:function(e,t){var n=e.peek(),r=e.sol();if("#"==n)return e.skipToEnd(),"comment";if(r&&"-"==n){var a="variable-2";return e.eat(/-/),"-"==e.peek()&&(e.eat(/-/),a="keyword a"),"D"==e.peek()&&(e.eat(/[D]/),a="keyword c",t.define=!0),e.eatWhile(/[A-Z]/i),a}return n=e.peek(),0==t.inString&&"'"==n&&(t.inString=!0,e.next()),1==t.inString?(e.skipTo("'")||e.skipToEnd(),"'"==e.peek()&&(e.next(),t.inString=!1),"string"):(e.next(),null)},languageData:{commentTokens:{line:"#"}}}}}]);
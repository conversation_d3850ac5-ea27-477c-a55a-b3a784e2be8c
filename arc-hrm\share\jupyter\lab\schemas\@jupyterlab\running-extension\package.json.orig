{"name": "@jupyterlab/running-extension", "version": "4.1.8", "description": "JupyterLab - Running Sessions Extension", "homepage": "https://github.com/jupyterlab/jupyterlab", "bugs": {"url": "https://github.com/jupyterlab/jupyterlab/issues"}, "repository": {"type": "git", "url": "https://github.com/jupyterlab/jupyterlab.git"}, "license": "BSD-3-<PERSON><PERSON>", "author": "Project Jupyter", "sideEffects": ["style/**/*.css", "style/index.js"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "directories": {"lib": "lib/"}, "files": ["lib/*.d.ts", "lib/*.js.map", "lib/*.js", "schema/*.json", "style/**/*.css", "style/index.js", "src/**/*.{ts,tsx}"], "scripts": {"build": "tsc -b", "clean": "rimraf lib && rimraf tsconfig.tsbuildinfo", "docs": "typedoc src", "watch": "tsc -b --watch"}, "dependencies": {"@jupyterlab/application": "^4.1.8", "@jupyterlab/coreutils": "^6.1.8", "@jupyterlab/docregistry": "^4.1.8", "@jupyterlab/rendermime-interfaces": "^3.9.8", "@jupyterlab/running": "^4.1.8", "@jupyterlab/services": "^7.1.8", "@jupyterlab/translation": "^4.1.8", "@jupyterlab/ui-components": "^4.1.8", "@lumino/commands": "^2.2.0", "@lumino/polling": "^2.1.2", "@lumino/signaling": "^2.1.2", "@lumino/widgets": "^2.3.1"}, "devDependencies": {"rimraf": "~5.0.5", "typedoc": "~0.24.7", "typescript": "~5.1.6"}, "publishConfig": {"access": "public"}, "jupyterlab": {"extension": true, "schemaDir": "schema"}, "styleModule": "style/index.js"}
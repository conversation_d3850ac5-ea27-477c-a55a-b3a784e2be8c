"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[2323],{92323:(t,e,n)=>{n.r(e),n.d(e,{Annotation:()=>ot,AnnotationType:()=>lt,ChangeDesc:()=>E,ChangeSet:()=>A,CharCategory:()=>xt,Compartment:()=>H,EditorSelection:()=>J,EditorState:()=>St,Facet:()=>D,Line:()=>f,MapMode:()=>P,Prec:()=>G,Range:()=>bt,RangeSet:()=>At,RangeSetBuilder:()=>Rt,RangeValue:()=>It,SelectionRange:()=>B,StateEffect:()=>ct,StateEffectType:()=>at,StateField:()=>U,Text:()=>i,Transaction:()=>ft,codePointAt:()=>S,codePointSize:()=>I,combineConfig:()=>yt,countColumn:()=>jt,findClusterBreak:()=>m,findColumn:()=>qt,fromCodePoint:()=>y});class i{lineAt(t){if(t<0||t>this.length)throw new RangeError(`Invalid position ${t} in document of length ${this.length}`);return this.lineInner(t,!1,1,0)}line(t){if(t<1||t>this.lines)throw new RangeError(`Invalid line number ${t} in ${this.lines}-line document`);return this.lineInner(t,!0,1,0)}replace(t,e,n){let i=[];return this.decompose(0,t,i,2),n.length&&n.decompose(0,n.length,i,3),this.decompose(e,this.length,i,1),r.from(i,this.length-(e-t)+n.length)}append(t){return this.replace(this.length,this.length,t)}slice(t,e=this.length){let n=[];return this.decompose(t,e,n,0),r.from(n,e-t)}eq(t){if(t==this)return!0;if(t.length!=this.length||t.lines!=this.lines)return!1;let e=this.scanIdentical(t,1),n=this.length-this.scanIdentical(t,-1),i=new l(this),s=new l(t);for(let t=e,r=e;;){if(i.next(t),s.next(t),t=0,i.lineBreak!=s.lineBreak||i.done!=s.done||i.value!=s.value)return!1;if(r+=i.value.length,i.done||r>=n)return!0}}iter(t=1){return new l(this,t)}iterRange(t,e=this.length){return new a(this,t,e)}iterLines(t,e){let n;if(null==t)n=this.iter();else{null==e&&(e=this.lines+1);let i=this.line(t).from;n=this.iterRange(i,Math.max(i,e==this.lines+1?this.length:e<=1?0:this.line(e-1).to))}return new c(n)}toString(){return this.sliceString(0)}toJSON(){let t=[];return this.flatten(t),t}constructor(){}static of(t){if(0==t.length)throw new RangeError("A document must have at least one line");return 1!=t.length||t[0]?t.length<=32?new s(t):r.from(s.split(t,[])):i.empty}}class s extends i{constructor(t,e=function(t){let e=-1;for(let n of t)e+=n.length+1;return e}(t)){super(),this.text=t,this.length=e}get lines(){return this.text.length}get children(){return null}lineInner(t,e,n,i){for(let s=0;;s++){let r=this.text[s],h=i+r.length;if((e?n:h)>=t)return new f(i,h,n,r);i=h+1,n++}}decompose(t,e,n,i){let r=t<=0&&e>=this.length?this:new s(o(this.text,t,e),Math.min(e,this.length)-Math.max(0,t));if(1&i){let t=n.pop(),e=h(r.text,t.text.slice(),0,r.length);if(e.length<=32)n.push(new s(e,t.length+r.length));else{let t=e.length>>1;n.push(new s(e.slice(0,t)),new s(e.slice(t)))}}else n.push(r)}replace(t,e,n){if(!(n instanceof s))return super.replace(t,e,n);let i=h(this.text,h(n.text,o(this.text,0,t)),e),l=this.length+n.length-(e-t);return i.length<=32?new s(i,l):r.from(s.split(i,[]),l)}sliceString(t,e=this.length,n="\n"){let i="";for(let s=0,r=0;s<=e&&r<this.text.length;r++){let h=this.text[r],o=s+h.length;s>t&&r&&(i+=n),t<o&&e>s&&(i+=h.slice(Math.max(0,t-s),e-s)),s=o+1}return i}flatten(t){for(let e of this.text)t.push(e)}scanIdentical(){return 0}static split(t,e){let n=[],i=-1;for(let r of t)n.push(r),i+=r.length+1,32==n.length&&(e.push(new s(n,i)),n=[],i=-1);return i>-1&&e.push(new s(n,i)),e}}class r extends i{constructor(t,e){super(),this.children=t,this.length=e,this.lines=0;for(let e of t)this.lines+=e.lines}lineInner(t,e,n,i){for(let s=0;;s++){let r=this.children[s],h=i+r.length,o=n+r.lines-1;if((e?o:h)>=t)return r.lineInner(t,e,n,i);i=h+1,n=o+1}}decompose(t,e,n,i){for(let s=0,r=0;r<=e&&s<this.children.length;s++){let h=this.children[s],o=r+h.length;if(t<=o&&e>=r){let s=i&((r<=t?1:0)|(o>=e?2:0));r>=t&&o<=e&&!s?n.push(h):h.decompose(t-r,e-r,n,s)}r=o+1}}replace(t,e,n){if(n.lines<this.lines)for(let i=0,s=0;i<this.children.length;i++){let h=this.children[i],o=s+h.length;if(t>=s&&e<=o){let l=h.replace(t-s,e-s,n),a=this.lines-h.lines+l.lines;if(l.lines<a>>4&&l.lines>a>>6){let s=this.children.slice();return s[i]=l,new r(s,this.length-(e-t)+n.length)}return super.replace(s,o,l)}s=o+1}return super.replace(t,e,n)}sliceString(t,e=this.length,n="\n"){let i="";for(let s=0,r=0;s<this.children.length&&r<=e;s++){let h=this.children[s],o=r+h.length;r>t&&s&&(i+=n),t<o&&e>r&&(i+=h.sliceString(t-r,e-r,n)),r=o+1}return i}flatten(t){for(let e of this.children)e.flatten(t)}scanIdentical(t,e){if(!(t instanceof r))return 0;let n=0,[i,s,h,o]=e>0?[0,0,this.children.length,t.children.length]:[this.children.length-1,t.children.length-1,-1,-1];for(;;i+=e,s+=e){if(i==h||s==o)return n;let r=this.children[i],l=t.children[s];if(r!=l)return n+r.scanIdentical(l,e);n+=r.length+1}}static from(t,e=t.reduce(((t,e)=>t+e.length+1),-1)){let n=0;for(let e of t)n+=e.lines;if(n<32){let n=[];for(let e of t)e.flatten(n);return new s(n,e)}let i=Math.max(32,n>>5),h=i<<1,o=i>>1,l=[],a=0,c=-1,f=[];function u(t){let e;if(t.lines>h&&t instanceof r)for(let e of t.children)u(e);else t.lines>o&&(a>o||!a)?(g(),l.push(t)):t instanceof s&&a&&(e=f[f.length-1])instanceof s&&t.lines+e.lines<=32?(a+=t.lines,c+=t.length+1,f[f.length-1]=new s(e.text.concat(t.text),e.length+1+t.length)):(a+t.lines>i&&g(),a+=t.lines,c+=t.length+1,f.push(t))}function g(){0!=a&&(l.push(1==f.length?f[0]:r.from(f,c)),c=-1,a=f.length=0)}for(let e of t)u(e);return g(),1==l.length?l[0]:new r(l,e)}}function h(t,e,n=0,i=1e9){for(let s=0,r=0,h=!0;r<t.length&&s<=i;r++){let o=t[r],l=s+o.length;l>=n&&(l>i&&(o=o.slice(0,i-s)),s<n&&(o=o.slice(n-s)),h?(e[e.length-1]+=o,h=!1):e.push(o)),s=l+1}return e}function o(t,e,n){return h(t,[""],e,n)}i.empty=new s([""],0);class l{constructor(t,e=1){this.dir=e,this.done=!1,this.lineBreak=!1,this.value="",this.nodes=[t],this.offsets=[e>0?1:(t instanceof s?t.text.length:t.children.length)<<1]}nextInner(t,e){for(this.done=this.lineBreak=!1;;){let n=this.nodes.length-1,i=this.nodes[n],r=this.offsets[n],h=r>>1,o=i instanceof s?i.text.length:i.children.length;if(h==(e>0?o:0)){if(0==n)return this.done=!0,this.value="",this;e>0&&this.offsets[n-1]++,this.nodes.pop(),this.offsets.pop()}else if((1&r)==(e>0?0:1)){if(this.offsets[n]+=e,0==t)return this.lineBreak=!0,this.value="\n",this;t--}else if(i instanceof s){let s=i.text[h+(e<0?-1:0)];if(this.offsets[n]+=e,s.length>Math.max(0,t))return this.value=0==t?s:e>0?s.slice(t):s.slice(0,s.length-t),this;t-=s.length}else{let r=i.children[h+(e<0?-1:0)];t>r.length?(t-=r.length,this.offsets[n]+=e):(e<0&&this.offsets[n]--,this.nodes.push(r),this.offsets.push(e>0?1:(r instanceof s?r.text.length:r.children.length)<<1))}}}next(t=0){return t<0&&(this.nextInner(-t,-this.dir),t=this.value.length),this.nextInner(t,this.dir)}}class a{constructor(t,e,n){this.value="",this.done=!1,this.cursor=new l(t,e>n?-1:1),this.pos=e>n?t.length:0,this.from=Math.min(e,n),this.to=Math.max(e,n)}nextInner(t,e){if(e<0?this.pos<=this.from:this.pos>=this.to)return this.value="",this.done=!0,this;t+=Math.max(0,e<0?this.pos-this.to:this.from-this.pos);let n=e<0?this.pos-this.from:this.to-this.pos;t>n&&(t=n),n-=t;let{value:i}=this.cursor.next(t);return this.pos+=(i.length+t)*e,this.value=i.length<=n?i:e<0?i.slice(i.length-n):i.slice(0,n),this.done=!this.value,this}next(t=0){return t<0?t=Math.max(t,this.from-this.pos):t>0&&(t=Math.min(t,this.to-this.pos)),this.nextInner(t,this.cursor.dir)}get lineBreak(){return this.cursor.lineBreak&&""!=this.value}}class c{constructor(t){this.inner=t,this.afterBreak=!0,this.value="",this.done=!1}next(t=0){let{done:e,lineBreak:n,value:i}=this.inner.next(t);return e?(this.done=!0,this.value=""):n?this.afterBreak?this.value="":(this.afterBreak=!0,this.next()):(this.value=i,this.afterBreak=!1),this}get lineBreak(){return!1}}"undefined"!=typeof Symbol&&(i.prototype[Symbol.iterator]=function(){return this.iter()},l.prototype[Symbol.iterator]=a.prototype[Symbol.iterator]=c.prototype[Symbol.iterator]=function(){return this});class f{constructor(t,e,n,i){this.from=t,this.to=e,this.number=n,this.text=i}get length(){return this.to-this.from}}let u="lc,34,7n,7,7b,19,,,,2,,2,,,20,b,1c,l,g,,2t,7,2,6,2,2,,4,z,,u,r,2j,b,1m,9,9,,o,4,,9,,3,,5,17,3,3b,f,,w,1j,,,,4,8,4,,3,7,a,2,t,,1m,,,,2,4,8,,9,,a,2,q,,2,2,1l,,4,2,4,2,2,3,3,,u,2,3,,b,2,1l,,4,5,,2,4,,k,2,m,6,,,1m,,,2,,4,8,,7,3,a,2,u,,1n,,,,c,,9,,14,,3,,1l,3,5,3,,4,7,2,b,2,t,,1m,,2,,2,,3,,5,2,7,2,b,2,s,2,1l,2,,,2,4,8,,9,,a,2,t,,20,,4,,2,3,,,8,,29,,2,7,c,8,2q,,2,9,b,6,22,2,r,,,,,,1j,e,,5,,2,5,b,,10,9,,2u,4,,6,,2,2,2,p,2,4,3,g,4,d,,2,2,6,,f,,jj,3,qa,3,t,3,t,2,u,2,1s,2,,7,8,,2,b,9,,19,3,3b,2,y,,3a,3,4,2,9,,6,3,63,2,2,,1m,,,7,,,,,2,8,6,a,2,,1c,h,1r,4,1c,7,,,5,,14,9,c,2,w,4,2,2,,3,1k,,,2,3,,,3,1m,8,2,2,48,3,,d,,7,4,,6,,3,2,5i,1m,,5,ek,,5f,x,2da,3,3x,,2o,w,fe,6,2x,2,n9w,4,,a,w,2,28,2,7k,,3,,4,,p,2,5,,47,2,q,i,d,,12,8,p,b,1a,3,1c,,2,4,2,2,13,,1v,6,2,2,2,2,c,,8,,1b,,1f,,,3,2,2,5,2,,,16,2,8,,6m,,2,,4,,fn4,,kh,g,g,g,a6,2,gt,,6a,,45,5,1ae,3,,2,5,4,14,3,4,,4l,2,fx,4,ar,2,49,b,4w,,1i,f,1k,3,1d,4,2,2,1x,3,10,5,,8,1q,,c,2,1g,9,a,4,2,,2n,3,2,,,2,6,,4g,,3,8,l,2,1l,2,,,,,m,,e,7,3,5,5f,8,2,3,,,n,,29,,2,6,,,2,,,2,,2,6j,,2,4,6,2,,2,r,2,2d,8,2,,,2,2y,,,,2,6,,,2t,3,2,4,,5,77,9,,2,6t,,a,2,,,4,,40,4,2,2,4,,w,a,14,6,2,4,8,,9,6,2,3,1a,d,,2,ba,7,,6,,,2a,m,2,7,,2,,2,3e,6,3,,,2,,7,,,20,2,3,,,,9n,2,f0b,5,1n,7,t4,,1r,4,29,,f5k,2,43q,,,3,4,5,8,8,2,7,u,4,44,3,1iz,1j,4,1e,8,,e,,m,5,,f,11s,7,,h,2,7,,2,,5,79,7,c5,4,15s,7,31,7,240,5,gx7k,2o,3k,6o".split(",").map((t=>t?parseInt(t,36):1));for(let t=1;t<u.length;t++)u[t]+=u[t-1];function g(t){for(let e=1;e<u.length;e+=2)if(u[e]>t)return u[e-1]<=t;return!1}function d(t){return t>=127462&&t<=127487}const p=8205;function m(t,e,n=!0,i=!0){return(n?v:x)(t,e,i)}function v(t,e,n){if(e==t.length)return e;e&&w(t.charCodeAt(e))&&k(t.charCodeAt(e-1))&&e--;let i=S(t,e);for(e+=I(i);e<t.length;){let s=S(t,e);if(i==p||s==p||n&&g(s))e+=I(s),i=s;else{if(!d(s))break;{let n=0,i=e-2;for(;i>=0&&d(S(t,i));)n++,i-=2;if(n%2==0)break;e+=2}}}return e}function x(t,e,n){for(;e>0;){let i=v(t,e-2,n);if(i<e)return i;e--}return 0}function w(t){return t>=56320&&t<57344}function k(t){return t>=55296&&t<56320}function S(t,e){let n=t.charCodeAt(e);if(!k(n)||e+1==t.length)return n;let i=t.charCodeAt(e+1);return w(i)?i-56320+(n-55296<<10)+65536:n}function y(t){return t<=65535?String.fromCharCode(t):(t-=65536,String.fromCharCode(55296+(t>>10),56320+(1023&t)))}function I(t){return t<65536?1:2}const b=/\r\n?|\n/;var P=function(t){return t[t.Simple=0]="Simple",t[t.TrackDel=1]="TrackDel",t[t.TrackBefore=2]="TrackBefore",t[t.TrackAfter=3]="TrackAfter",t}(P||(P={}));class E{constructor(t){this.sections=t}get length(){let t=0;for(let e=0;e<this.sections.length;e+=2)t+=this.sections[e];return t}get newLength(){let t=0;for(let e=0;e<this.sections.length;e+=2){let n=this.sections[e+1];t+=n<0?this.sections[e]:n}return t}get empty(){return 0==this.sections.length||2==this.sections.length&&this.sections[1]<0}iterGaps(t){for(let e=0,n=0,i=0;e<this.sections.length;){let s=this.sections[e++],r=this.sections[e++];r<0?(t(n,i,s),i+=s):i+=r,n+=s}}iterChangedRanges(t,e=!1){M(this,t,e)}get invertedDesc(){let t=[];for(let e=0;e<this.sections.length;){let n=this.sections[e++],i=this.sections[e++];i<0?t.push(n,i):t.push(i,n)}return new E(t)}composeDesc(t){return this.empty?t:t.empty?this:T(this,t)}mapDesc(t,e=!1){return t.empty?this:O(this,t,e)}mapPos(t,e=-1,n=P.Simple){let i=0,s=0;for(let r=0;r<this.sections.length;){let h=this.sections[r++],o=this.sections[r++],l=i+h;if(o<0){if(l>t)return s+(t-i);s+=h}else{if(n!=P.Simple&&l>=t&&(n==P.TrackDel&&i<t&&l>t||n==P.TrackBefore&&i<t||n==P.TrackAfter&&l>t))return null;if(l>t||l==t&&e<0&&!h)return t==i||e<0?s:s+o;s+=o}i=l}if(t>i)throw new RangeError(`Position ${t} is out of range for changeset of length ${i}`);return s}touchesRange(t,e=t){for(let n=0,i=0;n<this.sections.length&&i<=e;){let s=i+this.sections[n++];if(this.sections[n++]>=0&&i<=e&&s>=t)return!(i<t&&s>e)||"cover";i=s}return!1}toString(){let t="";for(let e=0;e<this.sections.length;){let n=this.sections[e++],i=this.sections[e++];t+=(t?" ":"")+n+(i>=0?":"+i:"")}return t}toJSON(){return this.sections}static fromJSON(t){if(!Array.isArray(t)||t.length%2||t.some((t=>"number"!=typeof t)))throw new RangeError("Invalid JSON representation of ChangeDesc");return new E(t)}static create(t){return new E(t)}}class A extends E{constructor(t,e){super(t),this.inserted=e}apply(t){if(this.length!=t.length)throw new RangeError("Applying change set to a document with the wrong length");return M(this,((e,n,i,s,r)=>t=t.replace(i,i+(n-e),r)),!1),t}mapDesc(t,e=!1){return O(this,t,e,!0)}invert(t){let e=this.sections.slice(),n=[];for(let s=0,r=0;s<e.length;s+=2){let h=e[s],o=e[s+1];if(o>=0){e[s]=o,e[s+1]=h;let l=s>>1;for(;n.length<l;)n.push(i.empty);n.push(h?t.slice(r,r+h):i.empty)}r+=h}return new A(e,n)}compose(t){return this.empty?t:t.empty?this:T(this,t,!0)}map(t,e=!1){return t.empty?this:O(this,t,e,!0)}iterChanges(t,e=!1){M(this,t,e)}get desc(){return E.create(this.sections)}filter(t){let e=[],n=[],i=[],s=new F(this);t:for(let r=0,h=0;;){let o=r==t.length?1e9:t[r++];for(;h<o||h==o&&0==s.len;){if(s.done)break t;let t=Math.min(s.len,o-h);R(i,t,-1);let r=-1==s.ins?-1:0==s.off?s.ins:0;R(e,t,r),r>0&&C(n,e,s.text),s.forward(t),h+=t}let l=t[r++];for(;h<l;){if(s.done)break t;let t=Math.min(s.len,l-h);R(e,t,-1),R(i,t,-1==s.ins?-1:0==s.off?s.ins:0),s.forward(t),h+=t}}return{changes:new A(e,n),filtered:E.create(i)}}toJSON(){let t=[];for(let e=0;e<this.sections.length;e+=2){let n=this.sections[e],i=this.sections[e+1];i<0?t.push(n):0==i?t.push([n]):t.push([n].concat(this.inserted[e>>1].toJSON()))}return t}static of(t,e,n){let s=[],r=[],h=0,o=null;function l(t=!1){if(!t&&!s.length)return;h<e&&R(s,e-h,-1);let n=new A(s,r);o=o?o.compose(n.map(o)):n,s=[],r=[],h=0}return function t(a){if(Array.isArray(a))for(let e of a)t(e);else if(a instanceof A){if(a.length!=e)throw new RangeError(`Mismatched change set length (got ${a.length}, expected ${e})`);l(),o=o?o.compose(a.map(o)):a}else{let{from:t,to:o=t,insert:c}=a;if(t>o||t<0||o>e)throw new RangeError(`Invalid change range ${t} to ${o} (in doc of length ${e})`);let f=c?"string"==typeof c?i.of(c.split(n||b)):c:i.empty,u=f.length;if(t==o&&0==u)return;t<h&&l(),t>h&&R(s,t-h,-1),R(s,o-t,u),C(r,s,f),h=o}}(t),l(!o),o}static empty(t){return new A(t?[t,-1]:[],[])}static fromJSON(t){if(!Array.isArray(t))throw new RangeError("Invalid JSON representation of ChangeSet");let e=[],n=[];for(let s=0;s<t.length;s++){let r=t[s];if("number"==typeof r)e.push(r,-1);else{if(!Array.isArray(r)||"number"!=typeof r[0]||r.some(((t,e)=>e&&"string"!=typeof t)))throw new RangeError("Invalid JSON representation of ChangeSet");if(1==r.length)e.push(r[0],0);else{for(;n.length<s;)n.push(i.empty);n[s]=i.of(r.slice(1)),e.push(r[0],n[s].length)}}}return new A(e,n)}static createSet(t,e){return new A(t,e)}}function R(t,e,n,i=!1){if(0==e&&n<=0)return;let s=t.length-2;s>=0&&n<=0&&n==t[s+1]?t[s]+=e:0==e&&0==t[s]?t[s+1]+=n:i?(t[s]+=e,t[s+1]+=n):t.push(e,n)}function C(t,e,n){if(0==n.length)return;let s=e.length-2>>1;if(s<t.length)t[t.length-1]=t[t.length-1].append(n);else{for(;t.length<s;)t.push(i.empty);t.push(n)}}function M(t,e,n){let s=t.inserted;for(let r=0,h=0,o=0;o<t.sections.length;){let l=t.sections[o++],a=t.sections[o++];if(a<0)r+=l,h+=l;else{let c=r,f=h,u=i.empty;for(;c+=l,f+=a,a&&s&&(u=u.append(s[o-2>>1])),!(n||o==t.sections.length||t.sections[o+1]<0);)l=t.sections[o++],a=t.sections[o++];e(r,c,h,f,u),r=c,h=f}}}function O(t,e,n,i=!1){let s=[],r=i?[]:null,h=new F(t),o=new F(e);for(let t=-1;;)if(-1==h.ins&&-1==o.ins){let t=Math.min(h.len,o.len);R(s,t,-1),h.forward(t),o.forward(t)}else if(o.ins>=0&&(h.ins<0||t==h.i||0==h.off&&(o.len<h.len||o.len==h.len&&!n))){let e=o.len;for(R(s,o.ins,-1);e;){let n=Math.min(h.len,e);h.ins>=0&&t<h.i&&h.len<=n&&(R(s,0,h.ins),r&&C(r,s,h.text),t=h.i),h.forward(n),e-=n}o.next()}else{if(!(h.ins>=0)){if(h.done&&o.done)return r?A.createSet(s,r):E.create(s);throw new Error("Mismatched change set lengths")}{let e=0,n=h.len;for(;n;)if(-1==o.ins){let t=Math.min(n,o.len);e+=t,n-=t,o.forward(t)}else{if(!(0==o.ins&&o.len<n))break;n-=o.len,o.next()}R(s,e,t<h.i?h.ins:0),r&&t<h.i&&C(r,s,h.text),t=h.i,h.forward(h.len-n)}}}function T(t,e,n=!1){let i=[],s=n?[]:null,r=new F(t),h=new F(e);for(let t=!1;;){if(r.done&&h.done)return s?A.createSet(i,s):E.create(i);if(0==r.ins)R(i,r.len,0,t),r.next();else if(0!=h.len||h.done){if(r.done||h.done)throw new Error("Mismatched change set lengths");{let e=Math.min(r.len2,h.len),n=i.length;if(-1==r.ins){let n=-1==h.ins?-1:h.off?0:h.ins;R(i,e,n,t),s&&n&&C(s,i,h.text)}else-1==h.ins?(R(i,r.off?0:r.len,e,t),s&&C(s,i,r.textBit(e))):(R(i,r.off?0:r.len,h.off?0:h.ins,t),s&&!h.off&&C(s,i,h.text));t=(r.ins>e||h.ins>=0&&h.len>e)&&(t||i.length>n),r.forward2(e),h.forward(e)}}else R(i,0,h.ins,t),s&&C(s,i,h.text),h.next()}}class F{constructor(t){this.set=t,this.i=0,this.next()}next(){let{sections:t}=this.set;this.i<t.length?(this.len=t[this.i++],this.ins=t[this.i++]):(this.len=0,this.ins=-2),this.off=0}get done(){return-2==this.ins}get len2(){return this.ins<0?this.len:this.ins}get text(){let{inserted:t}=this.set,e=this.i-2>>1;return e>=t.length?i.empty:t[e]}textBit(t){let{inserted:e}=this.set,n=this.i-2>>1;return n>=e.length&&!t?i.empty:e[n].slice(this.off,null==t?void 0:this.off+t)}forward(t){t==this.len?this.next():(this.len-=t,this.off+=t)}forward2(t){-1==this.ins?this.forward(t):t==this.ins?this.next():(this.ins-=t,this.off+=t)}}class B{constructor(t,e,n){this.from=t,this.to=e,this.flags=n}get anchor(){return 16&this.flags?this.to:this.from}get head(){return 16&this.flags?this.from:this.to}get empty(){return this.from==this.to}get assoc(){return 4&this.flags?-1:8&this.flags?1:0}get bidiLevel(){let t=3&this.flags;return 3==t?null:t}get goalColumn(){let t=this.flags>>5;return 33554431==t?void 0:t}map(t,e=-1){let n,i;return this.empty?n=i=t.mapPos(this.from,e):(n=t.mapPos(this.from,1),i=t.mapPos(this.to,-1)),n==this.from&&i==this.to?this:new B(n,i,this.flags)}extend(t,e=t){if(t<=this.anchor&&e>=this.anchor)return J.range(t,e);let n=Math.abs(t-this.anchor)>Math.abs(e-this.anchor)?t:e;return J.range(this.anchor,n)}eq(t){return this.anchor==t.anchor&&this.head==t.head}toJSON(){return{anchor:this.anchor,head:this.head}}static fromJSON(t){if(!t||"number"!=typeof t.anchor||"number"!=typeof t.head)throw new RangeError("Invalid JSON representation for SelectionRange");return J.range(t.anchor,t.head)}static create(t,e,n){return new B(t,e,n)}}class J{constructor(t,e){this.ranges=t,this.mainIndex=e}map(t,e=-1){return t.empty?this:J.create(this.ranges.map((n=>n.map(t,e))),this.mainIndex)}eq(t){if(this.ranges.length!=t.ranges.length||this.mainIndex!=t.mainIndex)return!1;for(let e=0;e<this.ranges.length;e++)if(!this.ranges[e].eq(t.ranges[e]))return!1;return!0}get main(){return this.ranges[this.mainIndex]}asSingle(){return 1==this.ranges.length?this:new J([this.main],0)}addRange(t,e=!0){return J.create([t].concat(this.ranges),e?0:this.mainIndex+1)}replaceRange(t,e=this.mainIndex){let n=this.ranges.slice();return n[e]=t,J.create(n,this.mainIndex)}toJSON(){return{ranges:this.ranges.map((t=>t.toJSON())),main:this.mainIndex}}static fromJSON(t){if(!t||!Array.isArray(t.ranges)||"number"!=typeof t.main||t.main>=t.ranges.length)throw new RangeError("Invalid JSON representation for EditorSelection");return new J(t.ranges.map((t=>B.fromJSON(t))),t.main)}static single(t,e=t){return new J([J.range(t,e)],0)}static create(t,e=0){if(0==t.length)throw new RangeError("A selection needs at least one range");for(let n=0,i=0;i<t.length;i++){let s=t[i];if(s.empty?s.from<=n:s.from<n)return J.normalized(t.slice(),e);n=s.to}return new J(t,e)}static cursor(t,e=0,n,i){return B.create(t,t,(0==e?0:e<0?4:8)|(null==n?3:Math.min(2,n))|(null!=i?i:33554431)<<5)}static range(t,e,n,i){let s=(null!=n?n:33554431)<<5|(null==i?3:Math.min(2,i));return e<t?B.create(e,t,24|s):B.create(t,e,(e>t?4:0)|s)}static normalized(t,e=0){let n=t[e];t.sort(((t,e)=>t.from-e.from)),e=t.indexOf(n);for(let n=1;n<t.length;n++){let i=t[n],s=t[n-1];if(i.empty?i.from<=s.to:i.from<s.to){let r=s.from,h=Math.max(i.to,s.to);n<=e&&e--,t.splice(--n,2,i.anchor>i.head?J.range(h,r):J.range(r,h))}}return new J(t,e)}}function N(t,e){for(let n of t.ranges)if(n.to>e)throw new RangeError("Selection points outside of document")}let L=0;class D{constructor(t,e,n,i,s){this.combine=t,this.compareInput=e,this.compare=n,this.isStatic=i,this.id=L++,this.default=t([]),this.extensions="function"==typeof s?s(this):s}static define(t={}){return new D(t.combine||(t=>t),t.compareInput||((t,e)=>t===e),t.compare||(t.combine?(t,e)=>t===e:j),!!t.static,t.enables)}of(t){return new q([],this,0,t)}compute(t,e){if(this.isStatic)throw new Error("Can't compute a static facet");return new q(t,this,1,e)}computeN(t,e){if(this.isStatic)throw new Error("Can't compute a static facet");return new q(t,this,2,e)}from(t,e){return e||(e=t=>t),this.compute([t],(n=>e(n.field(t))))}}function j(t,e){return t==e||t.length==e.length&&t.every(((t,n)=>t===e[n]))}class q{constructor(t,e,n,i){this.dependencies=t,this.facet=e,this.type=n,this.value=i,this.id=L++}dynamicSlot(t){var e;let n=this.value,i=this.facet.compareInput,s=this.id,r=t[s]>>1,h=2==this.type,o=!1,l=!1,a=[];for(let n of this.dependencies)"doc"==n?o=!0:"selection"==n?l=!0:0==(1&(null!==(e=t[n.id])&&void 0!==e?e:1))&&a.push(t[n.id]);return{create:t=>(t.values[r]=n(t),1),update(t,e){if(o&&e.docChanged||l&&(e.docChanged||e.selection)||_(t,a)){let e=n(t);if(h?!$(e,t.values[r],i):!i(e,t.values[r]))return t.values[r]=e,1}return 0},reconfigure:(t,e)=>{let o,l=e.config.address[s];if(null!=l){let s=Z(e,l);if(this.dependencies.every((n=>n instanceof D?e.facet(n)===t.facet(n):!(n instanceof U)||e.field(n,!1)==t.field(n,!1)))||(h?$(o=n(t),s,i):i(o=n(t),s)))return t.values[r]=s,0}else o=n(t);return t.values[r]=o,1}}}}function $(t,e,n){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++)if(!n(t[i],e[i]))return!1;return!0}function _(t,e){let n=!1;for(let i of e)1&X(t,i)&&(n=!0);return n}function z(t,e,n){let i=n.map((e=>t[e.id])),s=n.map((t=>t.type)),r=i.filter((t=>!(1&t))),h=t[e.id]>>1;function o(t){let n=[];for(let e=0;e<i.length;e++){let r=Z(t,i[e]);if(2==s[e])for(let t of r)n.push(t);else n.push(r)}return e.combine(n)}return{create(t){for(let e of i)X(t,e);return t.values[h]=o(t),1},update(t,n){if(!_(t,r))return 0;let i=o(t);return e.compare(i,t.values[h])?0:(t.values[h]=i,1)},reconfigure(t,s){let r=_(t,i),l=s.config.facets[e.id],a=s.facet(e);if(l&&!r&&j(n,l))return t.values[h]=a,0;let c=o(t);return e.compare(c,a)?(t.values[h]=a,0):(t.values[h]=c,1)}}}const V=D.define({static:!0});class U{constructor(t,e,n,i,s){this.id=t,this.createF=e,this.updateF=n,this.compareF=i,this.spec=s,this.provides=void 0}static define(t){let e=new U(L++,t.create,t.update,t.compare||((t,e)=>t===e),t);return t.provide&&(e.provides=t.provide(e)),e}create(t){let e=t.facet(V).find((t=>t.field==this));return((null==e?void 0:e.create)||this.createF)(t)}slot(t){let e=t[this.id]>>1;return{create:t=>(t.values[e]=this.create(t),1),update:(t,n)=>{let i=t.values[e],s=this.updateF(i,n);return this.compareF(i,s)?0:(t.values[e]=s,1)},reconfigure:(t,n)=>null!=n.config.address[this.id]?(t.values[e]=n.field(this),0):(t.values[e]=this.create(t),1)}}init(t){return[this,V.of({field:this,create:t})]}get extension(){return this}}function W(t){return e=>new Y(e,t)}const G={highest:W(0),high:W(1),default:W(2),low:W(3),lowest:W(4)};class Y{constructor(t,e){this.inner=t,this.prec=e}}class H{of(t){return new K(this,t)}reconfigure(t){return H.reconfigure.of({compartment:this,extension:t})}get(t){return t.config.compartments.get(this)}}class K{constructor(t,e){this.compartment=t,this.inner=e}}class Q{constructor(t,e,n,i,s,r){for(this.base=t,this.compartments=e,this.dynamicSlots=n,this.address=i,this.staticValues=s,this.facets=r,this.statusTemplate=[];this.statusTemplate.length<n.length;)this.statusTemplate.push(0)}staticFacet(t){let e=this.address[t.id];return null==e?t.default:this.staticValues[e>>1]}static resolve(t,e,n){let i=[],s=Object.create(null),r=new Map;for(let n of function(t,e,n){let i=[[],[],[],[],[]],s=new Map;return function t(r,h){let o=s.get(r);if(null!=o){if(o<=h)return;let t=i[o].indexOf(r);t>-1&&i[o].splice(t,1),r instanceof K&&n.delete(r.compartment)}if(s.set(r,h),Array.isArray(r))for(let e of r)t(e,h);else if(r instanceof K){if(n.has(r.compartment))throw new RangeError("Duplicate use of compartment in extensions");let i=e.get(r.compartment)||r.inner;n.set(r.compartment,i),t(i,h)}else if(r instanceof Y)t(r.inner,r.prec);else if(r instanceof U)i[h].push(r),r.provides&&t(r.provides,h);else if(r instanceof q)i[h].push(r),r.facet.extensions&&t(r.facet.extensions,2);else{let e=r.extension;if(!e)throw new Error(`Unrecognized extension value in extension set (${r}). This sometimes happens because multiple instances of @codemirror/state are loaded, breaking instanceof checks.`);t(e,h)}}(t,2),i.reduce(((t,e)=>t.concat(e)))}(t,e,r))n instanceof U?i.push(n):(s[n.facet.id]||(s[n.facet.id]=[])).push(n);let h=Object.create(null),o=[],l=[];for(let t of i)h[t.id]=l.length<<1,l.push((e=>t.slot(e)));let a=null==n?void 0:n.config.facets;for(let t in s){let e=s[t],i=e[0].facet,r=a&&a[t]||[];if(e.every((t=>0==t.type)))if(h[i.id]=o.length<<1|1,j(r,e))o.push(n.facet(i));else{let t=i.combine(e.map((t=>t.value)));o.push(n&&i.compare(t,n.facet(i))?n.facet(i):t)}else{for(let t of e)0==t.type?(h[t.id]=o.length<<1|1,o.push(t.value)):(h[t.id]=l.length<<1,l.push((e=>t.dynamicSlot(e))));h[i.id]=l.length<<1,l.push((t=>z(t,i,e)))}}let c=l.map((t=>t(h)));return new Q(t,r,c,h,o,s)}}function X(t,e){if(1&e)return 2;let n=e>>1,i=t.status[n];if(4==i)throw new Error("Cyclic dependency between fields and/or facets");if(2&i)return i;t.status[n]=4;let s=t.computeSlot(t,t.config.dynamicSlots[n]);return t.status[n]=2|s}function Z(t,e){return 1&e?t.config.staticValues[e>>1]:t.values[e>>1]}const tt=D.define(),et=D.define({combine:t=>t.some((t=>t)),static:!0}),nt=D.define({combine:t=>t.length?t[0]:void 0,static:!0}),it=D.define(),st=D.define(),rt=D.define(),ht=D.define({combine:t=>!!t.length&&t[0]});class ot{constructor(t,e){this.type=t,this.value=e}static define(){return new lt}}class lt{of(t){return new ot(this,t)}}class at{constructor(t){this.map=t}of(t){return new ct(this,t)}}class ct{constructor(t,e){this.type=t,this.value=e}map(t){let e=this.type.map(this.value,t);return void 0===e?void 0:e==this.value?this:new ct(this.type,e)}is(t){return this.type==t}static define(t={}){return new at(t.map||(t=>t))}static mapEffects(t,e){if(!t.length)return t;let n=[];for(let i of t){let t=i.map(e);t&&n.push(t)}return n}}ct.reconfigure=ct.define(),ct.appendConfig=ct.define();class ft{constructor(t,e,n,i,s,r){this.startState=t,this.changes=e,this.selection=n,this.effects=i,this.annotations=s,this.scrollIntoView=r,this._doc=null,this._state=null,n&&N(n,e.newLength),s.some((t=>t.type==ft.time))||(this.annotations=s.concat(ft.time.of(Date.now())))}static create(t,e,n,i,s,r){return new ft(t,e,n,i,s,r)}get newDoc(){return this._doc||(this._doc=this.changes.apply(this.startState.doc))}get newSelection(){return this.selection||this.startState.selection.map(this.changes)}get state(){return this._state||this.startState.applyTransaction(this),this._state}annotation(t){for(let e of this.annotations)if(e.type==t)return e.value}get docChanged(){return!this.changes.empty}get reconfigured(){return this.startState.config!=this.state.config}isUserEvent(t){let e=this.annotation(ft.userEvent);return!(!e||!(e==t||e.length>t.length&&e.slice(0,t.length)==t&&"."==e[t.length]))}}function ut(t,e){let n=[];for(let i=0,s=0;;){let r,h;if(i<t.length&&(s==e.length||e[s]>=t[i]))r=t[i++],h=t[i++];else{if(!(s<e.length))return n;r=e[s++],h=e[s++]}!n.length||n[n.length-1]<r?n.push(r,h):n[n.length-1]<h&&(n[n.length-1]=h)}}function gt(t,e,n){var i;let s,r,h;return n?(s=e.changes,r=A.empty(e.changes.length),h=t.changes.compose(e.changes)):(s=e.changes.map(t.changes),r=t.changes.mapDesc(e.changes,!0),h=t.changes.compose(s)),{changes:h,selection:e.selection?e.selection.map(r):null===(i=t.selection)||void 0===i?void 0:i.map(s),effects:ct.mapEffects(t.effects,s).concat(ct.mapEffects(e.effects,r)),annotations:t.annotations.length?t.annotations.concat(e.annotations):e.annotations,scrollIntoView:t.scrollIntoView||e.scrollIntoView}}function dt(t,e,n){let i=e.selection,s=vt(e.annotations);return e.userEvent&&(s=s.concat(ft.userEvent.of(e.userEvent))),{changes:e.changes instanceof A?e.changes:A.of(e.changes||[],n,t.facet(nt)),selection:i&&(i instanceof J?i:J.single(i.anchor,i.head)),effects:vt(e.effects),annotations:s,scrollIntoView:!!e.scrollIntoView}}function pt(t,e,n){let i=dt(t,e.length?e[0]:{},t.doc.length);e.length&&!1===e[0].filter&&(n=!1);for(let s=1;s<e.length;s++){!1===e[s].filter&&(n=!1);let r=!!e[s].sequential;i=gt(i,dt(t,e[s],r?i.changes.newLength:t.doc.length),r)}let s=ft.create(t,i.changes,i.selection,i.effects,i.annotations,i.scrollIntoView);return function(t){let e=t.startState,n=e.facet(rt),i=t;for(let s=n.length-1;s>=0;s--){let r=n[s](t);r&&Object.keys(r).length&&(i=gt(i,dt(e,r,t.changes.newLength),!0))}return i==t?t:ft.create(e,t.changes,t.selection,i.effects,i.annotations,i.scrollIntoView)}(n?function(t){let e=t.startState,n=!0;for(let i of e.facet(it)){let e=i(t);if(!1===e){n=!1;break}Array.isArray(e)&&(n=!0===n?e:ut(n,e))}if(!0!==n){let i,s;if(!1===n)s=t.changes.invertedDesc,i=A.empty(e.doc.length);else{let e=t.changes.filter(n);i=e.changes,s=e.filtered.mapDesc(e.changes).invertedDesc}t=ft.create(e,i,t.selection&&t.selection.map(s),ct.mapEffects(t.effects,s),t.annotations,t.scrollIntoView)}let i=e.facet(st);for(let n=i.length-1;n>=0;n--){let s=i[n](t);t=s instanceof ft?s:Array.isArray(s)&&1==s.length&&s[0]instanceof ft?s[0]:pt(e,vt(s),!1)}return t}(s):s)}ft.time=ot.define(),ft.userEvent=ot.define(),ft.addToHistory=ot.define(),ft.remote=ot.define();const mt=[];function vt(t){return null==t?mt:Array.isArray(t)?t:[t]}var xt=function(t){return t[t.Word=0]="Word",t[t.Space=1]="Space",t[t.Other=2]="Other",t}(xt||(xt={}));const wt=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;let kt;try{kt=new RegExp("[\\p{Alphabetic}\\p{Number}_]","u")}catch(t){}class St{constructor(t,e,n,i,s,r){this.config=t,this.doc=e,this.selection=n,this.values=i,this.status=t.statusTemplate.slice(),this.computeSlot=s,r&&(r._state=this);for(let t=0;t<this.config.dynamicSlots.length;t++)X(this,t<<1);this.computeSlot=null}field(t,e=!0){let n=this.config.address[t.id];if(null!=n)return X(this,n),Z(this,n);if(e)throw new RangeError("Field is not present in this state")}update(...t){return pt(this,t,!0)}applyTransaction(t){let e,n=this.config,{base:i,compartments:s}=n;for(let e of t.effects)e.is(H.reconfigure)?(n&&(s=new Map,n.compartments.forEach(((t,e)=>s.set(e,t))),n=null),s.set(e.value.compartment,e.value.extension)):e.is(ct.reconfigure)?(n=null,i=e.value):e.is(ct.appendConfig)&&(n=null,i=vt(i).concat(e.value));n?e=t.startState.values.slice():(n=Q.resolve(i,s,this),e=new St(n,this.doc,this.selection,n.dynamicSlots.map((()=>null)),((t,e)=>e.reconfigure(t,this)),null).values),new St(n,t.newDoc,t.newSelection,e,((e,n)=>n.update(e,t)),t)}replaceSelection(t){return"string"==typeof t&&(t=this.toText(t)),this.changeByRange((e=>({changes:{from:e.from,to:e.to,insert:t},range:J.cursor(e.from+t.length)})))}changeByRange(t){let e=this.selection,n=t(e.ranges[0]),i=this.changes(n.changes),s=[n.range],r=vt(n.effects);for(let n=1;n<e.ranges.length;n++){let h=t(e.ranges[n]),o=this.changes(h.changes),l=o.map(i);for(let t=0;t<n;t++)s[t]=s[t].map(l);let a=i.mapDesc(o,!0);s.push(h.range.map(a)),i=i.compose(l),r=ct.mapEffects(r,l).concat(ct.mapEffects(vt(h.effects),a))}return{changes:i,selection:J.create(s,e.mainIndex),effects:r}}changes(t=[]){return t instanceof A?t:A.of(t,this.doc.length,this.facet(St.lineSeparator))}toText(t){return i.of(t.split(this.facet(St.lineSeparator)||b))}sliceDoc(t=0,e=this.doc.length){return this.doc.sliceString(t,e,this.lineBreak)}facet(t){let e=this.config.address[t.id];return null==e?t.default:(X(this,e),Z(this,e))}toJSON(t){let e={doc:this.sliceDoc(),selection:this.selection.toJSON()};if(t)for(let n in t){let i=t[n];i instanceof U&&null!=this.config.address[i.id]&&(e[n]=i.spec.toJSON(this.field(t[n]),this))}return e}static fromJSON(t,e={},n){if(!t||"string"!=typeof t.doc)throw new RangeError("Invalid JSON representation for EditorState");let i=[];if(n)for(let e in n)if(Object.prototype.hasOwnProperty.call(t,e)){let s=n[e],r=t[e];i.push(s.init((t=>s.spec.fromJSON(r,t))))}return St.create({doc:t.doc,selection:J.fromJSON(t.selection),extensions:e.extensions?i.concat([e.extensions]):i})}static create(t={}){let e=Q.resolve(t.extensions||[],new Map),n=t.doc instanceof i?t.doc:i.of((t.doc||"").split(e.staticFacet(St.lineSeparator)||b)),s=t.selection?t.selection instanceof J?t.selection:J.single(t.selection.anchor,t.selection.head):J.single(0);return N(s,n.length),e.staticFacet(et)||(s=s.asSingle()),new St(e,n,s,e.dynamicSlots.map((()=>null)),((t,e)=>e.create(t)),null)}get tabSize(){return this.facet(St.tabSize)}get lineBreak(){return this.facet(St.lineSeparator)||"\n"}get readOnly(){return this.facet(ht)}phrase(t,...e){for(let e of this.facet(St.phrases))if(Object.prototype.hasOwnProperty.call(e,t)){t=e[t];break}return e.length&&(t=t.replace(/\$(\$|\d*)/g,((t,n)=>{if("$"==n)return"$";let i=+(n||1);return!i||i>e.length?t:e[i-1]}))),t}languageDataAt(t,e,n=-1){let i=[];for(let s of this.facet(tt))for(let r of s(this,e,n))Object.prototype.hasOwnProperty.call(r,t)&&i.push(r[t]);return i}charCategorizer(t){return e=this.languageDataAt("wordChars",t).join(""),t=>{if(!/\S/.test(t))return xt.Space;if(function(t){if(kt)return kt.test(t);for(let e=0;e<t.length;e++){let n=t[e];if(/\w/.test(n)||n>""&&(n.toUpperCase()!=n.toLowerCase()||wt.test(n)))return!0}return!1}(t))return xt.Word;for(let n=0;n<e.length;n++)if(t.indexOf(e[n])>-1)return xt.Word;return xt.Other};var e}wordAt(t){let{text:e,from:n,length:i}=this.doc.lineAt(t),s=this.charCategorizer(t),r=t-n,h=t-n;for(;r>0;){let t=m(e,r,!1);if(s(e.slice(t,r))!=xt.Word)break;r=t}for(;h<i;){let t=m(e,h);if(s(e.slice(h,t))!=xt.Word)break;h=t}return r==h?null:J.range(r+n,h+n)}}function yt(t,e,n={}){let i={};for(let e of t)for(let t of Object.keys(e)){let s=e[t],r=i[t];if(void 0===r)i[t]=s;else if(r===s||void 0===s);else{if(!Object.hasOwnProperty.call(n,t))throw new Error("Config merge conflict for field "+t);i[t]=n[t](r,s)}}for(let t in e)void 0===i[t]&&(i[t]=e[t]);return i}St.allowMultipleSelections=et,St.tabSize=D.define({combine:t=>t.length?t[0]:4}),St.lineSeparator=nt,St.readOnly=ht,St.phrases=D.define({compare(t,e){let n=Object.keys(t),i=Object.keys(e);return n.length==i.length&&n.every((n=>t[n]==e[n]))}}),St.languageData=tt,St.changeFilter=it,St.transactionFilter=st,St.transactionExtender=rt,H.reconfigure=ct.define();class It{eq(t){return this==t}range(t,e=t){return bt.create(t,e,this)}}It.prototype.startSide=It.prototype.endSide=0,It.prototype.point=!1,It.prototype.mapMode=P.TrackDel;class bt{constructor(t,e,n){this.from=t,this.to=e,this.value=n}static create(t,e,n){return new bt(t,e,n)}}function Pt(t,e){return t.from-e.from||t.value.startSide-e.value.startSide}class Et{constructor(t,e,n,i){this.from=t,this.to=e,this.value=n,this.maxPoint=i}get length(){return this.to[this.to.length-1]}findIndex(t,e,n,i=0){let s=n?this.to:this.from;for(let r=i,h=s.length;;){if(r==h)return r;let i=r+h>>1,o=s[i]-t||(n?this.value[i].endSide:this.value[i].startSide)-e;if(i==r)return o>=0?r:h;o>=0?h=i:r=i+1}}between(t,e,n,i){for(let s=this.findIndex(e,-1e9,!0),r=this.findIndex(n,1e9,!1,s);s<r;s++)if(!1===i(this.from[s]+t,this.to[s]+t,this.value[s]))return!1}map(t,e){let n=[],i=[],s=[],r=-1,h=-1;for(let o=0;o<this.value.length;o++){let l,a,c=this.value[o],f=this.from[o]+t,u=this.to[o]+t;if(f==u){let t=e.mapPos(f,c.startSide,c.mapMode);if(null==t)continue;if(l=a=t,c.startSide!=c.endSide&&(a=e.mapPos(f,c.endSide),a<l))continue}else if(l=e.mapPos(f,c.startSide),a=e.mapPos(u,c.endSide),l>a||l==a&&c.startSide>0&&c.endSide<=0)continue;(a-l||c.endSide-c.startSide)<0||(r<0&&(r=l),c.point&&(h=Math.max(h,a-l)),n.push(c),i.push(l-r),s.push(a-r))}return{mapped:n.length?new Et(i,s,n,h):null,pos:r}}}class At{constructor(t,e,n,i){this.chunkPos=t,this.chunk=e,this.nextLayer=n,this.maxPoint=i}static create(t,e,n,i){return new At(t,e,n,i)}get length(){let t=this.chunk.length-1;return t<0?0:Math.max(this.chunkEnd(t),this.nextLayer.length)}get size(){if(this.isEmpty)return 0;let t=this.nextLayer.size;for(let e of this.chunk)t+=e.value.length;return t}chunkEnd(t){return this.chunkPos[t]+this.chunk[t].length}update(t){let{add:e=[],sort:n=!1,filterFrom:i=0,filterTo:s=this.length}=t,r=t.filter;if(0==e.length&&!r)return this;if(n&&(e=e.slice().sort(Pt)),this.isEmpty)return e.length?At.of(e):this;let h=new Mt(this,null,-1).goto(0),o=0,l=[],a=new Rt;for(;h.value||o<e.length;)if(o<e.length&&(h.from-e[o].from||h.startSide-e[o].value.startSide)>=0){let t=e[o++];a.addInner(t.from,t.to,t.value)||l.push(t)}else 1==h.rangeIndex&&h.chunkIndex<this.chunk.length&&(o==e.length||this.chunkEnd(h.chunkIndex)<e[o].from)&&(!r||i>this.chunkEnd(h.chunkIndex)||s<this.chunkPos[h.chunkIndex])&&a.addChunk(this.chunkPos[h.chunkIndex],this.chunk[h.chunkIndex])?h.nextChunk():((!r||i>h.to||s<h.from||r(h.from,h.to,h.value))&&(a.addInner(h.from,h.to,h.value)||l.push(bt.create(h.from,h.to,h.value))),h.next());return a.finishInner(this.nextLayer.isEmpty&&!l.length?At.empty:this.nextLayer.update({add:l,filter:r,filterFrom:i,filterTo:s}))}map(t){if(t.empty||this.isEmpty)return this;let e=[],n=[],i=-1;for(let s=0;s<this.chunk.length;s++){let r=this.chunkPos[s],h=this.chunk[s],o=t.touchesRange(r,r+h.length);if(!1===o)i=Math.max(i,h.maxPoint),e.push(h),n.push(t.mapPos(r));else if(!0===o){let{mapped:s,pos:o}=h.map(r,t);s&&(i=Math.max(i,s.maxPoint),e.push(s),n.push(o))}}let s=this.nextLayer.map(t);return 0==e.length?s:new At(n,e,s||At.empty,i)}between(t,e,n){if(!this.isEmpty){for(let i=0;i<this.chunk.length;i++){let s=this.chunkPos[i],r=this.chunk[i];if(e>=s&&t<=s+r.length&&!1===r.between(s,t-s,e-s,n))return}this.nextLayer.between(t,e,n)}}iter(t=0){return Ot.from([this]).goto(t)}get isEmpty(){return this.nextLayer==this}static iter(t,e=0){return Ot.from(t).goto(e)}static compare(t,e,n,i,s=-1){let r=t.filter((t=>t.maxPoint>0||!t.isEmpty&&t.maxPoint>=s)),h=e.filter((t=>t.maxPoint>0||!t.isEmpty&&t.maxPoint>=s)),o=Ct(r,h,n),l=new Ft(r,o,s),a=new Ft(h,o,s);n.iterGaps(((t,e,n)=>Bt(l,t,a,e,n,i))),n.empty&&0==n.length&&Bt(l,0,a,0,0,i)}static eq(t,e,n=0,i){null==i&&(i=999999999);let s=t.filter((t=>!t.isEmpty&&e.indexOf(t)<0)),r=e.filter((e=>!e.isEmpty&&t.indexOf(e)<0));if(s.length!=r.length)return!1;if(!s.length)return!0;let h=Ct(s,r),o=new Ft(s,h,0).goto(n),l=new Ft(r,h,0).goto(n);for(;;){if(o.to!=l.to||!Jt(o.active,l.active)||o.point&&(!l.point||!o.point.eq(l.point)))return!1;if(o.to>i)return!0;o.next(),l.next()}}static spans(t,e,n,i,s=-1){let r=new Ft(t,null,s).goto(e),h=e,o=r.openStart;for(;;){let t=Math.min(r.to,n);if(r.point){let n=r.activeForPoint(r.to),s=r.pointFrom<e?n.length+1:Math.min(n.length,o);i.point(h,t,r.point,n,s,r.pointRank),o=Math.min(r.openEnd(t),n.length)}else t>h&&(i.span(h,t,r.active,o),o=r.openEnd(t));if(r.to>n)return o+(r.point&&r.to>n?1:0);h=r.to,r.next()}}static of(t,e=!1){let n=new Rt;for(let i of t instanceof bt?[t]:e?function(t){if(t.length>1)for(let e=t[0],n=1;n<t.length;n++){let i=t[n];if(Pt(e,i)>0)return t.slice().sort(Pt);e=i}return t}(t):t)n.add(i.from,i.to,i.value);return n.finish()}}At.empty=new At([],[],null,-1),At.empty.nextLayer=At.empty;class Rt{finishChunk(t){this.chunks.push(new Et(this.from,this.to,this.value,this.maxPoint)),this.chunkPos.push(this.chunkStart),this.chunkStart=-1,this.setMaxPoint=Math.max(this.setMaxPoint,this.maxPoint),this.maxPoint=-1,t&&(this.from=[],this.to=[],this.value=[])}constructor(){this.chunks=[],this.chunkPos=[],this.chunkStart=-1,this.last=null,this.lastFrom=-1e9,this.lastTo=-1e9,this.from=[],this.to=[],this.value=[],this.maxPoint=-1,this.setMaxPoint=-1,this.nextLayer=null}add(t,e,n){this.addInner(t,e,n)||(this.nextLayer||(this.nextLayer=new Rt)).add(t,e,n)}addInner(t,e,n){let i=t-this.lastTo||n.startSide-this.last.endSide;if(i<=0&&(t-this.lastFrom||n.startSide-this.last.startSide)<0)throw new Error("Ranges must be added sorted by `from` position and `startSide`");return!(i<0||(250==this.from.length&&this.finishChunk(!0),this.chunkStart<0&&(this.chunkStart=t),this.from.push(t-this.chunkStart),this.to.push(e-this.chunkStart),this.last=n,this.lastFrom=t,this.lastTo=e,this.value.push(n),n.point&&(this.maxPoint=Math.max(this.maxPoint,e-t)),0))}addChunk(t,e){if((t-this.lastTo||e.value[0].startSide-this.last.endSide)<0)return!1;this.from.length&&this.finishChunk(!0),this.setMaxPoint=Math.max(this.setMaxPoint,e.maxPoint),this.chunks.push(e),this.chunkPos.push(t);let n=e.value.length-1;return this.last=e.value[n],this.lastFrom=e.from[n]+t,this.lastTo=e.to[n]+t,!0}finish(){return this.finishInner(At.empty)}finishInner(t){if(this.from.length&&this.finishChunk(!1),0==this.chunks.length)return t;let e=At.create(this.chunkPos,this.chunks,this.nextLayer?this.nextLayer.finishInner(t):t,this.setMaxPoint);return this.from=null,e}}function Ct(t,e,n){let i=new Map;for(let e of t)for(let t=0;t<e.chunk.length;t++)e.chunk[t].maxPoint<=0&&i.set(e.chunk[t],e.chunkPos[t]);let s=new Set;for(let t of e)for(let e=0;e<t.chunk.length;e++){let r=i.get(t.chunk[e]);null==r||(n?n.mapPos(r):r)!=t.chunkPos[e]||(null==n?void 0:n.touchesRange(r,r+t.chunk[e].length))||s.add(t.chunk[e])}return s}class Mt{constructor(t,e,n,i=0){this.layer=t,this.skip=e,this.minPoint=n,this.rank=i}get startSide(){return this.value?this.value.startSide:0}get endSide(){return this.value?this.value.endSide:0}goto(t,e=-1e9){return this.chunkIndex=this.rangeIndex=0,this.gotoInner(t,e,!1),this}gotoInner(t,e,n){for(;this.chunkIndex<this.layer.chunk.length;){let e=this.layer.chunk[this.chunkIndex];if(!(this.skip&&this.skip.has(e)||this.layer.chunkEnd(this.chunkIndex)<t||e.maxPoint<this.minPoint))break;this.chunkIndex++,n=!1}if(this.chunkIndex<this.layer.chunk.length){let i=this.layer.chunk[this.chunkIndex].findIndex(t-this.layer.chunkPos[this.chunkIndex],e,!0);(!n||this.rangeIndex<i)&&this.setRangeIndex(i)}this.next()}forward(t,e){(this.to-t||this.endSide-e)<0&&this.gotoInner(t,e,!0)}next(){for(;;){if(this.chunkIndex==this.layer.chunk.length){this.from=this.to=1e9,this.value=null;break}{let t=this.layer.chunkPos[this.chunkIndex],e=this.layer.chunk[this.chunkIndex],n=t+e.from[this.rangeIndex];if(this.from=n,this.to=t+e.to[this.rangeIndex],this.value=e.value[this.rangeIndex],this.setRangeIndex(this.rangeIndex+1),this.minPoint<0||this.value.point&&this.to-this.from>=this.minPoint)break}}}setRangeIndex(t){if(t==this.layer.chunk[this.chunkIndex].value.length){if(this.chunkIndex++,this.skip)for(;this.chunkIndex<this.layer.chunk.length&&this.skip.has(this.layer.chunk[this.chunkIndex]);)this.chunkIndex++;this.rangeIndex=0}else this.rangeIndex=t}nextChunk(){this.chunkIndex++,this.rangeIndex=0,this.next()}compare(t){return this.from-t.from||this.startSide-t.startSide||this.rank-t.rank||this.to-t.to||this.endSide-t.endSide}}class Ot{constructor(t){this.heap=t}static from(t,e=null,n=-1){let i=[];for(let s=0;s<t.length;s++)for(let r=t[s];!r.isEmpty;r=r.nextLayer)r.maxPoint>=n&&i.push(new Mt(r,e,n,s));return 1==i.length?i[0]:new Ot(i)}get startSide(){return this.value?this.value.startSide:0}goto(t,e=-1e9){for(let n of this.heap)n.goto(t,e);for(let t=this.heap.length>>1;t>=0;t--)Tt(this.heap,t);return this.next(),this}forward(t,e){for(let n of this.heap)n.forward(t,e);for(let t=this.heap.length>>1;t>=0;t--)Tt(this.heap,t);(this.to-t||this.value.endSide-e)<0&&this.next()}next(){if(0==this.heap.length)this.from=this.to=1e9,this.value=null,this.rank=-1;else{let t=this.heap[0];this.from=t.from,this.to=t.to,this.value=t.value,this.rank=t.rank,t.value&&t.next(),Tt(this.heap,0)}}}function Tt(t,e){for(let n=t[e];;){let i=1+(e<<1);if(i>=t.length)break;let s=t[i];if(i+1<t.length&&s.compare(t[i+1])>=0&&(s=t[i+1],i++),n.compare(s)<0)break;t[i]=n,t[e]=s,e=i}}class Ft{constructor(t,e,n){this.minPoint=n,this.active=[],this.activeTo=[],this.activeRank=[],this.minActive=-1,this.point=null,this.pointFrom=0,this.pointRank=0,this.to=-1e9,this.endSide=0,this.openStart=-1,this.cursor=Ot.from(t,e,n)}goto(t,e=-1e9){return this.cursor.goto(t,e),this.active.length=this.activeTo.length=this.activeRank.length=0,this.minActive=-1,this.to=t,this.endSide=e,this.openStart=-1,this.next(),this}forward(t,e){for(;this.minActive>-1&&(this.activeTo[this.minActive]-t||this.active[this.minActive].endSide-e)<0;)this.removeActive(this.minActive);this.cursor.forward(t,e)}removeActive(t){Nt(this.active,t),Nt(this.activeTo,t),Nt(this.activeRank,t),this.minActive=Dt(this.active,this.activeTo)}addActive(t){let e=0,{value:n,to:i,rank:s}=this.cursor;for(;e<this.activeRank.length&&this.activeRank[e]<=s;)e++;Lt(this.active,e,n),Lt(this.activeTo,e,i),Lt(this.activeRank,e,s),t&&Lt(t,e,this.cursor.from),this.minActive=Dt(this.active,this.activeTo)}next(){let t=this.to,e=this.point;this.point=null;let n=this.openStart<0?[]:null;for(;;){let i=this.minActive;if(i>-1&&(this.activeTo[i]-this.cursor.from||this.active[i].endSide-this.cursor.startSide)<0){if(this.activeTo[i]>t){this.to=this.activeTo[i],this.endSide=this.active[i].endSide;break}this.removeActive(i),n&&Nt(n,i)}else{if(!this.cursor.value){this.to=this.endSide=1e9;break}if(this.cursor.from>t){this.to=this.cursor.from,this.endSide=this.cursor.startSide;break}{let t=this.cursor.value;if(t.point){if(!(e&&this.cursor.to==this.to&&this.cursor.from<this.cursor.to)){this.point=t,this.pointFrom=this.cursor.from,this.pointRank=this.cursor.rank,this.to=this.cursor.to,this.endSide=t.endSide,this.cursor.next(),this.forward(this.to,this.endSide);break}this.cursor.next()}else this.addActive(n),this.cursor.next()}}}if(n){this.openStart=0;for(let e=n.length-1;e>=0&&n[e]<t;e--)this.openStart++}}activeForPoint(t){if(!this.active.length)return this.active;let e=[];for(let n=this.active.length-1;n>=0&&!(this.activeRank[n]<this.pointRank);n--)(this.activeTo[n]>t||this.activeTo[n]==t&&this.active[n].endSide>=this.point.endSide)&&e.push(this.active[n]);return e.reverse()}openEnd(t){let e=0;for(let n=this.activeTo.length-1;n>=0&&this.activeTo[n]>t;n--)e++;return e}}function Bt(t,e,n,i,s,r){t.goto(e),n.goto(i);let h=i+s,o=i,l=i-e;for(;;){let e=t.to+l-n.to||t.endSide-n.endSide,i=e<0?t.to+l:n.to,s=Math.min(i,h);if(t.point||n.point?t.point&&n.point&&(t.point==n.point||t.point.eq(n.point))&&Jt(t.activeForPoint(t.to),n.activeForPoint(n.to))||r.comparePoint(o,s,t.point,n.point):s>o&&!Jt(t.active,n.active)&&r.compareRange(o,s,t.active,n.active),i>h)break;o=i,e<=0&&t.next(),e>=0&&n.next()}}function Jt(t,e){if(t.length!=e.length)return!1;for(let n=0;n<t.length;n++)if(t[n]!=e[n]&&!t[n].eq(e[n]))return!1;return!0}function Nt(t,e){for(let n=e,i=t.length-1;n<i;n++)t[n]=t[n+1];t.pop()}function Lt(t,e,n){for(let n=t.length-1;n>=e;n--)t[n+1]=t[n];t[e]=n}function Dt(t,e){let n=-1,i=1e9;for(let s=0;s<e.length;s++)(e[s]-i||t[s].endSide-t[n].endSide)<0&&(n=s,i=e[s]);return n}function jt(t,e,n=t.length){let i=0;for(let s=0;s<n;)9==t.charCodeAt(s)?(i+=e-i%e,s++):(i++,s=m(t,s));return i}function qt(t,e,n,i){for(let i=0,s=0;;){if(s>=e)return i;if(i==t.length)break;s+=9==t.charCodeAt(i)?n-s%n:1,i=m(t,i)}return!0===i?-1:t.length}}}]);
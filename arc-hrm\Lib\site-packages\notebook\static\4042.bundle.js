"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[4042,134],{44042:(t,e,s)=>{s.r(e),s.d(e,{AttachmentsModel:()=>r,AttachmentsResolver:()=>h});var a=s(11351),n=s(70856),i=s(81997);class r{constructor(t){var e;if(this._map=new a.ObservableMap,this._isDisposed=!1,this._stateChanged=new i.Signal(this),this._changed=new i.Signal(this),this._serialized=null,this._changeGuard=!1,this.contentFactory=null!==(e=t.contentFactory)&&void 0!==e?e:r.defaultContentFactory,t.values)for(const e of Object.keys(t.values))void 0!==t.values[e]&&this.set(e,t.values[e]);this._map.changed.connect(this._onMapChanged,this)}get stateChanged(){return this._stateChanged}get changed(){return this._changed}get keys(){return this._map.keys()}get length(){return this._map.keys().length}get isDisposed(){return this._isDisposed}dispose(){this.isDisposed||(this._isDisposed=!0,this._map.dispose(),i.Signal.clearData(this))}has(t){return this._map.has(t)}get(t){return this._map.get(t)}set(t,e){const s=this._createItem({value:e});this._map.set(t,s)}remove(t){this._map.delete(t)}clear(){this._map.values().forEach((t=>{t.dispose()})),this._map.clear()}fromJSON(t){this.clear(),Object.keys(t).forEach((e=>{void 0!==t[e]&&this.set(e,t[e])}))}toJSON(){const t={};for(const e of this._map.keys())t[e]=this._map.get(e).toJSON();return t}_createItem(t){const e=this.contentFactory.createAttachmentModel(t);return e.changed.connect(this._onGenericChange,this),e}_onMapChanged(t,e){this._serialized&&!this._changeGuard&&(this._changeGuard=!0,this._serialized.set(this.toJSON()),this._changeGuard=!1),this._changed.emit(e),this._stateChanged.emit(void 0)}_onGenericChange(){this._stateChanged.emit(void 0)}}!function(t){class e{createAttachmentModel(t){return new n.AttachmentModel(t)}}t.ContentFactory=e,t.defaultContentFactory=new e}(r||(r={}));class h{constructor(t){this._parent=t.parent||null,this._model=t.model}async resolveUrl(t){return this._parent&&!t.startsWith("attachment:")?this._parent.resolveUrl(t):t}async getDownloadUrl(t){if(this._parent&&!t.startsWith("attachment:"))return this._parent.getDownloadUrl(t);const e=t.slice(11),s=this._model.get(e);if(void 0===s)return t;const{data:a}=s,i=Object.keys(a)[0];if(void 0===i||-1===n.imageRendererFactory.mimeTypes.indexOf(i))throw new Error(`Cannot render unknown image mime type "${i}".`);return`data:${i};base64,${a[i]}`}isLocal(t){var e,s,a;return!(this._parent&&!t.startsWith("attachment:"))||null===(a=null===(s=(e=this._parent).isLocal)||void 0===s?void 0:s.call(e,t))||void 0===a||a}}}}]);
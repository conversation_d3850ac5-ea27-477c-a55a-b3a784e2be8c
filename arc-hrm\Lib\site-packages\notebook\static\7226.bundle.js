"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[7226],{47226:(t,e,s)=>{s.r(e),s.d(e,{OutputArea:()=>x,OutputAreaModel:()=>d,OutputPrompt:()=>w,SimplifiedOutputArea:()=>v,Stdin:()=>O});var i,n=s(85923),r=s(11351),a=s(70856),o=s(33625),h=s(20998),u=s(81997);class d{constructor(t={}){if(this.clearNext=!1,this._lastStream="",this._trusted=!1,this._isDisposed=!1,this._stateChanged=new u.Signal(this),this._changed=new u.Signal(this),this._trusted=!!t.trusted,this.contentFactory=t.contentFactory||d.defaultContentFactory,this.list=new r.ObservableList,t.values)for(const e of t.values){const t=this._add(e)-1;this.list.get(t).changed.connect(this._onGenericChange,this)}this.list.changed.connect(this._onListChanged,this)}get stateChanged(){return this._stateChanged}get changed(){return this._changed}get length(){return this.list?this.list.length:0}get trusted(){return this._trusted}set trusted(t){if(t===this._trusted)return;const e=this._trusted=t;for(let t=0;t<this.list.length;t++){const s=this.list.get(t),i=s.toJSON(),n=this._createItem({value:i,trusted:e});this.list.set(t,n),s.dispose()}}get isDisposed(){return this._isDisposed}dispose(){this.isDisposed||(this._isDisposed=!0,this.list.dispose(),u.Signal.clearData(this))}get(t){return this.list.get(t)}set(t,e){e=h.JSONExt.deepCopy(e),i.normalize(e);const s=this._createItem({value:e,trusted:this._trusted});this.list.set(t,s)}add(t){return this.clearNext&&(this.clear(),this.clearNext=!1),this._add(t)}clear(t=!1){if(this._lastStream="",t)this.clearNext=!0;else{for(const t of this.list)t.dispose();this.list.clear()}}fromJSON(t){this.clear();for(const e of t)this._add(e)}toJSON(){return Array.from((0,o.map)(this.list,(t=>t.toJSON())))}_add(t){const e=this._trusted;if(t=h.JSONExt.deepCopy(t),i.normalize(t),n.isStream(t)&&this._lastStream&&t.name===this._lastName&&this.shouldCombine({value:t,lastModel:this.list.get(this.length-1)})){this._lastStream+=t.text,this._lastStream=i.removeOverwrittenChars(this._lastStream),t.text=this._lastStream;const s=this._createItem({value:t,trusted:e}),n=this.length-1,r=this.list.get(n);return this.list.set(n,s),r.dispose(),this.length}n.isStream(t)&&(t.text=i.removeOverwrittenChars(t.text));const s=this._createItem({value:t,trusted:e});return n.isStream(t)?(this._lastStream=t.text,this._lastName=t.name):this._lastStream="",this.list.push(s)}shouldCombine(t){return!0}_createItem(t){return this.contentFactory.createOutputModel(t)}_onListChanged(t,e){switch(e.type){case"add":e.newValues.forEach((t=>{t.changed.connect(this._onGenericChange,this)}));break;case"remove":e.oldValues.forEach((t=>{t.changed.disconnect(this._onGenericChange,this)}));break;case"set":e.newValues.forEach((t=>{t.changed.connect(this._onGenericChange,this)})),e.oldValues.forEach((t=>{t.changed.disconnect(this._onGenericChange,this)}))}this._changed.emit(e)}_onGenericChange(t){let e,s=null;for(e=0;e<this.list.length&&(s=this.list.get(e),s!==t);e++);null!=s&&(this._stateChanged.emit(e),this._changed.emit({type:"set",newIndex:e,oldIndex:e,oldValues:[s],newValues:[s]}))}}!function(t){class e{createOutputModel(t){return new a.OutputModel(t)}}t.ContentFactory=e,t.defaultContentFactory=new e}(d||(d={})),function(t){t.normalize=function(t){n.isStream(t)&&Array.isArray(t.text)&&(t.text=t.text.join("\n"))},t.removeOverwrittenChars=function(t){return function(t){for(t=t.replace(/\r+\n/gm,"\n");t.search(/\r[^$]/g)>-1;){const e=t.match(/^(.*)\r+/m)[1];let s=t.match(/\r+(.*)$/m)[1];s+=e.slice(s.length,e.length),t=t.replace(/\r+.*$/m,"\r").replace(/^.*\r/m,s)}return t}(function(t){let e=t;do{e=(t=e).replace(/[^\n]\x08/gm,"")}while(e.length<t.length);return t}(t))}}(i||(i={}));var l=s(12982),c=s(16954),p=s(71677),m=s(14421),_=s(31516);const g="jp-OutputArea-child",y="jp-OutputArea-output",f="jp-OutputArea-prompt";class x extends _.Widget{constructor(t){var e,s,i,n;super(),this.outputLengthChanged=new u.Signal(this),this._onIOPub=t=>{const e=this.model,s=t.header.msg_type;let i;const n=(t.content.transient||{}).display_id;let r;switch(s){case"execute_result":case"display_data":case"stream":case"error":i={...t.content,output_type:s},e.add(i);break;case"clear_output":{const s=t.content.wait;e.clear(s);break}case"update_display_data":if(i={...t.content,output_type:"display_data"},r=this._displayIdMap.get(n),r)for(const t of r)e.set(t,i)}n&&"display_data"===s&&(r=this._displayIdMap.get(n)||[],r.push(e.length-1),this._displayIdMap.set(n,r))},this._onExecuteReply=t=>{const e=this.model,s=t.content;if("ok"!==s.status)return;const i=s&&s.payload;if(!i||!i.length)return;const n=i.filter((t=>"page"===t.source));if(!n.length)return;const r={output_type:"display_data",data:JSON.parse(JSON.stringify(n[0])).data,metadata:{}};e.add(r)},this._displayIdMap=new Map,this._minHeightTimeout=null,this._inputRequested=new u.Signal(this),this._toggleScrolling=new u.Signal(this),this._initialize=new u.Signal(this),this._outputTracker=new l.WidgetTracker({namespace:h.UUID.uuid4()}),this._inputHistoryScope="global",super.layout=new _.PanelLayout,this.addClass("jp-OutputArea"),this.contentFactory=null!==(e=t.contentFactory)&&void 0!==e?e:x.defaultContentFactory,this.rendermime=t.rendermime,this._maxNumberOutputs=null!==(s=t.maxNumberOutputs)&&void 0!==s?s:1/0,this._translator=null!==(i=t.translator)&&void 0!==i?i:p.nullTranslator,this._inputHistoryScope=null!==(n=t.inputHistoryScope)&&void 0!==n?n:"global";const r=this.model=t.model;for(let t=0;t<Math.min(r.length,this._maxNumberOutputs+1);t++){const e=r.get(t);this._insertOutput(t,e)}r.changed.connect(this.onModelChanged,this),r.stateChanged.connect(this.onStateChanged,this),t.promptOverlay&&this._addPromptOverlay()}get layout(){return super.layout}get widgets(){return this.layout.widgets}get future(){return this._future}set future(t){if(this.model.isDisposed)throw Error("Model is disposed");this._future!==t&&(this._future&&this._future.dispose(),this._future=t,this.model.clear(),this.widgets.length&&(this._clear(),this.outputLengthChanged.emit(Math.min(this.model.length,this._maxNumberOutputs))),t.onIOPub=this._onIOPub,t.onReply=this._onExecuteReply,t.onStdin=e=>{c.KernelMessage.isInputRequestMsg(e)&&this.onInputRequest(e,t)})}get inputRequested(){return this._inputRequested}get maxNumberOutputs(){return this._maxNumberOutputs}set maxNumberOutputs(t){if(t<=0)return void console.warn("OutputArea.maxNumberOutputs must be strictly positive.");const e=this._maxNumberOutputs;this._maxNumberOutputs=t,e<t&&this._showTrimmedOutputs(e)}dispose(){this._future&&(this._future.dispose(),this._future=null),this._displayIdMap.clear(),this._outputTracker.dispose(),super.dispose()}onModelChanged(t,e){switch(e.type){case"add":this._insertOutput(e.newIndex,e.newValues[0]);break;case"remove":if(this.widgets.length)if(0===this.model.length)this._clear();else{const t=e.oldIndex;for(let s=0;s<e.oldValues.length&&t<this.widgets.length;++s){const e=this.widgets[t];e.parent=null,e.dispose()}this._moveDisplayIdIndices(t,e.oldValues.length),this._preventHeightChangeJitter()}break;case"set":this._setOutput(e.newIndex,e.newValues[0])}this.outputLengthChanged.emit(Math.min(this.model.length,this._maxNumberOutputs))}get toggleScrolling(){return this._toggleScrolling}get initialize(){return this._initialize}_addPromptOverlay(){const t=document.createElement("div");t.className="jp-OutputArea-promptOverlay",t.addEventListener("click",(()=>{this._toggleScrolling.emit()})),this.node.appendChild(t),requestAnimationFrame((()=>{this._initialize.emit()}))}_moveDisplayIdIndices(t,e){this._displayIdMap.forEach((s=>{const i=t+e;for(let n=s.length-1;n>=0;--n){const r=s[n];r>=t&&r<i?s.splice(n,1):r>=i&&(s[n]-=e)}}))}onStateChanged(t,e){const s=Math.min(this.model.length,this._maxNumberOutputs);if(e){if(e>=this._maxNumberOutputs)return;this._setOutput(e,this.model.get(e))}else for(let t=0;t<s;t++)this._setOutput(t,this.model.get(t));this.outputLengthChanged.emit(s)}_clear(){if(!this.widgets.length)return;const t=this.widgets.length;for(let e=0;e<t;e++){const t=this.widgets[0];t.parent=null,t.dispose()}this._displayIdMap.clear(),this._preventHeightChangeJitter()}_preventHeightChangeJitter(){const t=this.node.getBoundingClientRect();this.node.style.minHeight=`${t.height}px`,this._minHeightTimeout&&window.clearTimeout(this._minHeightTimeout),this._minHeightTimeout=window.setTimeout((()=>{this.isDisposed||(this.node.style.minHeight="")}),50)}onInputRequest(t,e){const s=this.contentFactory,i=t.content.prompt,n=t.content.password,r=new _.Panel;r.addClass(g),r.addClass("jp-OutputArea-stdin-item");const a=s.createOutputPrompt();a.addClass(f),r.addWidget(a);const o=s.createStdin({parent_header:t.header,prompt:i,password:n,future:e,translator:this._translator,inputHistoryScope:this._inputHistoryScope});o.addClass(y),r.addWidget(o),this.model.length>=this.maxNumberOutputs&&(this.maxNumberOutputs=this.model.length),this._inputRequested.emit(o);const h=o.node.getElementsByTagName("input")[0];o.value.then((t=>{this.model.length>=this.maxNumberOutputs&&(this.maxNumberOutputs=this.model.length+1),r.addClass("jp-OutputArea-stdin-hiding"),this.model.add({output_type:"stream",name:"stdin",text:t+"\n"}),h.focus(),window.setTimeout((()=>{const t=document.activeElement;r.dispose(),t&&t instanceof HTMLElement&&t.focus()}),500)})),this.layout.addWidget(r)}_setOutput(t,e){if(t>=this._maxNumberOutputs)return;const s=this.layout.widgets[t],i=s.widgets?s.widgets.filter((t=>"renderModel"in t)).pop():s,n=this.rendermime.preferredMimeType(e.data,e.trusted?"any":"ensure");C.currentPreferredMimetype.get(i)===n&&x.isIsolated(n,e.metadata)===i instanceof C.IsolatedRenderer?i.renderModel(e):(this.layout.widgets[t].dispose(),this._insertOutput(t,e))}_insertOutput(t,e){if(t>this._maxNumberOutputs)return;const s=this.layout;if(t===this._maxNumberOutputs){const e=new C.TrimmedOutputs(this._maxNumberOutputs,(()=>{const t=this._maxNumberOutputs;this._maxNumberOutputs=1/0,this._showTrimmedOutputs(t)}));s.insertWidget(t,this._wrappedOutput(e))}else{let i=this.createOutputItem(e);i?i.toggleClass("jp-OutputArea-executeResult",null!==e.executionCount):i=new _.Widget,this._outputTracker.has(i)||this._outputTracker.add(i),s.insertWidget(t,i)}}get outputTracker(){return this._outputTracker}_showTrimmedOutputs(t){this.widgets[t].dispose();for(let e=t;e<this.model.length;e++)this._insertOutput(e,this.model.get(e));this.outputLengthChanged.emit(Math.min(this.model.length,this._maxNumberOutputs))}createOutputItem(t){const e=this.createRenderedMimetype(t);return e?this._wrappedOutput(e,t.executionCount):null}createRenderedMimetype(t){const e=this.rendermime.preferredMimeType(t.data,t.trusted?"any":"ensure");if(!e)return null;let s=this.rendermime.createRenderer(e);return!0===x.isIsolated(e,t.metadata)&&(s=new C.IsolatedRenderer(s)),C.currentPreferredMimetype.set(s,e),s.renderModel(t).catch((t=>{const e=document.createElement("pre"),i=this._translator.load("jupyterlab");e.textContent=i.__("Javascript Error: %1",t.message),s.node.appendChild(e),s.node.className="lm-Widget jp-RenderedText",s.node.setAttribute("data-mime-type","application/vnd.jupyter.stderr")})),s}_wrappedOutput(t,e=null){const s=new C.OutputPanel;s.addClass(g);const i=this.contentFactory.createOutputPrompt();return i.executionCount=e,i.addClass(f),s.addWidget(i),t.addClass(y),s.addWidget(t),s}}class v extends x{onInputRequest(t,e){}createOutputItem(t){const e=this.createRenderedMimetype(t);if(!e)return null;const s=new C.OutputPanel;return s.addClass(g),e.addClass(y),s.addWidget(e),s}}!function(t){t.execute=async function(t,e,s,i){var n;let r=!0;i&&Array.isArray(i.tags)&&-1!==i.tags.indexOf("raises-exception")&&(r=!1);const a={code:t,stop_on_error:r},o=null===(n=s.session)||void 0===n?void 0:n.kernel;if(!o)throw new Error("Session has no kernel.");const h=o.requestExecute(a,!1,i);return e.future=h,h.done},t.isIsolated=function(t,e){const s=e[t];return s&&void 0!==s.isolated?!!s.isolated:!!e.isolated};class e{createOutputPrompt(){return new w}createStdin(t){return new O(t)}}t.ContentFactory=e,t.defaultContentFactory=new e}(x||(x={}));class w extends _.Widget{constructor(){super(),this._executionCount=null,this.addClass("jp-OutputPrompt")}get executionCount(){return this._executionCount}set executionCount(t){this._executionCount=t,this.node.textContent=null===t?"":`[${t}]:`}}class O extends _.Widget{static _historyIx(t,e){const s=O._history.get(t);if(!s)return;const i=s.length;return e<=0?i+e:void 0}static _historyAt(t,e){const s=O._history.get(t);if(!s)return;const i=s.length,n=O._historyIx(t,e);return void 0!==n&&n<i?s[n]:void 0}static _historyPush(t,e){const s=O._history.get(t);s.push(e),s.length>1e3&&s.shift()}static _historySearch(t,e,s,i=!0){const n=O._history.get(t),r=n.length,a=O._historyIx(t,s),o=t=>-1!==t.search(e);if(void 0!==a)if(i){if(0===a)return;const t=n.slice(0,a).findLastIndex(o);if(-1!==t)return t-r}else{if(a>=r-1)return;const t=n.slice(a+1).findIndex(o);if(-1!==t)return t-r+a+1}}constructor(t){var e;super({node:C.createInputWidgetNode(t.prompt,t.password)}),this._promise=new h.PromiseDelegate,this._resolved=!1,this.addClass("jp-Stdin"),this._future=t.future,this._historyIndex=0,this._historyKey="session"===t.inputHistoryScope?t.parent_header.session:"",this._historyPat="",this._parentHeader=t.parent_header,this._password=t.password,this._trans=(null!==(e=t.translator)&&void 0!==e?e:p.nullTranslator).load("jupyterlab"),this._value=t.prompt+" ",this._input=this.node.getElementsByTagName("input")[0],this._input.placeholder=this._trans.__("↑↓ for history. Search history with c-↑/c-↓"),O._history.has(this._historyKey)||O._history.set(this._historyKey,[])}get value(){return this._promise.promise.then((()=>this._value))}handleEvent(t){if(this._resolved)return void t.preventDefault();const e=this._input;if("keydown"===t.type)if("Enter"===t.key)this.resetSearch(),this._future.sendInputReply({status:"ok",value:e.value},this._parentHeader),this._password?this._value+="········":(this._value+=e.value,O._historyPush(this._historyKey,e.value)),this._resolved=!0,this._promise.resolve(void 0);else if("Escape"===t.key)this.resetSearch(),e.blur();else if(!t.ctrlKey||"ArrowUp"!==t.key&&"ArrowDown"!==t.key){if("ArrowUp"===t.key){this.resetSearch();const s=O._historyAt(this._historyKey,this._historyIndex-1);s&&(0===this._historyIndex&&(this._valueCache=e.value),this._setInputValue(s),--this._historyIndex,t.preventDefault())}else if("ArrowDown"===t.key)if(this.resetSearch(),0===this._historyIndex);else if(-1===this._historyIndex)this._setInputValue(this._valueCache),++this._historyIndex;else{const t=O._historyAt(this._historyKey,this._historyIndex+1);t&&(this._setInputValue(t),++this._historyIndex)}}else{""===this._historyPat&&(this._historyPat=e.value);const s="ArrowUp"===t.key,i=O._historySearch(this._historyKey,this._historyPat,this._historyIndex,s);if(void 0!==i){const s=O._historyAt(this._historyKey,i);void 0!==s&&(0===this._historyIndex&&(this._valueCache=e.value),this._setInputValue(s),this._historyIndex=i,t.preventDefault())}}}resetSearch(){this._historyPat=""}onAfterAttach(t){this._input.addEventListener("keydown",this),this._input.focus()}onBeforeDetach(t){this._input.removeEventListener("keydown",this)}_setInputValue(t){this._input.value=t,this._input.setSelectionRange(t.length,t.length)}}var C;O._history=new Map,function(t){t.createInputWidgetNode=function(t,e){const s=document.createElement("div"),i=document.createElement("pre");i.className="jp-Stdin-prompt",i.textContent=t;const n=document.createElement("input");return n.className="jp-Stdin-input",e&&(n.type="password"),s.appendChild(i),i.appendChild(n),s};class e extends _.Widget{constructor(t){super({node:document.createElement("iframe")}),this.addClass("jp-mod-isolated"),this._wrapped=t;const e=this.node;e.frameBorder="0",e.scrolling="auto",e.addEventListener("load",(()=>{e.contentDocument.open(),e.contentDocument.write(this._wrapped.node.innerHTML),e.contentDocument.close();const t=e.contentDocument.body;e.style.height=`${t.scrollHeight}px`,e.heightChangeObserver=new ResizeObserver((()=>{e.style.height=`${t.scrollHeight}px`})),e.heightChangeObserver.observe(t)}))}renderModel(t){return this._wrapped.renderModel(t)}}t.IsolatedRenderer=e,t.currentPreferredMimetype=new m.AttachedProperty({name:"preferredMimetype",create:t=>""});class s extends _.Panel{constructor(t){super(t)}_onContext(t){this.node.focus()}onAfterAttach(t){super.onAfterAttach(t),this.node.addEventListener("contextmenu",this._onContext.bind(this))}onBeforeDetach(t){super.onAfterDetach(t),this.node.removeEventListener("contextmenu",this._onContext.bind(this))}}t.OutputPanel=s;class i extends _.Widget{constructor(t,e){const s=document.createElement("div"),i=`The first ${t} are displayed`;s.insertAdjacentHTML("afterbegin",`<a title=${i}>\n          <pre>Show more outputs</pre>\n        </a>`),super({node:s}),this._onClick=e,this.addClass("jp-TrimmedOutputs"),this.addClass("jp-RenderedHTMLCommon")}handleEvent(t){"click"===t.type&&this._onClick(t)}onAfterAttach(t){super.onAfterAttach(t),this.node.addEventListener("click",this)}onBeforeDetach(t){super.onBeforeDetach(t),this.node.removeEventListener("click",this)}}t.TrimmedOutputs=i}(C||(C={}))}}]);
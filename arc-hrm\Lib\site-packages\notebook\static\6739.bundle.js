"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[6739],{56739:(e,t,r)=>{r.r(t),r.d(t,{stylus:()=>oe});var i=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","bgsound","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","nobr","noframes","noscript","object","ol","optgroup","option","output","p","param","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","track","u","ul","var","video"],a=["domain","regexp","url-prefix","url"],o=["all","aural","braille","handheld","print","projection","screen","tty","tv","embossed"],n=["width","min-width","max-width","height","min-height","max-height","device-width","min-device-width","max-device-width","device-height","min-device-height","max-device-height","aspect-ratio","min-aspect-ratio","max-aspect-ratio","device-aspect-ratio","min-device-aspect-ratio","max-device-aspect-ratio","color","min-color","max-color","color-index","min-color-index","max-color-index","monochrome","min-monochrome","max-monochrome","resolution","min-resolution","max-resolution","scan","grid","dynamic-range","video-dynamic-range"],l=["align-content","align-items","align-self","alignment-adjust","alignment-baseline","anchor-point","animation","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-timing-function","appearance","azimuth","backface-visibility","background","background-attachment","background-clip","background-color","background-image","background-origin","background-position","background-repeat","background-size","baseline-shift","binding","bleed","bookmark-label","bookmark-level","bookmark-state","bookmark-target","border","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-decoration-break","box-shadow","box-sizing","break-after","break-before","break-inside","caption-side","clear","clip","color","color-profile","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","column-span","column-width","columns","content","counter-increment","counter-reset","crop","cue","cue-after","cue-before","cursor","direction","display","dominant-baseline","drop-initial-after-adjust","drop-initial-after-align","drop-initial-before-adjust","drop-initial-before-align","drop-initial-size","drop-initial-value","elevation","empty-cells","fit","fit-position","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","float-offset","flow-from","flow-into","font","font-feature-settings","font-family","font-kerning","font-language-override","font-size","font-size-adjust","font-stretch","font-style","font-synthesis","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-ligatures","font-variant-numeric","font-variant-position","font-weight","grid","grid-area","grid-auto-columns","grid-auto-flow","grid-auto-position","grid-auto-rows","grid-column","grid-column-end","grid-column-start","grid-row","grid-row-end","grid-row-start","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","hanging-punctuation","height","hyphens","icon","image-orientation","image-rendering","image-resolution","inline-box-align","justify-content","left","letter-spacing","line-break","line-height","line-stacking","line-stacking-ruby","line-stacking-shift","line-stacking-strategy","list-style","list-style-image","list-style-position","list-style-type","margin","margin-bottom","margin-left","margin-right","margin-top","marker-offset","marks","marquee-direction","marquee-loop","marquee-play-count","marquee-speed","marquee-style","max-height","max-width","min-height","min-width","move-to","nav-down","nav-index","nav-left","nav-right","nav-up","object-fit","object-position","opacity","order","orphans","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-style","overflow-wrap","overflow-x","overflow-y","padding","padding-bottom","padding-left","padding-right","padding-top","page","page-break-after","page-break-before","page-break-inside","page-policy","pause","pause-after","pause-before","perspective","perspective-origin","pitch","pitch-range","play-during","position","presentation-level","punctuation-trim","quotes","region-break-after","region-break-before","region-break-inside","region-fragment","rendering-intent","resize","rest","rest-after","rest-before","richness","right","rotation","rotation-point","ruby-align","ruby-overhang","ruby-position","ruby-span","shape-image-threshold","shape-inside","shape-margin","shape-outside","size","speak","speak-as","speak-header","speak-numeral","speak-punctuation","speech-rate","stress","string-set","tab-size","table-layout","target","target-name","target-new","target-position","text-align","text-align-last","text-decoration","text-decoration-color","text-decoration-line","text-decoration-skip","text-decoration-style","text-emphasis","text-emphasis-color","text-emphasis-position","text-emphasis-style","text-height","text-indent","text-justify","text-outline","text-overflow","text-shadow","text-size-adjust","text-space-collapse","text-transform","text-underline-position","text-wrap","top","transform","transform-origin","transform-style","transition","transition-delay","transition-duration","transition-property","transition-timing-function","unicode-bidi","vertical-align","visibility","voice-balance","voice-duration","voice-family","voice-pitch","voice-range","voice-rate","voice-stress","voice-volume","volume","white-space","widows","width","will-change","word-break","word-spacing","word-wrap","z-index","clip-path","clip-rule","mask","enable-background","filter","flood-color","flood-opacity","lighting-color","stop-color","stop-opacity","pointer-events","color-interpolation","color-interpolation-filters","color-rendering","fill","fill-opacity","fill-rule","image-rendering","marker","marker-end","marker-mid","marker-start","shape-rendering","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","text-rendering","baseline-shift","dominant-baseline","glyph-orientation-horizontal","glyph-orientation-vertical","text-anchor","writing-mode","font-smoothing","osx-font-smoothing"],s=["scrollbar-arrow-color","scrollbar-base-color","scrollbar-dark-shadow-color","scrollbar-face-color","scrollbar-highlight-color","scrollbar-shadow-color","scrollbar-3d-light-color","scrollbar-track-color","shape-inside","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","zoom"],c=["font-family","src","unicode-range","font-variant","font-feature-settings","font-stretch","font-weight","font-style"],d=["aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","grey","green","greenyellow","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen"],u=["above","absolute","activeborder","additive","activecaption","afar","after-white-space","ahead","alias","all","all-scroll","alphabetic","alternate","always","amharic","amharic-abegede","antialiased","appworkspace","arabic-indic","armenian","asterisks","attr","auto","avoid","avoid-column","avoid-page","avoid-region","background","backwards","baseline","below","bidi-override","binary","bengali","blink","block","block-axis","bold","bolder","border","border-box","both","bottom","break","break-all","break-word","bullets","button","buttonface","buttonhighlight","buttonshadow","buttontext","calc","cambodian","capitalize","caps-lock-indicator","caption","captiontext","caret","cell","center","checkbox","circle","cjk-decimal","cjk-earthly-branch","cjk-heavenly-stem","cjk-ideographic","clear","clip","close-quote","col-resize","collapse","column","compact","condensed","conic-gradient","contain","content","contents","content-box","context-menu","continuous","copy","counter","counters","cover","crop","cross","crosshair","currentcolor","cursive","cyclic","dashed","decimal","decimal-leading-zero","default","default-button","destination-atop","destination-in","destination-out","destination-over","devanagari","disc","discard","disclosure-closed","disclosure-open","document","dot-dash","dot-dot-dash","dotted","double","down","e-resize","ease","ease-in","ease-in-out","ease-out","element","ellipse","ellipsis","embed","end","ethiopic","ethiopic-abegede","ethiopic-abegede-am-et","ethiopic-abegede-gez","ethiopic-abegede-ti-er","ethiopic-abegede-ti-et","ethiopic-halehame-aa-er","ethiopic-halehame-aa-et","ethiopic-halehame-am-et","ethiopic-halehame-gez","ethiopic-halehame-om-et","ethiopic-halehame-sid-et","ethiopic-halehame-so-et","ethiopic-halehame-ti-er","ethiopic-halehame-ti-et","ethiopic-halehame-tig","ethiopic-numeric","ew-resize","expanded","extends","extra-condensed","extra-expanded","fantasy","fast","fill","fixed","flat","flex","footnotes","forwards","from","geometricPrecision","georgian","graytext","groove","gujarati","gurmukhi","hand","hangul","hangul-consonant","hebrew","help","hidden","hide","high","higher","highlight","highlighttext","hiragana","hiragana-iroha","horizontal","hsl","hsla","icon","ignore","inactiveborder","inactivecaption","inactivecaptiontext","infinite","infobackground","infotext","inherit","initial","inline","inline-axis","inline-block","inline-flex","inline-table","inset","inside","intrinsic","invert","italic","japanese-formal","japanese-informal","justify","kannada","katakana","katakana-iroha","keep-all","khmer","korean-hangul-formal","korean-hanja-formal","korean-hanja-informal","landscape","lao","large","larger","left","level","lighter","line-through","linear","linear-gradient","lines","list-item","listbox","listitem","local","logical","loud","lower","lower-alpha","lower-armenian","lower-greek","lower-hexadecimal","lower-latin","lower-norwegian","lower-roman","lowercase","ltr","malayalam","match","matrix","matrix3d","media-play-button","media-slider","media-sliderthumb","media-volume-slider","media-volume-sliderthumb","medium","menu","menulist","menulist-button","menutext","message-box","middle","min-intrinsic","mix","mongolian","monospace","move","multiple","myanmar","n-resize","narrower","ne-resize","nesw-resize","no-close-quote","no-drop","no-open-quote","no-repeat","none","normal","not-allowed","nowrap","ns-resize","numbers","numeric","nw-resize","nwse-resize","oblique","octal","open-quote","optimizeLegibility","optimizeSpeed","oriya","oromo","outset","outside","outside-shape","overlay","overline","padding","padding-box","painted","page","paused","persian","perspective","plus-darker","plus-lighter","pointer","polygon","portrait","pre","pre-line","pre-wrap","preserve-3d","progress","push-button","radial-gradient","radio","read-only","read-write","read-write-plaintext-only","rectangle","region","relative","repeat","repeating-linear-gradient","repeating-radial-gradient","repeating-conic-gradient","repeat-x","repeat-y","reset","reverse","rgb","rgba","ridge","right","rotate","rotate3d","rotateX","rotateY","rotateZ","round","row-resize","rtl","run-in","running","s-resize","sans-serif","scale","scale3d","scaleX","scaleY","scaleZ","scroll","scrollbar","scroll-position","se-resize","searchfield","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","semi-condensed","semi-expanded","separate","serif","show","sidama","simp-chinese-formal","simp-chinese-informal","single","skew","skewX","skewY","skip-white-space","slide","slider-horizontal","slider-vertical","sliderthumb-horizontal","sliderthumb-vertical","slow","small","small-caps","small-caption","smaller","solid","somali","source-atop","source-in","source-out","source-over","space","spell-out","square","square-button","standard","start","static","status-bar","stretch","stroke","sub","subpixel-antialiased","super","sw-resize","symbolic","symbols","table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row","table-row-group","tamil","telugu","text","text-bottom","text-top","textarea","textfield","thai","thick","thin","threeddarkshadow","threedface","threedhighlight","threedlightshadow","threedshadow","tibetan","tigre","tigrinya-er","tigrinya-er-abegede","tigrinya-et","tigrinya-et-abegede","to","top","trad-chinese-formal","trad-chinese-informal","translate","translate3d","translateX","translateY","translateZ","transparent","ultra-condensed","ultra-expanded","underline","up","upper-alpha","upper-armenian","upper-greek","upper-hexadecimal","upper-latin","upper-norwegian","upper-roman","uppercase","urdu","url","var","vertical","vertical-text","visible","visibleFill","visiblePainted","visibleStroke","visual","w-resize","wait","wave","wider","window","windowframe","windowtext","words","x-large","x-small","xor","xx-large","xx-small","bicubic","optimizespeed","grayscale","row","row-reverse","wrap","wrap-reverse","column-reverse","flex-start","flex-end","space-between","space-around","unset"],m=["in","and","or","not","is not","is a","is","isnt","defined","if unless"],p=["for","if","else","unless","from","to"],h=["null","true","false","href","title","type","not-allowed","readonly","disabled"],b=i.concat(a,o,n,l,s,d,u,c,m,p,h,["@font-face","@keyframes","@media","@viewport","@page","@host","@supports","@block","@css"]);function g(e){return e=e.sort((function(e,t){return t>e})),new RegExp("^(("+e.join(")|(")+"))\\b")}function f(e){for(var t={},r=0;r<e.length;++r)t[e[r]]=!0;return t}var k,w,y,v,x=f(i),z=/^(a|b|i|s|col|em)$/i,q=f(l),$=f(s),j=f(u),_=f(d),P=f(a),U=g(a),B=f(n),C=f(o),L=f(c),E=/^\s*([.]{2,3}|&&|\|\||\*\*|[?!=:]?=|[-+*\/%<>]=?|\?:|\~)/,N=g(m),O=f(p),R=new RegExp(/^\-(moz|ms|o|webkit)-/i),T=f(h),A="",Y={};function W(e,t){if(A=e.string.match(/(^[\w-]+\s*=\s*$)|(^\s*[\w-]+\s*=\s*[\w-])|(^\s*(\.|#|@|\$|\&|\[|\d|\+|::?|\{|\>|~|\/)?\s*[\w-]*([a-z0-9-]|\*|\/\*)(\(|,)?)/),t.context.line.firstWord=A?A[0].replace(/^\s*/,""):"",t.context.line.indent=e.indentation(),k=e.peek(),e.match("//"))return e.skipToEnd(),["comment","comment"];if(e.match("/*"))return t.tokenize=S,S(e,t);if('"'==k||"'"==k)return e.next(),t.tokenize=X(k),t.tokenize(e,t);if("@"==k)return e.next(),e.eatWhile(/[\w\\-]/),["def",e.current()];if("#"==k){if(e.next(),e.match(/^[0-9a-f]{3}([0-9a-f]([0-9a-f]{2}){0,2})?\b(?!-)/i))return["atom","atom"];if(e.match(/^[a-z][\w-]*/i))return["builtin","hash"]}return e.match(R)?["meta","vendor-prefixes"]:e.match(/^-?[0-9]?\.?[0-9]/)?(e.eatWhile(/[a-z%]/i),["number","unit"]):"!"==k?(e.next(),[e.match(/^(important|optional)/i)?"keyword":"operator","important"]):"."==k&&e.match(/^\.[a-z][\w-]*/i)?["qualifier","qualifier"]:e.match(U)?("("==e.peek()&&(t.tokenize=Z),["property","word"]):e.match(/^[a-z][\w-]*\(/i)?(e.backUp(1),["keyword","mixin"]):e.match(/^(\+|-)[a-z][\w-]*\(/i)?(e.backUp(1),["keyword","block-mixin"]):e.string.match(/^\s*&/)&&e.match(/^[-_]+[a-z][\w-]*/)?["qualifier","qualifier"]:e.match(/^(\/|&)(-|_|:|\.|#|[a-z])/)?(e.backUp(1),["variableName.special","reference"]):e.match(/^&{1}\s*$/)?["variableName.special","reference"]:e.match(N)?["operator","operator"]:e.match(/^\$?[-_]*[a-z0-9]+[\w-]*/i)?e.match(/^(\.|\[)[\w-\'\"\]]+/i,!1)&&!G(e.current())?(e.match("."),["variable","variable-name"]):["variable","word"]:e.match(E)?["operator",e.current()]:/[:;,{}\[\]\(\)]/.test(k)?(e.next(),[null,k]):(e.next(),[null,null])}function S(e,t){for(var r,i=!1;null!=(r=e.next());){if(i&&"/"==r){t.tokenize=null;break}i="*"==r}return["comment","comment"]}function X(e){return function(t,r){for(var i,a=!1;null!=(i=t.next());){if(i==e&&!a){")"==e&&t.backUp(1);break}a=!a&&"\\"==i}return(i==e||!a&&")"!=e)&&(r.tokenize=null),["string","string"]}}function Z(e,t){return e.next(),e.match(/\s*[\"\')]/,!1)?t.tokenize=null:t.tokenize=X(")"),[null,"("]}function I(e,t,r,i){this.type=e,this.indent=t,this.prev=r,this.line=i||{firstWord:"",indent:0}}function J(e,t,r,i){return i=i>=0?i:t.indentUnit,e.context=new I(r,t.indentation()+i,e.context),r}function D(e,t,r){var i=e.context.indent-t.indentUnit;return r=r||!1,e.context=e.context.prev,r&&(e.context.indent=i),e.context.type}function F(e,t,r,i){for(var a=i||1;a>0;a--)r.context=r.context.prev;return function(e,t,r){return Y[r.context.type](e,t,r)}(e,t,r)}function G(e){return e.toLowerCase()in x}function H(e){return(e=e.toLowerCase())in q||e in L}function K(e){return e.toLowerCase()in O}function M(e){return e.toLowerCase().match(R)}function Q(e){var t=e.toLowerCase(),r="variable";return G(e)?r="tag":K(e)?r="block-keyword":H(e)?r="property":t in j||t in T?r="atom":"return"==t||t in _?r="keyword":e.match(/^[A-Z]/)&&(r="string"),r}function V(e,t){return ie(t)&&("{"==e||"]"==e||"hash"==e||"qualifier"==e)||"block-mixin"==e}function ee(e,t){return"{"==e&&t.match(/^\s*\$?[\w-]+/i,!1)}function te(e,t){return":"==e&&t.match(/^[a-z-]+/,!1)}function re(e){return e.sol()||e.string.match(new RegExp("^\\s*"+e.current().replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&")))}function ie(e){return e.eol()||e.match(/^\s*$/,!1)}function ae(e){var t=/^\s*[-_]*[a-z0-9]+[\w-]*/i,r="string"==typeof e?e.match(t):e.string.match(t);return r?r[0].replace(/^\s*/,""):""}Y.block=function(e,t,r){if("comment"==e&&re(t)||","==e&&ie(t)||"mixin"==e)return J(r,t,"block",0);if(ee(e,t))return J(r,t,"interpolation");if(ie(t)&&"]"==e&&!/^\s*(\.|#|:|\[|\*|&)/.test(t.string)&&!G(ae(t)))return J(r,t,"block",0);if(V(e,t))return J(r,t,"block");if("}"==e&&ie(t))return J(r,t,"block",0);if("variable-name"==e)return t.string.match(/^\s?\$[\w-\.\[\]\'\"]+$/)||K(ae(t))?J(r,t,"variableName"):J(r,t,"variableName",0);if("="==e)return ie(t)||K(ae(t))?J(r,t,"block"):J(r,t,"block",0);if("*"==e&&(ie(t)||t.match(/\s*(,|\.|#|\[|:|{)/,!1)))return v="tag",J(r,t,"block");if(te(e,t))return J(r,t,"pseudo");if(/@(font-face|media|supports|(-moz-)?document)/.test(e))return J(r,t,ie(t)?"block":"atBlock");if(/@(-(moz|ms|o|webkit)-)?keyframes$/.test(e))return J(r,t,"keyframes");if(/@extends?/.test(e))return J(r,t,"extend",0);if(e&&"@"==e.charAt(0))return t.indentation()>0&&H(t.current().slice(1))?(v="variable","block"):/(@import|@require|@charset)/.test(e)?J(r,t,"block",0):J(r,t,"block");if("reference"==e&&ie(t))return J(r,t,"block");if("("==e)return J(r,t,"parens");if("vendor-prefixes"==e)return J(r,t,"vendorPrefixes");if("word"==e){var i=t.current();if("property"==(v=Q(i)))return re(t)?J(r,t,"block",0):(v="atom","block");if("tag"==v){if(/embed|menu|pre|progress|sub|table/.test(i)&&H(ae(t)))return v="atom","block";if(t.string.match(new RegExp("\\[\\s*"+i+"|"+i+"\\s*\\]")))return v="atom","block";if(z.test(i)&&(re(t)&&t.string.match(/=/)||!re(t)&&!t.string.match(/^(\s*\.|#|\&|\[|\/|>|\*)/)&&!G(ae(t))))return v="variable",K(ae(t))?"block":J(r,t,"block",0);if(ie(t))return J(r,t,"block")}if("block-keyword"==v)return v="keyword",t.current(/(if|unless)/)&&!re(t)?"block":J(r,t,"block");if("return"==i)return J(r,t,"block",0);if("variable"==v&&t.string.match(/^\s?\$[\w-\.\[\]\'\"]+$/))return J(r,t,"block")}return r.context.type},Y.parens=function(e,t,r){if("("==e)return J(r,t,"parens");if(")"==e)return"parens"==r.context.prev.type?D(r,t):t.string.match(/^[a-z][\w-]*\(/i)&&ie(t)||K(ae(t))||/(\.|#|:|\[|\*|&|>|~|\+|\/)/.test(ae(t))||!t.string.match(/^-?[a-z][\w-\.\[\]\'\"]*\s*=/)&&G(ae(t))?J(r,t,"block"):t.string.match(/^[\$-]?[a-z][\w-\.\[\]\'\"]*\s*=/)||t.string.match(/^\s*(\(|\)|[0-9])/)||t.string.match(/^\s+[a-z][\w-]*\(/i)||t.string.match(/^\s+[\$-]?[a-z]/i)?J(r,t,"block",0):ie(t)?J(r,t,"block"):J(r,t,"block",0);if(e&&"@"==e.charAt(0)&&H(t.current().slice(1))&&(v="variable"),"word"==e){var i=t.current();"tag"==(v=Q(i))&&z.test(i)&&(v="variable"),"property"!=v&&"to"!=i||(v="atom")}return"variable-name"==e?J(r,t,"variableName"):te(e,t)?J(r,t,"pseudo"):r.context.type},Y.vendorPrefixes=function(e,t,r){return"word"==e?(v="property",J(r,t,"block",0)):D(r,t)},Y.pseudo=function(e,t,r){return H(ae(t.string))?F(e,t,r):(t.match(/^[a-z-]+/),v="variableName.special",ie(t)?J(r,t,"block"):D(r,t))},Y.atBlock=function(e,t,r){if("("==e)return J(r,t,"atBlock_parens");if(V(e,t))return J(r,t,"block");if(ee(e,t))return J(r,t,"interpolation");if("word"==e){var i=t.current().toLowerCase();if("tag"==(v=/^(only|not|and|or)$/.test(i)?"keyword":P.hasOwnProperty(i)?"tag":C.hasOwnProperty(i)?"attribute":B.hasOwnProperty(i)?"property":$.hasOwnProperty(i)?"string.special":Q(t.current()))&&ie(t))return J(r,t,"block")}return"operator"==e&&/^(not|and|or)$/.test(t.current())&&(v="keyword"),r.context.type},Y.atBlock_parens=function(e,t,r){if("{"==e||"}"==e)return r.context.type;if(")"==e)return ie(t)?J(r,t,"block"):J(r,t,"atBlock");if("word"==e){var i=t.current().toLowerCase();return v=Q(i),/^(max|min)/.test(i)&&(v="property"),"tag"==v&&(v=z.test(i)?"variable":"atom"),r.context.type}return Y.atBlock(e,t,r)},Y.keyframes=function(e,t,r){return"0"==t.indentation()&&("}"==e&&re(t)||"]"==e||"hash"==e||"qualifier"==e||G(t.current()))?F(e,t,r):"{"==e?J(r,t,"keyframes"):"}"==e?re(t)?D(r,t,!0):J(r,t,"keyframes"):"unit"==e&&/^[0-9]+\%$/.test(t.current())?J(r,t,"keyframes"):"word"==e&&"block-keyword"==(v=Q(t.current()))?(v="keyword",J(r,t,"keyframes")):/@(font-face|media|supports|(-moz-)?document)/.test(e)?J(r,t,ie(t)?"block":"atBlock"):"mixin"==e?J(r,t,"block",0):r.context.type},Y.interpolation=function(e,t,r){return"{"==e&&D(r,t)&&J(r,t,"block"),"}"==e?t.string.match(/^\s*(\.|#|:|\[|\*|&|>|~|\+|\/)/i)||t.string.match(/^\s*[a-z]/i)&&G(ae(t))?J(r,t,"block"):!t.string.match(/^(\{|\s*\&)/)||t.match(/\s*[\w-]/,!1)?J(r,t,"block",0):J(r,t,"block"):"variable-name"==e?J(r,t,"variableName",0):("word"==e&&"tag"==(v=Q(t.current()))&&(v="atom"),r.context.type)},Y.extend=function(e,t,r){return"["==e||"="==e?"extend":"]"==e?D(r,t):"word"==e?(v=Q(t.current()),"extend"):D(r,t)},Y.variableName=function(e,t,r){return"string"==e||"["==e||"]"==e||t.current().match(/^(\.|\$)/)?(t.current().match(/^\.[\w-]+/i)&&(v="variable"),"variableName"):F(e,t,r)};const oe={name:"stylus",startState:function(){return{tokenize:null,state:"block",context:new I("block",0,null)}},token:function(e,t){return!t.tokenize&&e.eatSpace()?null:((w=(t.tokenize||W)(e,t))&&"object"==typeof w&&(y=w[1],w=w[0]),v=w,t.state=Y[t.state](y,e,t),v)},indent:function(e,t,r){var i=e.context,a=t&&t.charAt(0),o=i.indent,n=ae(t),l=r.lineIndent(r.pos),s=e.context.prev?e.context.prev.line.firstWord:"",c=e.context.prev?e.context.prev.line.indent:l;return i.prev&&("}"==a&&("block"==i.type||"atBlock"==i.type||"keyframes"==i.type)||")"==a&&("parens"==i.type||"atBlock_parens"==i.type)||"{"==a&&"at"==i.type)?o=i.indent-r.unit:/(\})/.test(a)||(/@|\$|\d/.test(a)||/^\{/.test(t)||/^\s*\/(\/|\*)/.test(t)||/^\s*\/\*/.test(s)||/^\s*[\w-\.\[\]\'\"]+\s*(\?|:|\+)?=/i.test(t)||/^(\+|-)?[a-z][\w-]*\(/i.test(t)||/^return/.test(t)||K(n)?o=l:/(\.|#|:|\[|\*|&|>|~|\+|\/)/.test(a)||G(n)?o=/\,\s*$/.test(s)?c:e.sol()||!/(\.|#|:|\[|\*|&|>|~|\+|\/)/.test(s)&&!G(s)?l:l<=c?c:c+r.unit:/,\s*$/.test(t)||!M(n)&&!H(n)||(o=K(s)?l<=c?c:c+r.unit:/^\{/.test(s)?l<=c?l:c+r.unit:M(s)||H(s)?l>=c?c:l:/^(\.|#|:|\[|\*|&|@|\+|\-|>|~|\/)/.test(s)||/=\s*$/.test(s)||G(s)||/^\$[\w-\.\[\]\'\"]/.test(s)?c+r.unit:l)),o},languageData:{indentOnInput:/^\s*\}$/,commentTokens:{line:"//",block:{open:"/*",close:"*/"}},autocomplete:b}}}}]);
"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[2479],{72479:(e,t,s)=>{s.r(t),s.d(t,{AttachmentsCell:()=>ge,AttachmentsCellModel:()=>I,Cell:()=>ue,CellDragUtils:()=>l,CellFooter:()=>v,CellHeader:()=>_,CellModel:()=>H,CellSearchProvider:()=>B,CodeCell:()=>pe,CodeCellLayout:()=>ce,CodeCellModel:()=>k,Collapser:()=>p,InputArea:()=>w,InputCollapser:()=>g,InputPlaceholder:()=>W,InputPrompt:()=>f,MarkdownCell:()=>me,MarkdownCellModel:()=>j,OutputCollapser:()=>m,OutputPlaceholder:()=>T,Placeholder:()=>F,RawCell:()=>Ce,RawCellModel:()=>D,SELECTED_HIGHLIGHT_CLASS:()=>z,createCellSearchProvider:()=>J,isCodeCellModel:()=>b,isMarkdownCellModel:()=>A,isRawCellModel:()=>E});var i=s(24475);const n="jp-dragImage",r="jp-dragImage-singlePrompt",o="jp-dragImage-content",a="jp-dragImage-prompt",d="jp-dragImage-multipleBack";var l;!function(e){e.findCell=function(e,t,s){let i=-1;for(;e&&e.parentElement;){if(s(e)){let s=-1;for(const n of t)if(n.node===e){i=++s;break}break}e=e.parentElement}return i},e.detectTargetArea=function(e,t){var s,i;let n;return n=e?(null===(s=e.editorWidget)||void 0===s?void 0:s.node.contains(t))?"input":(null===(i=e.promptNode)||void 0===i?void 0:i.contains(t))?"prompt":"cell":"unknown",n},e.shouldStartDrag=function(e,t,s,i){const n=Math.abs(s-e),r=Math.abs(i-t);return n>=5||r>=5},e.createCellDragImage=function(e,t){const s=t.length;let l;if("code"===e.model.type){const t=e.model.executionCount;l=" ",t&&(l=t.toString())}else l="";const h=e.model.sharedModel.getSource().split("\n")[0].slice(0,26);return s>1?""!==l?i.VirtualDOM.realize(i.h.div(i.h.div({className:n},i.h.span({className:a},"["+l+"]:"),i.h.span({className:o},h)),i.h.div({className:d},""))):i.VirtualDOM.realize(i.h.div(i.h.div({className:n},i.h.span({className:a}),i.h.span({className:o},h)),i.h.div({className:d},""))):""!==l?i.VirtualDOM.realize(i.h.div(i.h.div({className:`${n} ${r}`},i.h.span({className:a},"["+l+"]:"),i.h.span({className:o},h)))):i.VirtualDOM.realize(i.h.div(i.h.div({className:`${n} ${r}`},i.h.span({className:a}),i.h.span({className:o},h))))}}(l||(l={}));var h=s(68239),u=s(18395),c=s(78156);class p extends h.ReactWidget{constructor(){super(),this.addClass("jp-Collapser")}get collapsed(){return!1}render(){return c.createElement("div",{className:"jp-Collapser-child",onClick:e=>this.handleClick(e)})}}class g extends p{constructor(){super(),this.addClass("jp-InputCollapser")}get collapsed(){var e;const t=null===(e=this.parent)||void 0===e?void 0:e.parent;return!!t&&t.inputHidden}handleClick(e){var t;const s=null===(t=this.parent)||void 0===t?void 0:t.parent;s&&(s.inputHidden=!s.inputHidden),this.update()}}class m extends p{constructor(){super(),this.addClass("jp-OutputCollapser")}get collapsed(){var e;const t=null===(e=this.parent)||void 0===e?void 0:e.parent;return!!t&&t.outputHidden}handleClick(e){var t,s;const i=null===(t=this.parent)||void 0===t?void 0:t.parent;if(i&&(i.outputHidden=!i.outputHidden,i.outputHidden)){let e=null===(s=i.parent)||void 0===s?void 0:s.node;e&&u.ElementExt.scrollIntoViewIfNeeded(e,i.node)}this.update()}}var C=s(31516);class _ extends C.Widget{constructor(){super(),this.addClass("jp-CellHeader")}}class v extends C.Widget{constructor(){super(),this.addClass("jp-CellFooter")}}var y=s(31536);class w extends C.Widget{constructor(e){super(),this.addClass("jp-InputArea");const{contentFactory:t,editorOptions:s,model:i}=e;this.model=i,this.contentFactory=t;const n=this._prompt=t.createInputPrompt();n.addClass("jp-InputArea-prompt");const r=this._editor=new y.CodeEditorWrapper({factory:t.editorFactory,model:i,editorOptions:s});r.addClass("jp-InputArea-editor");const o=this.layout=new C.PanelLayout;o.addWidget(n),o.addWidget(r)}get editorWidget(){return this._editor}get editor(){return this._editor.editor}get promptNode(){return this._prompt.node}get renderedInput(){return this._rendered}renderInput(e){const t=this.layout;this._rendered&&(this._rendered.parent=null),this._editor.hide(),this._rendered=e,t.addWidget(e)}showEditor(){this._rendered&&(this._rendered.parent=null),this._editor.show()}setPrompt(e){this._prompt.executionCount=e}dispose(){this.isDisposed||(this._prompt=null,this._editor=null,this._rendered=null,super.dispose())}}!function(e){e.ContentFactory=class{constructor(e){this._editor=e.editorFactory}get editorFactory(){return this._editor}createInputPrompt(){return new f}}}(w||(w={}));class f extends C.Widget{constructor(){super(),this._executionCount=null,this.addClass("jp-InputPrompt")}get executionCount(){return this._executionCount}set executionCount(e){this._executionCount=e,this.node.textContent=null===e?" ":`[${e||" "}]:`}}var x=s(81997),M=s(85207),P=s(51099),O=s(23781);const S=(0,O.createMutex)();function b(e){return"code"===e.type}function A(e){return"markdown"===e.type}function E(e){return"raw"===e.type}class H extends y.CodeEditor.Model{constructor(e={}){const{cell_type:t,sharedModel:s,...i}=e;super({sharedModel:null!=s?s:(0,O.createStandaloneCell)({cell_type:null!=t?t:"raw",id:e.id}),...i}),this.contentChanged=new x.Signal(this),this.stateChanged=new x.Signal(this),this._metadataChanged=new x.Signal(this),this._trusted=!1,this.standaloneModel=void 0===e.sharedModel,this.trusted=!!this.getMetadata("trusted")||!!e.trusted,this.sharedModel.changed.connect(this.onGenericChange,this),this.sharedModel.metadataChanged.connect(this._onMetadataChanged,this)}get metadataChanged(){return this._metadataChanged}get id(){return this.sharedModel.getId()}get metadata(){return this.sharedModel.metadata}get trusted(){return this._trusted}set trusted(e){const t=this.trusted;t!==e&&(this._trusted=e,this.onTrustedChanged(this,{newValue:e,oldValue:t}))}dispose(){this.isDisposed||(this.sharedModel.changed.disconnect(this.onGenericChange,this),this.sharedModel.metadataChanged.disconnect(this._onMetadataChanged,this),super.dispose())}onTrustedChanged(e,t){}deleteMetadata(e){return this.sharedModel.deleteMetadata(e)}getMetadata(e){return this.sharedModel.getMetadata(e)}setMetadata(e,t){void 0===t?this.sharedModel.deleteMetadata(e):this.sharedModel.setMetadata(e,t)}toJSON(){return this.sharedModel.toJSON()}onGenericChange(){this.contentChanged.emit(void 0)}_onMetadataChanged(e,t){this._metadataChanged.emit(t)}}class I extends H{constructor(e){var t;super(e);const s=null!==(t=e.contentFactory)&&void 0!==t?t:I.defaultContentFactory,i=this.sharedModel.getAttachments();this._attachments=s.createAttachmentsModel({values:i}),this._attachments.stateChanged.connect(this.onGenericChange,this),this._attachments.changed.connect(this._onAttachmentsChange,this),this.sharedModel.changed.connect(this._onSharedModelChanged,this)}get attachments(){return this._attachments}dispose(){this.isDisposed||(this._attachments.stateChanged.disconnect(this.onGenericChange,this),this._attachments.changed.disconnect(this._onAttachmentsChange,this),this._attachments.dispose(),this.sharedModel.changed.disconnect(this._onSharedModelChanged,this),super.dispose())}toJSON(){return super.toJSON()}_onAttachmentsChange(e,t){const s=this.sharedModel;S((()=>s.setAttachments(e.toJSON())))}_onSharedModelChanged(e,t){if(t.attachmentsChange){const e=this.sharedModel;S((()=>{var t;return this._attachments.fromJSON(null!==(t=e.getAttachments())&&void 0!==t?t:{})}))}}}!function(e){class t{createAttachmentsModel(e){return new M.AttachmentsModel(e)}}e.ContentFactory=t,e.defaultContentFactory=new t}(I||(I={}));class D extends I{constructor(e={}){super({cell_type:"raw",...e})}get type(){return"raw"}toJSON(){return super.toJSON()}}class j extends I{constructor(e={}){super({cell_type:"markdown",...e}),this.mimeType="text/x-ipythongfm"}get type(){return"markdown"}toJSON(){return super.toJSON()}}class k extends H{constructor(e={}){var t;super({cell_type:"code",...e}),this._executedCode="",this._isDirty=!1;const s=null!==(t=null==e?void 0:e.contentFactory)&&void 0!==t?t:k.defaultContentFactory,i=this.trusted,n=this.sharedModel.getOutputs();this._outputs=s.createOutputArea({trusted:i,values:n}),this.sharedModel.changed.connect(this._onSharedModelChanged,this),this._outputs.changed.connect(this.onGenericChange,this),this._outputs.changed.connect(this.onOutputsChange,this)}get type(){return"code"}get executionCount(){return this.sharedModel.execution_count||null}set executionCount(e){this.sharedModel.execution_count=e||null}get isDirty(){return this._isDirty}get outputs(){return this._outputs}clearExecution(){this.outputs.clear(),this.executionCount=null,this._setDirty(!1),this.sharedModel.deleteMetadata("execution"),this.trusted=!0}dispose(){this.isDisposed||(this.sharedModel.changed.disconnect(this._onSharedModelChanged,this),this._outputs.changed.disconnect(this.onGenericChange,this),this._outputs.changed.disconnect(this.onOutputsChange,this),this._outputs.dispose(),this._outputs=null,super.dispose())}onTrustedChanged(e,t){const s=t.newValue;if(this._outputs&&(this._outputs.trusted=s),s){const e=this.sharedModel,t=e.getMetadata();t.trusted=!0,e.setMetadata(t)}this.stateChanged.emit({name:"trusted",oldValue:t.oldValue,newValue:s})}toJSON(){return super.toJSON()}onOutputsChange(e,t){const s=this.sharedModel;S((()=>{switch(t.type){case"add":{const e=t.newValues.map((e=>e.toJSON()));s.updateOutputs(t.newIndex,t.newIndex,e);break}case"set":{const e=t.newValues.map((e=>e.toJSON()));s.updateOutputs(t.oldIndex,t.oldIndex+e.length,e);break}case"remove":s.updateOutputs(t.oldIndex,t.oldValues.length);break;default:throw new Error(`Invalid event type: ${t.type}`)}}))}_onSharedModelChanged(e,t){t.outputsChange&&S((()=>{this.outputs.clear(),e.getOutputs().forEach((e=>this._outputs.add(e)))})),t.executionCountChange&&(!t.executionCountChange.newValue||!this.isDirty&&t.executionCountChange.oldValue||this._setDirty(!1),this.stateChanged.emit({name:"executionCount",oldValue:t.executionCountChange.oldValue,newValue:t.executionCountChange.newValue})),t.sourceChange&&null!==this.executionCount&&this._setDirty(this._executedCode!==this.sharedModel.getSource().trim())}_setDirty(e){e||(this._executedCode=this.sharedModel.getSource().trim()),e!==this._isDirty&&(this._isDirty=e,this.stateChanged.emit({name:"isDirty",oldValue:!e,newValue:e}))}}!function(e){class t{createOutputArea(e){return new P.OutputAreaModel(e)}}e.ContentFactory=t,e.defaultContentFactory=new t}(k||(k={}));var N=s(71677);class F extends C.Widget{constructor(e){var t,s,i;const n=document.createElement("div");super({node:n});const r=(null!==(t=e.translator)&&void 0!==t?t:N.nullTranslator).load("jupyterlab"),o=document.createElement("div");o.className=null!==(s=e.promptClass)&&void 0!==s?s:"",n.insertAdjacentHTML("afterbegin",o.outerHTML),this._cell=document.createElement("div"),this._cell.classList.add("jp-Placeholder-content"),this._cell.title=r.__("Click to expand");const a=this._cell.appendChild(document.createElement("div"));a.classList.add("jp-Placeholder-contentContainer"),this._textContent=a.appendChild(document.createElement("span")),this._textContent.className="jp-PlaceholderText",this._textContent.innerText=null!==(i=e.text)&&void 0!==i?i:"",n.appendChild(this._cell),h.ellipsesIcon.element({container:a.appendChild(document.createElement("span")),className:"jp-MoreHorizIcon",elementPosition:"center",height:"auto",width:"32px"}),this.addClass("jp-Placeholder"),this._callback=e.callback}set text(e){this._textContent.innerText=e}get text(){return this._textContent.innerText}onAfterAttach(e){super.onAfterAttach(e),this.node.addEventListener("click",this._callback)}onBeforeDetach(e){this.node.removeEventListener("click",this._callback),super.onBeforeDetach(e)}}class W extends F{constructor(e){super({...e,promptClass:"jp-Placeholder-prompt jp-InputPrompt"}),this.addClass("jp-InputPlaceholder")}}class T extends F{constructor(e){super({...e,promptClass:"jp-Placeholder-prompt jp-OutputPrompt"}),this.addClass("jp-OutputPlaceholder")}}var L=s(52337),R=s(38639),V=s(3486);const z="jp-mod-selected";class B extends L.EditorSearchProvider{constructor(e){super(),this.cell=e,this.cell.inViewport||this.cell.editor||(0,R.signalToPromise)(e.inViewportChanged).then((([,e])=>{e&&this.cmHandler.setEditor(this.editor)}))}get editor(){return this.cell.editor}get model(){return this.cell.model}}class U extends B{constructor(e){super(e),this.currentProviderIndex=-1,this.outputsProvider=[];const t=this.cell.outputArea;this._onOutputsChanged(t,t.widgets.length).catch((e=>{console.error("Failed to initialize search on cell outputs.",e)})),t.outputLengthChanged.connect(this._onOutputsChanged,this),t.disposed.connect((()=>{t.outputLengthChanged.disconnect(this._onOutputsChanged)}),this)}get matchesCount(){return this.isActive?super.matchesCount+this.outputsProvider.reduce(((e,t)=>{var s;return e+(null!==(s=t.matchesCount)&&void 0!==s?s:0)}),0):0}async clearHighlight(){await super.clearHighlight(),await Promise.all(this.outputsProvider.map((e=>e.clearHighlight())))}dispose(){this.isDisposed||(super.dispose(),this.outputsProvider.map((e=>{e.dispose()})),this.outputsProvider.length=0)}async highlightNext(e,t){if(0!==this.matchesCount&&this.isActive){if(-1===this.currentProviderIndex){const e=await super.highlightNext(!0,t);if(e)return this.currentIndex=this.cmHandler.currentIndex,e;this.currentProviderIndex=0}for(;this.currentProviderIndex<this.outputsProvider.length;){const e=this.outputsProvider[this.currentProviderIndex],t=await e.highlightNext(!1);if(t)return this.currentIndex=super.matchesCount+this.outputsProvider.slice(0,this.currentProviderIndex).reduce(((e,t)=>{var s;return e+(null!==(s=t.matchesCount)&&void 0!==s?s:0)}),0)+e.currentMatchIndex,t;this.currentProviderIndex+=1}return this.currentProviderIndex=-1,void(this.currentIndex=null)}this.currentIndex=null}async highlightPrevious(){if(0!==this.matchesCount&&this.isActive){for(null===this.currentIndex&&(this.currentProviderIndex=this.outputsProvider.length-1);this.currentProviderIndex>=0;){const e=this.outputsProvider[this.currentProviderIndex],t=await e.highlightPrevious(!1);if(t)return this.currentIndex=super.matchesCount+this.outputsProvider.slice(0,this.currentProviderIndex).reduce(((e,t)=>{var s;return e+(null!==(s=t.matchesCount)&&void 0!==s?s:0)}),0)+e.currentMatchIndex,t;this.currentProviderIndex-=1}const e=await super.highlightPrevious();return e?(this.currentIndex=this.cmHandler.currentIndex,e):void(this.currentIndex=null)}this.currentIndex=null}async startQuery(e,t){await super.startQuery(e,t),!1!==(null==t?void 0:t.output)&&this.isActive&&await Promise.all(this.outputsProvider.map((t=>t.startQuery(e))))}async endQuery(){var e;await super.endQuery(),!1!==(null===(e=this.filters)||void 0===e?void 0:e.output)&&this.isActive&&await Promise.all(this.outputsProvider.map((e=>e.endQuery())))}async _onOutputsChanged(e,t){var s;this.outputsProvider.forEach((e=>{e.dispose()})),this.outputsProvider.length=0,this.currentProviderIndex=-1,this.outputsProvider=this.cell.outputArea.widgets.map((e=>new V.GenericSearchProvider(e))),this.isActive&&this.query&&!1!==(null===(s=this.filters)||void 0===s?void 0:s.output)&&await Promise.all([this.outputsProvider.map((e=>{e.startQuery(this.query)}))]),this._stateChanged.emit()}}class $ extends B{constructor(e){super(e),this._unrenderedByHighlight=!1,this.renderedProvider=new V.GenericSearchProvider(e.renderer)}async clearHighlight(){await super.clearHighlight(),await this.renderedProvider.clearHighlight()}dispose(){this.isDisposed||(super.dispose(),this.renderedProvider.dispose())}async endQuery(){await super.endQuery(),await this.renderedProvider.endQuery()}async highlightNext(){let e;if(!this.isActive)return e;const t=this.cell;if(t.rendered&&this.matchesCount>0){this._unrenderedByHighlight=!0;const e=(0,R.signalToPromise)(t.renderedChanged);t.rendered=!1,await e}return e=await super.highlightNext(),e}async highlightPrevious(){let e;const t=this.cell;if(t.rendered&&this.matchesCount>0){this._unrenderedByHighlight=!0;const e=(0,R.signalToPromise)(t.renderedChanged);t.rendered=!1,await e}return e=await super.highlightPrevious(),e}async startQuery(e,t){await super.startQuery(e,t);const s=this.cell;s.rendered&&this.onRenderedChanged(s,s.rendered),s.renderedChanged.connect(this.onRenderedChanged,this)}async replaceAllMatches(e){const t=await super.replaceAllMatches(e);return this.cell.rendered&&this.cell.update(),t}onRenderedChanged(e,t){var s;this._unrenderedByHighlight||(this.currentIndex=null),this._unrenderedByHighlight=!1,this.isActive&&(t?this.renderedProvider.startQuery(this.query):(null===(s=e.editor)||void 0===s||s.setCursorPosition({column:0,line:0}),this.renderedProvider.endQuery()))}}function J(e){if(e.isPlaceholder())return new B(e);switch(e.model.type){case"code":return new U(e);case"markdown":return new $(e);default:return new B(e)}}var Q=s(30890),G=s(12982),q=s(70856),X=s(87749),Y=s(20998),K=s(33625),Z=s(49503),ee=s(97934);const te="jp-mod-resizedCell";class se extends C.Widget{constructor(e){super(),this.targetNode=e,this._isActive=!1,this._isDragging=!1,this.sizeChanged=new x.Signal(this),this.addClass("jp-CellResizeHandle"),this._resizer=new ee.Throttler((e=>this._resize(e)),50)}dispose(){this._resizer.dispose(),super.dispose()}handleEvent(e){var t,s;switch(e.type){case"dblclick":null===(t=this.targetNode.parentNode)||void 0===t||t.childNodes.forEach((e=>{e.classList.remove(te)})),document.documentElement.style.setProperty("--jp-side-by-side-output-size","1fr"),this._isActive=!1;break;case"mousedown":this._isDragging=!0,this._isActive||(null===(s=this.targetNode.parentNode)||void 0===s||s.childNodes.forEach((e=>{e.classList.add(te)})),this._isActive=!0),window.addEventListener("mousemove",this),window.addEventListener("mouseup",this);break;case"mousemove":this._isActive&&this._isDragging&&this._resizer.invoke(e);break;case"mouseup":this._isDragging=!1,window.removeEventListener("mousemove",this),window.removeEventListener("mouseup",this)}}onAfterAttach(e){this.node.addEventListener("dblclick",this),this.node.addEventListener("mousedown",this),super.onAfterAttach(e)}onBeforeDetach(e){this.node.removeEventListener("dblclick",this),this.node.removeEventListener("mousedown",this),super.onBeforeDetach(e)}_resize(e){const{width:t,x:s}=this.targetNode.getBoundingClientRect(),i=t/(e.clientX-s)-1;if(0<i){const e=Math.max(Math.min(Math.abs(i),50),.05);document.documentElement.style.setProperty("--jp-side-by-side-output-size",`${e}fr`),this.sizeChanged.emit(e)}}}const ie="jp-Cell-outputWrapper",ne="jp-mod-dirty",re="jp-MarkdownHeadingCollapsed",oe="jp-collapseHeadingButton",ae="jp-showHiddenCellsButton",de="jp-mod-rendered",le="jp-mod-noOutputs",he="application/x-jupyter-icontentsrich";class ue extends C.Widget{constructor(e){var t,s,i,n;super(),this.prompt="",this._displayChanged=new x.Signal(this),this._editorConfig={},this._editorExtensions=[],this._inputHidden=!1,this._inViewportChanged=new x.Signal(this),this._readOnly=!1,this._ready=new Y.PromiseDelegate,this._resizeDebouncer=new ee.Debouncer((()=>{this._displayChanged.emit()}),0),this._syncCollapse=!1,this._syncEditable=!1,this.addClass("jp-Cell");const r=this._model=e.model;this.contentFactory=e.contentFactory,this.layout=null!==(t=e.layout)&&void 0!==t?t:new C.PanelLayout,this.translator=null!==(s=e.translator)&&void 0!==s?s:N.nullTranslator,this._editorConfig={searchWithCM:!1,...e.editorConfig},this._editorExtensions=null!==(i=e.editorExtensions)&&void 0!==i?i:[],this._placeholder=!0,this._inViewport=!1,this.placeholder=null===(n=e.placeholder)||void 0===n||n,r.metadataChanged.connect(this.onMetadataChanged,this)}initializeState(){return this.loadCollapseState(),this.loadEditableState(),this}get displayChanged(){return this._displayChanged}get inViewport(){return this._inViewport}set inViewport(e){this._inViewport!==e&&(this._inViewport=e,this._inViewportChanged.emit(this._inViewport))}get inViewportChanged(){return this._inViewportChanged}get placeholder(){return this._placeholder}set placeholder(e){this._placeholder!==e&&!1===e&&(this.initializeDOM(),this._placeholder=e,this._ready.resolve())}get promptNode(){return this.placeholder?null:this._inputHidden?this._inputPlaceholder.node.firstElementChild:this._input.promptNode}get editorWidget(){var e,t;return null!==(t=null===(e=this._input)||void 0===e?void 0:e.editorWidget)&&void 0!==t?t:null}get editor(){var e,t;return null!==(t=null===(e=this._input)||void 0===e?void 0:e.editor)&&void 0!==t?t:null}get editorConfig(){return this._editorConfig}get headings(){return new Array}get model(){return this._model}get inputArea(){return this._input}get readOnly(){return this._readOnly}set readOnly(e){e!==this._readOnly&&(this._readOnly=e,this.syncEditable&&this.saveEditableState(),this.update())}isPlaceholder(){return this.placeholder}saveEditableState(){const{sharedModel:e}=this.model,t=e.getMetadata("editable");this.readOnly&&!1===t||!this.readOnly&&void 0===t||(this.readOnly?e.setMetadata("editable",!1):e.deleteMetadata("editable"))}loadEditableState(){this.readOnly=!1===this.model.sharedModel.getMetadata("editable")}get ready(){return this._ready.promise}setPrompt(e){var t;this.prompt=e,null===(t=this._input)||void 0===t||t.setPrompt(e)}get inputHidden(){return this._inputHidden}set inputHidden(e){var t;if(this._inputHidden!==e){if(!this.placeholder){const s=this._inputWrapper.layout;e?(this._input.parent=null,this._inputPlaceholder&&(this._inputPlaceholder.text=null===(t=this.model.sharedModel.getSource().split("\n"))||void 0===t?void 0:t[0]),s.addWidget(this._inputPlaceholder)):(this._inputPlaceholder.parent=null,s.addWidget(this._input))}this._inputHidden=e,this.syncCollapse&&this.saveCollapseState(),this.handleInputHidden(e)}}saveCollapseState(){const e={...this.model.getMetadata("jupyter")};this.inputHidden&&!0===e.source_hidden||!this.inputHidden&&void 0===e.source_hidden||(this.inputHidden?e.source_hidden=!0:delete e.source_hidden,0===Object.keys(e).length?this.model.deleteMetadata("jupyter"):this.model.setMetadata("jupyter",e))}loadCollapseState(){var e;const t=null!==(e=this.model.getMetadata("jupyter"))&&void 0!==e?e:{};this.inputHidden=!!t.source_hidden}handleInputHidden(e){}get syncCollapse(){return this._syncCollapse}set syncCollapse(e){this._syncCollapse!==e&&(this._syncCollapse=e,e&&this.loadCollapseState())}get syncEditable(){return this._syncEditable}set syncEditable(e){this._syncEditable!==e&&(this._syncEditable=e,e&&this.loadEditableState())}clone(){return new(0,this.constructor)({model:this.model,contentFactory:this.contentFactory,placeholder:!1,translator:this.translator})}dispose(){this.isDisposed||(this._resizeDebouncer.dispose(),this._input=null,this._model=null,this._inputWrapper=null,this._inputPlaceholder=null,super.dispose())}updateEditorConfig(e){this._editorConfig={...this._editorConfig,...e},this.editor&&this.editor.setOptions(this._editorConfig)}initializeDOM(){if(!this.placeholder)return;const e=this.contentFactory,t=this._model,s=e.createCellHeader();s.addClass("jp-Cell-header"),this.layout.addWidget(s);const i=this._inputWrapper=new C.Panel;i.addClass("jp-Cell-inputWrapper");const n=new g;n.addClass("jp-Cell-inputCollapser");const r=this._input=new w({model:t,contentFactory:e,editorOptions:this.getEditorOptions()});r.addClass("jp-Cell-inputArea"),i.addWidget(n),i.addWidget(r),this.layout.addWidget(i),this._inputPlaceholder=new W({callback:()=>{this.inputHidden=!this.inputHidden},text:r.model.sharedModel.getSource().split("\n")[0],translator:this.translator}),r.model.contentChanged.connect(((e,t)=>{var s;this._inputPlaceholder&&this.inputHidden&&(this._inputPlaceholder.text=null===(s=e.sharedModel.getSource().split("\n"))||void 0===s?void 0:s[0])})),this.inputHidden&&(r.parent=null,i.layout.addWidget(this._inputPlaceholder));const o=this.contentFactory.createCellFooter();o.addClass("jp-Cell-footer"),this.layout.addWidget(o)}getEditorOptions(){return{config:this.editorConfig,extensions:this._editorExtensions}}onBeforeAttach(e){this.placeholder&&(this.placeholder=!1)}onAfterAttach(e){this.update()}onActivateRequest(e){var t;null===(t=this.editor)||void 0===t||t.focus()}onResize(e){this._resizeDebouncer.invoke()}onUpdateRequest(e){var t,s;this._model&&(null===(t=this.editor)||void 0===t?void 0:t.getOption("readOnly"))!==this._readOnly&&(null===(s=this.editor)||void 0===s||s.setOption("readOnly",this._readOnly))}onContentChanged(){var e;this.inputHidden&&this._inputPlaceholder&&(this._inputPlaceholder.text=null===(e=this.model.sharedModel.getSource().split("\n"))||void 0===e?void 0:e[0])}onMetadataChanged(e,t){switch(t.key){case"jupyter":this.syncCollapse&&this.loadCollapseState();break;case"editable":this.syncEditable&&this.loadEditableState()}}}!function(e){let t;!function(e){e[e.HTML=0]="HTML",e[e.Markdown=1]="Markdown"}(t=e.HeadingType||(e.HeadingType={})),e.ContentFactory=class{constructor(e){this._editorFactory=e.editorFactory}get editorFactory(){return this._editorFactory}createCellHeader(){return new _}createCellFooter(){return new v}createInputPrompt(){return new f}createOutputPrompt(){return new P.OutputPrompt}createStdin(e){return new P.Stdin(e)}}}(ue||(ue={}));class ce extends C.PanelLayout{onBeforeAttach(e){let t=!0;const s=this.parent.node.firstElementChild;for(const i of this)s&&(i.node===s?t=!1:(Z.MessageLoop.sendMessage(i,e),t?this.parent.node.insertBefore(i.node,s):this.parent.node.appendChild(i.node),this.parent.isHidden||i.setFlag(C.Widget.Flag.IsVisible),Z.MessageLoop.sendMessage(i,C.Widget.Msg.AfterAttach)))}onAfterDetach(e){for(const t of this)!t.hasClass(ie)&&t.node.isConnected&&(Z.MessageLoop.sendMessage(t,C.Widget.Msg.BeforeDetach),this.parent.node.removeChild(t.node),Z.MessageLoop.sendMessage(t,e))}}class pe extends ue{constructor(e){var t;super({layout:new ce,...e,placeholder:!0}),this._headingsCache=null,this._outputHidden=!1,this._outputWrapper=null,this._outputPlaceholder=null,this._syncScrolled=!1,this.addClass("jp-CodeCell");const s=this.translator.load("jupyterlab"),i=this._rendermime=e.rendermime,n=this.contentFactory,r=this.model;this.maxNumberOutputs=e.maxNumberOutputs;const o=0===r.outputs.length?s.__("Code Cell Content"):s.__("Code Cell Content with Output");this.node.setAttribute("aria-label",o);const a=this._output=new P.OutputArea({model:this.model.outputs,rendermime:i,contentFactory:n,maxNumberOutputs:this.maxNumberOutputs,translator:this.translator,promptOverlay:!0,inputHistoryScope:e.inputHistoryScope});a.addClass("jp-Cell-outputArea"),a.toggleScrolling.connect((()=>{this.outputsScrolled=!this.outputsScrolled})),a.initialize.connect((()=>{this.updatePromptOverlayIcon()})),this.placeholder=null===(t=e.placeholder)||void 0===t||t,r.outputs.changed.connect(this.onOutputChanged,this),r.outputs.stateChanged.connect(this.onOutputChanged,this),r.stateChanged.connect(this.onStateChanged,this)}initializeDOM(){if(!this.placeholder)return;super.initializeDOM(),this.setPrompt(this.prompt);const e=this._outputWrapper=new C.Panel;e.addClass(ie);const t=new m;t.addClass("jp-Cell-outputCollapser"),e.addWidget(t),0===this.model.outputs.length&&this.addClass(le),this._output.outputLengthChanged.connect(this._outputLengthHandler,this),e.addWidget(this._output);const s=this.layout,i=new se(this.node);i.sizeChanged.connect(this._sizeChangedHandler,this),s.insertWidget(s.widgets.length-1,i),s.insertWidget(s.widgets.length-1,e),this.model.isDirty&&this.addClass(ne),this._outputPlaceholder=new T({callback:()=>{this.outputHidden=!this.outputHidden},text:this.getOutputPlaceholderText(),translator:this.translator});const n=e.layout;this.outputHidden&&(n.removeWidget(this._output),n.addWidget(this._outputPlaceholder),this.inputHidden&&!e.isHidden&&this._outputWrapper.hide());const r=this.translator.load("jupyterlab"),o=0===this.model.outputs.length?r.__("Code Cell Content"):r.__("Code Cell Content with Output");this.node.setAttribute("aria-label",o)}getOutputPlaceholderText(){var e;const t=this.model.outputs.get(0);if(!(null==t?void 0:t.data))return;const s=["text/html","image/svg+xml","application/pdf","text/markdown","text/plain","application/vnd.jupyter.stderr","application/vnd.jupyter.stdout","text"].find((e=>{const s=t.data[e];return"string"==(Array.isArray(s)?typeof s[0]:typeof s)})),i=t.data[null!=s?s:""];return void 0!==i?null===(e=Array.isArray(i)?i:null==i?void 0:i.split("\n"))||void 0===e?void 0:e.find((e=>""!==e)):void 0}initializeState(){return super.initializeState(),this.loadScrolledState(),this.setPrompt(`${this.model.executionCount||""}`),this}get headings(){if(!this._headingsCache){const e=[],t=this.model.outputs;for(let s=0;s<t.length;s++){const i=t.get(s);let n=null,r=null;Object.keys(i.data).forEach((e=>{!r&&X.TableOfContentsUtils.Markdown.isMarkdown(e)?r=e:!n&&X.TableOfContentsUtils.isHTML(e)&&(n=e)})),n?e.push(...X.TableOfContentsUtils.getHTMLHeadings(this._rendermime.sanitizer.sanitize(i.data[n])).map((e=>({...e,outputIndex:s,type:ue.HeadingType.HTML})))):r&&e.push(...X.TableOfContentsUtils.Markdown.getHeadings(i.data[r]).map((e=>({...e,outputIndex:s,type:ue.HeadingType.Markdown}))))}this._headingsCache=e}return[...this._headingsCache]}get outputArea(){return this._output}get outputHidden(){return this._outputHidden}set outputHidden(e){var t;if(this._outputHidden!==e){if(!this.placeholder){const s=this._outputWrapper.layout;e?(s.removeWidget(this._output),s.addWidget(this._outputPlaceholder),this.inputHidden&&!this._outputWrapper.isHidden&&this._outputWrapper.hide(),this._outputPlaceholder&&(this._outputPlaceholder.text=null!==(t=this.getOutputPlaceholderText())&&void 0!==t?t:"")):(this._outputWrapper.isHidden&&this._outputWrapper.show(),s.removeWidget(this._outputPlaceholder),s.addWidget(this._output))}this._outputHidden=e,this.syncCollapse&&this.saveCollapseState()}}saveCollapseState(){this.model.sharedModel.transact((()=>{super.saveCollapseState();const e=this.model.getMetadata("collapsed");this.outputHidden&&!0===e||!this.outputHidden&&void 0===e||(this.outputHidden?this.model.setMetadata("collapsed",!0):this.model.deleteMetadata("collapsed"))}),!1)}loadCollapseState(){super.loadCollapseState(),this.outputHidden=!!this.model.getMetadata("collapsed")}get outputsScrolled(){return this._outputsScrolled}set outputsScrolled(e){this.toggleClass("jp-mod-outputsScrolled",e),this._outputsScrolled=e,this.syncScrolled&&this.saveScrolledState(),this.updatePromptOverlayIcon()}updatePromptOverlayIcon(){var e;const t=G.DOMUtils.findElement(this.node,"jp-OutputArea-promptOverlay");if(!t)return;if(t.clientHeight<=24)return void(null===(e=t.firstChild)||void 0===e||e.remove());let s;this._outputsScrolled?(h.expandIcon.element({container:t}),s="Expand Output"):(h.collapseIcon.element({container:t}),s="Collapse Output");const i=this.translator.load("jupyterlab");t.title=i.__(s)}saveScrolledState(){const e=this.model.getMetadata("scrolled");this.outputsScrolled&&!0===e||!this.outputsScrolled&&void 0===e||(this.outputsScrolled?this.model.setMetadata("scrolled",!0):this.model.deleteMetadata("scrolled"))}loadScrolledState(){"auto"===this.model.getMetadata("scrolled")?this.outputsScrolled=!1:this.outputsScrolled=!!this.model.getMetadata("scrolled")}get syncScrolled(){return this._syncScrolled}set syncScrolled(e){this._syncScrolled!==e&&(this._syncScrolled=e,e&&this.loadScrolledState())}handleInputHidden(e){this.placeholder||(!e&&this._outputWrapper.isHidden?this._outputWrapper.show():e&&!this._outputWrapper.isHidden&&this._outputHidden&&this._outputWrapper.hide())}clone(){return new(0,this.constructor)({model:this.model,contentFactory:this.contentFactory,rendermime:this._rendermime,placeholder:!1,translator:this.translator})}cloneOutputArea(){return new P.SimplifiedOutputArea({model:this.model.outputs,contentFactory:this.contentFactory,rendermime:this._rendermime})}dispose(){this.isDisposed||(this._output.outputLengthChanged.disconnect(this._outputLengthHandler,this),this._rendermime=null,this._output=null,this._outputWrapper=null,this._outputPlaceholder=null,super.dispose())}onStateChanged(e,t){switch(t.name){case"executionCount":this.setPrompt(`${e.executionCount||""}`);break;case"isDirty":e.isDirty?this.addClass(ne):this.removeClass(ne)}}onOutputChanged(){var e;this._headingsCache=null,this._outputPlaceholder&&this.outputHidden&&(this._outputPlaceholder.text=null!==(e=this.getOutputPlaceholderText())&&void 0!==e?e:""),this.updatePromptOverlayIcon()}onMetadataChanged(e,t){switch(t.key){case"scrolled":this.syncScrolled&&this.loadScrolledState();break;case"collapsed":this.syncCollapse&&this.loadCollapseState()}super.onMetadataChanged(e,t)}_outputLengthHandler(e,t){const s=0===t;this.toggleClass(le,s);const i=this.translator.load("jupyterlab"),n=s?i.__("Code Cell Content"):i.__("Code Cell Content with Output");this.node.setAttribute("aria-label",n)}_sizeChangedHandler(e){this._displayChanged.emit()}}!function(e){e.execute=async function(e,t,s){var i;const n=e.model,r=n.sharedModel.getSource();if(!r.trim()||!(null===(i=t.session)||void 0===i?void 0:i.kernel))return void n.sharedModel.transact((()=>{n.clearExecution()}),!1);const o={cellId:n.sharedModel.getId()};s={...n.metadata,...s,...o};const{recordTiming:a}=s;let d;n.sharedModel.transact((()=>{n.clearExecution(),e.outputHidden=!1}),!1),e.setPrompt("*"),n.trusted=!0;try{const i=P.OutputArea.execute(r,e.outputArea,t,s);if(a){const t=e=>{let t;switch(e.header.msg_type){case"status":t=`status.${e.content.execution_state}`;break;case"execute_input":t="execute_input";break;default:return!0}const s=e.header.date||(new Date).toISOString(),i=Object.assign({},n.getMetadata("execution"));return i[`iopub.${t}`]=s,n.setMetadata("execution",i),!0};e.outputArea.future.registerMessageHook(t)}else n.deleteMetadata("execution");d=e.outputArea.future;const o=await i;if(n.executionCount=o.content.execution_count,a){const e=Object.assign({},n.getMetadata("execution")),t=o.metadata.started;t&&(e["shell.execute_reply.started"]=t);const s=o.header.date;e["shell.execute_reply"]=s||(new Date).toISOString(),n.setMetadata("execution",e)}return o}catch(t){if(d&&!e.isDisposed&&e.outputArea.future===d&&(e.setPrompt(""),a&&d.isDisposed)){const e=Object.assign({},n.getMetadata("execution"));e.execution_failed=(new Date).toISOString(),n.setMetadata("execution",e)}throw t}}}(pe||(pe={}));class ge extends ue{handleEvent(e){switch(e.type){case"lm-dragover":this._evtDragOver(e);break;case"lm-drop":this._evtDrop(e)}}getEditorOptions(){var e,t;const s=null!==(e=super.getEditorOptions())&&void 0!==e?e:{};return s.extensions=[...null!==(t=s.extensions)&&void 0!==t?t:[],Q.EditorView.domEventHandlers({dragenter:e=>{e.preventDefault()},dragover:e=>{e.preventDefault()},drop:e=>{this._evtNativeDrop(e)},paste:e=>{this._evtPaste(e)}})],s}onAfterAttach(e){super.onAfterAttach(e);const t=this.node;t.addEventListener("lm-dragover",this),t.addEventListener("lm-drop",this)}onBeforeDetach(e){const t=this.node;t.removeEventListener("lm-dragover",this),t.removeEventListener("lm-drop",this),super.onBeforeDetach(e)}_evtDragOver(e){(0,K.some)(q.imageRendererFactory.mimeTypes,(t=>!!e.mimeData.hasData(he)&&e.mimeData.getData(he).model.mimetype===t))&&(e.preventDefault(),e.stopPropagation(),e.dropAction=e.proposedAction)}_evtPaste(e){if(e.clipboardData){const t=e.clipboardData.items;for(let s=0;s<t.length;s++){if("text/plain"===t[s].type){if(s<t.length-1&&"file"===t[s+1].kind)continue;t[s].getAsString((e=>{var t,s;null===(s=(t=this.editor).replaceSelection)||void 0===s||s.call(t,e.replace(/\r\n/g,"\n").replace(/\r/g,"\n"))}))}this._attachFiles(e.clipboardData.items)}}e.preventDefault()}_evtNativeDrop(e){e.dataTransfer&&this._attachFiles(e.dataTransfer.items),e.preventDefault()}_evtDrop(e){const t=e.mimeData.types().filter((t=>{if(t===he){const t=e.mimeData.getData(he);return-1!==q.imageRendererFactory.mimeTypes.indexOf(t.model.mimetype)}return-1!==q.imageRendererFactory.mimeTypes.indexOf(t)}));if(0!==t.length)if(e.preventDefault(),e.stopPropagation(),"none"!==e.proposedAction){e.dropAction="copy";for(const s of t)if(s===he){const{model:t,withContent:s}=e.mimeData.getData(he);if("file"===t.type){const e=this._generateURI(t.name);this.updateCellSourceWithAttachment(t.name,e),s().then((t=>{this.model.attachments.set(e,{[t.mimetype]:t.content})}))}}else{const t=this._generateURI();this.model.attachments.set(t,{[s]:e.mimeData.getData(s)}),this.updateCellSourceWithAttachment(t,t)}}else e.dropAction="none"}_attachFiles(e){for(let t=0;t<e.length;t++){const s=e[t];if("file"===s.kind){const e=s.getAsFile();e&&this._attachFile(e)}}}_attachFile(e){const t=new FileReader;t.onload=s=>{const{href:i,protocol:n}=R.URLExt.parse(t.result);if("data:"!==n)return;const r=/([\w+\/\+]+)?(?:;(charset=[\w\d-]*|base64))?,(.*)/.exec(i);if(!r||4!==r.length)return;const o=r[1],a={[o]:r[3]},d=this._generateURI(e.name);o.startsWith("image/")&&(this.model.attachments.set(d,a),this.updateCellSourceWithAttachment(e.name,d))},t.onerror=t=>{console.error(`Failed to attach ${e.name}`+t)},t.readAsDataURL(e)}_generateURI(e=""){const t=e.lastIndexOf(".");return-1!==t?Y.UUID.uuid4().concat(e.substring(t)):Y.UUID.uuid4()}}class me extends ge{constructor(e){var t,s,i,n;super({...e,placeholder:!0}),this._headingsCache=null,this._headingCollapsedChanged=new x.Signal(this),this._prevText="",this._rendered=!0,this._renderedChanged=new x.Signal(this),this._showEditorForReadOnlyMarkdown=!0,this.addClass("jp-MarkdownCell"),this.model.contentChanged.connect(this.onContentChanged,this);const r=this.translator.load("jupyterlab");this.node.setAttribute("aria-label",r.__("Markdown Cell Content")),this._rendermime=e.rendermime.clone({resolver:new M.AttachmentsResolver({parent:null!==(t=e.rendermime.resolver)&&void 0!==t?t:void 0,model:this.model.attachments})}),this._renderer=this._rendermime.createRenderer("text/markdown"),this._renderer.addClass("jp-MarkdownOutput"),this._headingCollapsed=null!==(s=this.model.getMetadata(re))&&void 0!==s&&s,this._showEditorForReadOnlyMarkdown=null!==(i=e.showEditorForReadOnlyMarkdown)&&void 0!==i?i:me.defaultShowEditorForReadOnlyMarkdown,this.placeholder=null===(n=e.placeholder)||void 0===n||n,this._monitor=new R.ActivityMonitor({signal:this.model.contentChanged,timeout:1e3}),this.ready.then((()=>{this.isDisposed||this._monitor.activityStopped.connect((()=>{this._rendered&&this.update()}),this)})).catch((e=>{console.error("Failed to be ready",e)}))}get headingInfo(){const e=this.headings;if(e.length>0){const{text:t,level:s}=e.reduce(((e,t)=>e.level<=t.level?e:t),e[0]);return{text:t,level:s}}return{text:"",level:-1}}get headings(){if(!this._headingsCache){const e=X.TableOfContentsUtils.Markdown.getHeadings(this.model.sharedModel.getSource());this._headingsCache=e.map((e=>({...e,type:ue.HeadingType.Markdown})))}return[...this._headingsCache]}get headingCollapsed(){return this._headingCollapsed}set headingCollapsed(e){var t;if(this._headingCollapsed!==e){this._headingCollapsed=e,e?this.model.setMetadata(re,e):"undefined"!==this.model.getMetadata(re)&&this.model.deleteMetadata(re);const s=null===(t=this.inputArea)||void 0===t?void 0:t.promptNode.getElementsByClassName(oe)[0];s&&(e?s.classList.add("jp-mod-collapsed"):s.classList.remove("jp-mod-collapsed")),this.renderCollapseButtons(this._renderer),this._headingCollapsedChanged.emit(this._headingCollapsed)}}get numberChildNodes(){return this._numberChildNodes}set numberChildNodes(e){this._numberChildNodes=e,this.renderCollapseButtons(this._renderer)}get headingCollapsedChanged(){return this._headingCollapsedChanged}get rendered(){return this._rendered}set rendered(e){this.readOnly&&!1===this._showEditorForReadOnlyMarkdown&&(e=!0),e!==this._rendered&&(this._rendered=e,this._handleRendered().then((()=>{this._displayChanged.emit(),this._renderedChanged.emit(this._rendered)})).catch((e=>{console.error("Failed to render",e)})))}get renderedChanged(){return this._renderedChanged}get showEditorForReadOnly(){return this._showEditorForReadOnlyMarkdown}set showEditorForReadOnly(e){this._showEditorForReadOnlyMarkdown=e,!1===e&&(this.rendered=!0)}get renderer(){return this._renderer}dispose(){this.isDisposed||(this._monitor.dispose(),super.dispose())}initializeDOM(){this.placeholder&&(super.initializeDOM(),this.renderCollapseButtons(this._renderer),this._handleRendered().catch((e=>{console.error("Failed to render",e)})))}maybeCreateCollapseButton(){var e;const{level:t}=this.headingInfo;if(t>0&&0==(null===(e=this.inputArea)||void 0===e?void 0:e.promptNode.getElementsByClassName(oe).length)){let e=this.inputArea.promptNode.appendChild(document.createElement("button"));e.className=`jp-Button ${oe}`,e.setAttribute("data-heading-level",t.toString()),this._headingCollapsed?e.classList.add("jp-mod-collapsed"):e.classList.remove("jp-mod-collapsed"),e.onclick=e=>{this.headingCollapsed=!this.headingCollapsed}}}maybeCreateOrUpdateExpandButton(){const e=this.node.getElementsByClassName(ae);let t=this.translator.load("jupyterlab")._n("%1 cell hidden","%1 cells hidden",this._numberChildNodes);if(this.headingCollapsed&&this._numberChildNodes>0&&0==e.length){const e=document.createElement("button");e.className=`jp-mod-minimal jp-Button ${ae}`,h.addIcon.render(e);const s=document.createElement("div");s.textContent=t,e.appendChild(s),e.onclick=()=>{this.headingCollapsed=!1},this.node.appendChild(e)}if(this.headingCollapsed&&this._numberChildNodes>0&&1==e.length&&(e[0].childNodes[1].textContent=t),!(this.headingCollapsed&&this._numberChildNodes>0))for(const t of e)this.node.removeChild(t)}onContentChanged(){super.onContentChanged(),this._headingsCache=null}renderCollapseButtons(e){this.node.classList.toggle(re,this._headingCollapsed),this.maybeCreateCollapseButton(),this.maybeCreateOrUpdateExpandButton()}renderInput(e){this.addClass(de),this.placeholder||this.isDisposed||(this.renderCollapseButtons(e),this.inputArea.renderInput(e))}showEditor(){if(this.removeClass(de),!this.placeholder&&!this.isDisposed){this.inputArea.showEditor();let e=(this.model.sharedModel.getSource().match(/^#+/g)||[""])[0].length;e>0&&this.inputArea.editor.setCursorPosition({column:e+1,line:0},{scroll:!1})}}onUpdateRequest(e){this._handleRendered().catch((e=>{console.error("Failed to render",e)})),super.onUpdateRequest(e)}updateCellSourceWithAttachment(e,t){var s,i;const n=`![${e}](attachment:${null!=t?t:e})`;null===(i=null===(s=this.editor)||void 0===s?void 0:s.replaceSelection)||void 0===i||i.call(s,n)}async _handleRendered(){this._rendered?(await this._updateRenderedInput(),this._rendered&&this.renderInput(this._renderer)):this.showEditor()}_updateRenderedInput(){if(this.placeholder)return Promise.resolve();const e=this.model,t=e&&e.sharedModel.getSource()||"Type Markdown and LaTeX: $ α^2 $";if(t!==this._prevText){const e=new q.MimeModel({data:{"text/markdown":t}});return this._prevText=t,this._renderer.renderModel(e)}return Promise.resolve()}clone(){return new(0,this.constructor)({model:this.model,contentFactory:this.contentFactory,rendermime:this._rendermime,placeholder:!1,translator:this.translator})}}!function(e){e.defaultShowEditorForReadOnlyMarkdown=!0}(me||(me={}));class Ce extends ue{constructor(e){super(e),this.addClass("jp-RawCell");const t=this.translator.load("jupyterlab");this.node.setAttribute("aria-label",t.__("Raw Cell Content"))}clone(){return new(0,this.constructor)({model:this.model,contentFactory:this.contentFactory,placeholder:!1,translator:this.translator})}}}}]);
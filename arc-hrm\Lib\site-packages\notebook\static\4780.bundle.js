"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[4780],{14780:(e,n,t)=>{t.d(n,{a:()=>cn,c:()=>un});var r={};t.r(r),t.d(r,{attentionMarkers:()=>Le,contentInitial:()=>Ce,disable:()=>Pe,document:()=>we,flow:()=>ze,flowInitial:()=>Te,insideSpan:()=>De,string:()=>_e,text:()=>Be});var i=t(24028);const u={};function o(e,n,t){if(function(e){return Boolean(e&&"object"==typeof e)}(e)){if("value"in e)return"html"!==e.type||t?e.value:"";if(n&&"alt"in e&&e.alt)return e.alt;if("children"in e)return c(e.children,n,t)}return Array.isArray(e)?c(e,n,t):""}function c(e,n,t){const r=[];let i=-1;for(;++i<e.length;)r[i]=o(e[i],n,t);return r.join("")}function s(e,n,t,r){const i=e.length;let u,o=0;if(n=n<0?-n>i?0:i+n:n>i?i:n,t=t>0?t:0,r.length<1e4)u=Array.from(r),u.unshift(n,t),e.splice(...u);else for(t&&e.splice(n,t);o<r.length;)u=r.slice(o,o+1e4),u.unshift(n,0),e.splice(...u),o+=1e4,n+=1e4}function l(e,n){return e.length>0?(s(e,e.length,0,n),e):n}const a={}.hasOwnProperty;function f(e,n){let t;for(t in n){const r=(a.call(e,t)?e[t]:void 0)||(e[t]={}),i=n[t];let u;if(i)for(u in i){a.call(r,u)||(r[u]=[]);const e=i[u];d(r[u],Array.isArray(e)?e:e?[e]:[])}}}function d(e,n){let t=-1;const r=[];for(;++t<n.length;)("after"===n[t].add?e:r).push(n[t]);s(e,0,0,r)}const h=A(/[A-Za-z]/),p=A(/[\dA-Za-z]/),m=A(/[#-'*+\--9=?A-Z^-~]/);function g(e){return null!==e&&(e<32||127===e)}const x=A(/\d/),k=A(/[\dA-Fa-f]/),y=A(/[!-/:-@[-`{-~]/);function F(e){return null!==e&&e<-2}function v(e){return null!==e&&(e<0||32===e)}function b(e){return-2===e||-1===e||32===e}const S=A(/[!-\/:-@\[-`\{-~\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]/),E=A(/\s/);function A(e){return function(n){return null!==n&&e.test(String.fromCharCode(n))}}function I(e,n,t,r){const i=r?r-1:Number.POSITIVE_INFINITY;let u=0;return function(r){return b(r)?(e.enter(t),o(r)):n(r)};function o(r){return b(r)&&u++<i?(e.consume(r),o):(e.exit(t),n(r))}}const w={tokenize:function(e){const n=e.attempt(this.parser.constructs.contentInitial,(function(t){if(null!==t)return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),I(e,n,"linePrefix");e.consume(t)}),(function(n){return e.enter("paragraph"),r(n)}));let t;return n;function r(n){const r=e.enter("chunkText",{contentType:"text",previous:t});return t&&(t.next=r),t=r,i(n)}function i(n){return null===n?(e.exit("chunkText"),e.exit("paragraph"),void e.consume(n)):F(n)?(e.consume(n),e.exit("chunkText"),r):(e.consume(n),i)}}},C={tokenize:function(e){const n=this,t=[];let r,i,u,o=0;return c;function c(r){if(o<t.length){const i=t[o];return n.containerState=i[1],e.attempt(i[0].continuation,l,a)(r)}return a(r)}function l(e){if(o++,n.containerState._closeFlow){n.containerState._closeFlow=void 0,r&&y();const t=n.events.length;let i,u=t;for(;u--;)if("exit"===n.events[u][0]&&"chunkFlow"===n.events[u][1].type){i=n.events[u][1].end;break}k(o);let c=t;for(;c<n.events.length;)n.events[c][1].end=Object.assign({},i),c++;return s(n.events,u+1,0,n.events.slice(t)),n.events.length=c,a(e)}return c(e)}function a(i){if(o===t.length){if(!r)return h(i);if(r.currentConstruct&&r.currentConstruct.concrete)return m(i);n.interrupt=Boolean(r.currentConstruct&&!r._gfmTableDynamicInterruptHack)}return n.containerState={},e.check(T,f,d)(i)}function f(e){return r&&y(),k(o),h(e)}function d(e){return n.parser.lazy[n.now().line]=o!==t.length,u=n.now().offset,m(e)}function h(t){return n.containerState={},e.attempt(T,p,m)(t)}function p(e){return o++,t.push([n.currentConstruct,n.containerState]),h(e)}function m(t){return null===t?(r&&y(),k(0),void e.consume(t)):(r=r||n.parser.flow(n.now()),e.enter("chunkFlow",{contentType:"flow",previous:i,_tokenizer:r}),g(t))}function g(t){return null===t?(x(e.exit("chunkFlow"),!0),k(0),void e.consume(t)):F(t)?(e.consume(t),x(e.exit("chunkFlow")),o=0,n.interrupt=void 0,c):(e.consume(t),g)}function x(e,t){const c=n.sliceStream(e);if(t&&c.push(null),e.previous=i,i&&(i.next=e),i=e,r.defineSkip(e.start),r.write(c),n.parser.lazy[e.start.line]){let e=r.events.length;for(;e--;)if(r.events[e][1].start.offset<u&&(!r.events[e][1].end||r.events[e][1].end.offset>u))return;const t=n.events.length;let i,c,l=t;for(;l--;)if("exit"===n.events[l][0]&&"chunkFlow"===n.events[l][1].type){if(i){c=n.events[l][1].end;break}i=!0}for(k(o),e=t;e<n.events.length;)n.events[e][1].end=Object.assign({},c),e++;s(n.events,l+1,0,n.events.slice(t)),n.events.length=e}}function k(r){let i=t.length;for(;i-- >r;){const r=t[i];n.containerState=r[1],r[0].exit.call(n,e)}t.length=r}function y(){r.write([null]),i=void 0,r=void 0,n.containerState._closeFlow=void 0}}},T={tokenize:function(e,n,t){return I(e,e.attempt(this.parser.constructs.document,n,t),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}},z={tokenize:function(e,n,t){return function(n){return b(n)?I(e,r,"linePrefix")(n):r(n)};function r(e){return null===e||F(e)?n(e):t(e)}},partial:!0};function _(e){const n={};let t,r,i,u,o,c,l,a=-1;for(;++a<e.length;){for(;a in n;)a=n[a];if(t=e[a],a&&"chunkFlow"===t[1].type&&"listItemPrefix"===e[a-1][1].type&&(c=t[1]._tokenizer.events,i=0,i<c.length&&"lineEndingBlank"===c[i][1].type&&(i+=2),i<c.length&&"content"===c[i][1].type))for(;++i<c.length&&"content"!==c[i][1].type;)"chunkText"===c[i][1].type&&(c[i][1]._isInFirstContentOfListItem=!0,i++);if("enter"===t[0])t[1].contentType&&(Object.assign(n,B(e,a)),a=n[a],l=!0);else if(t[1]._container){for(i=a,r=void 0;i--&&(u=e[i],"lineEnding"===u[1].type||"lineEndingBlank"===u[1].type);)"enter"===u[0]&&(r&&(e[r][1].type="lineEndingBlank"),u[1].type="lineEnding",r=i);r&&(t[1].end=Object.assign({},e[r][1].start),o=e.slice(r,a),o.unshift(t),s(e,r,a-r+1,o))}}return!l}function B(e,n){const t=e[n][1],r=e[n][2];let i=n-1;const u=[],o=t._tokenizer||r.parser[t.contentType](t.start),c=o.events,l=[],a={};let f,d,h=-1,p=t,m=0,g=0;const x=[g];for(;p;){for(;e[++i][1]!==p;);u.push(i),p._tokenizer||(f=r.sliceStream(p),p.next||f.push(null),d&&o.defineSkip(p.start),p._isInFirstContentOfListItem&&(o._gfmTasklistFirstContentOfListItem=!0),o.write(f),p._isInFirstContentOfListItem&&(o._gfmTasklistFirstContentOfListItem=void 0)),d=p,p=p.next}for(p=t;++h<c.length;)"exit"===c[h][0]&&"enter"===c[h-1][0]&&c[h][1].type===c[h-1][1].type&&c[h][1].start.line!==c[h][1].end.line&&(g=h+1,x.push(g),p._tokenizer=void 0,p.previous=void 0,p=p.next);for(o.events=[],p?(p._tokenizer=void 0,p.previous=void 0):x.pop(),h=x.length;h--;){const n=c.slice(x[h],x[h+1]),t=u.pop();l.unshift([t,t+n.length-1]),s(e,t,2,n)}for(h=-1;++h<l.length;)a[m+l[h][0]]=m+l[h][1],m+=l[h][1]-l[h][0]-1;return a}const D={tokenize:function(e,n){let t;return function(n){return e.enter("content"),t=e.enter("chunkContent",{contentType:"content"}),r(n)};function r(n){return null===n?i(n):F(n)?e.check(L,u,i)(n):(e.consume(n),r)}function i(t){return e.exit("chunkContent"),e.exit("content"),n(t)}function u(n){return e.consume(n),e.exit("chunkContent"),t.next=e.enter("chunkContent",{contentType:"content",previous:t}),t=t.next,r}},resolve:function(e){return _(e),e}},L={tokenize:function(e,n,t){const r=this;return function(n){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),I(e,i,"linePrefix")};function i(i){if(null===i||F(i))return t(i);const u=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&u&&"linePrefix"===u[1].type&&u[2].sliceSerialize(u[1],!0).length>=4?n(i):e.interrupt(r.parser.constructs.flow,t,n)(i)}},partial:!0},P={tokenize:function(e){const n=this,t=e.attempt(z,(function(r){if(null!==r)return e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),n.currentConstruct=void 0,t;e.consume(r)}),e.attempt(this.parser.constructs.flowInitial,r,I(e,e.attempt(this.parser.constructs.flow,r,e.attempt(D,r)),"linePrefix")));return t;function r(r){if(null!==r)return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),n.currentConstruct=void 0,t;e.consume(r)}}},M={resolveAll:H()},O=R("string"),j=R("text");function R(e){return{tokenize:function(n){const t=this,r=this.parser.constructs[e],i=n.attempt(r,u,o);return u;function u(e){return s(e)?i(e):o(e)}function o(e){if(null!==e)return n.enter("data"),n.consume(e),c;n.consume(e)}function c(e){return s(e)?(n.exit("data"),i(e)):(n.consume(e),c)}function s(e){if(null===e)return!0;const n=r[e];let i=-1;if(n)for(;++i<n.length;){const e=n[i];if(!e.previous||e.previous.call(t,t.previous))return!0}return!1}},resolveAll:H("text"===e?q:void 0)}}function H(e){return function(n,t){let r,i=-1;for(;++i<=n.length;)void 0===r?n[i]&&"data"===n[i][1].type&&(r=i,i++):n[i]&&"data"===n[i][1].type||(i!==r+2&&(n[r][1].end=n[i-1][1].end,n.splice(r+2,i-r-2),i=r+2),r=void 0);return e?e(n,t):n}}function q(e,n){let t=0;for(;++t<=e.length;)if((t===e.length||"lineEnding"===e[t][1].type)&&"data"===e[t-1][1].type){const r=e[t-1][1],i=n.sliceStream(r);let u,o=i.length,c=-1,s=0;for(;o--;){const e=i[o];if("string"==typeof e){for(c=e.length;32===e.charCodeAt(c-1);)s++,c--;if(c)break;c=-1}else if(-2===e)u=!0,s++;else if(-1!==e){o++;break}}if(s){const i={type:t===e.length||u||s<2?"lineSuffix":"hardBreakTrailing",start:{line:r.end.line,column:r.end.column-s,offset:r.end.offset-s,_index:r.start._index+o,_bufferIndex:o?c:r.start._bufferIndex+c},end:Object.assign({},r.end)};r.end=Object.assign({},i.start),r.start.offset===r.end.offset?Object.assign(r,i):(e.splice(t,0,["enter",i,n],["exit",i,n]),t+=2)}t++}return e}function V(e,n,t){const r=[];let i=-1;for(;++i<e.length;){const u=e[i].resolveAll;u&&!r.includes(u)&&(n=u(n,t),r.push(u))}return n}function U(e,n,t){let r=Object.assign(t?Object.assign({},t):{line:1,column:1,offset:0},{_index:0,_bufferIndex:-1});const i={},u=[];let o=[],c=[],a=!0;const f={consume:function(e){F(e)?(r.line++,r.column=1,r.offset+=-3===e?2:1,b()):-1!==e&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===o[r._index].length&&(r._bufferIndex=-1,r._index++)),d.previous=e,a=!0},enter:function(e,n){const t=n||{};return t.type=e,t.start=g(),d.events.push(["enter",t,d]),c.push(t),t},exit:function(e){const n=c.pop();return n.end=g(),d.events.push(["exit",n,d]),n},attempt:y((function(e,n){v(e,n.from)})),check:y(k),interrupt:y(k,{interrupt:!0})},d={previous:null,code:null,containerState:{},events:[],parser:e,sliceStream:m,sliceSerialize:function(e,n){return function(e,n){let t=-1;const r=[];let i;for(;++t<e.length;){const u=e[t];let o;if("string"==typeof u)o=u;else switch(u){case-5:o="\r";break;case-4:o="\n";break;case-3:o="\r\n";break;case-2:o=n?" ":"\t";break;case-1:if(!n&&i)continue;o=" ";break;default:o=String.fromCharCode(u)}i=-2===u,r.push(o)}return r.join("")}(m(e),n)},now:g,defineSkip:function(e){i[e.line]=e.column,b()},write:function(e){return o=l(o,e),function(){let e;for(;r._index<o.length;){const n=o[r._index];if("string"==typeof n)for(e=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===e&&r._bufferIndex<n.length;)x(n.charCodeAt(r._bufferIndex));else x(n)}}(),null!==o[o.length-1]?[]:(v(n,0),d.events=V(u,d.events,d),d.events)}};let h,p=n.tokenize.call(d,f);return n.resolveAll&&u.push(n),d;function m(e){return function(e,n){const t=n.start._index,r=n.start._bufferIndex,i=n.end._index,u=n.end._bufferIndex;let o;if(t===i)o=[e[t].slice(r,u)];else{if(o=e.slice(t,i),r>-1){const e=o[0];"string"==typeof e?o[0]=e.slice(r):o.shift()}u>0&&o.push(e[i].slice(0,u))}return o}(o,e)}function g(){const{line:e,column:n,offset:t,_index:i,_bufferIndex:u}=r;return{line:e,column:n,offset:t,_index:i,_bufferIndex:u}}function x(e){a=void 0,h=e,p=p(e)}function k(e,n){n.restore()}function y(e,n){return function(t,i,u){let o,s,l,h;return Array.isArray(t)?m(t):"tokenize"in t?m([t]):(p=t,function(e){const n=null!==e&&p[e],t=null!==e&&p.null;return m([...Array.isArray(n)?n:n?[n]:[],...Array.isArray(t)?t:t?[t]:[]])(e)});var p;function m(e){return o=e,s=0,0===e.length?u:x(e[s])}function x(e){return function(t){return h=function(){const e=g(),n=d.previous,t=d.currentConstruct,i=d.events.length,u=Array.from(c);return{restore:function(){r=e,d.previous=n,d.currentConstruct=t,d.events.length=i,c=u,b()},from:i}}(),l=e,e.partial||(d.currentConstruct=e),e.name&&d.parser.constructs.disable.null.includes(e.name)?y():e.tokenize.call(n?Object.assign(Object.create(d),n):d,f,k,y)(t)}}function k(n){return a=!0,e(l,h),i}function y(e){return a=!0,h.restore(),++s<o.length?x(o[s]):u}}}function v(e,n){e.resolveAll&&!u.includes(e)&&u.push(e),e.resolve&&s(d.events,n,d.events.length-n,e.resolve(d.events.slice(n),d)),e.resolveTo&&(d.events=e.resolveTo(d.events,d))}function b(){r.line in i&&r.column<2&&(r.column=i[r.line],r.offset+=i[r.line]-1)}}const Q={name:"thematicBreak",tokenize:function(e,n,t){let r,i=0;return function(n){return e.enter("thematicBreak"),function(e){return r=e,u(e)}(n)};function u(u){return u===r?(e.enter("thematicBreakSequence"),o(u)):i>=3&&(null===u||F(u))?(e.exit("thematicBreak"),n(u)):t(u)}function o(n){return n===r?(e.consume(n),i++,o):(e.exit("thematicBreakSequence"),b(n)?I(e,u,"whitespace")(n):u(n))}}},N={name:"list",tokenize:function(e,n,t){const r=this,i=r.events[r.events.length-1];let u=i&&"linePrefix"===i[1].type?i[2].sliceSerialize(i[1],!0).length:0,o=0;return function(n){const i=r.containerState.type||(42===n||43===n||45===n?"listUnordered":"listOrdered");if("listUnordered"===i?!r.containerState.marker||n===r.containerState.marker:x(n)){if(r.containerState.type||(r.containerState.type=i,e.enter(i,{_container:!0})),"listUnordered"===i)return e.enter("listItemPrefix"),42===n||45===n?e.check(Q,t,s)(n):s(n);if(!r.interrupt||49===n)return e.enter("listItemPrefix"),e.enter("listItemValue"),c(n)}return t(n)};function c(n){return x(n)&&++o<10?(e.consume(n),c):(!r.interrupt||o<2)&&(r.containerState.marker?n===r.containerState.marker:41===n||46===n)?(e.exit("listItemValue"),s(n)):t(n)}function s(n){return e.enter("listItemMarker"),e.consume(n),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||n,e.check(z,r.interrupt?t:l,e.attempt($,f,a))}function l(e){return r.containerState.initialBlankLine=!0,u++,f(e)}function a(n){return b(n)?(e.enter("listItemPrefixWhitespace"),e.consume(n),e.exit("listItemPrefixWhitespace"),f):t(n)}function f(t){return r.containerState.size=u+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,n(t)}},continuation:{tokenize:function(e,n,t){const r=this;return r.containerState._closeFlow=void 0,e.check(z,(function(t){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,I(e,n,"listItemIndent",r.containerState.size+1)(t)}),(function(t){return r.containerState.furtherBlankLines||!b(t)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,i(t)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(W,n,i)(t))}));function i(i){return r.containerState._closeFlow=!0,r.interrupt=void 0,I(e,e.attempt(N,n,t),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(i)}}},exit:function(e){e.exit(this.containerState.type)}},$={tokenize:function(e,n,t){const r=this;return I(e,(function(e){const i=r.events[r.events.length-1];return!b(e)&&i&&"listItemPrefixWhitespace"===i[1].type?n(e):t(e)}),"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5)},partial:!0},W={tokenize:function(e,n,t){const r=this;return I(e,(function(e){const i=r.events[r.events.length-1];return i&&"listItemIndent"===i[1].type&&i[2].sliceSerialize(i[1],!0).length===r.containerState.size?n(e):t(e)}),"listItemIndent",r.containerState.size+1)},partial:!0},Y={name:"blockQuote",tokenize:function(e,n,t){const r=this;return function(n){if(62===n){const t=r.containerState;return t.open||(e.enter("blockQuote",{_container:!0}),t.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(n),e.exit("blockQuoteMarker"),i}return t(n)};function i(t){return b(t)?(e.enter("blockQuotePrefixWhitespace"),e.consume(t),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),n):(e.exit("blockQuotePrefix"),n(t))}},continuation:{tokenize:function(e,n,t){const r=this;return function(n){return b(n)?I(e,i,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(n):i(n)};function i(r){return e.attempt(Y,n,t)(r)}}},exit:function(e){e.exit("blockQuote")}};function Z(e,n,t,r,i,u,o,c,s){const l=s||Number.POSITIVE_INFINITY;let a=0;return function(n){return 60===n?(e.enter(r),e.enter(i),e.enter(u),e.consume(n),e.exit(u),f):null===n||32===n||41===n||g(n)?t(n):(e.enter(r),e.enter(o),e.enter(c),e.enter("chunkString",{contentType:"string"}),p(n))};function f(t){return 62===t?(e.enter(u),e.consume(t),e.exit(u),e.exit(i),e.exit(r),n):(e.enter(c),e.enter("chunkString",{contentType:"string"}),d(t))}function d(n){return 62===n?(e.exit("chunkString"),e.exit(c),f(n)):null===n||60===n||F(n)?t(n):(e.consume(n),92===n?h:d)}function h(n){return 60===n||62===n||92===n?(e.consume(n),d):d(n)}function p(i){return a||null!==i&&41!==i&&!v(i)?a<l&&40===i?(e.consume(i),a++,p):41===i?(e.consume(i),a--,p):null===i||32===i||40===i||g(i)?t(i):(e.consume(i),92===i?m:p):(e.exit("chunkString"),e.exit(c),e.exit(o),e.exit(r),n(i))}function m(n){return 40===n||41===n||92===n?(e.consume(n),p):p(n)}}function J(e,n,t,r,i,u){const o=this;let c,s=0;return function(n){return e.enter(r),e.enter(i),e.consume(n),e.exit(i),e.enter(u),l};function l(f){return s>999||null===f||91===f||93===f&&!c||94===f&&!s&&"_hiddenFootnoteSupport"in o.parser.constructs?t(f):93===f?(e.exit(u),e.enter(i),e.consume(f),e.exit(i),e.exit(r),n):F(f)?(e.enter("lineEnding"),e.consume(f),e.exit("lineEnding"),l):(e.enter("chunkString",{contentType:"string"}),a(f))}function a(n){return null===n||91===n||93===n||F(n)||s++>999?(e.exit("chunkString"),l(n)):(e.consume(n),c||(c=!b(n)),92===n?f:a)}function f(n){return 91===n||92===n||93===n?(e.consume(n),s++,a):a(n)}}function G(e,n,t,r,i,u){let o;return function(n){return 34===n||39===n||40===n?(e.enter(r),e.enter(i),e.consume(n),e.exit(i),o=40===n?41:n,c):t(n)};function c(t){return t===o?(e.enter(i),e.consume(t),e.exit(i),e.exit(r),n):(e.enter(u),s(t))}function s(n){return n===o?(e.exit(u),c(o)):null===n?t(n):F(n)?(e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),I(e,s,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),l(n))}function l(n){return n===o||null===n||F(n)?(e.exit("chunkString"),s(n)):(e.consume(n),92===n?a:l)}function a(n){return n===o||92===n?(e.consume(n),l):l(n)}}function K(e,n){let t;return function r(i){return F(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),t=!0,r):b(i)?I(e,r,t?"linePrefix":"lineSuffix")(i):n(i)}}function X(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}const ee={name:"definition",tokenize:function(e,n,t){const r=this;let i;return function(n){return e.enter("definition"),function(n){return J.call(r,e,u,t,"definitionLabel","definitionLabelMarker","definitionLabelString")(n)}(n)};function u(n){return i=X(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)),58===n?(e.enter("definitionMarker"),e.consume(n),e.exit("definitionMarker"),o):t(n)}function o(n){return v(n)?K(e,c)(n):c(n)}function c(n){return Z(e,s,t,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(n)}function s(n){return e.attempt(ne,l,l)(n)}function l(n){return b(n)?I(e,a,"whitespace")(n):a(n)}function a(u){return null===u||F(u)?(e.exit("definition"),r.parser.defined.push(i),n(u)):t(u)}}},ne={tokenize:function(e,n,t){return function(n){return v(n)?K(e,r)(n):t(n)};function r(n){return G(e,i,t,"definitionTitle","definitionTitleMarker","definitionTitleString")(n)}function i(n){return b(n)?I(e,u,"whitespace")(n):u(n)}function u(e){return null===e||F(e)?n(e):t(e)}},partial:!0},te={name:"codeIndented",tokenize:function(e,n,t){const r=this;return function(n){return e.enter("codeIndented"),I(e,i,"linePrefix",5)(n)};function i(e){const n=r.events[r.events.length-1];return n&&"linePrefix"===n[1].type&&n[2].sliceSerialize(n[1],!0).length>=4?u(e):t(e)}function u(n){return null===n?c(n):F(n)?e.attempt(re,u,c)(n):(e.enter("codeFlowValue"),o(n))}function o(n){return null===n||F(n)?(e.exit("codeFlowValue"),u(n)):(e.consume(n),o)}function c(t){return e.exit("codeIndented"),n(t)}}},re={tokenize:function(e,n,t){const r=this;return i;function i(n){return r.parser.lazy[r.now().line]?t(n):F(n)?(e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),i):I(e,u,"linePrefix",5)(n)}function u(e){const u=r.events[r.events.length-1];return u&&"linePrefix"===u[1].type&&u[2].sliceSerialize(u[1],!0).length>=4?n(e):F(e)?i(e):t(e)}},partial:!0},ie={name:"headingAtx",tokenize:function(e,n,t){let r=0;return function(n){return e.enter("atxHeading"),function(n){return e.enter("atxHeadingSequence"),i(n)}(n)};function i(n){return 35===n&&r++<6?(e.consume(n),i):null===n||v(n)?(e.exit("atxHeadingSequence"),u(n)):t(n)}function u(t){return 35===t?(e.enter("atxHeadingSequence"),o(t)):null===t||F(t)?(e.exit("atxHeading"),n(t)):b(t)?I(e,u,"whitespace")(t):(e.enter("atxHeadingText"),c(t))}function o(n){return 35===n?(e.consume(n),o):(e.exit("atxHeadingSequence"),u(n))}function c(n){return null===n||35===n||v(n)?(e.exit("atxHeadingText"),u(n)):(e.consume(n),c)}},resolve:function(e,n){let t,r,i=e.length-2,u=3;return"whitespace"===e[u][1].type&&(u+=2),i-2>u&&"whitespace"===e[i][1].type&&(i-=2),"atxHeadingSequence"===e[i][1].type&&(u===i-1||i-4>u&&"whitespace"===e[i-2][1].type)&&(i-=u+1===i?2:4),i>u&&(t={type:"atxHeadingText",start:e[u][1].start,end:e[i][1].end},r={type:"chunkText",start:e[u][1].start,end:e[i][1].end,contentType:"text"},s(e,u,i-u+1,[["enter",t,n],["enter",r,n],["exit",r,n],["exit",t,n]])),e}},ue={name:"setextUnderline",tokenize:function(e,n,t){const r=this;let i;return function(n){let o,c=r.events.length;for(;c--;)if("lineEnding"!==r.events[c][1].type&&"linePrefix"!==r.events[c][1].type&&"content"!==r.events[c][1].type){o="paragraph"===r.events[c][1].type;break}return r.parser.lazy[r.now().line]||!r.interrupt&&!o?t(n):(e.enter("setextHeadingLine"),i=n,function(n){return e.enter("setextHeadingLineSequence"),u(n)}(n))};function u(n){return n===i?(e.consume(n),u):(e.exit("setextHeadingLineSequence"),b(n)?I(e,o,"lineSuffix")(n):o(n))}function o(r){return null===r||F(r)?(e.exit("setextHeadingLine"),n(r)):t(r)}},resolveTo:function(e,n){let t,r,i,u=e.length;for(;u--;)if("enter"===e[u][0]){if("content"===e[u][1].type){t=u;break}"paragraph"===e[u][1].type&&(r=u)}else"content"===e[u][1].type&&e.splice(u,1),i||"definition"!==e[u][1].type||(i=u);const o={type:"setextHeading",start:Object.assign({},e[r][1].start),end:Object.assign({},e[e.length-1][1].end)};return e[r][1].type="setextHeadingText",i?(e.splice(r,0,["enter",o,n]),e.splice(i+1,0,["exit",e[t][1],n]),e[t][1].end=Object.assign({},e[i][1].end)):e[t][1]=o,e.push(["exit",o,n]),e}},oe=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],ce=["pre","script","style","textarea"],se={name:"htmlFlow",tokenize:function(e,n,t){const r=this;let i,u,o,c,s;return function(n){return function(n){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(n),l}(n)};function l(c){return 33===c?(e.consume(c),a):47===c?(e.consume(c),u=!0,m):63===c?(e.consume(c),i=3,r.interrupt?n:R):h(c)?(e.consume(c),o=String.fromCharCode(c),g):t(c)}function a(u){return 45===u?(e.consume(u),i=2,f):91===u?(e.consume(u),i=5,c=0,d):h(u)?(e.consume(u),i=4,r.interrupt?n:R):t(u)}function f(i){return 45===i?(e.consume(i),r.interrupt?n:R):t(i)}function d(i){return i==="CDATA[".charCodeAt(c++)?(e.consume(i),6===c?r.interrupt?n:_:d):t(i)}function m(n){return h(n)?(e.consume(n),o=String.fromCharCode(n),g):t(n)}function g(c){if(null===c||47===c||62===c||v(c)){const s=47===c,l=o.toLowerCase();return s||u||!ce.includes(l)?oe.includes(o.toLowerCase())?(i=6,s?(e.consume(c),x):r.interrupt?n(c):_(c)):(i=7,r.interrupt&&!r.parser.lazy[r.now().line]?t(c):u?k(c):y(c)):(i=1,r.interrupt?n(c):_(c))}return 45===c||p(c)?(e.consume(c),o+=String.fromCharCode(c),g):t(c)}function x(i){return 62===i?(e.consume(i),r.interrupt?n:_):t(i)}function k(n){return b(n)?(e.consume(n),k):T(n)}function y(n){return 47===n?(e.consume(n),T):58===n||95===n||h(n)?(e.consume(n),S):b(n)?(e.consume(n),y):T(n)}function S(n){return 45===n||46===n||58===n||95===n||p(n)?(e.consume(n),S):E(n)}function E(n){return 61===n?(e.consume(n),A):b(n)?(e.consume(n),E):y(n)}function A(n){return null===n||60===n||61===n||62===n||96===n?t(n):34===n||39===n?(e.consume(n),s=n,I):b(n)?(e.consume(n),A):w(n)}function I(n){return n===s?(e.consume(n),s=null,C):null===n||F(n)?t(n):(e.consume(n),I)}function w(n){return null===n||34===n||39===n||47===n||60===n||61===n||62===n||96===n||v(n)?E(n):(e.consume(n),w)}function C(e){return 47===e||62===e||b(e)?y(e):t(e)}function T(n){return 62===n?(e.consume(n),z):t(n)}function z(n){return null===n||F(n)?_(n):b(n)?(e.consume(n),z):t(n)}function _(n){return 45===n&&2===i?(e.consume(n),P):60===n&&1===i?(e.consume(n),M):62===n&&4===i?(e.consume(n),H):63===n&&3===i?(e.consume(n),R):93===n&&5===i?(e.consume(n),j):!F(n)||6!==i&&7!==i?null===n||F(n)?(e.exit("htmlFlowData"),B(n)):(e.consume(n),_):(e.exit("htmlFlowData"),e.check(le,q,B)(n))}function B(n){return e.check(ae,D,q)(n)}function D(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),L}function L(n){return null===n||F(n)?B(n):(e.enter("htmlFlowData"),_(n))}function P(n){return 45===n?(e.consume(n),R):_(n)}function M(n){return 47===n?(e.consume(n),o="",O):_(n)}function O(n){if(62===n){const t=o.toLowerCase();return ce.includes(t)?(e.consume(n),H):_(n)}return h(n)&&o.length<8?(e.consume(n),o+=String.fromCharCode(n),O):_(n)}function j(n){return 93===n?(e.consume(n),R):_(n)}function R(n){return 62===n?(e.consume(n),H):45===n&&2===i?(e.consume(n),R):_(n)}function H(n){return null===n||F(n)?(e.exit("htmlFlowData"),q(n)):(e.consume(n),H)}function q(t){return e.exit("htmlFlow"),n(t)}},resolveTo:function(e){let n=e.length;for(;n--&&("enter"!==e[n][0]||"htmlFlow"!==e[n][1].type););return n>1&&"linePrefix"===e[n-2][1].type&&(e[n][1].start=e[n-2][1].start,e[n+1][1].start=e[n-2][1].start,e.splice(n-2,2)),e},concrete:!0},le={tokenize:function(e,n,t){return function(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),e.attempt(z,n,t)}},partial:!0},ae={tokenize:function(e,n,t){const r=this;return function(n){return F(n)?(e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),i):t(n)};function i(e){return r.parser.lazy[r.now().line]?t(e):n(e)}},partial:!0},fe={tokenize:function(e,n,t){const r=this;return function(n){return null===n?t(n):(e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),i)};function i(e){return r.parser.lazy[r.now().line]?t(e):n(e)}},partial:!0},de={name:"codeFenced",tokenize:function(e,n,t){const r=this,i={tokenize:function(e,n,t){let i=0;return function(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),o};function o(n){return e.enter("codeFencedFence"),b(n)?I(e,s,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(n):s(n)}function s(n){return n===u?(e.enter("codeFencedFenceSequence"),l(n)):t(n)}function l(n){return n===u?(i++,e.consume(n),l):i>=c?(e.exit("codeFencedFenceSequence"),b(n)?I(e,a,"whitespace")(n):a(n)):t(n)}function a(r){return null===r||F(r)?(e.exit("codeFencedFence"),n(r)):t(r)}},partial:!0};let u,o=0,c=0;return function(n){return function(n){const t=r.events[r.events.length-1];return o=t&&"linePrefix"===t[1].type?t[2].sliceSerialize(t[1],!0).length:0,u=n,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),s(n)}(n)};function s(n){return n===u?(c++,e.consume(n),s):c<3?t(n):(e.exit("codeFencedFenceSequence"),b(n)?I(e,l,"whitespace")(n):l(n))}function l(t){return null===t||F(t)?(e.exit("codeFencedFence"),r.interrupt?n(t):e.check(fe,h,k)(t)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),a(t))}function a(n){return null===n||F(n)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),l(n)):b(n)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),I(e,f,"whitespace")(n)):96===n&&n===u?t(n):(e.consume(n),a)}function f(n){return null===n||F(n)?l(n):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),d(n))}function d(n){return null===n||F(n)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),l(n)):96===n&&n===u?t(n):(e.consume(n),d)}function h(n){return e.attempt(i,k,p)(n)}function p(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),m}function m(n){return o>0&&b(n)?I(e,g,"linePrefix",o+1)(n):g(n)}function g(n){return null===n||F(n)?e.check(fe,h,k)(n):(e.enter("codeFlowValue"),x(n))}function x(n){return null===n||F(n)?(e.exit("codeFlowValue"),g(n)):(e.consume(n),x)}function k(t){return e.exit("codeFenced"),n(t)}},concrete:!0},he=document.createElement("i");function pe(e){const n="&"+e+";";he.innerHTML=n;const t=he.textContent;return(59!==t.charCodeAt(t.length-1)||"semi"===e)&&t!==n&&t}const me={name:"characterReference",tokenize:function(e,n,t){const r=this;let i,u,o=0;return function(n){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(n),e.exit("characterReferenceMarker"),c};function c(n){return 35===n?(e.enter("characterReferenceMarkerNumeric"),e.consume(n),e.exit("characterReferenceMarkerNumeric"),s):(e.enter("characterReferenceValue"),i=31,u=p,l(n))}function s(n){return 88===n||120===n?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(n),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),i=6,u=k,l):(e.enter("characterReferenceValue"),i=7,u=x,l(n))}function l(c){if(59===c&&o){const i=e.exit("characterReferenceValue");return u!==p||pe(r.sliceSerialize(i))?(e.enter("characterReferenceMarker"),e.consume(c),e.exit("characterReferenceMarker"),e.exit("characterReference"),n):t(c)}return u(c)&&o++<i?(e.consume(c),l):t(c)}}},ge={name:"characterEscape",tokenize:function(e,n,t){return function(n){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(n),e.exit("escapeMarker"),r};function r(r){return y(r)?(e.enter("characterEscapeValue"),e.consume(r),e.exit("characterEscapeValue"),e.exit("characterEscape"),n):t(r)}}},xe={name:"lineEnding",tokenize:function(e,n){return function(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),I(e,n,"linePrefix")}}},ke={name:"labelEnd",tokenize:function(e,n,t){const r=this;let i,u,o=r.events.length;for(;o--;)if(("labelImage"===r.events[o][1].type||"labelLink"===r.events[o][1].type)&&!r.events[o][1]._balanced){i=r.events[o][1];break}return function(n){return i?i._inactive?a(n):(u=r.parser.defined.includes(X(r.sliceSerialize({start:i.end,end:r.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(n),e.exit("labelMarker"),e.exit("labelEnd"),c):t(n)};function c(n){return 40===n?e.attempt(ye,l,u?l:a)(n):91===n?e.attempt(Fe,l,u?s:a)(n):u?l(n):a(n)}function s(n){return e.attempt(ve,l,a)(n)}function l(e){return n(e)}function a(e){return i._balanced=!0,t(e)}},resolveTo:function(e,n){let t,r,i,u,o=e.length,c=0;for(;o--;)if(t=e[o][1],r){if("link"===t.type||"labelLink"===t.type&&t._inactive)break;"enter"===e[o][0]&&"labelLink"===t.type&&(t._inactive=!0)}else if(i){if("enter"===e[o][0]&&("labelImage"===t.type||"labelLink"===t.type)&&!t._balanced&&(r=o,"labelLink"!==t.type)){c=2;break}}else"labelEnd"===t.type&&(i=o);const a={type:"labelLink"===e[r][1].type?"link":"image",start:Object.assign({},e[r][1].start),end:Object.assign({},e[e.length-1][1].end)},f={type:"label",start:Object.assign({},e[r][1].start),end:Object.assign({},e[i][1].end)},d={type:"labelText",start:Object.assign({},e[r+c+2][1].end),end:Object.assign({},e[i-2][1].start)};return u=[["enter",a,n],["enter",f,n]],u=l(u,e.slice(r+1,r+c+3)),u=l(u,[["enter",d,n]]),u=l(u,V(n.parser.constructs.insideSpan.null,e.slice(r+c+4,i-3),n)),u=l(u,[["exit",d,n],e[i-2],e[i-1],["exit",f,n]]),u=l(u,e.slice(i+1)),u=l(u,[["exit",a,n]]),s(e,r,e.length,u),e},resolveAll:function(e){let n=-1;for(;++n<e.length;){const t=e[n][1];"labelImage"!==t.type&&"labelLink"!==t.type&&"labelEnd"!==t.type||(e.splice(n+1,"labelImage"===t.type?4:2),t.type="data",n++)}return e}},ye={tokenize:function(e,n,t){return function(n){return e.enter("resource"),e.enter("resourceMarker"),e.consume(n),e.exit("resourceMarker"),r};function r(n){return v(n)?K(e,i)(n):i(n)}function i(n){return 41===n?l(n):Z(e,u,o,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(n)}function u(n){return v(n)?K(e,c)(n):l(n)}function o(e){return t(e)}function c(n){return 34===n||39===n||40===n?G(e,s,t,"resourceTitle","resourceTitleMarker","resourceTitleString")(n):l(n)}function s(n){return v(n)?K(e,l)(n):l(n)}function l(r){return 41===r?(e.enter("resourceMarker"),e.consume(r),e.exit("resourceMarker"),e.exit("resource"),n):t(r)}}},Fe={tokenize:function(e,n,t){const r=this;return function(n){return J.call(r,e,i,u,"reference","referenceMarker","referenceString")(n)};function i(e){return r.parser.defined.includes(X(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?n(e):t(e)}function u(e){return t(e)}}},ve={tokenize:function(e,n,t){return function(n){return e.enter("reference"),e.enter("referenceMarker"),e.consume(n),e.exit("referenceMarker"),r};function r(r){return 93===r?(e.enter("referenceMarker"),e.consume(r),e.exit("referenceMarker"),e.exit("reference"),n):t(r)}}};function be(e){return null===e||v(e)||E(e)?1:S(e)?2:void 0}const Se={name:"attention",tokenize:function(e,n){const t=this.parser.constructs.attentionMarkers.null,r=this.previous,i=be(r);let u;return function(n){return u=n,e.enter("attentionSequence"),o(n)};function o(c){if(c===u)return e.consume(c),o;const s=e.exit("attentionSequence"),l=be(c),a=!l||2===l&&i||t.includes(c),f=!i||2===i&&l||t.includes(r);return s._open=Boolean(42===u?a:a&&(i||!f)),s._close=Boolean(42===u?f:f&&(l||!a)),n(c)}},resolveAll:function(e,n){let t,r,i,u,o,c,a,f,d=-1;for(;++d<e.length;)if("enter"===e[d][0]&&"attentionSequence"===e[d][1].type&&e[d][1]._close)for(t=d;t--;)if("exit"===e[t][0]&&"attentionSequence"===e[t][1].type&&e[t][1]._open&&n.sliceSerialize(e[t][1]).charCodeAt(0)===n.sliceSerialize(e[d][1]).charCodeAt(0)){if((e[t][1]._close||e[d][1]._open)&&(e[d][1].end.offset-e[d][1].start.offset)%3&&!((e[t][1].end.offset-e[t][1].start.offset+e[d][1].end.offset-e[d][1].start.offset)%3))continue;c=e[t][1].end.offset-e[t][1].start.offset>1&&e[d][1].end.offset-e[d][1].start.offset>1?2:1;const h=Object.assign({},e[t][1].end),p=Object.assign({},e[d][1].start);Ee(h,-c),Ee(p,c),u={type:c>1?"strongSequence":"emphasisSequence",start:h,end:Object.assign({},e[t][1].end)},o={type:c>1?"strongSequence":"emphasisSequence",start:Object.assign({},e[d][1].start),end:p},i={type:c>1?"strongText":"emphasisText",start:Object.assign({},e[t][1].end),end:Object.assign({},e[d][1].start)},r={type:c>1?"strong":"emphasis",start:Object.assign({},u.start),end:Object.assign({},o.end)},e[t][1].end=Object.assign({},u.start),e[d][1].start=Object.assign({},o.end),a=[],e[t][1].end.offset-e[t][1].start.offset&&(a=l(a,[["enter",e[t][1],n],["exit",e[t][1],n]])),a=l(a,[["enter",r,n],["enter",u,n],["exit",u,n],["enter",i,n]]),a=l(a,V(n.parser.constructs.insideSpan.null,e.slice(t+1,d),n)),a=l(a,[["exit",i,n],["enter",o,n],["exit",o,n],["exit",r,n]]),e[d][1].end.offset-e[d][1].start.offset?(f=2,a=l(a,[["enter",e[d][1],n],["exit",e[d][1],n]])):f=0,s(e,t-1,d-t+3,a),d=t+a.length-f-2;break}for(d=-1;++d<e.length;)"attentionSequence"===e[d][1].type&&(e[d][1].type="data");return e}};function Ee(e,n){e.column+=n,e.offset+=n,e._bufferIndex+=n}const Ae={name:"htmlText",tokenize:function(e,n,t){const r=this;let i,u,o;return function(n){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(n),c};function c(n){return 33===n?(e.consume(n),s):47===n?(e.consume(n),A):63===n?(e.consume(n),S):h(n)?(e.consume(n),T):t(n)}function s(n){return 45===n?(e.consume(n),l):91===n?(e.consume(n),u=0,m):h(n)?(e.consume(n),y):t(n)}function l(n){return 45===n?(e.consume(n),d):t(n)}function a(n){return null===n?t(n):45===n?(e.consume(n),f):F(n)?(o=a,j(n)):(e.consume(n),a)}function f(n){return 45===n?(e.consume(n),d):a(n)}function d(e){return 62===e?O(e):45===e?f(e):a(e)}function m(n){return n==="CDATA[".charCodeAt(u++)?(e.consume(n),6===u?g:m):t(n)}function g(n){return null===n?t(n):93===n?(e.consume(n),x):F(n)?(o=g,j(n)):(e.consume(n),g)}function x(n){return 93===n?(e.consume(n),k):g(n)}function k(n){return 62===n?O(n):93===n?(e.consume(n),k):g(n)}function y(n){return null===n||62===n?O(n):F(n)?(o=y,j(n)):(e.consume(n),y)}function S(n){return null===n?t(n):63===n?(e.consume(n),E):F(n)?(o=S,j(n)):(e.consume(n),S)}function E(e){return 62===e?O(e):S(e)}function A(n){return h(n)?(e.consume(n),w):t(n)}function w(n){return 45===n||p(n)?(e.consume(n),w):C(n)}function C(n){return F(n)?(o=C,j(n)):b(n)?(e.consume(n),C):O(n)}function T(n){return 45===n||p(n)?(e.consume(n),T):47===n||62===n||v(n)?z(n):t(n)}function z(n){return 47===n?(e.consume(n),O):58===n||95===n||h(n)?(e.consume(n),_):F(n)?(o=z,j(n)):b(n)?(e.consume(n),z):O(n)}function _(n){return 45===n||46===n||58===n||95===n||p(n)?(e.consume(n),_):B(n)}function B(n){return 61===n?(e.consume(n),D):F(n)?(o=B,j(n)):b(n)?(e.consume(n),B):z(n)}function D(n){return null===n||60===n||61===n||62===n||96===n?t(n):34===n||39===n?(e.consume(n),i=n,L):F(n)?(o=D,j(n)):b(n)?(e.consume(n),D):(e.consume(n),P)}function L(n){return n===i?(e.consume(n),i=void 0,M):null===n?t(n):F(n)?(o=L,j(n)):(e.consume(n),L)}function P(n){return null===n||34===n||39===n||60===n||61===n||96===n?t(n):47===n||62===n||v(n)?z(n):(e.consume(n),P)}function M(e){return 47===e||62===e||v(e)?z(e):t(e)}function O(r){return 62===r?(e.consume(r),e.exit("htmlTextData"),e.exit("htmlText"),n):t(r)}function j(n){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),R}function R(n){return b(n)?I(e,H,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(n):H(n)}function H(n){return e.enter("htmlTextData"),o(n)}}},Ie={name:"codeText",tokenize:function(e,n,t){let r,i,u=0;return function(n){return e.enter("codeText"),e.enter("codeTextSequence"),o(n)};function o(n){return 96===n?(e.consume(n),u++,o):(e.exit("codeTextSequence"),c(n))}function c(n){return null===n?t(n):32===n?(e.enter("space"),e.consume(n),e.exit("space"),c):96===n?(i=e.enter("codeTextSequence"),r=0,l(n)):F(n)?(e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),c):(e.enter("codeTextData"),s(n))}function s(n){return null===n||32===n||96===n||F(n)?(e.exit("codeTextData"),c(n)):(e.consume(n),s)}function l(t){return 96===t?(e.consume(t),r++,l):r===u?(e.exit("codeTextSequence"),e.exit("codeText"),n(t)):(i.type="codeTextData",s(t))}},resolve:function(e){let n,t,r=e.length-4,i=3;if(!("lineEnding"!==e[i][1].type&&"space"!==e[i][1].type||"lineEnding"!==e[r][1].type&&"space"!==e[r][1].type))for(n=i;++n<r;)if("codeTextData"===e[n][1].type){e[i][1].type="codeTextPadding",e[r][1].type="codeTextPadding",i+=2,r-=2;break}for(n=i-1,r++;++n<=r;)void 0===t?n!==r&&"lineEnding"!==e[n][1].type&&(t=n):n!==r&&"lineEnding"!==e[n][1].type||(e[t][1].type="codeTextData",n!==t+2&&(e[t][1].end=e[n-1][1].end,e.splice(t+2,n-t-2),r-=n-t-2,n=t+2),t=void 0);return e},previous:function(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type}},we={42:N,43:N,45:N,48:N,49:N,50:N,51:N,52:N,53:N,54:N,55:N,56:N,57:N,62:Y},Ce={91:ee},Te={[-2]:te,[-1]:te,32:te},ze={35:ie,42:Q,45:[ue,Q],60:se,61:ue,95:Q,96:de,126:de},_e={38:me,92:ge},Be={[-5]:xe,[-4]:xe,[-3]:xe,33:{name:"labelStartImage",tokenize:function(e,n,t){const r=this;return function(n){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(n),e.exit("labelImageMarker"),i};function i(n){return 91===n?(e.enter("labelMarker"),e.consume(n),e.exit("labelMarker"),e.exit("labelImage"),u):t(n)}function u(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?t(e):n(e)}},resolveAll:ke.resolveAll},38:me,42:Se,60:[{name:"autolink",tokenize:function(e,n,t){let r=0;return function(n){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(n),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),i};function i(n){return h(n)?(e.consume(n),u):s(n)}function u(e){return 43===e||45===e||46===e||p(e)?(r=1,o(e)):s(e)}function o(n){return 58===n?(e.consume(n),r=0,c):(43===n||45===n||46===n||p(n))&&r++<32?(e.consume(n),o):(r=0,s(n))}function c(r){return 62===r?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(r),e.exit("autolinkMarker"),e.exit("autolink"),n):null===r||32===r||60===r||g(r)?t(r):(e.consume(r),c)}function s(n){return 64===n?(e.consume(n),l):m(n)?(e.consume(n),s):t(n)}function l(e){return p(e)?a(e):t(e)}function a(t){return 46===t?(e.consume(t),r=0,l):62===t?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(t),e.exit("autolinkMarker"),e.exit("autolink"),n):f(t)}function f(n){if((45===n||p(n))&&r++<63){const t=45===n?f:a;return e.consume(n),t}return t(n)}}},Ae],91:{name:"labelStartLink",tokenize:function(e,n,t){const r=this;return function(n){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(n),e.exit("labelMarker"),e.exit("labelLink"),i};function i(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?t(e):n(e)}},resolveAll:ke.resolveAll},92:[{name:"hardBreakEscape",tokenize:function(e,n,t){return function(n){return e.enter("hardBreakEscape"),e.consume(n),r};function r(r){return F(r)?(e.exit("hardBreakEscape"),n(r)):t(r)}}},ge],93:ke,95:Se,96:Ie},De={null:[Se,M]},Le={null:[42,95]},Pe={null:[]};const Me=/[\0\t\n\r]/g;function Oe(e,n){const t=Number.parseInt(e,n);return t<9||11===t||t>13&&t<32||t>126&&t<160||t>55295&&t<57344||t>64975&&t<65008||65535==(65535&t)||65534==(65535&t)||t>1114111?"�":String.fromCharCode(t)}const je=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function Re(e,n,t){if(n)return n;if(35===t.charCodeAt(0)){const e=t.charCodeAt(1),n=120===e||88===e;return Oe(t.slice(n?2:1),n?16:10)}return pe(t)||e}function He(e){return e&&"object"==typeof e?"position"in e||"type"in e?Ve(e.position):"start"in e||"end"in e?Ve(e):"line"in e||"column"in e?qe(e):"":""}function qe(e){return Ue(e&&e.line)+":"+Ue(e&&e.column)}function Ve(e){return qe(e&&e.start)+"-"+qe(e&&e.end)}function Ue(e){return e&&"number"==typeof e?e:1}const Qe={}.hasOwnProperty,Ne=function(e,n,t){return"string"!=typeof n&&(t=n,n=void 0),function(e){const n={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:s(b),autolinkProtocol:p,autolinkEmail:p,atxHeading:s(y),blockQuote:s((function(){return{type:"blockquote",children:[]}})),characterEscape:p,characterReference:p,codeFenced:s(k),codeFencedFenceInfo:l,codeFencedFenceMeta:l,codeIndented:s(k,l),codeText:s((function(){return{type:"inlineCode",value:""}}),l),codeTextData:p,data:p,codeFlowValue:p,definition:s((function(){return{type:"definition",identifier:"",label:null,title:null,url:""}})),definitionDestinationString:l,definitionLabelString:l,definitionTitleString:l,emphasis:s((function(){return{type:"emphasis",children:[]}})),hardBreakEscape:s(F),hardBreakTrailing:s(F),htmlFlow:s(v,l),htmlFlowData:p,htmlText:s(v,l),htmlTextData:p,image:s((function(){return{type:"image",title:null,url:"",alt:null}})),label:l,link:s(b),listItem:s((function(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}})),listItemValue:function(e){c("expectingFirstListItemValue")&&(this.stack[this.stack.length-2].start=Number.parseInt(this.sliceSerialize(e),10),i("expectingFirstListItemValue"))},listOrdered:s(S,(function(){i("expectingFirstListItemValue",!0)})),listUnordered:s(S),paragraph:s((function(){return{type:"paragraph",children:[]}})),reference:function(){i("referenceType","collapsed")},referenceString:l,resourceDestinationString:l,resourceTitleString:l,setextHeading:s(y),strong:s((function(){return{type:"strong",children:[]}})),thematicBreak:s((function(){return{type:"thematicBreak"}}))},exit:{atxHeading:f(),atxHeadingSequence:function(e){const n=this.stack[this.stack.length-1];if(!n.depth){const t=this.sliceSerialize(e).length;n.depth=t}},autolink:f(),autolinkEmail:function(e){m.call(this,e),this.stack[this.stack.length-1].url="mailto:"+this.sliceSerialize(e)},autolinkProtocol:function(e){m.call(this,e),this.stack[this.stack.length-1].url=this.sliceSerialize(e)},blockQuote:f(),characterEscapeValue:m,characterReferenceMarkerHexadecimal:x,characterReferenceMarkerNumeric:x,characterReferenceValue:function(e){const n=this.sliceSerialize(e),t=c("characterReferenceType");let r;t?(r=Oe(n,"characterReferenceMarkerNumeric"===t?10:16),i("characterReferenceType")):r=pe(n);const u=this.stack.pop();u.value+=r,u.position.end=$e(e.end)},codeFenced:f((function(){const e=this.resume();this.stack[this.stack.length-1].value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),i("flowCodeInside")})),codeFencedFence:function(){c("flowCodeInside")||(this.buffer(),i("flowCodeInside",!0))},codeFencedFenceInfo:function(){const e=this.resume();this.stack[this.stack.length-1].lang=e},codeFencedFenceMeta:function(){const e=this.resume();this.stack[this.stack.length-1].meta=e},codeFlowValue:m,codeIndented:f((function(){const e=this.resume();this.stack[this.stack.length-1].value=e.replace(/(\r?\n|\r)$/g,"")})),codeText:f((function(){const e=this.resume();this.stack[this.stack.length-1].value=e})),codeTextData:m,data:m,definition:f(),definitionDestinationString:function(){const e=this.resume();this.stack[this.stack.length-1].url=e},definitionLabelString:function(e){const n=this.resume(),t=this.stack[this.stack.length-1];t.label=n,t.identifier=X(this.sliceSerialize(e)).toLowerCase()},definitionTitleString:function(){const e=this.resume();this.stack[this.stack.length-1].title=e},emphasis:f(),hardBreakEscape:f(g),hardBreakTrailing:f(g),htmlFlow:f((function(){const e=this.resume();this.stack[this.stack.length-1].value=e})),htmlFlowData:m,htmlText:f((function(){const e=this.resume();this.stack[this.stack.length-1].value=e})),htmlTextData:m,image:f((function(){const e=this.stack[this.stack.length-1];if(c("inReference")){const n=c("referenceType")||"shortcut";e.type+="Reference",e.referenceType=n,delete e.url,delete e.title}else delete e.identifier,delete e.label;i("referenceType")})),label:function(){const e=this.stack[this.stack.length-1],n=this.resume(),t=this.stack[this.stack.length-1];if(i("inReference",!0),"link"===t.type){const n=e.children;t.children=n}else t.alt=n},labelText:function(e){const n=this.sliceSerialize(e),t=this.stack[this.stack.length-2];t.label=function(e){return e.replace(je,Re)}(n),t.identifier=X(n).toLowerCase()},lineEnding:function(e){const t=this.stack[this.stack.length-1];if(c("atHardBreak"))return t.children[t.children.length-1].position.end=$e(e.end),void i("atHardBreak");!c("setextHeadingSlurpLineEnding")&&n.canContainEols.includes(t.type)&&(p.call(this,e),m.call(this,e))},link:f((function(){const e=this.stack[this.stack.length-1];if(c("inReference")){const n=c("referenceType")||"shortcut";e.type+="Reference",e.referenceType=n,delete e.url,delete e.title}else delete e.identifier,delete e.label;i("referenceType")})),listItem:f(),listOrdered:f(),listUnordered:f(),paragraph:f(),referenceString:function(e){const n=this.resume(),t=this.stack[this.stack.length-1];t.label=n,t.identifier=X(this.sliceSerialize(e)).toLowerCase(),i("referenceType","full")},resourceDestinationString:function(){const e=this.resume();this.stack[this.stack.length-1].url=e},resourceTitleString:function(){const e=this.resume();this.stack[this.stack.length-1].title=e},resource:function(){i("inReference")},setextHeading:f((function(){i("setextHeadingSlurpLineEnding")})),setextHeadingLineSequence:function(e){this.stack[this.stack.length-1].depth=61===this.sliceSerialize(e).charCodeAt(0)?1:2},setextHeadingText:function(){i("setextHeadingSlurpLineEnding",!0)},strong:f(),thematicBreak:f()}};We(n,(e||{}).mdastExtensions||[]);const t={};return function(e){let t={type:"root",children:[]};const u={stack:[t],tokenStack:[],config:n,enter:a,exit:d,buffer:l,resume:h,setData:i,getData:c},o=[];let s=-1;for(;++s<e.length;)"listOrdered"!==e[s][1].type&&"listUnordered"!==e[s][1].type||("enter"===e[s][0]?o.push(s):s=r(e,o.pop(),s));for(s=-1;++s<e.length;){const t=n[e[s][0]];Qe.call(t,e[s][1].type)&&t[e[s][1].type].call(Object.assign({sliceSerialize:e[s][2].sliceSerialize},u),e[s][1])}if(u.tokenStack.length>0){const e=u.tokenStack[u.tokenStack.length-1];(e[1]||Ze).call(u,void 0,e[0])}for(t.position={start:$e(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:$e(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},s=-1;++s<n.transforms.length;)t=n.transforms[s](t)||t;return t};function r(e,n,t){let r,i,u,o,c=n-1,s=-1,l=!1;for(;++c<=t;){const n=e[c];if("listUnordered"===n[1].type||"listOrdered"===n[1].type||"blockQuote"===n[1].type?("enter"===n[0]?s++:s--,o=void 0):"lineEndingBlank"===n[1].type?"enter"===n[0]&&(!r||o||s||u||(u=c),o=void 0):"linePrefix"===n[1].type||"listItemValue"===n[1].type||"listItemMarker"===n[1].type||"listItemPrefix"===n[1].type||"listItemPrefixWhitespace"===n[1].type||(o=void 0),!s&&"enter"===n[0]&&"listItemPrefix"===n[1].type||-1===s&&"exit"===n[0]&&("listUnordered"===n[1].type||"listOrdered"===n[1].type)){if(r){let o=c;for(i=void 0;o--;){const n=e[o];if("lineEnding"===n[1].type||"lineEndingBlank"===n[1].type){if("exit"===n[0])continue;i&&(e[i][1].type="lineEndingBlank",l=!0),n[1].type="lineEnding",i=o}else if("linePrefix"!==n[1].type&&"blockQuotePrefix"!==n[1].type&&"blockQuotePrefixWhitespace"!==n[1].type&&"blockQuoteMarker"!==n[1].type&&"listItemIndent"!==n[1].type)break}u&&(!i||u<i)&&(r._spread=!0),r.end=Object.assign({},i?e[i][1].start:n[1].end),e.splice(i||c,0,["exit",r,n[2]]),c++,t++}"listItemPrefix"===n[1].type&&(r={type:"listItem",_spread:!1,start:Object.assign({},n[1].start),end:void 0},e.splice(c,0,["enter",r,n[2]]),c++,t++,u=void 0,o=!0)}}return e[n][1]._spread=l,t}function i(e,n){t[e]=n}function c(e){return t[e]}function s(e,n){return function(t){a.call(this,e(t),t),n&&n.call(this,t)}}function l(){this.stack.push({type:"fragment",children:[]})}function a(e,n,t){return this.stack[this.stack.length-1].children.push(e),this.stack.push(e),this.tokenStack.push([n,t]),e.position={start:$e(n.start)},e}function f(e){return function(n){e&&e.call(this,n),d.call(this,n)}}function d(e,n){const t=this.stack.pop(),r=this.tokenStack.pop();if(!r)throw new Error("Cannot close `"+e.type+"` ("+He({start:e.start,end:e.end})+"): it’s not open");return r[0].type!==e.type&&(n?n.call(this,e,r[0]):(r[1]||Ze).call(this,e,r[0])),t.position.end=$e(e.end),t}function h(){return function(e,n){return o(e,"boolean"!=typeof u.includeImageAlt||u.includeImageAlt,"boolean"!=typeof u.includeHtml||u.includeHtml)}(this.stack.pop())}function p(e){const n=this.stack[this.stack.length-1];let t=n.children[n.children.length-1];t&&"text"===t.type||(t={type:"text",value:""},t.position={start:$e(e.start)},n.children.push(t)),this.stack.push(t)}function m(e){const n=this.stack.pop();n.value+=this.sliceSerialize(e),n.position.end=$e(e.end)}function g(){i("atHardBreak",!0)}function x(e){i("characterReferenceType",e.type)}function k(){return{type:"code",lang:null,meta:null,value:""}}function y(){return{type:"heading",depth:void 0,children:[]}}function F(){return{type:"break"}}function v(){return{type:"html",value:""}}function b(){return{type:"link",title:null,url:"",children:[]}}function S(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}}(t)(function(e){for(;!_(e););return e}(function(e){const n=function(e){const n={};let t=-1;for(;++t<e.length;)f(n,e[t]);return n}([r,...(e||{}).extensions||[]]),t={defined:[],lazy:{},constructs:n,content:i(w),document:i(C),flow:i(P),string:i(O),text:i(j)};return t;function i(e){return function(n){return U(t,e,n)}}}(t).document().write(function(){let e,n=1,t="",r=!0;return function(i,u,o){const c=[];let s,l,a,f,d;for(i=t+i.toString(u),a=0,t="",r&&(65279===i.charCodeAt(0)&&a++,r=void 0);a<i.length;){if(Me.lastIndex=a,s=Me.exec(i),f=s&&void 0!==s.index?s.index:i.length,d=i.charCodeAt(f),!s){t=i.slice(a);break}if(10===d&&a===f&&e)c.push(-3),e=void 0;else switch(e&&(c.push(-5),e=void 0),a<f&&(c.push(i.slice(a,f)),n+=f-a),d){case 0:c.push(65533),n++;break;case 9:for(l=4*Math.ceil(n/4),c.push(-2);n++<l;)c.push(-1);break;case 10:c.push(-4),n=1;break;default:e=!0,n=1}a=f+1}return o&&(e&&c.push(-5),t&&c.push(t),c.push(null)),c}}()(e,n,!0))))};function $e(e){return{line:e.line,column:e.column,offset:e.offset}}function We(e,n){let t=-1;for(;++t<n.length;){const r=n[t];Array.isArray(r)?We(e,r):Ye(e,r)}}function Ye(e,n){let t;for(t in n)if(Qe.call(n,t))if("canContainEols"===t){const r=n[t];r&&e[t].push(...r)}else if("transforms"===t){const r=n[t];r&&e[t].push(...r)}else if("enter"===t||"exit"===t){const r=n[t];r&&Object.assign(e[t],r)}}function Ze(e,n){throw e?new Error("Cannot close `"+e.type+"` ("+He({start:e.start,end:e.end})+"): a different token (`"+n.type+"`, "+He({start:n.start,end:n.end})+") is open"):new Error("Cannot close document, a token (`"+n.type+"`, "+He({start:n.start,end:n.end})+") is still open")}var Je=t(11464);function Ge(e){const n=function(e){const n=e.replace(/\n{2,}/g,"\n");return(0,Je.Z)(n)}(e),{children:t}=Ne(n),r=[[]];let i=0;function u(e,n="normal"){"text"===e.type?e.value.split("\n").forEach(((e,t)=>{0!==t&&(i++,r.push([])),e.split(" ").forEach((e=>{e&&r[i].push({content:e,type:n})}))})):"strong"!==e.type&&"emphasis"!==e.type||e.children.forEach((n=>{u(n,e.type)}))}return t.forEach((e=>{"paragraph"===e.type&&e.children.forEach((e=>{u(e)}))})),r}function Ke(e,n){var t;return Xe(e,[],(t=n.content,Intl.Segmenter?[...(new Intl.Segmenter).segment(t)].map((e=>e.segment)):[...t]),n.type)}function Xe(e,n,t,r){if(0===t.length)return[{content:n.join(""),type:r},{content:"",type:r}];const[i,...u]=t,o=[...n,i];return e([{content:o.join(""),type:r}])?Xe(e,o,u,r):(0===n.length&&i&&(n.push(i),t.shift()),[{content:n.join(""),type:r},{content:t.join(""),type:r}])}function en(e,n){if(e.some((({content:e})=>e.includes("\n"))))throw new Error("splitLineToFitWidth does not support newlines in the line");return nn(e,n)}function nn(e,n,t=[],r=[]){if(0===e.length)return r.length>0&&t.push(r),t.length>0?t:[];let i="";" "===e[0].content&&(i=" ",e.shift());const u=e.shift()??{content:" ",type:"normal"},o=[...r];if(""!==i&&o.push({content:i,type:"normal"}),o.push(u),n(o))return nn(e,n,t,o);if(r.length>0)t.push(r),e.unshift(u);else if(u.content){const[r,i]=Ke(n,u);t.push([r]),i.content&&e.unshift(i)}return nn(e,n,t)}function tn(e,n,t){return e.append("tspan").attr("class","text-outer-tspan").attr("x",0).attr("y",n*t-.1+"em").attr("dy",t+"em")}function rn(e,n,t){const r=e.append("text"),i=tn(r,1,n);on(i,t);const u=i.node().getComputedTextLength();return r.remove(),u}function un(e,n,t){var r;const i=e.append("text"),u=tn(i,1,n);on(u,[{content:t,type:"normal"}]);const o=null==(r=u.node())?void 0:r.getBoundingClientRect();return o&&i.remove(),o}function on(e,n){e.text(""),n.forEach(((n,t)=>{const r=e.append("tspan").attr("font-style","emphasis"===n.type?"italic":"normal").attr("class","text-inner-tspan").attr("font-weight","strong"===n.type?"bold":"normal");0===t?r.text(n.content):r.text(" "+n.content)}))}const cn=(e,n="",{style:t="",isTitle:r=!1,classes:u="",useHtmlLabels:o=!0,isNode:c=!0,width:s=200,addSvgBackground:l=!1}={})=>{if(i.l.info("createText",n,t,r,u,o,c,l),o){const r=function(e){const{children:n}=Ne(e);return n.map((function e(n){return"text"===n.type?n.value.replace(/\n/g,"<br/>"):"strong"===n.type?`<strong>${n.children.map(e).join("")}</strong>`:"emphasis"===n.type?`<em>${n.children.map(e).join("")}</em>`:"paragraph"===n.type?`<p>${n.children.map(e).join("")}</p>`:`Unsupported markdown: ${n.type}`})).join("")}(n),o=function(e,n,t,r,i=!1){const u=e.append("foreignObject"),o=u.append("xhtml:div"),c=n.label,s=n.isNode?"nodeLabel":"edgeLabel";var l,a;o.html(`\n    <span class="${s} ${r}" `+(n.labelStyle?'style="'+n.labelStyle+'"':"")+">"+c+"</span>"),l=o,(a=n.labelStyle)&&l.attr("style",a),o.style("display","table-cell"),o.style("white-space","nowrap"),o.style("max-width",t+"px"),o.attr("xmlns","http://www.w3.org/1999/xhtml"),i&&o.attr("class","labelBkg");let f=o.node().getBoundingClientRect();return f.width===t&&(o.style("display","table"),o.style("white-space","break-spaces"),o.style("width",t+"px"),f=o.node().getBoundingClientRect()),u.style("width",f.width),u.style("height",f.height),u.node()}(e,{isNode:c,label:(0,i.J)(r).replace(/fa[blrs]?:fa-[\w-]+/g,(e=>`<i class='${e.replace(":"," ")}'></i>`)),labelStyle:t.replace("fill:","color:")},s,u,l);return o}{const t=function(e,n,t,r=!1){const i=n.append("g"),u=i.insert("rect").attr("class","background"),o=i.append("text").attr("y","-10.1");let c=0;for(const n of t){const t=n=>rn(i,1.1,n)<=e,r=t(n)?[n]:en(n,t);for(const e of r)on(tn(o,c,1.1),e),c++}if(r){const e=o.node().getBBox(),n=2;return u.attr("x",-n).attr("y",-n).attr("width",e.width+2*n).attr("height",e.height+2*n),i.node()}return o.node()}(s,e,Ge(n),l);return t}}}}]);
"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[270],{95564:function(t,e,r){var o,n=this&&this.__extends||(o=function(t,e){return o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},o(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.HTMLAdaptor=void 0;var u=function(t){function e(e){var r=t.call(this,e.document)||this;return r.window=e,r.parser=new e.DOMParser,r}return n(e,t),e.prototype.parse=function(t,e){return void 0===e&&(e="text/html"),this.parser.parseFromString(t,e)},e.prototype.create=function(t,e){return e?this.document.createElementNS(e,t):this.document.createElement(t)},e.prototype.text=function(t){return this.document.createTextNode(t)},e.prototype.head=function(t){return t.head||t},e.prototype.body=function(t){return t.body||t},e.prototype.root=function(t){return t.documentElement||t},e.prototype.doctype=function(t){return t.doctype?"<!DOCTYPE ".concat(t.doctype.name,">"):""},e.prototype.tags=function(t,e,r){void 0===r&&(r=null);var o=r?t.getElementsByTagNameNS(r,e):t.getElementsByTagName(e);return Array.from(o)},e.prototype.getElements=function(t,e){var r,o,n=[];try{for(var u=i(t),a=u.next();!a.done;a=u.next()){var s=a.value;"string"==typeof s?n=n.concat(Array.from(this.document.querySelectorAll(s))):Array.isArray(s)||s instanceof this.window.NodeList||s instanceof this.window.HTMLCollection?n=n.concat(Array.from(s)):n.push(s)}}catch(t){r={error:t}}finally{try{a&&!a.done&&(o=u.return)&&o.call(u)}finally{if(r)throw r.error}}return n},e.prototype.contains=function(t,e){return t.contains(e)},e.prototype.parent=function(t){return t.parentNode},e.prototype.append=function(t,e){return t.appendChild(e)},e.prototype.insert=function(t,e){return this.parent(e).insertBefore(t,e)},e.prototype.remove=function(t){return this.parent(t).removeChild(t)},e.prototype.replace=function(t,e){return this.parent(e).replaceChild(t,e)},e.prototype.clone=function(t){return t.cloneNode(!0)},e.prototype.split=function(t,e){return t.splitText(e)},e.prototype.next=function(t){return t.nextSibling},e.prototype.previous=function(t){return t.previousSibling},e.prototype.firstChild=function(t){return t.firstChild},e.prototype.lastChild=function(t){return t.lastChild},e.prototype.childNodes=function(t){return Array.from(t.childNodes)},e.prototype.childNode=function(t,e){return t.childNodes[e]},e.prototype.kind=function(t){var e=t.nodeType;return 1===e||3===e||8===e?t.nodeName.toLowerCase():""},e.prototype.value=function(t){return t.nodeValue||""},e.prototype.textContent=function(t){return t.textContent},e.prototype.innerHTML=function(t){return t.innerHTML},e.prototype.outerHTML=function(t){return t.outerHTML},e.prototype.serializeXML=function(t){return(new this.window.XMLSerializer).serializeToString(t)},e.prototype.setAttribute=function(t,e,r,o){return void 0===o&&(o=null),o?(e=o.replace(/.*\//,"")+":"+e.replace(/^.*:/,""),t.setAttributeNS(o,e,r)):t.setAttribute(e,r)},e.prototype.getAttribute=function(t,e){return t.getAttribute(e)},e.prototype.removeAttribute=function(t,e){return t.removeAttribute(e)},e.prototype.hasAttribute=function(t,e){return t.hasAttribute(e)},e.prototype.allAttributes=function(t){return Array.from(t.attributes).map((function(t){return{name:t.name,value:t.value}}))},e.prototype.addClass=function(t,e){t.classList?t.classList.add(e):t.className=(t.className+" "+e).trim()},e.prototype.removeClass=function(t,e){t.classList?t.classList.remove(e):t.className=t.className.split(/ /).filter((function(t){return t!==e})).join(" ")},e.prototype.hasClass=function(t,e){return t.classList?t.classList.contains(e):t.className.split(/ /).indexOf(e)>=0},e.prototype.setStyle=function(t,e,r){t.style[e]=r},e.prototype.getStyle=function(t,e){return t.style[e]},e.prototype.allStyles=function(t){return t.style.cssText},e.prototype.insertRules=function(t,e){var r,o;try{for(var n=i(e.reverse()),u=n.next();!u.done;u=n.next()){var a=u.value;try{t.sheet.insertRule(a,0)}catch(t){console.warn("MathJax: can't insert css rule '".concat(a,"': ").concat(t.message))}}}catch(t){r={error:t}}finally{try{u&&!u.done&&(o=n.return)&&o.call(n)}finally{if(r)throw r.error}}},e.prototype.fontSize=function(t){var e=this.window.getComputedStyle(t);return parseFloat(e.fontSize)},e.prototype.fontFamily=function(t){return this.window.getComputedStyle(t).fontFamily||""},e.prototype.nodeSize=function(t,e,r){if(void 0===e&&(e=1),void 0===r&&(r=!1),r&&t.getBBox){var o=t.getBBox();return[o.width/e,o.height/e]}return[t.offsetWidth/e,t.offsetHeight/e]},e.prototype.nodeBBox=function(t){var e=t.getBoundingClientRect();return{left:e.left,right:e.right,top:e.top,bottom:e.bottom}},e}(r(66791).AbstractDOMAdaptor);e.HTMLAdaptor=u},90270:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.browserAdaptor=void 0;var o=r(95564);e.browserAdaptor=function(){return new o.HTMLAdaptor(window)}},66791:function(t,e){var r=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractDOMAdaptor=void 0;var o=function(){function t(t){void 0===t&&(t=null),this.document=t}return t.prototype.node=function(t,e,o,n){var i,u;void 0===e&&(e={}),void 0===o&&(o=[]);var a=this.create(t,n);this.setAttributes(a,e);try{for(var s=r(o),p=s.next();!p.done;p=s.next()){var l=p.value;this.append(a,l)}}catch(t){i={error:t}}finally{try{p&&!p.done&&(u=s.return)&&u.call(s)}finally{if(i)throw i.error}}return a},t.prototype.setAttributes=function(t,e){var o,n,i,u,a,s;if(e.style&&"string"!=typeof e.style)try{for(var p=r(Object.keys(e.style)),l=p.next();!l.done;l=p.next()){var c=l.value;this.setStyle(t,c.replace(/-([a-z])/g,(function(t,e){return e.toUpperCase()})),e.style[c])}}catch(t){o={error:t}}finally{try{l&&!l.done&&(n=p.return)&&n.call(p)}finally{if(o)throw o.error}}if(e.properties)try{for(var f=r(Object.keys(e.properties)),y=f.next();!y.done;y=f.next())t[c=y.value]=e.properties[c]}catch(t){i={error:t}}finally{try{y&&!y.done&&(u=f.return)&&u.call(f)}finally{if(i)throw i.error}}try{for(var d=r(Object.keys(e)),h=d.next();!h.done;h=d.next())"style"===(c=h.value)&&"string"!=typeof e.style||"properties"===c||this.setAttribute(t,c,e[c])}catch(t){a={error:t}}finally{try{h&&!h.done&&(s=d.return)&&s.call(d)}finally{if(a)throw a.error}}},t.prototype.replace=function(t,e){return this.insert(t,e),this.remove(e),e},t.prototype.childNode=function(t,e){return this.childNodes(t)[e]},t.prototype.allClasses=function(t){var e=this.getAttribute(t,"class");return e?e.replace(/  +/g," ").replace(/^ /,"").replace(/ $/,"").split(/ /):[]},t}();e.AbstractDOMAdaptor=o}}]);
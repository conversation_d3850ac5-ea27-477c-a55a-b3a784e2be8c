"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[901],{67863:(e,t,r)=>{r.d(t,{L:()=>s,c:()=>o});var i=r(20998),n=r(14421);const s=new i.Token("@jupyterlab/application:ILayoutRestorer","A service providing application layout restoration functionality. Use this to have your activities restored across page loads."),a="layout-restorer:data";class o{constructor(e){this._deferred=new Array,this._deferredMainArea=null,this._firstDone=!1,this._promisesDone=!1,this._promises=[],this._restored=new i.PromiseDelegate,this._trackers=new Set,this._widgets=new Map,this._mode="multiple-document",this._connector=e.connector,this._first=e.first,this._registry=e.registry,e.mode&&(this._mode=e.mode),this._first.then((()=>{this._firstDone=!0})).then((()=>Promise.all(this._promises))).then((()=>{this._promisesDone=!0,this._trackers.clear()})).then((()=>{this._restored.resolve(void 0)}))}get isDeferred(){return this._deferred.length>0}get restored(){return this._restored.promise}add(e,t){d.nameProperty.set(e,t),this._widgets.set(t,e),e.disposed.connect(this._onWidgetDisposed,this)}async fetch(){var e;const t={fresh:!0,mainArea:null,downArea:null,leftArea:null,rightArea:null,topArea:null,relativeSizes:null},r=this._connector.fetch(a);try{const[i]=await Promise.all([r,this.restored]);if(!i)return t;const{main:n,down:s,left:a,right:o,relativeSizes:d,top:c}=i,l=!1;let u=null;"multiple-document"===this._mode?u=this._rehydrateMainArea(n):this._deferredMainArea=n;const h=this._rehydrateDownArea(s);return{fresh:l,mainArea:u,downArea:h,leftArea:this._rehydrateSideArea(a),rightArea:this._rehydrateSideArea(o),relativeSizes:d||null,topArea:null!==(e=c)&&void 0!==e?e:null}}catch(e){return t}}async restore(e,t){if(this._firstDone)throw new Error("restore() must be called before `first` has resolved.");const{namespace:r}=e;if(this._trackers.has(r))throw new Error(`The tracker "${r}" is already restored.`);const{args:n,command:s,name:a,when:o}=t;this._trackers.add(r),e.widgetAdded.connect(((e,t)=>{const i=a(t);i&&this.add(t,`${r}:${i}`)}),this),e.widgetUpdated.connect(((e,t)=>{const i=a(t);if(i){const e=`${r}:${i}`;d.nameProperty.set(t,e),this._widgets.set(e,t)}}));const c=this._first;if("multiple-document"==this._mode){const t=e.restore({args:n||(()=>i.JSONExt.emptyObject),command:s,connector:this._connector,name:a,registry:this._registry,when:o?[c].concat(o):c}).catch((e=>{console.error(e)}));return this._promises.push(t),t}e.defer({args:n||(()=>i.JSONExt.emptyObject),command:s,connector:this._connector,name:a,registry:this._registry,when:o?[c].concat(o):c}),this._deferred.push(e)}async restoreDeferred(){if(!this.isDeferred)return null;const e=Promise.resolve(),t=this._deferred.map((t=>e.then((()=>t.restore()))));return this._deferred.length=0,await Promise.all(t),this._rehydrateMainArea(this._deferredMainArea)}save(e){if(!this._promisesDone){const e="save() was called prematurely.";return console.warn(e),Promise.reject(e)}const t={};return t.main=this.isDeferred?this._deferredMainArea:this._dehydrateMainArea(e.mainArea),t.down=this._dehydrateDownArea(e.downArea),t.left=this._dehydrateSideArea(e.leftArea),t.right=this._dehydrateSideArea(e.rightArea),t.relativeSizes=e.relativeSizes,t.top={...e.topArea},this._connector.save(a,t)}_dehydrateMainArea(e){return e?d.serializeMain(e):null}_rehydrateMainArea(e){return e?d.deserializeMain(e,this._widgets):null}_dehydrateDownArea(e){if(!e)return null;const t={size:e.size};if(e.currentWidget){const r=d.nameProperty.get(e.currentWidget);r&&(t.current=r)}return e.widgets&&(t.widgets=e.widgets.map((e=>d.nameProperty.get(e))).filter((e=>!!e))),t}_rehydrateDownArea(e){var t;if(!e)return{currentWidget:null,size:0,widgets:null};const r=this._widgets,i=e.current&&r.has(`${e.current}`)?r.get(`${e.current}`):null,n=Array.isArray(e.widgets)?e.widgets.map((e=>r.has(`${e}`)?r.get(`${e}`):null)).filter((e=>!!e)):null;return{currentWidget:i,size:null!==(t=e.size)&&void 0!==t?t:0,widgets:n}}_dehydrateSideArea(e){if(!e)return null;const t={collapsed:e.collapsed,visible:e.visible};if(e.currentWidget){const r=d.nameProperty.get(e.currentWidget);r&&(t.current=r)}return e.widgets&&(t.widgets=e.widgets.map((e=>d.nameProperty.get(e))).filter((e=>!!e))),e.widgetStates&&(t.widgetStates=e.widgetStates),t}_rehydrateSideArea(e){var t,r;if(!e)return{collapsed:!0,currentWidget:null,visible:!0,widgets:null,widgetStates:{null:{sizes:null,expansionStates:null}}};const i=this._widgets,n=null!==(t=e.collapsed)&&void 0!==t&&t,s=e.current&&i.has(`${e.current}`)?i.get(`${e.current}`):null,a=Array.isArray(e.widgets)?e.widgets.map((e=>i.has(`${e}`)?i.get(`${e}`):null)).filter((e=>!!e)):null,o=e.widgetStates;return{collapsed:n,currentWidget:s,widgets:a,visible:null===(r=e.visible)||void 0===r||r,widgetStates:o}}_onWidgetDisposed(e){const t=d.nameProperty.get(e);this._widgets.delete(t)}}var d;!function(e){function t(r){return r&&r.type?"tab-area"===r.type?{type:"tab-area",currentIndex:r.currentIndex,widgets:r.widgets.map((t=>e.nameProperty.get(t))).filter((e=>!!e))}:{type:"split-area",orientation:r.orientation,sizes:r.sizes,children:r.children.map(t).filter((e=>!!e))}:null}function r(e,t){if(!e)return null;const i=e.type||"unknown";if("unknown"===i||"tab-area"!==i&&"split-area"!==i)return console.warn(`Attempted to deserialize unknown type: ${i}`),null;if("tab-area"===i){const{currentIndex:r,widgets:i}=e,n={type:"tab-area",currentIndex:r||0,widgets:i&&i.map((e=>t.get(e))).filter((e=>!!e))||[]};return n.currentIndex>n.widgets.length-1&&(n.currentIndex=0),n}const{orientation:n,sizes:s,children:a}=e;return{type:"split-area",orientation:n,sizes:s||[],children:a&&a.map((e=>r(e,t))).filter((e=>!!e))||[]}}e.nameProperty=new n.AttachedProperty({name:"name",create:e=>""}),e.serializeMain=function(r){const i={dock:r&&r.dock&&t(r.dock.main)||null};if(r&&r.currentWidget){const t=e.nameProperty.get(r.currentWidget);t&&(i.current=t)}return i},e.deserializeMain=function(e,t){if(!e)return null;const i=e.current||null,n=e.dock||null;return{currentWidget:i&&t.has(i)&&t.get(i)||null,dock:n?{main:r(n,t)}:null}}}(d||(d={}))},75677:(e,t,r)=>{r.d(t,{aX:()=>y,as:()=>h,w2:()=>u});var i=r(12982),n=r(35312),s=r(70856),a=r(71677),o=r(68239),d=r(20998),c=r(14421),l=r(67863);const u=new d.Token("@jupyterlab/application:IMimeDocumentTracker","A widget tracker for documents rendered using a mime renderer extension. Use this if you want to list and interact with documents rendered by such extensions.");function h(e){const t=[],r=new i.WidgetTracker({namespace:"application-mimedocuments"});return e.forEach((e=>{let i=e.default;e.hasOwnProperty("__esModule")||(i=e),Array.isArray(i)||(i=[i]),i.forEach((e=>{t.push(y(r,e))}))})),t.push({id:"@jupyterlab/application:mimedocument",description:"Provides a mime document widget tracker.",optional:[l.L],provides:u,autoStart:!0,activate:(e,t)=>(t&&t.restore(r,{command:"docmanager:open",args:e=>({path:e.context.path,factory:g.factoryNameProperty.get(e)}),name:e=>`${e.context.path}:${g.factoryNameProperty.get(e)}`}),r)}),t}function y(e,t){return{id:t.id,description:t.description,requires:[s.IRenderMimeRegistry,a.ITranslator],autoStart:!0,activate:(r,i,s)=>{if(void 0!==t.rank?i.addFactory(t.rendererFactory,t.rank):i.addFactory(t.rendererFactory),!t.documentWidgetFactoryOptions)return;const a=r.docRegistry;let d=[];d=Array.isArray(t.documentWidgetFactoryOptions)?t.documentWidgetFactoryOptions:[t.documentWidgetFactoryOptions],t.fileTypes&&t.fileTypes.forEach((e=>{e.icon&&(e={...e,icon:o.LabIcon.resolve({icon:e.icon})}),r.docRegistry.addFileType(e)})),d.forEach((r=>{const o=r.toolbarFactory?e=>r.toolbarFactory(e.content.renderer):void 0,d=new n.MimeDocumentFactory({renderTimeout:t.renderTimeout,dataType:t.dataType,rendermime:i,modelName:r.modelName,name:r.name,primaryFileType:a.getFileType(r.primaryFileType),fileTypes:r.fileTypes,defaultFor:r.defaultFor,defaultRendered:r.defaultRendered,toolbarFactory:o,translator:s,factory:t.rendererFactory});a.addWidgetFactory(d),d.widgetCreated.connect(((t,r)=>{g.factoryNameProperty.set(r,d.name),r.context.pathChanged.connect((()=>{e.save(r)})),e.add(r)}))}))}}}var g;!function(e){e.factoryNameProperty=new c.AttachedProperty({name:"factoryName",create:()=>{}})}(g||(g={}))},20389:(e,t,r)=>{r.d(t,{J:()=>s});var i=r(2549),n=r(81997);class s{constructor(e){this._busyCount=0,this._dirtyCount=0,this._busySignal=new n.Signal(e),this._dirtySignal=new n.Signal(e)}get busySignal(){return this._busySignal}get dirtySignal(){return this._dirtySignal}get isBusy(){return this._busyCount>0}get isDirty(){return this._dirtyCount>0}setDirty(){const e=this.isDirty;return this._dirtyCount++,this.isDirty!==e&&this._dirtySignal.emit(this.isDirty),new i.DisposableDelegate((()=>{const e=this.isDirty;this._dirtyCount=Math.max(0,this._dirtyCount-1),this.isDirty!==e&&this._dirtySignal.emit(this.isDirty)}))}setBusy(){const e=this.isBusy;return this._busyCount++,this.isBusy!==e&&this._busySignal.emit(this.isBusy),new i.DisposableDelegate((()=>{const e=this.isBusy;this._busyCount--,this.isBusy!==e&&this._busySignal.emit(this.isBusy)}))}}}}]);
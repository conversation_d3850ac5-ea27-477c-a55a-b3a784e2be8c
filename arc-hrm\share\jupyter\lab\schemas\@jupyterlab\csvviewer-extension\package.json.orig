{"name": "@jupyterlab/csvviewer-extension", "version": "4.1.8", "description": "JupyterLab - CSV Widget Extension", "homepage": "https://github.com/jupyterlab/jupyterlab", "bugs": {"url": "https://github.com/jupyterlab/jupyterlab/issues"}, "repository": {"type": "git", "url": "https://github.com/jupyterlab/jupyterlab.git"}, "license": "BSD-3-<PERSON><PERSON>", "author": "Project Jupyter", "sideEffects": ["style/**/*.css", "style/index.js"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "directories": {"lib": "lib/"}, "files": ["lib/*.d.ts", "lib/*.js.map", "lib/*.js", "schema/*.json", "style/**/*.css", "style/index.js", "src/**/*.{ts,tsx}"], "scripts": {"build": "tsc -b", "clean": "rimraf lib && rimraf tsconfig.tsbuildinfo", "docs": "typedoc src", "watch": "tsc -b --watch"}, "dependencies": {"@jupyterlab/application": "^4.1.8", "@jupyterlab/apputils": "^4.2.8", "@jupyterlab/csvviewer": "^4.1.8", "@jupyterlab/docregistry": "^4.1.8", "@jupyterlab/documentsearch": "^4.1.8", "@jupyterlab/mainmenu": "^4.1.8", "@jupyterlab/observables": "^5.1.8", "@jupyterlab/settingregistry": "^4.1.8", "@jupyterlab/translation": "^4.1.8", "@lumino/datagrid": "^2.3.0", "@lumino/widgets": "^2.3.1"}, "devDependencies": {"rimraf": "~5.0.5", "typedoc": "~0.24.7", "typescript": "~5.1.6"}, "publishConfig": {"access": "public"}, "jupyterlab": {"extension": true, "schemaDir": "schema"}, "styleModule": "style/index.js"}
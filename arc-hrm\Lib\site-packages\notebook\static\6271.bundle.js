"use strict";(self.webpackChunk_JUPYTERLAB_CORE_OUTPUT=self.webpackChunk_JUPYTERLAB_CORE_OUTPUT||[]).push([[6271],{76271:(e,t,n)=>{n.r(t),n.d(t,{commonmarkLanguage:()=>je,deleteMarkupBackward:()=>et,insertNewlineContinueMarkup:()=>Ye,markdown:()=>rt,markdownKeymap:()=>tt,markdownLanguage:()=>Qe});var r,s=n(17811),i=n(30890),o=n(7201),a=n(56318),l=n(73887),h=n(66361);class f{static create(e,t,n,r,s){return new f(e,t,n,r+(r<<8)+e+(t<<4)|0,s,[],[])}constructor(e,t,n,r,s,i,o){this.type=e,this.value=t,this.from=n,this.hash=r,this.end=s,this.children=i,this.positions=o,this.hashProp=[[l.NodeProp.contextHash,r]]}addChild(e,t){e.prop(l.NodeProp.contextHash)!=this.hash&&(e=new l.Tree(e.type,e.children,e.positions,e.length,this.hashProp)),this.children.push(e),this.positions.push(t)}toTree(e,t=this.end){let n=this.children.length-1;return n>=0&&(t=Math.max(t,this.positions[n]+this.children[n].length+this.from)),new l.Tree(e.types[this.type],this.children,this.positions,t-this.from).balance({makeTree:(e,t,n)=>new l.Tree(l.NodeType.none,e,t,n,this.hashProp)})}}!function(e){e[e.Document=1]="Document",e[e.CodeBlock=2]="CodeBlock",e[e.FencedCode=3]="FencedCode",e[e.Blockquote=4]="Blockquote",e[e.HorizontalRule=5]="HorizontalRule",e[e.BulletList=6]="BulletList",e[e.OrderedList=7]="OrderedList",e[e.ListItem=8]="ListItem",e[e.ATXHeading1=9]="ATXHeading1",e[e.ATXHeading2=10]="ATXHeading2",e[e.ATXHeading3=11]="ATXHeading3",e[e.ATXHeading4=12]="ATXHeading4",e[e.ATXHeading5=13]="ATXHeading5",e[e.ATXHeading6=14]="ATXHeading6",e[e.SetextHeading1=15]="SetextHeading1",e[e.SetextHeading2=16]="SetextHeading2",e[e.HTMLBlock=17]="HTMLBlock",e[e.LinkReference=18]="LinkReference",e[e.Paragraph=19]="Paragraph",e[e.CommentBlock=20]="CommentBlock",e[e.ProcessingInstructionBlock=21]="ProcessingInstructionBlock",e[e.Escape=22]="Escape",e[e.Entity=23]="Entity",e[e.HardBreak=24]="HardBreak",e[e.Emphasis=25]="Emphasis",e[e.StrongEmphasis=26]="StrongEmphasis",e[e.Link=27]="Link",e[e.Image=28]="Image",e[e.InlineCode=29]="InlineCode",e[e.HTMLTag=30]="HTMLTag",e[e.Comment=31]="Comment",e[e.ProcessingInstruction=32]="ProcessingInstruction",e[e.URL=33]="URL",e[e.HeaderMark=34]="HeaderMark",e[e.QuoteMark=35]="QuoteMark",e[e.ListMark=36]="ListMark",e[e.LinkMark=37]="LinkMark",e[e.EmphasisMark=38]="EmphasisMark",e[e.CodeMark=39]="CodeMark",e[e.CodeText=40]="CodeText",e[e.CodeInfo=41]="CodeInfo",e[e.LinkTitle=42]="LinkTitle",e[e.LinkLabel=43]="LinkLabel"}(r||(r={}));class c{constructor(e,t){this.start=e,this.content=t,this.marks=[],this.parsers=[]}}class d{constructor(){this.text="",this.baseIndent=0,this.basePos=0,this.depth=0,this.markers=[],this.pos=0,this.indent=0,this.next=-1}forward(){this.basePos>this.pos&&this.forwardInner()}forwardInner(){let e=this.skipSpace(this.basePos);this.indent=this.countIndent(e,this.pos,this.indent),this.pos=e,this.next=e==this.text.length?-1:this.text.charCodeAt(e)}skipSpace(e){return m(this.text,e)}reset(e){for(this.text=e,this.baseIndent=this.basePos=this.pos=this.indent=0,this.forwardInner(),this.depth=1;this.markers.length;)this.markers.pop()}moveBase(e){this.basePos=e,this.baseIndent=this.countIndent(e,this.pos,this.indent)}moveBaseColumn(e){this.baseIndent=e,this.basePos=this.findColumn(e)}addMarker(e){this.markers.push(e)}countIndent(e,t=0,n=0){for(let r=t;r<e;r++)n+=9==this.text.charCodeAt(r)?4-n%4:1;return n}findColumn(e){let t=0;for(let n=0;t<this.text.length&&n<e;t++)n+=9==this.text.charCodeAt(t)?4-n%4:1;return t}scrub(){if(!this.baseIndent)return this.text;let e="";for(let t=0;t<this.basePos;t++)e+=" ";return e+this.text.slice(this.basePos)}}function u(e,t,n){if(n.pos==n.text.length||e!=t.block&&n.indent>=t.stack[n.depth+1].value+n.baseIndent)return!0;if(n.indent>=n.baseIndent+4)return!1;let s=(e.type==r.OrderedList?y:C)(n,t,!1);return s>0&&(e.type!=r.BulletList||L(n,t,!1)<0)&&n.text.charCodeAt(n.pos+s-1)==e.value}const p={[r.Blockquote]:(e,t,n)=>62==n.next&&(n.markers.push(J(r.QuoteMark,t.lineStart+n.pos,t.lineStart+n.pos+1)),n.moveBase(n.pos+(g(n.text.charCodeAt(n.pos+1))?2:1)),e.end=t.lineStart+n.text.length,!0),[r.ListItem]:(e,t,n)=>!(n.indent<n.baseIndent+e.value&&n.next>-1||(n.moveBaseColumn(n.baseIndent+e.value),0)),[r.OrderedList]:u,[r.BulletList]:u,[r.Document]:()=>!0};function g(e){return 32==e||9==e||10==e||13==e}function m(e,t=0){for(;t<e.length&&g(e.charCodeAt(t));)t++;return t}function k(e,t,n){for(;t>n&&g(e.charCodeAt(t-1));)t--;return t}function x(e){if(96!=e.next&&126!=e.next)return-1;let t=e.pos+1;for(;t<e.text.length&&e.text.charCodeAt(t)==e.next;)t++;if(t<e.pos+3)return-1;if(96==e.next)for(let n=t;n<e.text.length;n++)if(96==e.text.charCodeAt(n))return-1;return t}function b(e){return 62!=e.next?-1:32==e.text.charCodeAt(e.pos+1)?2:1}function L(e,t,n){if(42!=e.next&&45!=e.next&&95!=e.next)return-1;let r=1;for(let t=e.pos+1;t<e.text.length;t++){let n=e.text.charCodeAt(t);if(n==e.next)r++;else if(!g(n))return-1}return n&&45==e.next&&T(e)>-1&&e.depth==t.stack.length||r<3?-1:1}function S(e,t){for(let n=e.stack.length-1;n>=0;n--)if(e.stack[n].type==t)return!0;return!1}function C(e,t,n){return 45!=e.next&&43!=e.next&&42!=e.next||e.pos!=e.text.length-1&&!g(e.text.charCodeAt(e.pos+1))||!(!n||S(t,r.BulletList)||e.skipSpace(e.pos+2)<e.text.length)?-1:1}function y(e,t,n){let s=e.pos,i=e.next;for(;i>=48&&i<=57;){if(s++,s==e.text.length)return-1;i=e.text.charCodeAt(s)}return s==e.pos||s>e.pos+9||46!=i&&41!=i||s<e.text.length-1&&!g(e.text.charCodeAt(s+1))||n&&!S(t,r.OrderedList)&&(e.skipSpace(s+1)==e.text.length||s>e.pos+1||49!=e.next)?-1:s+1-e.pos}function w(e){if(35!=e.next)return-1;let t=e.pos+1;for(;t<e.text.length&&35==e.text.charCodeAt(t);)t++;if(t<e.text.length&&32!=e.text.charCodeAt(t))return-1;let n=t-e.pos;return n>6?-1:n}function T(e){if(45!=e.next&&61!=e.next||e.indent>=e.baseIndent+4)return-1;let t=e.pos+1;for(;t<e.text.length&&e.text.charCodeAt(t)==e.next;)t++;let n=t;for(;t<e.text.length&&g(e.text.charCodeAt(t));)t++;return t==e.text.length?n:-1}const A=/^[ \t]*$/,I=/-->/,B=/\?>/,E=[[/^<(?:script|pre|style)(?:\s|>|$)/i,/<\/(?:script|pre|style)>/i],[/^\s*<!--/,I],[/^\s*<\?/,B],[/^\s*<![A-Z]/,/>/],[/^\s*<!\[CDATA\[/,/\]\]>/],[/^\s*<\/?(?:address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h1|h2|h3|h4|h5|h6|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul)(?:\s|\/?>|$)/i,A],[/^\s*(?:<\/[a-z][\w-]*\s*>|<[a-z][\w-]*(\s+[a-z:_][\w-.]*(?:\s*=\s*(?:[^\s"'=<>`]+|'[^']*'|"[^"]*"))?)*\s*>)\s*$/i,A]];function P(e,t,n){if(60!=e.next)return-1;let r=e.text.slice(e.pos);for(let e=0,t=E.length-(n?1:0);e<t;e++)if(E[e][0].test(r))return e;return-1}function M(e,t){let n=e.countIndent(t,e.pos,e.indent),r=e.countIndent(e.skipSpace(t),t,n);return r>=n+5?n+1:r}function H(e,t,n){let s=e.length-1;s>=0&&e[s].to==t&&e[s].type==r.CodeText?e[s].to=n:e.push(J(r.CodeText,t,n))}const v={LinkReference:void 0,IndentedCode(e,t){let n=t.baseIndent+4;if(t.indent<n)return!1;let s=t.findColumn(n),i=e.lineStart+s,o=e.lineStart+t.text.length,a=[],l=[];for(H(a,i,o);e.nextLine()&&t.depth>=e.stack.length;)if(t.pos==t.text.length){H(l,e.lineStart-1,e.lineStart);for(let e of t.markers)l.push(e)}else{if(t.indent<n)break;{if(l.length){for(let e of l)e.type==r.CodeText?H(a,e.from,e.to):a.push(e);l=[]}H(a,e.lineStart-1,e.lineStart);for(let e of t.markers)a.push(e);o=e.lineStart+t.text.length;let n=e.lineStart+t.findColumn(t.baseIndent+4);n<o&&H(a,n,o)}}return l.length&&(l=l.filter((e=>e.type!=r.CodeText)),l.length&&(t.markers=l.concat(t.markers))),e.addNode(e.buffer.writeElements(a,-i).finish(r.CodeBlock,o-i),i),!0},FencedCode(e,t){let n=x(t);if(n<0)return!1;let s=e.lineStart+t.pos,i=t.next,o=n-t.pos,a=t.skipSpace(n),l=k(t.text,t.text.length,a),h=[J(r.CodeMark,s,s+o)];a<l&&h.push(J(r.CodeInfo,e.lineStart+a,e.lineStart+l));for(let n=!0;e.nextLine()&&t.depth>=e.stack.length;n=!1){let s=t.pos;if(t.indent-t.baseIndent<4)for(;s<t.text.length&&t.text.charCodeAt(s)==i;)s++;if(s-t.pos>=o&&t.skipSpace(s)==t.text.length){for(let e of t.markers)h.push(e);h.push(J(r.CodeMark,e.lineStart+t.pos,e.lineStart+s)),e.nextLine();break}{n||H(h,e.lineStart-1,e.lineStart);for(let e of t.markers)h.push(e);let r=e.lineStart+t.basePos,s=e.lineStart+t.text.length;r<s&&H(h,r,s)}}return e.addNode(e.buffer.writeElements(h,-s).finish(r.FencedCode,e.prevLineEnd()-s),s),!0},Blockquote(e,t){let n=b(t);return!(n<0)&&(e.startContext(r.Blockquote,t.pos),e.addNode(r.QuoteMark,e.lineStart+t.pos,e.lineStart+t.pos+1),t.moveBase(t.pos+n),null)},HorizontalRule(e,t){if(L(t,e,!1)<0)return!1;let n=e.lineStart+t.pos;return e.nextLine(),e.addNode(r.HorizontalRule,n),!0},BulletList(e,t){let n=C(t,e,!1);if(n<0)return!1;e.block.type!=r.BulletList&&e.startContext(r.BulletList,t.basePos,t.next);let s=M(t,t.pos+1);return e.startContext(r.ListItem,t.basePos,s-t.baseIndent),e.addNode(r.ListMark,e.lineStart+t.pos,e.lineStart+t.pos+n),t.moveBaseColumn(s),null},OrderedList(e,t){let n=y(t,e,!1);if(n<0)return!1;e.block.type!=r.OrderedList&&e.startContext(r.OrderedList,t.basePos,t.text.charCodeAt(t.pos+n-1));let s=M(t,t.pos+n);return e.startContext(r.ListItem,t.basePos,s-t.baseIndent),e.addNode(r.ListMark,e.lineStart+t.pos,e.lineStart+t.pos+n),t.moveBaseColumn(s),null},ATXHeading(e,t){let n=w(t);if(n<0)return!1;let s=t.pos,i=e.lineStart+s,o=k(t.text,t.text.length,s),a=o;for(;a>s&&t.text.charCodeAt(a-1)==t.next;)a--;a!=o&&a!=s&&g(t.text.charCodeAt(a-1))||(a=t.text.length);let l=e.buffer.write(r.HeaderMark,0,n).writeElements(e.parser.parseInline(t.text.slice(s+n+1,a),i+n+1),-i);a<t.text.length&&l.write(r.HeaderMark,a-s,o-s);let h=l.finish(r.ATXHeading1-1+n,t.text.length-s);return e.nextLine(),e.addNode(h,i),!0},HTMLBlock(e,t){let n=P(t,0,!1);if(n<0)return!1;let s=e.lineStart+t.pos,i=E[n][1],o=[],a=i!=A;for(;!i.test(t.text)&&e.nextLine();){if(t.depth<e.stack.length){a=!1;break}for(let e of t.markers)o.push(e)}a&&e.nextLine();let l=i==I?r.CommentBlock:i==B?r.ProcessingInstructionBlock:r.HTMLBlock,h=e.prevLineEnd();return e.addNode(e.buffer.writeElements(o,-s).finish(l,h-s),s),!0},SetextHeading:void 0};class N{constructor(e){this.stage=0,this.elts=[],this.pos=0,this.start=e.start,this.advance(e.content)}nextLine(e,t,n){if(-1==this.stage)return!1;let r=n.content+"\n"+t.scrub(),s=this.advance(r);return s>-1&&s<r.length&&this.complete(e,n,s)}finish(e,t){return(2==this.stage||3==this.stage)&&m(t.content,this.pos)==t.content.length&&this.complete(e,t,t.content.length)}complete(e,t,n){return e.addLeafElement(t,J(r.LinkReference,this.start,this.start+n,this.elts)),!0}nextStage(e){return e?(this.pos=e.to-this.start,this.elts.push(e),this.stage++,!0):(!1===e&&(this.stage=-1),!1)}advance(e){for(;;){if(-1==this.stage)return-1;if(0==this.stage){if(!this.nextStage(le(e,this.pos,this.start,!0)))return-1;if(58!=e.charCodeAt(this.pos))return this.stage=-1;this.elts.push(J(r.LinkMark,this.pos+this.start,this.pos+this.start+1)),this.pos++}else{if(1!=this.stage){if(2==this.stage){let t=m(e,this.pos),n=0;if(t>this.pos){let r=ae(e,t,this.start);if(r){let t=O(e,r.to-this.start);t>0&&(this.nextStage(r),n=t)}}return n||(n=O(e,this.pos)),n>0&&n<e.length?n:-1}return O(e,this.pos)}if(!this.nextStage(oe(e,m(e,this.pos),this.start)))return-1}}}}function O(e,t){for(;t<e.length;t++){let n=e.charCodeAt(t);if(10==n)break;if(!g(n))return-1}return t}class R{nextLine(e,t,n){let s=t.depth<e.stack.length?-1:T(t),i=t.next;if(s<0)return!1;let o=J(r.HeaderMark,e.lineStart+t.pos,e.lineStart+s);return e.nextLine(),e.addLeafElement(n,J(61==i?r.SetextHeading1:r.SetextHeading2,n.start,e.prevLineEnd(),[...e.parser.parseInline(n.content,n.start),o])),!0}finish(){return!1}}const X={LinkReference:(e,t)=>91==t.content.charCodeAt(0)?new N(t):null,SetextHeading:()=>new R},D=[(e,t)=>w(t)>=0,(e,t)=>x(t)>=0,(e,t)=>b(t)>=0,(e,t)=>C(t,e,!0)>=0,(e,t)=>y(t,e,!0)>=0,(e,t)=>L(t,e,!0)>=0,(e,t)=>P(t,0,!0)>=0],z={text:"",end:0};class ${constructor(e,t,n,s){this.parser=e,this.input=t,this.ranges=s,this.line=new d,this.atEnd=!1,this.reusePlaceholders=new Map,this.stoppedAt=null,this.rangeI=0,this.to=s[s.length-1].to,this.lineStart=this.absoluteLineStart=this.absoluteLineEnd=s[0].from,this.block=f.create(r.Document,0,this.lineStart,0,0),this.stack=[this.block],this.fragments=n.length?new de(n,t):null,this.readLine()}get parsedPos(){return this.absoluteLineStart}advance(){if(null!=this.stoppedAt&&this.absoluteLineStart>this.stoppedAt)return this.finish();let{line:e}=this;for(;;){for(;e.depth<this.stack.length;)this.finishContext();for(let t of e.markers)this.addNode(t.type,t.from,t.to);if(e.pos<e.text.length)break;if(!this.nextLine())return this.finish()}if(this.fragments&&this.reuseFragment(e.basePos))return null;e:for(;;){for(let t of this.parser.blockParsers)if(t){let n=t(this,e);if(0!=n){if(1==n)return null;e.forward();continue e}}break}let t=new c(this.lineStart+e.pos,e.text.slice(e.pos));for(let e of this.parser.leafBlockParsers)if(e){let n=e(this,t);n&&t.parsers.push(n)}e:for(;this.nextLine()&&e.pos!=e.text.length;){if(e.indent<e.baseIndent+4)for(let n of this.parser.endLeafBlock)if(n(this,e,t))break e;for(let n of t.parsers)if(n.nextLine(this,e,t))return null;t.content+="\n"+e.scrub();for(let n of e.markers)t.marks.push(n)}return this.finishLeaf(t),null}stopAt(e){if(null!=this.stoppedAt&&this.stoppedAt<e)throw new RangeError("Can't move stoppedAt forward");this.stoppedAt=e}reuseFragment(e){if(!this.fragments.moveTo(this.absoluteLineStart+e,this.absoluteLineStart)||!this.fragments.matches(this.block.hash))return!1;let t=this.fragments.takeNodes(this);return!!t&&(this.absoluteLineStart+=t,this.lineStart=ue(this.absoluteLineStart,this.ranges),this.moveRangeI(),this.absoluteLineStart<this.to?(this.lineStart++,this.absoluteLineStart++,this.readLine()):(this.atEnd=!0,this.readLine()),!0)}get depth(){return this.stack.length}parentType(e=this.depth-1){return this.parser.nodeSet.types[this.stack[e].type]}nextLine(){return this.lineStart+=this.line.text.length,this.absoluteLineEnd>=this.to?(this.absoluteLineStart=this.absoluteLineEnd,this.atEnd=!0,this.readLine(),!1):(this.lineStart++,this.absoluteLineStart=this.absoluteLineEnd+1,this.moveRangeI(),this.readLine(),!0)}moveRangeI(){for(;this.rangeI<this.ranges.length-1&&this.absoluteLineStart>=this.ranges[this.rangeI].to;)this.rangeI++,this.absoluteLineStart=Math.max(this.absoluteLineStart,this.ranges[this.rangeI].from)}scanLine(e){let t=z;if(t.end=e,e>=this.to)t.text="";else if(t.text=this.lineChunkAt(e),t.end+=t.text.length,this.ranges.length>1){let e=this.absoluteLineStart,n=this.rangeI;for(;this.ranges[n].to<t.end;){n++;let r=this.ranges[n].from,s=this.lineChunkAt(r);t.end=r+s.length,t.text=t.text.slice(0,this.ranges[n-1].to-e)+s,e=t.end-t.text.length}}return t}readLine(){let{line:e}=this,{text:t,end:n}=this.scanLine(this.absoluteLineStart);for(this.absoluteLineEnd=n,e.reset(t);e.depth<this.stack.length;e.depth++){let t=this.stack[e.depth],n=this.parser.skipContextMarkup[t.type];if(!n)throw new Error("Unhandled block context "+r[t.type]);if(!n(t,this,e))break;e.forward()}}lineChunkAt(e){let t,n=this.input.chunk(e);if(this.input.lineChunks)t="\n"==n?"":n;else{let e=n.indexOf("\n");t=e<0?n:n.slice(0,e)}return e+t.length>this.to?t.slice(0,this.to-e):t}prevLineEnd(){return this.atEnd?this.lineStart:this.lineStart-1}startContext(e,t,n=0){this.block=f.create(e,n,this.lineStart+t,this.block.hash,this.lineStart+this.line.text.length),this.stack.push(this.block)}startComposite(e,t,n=0){this.startContext(this.parser.getNodeType(e),t,n)}addNode(e,t,n){"number"==typeof e&&(e=new l.Tree(this.parser.nodeSet.types[e],Z,Z,(null!=n?n:this.prevLineEnd())-t)),this.block.addChild(e,t-this.block.from)}addElement(e){this.block.addChild(e.toTree(this.parser.nodeSet),e.from-this.block.from)}addLeafElement(e,t){this.addNode(this.buffer.writeElements(fe(t.children,e.marks),-t.from).finish(t.type,t.to-t.from),t.from)}finishContext(){let e=this.stack.pop(),t=this.stack[this.stack.length-1];t.addChild(e.toTree(this.parser.nodeSet),e.from-t.from),this.block=t}finish(){for(;this.stack.length>1;)this.finishContext();return this.addGaps(this.block.toTree(this.parser.nodeSet,this.lineStart))}addGaps(e){return this.ranges.length>1?U(this.ranges,0,e.topNode,this.ranges[0].from,this.reusePlaceholders):e}finishLeaf(e){for(let t of e.parsers)if(t.finish(this,e))return;let t=fe(this.parser.parseInline(e.content,e.start),e.marks);this.addNode(this.buffer.writeElements(t,-e.start).finish(r.Paragraph,e.content.length),e.start)}elt(e,t,n,r){return"string"==typeof e?J(this.parser.getNodeType(e),t,n,r):new G(e,t)}get buffer(){return new K(this.parser.nodeSet)}}function U(e,t,n,r,s){let i=e[t].to,o=[],a=[],h=n.from+r;function f(n,s){for(;s?n>=i:n>i;){let s=e[t+1].from-i;r+=s,n+=s,t++,i=e[t].to}}for(let l=n.firstChild;l;l=l.nextSibling){f(l.from+r,!0);let n,c=l.from+r,d=s.get(l.tree);d?n=d:l.to+r>i?(n=U(e,t,l,r,s),f(l.to+r,!1)):n=l.toTree(),o.push(n),a.push(c-h)}return f(n.to+r,!1),new l.Tree(n.type,o,a,n.to+r-h,n.tree?n.tree.propValues:void 0)}class F extends l.Parser{constructor(e,t,n,r,s,i,o,a,l){super(),this.nodeSet=e,this.blockParsers=t,this.leafBlockParsers=n,this.blockNames=r,this.endLeafBlock=s,this.skipContextMarkup=i,this.inlineParsers=o,this.inlineNames=a,this.wrappers=l,this.nodeTypes=Object.create(null);for(let t of e.types)this.nodeTypes[t.name]=t.id}createParse(e,t,n){let r=new $(this,e,t,n);for(let s of this.wrappers)r=s(r,e,t,n);return r}configure(e){let t=q(e);if(!t)return this;let{nodeSet:n,skipContextMarkup:s}=this,i=this.blockParsers.slice(),o=this.leafBlockParsers.slice(),a=this.blockNames.slice(),f=this.inlineParsers.slice(),c=this.inlineNames.slice(),d=this.endLeafBlock.slice(),u=this.wrappers;if(_(t.defineNodes)){s=Object.assign({},s);let e,i=n.types.slice();for(let n of t.defineNodes){let{name:t,block:o,composite:a,style:f}="string"==typeof n?{name:n}:n;if(i.some((e=>e.name==t)))continue;a&&(s[i.length]=(e,t,n)=>a(t,n,e.value));let c=i.length,d=a?["Block","BlockContext"]:o?c>=r.ATXHeading1&&c<=r.SetextHeading2?["Block","LeafBlock","Heading"]:["Block","LeafBlock"]:void 0;i.push(l.NodeType.define({id:c,name:t,props:d&&[[l.NodeProp.group,d]]})),f&&(e||(e={}),Array.isArray(f)||f instanceof h.Tag?e[t]=f:Object.assign(e,f))}n=new l.NodeSet(i),e&&(n=n.extend((0,h.styleTags)(e)))}if(_(t.props)&&(n=n.extend(...t.props)),_(t.remove))for(let e of t.remove){let t=this.blockNames.indexOf(e),n=this.inlineNames.indexOf(e);t>-1&&(i[t]=o[t]=void 0),n>-1&&(f[n]=void 0)}if(_(t.parseBlock))for(let e of t.parseBlock){let t=a.indexOf(e.name);if(t>-1)i[t]=e.parse,o[t]=e.leaf;else{let t=e.before?j(a,e.before):e.after?j(a,e.after)+1:a.length-1;i.splice(t,0,e.parse),o.splice(t,0,e.leaf),a.splice(t,0,e.name)}e.endLeaf&&d.push(e.endLeaf)}if(_(t.parseInline))for(let e of t.parseInline){let t=c.indexOf(e.name);if(t>-1)f[t]=e.parse;else{let t=e.before?j(c,e.before):e.after?j(c,e.after)+1:c.length-1;f.splice(t,0,e.parse),c.splice(t,0,e.name)}}return t.wrap&&(u=u.concat(t.wrap)),new F(n,i,o,a,d,s,f,c,u)}getNodeType(e){let t=this.nodeTypes[e];if(null==t)throw new RangeError(`Unknown node type '${e}'`);return t}parseInline(e,t){let n=new he(this,e,t);e:for(let e=t;e<n.end;){let t=n.char(e);for(let r of this.inlineParsers)if(r){let s=r(n,t,e);if(s>=0){e=s;continue e}}e++}return n.resolveMarkers(0)}}function _(e){return null!=e&&e.length>0}function q(e){if(!Array.isArray(e))return e;if(0==e.length)return null;let t=q(e[0]);if(1==e.length)return t;let n=q(e.slice(1));if(!n||!t)return t||n;let r=(e,t)=>(e||Z).concat(t||Z),s=t.wrap,i=n.wrap;return{props:r(t.props,n.props),defineNodes:r(t.defineNodes,n.defineNodes),parseBlock:r(t.parseBlock,n.parseBlock),parseInline:r(t.parseInline,n.parseInline),remove:r(t.remove,n.remove),wrap:s?i?(e,t,n,r)=>s(i(e,t,n,r),t,n,r):s:i}}function j(e,t){let n=e.indexOf(t);if(n<0)throw new RangeError(`Position specified relative to unknown parser ${t}`);return n}let Q=[l.NodeType.none];for(let e,t=1;e=r[t];t++)Q[t]=l.NodeType.define({id:t,name:e,props:t>=r.Escape?[]:[[l.NodeProp.group,t in p?["Block","BlockContext"]:["Block","LeafBlock"]]],top:"Document"==e});const Z=[];class K{constructor(e){this.nodeSet=e,this.content=[],this.nodes=[]}write(e,t,n,r=0){return this.content.push(e,t,n,4+4*r),this}writeElements(e,t=0){for(let n of e)n.writeTo(this,t);return this}finish(e,t){return l.Tree.build({buffer:this.content,nodeSet:this.nodeSet,reused:this.nodes,topID:e,length:t})}}class V{constructor(e,t,n,r=Z){this.type=e,this.from=t,this.to=n,this.children=r}writeTo(e,t){let n=e.content.length;e.writeElements(this.children,t),e.content.push(this.type,this.from+t,this.to+t,e.content.length+4-n)}toTree(e){return new K(e).writeElements(this.children,-this.from).finish(this.type,this.to-this.from)}}class G{constructor(e,t){this.tree=e,this.from=t}get to(){return this.from+this.tree.length}get type(){return this.tree.type.id}get children(){return Z}writeTo(e,t){e.nodes.push(this.tree),e.content.push(e.nodes.length-1,this.from+t,this.to+t,-1)}toTree(){return this.tree}}function J(e,t,n,r){return new V(e,t,n,r)}const Y={resolve:"Emphasis",mark:"EmphasisMark"},W={resolve:"Emphasis",mark:"EmphasisMark"},ee={},te={};class ne{constructor(e,t,n,r){this.type=e,this.from=t,this.to=n,this.side=r}}let re=/[!"#$%&'()*+,\-.\/:;<=>?@\[\\\]^_`{|}~\xA1\u2010-\u2027]/;try{re=new RegExp("[\\p{Pc}|\\p{Pd}|\\p{Pe}|\\p{Pf}|\\p{Pi}|\\p{Po}|\\p{Ps}]","u")}catch(e){}const se={Escape(e,t,n){if(92!=t||n==e.end-1)return-1;let s=e.char(n+1);for(let t=0;t<32;t++)if("!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~".charCodeAt(t)==s)return e.append(J(r.Escape,n,n+2));return-1},Entity(e,t,n){if(38!=t)return-1;let s=/^(?:#\d+|#x[a-f\d]+|\w+);/i.exec(e.slice(n+1,n+31));return s?e.append(J(r.Entity,n,n+1+s[0].length)):-1},InlineCode(e,t,n){if(96!=t||n&&96==e.char(n-1))return-1;let s=n+1;for(;s<e.end&&96==e.char(s);)s++;let i=s-n,o=0;for(;s<e.end;s++)if(96==e.char(s)){if(o++,o==i&&96!=e.char(s+1))return e.append(J(r.InlineCode,n,s+1,[J(r.CodeMark,n,n+i),J(r.CodeMark,s+1-i,s+1)]))}else o=0;return-1},HTMLTag(e,t,n){if(60!=t||n==e.end-1)return-1;let s=e.slice(n+1,e.end),i=/^(?:[a-z][-\w+.]+:[^\s>]+|[a-z\d.!#$%&'*+/=?^_`{|}~-]+@[a-z\d](?:[a-z\d-]{0,61}[a-z\d])?(?:\.[a-z\d](?:[a-z\d-]{0,61}[a-z\d])?)*)>/i.exec(s);if(i)return e.append(J(r.URL,n,n+1+i[0].length));let o=/^!--[^>](?:-[^-]|[^-])*?-->/i.exec(s);if(o)return e.append(J(r.Comment,n,n+1+o[0].length));let a=/^\?[^]*?\?>/.exec(s);if(a)return e.append(J(r.ProcessingInstruction,n,n+1+a[0].length));let l=/^(?:![A-Z][^]*?>|!\[CDATA\[[^]*?\]\]>|\/\s*[a-zA-Z][\w-]*\s*>|\s*[a-zA-Z][\w-]*(\s+[a-zA-Z:_][\w-.:]*(?:\s*=\s*(?:[^\s"'=<>`]+|'[^']*'|"[^"]*"))?)*\s*(\/\s*)?>)/.exec(s);return l?e.append(J(r.HTMLTag,n,n+1+l[0].length)):-1},Emphasis(e,t,n){if(95!=t&&42!=t)return-1;let r=n+1;for(;e.char(r)==t;)r++;let s=e.slice(n-1,n),i=e.slice(r,r+1),o=re.test(s),a=re.test(i),l=/\s|^$/.test(s),h=/\s|^$/.test(i),f=!h&&(!a||l||o),c=!l&&(!o||h||a),d=f&&(42==t||!c||o),u=c&&(42==t||!f||a);return e.append(new ne(95==t?Y:W,n,r,(d?1:0)|(u?2:0)))},HardBreak(e,t,n){if(92==t&&10==e.char(n+1))return e.append(J(r.HardBreak,n,n+2));if(32==t){let t=n+1;for(;32==e.char(t);)t++;if(10==e.char(t)&&t>=n+2)return e.append(J(r.HardBreak,n,t+1))}return-1},Link:(e,t,n)=>91==t?e.append(new ne(ee,n,n+1,1)):-1,Image:(e,t,n)=>33==t&&91==e.char(n+1)?e.append(new ne(te,n,n+2,1)):-1,LinkEnd(e,t,n){if(93!=t)return-1;for(let t=e.parts.length-1;t>=0;t--){let s=e.parts[t];if(s instanceof ne&&(s.type==ee||s.type==te)){if(!s.side||e.skipSpace(s.to)==n&&!/[(\[]/.test(e.slice(n+1,n+2)))return e.parts[t]=null,-1;let i=e.takeContent(t),o=e.parts[t]=ie(e,i,s.type==ee?r.Link:r.Image,s.from,n+1);if(s.type==ee)for(let n=0;n<t;n++){let t=e.parts[n];t instanceof ne&&t.type==ee&&(t.side=0)}return o.to}}return-1}};function ie(e,t,n,s,i){let{text:o}=e,a=e.char(i),l=i;if(t.unshift(J(r.LinkMark,s,s+(n==r.Image?2:1))),t.push(J(r.LinkMark,i-1,i)),40==a){let n,s=e.skipSpace(i+1),a=oe(o,s-e.offset,e.offset);a&&(s=e.skipSpace(a.to),n=ae(o,s-e.offset,e.offset),n&&(s=e.skipSpace(n.to))),41==e.char(s)&&(t.push(J(r.LinkMark,i,i+1)),l=s+1,a&&t.push(a),n&&t.push(n),t.push(J(r.LinkMark,s,l)))}else if(91==a){let n=le(o,i-e.offset,e.offset,!1);n&&(t.push(n),l=n.to)}return J(n,s,l,t)}function oe(e,t,n){if(60==e.charCodeAt(t)){for(let s=t+1;s<e.length;s++){let i=e.charCodeAt(s);if(62==i)return J(r.URL,t+n,s+1+n);if(60==i||10==i)return!1}return null}{let s=0,i=t;for(let t=!1;i<e.length;i++){let n=e.charCodeAt(i);if(g(n))break;if(t)t=!1;else if(40==n)s++;else if(41==n){if(!s)break;s--}else 92==n&&(t=!0)}return i>t?J(r.URL,t+n,i+n):i==e.length&&null}}function ae(e,t,n){let s=e.charCodeAt(t);if(39!=s&&34!=s&&40!=s)return!1;let i=40==s?41:s;for(let s=t+1,o=!1;s<e.length;s++){let a=e.charCodeAt(s);if(o)o=!1;else{if(a==i)return J(r.LinkTitle,t+n,s+1+n);92==a&&(o=!0)}}return null}function le(e,t,n,s){for(let i=!1,o=t+1,a=Math.min(e.length,o+999);o<a;o++){let a=e.charCodeAt(o);if(i)i=!1;else{if(93==a)return!s&&J(r.LinkLabel,t+n,o+1+n);if(s&&!g(a)&&(s=!1),91==a)return!1;92==a&&(i=!0)}}return null}class he{constructor(e,t,n){this.parser=e,this.text=t,this.offset=n,this.parts=[]}char(e){return e>=this.end?-1:this.text.charCodeAt(e-this.offset)}get end(){return this.offset+this.text.length}slice(e,t){return this.text.slice(e-this.offset,t-this.offset)}append(e){return this.parts.push(e),e.to}addDelimiter(e,t,n,r,s){return this.append(new ne(e,t,n,(r?1:0)|(s?2:0)))}addElement(e){return this.append(e)}resolveMarkers(e){for(let t=e;t<this.parts.length;t++){let n=this.parts[t];if(!(n instanceof ne&&n.type.resolve&&2&n.side))continue;let r,s=n.type==Y||n.type==W,i=n.to-n.from,o=t-1;for(;o>=e;o--){let e=this.parts[o];if(e instanceof ne&&1&e.side&&e.type==n.type&&!(s&&(1&n.side||2&e.side)&&(e.to-e.from+i)%3==0&&((e.to-e.from)%3||i%3))){r=e;break}}if(!r)continue;let a=n.type.resolve,l=[],h=r.from,f=n.to;if(s){let e=Math.min(2,r.to-r.from,i);h=r.to-e,f=n.from+e,a=1==e?"Emphasis":"StrongEmphasis"}r.type.mark&&l.push(this.elt(r.type.mark,h,r.to));for(let e=o+1;e<t;e++)this.parts[e]instanceof V&&l.push(this.parts[e]),this.parts[e]=null;n.type.mark&&l.push(this.elt(n.type.mark,n.from,f));let c=this.elt(a,h,f,l);this.parts[o]=s&&r.from!=h?new ne(r.type,r.from,h,r.side):null,(this.parts[t]=s&&n.to!=f?new ne(n.type,f,n.to,n.side):null)?this.parts.splice(t,0,c):this.parts[t]=c}let t=[];for(let n=e;n<this.parts.length;n++){let e=this.parts[n];e instanceof V&&t.push(e)}return t}findOpeningDelimiter(e){for(let t=this.parts.length-1;t>=0;t--){let n=this.parts[t];if(n instanceof ne&&n.type==e)return t}return null}takeContent(e){let t=this.resolveMarkers(e);return this.parts.length=e,t}skipSpace(e){return m(this.text,e-this.offset)+this.offset}elt(e,t,n,r){return"string"==typeof e?J(this.parser.getNodeType(e),t,n,r):new G(e,t)}}function fe(e,t){if(!t.length)return e;if(!e.length)return t;let n=e.slice(),r=0;for(let e of t){for(;r<n.length&&n[r].to<e.to;)r++;if(r<n.length&&n[r].from<e.from){let t=n[r];t instanceof V&&(n[r]=new V(t.type,t.from,t.to,fe(t.children,[e])))}else n.splice(r++,0,e)}return n}const ce=[r.CodeBlock,r.ListItem,r.OrderedList,r.BulletList];class de{constructor(e,t){this.fragments=e,this.input=t,this.i=0,this.fragment=null,this.fragmentEnd=-1,this.cursor=null,e.length&&(this.fragment=e[this.i++])}nextFragment(){this.fragment=this.i<this.fragments.length?this.fragments[this.i++]:null,this.cursor=null,this.fragmentEnd=-1}moveTo(e,t){for(;this.fragment&&this.fragment.to<=e;)this.nextFragment();if(!this.fragment||this.fragment.from>(e?e-1:0))return!1;if(this.fragmentEnd<0){let e=this.fragment.to;for(;e>0&&"\n"!=this.input.read(e-1,e);)e--;this.fragmentEnd=e?e-1:0}let n=this.cursor;n||(n=this.cursor=this.fragment.tree.cursor(),n.firstChild());let r=e+this.fragment.offset;for(;n.to<=r;)if(!n.parent())return!1;for(;;){if(n.from>=r)return this.fragment.from<=t;if(!n.childAfter(r))return!1}}matches(e){let t=this.cursor.tree;return t&&t.prop(l.NodeProp.contextHash)==e}takeNodes(e){let t=this.cursor,n=this.fragment.offset,s=this.fragmentEnd-(this.fragment.openEnd?1:0),i=e.absoluteLineStart,o=i,a=e.block.children.length,h=o,f=a;for(;;){if(t.to-n>s){if(t.type.isAnonymous&&t.firstChild())continue;break}let i=ue(t.from-n,e.ranges);if(t.to-n<=e.ranges[e.rangeI].to)e.addNode(t.tree,i);else{let n=new l.Tree(e.parser.nodeSet.types[r.Paragraph],[],[],0,e.block.hashProp);e.reusePlaceholders.set(n,t.tree),e.addNode(n,i)}if(t.type.is("Block")&&(ce.indexOf(t.type.id)<0?(o=t.to-n,a=e.block.children.length):(o=h,a=f,h=t.to-n,f=e.block.children.length)),!t.nextSibling())break}for(;e.block.children.length>a;)e.block.children.pop(),e.block.positions.pop();return o-i}}function ue(e,t){let n=e;for(let r=1;r<t.length;r++){let s=t[r-1].to,i=t[r].from;s<e&&(n-=i-s)}return n}const pe=(0,h.styleTags)({"Blockquote/...":h.tags.quote,HorizontalRule:h.tags.contentSeparator,"ATXHeading1/... SetextHeading1/...":h.tags.heading1,"ATXHeading2/... SetextHeading2/...":h.tags.heading2,"ATXHeading3/...":h.tags.heading3,"ATXHeading4/...":h.tags.heading4,"ATXHeading5/...":h.tags.heading5,"ATXHeading6/...":h.tags.heading6,"Comment CommentBlock":h.tags.comment,Escape:h.tags.escape,Entity:h.tags.character,"Emphasis/...":h.tags.emphasis,"StrongEmphasis/...":h.tags.strong,"Link/... Image/...":h.tags.link,"OrderedList/... BulletList/...":h.tags.list,"BlockQuote/...":h.tags.quote,"InlineCode CodeText":h.tags.monospace,URL:h.tags.url,"HeaderMark HardBreak QuoteMark ListMark LinkMark EmphasisMark CodeMark":h.tags.processingInstruction,"CodeInfo LinkLabel":h.tags.labelName,LinkTitle:h.tags.string,Paragraph:h.tags.content}),ge=new F(new l.NodeSet(Q).extend(pe),Object.keys(v).map((e=>v[e])),Object.keys(v).map((e=>X[e])),Object.keys(v),D,p,Object.keys(se).map((e=>se[e])),Object.keys(se),[]);function me(e,t,n){let r=[];for(let s=e.firstChild,i=t;;s=s.nextSibling){let e=s?s.from:n;if(e>i&&r.push({from:i,to:e}),!s)break;i=s.to}return r}const ke={resolve:"Strikethrough",mark:"StrikethroughMark"},xe={defineNodes:[{name:"Strikethrough",style:{"Strikethrough/...":h.tags.strikethrough}},{name:"StrikethroughMark",style:h.tags.processingInstruction}],parseInline:[{name:"Strikethrough",parse(e,t,n){if(126!=t||126!=e.char(n+1)||126==e.char(n+2))return-1;let r=e.slice(n-1,n),s=e.slice(n+2,n+3),i=/\s|^$/.test(r),o=/\s|^$/.test(s),a=re.test(r),l=re.test(s);return e.addDelimiter(ke,n,n+2,!o&&(!l||i||a),!i&&(!a||o||l))},after:"Emphasis"}]};function be(e,t,n=0,r,s=0){let i=0,o=!0,a=-1,l=-1,h=!1,f=()=>{r.push(e.elt("TableCell",s+a,s+l,e.parser.parseInline(t.slice(a,l),s+a)))};for(let c=n;c<t.length;c++){let n=t.charCodeAt(c);124!=n||h?(h||32!=n&&9!=n)&&(a<0&&(a=c),l=c+1):((!o||a>-1)&&i++,o=!1,r&&(a>-1&&f(),r.push(e.elt("TableDelimiter",c+s,c+s+1))),a=l=-1),h=!h&&92==n}return a>-1&&(i++,r&&f()),i}function Le(e,t){for(let n=t;n<e.length;n++){let t=e.charCodeAt(n);if(124==t)return!0;92==t&&n++}return!1}const Se=/^\|?(\s*:?-+:?\s*\|)+(\s*:?-+:?\s*)?$/;class Ce{constructor(){this.rows=null}nextLine(e,t,n){if(null==this.rows){let r;if(this.rows=!1,(45==t.next||58==t.next||124==t.next)&&Se.test(r=t.text.slice(t.pos))){let s=[];be(e,n.content,0,s,n.start)==be(e,r,t.pos)&&(this.rows=[e.elt("TableHeader",n.start,n.start+n.content.length,s),e.elt("TableDelimiter",e.lineStart+t.pos,e.lineStart+t.text.length)])}}else if(this.rows){let n=[];be(e,t.text,t.pos,n,e.lineStart),this.rows.push(e.elt("TableRow",e.lineStart+t.pos,e.lineStart+t.text.length,n))}return!1}finish(e,t){return!!this.rows&&(e.addLeafElement(t,e.elt("Table",t.start,t.start+t.content.length,this.rows)),!0)}}const ye={defineNodes:[{name:"Table",block:!0},{name:"TableHeader",style:{"TableHeader/...":h.tags.heading}},"TableRow",{name:"TableCell",style:h.tags.content},{name:"TableDelimiter",style:h.tags.processingInstruction}],parseBlock:[{name:"Table",leaf:(e,t)=>Le(t.content,0)?new Ce:null,endLeaf(e,t,n){if(n.parsers.some((e=>e instanceof Ce))||!Le(t.text,t.basePos))return!1;let r=e.scanLine(e.absoluteLineEnd+1).text;return Se.test(r)&&be(e,t.text,t.basePos)==be(e,r,t.basePos)},before:"SetextHeading"}]};class we{nextLine(){return!1}finish(e,t){return e.addLeafElement(t,e.elt("Task",t.start,t.start+t.content.length,[e.elt("TaskMarker",t.start,t.start+3),...e.parser.parseInline(t.content.slice(3),t.start+3)])),!0}}const Te={defineNodes:[{name:"Task",block:!0,style:h.tags.list},{name:"TaskMarker",style:h.tags.atom}],parseBlock:[{name:"TaskList",leaf:(e,t)=>/^\[[ xX]\][ \t]/.test(t.content)&&"ListItem"==e.parentType().name?new we:null,after:"SetextHeading"}]},Ae=/(www\.)|(https?:\/\/)|([\w.+-]+@)|(mailto:|xmpp:)/gy,Ie=/[\w-]+(\.\w+(\.\w+)?)(\/[^\s<]*)?/gy,Be=/[\w.+-]+@[\w-]+\.[\w.-]+/gy,Ee=/\/[a-zA-Z\d@.]+/gy;function Pe(e,t,n,r){let s=0;for(let i=t;i<n;i++)e[i]==r&&s++;return s}function Me(e,t){Be.lastIndex=t;let n=Be.exec(e);if(!n)return-1;let r=n[0][n[0].length-1];return"_"==r||"-"==r?-1:t+n[0].length-("."==r?1:0)}const He=[ye,Te,xe,{parseInline:[{name:"Autolink",parse(e,t,n){let r=n-e.offset;Ae.lastIndex=r;let s=Ae.exec(e.text),i=-1;return s?(s[1]||s[2]?i=function(e,t){Ie.lastIndex=t;let n=Ie.exec(e);if(!n)return-1;let r=t+n[0].length;for(;;){let n,s=e[r-1];if(/[?!.,:*_~]/.test(s)||")"==s&&Pe(e,t,r,")")>Pe(e,t,r,"("))r--;else{if(";"!=s||!(n=/&(?:#\d+|#x[a-f\d]+|\w+);$/.exec(e.slice(t,r))))break;r=t+n.index}}return r}(e.text,r+s[0].length):s[3]?i=Me(e.text,r):(i=Me(e.text,r+s[0].length),i>-1&&"xmpp:"==s[0]&&(Ee.lastIndex=i,s=Ee.exec(e.text),s&&(i=s.index+s[0].length))),i<0?-1:(e.addElement(e.elt("URL",n,i+e.offset)),i+e.offset)):-1}}]}];function ve(e,t,n){return(r,s,i)=>{if(s!=e||r.char(i+1)==e)return-1;let o=[r.elt(n,i,i+1)];for(let s=i+1;s<r.end;s++){let a=r.char(s);if(a==e)return r.addElement(r.elt(t,i,s+1,o.concat(r.elt(n,s,s+1))));if(92==a&&o.push(r.elt("Escape",s,2+s++)),g(a))break}return-1}}const Ne={defineNodes:[{name:"Superscript",style:h.tags.special(h.tags.content)},{name:"SuperscriptMark",style:h.tags.processingInstruction}],parseInline:[{name:"Superscript",parse:ve(94,"Superscript","SuperscriptMark")}]},Oe={defineNodes:[{name:"Subscript",style:h.tags.special(h.tags.content)},{name:"SubscriptMark",style:h.tags.processingInstruction}],parseInline:[{name:"Subscript",parse:ve(126,"Subscript","SubscriptMark")}]},Re={defineNodes:[{name:"Emoji",style:h.tags.character}],parseInline:[{name:"Emoji",parse(e,t,n){let r;return 58==t&&(r=/^[a-zA-Z_0-9]+:/.exec(e.slice(n+1,e.end)))?e.addElement(e.elt("Emoji",n,n+1+r[0].length)):-1}}]};var Xe=n(77866);const De=(0,o.defineLanguageFacet)({commentTokens:{block:{open:"\x3c!--",close:"--\x3e"}}}),ze=new l.NodeProp,$e=ge.configure({props:[o.foldNodeProp.add((e=>!e.is("Block")||e.is("Document")||null!=Ue(e)?void 0:(e,t)=>({from:t.doc.lineAt(e.from).to,to:e.to}))),ze.add(Ue),o.indentNodeProp.add({Document:()=>null}),o.languageDataProp.add({Document:De})]});function Ue(e){let t=/^(?:ATX|Setext)Heading(\d)$/.exec(e.name);return t?+t[1]:void 0}function Fe(e,t){let n=e;for(;;){let e,r=n.nextSibling;if(!r||null!=(e=Ue(r.type))&&e<=t)break;n=r}return n.to}const _e=o.foldService.of(((e,t,n)=>{for(let r=(0,o.syntaxTree)(e).resolveInner(n,-1);r&&!(r.from<t);r=r.parent){let e=r.type.prop(ze);if(null==e)continue;let t=Fe(r,e);if(t>n)return{from:n,to:t}}return null}));function qe(e){return new o.Language(De,e,[_e],"markdown")}const je=qe($e),Qe=qe($e.configure([He,Oe,Ne,Re]));class Ze{constructor(e,t,n,r,s,i,o){this.node=e,this.from=t,this.to=n,this.spaceBefore=r,this.spaceAfter=s,this.type=i,this.item=o}blank(e,t=!0){let n=this.spaceBefore+("Blockquote"==this.node.name?">":"");if(null!=e){for(;n.length<e;)n+=" ";return n}for(let e=this.to-this.from-n.length-this.spaceAfter.length;e>0;e--)n+=" ";return n+(t?this.spaceAfter:"")}marker(e,t){let n="OrderedList"==this.node.name?String(+Ve(this.item,e)[2]+t):"";return this.spaceBefore+n+this.type+this.spaceAfter}}function Ke(e,t){let n=[];for(let t=e;t&&"Document"!=t.name;t=t.parent)"ListItem"!=t.name&&"Blockquote"!=t.name&&"FencedCode"!=t.name||n.push(t);let r=[];for(let e=n.length-1;e>=0;e--){let s,i=n[e],o=t.lineAt(i.from),a=i.from-o.from;if("FencedCode"==i.name)r.push(new Ze(i,a,a,"","","",null));else if("Blockquote"==i.name&&(s=/^ *>( ?)/.exec(o.text.slice(a))))r.push(new Ze(i,a,a+s[0].length,"",s[1],">",null));else if("ListItem"==i.name&&"OrderedList"==i.parent.name&&(s=/^( *)\d+([.)])( *)/.exec(o.text.slice(a)))){let e=s[3],t=s[0].length;e.length>=4&&(e=e.slice(0,e.length-4),t-=4),r.push(new Ze(i.parent,a,a+t,s[1],e,s[2],i))}else if("ListItem"==i.name&&"BulletList"==i.parent.name&&(s=/^( *)([-+*])( {1,4}\[[ xX]\])?( +)/.exec(o.text.slice(a)))){let e=s[4],t=s[0].length;e.length>4&&(e=e.slice(0,e.length-4),t-=4);let n=s[2];s[3]&&(n+=s[3].replace(/[xX]/," ")),r.push(new Ze(i.parent,a,a+t,s[1],e,n,i))}}return r}function Ve(e,t){return/^(\s*)(\d+)(?=[.)])/.exec(t.sliceString(e.from,e.from+10))}function Ge(e,t,n,r=0){for(let s=-1,i=e;;){if("ListItem"==i.name){let e=Ve(i,t),o=+e[2];if(s>=0){if(o!=s+1)return;n.push({from:i.from+e[1].length,to:i.from+e[0].length,insert:String(s+2+r)})}s=o}let e=i.nextSibling;if(!e)break;i=e}}function Je(e,t){let n=/^[ \t]*/.exec(e)[0].length;if(!n||"\t"!=t.facet(o.indentUnit))return e;let r="";for(let t=(0,s.countColumn)(e,4,n);t>0;)t>=4?(r+="\t",t-=4):(r+=" ",t--);return r+e.slice(n)}const Ye=({state:e,dispatch:t})=>{let n=(0,o.syntaxTree)(e),{doc:r}=e,i=null,a=e.changeByRange((t=>{if(!t.empty||!Qe.isActiveAt(e,t.from))return i={range:t};let o=t.from,a=r.lineAt(o),l=Ke(n.resolveInner(o,-1),r);for(;l.length&&l[l.length-1].from>o-a.from;)l.pop();if(!l.length)return i={range:t};let h=l[l.length-1];if(h.to-h.spaceAfter.length>o-a.from)return i={range:t};let f=o>=h.to-h.spaceAfter.length&&!/\S/.test(a.text.slice(h.to));if(h.item&&f){if(h.node.firstChild.to>=o||a.from>0&&!/[^\s>]/.test(r.lineAt(a.from-1).text)){let e,t=l.length>1?l[l.length-2]:null,n="";t&&t.item?(e=a.from+t.from,n=t.marker(r,1)):e=a.from+(t?t.to:0);let i=[{from:e,to:o,insert:n}];return"OrderedList"==h.node.name&&Ge(h.item,r,i,-2),t&&"OrderedList"==t.node.name&&Ge(t.item,r,i),{range:s.EditorSelection.cursor(e+n.length),changes:i}}{let t="";for(let e=0,n=l.length-2;e<=n;e++)t+=l[e].blank(e<n?(0,s.countColumn)(a.text,4,l[e+1].from)-t.length:null,e<n);return t=Je(t,e),{range:s.EditorSelection.cursor(o+t.length+1),changes:{from:a.from,insert:t+e.lineBreak}}}}if("Blockquote"==h.node.name&&f&&a.from){let n=r.lineAt(a.from-1),s=/>\s*$/.exec(n.text);if(s&&s.index==h.from){let r=e.changes([{from:n.from+s.index,to:n.to},{from:a.from+h.from,to:a.to}]);return{range:t.map(r),changes:r}}}let c=[];"OrderedList"==h.node.name&&Ge(h.item,r,c);let d=h.item&&h.item.from<a.from,u="";if(!d||/^[\s\d.)\-+*>]*/.exec(a.text)[0].length>=h.to)for(let e=0,t=l.length-1;e<=t;e++)u+=e!=t||d?l[e].blank(e<t?(0,s.countColumn)(a.text,4,l[e+1].from)-u.length:null):l[e].marker(r,1);let p=o;for(;p>a.from&&/\s/.test(a.text.charAt(p-a.from-1));)p--;return u=Je(u,e),c.push({from:p,to:o,insert:e.lineBreak+u}),{range:s.EditorSelection.cursor(p+u.length+1),changes:c}}));return!i&&(t(e.update(a,{scrollIntoView:!0,userEvent:"input"})),!0)};function We(e){return"QuoteMark"==e.name||"ListMark"==e.name}const et=({state:e,dispatch:t})=>{let n=(0,o.syntaxTree)(e),r=null,i=e.changeByRange((t=>{let i=t.from,{doc:o}=e;if(t.empty&&Qe.isActiveAt(e,t.from)){let t=o.lineAt(i),r=Ke(function(e,t){let n=e.resolveInner(t,-1),r=t;We(n)&&(r=n.from,n=n.parent);for(let e;e=n.childBefore(r);)if(We(e))r=e.from;else{if("OrderedList"!=e.name&&"BulletList"!=e.name)break;n=e.lastChild,r=n.to}return n}(n,i),o);if(r.length){let n=r[r.length-1],o=n.to-n.spaceAfter.length+(n.spaceAfter?1:0);if(i-t.from>o&&!/\S/.test(t.text.slice(o,i-t.from)))return{range:s.EditorSelection.cursor(t.from+o),changes:{from:t.from+o,to:i}};if(i-t.from==o&&(!n.item||t.from<=n.item.from||!/\S/.test(t.text.slice(0,n.to)))){let r=t.from+n.from;if(n.item&&n.node.from<n.item.from&&/\S/.test(t.text.slice(n.from,n.to))){let i=n.blank((0,s.countColumn)(t.text,4,n.to)-(0,s.countColumn)(t.text,4,n.from));return r==t.from&&(i=Je(i,e)),{range:s.EditorSelection.cursor(r+i.length),changes:{from:r,to:t.from+n.to,insert:i}}}if(r<i)return{range:s.EditorSelection.cursor(r),changes:{from:r,to:i}}}}}return r={range:t}}));return!r&&(t(e.update(i,{scrollIntoView:!0,userEvent:"delete"})),!0)},tt=[{key:"Enter",run:Ye},{key:"Backspace",run:et}],nt=(0,Xe.html)({matchClosingTags:!1});function rt(e={}){let{codeLanguages:t,defaultCodeLanguage:n,addKeymap:a=!0,base:{parser:h}=je,completeHTMLTags:f=!0}=e;if(!(h instanceof F))throw new RangeError("Base parser provided to `markdown` should be a Markdown parser");let c,d=e.extensions?[e.extensions]:[],u=[nt.support];n instanceof o.LanguageSupport?(u.push(n.support),c=n.language):n&&(c=n);let p=t||c?(g=t,m=c,e=>{if(e&&g){let t=null;if(e=/\S*/.exec(e)[0],t="function"==typeof g?g(e):o.LanguageDescription.matchLanguageName(g,e,!0),t instanceof o.LanguageDescription)return t.support?t.support.language.parser:o.ParseContext.getSkippingParser(t.load());if(t)return t.parser}return m?m.parser:null}):void 0;var g,m;d.push(function(e){let{codeParser:t,htmlParser:n}=e,s=(0,l.parseMixed)(((e,s)=>{let i=e.type.id;if(!t||i!=r.CodeBlock&&i!=r.FencedCode){if(n&&(i==r.HTMLBlock||i==r.HTMLTag))return{parser:n,overlay:me(e.node,e.from,e.to)}}else{let n="";if(i==r.FencedCode){let t=e.node.getChild(r.CodeInfo);t&&(n=s.read(t.from,t.to))}let o=t(n);if(o)return{parser:o,overlay:e=>e.type.id==r.CodeText}}return null}));return{wrap:s}}({codeParser:p,htmlParser:nt.language.parser})),a&&u.push(s.Prec.high(i.keymap.of(tt)));let k=qe(h.configure(d));return f&&u.push(k.data.of({autocomplete:st})),new o.LanguageSupport(k,u)}function st(e){let{state:t,pos:n}=e,r=/<[:\-\.\w\u00b7-\uffff]*$/.exec(t.sliceDoc(n-25,n));if(!r)return null;let s=(0,o.syntaxTree)(t).resolveInner(n,-1);for(;s&&!s.type.isTop;){if("CodeBlock"==s.name||"FencedCode"==s.name||"ProcessingInstructionBlock"==s.name||"CommentBlock"==s.name||"Link"==s.name||"Image"==s.name)return null;s=s.parent}return{from:n-r[0].length,to:n,options:ot(),validFor:/^<[:\-\.\w\u00b7-\uffff]*$/}}let it=null;function ot(){if(it)return it;let e=(0,Xe.htmlCompletionSource)(new a.TK(s.EditorState.create({extensions:nt}),0,!0));return it=e?e.options:[]}}}]);